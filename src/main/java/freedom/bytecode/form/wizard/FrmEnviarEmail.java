package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmEnviarEmail extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.EnviarEmailRNA rn = null;

    public FrmEnviarEmail() {
        try {
            rn = (freedom.bytecode.rn.EnviarEmailRNA) getRN(freedom.bytecode.rn.wizard.EnviarEmailRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmailFluxo();
        init_tbEmailModelo();
        init_tbEmailModeloTag();
        init_tbCrmpartsDadosEmail();
        init_tbParmFluxo();
        init_tbEmailFluxoAnexo();
        init_spEnviarEmail();
        init_FVBox3();
        init_FHBox9();
        init_FHBox10();
        init_btnVoltar();
        init_btnEnviarEmail();
        init_FHBox2();
        init_FHBox3();
        init_ckSolicitarAprCliente();
        init_FHBox4();
        init_lblMensagem();
        init_FVBox2();
        init_vBoxTemplate();
        init_FLabel2();
        init_cmbTemplate();
        init_FVBox1();
        init_FLabel1();
        init_edAssunto();
        init_edMailBody();
        init_scCrmEmailFluxo();
        init_FrmEnviarEmail();
    }

    public CRM_EMAIL_FLUXO tbEmailFluxo;

    private void init_tbEmailFluxo() {
        tbEmailFluxo = rn.tbEmailFluxo;
        tbEmailFluxo.setName("tbEmailFluxo");
        tbEmailFluxo.setMaxRowCount(200);
        tbEmailFluxo.setWKey("340042;34001");
        tbEmailFluxo.setRatioBatchSize(20);
        getTables().put(tbEmailFluxo, "tbEmailFluxo");
        tbEmailFluxo.applyProperties();
    }

    public CRM_EMAIL_MODELO tbEmailModelo;

    private void init_tbEmailModelo() {
        tbEmailModelo = rn.tbEmailModelo;
        tbEmailModelo.setName("tbEmailModelo");
        tbEmailModelo.setMaxRowCount(200);
        tbEmailModelo.setWKey("340042;34002");
        tbEmailModelo.setRatioBatchSize(20);
        getTables().put(tbEmailModelo, "tbEmailModelo");
        tbEmailModelo.applyProperties();
    }

    public CRM_EMAIL_MODELO_TAG tbEmailModeloTag;

    private void init_tbEmailModeloTag() {
        tbEmailModeloTag = rn.tbEmailModeloTag;
        tbEmailModeloTag.setName("tbEmailModeloTag");
        tbEmailModeloTag.setMaxRowCount(200);
        tbEmailModeloTag.setWKey("340042;34004");
        tbEmailModeloTag.setRatioBatchSize(20);
        getTables().put(tbEmailModeloTag, "tbEmailModeloTag");
        tbEmailModeloTag.applyProperties();
    }

    public VW_CRMPARTS_DADOS_EMAIL tbCrmpartsDadosEmail;

    private void init_tbCrmpartsDadosEmail() {
        tbCrmpartsDadosEmail = rn.tbCrmpartsDadosEmail;
        tbCrmpartsDadosEmail.setName("tbCrmpartsDadosEmail");
        tbCrmpartsDadosEmail.setMaxRowCount(200);
        tbCrmpartsDadosEmail.setWKey("340042;31002");
        tbCrmpartsDadosEmail.setRatioBatchSize(20);
        getTables().put(tbCrmpartsDadosEmail, "tbCrmpartsDadosEmail");
        tbCrmpartsDadosEmail.applyProperties();
    }

    public CRM_PARM_FLUXO tbParmFluxo;

    private void init_tbParmFluxo() {
        tbParmFluxo = rn.tbParmFluxo;
        tbParmFluxo.setName("tbParmFluxo");
        tbParmFluxo.setMaxRowCount(200);
        tbParmFluxo.setWKey("340042;31003");
        tbParmFluxo.setRatioBatchSize(20);
        getTables().put(tbParmFluxo, "tbParmFluxo");
        tbParmFluxo.applyProperties();
    }

    public CRM_EMAIL_FLUXO_ANEXO tbEmailFluxoAnexo;

    private void init_tbEmailFluxoAnexo() {
        tbEmailFluxoAnexo = rn.tbEmailFluxoAnexo;
        tbEmailFluxoAnexo.setName("tbEmailFluxoAnexo");
        tbEmailFluxoAnexo.setMaxRowCount(200);
        tbEmailFluxoAnexo.setWKey("340042;31004");
        tbEmailFluxoAnexo.setRatioBatchSize(20);
        getTables().put(tbEmailFluxoAnexo, "tbEmailFluxoAnexo");
        tbEmailFluxoAnexo.applyProperties();
    }

    public TFStoredProcedure spEnviarEmail = new TFStoredProcedure();

    private void init_spEnviarEmail() {
        spEnviarEmail.setName("spEnviarEmail");
        TFStoredProcedureParam item0 = new TFStoredProcedureParam();
        item0.setDataType("dtNUMBER");
        item0.setDirection("pdIN");
        item0.setName("I_COD_EMPRESA");
        spEnviarEmail.getParams().add(item0);
        TFStoredProcedureParam item1 = new TFStoredProcedureParam();
        item1.setDataType("dtNUMBER");
        item1.setDirection("pdIN");
        item1.setName("I_COD_EVENTO");
        spEnviarEmail.getParams().add(item1);
        TFStoredProcedureParam item2 = new TFStoredProcedureParam();
        item2.setDataType("dtVARCHAR2");
        item2.setDirection("pdIN");
        item2.setName("I_USUARIO");
        spEnviarEmail.getParams().add(item2);
        TFStoredProcedureParam item3 = new TFStoredProcedureParam();
        item3.setDataType("dtVARCHAR2");
        item3.setDirection("pdIN");
        item3.setName("I_EMAIL_PARA");
        spEnviarEmail.getParams().add(item3);
        TFStoredProcedureParam item4 = new TFStoredProcedureParam();
        item4.setDataType("dtVARCHAR2");
        item4.setDirection("pdIN");
        item4.setName("I_EMAIL_ASSUNTO");
        spEnviarEmail.getParams().add(item4);
        TFStoredProcedureParam item5 = new TFStoredProcedureParam();
        item5.setDataType("dtVARCHAR2");
        item5.setDirection("pdIN");
        item5.setName("I_EMAIL_MSG");
        spEnviarEmail.getParams().add(item5);
        TFStoredProcedureParam item6 = new TFStoredProcedureParam();
        item6.setDataType("dtVARCHAR2");
        item6.setDirection("pdIN");
        item6.setName("I_EMAIL_MSG_PURE");
        spEnviarEmail.getParams().add(item6);
        spEnviarEmail.setSql("call PK_CRM_EVENTOS_RN_2.CRM_ACAO_ENVIAR_EMAIL_NOVO(:I_COD_EMPRESA,\r\n                                          :I_COD_EVENTO,\r\n                                         :I_USUARIO,\r\n                                         :I_EMAIL_PARA,\r\n                                         :I_EMAIL_ASSUNTO,\r\n                                         :I_EMAIL_MSG,\r\n                                         :I_EMAIL_MSG_PURE)");
        spEnviarEmail.applyProperties();
    }

    protected TFForm FrmEnviarEmail = this;
    private void init_FrmEnviarEmail() {
        FrmEnviarEmail.setName("FrmEnviarEmail");
        FrmEnviarEmail.setCaption("Enviar E-Mail");
        FrmEnviarEmail.setClientHeight(462);
        FrmEnviarEmail.setClientWidth(1024);
        FrmEnviarEmail.setColor("clBtnFace");
        FrmEnviarEmail.setWKey("340042");
        FrmEnviarEmail.setSpacing(0);
        FrmEnviarEmail.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(1024);
        FVBox3.setHeight(462);
        FVBox3.setAlign("alClient");
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(5);
        FVBox3.setPaddingLeft(5);
        FVBox3.setPaddingRight(8);
        FVBox3.setPaddingBottom(5);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FrmEnviarEmail.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(1024);
        FHBox9.setHeight(68);
        FHBox9.setAlign("alTop");
        FHBox9.setBorderStyle("stNone");
        FHBox9.setColor("16514043");
        FHBox9.setPaddingTop(5);
        FHBox9.setPaddingLeft(2);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(5);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox3.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(829);
        FHBox10.setHeight(60);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(2);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(2);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(3);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox9.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(53);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmEnviarEmail", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox10.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnEnviarEmail = new TFButton();

    private void init_btnEnviarEmail() {
        btnEnviarEmail.setName("btnEnviarEmail");
        btnEnviarEmail.setLeft(65);
        btnEnviarEmail.setTop(0);
        btnEnviarEmail.setWidth(65);
        btnEnviarEmail.setHeight(53);
        btnEnviarEmail.setHint("Enviar");
        btnEnviarEmail.setCaption("Enviar");
        btnEnviarEmail.setFontColor("clWindowText");
        btnEnviarEmail.setFontSize(-11);
        btnEnviarEmail.setFontName("Tahoma");
        btnEnviarEmail.setFontStyle("[]");
        btnEnviarEmail.setLayout("blGlyphTop");
        btnEnviarEmail.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnEnviarEmailClick(event);
            processarFlow("FrmEnviarEmail", "btnEnviarEmail", "OnClick");
        });
        btnEnviarEmail.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000002674944415478DAB5D57D68CD511CC7F1EF2EB250CB166516A5146A79"
 + "285126A5E629F2108D7F742333F963F3B83C95A7248F434AE10F91A78CF28714"
 + "6BFEC15F9A566BFCE58F292BD414CAB3F7B7F3594EBFEE76BB7773EA55F79E73"
 + "7EF7FB3BDFF33DE716D8FF69D3D180E2827EFEE1513882B4BE77F6578042D461"
 + "0F86A10553D1D4D700FEFC529CC438BC433D5EA21567FB12A0DC429EE7E21B4E"
 + "E0283EA30A37B1319F002538881AA4D0881D7813CDF1F17DA8C825C0206CC27E"
 + "0C570A6AF144E303F04B9F3DE80ACBA18AE6E33426E203F6E252F483D35086FB"
 + "FADE8E2294660B301EA7B0183F714ECBEFD2B83FBF19EB30033F30185FD08CCA"
 + "9E0214E92D6B959A07D88657D11C4FD3652CB7B0D1CDEA2F57FACEA02E19C0F3"
 + "98B6705846E235B62A40DC665AA892B1B885D5D1987FBE816A5C8C03CC56543F"
 + "209F7000E7F13D9A93D24AFC05062A1513F0369A7348ABAFC0530F3006C72CD4"
 + "EE6F8F6AA1C4DE27DE7A04AE6061D457AF67E3765769F3147679808F28D626FA"
 + "F21A33ECC91C5C4769D4E7FB3139B142535AFDBA18DD5D055B94E7324D788C0B"
 + "164ACE57B4DB42EDA7123F54A9B9712B54DA9A30AF3B80299F8B2C9CCE05EAEF"
 + "448785AB37D9EE6055867E5F91DF430D7A71CB54A67E696DC07AE53DD9BE6A63"
 + "3B328CAD512AABB597D6DB419B84B60CFDBEFC2AED5DB21DB67065CFC2B36C01"
 + "FC2DDB7B18F3DBF3B685BD7A8E3FEABF8765A60ACA2780A7C7CFC04A4C515FAB"
 + "025DC30B0CB17F059353002FE32578A8E7FCEEA951BABC7AFC7F60281E59B81C"
 + "730EB0165733CCF333945630BF1C8F6367AE01B65BF85BECADF939F1926ED36A"
 + "B206285100BF3177599EED2FF82981D6887056640000000049454E44AE426082");
        btnEnviarEmail.setImageId(34004);
        btnEnviarEmail.setColor("clBtnFace");
        btnEnviarEmail.setAccess(false);
        btnEnviarEmail.setIconReverseDirection(false);
        FHBox10.addChildren(btnEnviarEmail);
        btnEnviarEmail.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(130);
        FHBox2.setTop(0);
        FHBox2.setWidth(57);
        FHBox2.setHeight(41);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FHBox10.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(187);
        FHBox3.setTop(0);
        FHBox3.setWidth(185);
        FHBox3.setHeight(51);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox10.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFCheckBox ckSolicitarAprCliente = new TFCheckBox();

    private void init_ckSolicitarAprCliente() {
        ckSolicitarAprCliente.setName("ckSolicitarAprCliente");
        ckSolicitarAprCliente.setLeft(0);
        ckSolicitarAprCliente.setTop(0);
        ckSolicitarAprCliente.setWidth(169);
        ckSolicitarAprCliente.setHeight(17);
        ckSolicitarAprCliente.setCaption("Solicitar Aprova\u00E7\u00E3o do Cliente");
        ckSolicitarAprCliente.setFontColor("clWindowText");
        ckSolicitarAprCliente.setFontSize(-11);
        ckSolicitarAprCliente.setFontName("Tahoma");
        ckSolicitarAprCliente.setFontStyle("[]");
        ckSolicitarAprCliente.setVisible(false);
        ckSolicitarAprCliente.setVerticalAlignment("taAlignTop");
        FHBox3.addChildren(ckSolicitarAprCliente);
        ckSolicitarAprCliente.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(69);
        FHBox4.setWidth(750);
        FHBox4.setHeight(18);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setColor("15592941");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(5);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox3.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblMensagem = new TFLabel();

    private void init_lblMensagem() {
        lblMensagem.setName("lblMensagem");
        lblMensagem.setLeft(0);
        lblMensagem.setTop(0);
        lblMensagem.setWidth(67);
        lblMensagem.setHeight(13);
        lblMensagem.setAlign("alLeft");
        lblMensagem.setCaption("Mensagem....");
        lblMensagem.setFontColor("clNavy");
        lblMensagem.setFontSize(-11);
        lblMensagem.setFontName("Tahoma");
        lblMensagem.setFontStyle("[]");
        lblMensagem.setVerticalAlignment("taAlignTop");
        lblMensagem.setWordBreak(false);
        FHBox4.addChildren(lblMensagem);
        lblMensagem.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(88);
        FVBox2.setWidth(697);
        FVBox2.setHeight(125);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFVBox vBoxTemplate = new TFVBox();

    private void init_vBoxTemplate() {
        vBoxTemplate.setName("vBoxTemplate");
        vBoxTemplate.setLeft(0);
        vBoxTemplate.setTop(0);
        vBoxTemplate.setWidth(107);
        vBoxTemplate.setHeight(25);
        vBoxTemplate.setBorderStyle("stNone");
        vBoxTemplate.setPaddingTop(2);
        vBoxTemplate.setPaddingLeft(2);
        vBoxTemplate.setPaddingRight(0);
        vBoxTemplate.setPaddingBottom(0);
        vBoxTemplate.setMarginTop(0);
        vBoxTemplate.setMarginLeft(0);
        vBoxTemplate.setMarginRight(0);
        vBoxTemplate.setMarginBottom(0);
        vBoxTemplate.setSpacing(1);
        vBoxTemplate.setFlexVflex("ftFalse");
        vBoxTemplate.setFlexHflex("ftFalse");
        vBoxTemplate.setScrollable(false);
        vBoxTemplate.setBoxShadowConfigHorizontalLength(10);
        vBoxTemplate.setBoxShadowConfigVerticalLength(10);
        vBoxTemplate.setBoxShadowConfigBlurRadius(5);
        vBoxTemplate.setBoxShadowConfigSpreadRadius(0);
        vBoxTemplate.setBoxShadowConfigShadowColor("clBlack");
        vBoxTemplate.setBoxShadowConfigOpacity(75);
        FVBox2.addChildren(vBoxTemplate);
        vBoxTemplate.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(47);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Template ");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        vBoxTemplate.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cmbTemplate = new TFCombo();

    private void init_cmbTemplate() {
        cmbTemplate.setName("cmbTemplate");
        cmbTemplate.setLeft(0);
        cmbTemplate.setTop(26);
        cmbTemplate.setWidth(240);
        cmbTemplate.setHeight(21);
        cmbTemplate.setLookupTable(tbEmailModelo);
        cmbTemplate.setLookupKey("ID_EMAIL_MODELO");
        cmbTemplate.setLookupDesc("MODELO");
        cmbTemplate.setFlex(true);
        cmbTemplate.setReadOnly(true);
        cmbTemplate.setRequired(false);
        cmbTemplate.setPrompt("Selecione");
        cmbTemplate.setConstraintCheckWhen("cwImmediate");
        cmbTemplate.setConstraintCheckType("ctExpression");
        cmbTemplate.setConstraintFocusOnError(false);
        cmbTemplate.setConstraintEnableUI(true);
        cmbTemplate.setConstraintEnabled(false);
        cmbTemplate.setConstraintFormCheck(true);
        cmbTemplate.setClearOnDelKey(true);
        cmbTemplate.setUseClearButton(true);
        cmbTemplate.setHideClearButtonOnNullValue(true);
        cmbTemplate.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbTemplateChange(event);
            processarFlow("FrmEnviarEmail", "cmbTemplate", "OnChange");
        });
        FVBox2.addChildren(cmbTemplate);
        cmbTemplate.applyProperties();
        addValidatable(cmbTemplate);
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(48);
        FVBox1.setWidth(52);
        FVBox1.setHeight(25);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(2);
        FVBox1.setPaddingLeft(2);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FVBox2.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(42);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Assunto ");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFString edAssunto = new TFString();

    private void init_edAssunto() {
        edAssunto.setName("edAssunto");
        edAssunto.setLeft(0);
        edAssunto.setTop(74);
        edAssunto.setWidth(457);
        edAssunto.setHeight(24);
        edAssunto.setFlex(true);
        edAssunto.setRequired(false);
        edAssunto.setConstraintCheckWhen("cwImmediate");
        edAssunto.setConstraintCheckType("ctExpression");
        edAssunto.setConstraintFocusOnError(false);
        edAssunto.setConstraintEnableUI(true);
        edAssunto.setConstraintEnabled(false);
        edAssunto.setConstraintFormCheck(true);
        edAssunto.setCharCase("ccNormal");
        edAssunto.setPwd(false);
        edAssunto.setMaxlength(0);
        edAssunto.setFontColor("clWindowText");
        edAssunto.setFontSize(-13);
        edAssunto.setFontName("Tahoma");
        edAssunto.setFontStyle("[]");
        edAssunto.setSaveLiteralCharacter(false);
        edAssunto.applyProperties();
        FVBox2.addChildren(edAssunto);
        addValidatable(edAssunto);
    }

    public TFRichEdit edMailBody = new TFRichEdit();

    private void init_edMailBody() {
        edMailBody.setName("edMailBody");
        edMailBody.setLeft(0);
        edMailBody.setTop(214);
        edMailBody.setWidth(433);
        edMailBody.setHeight(157);
        edMailBody.setAlign("alClient");
        edMailBody.setFlexVflex("ftTrue");
        edMailBody.setFlexHflex("ftTrue");
        edMailBody.setToolbarConfig("tcBasicPlus");
        FVBox3.addChildren(edMailBody);
        edMailBody.applyProperties();
    }

    public TFSchema scCrmEmailFluxo;

    private void init_scCrmEmailFluxo() {
        scCrmEmailFluxo = rn.scCrmEmailFluxo;
        scCrmEmailFluxo.setName("scCrmEmailFluxo");
        TFSchemaItem item7 = new TFSchemaItem();
        item7.setTable(tbEmailFluxo);
        scCrmEmailFluxo.getTables().add(item7);
        scCrmEmailFluxo.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnEnviarEmailClick(final Event<Object> event) {
        if (btnEnviarEmail.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnEnviarEmail");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cmbTemplateChange(final Event<Object> event);

}