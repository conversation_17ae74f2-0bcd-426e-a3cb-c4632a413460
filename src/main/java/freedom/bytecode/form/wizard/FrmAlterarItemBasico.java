package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAlterarItemBasico extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AlterarItemBasicoRNA rn = null;

    public FrmAlterarItemBasico() {
        try {
            rn = (freedom.bytecode.rn.AlterarItemBasicoRNA) getRN(freedom.bytecode.rn.wizard.AlterarItemBasicoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbItens();
        init_tbItensFornecedor();
        init_tbItensGrupoInterno();
        init_tbItensSubGrupo();
        init_tbFabricante();
        init_tbItensUnidadeMedida();
        init_tbItemSituacaoEspecial();
        init_tbMarca();
        init_tbListaLetraDescontoGeral();
        init_tbItensOrigem();
        init_tbItensHistBloqueio();
        init_tbBuscaItensFornecedor();
        init_vBoxPrincipal();
        init_hboxEditarItem();
        init_btnSalvar();
        init_btnCancelar();
        init_btnBloquearDesbloquearItemParaCompra();
        init_hBoxCampos();
        init_vBoxColuna01();
        init_vBoxGrupo();
        init_lblGrupo();
        init_cboGrupo();
        init_vBoxSubgrupo();
        init_lblSubGrupo();
        init_cboSubgrupo();
        init_vBoxFabricante();
        init_lblFabricante();
        init_cboFabricante();
        init_vBoxMarca();
        init_lblMarca();
        init_cboMarca();
        init_vBoxSituacaoEspecial();
        init_lblSituacaoEspecial();
        init_cboSituacaoEspecial();
        init_vBoxLetraDesconto();
        init_lblLetraDesconto();
        init_cboLetraDesconto();
        init_vBoxBloquearItemCompra();
        init_lblStatus();
        init_edtStatus();
        init_vBoxColuna02();
        init_vBoxCodigoGTIN();
        init_lblCodigoGTIN();
        init_edtStringCodigoGTIN();
        init_vBoxCodigoBarras();
        init_lblCodigoBarras();
        init_edtStringCodigoBarras();
        init_vBoxTipoPeca();
        init_lblTipoPeca();
        init_cboTipoPeca();
        init_vBoxEstocagemReservaDias();
        init_lblEstocagemReservaDias();
        init_edtIntegerReservaDias();
        init_vBoxPartNumber();
        init_lblPartNumber();
        init_edtStringPartNumber();
        init_vBoxOrigem();
        init_lblOrigem();
        init_cboOrigem();
        init_vBoxColuna03();
        init_vBoxEmbalagemUnidade();
        init_lblEmbalagemUnidade();
        init_hBoxEmbalagemUnidade();
        init_edtSpinnerEmbalagem();
        init_cboUnidade();
        init_vBoxPesoLiquido();
        init_lblPesoLiquido();
        init_edtDecimalPesoLiquido();
        init_vBoxPesoBruto();
        init_lblPesoBruto();
        init_edtDecimalPesoBruto();
        init_vBoxLargura();
        init_lblLargura();
        init_edtDecimalLargura();
        init_vBoxComprimento();
        init_lblComprimento();
        init_edtDecimalComprimento();
        init_vBoxAltura();
        init_lblAltura();
        init_edtDecimalAltura();
        init_vBoxVolume();
        init_lblVolume();
        init_edtDecimalVolume();
        init_sc();
        init_FrmAlterarItemBasico();
    }

    public ITENS tbItens;

    private void init_tbItens() {
        tbItens = rn.tbItens;
        tbItens.setName("tbItens");
        tbItens.setMaxRowCount(200);
        tbItens.setWKey("7000173;70001");
        tbItens.setRatioBatchSize(20);
        getTables().put(tbItens, "tbItens");
        tbItens.applyProperties();
    }

    public ITENS_FORNECEDOR tbItensFornecedor;

    private void init_tbItensFornecedor() {
        tbItensFornecedor = rn.tbItensFornecedor;
        tbItensFornecedor.setName("tbItensFornecedor");
        tbItensFornecedor.setMaxRowCount(1000);
        tbItensFornecedor.setWKey("7000173;70002");
        tbItensFornecedor.setRatioBatchSize(20);
        getTables().put(tbItensFornecedor, "tbItensFornecedor");
        tbItensFornecedor.applyProperties();
    }

    public ITENS_GRUPO_INTERNO tbItensGrupoInterno;

    private void init_tbItensGrupoInterno() {
        tbItensGrupoInterno = rn.tbItensGrupoInterno;
        tbItensGrupoInterno.setName("tbItensGrupoInterno");
        tbItensGrupoInterno.setMaxRowCount(200);
        tbItensGrupoInterno.setWKey("7000173;70003");
        tbItensGrupoInterno.setRatioBatchSize(20);
        getTables().put(tbItensGrupoInterno, "tbItensGrupoInterno");
        tbItensGrupoInterno.applyProperties();
    }

    public ITENS_SUB_GRUPO tbItensSubGrupo;

    private void init_tbItensSubGrupo() {
        tbItensSubGrupo = rn.tbItensSubGrupo;
        tbItensSubGrupo.setName("tbItensSubGrupo");
        tbItensSubGrupo.setMaxRowCount(200);
        tbItensSubGrupo.setWKey("7000173;70004");
        tbItensSubGrupo.setRatioBatchSize(20);
        getTables().put(tbItensSubGrupo, "tbItensSubGrupo");
        tbItensSubGrupo.applyProperties();
    }

    public FABRICANTE tbFabricante;

    private void init_tbFabricante() {
        tbFabricante = rn.tbFabricante;
        tbFabricante.setName("tbFabricante");
        tbFabricante.setMaxRowCount(2000);
        tbFabricante.setWKey("7000173;70005");
        tbFabricante.setRatioBatchSize(20);
        getTables().put(tbFabricante, "tbFabricante");
        tbFabricante.applyProperties();
    }

    public ITENS_UNIDADE_MEDIDA tbItensUnidadeMedida;

    private void init_tbItensUnidadeMedida() {
        tbItensUnidadeMedida = rn.tbItensUnidadeMedida;
        tbItensUnidadeMedida.setName("tbItensUnidadeMedida");
        tbItensUnidadeMedida.setMaxRowCount(200);
        tbItensUnidadeMedida.setWKey("7000173;70006");
        tbItensUnidadeMedida.setRatioBatchSize(20);
        getTables().put(tbItensUnidadeMedida, "tbItensUnidadeMedida");
        tbItensUnidadeMedida.applyProperties();
    }

    public ITEM_SITUACAO_ESPECIAL tbItemSituacaoEspecial;

    private void init_tbItemSituacaoEspecial() {
        tbItemSituacaoEspecial = rn.tbItemSituacaoEspecial;
        tbItemSituacaoEspecial.setName("tbItemSituacaoEspecial");
        tbItemSituacaoEspecial.setMaxRowCount(200);
        tbItemSituacaoEspecial.setWKey("7000173;70007");
        tbItemSituacaoEspecial.setRatioBatchSize(20);
        getTables().put(tbItemSituacaoEspecial, "tbItemSituacaoEspecial");
        tbItemSituacaoEspecial.applyProperties();
    }

    public MARCA tbMarca;

    private void init_tbMarca() {
        tbMarca = rn.tbMarca;
        tbMarca.setName("tbMarca");
        tbMarca.setMaxRowCount(200);
        tbMarca.setWKey("7000173;24301");
        tbMarca.setRatioBatchSize(20);
        getTables().put(tbMarca, "tbMarca");
        tbMarca.applyProperties();
    }

    public LISTA_LETRA_DESCONTO_GERAL tbListaLetraDescontoGeral;

    private void init_tbListaLetraDescontoGeral() {
        tbListaLetraDescontoGeral = rn.tbListaLetraDescontoGeral;
        tbListaLetraDescontoGeral.setName("tbListaLetraDescontoGeral");
        tbListaLetraDescontoGeral.setMaxRowCount(200);
        tbListaLetraDescontoGeral.setWKey("7000173;27402");
        tbListaLetraDescontoGeral.setRatioBatchSize(20);
        getTables().put(tbListaLetraDescontoGeral, "tbListaLetraDescontoGeral");
        tbListaLetraDescontoGeral.applyProperties();
    }

    public BUSCA_ITENS_ORIGEM tbItensOrigem;

    private void init_tbItensOrigem() {
        tbItensOrigem = rn.tbItensOrigem;
        tbItensOrigem.setName("tbItensOrigem");
        tbItensOrigem.setMaxRowCount(0);
        tbItensOrigem.setWKey("7000173;31101");
        tbItensOrigem.setRatioBatchSize(20);
        getTables().put(tbItensOrigem, "tbItensOrigem");
        tbItensOrigem.applyProperties();
    }

    public ITENS_HIST_BLOQUEIO tbItensHistBloqueio;

    private void init_tbItensHistBloqueio() {
        tbItensHistBloqueio = rn.tbItensHistBloqueio;
        tbItensHistBloqueio.setName("tbItensHistBloqueio");
        tbItensHistBloqueio.setMaxRowCount(200);
        tbItensHistBloqueio.setWKey("7000173;47703");
        tbItensHistBloqueio.setRatioBatchSize(20);
        getTables().put(tbItensHistBloqueio, "tbItensHistBloqueio");
        tbItensHistBloqueio.applyProperties();
    }

    public BUSCA_ITENS_FORNECEDOR tbBuscaItensFornecedor;

    private void init_tbBuscaItensFornecedor() {
        tbBuscaItensFornecedor = rn.tbBuscaItensFornecedor;
        tbBuscaItensFornecedor.setName("tbBuscaItensFornecedor");
        tbBuscaItensFornecedor.setMaxRowCount(200);
        tbBuscaItensFornecedor.setWKey("7000173;47704");
        tbBuscaItensFornecedor.setRatioBatchSize(20);
        getTables().put(tbBuscaItensFornecedor, "tbBuscaItensFornecedor");
        tbBuscaItensFornecedor.applyProperties();
    }

    protected TFForm FrmAlterarItemBasico = this;
    private void init_FrmAlterarItemBasico() {
        FrmAlterarItemBasico.setName("FrmAlterarItemBasico");
        FrmAlterarItemBasico.setCaption("Alterar Item");
        FrmAlterarItemBasico.setClientHeight(496);
        FrmAlterarItemBasico.setClientWidth(904);
        FrmAlterarItemBasico.setColor("clBtnFace");
        FrmAlterarItemBasico.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAlterarItemBasico", "FrmAlterarItemBasico", "OnCreate");
        });
        FrmAlterarItemBasico.setWOrigem("EhMain");
        FrmAlterarItemBasico.setWKey("7000173");
        FrmAlterarItemBasico.setSpacing(0);
        FrmAlterarItemBasico.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(904);
        vBoxPrincipal.setHeight(496);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(5);
        vBoxPrincipal.setPaddingLeft(5);
        vBoxPrincipal.setPaddingRight(5);
        vBoxPrincipal.setPaddingBottom(5);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(5);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmAlterarItemBasico.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hboxEditarItem = new TFHBox();

    private void init_hboxEditarItem() {
        hboxEditarItem.setName("hboxEditarItem");
        hboxEditarItem.setLeft(0);
        hboxEditarItem.setTop(0);
        hboxEditarItem.setWidth(900);
        hboxEditarItem.setHeight(60);
        hboxEditarItem.setBorderStyle("stNone");
        hboxEditarItem.setPaddingTop(0);
        hboxEditarItem.setPaddingLeft(0);
        hboxEditarItem.setPaddingRight(0);
        hboxEditarItem.setPaddingBottom(0);
        hboxEditarItem.setMarginTop(0);
        hboxEditarItem.setMarginLeft(0);
        hboxEditarItem.setMarginRight(0);
        hboxEditarItem.setMarginBottom(0);
        hboxEditarItem.setSpacing(5);
        hboxEditarItem.setFlexVflex("ftMin");
        hboxEditarItem.setFlexHflex("ftTrue");
        hboxEditarItem.setScrollable(false);
        hboxEditarItem.setBoxShadowConfigHorizontalLength(10);
        hboxEditarItem.setBoxShadowConfigVerticalLength(10);
        hboxEditarItem.setBoxShadowConfigBlurRadius(5);
        hboxEditarItem.setBoxShadowConfigSpreadRadius(0);
        hboxEditarItem.setBoxShadowConfigShadowColor("clBlack");
        hboxEditarItem.setBoxShadowConfigOpacity(75);
        hboxEditarItem.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hboxEditarItem);
        hboxEditarItem.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(60);
        btnSalvar.setHeight(55);
        btnSalvar.setHint("Salvar");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAlterarItemBasico", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(700080);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hboxEditarItem.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(60);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(60);
        btnCancelar.setHeight(55);
        btnCancelar.setHint("Cancelar");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmAlterarItemBasico", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(700098);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hboxEditarItem.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFButton btnBloquearDesbloquearItemParaCompra = new TFButton();

    private void init_btnBloquearDesbloquearItemParaCompra() {
        btnBloquearDesbloquearItemParaCompra.setName("btnBloquearDesbloquearItemParaCompra");
        btnBloquearDesbloquearItemParaCompra.setLeft(120);
        btnBloquearDesbloquearItemParaCompra.setTop(0);
        btnBloquearDesbloquearItemParaCompra.setWidth(70);
        btnBloquearDesbloquearItemParaCompra.setHeight(55);
        btnBloquearDesbloquearItemParaCompra.setHint("Bloquear");
        btnBloquearDesbloquearItemParaCompra.setCaption("Bloquear");
        btnBloquearDesbloquearItemParaCompra.setFontColor("clWindowText");
        btnBloquearDesbloquearItemParaCompra.setFontSize(-11);
        btnBloquearDesbloquearItemParaCompra.setFontName("Tahoma");
        btnBloquearDesbloquearItemParaCompra.setFontStyle("[]");
        btnBloquearDesbloquearItemParaCompra.setLayout("blGlyphTop");
        btnBloquearDesbloquearItemParaCompra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnBloquearDesbloquearItemParaCompraClick(event);
            processarFlow("FrmAlterarItemBasico", "btnBloquearDesbloquearItemParaCompra", "OnClick");
        });
        btnBloquearDesbloquearItemParaCompra.setImageId(7000189);
        btnBloquearDesbloquearItemParaCompra.setColor("clBtnFace");
        btnBloquearDesbloquearItemParaCompra.setAccess(false);
        btnBloquearDesbloquearItemParaCompra.setIconReverseDirection(false);
        hboxEditarItem.addChildren(btnBloquearDesbloquearItemParaCompra);
        btnBloquearDesbloquearItemParaCompra.applyProperties();
    }

    public TFHBox hBoxCampos = new TFHBox();

    private void init_hBoxCampos() {
        hBoxCampos.setName("hBoxCampos");
        hBoxCampos.setLeft(0);
        hBoxCampos.setTop(61);
        hBoxCampos.setWidth(900);
        hBoxCampos.setHeight(350);
        hBoxCampos.setBorderStyle("stNone");
        hBoxCampos.setPaddingTop(0);
        hBoxCampos.setPaddingLeft(0);
        hBoxCampos.setPaddingRight(0);
        hBoxCampos.setPaddingBottom(0);
        hBoxCampos.setMarginTop(0);
        hBoxCampos.setMarginLeft(0);
        hBoxCampos.setMarginRight(0);
        hBoxCampos.setMarginBottom(0);
        hBoxCampos.setSpacing(5);
        hBoxCampos.setFlexVflex("ftMin");
        hBoxCampos.setFlexHflex("ftTrue");
        hBoxCampos.setScrollable(false);
        hBoxCampos.setBoxShadowConfigHorizontalLength(10);
        hBoxCampos.setBoxShadowConfigVerticalLength(10);
        hBoxCampos.setBoxShadowConfigBlurRadius(5);
        hBoxCampos.setBoxShadowConfigSpreadRadius(0);
        hBoxCampos.setBoxShadowConfigShadowColor("clBlack");
        hBoxCampos.setBoxShadowConfigOpacity(75);
        hBoxCampos.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxCampos);
        hBoxCampos.applyProperties();
    }

    public TFVBox vBoxColuna01 = new TFVBox();

    private void init_vBoxColuna01() {
        vBoxColuna01.setName("vBoxColuna01");
        vBoxColuna01.setLeft(0);
        vBoxColuna01.setTop(0);
        vBoxColuna01.setWidth(300);
        vBoxColuna01.setHeight(340);
        vBoxColuna01.setBorderStyle("stNone");
        vBoxColuna01.setPaddingTop(0);
        vBoxColuna01.setPaddingLeft(0);
        vBoxColuna01.setPaddingRight(0);
        vBoxColuna01.setPaddingBottom(0);
        vBoxColuna01.setMarginTop(0);
        vBoxColuna01.setMarginLeft(0);
        vBoxColuna01.setMarginRight(0);
        vBoxColuna01.setMarginBottom(0);
        vBoxColuna01.setSpacing(1);
        vBoxColuna01.setFlexVflex("ftMin");
        vBoxColuna01.setFlexHflex("ftTrue");
        vBoxColuna01.setScrollable(false);
        vBoxColuna01.setBoxShadowConfigHorizontalLength(10);
        vBoxColuna01.setBoxShadowConfigVerticalLength(10);
        vBoxColuna01.setBoxShadowConfigBlurRadius(5);
        vBoxColuna01.setBoxShadowConfigSpreadRadius(0);
        vBoxColuna01.setBoxShadowConfigShadowColor("clBlack");
        vBoxColuna01.setBoxShadowConfigOpacity(75);
        hBoxCampos.addChildren(vBoxColuna01);
        vBoxColuna01.applyProperties();
    }

    public TFVBox vBoxGrupo = new TFVBox();

    private void init_vBoxGrupo() {
        vBoxGrupo.setName("vBoxGrupo");
        vBoxGrupo.setLeft(0);
        vBoxGrupo.setTop(0);
        vBoxGrupo.setWidth(290);
        vBoxGrupo.setHeight(45);
        vBoxGrupo.setBorderStyle("stNone");
        vBoxGrupo.setPaddingTop(0);
        vBoxGrupo.setPaddingLeft(0);
        vBoxGrupo.setPaddingRight(0);
        vBoxGrupo.setPaddingBottom(0);
        vBoxGrupo.setMarginTop(0);
        vBoxGrupo.setMarginLeft(0);
        vBoxGrupo.setMarginRight(0);
        vBoxGrupo.setMarginBottom(0);
        vBoxGrupo.setSpacing(1);
        vBoxGrupo.setFlexVflex("ftMin");
        vBoxGrupo.setFlexHflex("ftTrue");
        vBoxGrupo.setScrollable(false);
        vBoxGrupo.setBoxShadowConfigHorizontalLength(10);
        vBoxGrupo.setBoxShadowConfigVerticalLength(10);
        vBoxGrupo.setBoxShadowConfigBlurRadius(5);
        vBoxGrupo.setBoxShadowConfigSpreadRadius(0);
        vBoxGrupo.setBoxShadowConfigShadowColor("clBlack");
        vBoxGrupo.setBoxShadowConfigOpacity(75);
        vBoxColuna01.addChildren(vBoxGrupo);
        vBoxGrupo.applyProperties();
    }

    public TFLabel lblGrupo = new TFLabel();

    private void init_lblGrupo() {
        lblGrupo.setName("lblGrupo");
        lblGrupo.setLeft(0);
        lblGrupo.setTop(0);
        lblGrupo.setWidth(29);
        lblGrupo.setHeight(13);
        lblGrupo.setCaption("Grupo");
        lblGrupo.setFontColor("clWindowText");
        lblGrupo.setFontSize(-11);
        lblGrupo.setFontName("Tahoma");
        lblGrupo.setFontStyle("[]");
        lblGrupo.setVerticalAlignment("taVerticalCenter");
        lblGrupo.setWordBreak(false);
        vBoxGrupo.addChildren(lblGrupo);
        lblGrupo.applyProperties();
    }

    public TFCombo cboGrupo = new TFCombo();

    private void init_cboGrupo() {
        cboGrupo.setName("cboGrupo");
        cboGrupo.setLeft(0);
        cboGrupo.setTop(14);
        cboGrupo.setWidth(200);
        cboGrupo.setHeight(21);
        cboGrupo.setHint("Grupo");
        cboGrupo.setTable(tbItens);
        cboGrupo.setLookupTable(tbItensGrupoInterno);
        cboGrupo.setFieldName("COD_GRUPO_INTERNO");
        cboGrupo.setLookupKey("COD_GRUPO_INTERNO");
        cboGrupo.setLookupDesc("DESCRICAO");
        cboGrupo.setFlex(true);
        cboGrupo.setHelpCaption("Grupo");
        cboGrupo.setReadOnly(true);
        cboGrupo.setRequired(false);
        cboGrupo.setPrompt("Grupo");
        cboGrupo.setConstraintCheckWhen("cwImmediate");
        cboGrupo.setConstraintCheckType("ctExpression");
        cboGrupo.setConstraintFocusOnError(false);
        cboGrupo.setConstraintEnableUI(true);
        cboGrupo.setConstraintEnabled(false);
        cboGrupo.setConstraintFormCheck(true);
        cboGrupo.setClearOnDelKey(true);
        cboGrupo.setUseClearButton(true);
        cboGrupo.setHideClearButtonOnNullValue(true);
        cboGrupo.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboGrupoChange(event);
            processarFlow("FrmAlterarItemBasico", "cboGrupo", "OnChange");
        });
        vBoxGrupo.addChildren(cboGrupo);
        cboGrupo.applyProperties();
        addValidatable(cboGrupo);
    }

    public TFVBox vBoxSubgrupo = new TFVBox();

    private void init_vBoxSubgrupo() {
        vBoxSubgrupo.setName("vBoxSubgrupo");
        vBoxSubgrupo.setLeft(0);
        vBoxSubgrupo.setTop(46);
        vBoxSubgrupo.setWidth(290);
        vBoxSubgrupo.setHeight(45);
        vBoxSubgrupo.setBorderStyle("stNone");
        vBoxSubgrupo.setPaddingTop(0);
        vBoxSubgrupo.setPaddingLeft(0);
        vBoxSubgrupo.setPaddingRight(0);
        vBoxSubgrupo.setPaddingBottom(0);
        vBoxSubgrupo.setMarginTop(0);
        vBoxSubgrupo.setMarginLeft(0);
        vBoxSubgrupo.setMarginRight(0);
        vBoxSubgrupo.setMarginBottom(0);
        vBoxSubgrupo.setSpacing(1);
        vBoxSubgrupo.setFlexVflex("ftMin");
        vBoxSubgrupo.setFlexHflex("ftTrue");
        vBoxSubgrupo.setScrollable(false);
        vBoxSubgrupo.setBoxShadowConfigHorizontalLength(10);
        vBoxSubgrupo.setBoxShadowConfigVerticalLength(10);
        vBoxSubgrupo.setBoxShadowConfigBlurRadius(5);
        vBoxSubgrupo.setBoxShadowConfigSpreadRadius(0);
        vBoxSubgrupo.setBoxShadowConfigShadowColor("clBlack");
        vBoxSubgrupo.setBoxShadowConfigOpacity(75);
        vBoxColuna01.addChildren(vBoxSubgrupo);
        vBoxSubgrupo.applyProperties();
    }

    public TFLabel lblSubGrupo = new TFLabel();

    private void init_lblSubGrupo() {
        lblSubGrupo.setName("lblSubGrupo");
        lblSubGrupo.setLeft(0);
        lblSubGrupo.setTop(0);
        lblSubGrupo.setWidth(46);
        lblSubGrupo.setHeight(13);
        lblSubGrupo.setCaption("Subgrupo");
        lblSubGrupo.setFontColor("clWindowText");
        lblSubGrupo.setFontSize(-11);
        lblSubGrupo.setFontName("Tahoma");
        lblSubGrupo.setFontStyle("[]");
        lblSubGrupo.setVerticalAlignment("taVerticalCenter");
        lblSubGrupo.setWordBreak(false);
        vBoxSubgrupo.addChildren(lblSubGrupo);
        lblSubGrupo.applyProperties();
    }

    public TFCombo cboSubgrupo = new TFCombo();

    private void init_cboSubgrupo() {
        cboSubgrupo.setName("cboSubgrupo");
        cboSubgrupo.setLeft(0);
        cboSubgrupo.setTop(14);
        cboSubgrupo.setWidth(200);
        cboSubgrupo.setHeight(21);
        cboSubgrupo.setHint("Subgrupo");
        cboSubgrupo.setTable(tbItens);
        cboSubgrupo.setLookupTable(tbItensSubGrupo);
        cboSubgrupo.setFieldName("COD_SUB_GRUPO_INTERNO");
        cboSubgrupo.setLookupKey("COD_SUB_GRUPO_INTERNO");
        cboSubgrupo.setLookupDesc("DESCRICAO");
        cboSubgrupo.setFlex(true);
        cboSubgrupo.setHelpCaption("Subgrupo");
        cboSubgrupo.setReadOnly(true);
        cboSubgrupo.setRequired(false);
        cboSubgrupo.setPrompt("Subgrupo");
        cboSubgrupo.setConstraintCheckWhen("cwImmediate");
        cboSubgrupo.setConstraintCheckType("ctExpression");
        cboSubgrupo.setConstraintFocusOnError(false);
        cboSubgrupo.setConstraintEnableUI(true);
        cboSubgrupo.setConstraintEnabled(false);
        cboSubgrupo.setConstraintFormCheck(true);
        cboSubgrupo.setClearOnDelKey(true);
        cboSubgrupo.setUseClearButton(true);
        cboSubgrupo.setHideClearButtonOnNullValue(true);
        vBoxSubgrupo.addChildren(cboSubgrupo);
        cboSubgrupo.applyProperties();
        addValidatable(cboSubgrupo);
    }

    public TFVBox vBoxFabricante = new TFVBox();

    private void init_vBoxFabricante() {
        vBoxFabricante.setName("vBoxFabricante");
        vBoxFabricante.setLeft(0);
        vBoxFabricante.setTop(92);
        vBoxFabricante.setWidth(290);
        vBoxFabricante.setHeight(45);
        vBoxFabricante.setBorderStyle("stNone");
        vBoxFabricante.setPaddingTop(0);
        vBoxFabricante.setPaddingLeft(0);
        vBoxFabricante.setPaddingRight(0);
        vBoxFabricante.setPaddingBottom(0);
        vBoxFabricante.setMarginTop(0);
        vBoxFabricante.setMarginLeft(0);
        vBoxFabricante.setMarginRight(0);
        vBoxFabricante.setMarginBottom(0);
        vBoxFabricante.setSpacing(1);
        vBoxFabricante.setFlexVflex("ftMin");
        vBoxFabricante.setFlexHflex("ftTrue");
        vBoxFabricante.setScrollable(false);
        vBoxFabricante.setBoxShadowConfigHorizontalLength(10);
        vBoxFabricante.setBoxShadowConfigVerticalLength(10);
        vBoxFabricante.setBoxShadowConfigBlurRadius(5);
        vBoxFabricante.setBoxShadowConfigSpreadRadius(0);
        vBoxFabricante.setBoxShadowConfigShadowColor("clBlack");
        vBoxFabricante.setBoxShadowConfigOpacity(75);
        vBoxColuna01.addChildren(vBoxFabricante);
        vBoxFabricante.applyProperties();
    }

    public TFLabel lblFabricante = new TFLabel();

    private void init_lblFabricante() {
        lblFabricante.setName("lblFabricante");
        lblFabricante.setLeft(0);
        lblFabricante.setTop(0);
        lblFabricante.setWidth(51);
        lblFabricante.setHeight(13);
        lblFabricante.setCaption("Fabricante");
        lblFabricante.setFontColor("clWindowText");
        lblFabricante.setFontSize(-11);
        lblFabricante.setFontName("Tahoma");
        lblFabricante.setFontStyle("[]");
        lblFabricante.setVerticalAlignment("taVerticalCenter");
        lblFabricante.setWordBreak(false);
        vBoxFabricante.addChildren(lblFabricante);
        lblFabricante.applyProperties();
    }

    public TFCombo cboFabricante = new TFCombo();

    private void init_cboFabricante() {
        cboFabricante.setName("cboFabricante");
        cboFabricante.setLeft(0);
        cboFabricante.setTop(14);
        cboFabricante.setWidth(200);
        cboFabricante.setHeight(21);
        cboFabricante.setHint("Fabricante");
        cboFabricante.setTable(tbItensFornecedor);
        cboFabricante.setLookupTable(tbFabricante);
        cboFabricante.setFieldName("COD_FABRICANTE");
        cboFabricante.setLookupKey("COD_FABRICANTE");
        cboFabricante.setLookupDesc("NOME");
        cboFabricante.setFlex(true);
        cboFabricante.setHelpCaption("Fabricante");
        cboFabricante.setReadOnly(true);
        cboFabricante.setRequired(false);
        cboFabricante.setPrompt("Fabricante");
        cboFabricante.setConstraintCheckWhen("cwImmediate");
        cboFabricante.setConstraintCheckType("ctExpression");
        cboFabricante.setConstraintFocusOnError(false);
        cboFabricante.setConstraintEnableUI(true);
        cboFabricante.setConstraintEnabled(false);
        cboFabricante.setConstraintFormCheck(true);
        cboFabricante.setClearOnDelKey(true);
        cboFabricante.setUseClearButton(true);
        cboFabricante.setHideClearButtonOnNullValue(true);
        vBoxFabricante.addChildren(cboFabricante);
        cboFabricante.applyProperties();
        addValidatable(cboFabricante);
    }

    public TFVBox vBoxMarca = new TFVBox();

    private void init_vBoxMarca() {
        vBoxMarca.setName("vBoxMarca");
        vBoxMarca.setLeft(0);
        vBoxMarca.setTop(138);
        vBoxMarca.setWidth(290);
        vBoxMarca.setHeight(45);
        vBoxMarca.setBorderStyle("stNone");
        vBoxMarca.setPaddingTop(0);
        vBoxMarca.setPaddingLeft(0);
        vBoxMarca.setPaddingRight(0);
        vBoxMarca.setPaddingBottom(0);
        vBoxMarca.setMarginTop(0);
        vBoxMarca.setMarginLeft(0);
        vBoxMarca.setMarginRight(0);
        vBoxMarca.setMarginBottom(0);
        vBoxMarca.setSpacing(1);
        vBoxMarca.setFlexVflex("ftMin");
        vBoxMarca.setFlexHflex("ftTrue");
        vBoxMarca.setScrollable(false);
        vBoxMarca.setBoxShadowConfigHorizontalLength(10);
        vBoxMarca.setBoxShadowConfigVerticalLength(10);
        vBoxMarca.setBoxShadowConfigBlurRadius(5);
        vBoxMarca.setBoxShadowConfigSpreadRadius(0);
        vBoxMarca.setBoxShadowConfigShadowColor("clBlack");
        vBoxMarca.setBoxShadowConfigOpacity(75);
        vBoxColuna01.addChildren(vBoxMarca);
        vBoxMarca.applyProperties();
    }

    public TFLabel lblMarca = new TFLabel();

    private void init_lblMarca() {
        lblMarca.setName("lblMarca");
        lblMarca.setLeft(0);
        lblMarca.setTop(0);
        lblMarca.setWidth(29);
        lblMarca.setHeight(13);
        lblMarca.setCaption("Marca");
        lblMarca.setFontColor("clWindowText");
        lblMarca.setFontSize(-11);
        lblMarca.setFontName("Tahoma");
        lblMarca.setFontStyle("[]");
        lblMarca.setVerticalAlignment("taVerticalCenter");
        lblMarca.setWordBreak(false);
        vBoxMarca.addChildren(lblMarca);
        lblMarca.applyProperties();
    }

    public TFCombo cboMarca = new TFCombo();

    private void init_cboMarca() {
        cboMarca.setName("cboMarca");
        cboMarca.setLeft(0);
        cboMarca.setTop(14);
        cboMarca.setWidth(200);
        cboMarca.setHeight(21);
        cboMarca.setHint("Marca");
        cboMarca.setTable(tbItensFornecedor);
        cboMarca.setLookupTable(tbMarca);
        cboMarca.setFieldName("COD_MARCA");
        cboMarca.setLookupKey("COD_MARCA");
        cboMarca.setLookupDesc("DESCRICAO");
        cboMarca.setFlex(true);
        cboMarca.setHelpCaption("Marca");
        cboMarca.setReadOnly(true);
        cboMarca.setRequired(false);
        cboMarca.setPrompt("Marca");
        cboMarca.setConstraintCheckWhen("cwImmediate");
        cboMarca.setConstraintCheckType("ctExpression");
        cboMarca.setConstraintFocusOnError(false);
        cboMarca.setConstraintEnableUI(true);
        cboMarca.setConstraintEnabled(false);
        cboMarca.setConstraintFormCheck(true);
        cboMarca.setClearOnDelKey(true);
        cboMarca.setUseClearButton(true);
        cboMarca.setHideClearButtonOnNullValue(true);
        vBoxMarca.addChildren(cboMarca);
        cboMarca.applyProperties();
        addValidatable(cboMarca);
    }

    public TFVBox vBoxSituacaoEspecial = new TFVBox();

    private void init_vBoxSituacaoEspecial() {
        vBoxSituacaoEspecial.setName("vBoxSituacaoEspecial");
        vBoxSituacaoEspecial.setLeft(0);
        vBoxSituacaoEspecial.setTop(184);
        vBoxSituacaoEspecial.setWidth(290);
        vBoxSituacaoEspecial.setHeight(45);
        vBoxSituacaoEspecial.setBorderStyle("stNone");
        vBoxSituacaoEspecial.setPaddingTop(0);
        vBoxSituacaoEspecial.setPaddingLeft(0);
        vBoxSituacaoEspecial.setPaddingRight(0);
        vBoxSituacaoEspecial.setPaddingBottom(0);
        vBoxSituacaoEspecial.setMarginTop(0);
        vBoxSituacaoEspecial.setMarginLeft(0);
        vBoxSituacaoEspecial.setMarginRight(0);
        vBoxSituacaoEspecial.setMarginBottom(0);
        vBoxSituacaoEspecial.setSpacing(1);
        vBoxSituacaoEspecial.setFlexVflex("ftMin");
        vBoxSituacaoEspecial.setFlexHflex("ftTrue");
        vBoxSituacaoEspecial.setScrollable(false);
        vBoxSituacaoEspecial.setBoxShadowConfigHorizontalLength(10);
        vBoxSituacaoEspecial.setBoxShadowConfigVerticalLength(10);
        vBoxSituacaoEspecial.setBoxShadowConfigBlurRadius(5);
        vBoxSituacaoEspecial.setBoxShadowConfigSpreadRadius(0);
        vBoxSituacaoEspecial.setBoxShadowConfigShadowColor("clBlack");
        vBoxSituacaoEspecial.setBoxShadowConfigOpacity(75);
        vBoxColuna01.addChildren(vBoxSituacaoEspecial);
        vBoxSituacaoEspecial.applyProperties();
    }

    public TFLabel lblSituacaoEspecial = new TFLabel();

    private void init_lblSituacaoEspecial() {
        lblSituacaoEspecial.setName("lblSituacaoEspecial");
        lblSituacaoEspecial.setLeft(0);
        lblSituacaoEspecial.setTop(0);
        lblSituacaoEspecial.setWidth(82);
        lblSituacaoEspecial.setHeight(13);
        lblSituacaoEspecial.setCaption("Situa\u00E7\u00E3o Especial");
        lblSituacaoEspecial.setFontColor("clWindowText");
        lblSituacaoEspecial.setFontSize(-11);
        lblSituacaoEspecial.setFontName("Tahoma");
        lblSituacaoEspecial.setFontStyle("[]");
        lblSituacaoEspecial.setVerticalAlignment("taVerticalCenter");
        lblSituacaoEspecial.setWordBreak(false);
        vBoxSituacaoEspecial.addChildren(lblSituacaoEspecial);
        lblSituacaoEspecial.applyProperties();
    }

    public TFCombo cboSituacaoEspecial = new TFCombo();

    private void init_cboSituacaoEspecial() {
        cboSituacaoEspecial.setName("cboSituacaoEspecial");
        cboSituacaoEspecial.setLeft(0);
        cboSituacaoEspecial.setTop(14);
        cboSituacaoEspecial.setWidth(200);
        cboSituacaoEspecial.setHeight(21);
        cboSituacaoEspecial.setHint("Situa\u00E7\u00E3o Especial");
        cboSituacaoEspecial.setTable(tbItensFornecedor);
        cboSituacaoEspecial.setLookupTable(tbItemSituacaoEspecial);
        cboSituacaoEspecial.setFieldName("ID_SITUACAO_ESPECIAL");
        cboSituacaoEspecial.setLookupKey("ID_SITUACAO_ESPECIAL");
        cboSituacaoEspecial.setLookupDesc("DESCRICAO");
        cboSituacaoEspecial.setFlex(true);
        cboSituacaoEspecial.setHelpCaption("Situa\u00E7\u00E3o Especial");
        cboSituacaoEspecial.setReadOnly(true);
        cboSituacaoEspecial.setRequired(false);
        cboSituacaoEspecial.setPrompt("Situa\u00E7\u00E3o Especial");
        cboSituacaoEspecial.setConstraintCheckWhen("cwImmediate");
        cboSituacaoEspecial.setConstraintCheckType("ctExpression");
        cboSituacaoEspecial.setConstraintFocusOnError(false);
        cboSituacaoEspecial.setConstraintEnableUI(true);
        cboSituacaoEspecial.setConstraintEnabled(false);
        cboSituacaoEspecial.setConstraintFormCheck(true);
        cboSituacaoEspecial.setClearOnDelKey(true);
        cboSituacaoEspecial.setUseClearButton(true);
        cboSituacaoEspecial.setHideClearButtonOnNullValue(true);
        vBoxSituacaoEspecial.addChildren(cboSituacaoEspecial);
        cboSituacaoEspecial.applyProperties();
        addValidatable(cboSituacaoEspecial);
    }

    public TFVBox vBoxLetraDesconto = new TFVBox();

    private void init_vBoxLetraDesconto() {
        vBoxLetraDesconto.setName("vBoxLetraDesconto");
        vBoxLetraDesconto.setLeft(0);
        vBoxLetraDesconto.setTop(230);
        vBoxLetraDesconto.setWidth(290);
        vBoxLetraDesconto.setHeight(45);
        vBoxLetraDesconto.setBorderStyle("stNone");
        vBoxLetraDesconto.setPaddingTop(0);
        vBoxLetraDesconto.setPaddingLeft(0);
        vBoxLetraDesconto.setPaddingRight(0);
        vBoxLetraDesconto.setPaddingBottom(0);
        vBoxLetraDesconto.setMarginTop(0);
        vBoxLetraDesconto.setMarginLeft(0);
        vBoxLetraDesconto.setMarginRight(0);
        vBoxLetraDesconto.setMarginBottom(0);
        vBoxLetraDesconto.setSpacing(1);
        vBoxLetraDesconto.setFlexVflex("ftMin");
        vBoxLetraDesconto.setFlexHflex("ftTrue");
        vBoxLetraDesconto.setScrollable(false);
        vBoxLetraDesconto.setBoxShadowConfigHorizontalLength(10);
        vBoxLetraDesconto.setBoxShadowConfigVerticalLength(10);
        vBoxLetraDesconto.setBoxShadowConfigBlurRadius(5);
        vBoxLetraDesconto.setBoxShadowConfigSpreadRadius(0);
        vBoxLetraDesconto.setBoxShadowConfigShadowColor("clBlack");
        vBoxLetraDesconto.setBoxShadowConfigOpacity(75);
        vBoxColuna01.addChildren(vBoxLetraDesconto);
        vBoxLetraDesconto.applyProperties();
    }

    public TFLabel lblLetraDesconto = new TFLabel();

    private void init_lblLetraDesconto() {
        lblLetraDesconto.setName("lblLetraDesconto");
        lblLetraDesconto.setLeft(0);
        lblLetraDesconto.setTop(0);
        lblLetraDesconto.setWidth(88);
        lblLetraDesconto.setHeight(13);
        lblLetraDesconto.setCaption("Letra de Desconto");
        lblLetraDesconto.setFontColor("clWindowText");
        lblLetraDesconto.setFontSize(-11);
        lblLetraDesconto.setFontName("Tahoma");
        lblLetraDesconto.setFontStyle("[]");
        lblLetraDesconto.setVerticalAlignment("taVerticalCenter");
        lblLetraDesconto.setWordBreak(false);
        vBoxLetraDesconto.addChildren(lblLetraDesconto);
        lblLetraDesconto.applyProperties();
    }

    public TFCombo cboLetraDesconto = new TFCombo();

    private void init_cboLetraDesconto() {
        cboLetraDesconto.setName("cboLetraDesconto");
        cboLetraDesconto.setLeft(0);
        cboLetraDesconto.setTop(14);
        cboLetraDesconto.setWidth(200);
        cboLetraDesconto.setHeight(21);
        cboLetraDesconto.setHint("Letra de Desconto");
        cboLetraDesconto.setTable(tbItens);
        cboLetraDesconto.setLookupTable(tbListaLetraDescontoGeral);
        cboLetraDesconto.setFieldName("COD_MAX_DESC");
        cboLetraDesconto.setLookupKey("LETRA_CODIGO");
        cboLetraDesconto.setLookupDesc("LETRA_CODIGO");
        cboLetraDesconto.setFlex(true);
        cboLetraDesconto.setHelpCaption("Letra de Desconto");
        cboLetraDesconto.setReadOnly(true);
        cboLetraDesconto.setRequired(false);
        cboLetraDesconto.setPrompt("Letra de Desconto");
        cboLetraDesconto.setConstraintCheckWhen("cwImmediate");
        cboLetraDesconto.setConstraintCheckType("ctExpression");
        cboLetraDesconto.setConstraintFocusOnError(false);
        cboLetraDesconto.setConstraintEnableUI(true);
        cboLetraDesconto.setConstraintEnabled(false);
        cboLetraDesconto.setConstraintFormCheck(true);
        cboLetraDesconto.setClearOnDelKey(false);
        cboLetraDesconto.setUseClearButton(false);
        cboLetraDesconto.setHideClearButtonOnNullValue(true);
        vBoxLetraDesconto.addChildren(cboLetraDesconto);
        cboLetraDesconto.applyProperties();
        addValidatable(cboLetraDesconto);
    }

    public TFVBox vBoxBloquearItemCompra = new TFVBox();

    private void init_vBoxBloquearItemCompra() {
        vBoxBloquearItemCompra.setName("vBoxBloquearItemCompra");
        vBoxBloquearItemCompra.setLeft(0);
        vBoxBloquearItemCompra.setTop(276);
        vBoxBloquearItemCompra.setWidth(290);
        vBoxBloquearItemCompra.setHeight(50);
        vBoxBloquearItemCompra.setBorderStyle("stNone");
        vBoxBloquearItemCompra.setPaddingTop(0);
        vBoxBloquearItemCompra.setPaddingLeft(0);
        vBoxBloquearItemCompra.setPaddingRight(0);
        vBoxBloquearItemCompra.setPaddingBottom(0);
        vBoxBloquearItemCompra.setMarginTop(0);
        vBoxBloquearItemCompra.setMarginLeft(0);
        vBoxBloquearItemCompra.setMarginRight(0);
        vBoxBloquearItemCompra.setMarginBottom(0);
        vBoxBloquearItemCompra.setSpacing(1);
        vBoxBloquearItemCompra.setFlexVflex("ftMin");
        vBoxBloquearItemCompra.setFlexHflex("ftTrue");
        vBoxBloquearItemCompra.setScrollable(false);
        vBoxBloquearItemCompra.setBoxShadowConfigHorizontalLength(10);
        vBoxBloquearItemCompra.setBoxShadowConfigVerticalLength(10);
        vBoxBloquearItemCompra.setBoxShadowConfigBlurRadius(5);
        vBoxBloquearItemCompra.setBoxShadowConfigSpreadRadius(0);
        vBoxBloquearItemCompra.setBoxShadowConfigShadowColor("clBlack");
        vBoxBloquearItemCompra.setBoxShadowConfigOpacity(75);
        vBoxColuna01.addChildren(vBoxBloquearItemCompra);
        vBoxBloquearItemCompra.applyProperties();
    }

    public TFLabel lblStatus = new TFLabel();

    private void init_lblStatus() {
        lblStatus.setName("lblStatus");
        lblStatus.setLeft(0);
        lblStatus.setTop(0);
        lblStatus.setWidth(31);
        lblStatus.setHeight(13);
        lblStatus.setCaption("Status");
        lblStatus.setFontColor("clWindowText");
        lblStatus.setFontSize(-11);
        lblStatus.setFontName("Tahoma");
        lblStatus.setFontStyle("[]");
        lblStatus.setVerticalAlignment("taVerticalCenter");
        lblStatus.setWordBreak(false);
        vBoxBloquearItemCompra.addChildren(lblStatus);
        lblStatus.applyProperties();
    }

    public TFString edtStatus = new TFString();

    private void init_edtStatus() {
        edtStatus.setName("edtStatus");
        edtStatus.setLeft(0);
        edtStatus.setTop(14);
        edtStatus.setWidth(200);
        edtStatus.setHeight(24);
        edtStatus.setHint("Status");
        edtStatus.setHelpCaption("Status");
        edtStatus.setFlex(true);
        edtStatus.setRequired(false);
        edtStatus.setPrompt("Status");
        edtStatus.setConstraintCheckWhen("cwImmediate");
        edtStatus.setConstraintCheckType("ctExpression");
        edtStatus.setConstraintFocusOnError(false);
        edtStatus.setConstraintEnableUI(true);
        edtStatus.setConstraintEnabled(false);
        edtStatus.setConstraintFormCheck(true);
        edtStatus.setCharCase("ccNormal");
        edtStatus.setPwd(false);
        edtStatus.setMaxlength(0);
        edtStatus.setEnabled(false);
        edtStatus.setFontColor("clWindowText");
        edtStatus.setFontSize(-13);
        edtStatus.setFontName("Tahoma");
        edtStatus.setFontStyle("[]");
        edtStatus.setSaveLiteralCharacter(false);
        edtStatus.applyProperties();
        vBoxBloquearItemCompra.addChildren(edtStatus);
        addValidatable(edtStatus);
    }

    public TFVBox vBoxColuna02 = new TFVBox();

    private void init_vBoxColuna02() {
        vBoxColuna02.setName("vBoxColuna02");
        vBoxColuna02.setLeft(300);
        vBoxColuna02.setTop(0);
        vBoxColuna02.setWidth(300);
        vBoxColuna02.setHeight(340);
        vBoxColuna02.setBorderStyle("stNone");
        vBoxColuna02.setPaddingTop(0);
        vBoxColuna02.setPaddingLeft(0);
        vBoxColuna02.setPaddingRight(0);
        vBoxColuna02.setPaddingBottom(0);
        vBoxColuna02.setMarginTop(0);
        vBoxColuna02.setMarginLeft(0);
        vBoxColuna02.setMarginRight(0);
        vBoxColuna02.setMarginBottom(0);
        vBoxColuna02.setSpacing(1);
        vBoxColuna02.setFlexVflex("ftMin");
        vBoxColuna02.setFlexHflex("ftTrue");
        vBoxColuna02.setScrollable(false);
        vBoxColuna02.setBoxShadowConfigHorizontalLength(10);
        vBoxColuna02.setBoxShadowConfigVerticalLength(10);
        vBoxColuna02.setBoxShadowConfigBlurRadius(5);
        vBoxColuna02.setBoxShadowConfigSpreadRadius(0);
        vBoxColuna02.setBoxShadowConfigShadowColor("clBlack");
        vBoxColuna02.setBoxShadowConfigOpacity(75);
        hBoxCampos.addChildren(vBoxColuna02);
        vBoxColuna02.applyProperties();
    }

    public TFVBox vBoxCodigoGTIN = new TFVBox();

    private void init_vBoxCodigoGTIN() {
        vBoxCodigoGTIN.setName("vBoxCodigoGTIN");
        vBoxCodigoGTIN.setLeft(0);
        vBoxCodigoGTIN.setTop(0);
        vBoxCodigoGTIN.setWidth(290);
        vBoxCodigoGTIN.setHeight(45);
        vBoxCodigoGTIN.setBorderStyle("stNone");
        vBoxCodigoGTIN.setPaddingTop(0);
        vBoxCodigoGTIN.setPaddingLeft(0);
        vBoxCodigoGTIN.setPaddingRight(0);
        vBoxCodigoGTIN.setPaddingBottom(0);
        vBoxCodigoGTIN.setMarginTop(0);
        vBoxCodigoGTIN.setMarginLeft(0);
        vBoxCodigoGTIN.setMarginRight(0);
        vBoxCodigoGTIN.setMarginBottom(0);
        vBoxCodigoGTIN.setSpacing(1);
        vBoxCodigoGTIN.setFlexVflex("ftMin");
        vBoxCodigoGTIN.setFlexHflex("ftTrue");
        vBoxCodigoGTIN.setScrollable(false);
        vBoxCodigoGTIN.setBoxShadowConfigHorizontalLength(10);
        vBoxCodigoGTIN.setBoxShadowConfigVerticalLength(10);
        vBoxCodigoGTIN.setBoxShadowConfigBlurRadius(5);
        vBoxCodigoGTIN.setBoxShadowConfigSpreadRadius(0);
        vBoxCodigoGTIN.setBoxShadowConfigShadowColor("clBlack");
        vBoxCodigoGTIN.setBoxShadowConfigOpacity(75);
        vBoxColuna02.addChildren(vBoxCodigoGTIN);
        vBoxCodigoGTIN.applyProperties();
    }

    public TFLabel lblCodigoGTIN = new TFLabel();

    private void init_lblCodigoGTIN() {
        lblCodigoGTIN.setName("lblCodigoGTIN");
        lblCodigoGTIN.setLeft(0);
        lblCodigoGTIN.setTop(0);
        lblCodigoGTIN.setWidth(60);
        lblCodigoGTIN.setHeight(13);
        lblCodigoGTIN.setHint("C\u00F3digo GTIN (Global Trade Item Number)");
        lblCodigoGTIN.setCaption("C\u00F3digo GTIN");
        lblCodigoGTIN.setFontColor("clWindowText");
        lblCodigoGTIN.setFontSize(-11);
        lblCodigoGTIN.setFontName("Tahoma");
        lblCodigoGTIN.setFontStyle("[]");
        lblCodigoGTIN.setVerticalAlignment("taVerticalCenter");
        lblCodigoGTIN.setWordBreak(false);
        vBoxCodigoGTIN.addChildren(lblCodigoGTIN);
        lblCodigoGTIN.applyProperties();
    }

    public TFString edtStringCodigoGTIN = new TFString();

    private void init_edtStringCodigoGTIN() {
        edtStringCodigoGTIN.setName("edtStringCodigoGTIN");
        edtStringCodigoGTIN.setLeft(0);
        edtStringCodigoGTIN.setTop(14);
        edtStringCodigoGTIN.setWidth(200);
        edtStringCodigoGTIN.setHeight(24);
        edtStringCodigoGTIN.setHint("C\u00F3digo GTIN");
        edtStringCodigoGTIN.setTable(tbItensFornecedor);
        edtStringCodigoGTIN.setFieldName("COD_GTIN");
        edtStringCodigoGTIN.setHelpCaption("C\u00F3digo GTIN");
        edtStringCodigoGTIN.setFlex(true);
        edtStringCodigoGTIN.setRequired(false);
        edtStringCodigoGTIN.setPrompt("C\u00F3digo GTIN");
        edtStringCodigoGTIN.setConstraintCheckWhen("cwImmediate");
        edtStringCodigoGTIN.setConstraintCheckType("ctExpression");
        edtStringCodigoGTIN.setConstraintFocusOnError(false);
        edtStringCodigoGTIN.setConstraintEnableUI(true);
        edtStringCodigoGTIN.setConstraintEnabled(false);
        edtStringCodigoGTIN.setConstraintFormCheck(true);
        edtStringCodigoGTIN.setCharCase("ccUpper");
        edtStringCodigoGTIN.setPwd(false);
        edtStringCodigoGTIN.setMaxlength(14);
        edtStringCodigoGTIN.setFontColor("clWindowText");
        edtStringCodigoGTIN.setFontSize(-13);
        edtStringCodigoGTIN.setFontName("Tahoma");
        edtStringCodigoGTIN.setFontStyle("[]");
        edtStringCodigoGTIN.setSaveLiteralCharacter(false);
        edtStringCodigoGTIN.applyProperties();
        vBoxCodigoGTIN.addChildren(edtStringCodigoGTIN);
        addValidatable(edtStringCodigoGTIN);
    }

    public TFVBox vBoxCodigoBarras = new TFVBox();

    private void init_vBoxCodigoBarras() {
        vBoxCodigoBarras.setName("vBoxCodigoBarras");
        vBoxCodigoBarras.setLeft(0);
        vBoxCodigoBarras.setTop(46);
        vBoxCodigoBarras.setWidth(290);
        vBoxCodigoBarras.setHeight(45);
        vBoxCodigoBarras.setBorderStyle("stNone");
        vBoxCodigoBarras.setPaddingTop(0);
        vBoxCodigoBarras.setPaddingLeft(0);
        vBoxCodigoBarras.setPaddingRight(0);
        vBoxCodigoBarras.setPaddingBottom(0);
        vBoxCodigoBarras.setMarginTop(0);
        vBoxCodigoBarras.setMarginLeft(0);
        vBoxCodigoBarras.setMarginRight(0);
        vBoxCodigoBarras.setMarginBottom(0);
        vBoxCodigoBarras.setSpacing(1);
        vBoxCodigoBarras.setFlexVflex("ftMin");
        vBoxCodigoBarras.setFlexHflex("ftTrue");
        vBoxCodigoBarras.setScrollable(false);
        vBoxCodigoBarras.setBoxShadowConfigHorizontalLength(10);
        vBoxCodigoBarras.setBoxShadowConfigVerticalLength(10);
        vBoxCodigoBarras.setBoxShadowConfigBlurRadius(5);
        vBoxCodigoBarras.setBoxShadowConfigSpreadRadius(0);
        vBoxCodigoBarras.setBoxShadowConfigShadowColor("clBlack");
        vBoxCodigoBarras.setBoxShadowConfigOpacity(75);
        vBoxColuna02.addChildren(vBoxCodigoBarras);
        vBoxCodigoBarras.applyProperties();
    }

    public TFLabel lblCodigoBarras = new TFLabel();

    private void init_lblCodigoBarras() {
        lblCodigoBarras.setName("lblCodigoBarras");
        lblCodigoBarras.setLeft(0);
        lblCodigoBarras.setTop(0);
        lblCodigoBarras.setWidth(82);
        lblCodigoBarras.setHeight(13);
        lblCodigoBarras.setCaption("C\u00F3digo de Barras");
        lblCodigoBarras.setFontColor("clWindowText");
        lblCodigoBarras.setFontSize(-11);
        lblCodigoBarras.setFontName("Tahoma");
        lblCodigoBarras.setFontStyle("[]");
        lblCodigoBarras.setVerticalAlignment("taVerticalCenter");
        lblCodigoBarras.setWordBreak(false);
        vBoxCodigoBarras.addChildren(lblCodigoBarras);
        lblCodigoBarras.applyProperties();
    }

    public TFString edtStringCodigoBarras = new TFString();

    private void init_edtStringCodigoBarras() {
        edtStringCodigoBarras.setName("edtStringCodigoBarras");
        edtStringCodigoBarras.setLeft(0);
        edtStringCodigoBarras.setTop(14);
        edtStringCodigoBarras.setWidth(200);
        edtStringCodigoBarras.setHeight(24);
        edtStringCodigoBarras.setHint("C\u00F3digo de Barras");
        edtStringCodigoBarras.setTable(tbItensFornecedor);
        edtStringCodigoBarras.setFieldName("COD_BARRAS");
        edtStringCodigoBarras.setHelpCaption("C\u00F3digo de Barras");
        edtStringCodigoBarras.setFlex(true);
        edtStringCodigoBarras.setRequired(false);
        edtStringCodigoBarras.setPrompt("C\u00F3digo de Barras");
        edtStringCodigoBarras.setConstraintCheckWhen("cwImmediate");
        edtStringCodigoBarras.setConstraintCheckType("ctExpression");
        edtStringCodigoBarras.setConstraintFocusOnError(false);
        edtStringCodigoBarras.setConstraintEnableUI(true);
        edtStringCodigoBarras.setConstraintEnabled(false);
        edtStringCodigoBarras.setConstraintFormCheck(true);
        edtStringCodigoBarras.setCharCase("ccUpper");
        edtStringCodigoBarras.setPwd(false);
        edtStringCodigoBarras.setMaxlength(0);
        edtStringCodigoBarras.setFontColor("clWindowText");
        edtStringCodigoBarras.setFontSize(-13);
        edtStringCodigoBarras.setFontName("Tahoma");
        edtStringCodigoBarras.setFontStyle("[]");
        edtStringCodigoBarras.setSaveLiteralCharacter(false);
        edtStringCodigoBarras.applyProperties();
        vBoxCodigoBarras.addChildren(edtStringCodigoBarras);
        addValidatable(edtStringCodigoBarras);
    }

    public TFVBox vBoxTipoPeca = new TFVBox();

    private void init_vBoxTipoPeca() {
        vBoxTipoPeca.setName("vBoxTipoPeca");
        vBoxTipoPeca.setLeft(0);
        vBoxTipoPeca.setTop(92);
        vBoxTipoPeca.setWidth(290);
        vBoxTipoPeca.setHeight(45);
        vBoxTipoPeca.setBorderStyle("stNone");
        vBoxTipoPeca.setPaddingTop(0);
        vBoxTipoPeca.setPaddingLeft(0);
        vBoxTipoPeca.setPaddingRight(0);
        vBoxTipoPeca.setPaddingBottom(0);
        vBoxTipoPeca.setMarginTop(0);
        vBoxTipoPeca.setMarginLeft(0);
        vBoxTipoPeca.setMarginRight(0);
        vBoxTipoPeca.setMarginBottom(0);
        vBoxTipoPeca.setSpacing(1);
        vBoxTipoPeca.setFlexVflex("ftMin");
        vBoxTipoPeca.setFlexHflex("ftTrue");
        vBoxTipoPeca.setScrollable(false);
        vBoxTipoPeca.setBoxShadowConfigHorizontalLength(10);
        vBoxTipoPeca.setBoxShadowConfigVerticalLength(10);
        vBoxTipoPeca.setBoxShadowConfigBlurRadius(5);
        vBoxTipoPeca.setBoxShadowConfigSpreadRadius(0);
        vBoxTipoPeca.setBoxShadowConfigShadowColor("clBlack");
        vBoxTipoPeca.setBoxShadowConfigOpacity(75);
        vBoxColuna02.addChildren(vBoxTipoPeca);
        vBoxTipoPeca.applyProperties();
    }

    public TFLabel lblTipoPeca = new TFLabel();

    private void init_lblTipoPeca() {
        lblTipoPeca.setName("lblTipoPeca");
        lblTipoPeca.setLeft(0);
        lblTipoPeca.setTop(0);
        lblTipoPeca.setWidth(49);
        lblTipoPeca.setHeight(13);
        lblTipoPeca.setCaption("Tipo Pe\u00E7a ");
        lblTipoPeca.setFontColor("clWindowText");
        lblTipoPeca.setFontSize(-11);
        lblTipoPeca.setFontName("Tahoma");
        lblTipoPeca.setFontStyle("[]");
        lblTipoPeca.setVerticalAlignment("taVerticalCenter");
        lblTipoPeca.setWordBreak(false);
        vBoxTipoPeca.addChildren(lblTipoPeca);
        lblTipoPeca.applyProperties();
    }

    public TFCombo cboTipoPeca = new TFCombo();

    private void init_cboTipoPeca() {
        cboTipoPeca.setName("cboTipoPeca");
        cboTipoPeca.setLeft(0);
        cboTipoPeca.setTop(14);
        cboTipoPeca.setWidth(200);
        cboTipoPeca.setHeight(21);
        cboTipoPeca.setHint("Tipo Pe\u00E7a");
        cboTipoPeca.setTable(tbItensFornecedor);
        cboTipoPeca.setFieldName("TIPO_PECA");
        cboTipoPeca.setFlex(true);
        cboTipoPeca.setListOptions("C\u00E2mbio=C;Motor=M");
        cboTipoPeca.setHelpCaption("Tipo Pe\u00E7a");
        cboTipoPeca.setReadOnly(true);
        cboTipoPeca.setRequired(false);
        cboTipoPeca.setPrompt("Tipo Pe\u00E7a");
        cboTipoPeca.setConstraintCheckWhen("cwImmediate");
        cboTipoPeca.setConstraintCheckType("ctExpression");
        cboTipoPeca.setConstraintFocusOnError(false);
        cboTipoPeca.setConstraintEnableUI(true);
        cboTipoPeca.setConstraintEnabled(false);
        cboTipoPeca.setConstraintFormCheck(true);
        cboTipoPeca.setClearOnDelKey(true);
        cboTipoPeca.setUseClearButton(true);
        cboTipoPeca.setHideClearButtonOnNullValue(false);
        vBoxTipoPeca.addChildren(cboTipoPeca);
        cboTipoPeca.applyProperties();
        addValidatable(cboTipoPeca);
    }

    public TFVBox vBoxEstocagemReservaDias = new TFVBox();

    private void init_vBoxEstocagemReservaDias() {
        vBoxEstocagemReservaDias.setName("vBoxEstocagemReservaDias");
        vBoxEstocagemReservaDias.setLeft(0);
        vBoxEstocagemReservaDias.setTop(138);
        vBoxEstocagemReservaDias.setWidth(290);
        vBoxEstocagemReservaDias.setHeight(45);
        vBoxEstocagemReservaDias.setBorderStyle("stNone");
        vBoxEstocagemReservaDias.setPaddingTop(0);
        vBoxEstocagemReservaDias.setPaddingLeft(0);
        vBoxEstocagemReservaDias.setPaddingRight(0);
        vBoxEstocagemReservaDias.setPaddingBottom(0);
        vBoxEstocagemReservaDias.setMarginTop(0);
        vBoxEstocagemReservaDias.setMarginLeft(0);
        vBoxEstocagemReservaDias.setMarginRight(0);
        vBoxEstocagemReservaDias.setMarginBottom(0);
        vBoxEstocagemReservaDias.setSpacing(1);
        vBoxEstocagemReservaDias.setFlexVflex("ftMin");
        vBoxEstocagemReservaDias.setFlexHflex("ftTrue");
        vBoxEstocagemReservaDias.setScrollable(false);
        vBoxEstocagemReservaDias.setBoxShadowConfigHorizontalLength(10);
        vBoxEstocagemReservaDias.setBoxShadowConfigVerticalLength(10);
        vBoxEstocagemReservaDias.setBoxShadowConfigBlurRadius(5);
        vBoxEstocagemReservaDias.setBoxShadowConfigSpreadRadius(0);
        vBoxEstocagemReservaDias.setBoxShadowConfigShadowColor("clBlack");
        vBoxEstocagemReservaDias.setBoxShadowConfigOpacity(75);
        vBoxColuna02.addChildren(vBoxEstocagemReservaDias);
        vBoxEstocagemReservaDias.applyProperties();
    }

    public TFLabel lblEstocagemReservaDias = new TFLabel();

    private void init_lblEstocagemReservaDias() {
        lblEstocagemReservaDias.setName("lblEstocagemReservaDias");
        lblEstocagemReservaDias.setLeft(0);
        lblEstocagemReservaDias.setTop(0);
        lblEstocagemReservaDias.setWidth(122);
        lblEstocagemReservaDias.setHeight(13);
        lblEstocagemReservaDias.setCaption("Estocagem-Reserva Dias ");
        lblEstocagemReservaDias.setFontColor("clWindowText");
        lblEstocagemReservaDias.setFontSize(-11);
        lblEstocagemReservaDias.setFontName("Tahoma");
        lblEstocagemReservaDias.setFontStyle("[]");
        lblEstocagemReservaDias.setVerticalAlignment("taVerticalCenter");
        lblEstocagemReservaDias.setWordBreak(false);
        vBoxEstocagemReservaDias.addChildren(lblEstocagemReservaDias);
        lblEstocagemReservaDias.applyProperties();
    }

    public TFInteger edtIntegerReservaDias = new TFInteger();

    private void init_edtIntegerReservaDias() {
        edtIntegerReservaDias.setName("edtIntegerReservaDias");
        edtIntegerReservaDias.setLeft(0);
        edtIntegerReservaDias.setTop(14);
        edtIntegerReservaDias.setWidth(200);
        edtIntegerReservaDias.setHeight(24);
        edtIntegerReservaDias.setHint("Estocagem-Reserva Dias ");
        edtIntegerReservaDias.setTable(tbItensFornecedor);
        edtIntegerReservaDias.setFieldName("RESERVA_DIAS");
        edtIntegerReservaDias.setHelpCaption("Estocagem-Reserva Dias ");
        edtIntegerReservaDias.setFlex(true);
        edtIntegerReservaDias.setRequired(false);
        edtIntegerReservaDias.setPrompt("Estocagem-Reserva Dias ");
        edtIntegerReservaDias.setConstraintCheckWhen("cwImmediate");
        edtIntegerReservaDias.setConstraintCheckType("ctExpression");
        edtIntegerReservaDias.setConstraintFocusOnError(false);
        edtIntegerReservaDias.setConstraintEnableUI(true);
        edtIntegerReservaDias.setConstraintEnabled(false);
        edtIntegerReservaDias.setConstraintFormCheck(true);
        edtIntegerReservaDias.setMaxlength(0);
        edtIntegerReservaDias.setFontColor("clWindowText");
        edtIntegerReservaDias.setFontSize(-13);
        edtIntegerReservaDias.setFontName("Tahoma");
        edtIntegerReservaDias.setFontStyle("[]");
        edtIntegerReservaDias.setAlignment("taRightJustify");
        vBoxEstocagemReservaDias.addChildren(edtIntegerReservaDias);
        edtIntegerReservaDias.applyProperties();
        addValidatable(edtIntegerReservaDias);
    }

    public TFVBox vBoxPartNumber = new TFVBox();

    private void init_vBoxPartNumber() {
        vBoxPartNumber.setName("vBoxPartNumber");
        vBoxPartNumber.setLeft(0);
        vBoxPartNumber.setTop(184);
        vBoxPartNumber.setWidth(290);
        vBoxPartNumber.setHeight(45);
        vBoxPartNumber.setBorderStyle("stNone");
        vBoxPartNumber.setPaddingTop(0);
        vBoxPartNumber.setPaddingLeft(0);
        vBoxPartNumber.setPaddingRight(0);
        vBoxPartNumber.setPaddingBottom(0);
        vBoxPartNumber.setMarginTop(0);
        vBoxPartNumber.setMarginLeft(0);
        vBoxPartNumber.setMarginRight(0);
        vBoxPartNumber.setMarginBottom(0);
        vBoxPartNumber.setSpacing(1);
        vBoxPartNumber.setFlexVflex("ftMin");
        vBoxPartNumber.setFlexHflex("ftTrue");
        vBoxPartNumber.setScrollable(false);
        vBoxPartNumber.setBoxShadowConfigHorizontalLength(10);
        vBoxPartNumber.setBoxShadowConfigVerticalLength(10);
        vBoxPartNumber.setBoxShadowConfigBlurRadius(5);
        vBoxPartNumber.setBoxShadowConfigSpreadRadius(0);
        vBoxPartNumber.setBoxShadowConfigShadowColor("clBlack");
        vBoxPartNumber.setBoxShadowConfigOpacity(75);
        vBoxColuna02.addChildren(vBoxPartNumber);
        vBoxPartNumber.applyProperties();
    }

    public TFLabel lblPartNumber = new TFLabel();

    private void init_lblPartNumber() {
        lblPartNumber.setName("lblPartNumber");
        lblPartNumber.setLeft(0);
        lblPartNumber.setTop(0);
        lblPartNumber.setWidth(61);
        lblPartNumber.setHeight(13);
        lblPartNumber.setCaption("Part-Number");
        lblPartNumber.setFontColor("clWindowText");
        lblPartNumber.setFontSize(-11);
        lblPartNumber.setFontName("Tahoma");
        lblPartNumber.setFontStyle("[]");
        lblPartNumber.setVerticalAlignment("taVerticalCenter");
        lblPartNumber.setWordBreak(false);
        vBoxPartNumber.addChildren(lblPartNumber);
        lblPartNumber.applyProperties();
    }

    public TFString edtStringPartNumber = new TFString();

    private void init_edtStringPartNumber() {
        edtStringPartNumber.setName("edtStringPartNumber");
        edtStringPartNumber.setLeft(0);
        edtStringPartNumber.setTop(14);
        edtStringPartNumber.setWidth(200);
        edtStringPartNumber.setHeight(24);
        edtStringPartNumber.setHint("Part-Number");
        edtStringPartNumber.setTable(tbItensFornecedor);
        edtStringPartNumber.setFieldName("PART_NUMBER");
        edtStringPartNumber.setHelpCaption("Part-Number");
        edtStringPartNumber.setFlex(true);
        edtStringPartNumber.setRequired(false);
        edtStringPartNumber.setPrompt("Part-Number");
        edtStringPartNumber.setConstraintCheckWhen("cwImmediate");
        edtStringPartNumber.setConstraintCheckType("ctExpression");
        edtStringPartNumber.setConstraintFocusOnError(false);
        edtStringPartNumber.setConstraintEnableUI(true);
        edtStringPartNumber.setConstraintEnabled(false);
        edtStringPartNumber.setConstraintFormCheck(true);
        edtStringPartNumber.setCharCase("ccNormal");
        edtStringPartNumber.setPwd(false);
        edtStringPartNumber.setMaxlength(20);
        edtStringPartNumber.setFontColor("clWindowText");
        edtStringPartNumber.setFontSize(-13);
        edtStringPartNumber.setFontName("Tahoma");
        edtStringPartNumber.setFontStyle("[]");
        edtStringPartNumber.setSaveLiteralCharacter(false);
        edtStringPartNumber.applyProperties();
        vBoxPartNumber.addChildren(edtStringPartNumber);
        addValidatable(edtStringPartNumber);
    }

    public TFVBox vBoxOrigem = new TFVBox();

    private void init_vBoxOrigem() {
        vBoxOrigem.setName("vBoxOrigem");
        vBoxOrigem.setLeft(0);
        vBoxOrigem.setTop(230);
        vBoxOrigem.setWidth(290);
        vBoxOrigem.setHeight(45);
        vBoxOrigem.setBorderStyle("stNone");
        vBoxOrigem.setPaddingTop(0);
        vBoxOrigem.setPaddingLeft(0);
        vBoxOrigem.setPaddingRight(0);
        vBoxOrigem.setPaddingBottom(0);
        vBoxOrigem.setMarginTop(0);
        vBoxOrigem.setMarginLeft(0);
        vBoxOrigem.setMarginRight(0);
        vBoxOrigem.setMarginBottom(0);
        vBoxOrigem.setSpacing(1);
        vBoxOrigem.setFlexVflex("ftMin");
        vBoxOrigem.setFlexHflex("ftTrue");
        vBoxOrigem.setScrollable(false);
        vBoxOrigem.setBoxShadowConfigHorizontalLength(10);
        vBoxOrigem.setBoxShadowConfigVerticalLength(10);
        vBoxOrigem.setBoxShadowConfigBlurRadius(5);
        vBoxOrigem.setBoxShadowConfigSpreadRadius(0);
        vBoxOrigem.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrigem.setBoxShadowConfigOpacity(75);
        vBoxColuna02.addChildren(vBoxOrigem);
        vBoxOrigem.applyProperties();
    }

    public TFLabel lblOrigem = new TFLabel();

    private void init_lblOrigem() {
        lblOrigem.setName("lblOrigem");
        lblOrigem.setLeft(0);
        lblOrigem.setTop(0);
        lblOrigem.setWidth(34);
        lblOrigem.setHeight(13);
        lblOrigem.setCaption("Origem");
        lblOrigem.setFontColor("clWindowText");
        lblOrigem.setFontSize(-11);
        lblOrigem.setFontName("Tahoma");
        lblOrigem.setFontStyle("[]");
        lblOrigem.setVerticalAlignment("taVerticalCenter");
        lblOrigem.setWordBreak(false);
        vBoxOrigem.addChildren(lblOrigem);
        lblOrigem.applyProperties();
    }

    public TFCombo cboOrigem = new TFCombo();

    private void init_cboOrigem() {
        cboOrigem.setName("cboOrigem");
        cboOrigem.setLeft(0);
        cboOrigem.setTop(14);
        cboOrigem.setWidth(200);
        cboOrigem.setHeight(21);
        cboOrigem.setHint("Origem");
        cboOrigem.setTable(tbItensFornecedor);
        cboOrigem.setLookupTable(tbItensOrigem);
        cboOrigem.setFieldName("COD_ORIGEM");
        cboOrigem.setLookupKey("COD_ORIGEM");
        cboOrigem.setLookupDesc("DESCRICAO");
        cboOrigem.setFlex(true);
        cboOrigem.setHelpCaption("Origem");
        cboOrigem.setReadOnly(true);
        cboOrigem.setRequired(false);
        cboOrigem.setPrompt("Origem");
        cboOrigem.setConstraintCheckWhen("cwImmediate");
        cboOrigem.setConstraintCheckType("ctExpression");
        cboOrigem.setConstraintFocusOnError(false);
        cboOrigem.setConstraintEnableUI(true);
        cboOrigem.setConstraintEnabled(false);
        cboOrigem.setConstraintFormCheck(true);
        cboOrigem.setClearOnDelKey(false);
        cboOrigem.setUseClearButton(false);
        cboOrigem.setHideClearButtonOnNullValue(true);
        vBoxOrigem.addChildren(cboOrigem);
        cboOrigem.applyProperties();
        addValidatable(cboOrigem);
    }

    public TFVBox vBoxColuna03 = new TFVBox();

    private void init_vBoxColuna03() {
        vBoxColuna03.setName("vBoxColuna03");
        vBoxColuna03.setLeft(600);
        vBoxColuna03.setTop(0);
        vBoxColuna03.setWidth(300);
        vBoxColuna03.setHeight(340);
        vBoxColuna03.setBorderStyle("stNone");
        vBoxColuna03.setPaddingTop(0);
        vBoxColuna03.setPaddingLeft(0);
        vBoxColuna03.setPaddingRight(0);
        vBoxColuna03.setPaddingBottom(0);
        vBoxColuna03.setMarginTop(0);
        vBoxColuna03.setMarginLeft(0);
        vBoxColuna03.setMarginRight(0);
        vBoxColuna03.setMarginBottom(0);
        vBoxColuna03.setSpacing(1);
        vBoxColuna03.setFlexVflex("ftMin");
        vBoxColuna03.setFlexHflex("ftTrue");
        vBoxColuna03.setScrollable(false);
        vBoxColuna03.setBoxShadowConfigHorizontalLength(10);
        vBoxColuna03.setBoxShadowConfigVerticalLength(10);
        vBoxColuna03.setBoxShadowConfigBlurRadius(5);
        vBoxColuna03.setBoxShadowConfigSpreadRadius(0);
        vBoxColuna03.setBoxShadowConfigShadowColor("clBlack");
        vBoxColuna03.setBoxShadowConfigOpacity(75);
        hBoxCampos.addChildren(vBoxColuna03);
        vBoxColuna03.applyProperties();
    }

    public TFVBox vBoxEmbalagemUnidade = new TFVBox();

    private void init_vBoxEmbalagemUnidade() {
        vBoxEmbalagemUnidade.setName("vBoxEmbalagemUnidade");
        vBoxEmbalagemUnidade.setLeft(0);
        vBoxEmbalagemUnidade.setTop(0);
        vBoxEmbalagemUnidade.setWidth(290);
        vBoxEmbalagemUnidade.setHeight(50);
        vBoxEmbalagemUnidade.setBorderStyle("stNone");
        vBoxEmbalagemUnidade.setPaddingTop(0);
        vBoxEmbalagemUnidade.setPaddingLeft(0);
        vBoxEmbalagemUnidade.setPaddingRight(0);
        vBoxEmbalagemUnidade.setPaddingBottom(0);
        vBoxEmbalagemUnidade.setMarginTop(0);
        vBoxEmbalagemUnidade.setMarginLeft(0);
        vBoxEmbalagemUnidade.setMarginRight(0);
        vBoxEmbalagemUnidade.setMarginBottom(0);
        vBoxEmbalagemUnidade.setSpacing(1);
        vBoxEmbalagemUnidade.setFlexVflex("ftMin");
        vBoxEmbalagemUnidade.setFlexHflex("ftTrue");
        vBoxEmbalagemUnidade.setScrollable(false);
        vBoxEmbalagemUnidade.setBoxShadowConfigHorizontalLength(10);
        vBoxEmbalagemUnidade.setBoxShadowConfigVerticalLength(10);
        vBoxEmbalagemUnidade.setBoxShadowConfigBlurRadius(5);
        vBoxEmbalagemUnidade.setBoxShadowConfigSpreadRadius(0);
        vBoxEmbalagemUnidade.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmbalagemUnidade.setBoxShadowConfigOpacity(75);
        vBoxColuna03.addChildren(vBoxEmbalagemUnidade);
        vBoxEmbalagemUnidade.applyProperties();
    }

    public TFLabel lblEmbalagemUnidade = new TFLabel();

    private void init_lblEmbalagemUnidade() {
        lblEmbalagemUnidade.setName("lblEmbalagemUnidade");
        lblEmbalagemUnidade.setLeft(0);
        lblEmbalagemUnidade.setTop(0);
        lblEmbalagemUnidade.setWidth(97);
        lblEmbalagemUnidade.setHeight(13);
        lblEmbalagemUnidade.setCaption("Embalagem/Unidade");
        lblEmbalagemUnidade.setFontColor("clWindowText");
        lblEmbalagemUnidade.setFontSize(-11);
        lblEmbalagemUnidade.setFontName("Tahoma");
        lblEmbalagemUnidade.setFontStyle("[]");
        lblEmbalagemUnidade.setVerticalAlignment("taVerticalCenter");
        lblEmbalagemUnidade.setWordBreak(false);
        vBoxEmbalagemUnidade.addChildren(lblEmbalagemUnidade);
        lblEmbalagemUnidade.applyProperties();
    }

    public TFHBox hBoxEmbalagemUnidade = new TFHBox();

    private void init_hBoxEmbalagemUnidade() {
        hBoxEmbalagemUnidade.setName("hBoxEmbalagemUnidade");
        hBoxEmbalagemUnidade.setLeft(0);
        hBoxEmbalagemUnidade.setTop(14);
        hBoxEmbalagemUnidade.setWidth(280);
        hBoxEmbalagemUnidade.setHeight(30);
        hBoxEmbalagemUnidade.setBorderStyle("stNone");
        hBoxEmbalagemUnidade.setPaddingTop(0);
        hBoxEmbalagemUnidade.setPaddingLeft(0);
        hBoxEmbalagemUnidade.setPaddingRight(0);
        hBoxEmbalagemUnidade.setPaddingBottom(0);
        hBoxEmbalagemUnidade.setMarginTop(0);
        hBoxEmbalagemUnidade.setMarginLeft(0);
        hBoxEmbalagemUnidade.setMarginRight(0);
        hBoxEmbalagemUnidade.setMarginBottom(0);
        hBoxEmbalagemUnidade.setSpacing(5);
        hBoxEmbalagemUnidade.setFlexVflex("ftMin");
        hBoxEmbalagemUnidade.setFlexHflex("ftTrue");
        hBoxEmbalagemUnidade.setScrollable(false);
        hBoxEmbalagemUnidade.setBoxShadowConfigHorizontalLength(10);
        hBoxEmbalagemUnidade.setBoxShadowConfigVerticalLength(10);
        hBoxEmbalagemUnidade.setBoxShadowConfigBlurRadius(5);
        hBoxEmbalagemUnidade.setBoxShadowConfigSpreadRadius(0);
        hBoxEmbalagemUnidade.setBoxShadowConfigShadowColor("clBlack");
        hBoxEmbalagemUnidade.setBoxShadowConfigOpacity(75);
        hBoxEmbalagemUnidade.setVAlign("tvTop");
        vBoxEmbalagemUnidade.addChildren(hBoxEmbalagemUnidade);
        hBoxEmbalagemUnidade.applyProperties();
    }

    public TFSpinner edtSpinnerEmbalagem = new TFSpinner();

    private void init_edtSpinnerEmbalagem() {
        edtSpinnerEmbalagem.setName("edtSpinnerEmbalagem");
        edtSpinnerEmbalagem.setLeft(0);
        edtSpinnerEmbalagem.setTop(0);
        edtSpinnerEmbalagem.setWidth(120);
        edtSpinnerEmbalagem.setHeight(24);
        edtSpinnerEmbalagem.setHint("Embalagem");
        edtSpinnerEmbalagem.setTable(tbItens);
        edtSpinnerEmbalagem.setFieldName("EMBALAGEM");
        edtSpinnerEmbalagem.setHelpCaption("Embalagem");
        edtSpinnerEmbalagem.setFlex(false);
        edtSpinnerEmbalagem.setRequired(false);
        edtSpinnerEmbalagem.setPrompt("Embalagem");
        edtSpinnerEmbalagem.setConstraintCheckWhen("cwImmediate");
        edtSpinnerEmbalagem.setConstraintCheckType("ctExpression");
        edtSpinnerEmbalagem.setConstraintFocusOnError(false);
        edtSpinnerEmbalagem.setConstraintEnableUI(true);
        edtSpinnerEmbalagem.setConstraintEnabled(false);
        edtSpinnerEmbalagem.setConstraintFormCheck(true);
        edtSpinnerEmbalagem.setMaxlength(0);
        edtSpinnerEmbalagem.setStep(1);
        edtSpinnerEmbalagem.setButtonVisible(true);
        edtSpinnerEmbalagem.setFontColor("clWindowText");
        edtSpinnerEmbalagem.setFontSize(-13);
        edtSpinnerEmbalagem.setFontName("Tahoma");
        edtSpinnerEmbalagem.setFontStyle("[]");
        hBoxEmbalagemUnidade.addChildren(edtSpinnerEmbalagem);
        edtSpinnerEmbalagem.applyProperties();
        addValidatable(edtSpinnerEmbalagem);
    }

    public TFCombo cboUnidade = new TFCombo();

    private void init_cboUnidade() {
        cboUnidade.setName("cboUnidade");
        cboUnidade.setLeft(120);
        cboUnidade.setTop(0);
        cboUnidade.setWidth(150);
        cboUnidade.setHeight(21);
        cboUnidade.setHint("Unidade");
        cboUnidade.setTable(tbItens);
        cboUnidade.setLookupTable(tbItensUnidadeMedida);
        cboUnidade.setFieldName("UNIDADE");
        cboUnidade.setLookupKey("COD_UNIDADE");
        cboUnidade.setLookupDesc("DESCRICAO");
        cboUnidade.setFlex(true);
        cboUnidade.setHelpCaption("Unidade");
        cboUnidade.setReadOnly(true);
        cboUnidade.setRequired(false);
        cboUnidade.setPrompt("Unidade");
        cboUnidade.setConstraintCheckWhen("cwImmediate");
        cboUnidade.setConstraintCheckType("ctExpression");
        cboUnidade.setConstraintFocusOnError(false);
        cboUnidade.setConstraintEnableUI(true);
        cboUnidade.setConstraintEnabled(false);
        cboUnidade.setConstraintFormCheck(true);
        cboUnidade.setClearOnDelKey(true);
        cboUnidade.setUseClearButton(true);
        cboUnidade.setHideClearButtonOnNullValue(true);
        hBoxEmbalagemUnidade.addChildren(cboUnidade);
        cboUnidade.applyProperties();
        addValidatable(cboUnidade);
    }

    public TFVBox vBoxPesoLiquido = new TFVBox();

    private void init_vBoxPesoLiquido() {
        vBoxPesoLiquido.setName("vBoxPesoLiquido");
        vBoxPesoLiquido.setLeft(0);
        vBoxPesoLiquido.setTop(51);
        vBoxPesoLiquido.setWidth(290);
        vBoxPesoLiquido.setHeight(45);
        vBoxPesoLiquido.setBorderStyle("stNone");
        vBoxPesoLiquido.setPaddingTop(0);
        vBoxPesoLiquido.setPaddingLeft(0);
        vBoxPesoLiquido.setPaddingRight(0);
        vBoxPesoLiquido.setPaddingBottom(0);
        vBoxPesoLiquido.setMarginTop(0);
        vBoxPesoLiquido.setMarginLeft(0);
        vBoxPesoLiquido.setMarginRight(0);
        vBoxPesoLiquido.setMarginBottom(0);
        vBoxPesoLiquido.setSpacing(1);
        vBoxPesoLiquido.setFlexVflex("ftMin");
        vBoxPesoLiquido.setFlexHflex("ftTrue");
        vBoxPesoLiquido.setScrollable(false);
        vBoxPesoLiquido.setBoxShadowConfigHorizontalLength(10);
        vBoxPesoLiquido.setBoxShadowConfigVerticalLength(10);
        vBoxPesoLiquido.setBoxShadowConfigBlurRadius(5);
        vBoxPesoLiquido.setBoxShadowConfigSpreadRadius(0);
        vBoxPesoLiquido.setBoxShadowConfigShadowColor("clBlack");
        vBoxPesoLiquido.setBoxShadowConfigOpacity(75);
        vBoxColuna03.addChildren(vBoxPesoLiquido);
        vBoxPesoLiquido.applyProperties();
    }

    public TFLabel lblPesoLiquido = new TFLabel();

    private void init_lblPesoLiquido() {
        lblPesoLiquido.setName("lblPesoLiquido");
        lblPesoLiquido.setLeft(0);
        lblPesoLiquido.setTop(0);
        lblPesoLiquido.setWidth(166);
        lblPesoLiquido.setHeight(13);
        lblPesoLiquido.setCaption("Peso L\u00EDquido (Kg - Usado p/Rateio)");
        lblPesoLiquido.setFontColor("clWindowText");
        lblPesoLiquido.setFontSize(-11);
        lblPesoLiquido.setFontName("Tahoma");
        lblPesoLiquido.setFontStyle("[]");
        lblPesoLiquido.setVerticalAlignment("taVerticalCenter");
        lblPesoLiquido.setWordBreak(false);
        vBoxPesoLiquido.addChildren(lblPesoLiquido);
        lblPesoLiquido.applyProperties();
    }

    public TFDecimal edtDecimalPesoLiquido = new TFDecimal();

    private void init_edtDecimalPesoLiquido() {
        edtDecimalPesoLiquido.setName("edtDecimalPesoLiquido");
        edtDecimalPesoLiquido.setLeft(0);
        edtDecimalPesoLiquido.setTop(14);
        edtDecimalPesoLiquido.setWidth(200);
        edtDecimalPesoLiquido.setHeight(24);
        edtDecimalPesoLiquido.setHint("Peso L\u00EDquido (Kg - Usado p/Rateio)");
        edtDecimalPesoLiquido.setTable(tbItens);
        edtDecimalPesoLiquido.setFieldName("PESO_LIQUIDO");
        edtDecimalPesoLiquido.setHelpCaption("Peso L\u00EDquido (Kg - Usado p/Rateio)");
        edtDecimalPesoLiquido.setFlex(true);
        edtDecimalPesoLiquido.setRequired(false);
        edtDecimalPesoLiquido.setPrompt("Peso L\u00EDquido (Kg - Usado p/Rateio)");
        edtDecimalPesoLiquido.setConstraintCheckWhen("cwImmediate");
        edtDecimalPesoLiquido.setConstraintCheckType("ctExpression");
        edtDecimalPesoLiquido.setConstraintFocusOnError(false);
        edtDecimalPesoLiquido.setConstraintEnableUI(true);
        edtDecimalPesoLiquido.setConstraintEnabled(false);
        edtDecimalPesoLiquido.setConstraintFormCheck(true);
        edtDecimalPesoLiquido.setMaxlength(0);
        edtDecimalPesoLiquido.setPrecision(5);
        edtDecimalPesoLiquido.setFontColor("clWindowText");
        edtDecimalPesoLiquido.setFontSize(-13);
        edtDecimalPesoLiquido.setFontName("Tahoma");
        edtDecimalPesoLiquido.setFontStyle("[]");
        edtDecimalPesoLiquido.setAlignment("taRightJustify");
        vBoxPesoLiquido.addChildren(edtDecimalPesoLiquido);
        edtDecimalPesoLiquido.applyProperties();
        addValidatable(edtDecimalPesoLiquido);
    }

    public TFVBox vBoxPesoBruto = new TFVBox();

    private void init_vBoxPesoBruto() {
        vBoxPesoBruto.setName("vBoxPesoBruto");
        vBoxPesoBruto.setLeft(0);
        vBoxPesoBruto.setTop(97);
        vBoxPesoBruto.setWidth(290);
        vBoxPesoBruto.setHeight(45);
        vBoxPesoBruto.setBorderStyle("stNone");
        vBoxPesoBruto.setPaddingTop(0);
        vBoxPesoBruto.setPaddingLeft(0);
        vBoxPesoBruto.setPaddingRight(0);
        vBoxPesoBruto.setPaddingBottom(0);
        vBoxPesoBruto.setMarginTop(0);
        vBoxPesoBruto.setMarginLeft(0);
        vBoxPesoBruto.setMarginRight(0);
        vBoxPesoBruto.setMarginBottom(0);
        vBoxPesoBruto.setSpacing(1);
        vBoxPesoBruto.setFlexVflex("ftMin");
        vBoxPesoBruto.setFlexHflex("ftTrue");
        vBoxPesoBruto.setScrollable(false);
        vBoxPesoBruto.setBoxShadowConfigHorizontalLength(10);
        vBoxPesoBruto.setBoxShadowConfigVerticalLength(10);
        vBoxPesoBruto.setBoxShadowConfigBlurRadius(5);
        vBoxPesoBruto.setBoxShadowConfigSpreadRadius(0);
        vBoxPesoBruto.setBoxShadowConfigShadowColor("clBlack");
        vBoxPesoBruto.setBoxShadowConfigOpacity(75);
        vBoxColuna03.addChildren(vBoxPesoBruto);
        vBoxPesoBruto.applyProperties();
    }

    public TFLabel lblPesoBruto = new TFLabel();

    private void init_lblPesoBruto() {
        lblPesoBruto.setName("lblPesoBruto");
        lblPesoBruto.setLeft(0);
        lblPesoBruto.setTop(0);
        lblPesoBruto.setWidth(75);
        lblPesoBruto.setHeight(13);
        lblPesoBruto.setCaption("Peso Bruto (Kg)");
        lblPesoBruto.setFontColor("clWindowText");
        lblPesoBruto.setFontSize(-11);
        lblPesoBruto.setFontName("Tahoma");
        lblPesoBruto.setFontStyle("[]");
        lblPesoBruto.setVerticalAlignment("taVerticalCenter");
        lblPesoBruto.setWordBreak(false);
        vBoxPesoBruto.addChildren(lblPesoBruto);
        lblPesoBruto.applyProperties();
    }

    public TFDecimal edtDecimalPesoBruto = new TFDecimal();

    private void init_edtDecimalPesoBruto() {
        edtDecimalPesoBruto.setName("edtDecimalPesoBruto");
        edtDecimalPesoBruto.setLeft(0);
        edtDecimalPesoBruto.setTop(14);
        edtDecimalPesoBruto.setWidth(200);
        edtDecimalPesoBruto.setHeight(24);
        edtDecimalPesoBruto.setHint("Peso Bruto (Kg)");
        edtDecimalPesoBruto.setTable(tbItens);
        edtDecimalPesoBruto.setFieldName("PESO_BRUTO");
        edtDecimalPesoBruto.setHelpCaption("Peso Bruto (Kg)");
        edtDecimalPesoBruto.setFlex(true);
        edtDecimalPesoBruto.setRequired(false);
        edtDecimalPesoBruto.setPrompt("Peso Bruto (Kg)");
        edtDecimalPesoBruto.setConstraintCheckWhen("cwImmediate");
        edtDecimalPesoBruto.setConstraintCheckType("ctExpression");
        edtDecimalPesoBruto.setConstraintFocusOnError(false);
        edtDecimalPesoBruto.setConstraintEnableUI(true);
        edtDecimalPesoBruto.setConstraintEnabled(false);
        edtDecimalPesoBruto.setConstraintFormCheck(true);
        edtDecimalPesoBruto.setMaxlength(0);
        edtDecimalPesoBruto.setPrecision(3);
        edtDecimalPesoBruto.setFontColor("clWindowText");
        edtDecimalPesoBruto.setFontSize(-13);
        edtDecimalPesoBruto.setFontName("Tahoma");
        edtDecimalPesoBruto.setFontStyle("[]");
        edtDecimalPesoBruto.setAlignment("taRightJustify");
        vBoxPesoBruto.addChildren(edtDecimalPesoBruto);
        edtDecimalPesoBruto.applyProperties();
        addValidatable(edtDecimalPesoBruto);
    }

    public TFVBox vBoxLargura = new TFVBox();

    private void init_vBoxLargura() {
        vBoxLargura.setName("vBoxLargura");
        vBoxLargura.setLeft(0);
        vBoxLargura.setTop(143);
        vBoxLargura.setWidth(290);
        vBoxLargura.setHeight(45);
        vBoxLargura.setBorderStyle("stNone");
        vBoxLargura.setPaddingTop(0);
        vBoxLargura.setPaddingLeft(0);
        vBoxLargura.setPaddingRight(0);
        vBoxLargura.setPaddingBottom(0);
        vBoxLargura.setMarginTop(0);
        vBoxLargura.setMarginLeft(0);
        vBoxLargura.setMarginRight(0);
        vBoxLargura.setMarginBottom(0);
        vBoxLargura.setSpacing(1);
        vBoxLargura.setFlexVflex("ftMin");
        vBoxLargura.setFlexHflex("ftTrue");
        vBoxLargura.setScrollable(false);
        vBoxLargura.setBoxShadowConfigHorizontalLength(10);
        vBoxLargura.setBoxShadowConfigVerticalLength(10);
        vBoxLargura.setBoxShadowConfigBlurRadius(5);
        vBoxLargura.setBoxShadowConfigSpreadRadius(0);
        vBoxLargura.setBoxShadowConfigShadowColor("clBlack");
        vBoxLargura.setBoxShadowConfigOpacity(75);
        vBoxColuna03.addChildren(vBoxLargura);
        vBoxLargura.applyProperties();
    }

    public TFLabel lblLargura = new TFLabel();

    private void init_lblLargura() {
        lblLargura.setName("lblLargura");
        lblLargura.setLeft(0);
        lblLargura.setTop(0);
        lblLargura.setWidth(56);
        lblLargura.setHeight(13);
        lblLargura.setCaption("Largura (m)");
        lblLargura.setFontColor("clWindowText");
        lblLargura.setFontSize(-11);
        lblLargura.setFontName("Tahoma");
        lblLargura.setFontStyle("[]");
        lblLargura.setVerticalAlignment("taVerticalCenter");
        lblLargura.setWordBreak(false);
        vBoxLargura.addChildren(lblLargura);
        lblLargura.applyProperties();
    }

    public TFDecimal edtDecimalLargura = new TFDecimal();

    private void init_edtDecimalLargura() {
        edtDecimalLargura.setName("edtDecimalLargura");
        edtDecimalLargura.setLeft(0);
        edtDecimalLargura.setTop(14);
        edtDecimalLargura.setWidth(200);
        edtDecimalLargura.setHeight(24);
        edtDecimalLargura.setHint("Largura (m)");
        edtDecimalLargura.setTable(tbItens);
        edtDecimalLargura.setFieldName("LARGURA_VOLUME");
        edtDecimalLargura.setHelpCaption("Largura (m)");
        edtDecimalLargura.setFlex(true);
        edtDecimalLargura.setRequired(false);
        edtDecimalLargura.setPrompt("Largura (m)");
        edtDecimalLargura.setConstraintCheckWhen("cwImmediate");
        edtDecimalLargura.setConstraintCheckType("ctExpression");
        edtDecimalLargura.setConstraintFocusOnError(false);
        edtDecimalLargura.setConstraintEnableUI(true);
        edtDecimalLargura.setConstraintEnabled(false);
        edtDecimalLargura.setConstraintFormCheck(true);
        edtDecimalLargura.setMaxlength(0);
        edtDecimalLargura.setPrecision(3);
        edtDecimalLargura.setFontColor("clWindowText");
        edtDecimalLargura.setFontSize(-13);
        edtDecimalLargura.setFontName("Tahoma");
        edtDecimalLargura.setFontStyle("[]");
        edtDecimalLargura.setAlignment("taRightJustify");
        vBoxLargura.addChildren(edtDecimalLargura);
        edtDecimalLargura.applyProperties();
        addValidatable(edtDecimalLargura);
    }

    public TFVBox vBoxComprimento = new TFVBox();

    private void init_vBoxComprimento() {
        vBoxComprimento.setName("vBoxComprimento");
        vBoxComprimento.setLeft(0);
        vBoxComprimento.setTop(189);
        vBoxComprimento.setWidth(290);
        vBoxComprimento.setHeight(45);
        vBoxComprimento.setBorderStyle("stNone");
        vBoxComprimento.setPaddingTop(0);
        vBoxComprimento.setPaddingLeft(0);
        vBoxComprimento.setPaddingRight(0);
        vBoxComprimento.setPaddingBottom(0);
        vBoxComprimento.setMarginTop(0);
        vBoxComprimento.setMarginLeft(0);
        vBoxComprimento.setMarginRight(0);
        vBoxComprimento.setMarginBottom(0);
        vBoxComprimento.setSpacing(1);
        vBoxComprimento.setFlexVflex("ftMin");
        vBoxComprimento.setFlexHflex("ftTrue");
        vBoxComprimento.setScrollable(false);
        vBoxComprimento.setBoxShadowConfigHorizontalLength(10);
        vBoxComprimento.setBoxShadowConfigVerticalLength(10);
        vBoxComprimento.setBoxShadowConfigBlurRadius(5);
        vBoxComprimento.setBoxShadowConfigSpreadRadius(0);
        vBoxComprimento.setBoxShadowConfigShadowColor("clBlack");
        vBoxComprimento.setBoxShadowConfigOpacity(75);
        vBoxColuna03.addChildren(vBoxComprimento);
        vBoxComprimento.applyProperties();
    }

    public TFLabel lblComprimento = new TFLabel();

    private void init_lblComprimento() {
        lblComprimento.setName("lblComprimento");
        lblComprimento.setLeft(0);
        lblComprimento.setTop(0);
        lblComprimento.setWidth(82);
        lblComprimento.setHeight(13);
        lblComprimento.setCaption("Comprimento (m)");
        lblComprimento.setFontColor("clWindowText");
        lblComprimento.setFontSize(-11);
        lblComprimento.setFontName("Tahoma");
        lblComprimento.setFontStyle("[]");
        lblComprimento.setVerticalAlignment("taVerticalCenter");
        lblComprimento.setWordBreak(false);
        vBoxComprimento.addChildren(lblComprimento);
        lblComprimento.applyProperties();
    }

    public TFDecimal edtDecimalComprimento = new TFDecimal();

    private void init_edtDecimalComprimento() {
        edtDecimalComprimento.setName("edtDecimalComprimento");
        edtDecimalComprimento.setLeft(0);
        edtDecimalComprimento.setTop(14);
        edtDecimalComprimento.setWidth(200);
        edtDecimalComprimento.setHeight(24);
        edtDecimalComprimento.setHint("Comprimento");
        edtDecimalComprimento.setTable(tbItens);
        edtDecimalComprimento.setFieldName("COMPRIMENTO_VOLUME");
        edtDecimalComprimento.setHelpCaption("Comprimento");
        edtDecimalComprimento.setFlex(true);
        edtDecimalComprimento.setRequired(false);
        edtDecimalComprimento.setPrompt("Comprimento");
        edtDecimalComprimento.setConstraintCheckWhen("cwImmediate");
        edtDecimalComprimento.setConstraintCheckType("ctExpression");
        edtDecimalComprimento.setConstraintFocusOnError(false);
        edtDecimalComprimento.setConstraintEnableUI(true);
        edtDecimalComprimento.setConstraintEnabled(false);
        edtDecimalComprimento.setConstraintFormCheck(true);
        edtDecimalComprimento.setMaxlength(0);
        edtDecimalComprimento.setPrecision(3);
        edtDecimalComprimento.setFontColor("clWindowText");
        edtDecimalComprimento.setFontSize(-13);
        edtDecimalComprimento.setFontName("Tahoma");
        edtDecimalComprimento.setFontStyle("[]");
        edtDecimalComprimento.setAlignment("taRightJustify");
        vBoxComprimento.addChildren(edtDecimalComprimento);
        edtDecimalComprimento.applyProperties();
        addValidatable(edtDecimalComprimento);
    }

    public TFVBox vBoxAltura = new TFVBox();

    private void init_vBoxAltura() {
        vBoxAltura.setName("vBoxAltura");
        vBoxAltura.setLeft(0);
        vBoxAltura.setTop(235);
        vBoxAltura.setWidth(290);
        vBoxAltura.setHeight(45);
        vBoxAltura.setBorderStyle("stNone");
        vBoxAltura.setPaddingTop(0);
        vBoxAltura.setPaddingLeft(0);
        vBoxAltura.setPaddingRight(0);
        vBoxAltura.setPaddingBottom(0);
        vBoxAltura.setMarginTop(0);
        vBoxAltura.setMarginLeft(0);
        vBoxAltura.setMarginRight(0);
        vBoxAltura.setMarginBottom(0);
        vBoxAltura.setSpacing(1);
        vBoxAltura.setFlexVflex("ftMin");
        vBoxAltura.setFlexHflex("ftTrue");
        vBoxAltura.setScrollable(false);
        vBoxAltura.setBoxShadowConfigHorizontalLength(10);
        vBoxAltura.setBoxShadowConfigVerticalLength(10);
        vBoxAltura.setBoxShadowConfigBlurRadius(5);
        vBoxAltura.setBoxShadowConfigSpreadRadius(0);
        vBoxAltura.setBoxShadowConfigShadowColor("clBlack");
        vBoxAltura.setBoxShadowConfigOpacity(75);
        vBoxColuna03.addChildren(vBoxAltura);
        vBoxAltura.applyProperties();
    }

    public TFLabel lblAltura = new TFLabel();

    private void init_lblAltura() {
        lblAltura.setName("lblAltura");
        lblAltura.setLeft(0);
        lblAltura.setTop(0);
        lblAltura.setWidth(48);
        lblAltura.setHeight(13);
        lblAltura.setCaption("Altura (m)");
        lblAltura.setFontColor("clWindowText");
        lblAltura.setFontSize(-11);
        lblAltura.setFontName("Tahoma");
        lblAltura.setFontStyle("[]");
        lblAltura.setVerticalAlignment("taVerticalCenter");
        lblAltura.setWordBreak(false);
        vBoxAltura.addChildren(lblAltura);
        lblAltura.applyProperties();
    }

    public TFDecimal edtDecimalAltura = new TFDecimal();

    private void init_edtDecimalAltura() {
        edtDecimalAltura.setName("edtDecimalAltura");
        edtDecimalAltura.setLeft(0);
        edtDecimalAltura.setTop(14);
        edtDecimalAltura.setWidth(200);
        edtDecimalAltura.setHeight(24);
        edtDecimalAltura.setHint("Altura");
        edtDecimalAltura.setTable(tbItens);
        edtDecimalAltura.setFieldName("ALTURA_VOLUME");
        edtDecimalAltura.setHelpCaption("Altura");
        edtDecimalAltura.setFlex(true);
        edtDecimalAltura.setRequired(false);
        edtDecimalAltura.setPrompt("Altura");
        edtDecimalAltura.setConstraintCheckWhen("cwImmediate");
        edtDecimalAltura.setConstraintCheckType("ctExpression");
        edtDecimalAltura.setConstraintFocusOnError(false);
        edtDecimalAltura.setConstraintEnableUI(true);
        edtDecimalAltura.setConstraintEnabled(false);
        edtDecimalAltura.setConstraintFormCheck(true);
        edtDecimalAltura.setMaxlength(0);
        edtDecimalAltura.setPrecision(3);
        edtDecimalAltura.setFontColor("clWindowText");
        edtDecimalAltura.setFontSize(-13);
        edtDecimalAltura.setFontName("Tahoma");
        edtDecimalAltura.setFontStyle("[]");
        edtDecimalAltura.setAlignment("taRightJustify");
        vBoxAltura.addChildren(edtDecimalAltura);
        edtDecimalAltura.applyProperties();
        addValidatable(edtDecimalAltura);
    }

    public TFVBox vBoxVolume = new TFVBox();

    private void init_vBoxVolume() {
        vBoxVolume.setName("vBoxVolume");
        vBoxVolume.setLeft(0);
        vBoxVolume.setTop(281);
        vBoxVolume.setWidth(290);
        vBoxVolume.setHeight(45);
        vBoxVolume.setBorderStyle("stNone");
        vBoxVolume.setPaddingTop(0);
        vBoxVolume.setPaddingLeft(0);
        vBoxVolume.setPaddingRight(0);
        vBoxVolume.setPaddingBottom(0);
        vBoxVolume.setMarginTop(0);
        vBoxVolume.setMarginLeft(0);
        vBoxVolume.setMarginRight(0);
        vBoxVolume.setMarginBottom(0);
        vBoxVolume.setSpacing(1);
        vBoxVolume.setFlexVflex("ftMin");
        vBoxVolume.setFlexHflex("ftTrue");
        vBoxVolume.setScrollable(false);
        vBoxVolume.setBoxShadowConfigHorizontalLength(10);
        vBoxVolume.setBoxShadowConfigVerticalLength(10);
        vBoxVolume.setBoxShadowConfigBlurRadius(5);
        vBoxVolume.setBoxShadowConfigSpreadRadius(0);
        vBoxVolume.setBoxShadowConfigShadowColor("clBlack");
        vBoxVolume.setBoxShadowConfigOpacity(75);
        vBoxColuna03.addChildren(vBoxVolume);
        vBoxVolume.applyProperties();
    }

    public TFLabel lblVolume = new TFLabel();

    private void init_lblVolume() {
        lblVolume.setName("lblVolume");
        lblVolume.setLeft(0);
        lblVolume.setTop(0);
        lblVolume.setWidth(58);
        lblVolume.setHeight(13);
        lblVolume.setCaption("Volume (m\u00B3)");
        lblVolume.setFontColor("clWindowText");
        lblVolume.setFontSize(-11);
        lblVolume.setFontName("Tahoma");
        lblVolume.setFontStyle("[]");
        lblVolume.setVerticalAlignment("taVerticalCenter");
        lblVolume.setWordBreak(false);
        vBoxVolume.addChildren(lblVolume);
        lblVolume.applyProperties();
    }

    public TFDecimal edtDecimalVolume = new TFDecimal();

    private void init_edtDecimalVolume() {
        edtDecimalVolume.setName("edtDecimalVolume");
        edtDecimalVolume.setLeft(0);
        edtDecimalVolume.setTop(14);
        edtDecimalVolume.setWidth(200);
        edtDecimalVolume.setHeight(24);
        edtDecimalVolume.setHint("Volume");
        edtDecimalVolume.setTable(tbItens);
        edtDecimalVolume.setFieldName("VOLUME");
        edtDecimalVolume.setHelpCaption("Volume");
        edtDecimalVolume.setFlex(true);
        edtDecimalVolume.setRequired(false);
        edtDecimalVolume.setPrompt("Volume");
        edtDecimalVolume.setConstraintCheckWhen("cwImmediate");
        edtDecimalVolume.setConstraintCheckType("ctExpression");
        edtDecimalVolume.setConstraintFocusOnError(false);
        edtDecimalVolume.setConstraintEnableUI(true);
        edtDecimalVolume.setConstraintEnabled(false);
        edtDecimalVolume.setConstraintFormCheck(true);
        edtDecimalVolume.setMaxlength(0);
        edtDecimalVolume.setPrecision(3);
        edtDecimalVolume.setFontColor("clWindowText");
        edtDecimalVolume.setFontSize(-13);
        edtDecimalVolume.setFontName("Tahoma");
        edtDecimalVolume.setFontStyle("[]");
        edtDecimalVolume.setAlignment("taRightJustify");
        vBoxVolume.addChildren(edtDecimalVolume);
        edtDecimalVolume.applyProperties();
        addValidatable(edtDecimalVolume);
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbItens);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbItensFornecedor);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbItensHistBloqueio);
        sc.getTables().add(item2);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnBloquearDesbloquearItemParaCompraClick(final Event<Object> event) {
        if (btnBloquearDesbloquearItemParaCompra.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnBloquearDesbloquearItemParaCompra");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cboGrupoChange(final Event<Object> event);

}