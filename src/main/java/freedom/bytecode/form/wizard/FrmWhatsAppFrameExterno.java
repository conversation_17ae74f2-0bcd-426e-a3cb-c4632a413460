package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmWhatsAppFrameExterno extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.WhatsAppFrameExternoRNA rn = null;

    public FrmWhatsAppFrameExterno() {
        try {
            rn = (freedom.bytecode.rn.WhatsAppFrameExternoRNA) getRN(freedom.bytecode.rn.wizard.WhatsAppFrameExternoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FFrameWhatsApp();
        init_FrmWhatsAppFrameExterno();
    }

    protected TFForm FrmWhatsAppFrameExterno = this;
    private void init_FrmWhatsAppFrameExterno() {
        FrmWhatsAppFrameExterno.setName("FrmWhatsAppFrameExterno");
        FrmWhatsAppFrameExterno.setCaption("WhatsApp");
        FrmWhatsAppFrameExterno.setClientHeight(560);
        FrmWhatsAppFrameExterno.setClientWidth(556);
        FrmWhatsAppFrameExterno.setColor("clBtnFace");
        FrmWhatsAppFrameExterno.setWKey("7000134");
        FrmWhatsAppFrameExterno.setSpacing(0);
        FrmWhatsAppFrameExterno.applyProperties();
    }

    public TFFrame FFrameWhatsApp = new TFFrame();

    private void init_FFrameWhatsApp() {
        FFrameWhatsApp.setName("FFrameWhatsApp");
        FFrameWhatsApp.setLeft(0);
        FFrameWhatsApp.setTop(0);
        FFrameWhatsApp.setWidth(556);
        FFrameWhatsApp.setHeight(560);
        FFrameWhatsApp.setAlign("alClient");
        FFrameWhatsApp.setFlexVflex("ftTrue");
        FFrameWhatsApp.setFlexHflex("ftTrue");
        FrmWhatsAppFrameExterno.addChildren(FFrameWhatsApp);
        FFrameWhatsApp.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}