package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAlterarClienteBasico extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AlterarClienteBasicoRNA rn = null;

    public FrmAlterarClienteBasico() {
        try {
            rn = (freedom.bytecode.rn.AlterarClienteBasicoRNA) getRN(freedom.bytecode.rn.wizard.AlterarClienteBasicoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClientes();
        init_hboxEditarLead();
        init_btnSalvar();
        init_FVBox4();
        init_btnCancelar();
        init_hboxDadosClientes();
        init_gridAlterarDadosBasicos();
        init_lblRes();
        init_FLabel4();
        init_FLabel3();
        init_FLabel2();
        init_email();
        init_FLabel1();
        init_edtNome();
        init_FHBox1();
        init_edtPrefixoCel();
        init_FVBox2();
        init_edtFoneCelular();
        init_FHBox2();
        init_edtPrefixRes();
        init_FVBox3();
        init_edtFoneRes();
        init_FHBox3();
        init_edtPrefixComercial();
        init_FVBox5();
        init_edtFoneComercial();
        init_edtEmail();
        init_edtEmailNfe();
        init_sc();
        init_FrmAlterarClienteBasico();
    }

    public CLIENTES tbClientes;

    private void init_tbClientes() {
        tbClientes = rn.tbClientes;
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("7000150;70001");
        tbClientes.setDeltaMode("dmAll");
        getTables().put(tbClientes, "tbClientes");
        tbClientes.applyProperties();
    }

    protected TFForm FrmAlterarClienteBasico = this;
    private void init_FrmAlterarClienteBasico() {
        FrmAlterarClienteBasico.setName("FrmAlterarClienteBasico");
        FrmAlterarClienteBasico.setCaption("Alterar Dados do Cliente");
        FrmAlterarClienteBasico.setClientHeight(266);
        FrmAlterarClienteBasico.setClientWidth(444);
        FrmAlterarClienteBasico.setColor("clBtnFace");
        FrmAlterarClienteBasico.setWKey("7000150");
        FrmAlterarClienteBasico.setSpacing(0);
        FrmAlterarClienteBasico.applyProperties();
    }

    public TFHBox hboxEditarLead = new TFHBox();

    private void init_hboxEditarLead() {
        hboxEditarLead.setName("hboxEditarLead");
        hboxEditarLead.setLeft(0);
        hboxEditarLead.setTop(0);
        hboxEditarLead.setWidth(444);
        hboxEditarLead.setHeight(61);
        hboxEditarLead.setAlign("alTop");
        hboxEditarLead.setBorderStyle("stNone");
        hboxEditarLead.setPaddingTop(3);
        hboxEditarLead.setPaddingLeft(5);
        hboxEditarLead.setPaddingRight(0);
        hboxEditarLead.setPaddingBottom(0);
        hboxEditarLead.setMarginTop(0);
        hboxEditarLead.setMarginLeft(0);
        hboxEditarLead.setMarginRight(0);
        hboxEditarLead.setMarginBottom(0);
        hboxEditarLead.setSpacing(1);
        hboxEditarLead.setFlexVflex("ftFalse");
        hboxEditarLead.setFlexHflex("ftTrue");
        hboxEditarLead.setScrollable(false);
        hboxEditarLead.setBoxShadowConfigHorizontalLength(10);
        hboxEditarLead.setBoxShadowConfigVerticalLength(10);
        hboxEditarLead.setBoxShadowConfigBlurRadius(5);
        hboxEditarLead.setBoxShadowConfigSpreadRadius(0);
        hboxEditarLead.setBoxShadowConfigShadowColor("clBlack");
        hboxEditarLead.setBoxShadowConfigOpacity(75);
        FrmAlterarClienteBasico.addChildren(hboxEditarLead);
        hboxEditarLead.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(60);
        btnSalvar.setHeight(56);
        btnSalvar.setHint("Salvar Altera\u00E7\u00F5es nos Dados do Cliente");
        btnSalvar.setAlign("alLeft");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAlterarClienteBasico", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E"
 + "C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74"
 + "D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F"
 + "1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24"
 + "D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6"
 + "9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300"
 + "A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F"
 + "BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082");
        btnSalvar.setImageId(700080);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hboxEditarLead.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(60);
        FVBox4.setTop(0);
        FVBox4.setWidth(5);
        FVBox4.setHeight(55);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hboxEditarLead.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(65);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(60);
        btnCancelar.setHeight(56);
        btnCancelar.setHint("Cancelar Altera\u00E7\u00E3o de Dados do Cliente");
        btnCancelar.setAlign("alLeft");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmAlterarClienteBasico", "btnCancelar", "OnClick");
        });
        btnCancelar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000022B4944415478DAB595DF4B544114C7CF2692A12F22195B28855AF8A0"
 + "50F4121111E9F64304F34585D4C00705317AAABFA18720AA97602B044B097221"
 + "88486559C444A8968405454537D0087F15063EC4BAF53DCC1918C6BDEBDC950E"
 + "7CE09C997BCFF7CECC997303F49F2DE0F85C31380D8E48FC037C053FF723C073"
 + "4DE036B898E1D934888147E02DF8EB47E004E807171C57380E3AC03717813360"
 + "0494585F1B078B1257C8961D309E590321D93A4F8172F0191C9638051E8207B2"
 + "EFA605C15D70C7185B950F5CC924C0FE28B82CF1266800531EDB9207C2E09635"
 + "FE015C23391353A05EB6866D87D4C17EF499DCCC35660B7025348AFF04F46549"
 + "FE0C741A6361C9D5257104349B0205E0173828315751D247F26E500566656C9B"
 + "D4DDF9A305F860BE883F074E79247F4EAA1CEDE4698997C071F16B40420B5C07"
 + "EFC4E773B8924372B628B8649E8316B80ADE8BCF9514CA21395B8C5471B07135"
 + "46B5402D98163F2967A093BF00ED0EC939D732382A71359F8916C827D5B80A8D"
 + "FD9BF1919C8D6F765CFC2D529D206596E91068117F90D42D764DAEDF6915FF25"
 + "B8A997A5ED3C98F07879AFE4DC7523467C8EA403D8BD6818DCC821F92B7048E2"
 + "D7C64EEC1228059F48353D6D03E03E481863FC1E17C63DD0668C27C159B0E125"
 + "C07692541F29B3C6B94216E49D4A70CC9AE77F419D3C43D904F44A9ECAF25DEC"
 + "0DE801EBF6C45EFF64FEA3F5926ADB45D6DC6F52B7FF3198F44AE0FAD3E77BC2"
 + "CD2C28F177304FAA94B39AAB40CEF60F541979196CA1E08A0000000049454E44"
 + "AE426082");
        btnCancelar.setImageId(700098);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hboxEditarLead.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFVBox hboxDadosClientes = new TFVBox();

    private void init_hboxDadosClientes() {
        hboxDadosClientes.setName("hboxDadosClientes");
        hboxDadosClientes.setLeft(0);
        hboxDadosClientes.setTop(61);
        hboxDadosClientes.setWidth(444);
        hboxDadosClientes.setHeight(205);
        hboxDadosClientes.setAlign("alClient");
        hboxDadosClientes.setBorderStyle("stNone");
        hboxDadosClientes.setPaddingTop(5);
        hboxDadosClientes.setPaddingLeft(5);
        hboxDadosClientes.setPaddingRight(5);
        hboxDadosClientes.setPaddingBottom(0);
        hboxDadosClientes.setMarginTop(0);
        hboxDadosClientes.setMarginLeft(0);
        hboxDadosClientes.setMarginRight(0);
        hboxDadosClientes.setMarginBottom(0);
        hboxDadosClientes.setSpacing(1);
        hboxDadosClientes.setFlexVflex("ftTrue");
        hboxDadosClientes.setFlexHflex("ftTrue");
        hboxDadosClientes.setScrollable(false);
        hboxDadosClientes.setBoxShadowConfigHorizontalLength(10);
        hboxDadosClientes.setBoxShadowConfigVerticalLength(10);
        hboxDadosClientes.setBoxShadowConfigBlurRadius(5);
        hboxDadosClientes.setBoxShadowConfigSpreadRadius(0);
        hboxDadosClientes.setBoxShadowConfigShadowColor("clBlack");
        hboxDadosClientes.setBoxShadowConfigOpacity(75);
        FrmAlterarClienteBasico.addChildren(hboxDadosClientes);
        hboxDadosClientes.applyProperties();
    }

    public TFGridPanel gridAlterarDadosBasicos = new TFGridPanel();

    private void init_gridAlterarDadosBasicos() {
        gridAlterarDadosBasicos.setName("gridAlterarDadosBasicos");
        gridAlterarDadosBasicos.setLeft(0);
        gridAlterarDadosBasicos.setTop(0);
        gridAlterarDadosBasicos.setWidth(435);
        gridAlterarDadosBasicos.setHeight(163);
        gridAlterarDadosBasicos.setAlign("alClient");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(70.000000000000000000);
        gridAlterarDadosBasicos.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("FLabel1");
        item2.setRow(0);
        gridAlterarDadosBasicos.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("edtNome");
        item3.setRow(0);
        gridAlterarDadosBasicos.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("FLabel2");
        item4.setRow(1);
        gridAlterarDadosBasicos.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(0);
        item5.setControl("email");
        item5.setRow(4);
        gridAlterarDadosBasicos.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(1);
        item6.setControl("FHBox1");
        item6.setRow(1);
        gridAlterarDadosBasicos.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(1);
        item7.setControl("edtEmail");
        item7.setRow(4);
        gridAlterarDadosBasicos.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(0);
        item8.setControl("lblRes");
        item8.setRow(2);
        gridAlterarDadosBasicos.getControlCollection().add(item8);
        TFControlItem item9 = new TFControlItem();
        item9.setColumn(1);
        item9.setControl("FHBox3");
        item9.setRow(3);
        gridAlterarDadosBasicos.getControlCollection().add(item9);
        TFControlItem item10 = new TFControlItem();
        item10.setColumn(1);
        item10.setControl("FHBox2");
        item10.setRow(2);
        gridAlterarDadosBasicos.getControlCollection().add(item10);
        TFControlItem item11 = new TFControlItem();
        item11.setColumn(0);
        item11.setControl("FLabel3");
        item11.setRow(3);
        gridAlterarDadosBasicos.getControlCollection().add(item11);
        TFControlItem item12 = new TFControlItem();
        item12.setColumn(0);
        item12.setControl("FLabel4");
        item12.setRow(5);
        gridAlterarDadosBasicos.getControlCollection().add(item12);
        TFControlItem item13 = new TFControlItem();
        item13.setColumn(1);
        item13.setControl("edtEmailNfe");
        item13.setRow(5);
        gridAlterarDadosBasicos.getControlCollection().add(item13);
        TFGridPanelRow item14 = new TFGridPanelRow();
        item14.setSizeStyle("ssAuto");
        gridAlterarDadosBasicos.getRowCollection().add(item14);
        TFGridPanelRow item15 = new TFGridPanelRow();
        item15.setSizeStyle("ssAuto");
        item15.setValue(28.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item15);
        TFGridPanelRow item16 = new TFGridPanelRow();
        item16.setSizeStyle("ssAuto");
        item16.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item16);
        TFGridPanelRow item17 = new TFGridPanelRow();
        item17.setSizeStyle("ssAuto");
        item17.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item17);
        TFGridPanelRow item18 = new TFGridPanelRow();
        item18.setSizeStyle("ssAuto");
        item18.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item18);
        TFGridPanelRow item19 = new TFGridPanelRow();
        item19.setSizeStyle("ssAuto");
        gridAlterarDadosBasicos.getRowCollection().add(item19);
        TFGridPanelRow item20 = new TFGridPanelRow();
        item20.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item20);
        gridAlterarDadosBasicos.setFlexVflex("ftTrue");
        gridAlterarDadosBasicos.setFlexHflex("ftTrue");
        gridAlterarDadosBasicos.setAllRowFlex(false);
        gridAlterarDadosBasicos.setColumnTabOrder(false);
        hboxDadosClientes.addChildren(gridAlterarDadosBasicos);
        gridAlterarDadosBasicos.applyProperties();
    }

    public TFLabel lblRes = new TFLabel();

    private void init_lblRes() {
        lblRes.setName("lblRes");
        lblRes.setLeft(7);
        lblRes.setTop(53);
        lblRes.setWidth(64);
        lblRes.setHeight(13);
        lblRes.setAlign("alRight");
        lblRes.setCaption("Resid\u00EAncial");
        lblRes.setFontColor("clWindowText");
        lblRes.setFontSize(-11);
        lblRes.setFontName("Tahoma");
        lblRes.setFontStyle("[fsBold]");
        lblRes.setVerticalAlignment("taVerticalCenter");
        gridAlterarDadosBasicos.addChildren(lblRes);
        lblRes.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(9);
        FLabel4.setTop(131);
        FLabel4.setWidth(62);
        FLabel4.setHeight(13);
        FLabel4.setAlign("alRight");
        FLabel4.setCaption("E-Mail NF-e");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[fsBold]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        gridAlterarDadosBasicos.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(15);
        FLabel3.setTop(78);
        FLabel3.setWidth(56);
        FLabel3.setHeight(13);
        FLabel3.setAlign("alRight");
        FLabel3.setCaption("Comercial");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[fsBold]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        gridAlterarDadosBasicos.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(32);
        FLabel2.setTop(25);
        FLabel2.setWidth(39);
        FLabel2.setHeight(13);
        FLabel2.setAlign("alRight");
        FLabel2.setCaption("Celular");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        gridAlterarDadosBasicos.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFLabel email = new TFLabel();

    private void init_email() {
        email.setName("email");
        email.setLeft(37);
        email.setTop(107);
        email.setWidth(34);
        email.setHeight(13);
        email.setAlign("alRight");
        email.setCaption("E-Mail");
        email.setFontColor("clWindowText");
        email.setFontSize(-11);
        email.setFontName("Tahoma");
        email.setFontStyle("[fsBold]");
        email.setVerticalAlignment("taVerticalCenter");
        gridAlterarDadosBasicos.addChildren(email);
        email.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(39);
        FLabel1.setTop(1);
        FLabel1.setWidth(32);
        FLabel1.setHeight(13);
        FLabel1.setAlign("alRight");
        FLabel1.setCaption("Nome");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        gridAlterarDadosBasicos.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFString edtNome = new TFString();

    private void init_edtNome() {
        edtNome.setName("edtNome");
        edtNome.setLeft(71);
        edtNome.setTop(1);
        edtNome.setWidth(305);
        edtNome.setHeight(24);
        edtNome.setTable(tbClientes);
        edtNome.setFieldName("NOME");
        edtNome.setFlex(true);
        edtNome.setRequired(false);
        edtNome.setConstraintCheckWhen("cwImmediate");
        edtNome.setConstraintCheckType("ctExpression");
        edtNome.setConstraintFocusOnError(false);
        edtNome.setConstraintEnableUI(true);
        edtNome.setConstraintEnabled(false);
        edtNome.setConstraintFormCheck(true);
        edtNome.setCharCase("ccNormal");
        edtNome.setPwd(false);
        edtNome.setMaxlength(0);
        edtNome.setAlign("alLeft");
        edtNome.setEnabled(false);
        edtNome.setFontColor("clWindowText");
        edtNome.setFontSize(-13);
        edtNome.setFontName("Tahoma");
        edtNome.setFontStyle("[]");
        edtNome.setSaveLiteralCharacter(false);
        edtNome.applyProperties();
        gridAlterarDadosBasicos.addChildren(edtNome);
        addValidatable(edtNome);
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(71);
        FHBox1.setTop(25);
        FHBox1.setWidth(201);
        FHBox1.setHeight(28);
        FHBox1.setAlign("alLeft");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftMin");
        FHBox1.setFlexHflex("ftMin");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        gridAlterarDadosBasicos.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFString edtPrefixoCel = new TFString();

    private void init_edtPrefixoCel() {
        edtPrefixoCel.setName("edtPrefixoCel");
        edtPrefixoCel.setLeft(0);
        edtPrefixoCel.setTop(0);
        edtPrefixoCel.setWidth(41);
        edtPrefixoCel.setHeight(24);
        edtPrefixoCel.setTable(tbClientes);
        edtPrefixoCel.setFieldName("PREFIXO_CEL");
        edtPrefixoCel.setFlex(false);
        edtPrefixoCel.setRequired(false);
        edtPrefixoCel.setConstraintCheckWhen("cwImmediate");
        edtPrefixoCel.setConstraintCheckType("ctExpression");
        edtPrefixoCel.setConstraintFocusOnError(false);
        edtPrefixoCel.setConstraintEnableUI(true);
        edtPrefixoCel.setConstraintEnabled(false);
        edtPrefixoCel.setConstraintFormCheck(true);
        edtPrefixoCel.setCharCase("ccNormal");
        edtPrefixoCel.setPwd(false);
        edtPrefixoCel.setMaxlength(0);
        edtPrefixoCel.setFontColor("clWindowText");
        edtPrefixoCel.setFontSize(-13);
        edtPrefixoCel.setFontName("Tahoma");
        edtPrefixoCel.setFontStyle("[]");
        edtPrefixoCel.setSaveLiteralCharacter(false);
        edtPrefixoCel.applyProperties();
        FHBox1.addChildren(edtPrefixoCel);
        addValidatable(edtPrefixoCel);
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(41);
        FVBox2.setTop(0);
        FVBox2.setWidth(5);
        FVBox2.setHeight(24);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFString edtFoneCelular = new TFString();

    private void init_edtFoneCelular() {
        edtFoneCelular.setName("edtFoneCelular");
        edtFoneCelular.setLeft(46);
        edtFoneCelular.setTop(0);
        edtFoneCelular.setWidth(145);
        edtFoneCelular.setHeight(24);
        edtFoneCelular.setTable(tbClientes);
        edtFoneCelular.setFieldName("TELEFONE_CEL");
        edtFoneCelular.setFlex(true);
        edtFoneCelular.setRequired(false);
        edtFoneCelular.setConstraintCheckWhen("cwImmediate");
        edtFoneCelular.setConstraintCheckType("ctExpression");
        edtFoneCelular.setConstraintFocusOnError(false);
        edtFoneCelular.setConstraintEnableUI(true);
        edtFoneCelular.setConstraintEnabled(false);
        edtFoneCelular.setConstraintFormCheck(true);
        edtFoneCelular.setCharCase("ccNormal");
        edtFoneCelular.setPwd(false);
        edtFoneCelular.setMask("99999-999?9");
        edtFoneCelular.setMaxlength(0);
        edtFoneCelular.setFontColor("clWindowText");
        edtFoneCelular.setFontSize(-13);
        edtFoneCelular.setFontName("Tahoma");
        edtFoneCelular.setFontStyle("[]");
        edtFoneCelular.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtFoneCelularChange(event);
            processarFlow("FrmAlterarClienteBasico", "edtFoneCelular", "OnChange");
        });
        edtFoneCelular.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtFoneCelularExit(event);
            processarFlow("FrmAlterarClienteBasico", "edtFoneCelular", "OnExit");
        });
        edtFoneCelular.setSaveLiteralCharacter(false);
        edtFoneCelular.applyProperties();
        FHBox1.addChildren(edtFoneCelular);
        addValidatable(edtFoneCelular);
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(71);
        FHBox2.setTop(53);
        FHBox2.setWidth(201);
        FHBox2.setHeight(25);
        FHBox2.setAlign("alLeft");
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftMin");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        gridAlterarDadosBasicos.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFString edtPrefixRes = new TFString();

    private void init_edtPrefixRes() {
        edtPrefixRes.setName("edtPrefixRes");
        edtPrefixRes.setLeft(0);
        edtPrefixRes.setTop(0);
        edtPrefixRes.setWidth(41);
        edtPrefixRes.setHeight(24);
        edtPrefixRes.setTable(tbClientes);
        edtPrefixRes.setFieldName("PREFIXO_RES");
        edtPrefixRes.setFlex(false);
        edtPrefixRes.setRequired(false);
        edtPrefixRes.setConstraintCheckWhen("cwImmediate");
        edtPrefixRes.setConstraintCheckType("ctExpression");
        edtPrefixRes.setConstraintFocusOnError(false);
        edtPrefixRes.setConstraintEnableUI(true);
        edtPrefixRes.setConstraintEnabled(false);
        edtPrefixRes.setConstraintFormCheck(true);
        edtPrefixRes.setCharCase("ccNormal");
        edtPrefixRes.setPwd(false);
        edtPrefixRes.setMaxlength(0);
        edtPrefixRes.setFontColor("clWindowText");
        edtPrefixRes.setFontSize(-13);
        edtPrefixRes.setFontName("Tahoma");
        edtPrefixRes.setFontStyle("[]");
        edtPrefixRes.setSaveLiteralCharacter(false);
        edtPrefixRes.applyProperties();
        FHBox2.addChildren(edtPrefixRes);
        addValidatable(edtPrefixRes);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(41);
        FVBox3.setTop(0);
        FVBox3.setWidth(5);
        FVBox3.setHeight(24);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFString edtFoneRes = new TFString();

    private void init_edtFoneRes() {
        edtFoneRes.setName("edtFoneRes");
        edtFoneRes.setLeft(46);
        edtFoneRes.setTop(0);
        edtFoneRes.setWidth(145);
        edtFoneRes.setHeight(24);
        edtFoneRes.setTable(tbClientes);
        edtFoneRes.setFieldName("TELEFONE_RES");
        edtFoneRes.setFlex(true);
        edtFoneRes.setRequired(false);
        edtFoneRes.setConstraintCheckWhen("cwImmediate");
        edtFoneRes.setConstraintCheckType("ctExpression");
        edtFoneRes.setConstraintFocusOnError(false);
        edtFoneRes.setConstraintEnableUI(true);
        edtFoneRes.setConstraintEnabled(false);
        edtFoneRes.setConstraintFormCheck(true);
        edtFoneRes.setCharCase("ccNormal");
        edtFoneRes.setPwd(false);
        edtFoneRes.setMask("9999-9999");
        edtFoneRes.setMaxlength(0);
        edtFoneRes.setFontColor("clWindowText");
        edtFoneRes.setFontSize(-13);
        edtFoneRes.setFontName("Tahoma");
        edtFoneRes.setFontStyle("[]");
        edtFoneRes.setSaveLiteralCharacter(false);
        edtFoneRes.applyProperties();
        FHBox2.addChildren(edtFoneRes);
        addValidatable(edtFoneRes);
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(71);
        FHBox3.setTop(78);
        FHBox3.setWidth(201);
        FHBox3.setHeight(29);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftMin");
        FHBox3.setFlexHflex("ftMin");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        gridAlterarDadosBasicos.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFString edtPrefixComercial = new TFString();

    private void init_edtPrefixComercial() {
        edtPrefixComercial.setName("edtPrefixComercial");
        edtPrefixComercial.setLeft(0);
        edtPrefixComercial.setTop(0);
        edtPrefixComercial.setWidth(41);
        edtPrefixComercial.setHeight(24);
        edtPrefixComercial.setTable(tbClientes);
        edtPrefixComercial.setFieldName("PREFIXO_COM");
        edtPrefixComercial.setFlex(false);
        edtPrefixComercial.setRequired(false);
        edtPrefixComercial.setConstraintCheckWhen("cwImmediate");
        edtPrefixComercial.setConstraintCheckType("ctExpression");
        edtPrefixComercial.setConstraintFocusOnError(false);
        edtPrefixComercial.setConstraintEnableUI(true);
        edtPrefixComercial.setConstraintEnabled(false);
        edtPrefixComercial.setConstraintFormCheck(true);
        edtPrefixComercial.setCharCase("ccNormal");
        edtPrefixComercial.setPwd(false);
        edtPrefixComercial.setMaxlength(0);
        edtPrefixComercial.setFontColor("clWindowText");
        edtPrefixComercial.setFontSize(-13);
        edtPrefixComercial.setFontName("Tahoma");
        edtPrefixComercial.setFontStyle("[]");
        edtPrefixComercial.setSaveLiteralCharacter(false);
        edtPrefixComercial.applyProperties();
        FHBox3.addChildren(edtPrefixComercial);
        addValidatable(edtPrefixComercial);
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(41);
        FVBox5.setTop(0);
        FVBox5.setWidth(5);
        FVBox5.setHeight(24);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFString edtFoneComercial = new TFString();

    private void init_edtFoneComercial() {
        edtFoneComercial.setName("edtFoneComercial");
        edtFoneComercial.setLeft(46);
        edtFoneComercial.setTop(0);
        edtFoneComercial.setWidth(145);
        edtFoneComercial.setHeight(24);
        edtFoneComercial.setTable(tbClientes);
        edtFoneComercial.setFieldName("TELEFONE_COM");
        edtFoneComercial.setFlex(true);
        edtFoneComercial.setRequired(false);
        edtFoneComercial.setConstraintCheckWhen("cwImmediate");
        edtFoneComercial.setConstraintCheckType("ctExpression");
        edtFoneComercial.setConstraintFocusOnError(false);
        edtFoneComercial.setConstraintEnableUI(true);
        edtFoneComercial.setConstraintEnabled(false);
        edtFoneComercial.setConstraintFormCheck(true);
        edtFoneComercial.setCharCase("ccNormal");
        edtFoneComercial.setPwd(false);
        edtFoneComercial.setMask("99999-999?9");
        edtFoneComercial.setMaxlength(0);
        edtFoneComercial.setFontColor("clWindowText");
        edtFoneComercial.setFontSize(-13);
        edtFoneComercial.setFontName("Tahoma");
        edtFoneComercial.setFontStyle("[]");
        edtFoneComercial.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtFoneComercialChange(event);
            processarFlow("FrmAlterarClienteBasico", "edtFoneComercial", "OnChange");
        });
        edtFoneComercial.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtFoneComercialExit(event);
            processarFlow("FrmAlterarClienteBasico", "edtFoneComercial", "OnExit");
        });
        edtFoneComercial.setSaveLiteralCharacter(false);
        edtFoneComercial.applyProperties();
        FHBox3.addChildren(edtFoneComercial);
        addValidatable(edtFoneComercial);
    }

    public TFString edtEmail = new TFString();

    private void init_edtEmail() {
        edtEmail.setName("edtEmail");
        edtEmail.setLeft(71);
        edtEmail.setTop(107);
        edtEmail.setWidth(304);
        edtEmail.setHeight(24);
        edtEmail.setTable(tbClientes);
        edtEmail.setFieldName("ENDERECO_ELETRONICO");
        edtEmail.setFlex(true);
        edtEmail.setRequired(false);
        edtEmail.setConstraintCheckWhen("cwImmediate");
        edtEmail.setConstraintCheckType("ctExpression");
        edtEmail.setConstraintFocusOnError(false);
        edtEmail.setConstraintEnableUI(true);
        edtEmail.setConstraintEnabled(false);
        edtEmail.setConstraintFormCheck(true);
        edtEmail.setCharCase("ccNormal");
        edtEmail.setPwd(false);
        edtEmail.setMaxlength(0);
        edtEmail.setFontColor("clWindowText");
        edtEmail.setFontSize(-13);
        edtEmail.setFontName("Tahoma");
        edtEmail.setFontStyle("[]");
        edtEmail.setSaveLiteralCharacter(false);
        edtEmail.applyProperties();
        gridAlterarDadosBasicos.addChildren(edtEmail);
        addValidatable(edtEmail);
    }

    public TFString edtEmailNfe = new TFString();

    private void init_edtEmailNfe() {
        edtEmailNfe.setName("edtEmailNfe");
        edtEmailNfe.setLeft(71);
        edtEmailNfe.setTop(131);
        edtEmailNfe.setWidth(304);
        edtEmailNfe.setHeight(24);
        edtEmailNfe.setTable(tbClientes);
        edtEmailNfe.setFieldName("EMAIL_NFE");
        edtEmailNfe.setFlex(true);
        edtEmailNfe.setRequired(false);
        edtEmailNfe.setConstraintCheckWhen("cwImmediate");
        edtEmailNfe.setConstraintCheckType("ctExpression");
        edtEmailNfe.setConstraintFocusOnError(false);
        edtEmailNfe.setConstraintEnableUI(true);
        edtEmailNfe.setConstraintEnabled(false);
        edtEmailNfe.setConstraintFormCheck(true);
        edtEmailNfe.setCharCase("ccNormal");
        edtEmailNfe.setPwd(false);
        edtEmailNfe.setMaxlength(0);
        edtEmailNfe.setAlign("alLeft");
        edtEmailNfe.setFontColor("clWindowText");
        edtEmailNfe.setFontSize(-13);
        edtEmailNfe.setFontName("Tahoma");
        edtEmailNfe.setFontStyle("[]");
        edtEmailNfe.setSaveLiteralCharacter(false);
        edtEmailNfe.applyProperties();
        gridAlterarDadosBasicos.addChildren(edtEmailNfe);
        addValidatable(edtEmailNfe);
    }

    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item21 = new TFSchemaItem();
        item21.setTable(tbClientes);
        sc.getTables().add(item21);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtFoneCelularChange(final Event<Object> event);

    public abstract void edtFoneCelularExit(final Event<Object> event);

    public abstract void edtFoneComercialChange(final Event<Object> event);

    public abstract void edtFoneComercialExit(final Event<Object> event);

}