package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAguardarPagamentoPosSitef extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AguardarPagamentoPosSitefRNA rn = null;

    public FrmAguardarPagamentoPosSitef() {
        try {
            rn = (freedom.bytecode.rn.AguardarPagamentoPosSitefRNA) getRN(freedom.bytecode.rn.wizard.AguardarPagamentoPosSitefRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_timerPos();
        init_VBoxPrincipal();
        init_hBoxStatus();
        init_hBoxSepLeft();
        init_vBoxStatusIntegracao();
        init_hBoxStatusIntegracaoAjusteCima();
        init_hBoxStatusIntegracao();
        init_hBoxStatusLeft();
        init_lblStatusIntegracao();
        init_hBoxStatusIntegracaoAjusteBaixo();
        init_hboxSep01();
        init_HBoxRodape();
        init_FHBox2();
        init_vBoxCancelar();
        init_hBoxCancelarAlto();
        init_hBoxCancelar();
        init_hBoxSepCancelar();
        init_vBoxIconCancelar();
        init_FHBox5();
        init_FHBox8();
        init_iconCancelar();
        init_FHBox12();
        init_hBoxCancelarEsq();
        init_vBoxBtnCancelar();
        init_FHBox6();
        init_FHBox1();
        init_lblAcaoCancelar();
        init_FHBox3();
        init_hBoxCancelarDir();
        init_hBoxCancelarBaixo();
        init_FHBox4();
        init_FrmAguardarPagamentoPosSitef();
    }

    public TFTimer timerPos = new TFTimer();

    private void init_timerPos() {
        timerPos.setName("timerPos");
        timerPos.setEnabled(false);
        timerPos.setInterval(0);
        timerPos.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            timerPosTimer(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "timerPos", "OnTimer");
        });
        timerPos.setRepeats(false);
        FrmAguardarPagamentoPosSitef.addChildren(timerPos);
        timerPos.applyProperties();
    }

    protected TFForm FrmAguardarPagamentoPosSitef = this;
    private void init_FrmAguardarPagamentoPosSitef() {
        FrmAguardarPagamentoPosSitef.setName("FrmAguardarPagamentoPosSitef");
        FrmAguardarPagamentoPosSitef.setCaption("Aguardar Pagamento Pos");
        FrmAguardarPagamentoPosSitef.setClientHeight(141);
        FrmAguardarPagamentoPosSitef.setClientWidth(469);
        FrmAguardarPagamentoPosSitef.setColor("clBtnFace");
        FrmAguardarPagamentoPosSitef.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            frmShow(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "FrmAguardarPagamentoPosSitef", "OnCreate");
        });
        FrmAguardarPagamentoPosSitef.setWOrigem("EhMain");
        FrmAguardarPagamentoPosSitef.setWKey("306033");
        FrmAguardarPagamentoPosSitef.setSpacing(0);
        FrmAguardarPagamentoPosSitef.applyProperties();
    }

    public TFVBox VBoxPrincipal = new TFVBox();

    private void init_VBoxPrincipal() {
        VBoxPrincipal.setName("VBoxPrincipal");
        VBoxPrincipal.setLeft(0);
        VBoxPrincipal.setTop(0);
        VBoxPrincipal.setWidth(469);
        VBoxPrincipal.setHeight(141);
        VBoxPrincipal.setAlign("alClient");
        VBoxPrincipal.setBorderStyle("stNone");
        VBoxPrincipal.setPaddingTop(0);
        VBoxPrincipal.setPaddingLeft(0);
        VBoxPrincipal.setPaddingRight(0);
        VBoxPrincipal.setPaddingBottom(0);
        VBoxPrincipal.setMarginTop(0);
        VBoxPrincipal.setMarginLeft(0);
        VBoxPrincipal.setMarginRight(0);
        VBoxPrincipal.setMarginBottom(0);
        VBoxPrincipal.setSpacing(1);
        VBoxPrincipal.setFlexVflex("ftFalse");
        VBoxPrincipal.setFlexHflex("ftFalse");
        VBoxPrincipal.setScrollable(false);
        VBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        VBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        VBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        VBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        VBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        VBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmAguardarPagamentoPosSitef.addChildren(VBoxPrincipal);
        VBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxStatus = new TFHBox();

    private void init_hBoxStatus() {
        hBoxStatus.setName("hBoxStatus");
        hBoxStatus.setLeft(0);
        hBoxStatus.setTop(0);
        hBoxStatus.setWidth(461);
        hBoxStatus.setHeight(58);
        hBoxStatus.setBorderStyle("stNone");
        hBoxStatus.setPaddingTop(0);
        hBoxStatus.setPaddingLeft(0);
        hBoxStatus.setPaddingRight(0);
        hBoxStatus.setPaddingBottom(0);
        hBoxStatus.setMarginTop(0);
        hBoxStatus.setMarginLeft(0);
        hBoxStatus.setMarginRight(0);
        hBoxStatus.setMarginBottom(0);
        hBoxStatus.setSpacing(1);
        hBoxStatus.setFlexVflex("ftFalse");
        hBoxStatus.setFlexHflex("ftTrue");
        hBoxStatus.setScrollable(false);
        hBoxStatus.setBoxShadowConfigHorizontalLength(10);
        hBoxStatus.setBoxShadowConfigVerticalLength(10);
        hBoxStatus.setBoxShadowConfigBlurRadius(5);
        hBoxStatus.setBoxShadowConfigSpreadRadius(0);
        hBoxStatus.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatus.setBoxShadowConfigOpacity(75);
        hBoxStatus.setVAlign("tvTop");
        VBoxPrincipal.addChildren(hBoxStatus);
        hBoxStatus.applyProperties();
    }

    public TFHBox hBoxSepLeft = new TFHBox();

    private void init_hBoxSepLeft() {
        hBoxSepLeft.setName("hBoxSepLeft");
        hBoxSepLeft.setLeft(0);
        hBoxSepLeft.setTop(0);
        hBoxSepLeft.setWidth(2);
        hBoxSepLeft.setHeight(20);
        hBoxSepLeft.setBorderStyle("stNone");
        hBoxSepLeft.setPaddingTop(0);
        hBoxSepLeft.setPaddingLeft(0);
        hBoxSepLeft.setPaddingRight(0);
        hBoxSepLeft.setPaddingBottom(0);
        hBoxSepLeft.setMarginTop(0);
        hBoxSepLeft.setMarginLeft(0);
        hBoxSepLeft.setMarginRight(0);
        hBoxSepLeft.setMarginBottom(0);
        hBoxSepLeft.setSpacing(1);
        hBoxSepLeft.setFlexVflex("ftFalse");
        hBoxSepLeft.setFlexHflex("ftFalse");
        hBoxSepLeft.setScrollable(false);
        hBoxSepLeft.setBoxShadowConfigHorizontalLength(10);
        hBoxSepLeft.setBoxShadowConfigVerticalLength(10);
        hBoxSepLeft.setBoxShadowConfigBlurRadius(5);
        hBoxSepLeft.setBoxShadowConfigSpreadRadius(0);
        hBoxSepLeft.setBoxShadowConfigShadowColor("clBlack");
        hBoxSepLeft.setBoxShadowConfigOpacity(75);
        hBoxSepLeft.setVAlign("tvTop");
        hBoxStatus.addChildren(hBoxSepLeft);
        hBoxSepLeft.applyProperties();
    }

    public TFVBox vBoxStatusIntegracao = new TFVBox();

    private void init_vBoxStatusIntegracao() {
        vBoxStatusIntegracao.setName("vBoxStatusIntegracao");
        vBoxStatusIntegracao.setLeft(2);
        vBoxStatusIntegracao.setTop(0);
        vBoxStatusIntegracao.setWidth(438);
        vBoxStatusIntegracao.setHeight(69);
        vBoxStatusIntegracao.setBorderStyle("stNone");
        vBoxStatusIntegracao.setPaddingTop(0);
        vBoxStatusIntegracao.setPaddingLeft(0);
        vBoxStatusIntegracao.setPaddingRight(0);
        vBoxStatusIntegracao.setPaddingBottom(0);
        vBoxStatusIntegracao.setMarginTop(0);
        vBoxStatusIntegracao.setMarginLeft(0);
        vBoxStatusIntegracao.setMarginRight(0);
        vBoxStatusIntegracao.setMarginBottom(0);
        vBoxStatusIntegracao.setSpacing(1);
        vBoxStatusIntegracao.setFlexVflex("ftTrue");
        vBoxStatusIntegracao.setFlexHflex("ftTrue");
        vBoxStatusIntegracao.setScrollable(false);
        vBoxStatusIntegracao.setBoxShadowConfigHorizontalLength(10);
        vBoxStatusIntegracao.setBoxShadowConfigVerticalLength(10);
        vBoxStatusIntegracao.setBoxShadowConfigBlurRadius(5);
        vBoxStatusIntegracao.setBoxShadowConfigSpreadRadius(0);
        vBoxStatusIntegracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxStatusIntegracao.setBoxShadowConfigOpacity(75);
        hBoxStatus.addChildren(vBoxStatusIntegracao);
        vBoxStatusIntegracao.applyProperties();
    }

    public TFHBox hBoxStatusIntegracaoAjusteCima = new TFHBox();

    private void init_hBoxStatusIntegracaoAjusteCima() {
        hBoxStatusIntegracaoAjusteCima.setName("hBoxStatusIntegracaoAjusteCima");
        hBoxStatusIntegracaoAjusteCima.setLeft(0);
        hBoxStatusIntegracaoAjusteCima.setTop(0);
        hBoxStatusIntegracaoAjusteCima.setWidth(185);
        hBoxStatusIntegracaoAjusteCima.setHeight(15);
        hBoxStatusIntegracaoAjusteCima.setBorderStyle("stNone");
        hBoxStatusIntegracaoAjusteCima.setPaddingTop(0);
        hBoxStatusIntegracaoAjusteCima.setPaddingLeft(0);
        hBoxStatusIntegracaoAjusteCima.setPaddingRight(0);
        hBoxStatusIntegracaoAjusteCima.setPaddingBottom(0);
        hBoxStatusIntegracaoAjusteCima.setMarginTop(0);
        hBoxStatusIntegracaoAjusteCima.setMarginLeft(0);
        hBoxStatusIntegracaoAjusteCima.setMarginRight(0);
        hBoxStatusIntegracaoAjusteCima.setMarginBottom(0);
        hBoxStatusIntegracaoAjusteCima.setSpacing(1);
        hBoxStatusIntegracaoAjusteCima.setFlexVflex("ftTrue");
        hBoxStatusIntegracaoAjusteCima.setFlexHflex("ftFalse");
        hBoxStatusIntegracaoAjusteCima.setScrollable(false);
        hBoxStatusIntegracaoAjusteCima.setBoxShadowConfigHorizontalLength(10);
        hBoxStatusIntegracaoAjusteCima.setBoxShadowConfigVerticalLength(10);
        hBoxStatusIntegracaoAjusteCima.setBoxShadowConfigBlurRadius(5);
        hBoxStatusIntegracaoAjusteCima.setBoxShadowConfigSpreadRadius(0);
        hBoxStatusIntegracaoAjusteCima.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatusIntegracaoAjusteCima.setBoxShadowConfigOpacity(75);
        hBoxStatusIntegracaoAjusteCima.setVAlign("tvTop");
        vBoxStatusIntegracao.addChildren(hBoxStatusIntegracaoAjusteCima);
        hBoxStatusIntegracaoAjusteCima.applyProperties();
    }

    public TFHBox hBoxStatusIntegracao = new TFHBox();

    private void init_hBoxStatusIntegracao() {
        hBoxStatusIntegracao.setName("hBoxStatusIntegracao");
        hBoxStatusIntegracao.setLeft(0);
        hBoxStatusIntegracao.setTop(16);
        hBoxStatusIntegracao.setWidth(425);
        hBoxStatusIntegracao.setHeight(29);
        hBoxStatusIntegracao.setBorderStyle("stNone");
        hBoxStatusIntegracao.setPaddingTop(0);
        hBoxStatusIntegracao.setPaddingLeft(0);
        hBoxStatusIntegracao.setPaddingRight(0);
        hBoxStatusIntegracao.setPaddingBottom(0);
        hBoxStatusIntegracao.setMarginTop(0);
        hBoxStatusIntegracao.setMarginLeft(0);
        hBoxStatusIntegracao.setMarginRight(0);
        hBoxStatusIntegracao.setMarginBottom(0);
        hBoxStatusIntegracao.setSpacing(1);
        hBoxStatusIntegracao.setFlexVflex("ftFalse");
        hBoxStatusIntegracao.setFlexHflex("ftTrue");
        hBoxStatusIntegracao.setScrollable(false);
        hBoxStatusIntegracao.setBoxShadowConfigHorizontalLength(10);
        hBoxStatusIntegracao.setBoxShadowConfigVerticalLength(10);
        hBoxStatusIntegracao.setBoxShadowConfigBlurRadius(5);
        hBoxStatusIntegracao.setBoxShadowConfigSpreadRadius(0);
        hBoxStatusIntegracao.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatusIntegracao.setBoxShadowConfigOpacity(75);
        hBoxStatusIntegracao.setVAlign("tvTop");
        vBoxStatusIntegracao.addChildren(hBoxStatusIntegracao);
        hBoxStatusIntegracao.applyProperties();
    }

    public TFHBox hBoxStatusLeft = new TFHBox();

    private void init_hBoxStatusLeft() {
        hBoxStatusLeft.setName("hBoxStatusLeft");
        hBoxStatusLeft.setLeft(0);
        hBoxStatusLeft.setTop(0);
        hBoxStatusLeft.setWidth(7);
        hBoxStatusLeft.setHeight(20);
        hBoxStatusLeft.setBorderStyle("stNone");
        hBoxStatusLeft.setPaddingTop(0);
        hBoxStatusLeft.setPaddingLeft(0);
        hBoxStatusLeft.setPaddingRight(0);
        hBoxStatusLeft.setPaddingBottom(0);
        hBoxStatusLeft.setMarginTop(0);
        hBoxStatusLeft.setMarginLeft(0);
        hBoxStatusLeft.setMarginRight(0);
        hBoxStatusLeft.setMarginBottom(0);
        hBoxStatusLeft.setSpacing(1);
        hBoxStatusLeft.setFlexVflex("ftFalse");
        hBoxStatusLeft.setFlexHflex("ftFalse");
        hBoxStatusLeft.setScrollable(false);
        hBoxStatusLeft.setBoxShadowConfigHorizontalLength(10);
        hBoxStatusLeft.setBoxShadowConfigVerticalLength(10);
        hBoxStatusLeft.setBoxShadowConfigBlurRadius(5);
        hBoxStatusLeft.setBoxShadowConfigSpreadRadius(0);
        hBoxStatusLeft.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatusLeft.setBoxShadowConfigOpacity(75);
        hBoxStatusLeft.setVAlign("tvTop");
        hBoxStatusIntegracao.addChildren(hBoxStatusLeft);
        hBoxStatusLeft.applyProperties();
    }

    public TFLabel lblStatusIntegracao = new TFLabel();

    private void init_lblStatusIntegracao() {
        lblStatusIntegracao.setName("lblStatusIntegracao");
        lblStatusIntegracao.setLeft(7);
        lblStatusIntegracao.setTop(0);
        lblStatusIntegracao.setWidth(321);
        lblStatusIntegracao.setHeight(19);
        lblStatusIntegracao.setCaption("Verificando Aprova\u00E7\u00E3o de Pagamento...");
        lblStatusIntegracao.setFontColor("clWindowText");
        lblStatusIntegracao.setFontSize(-16);
        lblStatusIntegracao.setFontName("Tahoma");
        lblStatusIntegracao.setFontStyle("[fsBold]");
        lblStatusIntegracao.setVerticalAlignment("taVerticalCenter");
        lblStatusIntegracao.setWordBreak(false);
        hBoxStatusIntegracao.addChildren(lblStatusIntegracao);
        lblStatusIntegracao.applyProperties();
    }

    public TFHBox hBoxStatusIntegracaoAjusteBaixo = new TFHBox();

    private void init_hBoxStatusIntegracaoAjusteBaixo() {
        hBoxStatusIntegracaoAjusteBaixo.setName("hBoxStatusIntegracaoAjusteBaixo");
        hBoxStatusIntegracaoAjusteBaixo.setLeft(0);
        hBoxStatusIntegracaoAjusteBaixo.setTop(46);
        hBoxStatusIntegracaoAjusteBaixo.setWidth(185);
        hBoxStatusIntegracaoAjusteBaixo.setHeight(5);
        hBoxStatusIntegracaoAjusteBaixo.setBorderStyle("stNone");
        hBoxStatusIntegracaoAjusteBaixo.setPaddingTop(0);
        hBoxStatusIntegracaoAjusteBaixo.setPaddingLeft(0);
        hBoxStatusIntegracaoAjusteBaixo.setPaddingRight(0);
        hBoxStatusIntegracaoAjusteBaixo.setPaddingBottom(0);
        hBoxStatusIntegracaoAjusteBaixo.setMarginTop(0);
        hBoxStatusIntegracaoAjusteBaixo.setMarginLeft(0);
        hBoxStatusIntegracaoAjusteBaixo.setMarginRight(0);
        hBoxStatusIntegracaoAjusteBaixo.setMarginBottom(0);
        hBoxStatusIntegracaoAjusteBaixo.setSpacing(1);
        hBoxStatusIntegracaoAjusteBaixo.setFlexVflex("ftFalse");
        hBoxStatusIntegracaoAjusteBaixo.setFlexHflex("ftFalse");
        hBoxStatusIntegracaoAjusteBaixo.setScrollable(false);
        hBoxStatusIntegracaoAjusteBaixo.setBoxShadowConfigHorizontalLength(10);
        hBoxStatusIntegracaoAjusteBaixo.setBoxShadowConfigVerticalLength(10);
        hBoxStatusIntegracaoAjusteBaixo.setBoxShadowConfigBlurRadius(5);
        hBoxStatusIntegracaoAjusteBaixo.setBoxShadowConfigSpreadRadius(0);
        hBoxStatusIntegracaoAjusteBaixo.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatusIntegracaoAjusteBaixo.setBoxShadowConfigOpacity(75);
        hBoxStatusIntegracaoAjusteBaixo.setVAlign("tvTop");
        vBoxStatusIntegracao.addChildren(hBoxStatusIntegracaoAjusteBaixo);
        hBoxStatusIntegracaoAjusteBaixo.applyProperties();
    }

    public TFHBox hboxSep01 = new TFHBox();

    private void init_hboxSep01() {
        hboxSep01.setName("hboxSep01");
        hboxSep01.setLeft(440);
        hboxSep01.setTop(0);
        hboxSep01.setWidth(10);
        hboxSep01.setHeight(20);
        hboxSep01.setBorderStyle("stNone");
        hboxSep01.setPaddingTop(0);
        hboxSep01.setPaddingLeft(0);
        hboxSep01.setPaddingRight(0);
        hboxSep01.setPaddingBottom(0);
        hboxSep01.setMarginTop(0);
        hboxSep01.setMarginLeft(0);
        hboxSep01.setMarginRight(0);
        hboxSep01.setMarginBottom(0);
        hboxSep01.setSpacing(1);
        hboxSep01.setFlexVflex("ftFalse");
        hboxSep01.setFlexHflex("ftFalse");
        hboxSep01.setScrollable(false);
        hboxSep01.setBoxShadowConfigHorizontalLength(10);
        hboxSep01.setBoxShadowConfigVerticalLength(10);
        hboxSep01.setBoxShadowConfigBlurRadius(5);
        hboxSep01.setBoxShadowConfigSpreadRadius(0);
        hboxSep01.setBoxShadowConfigShadowColor("clBlack");
        hboxSep01.setBoxShadowConfigOpacity(75);
        hboxSep01.setVAlign("tvTop");
        hBoxStatus.addChildren(hboxSep01);
        hboxSep01.applyProperties();
    }

    public TFHBox HBoxRodape = new TFHBox();

    private void init_HBoxRodape() {
        HBoxRodape.setName("HBoxRodape");
        HBoxRodape.setLeft(0);
        HBoxRodape.setTop(59);
        HBoxRodape.setWidth(461);
        HBoxRodape.setHeight(72);
        HBoxRodape.setBorderStyle("stNone");
        HBoxRodape.setPaddingTop(0);
        HBoxRodape.setPaddingLeft(0);
        HBoxRodape.setPaddingRight(0);
        HBoxRodape.setPaddingBottom(0);
        HBoxRodape.setMarginTop(0);
        HBoxRodape.setMarginLeft(0);
        HBoxRodape.setMarginRight(0);
        HBoxRodape.setMarginBottom(0);
        HBoxRodape.setSpacing(1);
        HBoxRodape.setFlexVflex("ftMin");
        HBoxRodape.setFlexHflex("ftTrue");
        HBoxRodape.setScrollable(false);
        HBoxRodape.setBoxShadowConfigHorizontalLength(10);
        HBoxRodape.setBoxShadowConfigVerticalLength(10);
        HBoxRodape.setBoxShadowConfigBlurRadius(5);
        HBoxRodape.setBoxShadowConfigSpreadRadius(0);
        HBoxRodape.setBoxShadowConfigShadowColor("clBlack");
        HBoxRodape.setBoxShadowConfigOpacity(75);
        HBoxRodape.setVAlign("tvTop");
        VBoxPrincipal.addChildren(HBoxRodape);
        HBoxRodape.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(45);
        FHBox2.setHeight(41);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        HBoxRodape.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox vBoxCancelar = new TFVBox();

    private void init_vBoxCancelar() {
        vBoxCancelar.setName("vBoxCancelar");
        vBoxCancelar.setLeft(45);
        vBoxCancelar.setTop(0);
        vBoxCancelar.setWidth(108);
        vBoxCancelar.setHeight(68);
        vBoxCancelar.setBorderStyle("stNone");
        vBoxCancelar.setPaddingTop(0);
        vBoxCancelar.setPaddingLeft(0);
        vBoxCancelar.setPaddingRight(0);
        vBoxCancelar.setPaddingBottom(0);
        vBoxCancelar.setMarginTop(0);
        vBoxCancelar.setMarginLeft(0);
        vBoxCancelar.setMarginRight(0);
        vBoxCancelar.setMarginBottom(0);
        vBoxCancelar.setSpacing(1);
        vBoxCancelar.setFlexVflex("ftMin");
        vBoxCancelar.setFlexHflex("ftMin");
        vBoxCancelar.setScrollable(false);
        vBoxCancelar.setBoxShadowConfigHorizontalLength(10);
        vBoxCancelar.setBoxShadowConfigVerticalLength(10);
        vBoxCancelar.setBoxShadowConfigBlurRadius(5);
        vBoxCancelar.setBoxShadowConfigSpreadRadius(0);
        vBoxCancelar.setBoxShadowConfigShadowColor("clBlack");
        vBoxCancelar.setBoxShadowConfigOpacity(75);
        HBoxRodape.addChildren(vBoxCancelar);
        vBoxCancelar.applyProperties();
    }

    public TFHBox hBoxCancelarAlto = new TFHBox();

    private void init_hBoxCancelarAlto() {
        hBoxCancelarAlto.setName("hBoxCancelarAlto");
        hBoxCancelarAlto.setLeft(0);
        hBoxCancelarAlto.setTop(0);
        hBoxCancelarAlto.setWidth(100);
        hBoxCancelarAlto.setHeight(5);
        hBoxCancelarAlto.setBorderStyle("stNone");
        hBoxCancelarAlto.setPaddingTop(0);
        hBoxCancelarAlto.setPaddingLeft(0);
        hBoxCancelarAlto.setPaddingRight(0);
        hBoxCancelarAlto.setPaddingBottom(0);
        hBoxCancelarAlto.setMarginTop(0);
        hBoxCancelarAlto.setMarginLeft(0);
        hBoxCancelarAlto.setMarginRight(0);
        hBoxCancelarAlto.setMarginBottom(0);
        hBoxCancelarAlto.setSpacing(1);
        hBoxCancelarAlto.setFlexVflex("ftFalse");
        hBoxCancelarAlto.setFlexHflex("ftFalse");
        hBoxCancelarAlto.setScrollable(false);
        hBoxCancelarAlto.setBoxShadowConfigHorizontalLength(10);
        hBoxCancelarAlto.setBoxShadowConfigVerticalLength(10);
        hBoxCancelarAlto.setBoxShadowConfigBlurRadius(5);
        hBoxCancelarAlto.setBoxShadowConfigSpreadRadius(0);
        hBoxCancelarAlto.setBoxShadowConfigShadowColor("clBlack");
        hBoxCancelarAlto.setBoxShadowConfigOpacity(75);
        hBoxCancelarAlto.setVAlign("tvTop");
        vBoxCancelar.addChildren(hBoxCancelarAlto);
        hBoxCancelarAlto.applyProperties();
    }

    public TFHBox hBoxCancelar = new TFHBox();

    private void init_hBoxCancelar() {
        hBoxCancelar.setName("hBoxCancelar");
        hBoxCancelar.setLeft(0);
        hBoxCancelar.setTop(6);
        hBoxCancelar.setWidth(102);
        hBoxCancelar.setHeight(48);
        hBoxCancelar.setBorderStyle("stDoubleRaised");
        hBoxCancelar.setPaddingTop(0);
        hBoxCancelar.setPaddingLeft(0);
        hBoxCancelar.setPaddingRight(0);
        hBoxCancelar.setPaddingBottom(0);
        hBoxCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "hBoxCancelar", "OnClick");
        });
        hBoxCancelar.setMarginTop(0);
        hBoxCancelar.setMarginLeft(0);
        hBoxCancelar.setMarginRight(0);
        hBoxCancelar.setMarginBottom(0);
        hBoxCancelar.setSpacing(1);
        hBoxCancelar.setFlexVflex("ftTrue");
        hBoxCancelar.setFlexHflex("ftTrue");
        hBoxCancelar.setScrollable(false);
        hBoxCancelar.setBoxShadowConfigHorizontalLength(10);
        hBoxCancelar.setBoxShadowConfigVerticalLength(10);
        hBoxCancelar.setBoxShadowConfigBlurRadius(5);
        hBoxCancelar.setBoxShadowConfigSpreadRadius(0);
        hBoxCancelar.setBoxShadowConfigShadowColor("clBlack");
        hBoxCancelar.setBoxShadowConfigOpacity(75);
        hBoxCancelar.setVAlign("tvTop");
        vBoxCancelar.addChildren(hBoxCancelar);
        hBoxCancelar.applyProperties();
    }

    public TFHBox hBoxSepCancelar = new TFHBox();

    private void init_hBoxSepCancelar() {
        hBoxSepCancelar.setName("hBoxSepCancelar");
        hBoxSepCancelar.setLeft(2);
        hBoxSepCancelar.setTop(2);
        hBoxSepCancelar.setWidth(5);
        hBoxSepCancelar.setHeight(20);
        hBoxSepCancelar.setBorderStyle("stNone");
        hBoxSepCancelar.setPaddingTop(0);
        hBoxSepCancelar.setPaddingLeft(0);
        hBoxSepCancelar.setPaddingRight(0);
        hBoxSepCancelar.setPaddingBottom(0);
        hBoxSepCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSepCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "hBoxSepCancelar", "OnClick");
        });
        hBoxSepCancelar.setMarginTop(0);
        hBoxSepCancelar.setMarginLeft(0);
        hBoxSepCancelar.setMarginRight(0);
        hBoxSepCancelar.setMarginBottom(0);
        hBoxSepCancelar.setSpacing(1);
        hBoxSepCancelar.setFlexVflex("ftFalse");
        hBoxSepCancelar.setFlexHflex("ftFalse");
        hBoxSepCancelar.setScrollable(false);
        hBoxSepCancelar.setBoxShadowConfigHorizontalLength(10);
        hBoxSepCancelar.setBoxShadowConfigVerticalLength(10);
        hBoxSepCancelar.setBoxShadowConfigBlurRadius(5);
        hBoxSepCancelar.setBoxShadowConfigSpreadRadius(0);
        hBoxSepCancelar.setBoxShadowConfigShadowColor("clBlack");
        hBoxSepCancelar.setBoxShadowConfigOpacity(75);
        hBoxSepCancelar.setVAlign("tvTop");
        hBoxCancelar.addChildren(hBoxSepCancelar);
        hBoxSepCancelar.applyProperties();
    }

    public TFVBox vBoxIconCancelar = new TFVBox();

    private void init_vBoxIconCancelar() {
        vBoxIconCancelar.setName("vBoxIconCancelar");
        vBoxIconCancelar.setLeft(7);
        vBoxIconCancelar.setTop(2);
        vBoxIconCancelar.setWidth(21);
        vBoxIconCancelar.setHeight(35);
        vBoxIconCancelar.setBorderStyle("stNone");
        vBoxIconCancelar.setPaddingTop(0);
        vBoxIconCancelar.setPaddingLeft(0);
        vBoxIconCancelar.setPaddingRight(0);
        vBoxIconCancelar.setPaddingBottom(0);
        vBoxIconCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxIconCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "vBoxIconCancelar", "OnClick");
        });
        vBoxIconCancelar.setMarginTop(0);
        vBoxIconCancelar.setMarginLeft(0);
        vBoxIconCancelar.setMarginRight(0);
        vBoxIconCancelar.setMarginBottom(0);
        vBoxIconCancelar.setSpacing(1);
        vBoxIconCancelar.setFlexVflex("ftFalse");
        vBoxIconCancelar.setFlexHflex("ftFalse");
        vBoxIconCancelar.setScrollable(false);
        vBoxIconCancelar.setBoxShadowConfigHorizontalLength(10);
        vBoxIconCancelar.setBoxShadowConfigVerticalLength(10);
        vBoxIconCancelar.setBoxShadowConfigBlurRadius(5);
        vBoxIconCancelar.setBoxShadowConfigSpreadRadius(0);
        vBoxIconCancelar.setBoxShadowConfigShadowColor("clBlack");
        vBoxIconCancelar.setBoxShadowConfigOpacity(75);
        hBoxCancelar.addChildren(vBoxIconCancelar);
        vBoxIconCancelar.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(16);
        FHBox5.setHeight(1);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        vBoxIconCancelar.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(2);
        FHBox8.setWidth(14);
        FHBox8.setHeight(3);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftTrue");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        vBoxIconCancelar.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFIconClass iconCancelar = new TFIconClass();

    private void init_iconCancelar() {
        iconCancelar.setName("iconCancelar");
        iconCancelar.setLeft(0);
        iconCancelar.setTop(6);
        iconCancelar.setHint("Cancelar aprova\u00E7\u00E3o POS-Sitef");
        iconCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "iconCancelar", "OnClick");
        });
        iconCancelar.setIconClass("times-circle");
        iconCancelar.setSize(16);
        iconCancelar.setColor("clRed");
        vBoxIconCancelar.addChildren(iconCancelar);
        iconCancelar.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(23);
        FHBox12.setWidth(14);
        FHBox12.setHeight(3);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftTrue");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        vBoxIconCancelar.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFHBox hBoxCancelarEsq = new TFHBox();

    private void init_hBoxCancelarEsq() {
        hBoxCancelarEsq.setName("hBoxCancelarEsq");
        hBoxCancelarEsq.setLeft(28);
        hBoxCancelarEsq.setTop(2);
        hBoxCancelarEsq.setWidth(3);
        hBoxCancelarEsq.setHeight(20);
        hBoxCancelarEsq.setBorderStyle("stNone");
        hBoxCancelarEsq.setPaddingTop(0);
        hBoxCancelarEsq.setPaddingLeft(0);
        hBoxCancelarEsq.setPaddingRight(0);
        hBoxCancelarEsq.setPaddingBottom(0);
        hBoxCancelarEsq.setVisible(false);
        hBoxCancelarEsq.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSepCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "hBoxCancelarEsq", "OnClick");
        });
        hBoxCancelarEsq.setMarginTop(0);
        hBoxCancelarEsq.setMarginLeft(0);
        hBoxCancelarEsq.setMarginRight(0);
        hBoxCancelarEsq.setMarginBottom(0);
        hBoxCancelarEsq.setSpacing(1);
        hBoxCancelarEsq.setFlexVflex("ftFalse");
        hBoxCancelarEsq.setFlexHflex("ftFalse");
        hBoxCancelarEsq.setScrollable(false);
        hBoxCancelarEsq.setBoxShadowConfigHorizontalLength(10);
        hBoxCancelarEsq.setBoxShadowConfigVerticalLength(10);
        hBoxCancelarEsq.setBoxShadowConfigBlurRadius(5);
        hBoxCancelarEsq.setBoxShadowConfigSpreadRadius(0);
        hBoxCancelarEsq.setBoxShadowConfigShadowColor("clBlack");
        hBoxCancelarEsq.setBoxShadowConfigOpacity(75);
        hBoxCancelarEsq.setVAlign("tvTop");
        hBoxCancelar.addChildren(hBoxCancelarEsq);
        hBoxCancelarEsq.applyProperties();
    }

    public TFVBox vBoxBtnCancelar = new TFVBox();

    private void init_vBoxBtnCancelar() {
        vBoxBtnCancelar.setName("vBoxBtnCancelar");
        vBoxBtnCancelar.setLeft(31);
        vBoxBtnCancelar.setTop(2);
        vBoxBtnCancelar.setWidth(58);
        vBoxBtnCancelar.setHeight(38);
        vBoxBtnCancelar.setBorderStyle("stNone");
        vBoxBtnCancelar.setPaddingTop(0);
        vBoxBtnCancelar.setPaddingLeft(0);
        vBoxBtnCancelar.setPaddingRight(0);
        vBoxBtnCancelar.setPaddingBottom(0);
        vBoxBtnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxBtnCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "vBoxBtnCancelar", "OnClick");
        });
        vBoxBtnCancelar.setMarginTop(0);
        vBoxBtnCancelar.setMarginLeft(0);
        vBoxBtnCancelar.setMarginRight(0);
        vBoxBtnCancelar.setMarginBottom(0);
        vBoxBtnCancelar.setSpacing(1);
        vBoxBtnCancelar.setFlexVflex("ftFalse");
        vBoxBtnCancelar.setFlexHflex("ftFalse");
        vBoxBtnCancelar.setScrollable(false);
        vBoxBtnCancelar.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnCancelar.setBoxShadowConfigVerticalLength(10);
        vBoxBtnCancelar.setBoxShadowConfigBlurRadius(5);
        vBoxBtnCancelar.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnCancelar.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnCancelar.setBoxShadowConfigOpacity(75);
        hBoxCancelar.addChildren(vBoxBtnCancelar);
        vBoxBtnCancelar.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(30);
        FHBox6.setHeight(1);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxBtnCancelar.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(2);
        FHBox1.setWidth(30);
        FHBox1.setHeight(3);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftTrue");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxBtnCancelar.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFLabel lblAcaoCancelar = new TFLabel();

    private void init_lblAcaoCancelar() {
        lblAcaoCancelar.setName("lblAcaoCancelar");
        lblAcaoCancelar.setLeft(0);
        lblAcaoCancelar.setTop(6);
        lblAcaoCancelar.setWidth(42);
        lblAcaoCancelar.setHeight(13);
        lblAcaoCancelar.setHint("Cancelar aprova\u00E7\u00E3o POS-Sitef");
        lblAcaoCancelar.setCaption("Cancelar");
        lblAcaoCancelar.setFontColor("clRed");
        lblAcaoCancelar.setFontSize(-11);
        lblAcaoCancelar.setFontName("Tahoma");
        lblAcaoCancelar.setFontStyle("[]");
        lblAcaoCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblAcaoCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "lblAcaoCancelar", "OnClick");
        });
        lblAcaoCancelar.setVerticalAlignment("taVerticalCenter");
        lblAcaoCancelar.setWordBreak(false);
        vBoxBtnCancelar.addChildren(lblAcaoCancelar);
        lblAcaoCancelar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(20);
        FHBox3.setWidth(30);
        FHBox3.setHeight(3);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftTrue");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        vBoxBtnCancelar.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox hBoxCancelarDir = new TFHBox();

    private void init_hBoxCancelarDir() {
        hBoxCancelarDir.setName("hBoxCancelarDir");
        hBoxCancelarDir.setLeft(89);
        hBoxCancelarDir.setTop(2);
        hBoxCancelarDir.setWidth(5);
        hBoxCancelarDir.setHeight(20);
        hBoxCancelarDir.setBorderStyle("stNone");
        hBoxCancelarDir.setPaddingTop(0);
        hBoxCancelarDir.setPaddingLeft(0);
        hBoxCancelarDir.setPaddingRight(0);
        hBoxCancelarDir.setPaddingBottom(0);
        hBoxCancelarDir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSepCancelarClick(event);
            processarFlow("FrmAguardarPagamentoPosSitef", "hBoxCancelarDir", "OnClick");
        });
        hBoxCancelarDir.setMarginTop(0);
        hBoxCancelarDir.setMarginLeft(0);
        hBoxCancelarDir.setMarginRight(0);
        hBoxCancelarDir.setMarginBottom(0);
        hBoxCancelarDir.setSpacing(1);
        hBoxCancelarDir.setFlexVflex("ftFalse");
        hBoxCancelarDir.setFlexHflex("ftFalse");
        hBoxCancelarDir.setScrollable(false);
        hBoxCancelarDir.setBoxShadowConfigHorizontalLength(10);
        hBoxCancelarDir.setBoxShadowConfigVerticalLength(10);
        hBoxCancelarDir.setBoxShadowConfigBlurRadius(5);
        hBoxCancelarDir.setBoxShadowConfigSpreadRadius(0);
        hBoxCancelarDir.setBoxShadowConfigShadowColor("clBlack");
        hBoxCancelarDir.setBoxShadowConfigOpacity(75);
        hBoxCancelarDir.setVAlign("tvTop");
        hBoxCancelar.addChildren(hBoxCancelarDir);
        hBoxCancelarDir.applyProperties();
    }

    public TFHBox hBoxCancelarBaixo = new TFHBox();

    private void init_hBoxCancelarBaixo() {
        hBoxCancelarBaixo.setName("hBoxCancelarBaixo");
        hBoxCancelarBaixo.setLeft(0);
        hBoxCancelarBaixo.setTop(55);
        hBoxCancelarBaixo.setWidth(100);
        hBoxCancelarBaixo.setHeight(5);
        hBoxCancelarBaixo.setBorderStyle("stNone");
        hBoxCancelarBaixo.setPaddingTop(0);
        hBoxCancelarBaixo.setPaddingLeft(0);
        hBoxCancelarBaixo.setPaddingRight(0);
        hBoxCancelarBaixo.setPaddingBottom(0);
        hBoxCancelarBaixo.setMarginTop(0);
        hBoxCancelarBaixo.setMarginLeft(0);
        hBoxCancelarBaixo.setMarginRight(0);
        hBoxCancelarBaixo.setMarginBottom(0);
        hBoxCancelarBaixo.setSpacing(1);
        hBoxCancelarBaixo.setFlexVflex("ftFalse");
        hBoxCancelarBaixo.setFlexHflex("ftFalse");
        hBoxCancelarBaixo.setScrollable(false);
        hBoxCancelarBaixo.setBoxShadowConfigHorizontalLength(10);
        hBoxCancelarBaixo.setBoxShadowConfigVerticalLength(10);
        hBoxCancelarBaixo.setBoxShadowConfigBlurRadius(5);
        hBoxCancelarBaixo.setBoxShadowConfigSpreadRadius(0);
        hBoxCancelarBaixo.setBoxShadowConfigShadowColor("clBlack");
        hBoxCancelarBaixo.setBoxShadowConfigOpacity(75);
        hBoxCancelarBaixo.setVAlign("tvTop");
        vBoxCancelar.addChildren(hBoxCancelarBaixo);
        hBoxCancelarBaixo.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(153);
        FHBox4.setTop(0);
        FHBox4.setWidth(45);
        FHBox4.setHeight(41);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        HBoxRodape.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void frmShow(final Event<Object> event);

    public abstract void hBoxCancelarClick(final Event<Object> event);

    public abstract void hBoxSepCancelarClick(final Event<Object> event);

    public abstract void vBoxIconCancelarClick(final Event<Object> event);

    public abstract void iconCancelarClick(final Event<Object> event);

    public abstract void vBoxBtnCancelarClick(final Event<Object> event);

    public abstract void lblAcaoCancelarClick(final Event<Object> event);

    public abstract void timerPosTimer(final Event<Object> event);

}