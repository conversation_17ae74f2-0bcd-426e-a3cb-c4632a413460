package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmSelecionarPeriodo extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.SelecionarPeriodoRNA rn = null;

    public FrmSelecionarPeriodo() {
        try {
            rn = (freedom.bytecode.rn.SelecionarPeriodoRNA) getRN(freedom.bytecode.rn.wizard.SelecionarPeriodoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxPrincipal();
        init_hBoxLinha01();
        init_dtInicial();
        init_dtFinal();
        init_btnAceitar();
        init_FrmSelecionarPeriodo();
    }

    protected TFForm FrmSelecionarPeriodo = this;
    private void init_FrmSelecionarPeriodo() {
        FrmSelecionarPeriodo.setName("FrmSelecionarPeriodo");
        FrmSelecionarPeriodo.setCaption("Selecionar per\u00EDodo");
        FrmSelecionarPeriodo.setClientHeight(62);
        FrmSelecionarPeriodo.setClientWidth(282);
        FrmSelecionarPeriodo.setColor("clBtnFace");
        FrmSelecionarPeriodo.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            frmShow(event);
            processarFlow("FrmSelecionarPeriodo", "FrmSelecionarPeriodo", "OnCreate");
        });
        FrmSelecionarPeriodo.setWOrigem("EhMain");
        FrmSelecionarPeriodo.setWKey("5300733");
        FrmSelecionarPeriodo.setSpacing(0);
        FrmSelecionarPeriodo.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(282);
        vBoxPrincipal.setHeight(62);
        vBoxPrincipal.setHint("Selecionar per\u00EDodo");
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(5);
        vBoxPrincipal.setPaddingLeft(5);
        vBoxPrincipal.setPaddingRight(5);
        vBoxPrincipal.setPaddingBottom(5);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmSelecionarPeriodo.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxLinha01 = new TFHBox();

    private void init_hBoxLinha01() {
        hBoxLinha01.setName("hBoxLinha01");
        hBoxLinha01.setLeft(0);
        hBoxLinha01.setTop(0);
        hBoxLinha01.setWidth(270);
        hBoxLinha01.setHeight(30);
        hBoxLinha01.setBorderStyle("stNone");
        hBoxLinha01.setPaddingTop(0);
        hBoxLinha01.setPaddingLeft(0);
        hBoxLinha01.setPaddingRight(0);
        hBoxLinha01.setPaddingBottom(0);
        hBoxLinha01.setMarginTop(0);
        hBoxLinha01.setMarginLeft(0);
        hBoxLinha01.setMarginRight(0);
        hBoxLinha01.setMarginBottom(0);
        hBoxLinha01.setSpacing(5);
        hBoxLinha01.setFlexVflex("ftMin");
        hBoxLinha01.setFlexHflex("ftTrue");
        hBoxLinha01.setScrollable(false);
        hBoxLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha01.setBoxShadowConfigOpacity(75);
        hBoxLinha01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha01);
        hBoxLinha01.applyProperties();
    }

    public TFDate dtInicial = new TFDate();

    private void init_dtInicial() {
        dtInicial.setName("dtInicial");
        dtInicial.setLeft(0);
        dtInicial.setTop(0);
        dtInicial.setWidth(121);
        dtInicial.setHeight(24);
        dtInicial.setHint("In\u00EDcio");
        dtInicial.setFlex(false);
        dtInicial.setRequired(true);
        dtInicial.setPrompt("In\u00EDcio");
        dtInicial.setConstraintCheckWhen("cwImmediate");
        dtInicial.setConstraintCheckType("ctExpression");
        dtInicial.setConstraintFocusOnError(false);
        dtInicial.setConstraintEnableUI(true);
        dtInicial.setConstraintEnabled(false);
        dtInicial.setConstraintFormCheck(true);
        dtInicial.setFormat("dd/MM/yyyy");
        dtInicial.setShowCheckBox(false);
        dtInicial.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            dtInicialEnter(event);
            processarFlow("FrmSelecionarPeriodo", "dtInicial", "OnEnter");
        });
        hBoxLinha01.addChildren(dtInicial);
        dtInicial.applyProperties();
        addValidatable(dtInicial);
    }

    public TFDate dtFinal = new TFDate();

    private void init_dtFinal() {
        dtFinal.setName("dtFinal");
        dtFinal.setLeft(121);
        dtFinal.setTop(0);
        dtFinal.setWidth(121);
        dtFinal.setHeight(24);
        dtFinal.setHint("Fim");
        dtFinal.setFlex(false);
        dtFinal.setRequired(true);
        dtFinal.setPrompt("Fim");
        dtFinal.setConstraintCheckWhen("cwImmediate");
        dtFinal.setConstraintCheckType("ctExpression");
        dtFinal.setConstraintFocusOnError(false);
        dtFinal.setConstraintEnableUI(true);
        dtFinal.setConstraintEnabled(false);
        dtFinal.setConstraintFormCheck(true);
        dtFinal.setFormat("dd/MM/yyyy");
        dtFinal.setShowCheckBox(false);
        dtFinal.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            dtFinalEnter(event);
            processarFlow("FrmSelecionarPeriodo", "dtFinal", "OnEnter");
        });
        hBoxLinha01.addChildren(dtFinal);
        dtFinal.applyProperties();
        addValidatable(dtFinal);
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setUploadMime("image/*");
        btnAceitar.setLeft(242);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(21);
        btnAceitar.setHeight(18);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmSelecionarPeriodo", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(0);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconClass("calendar-check-o");
        btnAceitar.setIconReverseDirection(false);        hBoxLinha01.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void frmShow(final Event<Object> event);

    public abstract void dtInicialEnter(final Event<Object> event);

    public abstract void dtFinalEnter(final Event<Object> event);

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}