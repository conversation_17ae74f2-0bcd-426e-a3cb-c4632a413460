package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmLoginCliente extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.LoginClienteRNA rn = null;

    public FrmLoginCliente() {
        try {
            rn = (freedom.bytecode.rn.LoginClienteRNA) getRN(freedom.bytecode.rn.wizard.LoginClienteRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbSchema();
        init_tbSchemaUser();
        init_tbUsuarioLogado();
        init_tbBtobCadastro();
        init_FHBox1();
        init_FHBox2();
        init_FGridPanelLabel();
        init_FLabel4();
        init_lblTema();
        init_FLabel3();
        init_cbxTema();
        init_txbUsuario();
        init_txbSenha();
        init_lblEsqueceuSenha();
        init_FCheckBoxLembrarSenha();
        init_btnLogin();
        init_FHBox3();
        init_FHBox4();
        init_FVBox1();
        init_FHBox5();
        init_FLabel5();
        init_FVBox2();
        init_FrmLoginCliente();
    }

    public NBS_SCHEMA tbSchema;

    private void init_tbSchema() {
        tbSchema = rn.tbSchema;
        tbSchema.setName("tbSchema");
        tbSchema.setMaxRowCount(200);
        tbSchema.setWKey("340052;34001");
        tbSchema.setRatioBatchSize(20);
        getTables().put(tbSchema, "tbSchema");
        tbSchema.applyProperties();
    }

    public NBS_SCHEMA_USER tbSchemaUser;

    private void init_tbSchemaUser() {
        tbSchemaUser = rn.tbSchemaUser;
        tbSchemaUser.setName("tbSchemaUser");
        tbSchemaUser.setMaxRowCount(200);
        tbSchemaUser.setWKey("340052;34003");
        tbSchemaUser.setRatioBatchSize(20);
        getTables().put(tbSchemaUser, "tbSchemaUser");
        tbSchemaUser.applyProperties();
    }

    public USUARIO_LOGADO tbUsuarioLogado;

    private void init_tbUsuarioLogado() {
        tbUsuarioLogado = rn.tbUsuarioLogado;
        tbUsuarioLogado.setName("tbUsuarioLogado");
        tbUsuarioLogado.setMaxRowCount(200);
        tbUsuarioLogado.setWKey("340052;34004");
        tbUsuarioLogado.setRatioBatchSize(20);
        getTables().put(tbUsuarioLogado, "tbUsuarioLogado");
        tbUsuarioLogado.applyProperties();
    }

    public BTOB_CADASTRO tbBtobCadastro;

    private void init_tbBtobCadastro() {
        tbBtobCadastro = rn.tbBtobCadastro;
        tbBtobCadastro.setName("tbBtobCadastro");
        tbBtobCadastro.setMaxRowCount(200);
        tbBtobCadastro.setWKey("340052;34007");
        tbBtobCadastro.setRatioBatchSize(20);
        getTables().put(tbBtobCadastro, "tbBtobCadastro");
        tbBtobCadastro.applyProperties();
    }

    protected TFForm FrmLoginCliente = this;
    private void init_FrmLoginCliente() {
        FrmLoginCliente.setName("FrmLoginCliente");
        FrmLoginCliente.setCaption("Form Login Cliente");
        FrmLoginCliente.setClientHeight(430);
        FrmLoginCliente.setClientWidth(826);
        FrmLoginCliente.setColor("clBtnFace");
        FrmLoginCliente.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmLoginCliente", "FrmLoginCliente", "OnCreate");
        });
        FrmLoginCliente.setWKey("340052");
        FrmLoginCliente.setSpacing(0);
        FrmLoginCliente.setBackgroundImage("images/CRM.jpg");
        FrmLoginCliente.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(826);
        FHBox1.setHeight(33);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FrmLoginCliente.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(32);
        FHBox2.setWidth(824);
        FHBox2.setHeight(367);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FrmLoginCliente.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFGridPanel FGridPanelLabel = new TFGridPanel();

    private void init_FGridPanelLabel() {
        FGridPanelLabel.setName("FGridPanelLabel");
        FGridPanelLabel.setLeft(0);
        FGridPanelLabel.setTop(0);
        FGridPanelLabel.setWidth(697);
        FGridPanelLabel.setHeight(197);
        FGridPanelLabel.setAlign("alClient");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(84.223292793855070000);
        FGridPanelLabel.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setSizeStyle("ssAbsolute");
        item1.setValue(180.000000000000000000);
        FGridPanelLabel.getColumnCollection().add(item1);
        TFGridPanelColumn item2 = new TFGridPanelColumn();
        item2.setValue(15.776707206144930000);
        FGridPanelLabel.getColumnCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("cbxTema");
        item3.setRow(0);
        FGridPanelLabel.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(1);
        item4.setControl("txbUsuario");
        item4.setRow(1);
        FGridPanelLabel.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("txbSenha");
        item5.setRow(2);
        FGridPanelLabel.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(1);
        item6.setControl("btnLogin");
        item6.setRow(5);
        FGridPanelLabel.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(0);
        item7.setControl("lblTema");
        item7.setRow(0);
        FGridPanelLabel.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(0);
        item8.setControl("FLabel3");
        item8.setRow(1);
        FGridPanelLabel.getControlCollection().add(item8);
        TFControlItem item9 = new TFControlItem();
        item9.setColumn(0);
        item9.setControl("FLabel4");
        item9.setRow(2);
        FGridPanelLabel.getControlCollection().add(item9);
        TFControlItem item10 = new TFControlItem();
        item10.setColumn(1);
        item10.setControl("FCheckBoxLembrarSenha");
        item10.setRow(3);
        FGridPanelLabel.getControlCollection().add(item10);
        TFControlItem item11 = new TFControlItem();
        item11.setColumn(1);
        item11.setControl("lblEsqueceuSenha");
        item11.setRow(4);
        FGridPanelLabel.getControlCollection().add(item11);
        TFGridPanelRow item12 = new TFGridPanelRow();
        item12.setSizeStyle("ssAuto");
        FGridPanelLabel.getRowCollection().add(item12);
        TFGridPanelRow item13 = new TFGridPanelRow();
        item13.setSizeStyle("ssAuto");
        item13.setValue(100.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item13);
        TFGridPanelRow item14 = new TFGridPanelRow();
        item14.setSizeStyle("ssAuto");
        item14.setValue(50.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item14);
        TFGridPanelRow item15 = new TFGridPanelRow();
        item15.setSizeStyle("ssAuto");
        item15.setValue(100.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item15);
        TFGridPanelRow item16 = new TFGridPanelRow();
        item16.setSizeStyle("ssAuto");
        FGridPanelLabel.getRowCollection().add(item16);
        TFGridPanelRow item17 = new TFGridPanelRow();
        item17.setSizeStyle("ssAuto");
        item17.setValue(100.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item17);
        FGridPanelLabel.setFlexVflex("ftFalse");
        FGridPanelLabel.setFlexHflex("ftTrue");
        FGridPanelLabel.setAllRowFlex(true);
        FGridPanelLabel.setColumnTabOrder(false);
        FHBox2.addChildren(FGridPanelLabel);
        FGridPanelLabel.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(396);
        FLabel4.setTop(43);
        FLabel4.setWidth(38);
        FLabel4.setHeight(21);
        FLabel4.setAlign("alRight");
        FLabel4.setCaption("Senha:");
        FLabel4.setFontColor("16645629");
        FLabel4.setFontSize(-12);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FGridPanelLabel.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFLabel lblTema = new TFLabel();

    private void init_lblTema() {
        lblTema.setName("lblTema");
        lblTema.setLeft(399);
        lblTema.setTop(1);
        lblTema.setWidth(35);
        lblTema.setHeight(21);
        lblTema.setAlign("alRight");
        lblTema.setCaption("Tema:");
        lblTema.setColor("clBtnFace");
        lblTema.setFontColor("16645629");
        lblTema.setFontSize(-12);
        lblTema.setFontName("Tahoma");
        lblTema.setFontStyle("[]");
        lblTema.setVerticalAlignment("taVerticalCenter");
        FGridPanelLabel.addChildren(lblTema);
        lblTema.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(399);
        FLabel3.setTop(22);
        FLabel3.setWidth(35);
        FLabel3.setHeight(21);
        FLabel3.setAlign("alRight");
        FLabel3.setCaption("E-mail:");
        FLabel3.setFontColor("16645629");
        FLabel3.setFontSize(-12);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FGridPanelLabel.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFCombo cbxTema = new TFCombo();

    private void init_cbxTema() {
        cbxTema.setName("cbxTema");
        cbxTema.setLeft(434);
        cbxTema.setTop(1);
        cbxTema.setWidth(145);
        cbxTema.setHeight(21);
        cbxTema.setFlex(true);
        cbxTema.setReadOnly(false);
        cbxTema.setRequired(false);
        cbxTema.setPrompt("Selecione");
        cbxTema.setConstraintCheckWhen("cwImmediate");
        cbxTema.setConstraintCheckType("ctExpression");
        cbxTema.setConstraintFocusOnError(false);
        cbxTema.setConstraintEnableUI(true);
        cbxTema.setConstraintEnabled(false);
        cbxTema.setConstraintFormCheck(true);
        cbxTema.setClearOnDelKey(true);
        cbxTema.setUseClearButton(false);
        cbxTema.setHideClearButtonOnNullValue(false);
        cbxTema.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbxTemaChange(event);
            processarFlow("FrmLoginCliente", "cbxTema", "OnChange");
        });
        FGridPanelLabel.addChildren(cbxTema);
        cbxTema.applyProperties();
        addValidatable(cbxTema);
    }

    public TFString txbUsuario = new TFString();

    private void init_txbUsuario() {
        txbUsuario.setName("txbUsuario");
        txbUsuario.setLeft(434);
        txbUsuario.setTop(22);
        txbUsuario.setWidth(145);
        txbUsuario.setHeight(21);
        txbUsuario.setFlex(true);
        txbUsuario.setRequired(true);
        txbUsuario.setConstraintCheckWhen("cwImmediate");
        txbUsuario.setConstraintCheckType("ctExpression");
        txbUsuario.setConstraintFocusOnError(false);
        txbUsuario.setConstraintEnableUI(true);
        txbUsuario.setConstraintEnabled(false);
        txbUsuario.setConstraintFormCheck(true);
        txbUsuario.setCharCase("ccUpper");
        txbUsuario.setPwd(false);
        txbUsuario.setMaxlength(0);
        txbUsuario.setFontColor("clWindowText");
        txbUsuario.setFontSize(-11);
        txbUsuario.setFontName("Tahoma");
        txbUsuario.setFontStyle("[]");
        txbUsuario.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            txbUsuarioEnter(event);
            processarFlow("FrmLoginCliente", "txbUsuario", "OnEnter");
        });
        txbUsuario.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            txbUsuarioExit(event);
            processarFlow("FrmLoginCliente", "txbUsuario", "OnExit");
        });
        txbUsuario.setSaveLiteralCharacter(true);
        txbUsuario.applyProperties();
        FGridPanelLabel.addChildren(txbUsuario);
        addValidatable(txbUsuario);
    }

    public TFString txbSenha = new TFString();

    private void init_txbSenha() {
        txbSenha.setName("txbSenha");
        txbSenha.setLeft(434);
        txbSenha.setTop(43);
        txbSenha.setWidth(145);
        txbSenha.setHeight(21);
        txbSenha.setFlex(true);
        txbSenha.setRequired(true);
        txbSenha.setConstraintCheckWhen("cwImmediate");
        txbSenha.setConstraintCheckType("ctExpression");
        txbSenha.setConstraintFocusOnError(false);
        txbSenha.setConstraintEnableUI(true);
        txbSenha.setConstraintEnabled(false);
        txbSenha.setConstraintFormCheck(true);
        txbSenha.setCharCase("ccNormal");
        txbSenha.setPwd(true);
        txbSenha.setMaxlength(0);
        txbSenha.setFontColor("clWindowText");
        txbSenha.setFontSize(-11);
        txbSenha.setFontName("Tahoma");
        txbSenha.setFontStyle("[]");
        txbSenha.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            txbSenhaEnter(event);
            processarFlow("FrmLoginCliente", "txbSenha", "OnEnter");
        });
        txbSenha.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            txbSenhaExit(event);
            processarFlow("FrmLoginCliente", "txbSenha", "OnExit");
        });
        txbSenha.setSaveLiteralCharacter(true);
        txbSenha.applyProperties();
        FGridPanelLabel.addChildren(txbSenha);
        addValidatable(txbSenha);
    }

    public TFLabel lblEsqueceuSenha = new TFLabel();

    private void init_lblEsqueceuSenha() {
        lblEsqueceuSenha.setName("lblEsqueceuSenha");
        lblEsqueceuSenha.setLeft(434);
        lblEsqueceuSenha.setTop(81);
        lblEsqueceuSenha.setWidth(84);
        lblEsqueceuSenha.setHeight(13);
        lblEsqueceuSenha.setAlign("alLeft");
        lblEsqueceuSenha.setCaption("Esqueceu Senha?");
        lblEsqueceuSenha.setFontColor("clWindowText");
        lblEsqueceuSenha.setFontSize(-11);
        lblEsqueceuSenha.setFontName("Tahoma");
        lblEsqueceuSenha.setFontStyle("[fsUnderline]");
        lblEsqueceuSenha.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblEsqueceuSenhaClick(event);
            processarFlow("FrmLoginCliente", "lblEsqueceuSenha", "OnClick");
        });
        lblEsqueceuSenha.setVerticalAlignment("taVerticalCenter");
        FGridPanelLabel.addChildren(lblEsqueceuSenha);
        lblEsqueceuSenha.applyProperties();
    }

    public TFCheckBox FCheckBoxLembrarSenha = new TFCheckBox();

    private void init_FCheckBoxLembrarSenha() {
        FCheckBoxLembrarSenha.setName("FCheckBoxLembrarSenha");
        FCheckBoxLembrarSenha.setLeft(475);
        FCheckBoxLembrarSenha.setTop(64);
        FCheckBoxLembrarSenha.setWidth(97);
        FCheckBoxLembrarSenha.setHeight(17);
        FCheckBoxLembrarSenha.setCaption("Lembrar Senha?");
        FCheckBoxLembrarSenha.setFontColor("clBlack");
        FCheckBoxLembrarSenha.setFontSize(-11);
        FCheckBoxLembrarSenha.setFontName("Tahoma");
        FCheckBoxLembrarSenha.setFontStyle("[]");
        FCheckBoxLembrarSenha.setCheckedValue("S");
        FCheckBoxLembrarSenha.setUncheckedValue("N");
        FCheckBoxLembrarSenha.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FCheckBoxLembrarSenhaCheck(event);
            processarFlow("FrmLoginCliente", "FCheckBoxLembrarSenha", "OnCheck");
        });
        FCheckBoxLembrarSenha.setVerticalAlignment("taAlignTop");
        FGridPanelLabel.addChildren(FCheckBoxLembrarSenha);
        FCheckBoxLembrarSenha.applyProperties();
    }

    public TFButton btnLogin = new TFButton();

    private void init_btnLogin() {
        btnLogin.setName("btnLogin");
        btnLogin.setLeft(539);
        btnLogin.setTop(94);
        btnLogin.setWidth(75);
        btnLogin.setHeight(25);
        btnLogin.setAlign("alRight");
        btnLogin.setCaption("Login");
        btnLogin.setFontColor("clWindowText");
        btnLogin.setFontSize(-11);
        btnLogin.setFontName("Tahoma");
        btnLogin.setFontStyle("[]");
        btnLogin.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLoginClick(event);
            processarFlow("FrmLoginCliente", "btnLogin", "OnClick");
        });
        btnLogin.setImageId(0);
        btnLogin.setColor("clBtnFace");
        btnLogin.setAccess(false);
        btnLogin.setIconReverseDirection(false);
        FGridPanelLabel.addChildren(btnLogin);
        btnLogin.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(402);
        FHBox3.setWidth(842);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setColor("6776679");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(0);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FrmLoginCliente.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(409);
        FHBox4.setHeight(23);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox3.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(409);
        FVBox1.setTop(0);
        FVBox1.setWidth(392);
        FVBox1.setHeight(23);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(0);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftMin");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(185);
        FHBox5.setHeight(9);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(0);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox1.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(10);
        FLabel5.setWidth(278);
        FLabel5.setHeight(13);
        FLabel5.setAlign("alRight");
        FLabel5.setCaption("Copyright NBS Inform\u00E1tica - Todos os direitos reservados ");
        FLabel5.setFontColor("16645629");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FVBox1.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(801);
        FVBox2.setTop(0);
        FVBox2.setWidth(10);
        FVBox2.setHeight(7);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void cbxTemaChange(final Event<Object> event);

    public abstract void txbUsuarioEnter(final Event<Object> event);

    public abstract void txbUsuarioExit(final Event<Object> event);

    public abstract void txbSenhaEnter(final Event<Object> event);

    public abstract void txbSenhaExit(final Event<Object> event);

    public void btnLoginClick(final Event<Object> event) {
        if (btnLogin.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLogin");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void FCheckBoxLembrarSenhaCheck(final Event<Object> event);

    public abstract void lblEsqueceuSenhaClick(final Event<Object> event);

}