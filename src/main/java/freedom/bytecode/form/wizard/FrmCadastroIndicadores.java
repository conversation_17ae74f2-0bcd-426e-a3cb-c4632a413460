package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadastroIndicadores extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadastroIndicadoresRNA rn = null;

    public FrmCadastroIndicadores() {
        try {
            rn = (freedom.bytecode.rn.CadastroIndicadoresRNA) getRN(freedom.bytecode.rn.wizard.CadastroIndicadoresRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbComboGrupoIndicadores();
        init_tbPainelIndicador();
        init_tbGridGrupoIndicadores();
        init_tbMaxDisplayOrder();
        init_FVBox1();
        init_FHBox1();
        init_btnSalvar();
        init_btnCancelar();
        init_FVBox3();
        init_FLabel2();
        init_cbbGrupoIndicadores();
        init_gridIndicadores();
        init_FrmCadastroIndicadores();
    }

    public BSC_COMBO_GRUPO_INDICADORES tbComboGrupoIndicadores;

    private void init_tbComboGrupoIndicadores() {
        tbComboGrupoIndicadores = rn.tbComboGrupoIndicadores;
        tbComboGrupoIndicadores.setName("tbComboGrupoIndicadores");
        tbComboGrupoIndicadores.setMaxRowCount(200);
        tbComboGrupoIndicadores.setWKey("382033;38201");
        tbComboGrupoIndicadores.setRatioBatchSize(20);
        getTables().put(tbComboGrupoIndicadores, "tbComboGrupoIndicadores");
        tbComboGrupoIndicadores.applyProperties();
    }

    public BSC_PAINEL_INDICADOR tbPainelIndicador;

    private void init_tbPainelIndicador() {
        tbPainelIndicador = rn.tbPainelIndicador;
        tbPainelIndicador.setName("tbPainelIndicador");
        tbPainelIndicador.setMaxRowCount(200);
        tbPainelIndicador.setWKey("382033;38203");
        tbPainelIndicador.setRatioBatchSize(20);
        getTables().put(tbPainelIndicador, "tbPainelIndicador");
        tbPainelIndicador.applyProperties();
    }

    public BSC_GRID_GRUPO_INDICADORES tbGridGrupoIndicadores;

    private void init_tbGridGrupoIndicadores() {
        tbGridGrupoIndicadores = rn.tbGridGrupoIndicadores;
        tbGridGrupoIndicadores.setName("tbGridGrupoIndicadores");
        tbGridGrupoIndicadores.setMaxRowCount(200);
        tbGridGrupoIndicadores.setWKey("382033;38204");
        tbGridGrupoIndicadores.setRatioBatchSize(20);
        getTables().put(tbGridGrupoIndicadores, "tbGridGrupoIndicadores");
        tbGridGrupoIndicadores.applyProperties();
    }

    public BSC_MAX_DISPLAY_ORDER tbMaxDisplayOrder;

    private void init_tbMaxDisplayOrder() {
        tbMaxDisplayOrder = rn.tbMaxDisplayOrder;
        tbMaxDisplayOrder.setName("tbMaxDisplayOrder");
        tbMaxDisplayOrder.setMaxRowCount(200);
        tbMaxDisplayOrder.setWKey("382033;38205");
        tbMaxDisplayOrder.setRatioBatchSize(20);
        getTables().put(tbMaxDisplayOrder, "tbMaxDisplayOrder");
        tbMaxDisplayOrder.applyProperties();
    }

    protected TFForm FrmCadastroIndicadores = this;
    private void init_FrmCadastroIndicadores() {
        FrmCadastroIndicadores.setName("FrmCadastroIndicadores");
        FrmCadastroIndicadores.setCaption("Novo Indicadores");
        FrmCadastroIndicadores.setClientHeight(453);
        FrmCadastroIndicadores.setClientWidth(556);
        FrmCadastroIndicadores.setColor("clBtnFace");
        FrmCadastroIndicadores.setWKey("382033");
        FrmCadastroIndicadores.setSpacing(0);
        FrmCadastroIndicadores.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(556);
        FVBox1.setHeight(453);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmCadastroIndicadores.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(476);
        FHBox1.setHeight(70);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(75);
        btnSalvar.setHeight(60);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadastroIndicadores", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(75);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(75);
        btnCancelar.setHeight(60);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmCadastroIndicadores", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(71);
        FVBox3.setWidth(476);
        FVBox3.setHeight(57);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(2);
        FVBox3.setFlexVflex("ftMin");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(37);
        FLabel2.setHeight(14);
        FLabel2.setCaption("Grupo");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-12);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox3.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cbbGrupoIndicadores = new TFCombo();

    private void init_cbbGrupoIndicadores() {
        cbbGrupoIndicadores.setName("cbbGrupoIndicadores");
        cbbGrupoIndicadores.setLeft(0);
        cbbGrupoIndicadores.setTop(15);
        cbbGrupoIndicadores.setWidth(145);
        cbbGrupoIndicadores.setHeight(21);
        cbbGrupoIndicadores.setLookupTable(tbComboGrupoIndicadores);
        cbbGrupoIndicadores.setFieldName("ID_GRUPO");
        cbbGrupoIndicadores.setLookupKey("ID");
        cbbGrupoIndicadores.setLookupDesc("DESCRICAO_CLASSE");
        cbbGrupoIndicadores.setFlex(true);
        cbbGrupoIndicadores.setReadOnly(true);
        cbbGrupoIndicadores.setRequired(true);
        cbbGrupoIndicadores.setPrompt("Selecione");
        cbbGrupoIndicadores.setConstraintCheckWhen("cwImmediate");
        cbbGrupoIndicadores.setConstraintCheckType("ctExpression");
        cbbGrupoIndicadores.setConstraintFocusOnError(false);
        cbbGrupoIndicadores.setConstraintEnableUI(true);
        cbbGrupoIndicadores.setConstraintEnabled(false);
        cbbGrupoIndicadores.setConstraintFormCheck(true);
        cbbGrupoIndicadores.setClearOnDelKey(true);
        cbbGrupoIndicadores.setUseClearButton(false);
        cbbGrupoIndicadores.setHideClearButtonOnNullValue(false);
        cbbGrupoIndicadores.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbGrupoIndicadoresChange(event);
            processarFlow("FrmCadastroIndicadores", "cbbGrupoIndicadores", "OnChange");
        });
        FVBox3.addChildren(cbbGrupoIndicadores);
        cbbGrupoIndicadores.applyProperties();
        addValidatable(cbbGrupoIndicadores);
    }

    public TFGrid gridIndicadores = new TFGrid();

    private void init_gridIndicadores() {
        gridIndicadores.setName("gridIndicadores");
        gridIndicadores.setLeft(0);
        gridIndicadores.setTop(129);
        gridIndicadores.setWidth(474);
        gridIndicadores.setHeight(232);
        gridIndicadores.setTable(tbGridGrupoIndicadores);
        gridIndicadores.setFlexVflex("ftTrue");
        gridIndicadores.setFlexHflex("ftTrue");
        gridIndicadores.setPagingEnabled(false);
        gridIndicadores.setFrozenColumns(0);
        gridIndicadores.setShowFooter(false);
        gridIndicadores.setShowHeader(true);
        gridIndicadores.setMultiSelection(false);
        gridIndicadores.setGroupingEnabled(false);
        gridIndicadores.setGroupingExpanded(false);
        gridIndicadores.setGroupingShowFooter(false);
        gridIndicadores.setCrosstabEnabled(false);
        gridIndicadores.setCrosstabGroupType("cgtConcat");
        gridIndicadores.setEditionEnabled(false);
        gridIndicadores.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEL");
        item0.setTitleCaption("Sel");
        item0.setWidth(60);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL = 'N'");
        item1.setEvalType("etExpression");
        item1.setImageId(34006);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridIndicadoresgridIndUnChecked(event);
            processarFlow("FrmCadastroIndicadores", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL = 'S'");
        item2.setEvalType("etExpression");
        item2.setImageId(34007);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridIndicadoresgridIndChecked(event);
            processarFlow("FrmCadastroIndicadores", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("ID");
        item3.setTitleCaption("Id");
        item3.setWidth(60);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DESCRICAO");
        item4.setTitleCaption("Descri\u00E7\u00E3o");
        item4.setWidth(40);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("UN");
        item5.setTitleCaption("Un");
        item5.setWidth(60);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item5);
        FVBox1.addChildren(gridIndicadores);
        gridIndicadores.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbbGrupoIndicadoresChange(final Event<Object> event);

    public abstract void gridIndicadoresgridIndUnChecked(final Event<Object> event);

    public abstract void gridIndicadoresgridIndChecked(final Event<Object> event);

}