package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClientesFlagsServAcesso extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClientesFlagsServAcessoRNA rn = null;

    public FrmClientesFlagsServAcesso() {
        try {
            rn = (freedom.bytecode.rn.ClientesFlagsServAcessoRNA) getRN(freedom.bytecode.rn.wizard.ClientesFlagsServAcessoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresasFuncoes();
        init_tbClienteFlagGrupo();
        init_tbEmpresasUsuarios();
        init_tbClienteFlagAcesso();
        init_vboxPrincipal();
        init_hboxPesquisa();
        init_vboxCombo();
        init_cbbGrupo();
        init_FHBox1();
        init_cbbFuncoes();
        init_cbbNome();
        init_btnAdicionar();
        init_hboxGrid();
        init_gridCliFlagAcesso();
        init_sc();
        init_FrmClientesFlagsServAcesso();
    }

    public EMPRESAS_FUNCOES tbEmpresasFuncoes;

    private void init_tbEmpresasFuncoes() {
        tbEmpresasFuncoes = rn.tbEmpresasFuncoes;
        tbEmpresasFuncoes.setName("tbEmpresasFuncoes");
        tbEmpresasFuncoes.setMaxRowCount(200);
        tbEmpresasFuncoes.setWKey("4600456;46001");
        tbEmpresasFuncoes.setRatioBatchSize(20);
        getTables().put(tbEmpresasFuncoes, "tbEmpresasFuncoes");
        tbEmpresasFuncoes.applyProperties();
    }

    public CLIENTE_FLAG_GRUPO tbClienteFlagGrupo;

    private void init_tbClienteFlagGrupo() {
        tbClienteFlagGrupo = rn.tbClienteFlagGrupo;
        tbClienteFlagGrupo.setName("tbClienteFlagGrupo");
        tbClienteFlagGrupo.setMaxRowCount(200);
        tbClienteFlagGrupo.setWKey("4600456;46002");
        tbClienteFlagGrupo.setRatioBatchSize(20);
        getTables().put(tbClienteFlagGrupo, "tbClienteFlagGrupo");
        tbClienteFlagGrupo.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.addEventListener("onMaxRow", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbEmpresasUsuariosMaxRow(event);
            processarFlow("FrmClientesFlagsServAcesso", "tbEmpresasUsuarios", "OnMaxRow");
        });
        tbEmpresasUsuarios.setWKey("4600456;46004");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public CLIENTE_FLAG_ACESSO tbClienteFlagAcesso;

    private void init_tbClienteFlagAcesso() {
        tbClienteFlagAcesso = rn.tbClienteFlagAcesso;
        tbClienteFlagAcesso.setName("tbClienteFlagAcesso");
        tbClienteFlagAcesso.setMaxRowCount(200);
        tbClienteFlagAcesso.setWKey("4600456;46005");
        tbClienteFlagAcesso.setRatioBatchSize(20);
        getTables().put(tbClienteFlagAcesso, "tbClienteFlagAcesso");
        tbClienteFlagAcesso.applyProperties();
    }

    protected TFForm FrmClientesFlagsServAcesso = this;
    private void init_FrmClientesFlagsServAcesso() {
        FrmClientesFlagsServAcesso.setName("FrmClientesFlagsServAcesso");
        FrmClientesFlagsServAcesso.setCaption("Clientes Flags Acesso");
        FrmClientesFlagsServAcesso.setClientHeight(442);
        FrmClientesFlagsServAcesso.setClientWidth(737);
        FrmClientesFlagsServAcesso.setColor("clBtnFace");
        FrmClientesFlagsServAcesso.setWKey("4600456");
        FrmClientesFlagsServAcesso.setSpacing(0);
        FrmClientesFlagsServAcesso.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(737);
        vboxPrincipal.setHeight(442);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(0);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(0);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(3);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmClientesFlagsServAcesso.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox hboxPesquisa = new TFHBox();

    private void init_hboxPesquisa() {
        hboxPesquisa.setName("hboxPesquisa");
        hboxPesquisa.setLeft(0);
        hboxPesquisa.setTop(0);
        hboxPesquisa.setWidth(730);
        hboxPesquisa.setHeight(80);
        hboxPesquisa.setBorderStyle("stNone");
        hboxPesquisa.setPaddingTop(0);
        hboxPesquisa.setPaddingLeft(0);
        hboxPesquisa.setPaddingRight(0);
        hboxPesquisa.setPaddingBottom(0);
        hboxPesquisa.setMarginTop(0);
        hboxPesquisa.setMarginLeft(0);
        hboxPesquisa.setMarginRight(0);
        hboxPesquisa.setMarginBottom(0);
        hboxPesquisa.setSpacing(1);
        hboxPesquisa.setFlexVflex("ftFalse");
        hboxPesquisa.setFlexHflex("ftTrue");
        hboxPesquisa.setScrollable(false);
        hboxPesquisa.setBoxShadowConfigHorizontalLength(10);
        hboxPesquisa.setBoxShadowConfigVerticalLength(10);
        hboxPesquisa.setBoxShadowConfigBlurRadius(5);
        hboxPesquisa.setBoxShadowConfigSpreadRadius(0);
        hboxPesquisa.setBoxShadowConfigShadowColor("clBlack");
        hboxPesquisa.setBoxShadowConfigOpacity(75);
        hboxPesquisa.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxPesquisa);
        hboxPesquisa.applyProperties();
    }

    public TFVBox vboxCombo = new TFVBox();

    private void init_vboxCombo() {
        vboxCombo.setName("vboxCombo");
        vboxCombo.setLeft(0);
        vboxCombo.setTop(0);
        vboxCombo.setWidth(644);
        vboxCombo.setHeight(80);
        vboxCombo.setBorderStyle("stNone");
        vboxCombo.setPaddingTop(0);
        vboxCombo.setPaddingLeft(5);
        vboxCombo.setPaddingRight(5);
        vboxCombo.setPaddingBottom(0);
        vboxCombo.setMarginTop(0);
        vboxCombo.setMarginLeft(0);
        vboxCombo.setMarginRight(0);
        vboxCombo.setMarginBottom(0);
        vboxCombo.setSpacing(5);
        vboxCombo.setFlexVflex("ftFalse");
        vboxCombo.setFlexHflex("ftTrue");
        vboxCombo.setScrollable(false);
        vboxCombo.setBoxShadowConfigHorizontalLength(10);
        vboxCombo.setBoxShadowConfigVerticalLength(10);
        vboxCombo.setBoxShadowConfigBlurRadius(5);
        vboxCombo.setBoxShadowConfigSpreadRadius(0);
        vboxCombo.setBoxShadowConfigShadowColor("clBlack");
        vboxCombo.setBoxShadowConfigOpacity(75);
        hboxPesquisa.addChildren(vboxCombo);
        vboxCombo.applyProperties();
    }

    public TFCombo cbbGrupo = new TFCombo();

    private void init_cbbGrupo() {
        cbbGrupo.setName("cbbGrupo");
        cbbGrupo.setLeft(0);
        cbbGrupo.setTop(0);
        cbbGrupo.setWidth(145);
        cbbGrupo.setHeight(21);
        cbbGrupo.setLookupTable(tbClienteFlagGrupo);
        cbbGrupo.setLookupKey("ID_GRUPO");
        cbbGrupo.setLookupDesc("DESCRICAO");
        cbbGrupo.setFlex(true);
        cbbGrupo.setReadOnly(true);
        cbbGrupo.setRequired(false);
        cbbGrupo.setPrompt("Grupo");
        cbbGrupo.setConstraintCheckWhen("cwImmediate");
        cbbGrupo.setConstraintCheckType("ctExpression");
        cbbGrupo.setConstraintFocusOnError(false);
        cbbGrupo.setConstraintEnableUI(true);
        cbbGrupo.setConstraintEnabled(false);
        cbbGrupo.setConstraintFormCheck(true);
        cbbGrupo.setClearOnDelKey(true);
        cbbGrupo.setUseClearButton(true);
        cbbGrupo.setHideClearButtonOnNullValue(false);
        cbbGrupo.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbGrupoChange(event);
            processarFlow("FrmClientesFlagsServAcesso", "cbbGrupo", "OnChange");
        });
        cbbGrupo.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbGrupoClearClick(event);
            processarFlow("FrmClientesFlagsServAcesso", "cbbGrupo", "OnClearClick");
        });
        vboxCombo.addChildren(cbbGrupo);
        cbbGrupo.applyProperties();
        addValidatable(cbbGrupo);
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(22);
        FHBox1.setWidth(727);
        FHBox1.setHeight(55);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(4);
        FHBox1.setFlexVflex("ftTrue");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vboxCombo.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFCombo cbbFuncoes = new TFCombo();

    private void init_cbbFuncoes() {
        cbbFuncoes.setName("cbbFuncoes");
        cbbFuncoes.setLeft(0);
        cbbFuncoes.setTop(0);
        cbbFuncoes.setWidth(145);
        cbbFuncoes.setHeight(21);
        cbbFuncoes.setLookupTable(tbEmpresasFuncoes);
        cbbFuncoes.setLookupKey("COD_FUNCAO");
        cbbFuncoes.setLookupDesc("DESCRICAO");
        cbbFuncoes.setFlex(true);
        cbbFuncoes.setReadOnly(true);
        cbbFuncoes.setRequired(false);
        cbbFuncoes.setPrompt("Fun\u00E7\u00E3o");
        cbbFuncoes.setConstraintCheckWhen("cwImmediate");
        cbbFuncoes.setConstraintCheckType("ctExpression");
        cbbFuncoes.setConstraintFocusOnError(false);
        cbbFuncoes.setConstraintEnableUI(true);
        cbbFuncoes.setConstraintEnabled(false);
        cbbFuncoes.setConstraintFormCheck(true);
        cbbFuncoes.setClearOnDelKey(true);
        cbbFuncoes.setUseClearButton(true);
        cbbFuncoes.setHideClearButtonOnNullValue(false);
        cbbFuncoes.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbFuncoesChange(event);
            processarFlow("FrmClientesFlagsServAcesso", "cbbFuncoes", "OnChange");
        });
        cbbFuncoes.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbFuncoesClearClick(event);
            processarFlow("FrmClientesFlagsServAcesso", "cbbFuncoes", "OnClearClick");
        });
        FHBox1.addChildren(cbbFuncoes);
        cbbFuncoes.applyProperties();
        addValidatable(cbbFuncoes);
    }

    public TFCombo cbbNome = new TFCombo();

    private void init_cbbNome() {
        cbbNome.setName("cbbNome");
        cbbNome.setLeft(145);
        cbbNome.setTop(0);
        cbbNome.setWidth(145);
        cbbNome.setHeight(21);
        cbbNome.setLookupTable(tbEmpresasUsuarios);
        cbbNome.setLookupKey("NOME");
        cbbNome.setLookupDesc("NOME_COMPLETO");
        cbbNome.setFlex(true);
        cbbNome.setReadOnly(true);
        cbbNome.setRequired(false);
        cbbNome.setPrompt("Nome usu\u00E1rio");
        cbbNome.setConstraintCheckWhen("cwImmediate");
        cbbNome.setConstraintCheckType("ctExpression");
        cbbNome.setConstraintFocusOnError(false);
        cbbNome.setConstraintEnableUI(true);
        cbbNome.setConstraintEnabled(false);
        cbbNome.setConstraintFormCheck(true);
        cbbNome.setClearOnDelKey(true);
        cbbNome.setUseClearButton(true);
        cbbNome.setHideClearButtonOnNullValue(false);
        cbbNome.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbNomeChange(event);
            processarFlow("FrmClientesFlagsServAcesso", "cbbNome", "OnChange");
        });
        cbbNome.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbNomeClearClick(event);
            processarFlow("FrmClientesFlagsServAcesso", "cbbNome", "OnClearClick");
        });
        FHBox1.addChildren(cbbNome);
        cbbNome.applyProperties();
        addValidatable(cbbNome);
    }

    public TFButton btnAdicionar = new TFButton();

    private void init_btnAdicionar() {
        btnAdicionar.setName("btnAdicionar");
        btnAdicionar.setLeft(644);
        btnAdicionar.setTop(0);
        btnAdicionar.setWidth(85);
        btnAdicionar.setHeight(75);
        btnAdicionar.setFontColor("clWindowText");
        btnAdicionar.setFontSize(-11);
        btnAdicionar.setFontName("Tahoma");
        btnAdicionar.setFontStyle("[]");
        btnAdicionar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAdicionarClick(event);
            processarFlow("FrmClientesFlagsServAcesso", "btnAdicionar", "OnClick");
        });
        btnAdicionar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000000EE4944415478DAED94BB0AC23018850D88820FE165557077535CBBF5"
 + "4144049FC307D151111C74707475B555475DDC1C84FA1552282537A5E2D2C0E1"
 + "87E43FE70B491B51FAF11005A000E40F88A2C8A74CD0014D85104F5B089E3A65"
 + "866AB117CF5109A07148D9A4E657C83741F034285BD4925337D4C6735701E25D"
 + "8C32195A88223C191EFD4B15A047D9A18A0D6208BFA02EBD0FDD1D7894850962"
 + "08BFA23E3D27E51D3842C668ADD9F9201DAE0558202F54760937022C906C787C"
 + "2C816AD1FAA35920C6702780017296C71298BCCE4F8584CC511585323CB4F93E"
 + "7A8B8034291DB44FBEF35C01DF8C02F07FC01B129766193AD608920000000049"
 + "454E44AE426082");
        btnAdicionar.setImageId(4600164);
        btnAdicionar.setColor("clBtnFace");
        btnAdicionar.setAccess(false);
        btnAdicionar.setIconReverseDirection(false);
        hboxPesquisa.addChildren(btnAdicionar);
        btnAdicionar.applyProperties();
    }

    public TFHBox hboxGrid = new TFHBox();

    private void init_hboxGrid() {
        hboxGrid.setName("hboxGrid");
        hboxGrid.setLeft(0);
        hboxGrid.setTop(81);
        hboxGrid.setWidth(729);
        hboxGrid.setHeight(285);
        hboxGrid.setBorderStyle("stNone");
        hboxGrid.setPaddingTop(0);
        hboxGrid.setPaddingLeft(5);
        hboxGrid.setPaddingRight(5);
        hboxGrid.setPaddingBottom(5);
        hboxGrid.setMarginTop(0);
        hboxGrid.setMarginLeft(0);
        hboxGrid.setMarginRight(0);
        hboxGrid.setMarginBottom(0);
        hboxGrid.setSpacing(1);
        hboxGrid.setFlexVflex("ftTrue");
        hboxGrid.setFlexHflex("ftTrue");
        hboxGrid.setScrollable(false);
        hboxGrid.setBoxShadowConfigHorizontalLength(10);
        hboxGrid.setBoxShadowConfigVerticalLength(10);
        hboxGrid.setBoxShadowConfigBlurRadius(5);
        hboxGrid.setBoxShadowConfigSpreadRadius(0);
        hboxGrid.setBoxShadowConfigShadowColor("clBlack");
        hboxGrid.setBoxShadowConfigOpacity(75);
        hboxGrid.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxGrid);
        hboxGrid.applyProperties();
    }

    public TFGrid gridCliFlagAcesso = new TFGrid();

    private void init_gridCliFlagAcesso() {
        gridCliFlagAcesso.setName("gridCliFlagAcesso");
        gridCliFlagAcesso.setLeft(0);
        gridCliFlagAcesso.setTop(0);
        gridCliFlagAcesso.setWidth(723);
        gridCliFlagAcesso.setHeight(280);
        gridCliFlagAcesso.setTable(tbClienteFlagAcesso);
        gridCliFlagAcesso.setFlexVflex("ftTrue");
        gridCliFlagAcesso.setFlexHflex("ftTrue");
        gridCliFlagAcesso.setPagingEnabled(true);
        gridCliFlagAcesso.setFrozenColumns(0);
        gridCliFlagAcesso.setShowFooter(false);
        gridCliFlagAcesso.setShowHeader(true);
        gridCliFlagAcesso.setMultiSelection(false);
        gridCliFlagAcesso.setGroupingEnabled(false);
        gridCliFlagAcesso.setGroupingExpanded(false);
        gridCliFlagAcesso.setGroupingShowFooter(false);
        gridCliFlagAcesso.setCrosstabEnabled(false);
        gridCliFlagAcesso.setCrosstabGroupType("cgtConcat");
        gridCliFlagAcesso.setEditionEnabled(false);
        gridCliFlagAcesso.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_GRUPO");
        item0.setTitleCaption("Id. Grupo");
        item0.setWidth(40);
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("ID_SEQ");
        item1.setTitleCaption("Id. Seq");
        item1.setWidth(40);
        item1.setVisible(false);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("GRUPO");
        item2.setTitleCaption("Grupo");
        item2.setWidth(40);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("FUNCAO");
        item3.setTitleCaption("Fun\u00E7\u00E3o");
        item3.setWidth(40);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("NOME_COMPLETO");
        item4.setTitleCaption("Nome usu\u00E1rio");
        item4.setWidth(40);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("NOME");
        item5.setTitleCaption("Nome Usu\u00E1rio");
        item5.setWidth(40);
        item5.setVisible(false);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("COD_FUNCAO");
        item6.setTitleCaption("C\u00F3d. Fun\u00E7\u00E3o");
        item6.setWidth(40);
        item6.setVisible(false);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setWidth(44);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taCenter");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        TFImageExpression item8 = new TFImageExpression();
        item8.setExpression("*");
        item8.setEvalType("etExpression");
        item8.setImageId(8);
        item7.getImages().add(item8);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(false);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setHiperLink(false);
        item7.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridCliFlagAcessoExcluirClick(event);
            processarFlow("FrmClientesFlagsServAcesso", "item7", "OnClick");
        });
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridCliFlagAcesso.getColumns().add(item7);
        hboxGrid.addChildren(gridCliFlagAcesso);
        gridCliFlagAcesso.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item14 = new TFSchemaItem();
        item14.setTable(tbClienteFlagAcesso);
        sc.getTables().add(item14);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void cbbGrupoChange(final Event<Object> event);

    public abstract void cbbGrupoClearClick(final Event<Object> event);

    public abstract void cbbFuncoesChange(final Event<Object> event);

    public abstract void cbbFuncoesClearClick(final Event<Object> event);

    public abstract void cbbNomeChange(final Event<Object> event);

    public abstract void cbbNomeClearClick(final Event<Object> event);

    public void btnAdicionarClick(final Event<Object> event) {
        if (btnAdicionar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAdicionar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridCliFlagAcessoExcluirClick(final Event<Object> event);

    public abstract void tbEmpresasUsuariosMaxRow(final Event<Object> event);

}