package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmBtoBEnviarLink extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.BtoBEnviarLinkRNA rn = null;

    public FrmBtoBEnviarLink() {
        try {
            rn = (freedom.bytecode.rn.BtoBEnviarLinkRNA) getRN(freedom.bytecode.rn.wizard.BtoBEnviarLinkRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCrmFichaEventoBalcao();
        init_tbBtobEnderecoClienteFatura();
        init_tbBtobEnderecoClienteEntrega();
        init_tbEmpresasUsuarios();
        init_tbClienteDiverso();
        init_tbEmpresasClienteEnxerga();
        init_tbBtobEnderecosCadastrados();
        init_tbEmpresasClientesEnxerga();
        init_tbBtobClientesPequisaEmail();
        init_tbBtobLink();
        init_rg1();
        init_FVBox1();
        init_FHBox9();
        init_FHBox10();
        init_btnAceitar();
        init_btnVoltar();
        init_FVBox2();
        init_FHBox1();
        init_FLabel1();
        init_lbVendedor();
        init_FGroupbox2();
        init_FVBox3();
        init_FHBox2();
        init_GridClientesEmail();
        init_FVBox4();
        init_FHBox3();
        init_FLabel5();
        init_FGridPanel1();
        init_rb1();
        init_rb2();
        init_pagDadosCli();
        init_FTabsheet1();
        init_gridFat();
        init_FTabsheet2();
        init_gridEnt();
        init_FTabsheet3();
        init_gridEmp();
        init_FrmBtoBEnviarLink();
    }

    public VW_CRM_FICHA_EVENTO_BALCAO tbCrmFichaEventoBalcao;

    private void init_tbCrmFichaEventoBalcao() {
        tbCrmFichaEventoBalcao = rn.tbCrmFichaEventoBalcao;
        tbCrmFichaEventoBalcao.setName("tbCrmFichaEventoBalcao");
        tbCrmFichaEventoBalcao.setMaxRowCount(200);
        tbCrmFichaEventoBalcao.setWKey("340055;34003");
        tbCrmFichaEventoBalcao.setDeltaMode("dmChanged");
        getTables().put(tbCrmFichaEventoBalcao, "tbCrmFichaEventoBalcao");
        tbCrmFichaEventoBalcao.applyProperties();
    }

    public BTOB_ENDERECO_CLIENTE tbBtobEnderecoClienteFatura;

    private void init_tbBtobEnderecoClienteFatura() {
        tbBtobEnderecoClienteFatura = rn.tbBtobEnderecoClienteFatura;
        tbBtobEnderecoClienteFatura.setName("tbBtobEnderecoClienteFatura");
        TFTableField item123 = new TFTableField();
        item123.setName("CHECKED");
        item123.setCalculated(true);
        item123.setUpdatable(false);
        item123.setPrimaryKey(false);
        item123.setFieldType("ftString");
        tbBtobEnderecoClienteFatura.getFieldDefs().add(item123);
        tbBtobEnderecoClienteFatura.setMaxRowCount(200);
        tbBtobEnderecoClienteFatura.setWKey("340055;34004");
        tbBtobEnderecoClienteFatura.setDeltaMode("dmChanged");
        getTables().put(tbBtobEnderecoClienteFatura, "tbBtobEnderecoClienteFatura");
        tbBtobEnderecoClienteFatura.applyProperties();
    }

    public BTOB_ENDERECO_CLIENTE tbBtobEnderecoClienteEntrega;

    private void init_tbBtobEnderecoClienteEntrega() {
        tbBtobEnderecoClienteEntrega = rn.tbBtobEnderecoClienteEntrega;
        tbBtobEnderecoClienteEntrega.setName("tbBtobEnderecoClienteEntrega");
        TFTableField item131 = new TFTableField();
        item131.setName("CHECKED");
        item131.setCalculated(true);
        item131.setUpdatable(false);
        item131.setPrimaryKey(false);
        item131.setFieldType("ftString");
        tbBtobEnderecoClienteEntrega.getFieldDefs().add(item131);
        tbBtobEnderecoClienteEntrega.setMaxRowCount(200);
        tbBtobEnderecoClienteEntrega.setWKey("340055;34005");
        tbBtobEnderecoClienteEntrega.setDeltaMode("dmChanged");
        getTables().put(tbBtobEnderecoClienteEntrega, "tbBtobEnderecoClienteEntrega");
        tbBtobEnderecoClienteEntrega.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("340055;34006");
        tbEmpresasUsuarios.setDeltaMode("dmChanged");
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public CLIENTE_DIVERSO tbClienteDiverso;

    private void init_tbClienteDiverso() {
        tbClienteDiverso = rn.tbClienteDiverso;
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("340055;34007");
        tbClienteDiverso.setDeltaMode("dmChanged");
        getTables().put(tbClienteDiverso, "tbClienteDiverso");
        tbClienteDiverso.applyProperties();
    }

    public EMPRESAS_CLIENTE_ENXERGA tbEmpresasClienteEnxerga;

    private void init_tbEmpresasClienteEnxerga() {
        tbEmpresasClienteEnxerga = rn.tbEmpresasClienteEnxerga;
        tbEmpresasClienteEnxerga.setName("tbEmpresasClienteEnxerga");
        tbEmpresasClienteEnxerga.setMaxRowCount(200);
        tbEmpresasClienteEnxerga.setWKey("340055;34008");
        tbEmpresasClienteEnxerga.setDeltaMode("dmChanged");
        getTables().put(tbEmpresasClienteEnxerga, "tbEmpresasClienteEnxerga");
        tbEmpresasClienteEnxerga.applyProperties();
    }

    public BTOB_ENDERECOS_CADASTRADOS tbBtobEnderecosCadastrados;

    private void init_tbBtobEnderecosCadastrados() {
        tbBtobEnderecosCadastrados = rn.tbBtobEnderecosCadastrados;
        tbBtobEnderecosCadastrados.setName("tbBtobEnderecosCadastrados");
        tbBtobEnderecosCadastrados.setMaxRowCount(200);
        tbBtobEnderecosCadastrados.setWKey("340055;34009");
        tbBtobEnderecosCadastrados.setDeltaMode("dmChanged");
        getTables().put(tbBtobEnderecosCadastrados, "tbBtobEnderecosCadastrados");
        tbBtobEnderecosCadastrados.applyProperties();
    }

    public EMPRESAS_CLIENTES_ENXERGA tbEmpresasClientesEnxerga;

    private void init_tbEmpresasClientesEnxerga() {
        tbEmpresasClientesEnxerga = rn.tbEmpresasClientesEnxerga;
        tbEmpresasClientesEnxerga.setName("tbEmpresasClientesEnxerga");
        tbEmpresasClientesEnxerga.setMaxRowCount(200);
        tbEmpresasClientesEnxerga.setWKey("340055;340010");
        tbEmpresasClientesEnxerga.setDeltaMode("dmChanged");
        getTables().put(tbEmpresasClientesEnxerga, "tbEmpresasClientesEnxerga");
        tbEmpresasClientesEnxerga.applyProperties();
    }

    public BTOB_CLIENTES_PEQUISA_EMAIL tbBtobClientesPequisaEmail;

    private void init_tbBtobClientesPequisaEmail() {
        tbBtobClientesPequisaEmail = rn.tbBtobClientesPequisaEmail;
        tbBtobClientesPequisaEmail.setName("tbBtobClientesPequisaEmail");
        TFTableField item149 = new TFTableField();
        item149.setName("CHECKED");
        item149.setCalculated(true);
        item149.setUpdatable(false);
        item149.setPrimaryKey(false);
        item149.setFieldType("ftString");
        tbBtobClientesPequisaEmail.getFieldDefs().add(item149);
        tbBtobClientesPequisaEmail.setMaxRowCount(200);
        tbBtobClientesPequisaEmail.setWKey("340055;340011");
        tbBtobClientesPequisaEmail.setDeltaMode("dmChanged");
        getTables().put(tbBtobClientesPequisaEmail, "tbBtobClientesPequisaEmail");
        tbBtobClientesPequisaEmail.applyProperties();
    }

    public BTOB_LINK tbBtobLink;

    private void init_tbBtobLink() {
        tbBtobLink = rn.tbBtobLink;
        tbBtobLink.setName("tbBtobLink");
        tbBtobLink.setMaxRowCount(200);
        tbBtobLink.setWKey("340055;340012");
        tbBtobLink.setDeltaMode("dmChanged");
        getTables().put(tbBtobLink, "tbBtobLink");
        tbBtobLink.applyProperties();
    }

    public TFRadioGroup rg1 = new TFRadioGroup();

    private void init_rg1() {
        rg1.setName("rg1");
        FrmBtoBEnviarLink.addChildren(rg1);
        rg1.applyProperties();
    }

    protected TFForm FrmBtoBEnviarLink = this;
    private void init_FrmBtoBEnviarLink() {
        FrmBtoBEnviarLink.setName("FrmBtoBEnviarLink");
        FrmBtoBEnviarLink.setCaption("Cadastro do BtoB para o E-mail:");
        FrmBtoBEnviarLink.setClientHeight(600);
        FrmBtoBEnviarLink.setClientWidth(841);
        FrmBtoBEnviarLink.setColor("clBtnFace");
        FrmBtoBEnviarLink.setWKey("340055");
        FrmBtoBEnviarLink.setSpacing(0);
        FrmBtoBEnviarLink.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(841);
        FVBox1.setHeight(600);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmBtoBEnviarLink.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(979);
        FHBox9.setHeight(68);
        FHBox9.setAlign("alTop");
        FHBox9.setBorderStyle("stNone");
        FHBox9.setColor("16514043");
        FHBox9.setPaddingTop(5);
        FHBox9.setPaddingLeft(2);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(5);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(829);
        FHBox10.setHeight(60);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(2);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(3);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(0);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Enviar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmBtoBEnviarLink", "btnAceitar", "OnClick");
        });
        btnAceitar.setPngImageData("89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF"
 + "61000000D14944415478DA6364A01030526C0010B000E95D40EC48A2DEFD40EC"
 + "0632C00CC8380EC4AB81389C48CDCBA16A2D4106D800197BFFFFFFCF0E649B02"
 + "D95B80580C87C65740EC0D547B06A8F62790ED0C33E030101701F12468B85401"
 + "71239AE67A206E03E2FF409C07C47D406C8B6C00083C85DA7011282E03646F82"
 + "8AFB01C59E00C5F481ECAD402C0D15C73000069600713A107F87F2B980780610"
 + "C7A0A9C3690008FC01E248A8979601310B1635780D20060C1203CC818C13641A"
 + "600E4BCA7B81D88E44CD87184009894C9BE1806203009DCD3189FB5EB35C0000"
 + "000049454E44AE426082");
        btnAceitar.setImageId(700084);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox10.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(60);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmBtoBEnviarLink", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox10.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(69);
        FVBox2.setWidth(832);
        FVBox2.setHeight(525);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(833);
        FHBox1.setHeight(25);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FVBox2.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(101);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Vendedor Associado:");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FHBox1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFLabel lbVendedor = new TFLabel();

    private void init_lbVendedor() {
        lbVendedor.setName("lbVendedor");
        lbVendedor.setLeft(101);
        lbVendedor.setTop(0);
        lbVendedor.setWidth(70);
        lbVendedor.setHeight(13);
        lbVendedor.setCaption("Ant\u00F4nio Carlos");
        lbVendedor.setFontColor("clRed");
        lbVendedor.setFontSize(-11);
        lbVendedor.setFontName("Tahoma");
        lbVendedor.setFontStyle("[]");
        lbVendedor.setVerticalAlignment("taVerticalCenter");
        FHBox1.addChildren(lbVendedor);
        lbVendedor.applyProperties();
    }

    public TFGroupbox FGroupbox2 = new TFGroupbox();

    private void init_FGroupbox2() {
        FGroupbox2.setName("FGroupbox2");
        FGroupbox2.setLeft(0);
        FGroupbox2.setTop(26);
        FGroupbox2.setWidth(865);
        FGroupbox2.setHeight(161);
        FGroupbox2.setCaption("Cliente Faturamento");
        FGroupbox2.setFontColor("clWindowText");
        FGroupbox2.setFontSize(-11);
        FGroupbox2.setFontName("Tahoma");
        FGroupbox2.setFontStyle("[]");
        FGroupbox2.setFlexVflex("ftFalse");
        FGroupbox2.setFlexHflex("ftFalse");
        FGroupbox2.setScrollable(false);
        FGroupbox2.setClosable(false);
        FGroupbox2.setClosed(false);
        FGroupbox2.setOrient("coHorizontal");
        FGroupbox2.setStyle("grp3D");
        FGroupbox2.setHeaderImageId(0);
        FVBox2.addChildren(FGroupbox2);
        FGroupbox2.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(2);
        FVBox3.setTop(15);
        FVBox3.setWidth(861);
        FVBox3.setHeight(144);
        FVBox3.setAlign("alClient");
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FVBox3Click(event);
            processarFlow("FrmBtoBEnviarLink", "FVBox3", "OnClick");
        });
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FGroupbox2.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(816);
        FHBox2.setHeight(125);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFGrid GridClientesEmail = new TFGrid();

    private void init_GridClientesEmail() {
        GridClientesEmail.setName("GridClientesEmail");
        GridClientesEmail.setLeft(0);
        GridClientesEmail.setTop(0);
        GridClientesEmail.setWidth(568);
        GridClientesEmail.setHeight(120);
        GridClientesEmail.setTable(tbBtobClientesPequisaEmail);
        GridClientesEmail.setFlexVflex("ftTrue");
        GridClientesEmail.setFlexHflex("ftTrue");
        GridClientesEmail.setPagingEnabled(false);
        GridClientesEmail.setFrozenColumns(0);
        GridClientesEmail.setShowFooter(false);
        GridClientesEmail.setShowHeader(true);
        GridClientesEmail.setMultiSelection(false);
        GridClientesEmail.setGroupingEnabled(false);
        GridClientesEmail.setGroupingExpanded(false);
        GridClientesEmail.setGroupingShowFooter(false);
        GridClientesEmail.setCrosstabEnabled(false);
        GridClientesEmail.setCrosstabGroupType("cgtConcat");
        GridClientesEmail.setEditionEnabled(false);
        GridClientesEmail.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("CHECKED");
        item0.setTitleCaption("\u00A0");
        item0.setWidth(39);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("CHECKED='S'");
        item1.setEvalType("etExpression");
        item1.setImageId(7000110);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("CHECKED='N'");
        item2.setEvalType("etExpression");
        item2.setImageId(7000111);
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            GridClientesEmailGridClientesEmailCheckClick(event);
            processarFlow("FrmBtoBEnviarLink", "item0", "OnClick");
        });
        item0.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            GridClientesEmailGridClientesEmailCheckDoubleClick(event);
            processarFlow("FrmBtoBEnviarLink", "item0", "OnDoubleClick");
        });
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        GridClientesEmail.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("CLIENTE");
        item3.setTitleCaption("Cliente");
        item3.setWidth(306);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setHiperLink(false);
        item3.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            GridClientesEmailGridClientesEmaiClienteDoubleClick(event);
            processarFlow("FrmBtoBEnviarLink", "item3", "OnDoubleClick");
        });
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        GridClientesEmail.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("CNPJ");
        item4.setTitleCaption("CPF/CNPJ");
        item4.setWidth(132);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setHiperLink(false);
        item4.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            GridClientesEmailGridClientesEmailCnpjDoubleClick(event);
            processarFlow("FrmBtoBEnviarLink", "item4", "OnDoubleClick");
        });
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        GridClientesEmail.getColumns().add(item4);
        FHBox2.addChildren(GridClientesEmail);
        GridClientesEmail.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(568);
        FVBox4.setTop(0);
        FVBox4.setWidth(166);
        FVBox4.setHeight(109);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(137);
        FHBox3.setHeight(23);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(12);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FVBox4.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(76);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Tipo de Entrega");
        FLabel5.setFontColor("clRed");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FHBox3.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFGridPanel FGridPanel1 = new TFGridPanel();

    private void init_FGridPanel1() {
        FGridPanel1.setName("FGridPanel1");
        FGridPanel1.setLeft(0);
        FGridPanel1.setTop(24);
        FGridPanel1.setWidth(161);
        FGridPanel1.setHeight(66);
        FGridPanel1.setAlign("alLeft");
        TFGridPanelColumn item5 = new TFGridPanelColumn();
        item5.setValue(100.000000000000000000);
        FGridPanel1.getColumnCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(0);
        item6.setControl("rb1");
        item6.setRow(0);
        FGridPanel1.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(0);
        item7.setControl("rb2");
        item7.setRow(1);
        FGridPanel1.getControlCollection().add(item7);
        TFGridPanelRow item8 = new TFGridPanelRow();
        item8.setValue(50.001525832341540000);
        FGridPanel1.getRowCollection().add(item8);
        TFGridPanelRow item9 = new TFGridPanelRow();
        item9.setValue(49.998474167658460000);
        FGridPanel1.getRowCollection().add(item9);
        FGridPanel1.setFlexVflex("ftFalse");
        FGridPanel1.setFlexHflex("ftTrue");
        FGridPanel1.setAllRowFlex(false);
        FGridPanel1.setColumnTabOrder(false);
        FVBox4.addChildren(FGridPanel1);
        FGridPanel1.applyProperties();
    }

    public TFRadioButton rb1 = new TFRadioButton();

    private void init_rb1() {
        rb1.setName("rb1");
        rb1.setLeft(1);
        rb1.setTop(1);
        rb1.setWidth(153);
        rb1.setHeight(32);
        rb1.setAlign("alLeft");
        rb1.setCaption("Concessionaria Entrega");
        rb1.setFontColor("clWindowText");
        rb1.setFontSize(-11);
        rb1.setFontName("Tahoma");
        rb1.setFontStyle("[]");
        rb1.setRadioGroup(rg1);
        FGridPanel1.addChildren(rb1);
        rb1.applyProperties();
    }

    public TFRadioButton rb2 = new TFRadioButton();

    private void init_rb2() {
        rb2.setName("rb2");
        rb2.setLeft(1);
        rb2.setTop(33);
        rb2.setWidth(154);
        rb2.setHeight(32);
        rb2.setAlign("alLeft");
        rb2.setCaption("Cliente Retira Mercadoria");
        rb2.setFontColor("clWindowText");
        rb2.setFontSize(-11);
        rb2.setFontName("Tahoma");
        rb2.setFontStyle("[]");
        rb2.setRadioGroup(rg1);
        FGridPanel1.addChildren(rb2);
        rb2.applyProperties();
    }

    public TFPageControl pagDadosCli = new TFPageControl();

    private void init_pagDadosCli() {
        pagDadosCli.setName("pagDadosCli");
        pagDadosCli.setLeft(0);
        pagDadosCli.setTop(188);
        pagDadosCli.setWidth(820);
        pagDadosCli.setHeight(153);
        pagDadosCli.setAlign("alClient");
        pagDadosCli.setFlexVflex("ftTrue");
        pagDadosCli.setFlexHflex("ftTrue");
        pagDadosCli.setRenderStyle("rsTabbed");
        pagDadosCli.applyProperties();
        FVBox2.addChildren(pagDadosCli);
    }

    public TFTabsheet FTabsheet1 = new TFTabsheet();

    private void init_FTabsheet1() {
        FTabsheet1.setName("FTabsheet1");
        FTabsheet1.setCaption("Faturamento");
        FTabsheet1.setClosable(false);
        pagDadosCli.addChildren(FTabsheet1);
        FTabsheet1.applyProperties();
    }

    public TFGrid gridFat = new TFGrid();

    private void init_gridFat() {
        gridFat.setName("gridFat");
        gridFat.setLeft(0);
        gridFat.setTop(14);
        gridFat.setWidth(810);
        gridFat.setHeight(76);
        gridFat.setTable(tbBtobEnderecoClienteFatura);
        gridFat.setFlexVflex("ftTrue");
        gridFat.setFlexHflex("ftTrue");
        gridFat.setPagingEnabled(false);
        gridFat.setFrozenColumns(0);
        gridFat.setShowFooter(false);
        gridFat.setShowHeader(true);
        gridFat.setMultiSelection(false);
        gridFat.setGroupingEnabled(false);
        gridFat.setGroupingExpanded(false);
        gridFat.setGroupingShowFooter(false);
        gridFat.setCrosstabEnabled(false);
        gridFat.setCrosstabGroupType("cgtConcat");
        gridFat.setEditionEnabled(false);
        gridFat.setNoBorder(false);
        TFGridColumn item10 = new TFGridColumn();
        item10.setTitleCaption("\u00A0");
        item10.setWidth(37);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        TFImageExpression item11 = new TFImageExpression();
        item11.setExpression("CHECKED = 'S'");
        item11.setEvalType("etExpression");
        item11.setImageId(7000110);
        item11.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridFatimgFatura(event);
            processarFlow("FrmBtoBEnviarLink", "item11", "OnClick");
        });
        item10.getImages().add(item11);
        TFImageExpression item12 = new TFImageExpression();
        item12.setExpression("CHECKED = 'N'");
        item12.setEvalType("etExpression");
        item12.setImageId(7000111);
        item12.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridFatimgFatura(event);
            processarFlow("FrmBtoBEnviarLink", "item12", "OnClick");
        });
        item10.getImages().add(item12);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(false);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        gridFat.getColumns().add(item10);
        TFGridColumn item13 = new TFGridColumn();
        item13.setFieldName("TIPO_ENDERECO");
        item13.setTitleCaption("Tipo");
        item13.setWidth(157);
        item13.setVisible(true);
        item13.setPrecision(0);
        item13.setTextAlign("taLeft");
        item13.setFieldType("ftString");
        item13.setFlexRatio(0);
        item13.setSort(false);
        item13.setImageHeader(0);
        item13.setWrap(false);
        item13.setFlex(false);
        item13.setCharCase("ccNormal");
        item13.setBlobConfigMimeType("bmtText");
        item13.setBlobConfigShowType("btImageViewer");
        item13.setShowLabel(true);
        item13.setEditorEditType("etTFString");
        item13.setEditorPrecision(0);
        item13.setEditorMaxLength(100);
        item13.setEditorLookupFilterKey(0);
        item13.setEditorLookupFilterDesc(0);
        item13.setEditorPopupHeight(400);
        item13.setEditorPopupWidth(400);
        item13.setEditorCharCase("ccNormal");
        item13.setEditorEnabled(false);
        item13.setEditorReadOnly(false);
        item13.setHiperLink(false);
        item13.setEditorConstraintCheckWhen("cwImmediate");
        item13.setEditorConstraintCheckType("ctExpression");
        item13.setEditorConstraintFocusOnError(false);
        item13.setEditorConstraintEnableUI(true);
        item13.setEditorConstraintEnabled(false);
        item13.setEmpty(false);
        item13.setMobileOptsShowMobile(false);
        item13.setMobileOptsOrder(0);
        gridFat.getColumns().add(item13);
        TFGridColumn item14 = new TFGridColumn();
        item14.setFieldName("UF");
        item14.setWidth(71);
        item14.setVisible(true);
        item14.setPrecision(0);
        item14.setTextAlign("taLeft");
        item14.setFieldType("ftString");
        item14.setFlexRatio(0);
        item14.setSort(false);
        item14.setImageHeader(0);
        item14.setWrap(false);
        item14.setFlex(false);
        item14.setCharCase("ccNormal");
        item14.setBlobConfigMimeType("bmtText");
        item14.setBlobConfigShowType("btImageViewer");
        item14.setShowLabel(true);
        item14.setEditorEditType("etTFString");
        item14.setEditorPrecision(0);
        item14.setEditorMaxLength(100);
        item14.setEditorLookupFilterKey(0);
        item14.setEditorLookupFilterDesc(0);
        item14.setEditorPopupHeight(400);
        item14.setEditorPopupWidth(400);
        item14.setEditorCharCase("ccNormal");
        item14.setEditorEnabled(false);
        item14.setEditorReadOnly(false);
        item14.setHiperLink(false);
        item14.setEditorConstraintCheckWhen("cwImmediate");
        item14.setEditorConstraintCheckType("ctExpression");
        item14.setEditorConstraintFocusOnError(false);
        item14.setEditorConstraintEnableUI(true);
        item14.setEditorConstraintEnabled(false);
        item14.setEmpty(false);
        item14.setMobileOptsShowMobile(false);
        item14.setMobileOptsOrder(0);
        gridFat.getColumns().add(item14);
        TFGridColumn item15 = new TFGridColumn();
        item15.setFieldName("CIDADE");
        item15.setTitleCaption("Cidade");
        item15.setWidth(194);
        item15.setVisible(true);
        item15.setPrecision(0);
        item15.setTextAlign("taLeft");
        item15.setFieldType("ftString");
        item15.setFlexRatio(0);
        item15.setSort(false);
        item15.setImageHeader(0);
        item15.setWrap(false);
        item15.setFlex(true);
        item15.setCharCase("ccNormal");
        item15.setBlobConfigMimeType("bmtText");
        item15.setBlobConfigShowType("btImageViewer");
        item15.setShowLabel(true);
        item15.setEditorEditType("etTFString");
        item15.setEditorPrecision(0);
        item15.setEditorMaxLength(100);
        item15.setEditorLookupFilterKey(0);
        item15.setEditorLookupFilterDesc(0);
        item15.setEditorPopupHeight(400);
        item15.setEditorPopupWidth(400);
        item15.setEditorCharCase("ccNormal");
        item15.setEditorEnabled(false);
        item15.setEditorReadOnly(false);
        item15.setHiperLink(false);
        item15.setEditorConstraintCheckWhen("cwImmediate");
        item15.setEditorConstraintCheckType("ctExpression");
        item15.setEditorConstraintFocusOnError(false);
        item15.setEditorConstraintEnableUI(true);
        item15.setEditorConstraintEnabled(false);
        item15.setEmpty(false);
        item15.setMobileOptsShowMobile(false);
        item15.setMobileOptsOrder(0);
        gridFat.getColumns().add(item15);
        TFGridColumn item16 = new TFGridColumn();
        item16.setFieldName("CEP");
        item16.setWidth(108);
        item16.setVisible(true);
        item16.setPrecision(0);
        item16.setTextAlign("taLeft");
        item16.setFieldType("ftString");
        item16.setFlexRatio(0);
        item16.setSort(false);
        item16.setImageHeader(0);
        item16.setWrap(false);
        item16.setFlex(false);
        item16.setCharCase("ccNormal");
        item16.setBlobConfigMimeType("bmtText");
        item16.setBlobConfigShowType("btImageViewer");
        item16.setShowLabel(true);
        item16.setEditorEditType("etTFString");
        item16.setEditorPrecision(0);
        item16.setEditorMaxLength(100);
        item16.setEditorLookupFilterKey(0);
        item16.setEditorLookupFilterDesc(0);
        item16.setEditorPopupHeight(400);
        item16.setEditorPopupWidth(400);
        item16.setEditorCharCase("ccNormal");
        item16.setEditorEnabled(false);
        item16.setEditorReadOnly(false);
        item16.setHiperLink(false);
        item16.setEditorConstraintCheckWhen("cwImmediate");
        item16.setEditorConstraintCheckType("ctExpression");
        item16.setEditorConstraintFocusOnError(false);
        item16.setEditorConstraintEnableUI(true);
        item16.setEditorConstraintEnabled(false);
        item16.setEmpty(false);
        item16.setMobileOptsShowMobile(false);
        item16.setMobileOptsOrder(0);
        gridFat.getColumns().add(item16);
        TFGridColumn item17 = new TFGridColumn();
        item17.setFieldName("INSCRICAO_ESTADUAL");
        item17.setTitleCaption("Inscri\u00E7\u00E3o Estadual");
        item17.setWidth(215);
        item17.setVisible(true);
        item17.setPrecision(0);
        item17.setTextAlign("taLeft");
        item17.setFieldType("ftString");
        item17.setFlexRatio(0);
        item17.setSort(false);
        item17.setImageHeader(0);
        item17.setWrap(false);
        item17.setFlex(false);
        item17.setCharCase("ccNormal");
        item17.setBlobConfigMimeType("bmtText");
        item17.setBlobConfigShowType("btImageViewer");
        item17.setShowLabel(true);
        item17.setEditorEditType("etTFString");
        item17.setEditorPrecision(0);
        item17.setEditorMaxLength(100);
        item17.setEditorLookupFilterKey(0);
        item17.setEditorLookupFilterDesc(0);
        item17.setEditorPopupHeight(400);
        item17.setEditorPopupWidth(400);
        item17.setEditorCharCase("ccNormal");
        item17.setEditorEnabled(false);
        item17.setEditorReadOnly(false);
        item17.setHiperLink(false);
        item17.setEditorConstraintCheckWhen("cwImmediate");
        item17.setEditorConstraintCheckType("ctExpression");
        item17.setEditorConstraintFocusOnError(false);
        item17.setEditorConstraintEnableUI(true);
        item17.setEditorConstraintEnabled(false);
        item17.setEmpty(false);
        item17.setMobileOptsShowMobile(false);
        item17.setMobileOptsOrder(0);
        gridFat.getColumns().add(item17);
        FTabsheet1.addChildren(gridFat);
        gridFat.applyProperties();
    }

    public TFTabsheet FTabsheet2 = new TFTabsheet();

    private void init_FTabsheet2() {
        FTabsheet2.setName("FTabsheet2");
        FTabsheet2.setCaption("Entrega");
        FTabsheet2.setClosable(false);
        pagDadosCli.addChildren(FTabsheet2);
        FTabsheet2.applyProperties();
    }

    public TFGrid gridEnt = new TFGrid();

    private void init_gridEnt() {
        gridEnt.setName("gridEnt");
        gridEnt.setLeft(0);
        gridEnt.setTop(0);
        gridEnt.setWidth(602);
        gridEnt.setHeight(110);
        gridEnt.setTable(tbBtobEnderecoClienteEntrega);
        gridEnt.setFlexVflex("ftTrue");
        gridEnt.setFlexHflex("ftTrue");
        gridEnt.setPagingEnabled(false);
        gridEnt.setFrozenColumns(0);
        gridEnt.setShowFooter(false);
        gridEnt.setShowHeader(true);
        gridEnt.setMultiSelection(false);
        gridEnt.setGroupingEnabled(false);
        gridEnt.setGroupingExpanded(false);
        gridEnt.setGroupingShowFooter(false);
        gridEnt.setCrosstabEnabled(false);
        gridEnt.setCrosstabGroupType("cgtConcat");
        gridEnt.setEditionEnabled(false);
        gridEnt.setNoBorder(false);
        TFGridColumn item18 = new TFGridColumn();
        item18.setTitleCaption("\u00A0");
        item18.setWidth(37);
        item18.setVisible(true);
        item18.setPrecision(0);
        item18.setTextAlign("taLeft");
        item18.setFieldType("ftString");
        item18.setFlexRatio(0);
        item18.setSort(false);
        item18.setImageHeader(0);
        item18.setWrap(false);
        item18.setFlex(false);
        TFImageExpression item19 = new TFImageExpression();
        item19.setExpression("CHECKED = 'S'");
        item19.setEvalType("etExpression");
        item19.setImageId(7000110);
        item19.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridEntimgEntrega(event);
            processarFlow("FrmBtoBEnviarLink", "item19", "OnClick");
        });
        item18.getImages().add(item19);
        TFImageExpression item20 = new TFImageExpression();
        item20.setExpression("CHECKED = 'N'");
        item20.setEvalType("etExpression");
        item20.setImageId(7000111);
        item20.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridEntimgEntrega(event);
            processarFlow("FrmBtoBEnviarLink", "item20", "OnClick");
        });
        item18.getImages().add(item20);
        item18.setCharCase("ccNormal");
        item18.setBlobConfigMimeType("bmtText");
        item18.setBlobConfigShowType("btImageViewer");
        item18.setShowLabel(false);
        item18.setEditorEditType("etTFString");
        item18.setEditorPrecision(0);
        item18.setEditorMaxLength(100);
        item18.setEditorLookupFilterKey(0);
        item18.setEditorLookupFilterDesc(0);
        item18.setEditorPopupHeight(400);
        item18.setEditorPopupWidth(400);
        item18.setEditorCharCase("ccNormal");
        item18.setEditorEnabled(false);
        item18.setEditorReadOnly(false);
        item18.setHiperLink(false);
        item18.setEditorConstraintCheckWhen("cwImmediate");
        item18.setEditorConstraintCheckType("ctExpression");
        item18.setEditorConstraintFocusOnError(false);
        item18.setEditorConstraintEnableUI(true);
        item18.setEditorConstraintEnabled(false);
        item18.setEmpty(false);
        item18.setMobileOptsShowMobile(false);
        item18.setMobileOptsOrder(0);
        gridEnt.getColumns().add(item18);
        TFGridColumn item21 = new TFGridColumn();
        item21.setFieldName("TIPO_ENDERECO");
        item21.setTitleCaption("Tipo");
        item21.setWidth(94);
        item21.setVisible(true);
        item21.setPrecision(0);
        item21.setTextAlign("taLeft");
        item21.setFieldType("ftString");
        item21.setFlexRatio(0);
        item21.setSort(false);
        item21.setImageHeader(0);
        item21.setWrap(false);
        item21.setFlex(false);
        item21.setCharCase("ccNormal");
        item21.setBlobConfigMimeType("bmtText");
        item21.setBlobConfigShowType("btImageViewer");
        item21.setShowLabel(true);
        item21.setEditorEditType("etTFString");
        item21.setEditorPrecision(0);
        item21.setEditorMaxLength(100);
        item21.setEditorLookupFilterKey(0);
        item21.setEditorLookupFilterDesc(0);
        item21.setEditorPopupHeight(400);
        item21.setEditorPopupWidth(400);
        item21.setEditorCharCase("ccNormal");
        item21.setEditorEnabled(false);
        item21.setEditorReadOnly(false);
        item21.setHiperLink(false);
        item21.setEditorConstraintCheckWhen("cwImmediate");
        item21.setEditorConstraintCheckType("ctExpression");
        item21.setEditorConstraintFocusOnError(false);
        item21.setEditorConstraintEnableUI(true);
        item21.setEditorConstraintEnabled(false);
        item21.setEmpty(false);
        item21.setMobileOptsShowMobile(false);
        item21.setMobileOptsOrder(0);
        gridEnt.getColumns().add(item21);
        TFGridColumn item22 = new TFGridColumn();
        item22.setFieldName("UF");
        item22.setWidth(71);
        item22.setVisible(true);
        item22.setPrecision(0);
        item22.setTextAlign("taLeft");
        item22.setFieldType("ftString");
        item22.setFlexRatio(0);
        item22.setSort(false);
        item22.setImageHeader(0);
        item22.setWrap(false);
        item22.setFlex(false);
        item22.setCharCase("ccNormal");
        item22.setBlobConfigMimeType("bmtText");
        item22.setBlobConfigShowType("btImageViewer");
        item22.setShowLabel(true);
        item22.setEditorEditType("etTFString");
        item22.setEditorPrecision(0);
        item22.setEditorMaxLength(100);
        item22.setEditorLookupFilterKey(0);
        item22.setEditorLookupFilterDesc(0);
        item22.setEditorPopupHeight(400);
        item22.setEditorPopupWidth(400);
        item22.setEditorCharCase("ccNormal");
        item22.setEditorEnabled(false);
        item22.setEditorReadOnly(false);
        item22.setHiperLink(false);
        item22.setEditorConstraintCheckWhen("cwImmediate");
        item22.setEditorConstraintCheckType("ctExpression");
        item22.setEditorConstraintFocusOnError(false);
        item22.setEditorConstraintEnableUI(true);
        item22.setEditorConstraintEnabled(false);
        item22.setEmpty(false);
        item22.setMobileOptsShowMobile(false);
        item22.setMobileOptsOrder(0);
        gridEnt.getColumns().add(item22);
        TFGridColumn item23 = new TFGridColumn();
        item23.setFieldName("CIDADE");
        item23.setTitleCaption("Cidade");
        item23.setWidth(134);
        item23.setVisible(true);
        item23.setPrecision(0);
        item23.setTextAlign("taLeft");
        item23.setFieldType("ftString");
        item23.setFlexRatio(0);
        item23.setSort(false);
        item23.setImageHeader(0);
        item23.setWrap(false);
        item23.setFlex(true);
        item23.setCharCase("ccNormal");
        item23.setBlobConfigMimeType("bmtText");
        item23.setBlobConfigShowType("btImageViewer");
        item23.setShowLabel(true);
        item23.setEditorEditType("etTFString");
        item23.setEditorPrecision(0);
        item23.setEditorMaxLength(100);
        item23.setEditorLookupFilterKey(0);
        item23.setEditorLookupFilterDesc(0);
        item23.setEditorPopupHeight(400);
        item23.setEditorPopupWidth(400);
        item23.setEditorCharCase("ccNormal");
        item23.setEditorEnabled(false);
        item23.setEditorReadOnly(false);
        item23.setHiperLink(false);
        item23.setEditorConstraintCheckWhen("cwImmediate");
        item23.setEditorConstraintCheckType("ctExpression");
        item23.setEditorConstraintFocusOnError(false);
        item23.setEditorConstraintEnableUI(true);
        item23.setEditorConstraintEnabled(false);
        item23.setEmpty(false);
        item23.setMobileOptsShowMobile(false);
        item23.setMobileOptsOrder(0);
        gridEnt.getColumns().add(item23);
        TFGridColumn item24 = new TFGridColumn();
        item24.setFieldName("CEP");
        item24.setWidth(86);
        item24.setVisible(true);
        item24.setPrecision(0);
        item24.setTextAlign("taLeft");
        item24.setFieldType("ftString");
        item24.setFlexRatio(0);
        item24.setSort(false);
        item24.setImageHeader(0);
        item24.setWrap(false);
        item24.setFlex(false);
        item24.setCharCase("ccNormal");
        item24.setBlobConfigMimeType("bmtText");
        item24.setBlobConfigShowType("btImageViewer");
        item24.setShowLabel(true);
        item24.setEditorEditType("etTFString");
        item24.setEditorPrecision(0);
        item24.setEditorMaxLength(100);
        item24.setEditorLookupFilterKey(0);
        item24.setEditorLookupFilterDesc(0);
        item24.setEditorPopupHeight(400);
        item24.setEditorPopupWidth(400);
        item24.setEditorCharCase("ccNormal");
        item24.setEditorEnabled(false);
        item24.setEditorReadOnly(false);
        item24.setHiperLink(false);
        item24.setEditorConstraintCheckWhen("cwImmediate");
        item24.setEditorConstraintCheckType("ctExpression");
        item24.setEditorConstraintFocusOnError(false);
        item24.setEditorConstraintEnableUI(true);
        item24.setEditorConstraintEnabled(false);
        item24.setEmpty(false);
        item24.setMobileOptsShowMobile(false);
        item24.setMobileOptsOrder(0);
        gridEnt.getColumns().add(item24);
        TFGridColumn item25 = new TFGridColumn();
        item25.setFieldName("INSCRICAO_ESTADUAL");
        item25.setTitleCaption("Inscri\u00E7\u00E3o Estadual");
        item25.setWidth(150);
        item25.setVisible(true);
        item25.setPrecision(0);
        item25.setTextAlign("taLeft");
        item25.setFieldType("ftString");
        item25.setFlexRatio(0);
        item25.setSort(false);
        item25.setImageHeader(0);
        item25.setWrap(false);
        item25.setFlex(false);
        item25.setCharCase("ccNormal");
        item25.setBlobConfigMimeType("bmtText");
        item25.setBlobConfigShowType("btImageViewer");
        item25.setShowLabel(true);
        item25.setEditorEditType("etTFString");
        item25.setEditorPrecision(0);
        item25.setEditorMaxLength(100);
        item25.setEditorLookupFilterKey(0);
        item25.setEditorLookupFilterDesc(0);
        item25.setEditorPopupHeight(400);
        item25.setEditorPopupWidth(400);
        item25.setEditorCharCase("ccNormal");
        item25.setEditorEnabled(false);
        item25.setEditorReadOnly(false);
        item25.setHiperLink(false);
        item25.setEditorConstraintCheckWhen("cwImmediate");
        item25.setEditorConstraintCheckType("ctExpression");
        item25.setEditorConstraintFocusOnError(false);
        item25.setEditorConstraintEnableUI(true);
        item25.setEditorConstraintEnabled(false);
        item25.setEmpty(false);
        item25.setMobileOptsShowMobile(false);
        item25.setMobileOptsOrder(0);
        gridEnt.getColumns().add(item25);
        FTabsheet2.addChildren(gridEnt);
        gridEnt.applyProperties();
    }

    public TFTabsheet FTabsheet3 = new TFTabsheet();

    private void init_FTabsheet3() {
        FTabsheet3.setName("FTabsheet3");
        FTabsheet3.setCaption("Enxerga Estoque");
        FTabsheet3.setClosable(false);
        pagDadosCli.addChildren(FTabsheet3);
        FTabsheet3.applyProperties();
    }

    public TFGrid gridEmp = new TFGrid();

    private void init_gridEmp() {
        gridEmp.setName("gridEmp");
        gridEmp.setLeft(7);
        gridEmp.setTop(7);
        gridEmp.setWidth(812);
        gridEmp.setHeight(112);
        gridEmp.setTable(tbEmpresasClienteEnxerga);
        gridEmp.setFlexVflex("ftTrue");
        gridEmp.setFlexHflex("ftTrue");
        gridEmp.setPagingEnabled(false);
        gridEmp.setFrozenColumns(0);
        gridEmp.setShowFooter(false);
        gridEmp.setShowHeader(true);
        gridEmp.setMultiSelection(false);
        gridEmp.setGroupingEnabled(false);
        gridEmp.setGroupingExpanded(false);
        gridEmp.setGroupingShowFooter(false);
        gridEmp.setCrosstabEnabled(false);
        gridEmp.setCrosstabGroupType("cgtConcat");
        gridEmp.setEditionEnabled(false);
        gridEmp.setNoBorder(false);
        TFGridColumn item26 = new TFGridColumn();
        item26.setFieldName("CHECKED");
        item26.setTitleCaption("Escolhe");
        item26.setWidth(90);
        item26.setVisible(true);
        item26.setPrecision(0);
        item26.setTextAlign("taLeft");
        item26.setFieldType("ftString");
        item26.setFlexRatio(0);
        item26.setSort(false);
        item26.setImageHeader(0);
        item26.setWrap(false);
        item26.setFlex(false);
        TFImageExpression item27 = new TFImageExpression();
        item27.setExpression("CHECKED = 'S'");
        item27.setEvalType("etExpression");
        item27.setImageId(310010);
        item27.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridEmpmarcar(event);
            processarFlow("FrmBtoBEnviarLink", "item27", "OnClick");
        });
        item26.getImages().add(item27);
        TFImageExpression item28 = new TFImageExpression();
        item28.setExpression("CHECKED = 'N'");
        item28.setEvalType("etExpression");
        item28.setImageId(310011);
        item28.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridEmpmarcar(event);
            processarFlow("FrmBtoBEnviarLink", "item28", "OnClick");
        });
        item26.getImages().add(item28);
        item26.setCharCase("ccNormal");
        item26.setBlobConfigMimeType("bmtText");
        item26.setBlobConfigShowType("btImageViewer");
        item26.setShowLabel(false);
        item26.setEditorEditType("etTFString");
        item26.setEditorPrecision(0);
        item26.setEditorMaxLength(100);
        item26.setEditorLookupFilterKey(0);
        item26.setEditorLookupFilterDesc(0);
        item26.setEditorPopupHeight(400);
        item26.setEditorPopupWidth(400);
        item26.setEditorCharCase("ccNormal");
        item26.setEditorEnabled(false);
        item26.setEditorReadOnly(false);
        item26.setHiperLink(false);
        item26.setEditorConstraintCheckWhen("cwImmediate");
        item26.setEditorConstraintCheckType("ctExpression");
        item26.setEditorConstraintFocusOnError(false);
        item26.setEditorConstraintEnableUI(true);
        item26.setEditorConstraintEnabled(false);
        item26.setEmpty(false);
        item26.setMobileOptsShowMobile(false);
        item26.setMobileOptsOrder(0);
        gridEmp.getColumns().add(item26);
        TFGridColumn item29 = new TFGridColumn();
        item29.setFieldName("PRINCIPAL");
        item29.setTitleCaption("Principal");
        item29.setWidth(90);
        item29.setVisible(true);
        item29.setPrecision(0);
        item29.setTextAlign("taLeft");
        item29.setFieldType("ftString");
        item29.setFlexRatio(0);
        item29.setSort(false);
        item29.setImageHeader(0);
        item29.setWrap(false);
        item29.setFlex(false);
        TFImageExpression item30 = new TFImageExpression();
        item30.setExpression("PRINCIPAL='S'");
        item30.setEvalType("etExpression");
        item30.setImageId(7000110);
        item30.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridEmpimgClick(event);
            processarFlow("FrmBtoBEnviarLink", "item30", "OnClick");
        });
        item29.getImages().add(item30);
        TFImageExpression item31 = new TFImageExpression();
        item31.setExpression("PRINCIPAL = 'N'");
        item31.setEvalType("etExpression");
        item31.setImageId(7000111);
        item31.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridEmpimgClick(event);
            processarFlow("FrmBtoBEnviarLink", "item31", "OnClick");
        });
        item29.getImages().add(item31);
        item29.setCharCase("ccNormal");
        item29.setBlobConfigMimeType("bmtText");
        item29.setBlobConfigShowType("btImageViewer");
        item29.setShowLabel(false);
        item29.setEditorEditType("etTFString");
        item29.setEditorPrecision(0);
        item29.setEditorMaxLength(100);
        item29.setEditorLookupFilterKey(0);
        item29.setEditorLookupFilterDesc(0);
        item29.setEditorPopupHeight(400);
        item29.setEditorPopupWidth(400);
        item29.setEditorCharCase("ccNormal");
        item29.setEditorEnabled(false);
        item29.setEditorReadOnly(false);
        item29.setHiperLink(false);
        item29.setEditorConstraintCheckWhen("cwImmediate");
        item29.setEditorConstraintCheckType("ctExpression");
        item29.setEditorConstraintFocusOnError(false);
        item29.setEditorConstraintEnableUI(true);
        item29.setEditorConstraintEnabled(false);
        item29.setEmpty(false);
        item29.setMobileOptsShowMobile(false);
        item29.setMobileOptsOrder(0);
        gridEmp.getColumns().add(item29);
        TFGridColumn item32 = new TFGridColumn();
        item32.setFieldName("COD_EMPRESA");
        item32.setTitleCaption("C\u00F3d. Empresa");
        item32.setWidth(90);
        item32.setVisible(true);
        item32.setPrecision(0);
        item32.setTextAlign("taLeft");
        item32.setFieldType("ftString");
        item32.setFlexRatio(0);
        item32.setSort(false);
        item32.setImageHeader(0);
        item32.setWrap(false);
        item32.setFlex(false);
        item32.setCharCase("ccNormal");
        item32.setBlobConfigMimeType("bmtText");
        item32.setBlobConfigShowType("btImageViewer");
        item32.setShowLabel(true);
        item32.setEditorEditType("etTFString");
        item32.setEditorPrecision(0);
        item32.setEditorMaxLength(100);
        item32.setEditorLookupFilterKey(0);
        item32.setEditorLookupFilterDesc(0);
        item32.setEditorPopupHeight(400);
        item32.setEditorPopupWidth(400);
        item32.setEditorCharCase("ccNormal");
        item32.setEditorEnabled(false);
        item32.setEditorReadOnly(false);
        item32.setHiperLink(false);
        item32.setEditorConstraintCheckWhen("cwImmediate");
        item32.setEditorConstraintCheckType("ctExpression");
        item32.setEditorConstraintFocusOnError(false);
        item32.setEditorConstraintEnableUI(true);
        item32.setEditorConstraintEnabled(false);
        item32.setEmpty(false);
        item32.setMobileOptsShowMobile(false);
        item32.setMobileOptsOrder(0);
        gridEmp.getColumns().add(item32);
        TFGridColumn item33 = new TFGridColumn();
        item33.setFieldName("NOME");
        item33.setTitleCaption("Empresa");
        item33.setWidth(180);
        item33.setVisible(true);
        item33.setPrecision(0);
        item33.setTextAlign("taLeft");
        item33.setFieldType("ftString");
        item33.setFlexRatio(0);
        item33.setSort(false);
        item33.setImageHeader(0);
        item33.setWrap(false);
        item33.setFlex(true);
        item33.setCharCase("ccNormal");
        item33.setBlobConfigMimeType("bmtText");
        item33.setBlobConfigShowType("btImageViewer");
        item33.setShowLabel(true);
        item33.setEditorEditType("etTFString");
        item33.setEditorPrecision(0);
        item33.setEditorMaxLength(100);
        item33.setEditorLookupFilterKey(0);
        item33.setEditorLookupFilterDesc(0);
        item33.setEditorPopupHeight(400);
        item33.setEditorPopupWidth(400);
        item33.setEditorCharCase("ccNormal");
        item33.setEditorEnabled(false);
        item33.setEditorReadOnly(false);
        item33.setHiperLink(false);
        item33.setEditorConstraintCheckWhen("cwImmediate");
        item33.setEditorConstraintCheckType("ctExpression");
        item33.setEditorConstraintFocusOnError(false);
        item33.setEditorConstraintEnableUI(true);
        item33.setEditorConstraintEnabled(false);
        item33.setEmpty(false);
        item33.setMobileOptsShowMobile(false);
        item33.setMobileOptsOrder(0);
        gridEmp.getColumns().add(item33);
        TFGridColumn item34 = new TFGridColumn();
        item34.setFieldName("ESTADO");
        item34.setTitleCaption("UF");
        item34.setWidth(90);
        item34.setVisible(true);
        item34.setPrecision(0);
        item34.setTextAlign("taLeft");
        item34.setFieldType("ftString");
        item34.setFlexRatio(0);
        item34.setSort(false);
        item34.setImageHeader(0);
        item34.setWrap(false);
        item34.setFlex(false);
        item34.setCharCase("ccNormal");
        item34.setBlobConfigMimeType("bmtText");
        item34.setBlobConfigShowType("btImageViewer");
        item34.setShowLabel(true);
        item34.setEditorEditType("etTFString");
        item34.setEditorPrecision(0);
        item34.setEditorMaxLength(100);
        item34.setEditorLookupFilterKey(0);
        item34.setEditorLookupFilterDesc(0);
        item34.setEditorPopupHeight(400);
        item34.setEditorPopupWidth(400);
        item34.setEditorCharCase("ccNormal");
        item34.setEditorEnabled(false);
        item34.setEditorReadOnly(false);
        item34.setHiperLink(false);
        item34.setEditorConstraintCheckWhen("cwImmediate");
        item34.setEditorConstraintCheckType("ctExpression");
        item34.setEditorConstraintFocusOnError(false);
        item34.setEditorConstraintEnableUI(true);
        item34.setEditorConstraintEnabled(false);
        item34.setEmpty(false);
        item34.setMobileOptsShowMobile(false);
        item34.setMobileOptsOrder(0);
        gridEmp.getColumns().add(item34);
        TFGridColumn item35 = new TFGridColumn();
        item35.setFieldName("CIDADE");
        item35.setTitleCaption("Cidade");
        item35.setWidth(180);
        item35.setVisible(true);
        item35.setPrecision(0);
        item35.setTextAlign("taLeft");
        item35.setFieldType("ftString");
        item35.setFlexRatio(0);
        item35.setSort(false);
        item35.setImageHeader(0);
        item35.setWrap(false);
        item35.setFlex(false);
        item35.setCharCase("ccNormal");
        item35.setBlobConfigMimeType("bmtText");
        item35.setBlobConfigShowType("btImageViewer");
        item35.setShowLabel(true);
        item35.setEditorEditType("etTFString");
        item35.setEditorPrecision(0);
        item35.setEditorMaxLength(100);
        item35.setEditorLookupFilterKey(0);
        item35.setEditorLookupFilterDesc(0);
        item35.setEditorPopupHeight(400);
        item35.setEditorPopupWidth(400);
        item35.setEditorCharCase("ccNormal");
        item35.setEditorEnabled(false);
        item35.setEditorReadOnly(false);
        item35.setHiperLink(false);
        item35.setEditorConstraintCheckWhen("cwImmediate");
        item35.setEditorConstraintCheckType("ctExpression");
        item35.setEditorConstraintFocusOnError(false);
        item35.setEditorConstraintEnableUI(true);
        item35.setEditorConstraintEnabled(false);
        item35.setEmpty(false);
        item35.setMobileOptsShowMobile(false);
        item35.setMobileOptsOrder(0);
        gridEmp.getColumns().add(item35);
        FTabsheet3.addChildren(gridEmp);
        gridEmp.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void FVBox3Click(final Event<Object> event);

    public abstract void GridClientesEmailGridClientesEmailCheckClick(final Event<Object> event);

    public abstract void GridClientesEmailGridClientesEmailCheckDoubleClick(final Event<Object> event);

    public abstract void GridClientesEmailGridClientesEmaiClienteDoubleClick(final Event<Object> event);

    public abstract void GridClientesEmailGridClientesEmailCnpjDoubleClick(final Event<Object> event);

    public abstract void gridFatimgFatura(final Event<Object> event);

    public abstract void gridEntimgEntrega(final Event<Object> event);

    public abstract void gridEmpmarcar(final Event<Object> event);

    public abstract void gridEmpimgClick(final Event<Object> event);

}