package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmIntegracaoSap extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.IntegracaoSapRNA rn = null;

    public FrmIntegracaoSap() {
        try {
            rn = (freedom.bytecode.rn.IntegracaoSapRNA) getRN(freedom.bytecode.rn.wizard.IntegracaoSapRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_timerSap();
        init_FVBox1();
        init_FHBox1();
        init_hBoxStatus();
        init_FHBox2();
        init_lblStatusIntegracao();
        init_hBoxExpande();
        init_hBoxCancelar();
        init_hBoxSepCancelar();
        init_vBoxIconCancelar();
        init_FHBox5();
        init_iconCancelar();
        init_FHBox7();
        init_vBoxBtnCancelar();
        init_FHBox6();
        init_lblAcaoCancelar();
        init_FHBox4();
        init_FHBox3();
        init_FrmIntegracaoSap();
    }

    public TFTimer timerSap = new TFTimer();

    private void init_timerSap() {
        timerSap.setName("timerSap");
        timerSap.setEnabled(false);
        timerSap.setInterval(0);
        timerSap.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            timerSapTimer(event);
            processarFlow("FrmIntegracaoSap", "timerSap", "OnTimer");
        });
        timerSap.setRepeats(false);
        FrmIntegracaoSap.addChildren(timerSap);
        timerSap.applyProperties();
    }

    protected TFForm FrmIntegracaoSap = this;
    private void init_FrmIntegracaoSap() {
        FrmIntegracaoSap.setName("FrmIntegracaoSap");
        FrmIntegracaoSap.setCaption("Integra\u00E7\u00E3o SAP");
        FrmIntegracaoSap.setClientHeight(57);
        FrmIntegracaoSap.setClientWidth(724);
        FrmIntegracaoSap.setColor("clBtnFace");
        FrmIntegracaoSap.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmIntegracaoSap", "FrmIntegracaoSap", "OnCreate");
        });
        FrmIntegracaoSap.setWOrigem("EhMain");
        FrmIntegracaoSap.setWKey("171027");
        FrmIntegracaoSap.setSpacing(0);
        FrmIntegracaoSap.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(8);
        FVBox1.setTop(3);
        FVBox1.setWidth(716);
        FVBox1.setHeight(50);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmIntegracaoSap.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(185);
        FHBox1.setHeight(3);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox hBoxStatus = new TFHBox();

    private void init_hBoxStatus() {
        hBoxStatus.setName("hBoxStatus");
        hBoxStatus.setLeft(0);
        hBoxStatus.setTop(4);
        hBoxStatus.setWidth(637);
        hBoxStatus.setHeight(31);
        hBoxStatus.setBorderStyle("stNone");
        hBoxStatus.setPaddingTop(0);
        hBoxStatus.setPaddingLeft(0);
        hBoxStatus.setPaddingRight(0);
        hBoxStatus.setPaddingBottom(0);
        hBoxStatus.setMarginTop(0);
        hBoxStatus.setMarginLeft(0);
        hBoxStatus.setMarginRight(0);
        hBoxStatus.setMarginBottom(0);
        hBoxStatus.setSpacing(1);
        hBoxStatus.setFlexVflex("ftFalse");
        hBoxStatus.setFlexHflex("ftTrue");
        hBoxStatus.setScrollable(false);
        hBoxStatus.setBoxShadowConfigHorizontalLength(10);
        hBoxStatus.setBoxShadowConfigVerticalLength(10);
        hBoxStatus.setBoxShadowConfigBlurRadius(5);
        hBoxStatus.setBoxShadowConfigSpreadRadius(0);
        hBoxStatus.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatus.setBoxShadowConfigOpacity(75);
        hBoxStatus.setVAlign("tvTop");
        FVBox1.addChildren(hBoxStatus);
        hBoxStatus.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(7);
        FHBox2.setHeight(20);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxStatus.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFLabel lblStatusIntegracao = new TFLabel();

    private void init_lblStatusIntegracao() {
        lblStatusIntegracao.setName("lblStatusIntegracao");
        lblStatusIntegracao.setLeft(7);
        lblStatusIntegracao.setTop(0);
        lblStatusIntegracao.setWidth(68);
        lblStatusIntegracao.setHeight(16);
        lblStatusIntegracao.setCaption("Aguarde...");
        lblStatusIntegracao.setFontColor("clWindowText");
        lblStatusIntegracao.setFontSize(-13);
        lblStatusIntegracao.setFontName("Tahoma");
        lblStatusIntegracao.setFontStyle("[fsBold]");
        lblStatusIntegracao.setVerticalAlignment("taVerticalCenter");
        lblStatusIntegracao.setWordBreak(false);
        hBoxStatus.addChildren(lblStatusIntegracao);
        lblStatusIntegracao.applyProperties();
    }

    public TFHBox hBoxExpande = new TFHBox();

    private void init_hBoxExpande() {
        hBoxExpande.setName("hBoxExpande");
        hBoxExpande.setLeft(75);
        hBoxExpande.setTop(0);
        hBoxExpande.setWidth(5);
        hBoxExpande.setHeight(15);
        hBoxExpande.setBorderStyle("stNone");
        hBoxExpande.setPaddingTop(0);
        hBoxExpande.setPaddingLeft(0);
        hBoxExpande.setPaddingRight(0);
        hBoxExpande.setPaddingBottom(0);
        hBoxExpande.setMarginTop(0);
        hBoxExpande.setMarginLeft(0);
        hBoxExpande.setMarginRight(0);
        hBoxExpande.setMarginBottom(0);
        hBoxExpande.setSpacing(1);
        hBoxExpande.setFlexVflex("ftFalse");
        hBoxExpande.setFlexHflex("ftTrue");
        hBoxExpande.setScrollable(false);
        hBoxExpande.setBoxShadowConfigHorizontalLength(10);
        hBoxExpande.setBoxShadowConfigVerticalLength(10);
        hBoxExpande.setBoxShadowConfigBlurRadius(5);
        hBoxExpande.setBoxShadowConfigSpreadRadius(0);
        hBoxExpande.setBoxShadowConfigShadowColor("clBlack");
        hBoxExpande.setBoxShadowConfigOpacity(75);
        hBoxExpande.setVAlign("tvTop");
        hBoxStatus.addChildren(hBoxExpande);
        hBoxExpande.applyProperties();
    }

    public TFHBox hBoxCancelar = new TFHBox();

    private void init_hBoxCancelar() {
        hBoxCancelar.setName("hBoxCancelar");
        hBoxCancelar.setLeft(80);
        hBoxCancelar.setTop(0);
        hBoxCancelar.setWidth(87);
        hBoxCancelar.setHeight(26);
        hBoxCancelar.setBorderStyle("stRaised");
        hBoxCancelar.setPaddingTop(0);
        hBoxCancelar.setPaddingLeft(0);
        hBoxCancelar.setPaddingRight(0);
        hBoxCancelar.setPaddingBottom(0);
        hBoxCancelar.setVisible(false);
        hBoxCancelar.setMarginTop(0);
        hBoxCancelar.setMarginLeft(0);
        hBoxCancelar.setMarginRight(0);
        hBoxCancelar.setMarginBottom(0);
        hBoxCancelar.setSpacing(1);
        hBoxCancelar.setFlexVflex("ftFalse");
        hBoxCancelar.setFlexHflex("ftFalse");
        hBoxCancelar.setScrollable(false);
        hBoxCancelar.setBoxShadowConfigHorizontalLength(10);
        hBoxCancelar.setBoxShadowConfigVerticalLength(10);
        hBoxCancelar.setBoxShadowConfigBlurRadius(5);
        hBoxCancelar.setBoxShadowConfigSpreadRadius(0);
        hBoxCancelar.setBoxShadowConfigShadowColor("clBlack");
        hBoxCancelar.setBoxShadowConfigOpacity(75);
        hBoxCancelar.setVAlign("tvTop");
        hBoxStatus.addChildren(hBoxCancelar);
        hBoxCancelar.applyProperties();
    }

    public TFHBox hBoxSepCancelar = new TFHBox();

    private void init_hBoxSepCancelar() {
        hBoxSepCancelar.setName("hBoxSepCancelar");
        hBoxSepCancelar.setLeft(1);
        hBoxSepCancelar.setTop(1);
        hBoxSepCancelar.setWidth(5);
        hBoxSepCancelar.setHeight(20);
        hBoxSepCancelar.setBorderStyle("stNone");
        hBoxSepCancelar.setPaddingTop(0);
        hBoxSepCancelar.setPaddingLeft(0);
        hBoxSepCancelar.setPaddingRight(0);
        hBoxSepCancelar.setPaddingBottom(0);
        hBoxSepCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSepCancelarClick(event);
            processarFlow("FrmIntegracaoSap", "hBoxSepCancelar", "OnClick");
        });
        hBoxSepCancelar.setMarginTop(0);
        hBoxSepCancelar.setMarginLeft(0);
        hBoxSepCancelar.setMarginRight(0);
        hBoxSepCancelar.setMarginBottom(0);
        hBoxSepCancelar.setSpacing(1);
        hBoxSepCancelar.setFlexVflex("ftFalse");
        hBoxSepCancelar.setFlexHflex("ftFalse");
        hBoxSepCancelar.setScrollable(false);
        hBoxSepCancelar.setBoxShadowConfigHorizontalLength(10);
        hBoxSepCancelar.setBoxShadowConfigVerticalLength(10);
        hBoxSepCancelar.setBoxShadowConfigBlurRadius(5);
        hBoxSepCancelar.setBoxShadowConfigSpreadRadius(0);
        hBoxSepCancelar.setBoxShadowConfigShadowColor("clBlack");
        hBoxSepCancelar.setBoxShadowConfigOpacity(75);
        hBoxSepCancelar.setVAlign("tvTop");
        hBoxCancelar.addChildren(hBoxSepCancelar);
        hBoxSepCancelar.applyProperties();
    }

    public TFVBox vBoxIconCancelar = new TFVBox();

    private void init_vBoxIconCancelar() {
        vBoxIconCancelar.setName("vBoxIconCancelar");
        vBoxIconCancelar.setLeft(6);
        vBoxIconCancelar.setTop(1);
        vBoxIconCancelar.setWidth(21);
        vBoxIconCancelar.setHeight(20);
        vBoxIconCancelar.setBorderStyle("stNone");
        vBoxIconCancelar.setPaddingTop(0);
        vBoxIconCancelar.setPaddingLeft(0);
        vBoxIconCancelar.setPaddingRight(0);
        vBoxIconCancelar.setPaddingBottom(0);
        vBoxIconCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxIconCancelarClick(event);
            processarFlow("FrmIntegracaoSap", "vBoxIconCancelar", "OnClick");
        });
        vBoxIconCancelar.setMarginTop(0);
        vBoxIconCancelar.setMarginLeft(0);
        vBoxIconCancelar.setMarginRight(0);
        vBoxIconCancelar.setMarginBottom(0);
        vBoxIconCancelar.setSpacing(1);
        vBoxIconCancelar.setFlexVflex("ftFalse");
        vBoxIconCancelar.setFlexHflex("ftFalse");
        vBoxIconCancelar.setScrollable(false);
        vBoxIconCancelar.setBoxShadowConfigHorizontalLength(10);
        vBoxIconCancelar.setBoxShadowConfigVerticalLength(10);
        vBoxIconCancelar.setBoxShadowConfigBlurRadius(5);
        vBoxIconCancelar.setBoxShadowConfigSpreadRadius(0);
        vBoxIconCancelar.setBoxShadowConfigShadowColor("clBlack");
        vBoxIconCancelar.setBoxShadowConfigOpacity(75);
        hBoxCancelar.addChildren(vBoxIconCancelar);
        vBoxIconCancelar.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(16);
        FHBox5.setHeight(1);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        vBoxIconCancelar.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFIconClass iconCancelar = new TFIconClass();

    private void init_iconCancelar() {
        iconCancelar.setName("iconCancelar");
        iconCancelar.setLeft(0);
        iconCancelar.setTop(2);
        iconCancelar.setHint("Cancelar consulta SAP");
        iconCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconCancelarClick(event);
            processarFlow("FrmIntegracaoSap", "iconCancelar", "OnClick");
        });
        iconCancelar.setIconClass("times-circle");
        iconCancelar.setSize(16);
        iconCancelar.setColor("clRed");
        vBoxIconCancelar.addChildren(iconCancelar);
        iconCancelar.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(27);
        FHBox7.setTop(1);
        FHBox7.setWidth(3);
        FHBox7.setHeight(20);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setVisible(false);
        FHBox7.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSepCancelarClick(event);
            processarFlow("FrmIntegracaoSap", "FHBox7", "OnClick");
        });
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        hBoxCancelar.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFVBox vBoxBtnCancelar = new TFVBox();

    private void init_vBoxBtnCancelar() {
        vBoxBtnCancelar.setName("vBoxBtnCancelar");
        vBoxBtnCancelar.setLeft(30);
        vBoxBtnCancelar.setTop(1);
        vBoxBtnCancelar.setWidth(54);
        vBoxBtnCancelar.setHeight(21);
        vBoxBtnCancelar.setBorderStyle("stNone");
        vBoxBtnCancelar.setPaddingTop(0);
        vBoxBtnCancelar.setPaddingLeft(0);
        vBoxBtnCancelar.setPaddingRight(0);
        vBoxBtnCancelar.setPaddingBottom(0);
        vBoxBtnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxBtnCancelarClick(event);
            processarFlow("FrmIntegracaoSap", "vBoxBtnCancelar", "OnClick");
        });
        vBoxBtnCancelar.setMarginTop(0);
        vBoxBtnCancelar.setMarginLeft(0);
        vBoxBtnCancelar.setMarginRight(0);
        vBoxBtnCancelar.setMarginBottom(0);
        vBoxBtnCancelar.setSpacing(1);
        vBoxBtnCancelar.setFlexVflex("ftFalse");
        vBoxBtnCancelar.setFlexHflex("ftFalse");
        vBoxBtnCancelar.setScrollable(false);
        vBoxBtnCancelar.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnCancelar.setBoxShadowConfigVerticalLength(10);
        vBoxBtnCancelar.setBoxShadowConfigBlurRadius(5);
        vBoxBtnCancelar.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnCancelar.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnCancelar.setBoxShadowConfigOpacity(75);
        hBoxCancelar.addChildren(vBoxBtnCancelar);
        vBoxBtnCancelar.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(30);
        FHBox6.setHeight(1);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxBtnCancelar.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFLabel lblAcaoCancelar = new TFLabel();

    private void init_lblAcaoCancelar() {
        lblAcaoCancelar.setName("lblAcaoCancelar");
        lblAcaoCancelar.setLeft(0);
        lblAcaoCancelar.setTop(2);
        lblAcaoCancelar.setWidth(42);
        lblAcaoCancelar.setHeight(13);
        lblAcaoCancelar.setCaption("Cancelar");
        lblAcaoCancelar.setFontColor("clRed");
        lblAcaoCancelar.setFontSize(-11);
        lblAcaoCancelar.setFontName("Tahoma");
        lblAcaoCancelar.setFontStyle("[]");
        lblAcaoCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblAcaoCancelarClick(event);
            processarFlow("FrmIntegracaoSap", "lblAcaoCancelar", "OnClick");
        });
        lblAcaoCancelar.setVerticalAlignment("taVerticalCenter");
        lblAcaoCancelar.setWordBreak(false);
        vBoxBtnCancelar.addChildren(lblAcaoCancelar);
        lblAcaoCancelar.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(167);
        FHBox4.setTop(0);
        FHBox4.setWidth(2);
        FHBox4.setHeight(20);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        hBoxStatus.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(36);
        FHBox3.setWidth(185);
        FHBox3.setHeight(5);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void hBoxSepCancelarClick(final Event<Object> event);

    public abstract void vBoxIconCancelarClick(final Event<Object> event);

    public abstract void iconCancelarClick(final Event<Object> event);

    public abstract void vBoxBtnCancelarClick(final Event<Object> event);

    public abstract void lblAcaoCancelarClick(final Event<Object> event);

    public abstract void timerSapTimer(final Event<Object> event);

}