package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmVendedorResponsavel extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.VendedorResponsavelRNA rn = null;

    public FrmVendedorResponsavel() {
        try {
            rn = (freedom.bytecode.rn.VendedorResponsavelRNA) getRN(freedom.bytecode.rn.wizard.VendedorResponsavelRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresasCadastro();
        init_tbEmpresasUsuarios();
        init_tbLeadsVendedores();
        init_tbListaClienteResponsavel();
        init_tbClienteResponsavel();
        init_tbVendedoresListagem();
        init_tbEmpresasListagem();
        init_tbVendedoresCadastro();
        init_vBoxVendedorResponsavel();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnPesquisar();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnCancelar();
        init_hBoxSeparadorBotoes();
        init_vBoxHelp();
        init_iconClassHelp();
        init_pgcVendedorResponsavel();
        init_tbsListagem();
        init_vBoxCadastroVendResp();
        init_hBoxComboBox();
        init_hBoxSeparadorComboBox1();
        init_vBoxEmpresa();
        init_vBoxEmpresaSep1();
        init_lblEmpresa();
        init_vBoxEmpresaSep2();
        init_cboEmpresa();
        init_vBoxEmpresaSep3();
        init_hBoxSeparadorComboBox2();
        init_vBoxSistema();
        init_vBoxSistemaSep1();
        init_lblSistema();
        init_vBoxSistemaSep2();
        init_cboSistema();
        init_vBoxSistemaSep3();
        init_hBoxSeparadorComboBox3();
        init_vBoxProcesso();
        init_vBoxProcessoSep1();
        init_lblProcesso();
        init_vBoxProcessoSep2();
        init_cboProcesso();
        init_vBoxProcessoSep3();
        init_hBoxSeparadorComboBox4();
        init_vBoxTemperatura();
        init_vBoxTempSep1();
        init_lblTemperatura();
        init_vBoxTempSep2();
        init_cboTemperatura();
        init_vBoxTempSep3();
        init_hBoxSeparadorComboBox5();
        init_vBoxVendedor();
        init_vBoxVendedorSep1();
        init_lblVendedor();
        init_vBoxVendedorSep2();
        init_cboVendedor();
        init_vBoxVendedorSep3();
        init_hBoxSeparadorComboBox6();
        init_hBoxGrid();
        init_hBoxGridDiv1();
        init_gridVendedores();
        init_hBoxGridDiv2();
        init_hBoxDiv3();
        init_tbsCadastro();
        init_vBoxAlteracaoVendResp();
        init_hBoxComboBoxAlteracao();
        init_hBoxaltDiv2();
        init_vBoxEmpresaAlteracao();
        init_hBoxaltDiv3();
        init_lblAltEmpresa();
        init_hBoxaltDiv4();
        init_cboAltEmpresa();
        init_hBoxaltDiv5();
        init_hBoxaltDiv19();
        init_vBoxSistemaAlteracao();
        init_hBoxaltDiv6();
        init_lblAltSistema();
        init_hBoxaltDiv7();
        init_cboAltSistema();
        init_hBoxaltDiv8();
        init_hBoxaltDiv20();
        init_vBoxProcessoAlteracao();
        init_hBoxaltDiv9();
        init_lblAltProcesso();
        init_hBoxaltDiv10();
        init_cboAltProcesso();
        init_hBoxaltDiv11();
        init_hBoxaltDiv21();
        init_vBoxTemperaturaAlteracao();
        init_hBoxaltDiv12();
        init_lblAltTemperatura();
        init_hBoxaltDiv13();
        init_cboAltTemperatura();
        init_hBoxaltDiv14();
        init_hBoxaltDiv22();
        init_vBoxVendedorAlteracao();
        init_hBoxaltDiv15();
        init_lblAltvendedor();
        init_hBoxaltDiv16();
        init_cboAltVendedor();
        init_hBoxaltDiv17();
        init_hBoxaltDiv18();
        init_scClienteResponsavel();
        init_FrmVendedorResponsavel();
    }

    public LEADS_EMPRESAS_USUARIOS tbEmpresasCadastro;

    private void init_tbEmpresasCadastro() {
        tbEmpresasCadastro = rn.tbEmpresasCadastro;
        tbEmpresasCadastro.setName("tbEmpresasCadastro");
        tbEmpresasCadastro.setMaxRowCount(200);
        tbEmpresasCadastro.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbEmpresasCadastroAfterScroll(event);
            processarFlow("FrmVendedorResponsavel", "tbEmpresasCadastro", "OnAfterScroll");
        });
        tbEmpresasCadastro.setWKey("284017;28401");
        tbEmpresasCadastro.setRatioBatchSize(20);
        getTables().put(tbEmpresasCadastro, "tbEmpresasCadastro");
        tbEmpresasCadastro.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("284017;28403");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public LEADS_VENDEDORES tbLeadsVendedores;

    private void init_tbLeadsVendedores() {
        tbLeadsVendedores = rn.tbLeadsVendedores;
        tbLeadsVendedores.setName("tbLeadsVendedores");
        tbLeadsVendedores.setMaxRowCount(200);
        tbLeadsVendedores.setWKey("284017;28404");
        tbLeadsVendedores.setRatioBatchSize(20);
        getTables().put(tbLeadsVendedores, "tbLeadsVendedores");
        tbLeadsVendedores.applyProperties();
    }

    public LISTA_CLIENTE_RESPONSAVEL tbListaClienteResponsavel;

    private void init_tbListaClienteResponsavel() {
        tbListaClienteResponsavel = rn.tbListaClienteResponsavel;
        tbListaClienteResponsavel.setName("tbListaClienteResponsavel");
        tbListaClienteResponsavel.setMaxRowCount(200);
        tbListaClienteResponsavel.setWKey("284017;28405");
        tbListaClienteResponsavel.setRatioBatchSize(20);
        getTables().put(tbListaClienteResponsavel, "tbListaClienteResponsavel");
        tbListaClienteResponsavel.applyProperties();
    }

    public CLIENTE_RESPONSAVEL tbClienteResponsavel;

    private void init_tbClienteResponsavel() {
        tbClienteResponsavel = rn.tbClienteResponsavel;
        tbClienteResponsavel.setName("tbClienteResponsavel");
        tbClienteResponsavel.setMaxRowCount(200);
        tbClienteResponsavel.setWKey("284017;28407");
        tbClienteResponsavel.setRatioBatchSize(20);
        getTables().put(tbClienteResponsavel, "tbClienteResponsavel");
        tbClienteResponsavel.applyProperties();
    }

    public LISTAR_VENDEDORES tbVendedoresListagem;

    private void init_tbVendedoresListagem() {
        tbVendedoresListagem = rn.tbVendedoresListagem;
        tbVendedoresListagem.setName("tbVendedoresListagem");
        tbVendedoresListagem.setMaxRowCount(200);
        tbVendedoresListagem.setWKey("284017;28408");
        tbVendedoresListagem.setRatioBatchSize(20);
        getTables().put(tbVendedoresListagem, "tbVendedoresListagem");
        tbVendedoresListagem.applyProperties();
    }

    public LEADS_EMPRESAS_USUARIOS tbEmpresasListagem;

    private void init_tbEmpresasListagem() {
        tbEmpresasListagem = rn.tbEmpresasListagem;
        tbEmpresasListagem.setName("tbEmpresasListagem");
        tbEmpresasListagem.setMaxRowCount(200);
        tbEmpresasListagem.setWKey("284017;42201");
        tbEmpresasListagem.setRatioBatchSize(20);
        getTables().put(tbEmpresasListagem, "tbEmpresasListagem");
        tbEmpresasListagem.applyProperties();
    }

    public LISTAR_VENDEDORES tbVendedoresCadastro;

    private void init_tbVendedoresCadastro() {
        tbVendedoresCadastro = rn.tbVendedoresCadastro;
        tbVendedoresCadastro.setName("tbVendedoresCadastro");
        tbVendedoresCadastro.setMaxRowCount(200);
        tbVendedoresCadastro.setWKey("284017;42203");
        tbVendedoresCadastro.setRatioBatchSize(20);
        getTables().put(tbVendedoresCadastro, "tbVendedoresCadastro");
        tbVendedoresCadastro.applyProperties();
    }

    protected TFForm FrmVendedorResponsavel = this;
    private void init_FrmVendedorResponsavel() {
        FrmVendedorResponsavel.setName("FrmVendedorResponsavel");
        FrmVendedorResponsavel.setCaption("Vendedor Respons\u00E1vel");
        FrmVendedorResponsavel.setClientHeight(545);
        FrmVendedorResponsavel.setClientWidth(866);
        FrmVendedorResponsavel.setColor("clBtnFace");
        FrmVendedorResponsavel.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmVendedorResponsavel", "FrmVendedorResponsavel", "OnCreate");
        });
        FrmVendedorResponsavel.setWOrigem("EhMain");
        FrmVendedorResponsavel.setWKey("284017");
        FrmVendedorResponsavel.setSpacing(0);
        FrmVendedorResponsavel.applyProperties();
    }

    public TFVBox vBoxVendedorResponsavel = new TFVBox();

    private void init_vBoxVendedorResponsavel() {
        vBoxVendedorResponsavel.setName("vBoxVendedorResponsavel");
        vBoxVendedorResponsavel.setLeft(0);
        vBoxVendedorResponsavel.setTop(0);
        vBoxVendedorResponsavel.setWidth(866);
        vBoxVendedorResponsavel.setHeight(545);
        vBoxVendedorResponsavel.setAlign("alClient");
        vBoxVendedorResponsavel.setBorderStyle("stNone");
        vBoxVendedorResponsavel.setPaddingTop(0);
        vBoxVendedorResponsavel.setPaddingLeft(0);
        vBoxVendedorResponsavel.setPaddingRight(0);
        vBoxVendedorResponsavel.setPaddingBottom(0);
        vBoxVendedorResponsavel.setMarginTop(0);
        vBoxVendedorResponsavel.setMarginLeft(0);
        vBoxVendedorResponsavel.setMarginRight(0);
        vBoxVendedorResponsavel.setMarginBottom(0);
        vBoxVendedorResponsavel.setSpacing(1);
        vBoxVendedorResponsavel.setFlexVflex("ftTrue");
        vBoxVendedorResponsavel.setFlexHflex("ftTrue");
        vBoxVendedorResponsavel.setScrollable(false);
        vBoxVendedorResponsavel.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedorResponsavel.setBoxShadowConfigVerticalLength(10);
        vBoxVendedorResponsavel.setBoxShadowConfigBlurRadius(5);
        vBoxVendedorResponsavel.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedorResponsavel.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedorResponsavel.setBoxShadowConfigOpacity(75);
        FrmVendedorResponsavel.addChildren(vBoxVendedorResponsavel);
        vBoxVendedorResponsavel.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(842);
        hBoxBotoes.setHeight(65);
        hBoxBotoes.setAlign("alTop");
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(5);
        hBoxBotoes.setPaddingLeft(5);
        hBoxBotoes.setPaddingRight(5);
        hBoxBotoes.setPaddingBottom(5);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftFalse");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxVendedorResponsavel.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(55);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmVendedorResponsavel", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(65);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(65);
        btnPesquisar.setHeight(55);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmVendedorResponsavel", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(13);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(130);
        btnNovo.setTop(0);
        btnNovo.setWidth(60);
        btnNovo.setHeight(55);
        btnNovo.setHint("Novo");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-13);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmVendedorResponsavel", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(false);
        btnNovo.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(190);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(55);
        btnAlterar.setHint("Alterar Itens");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setEnabled(false);
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmVendedorResponsavel", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(255);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(55);
        btnExcluir.setHint("Excluir do banco de Pedido");
        btnExcluir.setAlign("alCustom");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setEnabled(false);
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmVendedorResponsavel", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(320);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(55);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setEnabled(false);
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmVendedorResponsavel", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(385);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(55);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setEnabled(false);
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmVendedorResponsavel", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox hBoxSeparadorBotoes = new TFHBox();

    private void init_hBoxSeparadorBotoes() {
        hBoxSeparadorBotoes.setName("hBoxSeparadorBotoes");
        hBoxSeparadorBotoes.setLeft(450);
        hBoxSeparadorBotoes.setTop(0);
        hBoxSeparadorBotoes.setWidth(20);
        hBoxSeparadorBotoes.setHeight(20);
        hBoxSeparadorBotoes.setBorderStyle("stNone");
        hBoxSeparadorBotoes.setPaddingTop(0);
        hBoxSeparadorBotoes.setPaddingLeft(0);
        hBoxSeparadorBotoes.setPaddingRight(0);
        hBoxSeparadorBotoes.setPaddingBottom(0);
        hBoxSeparadorBotoes.setMarginTop(0);
        hBoxSeparadorBotoes.setMarginLeft(0);
        hBoxSeparadorBotoes.setMarginRight(0);
        hBoxSeparadorBotoes.setMarginBottom(0);
        hBoxSeparadorBotoes.setSpacing(1);
        hBoxSeparadorBotoes.setFlexVflex("ftFalse");
        hBoxSeparadorBotoes.setFlexHflex("ftTrue");
        hBoxSeparadorBotoes.setScrollable(false);
        hBoxSeparadorBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorBotoes.setBoxShadowConfigOpacity(75);
        hBoxSeparadorBotoes.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxSeparadorBotoes);
        hBoxSeparadorBotoes.applyProperties();
    }

    public TFVBox vBoxHelp = new TFVBox();

    private void init_vBoxHelp() {
        vBoxHelp.setName("vBoxHelp");
        vBoxHelp.setLeft(470);
        vBoxHelp.setTop(0);
        vBoxHelp.setWidth(30);
        vBoxHelp.setHeight(30);
        vBoxHelp.setBorderStyle("stNone");
        vBoxHelp.setPaddingTop(0);
        vBoxHelp.setPaddingLeft(0);
        vBoxHelp.setPaddingRight(0);
        vBoxHelp.setPaddingBottom(0);
        vBoxHelp.setMarginTop(0);
        vBoxHelp.setMarginLeft(0);
        vBoxHelp.setMarginRight(0);
        vBoxHelp.setMarginBottom(0);
        vBoxHelp.setSpacing(1);
        vBoxHelp.setFlexVflex("ftMin");
        vBoxHelp.setFlexHflex("ftMin");
        vBoxHelp.setScrollable(false);
        vBoxHelp.setBoxShadowConfigHorizontalLength(10);
        vBoxHelp.setBoxShadowConfigVerticalLength(10);
        vBoxHelp.setBoxShadowConfigBlurRadius(5);
        vBoxHelp.setBoxShadowConfigSpreadRadius(0);
        vBoxHelp.setBoxShadowConfigShadowColor("clBlack");
        vBoxHelp.setBoxShadowConfigOpacity(75);
        hBoxBotoes.addChildren(vBoxHelp);
        vBoxHelp.applyProperties();
    }

    public TFIconClass iconClassHelp = new TFIconClass();

    private void init_iconClassHelp() {
        iconClassHelp.setName("iconClassHelp");
        iconClassHelp.setLeft(0);
        iconClassHelp.setTop(0);
        iconClassHelp.setHint("Help");
        iconClassHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassHelpClick(event);
            processarFlow("FrmVendedorResponsavel", "iconClassHelp", "OnClick");
        });
        iconClassHelp.setIconClass("question-circle");
        iconClassHelp.setSize(24);
        iconClassHelp.setColor("clRed");
        vBoxHelp.addChildren(iconClassHelp);
        iconClassHelp.applyProperties();
    }

    public TFPageControl pgcVendedorResponsavel = new TFPageControl();

    private void init_pgcVendedorResponsavel() {
        pgcVendedorResponsavel.setName("pgcVendedorResponsavel");
        pgcVendedorResponsavel.setLeft(0);
        pgcVendedorResponsavel.setTop(66);
        pgcVendedorResponsavel.setWidth(854);
        pgcVendedorResponsavel.setHeight(473);
        pgcVendedorResponsavel.setTabPosition("tpTop");
        pgcVendedorResponsavel.setFlexVflex("ftTrue");
        pgcVendedorResponsavel.setFlexHflex("ftTrue");
        pgcVendedorResponsavel.setRenderStyle("rsTabbed");
        pgcVendedorResponsavel.applyProperties();
        vBoxVendedorResponsavel.addChildren(pgcVendedorResponsavel);
    }

    public TFTabsheet tbsListagem = new TFTabsheet();

    private void init_tbsListagem() {
        tbsListagem.setName("tbsListagem");
        tbsListagem.setCaption("Listagem");
        tbsListagem.setFontColor("clWindowText");
        tbsListagem.setFontSize(-11);
        tbsListagem.setFontName("Tahoma");
        tbsListagem.setFontStyle("[]");
        tbsListagem.setVisible(true);
        tbsListagem.setClosable(false);
        pgcVendedorResponsavel.addChildren(tbsListagem);
        tbsListagem.applyProperties();
    }

    public TFVBox vBoxCadastroVendResp = new TFVBox();

    private void init_vBoxCadastroVendResp() {
        vBoxCadastroVendResp.setName("vBoxCadastroVendResp");
        vBoxCadastroVendResp.setLeft(0);
        vBoxCadastroVendResp.setTop(0);
        vBoxCadastroVendResp.setWidth(846);
        vBoxCadastroVendResp.setHeight(445);
        vBoxCadastroVendResp.setAlign("alClient");
        vBoxCadastroVendResp.setBorderStyle("stNone");
        vBoxCadastroVendResp.setPaddingTop(0);
        vBoxCadastroVendResp.setPaddingLeft(0);
        vBoxCadastroVendResp.setPaddingRight(0);
        vBoxCadastroVendResp.setPaddingBottom(0);
        vBoxCadastroVendResp.setMarginTop(0);
        vBoxCadastroVendResp.setMarginLeft(0);
        vBoxCadastroVendResp.setMarginRight(0);
        vBoxCadastroVendResp.setMarginBottom(0);
        vBoxCadastroVendResp.setSpacing(1);
        vBoxCadastroVendResp.setFlexVflex("ftTrue");
        vBoxCadastroVendResp.setFlexHflex("ftTrue");
        vBoxCadastroVendResp.setScrollable(false);
        vBoxCadastroVendResp.setBoxShadowConfigHorizontalLength(10);
        vBoxCadastroVendResp.setBoxShadowConfigVerticalLength(10);
        vBoxCadastroVendResp.setBoxShadowConfigBlurRadius(5);
        vBoxCadastroVendResp.setBoxShadowConfigSpreadRadius(0);
        vBoxCadastroVendResp.setBoxShadowConfigShadowColor("clBlack");
        vBoxCadastroVendResp.setBoxShadowConfigOpacity(75);
        tbsListagem.addChildren(vBoxCadastroVendResp);
        vBoxCadastroVendResp.applyProperties();
    }

    public TFHBox hBoxComboBox = new TFHBox();

    private void init_hBoxComboBox() {
        hBoxComboBox.setName("hBoxComboBox");
        hBoxComboBox.setLeft(0);
        hBoxComboBox.setTop(0);
        hBoxComboBox.setWidth(836);
        hBoxComboBox.setHeight(75);
        hBoxComboBox.setAlign("alTop");
        hBoxComboBox.setBorderStyle("stNone");
        hBoxComboBox.setPaddingTop(0);
        hBoxComboBox.setPaddingLeft(0);
        hBoxComboBox.setPaddingRight(0);
        hBoxComboBox.setPaddingBottom(0);
        hBoxComboBox.setMarginTop(0);
        hBoxComboBox.setMarginLeft(0);
        hBoxComboBox.setMarginRight(0);
        hBoxComboBox.setMarginBottom(0);
        hBoxComboBox.setSpacing(1);
        hBoxComboBox.setFlexVflex("ftMin");
        hBoxComboBox.setFlexHflex("ftTrue");
        hBoxComboBox.setScrollable(false);
        hBoxComboBox.setBoxShadowConfigHorizontalLength(10);
        hBoxComboBox.setBoxShadowConfigVerticalLength(10);
        hBoxComboBox.setBoxShadowConfigBlurRadius(5);
        hBoxComboBox.setBoxShadowConfigSpreadRadius(0);
        hBoxComboBox.setBoxShadowConfigShadowColor("clBlack");
        hBoxComboBox.setBoxShadowConfigOpacity(75);
        hBoxComboBox.setVAlign("tvTop");
        vBoxCadastroVendResp.addChildren(hBoxComboBox);
        hBoxComboBox.applyProperties();
    }

    public TFHBox hBoxSeparadorComboBox1 = new TFHBox();

    private void init_hBoxSeparadorComboBox1() {
        hBoxSeparadorComboBox1.setName("hBoxSeparadorComboBox1");
        hBoxSeparadorComboBox1.setLeft(0);
        hBoxSeparadorComboBox1.setTop(0);
        hBoxSeparadorComboBox1.setWidth(5);
        hBoxSeparadorComboBox1.setHeight(20);
        hBoxSeparadorComboBox1.setBorderStyle("stNone");
        hBoxSeparadorComboBox1.setPaddingTop(0);
        hBoxSeparadorComboBox1.setPaddingLeft(0);
        hBoxSeparadorComboBox1.setPaddingRight(0);
        hBoxSeparadorComboBox1.setPaddingBottom(0);
        hBoxSeparadorComboBox1.setMarginTop(0);
        hBoxSeparadorComboBox1.setMarginLeft(0);
        hBoxSeparadorComboBox1.setMarginRight(0);
        hBoxSeparadorComboBox1.setMarginBottom(0);
        hBoxSeparadorComboBox1.setSpacing(1);
        hBoxSeparadorComboBox1.setFlexVflex("ftFalse");
        hBoxSeparadorComboBox1.setFlexHflex("ftFalse");
        hBoxSeparadorComboBox1.setScrollable(false);
        hBoxSeparadorComboBox1.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorComboBox1.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorComboBox1.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorComboBox1.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorComboBox1.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorComboBox1.setBoxShadowConfigOpacity(75);
        hBoxSeparadorComboBox1.setVAlign("tvTop");
        hBoxComboBox.addChildren(hBoxSeparadorComboBox1);
        hBoxSeparadorComboBox1.applyProperties();
    }

    public TFVBox vBoxEmpresa = new TFVBox();

    private void init_vBoxEmpresa() {
        vBoxEmpresa.setName("vBoxEmpresa");
        vBoxEmpresa.setLeft(5);
        vBoxEmpresa.setTop(0);
        vBoxEmpresa.setWidth(150);
        vBoxEmpresa.setHeight(70);
        vBoxEmpresa.setBorderStyle("stNone");
        vBoxEmpresa.setPaddingTop(0);
        vBoxEmpresa.setPaddingLeft(0);
        vBoxEmpresa.setPaddingRight(0);
        vBoxEmpresa.setPaddingBottom(0);
        vBoxEmpresa.setMarginTop(0);
        vBoxEmpresa.setMarginLeft(0);
        vBoxEmpresa.setMarginRight(0);
        vBoxEmpresa.setMarginBottom(0);
        vBoxEmpresa.setSpacing(1);
        vBoxEmpresa.setFlexVflex("ftMin");
        vBoxEmpresa.setFlexHflex("ftTrue");
        vBoxEmpresa.setScrollable(false);
        vBoxEmpresa.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresa.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresa.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresa.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresa.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresa.setBoxShadowConfigOpacity(75);
        hBoxComboBox.addChildren(vBoxEmpresa);
        vBoxEmpresa.applyProperties();
    }

    public TFVBox vBoxEmpresaSep1 = new TFVBox();

    private void init_vBoxEmpresaSep1() {
        vBoxEmpresaSep1.setName("vBoxEmpresaSep1");
        vBoxEmpresaSep1.setLeft(0);
        vBoxEmpresaSep1.setTop(0);
        vBoxEmpresaSep1.setWidth(100);
        vBoxEmpresaSep1.setHeight(5);
        vBoxEmpresaSep1.setBorderStyle("stNone");
        vBoxEmpresaSep1.setPaddingTop(0);
        vBoxEmpresaSep1.setPaddingLeft(0);
        vBoxEmpresaSep1.setPaddingRight(0);
        vBoxEmpresaSep1.setPaddingBottom(0);
        vBoxEmpresaSep1.setMarginTop(0);
        vBoxEmpresaSep1.setMarginLeft(0);
        vBoxEmpresaSep1.setMarginRight(0);
        vBoxEmpresaSep1.setMarginBottom(0);
        vBoxEmpresaSep1.setSpacing(1);
        vBoxEmpresaSep1.setFlexVflex("ftFalse");
        vBoxEmpresaSep1.setFlexHflex("ftFalse");
        vBoxEmpresaSep1.setScrollable(false);
        vBoxEmpresaSep1.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresaSep1.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresaSep1.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresaSep1.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresaSep1.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresaSep1.setBoxShadowConfigOpacity(75);
        vBoxEmpresa.addChildren(vBoxEmpresaSep1);
        vBoxEmpresaSep1.applyProperties();
    }

    public TFLabel lblEmpresa = new TFLabel();

    private void init_lblEmpresa() {
        lblEmpresa.setName("lblEmpresa");
        lblEmpresa.setLeft(0);
        lblEmpresa.setTop(6);
        lblEmpresa.setWidth(57);
        lblEmpresa.setHeight(13);
        lblEmpresa.setCaption("1 - Empresa");
        lblEmpresa.setFontColor("clWindowText");
        lblEmpresa.setFontSize(-11);
        lblEmpresa.setFontName("Tahoma");
        lblEmpresa.setFontStyle("[]");
        lblEmpresa.setVerticalAlignment("taVerticalCenter");
        lblEmpresa.setWordBreak(false);
        vBoxEmpresa.addChildren(lblEmpresa);
        lblEmpresa.applyProperties();
    }

    public TFVBox vBoxEmpresaSep2 = new TFVBox();

    private void init_vBoxEmpresaSep2() {
        vBoxEmpresaSep2.setName("vBoxEmpresaSep2");
        vBoxEmpresaSep2.setLeft(0);
        vBoxEmpresaSep2.setTop(20);
        vBoxEmpresaSep2.setWidth(100);
        vBoxEmpresaSep2.setHeight(5);
        vBoxEmpresaSep2.setBorderStyle("stNone");
        vBoxEmpresaSep2.setPaddingTop(0);
        vBoxEmpresaSep2.setPaddingLeft(0);
        vBoxEmpresaSep2.setPaddingRight(0);
        vBoxEmpresaSep2.setPaddingBottom(0);
        vBoxEmpresaSep2.setMarginTop(0);
        vBoxEmpresaSep2.setMarginLeft(0);
        vBoxEmpresaSep2.setMarginRight(0);
        vBoxEmpresaSep2.setMarginBottom(0);
        vBoxEmpresaSep2.setSpacing(1);
        vBoxEmpresaSep2.setFlexVflex("ftFalse");
        vBoxEmpresaSep2.setFlexHflex("ftFalse");
        vBoxEmpresaSep2.setScrollable(false);
        vBoxEmpresaSep2.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresaSep2.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresaSep2.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresaSep2.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresaSep2.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresaSep2.setBoxShadowConfigOpacity(75);
        vBoxEmpresa.addChildren(vBoxEmpresaSep2);
        vBoxEmpresaSep2.applyProperties();
    }

    public TFCombo cboEmpresa = new TFCombo();

    private void init_cboEmpresa() {
        cboEmpresa.setName("cboEmpresa");
        cboEmpresa.setLeft(0);
        cboEmpresa.setTop(26);
        cboEmpresa.setWidth(140);
        cboEmpresa.setHeight(21);
        cboEmpresa.setHint("Empresa");
        cboEmpresa.setLookupTable(tbEmpresasListagem);
        cboEmpresa.setLookupKey("COD_EMPRESA");
        cboEmpresa.setLookupDesc("EMPRESA_UPPER");
        cboEmpresa.setFlex(true);
        cboEmpresa.setHelpCaption("Empresa");
        cboEmpresa.setReadOnly(true);
        cboEmpresa.setRequired(false);
        cboEmpresa.setPrompt("Empresa");
        cboEmpresa.setConstraintCheckWhen("cwImmediate");
        cboEmpresa.setConstraintCheckType("ctExpression");
        cboEmpresa.setConstraintFocusOnError(false);
        cboEmpresa.setConstraintEnableUI(true);
        cboEmpresa.setConstraintEnabled(false);
        cboEmpresa.setConstraintFormCheck(true);
        cboEmpresa.setClearOnDelKey(false);
        cboEmpresa.setUseClearButton(true);
        cboEmpresa.setHideClearButtonOnNullValue(true);
        cboEmpresa.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaChange(event);
            processarFlow("FrmVendedorResponsavel", "cboEmpresa", "OnChange");
        });
        cboEmpresa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboEmpresa", "OnEnter");
        });
        cboEmpresa.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaChange(event);
            processarFlow("FrmVendedorResponsavel", "cboEmpresa", "OnClearClick");
        });
        vBoxEmpresa.addChildren(cboEmpresa);
        cboEmpresa.applyProperties();
        addValidatable(cboEmpresa);
    }

    public TFVBox vBoxEmpresaSep3 = new TFVBox();

    private void init_vBoxEmpresaSep3() {
        vBoxEmpresaSep3.setName("vBoxEmpresaSep3");
        vBoxEmpresaSep3.setLeft(0);
        vBoxEmpresaSep3.setTop(48);
        vBoxEmpresaSep3.setWidth(100);
        vBoxEmpresaSep3.setHeight(5);
        vBoxEmpresaSep3.setBorderStyle("stNone");
        vBoxEmpresaSep3.setPaddingTop(0);
        vBoxEmpresaSep3.setPaddingLeft(0);
        vBoxEmpresaSep3.setPaddingRight(0);
        vBoxEmpresaSep3.setPaddingBottom(0);
        vBoxEmpresaSep3.setMarginTop(0);
        vBoxEmpresaSep3.setMarginLeft(0);
        vBoxEmpresaSep3.setMarginRight(0);
        vBoxEmpresaSep3.setMarginBottom(0);
        vBoxEmpresaSep3.setSpacing(1);
        vBoxEmpresaSep3.setFlexVflex("ftFalse");
        vBoxEmpresaSep3.setFlexHflex("ftFalse");
        vBoxEmpresaSep3.setScrollable(false);
        vBoxEmpresaSep3.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresaSep3.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresaSep3.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresaSep3.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresaSep3.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresaSep3.setBoxShadowConfigOpacity(75);
        vBoxEmpresa.addChildren(vBoxEmpresaSep3);
        vBoxEmpresaSep3.applyProperties();
    }

    public TFHBox hBoxSeparadorComboBox2 = new TFHBox();

    private void init_hBoxSeparadorComboBox2() {
        hBoxSeparadorComboBox2.setName("hBoxSeparadorComboBox2");
        hBoxSeparadorComboBox2.setLeft(155);
        hBoxSeparadorComboBox2.setTop(0);
        hBoxSeparadorComboBox2.setWidth(5);
        hBoxSeparadorComboBox2.setHeight(56);
        hBoxSeparadorComboBox2.setBorderStyle("stNone");
        hBoxSeparadorComboBox2.setPaddingTop(0);
        hBoxSeparadorComboBox2.setPaddingLeft(0);
        hBoxSeparadorComboBox2.setPaddingRight(0);
        hBoxSeparadorComboBox2.setPaddingBottom(0);
        hBoxSeparadorComboBox2.setMarginTop(0);
        hBoxSeparadorComboBox2.setMarginLeft(0);
        hBoxSeparadorComboBox2.setMarginRight(0);
        hBoxSeparadorComboBox2.setMarginBottom(0);
        hBoxSeparadorComboBox2.setSpacing(1);
        hBoxSeparadorComboBox2.setFlexVflex("ftFalse");
        hBoxSeparadorComboBox2.setFlexHflex("ftFalse");
        hBoxSeparadorComboBox2.setScrollable(false);
        hBoxSeparadorComboBox2.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorComboBox2.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorComboBox2.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorComboBox2.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorComboBox2.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorComboBox2.setBoxShadowConfigOpacity(75);
        hBoxSeparadorComboBox2.setVAlign("tvTop");
        hBoxComboBox.addChildren(hBoxSeparadorComboBox2);
        hBoxSeparadorComboBox2.applyProperties();
    }

    public TFVBox vBoxSistema = new TFVBox();

    private void init_vBoxSistema() {
        vBoxSistema.setName("vBoxSistema");
        vBoxSistema.setLeft(160);
        vBoxSistema.setTop(0);
        vBoxSistema.setWidth(150);
        vBoxSistema.setHeight(70);
        vBoxSistema.setBorderStyle("stNone");
        vBoxSistema.setPaddingTop(0);
        vBoxSistema.setPaddingLeft(0);
        vBoxSistema.setPaddingRight(0);
        vBoxSistema.setPaddingBottom(0);
        vBoxSistema.setMarginTop(0);
        vBoxSistema.setMarginLeft(0);
        vBoxSistema.setMarginRight(0);
        vBoxSistema.setMarginBottom(0);
        vBoxSistema.setSpacing(1);
        vBoxSistema.setFlexVflex("ftMin");
        vBoxSistema.setFlexHflex("ftTrue");
        vBoxSistema.setScrollable(false);
        vBoxSistema.setBoxShadowConfigHorizontalLength(10);
        vBoxSistema.setBoxShadowConfigVerticalLength(10);
        vBoxSistema.setBoxShadowConfigBlurRadius(5);
        vBoxSistema.setBoxShadowConfigSpreadRadius(0);
        vBoxSistema.setBoxShadowConfigShadowColor("clBlack");
        vBoxSistema.setBoxShadowConfigOpacity(75);
        hBoxComboBox.addChildren(vBoxSistema);
        vBoxSistema.applyProperties();
    }

    public TFVBox vBoxSistemaSep1 = new TFVBox();

    private void init_vBoxSistemaSep1() {
        vBoxSistemaSep1.setName("vBoxSistemaSep1");
        vBoxSistemaSep1.setLeft(0);
        vBoxSistemaSep1.setTop(0);
        vBoxSistemaSep1.setWidth(100);
        vBoxSistemaSep1.setHeight(5);
        vBoxSistemaSep1.setBorderStyle("stNone");
        vBoxSistemaSep1.setPaddingTop(0);
        vBoxSistemaSep1.setPaddingLeft(0);
        vBoxSistemaSep1.setPaddingRight(0);
        vBoxSistemaSep1.setPaddingBottom(0);
        vBoxSistemaSep1.setMarginTop(0);
        vBoxSistemaSep1.setMarginLeft(0);
        vBoxSistemaSep1.setMarginRight(0);
        vBoxSistemaSep1.setMarginBottom(0);
        vBoxSistemaSep1.setSpacing(1);
        vBoxSistemaSep1.setFlexVflex("ftFalse");
        vBoxSistemaSep1.setFlexHflex("ftFalse");
        vBoxSistemaSep1.setScrollable(false);
        vBoxSistemaSep1.setBoxShadowConfigHorizontalLength(10);
        vBoxSistemaSep1.setBoxShadowConfigVerticalLength(10);
        vBoxSistemaSep1.setBoxShadowConfigBlurRadius(5);
        vBoxSistemaSep1.setBoxShadowConfigSpreadRadius(0);
        vBoxSistemaSep1.setBoxShadowConfigShadowColor("clBlack");
        vBoxSistemaSep1.setBoxShadowConfigOpacity(75);
        vBoxSistema.addChildren(vBoxSistemaSep1);
        vBoxSistemaSep1.applyProperties();
    }

    public TFLabel lblSistema = new TFLabel();

    private void init_lblSistema() {
        lblSistema.setName("lblSistema");
        lblSistema.setLeft(0);
        lblSistema.setTop(6);
        lblSistema.setWidth(53);
        lblSistema.setHeight(13);
        lblSistema.setCaption("2 - Sistema");
        lblSistema.setFontColor("clWindowText");
        lblSistema.setFontSize(-11);
        lblSistema.setFontName("Tahoma");
        lblSistema.setFontStyle("[]");
        lblSistema.setVerticalAlignment("taVerticalCenter");
        lblSistema.setWordBreak(false);
        vBoxSistema.addChildren(lblSistema);
        lblSistema.applyProperties();
    }

    public TFVBox vBoxSistemaSep2 = new TFVBox();

    private void init_vBoxSistemaSep2() {
        vBoxSistemaSep2.setName("vBoxSistemaSep2");
        vBoxSistemaSep2.setLeft(0);
        vBoxSistemaSep2.setTop(20);
        vBoxSistemaSep2.setWidth(100);
        vBoxSistemaSep2.setHeight(5);
        vBoxSistemaSep2.setBorderStyle("stNone");
        vBoxSistemaSep2.setPaddingTop(0);
        vBoxSistemaSep2.setPaddingLeft(0);
        vBoxSistemaSep2.setPaddingRight(0);
        vBoxSistemaSep2.setPaddingBottom(0);
        vBoxSistemaSep2.setMarginTop(0);
        vBoxSistemaSep2.setMarginLeft(0);
        vBoxSistemaSep2.setMarginRight(0);
        vBoxSistemaSep2.setMarginBottom(0);
        vBoxSistemaSep2.setSpacing(1);
        vBoxSistemaSep2.setFlexVflex("ftFalse");
        vBoxSistemaSep2.setFlexHflex("ftFalse");
        vBoxSistemaSep2.setScrollable(false);
        vBoxSistemaSep2.setBoxShadowConfigHorizontalLength(10);
        vBoxSistemaSep2.setBoxShadowConfigVerticalLength(10);
        vBoxSistemaSep2.setBoxShadowConfigBlurRadius(5);
        vBoxSistemaSep2.setBoxShadowConfigSpreadRadius(0);
        vBoxSistemaSep2.setBoxShadowConfigShadowColor("clBlack");
        vBoxSistemaSep2.setBoxShadowConfigOpacity(75);
        vBoxSistema.addChildren(vBoxSistemaSep2);
        vBoxSistemaSep2.applyProperties();
    }

    public TFCombo cboSistema = new TFCombo();

    private void init_cboSistema() {
        cboSistema.setName("cboSistema");
        cboSistema.setLeft(0);
        cboSistema.setTop(26);
        cboSistema.setWidth(140);
        cboSistema.setHeight(21);
        cboSistema.setHint("Sistema");
        cboSistema.setFlex(true);
        cboSistema.setListOptions("Gold [1]=1;Parts [2]=2;Service [3]=3");
        cboSistema.setHelpCaption("Sistema");
        cboSistema.setReadOnly(true);
        cboSistema.setRequired(false);
        cboSistema.setPrompt("Sistema");
        cboSistema.setConstraintCheckWhen("cwImmediate");
        cboSistema.setConstraintCheckType("ctExpression");
        cboSistema.setConstraintFocusOnError(false);
        cboSistema.setConstraintEnableUI(true);
        cboSistema.setConstraintEnabled(false);
        cboSistema.setConstraintFormCheck(true);
        cboSistema.setClearOnDelKey(false);
        cboSistema.setUseClearButton(true);
        cboSistema.setHideClearButtonOnNullValue(true);
        cboSistema.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboSistemaChange(event);
            processarFlow("FrmVendedorResponsavel", "cboSistema", "OnChange");
        });
        cboSistema.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboSistema", "OnEnter");
        });
        cboSistema.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboSistemaChange(event);
            processarFlow("FrmVendedorResponsavel", "cboSistema", "OnClearClick");
        });
        vBoxSistema.addChildren(cboSistema);
        cboSistema.applyProperties();
        addValidatable(cboSistema);
    }

    public TFVBox vBoxSistemaSep3 = new TFVBox();

    private void init_vBoxSistemaSep3() {
        vBoxSistemaSep3.setName("vBoxSistemaSep3");
        vBoxSistemaSep3.setLeft(0);
        vBoxSistemaSep3.setTop(48);
        vBoxSistemaSep3.setWidth(100);
        vBoxSistemaSep3.setHeight(5);
        vBoxSistemaSep3.setBorderStyle("stNone");
        vBoxSistemaSep3.setPaddingTop(0);
        vBoxSistemaSep3.setPaddingLeft(0);
        vBoxSistemaSep3.setPaddingRight(0);
        vBoxSistemaSep3.setPaddingBottom(0);
        vBoxSistemaSep3.setMarginTop(0);
        vBoxSistemaSep3.setMarginLeft(0);
        vBoxSistemaSep3.setMarginRight(0);
        vBoxSistemaSep3.setMarginBottom(0);
        vBoxSistemaSep3.setSpacing(1);
        vBoxSistemaSep3.setFlexVflex("ftFalse");
        vBoxSistemaSep3.setFlexHflex("ftFalse");
        vBoxSistemaSep3.setScrollable(false);
        vBoxSistemaSep3.setBoxShadowConfigHorizontalLength(10);
        vBoxSistemaSep3.setBoxShadowConfigVerticalLength(10);
        vBoxSistemaSep3.setBoxShadowConfigBlurRadius(5);
        vBoxSistemaSep3.setBoxShadowConfigSpreadRadius(0);
        vBoxSistemaSep3.setBoxShadowConfigShadowColor("clBlack");
        vBoxSistemaSep3.setBoxShadowConfigOpacity(75);
        vBoxSistema.addChildren(vBoxSistemaSep3);
        vBoxSistemaSep3.applyProperties();
    }

    public TFHBox hBoxSeparadorComboBox3 = new TFHBox();

    private void init_hBoxSeparadorComboBox3() {
        hBoxSeparadorComboBox3.setName("hBoxSeparadorComboBox3");
        hBoxSeparadorComboBox3.setLeft(310);
        hBoxSeparadorComboBox3.setTop(0);
        hBoxSeparadorComboBox3.setWidth(5);
        hBoxSeparadorComboBox3.setHeight(56);
        hBoxSeparadorComboBox3.setBorderStyle("stNone");
        hBoxSeparadorComboBox3.setPaddingTop(0);
        hBoxSeparadorComboBox3.setPaddingLeft(0);
        hBoxSeparadorComboBox3.setPaddingRight(0);
        hBoxSeparadorComboBox3.setPaddingBottom(0);
        hBoxSeparadorComboBox3.setMarginTop(0);
        hBoxSeparadorComboBox3.setMarginLeft(0);
        hBoxSeparadorComboBox3.setMarginRight(0);
        hBoxSeparadorComboBox3.setMarginBottom(0);
        hBoxSeparadorComboBox3.setSpacing(1);
        hBoxSeparadorComboBox3.setFlexVflex("ftFalse");
        hBoxSeparadorComboBox3.setFlexHflex("ftFalse");
        hBoxSeparadorComboBox3.setScrollable(false);
        hBoxSeparadorComboBox3.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorComboBox3.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorComboBox3.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorComboBox3.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorComboBox3.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorComboBox3.setBoxShadowConfigOpacity(75);
        hBoxSeparadorComboBox3.setVAlign("tvTop");
        hBoxComboBox.addChildren(hBoxSeparadorComboBox3);
        hBoxSeparadorComboBox3.applyProperties();
    }

    public TFVBox vBoxProcesso = new TFVBox();

    private void init_vBoxProcesso() {
        vBoxProcesso.setName("vBoxProcesso");
        vBoxProcesso.setLeft(315);
        vBoxProcesso.setTop(0);
        vBoxProcesso.setWidth(150);
        vBoxProcesso.setHeight(70);
        vBoxProcesso.setBorderStyle("stNone");
        vBoxProcesso.setPaddingTop(0);
        vBoxProcesso.setPaddingLeft(0);
        vBoxProcesso.setPaddingRight(0);
        vBoxProcesso.setPaddingBottom(0);
        vBoxProcesso.setMarginTop(0);
        vBoxProcesso.setMarginLeft(0);
        vBoxProcesso.setMarginRight(0);
        vBoxProcesso.setMarginBottom(0);
        vBoxProcesso.setSpacing(1);
        vBoxProcesso.setFlexVflex("ftMin");
        vBoxProcesso.setFlexHflex("ftTrue");
        vBoxProcesso.setScrollable(false);
        vBoxProcesso.setBoxShadowConfigHorizontalLength(10);
        vBoxProcesso.setBoxShadowConfigVerticalLength(10);
        vBoxProcesso.setBoxShadowConfigBlurRadius(5);
        vBoxProcesso.setBoxShadowConfigSpreadRadius(0);
        vBoxProcesso.setBoxShadowConfigShadowColor("clBlack");
        vBoxProcesso.setBoxShadowConfigOpacity(75);
        hBoxComboBox.addChildren(vBoxProcesso);
        vBoxProcesso.applyProperties();
    }

    public TFVBox vBoxProcessoSep1 = new TFVBox();

    private void init_vBoxProcessoSep1() {
        vBoxProcessoSep1.setName("vBoxProcessoSep1");
        vBoxProcessoSep1.setLeft(0);
        vBoxProcessoSep1.setTop(0);
        vBoxProcessoSep1.setWidth(100);
        vBoxProcessoSep1.setHeight(5);
        vBoxProcessoSep1.setBorderStyle("stNone");
        vBoxProcessoSep1.setPaddingTop(0);
        vBoxProcessoSep1.setPaddingLeft(0);
        vBoxProcessoSep1.setPaddingRight(0);
        vBoxProcessoSep1.setPaddingBottom(0);
        vBoxProcessoSep1.setMarginTop(0);
        vBoxProcessoSep1.setMarginLeft(0);
        vBoxProcessoSep1.setMarginRight(0);
        vBoxProcessoSep1.setMarginBottom(0);
        vBoxProcessoSep1.setSpacing(1);
        vBoxProcessoSep1.setFlexVflex("ftFalse");
        vBoxProcessoSep1.setFlexHflex("ftFalse");
        vBoxProcessoSep1.setScrollable(false);
        vBoxProcessoSep1.setBoxShadowConfigHorizontalLength(10);
        vBoxProcessoSep1.setBoxShadowConfigVerticalLength(10);
        vBoxProcessoSep1.setBoxShadowConfigBlurRadius(5);
        vBoxProcessoSep1.setBoxShadowConfigSpreadRadius(0);
        vBoxProcessoSep1.setBoxShadowConfigShadowColor("clBlack");
        vBoxProcessoSep1.setBoxShadowConfigOpacity(75);
        vBoxProcesso.addChildren(vBoxProcessoSep1);
        vBoxProcessoSep1.applyProperties();
    }

    public TFLabel lblProcesso = new TFLabel();

    private void init_lblProcesso() {
        lblProcesso.setName("lblProcesso");
        lblProcesso.setLeft(0);
        lblProcesso.setTop(6);
        lblProcesso.setWidth(59);
        lblProcesso.setHeight(13);
        lblProcesso.setCaption("3 - Processo");
        lblProcesso.setFontColor("clWindowText");
        lblProcesso.setFontSize(-11);
        lblProcesso.setFontName("Tahoma");
        lblProcesso.setFontStyle("[]");
        lblProcesso.setVerticalAlignment("taVerticalCenter");
        lblProcesso.setWordBreak(false);
        vBoxProcesso.addChildren(lblProcesso);
        lblProcesso.applyProperties();
    }

    public TFVBox vBoxProcessoSep2 = new TFVBox();

    private void init_vBoxProcessoSep2() {
        vBoxProcessoSep2.setName("vBoxProcessoSep2");
        vBoxProcessoSep2.setLeft(0);
        vBoxProcessoSep2.setTop(20);
        vBoxProcessoSep2.setWidth(100);
        vBoxProcessoSep2.setHeight(5);
        vBoxProcessoSep2.setBorderStyle("stNone");
        vBoxProcessoSep2.setPaddingTop(0);
        vBoxProcessoSep2.setPaddingLeft(0);
        vBoxProcessoSep2.setPaddingRight(0);
        vBoxProcessoSep2.setPaddingBottom(0);
        vBoxProcessoSep2.setMarginTop(0);
        vBoxProcessoSep2.setMarginLeft(0);
        vBoxProcessoSep2.setMarginRight(0);
        vBoxProcessoSep2.setMarginBottom(0);
        vBoxProcessoSep2.setSpacing(1);
        vBoxProcessoSep2.setFlexVflex("ftFalse");
        vBoxProcessoSep2.setFlexHflex("ftFalse");
        vBoxProcessoSep2.setScrollable(false);
        vBoxProcessoSep2.setBoxShadowConfigHorizontalLength(10);
        vBoxProcessoSep2.setBoxShadowConfigVerticalLength(10);
        vBoxProcessoSep2.setBoxShadowConfigBlurRadius(5);
        vBoxProcessoSep2.setBoxShadowConfigSpreadRadius(0);
        vBoxProcessoSep2.setBoxShadowConfigShadowColor("clBlack");
        vBoxProcessoSep2.setBoxShadowConfigOpacity(75);
        vBoxProcesso.addChildren(vBoxProcessoSep2);
        vBoxProcessoSep2.applyProperties();
    }

    public TFCombo cboProcesso = new TFCombo();

    private void init_cboProcesso() {
        cboProcesso.setName("cboProcesso");
        cboProcesso.setLeft(0);
        cboProcesso.setTop(26);
        cboProcesso.setWidth(140);
        cboProcesso.setHeight(21);
        cboProcesso.setHint("Processo");
        cboProcesso.setFlex(true);
        cboProcesso.setListOptions("Caminh\u00E3o [1]=1;Vans [2]=2;\u00D4nibus [3]=3;Vendedor Interno [4]=4;Vendedor Externo [5]=5;Vendedor Pneu [6]=6;");
        cboProcesso.setHelpCaption("Processo");
        cboProcesso.setReadOnly(true);
        cboProcesso.setRequired(false);
        cboProcesso.setPrompt("Processo");
        cboProcesso.setConstraintCheckWhen("cwImmediate");
        cboProcesso.setConstraintCheckType("ctExpression");
        cboProcesso.setConstraintFocusOnError(false);
        cboProcesso.setConstraintEnableUI(true);
        cboProcesso.setConstraintEnabled(false);
        cboProcesso.setConstraintFormCheck(true);
        cboProcesso.setClearOnDelKey(false);
        cboProcesso.setUseClearButton(true);
        cboProcesso.setHideClearButtonOnNullValue(true);
        cboProcesso.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaChange(event);
            processarFlow("FrmVendedorResponsavel", "cboProcesso", "OnChange");
        });
        cboProcesso.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboProcesso", "OnEnter");
        });
        cboProcesso.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaChange(event);
            processarFlow("FrmVendedorResponsavel", "cboProcesso", "OnClearClick");
        });
        vBoxProcesso.addChildren(cboProcesso);
        cboProcesso.applyProperties();
        addValidatable(cboProcesso);
    }

    public TFVBox vBoxProcessoSep3 = new TFVBox();

    private void init_vBoxProcessoSep3() {
        vBoxProcessoSep3.setName("vBoxProcessoSep3");
        vBoxProcessoSep3.setLeft(0);
        vBoxProcessoSep3.setTop(48);
        vBoxProcessoSep3.setWidth(100);
        vBoxProcessoSep3.setHeight(5);
        vBoxProcessoSep3.setBorderStyle("stNone");
        vBoxProcessoSep3.setPaddingTop(0);
        vBoxProcessoSep3.setPaddingLeft(0);
        vBoxProcessoSep3.setPaddingRight(0);
        vBoxProcessoSep3.setPaddingBottom(0);
        vBoxProcessoSep3.setMarginTop(0);
        vBoxProcessoSep3.setMarginLeft(0);
        vBoxProcessoSep3.setMarginRight(0);
        vBoxProcessoSep3.setMarginBottom(0);
        vBoxProcessoSep3.setSpacing(1);
        vBoxProcessoSep3.setFlexVflex("ftFalse");
        vBoxProcessoSep3.setFlexHflex("ftFalse");
        vBoxProcessoSep3.setScrollable(false);
        vBoxProcessoSep3.setBoxShadowConfigHorizontalLength(10);
        vBoxProcessoSep3.setBoxShadowConfigVerticalLength(10);
        vBoxProcessoSep3.setBoxShadowConfigBlurRadius(5);
        vBoxProcessoSep3.setBoxShadowConfigSpreadRadius(0);
        vBoxProcessoSep3.setBoxShadowConfigShadowColor("clBlack");
        vBoxProcessoSep3.setBoxShadowConfigOpacity(75);
        vBoxProcesso.addChildren(vBoxProcessoSep3);
        vBoxProcessoSep3.applyProperties();
    }

    public TFHBox hBoxSeparadorComboBox4 = new TFHBox();

    private void init_hBoxSeparadorComboBox4() {
        hBoxSeparadorComboBox4.setName("hBoxSeparadorComboBox4");
        hBoxSeparadorComboBox4.setLeft(465);
        hBoxSeparadorComboBox4.setTop(0);
        hBoxSeparadorComboBox4.setWidth(5);
        hBoxSeparadorComboBox4.setHeight(56);
        hBoxSeparadorComboBox4.setBorderStyle("stNone");
        hBoxSeparadorComboBox4.setPaddingTop(0);
        hBoxSeparadorComboBox4.setPaddingLeft(0);
        hBoxSeparadorComboBox4.setPaddingRight(0);
        hBoxSeparadorComboBox4.setPaddingBottom(0);
        hBoxSeparadorComboBox4.setMarginTop(0);
        hBoxSeparadorComboBox4.setMarginLeft(0);
        hBoxSeparadorComboBox4.setMarginRight(0);
        hBoxSeparadorComboBox4.setMarginBottom(0);
        hBoxSeparadorComboBox4.setSpacing(1);
        hBoxSeparadorComboBox4.setFlexVflex("ftFalse");
        hBoxSeparadorComboBox4.setFlexHflex("ftFalse");
        hBoxSeparadorComboBox4.setScrollable(false);
        hBoxSeparadorComboBox4.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorComboBox4.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorComboBox4.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorComboBox4.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorComboBox4.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorComboBox4.setBoxShadowConfigOpacity(75);
        hBoxSeparadorComboBox4.setVAlign("tvTop");
        hBoxComboBox.addChildren(hBoxSeparadorComboBox4);
        hBoxSeparadorComboBox4.applyProperties();
    }

    public TFVBox vBoxTemperatura = new TFVBox();

    private void init_vBoxTemperatura() {
        vBoxTemperatura.setName("vBoxTemperatura");
        vBoxTemperatura.setLeft(470);
        vBoxTemperatura.setTop(0);
        vBoxTemperatura.setWidth(150);
        vBoxTemperatura.setHeight(70);
        vBoxTemperatura.setBorderStyle("stNone");
        vBoxTemperatura.setPaddingTop(0);
        vBoxTemperatura.setPaddingLeft(0);
        vBoxTemperatura.setPaddingRight(0);
        vBoxTemperatura.setPaddingBottom(0);
        vBoxTemperatura.setMarginTop(0);
        vBoxTemperatura.setMarginLeft(0);
        vBoxTemperatura.setMarginRight(0);
        vBoxTemperatura.setMarginBottom(0);
        vBoxTemperatura.setSpacing(1);
        vBoxTemperatura.setFlexVflex("ftMin");
        vBoxTemperatura.setFlexHflex("ftTrue");
        vBoxTemperatura.setScrollable(false);
        vBoxTemperatura.setBoxShadowConfigHorizontalLength(10);
        vBoxTemperatura.setBoxShadowConfigVerticalLength(10);
        vBoxTemperatura.setBoxShadowConfigBlurRadius(5);
        vBoxTemperatura.setBoxShadowConfigSpreadRadius(0);
        vBoxTemperatura.setBoxShadowConfigShadowColor("clBlack");
        vBoxTemperatura.setBoxShadowConfigOpacity(75);
        hBoxComboBox.addChildren(vBoxTemperatura);
        vBoxTemperatura.applyProperties();
    }

    public TFVBox vBoxTempSep1 = new TFVBox();

    private void init_vBoxTempSep1() {
        vBoxTempSep1.setName("vBoxTempSep1");
        vBoxTempSep1.setLeft(0);
        vBoxTempSep1.setTop(0);
        vBoxTempSep1.setWidth(100);
        vBoxTempSep1.setHeight(5);
        vBoxTempSep1.setBorderStyle("stNone");
        vBoxTempSep1.setPaddingTop(0);
        vBoxTempSep1.setPaddingLeft(0);
        vBoxTempSep1.setPaddingRight(0);
        vBoxTempSep1.setPaddingBottom(0);
        vBoxTempSep1.setMarginTop(0);
        vBoxTempSep1.setMarginLeft(0);
        vBoxTempSep1.setMarginRight(0);
        vBoxTempSep1.setMarginBottom(0);
        vBoxTempSep1.setSpacing(1);
        vBoxTempSep1.setFlexVflex("ftFalse");
        vBoxTempSep1.setFlexHflex("ftFalse");
        vBoxTempSep1.setScrollable(false);
        vBoxTempSep1.setBoxShadowConfigHorizontalLength(10);
        vBoxTempSep1.setBoxShadowConfigVerticalLength(10);
        vBoxTempSep1.setBoxShadowConfigBlurRadius(5);
        vBoxTempSep1.setBoxShadowConfigSpreadRadius(0);
        vBoxTempSep1.setBoxShadowConfigShadowColor("clBlack");
        vBoxTempSep1.setBoxShadowConfigOpacity(75);
        vBoxTemperatura.addChildren(vBoxTempSep1);
        vBoxTempSep1.applyProperties();
    }

    public TFLabel lblTemperatura = new TFLabel();

    private void init_lblTemperatura() {
        lblTemperatura.setName("lblTemperatura");
        lblTemperatura.setLeft(0);
        lblTemperatura.setTop(6);
        lblTemperatura.setWidth(78);
        lblTemperatura.setHeight(13);
        lblTemperatura.setCaption("4 - Temperatura");
        lblTemperatura.setFontColor("clWindowText");
        lblTemperatura.setFontSize(-11);
        lblTemperatura.setFontName("Tahoma");
        lblTemperatura.setFontStyle("[]");
        lblTemperatura.setVerticalAlignment("taVerticalCenter");
        lblTemperatura.setWordBreak(false);
        vBoxTemperatura.addChildren(lblTemperatura);
        lblTemperatura.applyProperties();
    }

    public TFVBox vBoxTempSep2 = new TFVBox();

    private void init_vBoxTempSep2() {
        vBoxTempSep2.setName("vBoxTempSep2");
        vBoxTempSep2.setLeft(0);
        vBoxTempSep2.setTop(20);
        vBoxTempSep2.setWidth(100);
        vBoxTempSep2.setHeight(5);
        vBoxTempSep2.setBorderStyle("stNone");
        vBoxTempSep2.setPaddingTop(0);
        vBoxTempSep2.setPaddingLeft(0);
        vBoxTempSep2.setPaddingRight(0);
        vBoxTempSep2.setPaddingBottom(0);
        vBoxTempSep2.setMarginTop(0);
        vBoxTempSep2.setMarginLeft(0);
        vBoxTempSep2.setMarginRight(0);
        vBoxTempSep2.setMarginBottom(0);
        vBoxTempSep2.setSpacing(1);
        vBoxTempSep2.setFlexVflex("ftFalse");
        vBoxTempSep2.setFlexHflex("ftFalse");
        vBoxTempSep2.setScrollable(false);
        vBoxTempSep2.setBoxShadowConfigHorizontalLength(10);
        vBoxTempSep2.setBoxShadowConfigVerticalLength(10);
        vBoxTempSep2.setBoxShadowConfigBlurRadius(5);
        vBoxTempSep2.setBoxShadowConfigSpreadRadius(0);
        vBoxTempSep2.setBoxShadowConfigShadowColor("clBlack");
        vBoxTempSep2.setBoxShadowConfigOpacity(75);
        vBoxTemperatura.addChildren(vBoxTempSep2);
        vBoxTempSep2.applyProperties();
    }

    public TFCombo cboTemperatura = new TFCombo();

    private void init_cboTemperatura() {
        cboTemperatura.setName("cboTemperatura");
        cboTemperatura.setLeft(0);
        cboTemperatura.setTop(26);
        cboTemperatura.setWidth(140);
        cboTemperatura.setHeight(21);
        cboTemperatura.setHint("Temperatura");
        cboTemperatura.setFlex(true);
        cboTemperatura.setListOptions("Super Quente [1]=1;Quente [2]=2;Morno [3]=3;Frio [4]=4;Bols\u00E3o [5]=5");
        cboTemperatura.setHelpCaption("Temperatura");
        cboTemperatura.setReadOnly(true);
        cboTemperatura.setRequired(false);
        cboTemperatura.setPrompt("Temperatura");
        cboTemperatura.setConstraintCheckWhen("cwImmediate");
        cboTemperatura.setConstraintCheckType("ctExpression");
        cboTemperatura.setConstraintFocusOnError(false);
        cboTemperatura.setConstraintEnableUI(true);
        cboTemperatura.setConstraintEnabled(false);
        cboTemperatura.setConstraintFormCheck(true);
        cboTemperatura.setClearOnDelKey(false);
        cboTemperatura.setUseClearButton(true);
        cboTemperatura.setHideClearButtonOnNullValue(true);
        cboTemperatura.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboTemperatura", "OnEnter");
        });
        vBoxTemperatura.addChildren(cboTemperatura);
        cboTemperatura.applyProperties();
        addValidatable(cboTemperatura);
    }

    public TFVBox vBoxTempSep3 = new TFVBox();

    private void init_vBoxTempSep3() {
        vBoxTempSep3.setName("vBoxTempSep3");
        vBoxTempSep3.setLeft(0);
        vBoxTempSep3.setTop(48);
        vBoxTempSep3.setWidth(100);
        vBoxTempSep3.setHeight(5);
        vBoxTempSep3.setBorderStyle("stNone");
        vBoxTempSep3.setPaddingTop(0);
        vBoxTempSep3.setPaddingLeft(0);
        vBoxTempSep3.setPaddingRight(0);
        vBoxTempSep3.setPaddingBottom(0);
        vBoxTempSep3.setMarginTop(0);
        vBoxTempSep3.setMarginLeft(0);
        vBoxTempSep3.setMarginRight(0);
        vBoxTempSep3.setMarginBottom(0);
        vBoxTempSep3.setSpacing(1);
        vBoxTempSep3.setFlexVflex("ftFalse");
        vBoxTempSep3.setFlexHflex("ftFalse");
        vBoxTempSep3.setScrollable(false);
        vBoxTempSep3.setBoxShadowConfigHorizontalLength(10);
        vBoxTempSep3.setBoxShadowConfigVerticalLength(10);
        vBoxTempSep3.setBoxShadowConfigBlurRadius(5);
        vBoxTempSep3.setBoxShadowConfigSpreadRadius(0);
        vBoxTempSep3.setBoxShadowConfigShadowColor("clBlack");
        vBoxTempSep3.setBoxShadowConfigOpacity(75);
        vBoxTemperatura.addChildren(vBoxTempSep3);
        vBoxTempSep3.applyProperties();
    }

    public TFHBox hBoxSeparadorComboBox5 = new TFHBox();

    private void init_hBoxSeparadorComboBox5() {
        hBoxSeparadorComboBox5.setName("hBoxSeparadorComboBox5");
        hBoxSeparadorComboBox5.setLeft(620);
        hBoxSeparadorComboBox5.setTop(0);
        hBoxSeparadorComboBox5.setWidth(5);
        hBoxSeparadorComboBox5.setHeight(56);
        hBoxSeparadorComboBox5.setBorderStyle("stNone");
        hBoxSeparadorComboBox5.setPaddingTop(0);
        hBoxSeparadorComboBox5.setPaddingLeft(0);
        hBoxSeparadorComboBox5.setPaddingRight(0);
        hBoxSeparadorComboBox5.setPaddingBottom(0);
        hBoxSeparadorComboBox5.setMarginTop(0);
        hBoxSeparadorComboBox5.setMarginLeft(0);
        hBoxSeparadorComboBox5.setMarginRight(0);
        hBoxSeparadorComboBox5.setMarginBottom(0);
        hBoxSeparadorComboBox5.setSpacing(1);
        hBoxSeparadorComboBox5.setFlexVflex("ftFalse");
        hBoxSeparadorComboBox5.setFlexHflex("ftFalse");
        hBoxSeparadorComboBox5.setScrollable(false);
        hBoxSeparadorComboBox5.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorComboBox5.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorComboBox5.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorComboBox5.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorComboBox5.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorComboBox5.setBoxShadowConfigOpacity(75);
        hBoxSeparadorComboBox5.setVAlign("tvTop");
        hBoxComboBox.addChildren(hBoxSeparadorComboBox5);
        hBoxSeparadorComboBox5.applyProperties();
    }

    public TFVBox vBoxVendedor = new TFVBox();

    private void init_vBoxVendedor() {
        vBoxVendedor.setName("vBoxVendedor");
        vBoxVendedor.setLeft(625);
        vBoxVendedor.setTop(0);
        vBoxVendedor.setWidth(150);
        vBoxVendedor.setHeight(70);
        vBoxVendedor.setBorderStyle("stNone");
        vBoxVendedor.setPaddingTop(0);
        vBoxVendedor.setPaddingLeft(0);
        vBoxVendedor.setPaddingRight(0);
        vBoxVendedor.setPaddingBottom(0);
        vBoxVendedor.setMarginTop(0);
        vBoxVendedor.setMarginLeft(0);
        vBoxVendedor.setMarginRight(0);
        vBoxVendedor.setMarginBottom(0);
        vBoxVendedor.setSpacing(1);
        vBoxVendedor.setFlexVflex("ftMin");
        vBoxVendedor.setFlexHflex("ftTrue");
        vBoxVendedor.setScrollable(false);
        vBoxVendedor.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedor.setBoxShadowConfigVerticalLength(10);
        vBoxVendedor.setBoxShadowConfigBlurRadius(5);
        vBoxVendedor.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedor.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedor.setBoxShadowConfigOpacity(75);
        hBoxComboBox.addChildren(vBoxVendedor);
        vBoxVendedor.applyProperties();
    }

    public TFVBox vBoxVendedorSep1 = new TFVBox();

    private void init_vBoxVendedorSep1() {
        vBoxVendedorSep1.setName("vBoxVendedorSep1");
        vBoxVendedorSep1.setLeft(0);
        vBoxVendedorSep1.setTop(0);
        vBoxVendedorSep1.setWidth(100);
        vBoxVendedorSep1.setHeight(5);
        vBoxVendedorSep1.setBorderStyle("stNone");
        vBoxVendedorSep1.setPaddingTop(0);
        vBoxVendedorSep1.setPaddingLeft(0);
        vBoxVendedorSep1.setPaddingRight(0);
        vBoxVendedorSep1.setPaddingBottom(0);
        vBoxVendedorSep1.setMarginTop(0);
        vBoxVendedorSep1.setMarginLeft(0);
        vBoxVendedorSep1.setMarginRight(0);
        vBoxVendedorSep1.setMarginBottom(0);
        vBoxVendedorSep1.setSpacing(1);
        vBoxVendedorSep1.setFlexVflex("ftFalse");
        vBoxVendedorSep1.setFlexHflex("ftFalse");
        vBoxVendedorSep1.setScrollable(false);
        vBoxVendedorSep1.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedorSep1.setBoxShadowConfigVerticalLength(10);
        vBoxVendedorSep1.setBoxShadowConfigBlurRadius(5);
        vBoxVendedorSep1.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedorSep1.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedorSep1.setBoxShadowConfigOpacity(75);
        vBoxVendedor.addChildren(vBoxVendedorSep1);
        vBoxVendedorSep1.applyProperties();
    }

    public TFLabel lblVendedor = new TFLabel();

    private void init_lblVendedor() {
        lblVendedor.setName("lblVendedor");
        lblVendedor.setLeft(0);
        lblVendedor.setTop(6);
        lblVendedor.setWidth(62);
        lblVendedor.setHeight(13);
        lblVendedor.setCaption("5 - Vendedor");
        lblVendedor.setFontColor("clWindowText");
        lblVendedor.setFontSize(-11);
        lblVendedor.setFontName("Tahoma");
        lblVendedor.setFontStyle("[]");
        lblVendedor.setVerticalAlignment("taVerticalCenter");
        lblVendedor.setWordBreak(false);
        vBoxVendedor.addChildren(lblVendedor);
        lblVendedor.applyProperties();
    }

    public TFVBox vBoxVendedorSep2 = new TFVBox();

    private void init_vBoxVendedorSep2() {
        vBoxVendedorSep2.setName("vBoxVendedorSep2");
        vBoxVendedorSep2.setLeft(0);
        vBoxVendedorSep2.setTop(20);
        vBoxVendedorSep2.setWidth(100);
        vBoxVendedorSep2.setHeight(5);
        vBoxVendedorSep2.setBorderStyle("stNone");
        vBoxVendedorSep2.setPaddingTop(0);
        vBoxVendedorSep2.setPaddingLeft(0);
        vBoxVendedorSep2.setPaddingRight(0);
        vBoxVendedorSep2.setPaddingBottom(0);
        vBoxVendedorSep2.setMarginTop(0);
        vBoxVendedorSep2.setMarginLeft(0);
        vBoxVendedorSep2.setMarginRight(0);
        vBoxVendedorSep2.setMarginBottom(0);
        vBoxVendedorSep2.setSpacing(1);
        vBoxVendedorSep2.setFlexVflex("ftFalse");
        vBoxVendedorSep2.setFlexHflex("ftFalse");
        vBoxVendedorSep2.setScrollable(false);
        vBoxVendedorSep2.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedorSep2.setBoxShadowConfigVerticalLength(10);
        vBoxVendedorSep2.setBoxShadowConfigBlurRadius(5);
        vBoxVendedorSep2.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedorSep2.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedorSep2.setBoxShadowConfigOpacity(75);
        vBoxVendedor.addChildren(vBoxVendedorSep2);
        vBoxVendedorSep2.applyProperties();
    }

    public TFCombo cboVendedor = new TFCombo();

    private void init_cboVendedor() {
        cboVendedor.setName("cboVendedor");
        cboVendedor.setLeft(0);
        cboVendedor.setTop(26);
        cboVendedor.setWidth(140);
        cboVendedor.setHeight(21);
        cboVendedor.setHint("Vendedor");
        cboVendedor.setLookupTable(tbVendedoresListagem);
        cboVendedor.setLookupKey("NOME");
        cboVendedor.setLookupDesc("NOME_COMPLETO_LOGIN");
        cboVendedor.setFlex(true);
        cboVendedor.setHelpCaption("Vendedor");
        cboVendedor.setReadOnly(true);
        cboVendedor.setRequired(false);
        cboVendedor.setPrompt("Vendedor");
        cboVendedor.setConstraintCheckWhen("cwImmediate");
        cboVendedor.setConstraintCheckType("ctExpression");
        cboVendedor.setConstraintFocusOnError(false);
        cboVendedor.setConstraintEnableUI(true);
        cboVendedor.setConstraintEnabled(false);
        cboVendedor.setConstraintFormCheck(true);
        cboVendedor.setClearOnDelKey(false);
        cboVendedor.setUseClearButton(true);
        cboVendedor.setHideClearButtonOnNullValue(true);
        cboVendedor.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboVendedor", "OnEnter");
        });
        vBoxVendedor.addChildren(cboVendedor);
        cboVendedor.applyProperties();
        addValidatable(cboVendedor);
    }

    public TFVBox vBoxVendedorSep3 = new TFVBox();

    private void init_vBoxVendedorSep3() {
        vBoxVendedorSep3.setName("vBoxVendedorSep3");
        vBoxVendedorSep3.setLeft(0);
        vBoxVendedorSep3.setTop(48);
        vBoxVendedorSep3.setWidth(100);
        vBoxVendedorSep3.setHeight(5);
        vBoxVendedorSep3.setBorderStyle("stNone");
        vBoxVendedorSep3.setPaddingTop(0);
        vBoxVendedorSep3.setPaddingLeft(0);
        vBoxVendedorSep3.setPaddingRight(0);
        vBoxVendedorSep3.setPaddingBottom(0);
        vBoxVendedorSep3.setMarginTop(0);
        vBoxVendedorSep3.setMarginLeft(0);
        vBoxVendedorSep3.setMarginRight(0);
        vBoxVendedorSep3.setMarginBottom(0);
        vBoxVendedorSep3.setSpacing(1);
        vBoxVendedorSep3.setFlexVflex("ftFalse");
        vBoxVendedorSep3.setFlexHflex("ftFalse");
        vBoxVendedorSep3.setScrollable(false);
        vBoxVendedorSep3.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedorSep3.setBoxShadowConfigVerticalLength(10);
        vBoxVendedorSep3.setBoxShadowConfigBlurRadius(5);
        vBoxVendedorSep3.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedorSep3.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedorSep3.setBoxShadowConfigOpacity(75);
        vBoxVendedor.addChildren(vBoxVendedorSep3);
        vBoxVendedorSep3.applyProperties();
    }

    public TFHBox hBoxSeparadorComboBox6 = new TFHBox();

    private void init_hBoxSeparadorComboBox6() {
        hBoxSeparadorComboBox6.setName("hBoxSeparadorComboBox6");
        hBoxSeparadorComboBox6.setLeft(775);
        hBoxSeparadorComboBox6.setTop(0);
        hBoxSeparadorComboBox6.setWidth(5);
        hBoxSeparadorComboBox6.setHeight(20);
        hBoxSeparadorComboBox6.setBorderStyle("stNone");
        hBoxSeparadorComboBox6.setPaddingTop(0);
        hBoxSeparadorComboBox6.setPaddingLeft(0);
        hBoxSeparadorComboBox6.setPaddingRight(0);
        hBoxSeparadorComboBox6.setPaddingBottom(0);
        hBoxSeparadorComboBox6.setMarginTop(0);
        hBoxSeparadorComboBox6.setMarginLeft(0);
        hBoxSeparadorComboBox6.setMarginRight(0);
        hBoxSeparadorComboBox6.setMarginBottom(0);
        hBoxSeparadorComboBox6.setSpacing(1);
        hBoxSeparadorComboBox6.setFlexVflex("ftFalse");
        hBoxSeparadorComboBox6.setFlexHflex("ftFalse");
        hBoxSeparadorComboBox6.setScrollable(false);
        hBoxSeparadorComboBox6.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorComboBox6.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorComboBox6.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorComboBox6.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorComboBox6.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorComboBox6.setBoxShadowConfigOpacity(75);
        hBoxSeparadorComboBox6.setVAlign("tvTop");
        hBoxComboBox.addChildren(hBoxSeparadorComboBox6);
        hBoxSeparadorComboBox6.applyProperties();
    }

    public TFHBox hBoxGrid = new TFHBox();

    private void init_hBoxGrid() {
        hBoxGrid.setName("hBoxGrid");
        hBoxGrid.setLeft(0);
        hBoxGrid.setTop(76);
        hBoxGrid.setWidth(843);
        hBoxGrid.setHeight(260);
        hBoxGrid.setAlign("alTop");
        hBoxGrid.setBorderStyle("stNone");
        hBoxGrid.setPaddingTop(3);
        hBoxGrid.setPaddingLeft(3);
        hBoxGrid.setPaddingRight(3);
        hBoxGrid.setPaddingBottom(0);
        hBoxGrid.setMarginTop(0);
        hBoxGrid.setMarginLeft(0);
        hBoxGrid.setMarginRight(0);
        hBoxGrid.setMarginBottom(0);
        hBoxGrid.setSpacing(1);
        hBoxGrid.setFlexVflex("ftTrue");
        hBoxGrid.setFlexHflex("ftTrue");
        hBoxGrid.setScrollable(false);
        hBoxGrid.setBoxShadowConfigHorizontalLength(10);
        hBoxGrid.setBoxShadowConfigVerticalLength(10);
        hBoxGrid.setBoxShadowConfigBlurRadius(5);
        hBoxGrid.setBoxShadowConfigSpreadRadius(0);
        hBoxGrid.setBoxShadowConfigShadowColor("clBlack");
        hBoxGrid.setBoxShadowConfigOpacity(75);
        hBoxGrid.setVAlign("tvTop");
        vBoxCadastroVendResp.addChildren(hBoxGrid);
        hBoxGrid.applyProperties();
    }

    public TFHBox hBoxGridDiv1 = new TFHBox();

    private void init_hBoxGridDiv1() {
        hBoxGridDiv1.setName("hBoxGridDiv1");
        hBoxGridDiv1.setLeft(0);
        hBoxGridDiv1.setTop(0);
        hBoxGridDiv1.setWidth(5);
        hBoxGridDiv1.setHeight(56);
        hBoxGridDiv1.setBorderStyle("stNone");
        hBoxGridDiv1.setPaddingTop(0);
        hBoxGridDiv1.setPaddingLeft(0);
        hBoxGridDiv1.setPaddingRight(0);
        hBoxGridDiv1.setPaddingBottom(0);
        hBoxGridDiv1.setMarginTop(0);
        hBoxGridDiv1.setMarginLeft(0);
        hBoxGridDiv1.setMarginRight(0);
        hBoxGridDiv1.setMarginBottom(0);
        hBoxGridDiv1.setSpacing(1);
        hBoxGridDiv1.setFlexVflex("ftFalse");
        hBoxGridDiv1.setFlexHflex("ftFalse");
        hBoxGridDiv1.setScrollable(false);
        hBoxGridDiv1.setBoxShadowConfigHorizontalLength(10);
        hBoxGridDiv1.setBoxShadowConfigVerticalLength(10);
        hBoxGridDiv1.setBoxShadowConfigBlurRadius(5);
        hBoxGridDiv1.setBoxShadowConfigSpreadRadius(0);
        hBoxGridDiv1.setBoxShadowConfigShadowColor("clBlack");
        hBoxGridDiv1.setBoxShadowConfigOpacity(75);
        hBoxGridDiv1.setVAlign("tvTop");
        hBoxGrid.addChildren(hBoxGridDiv1);
        hBoxGridDiv1.applyProperties();
    }

    public TFGrid gridVendedores = new TFGrid();

    private void init_gridVendedores() {
        gridVendedores.setName("gridVendedores");
        gridVendedores.setLeft(5);
        gridVendedores.setTop(0);
        gridVendedores.setWidth(829);
        gridVendedores.setHeight(242);
        gridVendedores.setAlign("alClient");
        gridVendedores.setTable(tbListaClienteResponsavel);
        gridVendedores.setFlexVflex("ftTrue");
        gridVendedores.setFlexHflex("ftTrue");
        gridVendedores.setPagingEnabled(false);
        gridVendedores.setFrozenColumns(0);
        gridVendedores.setShowFooter(false);
        gridVendedores.setShowHeader(true);
        gridVendedores.setMultiSelection(false);
        gridVendedores.setGroupingEnabled(false);
        gridVendedores.setGroupingExpanded(false);
        gridVendedores.setGroupingShowFooter(false);
        gridVendedores.setCrosstabEnabled(false);
        gridVendedores.setCrosstabGroupType("cgtConcat");
        gridVendedores.setEditionEnabled(false);
        gridVendedores.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("EMPRESAS_NOME_INITCAP_COD");
        item0.setTitleCaption("Empresa");
        item0.setWidth(200);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("COD_CLIENTE");
        item1.setTitleCaption("C\u00F3d. Cliente");
        item1.setWidth(136);
        item1.setVisible(false);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("SISTEMA");
        item2.setTitleCaption("Sistema");
        item2.setWidth(91);
        item2.setVisible(false);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("PROCESSO");
        item3.setTitleCaption("Processo");
        item3.setWidth(92);
        item3.setVisible(false);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("TEMPERATURA");
        item4.setTitleCaption("Temperatura");
        item4.setWidth(108);
        item4.setVisible(false);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("SISTEMA_STR");
        item5.setTitleCaption("Sistema");
        item5.setWidth(85);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("PROCESSO_STR");
        item6.setTitleCaption("Processo");
        item6.setWidth(132);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("TEMPERATURA_STR");
        item7.setTitleCaption("Temperatura");
        item7.setWidth(118);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("RESPONSAVEL");
        item8.setTitleCaption("Responsavel");
        item8.setWidth(86);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("VENDEDOR");
        item9.setTitleCaption("Vendedor");
        item9.setWidth(230);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(true);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridVendedores.getColumns().add(item9);
        hBoxGrid.addChildren(gridVendedores);
        gridVendedores.applyProperties();
    }

    public TFHBox hBoxGridDiv2 = new TFHBox();

    private void init_hBoxGridDiv2() {
        hBoxGridDiv2.setName("hBoxGridDiv2");
        hBoxGridDiv2.setLeft(834);
        hBoxGridDiv2.setTop(0);
        hBoxGridDiv2.setWidth(5);
        hBoxGridDiv2.setHeight(56);
        hBoxGridDiv2.setBorderStyle("stNone");
        hBoxGridDiv2.setPaddingTop(0);
        hBoxGridDiv2.setPaddingLeft(0);
        hBoxGridDiv2.setPaddingRight(0);
        hBoxGridDiv2.setPaddingBottom(0);
        hBoxGridDiv2.setMarginTop(0);
        hBoxGridDiv2.setMarginLeft(0);
        hBoxGridDiv2.setMarginRight(0);
        hBoxGridDiv2.setMarginBottom(0);
        hBoxGridDiv2.setSpacing(1);
        hBoxGridDiv2.setFlexVflex("ftFalse");
        hBoxGridDiv2.setFlexHflex("ftFalse");
        hBoxGridDiv2.setScrollable(false);
        hBoxGridDiv2.setBoxShadowConfigHorizontalLength(10);
        hBoxGridDiv2.setBoxShadowConfigVerticalLength(10);
        hBoxGridDiv2.setBoxShadowConfigBlurRadius(5);
        hBoxGridDiv2.setBoxShadowConfigSpreadRadius(0);
        hBoxGridDiv2.setBoxShadowConfigShadowColor("clBlack");
        hBoxGridDiv2.setBoxShadowConfigOpacity(75);
        hBoxGridDiv2.setVAlign("tvTop");
        hBoxGrid.addChildren(hBoxGridDiv2);
        hBoxGridDiv2.applyProperties();
    }

    public TFHBox hBoxDiv3 = new TFHBox();

    private void init_hBoxDiv3() {
        hBoxDiv3.setName("hBoxDiv3");
        hBoxDiv3.setLeft(0);
        hBoxDiv3.setTop(337);
        hBoxDiv3.setWidth(697);
        hBoxDiv3.setHeight(5);
        hBoxDiv3.setAlign("alTop");
        hBoxDiv3.setBorderStyle("stNone");
        hBoxDiv3.setPaddingTop(3);
        hBoxDiv3.setPaddingLeft(3);
        hBoxDiv3.setPaddingRight(3);
        hBoxDiv3.setPaddingBottom(0);
        hBoxDiv3.setMarginTop(0);
        hBoxDiv3.setMarginLeft(0);
        hBoxDiv3.setMarginRight(0);
        hBoxDiv3.setMarginBottom(0);
        hBoxDiv3.setSpacing(1);
        hBoxDiv3.setFlexVflex("ftFalse");
        hBoxDiv3.setFlexHflex("ftTrue");
        hBoxDiv3.setScrollable(false);
        hBoxDiv3.setBoxShadowConfigHorizontalLength(10);
        hBoxDiv3.setBoxShadowConfigVerticalLength(10);
        hBoxDiv3.setBoxShadowConfigBlurRadius(5);
        hBoxDiv3.setBoxShadowConfigSpreadRadius(0);
        hBoxDiv3.setBoxShadowConfigShadowColor("clBlack");
        hBoxDiv3.setBoxShadowConfigOpacity(75);
        hBoxDiv3.setVAlign("tvTop");
        vBoxCadastroVendResp.addChildren(hBoxDiv3);
        hBoxDiv3.applyProperties();
    }

    public TFTabsheet tbsCadastro = new TFTabsheet();

    private void init_tbsCadastro() {
        tbsCadastro.setName("tbsCadastro");
        tbsCadastro.setCaption("Cadastro");
        tbsCadastro.setFontColor("clWindowText");
        tbsCadastro.setFontSize(-11);
        tbsCadastro.setFontName("Tahoma");
        tbsCadastro.setFontStyle("[]");
        tbsCadastro.setVisible(false);
        tbsCadastro.setClosable(false);
        pgcVendedorResponsavel.addChildren(tbsCadastro);
        tbsCadastro.applyProperties();
    }

    public TFVBox vBoxAlteracaoVendResp = new TFVBox();

    private void init_vBoxAlteracaoVendResp() {
        vBoxAlteracaoVendResp.setName("vBoxAlteracaoVendResp");
        vBoxAlteracaoVendResp.setLeft(0);
        vBoxAlteracaoVendResp.setTop(0);
        vBoxAlteracaoVendResp.setWidth(846);
        vBoxAlteracaoVendResp.setHeight(445);
        vBoxAlteracaoVendResp.setAlign("alClient");
        vBoxAlteracaoVendResp.setBorderStyle("stNone");
        vBoxAlteracaoVendResp.setPaddingTop(0);
        vBoxAlteracaoVendResp.setPaddingLeft(0);
        vBoxAlteracaoVendResp.setPaddingRight(0);
        vBoxAlteracaoVendResp.setPaddingBottom(0);
        vBoxAlteracaoVendResp.setMarginTop(0);
        vBoxAlteracaoVendResp.setMarginLeft(0);
        vBoxAlteracaoVendResp.setMarginRight(0);
        vBoxAlteracaoVendResp.setMarginBottom(0);
        vBoxAlteracaoVendResp.setSpacing(1);
        vBoxAlteracaoVendResp.setFlexVflex("ftTrue");
        vBoxAlteracaoVendResp.setFlexHflex("ftTrue");
        vBoxAlteracaoVendResp.setScrollable(false);
        vBoxAlteracaoVendResp.setBoxShadowConfigHorizontalLength(10);
        vBoxAlteracaoVendResp.setBoxShadowConfigVerticalLength(10);
        vBoxAlteracaoVendResp.setBoxShadowConfigBlurRadius(5);
        vBoxAlteracaoVendResp.setBoxShadowConfigSpreadRadius(0);
        vBoxAlteracaoVendResp.setBoxShadowConfigShadowColor("clBlack");
        vBoxAlteracaoVendResp.setBoxShadowConfigOpacity(75);
        tbsCadastro.addChildren(vBoxAlteracaoVendResp);
        vBoxAlteracaoVendResp.applyProperties();
    }

    public TFHBox hBoxComboBoxAlteracao = new TFHBox();

    private void init_hBoxComboBoxAlteracao() {
        hBoxComboBoxAlteracao.setName("hBoxComboBoxAlteracao");
        hBoxComboBoxAlteracao.setLeft(0);
        hBoxComboBoxAlteracao.setTop(0);
        hBoxComboBoxAlteracao.setWidth(790);
        hBoxComboBoxAlteracao.setHeight(77);
        hBoxComboBoxAlteracao.setAlign("alTop");
        hBoxComboBoxAlteracao.setBorderStyle("stNone");
        hBoxComboBoxAlteracao.setPaddingTop(3);
        hBoxComboBoxAlteracao.setPaddingLeft(3);
        hBoxComboBoxAlteracao.setPaddingRight(3);
        hBoxComboBoxAlteracao.setPaddingBottom(0);
        hBoxComboBoxAlteracao.setMarginTop(0);
        hBoxComboBoxAlteracao.setMarginLeft(0);
        hBoxComboBoxAlteracao.setMarginRight(0);
        hBoxComboBoxAlteracao.setMarginBottom(0);
        hBoxComboBoxAlteracao.setSpacing(1);
        hBoxComboBoxAlteracao.setFlexVflex("ftMin");
        hBoxComboBoxAlteracao.setFlexHflex("ftTrue");
        hBoxComboBoxAlteracao.setScrollable(false);
        hBoxComboBoxAlteracao.setBoxShadowConfigHorizontalLength(10);
        hBoxComboBoxAlteracao.setBoxShadowConfigVerticalLength(10);
        hBoxComboBoxAlteracao.setBoxShadowConfigBlurRadius(5);
        hBoxComboBoxAlteracao.setBoxShadowConfigSpreadRadius(0);
        hBoxComboBoxAlteracao.setBoxShadowConfigShadowColor("clBlack");
        hBoxComboBoxAlteracao.setBoxShadowConfigOpacity(75);
        hBoxComboBoxAlteracao.setVAlign("tvTop");
        vBoxAlteracaoVendResp.addChildren(hBoxComboBoxAlteracao);
        hBoxComboBoxAlteracao.applyProperties();
    }

    public TFHBox hBoxaltDiv2 = new TFHBox();

    private void init_hBoxaltDiv2() {
        hBoxaltDiv2.setName("hBoxaltDiv2");
        hBoxaltDiv2.setLeft(0);
        hBoxaltDiv2.setTop(0);
        hBoxaltDiv2.setWidth(5);
        hBoxaltDiv2.setHeight(20);
        hBoxaltDiv2.setBorderStyle("stNone");
        hBoxaltDiv2.setPaddingTop(0);
        hBoxaltDiv2.setPaddingLeft(0);
        hBoxaltDiv2.setPaddingRight(0);
        hBoxaltDiv2.setPaddingBottom(0);
        hBoxaltDiv2.setMarginTop(0);
        hBoxaltDiv2.setMarginLeft(0);
        hBoxaltDiv2.setMarginRight(0);
        hBoxaltDiv2.setMarginBottom(0);
        hBoxaltDiv2.setSpacing(1);
        hBoxaltDiv2.setFlexVflex("ftFalse");
        hBoxaltDiv2.setFlexHflex("ftFalse");
        hBoxaltDiv2.setScrollable(false);
        hBoxaltDiv2.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv2.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv2.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv2.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv2.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv2.setBoxShadowConfigOpacity(75);
        hBoxaltDiv2.setVAlign("tvTop");
        hBoxComboBoxAlteracao.addChildren(hBoxaltDiv2);
        hBoxaltDiv2.applyProperties();
    }

    public TFVBox vBoxEmpresaAlteracao = new TFVBox();

    private void init_vBoxEmpresaAlteracao() {
        vBoxEmpresaAlteracao.setName("vBoxEmpresaAlteracao");
        vBoxEmpresaAlteracao.setLeft(5);
        vBoxEmpresaAlteracao.setTop(0);
        vBoxEmpresaAlteracao.setWidth(150);
        vBoxEmpresaAlteracao.setHeight(70);
        vBoxEmpresaAlteracao.setBorderStyle("stNone");
        vBoxEmpresaAlteracao.setPaddingTop(0);
        vBoxEmpresaAlteracao.setPaddingLeft(0);
        vBoxEmpresaAlteracao.setPaddingRight(0);
        vBoxEmpresaAlteracao.setPaddingBottom(0);
        vBoxEmpresaAlteracao.setMarginTop(0);
        vBoxEmpresaAlteracao.setMarginLeft(0);
        vBoxEmpresaAlteracao.setMarginRight(0);
        vBoxEmpresaAlteracao.setMarginBottom(0);
        vBoxEmpresaAlteracao.setSpacing(1);
        vBoxEmpresaAlteracao.setFlexVflex("ftMin");
        vBoxEmpresaAlteracao.setFlexHflex("ftTrue");
        vBoxEmpresaAlteracao.setScrollable(false);
        vBoxEmpresaAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresaAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresaAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresaAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresaAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresaAlteracao.setBoxShadowConfigOpacity(75);
        hBoxComboBoxAlteracao.addChildren(vBoxEmpresaAlteracao);
        vBoxEmpresaAlteracao.applyProperties();
    }

    public TFVBox hBoxaltDiv3 = new TFVBox();

    private void init_hBoxaltDiv3() {
        hBoxaltDiv3.setName("hBoxaltDiv3");
        hBoxaltDiv3.setLeft(0);
        hBoxaltDiv3.setTop(0);
        hBoxaltDiv3.setWidth(100);
        hBoxaltDiv3.setHeight(5);
        hBoxaltDiv3.setBorderStyle("stNone");
        hBoxaltDiv3.setPaddingTop(0);
        hBoxaltDiv3.setPaddingLeft(0);
        hBoxaltDiv3.setPaddingRight(0);
        hBoxaltDiv3.setPaddingBottom(0);
        hBoxaltDiv3.setMarginTop(0);
        hBoxaltDiv3.setMarginLeft(0);
        hBoxaltDiv3.setMarginRight(0);
        hBoxaltDiv3.setMarginBottom(0);
        hBoxaltDiv3.setSpacing(1);
        hBoxaltDiv3.setFlexVflex("ftFalse");
        hBoxaltDiv3.setFlexHflex("ftFalse");
        hBoxaltDiv3.setScrollable(false);
        hBoxaltDiv3.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv3.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv3.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv3.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv3.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv3.setBoxShadowConfigOpacity(75);
        vBoxEmpresaAlteracao.addChildren(hBoxaltDiv3);
        hBoxaltDiv3.applyProperties();
    }

    public TFLabel lblAltEmpresa = new TFLabel();

    private void init_lblAltEmpresa() {
        lblAltEmpresa.setName("lblAltEmpresa");
        lblAltEmpresa.setLeft(0);
        lblAltEmpresa.setTop(6);
        lblAltEmpresa.setWidth(57);
        lblAltEmpresa.setHeight(13);
        lblAltEmpresa.setCaption("1 - Empresa");
        lblAltEmpresa.setFontColor("clWindowText");
        lblAltEmpresa.setFontSize(-11);
        lblAltEmpresa.setFontName("Tahoma");
        lblAltEmpresa.setFontStyle("[]");
        lblAltEmpresa.setVerticalAlignment("taVerticalCenter");
        lblAltEmpresa.setWordBreak(false);
        vBoxEmpresaAlteracao.addChildren(lblAltEmpresa);
        lblAltEmpresa.applyProperties();
    }

    public TFVBox hBoxaltDiv4 = new TFVBox();

    private void init_hBoxaltDiv4() {
        hBoxaltDiv4.setName("hBoxaltDiv4");
        hBoxaltDiv4.setLeft(0);
        hBoxaltDiv4.setTop(20);
        hBoxaltDiv4.setWidth(100);
        hBoxaltDiv4.setHeight(5);
        hBoxaltDiv4.setBorderStyle("stNone");
        hBoxaltDiv4.setPaddingTop(0);
        hBoxaltDiv4.setPaddingLeft(0);
        hBoxaltDiv4.setPaddingRight(0);
        hBoxaltDiv4.setPaddingBottom(0);
        hBoxaltDiv4.setMarginTop(0);
        hBoxaltDiv4.setMarginLeft(0);
        hBoxaltDiv4.setMarginRight(0);
        hBoxaltDiv4.setMarginBottom(0);
        hBoxaltDiv4.setSpacing(1);
        hBoxaltDiv4.setFlexVflex("ftFalse");
        hBoxaltDiv4.setFlexHflex("ftFalse");
        hBoxaltDiv4.setScrollable(false);
        hBoxaltDiv4.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv4.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv4.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv4.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv4.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv4.setBoxShadowConfigOpacity(75);
        vBoxEmpresaAlteracao.addChildren(hBoxaltDiv4);
        hBoxaltDiv4.applyProperties();
    }

    public TFCombo cboAltEmpresa = new TFCombo();

    private void init_cboAltEmpresa() {
        cboAltEmpresa.setName("cboAltEmpresa");
        cboAltEmpresa.setLeft(0);
        cboAltEmpresa.setTop(26);
        cboAltEmpresa.setWidth(140);
        cboAltEmpresa.setHeight(21);
        cboAltEmpresa.setHint("Empresa");
        cboAltEmpresa.setLookupTable(tbEmpresasCadastro);
        cboAltEmpresa.setLookupKey("COD_EMPRESA");
        cboAltEmpresa.setLookupDesc("EMPRESA_UPPER");
        cboAltEmpresa.setFlex(true);
        cboAltEmpresa.setHelpCaption("Empresa");
        cboAltEmpresa.setReadOnly(true);
        cboAltEmpresa.setRequired(true);
        cboAltEmpresa.setPrompt("Empresa");
        cboAltEmpresa.setConstraintCheckWhen("cwImmediate");
        cboAltEmpresa.setConstraintCheckType("ctExpression");
        cboAltEmpresa.setConstraintFocusOnError(false);
        cboAltEmpresa.setConstraintEnableUI(true);
        cboAltEmpresa.setConstraintEnabled(false);
        cboAltEmpresa.setConstraintFormCheck(true);
        cboAltEmpresa.setClearOnDelKey(false);
        cboAltEmpresa.setUseClearButton(false);
        cboAltEmpresa.setHideClearButtonOnNullValue(false);
        cboAltEmpresa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboAltEmpresa", "OnEnter");
        });
        vBoxEmpresaAlteracao.addChildren(cboAltEmpresa);
        cboAltEmpresa.applyProperties();
        addValidatable(cboAltEmpresa);
    }

    public TFVBox hBoxaltDiv5 = new TFVBox();

    private void init_hBoxaltDiv5() {
        hBoxaltDiv5.setName("hBoxaltDiv5");
        hBoxaltDiv5.setLeft(0);
        hBoxaltDiv5.setTop(48);
        hBoxaltDiv5.setWidth(100);
        hBoxaltDiv5.setHeight(5);
        hBoxaltDiv5.setBorderStyle("stNone");
        hBoxaltDiv5.setPaddingTop(0);
        hBoxaltDiv5.setPaddingLeft(0);
        hBoxaltDiv5.setPaddingRight(0);
        hBoxaltDiv5.setPaddingBottom(0);
        hBoxaltDiv5.setMarginTop(0);
        hBoxaltDiv5.setMarginLeft(0);
        hBoxaltDiv5.setMarginRight(0);
        hBoxaltDiv5.setMarginBottom(0);
        hBoxaltDiv5.setSpacing(1);
        hBoxaltDiv5.setFlexVflex("ftFalse");
        hBoxaltDiv5.setFlexHflex("ftFalse");
        hBoxaltDiv5.setScrollable(false);
        hBoxaltDiv5.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv5.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv5.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv5.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv5.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv5.setBoxShadowConfigOpacity(75);
        vBoxEmpresaAlteracao.addChildren(hBoxaltDiv5);
        hBoxaltDiv5.applyProperties();
    }

    public TFHBox hBoxaltDiv19 = new TFHBox();

    private void init_hBoxaltDiv19() {
        hBoxaltDiv19.setName("hBoxaltDiv19");
        hBoxaltDiv19.setLeft(155);
        hBoxaltDiv19.setTop(0);
        hBoxaltDiv19.setWidth(5);
        hBoxaltDiv19.setHeight(56);
        hBoxaltDiv19.setBorderStyle("stNone");
        hBoxaltDiv19.setPaddingTop(0);
        hBoxaltDiv19.setPaddingLeft(0);
        hBoxaltDiv19.setPaddingRight(0);
        hBoxaltDiv19.setPaddingBottom(0);
        hBoxaltDiv19.setMarginTop(0);
        hBoxaltDiv19.setMarginLeft(0);
        hBoxaltDiv19.setMarginRight(0);
        hBoxaltDiv19.setMarginBottom(0);
        hBoxaltDiv19.setSpacing(1);
        hBoxaltDiv19.setFlexVflex("ftFalse");
        hBoxaltDiv19.setFlexHflex("ftFalse");
        hBoxaltDiv19.setScrollable(false);
        hBoxaltDiv19.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv19.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv19.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv19.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv19.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv19.setBoxShadowConfigOpacity(75);
        hBoxaltDiv19.setVAlign("tvTop");
        hBoxComboBoxAlteracao.addChildren(hBoxaltDiv19);
        hBoxaltDiv19.applyProperties();
    }

    public TFVBox vBoxSistemaAlteracao = new TFVBox();

    private void init_vBoxSistemaAlteracao() {
        vBoxSistemaAlteracao.setName("vBoxSistemaAlteracao");
        vBoxSistemaAlteracao.setLeft(160);
        vBoxSistemaAlteracao.setTop(0);
        vBoxSistemaAlteracao.setWidth(150);
        vBoxSistemaAlteracao.setHeight(70);
        vBoxSistemaAlteracao.setBorderStyle("stNone");
        vBoxSistemaAlteracao.setPaddingTop(0);
        vBoxSistemaAlteracao.setPaddingLeft(0);
        vBoxSistemaAlteracao.setPaddingRight(0);
        vBoxSistemaAlteracao.setPaddingBottom(0);
        vBoxSistemaAlteracao.setMarginTop(0);
        vBoxSistemaAlteracao.setMarginLeft(0);
        vBoxSistemaAlteracao.setMarginRight(0);
        vBoxSistemaAlteracao.setMarginBottom(0);
        vBoxSistemaAlteracao.setSpacing(1);
        vBoxSistemaAlteracao.setFlexVflex("ftMin");
        vBoxSistemaAlteracao.setFlexHflex("ftTrue");
        vBoxSistemaAlteracao.setScrollable(false);
        vBoxSistemaAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxSistemaAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxSistemaAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxSistemaAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxSistemaAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxSistemaAlteracao.setBoxShadowConfigOpacity(75);
        hBoxComboBoxAlteracao.addChildren(vBoxSistemaAlteracao);
        vBoxSistemaAlteracao.applyProperties();
    }

    public TFVBox hBoxaltDiv6 = new TFVBox();

    private void init_hBoxaltDiv6() {
        hBoxaltDiv6.setName("hBoxaltDiv6");
        hBoxaltDiv6.setLeft(0);
        hBoxaltDiv6.setTop(0);
        hBoxaltDiv6.setWidth(100);
        hBoxaltDiv6.setHeight(5);
        hBoxaltDiv6.setBorderStyle("stNone");
        hBoxaltDiv6.setPaddingTop(0);
        hBoxaltDiv6.setPaddingLeft(0);
        hBoxaltDiv6.setPaddingRight(0);
        hBoxaltDiv6.setPaddingBottom(0);
        hBoxaltDiv6.setMarginTop(0);
        hBoxaltDiv6.setMarginLeft(0);
        hBoxaltDiv6.setMarginRight(0);
        hBoxaltDiv6.setMarginBottom(0);
        hBoxaltDiv6.setSpacing(1);
        hBoxaltDiv6.setFlexVflex("ftFalse");
        hBoxaltDiv6.setFlexHflex("ftFalse");
        hBoxaltDiv6.setScrollable(false);
        hBoxaltDiv6.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv6.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv6.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv6.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv6.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv6.setBoxShadowConfigOpacity(75);
        vBoxSistemaAlteracao.addChildren(hBoxaltDiv6);
        hBoxaltDiv6.applyProperties();
    }

    public TFLabel lblAltSistema = new TFLabel();

    private void init_lblAltSistema() {
        lblAltSistema.setName("lblAltSistema");
        lblAltSistema.setLeft(0);
        lblAltSistema.setTop(6);
        lblAltSistema.setWidth(53);
        lblAltSistema.setHeight(13);
        lblAltSistema.setCaption("2 - Sistema");
        lblAltSistema.setFontColor("clWindowText");
        lblAltSistema.setFontSize(-11);
        lblAltSistema.setFontName("Tahoma");
        lblAltSistema.setFontStyle("[]");
        lblAltSistema.setVerticalAlignment("taVerticalCenter");
        lblAltSistema.setWordBreak(false);
        vBoxSistemaAlteracao.addChildren(lblAltSistema);
        lblAltSistema.applyProperties();
    }

    public TFVBox hBoxaltDiv7 = new TFVBox();

    private void init_hBoxaltDiv7() {
        hBoxaltDiv7.setName("hBoxaltDiv7");
        hBoxaltDiv7.setLeft(0);
        hBoxaltDiv7.setTop(20);
        hBoxaltDiv7.setWidth(100);
        hBoxaltDiv7.setHeight(5);
        hBoxaltDiv7.setBorderStyle("stNone");
        hBoxaltDiv7.setPaddingTop(0);
        hBoxaltDiv7.setPaddingLeft(0);
        hBoxaltDiv7.setPaddingRight(0);
        hBoxaltDiv7.setPaddingBottom(0);
        hBoxaltDiv7.setMarginTop(0);
        hBoxaltDiv7.setMarginLeft(0);
        hBoxaltDiv7.setMarginRight(0);
        hBoxaltDiv7.setMarginBottom(0);
        hBoxaltDiv7.setSpacing(1);
        hBoxaltDiv7.setFlexVflex("ftFalse");
        hBoxaltDiv7.setFlexHflex("ftFalse");
        hBoxaltDiv7.setScrollable(false);
        hBoxaltDiv7.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv7.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv7.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv7.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv7.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv7.setBoxShadowConfigOpacity(75);
        vBoxSistemaAlteracao.addChildren(hBoxaltDiv7);
        hBoxaltDiv7.applyProperties();
    }

    public TFCombo cboAltSistema = new TFCombo();

    private void init_cboAltSistema() {
        cboAltSistema.setName("cboAltSistema");
        cboAltSistema.setLeft(0);
        cboAltSistema.setTop(26);
        cboAltSistema.setWidth(140);
        cboAltSistema.setHeight(21);
        cboAltSistema.setHint("Sistema");
        cboAltSistema.setFlex(true);
        cboAltSistema.setListOptions("Gold [1]=1;Parts [2]=2;Service [3]=3");
        cboAltSistema.setHelpCaption("Sistema");
        cboAltSistema.setReadOnly(true);
        cboAltSistema.setRequired(true);
        cboAltSistema.setPrompt("Sistema");
        cboAltSistema.setConstraintCheckWhen("cwImmediate");
        cboAltSistema.setConstraintCheckType("ctExpression");
        cboAltSistema.setConstraintFocusOnError(false);
        cboAltSistema.setConstraintEnableUI(true);
        cboAltSistema.setConstraintEnabled(false);
        cboAltSistema.setConstraintFormCheck(true);
        cboAltSistema.setClearOnDelKey(false);
        cboAltSistema.setUseClearButton(false);
        cboAltSistema.setHideClearButtonOnNullValue(false);
        cboAltSistema.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboAltSistema", "OnEnter");
        });
        vBoxSistemaAlteracao.addChildren(cboAltSistema);
        cboAltSistema.applyProperties();
        addValidatable(cboAltSistema);
    }

    public TFVBox hBoxaltDiv8 = new TFVBox();

    private void init_hBoxaltDiv8() {
        hBoxaltDiv8.setName("hBoxaltDiv8");
        hBoxaltDiv8.setLeft(0);
        hBoxaltDiv8.setTop(48);
        hBoxaltDiv8.setWidth(100);
        hBoxaltDiv8.setHeight(5);
        hBoxaltDiv8.setBorderStyle("stNone");
        hBoxaltDiv8.setPaddingTop(0);
        hBoxaltDiv8.setPaddingLeft(0);
        hBoxaltDiv8.setPaddingRight(0);
        hBoxaltDiv8.setPaddingBottom(0);
        hBoxaltDiv8.setMarginTop(0);
        hBoxaltDiv8.setMarginLeft(0);
        hBoxaltDiv8.setMarginRight(0);
        hBoxaltDiv8.setMarginBottom(0);
        hBoxaltDiv8.setSpacing(1);
        hBoxaltDiv8.setFlexVflex("ftFalse");
        hBoxaltDiv8.setFlexHflex("ftFalse");
        hBoxaltDiv8.setScrollable(false);
        hBoxaltDiv8.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv8.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv8.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv8.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv8.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv8.setBoxShadowConfigOpacity(75);
        vBoxSistemaAlteracao.addChildren(hBoxaltDiv8);
        hBoxaltDiv8.applyProperties();
    }

    public TFHBox hBoxaltDiv20 = new TFHBox();

    private void init_hBoxaltDiv20() {
        hBoxaltDiv20.setName("hBoxaltDiv20");
        hBoxaltDiv20.setLeft(310);
        hBoxaltDiv20.setTop(0);
        hBoxaltDiv20.setWidth(5);
        hBoxaltDiv20.setHeight(56);
        hBoxaltDiv20.setBorderStyle("stNone");
        hBoxaltDiv20.setPaddingTop(0);
        hBoxaltDiv20.setPaddingLeft(0);
        hBoxaltDiv20.setPaddingRight(0);
        hBoxaltDiv20.setPaddingBottom(0);
        hBoxaltDiv20.setMarginTop(0);
        hBoxaltDiv20.setMarginLeft(0);
        hBoxaltDiv20.setMarginRight(0);
        hBoxaltDiv20.setMarginBottom(0);
        hBoxaltDiv20.setSpacing(1);
        hBoxaltDiv20.setFlexVflex("ftFalse");
        hBoxaltDiv20.setFlexHflex("ftFalse");
        hBoxaltDiv20.setScrollable(false);
        hBoxaltDiv20.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv20.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv20.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv20.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv20.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv20.setBoxShadowConfigOpacity(75);
        hBoxaltDiv20.setVAlign("tvTop");
        hBoxComboBoxAlteracao.addChildren(hBoxaltDiv20);
        hBoxaltDiv20.applyProperties();
    }

    public TFVBox vBoxProcessoAlteracao = new TFVBox();

    private void init_vBoxProcessoAlteracao() {
        vBoxProcessoAlteracao.setName("vBoxProcessoAlteracao");
        vBoxProcessoAlteracao.setLeft(315);
        vBoxProcessoAlteracao.setTop(0);
        vBoxProcessoAlteracao.setWidth(150);
        vBoxProcessoAlteracao.setHeight(70);
        vBoxProcessoAlteracao.setBorderStyle("stNone");
        vBoxProcessoAlteracao.setPaddingTop(0);
        vBoxProcessoAlteracao.setPaddingLeft(0);
        vBoxProcessoAlteracao.setPaddingRight(0);
        vBoxProcessoAlteracao.setPaddingBottom(0);
        vBoxProcessoAlteracao.setMarginTop(0);
        vBoxProcessoAlteracao.setMarginLeft(0);
        vBoxProcessoAlteracao.setMarginRight(0);
        vBoxProcessoAlteracao.setMarginBottom(0);
        vBoxProcessoAlteracao.setSpacing(1);
        vBoxProcessoAlteracao.setFlexVflex("ftMin");
        vBoxProcessoAlteracao.setFlexHflex("ftTrue");
        vBoxProcessoAlteracao.setScrollable(false);
        vBoxProcessoAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxProcessoAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxProcessoAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxProcessoAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxProcessoAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxProcessoAlteracao.setBoxShadowConfigOpacity(75);
        hBoxComboBoxAlteracao.addChildren(vBoxProcessoAlteracao);
        vBoxProcessoAlteracao.applyProperties();
    }

    public TFVBox hBoxaltDiv9 = new TFVBox();

    private void init_hBoxaltDiv9() {
        hBoxaltDiv9.setName("hBoxaltDiv9");
        hBoxaltDiv9.setLeft(0);
        hBoxaltDiv9.setTop(0);
        hBoxaltDiv9.setWidth(100);
        hBoxaltDiv9.setHeight(5);
        hBoxaltDiv9.setBorderStyle("stNone");
        hBoxaltDiv9.setPaddingTop(0);
        hBoxaltDiv9.setPaddingLeft(0);
        hBoxaltDiv9.setPaddingRight(0);
        hBoxaltDiv9.setPaddingBottom(0);
        hBoxaltDiv9.setMarginTop(0);
        hBoxaltDiv9.setMarginLeft(0);
        hBoxaltDiv9.setMarginRight(0);
        hBoxaltDiv9.setMarginBottom(0);
        hBoxaltDiv9.setSpacing(1);
        hBoxaltDiv9.setFlexVflex("ftFalse");
        hBoxaltDiv9.setFlexHflex("ftFalse");
        hBoxaltDiv9.setScrollable(false);
        hBoxaltDiv9.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv9.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv9.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv9.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv9.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv9.setBoxShadowConfigOpacity(75);
        vBoxProcessoAlteracao.addChildren(hBoxaltDiv9);
        hBoxaltDiv9.applyProperties();
    }

    public TFLabel lblAltProcesso = new TFLabel();

    private void init_lblAltProcesso() {
        lblAltProcesso.setName("lblAltProcesso");
        lblAltProcesso.setLeft(0);
        lblAltProcesso.setTop(6);
        lblAltProcesso.setWidth(59);
        lblAltProcesso.setHeight(13);
        lblAltProcesso.setCaption("3 - Processo");
        lblAltProcesso.setFontColor("clWindowText");
        lblAltProcesso.setFontSize(-11);
        lblAltProcesso.setFontName("Tahoma");
        lblAltProcesso.setFontStyle("[]");
        lblAltProcesso.setVerticalAlignment("taVerticalCenter");
        lblAltProcesso.setWordBreak(false);
        vBoxProcessoAlteracao.addChildren(lblAltProcesso);
        lblAltProcesso.applyProperties();
    }

    public TFVBox hBoxaltDiv10 = new TFVBox();

    private void init_hBoxaltDiv10() {
        hBoxaltDiv10.setName("hBoxaltDiv10");
        hBoxaltDiv10.setLeft(0);
        hBoxaltDiv10.setTop(20);
        hBoxaltDiv10.setWidth(100);
        hBoxaltDiv10.setHeight(5);
        hBoxaltDiv10.setBorderStyle("stNone");
        hBoxaltDiv10.setPaddingTop(0);
        hBoxaltDiv10.setPaddingLeft(0);
        hBoxaltDiv10.setPaddingRight(0);
        hBoxaltDiv10.setPaddingBottom(0);
        hBoxaltDiv10.setMarginTop(0);
        hBoxaltDiv10.setMarginLeft(0);
        hBoxaltDiv10.setMarginRight(0);
        hBoxaltDiv10.setMarginBottom(0);
        hBoxaltDiv10.setSpacing(1);
        hBoxaltDiv10.setFlexVflex("ftFalse");
        hBoxaltDiv10.setFlexHflex("ftFalse");
        hBoxaltDiv10.setScrollable(false);
        hBoxaltDiv10.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv10.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv10.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv10.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv10.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv10.setBoxShadowConfigOpacity(75);
        vBoxProcessoAlteracao.addChildren(hBoxaltDiv10);
        hBoxaltDiv10.applyProperties();
    }

    public TFCombo cboAltProcesso = new TFCombo();

    private void init_cboAltProcesso() {
        cboAltProcesso.setName("cboAltProcesso");
        cboAltProcesso.setLeft(0);
        cboAltProcesso.setTop(26);
        cboAltProcesso.setWidth(140);
        cboAltProcesso.setHeight(21);
        cboAltProcesso.setHint("Processo");
        cboAltProcesso.setFlex(true);
        cboAltProcesso.setListOptions("Caminh\u00E3o [1]=1;Vans [2]=2;\u00D4nibus [3]=3;Vendedor Interno [4]=4;Vendedor Externo [5]=5;Vendedor Pneu [6]=6");
        cboAltProcesso.setHelpCaption("Processo");
        cboAltProcesso.setReadOnly(true);
        cboAltProcesso.setRequired(true);
        cboAltProcesso.setPrompt("Processo");
        cboAltProcesso.setConstraintCheckWhen("cwImmediate");
        cboAltProcesso.setConstraintCheckType("ctExpression");
        cboAltProcesso.setConstraintFocusOnError(false);
        cboAltProcesso.setConstraintEnableUI(true);
        cboAltProcesso.setConstraintEnabled(false);
        cboAltProcesso.setConstraintFormCheck(true);
        cboAltProcesso.setClearOnDelKey(false);
        cboAltProcesso.setUseClearButton(false);
        cboAltProcesso.setHideClearButtonOnNullValue(false);
        cboAltProcesso.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            tbEmpresasCadastroAfterScroll(event);
            processarFlow("FrmVendedorResponsavel", "cboAltProcesso", "OnChange");
        });
        cboAltProcesso.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboAltProcesso", "OnEnter");
        });
        vBoxProcessoAlteracao.addChildren(cboAltProcesso);
        cboAltProcesso.applyProperties();
        addValidatable(cboAltProcesso);
    }

    public TFVBox hBoxaltDiv11 = new TFVBox();

    private void init_hBoxaltDiv11() {
        hBoxaltDiv11.setName("hBoxaltDiv11");
        hBoxaltDiv11.setLeft(0);
        hBoxaltDiv11.setTop(48);
        hBoxaltDiv11.setWidth(100);
        hBoxaltDiv11.setHeight(5);
        hBoxaltDiv11.setBorderStyle("stNone");
        hBoxaltDiv11.setPaddingTop(0);
        hBoxaltDiv11.setPaddingLeft(0);
        hBoxaltDiv11.setPaddingRight(0);
        hBoxaltDiv11.setPaddingBottom(0);
        hBoxaltDiv11.setMarginTop(0);
        hBoxaltDiv11.setMarginLeft(0);
        hBoxaltDiv11.setMarginRight(0);
        hBoxaltDiv11.setMarginBottom(0);
        hBoxaltDiv11.setSpacing(1);
        hBoxaltDiv11.setFlexVflex("ftFalse");
        hBoxaltDiv11.setFlexHflex("ftFalse");
        hBoxaltDiv11.setScrollable(false);
        hBoxaltDiv11.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv11.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv11.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv11.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv11.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv11.setBoxShadowConfigOpacity(75);
        vBoxProcessoAlteracao.addChildren(hBoxaltDiv11);
        hBoxaltDiv11.applyProperties();
    }

    public TFHBox hBoxaltDiv21 = new TFHBox();

    private void init_hBoxaltDiv21() {
        hBoxaltDiv21.setName("hBoxaltDiv21");
        hBoxaltDiv21.setLeft(465);
        hBoxaltDiv21.setTop(0);
        hBoxaltDiv21.setWidth(5);
        hBoxaltDiv21.setHeight(56);
        hBoxaltDiv21.setBorderStyle("stNone");
        hBoxaltDiv21.setPaddingTop(0);
        hBoxaltDiv21.setPaddingLeft(0);
        hBoxaltDiv21.setPaddingRight(0);
        hBoxaltDiv21.setPaddingBottom(0);
        hBoxaltDiv21.setMarginTop(0);
        hBoxaltDiv21.setMarginLeft(0);
        hBoxaltDiv21.setMarginRight(0);
        hBoxaltDiv21.setMarginBottom(0);
        hBoxaltDiv21.setSpacing(1);
        hBoxaltDiv21.setFlexVflex("ftFalse");
        hBoxaltDiv21.setFlexHflex("ftFalse");
        hBoxaltDiv21.setScrollable(false);
        hBoxaltDiv21.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv21.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv21.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv21.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv21.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv21.setBoxShadowConfigOpacity(75);
        hBoxaltDiv21.setVAlign("tvTop");
        hBoxComboBoxAlteracao.addChildren(hBoxaltDiv21);
        hBoxaltDiv21.applyProperties();
    }

    public TFVBox vBoxTemperaturaAlteracao = new TFVBox();

    private void init_vBoxTemperaturaAlteracao() {
        vBoxTemperaturaAlteracao.setName("vBoxTemperaturaAlteracao");
        vBoxTemperaturaAlteracao.setLeft(470);
        vBoxTemperaturaAlteracao.setTop(0);
        vBoxTemperaturaAlteracao.setWidth(150);
        vBoxTemperaturaAlteracao.setHeight(70);
        vBoxTemperaturaAlteracao.setBorderStyle("stNone");
        vBoxTemperaturaAlteracao.setPaddingTop(0);
        vBoxTemperaturaAlteracao.setPaddingLeft(0);
        vBoxTemperaturaAlteracao.setPaddingRight(0);
        vBoxTemperaturaAlteracao.setPaddingBottom(0);
        vBoxTemperaturaAlteracao.setMarginTop(0);
        vBoxTemperaturaAlteracao.setMarginLeft(0);
        vBoxTemperaturaAlteracao.setMarginRight(0);
        vBoxTemperaturaAlteracao.setMarginBottom(0);
        vBoxTemperaturaAlteracao.setSpacing(1);
        vBoxTemperaturaAlteracao.setFlexVflex("ftMin");
        vBoxTemperaturaAlteracao.setFlexHflex("ftTrue");
        vBoxTemperaturaAlteracao.setScrollable(false);
        vBoxTemperaturaAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxTemperaturaAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxTemperaturaAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxTemperaturaAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxTemperaturaAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxTemperaturaAlteracao.setBoxShadowConfigOpacity(75);
        hBoxComboBoxAlteracao.addChildren(vBoxTemperaturaAlteracao);
        vBoxTemperaturaAlteracao.applyProperties();
    }

    public TFVBox hBoxaltDiv12 = new TFVBox();

    private void init_hBoxaltDiv12() {
        hBoxaltDiv12.setName("hBoxaltDiv12");
        hBoxaltDiv12.setLeft(0);
        hBoxaltDiv12.setTop(0);
        hBoxaltDiv12.setWidth(100);
        hBoxaltDiv12.setHeight(5);
        hBoxaltDiv12.setBorderStyle("stNone");
        hBoxaltDiv12.setPaddingTop(0);
        hBoxaltDiv12.setPaddingLeft(0);
        hBoxaltDiv12.setPaddingRight(0);
        hBoxaltDiv12.setPaddingBottom(0);
        hBoxaltDiv12.setMarginTop(0);
        hBoxaltDiv12.setMarginLeft(0);
        hBoxaltDiv12.setMarginRight(0);
        hBoxaltDiv12.setMarginBottom(0);
        hBoxaltDiv12.setSpacing(1);
        hBoxaltDiv12.setFlexVflex("ftFalse");
        hBoxaltDiv12.setFlexHflex("ftFalse");
        hBoxaltDiv12.setScrollable(false);
        hBoxaltDiv12.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv12.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv12.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv12.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv12.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv12.setBoxShadowConfigOpacity(75);
        vBoxTemperaturaAlteracao.addChildren(hBoxaltDiv12);
        hBoxaltDiv12.applyProperties();
    }

    public TFLabel lblAltTemperatura = new TFLabel();

    private void init_lblAltTemperatura() {
        lblAltTemperatura.setName("lblAltTemperatura");
        lblAltTemperatura.setLeft(0);
        lblAltTemperatura.setTop(6);
        lblAltTemperatura.setWidth(78);
        lblAltTemperatura.setHeight(13);
        lblAltTemperatura.setCaption("4 - Temperatura");
        lblAltTemperatura.setFontColor("clWindowText");
        lblAltTemperatura.setFontSize(-11);
        lblAltTemperatura.setFontName("Tahoma");
        lblAltTemperatura.setFontStyle("[]");
        lblAltTemperatura.setVerticalAlignment("taVerticalCenter");
        lblAltTemperatura.setWordBreak(false);
        vBoxTemperaturaAlteracao.addChildren(lblAltTemperatura);
        lblAltTemperatura.applyProperties();
    }

    public TFVBox hBoxaltDiv13 = new TFVBox();

    private void init_hBoxaltDiv13() {
        hBoxaltDiv13.setName("hBoxaltDiv13");
        hBoxaltDiv13.setLeft(0);
        hBoxaltDiv13.setTop(20);
        hBoxaltDiv13.setWidth(100);
        hBoxaltDiv13.setHeight(5);
        hBoxaltDiv13.setBorderStyle("stNone");
        hBoxaltDiv13.setPaddingTop(0);
        hBoxaltDiv13.setPaddingLeft(0);
        hBoxaltDiv13.setPaddingRight(0);
        hBoxaltDiv13.setPaddingBottom(0);
        hBoxaltDiv13.setMarginTop(0);
        hBoxaltDiv13.setMarginLeft(0);
        hBoxaltDiv13.setMarginRight(0);
        hBoxaltDiv13.setMarginBottom(0);
        hBoxaltDiv13.setSpacing(1);
        hBoxaltDiv13.setFlexVflex("ftFalse");
        hBoxaltDiv13.setFlexHflex("ftFalse");
        hBoxaltDiv13.setScrollable(false);
        hBoxaltDiv13.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv13.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv13.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv13.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv13.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv13.setBoxShadowConfigOpacity(75);
        vBoxTemperaturaAlteracao.addChildren(hBoxaltDiv13);
        hBoxaltDiv13.applyProperties();
    }

    public TFCombo cboAltTemperatura = new TFCombo();

    private void init_cboAltTemperatura() {
        cboAltTemperatura.setName("cboAltTemperatura");
        cboAltTemperatura.setLeft(0);
        cboAltTemperatura.setTop(26);
        cboAltTemperatura.setWidth(140);
        cboAltTemperatura.setHeight(21);
        cboAltTemperatura.setHint("Temperatura");
        cboAltTemperatura.setFlex(true);
        cboAltTemperatura.setListOptions("Super Quente [1]=1;Quente [2]=2;Morno [3]=3;Frio [4]=4;Bols\u00E3o [5]=5");
        cboAltTemperatura.setHelpCaption("Temperatura");
        cboAltTemperatura.setReadOnly(true);
        cboAltTemperatura.setRequired(true);
        cboAltTemperatura.setPrompt("Temperatura");
        cboAltTemperatura.setConstraintCheckWhen("cwImmediate");
        cboAltTemperatura.setConstraintCheckType("ctExpression");
        cboAltTemperatura.setConstraintFocusOnError(false);
        cboAltTemperatura.setConstraintEnableUI(true);
        cboAltTemperatura.setConstraintEnabled(false);
        cboAltTemperatura.setConstraintFormCheck(true);
        cboAltTemperatura.setClearOnDelKey(false);
        cboAltTemperatura.setUseClearButton(false);
        cboAltTemperatura.setHideClearButtonOnNullValue(false);
        cboAltTemperatura.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboAltTemperatura", "OnEnter");
        });
        vBoxTemperaturaAlteracao.addChildren(cboAltTemperatura);
        cboAltTemperatura.applyProperties();
        addValidatable(cboAltTemperatura);
    }

    public TFVBox hBoxaltDiv14 = new TFVBox();

    private void init_hBoxaltDiv14() {
        hBoxaltDiv14.setName("hBoxaltDiv14");
        hBoxaltDiv14.setLeft(0);
        hBoxaltDiv14.setTop(48);
        hBoxaltDiv14.setWidth(100);
        hBoxaltDiv14.setHeight(5);
        hBoxaltDiv14.setBorderStyle("stNone");
        hBoxaltDiv14.setPaddingTop(0);
        hBoxaltDiv14.setPaddingLeft(0);
        hBoxaltDiv14.setPaddingRight(0);
        hBoxaltDiv14.setPaddingBottom(0);
        hBoxaltDiv14.setMarginTop(0);
        hBoxaltDiv14.setMarginLeft(0);
        hBoxaltDiv14.setMarginRight(0);
        hBoxaltDiv14.setMarginBottom(0);
        hBoxaltDiv14.setSpacing(1);
        hBoxaltDiv14.setFlexVflex("ftFalse");
        hBoxaltDiv14.setFlexHflex("ftFalse");
        hBoxaltDiv14.setScrollable(false);
        hBoxaltDiv14.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv14.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv14.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv14.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv14.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv14.setBoxShadowConfigOpacity(75);
        vBoxTemperaturaAlteracao.addChildren(hBoxaltDiv14);
        hBoxaltDiv14.applyProperties();
    }

    public TFHBox hBoxaltDiv22 = new TFHBox();

    private void init_hBoxaltDiv22() {
        hBoxaltDiv22.setName("hBoxaltDiv22");
        hBoxaltDiv22.setLeft(620);
        hBoxaltDiv22.setTop(0);
        hBoxaltDiv22.setWidth(5);
        hBoxaltDiv22.setHeight(56);
        hBoxaltDiv22.setBorderStyle("stNone");
        hBoxaltDiv22.setPaddingTop(0);
        hBoxaltDiv22.setPaddingLeft(0);
        hBoxaltDiv22.setPaddingRight(0);
        hBoxaltDiv22.setPaddingBottom(0);
        hBoxaltDiv22.setMarginTop(0);
        hBoxaltDiv22.setMarginLeft(0);
        hBoxaltDiv22.setMarginRight(0);
        hBoxaltDiv22.setMarginBottom(0);
        hBoxaltDiv22.setSpacing(1);
        hBoxaltDiv22.setFlexVflex("ftFalse");
        hBoxaltDiv22.setFlexHflex("ftFalse");
        hBoxaltDiv22.setScrollable(false);
        hBoxaltDiv22.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv22.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv22.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv22.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv22.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv22.setBoxShadowConfigOpacity(75);
        hBoxaltDiv22.setVAlign("tvTop");
        hBoxComboBoxAlteracao.addChildren(hBoxaltDiv22);
        hBoxaltDiv22.applyProperties();
    }

    public TFVBox vBoxVendedorAlteracao = new TFVBox();

    private void init_vBoxVendedorAlteracao() {
        vBoxVendedorAlteracao.setName("vBoxVendedorAlteracao");
        vBoxVendedorAlteracao.setLeft(625);
        vBoxVendedorAlteracao.setTop(0);
        vBoxVendedorAlteracao.setWidth(155);
        vBoxVendedorAlteracao.setHeight(70);
        vBoxVendedorAlteracao.setBorderStyle("stNone");
        vBoxVendedorAlteracao.setPaddingTop(0);
        vBoxVendedorAlteracao.setPaddingLeft(0);
        vBoxVendedorAlteracao.setPaddingRight(0);
        vBoxVendedorAlteracao.setPaddingBottom(0);
        vBoxVendedorAlteracao.setMarginTop(0);
        vBoxVendedorAlteracao.setMarginLeft(0);
        vBoxVendedorAlteracao.setMarginRight(0);
        vBoxVendedorAlteracao.setMarginBottom(0);
        vBoxVendedorAlteracao.setSpacing(1);
        vBoxVendedorAlteracao.setFlexVflex("ftMin");
        vBoxVendedorAlteracao.setFlexHflex("ftTrue");
        vBoxVendedorAlteracao.setScrollable(false);
        vBoxVendedorAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedorAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxVendedorAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxVendedorAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedorAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedorAlteracao.setBoxShadowConfigOpacity(75);
        hBoxComboBoxAlteracao.addChildren(vBoxVendedorAlteracao);
        vBoxVendedorAlteracao.applyProperties();
    }

    public TFVBox hBoxaltDiv15 = new TFVBox();

    private void init_hBoxaltDiv15() {
        hBoxaltDiv15.setName("hBoxaltDiv15");
        hBoxaltDiv15.setLeft(0);
        hBoxaltDiv15.setTop(0);
        hBoxaltDiv15.setWidth(100);
        hBoxaltDiv15.setHeight(5);
        hBoxaltDiv15.setBorderStyle("stNone");
        hBoxaltDiv15.setPaddingTop(0);
        hBoxaltDiv15.setPaddingLeft(0);
        hBoxaltDiv15.setPaddingRight(0);
        hBoxaltDiv15.setPaddingBottom(0);
        hBoxaltDiv15.setMarginTop(0);
        hBoxaltDiv15.setMarginLeft(0);
        hBoxaltDiv15.setMarginRight(0);
        hBoxaltDiv15.setMarginBottom(0);
        hBoxaltDiv15.setSpacing(1);
        hBoxaltDiv15.setFlexVflex("ftFalse");
        hBoxaltDiv15.setFlexHflex("ftFalse");
        hBoxaltDiv15.setScrollable(false);
        hBoxaltDiv15.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv15.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv15.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv15.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv15.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv15.setBoxShadowConfigOpacity(75);
        vBoxVendedorAlteracao.addChildren(hBoxaltDiv15);
        hBoxaltDiv15.applyProperties();
    }

    public TFLabel lblAltvendedor = new TFLabel();

    private void init_lblAltvendedor() {
        lblAltvendedor.setName("lblAltvendedor");
        lblAltvendedor.setLeft(0);
        lblAltvendedor.setTop(6);
        lblAltvendedor.setWidth(62);
        lblAltvendedor.setHeight(13);
        lblAltvendedor.setCaption("5 - Vendedor");
        lblAltvendedor.setFontColor("clWindowText");
        lblAltvendedor.setFontSize(-11);
        lblAltvendedor.setFontName("Tahoma");
        lblAltvendedor.setFontStyle("[]");
        lblAltvendedor.setVerticalAlignment("taVerticalCenter");
        lblAltvendedor.setWordBreak(false);
        vBoxVendedorAlteracao.addChildren(lblAltvendedor);
        lblAltvendedor.applyProperties();
    }

    public TFVBox hBoxaltDiv16 = new TFVBox();

    private void init_hBoxaltDiv16() {
        hBoxaltDiv16.setName("hBoxaltDiv16");
        hBoxaltDiv16.setLeft(0);
        hBoxaltDiv16.setTop(20);
        hBoxaltDiv16.setWidth(100);
        hBoxaltDiv16.setHeight(5);
        hBoxaltDiv16.setBorderStyle("stNone");
        hBoxaltDiv16.setPaddingTop(0);
        hBoxaltDiv16.setPaddingLeft(0);
        hBoxaltDiv16.setPaddingRight(0);
        hBoxaltDiv16.setPaddingBottom(0);
        hBoxaltDiv16.setMarginTop(0);
        hBoxaltDiv16.setMarginLeft(0);
        hBoxaltDiv16.setMarginRight(0);
        hBoxaltDiv16.setMarginBottom(0);
        hBoxaltDiv16.setSpacing(1);
        hBoxaltDiv16.setFlexVflex("ftFalse");
        hBoxaltDiv16.setFlexHflex("ftFalse");
        hBoxaltDiv16.setScrollable(false);
        hBoxaltDiv16.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv16.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv16.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv16.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv16.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv16.setBoxShadowConfigOpacity(75);
        vBoxVendedorAlteracao.addChildren(hBoxaltDiv16);
        hBoxaltDiv16.applyProperties();
    }

    public TFCombo cboAltVendedor = new TFCombo();

    private void init_cboAltVendedor() {
        cboAltVendedor.setName("cboAltVendedor");
        cboAltVendedor.setLeft(0);
        cboAltVendedor.setTop(26);
        cboAltVendedor.setWidth(140);
        cboAltVendedor.setHeight(21);
        cboAltVendedor.setHint("Vendedor");
        cboAltVendedor.setLookupTable(tbVendedoresCadastro);
        cboAltVendedor.setLookupKey("NOME");
        cboAltVendedor.setLookupDesc("NOME_COMPLETO_LOGIN_EMPRESA");
        cboAltVendedor.setFlex(true);
        cboAltVendedor.setHelpCaption("Vendedor");
        cboAltVendedor.setReadOnly(true);
        cboAltVendedor.setRequired(true);
        cboAltVendedor.setPrompt("Vendedor");
        cboAltVendedor.setConstraintCheckWhen("cwImmediate");
        cboAltVendedor.setConstraintCheckType("ctExpression");
        cboAltVendedor.setConstraintFocusOnError(false);
        cboAltVendedor.setConstraintEnableUI(true);
        cboAltVendedor.setConstraintEnabled(false);
        cboAltVendedor.setConstraintFormCheck(true);
        cboAltVendedor.setClearOnDelKey(false);
        cboAltVendedor.setUseClearButton(false);
        cboAltVendedor.setHideClearButtonOnNullValue(false);
        cboAltVendedor.setEnabled(false);
        cboAltVendedor.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmVendedorResponsavel", "cboAltVendedor", "OnEnter");
        });
        vBoxVendedorAlteracao.addChildren(cboAltVendedor);
        cboAltVendedor.applyProperties();
        addValidatable(cboAltVendedor);
    }

    public TFVBox hBoxaltDiv17 = new TFVBox();

    private void init_hBoxaltDiv17() {
        hBoxaltDiv17.setName("hBoxaltDiv17");
        hBoxaltDiv17.setLeft(0);
        hBoxaltDiv17.setTop(48);
        hBoxaltDiv17.setWidth(100);
        hBoxaltDiv17.setHeight(5);
        hBoxaltDiv17.setBorderStyle("stNone");
        hBoxaltDiv17.setPaddingTop(0);
        hBoxaltDiv17.setPaddingLeft(0);
        hBoxaltDiv17.setPaddingRight(0);
        hBoxaltDiv17.setPaddingBottom(0);
        hBoxaltDiv17.setMarginTop(0);
        hBoxaltDiv17.setMarginLeft(0);
        hBoxaltDiv17.setMarginRight(0);
        hBoxaltDiv17.setMarginBottom(0);
        hBoxaltDiv17.setSpacing(1);
        hBoxaltDiv17.setFlexVflex("ftFalse");
        hBoxaltDiv17.setFlexHflex("ftFalse");
        hBoxaltDiv17.setScrollable(false);
        hBoxaltDiv17.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv17.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv17.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv17.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv17.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv17.setBoxShadowConfigOpacity(75);
        vBoxVendedorAlteracao.addChildren(hBoxaltDiv17);
        hBoxaltDiv17.applyProperties();
    }

    public TFHBox hBoxaltDiv18 = new TFHBox();

    private void init_hBoxaltDiv18() {
        hBoxaltDiv18.setName("hBoxaltDiv18");
        hBoxaltDiv18.setLeft(780);
        hBoxaltDiv18.setTop(0);
        hBoxaltDiv18.setWidth(5);
        hBoxaltDiv18.setHeight(20);
        hBoxaltDiv18.setBorderStyle("stNone");
        hBoxaltDiv18.setPaddingTop(0);
        hBoxaltDiv18.setPaddingLeft(0);
        hBoxaltDiv18.setPaddingRight(0);
        hBoxaltDiv18.setPaddingBottom(0);
        hBoxaltDiv18.setMarginTop(0);
        hBoxaltDiv18.setMarginLeft(0);
        hBoxaltDiv18.setMarginRight(0);
        hBoxaltDiv18.setMarginBottom(0);
        hBoxaltDiv18.setSpacing(1);
        hBoxaltDiv18.setFlexVflex("ftFalse");
        hBoxaltDiv18.setFlexHflex("ftFalse");
        hBoxaltDiv18.setScrollable(false);
        hBoxaltDiv18.setBoxShadowConfigHorizontalLength(10);
        hBoxaltDiv18.setBoxShadowConfigVerticalLength(10);
        hBoxaltDiv18.setBoxShadowConfigBlurRadius(5);
        hBoxaltDiv18.setBoxShadowConfigSpreadRadius(0);
        hBoxaltDiv18.setBoxShadowConfigShadowColor("clBlack");
        hBoxaltDiv18.setBoxShadowConfigOpacity(75);
        hBoxaltDiv18.setVAlign("tvTop");
        hBoxComboBoxAlteracao.addChildren(hBoxaltDiv18);
        hBoxaltDiv18.applyProperties();
    }

    public TFSchema scClienteResponsavel;

    private void init_scClienteResponsavel() {
        scClienteResponsavel = rn.scClienteResponsavel;
        scClienteResponsavel.setName("scClienteResponsavel");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteResponsavel);
        scClienteResponsavel.getTables().add(item0);
        scClienteResponsavel.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void iconClassHelpClick(final Event<Object> event);

    public abstract void cboEmpresaChange(final Event<Object> event);

    public abstract void cboSistemaChange(final Event<Object> event);

    public abstract void tbEmpresasCadastroAfterScroll(final Event<Object> event);

}