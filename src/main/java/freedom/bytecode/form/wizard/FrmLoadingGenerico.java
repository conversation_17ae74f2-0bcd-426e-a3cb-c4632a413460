package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmLoadingGenerico extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.LoadingGenericoRNA rn = null;

    public FrmLoadingGenerico() {
        try {
            rn = (freedom.bytecode.rn.LoadingGenericoRNA) getRN(freedom.bytecode.rn.wizard.LoadingGenericoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_timerValidarEncerramento();
        init_vboxPrincipal();
        init_vboxPrincipalEspaco1();
        init_hboxIcone();
        init_hboxIconeEspaco1();
        init_IconIconeLoading();
        init_hboxIconeEspaco2();
        init_hboxMensagem();
        init_hboxMensagemEspaco1();
        init_lblMensagem();
        init_hboxMensagemEspaco2();
        init_FrmLoadingGenerico();
    }

    public TFTimer timerValidarEncerramento = new TFTimer();

    private void init_timerValidarEncerramento() {
        timerValidarEncerramento.setName("timerValidarEncerramento");
        timerValidarEncerramento.setInterval(5000);
        timerValidarEncerramento.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            timerValidarEncerramentoTimer(event);
            processarFlow("FrmLoadingGenerico", "timerValidarEncerramento", "OnTimer");
        });
        timerValidarEncerramento.setRepeats(true);
        FrmLoadingGenerico.addChildren(timerValidarEncerramento);
        timerValidarEncerramento.applyProperties();
    }

    protected TFForm FrmLoadingGenerico = this;
    private void init_FrmLoadingGenerico() {
        FrmLoadingGenerico.setName("FrmLoadingGenerico");
        FrmLoadingGenerico.setCaption("Loading");
        FrmLoadingGenerico.setClientHeight(145);
        FrmLoadingGenerico.setClientWidth(211);
        FrmLoadingGenerico.setColor("clBtnFace");
        FrmLoadingGenerico.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmLoadingGenerico", "FrmLoadingGenerico", "OnCreate");
        });
        FrmLoadingGenerico.setWOrigem("EhMain");
        FrmLoadingGenerico.setWKey("474014");
        FrmLoadingGenerico.setSpacing(0);
        FrmLoadingGenerico.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(211);
        vboxPrincipal.setHeight(145);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(20);
        vboxPrincipal.setPaddingLeft(20);
        vboxPrincipal.setPaddingRight(20);
        vboxPrincipal.setPaddingBottom(20);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(1);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmLoadingGenerico.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFVBox vboxPrincipalEspaco1 = new TFVBox();

    private void init_vboxPrincipalEspaco1() {
        vboxPrincipalEspaco1.setName("vboxPrincipalEspaco1");
        vboxPrincipalEspaco1.setLeft(0);
        vboxPrincipalEspaco1.setTop(0);
        vboxPrincipalEspaco1.setWidth(63);
        vboxPrincipalEspaco1.setHeight(16);
        vboxPrincipalEspaco1.setBorderStyle("stNone");
        vboxPrincipalEspaco1.setPaddingTop(0);
        vboxPrincipalEspaco1.setPaddingLeft(0);
        vboxPrincipalEspaco1.setPaddingRight(0);
        vboxPrincipalEspaco1.setPaddingBottom(0);
        vboxPrincipalEspaco1.setMarginTop(0);
        vboxPrincipalEspaco1.setMarginLeft(0);
        vboxPrincipalEspaco1.setMarginRight(0);
        vboxPrincipalEspaco1.setMarginBottom(0);
        vboxPrincipalEspaco1.setSpacing(1);
        vboxPrincipalEspaco1.setFlexVflex("ftTrue");
        vboxPrincipalEspaco1.setFlexHflex("ftTrue");
        vboxPrincipalEspaco1.setScrollable(false);
        vboxPrincipalEspaco1.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipalEspaco1.setBoxShadowConfigVerticalLength(10);
        vboxPrincipalEspaco1.setBoxShadowConfigBlurRadius(5);
        vboxPrincipalEspaco1.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipalEspaco1.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipalEspaco1.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(vboxPrincipalEspaco1);
        vboxPrincipalEspaco1.applyProperties();
    }

    public TFHBox hboxIcone = new TFHBox();

    private void init_hboxIcone() {
        hboxIcone.setName("hboxIcone");
        hboxIcone.setLeft(0);
        hboxIcone.setTop(17);
        hboxIcone.setWidth(205);
        hboxIcone.setHeight(73);
        hboxIcone.setBorderStyle("stNone");
        hboxIcone.setPaddingTop(0);
        hboxIcone.setPaddingLeft(0);
        hboxIcone.setPaddingRight(0);
        hboxIcone.setPaddingBottom(0);
        hboxIcone.setMarginTop(0);
        hboxIcone.setMarginLeft(0);
        hboxIcone.setMarginRight(0);
        hboxIcone.setMarginBottom(0);
        hboxIcone.setSpacing(1);
        hboxIcone.setFlexVflex("ftFalse");
        hboxIcone.setFlexHflex("ftTrue");
        hboxIcone.setScrollable(false);
        hboxIcone.setBoxShadowConfigHorizontalLength(10);
        hboxIcone.setBoxShadowConfigVerticalLength(10);
        hboxIcone.setBoxShadowConfigBlurRadius(5);
        hboxIcone.setBoxShadowConfigSpreadRadius(0);
        hboxIcone.setBoxShadowConfigShadowColor("clBlack");
        hboxIcone.setBoxShadowConfigOpacity(75);
        hboxIcone.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxIcone);
        hboxIcone.applyProperties();
    }

    public TFVBox hboxIconeEspaco1 = new TFVBox();

    private void init_hboxIconeEspaco1() {
        hboxIconeEspaco1.setName("hboxIconeEspaco1");
        hboxIconeEspaco1.setLeft(0);
        hboxIconeEspaco1.setTop(0);
        hboxIconeEspaco1.setWidth(69);
        hboxIconeEspaco1.setHeight(69);
        hboxIconeEspaco1.setBorderStyle("stNone");
        hboxIconeEspaco1.setPaddingTop(0);
        hboxIconeEspaco1.setPaddingLeft(0);
        hboxIconeEspaco1.setPaddingRight(0);
        hboxIconeEspaco1.setPaddingBottom(0);
        hboxIconeEspaco1.setMarginTop(0);
        hboxIconeEspaco1.setMarginLeft(0);
        hboxIconeEspaco1.setMarginRight(0);
        hboxIconeEspaco1.setMarginBottom(0);
        hboxIconeEspaco1.setSpacing(1);
        hboxIconeEspaco1.setFlexVflex("ftTrue");
        hboxIconeEspaco1.setFlexHflex("ftTrue");
        hboxIconeEspaco1.setScrollable(false);
        hboxIconeEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxIconeEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxIconeEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxIconeEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxIconeEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxIconeEspaco1.setBoxShadowConfigOpacity(75);
        hboxIcone.addChildren(hboxIconeEspaco1);
        hboxIconeEspaco1.applyProperties();
    }

    public TFIconClass IconIconeLoading = new TFIconClass();

    private void init_IconIconeLoading() {
        IconIconeLoading.setName("IconIconeLoading");
        IconIconeLoading.setLeft(69);
        IconIconeLoading.setTop(0);
        IconIconeLoading.setIconClass("far fa-spinner fa-spin");
        IconIconeLoading.setSize(60);
        IconIconeLoading.setColor("clSilver");
        hboxIcone.addChildren(IconIconeLoading);
        IconIconeLoading.applyProperties();
    }

    public TFVBox hboxIconeEspaco2 = new TFVBox();

    private void init_hboxIconeEspaco2() {
        hboxIconeEspaco2.setName("hboxIconeEspaco2");
        hboxIconeEspaco2.setLeft(129);
        hboxIconeEspaco2.setTop(0);
        hboxIconeEspaco2.setWidth(69);
        hboxIconeEspaco2.setHeight(69);
        hboxIconeEspaco2.setBorderStyle("stNone");
        hboxIconeEspaco2.setPaddingTop(0);
        hboxIconeEspaco2.setPaddingLeft(0);
        hboxIconeEspaco2.setPaddingRight(0);
        hboxIconeEspaco2.setPaddingBottom(0);
        hboxIconeEspaco2.setMarginTop(0);
        hboxIconeEspaco2.setMarginLeft(0);
        hboxIconeEspaco2.setMarginRight(0);
        hboxIconeEspaco2.setMarginBottom(0);
        hboxIconeEspaco2.setSpacing(1);
        hboxIconeEspaco2.setFlexVflex("ftTrue");
        hboxIconeEspaco2.setFlexHflex("ftTrue");
        hboxIconeEspaco2.setScrollable(false);
        hboxIconeEspaco2.setBoxShadowConfigHorizontalLength(10);
        hboxIconeEspaco2.setBoxShadowConfigVerticalLength(10);
        hboxIconeEspaco2.setBoxShadowConfigBlurRadius(5);
        hboxIconeEspaco2.setBoxShadowConfigSpreadRadius(0);
        hboxIconeEspaco2.setBoxShadowConfigShadowColor("clBlack");
        hboxIconeEspaco2.setBoxShadowConfigOpacity(75);
        hboxIcone.addChildren(hboxIconeEspaco2);
        hboxIconeEspaco2.applyProperties();
    }

    public TFHBox hboxMensagem = new TFHBox();

    private void init_hboxMensagem() {
        hboxMensagem.setName("hboxMensagem");
        hboxMensagem.setLeft(0);
        hboxMensagem.setTop(91);
        hboxMensagem.setWidth(204);
        hboxMensagem.setHeight(33);
        hboxMensagem.setBorderStyle("stNone");
        hboxMensagem.setPaddingTop(0);
        hboxMensagem.setPaddingLeft(0);
        hboxMensagem.setPaddingRight(0);
        hboxMensagem.setPaddingBottom(0);
        hboxMensagem.setMarginTop(0);
        hboxMensagem.setMarginLeft(0);
        hboxMensagem.setMarginRight(0);
        hboxMensagem.setMarginBottom(0);
        hboxMensagem.setSpacing(1);
        hboxMensagem.setFlexVflex("ftMin");
        hboxMensagem.setFlexHflex("ftTrue");
        hboxMensagem.setScrollable(false);
        hboxMensagem.setBoxShadowConfigHorizontalLength(10);
        hboxMensagem.setBoxShadowConfigVerticalLength(10);
        hboxMensagem.setBoxShadowConfigBlurRadius(5);
        hboxMensagem.setBoxShadowConfigSpreadRadius(0);
        hboxMensagem.setBoxShadowConfigShadowColor("clBlack");
        hboxMensagem.setBoxShadowConfigOpacity(75);
        hboxMensagem.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxMensagem);
        hboxMensagem.applyProperties();
    }

    public TFVBox hboxMensagemEspaco1 = new TFVBox();

    private void init_hboxMensagemEspaco1() {
        hboxMensagemEspaco1.setName("hboxMensagemEspaco1");
        hboxMensagemEspaco1.setLeft(0);
        hboxMensagemEspaco1.setTop(0);
        hboxMensagemEspaco1.setWidth(63);
        hboxMensagemEspaco1.setHeight(25);
        hboxMensagemEspaco1.setBorderStyle("stNone");
        hboxMensagemEspaco1.setPaddingTop(0);
        hboxMensagemEspaco1.setPaddingLeft(0);
        hboxMensagemEspaco1.setPaddingRight(0);
        hboxMensagemEspaco1.setPaddingBottom(0);
        hboxMensagemEspaco1.setMarginTop(0);
        hboxMensagemEspaco1.setMarginLeft(0);
        hboxMensagemEspaco1.setMarginRight(0);
        hboxMensagemEspaco1.setMarginBottom(0);
        hboxMensagemEspaco1.setSpacing(1);
        hboxMensagemEspaco1.setFlexVflex("ftTrue");
        hboxMensagemEspaco1.setFlexHflex("ftTrue");
        hboxMensagemEspaco1.setScrollable(false);
        hboxMensagemEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxMensagemEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxMensagemEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxMensagemEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxMensagemEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxMensagemEspaco1.setBoxShadowConfigOpacity(75);
        hboxMensagem.addChildren(hboxMensagemEspaco1);
        hboxMensagemEspaco1.applyProperties();
    }

    public TFLabel lblMensagem = new TFLabel();

    private void init_lblMensagem() {
        lblMensagem.setName("lblMensagem");
        lblMensagem.setLeft(63);
        lblMensagem.setTop(0);
        lblMensagem.setWidth(89);
        lblMensagem.setHeight(14);
        lblMensagem.setCaption("Processando...");
        lblMensagem.setFontColor("clGray");
        lblMensagem.setFontSize(-12);
        lblMensagem.setFontName("Tahoma");
        lblMensagem.setFontStyle("[fsBold]");
        lblMensagem.setVerticalAlignment("taVerticalCenter");
        lblMensagem.setWordBreak(true);
        hboxMensagem.addChildren(lblMensagem);
        lblMensagem.applyProperties();
    }

    public TFVBox hboxMensagemEspaco2 = new TFVBox();

    private void init_hboxMensagemEspaco2() {
        hboxMensagemEspaco2.setName("hboxMensagemEspaco2");
        hboxMensagemEspaco2.setLeft(152);
        hboxMensagemEspaco2.setTop(0);
        hboxMensagemEspaco2.setWidth(63);
        hboxMensagemEspaco2.setHeight(24);
        hboxMensagemEspaco2.setBorderStyle("stNone");
        hboxMensagemEspaco2.setPaddingTop(0);
        hboxMensagemEspaco2.setPaddingLeft(0);
        hboxMensagemEspaco2.setPaddingRight(0);
        hboxMensagemEspaco2.setPaddingBottom(0);
        hboxMensagemEspaco2.setMarginTop(0);
        hboxMensagemEspaco2.setMarginLeft(0);
        hboxMensagemEspaco2.setMarginRight(0);
        hboxMensagemEspaco2.setMarginBottom(0);
        hboxMensagemEspaco2.setSpacing(1);
        hboxMensagemEspaco2.setFlexVflex("ftTrue");
        hboxMensagemEspaco2.setFlexHflex("ftTrue");
        hboxMensagemEspaco2.setScrollable(false);
        hboxMensagemEspaco2.setBoxShadowConfigHorizontalLength(10);
        hboxMensagemEspaco2.setBoxShadowConfigVerticalLength(10);
        hboxMensagemEspaco2.setBoxShadowConfigBlurRadius(5);
        hboxMensagemEspaco2.setBoxShadowConfigSpreadRadius(0);
        hboxMensagemEspaco2.setBoxShadowConfigShadowColor("clBlack");
        hboxMensagemEspaco2.setBoxShadowConfigOpacity(75);
        hboxMensagem.addChildren(hboxMensagemEspaco2);
        hboxMensagemEspaco2.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void timerValidarEncerramentoTimer(final Event<Object> event);

}