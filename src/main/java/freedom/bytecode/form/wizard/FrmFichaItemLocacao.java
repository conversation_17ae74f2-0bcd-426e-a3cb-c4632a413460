package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmFichaItemLocacao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.FichaItemLocacaoRNA rn = null;

    public FrmFichaItemLocacao() {
        try {
            rn = (freedom.bytecode.rn.FichaItemLocacaoRNA) getRN(freedom.bytecode.rn.wizard.FichaItemLocacaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbItemLocacaoDispEmp();
        init_tbLocalEstoque();
        init_FHBox1();
        init_btnVoltar();
        init_btnSalvar();
        init_FVBox1();
        init_FHBox3();
        init_comboLocalEstoque();
        init_edtLocacao();
        init_gridLocacao();
        init_FrmFichaItemLocacao();
    }

    public ITEM_LOCACAO_DISP_EMP tbItemLocacaoDispEmp;

    private void init_tbItemLocacaoDispEmp() {
        tbItemLocacaoDispEmp = rn.tbItemLocacaoDispEmp;
        tbItemLocacaoDispEmp.setName("tbItemLocacaoDispEmp");
        tbItemLocacaoDispEmp.setMaxRowCount(0);
        tbItemLocacaoDispEmp.setWKey("310045;31001");
        tbItemLocacaoDispEmp.setRatioBatchSize(20);
        getTables().put(tbItemLocacaoDispEmp, "tbItemLocacaoDispEmp");
        tbItemLocacaoDispEmp.applyProperties();
    }

    public LOCAL_ESTOQUE tbLocalEstoque;

    private void init_tbLocalEstoque() {
        tbLocalEstoque = rn.tbLocalEstoque;
        tbLocalEstoque.setName("tbLocalEstoque");
        tbLocalEstoque.setMaxRowCount(200);
        tbLocalEstoque.setWKey("310045;31002");
        tbLocalEstoque.setRatioBatchSize(20);
        getTables().put(tbLocalEstoque, "tbLocalEstoque");
        tbLocalEstoque.applyProperties();
    }

    protected TFForm FrmFichaItemLocacao = this;
    private void init_FrmFichaItemLocacao() {
        FrmFichaItemLocacao.setName("FrmFichaItemLocacao");
        FrmFichaItemLocacao.setCaption("Selecionar Loca\u00E7\u00E3o");
        FrmFichaItemLocacao.setClientHeight(385);
        FrmFichaItemLocacao.setClientWidth(414);
        FrmFichaItemLocacao.setColor("clBtnFace");
        FrmFichaItemLocacao.setWKey("310045");
        FrmFichaItemLocacao.setSpacing(0);
        FrmFichaItemLocacao.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(414);
        FHBox1.setHeight(61);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(2);
        FHBox1.setMarginLeft(3);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FrmFichaItemLocacao.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(53);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmFichaItemLocacao", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox1.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(65);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmFichaItemLocacao", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934"
 + "31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2"
 + "D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE"
 + "6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5"
 + "6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013"
 + "090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF"
 + "349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E"
 + "8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871"
 + "175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39"
 + "FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622"
 + "F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673"
 + "BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E"
 + "01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146"
 + "0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219"
 + "506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6"
 + "0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991"
 + "CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313"
 + "9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0"
 + "39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D"
 + "AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7"
 + "C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743"
 + "FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE"
 + "E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660"
 + "474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF"
 + "D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61"
 + "33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E"
 + "0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE"
 + "F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF"
 + "921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5"
 + "A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E"
 + "9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163"
 + "8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09"
 + "C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C"
 + "D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E"
 + "23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA"
 + "FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E"
 + "9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1"
 + "5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A"
 + "25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2"
 + "FF02B065C443D9FE4B070000000049454E44AE426082");
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(61);
        FVBox1.setWidth(414);
        FVBox1.setHeight(324);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(3);
        FVBox1.setPaddingRight(3);
        FVBox1.setPaddingBottom(3);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(3);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmFichaItemLocacao.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(399);
        FHBox3.setHeight(33);
        FHBox3.setAlign("alTop");
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(3);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(5);
        FHBox3.setFlexVflex("ftMin");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFCombo comboLocalEstoque = new TFCombo();

    private void init_comboLocalEstoque() {
        comboLocalEstoque.setName("comboLocalEstoque");
        comboLocalEstoque.setLeft(0);
        comboLocalEstoque.setTop(0);
        comboLocalEstoque.setWidth(210);
        comboLocalEstoque.setHeight(21);
        comboLocalEstoque.setLookupTable(tbLocalEstoque);
        comboLocalEstoque.setLookupKey("COD_LOCAL_ESTOQUE");
        comboLocalEstoque.setLookupDesc("INITCAP_NOME_LOCAL");
        comboLocalEstoque.setFlex(true);
        comboLocalEstoque.setReadOnly(true);
        comboLocalEstoque.setRequired(false);
        comboLocalEstoque.setPrompt("Local estoque");
        comboLocalEstoque.setConstraintCheckWhen("cwImmediate");
        comboLocalEstoque.setConstraintCheckType("ctExpression");
        comboLocalEstoque.setConstraintFocusOnError(false);
        comboLocalEstoque.setConstraintEnableUI(true);
        comboLocalEstoque.setConstraintEnabled(false);
        comboLocalEstoque.setConstraintFormCheck(true);
        comboLocalEstoque.setClearOnDelKey(true);
        comboLocalEstoque.setUseClearButton(true);
        comboLocalEstoque.setHideClearButtonOnNullValue(true);
        comboLocalEstoque.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboLocalEstoqueChange(event);
            processarFlow("FrmFichaItemLocacao", "comboLocalEstoque", "OnChange");
        });
        FHBox3.addChildren(comboLocalEstoque);
        comboLocalEstoque.applyProperties();
        addValidatable(comboLocalEstoque);
    }

    public TFString edtLocacao = new TFString();

    private void init_edtLocacao() {
        edtLocacao.setName("edtLocacao");
        edtLocacao.setLeft(210);
        edtLocacao.setTop(0);
        edtLocacao.setWidth(150);
        edtLocacao.setHeight(24);
        edtLocacao.setFlex(true);
        edtLocacao.setRequired(false);
        edtLocacao.setPrompt("Loca\u00E7\u00E3o");
        edtLocacao.setConstraintCheckWhen("cwImmediate");
        edtLocacao.setConstraintCheckType("ctExpression");
        edtLocacao.setConstraintFocusOnError(false);
        edtLocacao.setConstraintEnableUI(true);
        edtLocacao.setConstraintEnabled(false);
        edtLocacao.setConstraintFormCheck(true);
        edtLocacao.setCharCase("ccNormal");
        edtLocacao.setPwd(false);
        edtLocacao.setMaxlength(0);
        edtLocacao.setFontColor("clWindowText");
        edtLocacao.setFontSize(-13);
        edtLocacao.setFontName("Tahoma");
        edtLocacao.setFontStyle("[]");
        edtLocacao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtLocacaoEnter(event);
            processarFlow("FrmFichaItemLocacao", "edtLocacao", "OnEnter");
        });
        edtLocacao.setSaveLiteralCharacter(false);
        edtLocacao.applyProperties();
        FHBox3.addChildren(edtLocacao);
        addValidatable(edtLocacao);
    }

    public TFGrid gridLocacao = new TFGrid();

    private void init_gridLocacao() {
        gridLocacao.setName("gridLocacao");
        gridLocacao.setLeft(0);
        gridLocacao.setTop(34);
        gridLocacao.setWidth(397);
        gridLocacao.setHeight(192);
        gridLocacao.setAlign("alClient");
        gridLocacao.setTable(tbItemLocacaoDispEmp);
        gridLocacao.setFlexVflex("ftTrue");
        gridLocacao.setFlexHflex("ftTrue");
        gridLocacao.setPagingEnabled(false);
        gridLocacao.setFrozenColumns(0);
        gridLocacao.setShowFooter(false);
        gridLocacao.setShowHeader(true);
        gridLocacao.setMultiSelection(false);
        gridLocacao.setGroupingEnabled(false);
        gridLocacao.setGroupingExpanded(false);
        gridLocacao.setGroupingShowFooter(false);
        gridLocacao.setCrosstabEnabled(false);
        gridLocacao.setCrosstabGroupType("cgtConcat");
        gridLocacao.setEditionEnabled(false);
        gridLocacao.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption(" #");
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL='S'");
        item1.setHint("Loca\u00E7\u00E3o selecionada");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridLocacaoselecionar(event);
            processarFlow("FrmFichaItemLocacao", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL='N'");
        item2.setHint("Loca\u00E7\u00E3o n\u00E3o selecionada.");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridLocacaoretirarSelecao(event);
            processarFlow("FrmFichaItemLocacao", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridLocacao.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("NOME_DO_LOCAL");
        item3.setTitleCaption("Local estoque");
        item3.setWidth(164);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridLocacao.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("LOCACAO");
        item4.setTitleCaption("Loca\u00E7\u00E3o");
        item4.setWidth(150);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridLocacao.getColumns().add(item4);
        FVBox1.addChildren(gridLocacao);
        gridLocacao.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void comboLocalEstoqueChange(final Event<Object> event);

    public abstract void edtLocacaoEnter(final Event<Object> event);

    public abstract void gridLocacaoselecionar(final Event<Object> event);

    public abstract void gridLocacaoretirarSelecao(final Event<Object> event);

}