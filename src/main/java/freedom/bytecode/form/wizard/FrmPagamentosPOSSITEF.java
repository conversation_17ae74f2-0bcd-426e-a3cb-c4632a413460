package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPagamentosPOSSITEF extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PagamentosPOSSITEFRNA rn = null;

    public FrmPagamentosPOSSITEF() {
        try {
            rn = (freedom.bytecode.rn.PagamentosPOSSITEFRNA) getRN(freedom.bytecode.rn.wizard.PagamentosPOSSITEFRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresas();
        init_tbPagamentosPosSitef();
        init_tbParcelasPgtoPosSitef();
        init_vBoxPrincipal();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnExcluir();
        init_vBoxEmpresa();
        init_lblEmpresa();
        init_cboEmpresa();
        init_hBoxFiltros();
        init_vBoxCPFCNPJCliente();
        init_lblCPFCNPJCliente();
        init_edtCPFCNPJCliente();
        init_vBoxPesquisarCliente();
        init_hBoxPesquisarClienteSeparador01();
        init_btnPesquisarCliente();
        init_vBoxOrcamentoPreNota();
        init_lblOrcamentoPreNota();
        init_edtOrcamentoPreNota();
        init_vBoxEmissao();
        init_lblEmissao();
        init_dtEmissaoLead();
        init_vBoxPesquisar();
        init_hBoxPesquisarSeparador01();
        init_btnPesquisar();
        init_lblPagamentoConfirmadoSNF();
        init_grdPagamentos();
        init_lblParcelas();
        init_grdParcelas();
        init_FrmPagamentosPOSSITEF();
    }

    public LEADS_EMPRESAS_USUARIOS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("53601464;53601");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public BUSCA_PAGAMENTOS_POS_SITEF tbPagamentosPosSitef;

    private void init_tbPagamentosPosSitef() {
        tbPagamentosPosSitef = rn.tbPagamentosPosSitef;
        tbPagamentosPosSitef.setName("tbPagamentosPosSitef");
        tbPagamentosPosSitef.setMaxRowCount(200);
        tbPagamentosPosSitef.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbPagamentosPosSitefAfterScroll(event);
            processarFlow("FrmPagamentosPOSSITEF", "tbPagamentosPosSitef", "OnAfterScroll");
        });
        tbPagamentosPosSitef.setWKey("53601464;53602");
        tbPagamentosPosSitef.setRatioBatchSize(20);
        getTables().put(tbPagamentosPosSitef, "tbPagamentosPosSitef");
        tbPagamentosPosSitef.applyProperties();
    }

    public BUSCA_PARCELAS_PGTO_POS_SITEF tbParcelasPgtoPosSitef;

    private void init_tbParcelasPgtoPosSitef() {
        tbParcelasPgtoPosSitef = rn.tbParcelasPgtoPosSitef;
        tbParcelasPgtoPosSitef.setName("tbParcelasPgtoPosSitef");
        tbParcelasPgtoPosSitef.setMaxRowCount(200);
        tbParcelasPgtoPosSitef.setWKey("53601464;53603");
        tbParcelasPgtoPosSitef.setRatioBatchSize(20);
        getTables().put(tbParcelasPgtoPosSitef, "tbParcelasPgtoPosSitef");
        tbParcelasPgtoPosSitef.applyProperties();
    }

    protected TFForm FrmPagamentosPOSSITEF = this;
    private void init_FrmPagamentosPOSSITEF() {
        FrmPagamentosPOSSITEF.setName("FrmPagamentosPOSSITEF");
        FrmPagamentosPOSSITEF.setCaption("POS Sitef");
        FrmPagamentosPOSSITEF.setClientHeight(561);
        FrmPagamentosPOSSITEF.setClientWidth(784);
        FrmPagamentosPOSSITEF.setColor("clBtnFace");
        FrmPagamentosPOSSITEF.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmPagamentosPOSSITEF", "FrmPagamentosPOSSITEF", "OnCreate");
        });
        FrmPagamentosPOSSITEF.setWOrigem("EhMain");
        FrmPagamentosPOSSITEF.setWKey("53601464");
        FrmPagamentosPOSSITEF.setSpacing(0);
        FrmPagamentosPOSSITEF.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(784);
        vBoxPrincipal.setHeight(561);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(5);
        vBoxPrincipal.setPaddingLeft(5);
        vBoxPrincipal.setPaddingRight(5);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(5);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmPagamentosPOSSITEF.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(350);
        hBoxBotoes.setHeight(70);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(60);
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmPagamentosPOSSITEF", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(60);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(60);
        btnExcluir.setHeight(60);
        btnExcluir.setHint("Acesso \"K0630\"");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.setVisible(false);
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmPagamentosPOSSITEF", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(4600385);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFVBox vBoxEmpresa = new TFVBox();

    private void init_vBoxEmpresa() {
        vBoxEmpresa.setName("vBoxEmpresa");
        vBoxEmpresa.setLeft(0);
        vBoxEmpresa.setTop(71);
        vBoxEmpresa.setWidth(160);
        vBoxEmpresa.setHeight(40);
        vBoxEmpresa.setBorderStyle("stNone");
        vBoxEmpresa.setPaddingTop(0);
        vBoxEmpresa.setPaddingLeft(0);
        vBoxEmpresa.setPaddingRight(0);
        vBoxEmpresa.setPaddingBottom(0);
        vBoxEmpresa.setMarginTop(0);
        vBoxEmpresa.setMarginLeft(0);
        vBoxEmpresa.setMarginRight(0);
        vBoxEmpresa.setMarginBottom(0);
        vBoxEmpresa.setSpacing(1);
        vBoxEmpresa.setFlexVflex("ftMin");
        vBoxEmpresa.setFlexHflex("ftTrue");
        vBoxEmpresa.setScrollable(false);
        vBoxEmpresa.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresa.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresa.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresa.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresa.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresa.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxEmpresa);
        vBoxEmpresa.applyProperties();
    }

    public TFLabel lblEmpresa = new TFLabel();

    private void init_lblEmpresa() {
        lblEmpresa.setName("lblEmpresa");
        lblEmpresa.setLeft(0);
        lblEmpresa.setTop(0);
        lblEmpresa.setWidth(41);
        lblEmpresa.setHeight(13);
        lblEmpresa.setCaption("Empresa");
        lblEmpresa.setFontColor("clWindowText");
        lblEmpresa.setFontSize(-11);
        lblEmpresa.setFontName("Tahoma");
        lblEmpresa.setFontStyle("[]");
        lblEmpresa.setVerticalAlignment("taVerticalCenter");
        lblEmpresa.setWordBreak(false);
        vBoxEmpresa.addChildren(lblEmpresa);
        lblEmpresa.applyProperties();
    }

    public TFCombo cboEmpresa = new TFCombo();

    private void init_cboEmpresa() {
        cboEmpresa.setName("cboEmpresa");
        cboEmpresa.setLeft(0);
        cboEmpresa.setTop(14);
        cboEmpresa.setWidth(150);
        cboEmpresa.setHeight(21);
        cboEmpresa.setHint("Empresa");
        cboEmpresa.setLookupTable(tbEmpresas);
        cboEmpresa.setLookupKey("COD_EMPRESA");
        cboEmpresa.setLookupDesc("EMPRESA");
        cboEmpresa.setFlex(true);
        cboEmpresa.setHelpCaption("Empresa");
        cboEmpresa.setReadOnly(true);
        cboEmpresa.setRequired(false);
        cboEmpresa.setPrompt("Empresa");
        cboEmpresa.setConstraintCheckWhen("cwImmediate");
        cboEmpresa.setConstraintCheckType("ctExpression");
        cboEmpresa.setConstraintFocusOnError(false);
        cboEmpresa.setConstraintEnableUI(true);
        cboEmpresa.setConstraintEnabled(false);
        cboEmpresa.setConstraintFormCheck(true);
        cboEmpresa.setClearOnDelKey(false);
        cboEmpresa.setUseClearButton(false);
        cboEmpresa.setHideClearButtonOnNullValue(true);
        vBoxEmpresa.addChildren(cboEmpresa);
        cboEmpresa.applyProperties();
        addValidatable(cboEmpresa);
    }

    public TFHBox hBoxFiltros = new TFHBox();

    private void init_hBoxFiltros() {
        hBoxFiltros.setName("hBoxFiltros");
        hBoxFiltros.setLeft(0);
        hBoxFiltros.setTop(112);
        hBoxFiltros.setWidth(780);
        hBoxFiltros.setHeight(60);
        hBoxFiltros.setBorderStyle("stNone");
        hBoxFiltros.setPaddingTop(0);
        hBoxFiltros.setPaddingLeft(0);
        hBoxFiltros.setPaddingRight(0);
        hBoxFiltros.setPaddingBottom(0);
        hBoxFiltros.setMarginTop(0);
        hBoxFiltros.setMarginLeft(0);
        hBoxFiltros.setMarginRight(0);
        hBoxFiltros.setMarginBottom(0);
        hBoxFiltros.setSpacing(5);
        hBoxFiltros.setFlexVflex("ftMin");
        hBoxFiltros.setFlexHflex("ftTrue");
        hBoxFiltros.setScrollable(false);
        hBoxFiltros.setBoxShadowConfigHorizontalLength(10);
        hBoxFiltros.setBoxShadowConfigVerticalLength(10);
        hBoxFiltros.setBoxShadowConfigBlurRadius(5);
        hBoxFiltros.setBoxShadowConfigSpreadRadius(0);
        hBoxFiltros.setBoxShadowConfigShadowColor("clBlack");
        hBoxFiltros.setBoxShadowConfigOpacity(75);
        hBoxFiltros.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxFiltros);
        hBoxFiltros.applyProperties();
    }

    public TFVBox vBoxCPFCNPJCliente = new TFVBox();

    private void init_vBoxCPFCNPJCliente() {
        vBoxCPFCNPJCliente.setName("vBoxCPFCNPJCliente");
        vBoxCPFCNPJCliente.setLeft(0);
        vBoxCPFCNPJCliente.setTop(0);
        vBoxCPFCNPJCliente.setWidth(130);
        vBoxCPFCNPJCliente.setHeight(45);
        vBoxCPFCNPJCliente.setBorderStyle("stNone");
        vBoxCPFCNPJCliente.setPaddingTop(0);
        vBoxCPFCNPJCliente.setPaddingLeft(0);
        vBoxCPFCNPJCliente.setPaddingRight(0);
        vBoxCPFCNPJCliente.setPaddingBottom(0);
        vBoxCPFCNPJCliente.setMarginTop(0);
        vBoxCPFCNPJCliente.setMarginLeft(0);
        vBoxCPFCNPJCliente.setMarginRight(0);
        vBoxCPFCNPJCliente.setMarginBottom(0);
        vBoxCPFCNPJCliente.setSpacing(1);
        vBoxCPFCNPJCliente.setFlexVflex("ftMin");
        vBoxCPFCNPJCliente.setFlexHflex("ftTrue");
        vBoxCPFCNPJCliente.setScrollable(false);
        vBoxCPFCNPJCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxCPFCNPJCliente.setBoxShadowConfigVerticalLength(10);
        vBoxCPFCNPJCliente.setBoxShadowConfigBlurRadius(5);
        vBoxCPFCNPJCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxCPFCNPJCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxCPFCNPJCliente.setBoxShadowConfigOpacity(75);
        hBoxFiltros.addChildren(vBoxCPFCNPJCliente);
        vBoxCPFCNPJCliente.applyProperties();
    }

    public TFLabel lblCPFCNPJCliente = new TFLabel();

    private void init_lblCPFCNPJCliente() {
        lblCPFCNPJCliente.setName("lblCPFCNPJCliente");
        lblCPFCNPJCliente.setLeft(0);
        lblCPFCNPJCliente.setTop(0);
        lblCPFCNPJCliente.setWidth(84);
        lblCPFCNPJCliente.setHeight(13);
        lblCPFCNPJCliente.setCaption("CPF/CNPJ Cliente");
        lblCPFCNPJCliente.setFontColor("clWindowText");
        lblCPFCNPJCliente.setFontSize(-11);
        lblCPFCNPJCliente.setFontName("Tahoma");
        lblCPFCNPJCliente.setFontStyle("[]");
        lblCPFCNPJCliente.setVerticalAlignment("taVerticalCenter");
        lblCPFCNPJCliente.setWordBreak(false);
        vBoxCPFCNPJCliente.addChildren(lblCPFCNPJCliente);
        lblCPFCNPJCliente.applyProperties();
    }

    public TFString edtCPFCNPJCliente = new TFString();

    private void init_edtCPFCNPJCliente() {
        edtCPFCNPJCliente.setName("edtCPFCNPJCliente");
        edtCPFCNPJCliente.setLeft(0);
        edtCPFCNPJCliente.setTop(14);
        edtCPFCNPJCliente.setWidth(120);
        edtCPFCNPJCliente.setHeight(24);
        edtCPFCNPJCliente.setHint("CPF/CNPJ Cliente");
        edtCPFCNPJCliente.setHelpCaption("CPF/CNPJ Cliente");
        edtCPFCNPJCliente.setFlex(true);
        edtCPFCNPJCliente.setRequired(false);
        edtCPFCNPJCliente.setPrompt("CPF/CNPJ Cliente");
        edtCPFCNPJCliente.setConstraintCheckWhen("cwImmediate");
        edtCPFCNPJCliente.setConstraintCheckType("ctExpression");
        edtCPFCNPJCliente.setConstraintFocusOnError(false);
        edtCPFCNPJCliente.setConstraintEnableUI(true);
        edtCPFCNPJCliente.setConstraintEnabled(false);
        edtCPFCNPJCliente.setConstraintFormCheck(true);
        edtCPFCNPJCliente.setCharCase("ccNormal");
        edtCPFCNPJCliente.setPwd(false);
        edtCPFCNPJCliente.setMaxlength(0);
        edtCPFCNPJCliente.setFontColor("clWindowText");
        edtCPFCNPJCliente.setFontSize(-13);
        edtCPFCNPJCliente.setFontName("Tahoma");
        edtCPFCNPJCliente.setFontStyle("[]");
        edtCPFCNPJCliente.setAlignment("taRightJustify");
        edtCPFCNPJCliente.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCPFCNPJClienteEnter(event);
            processarFlow("FrmPagamentosPOSSITEF", "edtCPFCNPJCliente", "OnEnter");
        });
        edtCPFCNPJCliente.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCPFCNPJClienteExit(event);
            processarFlow("FrmPagamentosPOSSITEF", "edtCPFCNPJCliente", "OnExit");
        });
        edtCPFCNPJCliente.setSaveLiteralCharacter(false);
        edtCPFCNPJCliente.applyProperties();
        vBoxCPFCNPJCliente.addChildren(edtCPFCNPJCliente);
        addValidatable(edtCPFCNPJCliente);
    }

    public TFVBox vBoxPesquisarCliente = new TFVBox();

    private void init_vBoxPesquisarCliente() {
        vBoxPesquisarCliente.setName("vBoxPesquisarCliente");
        vBoxPesquisarCliente.setLeft(130);
        vBoxPesquisarCliente.setTop(0);
        vBoxPesquisarCliente.setWidth(30);
        vBoxPesquisarCliente.setHeight(45);
        vBoxPesquisarCliente.setBorderStyle("stNone");
        vBoxPesquisarCliente.setPaddingTop(0);
        vBoxPesquisarCliente.setPaddingLeft(0);
        vBoxPesquisarCliente.setPaddingRight(0);
        vBoxPesquisarCliente.setPaddingBottom(0);
        vBoxPesquisarCliente.setMarginTop(0);
        vBoxPesquisarCliente.setMarginLeft(0);
        vBoxPesquisarCliente.setMarginRight(0);
        vBoxPesquisarCliente.setMarginBottom(0);
        vBoxPesquisarCliente.setSpacing(1);
        vBoxPesquisarCliente.setFlexVflex("ftMin");
        vBoxPesquisarCliente.setFlexHflex("ftMin");
        vBoxPesquisarCliente.setScrollable(false);
        vBoxPesquisarCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxPesquisarCliente.setBoxShadowConfigVerticalLength(10);
        vBoxPesquisarCliente.setBoxShadowConfigBlurRadius(5);
        vBoxPesquisarCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxPesquisarCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxPesquisarCliente.setBoxShadowConfigOpacity(75);
        hBoxFiltros.addChildren(vBoxPesquisarCliente);
        vBoxPesquisarCliente.applyProperties();
    }

    public TFHBox hBoxPesquisarClienteSeparador01 = new TFHBox();

    private void init_hBoxPesquisarClienteSeparador01() {
        hBoxPesquisarClienteSeparador01.setName("hBoxPesquisarClienteSeparador01");
        hBoxPesquisarClienteSeparador01.setLeft(0);
        hBoxPesquisarClienteSeparador01.setTop(0);
        hBoxPesquisarClienteSeparador01.setWidth(13);
        hBoxPesquisarClienteSeparador01.setHeight(13);
        hBoxPesquisarClienteSeparador01.setBorderStyle("stNone");
        hBoxPesquisarClienteSeparador01.setPaddingTop(0);
        hBoxPesquisarClienteSeparador01.setPaddingLeft(0);
        hBoxPesquisarClienteSeparador01.setPaddingRight(0);
        hBoxPesquisarClienteSeparador01.setPaddingBottom(0);
        hBoxPesquisarClienteSeparador01.setMarginTop(0);
        hBoxPesquisarClienteSeparador01.setMarginLeft(0);
        hBoxPesquisarClienteSeparador01.setMarginRight(0);
        hBoxPesquisarClienteSeparador01.setMarginBottom(0);
        hBoxPesquisarClienteSeparador01.setSpacing(1);
        hBoxPesquisarClienteSeparador01.setFlexVflex("ftFalse");
        hBoxPesquisarClienteSeparador01.setFlexHflex("ftFalse");
        hBoxPesquisarClienteSeparador01.setScrollable(false);
        hBoxPesquisarClienteSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxPesquisarClienteSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxPesquisarClienteSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxPesquisarClienteSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxPesquisarClienteSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxPesquisarClienteSeparador01.setBoxShadowConfigOpacity(75);
        hBoxPesquisarClienteSeparador01.setVAlign("tvTop");
        vBoxPesquisarCliente.addChildren(hBoxPesquisarClienteSeparador01);
        hBoxPesquisarClienteSeparador01.applyProperties();
    }

    public TFButton btnPesquisarCliente = new TFButton();

    private void init_btnPesquisarCliente() {
        btnPesquisarCliente.setName("btnPesquisarCliente");
        btnPesquisarCliente.setLeft(0);
        btnPesquisarCliente.setTop(14);
        btnPesquisarCliente.setWidth(24);
        btnPesquisarCliente.setHeight(24);
        btnPesquisarCliente.setHint("Pesquisar cliente");
        btnPesquisarCliente.setFontColor("clWindowText");
        btnPesquisarCliente.setFontSize(-11);
        btnPesquisarCliente.setFontName("Tahoma");
        btnPesquisarCliente.setFontStyle("[]");
        btnPesquisarCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClienteClick(event);
            processarFlow("FrmPagamentosPOSSITEF", "btnPesquisarCliente", "OnClick");
        });
        btnPesquisarCliente.setImageId(0);
        btnPesquisarCliente.setColor("clBtnFace");
        btnPesquisarCliente.setAccess(false);
        btnPesquisarCliente.setIconClass("search");
        btnPesquisarCliente.setIconReverseDirection(false);
        vBoxPesquisarCliente.addChildren(btnPesquisarCliente);
        btnPesquisarCliente.applyProperties();
    }

    public TFVBox vBoxOrcamentoPreNota = new TFVBox();

    private void init_vBoxOrcamentoPreNota() {
        vBoxOrcamentoPreNota.setName("vBoxOrcamentoPreNota");
        vBoxOrcamentoPreNota.setLeft(160);
        vBoxOrcamentoPreNota.setTop(0);
        vBoxOrcamentoPreNota.setWidth(185);
        vBoxOrcamentoPreNota.setHeight(45);
        vBoxOrcamentoPreNota.setBorderStyle("stNone");
        vBoxOrcamentoPreNota.setPaddingTop(0);
        vBoxOrcamentoPreNota.setPaddingLeft(0);
        vBoxOrcamentoPreNota.setPaddingRight(0);
        vBoxOrcamentoPreNota.setPaddingBottom(0);
        vBoxOrcamentoPreNota.setMarginTop(0);
        vBoxOrcamentoPreNota.setMarginLeft(0);
        vBoxOrcamentoPreNota.setMarginRight(0);
        vBoxOrcamentoPreNota.setMarginBottom(0);
        vBoxOrcamentoPreNota.setSpacing(1);
        vBoxOrcamentoPreNota.setFlexVflex("ftMin");
        vBoxOrcamentoPreNota.setFlexHflex("ftTrue");
        vBoxOrcamentoPreNota.setScrollable(false);
        vBoxOrcamentoPreNota.setBoxShadowConfigHorizontalLength(10);
        vBoxOrcamentoPreNota.setBoxShadowConfigVerticalLength(10);
        vBoxOrcamentoPreNota.setBoxShadowConfigBlurRadius(5);
        vBoxOrcamentoPreNota.setBoxShadowConfigSpreadRadius(0);
        vBoxOrcamentoPreNota.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrcamentoPreNota.setBoxShadowConfigOpacity(75);
        hBoxFiltros.addChildren(vBoxOrcamentoPreNota);
        vBoxOrcamentoPreNota.applyProperties();
    }

    public TFLabel lblOrcamentoPreNota = new TFLabel();

    private void init_lblOrcamentoPreNota() {
        lblOrcamentoPreNota.setName("lblOrcamentoPreNota");
        lblOrcamentoPreNota.setLeft(0);
        lblOrcamentoPreNota.setTop(0);
        lblOrcamentoPreNota.setWidth(106);
        lblOrcamentoPreNota.setHeight(13);
        lblOrcamentoPreNota.setCaption("Or\u00E7amento / Pr\u00E9-Nota");
        lblOrcamentoPreNota.setFontColor("clWindowText");
        lblOrcamentoPreNota.setFontSize(-11);
        lblOrcamentoPreNota.setFontName("Tahoma");
        lblOrcamentoPreNota.setFontStyle("[]");
        lblOrcamentoPreNota.setVerticalAlignment("taVerticalCenter");
        lblOrcamentoPreNota.setWordBreak(false);
        vBoxOrcamentoPreNota.addChildren(lblOrcamentoPreNota);
        lblOrcamentoPreNota.applyProperties();
    }

    public TFInteger edtOrcamentoPreNota = new TFInteger();

    private void init_edtOrcamentoPreNota() {
        edtOrcamentoPreNota.setName("edtOrcamentoPreNota");
        edtOrcamentoPreNota.setLeft(0);
        edtOrcamentoPreNota.setTop(14);
        edtOrcamentoPreNota.setWidth(120);
        edtOrcamentoPreNota.setHeight(24);
        edtOrcamentoPreNota.setHint("Or\u00E7amento / Pr\u00E9-Nota");
        edtOrcamentoPreNota.setHelpCaption("Or\u00E7amento / Pr\u00E9-Nota");
        edtOrcamentoPreNota.setFlex(true);
        edtOrcamentoPreNota.setRequired(false);
        edtOrcamentoPreNota.setPrompt("Or\u00E7amento / Pr\u00E9-Nota");
        edtOrcamentoPreNota.setConstraintCheckWhen("cwImmediate");
        edtOrcamentoPreNota.setConstraintCheckType("ctExpression");
        edtOrcamentoPreNota.setConstraintFocusOnError(false);
        edtOrcamentoPreNota.setConstraintEnableUI(true);
        edtOrcamentoPreNota.setConstraintEnabled(false);
        edtOrcamentoPreNota.setConstraintFormCheck(true);
        edtOrcamentoPreNota.setMaxlength(0);
        edtOrcamentoPreNota.setFontColor("clWindowText");
        edtOrcamentoPreNota.setFontSize(-13);
        edtOrcamentoPreNota.setFontName("Tahoma");
        edtOrcamentoPreNota.setFontStyle("[]");
        edtOrcamentoPreNota.setAlignment("taRightJustify");
        edtOrcamentoPreNota.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtOrcamentoPreNotaEnter(event);
            processarFlow("FrmPagamentosPOSSITEF", "edtOrcamentoPreNota", "OnEnter");
        });
        vBoxOrcamentoPreNota.addChildren(edtOrcamentoPreNota);
        edtOrcamentoPreNota.applyProperties();
        addValidatable(edtOrcamentoPreNota);
    }

    public TFVBox vBoxEmissao = new TFVBox();

    private void init_vBoxEmissao() {
        vBoxEmissao.setName("vBoxEmissao");
        vBoxEmissao.setLeft(345);
        vBoxEmissao.setTop(0);
        vBoxEmissao.setWidth(160);
        vBoxEmissao.setHeight(45);
        vBoxEmissao.setBorderStyle("stNone");
        vBoxEmissao.setPaddingTop(0);
        vBoxEmissao.setPaddingLeft(0);
        vBoxEmissao.setPaddingRight(0);
        vBoxEmissao.setPaddingBottom(0);
        vBoxEmissao.setMarginTop(0);
        vBoxEmissao.setMarginLeft(0);
        vBoxEmissao.setMarginRight(0);
        vBoxEmissao.setMarginBottom(0);
        vBoxEmissao.setSpacing(1);
        vBoxEmissao.setFlexVflex("ftMin");
        vBoxEmissao.setFlexHflex("ftMin");
        vBoxEmissao.setScrollable(false);
        vBoxEmissao.setBoxShadowConfigHorizontalLength(10);
        vBoxEmissao.setBoxShadowConfigVerticalLength(10);
        vBoxEmissao.setBoxShadowConfigBlurRadius(5);
        vBoxEmissao.setBoxShadowConfigSpreadRadius(0);
        vBoxEmissao.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmissao.setBoxShadowConfigOpacity(75);
        hBoxFiltros.addChildren(vBoxEmissao);
        vBoxEmissao.applyProperties();
    }

    public TFLabel lblEmissao = new TFLabel();

    private void init_lblEmissao() {
        lblEmissao.setName("lblEmissao");
        lblEmissao.setLeft(0);
        lblEmissao.setTop(0);
        lblEmissao.setWidth(38);
        lblEmissao.setHeight(13);
        lblEmissao.setCaption("Emiss\u00E3o");
        lblEmissao.setFontColor("clWindowText");
        lblEmissao.setFontSize(-11);
        lblEmissao.setFontName("Tahoma");
        lblEmissao.setFontStyle("[]");
        lblEmissao.setVerticalAlignment("taVerticalCenter");
        lblEmissao.setWordBreak(false);
        vBoxEmissao.addChildren(lblEmissao);
        lblEmissao.applyProperties();
    }

    public TFDate dtEmissaoLead = new TFDate();

    private void init_dtEmissaoLead() {
        dtEmissaoLead.setName("dtEmissaoLead");
        dtEmissaoLead.setLeft(0);
        dtEmissaoLead.setTop(14);
        dtEmissaoLead.setWidth(150);
        dtEmissaoLead.setHeight(24);
        dtEmissaoLead.setHint("Emiss\u00E3o");
        dtEmissaoLead.setHelpCaption("Emiss\u00E3o");
        dtEmissaoLead.setFlex(false);
        dtEmissaoLead.setRequired(false);
        dtEmissaoLead.setPrompt("Emiss\u00E3o");
        dtEmissaoLead.setConstraintCheckWhen("cwImmediate");
        dtEmissaoLead.setConstraintCheckType("ctExpression");
        dtEmissaoLead.setConstraintFocusOnError(false);
        dtEmissaoLead.setConstraintEnableUI(true);
        dtEmissaoLead.setConstraintEnabled(false);
        dtEmissaoLead.setConstraintFormCheck(true);
        dtEmissaoLead.setFormat("dd/MM/yyyy");
        dtEmissaoLead.setShowCheckBox(false);
        dtEmissaoLead.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            dtEmissaoLeadEnter(event);
            processarFlow("FrmPagamentosPOSSITEF", "dtEmissaoLead", "OnEnter");
        });
        vBoxEmissao.addChildren(dtEmissaoLead);
        dtEmissaoLead.applyProperties();
        addValidatable(dtEmissaoLead);
    }

    public TFVBox vBoxPesquisar = new TFVBox();

    private void init_vBoxPesquisar() {
        vBoxPesquisar.setName("vBoxPesquisar");
        vBoxPesquisar.setLeft(505);
        vBoxPesquisar.setTop(0);
        vBoxPesquisar.setWidth(90);
        vBoxPesquisar.setHeight(45);
        vBoxPesquisar.setBorderStyle("stNone");
        vBoxPesquisar.setPaddingTop(0);
        vBoxPesquisar.setPaddingLeft(0);
        vBoxPesquisar.setPaddingRight(0);
        vBoxPesquisar.setPaddingBottom(0);
        vBoxPesquisar.setMarginTop(0);
        vBoxPesquisar.setMarginLeft(0);
        vBoxPesquisar.setMarginRight(0);
        vBoxPesquisar.setMarginBottom(0);
        vBoxPesquisar.setSpacing(1);
        vBoxPesquisar.setFlexVflex("ftMin");
        vBoxPesquisar.setFlexHflex("ftMin");
        vBoxPesquisar.setScrollable(false);
        vBoxPesquisar.setBoxShadowConfigHorizontalLength(10);
        vBoxPesquisar.setBoxShadowConfigVerticalLength(10);
        vBoxPesquisar.setBoxShadowConfigBlurRadius(5);
        vBoxPesquisar.setBoxShadowConfigSpreadRadius(0);
        vBoxPesquisar.setBoxShadowConfigShadowColor("clBlack");
        vBoxPesquisar.setBoxShadowConfigOpacity(75);
        hBoxFiltros.addChildren(vBoxPesquisar);
        vBoxPesquisar.applyProperties();
    }

    public TFHBox hBoxPesquisarSeparador01 = new TFHBox();

    private void init_hBoxPesquisarSeparador01() {
        hBoxPesquisarSeparador01.setName("hBoxPesquisarSeparador01");
        hBoxPesquisarSeparador01.setLeft(0);
        hBoxPesquisarSeparador01.setTop(0);
        hBoxPesquisarSeparador01.setWidth(20);
        hBoxPesquisarSeparador01.setHeight(13);
        hBoxPesquisarSeparador01.setBorderStyle("stNone");
        hBoxPesquisarSeparador01.setPaddingTop(0);
        hBoxPesquisarSeparador01.setPaddingLeft(0);
        hBoxPesquisarSeparador01.setPaddingRight(0);
        hBoxPesquisarSeparador01.setPaddingBottom(0);
        hBoxPesquisarSeparador01.setMarginTop(0);
        hBoxPesquisarSeparador01.setMarginLeft(0);
        hBoxPesquisarSeparador01.setMarginRight(0);
        hBoxPesquisarSeparador01.setMarginBottom(0);
        hBoxPesquisarSeparador01.setSpacing(1);
        hBoxPesquisarSeparador01.setFlexVflex("ftFalse");
        hBoxPesquisarSeparador01.setFlexHflex("ftFalse");
        hBoxPesquisarSeparador01.setScrollable(false);
        hBoxPesquisarSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxPesquisarSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxPesquisarSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxPesquisarSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxPesquisarSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxPesquisarSeparador01.setBoxShadowConfigOpacity(75);
        hBoxPesquisarSeparador01.setVAlign("tvTop");
        vBoxPesquisar.addChildren(hBoxPesquisarSeparador01);
        hBoxPesquisarSeparador01.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(0);
        btnPesquisar.setTop(14);
        btnPesquisar.setWidth(80);
        btnPesquisar.setHeight(25);
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmPagamentosPOSSITEF", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(0);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconClass("search");
        btnPesquisar.setIconReverseDirection(false);
        vBoxPesquisar.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFLabel lblPagamentoConfirmadoSNF = new TFLabel();

    private void init_lblPagamentoConfirmadoSNF() {
        lblPagamentoConfirmadoSNF.setName("lblPagamentoConfirmadoSNF");
        lblPagamentoConfirmadoSNF.setLeft(0);
        lblPagamentoConfirmadoSNF.setTop(173);
        lblPagamentoConfirmadoSNF.setWidth(190);
        lblPagamentoConfirmadoSNF.setHeight(13);
        lblPagamentoConfirmadoSNF.setCaption("Pagamento Confirmado Sem Nota Fiscal");
        lblPagamentoConfirmadoSNF.setFontColor("clWindowText");
        lblPagamentoConfirmadoSNF.setFontSize(-11);
        lblPagamentoConfirmadoSNF.setFontName("Tahoma");
        lblPagamentoConfirmadoSNF.setFontStyle("[]");
        lblPagamentoConfirmadoSNF.setVerticalAlignment("taVerticalCenter");
        lblPagamentoConfirmadoSNF.setWordBreak(false);
        vBoxPrincipal.addChildren(lblPagamentoConfirmadoSNF);
        lblPagamentoConfirmadoSNF.applyProperties();
    }

    public TFGrid grdPagamentos = new TFGrid();

    private void init_grdPagamentos() {
        grdPagamentos.setName("grdPagamentos");
        grdPagamentos.setLeft(0);
        grdPagamentos.setTop(187);
        grdPagamentos.setWidth(770);
        grdPagamentos.setHeight(120);
        grdPagamentos.setTable(tbPagamentosPosSitef);
        grdPagamentos.setFlexVflex("ftTrue");
        grdPagamentos.setFlexHflex("ftTrue");
        grdPagamentos.setPagingEnabled(false);
        grdPagamentos.setFrozenColumns(0);
        grdPagamentos.setShowFooter(false);
        grdPagamentos.setShowHeader(true);
        grdPagamentos.setMultiSelection(false);
        grdPagamentos.setGroupingEnabled(false);
        grdPagamentos.setGroupingExpanded(false);
        grdPagamentos.setGroupingShowFooter(false);
        grdPagamentos.setCrosstabEnabled(false);
        grdPagamentos.setCrosstabGroupType("cgtConcat");
        grdPagamentos.setEditionEnabled(false);
        grdPagamentos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("EMPRESA_NOME_CODIGO");
        item0.setTitleCaption("Empresa");
        item0.setWidth(300);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdPagamentos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("COD_ORC_MAPA");
        item1.setTitleCaption("Or\u00E7amento / Pr\u00E9-Nota");
        item1.setWidth(140);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taRight");
        item1.setFieldType("ftInteger");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        TFMaskExpression item2 = new TFMaskExpression();
        item2.setExpression("*");
        item2.setEvalType("etExpression");
        item2.setMask(",##0");
        item2.setPadLength(0);
        item2.setPadDirection("pdNone");
        item2.setMaskType("mtDecimal");
        item1.getMasks().add(item2);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdPagamentos.getColumns().add(item1);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("EMISSAO_DATA");
        item3.setTitleCaption("Emiss\u00E3o");
        item3.setWidth(140);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftDateTime");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("dd/MM/yyyy HH:mm:ss");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDateTime");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdPagamentos.getColumns().add(item3);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("CLIENTE_NOME_CODIGO");
        item5.setTitleCaption("Cliente");
        item5.setWidth(200);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(true);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdPagamentos.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("STATUS_DESCRICAO_CODIGO");
        item6.setTitleCaption("Status");
        item6.setWidth(100);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdPagamentos.getColumns().add(item6);
        vBoxPrincipal.addChildren(grdPagamentos);
        grdPagamentos.applyProperties();
    }

    public TFLabel lblParcelas = new TFLabel();

    private void init_lblParcelas() {
        lblParcelas.setName("lblParcelas");
        lblParcelas.setLeft(0);
        lblParcelas.setTop(308);
        lblParcelas.setWidth(40);
        lblParcelas.setHeight(13);
        lblParcelas.setCaption("Parcelas");
        lblParcelas.setFontColor("clWindowText");
        lblParcelas.setFontSize(-11);
        lblParcelas.setFontName("Tahoma");
        lblParcelas.setFontStyle("[]");
        lblParcelas.setVerticalAlignment("taVerticalCenter");
        lblParcelas.setWordBreak(false);
        vBoxPrincipal.addChildren(lblParcelas);
        lblParcelas.applyProperties();
    }

    public TFGrid grdParcelas = new TFGrid();

    private void init_grdParcelas() {
        grdParcelas.setName("grdParcelas");
        grdParcelas.setLeft(0);
        grdParcelas.setTop(322);
        grdParcelas.setWidth(770);
        grdParcelas.setHeight(120);
        grdParcelas.setTable(tbParcelasPgtoPosSitef);
        grdParcelas.setFlexVflex("ftFalse");
        grdParcelas.setFlexHflex("ftTrue");
        grdParcelas.setPagingEnabled(false);
        grdParcelas.setFrozenColumns(0);
        grdParcelas.setShowFooter(false);
        grdParcelas.setShowHeader(true);
        grdParcelas.setMultiSelection(false);
        grdParcelas.setGroupingEnabled(false);
        grdParcelas.setGroupingExpanded(false);
        grdParcelas.setGroupingShowFooter(false);
        grdParcelas.setCrosstabEnabled(false);
        grdParcelas.setCrosstabGroupType("cgtConcat");
        grdParcelas.setEditionEnabled(false);
        grdParcelas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_PAGAMENTO");
        item0.setTitleCaption("Id.");
        item0.setWidth(100);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taRight");
        item0.setFieldType("ftInteger");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFMaskExpression item1 = new TFMaskExpression();
        item1.setExpression("*");
        item1.setEvalType("etExpression");
        item1.setMask(",##0");
        item1.setPadLength(0);
        item1.setPadDirection("pdNone");
        item1.setMaskType("mtDecimal");
        item0.getMasks().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdParcelas.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("SEQUENCIA");
        item2.setTitleCaption("Seq.");
        item2.setWidth(100);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftInteger");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFMaskExpression item3 = new TFMaskExpression();
        item3.setExpression("*");
        item3.setEvalType("etExpression");
        item3.setMask(",##0");
        item3.setPadLength(0);
        item3.setPadDirection("pdNone");
        item3.setMaskType("mtDecimal");
        item2.getMasks().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdParcelas.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DATA_PAGAMENTO");
        item4.setTitleCaption("Pagamento");
        item4.setWidth(140);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftDateTime");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        TFMaskExpression item5 = new TFMaskExpression();
        item5.setExpression("*");
        item5.setEvalType("etExpression");
        item5.setMask("dd/MM/yyyy HH:mm:ss");
        item5.setPadLength(0);
        item5.setPadDirection("pdNone");
        item5.setMaskType("mtDateTime");
        item4.getMasks().add(item5);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdParcelas.getColumns().add(item4);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("STATUS_DESCRICAO_CODIGO");
        item6.setTitleCaption("Status");
        item6.setWidth(100);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdParcelas.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("TIPO_CARTAO");
        item7.setTitleCaption("Tipo Cart\u00E3o");
        item7.setWidth(100);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(true);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        grdParcelas.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("QTDE_PARCELAS");
        item8.setTitleCaption("Qtde. Parc.");
        item8.setWidth(100);
        item8.setVisible(true);
        item8.setPrecision(0);
        item8.setTextAlign("taRight");
        item8.setFieldType("ftInteger");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        TFMaskExpression item9 = new TFMaskExpression();
        item9.setExpression("*");
        item9.setEvalType("etExpression");
        item9.setMask(",##0");
        item9.setPadLength(0);
        item9.setPadDirection("pdNone");
        item9.setMaskType("mtDecimal");
        item8.getMasks().add(item9);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        grdParcelas.getColumns().add(item8);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("VALOR_PARCELAS");
        item10.setTitleCaption("Valor");
        item10.setWidth(100);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taRight");
        item10.setFieldType("ftDecimal");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        TFMaskExpression item11 = new TFMaskExpression();
        item11.setExpression("*");
        item11.setEvalType("etExpression");
        item11.setMask("R$ ,##0.00");
        item11.setPadLength(0);
        item11.setPadDirection("pdNone");
        item11.setMaskType("mtDecimal");
        item10.getMasks().add(item11);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setCheckedValue("S");
        item10.setUncheckedValue("N");
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        grdParcelas.getColumns().add(item10);
        vBoxPrincipal.addChildren(grdParcelas);
        grdParcelas.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtCPFCNPJClienteEnter(final Event<Object> event);

    public abstract void edtCPFCNPJClienteExit(final Event<Object> event);

    public void btnPesquisarClienteClick(final Event<Object> event) {
        if (btnPesquisarCliente.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisarCliente");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtOrcamentoPreNotaEnter(final Event<Object> event);

    public abstract void dtEmissaoLeadEnter(final Event<Object> event);

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void tbPagamentosPosSitefAfterScroll(final Event<Object> event);

}