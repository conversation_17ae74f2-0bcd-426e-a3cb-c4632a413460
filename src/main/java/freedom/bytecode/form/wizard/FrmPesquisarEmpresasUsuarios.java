package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPesquisarEmpresasUsuarios extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PesquisarEmpresasUsuariosRNA rn = null;

    public FrmPesquisarEmpresasUsuarios() {
        try {
            rn = (freedom.bytecode.rn.PesquisarEmpresasUsuariosRNA) getRN(freedom.bytecode.rn.wizard.PesquisarEmpresasUsuariosRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresasUsuarios();
        init_tbFiltroEmpresas();
        init_vBoxPrincipal();
        init_FHBox11();
        init_btnVoltar();
        init_FHBox1();
        init_btnPesquisar();
        init_FHBox4();
        init_btnAceitar();
        init_FVBox1();
        init_FGridPanel1();
        init_lblConsultor();
        init_edtNomeUsuario();
        init_FLabel1();
        init_cmbFiltroEmpresa();
        init_gridEmpresaUsuario();
        init_FrmPesquisarEmpresasUsuarios();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("4600265;46001");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public LEADS_EMPRESAS_USUARIOS tbFiltroEmpresas;

    private void init_tbFiltroEmpresas() {
        tbFiltroEmpresas = rn.tbFiltroEmpresas;
        tbFiltroEmpresas.setName("tbFiltroEmpresas");
        tbFiltroEmpresas.setMaxRowCount(200);
        tbFiltroEmpresas.setWKey("4600265;46002");
        tbFiltroEmpresas.setRatioBatchSize(20);
        getTables().put(tbFiltroEmpresas, "tbFiltroEmpresas");
        tbFiltroEmpresas.applyProperties();
    }

    protected TFForm FrmPesquisarEmpresasUsuarios = this;
    private void init_FrmPesquisarEmpresasUsuarios() {
        FrmPesquisarEmpresasUsuarios.setName("FrmPesquisarEmpresasUsuarios");
        FrmPesquisarEmpresasUsuarios.setCaption("Pesquisar Usu\u00E1rios");
        FrmPesquisarEmpresasUsuarios.setClientHeight(410);
        FrmPesquisarEmpresasUsuarios.setClientWidth(747);
        FrmPesquisarEmpresasUsuarios.setColor("clBtnFace");
        FrmPesquisarEmpresasUsuarios.setWKey("4600265");
        FrmPesquisarEmpresasUsuarios.setSpacing(0);
        FrmPesquisarEmpresasUsuarios.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(747);
        vBoxPrincipal.setHeight(410);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmPesquisarEmpresasUsuarios.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(676);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(3);
        FHBox11.setPaddingLeft(3);
        FHBox11.setPaddingRight(3);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmPesquisarEmpresasUsuarios", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(60);
        FHBox1.setTop(0);
        FHBox1.setWidth(7);
        FHBox1.setHeight(48);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FHBox11.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(67);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(60);
        btnPesquisar.setHeight(56);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setAlign("alLeft");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmPesquisarEmpresasUsuarios", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000002224944415478DAAD954D481561148667840C347F1229DD0822680B4B"
 + "282368D12A90144522027321B8B44D8128B6085CA508091A486D021191D4853F"
 + "F8B30B42022D085D184482D4E6128982284A747B4E9DF0F03933772EDC0F5E5E"
 + "BE33EF39EF9CEF67C6F72286EFFBE7A13250020E40026C2593C95F5ECCE10714"
 + "95D86D30022A42F2C640274689B40CA85D08BD079762BE600FE8C72899D280E2"
 + "B20C5F414EDCF6758C53BF35D280E267A16D70D13C5B07ED608302C7BA74D2E1"
 + "033004B28CB60BCD4094813CEC34F17E693FAC75F4C5FA02A5267C01F98F00AD"
 + "5F00EFC66DD93149984E5E93D71E64700F9E34B13C84FB71169FDC87D00B133A"
 + "E31E61319886EFEA7C0E41539CE29A2CF764C7842AC9FFE21A6CC1E53AEF4030"
 + "928681ECE16F13BA45FE3B57B307E7EBBC0DC1685C032D600F4203F90BAEC10A"
 + "7C53E7CF103C49A3783674644237C85F750D9EC2BD3A97F52C8EBA994E722DB4"
 + "664292FBD33590CFC2A689D5235A8CB9FE9FC0150D7D23AF2C4827C2CFA05263"
 + "72CCCA117F4F61D007759B50333933A7742ABEECFDBB999E3191E3BAE42E17DA"
 + "73D02BD062C272518B8296D67EEC1E43CF9DE7B2272FB5C35C7007348634D542"
 + "FD8950033579040DA65AFF8871CA24E88723CB35E59DEC49D81806F36039CAC4"
 + "0FCAD48DAF02F7411DA80687E00378036629B2ABDAABD0C730934083740726D7"
 + "D4DC8EBFA72A23066AE25E3A195919335093EBD0FF4FC53A1DD464D4404DE4B7"
 + "5B03DECAAFF60FF9CFC91D70A0B6C30000000049454E44AE426082");
        btnPesquisar.setImageId(27001);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        FHBox11.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(127);
        FHBox4.setTop(0);
        FHBox4.setWidth(7);
        FHBox4.setHeight(50);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox11.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(134);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmPesquisarEmpresasUsuarios", "btnAceitar", "OnClick");
        });
        btnAceitar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515"
 + "0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3"
 + "1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7"
 + "95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC"
 + "101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D"
 + "1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537"
 + "A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC"
 + "C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB"
 + "482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB"
 + "1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D"
 + "C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F"
 + "8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082");
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox11.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(62);
        FVBox1.setWidth(676);
        FVBox1.setHeight(325);
        FVBox1.setAlign("alTop");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(2);
        FVBox1.setPaddingRight(2);
        FVBox1.setPaddingBottom(3);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGridPanel FGridPanel1 = new TFGridPanel();

    private void init_FGridPanel1() {
        FGridPanel1.setName("FGridPanel1");
        FGridPanel1.setLeft(0);
        FGridPanel1.setTop(0);
        FGridPanel1.setWidth(669);
        FGridPanel1.setHeight(52);
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(50.000000000000000000);
        FGridPanel1.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(50.000000000000000000);
        FGridPanel1.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("lblConsultor");
        item2.setRow(0);
        FGridPanel1.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(0);
        item3.setControl("edtNomeUsuario");
        item3.setRow(1);
        FGridPanel1.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(1);
        item4.setControl("FLabel1");
        item4.setRow(0);
        FGridPanel1.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("cmbFiltroEmpresa");
        item5.setRow(1);
        FGridPanel1.getControlCollection().add(item5);
        TFGridPanelRow item6 = new TFGridPanelRow();
        item6.setSizeStyle("ssAuto");
        FGridPanel1.getRowCollection().add(item6);
        TFGridPanelRow item7 = new TFGridPanelRow();
        item7.setSizeStyle("ssAuto");
        FGridPanel1.getRowCollection().add(item7);
        FGridPanel1.setFlexVflex("ftFalse");
        FGridPanel1.setFlexHflex("ftTrue");
        FGridPanel1.setAllRowFlex(false);
        FGridPanel1.setColumnTabOrder(false);
        FVBox1.addChildren(FGridPanel1);
        FGridPanel1.applyProperties();
    }

    public TFLabel lblConsultor = new TFLabel();

    private void init_lblConsultor() {
        lblConsultor.setName("lblConsultor");
        lblConsultor.setLeft(1);
        lblConsultor.setTop(1);
        lblConsultor.setWidth(80);
        lblConsultor.setHeight(16);
        lblConsultor.setAlign("alLeft");
        lblConsultor.setCaption("Nome Usu\u00E1rio");
        lblConsultor.setFontColor("clWindowText");
        lblConsultor.setFontSize(-13);
        lblConsultor.setFontName("Tahoma");
        lblConsultor.setFontStyle("[]");
        lblConsultor.setVerticalAlignment("taVerticalCenter");
        lblConsultor.setWordBreak(false);
        FGridPanel1.addChildren(lblConsultor);
        lblConsultor.applyProperties();
    }

    public TFString edtNomeUsuario = new TFString();

    private void init_edtNomeUsuario() {
        edtNomeUsuario.setName("edtNomeUsuario");
        edtNomeUsuario.setLeft(1);
        edtNomeUsuario.setTop(17);
        edtNomeUsuario.setWidth(225);
        edtNomeUsuario.setHeight(24);
        edtNomeUsuario.setFlex(true);
        edtNomeUsuario.setRequired(false);
        edtNomeUsuario.setPrompt("Consultor");
        edtNomeUsuario.setConstraintCheckWhen("cwImmediate");
        edtNomeUsuario.setConstraintCheckType("ctExpression");
        edtNomeUsuario.setConstraintFocusOnError(false);
        edtNomeUsuario.setConstraintEnableUI(true);
        edtNomeUsuario.setConstraintEnabled(false);
        edtNomeUsuario.setConstraintFormCheck(true);
        edtNomeUsuario.setCharCase("ccNormal");
        edtNomeUsuario.setPwd(false);
        edtNomeUsuario.setMaxlength(0);
        edtNomeUsuario.setAlign("alLeft");
        edtNomeUsuario.setFontColor("clWindowText");
        edtNomeUsuario.setFontSize(-13);
        edtNomeUsuario.setFontName("Tahoma");
        edtNomeUsuario.setFontStyle("[]");
        edtNomeUsuario.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtNomeUsuarioEnter(event);
            processarFlow("FrmPesquisarEmpresasUsuarios", "edtNomeUsuario", "OnEnter");
        });
        edtNomeUsuario.setSaveLiteralCharacter(false);
        edtNomeUsuario.applyProperties();
        FGridPanel1.addChildren(edtNomeUsuario);
        addValidatable(edtNomeUsuario);
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(334);
        FLabel1.setTop(1);
        FLabel1.setWidth(41);
        FLabel1.setHeight(16);
        FLabel1.setAlign("alLeft");
        FLabel1.setCaption("Empresa");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FGridPanel1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cmbFiltroEmpresa = new TFCombo();

    private void init_cmbFiltroEmpresa() {
        cmbFiltroEmpresa.setName("cmbFiltroEmpresa");
        cmbFiltroEmpresa.setLeft(334);
        cmbFiltroEmpresa.setTop(17);
        cmbFiltroEmpresa.setWidth(334);
        cmbFiltroEmpresa.setHeight(21);
        cmbFiltroEmpresa.setLookupTable(tbFiltroEmpresas);
        cmbFiltroEmpresa.setLookupKey("COD_EMPRESA");
        cmbFiltroEmpresa.setLookupDesc("EMPRESA");
        cmbFiltroEmpresa.setFlex(true);
        cmbFiltroEmpresa.setReadOnly(true);
        cmbFiltroEmpresa.setRequired(false);
        cmbFiltroEmpresa.setPrompt("Selecione");
        cmbFiltroEmpresa.setConstraintCheckWhen("cwImmediate");
        cmbFiltroEmpresa.setConstraintCheckType("ctExpression");
        cmbFiltroEmpresa.setConstraintFocusOnError(false);
        cmbFiltroEmpresa.setConstraintEnableUI(true);
        cmbFiltroEmpresa.setConstraintEnabled(false);
        cmbFiltroEmpresa.setConstraintFormCheck(true);
        cmbFiltroEmpresa.setClearOnDelKey(true);
        cmbFiltroEmpresa.setUseClearButton(true);
        cmbFiltroEmpresa.setHideClearButtonOnNullValue(true);
        cmbFiltroEmpresa.setAlign("alClient");
        cmbFiltroEmpresa.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbFiltroEmpresaChange(event);
            processarFlow("FrmPesquisarEmpresasUsuarios", "cmbFiltroEmpresa", "OnChange");
        });
        cmbFiltroEmpresa.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbFiltroEmpresaClearClick(event);
            processarFlow("FrmPesquisarEmpresasUsuarios", "cmbFiltroEmpresa", "OnClearClick");
        });
        FGridPanel1.addChildren(cmbFiltroEmpresa);
        cmbFiltroEmpresa.applyProperties();
        addValidatable(cmbFiltroEmpresa);
    }

    public TFGrid gridEmpresaUsuario = new TFGrid();

    private void init_gridEmpresaUsuario() {
        gridEmpresaUsuario.setName("gridEmpresaUsuario");
        gridEmpresaUsuario.setLeft(0);
        gridEmpresaUsuario.setTop(53);
        gridEmpresaUsuario.setWidth(424);
        gridEmpresaUsuario.setHeight(208);
        gridEmpresaUsuario.setTable(tbEmpresasUsuarios);
        gridEmpresaUsuario.setFlexVflex("ftTrue");
        gridEmpresaUsuario.setFlexHflex("ftTrue");
        gridEmpresaUsuario.setPagingEnabled(true);
        gridEmpresaUsuario.setFrozenColumns(0);
        gridEmpresaUsuario.setShowFooter(false);
        gridEmpresaUsuario.setShowHeader(true);
        gridEmpresaUsuario.setMultiSelection(false);
        gridEmpresaUsuario.setGroupingEnabled(false);
        gridEmpresaUsuario.setGroupingExpanded(false);
        gridEmpresaUsuario.setGroupingShowFooter(false);
        gridEmpresaUsuario.setCrosstabEnabled(false);
        gridEmpresaUsuario.setCrosstabGroupType("cgtConcat");
        gridEmpresaUsuario.setEditionEnabled(false);
        gridEmpresaUsuario.setNoBorder(false);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("COD_EMPRESA");
        item8.setTitleCaption("CE");
        item8.setWidth(40);
        item8.setVisible(true);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(true);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        gridEmpresaUsuario.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("NOME");
        item9.setTitleCaption("Nome");
        item9.setWidth(100);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        gridEmpresaUsuario.getColumns().add(item9);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("NOME_COMPLETO");
        item10.setTitleCaption("Nome Completo");
        item10.setWidth(197);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(true);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        gridEmpresaUsuario.getColumns().add(item10);
        TFGridColumn item11 = new TFGridColumn();
        item11.setWidth(50);
        item11.setVisible(true);
        item11.setPrecision(0);
        item11.setTextAlign("taLeft");
        item11.setFieldType("ftString");
        item11.setFlexRatio(0);
        item11.setSort(false);
        item11.setImageHeader(0);
        item11.setWrap(false);
        item11.setFlex(false);
        TFImageExpression item12 = new TFImageExpression();
        item12.setExpression("*");
        item12.setEvalType("etExpression");
        item12.setImageId(310047);
        item12.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridEmpresaUsuarioselecionarConsultor(event);
            processarFlow("FrmPesquisarEmpresasUsuarios", "item12", "OnClick");
        });
        item11.getImages().add(item12);
        item11.setCharCase("ccNormal");
        item11.setBlobConfigMimeType("bmtText");
        item11.setBlobConfigShowType("btImageViewer");
        item11.setShowLabel(true);
        item11.setEditorEditType("etTFString");
        item11.setEditorPrecision(0);
        item11.setEditorMaxLength(100);
        item11.setEditorLookupFilterKey(0);
        item11.setEditorLookupFilterDesc(0);
        item11.setEditorPopupHeight(400);
        item11.setEditorPopupWidth(400);
        item11.setEditorCharCase("ccNormal");
        item11.setEditorEnabled(false);
        item11.setEditorReadOnly(false);
        item11.setHiperLink(false);
        item11.setEditorConstraintCheckWhen("cwImmediate");
        item11.setEditorConstraintCheckType("ctExpression");
        item11.setEditorConstraintFocusOnError(false);
        item11.setEditorConstraintEnableUI(true);
        item11.setEditorConstraintEnabled(false);
        item11.setEmpty(false);
        item11.setMobileOptsShowMobile(false);
        item11.setMobileOptsOrder(0);
        gridEmpresaUsuario.getColumns().add(item11);
        FVBox1.addChildren(gridEmpresaUsuario);
        gridEmpresaUsuario.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtNomeUsuarioEnter(final Event<Object> event);

    public abstract void cmbFiltroEmpresaChange(final Event<Object> event);

    public abstract void cmbFiltroEmpresaClearClick(final Event<Object> event);

    public abstract void gridEmpresaUsuarioselecionarConsultor(final Event<Object> event);

}