/* -------------------------------------------------------------------------
   Projeto Freedom - Cliente - Versao: 1.0.7.35
   Class Main  : CadAreaDeContato
   Analista    : GIORDANNY
   Data Created: 06/09/2024 08:51:42
   Data Changed: 06/09/2024 13:36:26
  -------------------------------------------------------------------------- */

package freedom.bytecode.form.wizard;

import freedom.bytecode.rn.CadAreaDeContatoRNA;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.grid.TFGridExporter;
import freedom.client.controls.impl.treegrid.TFTreeGridExporter;
import freedom.client.controls.IBaseComponent;
import freedom.client.event.Event;
import freedom.client.controls.IFocusable;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.RowState;
import freedom.data.TableState;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.impl.Row;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import static freedom.client.util.Dialog.showMessage;
import java.util.Date;


public abstract class FrmCadAreaDeContatoW extends FrmCadAreaDeContato {

    private static final long serialVersionUID = 20130827081850L;

    public FrmCadAreaDeContatoW() {
        lblMensagem.setCaption("");
        habilitaComp(false);
                
        onAbreTabelaAux();            
    }

    protected void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnFiltroAvancado.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled && !menuSelecaoMultipla.isChecked());
        btnAlterar.setEnabled(!enabled && !tbClienteContatoTipo.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbClienteContatoTipo.isEmpty());
        if (! menuHabilitaNavegacao.isChecked()) {                                       // menu popup habilitar navegação
            btnProximo.setEnabled(!enabled && !tbClienteContatoTipo.isEmpty());
            btnAnterior.setEnabled(!enabled && !tbClienteContatoTipo.isEmpty());
        }
        btnAceitar.setEnabled(!enabled && !tbClienteContatoTipo.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);
        btnSalvarContinuar.setEnabled(enabled && !menuSelecaoMultipla.isChecked());
        menuSelecaoMultipla.setVisible(!enabled);
        
        edAreaContato44801.setEnabled(enabled && ( ! tbClienteContatoTipo.isEmpty()  || tbClienteContatoTipo.getState() == TableState.INSERTING));
        edDescricao44801.setEnabled(enabled && ( ! tbClienteContatoTipo.isEmpty()  || tbClienteContatoTipo.getState() == TableState.INSERTING));
        edAtivo44801.setEnabled(enabled && ( ! tbClienteContatoTipo.isEmpty()  || tbClienteContatoTipo.getState() == TableState.INSERTING));

        
    }

    @Override
    public void btnConsultarClick(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }
    
    @Override
    public void btnFiltroAvancadoClick(Event<Object> event) {
        filtroAvancado.doModal();
    }    

    @Override
    public void btnNovoClick(final Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            if (menuSelecaoMultipla.isChecked()) {
                onSalvarMultiplo();
            } else {
                onSalvar();
            }
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    public void executaFiltroPrincipal() throws DataException {
        tbClienteContatoTipo.clearFilters();
        if (! efAreaContato.getValue().asString().isEmpty()) {
           tbClienteContatoTipo.setFilterAREA_CONTATO(efAreaContato.getValue());
        }
        if (! efDescricao.getValue().asString().isEmpty()) {
           tbClienteContatoTipo.setFilterDESCRICAO(efDescricao.getValue());
        }
        if (! efAtivoEquals.getValue().equals(null)) {
           tbClienteContatoTipo.setFilterATIVO_EQUALS(efAtivoEquals.getValue());
        }

    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        if (menuHabilitaNavegacao.isChecked()) {
            btnProximo.setEnabled(true);
            btnAnterior.setEnabled(true);
        } else {
            btnProximo.setEnabled(btnNovo.isEnabled());
            btnAnterior.setEnabled(btnNovo.isEnabled());
        }
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {

        boolean checkedMenu = menuSelecaoMultipla.isChecked();
        gridPrincipal.setMultiSelection(checkedMenu);

        // tratamento das abas visto que pode mexer somente na tabela master
        // Não necessariamente as outras abas podem ser detalhe, pode ser um consulta etc
        // for (int i = 2; i <= pgPrincipal.getPageCount()-1; i++) {
        //      pgPrincipal.selectTab(i);
        //      pgPrincipal.getSelectedTab().setVisible(!checkedMenu);
        // }

        // opções da barra de ferramenta
        btnNovo.setEnabled(! checkedMenu && btnAlterar.isEnabled());
        btnSalvarContinuar.setEnabled(! checkedMenu && btnAlterar.isEnabled());


        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()+30);
            // desregistra o marter table dos componentes
                        edAreaContato44801.setTable(null);
            edAreaContato44801.setValue(null);
            edDescricao44801.setTable(null);
            edDescricao44801.setValue(null);
            edAtivo44801.setTable(null);
            edAtivo44801.setValue(null);

            gridPrincipal.clearSelection();
        } else {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()-30);
            // registra o master table para os componentes
                        edAreaContato44801.setTable(tbClienteContatoTipo);
            edDescricao44801.setTable(tbClienteContatoTipo);
            edAtivo44801.setTable(tbClienteContatoTipo);

        }
        pgPrincipal.selectTab(0);
    }

    @Override
    public void FrmCadAreaDeContatokeyActionPesquisar(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionIncluir(Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionExcluir(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionSalvar(Event<Object> event) {
        try {
            onSalvar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionSalvarContinuar(Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionCancelar(Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionAnterior(Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionProximo(Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmCadAreaDeContatokeyActionAceitar(Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }    
    
    @Override
    public void btnMaisClick(Event<Object> event) {
        popMenuPrincipal.open(this);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(Event<Object> event) {
        onAbreTabelaAux();
    }

    @Override
    public void menuItemConfgGridClick(Event<Object> event) {
        gridConfig.doModal();
    }

    @Override
    public void menuItemHelpClick(Event<Object> event) {
        FormUtil.redirect("help/FrmCadAreaDeContato.zul", true);
    }

    protected void onConsultar() throws Exception {
        
        tbClienteContatoTipo.close();
        executaFiltroPrincipal();
        tbClienteContatoTipo.setOrderBy("DESCRICAO");
        tbClienteContatoTipo.open();
        habilitaComp(false);

        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.clearSelection();
        }

        if (tbClienteContatoTipo.isEmpty()) {
            Dialog.create()
                      .title("Aviso")
                      .message("Registro Não Encontrado...")
                      .showInformation();
        }
    }

    protected void onAnterior() throws Exception {

        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbClienteContatoTipo.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnAnterior);
        }

        if (tbClienteContatoTipo.bof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o Primeiro Registro")
                    .showInformation();
        } else {
            tbClienteContatoTipo.prior();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onProximo() throws Exception {
        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbClienteContatoTipo.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnProximo);
        }

        if (tbClienteContatoTipo.eof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o último registgro")
                    .showInformation();
        } else {
            tbClienteContatoTipo.next();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onIncluir() throws Exception {
        
        rn.incluir(); 
        
        
        pgPrincipal.selectTab(1);
        edAreaContato44801.setFocus();
        habilitaComp(true);
        lblMensagem.setCaption("Incluindo...");
    }

    protected void onAlterar() throws Exception {
        if (menuSelecaoMultipla.isChecked()) {
            lblMensagem.setCaption("ATENÇÃO: Alterando multiplos registros. Será alterado todos os registros selecionados...");
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } else {
            if (!tbClienteContatoTipo.isEmpty()) {
                rn.alterar();
                if (pgPrincipal.getSelectedIndex() == 0)  {
                   pgPrincipal.selectTab(1);
                }                
                habilitaComp(true);
                edAreaContato44801.setFocus();
                lblMensagem.setCaption("Alterando "+tbClienteContatoTipo.getAREA_CONTATO().asString() + ", " + tbClienteContatoTipo.getDESCRICAO().asString()+"...");
            } else {
                Dialog.create()
                      .title("Erro ao editar")
                      .message("Selecione um registro antes de editar")
                      .showError();
            }
        }
    }

    protected void onExcluir() throws DataException {
        if (!tbClienteContatoTipo.isEmpty()) {
           String titulo;
           String mensagem;
           if (menuSelecaoMultipla.isChecked()) {
               titulo = "Exclusão Multipla";
               mensagem = "ATENÇÃO: Serão excluido(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?";
           } else {
               titulo = "Exclusão de Registro";
               mensagem = "Confirma a exclusão do registro selecionado?";
           }

            Dialog.create()
                    .title(titulo)
                    .message(mensagem)
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {


                            try {

                                rn.disableTables();  // desabilita controls e master table das tabelas da transação

                                if (menuSelecaoMultipla.isChecked()) {
                                    for (int bm : gridPrincipal.getSelectedIndices(false)) {
                                         tbClienteContatoTipo.gotoBookmark(bm);
                                         rn.excluiTableMaster();
                                     }
                                 } else {
                                      rn.excluiTableMaster();
                                 }

                                 rn.excluir();                                        
                                 
                                 habilitaComp(false);
                                 onBeforeExcluir();

                            } catch (DataException ex) {
                                Dialog.create()
                                    .title("Erro ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex);

                                    try {
                                        tbClienteContatoTipo.cancelUpdates();
                                    } catch (DataException e) {
                                        showMessage(e.getMessage());
                                    }

                            } finally {
                                  try {
                                      rn.enableTables();  // habilita controls e master table das tabelas da transação
                                  } catch (DataException e) {
                                      showMessage(e.getMessage());
                                  }
                            }
                        }
                    });
        } else {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message("Selecione um registro antes de excluir")
                    .showError();
        }
    }

    protected void onBeforeExcluir() {

    }


    protected void onSalvarMultiplo() throws DataException {

        Dialog.create()
            .title("Alteração Multipla")
            .message("ATENÇÃO: Serão alterado(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?")
            .confirmSimNao((String dialogResult) -> {
                if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                     try {
                           tbClienteContatoTipo.disableControls();
                           int lastBookmark = tbClienteContatoTipo.getBookmark();
                           try {
                               for (int bm : gridPrincipal.getSelectedIndices(true)) {
                                   tbClienteContatoTipo.gotoBookmark(bm);
                                   tbClienteContatoTipo.edit();
                                   
                                   if ( ! edAreaContato44801.getValue().isNull()) {
                                       tbClienteContatoTipo.setAREA_CONTATO(edAreaContato44801.getValue());
                                   }
                                   if ( ! edDescricao44801.getValue().isNull()) {
                                       tbClienteContatoTipo.setDESCRICAO(edDescricao44801.getValue());
                                   }
                                   if ( ! edAtivo44801.getValue().isNull()) {
                                       tbClienteContatoTipo.setATIVO(edAtivo44801.getValue());
                                   }

                                   tbClienteContatoTipo.post();
                               }

                               onSalvar();

                           } finally {
                               tbClienteContatoTipo.close();
                               tbClienteContatoTipo.open();
                               // tbClienteContatoTipo.gotoBookmark(lastBookmark);
                               tbClienteContatoTipo.enableControls();
                               gridPrincipal.clearSelection();
                           }

                     } catch (DataException e) {
                         Dialog.create()
                               .title("Erro ao salvar")
                               .message(e.getMessage())
                               .showException(e);
                    }
                }
        });
    }

    protected void onSalvar() throws DataException {
        // executa a validação das constraint dos objetos edition
//        check();
//        if (!getErrorMap().isEmpty()) {
//            StringBuilder strBuilder = new StringBuilder();
//
//            getErrorMap().values().stream().forEach((s) -> {
//                strBuilder.append(s).append("\n");
//            });        
//
//            // manda o focu para o primeiro objeto que deu erro de constraint
//            ((IFocusable)getErrorMap().keySet().iterator().next()).setFocus();
//
//            Dialog.create()
//                  .title("Erro ao validar")
//                  .message("Existe validação(s) pendente...\n" + strBuilder.toString())
//                  .showError();
//
//            return;
//        }

        setCalcUpdate();
        rn.salvar();

        // atualiza o registro
        tbClienteContatoTipo.refreshRecord();
        
        habilitaComp(false);
        lblMensagem.setCaption("");
    }

    protected void onSalvarContinuar() throws DataException {
        try {
            TableState st = tbClienteContatoTipo.getState();
            onSalvar();
            if (st == TableState.INSERTING) {
                onIncluir();
            } else if (st == TableState.MODIFYING) {
                onAlterar();
            }
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar a edição")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void onCancelar() throws DataException {
        habilitaComp(false);
        rn.cancelar();
        
        lblMensagem.setCaption("Registro Selecionado: "+tbClienteContatoTipo.getAREA_CONTATO().asString() + ", " + tbClienteContatoTipo.getDESCRICAO().asString());
    }
    
    protected void onAceitar() throws Exception {
        if (FormUtil.isExternalCall()) {
            // passa os parametros para a resposta ao VB
            FormUtil.externalCall(tbClienteContatoTipo.getField("ID_PESSOA").asString());
        } else {
            close();
        }
    }
    
    protected void onAbreTabelaAux() {
        try {
            rn.abreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao Abrir Tabelas Auxiliares")
                  .message(e.getMessage())
                  .showException(e);
        }
    }
    
    protected void setCalcUpdate() throws DataException {
        
        postTable();
    }

    private void postTable() throws DataException {
        tbClienteContatoTipo.post();
    }

    public void loadFormPk(String areaContato ) throws DataException {
        tbClienteContatoTipo.close(); 
        tbClienteContatoTipo.clearFilters();
        
        if (! areaContato.equals("")) {
            tbClienteContatoTipo.addFilter("AREA_CONTATO");
            tbClienteContatoTipo.addParam("AREA_CONTATO", areaContato);
        } else return;
        
        tbClienteContatoTipo.open();
        habilitaComp(false);          // se tem registro habilita botões da barra de ferramenta  
    }         

    // retorna true se o master esta sendo editado, pode ser usado para verificar se o form esta 
    // habilitado edição
    public boolean masterIsEditing() {
       return rn.getOperRN() != TableState.QUERYING;
    }
	
	private void isEditing() throws DataException {
        if (rn.getOperRN() == TableState.QUERYING) {
           throw new DataException("O Cadastro Não Esta em Modo de Edição...");
        }	
	}
    
    
    @Override
    public void tbClienteContatoTipoAfterScroll(final Event<Object> event) {
        lblMensagem.setCaption("Selecionado: "+tbClienteContatoTipo.getAREA_CONTATO().asString() + ", " + tbClienteContatoTipo.getDESCRICAO().asString());

    }

        
    
    @Override
    public void efAreaContatoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
    @Override
    public void efDescricaoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
    @Override
    public void efAtivoEqualsEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
        
        // Metodo responsavel por avisar o Usuario sobre a duplicidade da Pk 
    protected void onVerificaClienteContatoTipoPk(IBaseComponent sender) throws DataException {
        if (! tbClienteContatoTipo.getField("AREA_CONTATO").isNull() && 
            (tbClienteContatoTipo.getState() == TableState.INSERTING || 
            (tbClienteContatoTipo.getState() == TableState.MODIFYING && 
            (! tbClienteContatoTipo.getField("AREA_CONTATO").oldValue().isNull()) && 
            ! tbClienteContatoTipo.getField("AREA_CONTATO").equals(tbClienteContatoTipo.getField("AREA_CONTATO").oldValue()))) && 
            ( new CLIENTE_CONTATO_TIPO("CLIENTE_CONTATO_TIPO").obtem(tbClienteContatoTipo.getField("AREA_CONTATO").asString()) != null)) {
                 Dialog.create().showNotificationInfo("Registro Já Cadastrado...", "end_after", 3000, sender);
        }
    }

    @Override
    public void edAreaContato44801Exit(final Event<Object> event) {
        try {
            onVerificaClienteContatoTipoPk(event.getTarget());
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao Acessar o Metodo Remoto RN verificaClienteContatoTipoPk")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

        
    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void gridPrincipalClickImageAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuItemExportPdfClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportPdf(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportExcelClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportExcel(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }




}

