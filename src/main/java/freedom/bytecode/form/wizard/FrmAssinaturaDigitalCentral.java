package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAssinaturaDigitalCentral extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AssinaturaDigitalCentralRNA rn = null;

    public FrmAssinaturaDigitalCentral() {
        try {
            rn = (freedom.bytecode.rn.AssinaturaDigitalCentralRNA) getRN(freedom.bytecode.rn.wizard.AssinaturaDigitalCentralRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCentralAssinaturaGrid();
        init_tbLeadsEmpresasUsuarios();
        init_tbAssinaturaDigital();
        init_listaimagens();
        init_imgAssinaturaAguardando();
        init_imgAssinaturaAssinado();
        init_imgAssinaturaCancelado();
        init_imgAssinaturaFalha();
        init_imgAssinaturaTodos();
        init_imgAssinaturaExpiradas();
        init_vboxPrincipal();
        init_hboxTopo();
        init_FVBox28();
        init_ftLinha1();
        init_ftEmpresa();
        init_ftPeriodo();
        init_ftLinha3();
        init_ftTipoAssinatura();
        init_ftLinha2();
        init_ftNomeOuDocumento();
        init_FVBox1();
        init_btnPesquisar();
        init_btnExportarExcel();
        init_FHBox2();
        init_FHBox3();
        init_FPanelButtonTodas();
        init_FPanelButtonAguardando();
        init_FPanelButtonAssinadas();
        init_FPanelButtonCanceladas();
        init_FPanelButtonExpiradas();
        init_FPanelButtonFalhas();
        init_FHBox4();
        init_gridAssinaturas();
        init_FrmAssinaturaDigitalCentral();
    }

    public CENTRAL_ASSINATURA_GRID tbCentralAssinaturaGrid;

    private void init_tbCentralAssinaturaGrid() {
        tbCentralAssinaturaGrid = rn.tbCentralAssinaturaGrid;
        tbCentralAssinaturaGrid.setName("tbCentralAssinaturaGrid");
        tbCentralAssinaturaGrid.setMaxRowCount(0);
        tbCentralAssinaturaGrid.setWKey("455010;47401");
        tbCentralAssinaturaGrid.setRatioBatchSize(20);
        getTables().put(tbCentralAssinaturaGrid, "tbCentralAssinaturaGrid");
        tbCentralAssinaturaGrid.applyProperties();
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios;

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios = rn.tbLeadsEmpresasUsuarios;
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.setWKey("455010;47402");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbLeadsEmpresasUsuarios, "tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.applyProperties();
    }

    public CRM_ASSINATURA_DIGITAL tbAssinaturaDigital;

    private void init_tbAssinaturaDigital() {
        tbAssinaturaDigital = rn.tbAssinaturaDigital;
        tbAssinaturaDigital.setName("tbAssinaturaDigital");
        tbAssinaturaDigital.setMaxRowCount(200);
        tbAssinaturaDigital.setWKey("455010;47403");
        tbAssinaturaDigital.setRatioBatchSize(20);
        getTables().put(tbAssinaturaDigital, "tbAssinaturaDigital");
        tbAssinaturaDigital.applyProperties();
    }

    public TFPopupMenu listaimagens = new TFPopupMenu();

    private void init_listaimagens() {
        listaimagens.setName("listaimagens");
        FrmAssinaturaDigitalCentral.addChildren(listaimagens);
        listaimagens.applyProperties();
    }

    public TFMenuItem imgAssinaturaAguardando = new TFMenuItem();

    private void init_imgAssinaturaAguardando() {
        imgAssinaturaAguardando.setName("imgAssinaturaAguardando");
        imgAssinaturaAguardando.setCaption("imgAssinaturaAguardando");
        imgAssinaturaAguardando.setImageIndex(47409);
        imgAssinaturaAguardando.setAccess(false);
        imgAssinaturaAguardando.setCheckmark(false);
        listaimagens.addChildren(imgAssinaturaAguardando);
        imgAssinaturaAguardando.applyProperties();
    }

    public TFMenuItem imgAssinaturaAssinado = new TFMenuItem();

    private void init_imgAssinaturaAssinado() {
        imgAssinaturaAssinado.setName("imgAssinaturaAssinado");
        imgAssinaturaAssinado.setCaption("imgAssinaturaAssinado");
        imgAssinaturaAssinado.setImageIndex(474011);
        imgAssinaturaAssinado.setAccess(false);
        imgAssinaturaAssinado.setCheckmark(false);
        listaimagens.addChildren(imgAssinaturaAssinado);
        imgAssinaturaAssinado.applyProperties();
    }

    public TFMenuItem imgAssinaturaCancelado = new TFMenuItem();

    private void init_imgAssinaturaCancelado() {
        imgAssinaturaCancelado.setName("imgAssinaturaCancelado");
        imgAssinaturaCancelado.setCaption("imgAssinaturaCancelado");
        imgAssinaturaCancelado.setImageIndex(474013);
        imgAssinaturaCancelado.setAccess(false);
        imgAssinaturaCancelado.setCheckmark(false);
        listaimagens.addChildren(imgAssinaturaCancelado);
        imgAssinaturaCancelado.applyProperties();
    }

    public TFMenuItem imgAssinaturaFalha = new TFMenuItem();

    private void init_imgAssinaturaFalha() {
        imgAssinaturaFalha.setName("imgAssinaturaFalha");
        imgAssinaturaFalha.setCaption("imgAssinaturaFalha");
        imgAssinaturaFalha.setImageIndex(474012);
        imgAssinaturaFalha.setAccess(false);
        imgAssinaturaFalha.setCheckmark(false);
        listaimagens.addChildren(imgAssinaturaFalha);
        imgAssinaturaFalha.applyProperties();
    }

    public TFMenuItem imgAssinaturaTodos = new TFMenuItem();

    private void init_imgAssinaturaTodos() {
        imgAssinaturaTodos.setName("imgAssinaturaTodos");
        imgAssinaturaTodos.setCaption("imgAssinaturaTodos");
        imgAssinaturaTodos.setImageIndex(474010);
        imgAssinaturaTodos.setAccess(false);
        imgAssinaturaTodos.setCheckmark(false);
        listaimagens.addChildren(imgAssinaturaTodos);
        imgAssinaturaTodos.applyProperties();
    }

    public TFMenuItem imgAssinaturaExpiradas = new TFMenuItem();

    private void init_imgAssinaturaExpiradas() {
        imgAssinaturaExpiradas.setName("imgAssinaturaExpiradas");
        imgAssinaturaExpiradas.setCaption("imgAssinaturaExpiradas");
        imgAssinaturaExpiradas.setImageIndex(553026);
        imgAssinaturaExpiradas.setAccess(false);
        imgAssinaturaExpiradas.setCheckmark(false);
        listaimagens.addChildren(imgAssinaturaExpiradas);
        imgAssinaturaExpiradas.applyProperties();
    }

    protected TFForm FrmAssinaturaDigitalCentral = this;
    private void init_FrmAssinaturaDigitalCentral() {
        FrmAssinaturaDigitalCentral.setName("FrmAssinaturaDigitalCentral");
        FrmAssinaturaDigitalCentral.setCaption("Assinatura Digital");
        FrmAssinaturaDigitalCentral.setClientHeight(466);
        FrmAssinaturaDigitalCentral.setClientWidth(845);
        FrmAssinaturaDigitalCentral.setColor("clBtnFace");
        FrmAssinaturaDigitalCentral.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAssinaturaDigitalCentral", "FrmAssinaturaDigitalCentral", "OnCreate");
        });
        FrmAssinaturaDigitalCentral.setWOrigem("EhMain");
        FrmAssinaturaDigitalCentral.setWKey("455010");
        FrmAssinaturaDigitalCentral.setSpacing(0);
        FrmAssinaturaDigitalCentral.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(845);
        vboxPrincipal.setHeight(466);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(8);
        vboxPrincipal.setPaddingLeft(8);
        vboxPrincipal.setPaddingRight(8);
        vboxPrincipal.setPaddingBottom(8);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(8);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmAssinaturaDigitalCentral.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox hboxTopo = new TFHBox();

    private void init_hboxTopo() {
        hboxTopo.setName("hboxTopo");
        hboxTopo.setLeft(0);
        hboxTopo.setTop(0);
        hboxTopo.setWidth(779);
        hboxTopo.setHeight(137);
        hboxTopo.setBorderStyle("stNone");
        hboxTopo.setPaddingTop(0);
        hboxTopo.setPaddingLeft(0);
        hboxTopo.setPaddingRight(0);
        hboxTopo.setPaddingBottom(0);
        hboxTopo.setMarginTop(0);
        hboxTopo.setMarginLeft(0);
        hboxTopo.setMarginRight(0);
        hboxTopo.setMarginBottom(0);
        hboxTopo.setSpacing(5);
        hboxTopo.setFlexVflex("ftMin");
        hboxTopo.setFlexHflex("ftTrue");
        hboxTopo.setScrollable(false);
        hboxTopo.setBoxShadowConfigHorizontalLength(10);
        hboxTopo.setBoxShadowConfigVerticalLength(10);
        hboxTopo.setBoxShadowConfigBlurRadius(5);
        hboxTopo.setBoxShadowConfigSpreadRadius(0);
        hboxTopo.setBoxShadowConfigShadowColor("clBlack");
        hboxTopo.setBoxShadowConfigOpacity(75);
        hboxTopo.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxTopo);
        hboxTopo.applyProperties();
    }

    public TFVBox FVBox28 = new TFVBox();

    private void init_FVBox28() {
        FVBox28.setName("FVBox28");
        FVBox28.setLeft(0);
        FVBox28.setTop(0);
        FVBox28.setWidth(633);
        FVBox28.setHeight(130);
        FVBox28.setBorderStyle("stNone");
        FVBox28.setPaddingTop(0);
        FVBox28.setPaddingLeft(0);
        FVBox28.setPaddingRight(0);
        FVBox28.setPaddingBottom(0);
        FVBox28.setMarginTop(0);
        FVBox28.setMarginLeft(0);
        FVBox28.setMarginRight(0);
        FVBox28.setMarginBottom(0);
        FVBox28.setSpacing(8);
        FVBox28.setFlexVflex("ftMin");
        FVBox28.setFlexHflex("ftTrue");
        FVBox28.setScrollable(false);
        FVBox28.setBoxShadowConfigHorizontalLength(10);
        FVBox28.setBoxShadowConfigVerticalLength(10);
        FVBox28.setBoxShadowConfigBlurRadius(5);
        FVBox28.setBoxShadowConfigSpreadRadius(0);
        FVBox28.setBoxShadowConfigShadowColor("clBlack");
        FVBox28.setBoxShadowConfigOpacity(75);
        hboxTopo.addChildren(FVBox28);
        FVBox28.applyProperties();
    }

    public TFHBox ftLinha1 = new TFHBox();

    private void init_ftLinha1() {
        ftLinha1.setName("ftLinha1");
        ftLinha1.setLeft(0);
        ftLinha1.setTop(0);
        ftLinha1.setWidth(538);
        ftLinha1.setHeight(41);
        ftLinha1.setBorderStyle("stNone");
        ftLinha1.setPaddingTop(0);
        ftLinha1.setPaddingLeft(0);
        ftLinha1.setPaddingRight(0);
        ftLinha1.setPaddingBottom(0);
        ftLinha1.setMarginTop(0);
        ftLinha1.setMarginLeft(0);
        ftLinha1.setMarginRight(0);
        ftLinha1.setMarginBottom(0);
        ftLinha1.setSpacing(4);
        ftLinha1.setFlexVflex("ftMin");
        ftLinha1.setFlexHflex("ftTrue");
        ftLinha1.setScrollable(false);
        ftLinha1.setBoxShadowConfigHorizontalLength(10);
        ftLinha1.setBoxShadowConfigVerticalLength(10);
        ftLinha1.setBoxShadowConfigBlurRadius(5);
        ftLinha1.setBoxShadowConfigSpreadRadius(0);
        ftLinha1.setBoxShadowConfigShadowColor("clBlack");
        ftLinha1.setBoxShadowConfigOpacity(75);
        ftLinha1.setVAlign("tvTop");
        FVBox28.addChildren(ftLinha1);
        ftLinha1.applyProperties();
    }

    public TFCombo ftEmpresa = new TFCombo();

    private void init_ftEmpresa() {
        ftEmpresa.setName("ftEmpresa");
        ftEmpresa.setLeft(0);
        ftEmpresa.setTop(0);
        ftEmpresa.setWidth(246);
        ftEmpresa.setHeight(21);
        ftEmpresa.setLookupTable(tbLeadsEmpresasUsuarios);
        ftEmpresa.setLookupKey("COD_EMPRESA");
        ftEmpresa.setLookupDesc("CODIGO_NOME_APELIDO");
        ftEmpresa.setFlex(true);
        ftEmpresa.setReadOnly(true);
        ftEmpresa.setRequired(false);
        ftEmpresa.setPrompt("Empresa");
        ftEmpresa.setConstraintCheckWhen("cwImmediate");
        ftEmpresa.setConstraintCheckType("ctExpression");
        ftEmpresa.setConstraintFocusOnError(false);
        ftEmpresa.setConstraintEnableUI(true);
        ftEmpresa.setConstraintEnabled(false);
        ftEmpresa.setConstraintFormCheck(true);
        ftEmpresa.setClearOnDelKey(false);
        ftEmpresa.setUseClearButton(false);
        ftEmpresa.setHideClearButtonOnNullValue(false);
        ftLinha1.addChildren(ftEmpresa);
        ftEmpresa.applyProperties();
        addValidatable(ftEmpresa);
    }

    public TFCombo ftPeriodo = new TFCombo();

    private void init_ftPeriodo() {
        ftPeriodo.setName("ftPeriodo");
        ftPeriodo.setLeft(246);
        ftPeriodo.setTop(0);
        ftPeriodo.setWidth(241);
        ftPeriodo.setHeight(21);
        ftPeriodo.setFlex(true);
        ftPeriodo.setListOptions("Enviadas Hoje=HOJE;Enviadas Ontem=ONTEM;Esta Semana=SEMANA;Este M\u00EAs=MES;M\u00EAs Anterior=MES_ANTERIOR;Selecionar Per\u00EDodo=PERIODO");
        ftPeriodo.setReadOnly(true);
        ftPeriodo.setRequired(false);
        ftPeriodo.setPrompt("Periodo");
        ftPeriodo.setConstraintCheckWhen("cwImmediate");
        ftPeriodo.setConstraintCheckType("ctExpression");
        ftPeriodo.setConstraintFocusOnError(false);
        ftPeriodo.setConstraintEnableUI(true);
        ftPeriodo.setConstraintEnabled(false);
        ftPeriodo.setConstraintFormCheck(true);
        ftPeriodo.setClearOnDelKey(false);
        ftPeriodo.setUseClearButton(false);
        ftPeriodo.setHideClearButtonOnNullValue(false);
        ftPeriodo.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            ftPeriodoChange(event);
            processarFlow("FrmAssinaturaDigitalCentral", "ftPeriodo", "OnChange");
        });
        ftLinha1.addChildren(ftPeriodo);
        ftPeriodo.applyProperties();
        addValidatable(ftPeriodo);
    }

    public TFHBox ftLinha3 = new TFHBox();

    private void init_ftLinha3() {
        ftLinha3.setName("ftLinha3");
        ftLinha3.setLeft(0);
        ftLinha3.setTop(42);
        ftLinha3.setWidth(538);
        ftLinha3.setHeight(41);
        ftLinha3.setBorderStyle("stNone");
        ftLinha3.setPaddingTop(0);
        ftLinha3.setPaddingLeft(0);
        ftLinha3.setPaddingRight(0);
        ftLinha3.setPaddingBottom(0);
        ftLinha3.setMarginTop(0);
        ftLinha3.setMarginLeft(0);
        ftLinha3.setMarginRight(0);
        ftLinha3.setMarginBottom(0);
        ftLinha3.setSpacing(4);
        ftLinha3.setFlexVflex("ftMin");
        ftLinha3.setFlexHflex("ftTrue");
        ftLinha3.setScrollable(false);
        ftLinha3.setBoxShadowConfigHorizontalLength(10);
        ftLinha3.setBoxShadowConfigVerticalLength(10);
        ftLinha3.setBoxShadowConfigBlurRadius(5);
        ftLinha3.setBoxShadowConfigSpreadRadius(0);
        ftLinha3.setBoxShadowConfigShadowColor("clBlack");
        ftLinha3.setBoxShadowConfigOpacity(75);
        ftLinha3.setVAlign("tvTop");
        FVBox28.addChildren(ftLinha3);
        ftLinha3.applyProperties();
    }

    public TFCombo ftTipoAssinatura = new TFCombo();

    private void init_ftTipoAssinatura() {
        ftTipoAssinatura.setName("ftTipoAssinatura");
        ftTipoAssinatura.setLeft(0);
        ftTipoAssinatura.setTop(0);
        ftTipoAssinatura.setWidth(487);
        ftTipoAssinatura.setHeight(21);
        ftTipoAssinatura.setLookupTable(tbAssinaturaDigital);
        ftTipoAssinatura.setLookupKey("TIPO_ASSINATURA");
        ftTipoAssinatura.setLookupDesc("DESCRICAO");
        ftTipoAssinatura.setFlex(true);
        ftTipoAssinatura.setReadOnly(true);
        ftTipoAssinatura.setRequired(false);
        ftTipoAssinatura.setPrompt("Tipo Assinatura");
        ftTipoAssinatura.setConstraintCheckWhen("cwImmediate");
        ftTipoAssinatura.setConstraintCheckType("ctExpression");
        ftTipoAssinatura.setConstraintFocusOnError(false);
        ftTipoAssinatura.setConstraintEnableUI(true);
        ftTipoAssinatura.setConstraintEnabled(false);
        ftTipoAssinatura.setConstraintFormCheck(true);
        ftTipoAssinatura.setClearOnDelKey(true);
        ftTipoAssinatura.setUseClearButton(true);
        ftTipoAssinatura.setHideClearButtonOnNullValue(false);
        ftLinha3.addChildren(ftTipoAssinatura);
        ftTipoAssinatura.applyProperties();
        addValidatable(ftTipoAssinatura);
    }

    public TFHBox ftLinha2 = new TFHBox();

    private void init_ftLinha2() {
        ftLinha2.setName("ftLinha2");
        ftLinha2.setLeft(0);
        ftLinha2.setTop(84);
        ftLinha2.setWidth(538);
        ftLinha2.setHeight(41);
        ftLinha2.setBorderStyle("stNone");
        ftLinha2.setPaddingTop(0);
        ftLinha2.setPaddingLeft(0);
        ftLinha2.setPaddingRight(0);
        ftLinha2.setPaddingBottom(0);
        ftLinha2.setMarginTop(0);
        ftLinha2.setMarginLeft(0);
        ftLinha2.setMarginRight(0);
        ftLinha2.setMarginBottom(0);
        ftLinha2.setSpacing(4);
        ftLinha2.setFlexVflex("ftMin");
        ftLinha2.setFlexHflex("ftTrue");
        ftLinha2.setScrollable(false);
        ftLinha2.setBoxShadowConfigHorizontalLength(10);
        ftLinha2.setBoxShadowConfigVerticalLength(10);
        ftLinha2.setBoxShadowConfigBlurRadius(5);
        ftLinha2.setBoxShadowConfigSpreadRadius(0);
        ftLinha2.setBoxShadowConfigShadowColor("clBlack");
        ftLinha2.setBoxShadowConfigOpacity(75);
        ftLinha2.setVAlign("tvTop");
        FVBox28.addChildren(ftLinha2);
        ftLinha2.applyProperties();
    }

    public TFString ftNomeOuDocumento = new TFString();

    private void init_ftNomeOuDocumento() {
        ftNomeOuDocumento.setName("ftNomeOuDocumento");
        ftNomeOuDocumento.setLeft(0);
        ftNomeOuDocumento.setTop(0);
        ftNomeOuDocumento.setWidth(489);
        ftNomeOuDocumento.setHeight(24);
        ftNomeOuDocumento.setFlex(true);
        ftNomeOuDocumento.setRequired(false);
        ftNomeOuDocumento.setPrompt("Nome, Documento");
        ftNomeOuDocumento.setConstraintCheckWhen("cwImmediate");
        ftNomeOuDocumento.setConstraintCheckType("ctExpression");
        ftNomeOuDocumento.setConstraintFocusOnError(false);
        ftNomeOuDocumento.setConstraintEnableUI(true);
        ftNomeOuDocumento.setConstraintEnabled(false);
        ftNomeOuDocumento.setConstraintFormCheck(true);
        ftNomeOuDocumento.setCharCase("ccNormal");
        ftNomeOuDocumento.setPwd(false);
        ftNomeOuDocumento.setMaxlength(0);
        ftNomeOuDocumento.setFontColor("clWindowText");
        ftNomeOuDocumento.setFontSize(-13);
        ftNomeOuDocumento.setFontName("Tahoma");
        ftNomeOuDocumento.setFontStyle("[]");
        ftNomeOuDocumento.setSaveLiteralCharacter(false);
        ftNomeOuDocumento.applyProperties();
        ftLinha2.addChildren(ftNomeOuDocumento);
        addValidatable(ftNomeOuDocumento);
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(633);
        FVBox1.setTop(0);
        FVBox1.setWidth(57);
        FVBox1.setHeight(129);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(8);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        hboxTopo.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(0);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(50);
        btnPesquisar.setHeight(54);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(0);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconClass("fas fa-search");
        btnPesquisar.setIconReverseDirection(false);
        FVBox1.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFButton btnExportarExcel = new TFButton();

    private void init_btnExportarExcel() {
        btnExportarExcel.setName("btnExportarExcel");
        btnExportarExcel.setLeft(0);
        btnExportarExcel.setTop(55);
        btnExportarExcel.setWidth(50);
        btnExportarExcel.setHeight(54);
        btnExportarExcel.setHint("Exportar Excel");
        btnExportarExcel.setFontColor("clWindowText");
        btnExportarExcel.setFontSize(-11);
        btnExportarExcel.setFontName("Tahoma");
        btnExportarExcel.setFontStyle("[]");
        btnExportarExcel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExportarExcelClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "btnExportarExcel", "OnClick");
        });
        btnExportarExcel.setImageId(0);
        btnExportarExcel.setColor("clBtnFace");
        btnExportarExcel.setAccess(false);
        btnExportarExcel.setIconClass("fal fa-table");
        btnExportarExcel.setIconReverseDirection(false);
        FVBox1.addChildren(btnExportarExcel);
        btnExportarExcel.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(138);
        FHBox2.setWidth(779);
        FHBox2.setHeight(99);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(2);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(7);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vboxPrincipal.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(9);
        FHBox3.setHeight(68);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftTrue");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox2.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFPanelButton FPanelButtonTodas = new TFPanelButton();

    private void init_FPanelButtonTodas() {
        FPanelButtonTodas.setName("FPanelButtonTodas");
        FPanelButtonTodas.setLeft(9);
        FPanelButtonTodas.setTop(0);
        FPanelButtonTodas.setWidth(70);
        FPanelButtonTodas.setHeight(80);
        FPanelButtonTodas.setBorderStyle("stBoxShadow");
        FPanelButtonTodas.setPaddingTop(0);
        FPanelButtonTodas.setPaddingLeft(0);
        FPanelButtonTodas.setPaddingRight(0);
        FPanelButtonTodas.setPaddingBottom(0);
        FPanelButtonTodas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelButtonTodasClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "FPanelButtonTodas", "OnClick");
        });
        FPanelButtonTodas.setMarginTop(0);
        FPanelButtonTodas.setMarginLeft(0);
        FPanelButtonTodas.setMarginRight(0);
        FPanelButtonTodas.setMarginBottom(0);
        FPanelButtonTodas.setBoxShadowConfigHorizontalLength(0);
        FPanelButtonTodas.setBoxShadowConfigVerticalLength(7);
        FPanelButtonTodas.setBoxShadowConfigBlurRadius(10);
        FPanelButtonTodas.setBoxShadowConfigSpreadRadius(-5);
        FPanelButtonTodas.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonTodas.setBoxShadowConfigOpacity(75);
        FPanelButtonTodas.setBorderRadiusTopLeft(0);
        FPanelButtonTodas.setBorderRadiusTopRight(0);
        FPanelButtonTodas.setBorderRadiusBottomRight(0);
        FPanelButtonTodas.setBorderRadiusBottomLeft(0);
        FPanelButtonTodas.setToggle(true);
        FPanelButtonTodas.setToggleColor("clSilver");
        FHBox2.addChildren(FPanelButtonTodas);

        TFFastDesignCmpItems FPanelButtonTodasItems = new TFFastDesignCmpItems();
        FPanelButtonTodasItems.setName("FPanelButtonTodasItems");
        FPanelButtonTodas.addChildren(FPanelButtonTodasItems);
        FPanelButtonTodasItems.applyProperties();

        TFPanelButtonItem FPanelButtonTodasVbox = new TFPanelButtonItem();
        FPanelButtonTodasVbox.setName("FPanelButtonTodasVbox");
        FPanelButtonTodasVbox.setCaption("FPanelButtonItem1");
        FPanelButtonTodasVbox.setItemType("itVBox");
        FPanelButtonTodasVbox.setFontColor("clWindowText");
        FPanelButtonTodasVbox.setFontSize(-13);
        FPanelButtonTodasVbox.setFontName("Tahoma");
        FPanelButtonTodasVbox.setFontStyle("[]");
        FPanelButtonTodasVbox.setItemAlign("iaLeft");
        FPanelButtonTodasVbox.setFlex(false);
        FPanelButtonTodasVbox.setItemVAlign("ivaTop");
        FPanelButtonTodasVbox.setPaddingTop(0);
        FPanelButtonTodasVbox.setPaddingLeft(0);
        FPanelButtonTodasVbox.setPaddingRight(0);
        FPanelButtonTodasVbox.setPaddingBottom(0);
        FPanelButtonTodasVbox.setMarginTop(0);
        FPanelButtonTodasVbox.setMarginLeft(0);
        FPanelButtonTodasVbox.setMarginRight(0);
        FPanelButtonTodasVbox.setMarginBottom(0);
        FPanelButtonTodasVbox.setBorderStyle("stNone");
        FPanelButtonTodasVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonTodasVbox.setBoxShadowConfigVerticalLength(10);
        FPanelButtonTodasVbox.setBoxShadowConfigBlurRadius(5);
        FPanelButtonTodasVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonTodasVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonTodasVbox.setBoxShadowConfigOpacity(75);
        FPanelButtonTodasVbox.setBorderRadiusTopLeft(0);
        FPanelButtonTodasVbox.setBorderRadiusTopRight(0);
        FPanelButtonTodasVbox.setBorderRadiusBottomRight(0);
        FPanelButtonTodasVbox.setBorderRadiusBottomLeft(0);
        FPanelButtonTodasVbox.setColor("clBtnFace");
        FPanelButtonTodasVbox.setWordBreak(false);
        FPanelButtonTodasItems.addChildren(FPanelButtonTodasVbox);
        FPanelButtonTodasVbox.applyProperties();

        TFPanelButtonItem FPanelButtonTodasLblTotal = new TFPanelButtonItem();
        FPanelButtonTodasLblTotal.setName("FPanelButtonTodasLblTotal");
        FPanelButtonTodasLblTotal.setCaption("0");
        FPanelButtonTodasLblTotal.setItemType("itLabel");
        FPanelButtonTodasLblTotal.setFontColor("clBlack");
        FPanelButtonTodasLblTotal.setFontSize(-15);
        FPanelButtonTodasLblTotal.setFontName("Tahoma");
        FPanelButtonTodasLblTotal.setFontStyle("[fsBold]");
        FPanelButtonTodasLblTotal.setItemAlign("iaCenter");
        FPanelButtonTodasLblTotal.setFlex(false);
        FPanelButtonTodasLblTotal.setItemVAlign("ivaTop");
        FPanelButtonTodasLblTotal.setPaddingTop(0);
        FPanelButtonTodasLblTotal.setPaddingLeft(0);
        FPanelButtonTodasLblTotal.setPaddingRight(0);
        FPanelButtonTodasLblTotal.setPaddingBottom(0);
        FPanelButtonTodasLblTotal.setMarginTop(0);
        FPanelButtonTodasLblTotal.setMarginLeft(0);
        FPanelButtonTodasLblTotal.setMarginRight(0);
        FPanelButtonTodasLblTotal.setMarginBottom(0);
        FPanelButtonTodasLblTotal.setBorderStyle("stNone");
        FPanelButtonTodasLblTotal.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonTodasLblTotal.setBoxShadowConfigVerticalLength(10);
        FPanelButtonTodasLblTotal.setBoxShadowConfigBlurRadius(5);
        FPanelButtonTodasLblTotal.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonTodasLblTotal.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonTodasLblTotal.setBoxShadowConfigOpacity(75);
        FPanelButtonTodasLblTotal.setBorderRadiusTopLeft(0);
        FPanelButtonTodasLblTotal.setBorderRadiusTopRight(0);
        FPanelButtonTodasLblTotal.setBorderRadiusBottomRight(0);
        FPanelButtonTodasLblTotal.setBorderRadiusBottomLeft(0);
        FPanelButtonTodasLblTotal.setColor("clBtnFace");
        FPanelButtonTodasLblTotal.setWordBreak(false);
        FPanelButtonTodasVbox.addChildren(FPanelButtonTodasLblTotal);
        FPanelButtonTodasLblTotal.applyProperties();

        TFPanelButtonItem FPanelButtonTodasImg = new TFPanelButtonItem();
        FPanelButtonTodasImg.setName("FPanelButtonTodasImg");
        FPanelButtonTodasImg.setCaption("FPanelButtonItem2");
        FPanelButtonTodasImg.setItemType("itImage");
        FPanelButtonTodasImg.setFontColor("clWindowText");
        FPanelButtonTodasImg.setFontSize(-13);
        FPanelButtonTodasImg.setFontName("Tahoma");
        FPanelButtonTodasImg.setFontStyle("[]");
        FPanelButtonTodasImg.setItemAlign("iaCenter");
        FPanelButtonTodasImg.setImageSrc("/images/crmservice474010.png");
        FPanelButtonTodasImg.setFlex(false);
        FPanelButtonTodasImg.setWidth(25);
        FPanelButtonTodasImg.setHeight(25);
        FPanelButtonTodasImg.setItemVAlign("ivaTop");
        FPanelButtonTodasImg.setPaddingTop(0);
        FPanelButtonTodasImg.setPaddingLeft(0);
        FPanelButtonTodasImg.setPaddingRight(0);
        FPanelButtonTodasImg.setPaddingBottom(0);
        FPanelButtonTodasImg.setMarginTop(0);
        FPanelButtonTodasImg.setMarginLeft(0);
        FPanelButtonTodasImg.setMarginRight(0);
        FPanelButtonTodasImg.setMarginBottom(0);
        FPanelButtonTodasImg.setBorderStyle("stNone");
        FPanelButtonTodasImg.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonTodasImg.setBoxShadowConfigVerticalLength(10);
        FPanelButtonTodasImg.setBoxShadowConfigBlurRadius(5);
        FPanelButtonTodasImg.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonTodasImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonTodasImg.setBoxShadowConfigOpacity(75);
        FPanelButtonTodasImg.setBorderRadiusTopLeft(0);
        FPanelButtonTodasImg.setBorderRadiusTopRight(0);
        FPanelButtonTodasImg.setBorderRadiusBottomRight(0);
        FPanelButtonTodasImg.setBorderRadiusBottomLeft(0);
        FPanelButtonTodasImg.setColor("clBtnFace");
        FPanelButtonTodasImg.setWordBreak(false);
        FPanelButtonTodasVbox.addChildren(FPanelButtonTodasImg);
        FPanelButtonTodasImg.applyProperties();

        TFPanelButtonItem FPanelButtonTodasLblDescricao = new TFPanelButtonItem();
        FPanelButtonTodasLblDescricao.setName("FPanelButtonTodasLblDescricao");
        FPanelButtonTodasLblDescricao.setCaption("Todos");
        FPanelButtonTodasLblDescricao.setItemType("itLabel");
        FPanelButtonTodasLblDescricao.setFontColor("clWindowText");
        FPanelButtonTodasLblDescricao.setFontSize(-11);
        FPanelButtonTodasLblDescricao.setFontName("Tahoma");
        FPanelButtonTodasLblDescricao.setFontStyle("[]");
        FPanelButtonTodasLblDescricao.setItemAlign("iaCenter");
        FPanelButtonTodasLblDescricao.setFlex(false);
        FPanelButtonTodasLblDescricao.setItemVAlign("ivaBottom");
        FPanelButtonTodasLblDescricao.setPaddingTop(0);
        FPanelButtonTodasLblDescricao.setPaddingLeft(0);
        FPanelButtonTodasLblDescricao.setPaddingRight(0);
        FPanelButtonTodasLblDescricao.setPaddingBottom(0);
        FPanelButtonTodasLblDescricao.setMarginTop(0);
        FPanelButtonTodasLblDescricao.setMarginLeft(0);
        FPanelButtonTodasLblDescricao.setMarginRight(0);
        FPanelButtonTodasLblDescricao.setMarginBottom(0);
        FPanelButtonTodasLblDescricao.setBorderStyle("stNone");
        FPanelButtonTodasLblDescricao.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonTodasLblDescricao.setBoxShadowConfigVerticalLength(10);
        FPanelButtonTodasLblDescricao.setBoxShadowConfigBlurRadius(5);
        FPanelButtonTodasLblDescricao.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonTodasLblDescricao.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonTodasLblDescricao.setBoxShadowConfigOpacity(75);
        FPanelButtonTodasLblDescricao.setBorderRadiusTopLeft(0);
        FPanelButtonTodasLblDescricao.setBorderRadiusTopRight(0);
        FPanelButtonTodasLblDescricao.setBorderRadiusBottomRight(0);
        FPanelButtonTodasLblDescricao.setBorderRadiusBottomLeft(0);
        FPanelButtonTodasLblDescricao.setColor("clBtnFace");
        FPanelButtonTodasLblDescricao.setWordBreak(false);
        FPanelButtonTodasVbox.addChildren(FPanelButtonTodasLblDescricao);
        FPanelButtonTodasLblDescricao.applyProperties();
        FPanelButtonTodas.applyProperties();
    }

    public TFPanelButton FPanelButtonAguardando = new TFPanelButton();

    private void init_FPanelButtonAguardando() {
        FPanelButtonAguardando.setName("FPanelButtonAguardando");
        FPanelButtonAguardando.setLeft(79);
        FPanelButtonAguardando.setTop(0);
        FPanelButtonAguardando.setWidth(70);
        FPanelButtonAguardando.setHeight(80);
        FPanelButtonAguardando.setBorderStyle("stBoxShadow");
        FPanelButtonAguardando.setPaddingTop(0);
        FPanelButtonAguardando.setPaddingLeft(0);
        FPanelButtonAguardando.setPaddingRight(0);
        FPanelButtonAguardando.setPaddingBottom(0);
        FPanelButtonAguardando.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelButtonAguardandoClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "FPanelButtonAguardando", "OnClick");
        });
        FPanelButtonAguardando.setMarginTop(0);
        FPanelButtonAguardando.setMarginLeft(0);
        FPanelButtonAguardando.setMarginRight(0);
        FPanelButtonAguardando.setMarginBottom(0);
        FPanelButtonAguardando.setBoxShadowConfigHorizontalLength(0);
        FPanelButtonAguardando.setBoxShadowConfigVerticalLength(7);
        FPanelButtonAguardando.setBoxShadowConfigBlurRadius(10);
        FPanelButtonAguardando.setBoxShadowConfigSpreadRadius(-5);
        FPanelButtonAguardando.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAguardando.setBoxShadowConfigOpacity(75);
        FPanelButtonAguardando.setBorderRadiusTopLeft(0);
        FPanelButtonAguardando.setBorderRadiusTopRight(0);
        FPanelButtonAguardando.setBorderRadiusBottomRight(0);
        FPanelButtonAguardando.setBorderRadiusBottomLeft(0);
        FPanelButtonAguardando.setToggle(true);
        FPanelButtonAguardando.setToggleColor("clSilver");
        FHBox2.addChildren(FPanelButtonAguardando);

        TFFastDesignCmpItems FPanelButtonAguardandoItems = new TFFastDesignCmpItems();
        FPanelButtonAguardandoItems.setName("FPanelButtonAguardandoItems");
        FPanelButtonAguardando.addChildren(FPanelButtonAguardandoItems);
        FPanelButtonAguardandoItems.applyProperties();

        TFPanelButtonItem FPanelButtonAguardandoVbox = new TFPanelButtonItem();
        FPanelButtonAguardandoVbox.setName("FPanelButtonAguardandoVbox");
        FPanelButtonAguardandoVbox.setCaption("FPanelButtonItem1");
        FPanelButtonAguardandoVbox.setItemType("itVBox");
        FPanelButtonAguardandoVbox.setFontColor("clWindowText");
        FPanelButtonAguardandoVbox.setFontSize(-13);
        FPanelButtonAguardandoVbox.setFontName("Tahoma");
        FPanelButtonAguardandoVbox.setFontStyle("[]");
        FPanelButtonAguardandoVbox.setItemAlign("iaLeft");
        FPanelButtonAguardandoVbox.setFlex(false);
        FPanelButtonAguardandoVbox.setItemVAlign("ivaTop");
        FPanelButtonAguardandoVbox.setPaddingTop(0);
        FPanelButtonAguardandoVbox.setPaddingLeft(0);
        FPanelButtonAguardandoVbox.setPaddingRight(0);
        FPanelButtonAguardandoVbox.setPaddingBottom(0);
        FPanelButtonAguardandoVbox.setMarginTop(0);
        FPanelButtonAguardandoVbox.setMarginLeft(0);
        FPanelButtonAguardandoVbox.setMarginRight(0);
        FPanelButtonAguardandoVbox.setMarginBottom(0);
        FPanelButtonAguardandoVbox.setBorderStyle("stNone");
        FPanelButtonAguardandoVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAguardandoVbox.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAguardandoVbox.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAguardandoVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAguardandoVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAguardandoVbox.setBoxShadowConfigOpacity(75);
        FPanelButtonAguardandoVbox.setBorderRadiusTopLeft(0);
        FPanelButtonAguardandoVbox.setBorderRadiusTopRight(0);
        FPanelButtonAguardandoVbox.setBorderRadiusBottomRight(0);
        FPanelButtonAguardandoVbox.setBorderRadiusBottomLeft(0);
        FPanelButtonAguardandoVbox.setColor("clBtnFace");
        FPanelButtonAguardandoVbox.setWordBreak(false);
        FPanelButtonAguardandoItems.addChildren(FPanelButtonAguardandoVbox);
        FPanelButtonAguardandoVbox.applyProperties();

        TFPanelButtonItem FPanelButtonAguardandoLblTotal = new TFPanelButtonItem();
        FPanelButtonAguardandoLblTotal.setName("FPanelButtonAguardandoLblTotal");
        FPanelButtonAguardandoLblTotal.setCaption("0");
        FPanelButtonAguardandoLblTotal.setItemType("itLabel");
        FPanelButtonAguardandoLblTotal.setFontColor("clBlack");
        FPanelButtonAguardandoLblTotal.setFontSize(-15);
        FPanelButtonAguardandoLblTotal.setFontName("Tahoma");
        FPanelButtonAguardandoLblTotal.setFontStyle("[fsBold]");
        FPanelButtonAguardandoLblTotal.setItemAlign("iaCenter");
        FPanelButtonAguardandoLblTotal.setFlex(false);
        FPanelButtonAguardandoLblTotal.setItemVAlign("ivaTop");
        FPanelButtonAguardandoLblTotal.setPaddingTop(0);
        FPanelButtonAguardandoLblTotal.setPaddingLeft(0);
        FPanelButtonAguardandoLblTotal.setPaddingRight(0);
        FPanelButtonAguardandoLblTotal.setPaddingBottom(0);
        FPanelButtonAguardandoLblTotal.setMarginTop(0);
        FPanelButtonAguardandoLblTotal.setMarginLeft(0);
        FPanelButtonAguardandoLblTotal.setMarginRight(0);
        FPanelButtonAguardandoLblTotal.setMarginBottom(0);
        FPanelButtonAguardandoLblTotal.setBorderStyle("stNone");
        FPanelButtonAguardandoLblTotal.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAguardandoLblTotal.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAguardandoLblTotal.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAguardandoLblTotal.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAguardandoLblTotal.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAguardandoLblTotal.setBoxShadowConfigOpacity(75);
        FPanelButtonAguardandoLblTotal.setBorderRadiusTopLeft(0);
        FPanelButtonAguardandoLblTotal.setBorderRadiusTopRight(0);
        FPanelButtonAguardandoLblTotal.setBorderRadiusBottomRight(0);
        FPanelButtonAguardandoLblTotal.setBorderRadiusBottomLeft(0);
        FPanelButtonAguardandoLblTotal.setColor("clBtnFace");
        FPanelButtonAguardandoLblTotal.setWordBreak(false);
        FPanelButtonAguardandoVbox.addChildren(FPanelButtonAguardandoLblTotal);
        FPanelButtonAguardandoLblTotal.applyProperties();

        TFPanelButtonItem FPanelButtonAguardandoImg = new TFPanelButtonItem();
        FPanelButtonAguardandoImg.setName("FPanelButtonAguardandoImg");
        FPanelButtonAguardandoImg.setCaption("FPanelButtonItem2");
        FPanelButtonAguardandoImg.setItemType("itImage");
        FPanelButtonAguardandoImg.setFontColor("clWindowText");
        FPanelButtonAguardandoImg.setFontSize(-13);
        FPanelButtonAguardandoImg.setFontName("Tahoma");
        FPanelButtonAguardandoImg.setFontStyle("[]");
        FPanelButtonAguardandoImg.setItemAlign("iaCenter");
        FPanelButtonAguardandoImg.setImageSrc("/images/crmservice47409.png");
        FPanelButtonAguardandoImg.setFlex(false);
        FPanelButtonAguardandoImg.setWidth(25);
        FPanelButtonAguardandoImg.setHeight(25);
        FPanelButtonAguardandoImg.setItemVAlign("ivaTop");
        FPanelButtonAguardandoImg.setPaddingTop(0);
        FPanelButtonAguardandoImg.setPaddingLeft(0);
        FPanelButtonAguardandoImg.setPaddingRight(0);
        FPanelButtonAguardandoImg.setPaddingBottom(0);
        FPanelButtonAguardandoImg.setMarginTop(0);
        FPanelButtonAguardandoImg.setMarginLeft(0);
        FPanelButtonAguardandoImg.setMarginRight(0);
        FPanelButtonAguardandoImg.setMarginBottom(0);
        FPanelButtonAguardandoImg.setBorderStyle("stNone");
        FPanelButtonAguardandoImg.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAguardandoImg.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAguardandoImg.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAguardandoImg.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAguardandoImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAguardandoImg.setBoxShadowConfigOpacity(75);
        FPanelButtonAguardandoImg.setBorderRadiusTopLeft(0);
        FPanelButtonAguardandoImg.setBorderRadiusTopRight(0);
        FPanelButtonAguardandoImg.setBorderRadiusBottomRight(0);
        FPanelButtonAguardandoImg.setBorderRadiusBottomLeft(0);
        FPanelButtonAguardandoImg.setColor("clBtnFace");
        FPanelButtonAguardandoImg.setWordBreak(false);
        FPanelButtonAguardandoVbox.addChildren(FPanelButtonAguardandoImg);
        FPanelButtonAguardandoImg.applyProperties();

        TFPanelButtonItem FPanelButtonAguardandoLblDescricao = new TFPanelButtonItem();
        FPanelButtonAguardandoLblDescricao.setName("FPanelButtonAguardandoLblDescricao");
        FPanelButtonAguardandoLblDescricao.setCaption("Aguardando");
        FPanelButtonAguardandoLblDescricao.setItemType("itLabel");
        FPanelButtonAguardandoLblDescricao.setFontColor("clWindowText");
        FPanelButtonAguardandoLblDescricao.setFontSize(-11);
        FPanelButtonAguardandoLblDescricao.setFontName("Tahoma");
        FPanelButtonAguardandoLblDescricao.setFontStyle("[]");
        FPanelButtonAguardandoLblDescricao.setItemAlign("iaCenter");
        FPanelButtonAguardandoLblDescricao.setFlex(false);
        FPanelButtonAguardandoLblDescricao.setItemVAlign("ivaBottom");
        FPanelButtonAguardandoLblDescricao.setPaddingTop(0);
        FPanelButtonAguardandoLblDescricao.setPaddingLeft(0);
        FPanelButtonAguardandoLblDescricao.setPaddingRight(0);
        FPanelButtonAguardandoLblDescricao.setPaddingBottom(0);
        FPanelButtonAguardandoLblDescricao.setMarginTop(0);
        FPanelButtonAguardandoLblDescricao.setMarginLeft(0);
        FPanelButtonAguardandoLblDescricao.setMarginRight(0);
        FPanelButtonAguardandoLblDescricao.setMarginBottom(0);
        FPanelButtonAguardandoLblDescricao.setBorderStyle("stNone");
        FPanelButtonAguardandoLblDescricao.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAguardandoLblDescricao.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAguardandoLblDescricao.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAguardandoLblDescricao.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAguardandoLblDescricao.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAguardandoLblDescricao.setBoxShadowConfigOpacity(75);
        FPanelButtonAguardandoLblDescricao.setBorderRadiusTopLeft(0);
        FPanelButtonAguardandoLblDescricao.setBorderRadiusTopRight(0);
        FPanelButtonAguardandoLblDescricao.setBorderRadiusBottomRight(0);
        FPanelButtonAguardandoLblDescricao.setBorderRadiusBottomLeft(0);
        FPanelButtonAguardandoLblDescricao.setColor("clBtnFace");
        FPanelButtonAguardandoLblDescricao.setWordBreak(false);
        FPanelButtonAguardandoVbox.addChildren(FPanelButtonAguardandoLblDescricao);
        FPanelButtonAguardandoLblDescricao.applyProperties();
        FPanelButtonAguardando.applyProperties();
    }

    public TFPanelButton FPanelButtonAssinadas = new TFPanelButton();

    private void init_FPanelButtonAssinadas() {
        FPanelButtonAssinadas.setName("FPanelButtonAssinadas");
        FPanelButtonAssinadas.setLeft(149);
        FPanelButtonAssinadas.setTop(0);
        FPanelButtonAssinadas.setWidth(70);
        FPanelButtonAssinadas.setHeight(80);
        FPanelButtonAssinadas.setBorderStyle("stBoxShadow");
        FPanelButtonAssinadas.setPaddingTop(0);
        FPanelButtonAssinadas.setPaddingLeft(0);
        FPanelButtonAssinadas.setPaddingRight(0);
        FPanelButtonAssinadas.setPaddingBottom(0);
        FPanelButtonAssinadas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelButtonAssinadasClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "FPanelButtonAssinadas", "OnClick");
        });
        FPanelButtonAssinadas.setMarginTop(0);
        FPanelButtonAssinadas.setMarginLeft(0);
        FPanelButtonAssinadas.setMarginRight(0);
        FPanelButtonAssinadas.setMarginBottom(0);
        FPanelButtonAssinadas.setBoxShadowConfigHorizontalLength(0);
        FPanelButtonAssinadas.setBoxShadowConfigVerticalLength(7);
        FPanelButtonAssinadas.setBoxShadowConfigBlurRadius(10);
        FPanelButtonAssinadas.setBoxShadowConfigSpreadRadius(-5);
        FPanelButtonAssinadas.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAssinadas.setBoxShadowConfigOpacity(75);
        FPanelButtonAssinadas.setBorderRadiusTopLeft(0);
        FPanelButtonAssinadas.setBorderRadiusTopRight(0);
        FPanelButtonAssinadas.setBorderRadiusBottomRight(0);
        FPanelButtonAssinadas.setBorderRadiusBottomLeft(0);
        FPanelButtonAssinadas.setToggle(true);
        FPanelButtonAssinadas.setToggleColor("clSilver");
        FHBox2.addChildren(FPanelButtonAssinadas);

        TFFastDesignCmpItems FPanelButtonAssinadasItems = new TFFastDesignCmpItems();
        FPanelButtonAssinadasItems.setName("FPanelButtonAssinadasItems");
        FPanelButtonAssinadas.addChildren(FPanelButtonAssinadasItems);
        FPanelButtonAssinadasItems.applyProperties();

        TFPanelButtonItem FPanelButtonAssinadasVbox = new TFPanelButtonItem();
        FPanelButtonAssinadasVbox.setName("FPanelButtonAssinadasVbox");
        FPanelButtonAssinadasVbox.setCaption("FPanelButtonItem1");
        FPanelButtonAssinadasVbox.setItemType("itVBox");
        FPanelButtonAssinadasVbox.setFontColor("clWindowText");
        FPanelButtonAssinadasVbox.setFontSize(-13);
        FPanelButtonAssinadasVbox.setFontName("Tahoma");
        FPanelButtonAssinadasVbox.setFontStyle("[]");
        FPanelButtonAssinadasVbox.setItemAlign("iaLeft");
        FPanelButtonAssinadasVbox.setFlex(false);
        FPanelButtonAssinadasVbox.setItemVAlign("ivaTop");
        FPanelButtonAssinadasVbox.setPaddingTop(0);
        FPanelButtonAssinadasVbox.setPaddingLeft(0);
        FPanelButtonAssinadasVbox.setPaddingRight(0);
        FPanelButtonAssinadasVbox.setPaddingBottom(0);
        FPanelButtonAssinadasVbox.setMarginTop(0);
        FPanelButtonAssinadasVbox.setMarginLeft(0);
        FPanelButtonAssinadasVbox.setMarginRight(0);
        FPanelButtonAssinadasVbox.setMarginBottom(0);
        FPanelButtonAssinadasVbox.setBorderStyle("stNone");
        FPanelButtonAssinadasVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAssinadasVbox.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAssinadasVbox.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAssinadasVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAssinadasVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAssinadasVbox.setBoxShadowConfigOpacity(75);
        FPanelButtonAssinadasVbox.setBorderRadiusTopLeft(0);
        FPanelButtonAssinadasVbox.setBorderRadiusTopRight(0);
        FPanelButtonAssinadasVbox.setBorderRadiusBottomRight(0);
        FPanelButtonAssinadasVbox.setBorderRadiusBottomLeft(0);
        FPanelButtonAssinadasVbox.setColor("clBtnFace");
        FPanelButtonAssinadasVbox.setWordBreak(false);
        FPanelButtonAssinadasItems.addChildren(FPanelButtonAssinadasVbox);
        FPanelButtonAssinadasVbox.applyProperties();

        TFPanelButtonItem FPanelButtonAssinadasLblTotal = new TFPanelButtonItem();
        FPanelButtonAssinadasLblTotal.setName("FPanelButtonAssinadasLblTotal");
        FPanelButtonAssinadasLblTotal.setCaption("0");
        FPanelButtonAssinadasLblTotal.setItemType("itLabel");
        FPanelButtonAssinadasLblTotal.setFontColor("clGreen");
        FPanelButtonAssinadasLblTotal.setFontSize(-15);
        FPanelButtonAssinadasLblTotal.setFontName("Tahoma");
        FPanelButtonAssinadasLblTotal.setFontStyle("[fsBold]");
        FPanelButtonAssinadasLblTotal.setItemAlign("iaCenter");
        FPanelButtonAssinadasLblTotal.setFlex(false);
        FPanelButtonAssinadasLblTotal.setItemVAlign("ivaTop");
        FPanelButtonAssinadasLblTotal.setPaddingTop(0);
        FPanelButtonAssinadasLblTotal.setPaddingLeft(0);
        FPanelButtonAssinadasLblTotal.setPaddingRight(0);
        FPanelButtonAssinadasLblTotal.setPaddingBottom(0);
        FPanelButtonAssinadasLblTotal.setMarginTop(0);
        FPanelButtonAssinadasLblTotal.setMarginLeft(0);
        FPanelButtonAssinadasLblTotal.setMarginRight(0);
        FPanelButtonAssinadasLblTotal.setMarginBottom(0);
        FPanelButtonAssinadasLblTotal.setBorderStyle("stNone");
        FPanelButtonAssinadasLblTotal.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAssinadasLblTotal.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAssinadasLblTotal.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAssinadasLblTotal.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAssinadasLblTotal.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAssinadasLblTotal.setBoxShadowConfigOpacity(75);
        FPanelButtonAssinadasLblTotal.setBorderRadiusTopLeft(0);
        FPanelButtonAssinadasLblTotal.setBorderRadiusTopRight(0);
        FPanelButtonAssinadasLblTotal.setBorderRadiusBottomRight(0);
        FPanelButtonAssinadasLblTotal.setBorderRadiusBottomLeft(0);
        FPanelButtonAssinadasLblTotal.setColor("clBtnFace");
        FPanelButtonAssinadasLblTotal.setWordBreak(false);
        FPanelButtonAssinadasVbox.addChildren(FPanelButtonAssinadasLblTotal);
        FPanelButtonAssinadasLblTotal.applyProperties();

        TFPanelButtonItem FPanelButtonAssinadasImg = new TFPanelButtonItem();
        FPanelButtonAssinadasImg.setName("FPanelButtonAssinadasImg");
        FPanelButtonAssinadasImg.setCaption("FPanelButtonItem2");
        FPanelButtonAssinadasImg.setItemType("itImage");
        FPanelButtonAssinadasImg.setFontColor("clWindowText");
        FPanelButtonAssinadasImg.setFontSize(-13);
        FPanelButtonAssinadasImg.setFontName("Tahoma");
        FPanelButtonAssinadasImg.setFontStyle("[]");
        FPanelButtonAssinadasImg.setItemAlign("iaCenter");
        FPanelButtonAssinadasImg.setImageSrc("/images/crmservice474011.png");
        FPanelButtonAssinadasImg.setFlex(false);
        FPanelButtonAssinadasImg.setWidth(25);
        FPanelButtonAssinadasImg.setHeight(25);
        FPanelButtonAssinadasImg.setItemVAlign("ivaTop");
        FPanelButtonAssinadasImg.setPaddingTop(0);
        FPanelButtonAssinadasImg.setPaddingLeft(0);
        FPanelButtonAssinadasImg.setPaddingRight(0);
        FPanelButtonAssinadasImg.setPaddingBottom(0);
        FPanelButtonAssinadasImg.setMarginTop(0);
        FPanelButtonAssinadasImg.setMarginLeft(0);
        FPanelButtonAssinadasImg.setMarginRight(0);
        FPanelButtonAssinadasImg.setMarginBottom(0);
        FPanelButtonAssinadasImg.setBorderStyle("stNone");
        FPanelButtonAssinadasImg.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAssinadasImg.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAssinadasImg.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAssinadasImg.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAssinadasImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAssinadasImg.setBoxShadowConfigOpacity(75);
        FPanelButtonAssinadasImg.setBorderRadiusTopLeft(0);
        FPanelButtonAssinadasImg.setBorderRadiusTopRight(0);
        FPanelButtonAssinadasImg.setBorderRadiusBottomRight(0);
        FPanelButtonAssinadasImg.setBorderRadiusBottomLeft(0);
        FPanelButtonAssinadasImg.setColor("clBtnFace");
        FPanelButtonAssinadasImg.setWordBreak(false);
        FPanelButtonAssinadasVbox.addChildren(FPanelButtonAssinadasImg);
        FPanelButtonAssinadasImg.applyProperties();

        TFPanelButtonItem FPanelButtonAssinadasLblDescricao = new TFPanelButtonItem();
        FPanelButtonAssinadasLblDescricao.setName("FPanelButtonAssinadasLblDescricao");
        FPanelButtonAssinadasLblDescricao.setCaption("Assinados");
        FPanelButtonAssinadasLblDescricao.setItemType("itLabel");
        FPanelButtonAssinadasLblDescricao.setFontColor("clWindowText");
        FPanelButtonAssinadasLblDescricao.setFontSize(-11);
        FPanelButtonAssinadasLblDescricao.setFontName("Tahoma");
        FPanelButtonAssinadasLblDescricao.setFontStyle("[]");
        FPanelButtonAssinadasLblDescricao.setItemAlign("iaCenter");
        FPanelButtonAssinadasLblDescricao.setFlex(false);
        FPanelButtonAssinadasLblDescricao.setItemVAlign("ivaBottom");
        FPanelButtonAssinadasLblDescricao.setPaddingTop(0);
        FPanelButtonAssinadasLblDescricao.setPaddingLeft(0);
        FPanelButtonAssinadasLblDescricao.setPaddingRight(0);
        FPanelButtonAssinadasLblDescricao.setPaddingBottom(0);
        FPanelButtonAssinadasLblDescricao.setMarginTop(0);
        FPanelButtonAssinadasLblDescricao.setMarginLeft(0);
        FPanelButtonAssinadasLblDescricao.setMarginRight(0);
        FPanelButtonAssinadasLblDescricao.setMarginBottom(0);
        FPanelButtonAssinadasLblDescricao.setBorderStyle("stNone");
        FPanelButtonAssinadasLblDescricao.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonAssinadasLblDescricao.setBoxShadowConfigVerticalLength(10);
        FPanelButtonAssinadasLblDescricao.setBoxShadowConfigBlurRadius(5);
        FPanelButtonAssinadasLblDescricao.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonAssinadasLblDescricao.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonAssinadasLblDescricao.setBoxShadowConfigOpacity(75);
        FPanelButtonAssinadasLblDescricao.setBorderRadiusTopLeft(0);
        FPanelButtonAssinadasLblDescricao.setBorderRadiusTopRight(0);
        FPanelButtonAssinadasLblDescricao.setBorderRadiusBottomRight(0);
        FPanelButtonAssinadasLblDescricao.setBorderRadiusBottomLeft(0);
        FPanelButtonAssinadasLblDescricao.setColor("clBtnFace");
        FPanelButtonAssinadasLblDescricao.setWordBreak(false);
        FPanelButtonAssinadasVbox.addChildren(FPanelButtonAssinadasLblDescricao);
        FPanelButtonAssinadasLblDescricao.applyProperties();
        FPanelButtonAssinadas.applyProperties();
    }

    public TFPanelButton FPanelButtonCanceladas = new TFPanelButton();

    private void init_FPanelButtonCanceladas() {
        FPanelButtonCanceladas.setName("FPanelButtonCanceladas");
        FPanelButtonCanceladas.setLeft(219);
        FPanelButtonCanceladas.setTop(0);
        FPanelButtonCanceladas.setWidth(70);
        FPanelButtonCanceladas.setHeight(80);
        FPanelButtonCanceladas.setBorderStyle("stBoxShadow");
        FPanelButtonCanceladas.setPaddingTop(0);
        FPanelButtonCanceladas.setPaddingLeft(0);
        FPanelButtonCanceladas.setPaddingRight(0);
        FPanelButtonCanceladas.setPaddingBottom(0);
        FPanelButtonCanceladas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelButtonCanceladasClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "FPanelButtonCanceladas", "OnClick");
        });
        FPanelButtonCanceladas.setMarginTop(0);
        FPanelButtonCanceladas.setMarginLeft(0);
        FPanelButtonCanceladas.setMarginRight(0);
        FPanelButtonCanceladas.setMarginBottom(0);
        FPanelButtonCanceladas.setBoxShadowConfigHorizontalLength(0);
        FPanelButtonCanceladas.setBoxShadowConfigVerticalLength(7);
        FPanelButtonCanceladas.setBoxShadowConfigBlurRadius(10);
        FPanelButtonCanceladas.setBoxShadowConfigSpreadRadius(-5);
        FPanelButtonCanceladas.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonCanceladas.setBoxShadowConfigOpacity(75);
        FPanelButtonCanceladas.setBorderRadiusTopLeft(0);
        FPanelButtonCanceladas.setBorderRadiusTopRight(0);
        FPanelButtonCanceladas.setBorderRadiusBottomRight(0);
        FPanelButtonCanceladas.setBorderRadiusBottomLeft(0);
        FPanelButtonCanceladas.setToggle(true);
        FPanelButtonCanceladas.setToggleColor("clSilver");
        FHBox2.addChildren(FPanelButtonCanceladas);

        TFFastDesignCmpItems FPanelButtonCanceladasItems = new TFFastDesignCmpItems();
        FPanelButtonCanceladasItems.setName("FPanelButtonCanceladasItems");
        FPanelButtonCanceladas.addChildren(FPanelButtonCanceladasItems);
        FPanelButtonCanceladasItems.applyProperties();

        TFPanelButtonItem FPanelButtonCanceladasVbox = new TFPanelButtonItem();
        FPanelButtonCanceladasVbox.setName("FPanelButtonCanceladasVbox");
        FPanelButtonCanceladasVbox.setCaption("FPanelButtonItem1");
        FPanelButtonCanceladasVbox.setItemType("itVBox");
        FPanelButtonCanceladasVbox.setFontColor("clWindowText");
        FPanelButtonCanceladasVbox.setFontSize(-13);
        FPanelButtonCanceladasVbox.setFontName("Tahoma");
        FPanelButtonCanceladasVbox.setFontStyle("[]");
        FPanelButtonCanceladasVbox.setItemAlign("iaLeft");
        FPanelButtonCanceladasVbox.setFlex(false);
        FPanelButtonCanceladasVbox.setItemVAlign("ivaTop");
        FPanelButtonCanceladasVbox.setPaddingTop(0);
        FPanelButtonCanceladasVbox.setPaddingLeft(0);
        FPanelButtonCanceladasVbox.setPaddingRight(0);
        FPanelButtonCanceladasVbox.setPaddingBottom(0);
        FPanelButtonCanceladasVbox.setMarginTop(0);
        FPanelButtonCanceladasVbox.setMarginLeft(0);
        FPanelButtonCanceladasVbox.setMarginRight(0);
        FPanelButtonCanceladasVbox.setMarginBottom(0);
        FPanelButtonCanceladasVbox.setBorderStyle("stNone");
        FPanelButtonCanceladasVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonCanceladasVbox.setBoxShadowConfigVerticalLength(10);
        FPanelButtonCanceladasVbox.setBoxShadowConfigBlurRadius(5);
        FPanelButtonCanceladasVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonCanceladasVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonCanceladasVbox.setBoxShadowConfigOpacity(75);
        FPanelButtonCanceladasVbox.setBorderRadiusTopLeft(0);
        FPanelButtonCanceladasVbox.setBorderRadiusTopRight(0);
        FPanelButtonCanceladasVbox.setBorderRadiusBottomRight(0);
        FPanelButtonCanceladasVbox.setBorderRadiusBottomLeft(0);
        FPanelButtonCanceladasVbox.setColor("clBtnFace");
        FPanelButtonCanceladasVbox.setWordBreak(false);
        FPanelButtonCanceladasItems.addChildren(FPanelButtonCanceladasVbox);
        FPanelButtonCanceladasVbox.applyProperties();

        TFPanelButtonItem FPanelButtonCanceladasLblTotal = new TFPanelButtonItem();
        FPanelButtonCanceladasLblTotal.setName("FPanelButtonCanceladasLblTotal");
        FPanelButtonCanceladasLblTotal.setCaption("0");
        FPanelButtonCanceladasLblTotal.setItemType("itLabel");
        FPanelButtonCanceladasLblTotal.setFontColor("clRed");
        FPanelButtonCanceladasLblTotal.setFontSize(-15);
        FPanelButtonCanceladasLblTotal.setFontName("Tahoma");
        FPanelButtonCanceladasLblTotal.setFontStyle("[fsBold]");
        FPanelButtonCanceladasLblTotal.setItemAlign("iaCenter");
        FPanelButtonCanceladasLblTotal.setFlex(false);
        FPanelButtonCanceladasLblTotal.setItemVAlign("ivaTop");
        FPanelButtonCanceladasLblTotal.setPaddingTop(0);
        FPanelButtonCanceladasLblTotal.setPaddingLeft(0);
        FPanelButtonCanceladasLblTotal.setPaddingRight(0);
        FPanelButtonCanceladasLblTotal.setPaddingBottom(0);
        FPanelButtonCanceladasLblTotal.setMarginTop(0);
        FPanelButtonCanceladasLblTotal.setMarginLeft(0);
        FPanelButtonCanceladasLblTotal.setMarginRight(0);
        FPanelButtonCanceladasLblTotal.setMarginBottom(0);
        FPanelButtonCanceladasLblTotal.setBorderStyle("stNone");
        FPanelButtonCanceladasLblTotal.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonCanceladasLblTotal.setBoxShadowConfigVerticalLength(10);
        FPanelButtonCanceladasLblTotal.setBoxShadowConfigBlurRadius(5);
        FPanelButtonCanceladasLblTotal.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonCanceladasLblTotal.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonCanceladasLblTotal.setBoxShadowConfigOpacity(75);
        FPanelButtonCanceladasLblTotal.setBorderRadiusTopLeft(0);
        FPanelButtonCanceladasLblTotal.setBorderRadiusTopRight(0);
        FPanelButtonCanceladasLblTotal.setBorderRadiusBottomRight(0);
        FPanelButtonCanceladasLblTotal.setBorderRadiusBottomLeft(0);
        FPanelButtonCanceladasLblTotal.setColor("clBtnFace");
        FPanelButtonCanceladasLblTotal.setWordBreak(false);
        FPanelButtonCanceladasVbox.addChildren(FPanelButtonCanceladasLblTotal);
        FPanelButtonCanceladasLblTotal.applyProperties();

        TFPanelButtonItem FPanelButtonCanceladasImg = new TFPanelButtonItem();
        FPanelButtonCanceladasImg.setName("FPanelButtonCanceladasImg");
        FPanelButtonCanceladasImg.setCaption("FPanelButtonItem2");
        FPanelButtonCanceladasImg.setItemType("itImage");
        FPanelButtonCanceladasImg.setFontColor("clWindowText");
        FPanelButtonCanceladasImg.setFontSize(-13);
        FPanelButtonCanceladasImg.setFontName("Tahoma");
        FPanelButtonCanceladasImg.setFontStyle("[]");
        FPanelButtonCanceladasImg.setItemAlign("iaCenter");
        FPanelButtonCanceladasImg.setImageSrc("/images/crmservice474013.png");
        FPanelButtonCanceladasImg.setFlex(false);
        FPanelButtonCanceladasImg.setWidth(25);
        FPanelButtonCanceladasImg.setHeight(25);
        FPanelButtonCanceladasImg.setItemVAlign("ivaTop");
        FPanelButtonCanceladasImg.setPaddingTop(0);
        FPanelButtonCanceladasImg.setPaddingLeft(0);
        FPanelButtonCanceladasImg.setPaddingRight(0);
        FPanelButtonCanceladasImg.setPaddingBottom(0);
        FPanelButtonCanceladasImg.setMarginTop(0);
        FPanelButtonCanceladasImg.setMarginLeft(0);
        FPanelButtonCanceladasImg.setMarginRight(0);
        FPanelButtonCanceladasImg.setMarginBottom(0);
        FPanelButtonCanceladasImg.setBorderStyle("stNone");
        FPanelButtonCanceladasImg.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonCanceladasImg.setBoxShadowConfigVerticalLength(10);
        FPanelButtonCanceladasImg.setBoxShadowConfigBlurRadius(5);
        FPanelButtonCanceladasImg.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonCanceladasImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonCanceladasImg.setBoxShadowConfigOpacity(75);
        FPanelButtonCanceladasImg.setBorderRadiusTopLeft(0);
        FPanelButtonCanceladasImg.setBorderRadiusTopRight(0);
        FPanelButtonCanceladasImg.setBorderRadiusBottomRight(0);
        FPanelButtonCanceladasImg.setBorderRadiusBottomLeft(0);
        FPanelButtonCanceladasImg.setColor("clBtnFace");
        FPanelButtonCanceladasImg.setWordBreak(false);
        FPanelButtonCanceladasVbox.addChildren(FPanelButtonCanceladasImg);
        FPanelButtonCanceladasImg.applyProperties();

        TFPanelButtonItem FPanelButtonCanceladasLblDescricao = new TFPanelButtonItem();
        FPanelButtonCanceladasLblDescricao.setName("FPanelButtonCanceladasLblDescricao");
        FPanelButtonCanceladasLblDescricao.setCaption("Cancelados");
        FPanelButtonCanceladasLblDescricao.setItemType("itLabel");
        FPanelButtonCanceladasLblDescricao.setFontColor("clWindowText");
        FPanelButtonCanceladasLblDescricao.setFontSize(-11);
        FPanelButtonCanceladasLblDescricao.setFontName("Tahoma");
        FPanelButtonCanceladasLblDescricao.setFontStyle("[]");
        FPanelButtonCanceladasLblDescricao.setItemAlign("iaCenter");
        FPanelButtonCanceladasLblDescricao.setFlex(false);
        FPanelButtonCanceladasLblDescricao.setItemVAlign("ivaBottom");
        FPanelButtonCanceladasLblDescricao.setPaddingTop(0);
        FPanelButtonCanceladasLblDescricao.setPaddingLeft(0);
        FPanelButtonCanceladasLblDescricao.setPaddingRight(0);
        FPanelButtonCanceladasLblDescricao.setPaddingBottom(0);
        FPanelButtonCanceladasLblDescricao.setMarginTop(0);
        FPanelButtonCanceladasLblDescricao.setMarginLeft(0);
        FPanelButtonCanceladasLblDescricao.setMarginRight(0);
        FPanelButtonCanceladasLblDescricao.setMarginBottom(0);
        FPanelButtonCanceladasLblDescricao.setBorderStyle("stNone");
        FPanelButtonCanceladasLblDescricao.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonCanceladasLblDescricao.setBoxShadowConfigVerticalLength(10);
        FPanelButtonCanceladasLblDescricao.setBoxShadowConfigBlurRadius(5);
        FPanelButtonCanceladasLblDescricao.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonCanceladasLblDescricao.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonCanceladasLblDescricao.setBoxShadowConfigOpacity(75);
        FPanelButtonCanceladasLblDescricao.setBorderRadiusTopLeft(0);
        FPanelButtonCanceladasLblDescricao.setBorderRadiusTopRight(0);
        FPanelButtonCanceladasLblDescricao.setBorderRadiusBottomRight(0);
        FPanelButtonCanceladasLblDescricao.setBorderRadiusBottomLeft(0);
        FPanelButtonCanceladasLblDescricao.setColor("clBtnFace");
        FPanelButtonCanceladasLblDescricao.setWordBreak(true);
        FPanelButtonCanceladasVbox.addChildren(FPanelButtonCanceladasLblDescricao);
        FPanelButtonCanceladasLblDescricao.applyProperties();
        FPanelButtonCanceladas.applyProperties();
    }

    public TFPanelButton FPanelButtonExpiradas = new TFPanelButton();

    private void init_FPanelButtonExpiradas() {
        FPanelButtonExpiradas.setName("FPanelButtonExpiradas");
        FPanelButtonExpiradas.setLeft(289);
        FPanelButtonExpiradas.setTop(0);
        FPanelButtonExpiradas.setWidth(70);
        FPanelButtonExpiradas.setHeight(80);
        FPanelButtonExpiradas.setBorderStyle("stBoxShadow");
        FPanelButtonExpiradas.setPaddingTop(0);
        FPanelButtonExpiradas.setPaddingLeft(0);
        FPanelButtonExpiradas.setPaddingRight(0);
        FPanelButtonExpiradas.setPaddingBottom(0);
        FPanelButtonExpiradas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelButtonExpiradasClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "FPanelButtonExpiradas", "OnClick");
        });
        FPanelButtonExpiradas.setMarginTop(0);
        FPanelButtonExpiradas.setMarginLeft(0);
        FPanelButtonExpiradas.setMarginRight(0);
        FPanelButtonExpiradas.setMarginBottom(0);
        FPanelButtonExpiradas.setBoxShadowConfigHorizontalLength(0);
        FPanelButtonExpiradas.setBoxShadowConfigVerticalLength(7);
        FPanelButtonExpiradas.setBoxShadowConfigBlurRadius(10);
        FPanelButtonExpiradas.setBoxShadowConfigSpreadRadius(-5);
        FPanelButtonExpiradas.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonExpiradas.setBoxShadowConfigOpacity(75);
        FPanelButtonExpiradas.setBorderRadiusTopLeft(0);
        FPanelButtonExpiradas.setBorderRadiusTopRight(0);
        FPanelButtonExpiradas.setBorderRadiusBottomRight(0);
        FPanelButtonExpiradas.setBorderRadiusBottomLeft(0);
        FPanelButtonExpiradas.setToggle(true);
        FPanelButtonExpiradas.setToggleColor("clSilver");
        FHBox2.addChildren(FPanelButtonExpiradas);

        TFFastDesignCmpItems FPanelButtonExpiradasItems = new TFFastDesignCmpItems();
        FPanelButtonExpiradasItems.setName("FPanelButtonExpiradasItems");
        FPanelButtonExpiradas.addChildren(FPanelButtonExpiradasItems);
        FPanelButtonExpiradasItems.applyProperties();

        TFPanelButtonItem FPanelButtonItem1 = new TFPanelButtonItem();
        FPanelButtonItem1.setName("FPanelButtonItem1");
        FPanelButtonItem1.setCaption("FPanelButtonItem1");
        FPanelButtonItem1.setItemType("itVBox");
        FPanelButtonItem1.setFontColor("clWindowText");
        FPanelButtonItem1.setFontSize(-13);
        FPanelButtonItem1.setFontName("Tahoma");
        FPanelButtonItem1.setFontStyle("[]");
        FPanelButtonItem1.setItemAlign("iaLeft");
        FPanelButtonItem1.setFlex(false);
        FPanelButtonItem1.setItemVAlign("ivaTop");
        FPanelButtonItem1.setPaddingTop(0);
        FPanelButtonItem1.setPaddingLeft(0);
        FPanelButtonItem1.setPaddingRight(0);
        FPanelButtonItem1.setPaddingBottom(0);
        FPanelButtonItem1.setMarginTop(0);
        FPanelButtonItem1.setMarginLeft(0);
        FPanelButtonItem1.setMarginRight(0);
        FPanelButtonItem1.setMarginBottom(0);
        FPanelButtonItem1.setBorderStyle("stNone");
        FPanelButtonItem1.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonItem1.setBoxShadowConfigVerticalLength(10);
        FPanelButtonItem1.setBoxShadowConfigBlurRadius(5);
        FPanelButtonItem1.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonItem1.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonItem1.setBoxShadowConfigOpacity(75);
        FPanelButtonItem1.setBorderRadiusTopLeft(0);
        FPanelButtonItem1.setBorderRadiusTopRight(0);
        FPanelButtonItem1.setBorderRadiusBottomRight(0);
        FPanelButtonItem1.setBorderRadiusBottomLeft(0);
        FPanelButtonItem1.setColor("clBtnFace");
        FPanelButtonItem1.setWordBreak(false);
        FPanelButtonExpiradasItems.addChildren(FPanelButtonItem1);
        FPanelButtonItem1.applyProperties();

        TFPanelButtonItem FPanelButtonExpiradasLblTotal = new TFPanelButtonItem();
        FPanelButtonExpiradasLblTotal.setName("FPanelButtonExpiradasLblTotal");
        FPanelButtonExpiradasLblTotal.setCaption("0");
        FPanelButtonExpiradasLblTotal.setItemType("itLabel");
        FPanelButtonExpiradasLblTotal.setFontColor("clRed");
        FPanelButtonExpiradasLblTotal.setFontSize(-15);
        FPanelButtonExpiradasLblTotal.setFontName("Tahoma");
        FPanelButtonExpiradasLblTotal.setFontStyle("[fsBold]");
        FPanelButtonExpiradasLblTotal.setItemAlign("iaCenter");
        FPanelButtonExpiradasLblTotal.setFlex(false);
        FPanelButtonExpiradasLblTotal.setItemVAlign("ivaTop");
        FPanelButtonExpiradasLblTotal.setPaddingTop(0);
        FPanelButtonExpiradasLblTotal.setPaddingLeft(0);
        FPanelButtonExpiradasLblTotal.setPaddingRight(0);
        FPanelButtonExpiradasLblTotal.setPaddingBottom(0);
        FPanelButtonExpiradasLblTotal.setMarginTop(0);
        FPanelButtonExpiradasLblTotal.setMarginLeft(0);
        FPanelButtonExpiradasLblTotal.setMarginRight(0);
        FPanelButtonExpiradasLblTotal.setMarginBottom(0);
        FPanelButtonExpiradasLblTotal.setBorderStyle("stNone");
        FPanelButtonExpiradasLblTotal.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonExpiradasLblTotal.setBoxShadowConfigVerticalLength(10);
        FPanelButtonExpiradasLblTotal.setBoxShadowConfigBlurRadius(5);
        FPanelButtonExpiradasLblTotal.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonExpiradasLblTotal.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonExpiradasLblTotal.setBoxShadowConfigOpacity(75);
        FPanelButtonExpiradasLblTotal.setBorderRadiusTopLeft(0);
        FPanelButtonExpiradasLblTotal.setBorderRadiusTopRight(0);
        FPanelButtonExpiradasLblTotal.setBorderRadiusBottomRight(0);
        FPanelButtonExpiradasLblTotal.setBorderRadiusBottomLeft(0);
        FPanelButtonExpiradasLblTotal.setColor("clBtnFace");
        FPanelButtonExpiradasLblTotal.setWordBreak(false);
        FPanelButtonItem1.addChildren(FPanelButtonExpiradasLblTotal);
        FPanelButtonExpiradasLblTotal.applyProperties();

        TFPanelButtonItem FPanelButtonExpiradasImage = new TFPanelButtonItem();
        FPanelButtonExpiradasImage.setName("FPanelButtonExpiradasImage");
        FPanelButtonExpiradasImage.setCaption("FPanelButtonItem2");
        FPanelButtonExpiradasImage.setItemType("itImage");
        FPanelButtonExpiradasImage.setFontColor("clWindowText");
        FPanelButtonExpiradasImage.setFontSize(-13);
        FPanelButtonExpiradasImage.setFontName("Tahoma");
        FPanelButtonExpiradasImage.setFontStyle("[]");
        FPanelButtonExpiradasImage.setItemAlign("iaCenter");
        FPanelButtonExpiradasImage.setImageSrc("/images/crmservice553026.png");
        FPanelButtonExpiradasImage.setFlex(false);
        FPanelButtonExpiradasImage.setWidth(25);
        FPanelButtonExpiradasImage.setHeight(25);
        FPanelButtonExpiradasImage.setItemVAlign("ivaTop");
        FPanelButtonExpiradasImage.setPaddingTop(0);
        FPanelButtonExpiradasImage.setPaddingLeft(0);
        FPanelButtonExpiradasImage.setPaddingRight(0);
        FPanelButtonExpiradasImage.setPaddingBottom(0);
        FPanelButtonExpiradasImage.setMarginTop(0);
        FPanelButtonExpiradasImage.setMarginLeft(0);
        FPanelButtonExpiradasImage.setMarginRight(0);
        FPanelButtonExpiradasImage.setMarginBottom(0);
        FPanelButtonExpiradasImage.setBorderStyle("stNone");
        FPanelButtonExpiradasImage.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonExpiradasImage.setBoxShadowConfigVerticalLength(10);
        FPanelButtonExpiradasImage.setBoxShadowConfigBlurRadius(5);
        FPanelButtonExpiradasImage.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonExpiradasImage.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonExpiradasImage.setBoxShadowConfigOpacity(75);
        FPanelButtonExpiradasImage.setBorderRadiusTopLeft(0);
        FPanelButtonExpiradasImage.setBorderRadiusTopRight(0);
        FPanelButtonExpiradasImage.setBorderRadiusBottomRight(0);
        FPanelButtonExpiradasImage.setBorderRadiusBottomLeft(0);
        FPanelButtonExpiradasImage.setColor("clBtnFace");
        FPanelButtonExpiradasImage.setWordBreak(false);
        FPanelButtonItem1.addChildren(FPanelButtonExpiradasImage);
        FPanelButtonExpiradasImage.applyProperties();

        TFPanelButtonItem FPanelButtonExpiradasLblDescricao = new TFPanelButtonItem();
        FPanelButtonExpiradasLblDescricao.setName("FPanelButtonExpiradasLblDescricao");
        FPanelButtonExpiradasLblDescricao.setCaption("Expirados");
        FPanelButtonExpiradasLblDescricao.setItemType("itLabel");
        FPanelButtonExpiradasLblDescricao.setFontColor("clWindowText");
        FPanelButtonExpiradasLblDescricao.setFontSize(-11);
        FPanelButtonExpiradasLblDescricao.setFontName("Tahoma");
        FPanelButtonExpiradasLblDescricao.setFontStyle("[]");
        FPanelButtonExpiradasLblDescricao.setItemAlign("iaCenter");
        FPanelButtonExpiradasLblDescricao.setFlex(false);
        FPanelButtonExpiradasLblDescricao.setItemVAlign("ivaBottom");
        FPanelButtonExpiradasLblDescricao.setPaddingTop(0);
        FPanelButtonExpiradasLblDescricao.setPaddingLeft(0);
        FPanelButtonExpiradasLblDescricao.setPaddingRight(0);
        FPanelButtonExpiradasLblDescricao.setPaddingBottom(0);
        FPanelButtonExpiradasLblDescricao.setMarginTop(0);
        FPanelButtonExpiradasLblDescricao.setMarginLeft(0);
        FPanelButtonExpiradasLblDescricao.setMarginRight(0);
        FPanelButtonExpiradasLblDescricao.setMarginBottom(0);
        FPanelButtonExpiradasLblDescricao.setBorderStyle("stNone");
        FPanelButtonExpiradasLblDescricao.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonExpiradasLblDescricao.setBoxShadowConfigVerticalLength(10);
        FPanelButtonExpiradasLblDescricao.setBoxShadowConfigBlurRadius(5);
        FPanelButtonExpiradasLblDescricao.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonExpiradasLblDescricao.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonExpiradasLblDescricao.setBoxShadowConfigOpacity(75);
        FPanelButtonExpiradasLblDescricao.setBorderRadiusTopLeft(0);
        FPanelButtonExpiradasLblDescricao.setBorderRadiusTopRight(0);
        FPanelButtonExpiradasLblDescricao.setBorderRadiusBottomRight(0);
        FPanelButtonExpiradasLblDescricao.setBorderRadiusBottomLeft(0);
        FPanelButtonExpiradasLblDescricao.setColor("clBtnFace");
        FPanelButtonExpiradasLblDescricao.setWordBreak(false);
        FPanelButtonItem1.addChildren(FPanelButtonExpiradasLblDescricao);
        FPanelButtonExpiradasLblDescricao.applyProperties();
        FPanelButtonExpiradas.applyProperties();
    }

    public TFPanelButton FPanelButtonFalhas = new TFPanelButton();

    private void init_FPanelButtonFalhas() {
        FPanelButtonFalhas.setName("FPanelButtonFalhas");
        FPanelButtonFalhas.setLeft(359);
        FPanelButtonFalhas.setTop(0);
        FPanelButtonFalhas.setWidth(70);
        FPanelButtonFalhas.setHeight(80);
        FPanelButtonFalhas.setBorderStyle("stBoxShadow");
        FPanelButtonFalhas.setPaddingTop(0);
        FPanelButtonFalhas.setPaddingLeft(0);
        FPanelButtonFalhas.setPaddingRight(0);
        FPanelButtonFalhas.setPaddingBottom(0);
        FPanelButtonFalhas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FPanelButtonFalhasClick(event);
            processarFlow("FrmAssinaturaDigitalCentral", "FPanelButtonFalhas", "OnClick");
        });
        FPanelButtonFalhas.setMarginTop(0);
        FPanelButtonFalhas.setMarginLeft(0);
        FPanelButtonFalhas.setMarginRight(0);
        FPanelButtonFalhas.setMarginBottom(0);
        FPanelButtonFalhas.setBoxShadowConfigHorizontalLength(0);
        FPanelButtonFalhas.setBoxShadowConfigVerticalLength(7);
        FPanelButtonFalhas.setBoxShadowConfigBlurRadius(10);
        FPanelButtonFalhas.setBoxShadowConfigSpreadRadius(-5);
        FPanelButtonFalhas.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonFalhas.setBoxShadowConfigOpacity(75);
        FPanelButtonFalhas.setBorderRadiusTopLeft(0);
        FPanelButtonFalhas.setBorderRadiusTopRight(0);
        FPanelButtonFalhas.setBorderRadiusBottomRight(0);
        FPanelButtonFalhas.setBorderRadiusBottomLeft(0);
        FPanelButtonFalhas.setToggle(true);
        FPanelButtonFalhas.setToggleColor("clSilver");
        FHBox2.addChildren(FPanelButtonFalhas);

        TFFastDesignCmpItems FPanelButtonFalhasItems = new TFFastDesignCmpItems();
        FPanelButtonFalhasItems.setName("FPanelButtonFalhasItems");
        FPanelButtonFalhas.addChildren(FPanelButtonFalhasItems);
        FPanelButtonFalhasItems.applyProperties();

        TFPanelButtonItem FPanelButtonFalhasVbox = new TFPanelButtonItem();
        FPanelButtonFalhasVbox.setName("FPanelButtonFalhasVbox");
        FPanelButtonFalhasVbox.setCaption("FPanelButtonItem1");
        FPanelButtonFalhasVbox.setItemType("itVBox");
        FPanelButtonFalhasVbox.setFontColor("clWindowText");
        FPanelButtonFalhasVbox.setFontSize(-13);
        FPanelButtonFalhasVbox.setFontName("Tahoma");
        FPanelButtonFalhasVbox.setFontStyle("[]");
        FPanelButtonFalhasVbox.setItemAlign("iaLeft");
        FPanelButtonFalhasVbox.setFlex(false);
        FPanelButtonFalhasVbox.setItemVAlign("ivaTop");
        FPanelButtonFalhasVbox.setPaddingTop(0);
        FPanelButtonFalhasVbox.setPaddingLeft(0);
        FPanelButtonFalhasVbox.setPaddingRight(0);
        FPanelButtonFalhasVbox.setPaddingBottom(0);
        FPanelButtonFalhasVbox.setMarginTop(0);
        FPanelButtonFalhasVbox.setMarginLeft(0);
        FPanelButtonFalhasVbox.setMarginRight(0);
        FPanelButtonFalhasVbox.setMarginBottom(0);
        FPanelButtonFalhasVbox.setBorderStyle("stNone");
        FPanelButtonFalhasVbox.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonFalhasVbox.setBoxShadowConfigVerticalLength(10);
        FPanelButtonFalhasVbox.setBoxShadowConfigBlurRadius(5);
        FPanelButtonFalhasVbox.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonFalhasVbox.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonFalhasVbox.setBoxShadowConfigOpacity(75);
        FPanelButtonFalhasVbox.setBorderRadiusTopLeft(0);
        FPanelButtonFalhasVbox.setBorderRadiusTopRight(0);
        FPanelButtonFalhasVbox.setBorderRadiusBottomRight(0);
        FPanelButtonFalhasVbox.setBorderRadiusBottomLeft(0);
        FPanelButtonFalhasVbox.setColor("clBtnFace");
        FPanelButtonFalhasVbox.setWordBreak(false);
        FPanelButtonFalhasItems.addChildren(FPanelButtonFalhasVbox);
        FPanelButtonFalhasVbox.applyProperties();

        TFPanelButtonItem FPanelButtonFalhasLblTotal = new TFPanelButtonItem();
        FPanelButtonFalhasLblTotal.setName("FPanelButtonFalhasLblTotal");
        FPanelButtonFalhasLblTotal.setCaption("0");
        FPanelButtonFalhasLblTotal.setItemType("itLabel");
        FPanelButtonFalhasLblTotal.setFontColor("clRed");
        FPanelButtonFalhasLblTotal.setFontSize(-15);
        FPanelButtonFalhasLblTotal.setFontName("Tahoma");
        FPanelButtonFalhasLblTotal.setFontStyle("[fsBold]");
        FPanelButtonFalhasLblTotal.setItemAlign("iaCenter");
        FPanelButtonFalhasLblTotal.setFlex(false);
        FPanelButtonFalhasLblTotal.setItemVAlign("ivaTop");
        FPanelButtonFalhasLblTotal.setPaddingTop(0);
        FPanelButtonFalhasLblTotal.setPaddingLeft(0);
        FPanelButtonFalhasLblTotal.setPaddingRight(0);
        FPanelButtonFalhasLblTotal.setPaddingBottom(0);
        FPanelButtonFalhasLblTotal.setMarginTop(0);
        FPanelButtonFalhasLblTotal.setMarginLeft(0);
        FPanelButtonFalhasLblTotal.setMarginRight(0);
        FPanelButtonFalhasLblTotal.setMarginBottom(0);
        FPanelButtonFalhasLblTotal.setBorderStyle("stNone");
        FPanelButtonFalhasLblTotal.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonFalhasLblTotal.setBoxShadowConfigVerticalLength(10);
        FPanelButtonFalhasLblTotal.setBoxShadowConfigBlurRadius(5);
        FPanelButtonFalhasLblTotal.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonFalhasLblTotal.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonFalhasLblTotal.setBoxShadowConfigOpacity(75);
        FPanelButtonFalhasLblTotal.setBorderRadiusTopLeft(0);
        FPanelButtonFalhasLblTotal.setBorderRadiusTopRight(0);
        FPanelButtonFalhasLblTotal.setBorderRadiusBottomRight(0);
        FPanelButtonFalhasLblTotal.setBorderRadiusBottomLeft(0);
        FPanelButtonFalhasLblTotal.setColor("clBtnFace");
        FPanelButtonFalhasLblTotal.setWordBreak(false);
        FPanelButtonFalhasVbox.addChildren(FPanelButtonFalhasLblTotal);
        FPanelButtonFalhasLblTotal.applyProperties();

        TFPanelButtonItem FPanelButtonFalhasImg = new TFPanelButtonItem();
        FPanelButtonFalhasImg.setName("FPanelButtonFalhasImg");
        FPanelButtonFalhasImg.setCaption("FPanelButtonItem2");
        FPanelButtonFalhasImg.setItemType("itImage");
        FPanelButtonFalhasImg.setFontColor("clWindowText");
        FPanelButtonFalhasImg.setFontSize(-13);
        FPanelButtonFalhasImg.setFontName("Tahoma");
        FPanelButtonFalhasImg.setFontStyle("[]");
        FPanelButtonFalhasImg.setItemAlign("iaCenter");
        FPanelButtonFalhasImg.setImageSrc("/images/crmservice474012.png");
        FPanelButtonFalhasImg.setFlex(false);
        FPanelButtonFalhasImg.setWidth(25);
        FPanelButtonFalhasImg.setHeight(25);
        FPanelButtonFalhasImg.setItemVAlign("ivaTop");
        FPanelButtonFalhasImg.setPaddingTop(0);
        FPanelButtonFalhasImg.setPaddingLeft(0);
        FPanelButtonFalhasImg.setPaddingRight(0);
        FPanelButtonFalhasImg.setPaddingBottom(0);
        FPanelButtonFalhasImg.setMarginTop(0);
        FPanelButtonFalhasImg.setMarginLeft(0);
        FPanelButtonFalhasImg.setMarginRight(0);
        FPanelButtonFalhasImg.setMarginBottom(0);
        FPanelButtonFalhasImg.setBorderStyle("stNone");
        FPanelButtonFalhasImg.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonFalhasImg.setBoxShadowConfigVerticalLength(10);
        FPanelButtonFalhasImg.setBoxShadowConfigBlurRadius(5);
        FPanelButtonFalhasImg.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonFalhasImg.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonFalhasImg.setBoxShadowConfigOpacity(75);
        FPanelButtonFalhasImg.setBorderRadiusTopLeft(0);
        FPanelButtonFalhasImg.setBorderRadiusTopRight(0);
        FPanelButtonFalhasImg.setBorderRadiusBottomRight(0);
        FPanelButtonFalhasImg.setBorderRadiusBottomLeft(0);
        FPanelButtonFalhasImg.setColor("clBtnFace");
        FPanelButtonFalhasImg.setWordBreak(false);
        FPanelButtonFalhasVbox.addChildren(FPanelButtonFalhasImg);
        FPanelButtonFalhasImg.applyProperties();

        TFPanelButtonItem FPanelButtonFalhasLblDescricao = new TFPanelButtonItem();
        FPanelButtonFalhasLblDescricao.setName("FPanelButtonFalhasLblDescricao");
        FPanelButtonFalhasLblDescricao.setCaption("Falhas");
        FPanelButtonFalhasLblDescricao.setItemType("itLabel");
        FPanelButtonFalhasLblDescricao.setFontColor("clWindowText");
        FPanelButtonFalhasLblDescricao.setFontSize(-11);
        FPanelButtonFalhasLblDescricao.setFontName("Tahoma");
        FPanelButtonFalhasLblDescricao.setFontStyle("[]");
        FPanelButtonFalhasLblDescricao.setItemAlign("iaCenter");
        FPanelButtonFalhasLblDescricao.setFlex(false);
        FPanelButtonFalhasLblDescricao.setItemVAlign("ivaBottom");
        FPanelButtonFalhasLblDescricao.setPaddingTop(0);
        FPanelButtonFalhasLblDescricao.setPaddingLeft(0);
        FPanelButtonFalhasLblDescricao.setPaddingRight(0);
        FPanelButtonFalhasLblDescricao.setPaddingBottom(0);
        FPanelButtonFalhasLblDescricao.setMarginTop(0);
        FPanelButtonFalhasLblDescricao.setMarginLeft(0);
        FPanelButtonFalhasLblDescricao.setMarginRight(0);
        FPanelButtonFalhasLblDescricao.setMarginBottom(0);
        FPanelButtonFalhasLblDescricao.setBorderStyle("stNone");
        FPanelButtonFalhasLblDescricao.setBoxShadowConfigHorizontalLength(10);
        FPanelButtonFalhasLblDescricao.setBoxShadowConfigVerticalLength(10);
        FPanelButtonFalhasLblDescricao.setBoxShadowConfigBlurRadius(5);
        FPanelButtonFalhasLblDescricao.setBoxShadowConfigSpreadRadius(0);
        FPanelButtonFalhasLblDescricao.setBoxShadowConfigShadowColor("clBlack");
        FPanelButtonFalhasLblDescricao.setBoxShadowConfigOpacity(75);
        FPanelButtonFalhasLblDescricao.setBorderRadiusTopLeft(0);
        FPanelButtonFalhasLblDescricao.setBorderRadiusTopRight(0);
        FPanelButtonFalhasLblDescricao.setBorderRadiusBottomRight(0);
        FPanelButtonFalhasLblDescricao.setBorderRadiusBottomLeft(0);
        FPanelButtonFalhasLblDescricao.setColor("clBtnFace");
        FPanelButtonFalhasLblDescricao.setWordBreak(false);
        FPanelButtonFalhasVbox.addChildren(FPanelButtonFalhasLblDescricao);
        FPanelButtonFalhasLblDescricao.applyProperties();
        FPanelButtonFalhas.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(429);
        FHBox4.setTop(0);
        FHBox4.setWidth(9);
        FHBox4.setHeight(68);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftTrue");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox2.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFGrid gridAssinaturas = new TFGrid();

    private void init_gridAssinaturas() {
        gridAssinaturas.setName("gridAssinaturas");
        gridAssinaturas.setLeft(0);
        gridAssinaturas.setTop(238);
        gridAssinaturas.setWidth(568);
        gridAssinaturas.setHeight(184);
        gridAssinaturas.setTable(tbCentralAssinaturaGrid);
        gridAssinaturas.setFlexVflex("ftTrue");
        gridAssinaturas.setFlexHflex("ftTrue");
        gridAssinaturas.setPagingEnabled(true);
        gridAssinaturas.setFrozenColumns(0);
        gridAssinaturas.setShowFooter(false);
        gridAssinaturas.setShowHeader(true);
        gridAssinaturas.setMultiSelection(false);
        gridAssinaturas.setGroupingEnabled(false);
        gridAssinaturas.setGroupingExpanded(false);
        gridAssinaturas.setGroupingShowFooter(false);
        gridAssinaturas.setCrosstabEnabled(false);
        gridAssinaturas.setCrosstabGroupType("cgtConcat");
        gridAssinaturas.setEditionEnabled(false);
        gridAssinaturas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Tipo");
        item0.setWidth(106);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(30);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridAssinaturas.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DOCUMENTO");
        item1.setTitleCaption("Documento");
        item1.setWidth(88);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(40);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridAssinaturas.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("DATA_ENVIO");
        item2.setTitleCaption("Data");
        item2.setWidth(102);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFMaskExpression item3 = new TFMaskExpression();
        item3.setExpression("*");
        item3.setEvalType("etExpression");
        item3.setMask("dd/MM/yyyy");
        item3.setPadLength(0);
        item3.setPadDirection("pdNone");
        item3.setMaskType("mtDateTime");
        item2.getMasks().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridAssinaturas.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DESC_STATUS_LABEL");
        item4.setTitleCaption("Status");
        item4.setWidth(84);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taCenter");
        item4.setFieldType("ftString");
        item4.setFlexRatio(20);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridAssinaturas.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("ABRIR_ASSINATURA");
        item5.setTitleCaption(" ");
        item5.setWidth(46);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taCenter");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFImageExpression item6 = new TFImageExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setImageId(0);
        item6.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridAssinaturasAbrirAssinatura(event);
            processarFlow("FrmAssinaturaDigitalCentral", "item6", "OnClick");
        });
        item6.setIconClass("i fas fa-file-signature fa-lg");
        item5.getImages().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(false);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridAssinaturas.getColumns().add(item5);
        vboxPrincipal.addChildren(gridAssinaturas);
        gridAssinaturas.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void ftPeriodoChange(final Event<Object> event);

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExportarExcelClick(final Event<Object> event) {
        if (btnExportarExcel.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExportarExcel");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void FPanelButtonTodasClick(final Event<Object> event);

    public abstract void FPanelButtonAguardandoClick(final Event<Object> event);

    public abstract void FPanelButtonAssinadasClick(final Event<Object> event);

    public abstract void FPanelButtonCanceladasClick(final Event<Object> event);

    public abstract void FPanelButtonExpiradasClick(final Event<Object> event);

    public abstract void FPanelButtonFalhasClick(final Event<Object> event);

    public abstract void gridAssinaturasAbrirAssinatura(final Event<Object> event);

}