package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmLeadzapReceptivo extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.LeadzapReceptivoRNA rn = null;

    public FrmLeadzapReceptivo() {
        try {
            rn = (freedom.bytecode.rn.LeadzapReceptivoRNA) getRN(freedom.bytecode.rn.wizard.LeadzapReceptivoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbLeadzapItem();
        init_tbLeadzapArea();
        init_tbLeadzapMenu();
        init_tbTemplateQualificado();
        init_tbTemplateLead();
        init_tbTime();
        init_tbEventosTipo();
        init_tbLeadzapTemMembrosTime();
        init_tbLeadzapSetorMembroTime();
        init_tbTemplateLeadAprov();
        init_tbTemplateLeadReprov();
        init_tbCadastroWhatsapp();
        init_tbWhatsappEmpresa();
        init_tbReceptTimesMembrosEmpUser();
        init_vboxPrincipal();
        init_hboxBotoes();
        init_FHBox1();
        init_btnConsultar();
        init_btnNovo();
        init_btnAlterar();
        init_btnSalvar();
        init_btnCancelar();
        init_FHBox3();
        init_pgChatbot();
        init_tabListagem();
        init_FVBox1();
        init_gridPrincipal();
        init_tabCadastro();
        init_FVBox2();
        init_FHBox7();
        init_FGroupbox1();
        init_FHBox2();
        init_FHBox4();
        init_FHBox10();
        init_lblDescricao();
        init_edtDescricao();
        init_FHBox20();
        init_chkAtivo();
        init_FHBox8();
        init_FGroupbox3();
        init_FVBox9();
        init_FHBox5();
        init_FHBox14();
        init_FHBox18();
        init_FLabel3();
        init_cbbTemplateQualificado();
        init_FVBox3();
        init_chkMostraNome();
        init_chkMostraEmail();
        init_chkCadDuplo();
        init_FHBox9();
        init_FGroupbox2();
        init_FHBox15();
        init_FHBox16();
        init_FHBox17();
        init_FLabel2();
        init_cbbTemplateLead();
        init_FHBox36();
        init_FGroupbox5();
        init_FVBox10();
        init_FHBox33();
        init_FHBox34();
        init_FHBox35();
        init_FLabel7();
        init_cbbTemplateVendedor();
        init_FHBox24();
        init_FHBox37();
        init_FHBox38();
        init_FLabel8();
        init_cbbTemplateVendedorAprov();
        init_FHBox39();
        init_FHBox40();
        init_FHBox41();
        init_FLabel9();
        init_cbbTemplateVendedorReprov();
        init_tabMenu();
        init_FVBox4();
        init_gridItensMenu();
        init_FHBox6();
        init_FVBox5();
        init_FHBox11();
        init_btnNovoItem();
        init_btnAlterarItem();
        init_btnExcluirItem();
        init_btnSalvarItem();
        init_btnCancelarItem();
        init_pgItem();
        init_pgItemtabCadastro();
        init_FVBoxPgItemPrincipalCadastro();
        init_FHBox27();
        init_FHBox28();
        init_FHBox29();
        init_FLabel1();
        init_edtSequencia();
        init_FHBox12();
        init_FHBox13();
        init_FHBox19();
        init_lblDescricaoItem();
        init_edtDescricaoItem();
        init_FHBox21();
        init_FHBox22();
        init_FHBox23();
        init_lblArea();
        init_cbbArea();
        init_hboxTipoEvento();
        init_FHBox25();
        init_FHBox26();
        init_lblTipoEvento();
        init_cbbTipoEvento();
        init_pgItemtabTemplates();
        init_FVBoxPrincipalTemplatesPgItem();
        init_FVBoxPrincipalTemplatesPgItemItem1();
        init_FVBoxPrincipalTemplatesPgItemLabel1();
        init_FVBoxPrincipalTemplatesPgItemRecuo1();
        init_FLabelPrincipalTemplatesPgItem1();
        init_cbbIdTemplateBoasVIndasVendedor();
        init_FVBoxPrincipalTemplatesPgItemItem2();
        init_FVBoxPrincipalTemplatesPgItemLabel2();
        init_FVBoxPrincipalTemplatesPgItemRecuo2();
        init_FLabelPrincipalTemplatesPgItem2();
        init_cbbIdTemplateBoasVIndasConsultor();
        init_FVBoxPrincipalTemplatesPgItemItem3();
        init_FVBoxPrincipalTemplatesPgItemLabel3();
        init_FVBoxPrincipalTemplatesPgItemRecuo3();
        init_FLabelPrincipalTemplatesPgItem3();
        init_cbbIdTemplateJaExisteAtendimentoVendedor();
        init_FVBoxPrincipalTemplatesPgItemItem4();
        init_FVBoxPrincipalTemplatesPgItemLabel4();
        init_FVBoxPrincipalTemplatesPgItemRecuo4();
        init_FLabelPrincipalTemplatesPgItem4();
        init_cbbIdTemplateJaExisteAtendimentoConsultor();
        init_FVBox6();
        init_groupBoxAtendimento();
        init_FVBox8();
        init_FHBox30();
        init_FHBox31();
        init_FHBox32();
        init_FLabel4();
        init_FVBox7();
        init_cbbTime();
        init_FLabel6();
        init_FLabel5();
        init_chkUltimoAgenteEvento();
        init_chkEventoPerdido();
        init_chkCentralUnica();
        init_FrmLeadzapReceptivo();
    }

    public CRM_LEADZAP_ITEM tbLeadzapItem;

    private void init_tbLeadzapItem() {
        tbLeadzapItem = rn.tbLeadzapItem;
        tbLeadzapItem.setName("tbLeadzapItem");
        tbLeadzapItem.setMaxRowCount(200);
        tbLeadzapItem.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadzapItemAfterScroll(event);
            processarFlow("FrmLeadzapReceptivo", "tbLeadzapItem", "OnAfterScroll");
        });
        tbLeadzapItem.setWKey("4600506;46001");
        tbLeadzapItem.setRatioBatchSize(20);
        getTables().put(tbLeadzapItem, "tbLeadzapItem");
        tbLeadzapItem.applyProperties();
    }

    public CRM_LEADZAP_AREA tbLeadzapArea;

    private void init_tbLeadzapArea() {
        tbLeadzapArea = rn.tbLeadzapArea;
        tbLeadzapArea.setName("tbLeadzapArea");
        tbLeadzapArea.setMaxRowCount(200);
        tbLeadzapArea.setWKey("4600506;46002");
        tbLeadzapArea.setRatioBatchSize(20);
        getTables().put(tbLeadzapArea, "tbLeadzapArea");
        tbLeadzapArea.applyProperties();
    }

    public CRM_LEADZAP_MENU tbLeadzapMenu;

    private void init_tbLeadzapMenu() {
        tbLeadzapMenu = rn.tbLeadzapMenu;
        tbLeadzapMenu.setName("tbLeadzapMenu");
        tbLeadzapMenu.setMaxRowCount(200);
        tbLeadzapMenu.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadzapMenuAfterScroll(event);
            processarFlow("FrmLeadzapReceptivo", "tbLeadzapMenu", "OnAfterScroll");
        });
        tbLeadzapMenu.setWKey("4600506;46003");
        tbLeadzapMenu.setRatioBatchSize(20);
        getTables().put(tbLeadzapMenu, "tbLeadzapMenu");
        tbLeadzapMenu.applyProperties();
    }

    public CRM_EMAIL_MODELO tbTemplateQualificado;

    private void init_tbTemplateQualificado() {
        tbTemplateQualificado = rn.tbTemplateQualificado;
        tbTemplateQualificado.setName("tbTemplateQualificado");
        tbTemplateQualificado.setMaxRowCount(200);
        tbTemplateQualificado.setWKey("4600506;46004");
        tbTemplateQualificado.setRatioBatchSize(20);
        getTables().put(tbTemplateQualificado, "tbTemplateQualificado");
        tbTemplateQualificado.applyProperties();
    }

    public CRM_EMAIL_MODELO tbTemplateLead;

    private void init_tbTemplateLead() {
        tbTemplateLead = rn.tbTemplateLead;
        tbTemplateLead.setName("tbTemplateLead");
        tbTemplateLead.setMaxRowCount(200);
        tbTemplateLead.setWKey("4600506;46005");
        tbTemplateLead.setRatioBatchSize(20);
        getTables().put(tbTemplateLead, "tbTemplateLead");
        tbTemplateLead.applyProperties();
    }

    public CRM_TIME tbTime;

    private void init_tbTime() {
        tbTime = rn.tbTime;
        tbTime.setName("tbTime");
        tbTime.setMaxRowCount(200);
        tbTime.setWKey("4600506;46006");
        tbTime.setRatioBatchSize(20);
        getTables().put(tbTime, "tbTime");
        tbTime.applyProperties();
    }

    public CRM_EVENTOS_TIPO tbEventosTipo;

    private void init_tbEventosTipo() {
        tbEventosTipo = rn.tbEventosTipo;
        tbEventosTipo.setName("tbEventosTipo");
        tbEventosTipo.setMaxRowCount(600);
        tbEventosTipo.setWKey("4600506;46007");
        tbEventosTipo.setRatioBatchSize(20);
        getTables().put(tbEventosTipo, "tbEventosTipo");
        tbEventosTipo.applyProperties();
    }

    public LEADZAP_TEM_MEMBROS_TIME tbLeadzapTemMembrosTime;

    private void init_tbLeadzapTemMembrosTime() {
        tbLeadzapTemMembrosTime = rn.tbLeadzapTemMembrosTime;
        tbLeadzapTemMembrosTime.setName("tbLeadzapTemMembrosTime");
        tbLeadzapTemMembrosTime.setMaxRowCount(200);
        tbLeadzapTemMembrosTime.setWKey("4600506;46008");
        tbLeadzapTemMembrosTime.setRatioBatchSize(20);
        getTables().put(tbLeadzapTemMembrosTime, "tbLeadzapTemMembrosTime");
        tbLeadzapTemMembrosTime.applyProperties();
    }

    public LEADZAP_SETOR_MEMBRO_TIME tbLeadzapSetorMembroTime;

    private void init_tbLeadzapSetorMembroTime() {
        tbLeadzapSetorMembroTime = rn.tbLeadzapSetorMembroTime;
        tbLeadzapSetorMembroTime.setName("tbLeadzapSetorMembroTime");
        tbLeadzapSetorMembroTime.setMaxRowCount(200);
        tbLeadzapSetorMembroTime.addEventListener("onMaxRow", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadzapSetorMembroTimeMaxRow(event);
            processarFlow("FrmLeadzapReceptivo", "tbLeadzapSetorMembroTime", "OnMaxRow");
        });
        tbLeadzapSetorMembroTime.setWKey("4600506;46009");
        tbLeadzapSetorMembroTime.setRatioBatchSize(20);
        getTables().put(tbLeadzapSetorMembroTime, "tbLeadzapSetorMembroTime");
        tbLeadzapSetorMembroTime.applyProperties();
    }

    public CRM_EMAIL_MODELO tbTemplateLeadAprov;

    private void init_tbTemplateLeadAprov() {
        tbTemplateLeadAprov = rn.tbTemplateLeadAprov;
        tbTemplateLeadAprov.setName("tbTemplateLeadAprov");
        tbTemplateLeadAprov.setMaxRowCount(200);
        tbTemplateLeadAprov.setWKey("4600506;460010");
        tbTemplateLeadAprov.setRatioBatchSize(20);
        getTables().put(tbTemplateLeadAprov, "tbTemplateLeadAprov");
        tbTemplateLeadAprov.applyProperties();
    }

    public CRM_EMAIL_MODELO tbTemplateLeadReprov;

    private void init_tbTemplateLeadReprov() {
        tbTemplateLeadReprov = rn.tbTemplateLeadReprov;
        tbTemplateLeadReprov.setName("tbTemplateLeadReprov");
        tbTemplateLeadReprov.setMaxRowCount(200);
        tbTemplateLeadReprov.setWKey("4600506;460011");
        tbTemplateLeadReprov.setRatioBatchSize(20);
        getTables().put(tbTemplateLeadReprov, "tbTemplateLeadReprov");
        tbTemplateLeadReprov.applyProperties();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp;

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp = rn.tbCadastroWhatsapp;
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("4600506;460012");
        tbCadastroWhatsapp.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsapp, "tbCadastroWhatsapp");
        tbCadastroWhatsapp.applyProperties();
    }

    public CRM_WHATSAPP_EMPRESA tbWhatsappEmpresa;

    private void init_tbWhatsappEmpresa() {
        tbWhatsappEmpresa = rn.tbWhatsappEmpresa;
        tbWhatsappEmpresa.setName("tbWhatsappEmpresa");
        tbWhatsappEmpresa.setMaxRowCount(200);
        tbWhatsappEmpresa.setWKey("4600506;460013");
        tbWhatsappEmpresa.setRatioBatchSize(20);
        getTables().put(tbWhatsappEmpresa, "tbWhatsappEmpresa");
        tbWhatsappEmpresa.applyProperties();
    }

    public RECEPT_TIMES_MEMBROS_EMP_USER tbReceptTimesMembrosEmpUser;

    private void init_tbReceptTimesMembrosEmpUser() {
        tbReceptTimesMembrosEmpUser = rn.tbReceptTimesMembrosEmpUser;
        tbReceptTimesMembrosEmpUser.setName("tbReceptTimesMembrosEmpUser");
        tbReceptTimesMembrosEmpUser.setMaxRowCount(200);
        tbReceptTimesMembrosEmpUser.setWKey("4600506;460014");
        tbReceptTimesMembrosEmpUser.setRatioBatchSize(20);
        getTables().put(tbReceptTimesMembrosEmpUser, "tbReceptTimesMembrosEmpUser");
        tbReceptTimesMembrosEmpUser.applyProperties();
    }

    protected TFForm FrmLeadzapReceptivo = this;
    private void init_FrmLeadzapReceptivo() {
        FrmLeadzapReceptivo.setName("FrmLeadzapReceptivo");
        FrmLeadzapReceptivo.setCaption("Chatbot Receptivo");
        FrmLeadzapReceptivo.setClientHeight(667);
        FrmLeadzapReceptivo.setClientWidth(893);
        FrmLeadzapReceptivo.setColor("clBtnFace");
        FrmLeadzapReceptivo.setWKey("4600506");
        FrmLeadzapReceptivo.setSpacing(0);
        FrmLeadzapReceptivo.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(893);
        vboxPrincipal.setHeight(667);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(5);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(5);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(1);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmLeadzapReceptivo.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox hboxBotoes = new TFHBox();

    private void init_hboxBotoes() {
        hboxBotoes.setName("hboxBotoes");
        hboxBotoes.setLeft(0);
        hboxBotoes.setTop(0);
        hboxBotoes.setWidth(890);
        hboxBotoes.setHeight(68);
        hboxBotoes.setAlign("alTop");
        hboxBotoes.setBorderStyle("stNone");
        hboxBotoes.setColor("16514043");
        hboxBotoes.setPaddingTop(5);
        hboxBotoes.setPaddingLeft(2);
        hboxBotoes.setPaddingRight(0);
        hboxBotoes.setPaddingBottom(5);
        hboxBotoes.setMarginTop(0);
        hboxBotoes.setMarginLeft(0);
        hboxBotoes.setMarginRight(0);
        hboxBotoes.setMarginBottom(0);
        hboxBotoes.setSpacing(1);
        hboxBotoes.setFlexVflex("ftFalse");
        hboxBotoes.setFlexHflex("ftTrue");
        hboxBotoes.setScrollable(false);
        hboxBotoes.setBoxShadowConfigHorizontalLength(10);
        hboxBotoes.setBoxShadowConfigVerticalLength(10);
        hboxBotoes.setBoxShadowConfigBlurRadius(5);
        hboxBotoes.setBoxShadowConfigSpreadRadius(0);
        hboxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hboxBotoes.setBoxShadowConfigOpacity(75);
        hboxBotoes.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxBotoes);
        hboxBotoes.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(801);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(2);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        hboxBotoes.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnConsultar = new TFButton();

    private void init_btnConsultar() {
        btnConsultar.setName("btnConsultar");
        btnConsultar.setLeft(0);
        btnConsultar.setTop(0);
        btnConsultar.setWidth(65);
        btnConsultar.setHeight(53);
        btnConsultar.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultar.setCaption("Pesquisar");
        btnConsultar.setFontColor("clWindowText");
        btnConsultar.setFontSize(-11);
        btnConsultar.setFontName("Tahoma");
        btnConsultar.setFontStyle("[]");
        btnConsultar.setLayout("blGlyphTop");
        btnConsultar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConsultarClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnConsultar", "OnClick");
        });
        btnConsultar.setImageId(13);
        btnConsultar.setColor("clBtnFace");
        btnConsultar.setAccess(true);
        btnConsultar.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultar);
        btnConsultar.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(65);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(53);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(130);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(53);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(195);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(260);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(53);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(325);
        FHBox3.setTop(0);
        FHBox3.setWidth(26);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setVisible(false);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFPageControl pgChatbot = new TFPageControl();

    private void init_pgChatbot() {
        pgChatbot.setName("pgChatbot");
        pgChatbot.setLeft(0);
        pgChatbot.setTop(69);
        pgChatbot.setWidth(881);
        pgChatbot.setHeight(585);
        pgChatbot.setTabPosition("tpTop");
        pgChatbot.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgChatbotChange(event);
            processarFlow("FrmLeadzapReceptivo", "pgChatbot", "OnChange");
        });
        pgChatbot.setFlexVflex("ftTrue");
        pgChatbot.setFlexHflex("ftTrue");
        pgChatbot.setRenderStyle("rsTabbed");
        pgChatbot.applyProperties();
        vboxPrincipal.addChildren(pgChatbot);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setVisible(true);
        tabListagem.setClosable(false);
        pgChatbot.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(873);
        FVBox1.setHeight(557);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        tabListagem.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGrid gridPrincipal = new TFGrid();

    private void init_gridPrincipal() {
        gridPrincipal.setName("gridPrincipal");
        gridPrincipal.setLeft(0);
        gridPrincipal.setTop(0);
        gridPrincipal.setWidth(544);
        gridPrincipal.setHeight(260);
        gridPrincipal.setTable(tbLeadzapMenu);
        gridPrincipal.setFlexVflex("ftTrue");
        gridPrincipal.setFlexHflex("ftTrue");
        gridPrincipal.setPagingEnabled(true);
        gridPrincipal.setFrozenColumns(0);
        gridPrincipal.setShowFooter(false);
        gridPrincipal.setShowHeader(true);
        gridPrincipal.setMultiSelection(false);
        gridPrincipal.setGroupingEnabled(false);
        gridPrincipal.setGroupingExpanded(false);
        gridPrincipal.setGroupingShowFooter(false);
        gridPrincipal.setCrosstabEnabled(false);
        gridPrincipal.setCrosstabGroupType("cgtConcat");
        gridPrincipal.setEditionEnabled(false);
        gridPrincipal.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_MENU");
        item0.setTitleCaption("Id");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("MENU_DESCRICAO");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(120);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("ATIVO");
        item2.setTitleCaption("Ativo");
        item2.setWidth(60);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("ATIVO = 'S'");
        item3.setEvalType("etExpression");
        item3.setImageId(310012);
        item2.getImages().add(item3);
        TFImageExpression item4 = new TFImageExpression();
        item4.setExpression("ATIVO = 'N'");
        item4.setEvalType("etExpression");
        item4.setImageId(310013);
        item2.getImages().add(item4);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(false);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item2);
        FVBox1.addChildren(gridPrincipal);
        gridPrincipal.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        pgChatbot.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(873);
        FVBox2.setHeight(557);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(5);
        FVBox2.setPaddingRight(5);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(5);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(185);
        FHBox7.setHeight(13);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox2.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFGroupbox FGroupbox1 = new TFGroupbox();

    private void init_FGroupbox1() {
        FGroupbox1.setName("FGroupbox1");
        FGroupbox1.setLeft(0);
        FGroupbox1.setTop(14);
        FGroupbox1.setWidth(867);
        FGroupbox1.setHeight(64);
        FGroupbox1.setCaption("LeadZap Menu");
        FGroupbox1.setFontColor("clWindowText");
        FGroupbox1.setFontSize(-11);
        FGroupbox1.setFontName("Tahoma");
        FGroupbox1.setFontStyle("[]");
        FGroupbox1.setFlexVflex("ftMin");
        FGroupbox1.setFlexHflex("ftTrue");
        FGroupbox1.setScrollable(false);
        FGroupbox1.setClosable(false);
        FGroupbox1.setClosed(false);
        FGroupbox1.setOrient("coHorizontal");
        FGroupbox1.setStyle("grpLine");
        FGroupbox1.setHeaderImageId(0);
        FVBox2.addChildren(FGroupbox1);
        FGroupbox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(8);
        FHBox2.setTop(20);
        FHBox2.setWidth(537);
        FHBox2.setHeight(37);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(5);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FGroupbox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(80);
        FHBox4.setHeight(25);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(5);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox2.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(9);
        FHBox10.setHeight(17);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftTrue");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox4.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFLabel lblDescricao = new TFLabel();

    private void init_lblDescricao() {
        lblDescricao.setName("lblDescricao");
        lblDescricao.setLeft(9);
        lblDescricao.setTop(0);
        lblDescricao.setWidth(46);
        lblDescricao.setHeight(13);
        lblDescricao.setCaption("Descri\u00E7\u00E3o");
        lblDescricao.setFontColor("clWindowText");
        lblDescricao.setFontSize(-11);
        lblDescricao.setFontName("Tahoma");
        lblDescricao.setFontStyle("[]");
        lblDescricao.setVerticalAlignment("taVerticalCenter");
        lblDescricao.setWordBreak(false);
        FHBox4.addChildren(lblDescricao);
        lblDescricao.applyProperties();
    }

    public TFString edtDescricao = new TFString();

    private void init_edtDescricao() {
        edtDescricao.setName("edtDescricao");
        edtDescricao.setLeft(80);
        edtDescricao.setTop(0);
        edtDescricao.setWidth(121);
        edtDescricao.setHeight(24);
        edtDescricao.setTable(tbLeadzapMenu);
        edtDescricao.setFieldName("MENU_DESCRICAO");
        edtDescricao.setFlex(true);
        edtDescricao.setRequired(false);
        edtDescricao.setConstraintCheckWhen("cwImmediate");
        edtDescricao.setConstraintCheckType("ctExpression");
        edtDescricao.setConstraintFocusOnError(false);
        edtDescricao.setConstraintEnableUI(true);
        edtDescricao.setConstraintEnabled(false);
        edtDescricao.setConstraintFormCheck(true);
        edtDescricao.setCharCase("ccNormal");
        edtDescricao.setPwd(false);
        edtDescricao.setMaxlength(0);
        edtDescricao.setFontColor("clWindowText");
        edtDescricao.setFontSize(-13);
        edtDescricao.setFontName("Tahoma");
        edtDescricao.setFontStyle("[]");
        edtDescricao.setSaveLiteralCharacter(false);
        edtDescricao.applyProperties();
        FHBox2.addChildren(edtDescricao);
        addValidatable(edtDescricao);
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(201);
        FHBox20.setTop(0);
        FHBox20.setWidth(126);
        FHBox20.setHeight(29);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(5);
        FHBox20.setPaddingLeft(2);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftFalse");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FHBox2.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFCheckBox chkAtivo = new TFCheckBox();

    private void init_chkAtivo() {
        chkAtivo.setName("chkAtivo");
        chkAtivo.setLeft(0);
        chkAtivo.setTop(0);
        chkAtivo.setWidth(97);
        chkAtivo.setHeight(17);
        chkAtivo.setCaption("Ativo");
        chkAtivo.setFontColor("clWindowText");
        chkAtivo.setFontSize(-11);
        chkAtivo.setFontName("Tahoma");
        chkAtivo.setFontStyle("[]");
        chkAtivo.setTable(tbLeadzapMenu);
        chkAtivo.setFieldName("ATIVO");
        chkAtivo.setCheckedValue("S");
        chkAtivo.setUncheckedValue("N");
        chkAtivo.setVerticalAlignment("taAlignTop");
        FHBox20.addChildren(chkAtivo);
        chkAtivo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(79);
        FHBox8.setWidth(185);
        FHBox8.setHeight(13);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FVBox2.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFGroupbox FGroupbox3 = new TFGroupbox();

    private void init_FGroupbox3() {
        FGroupbox3.setName("FGroupbox3");
        FGroupbox3.setLeft(0);
        FGroupbox3.setTop(93);
        FGroupbox3.setWidth(865);
        FGroupbox3.setHeight(165);
        FGroupbox3.setCaption("Se N\u00C3O Achar o Fone no Cadastro");
        FGroupbox3.setFontColor("clWindowText");
        FGroupbox3.setFontSize(-11);
        FGroupbox3.setFontName("Tahoma");
        FGroupbox3.setFontStyle("[]");
        FGroupbox3.setFlexVflex("ftMin");
        FGroupbox3.setFlexHflex("ftTrue");
        FGroupbox3.setScrollable(false);
        FGroupbox3.setClosable(false);
        FGroupbox3.setClosed(false);
        FGroupbox3.setOrient("coHorizontal");
        FGroupbox3.setStyle("grpLine");
        FGroupbox3.setHeaderImageId(0);
        FVBox2.addChildren(FGroupbox3);
        FGroupbox3.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(4);
        FVBox9.setTop(15);
        FVBox9.setWidth(857);
        FVBox9.setHeight(146);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(3);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftTrue");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FGroupbox3.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(537);
        FHBox5.setHeight(37);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(5);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox9.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(0);
        FHBox14.setWidth(80);
        FHBox14.setHeight(25);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(5);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        FHBox5.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(0);
        FHBox18.setWidth(9);
        FHBox18.setHeight(17);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftTrue");
        FHBox18.setFlexHflex("ftTrue");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        FHBox14.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(9);
        FLabel3.setTop(0);
        FLabel3.setWidth(44);
        FLabel3.setHeight(13);
        FLabel3.setCaption("Template");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FHBox14.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFCombo cbbTemplateQualificado = new TFCombo();

    private void init_cbbTemplateQualificado() {
        cbbTemplateQualificado.setName("cbbTemplateQualificado");
        cbbTemplateQualificado.setLeft(80);
        cbbTemplateQualificado.setTop(0);
        cbbTemplateQualificado.setWidth(152);
        cbbTemplateQualificado.setHeight(21);
        cbbTemplateQualificado.setTable(tbLeadzapMenu);
        cbbTemplateQualificado.setLookupTable(tbTemplateQualificado);
        cbbTemplateQualificado.setFieldName("ID_TEMPLATE_LEAD");
        cbbTemplateQualificado.setLookupKey("ID_EMAIL_MODELO");
        cbbTemplateQualificado.setLookupDesc("MODELO");
        cbbTemplateQualificado.setFlex(true);
        cbbTemplateQualificado.setReadOnly(true);
        cbbTemplateQualificado.setRequired(false);
        cbbTemplateQualificado.setPrompt("Selecione");
        cbbTemplateQualificado.setConstraintCheckWhen("cwImmediate");
        cbbTemplateQualificado.setConstraintCheckType("ctExpression");
        cbbTemplateQualificado.setConstraintFocusOnError(false);
        cbbTemplateQualificado.setConstraintEnableUI(true);
        cbbTemplateQualificado.setConstraintEnabled(false);
        cbbTemplateQualificado.setConstraintFormCheck(true);
        cbbTemplateQualificado.setClearOnDelKey(true);
        cbbTemplateQualificado.setUseClearButton(false);
        cbbTemplateQualificado.setHideClearButtonOnNullValue(false);
        FHBox5.addChildren(cbbTemplateQualificado);
        cbbTemplateQualificado.applyProperties();
        addValidatable(cbbTemplateQualificado);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(38);
        FVBox3.setWidth(536);
        FVBox3.setHeight(58);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(70);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(5);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FVBox9.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFCheckBox chkMostraNome = new TFCheckBox();

    private void init_chkMostraNome() {
        chkMostraNome.setName("chkMostraNome");
        chkMostraNome.setLeft(0);
        chkMostraNome.setTop(0);
        chkMostraNome.setWidth(97);
        chkMostraNome.setHeight(17);
        chkMostraNome.setCaption("Mostra o Nome");
        chkMostraNome.setFontColor("clWindowText");
        chkMostraNome.setFontSize(-11);
        chkMostraNome.setFontName("Tahoma");
        chkMostraNome.setFontStyle("[]");
        chkMostraNome.setVisible(false);
        chkMostraNome.setTable(tbLeadzapMenu);
        chkMostraNome.setFieldName("LEAD_PERGUNTA_NOME");
        chkMostraNome.setCheckedValue("S");
        chkMostraNome.setUncheckedValue("N");
        chkMostraNome.setVerticalAlignment("taAlignTop");
        FVBox3.addChildren(chkMostraNome);
        chkMostraNome.applyProperties();
    }

    public TFCheckBox chkMostraEmail = new TFCheckBox();

    private void init_chkMostraEmail() {
        chkMostraEmail.setName("chkMostraEmail");
        chkMostraEmail.setLeft(0);
        chkMostraEmail.setTop(18);
        chkMostraEmail.setWidth(97);
        chkMostraEmail.setHeight(17);
        chkMostraEmail.setCaption("Pergunta  Email");
        chkMostraEmail.setFontColor("clWindowText");
        chkMostraEmail.setFontSize(-11);
        chkMostraEmail.setFontName("Tahoma");
        chkMostraEmail.setFontStyle("[]");
        chkMostraEmail.setTable(tbLeadzapMenu);
        chkMostraEmail.setFieldName("LEAD_PERGUNTA_EMAIL");
        chkMostraEmail.setCheckedValue("S");
        chkMostraEmail.setUncheckedValue("N");
        chkMostraEmail.setVerticalAlignment("taAlignTop");
        FVBox3.addChildren(chkMostraEmail);
        chkMostraEmail.applyProperties();
    }

    public TFCheckBox chkCadDuplo = new TFCheckBox();

    private void init_chkCadDuplo() {
        chkCadDuplo.setName("chkCadDuplo");
        chkCadDuplo.setLeft(0);
        chkCadDuplo.setTop(36);
        chkCadDuplo.setWidth(525);
        chkCadDuplo.setHeight(17);
        chkCadDuplo.setCaption("Se achar mais de um n\u00FAmero de telefone no cadastro de cliente, pergunta qual o cliente?");
        chkCadDuplo.setFontColor("clWindowText");
        chkCadDuplo.setFontSize(-11);
        chkCadDuplo.setFontName("Tahoma");
        chkCadDuplo.setFontStyle("[]");
        chkCadDuplo.setVisible(false);
        chkCadDuplo.setTable(tbLeadzapMenu);
        chkCadDuplo.setFieldName("CAD_DUPLO_MOSTRA_AO_CLIENTE");
        chkCadDuplo.setCheckedValue("S");
        chkCadDuplo.setUncheckedValue("N");
        chkCadDuplo.setVerticalAlignment("taAlignTop");
        FVBox3.addChildren(chkCadDuplo);
        chkCadDuplo.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(259);
        FHBox9.setWidth(185);
        FHBox9.setHeight(13);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox2.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFGroupbox FGroupbox2 = new TFGroupbox();

    private void init_FGroupbox2() {
        FGroupbox2.setName("FGroupbox2");
        FGroupbox2.setLeft(0);
        FGroupbox2.setTop(273);
        FGroupbox2.setWidth(866);
        FGroupbox2.setHeight(70);
        FGroupbox2.setCaption("Se Achar o Fone no Cadastro");
        FGroupbox2.setFontColor("clWindowText");
        FGroupbox2.setFontSize(-11);
        FGroupbox2.setFontName("Tahoma");
        FGroupbox2.setFontStyle("[]");
        FGroupbox2.setFlexVflex("ftMin");
        FGroupbox2.setFlexHflex("ftTrue");
        FGroupbox2.setScrollable(false);
        FGroupbox2.setClosable(false);
        FGroupbox2.setClosed(false);
        FGroupbox2.setOrient("coHorizontal");
        FGroupbox2.setStyle("grpLine");
        FGroupbox2.setHeaderImageId(0);
        FVBox2.addChildren(FGroupbox2);
        FGroupbox2.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(8);
        FHBox15.setTop(20);
        FHBox15.setWidth(537);
        FHBox15.setHeight(40);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(5);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftTrue");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FGroupbox2.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(0);
        FHBox16.setWidth(80);
        FHBox16.setHeight(25);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(5);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftFalse");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FHBox15.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(0);
        FHBox17.setWidth(9);
        FHBox17.setHeight(17);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftTrue");
        FHBox17.setFlexHflex("ftTrue");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FHBox16.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(9);
        FLabel2.setTop(0);
        FLabel2.setWidth(44);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Template");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FHBox16.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cbbTemplateLead = new TFCombo();

    private void init_cbbTemplateLead() {
        cbbTemplateLead.setName("cbbTemplateLead");
        cbbTemplateLead.setLeft(80);
        cbbTemplateLead.setTop(0);
        cbbTemplateLead.setWidth(145);
        cbbTemplateLead.setHeight(21);
        cbbTemplateLead.setTable(tbLeadzapMenu);
        cbbTemplateLead.setLookupTable(tbTemplateLead);
        cbbTemplateLead.setFieldName("ID_TEMPLATE_QUALIFICADO");
        cbbTemplateLead.setLookupKey("ID_EMAIL_MODELO");
        cbbTemplateLead.setLookupDesc("MODELO");
        cbbTemplateLead.setFlex(true);
        cbbTemplateLead.setReadOnly(true);
        cbbTemplateLead.setRequired(false);
        cbbTemplateLead.setPrompt("Selecione");
        cbbTemplateLead.setConstraintCheckWhen("cwImmediate");
        cbbTemplateLead.setConstraintCheckType("ctExpression");
        cbbTemplateLead.setConstraintFocusOnError(false);
        cbbTemplateLead.setConstraintEnableUI(true);
        cbbTemplateLead.setConstraintEnabled(false);
        cbbTemplateLead.setConstraintFormCheck(true);
        cbbTemplateLead.setClearOnDelKey(true);
        cbbTemplateLead.setUseClearButton(false);
        cbbTemplateLead.setHideClearButtonOnNullValue(false);
        FHBox15.addChildren(cbbTemplateLead);
        cbbTemplateLead.applyProperties();
        addValidatable(cbbTemplateLead);
    }

    public TFHBox FHBox36 = new TFHBox();

    private void init_FHBox36() {
        FHBox36.setName("FHBox36");
        FHBox36.setLeft(0);
        FHBox36.setTop(344);
        FHBox36.setWidth(185);
        FHBox36.setHeight(13);
        FHBox36.setBorderStyle("stNone");
        FHBox36.setPaddingTop(0);
        FHBox36.setPaddingLeft(0);
        FHBox36.setPaddingRight(0);
        FHBox36.setPaddingBottom(0);
        FHBox36.setMarginTop(0);
        FHBox36.setMarginLeft(0);
        FHBox36.setMarginRight(0);
        FHBox36.setMarginBottom(0);
        FHBox36.setSpacing(1);
        FHBox36.setFlexVflex("ftFalse");
        FHBox36.setFlexHflex("ftFalse");
        FHBox36.setScrollable(false);
        FHBox36.setBoxShadowConfigHorizontalLength(10);
        FHBox36.setBoxShadowConfigVerticalLength(10);
        FHBox36.setBoxShadowConfigBlurRadius(5);
        FHBox36.setBoxShadowConfigSpreadRadius(0);
        FHBox36.setBoxShadowConfigShadowColor("clBlack");
        FHBox36.setBoxShadowConfigOpacity(75);
        FHBox36.setVAlign("tvTop");
        FVBox2.addChildren(FHBox36);
        FHBox36.applyProperties();
    }

    public TFGroupbox FGroupbox5 = new TFGroupbox();

    private void init_FGroupbox5() {
        FGroupbox5.setName("FGroupbox5");
        FGroupbox5.setLeft(0);
        FGroupbox5.setTop(358);
        FGroupbox5.setWidth(866);
        FGroupbox5.setHeight(194);
        FGroupbox5.setCaption("Vendedor Template");
        FGroupbox5.setFontColor("clWindowText");
        FGroupbox5.setFontSize(-11);
        FGroupbox5.setFontName("Tahoma");
        FGroupbox5.setFontStyle("[]");
        FGroupbox5.setFlexVflex("ftMin");
        FGroupbox5.setFlexHflex("ftTrue");
        FGroupbox5.setScrollable(false);
        FGroupbox5.setClosable(false);
        FGroupbox5.setClosed(false);
        FGroupbox5.setOrient("coHorizontal");
        FGroupbox5.setStyle("grpLine");
        FGroupbox5.setHeaderImageId(0);
        FVBox2.addChildren(FGroupbox5);
        FGroupbox5.applyProperties();
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(8);
        FVBox10.setTop(19);
        FVBox10.setWidth(757);
        FVBox10.setHeight(158);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(0);
        FVBox10.setPaddingLeft(0);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(1);
        FVBox10.setFlexVflex("ftTrue");
        FVBox10.setFlexHflex("ftTrue");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        FGroupbox5.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFHBox FHBox33 = new TFHBox();

    private void init_FHBox33() {
        FHBox33.setName("FHBox33");
        FHBox33.setLeft(0);
        FHBox33.setTop(0);
        FHBox33.setWidth(537);
        FHBox33.setHeight(40);
        FHBox33.setBorderStyle("stNone");
        FHBox33.setPaddingTop(0);
        FHBox33.setPaddingLeft(0);
        FHBox33.setPaddingRight(0);
        FHBox33.setPaddingBottom(0);
        FHBox33.setMarginTop(0);
        FHBox33.setMarginLeft(0);
        FHBox33.setMarginRight(0);
        FHBox33.setMarginBottom(0);
        FHBox33.setSpacing(5);
        FHBox33.setFlexVflex("ftFalse");
        FHBox33.setFlexHflex("ftTrue");
        FHBox33.setScrollable(false);
        FHBox33.setBoxShadowConfigHorizontalLength(10);
        FHBox33.setBoxShadowConfigVerticalLength(10);
        FHBox33.setBoxShadowConfigBlurRadius(5);
        FHBox33.setBoxShadowConfigSpreadRadius(0);
        FHBox33.setBoxShadowConfigShadowColor("clBlack");
        FHBox33.setBoxShadowConfigOpacity(75);
        FHBox33.setVAlign("tvTop");
        FVBox10.addChildren(FHBox33);
        FHBox33.applyProperties();
    }

    public TFHBox FHBox34 = new TFHBox();

    private void init_FHBox34() {
        FHBox34.setName("FHBox34");
        FHBox34.setLeft(0);
        FHBox34.setTop(0);
        FHBox34.setWidth(120);
        FHBox34.setHeight(25);
        FHBox34.setBorderStyle("stNone");
        FHBox34.setPaddingTop(5);
        FHBox34.setPaddingLeft(0);
        FHBox34.setPaddingRight(0);
        FHBox34.setPaddingBottom(0);
        FHBox34.setMarginTop(0);
        FHBox34.setMarginLeft(0);
        FHBox34.setMarginRight(0);
        FHBox34.setMarginBottom(0);
        FHBox34.setSpacing(1);
        FHBox34.setFlexVflex("ftFalse");
        FHBox34.setFlexHflex("ftFalse");
        FHBox34.setScrollable(false);
        FHBox34.setBoxShadowConfigHorizontalLength(10);
        FHBox34.setBoxShadowConfigVerticalLength(10);
        FHBox34.setBoxShadowConfigBlurRadius(5);
        FHBox34.setBoxShadowConfigSpreadRadius(0);
        FHBox34.setBoxShadowConfigShadowColor("clBlack");
        FHBox34.setBoxShadowConfigOpacity(75);
        FHBox34.setVAlign("tvTop");
        FHBox33.addChildren(FHBox34);
        FHBox34.applyProperties();
    }

    public TFHBox FHBox35 = new TFHBox();

    private void init_FHBox35() {
        FHBox35.setName("FHBox35");
        FHBox35.setLeft(0);
        FHBox35.setTop(0);
        FHBox35.setWidth(9);
        FHBox35.setHeight(17);
        FHBox35.setBorderStyle("stNone");
        FHBox35.setPaddingTop(0);
        FHBox35.setPaddingLeft(0);
        FHBox35.setPaddingRight(0);
        FHBox35.setPaddingBottom(0);
        FHBox35.setMarginTop(0);
        FHBox35.setMarginLeft(0);
        FHBox35.setMarginRight(0);
        FHBox35.setMarginBottom(0);
        FHBox35.setSpacing(1);
        FHBox35.setFlexVflex("ftTrue");
        FHBox35.setFlexHflex("ftTrue");
        FHBox35.setScrollable(false);
        FHBox35.setBoxShadowConfigHorizontalLength(10);
        FHBox35.setBoxShadowConfigVerticalLength(10);
        FHBox35.setBoxShadowConfigBlurRadius(5);
        FHBox35.setBoxShadowConfigSpreadRadius(0);
        FHBox35.setBoxShadowConfigShadowColor("clBlack");
        FHBox35.setBoxShadowConfigOpacity(75);
        FHBox35.setVAlign("tvTop");
        FHBox34.addChildren(FHBox35);
        FHBox35.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(9);
        FLabel7.setTop(0);
        FLabel7.setWidth(71);
        FLabel7.setHeight(13);
        FLabel7.setCaption(" Novo Leadzap");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        FHBox34.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFCombo cbbTemplateVendedor = new TFCombo();

    private void init_cbbTemplateVendedor() {
        cbbTemplateVendedor.setName("cbbTemplateVendedor");
        cbbTemplateVendedor.setLeft(120);
        cbbTemplateVendedor.setTop(0);
        cbbTemplateVendedor.setWidth(245);
        cbbTemplateVendedor.setHeight(21);
        cbbTemplateVendedor.setTable(tbLeadzapMenu);
        cbbTemplateVendedor.setLookupTable(tbTemplateLead);
        cbbTemplateVendedor.setFieldName("ID_TEMPLATE_VENDEDOR");
        cbbTemplateVendedor.setLookupKey("ID_EMAIL_MODELO");
        cbbTemplateVendedor.setLookupDesc("MODELO");
        cbbTemplateVendedor.setFlex(true);
        cbbTemplateVendedor.setReadOnly(true);
        cbbTemplateVendedor.setRequired(false);
        cbbTemplateVendedor.setPrompt("Selecione");
        cbbTemplateVendedor.setConstraintCheckWhen("cwImmediate");
        cbbTemplateVendedor.setConstraintCheckType("ctExpression");
        cbbTemplateVendedor.setConstraintFocusOnError(false);
        cbbTemplateVendedor.setConstraintEnableUI(true);
        cbbTemplateVendedor.setConstraintEnabled(false);
        cbbTemplateVendedor.setConstraintFormCheck(true);
        cbbTemplateVendedor.setClearOnDelKey(true);
        cbbTemplateVendedor.setUseClearButton(false);
        cbbTemplateVendedor.setHideClearButtonOnNullValue(false);
        FHBox33.addChildren(cbbTemplateVendedor);
        cbbTemplateVendedor.applyProperties();
        addValidatable(cbbTemplateVendedor);
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(0);
        FHBox24.setTop(41);
        FHBox24.setWidth(537);
        FHBox24.setHeight(40);
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(0);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(5);
        FHBox24.setFlexVflex("ftFalse");
        FHBox24.setFlexHflex("ftTrue");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvTop");
        FVBox10.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFHBox FHBox37 = new TFHBox();

    private void init_FHBox37() {
        FHBox37.setName("FHBox37");
        FHBox37.setLeft(0);
        FHBox37.setTop(0);
        FHBox37.setWidth(120);
        FHBox37.setHeight(25);
        FHBox37.setBorderStyle("stNone");
        FHBox37.setPaddingTop(5);
        FHBox37.setPaddingLeft(0);
        FHBox37.setPaddingRight(0);
        FHBox37.setPaddingBottom(0);
        FHBox37.setMarginTop(0);
        FHBox37.setMarginLeft(0);
        FHBox37.setMarginRight(0);
        FHBox37.setMarginBottom(0);
        FHBox37.setSpacing(1);
        FHBox37.setFlexVflex("ftFalse");
        FHBox37.setFlexHflex("ftFalse");
        FHBox37.setScrollable(false);
        FHBox37.setBoxShadowConfigHorizontalLength(10);
        FHBox37.setBoxShadowConfigVerticalLength(10);
        FHBox37.setBoxShadowConfigBlurRadius(5);
        FHBox37.setBoxShadowConfigSpreadRadius(0);
        FHBox37.setBoxShadowConfigShadowColor("clBlack");
        FHBox37.setBoxShadowConfigOpacity(75);
        FHBox37.setVAlign("tvTop");
        FHBox24.addChildren(FHBox37);
        FHBox37.applyProperties();
    }

    public TFHBox FHBox38 = new TFHBox();

    private void init_FHBox38() {
        FHBox38.setName("FHBox38");
        FHBox38.setLeft(0);
        FHBox38.setTop(0);
        FHBox38.setWidth(9);
        FHBox38.setHeight(17);
        FHBox38.setBorderStyle("stNone");
        FHBox38.setPaddingTop(0);
        FHBox38.setPaddingLeft(0);
        FHBox38.setPaddingRight(0);
        FHBox38.setPaddingBottom(0);
        FHBox38.setMarginTop(0);
        FHBox38.setMarginLeft(0);
        FHBox38.setMarginRight(0);
        FHBox38.setMarginBottom(0);
        FHBox38.setSpacing(1);
        FHBox38.setFlexVflex("ftTrue");
        FHBox38.setFlexHflex("ftTrue");
        FHBox38.setScrollable(false);
        FHBox38.setBoxShadowConfigHorizontalLength(10);
        FHBox38.setBoxShadowConfigVerticalLength(10);
        FHBox38.setBoxShadowConfigBlurRadius(5);
        FHBox38.setBoxShadowConfigSpreadRadius(0);
        FHBox38.setBoxShadowConfigShadowColor("clBlack");
        FHBox38.setBoxShadowConfigOpacity(75);
        FHBox38.setVAlign("tvTop");
        FHBox37.addChildren(FHBox38);
        FHBox38.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(9);
        FLabel8.setTop(0);
        FLabel8.setWidth(84);
        FLabel8.setHeight(13);
        FLabel8.setCaption(" Apov. Orc. Parts");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FHBox37.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFCombo cbbTemplateVendedorAprov = new TFCombo();

    private void init_cbbTemplateVendedorAprov() {
        cbbTemplateVendedorAprov.setName("cbbTemplateVendedorAprov");
        cbbTemplateVendedorAprov.setLeft(120);
        cbbTemplateVendedorAprov.setTop(0);
        cbbTemplateVendedorAprov.setWidth(245);
        cbbTemplateVendedorAprov.setHeight(21);
        cbbTemplateVendedorAprov.setTable(tbLeadzapMenu);
        cbbTemplateVendedorAprov.setLookupTable(tbTemplateLeadAprov);
        cbbTemplateVendedorAprov.setFieldName("ID_TEMPLATE_VENDEDOR_PARTS");
        cbbTemplateVendedorAprov.setLookupKey("ID_EMAIL_MODELO");
        cbbTemplateVendedorAprov.setLookupDesc("MODELO");
        cbbTemplateVendedorAprov.setFlex(true);
        cbbTemplateVendedorAprov.setReadOnly(true);
        cbbTemplateVendedorAprov.setRequired(false);
        cbbTemplateVendedorAprov.setPrompt("Selecione");
        cbbTemplateVendedorAprov.setConstraintCheckWhen("cwImmediate");
        cbbTemplateVendedorAprov.setConstraintCheckType("ctExpression");
        cbbTemplateVendedorAprov.setConstraintFocusOnError(false);
        cbbTemplateVendedorAprov.setConstraintEnableUI(true);
        cbbTemplateVendedorAprov.setConstraintEnabled(false);
        cbbTemplateVendedorAprov.setConstraintFormCheck(true);
        cbbTemplateVendedorAprov.setClearOnDelKey(true);
        cbbTemplateVendedorAprov.setUseClearButton(false);
        cbbTemplateVendedorAprov.setHideClearButtonOnNullValue(false);
        FHBox24.addChildren(cbbTemplateVendedorAprov);
        cbbTemplateVendedorAprov.applyProperties();
        addValidatable(cbbTemplateVendedorAprov);
    }

    public TFHBox FHBox39 = new TFHBox();

    private void init_FHBox39() {
        FHBox39.setName("FHBox39");
        FHBox39.setLeft(0);
        FHBox39.setTop(82);
        FHBox39.setWidth(537);
        FHBox39.setHeight(40);
        FHBox39.setBorderStyle("stNone");
        FHBox39.setPaddingTop(0);
        FHBox39.setPaddingLeft(0);
        FHBox39.setPaddingRight(0);
        FHBox39.setPaddingBottom(0);
        FHBox39.setMarginTop(0);
        FHBox39.setMarginLeft(0);
        FHBox39.setMarginRight(0);
        FHBox39.setMarginBottom(0);
        FHBox39.setSpacing(5);
        FHBox39.setFlexVflex("ftFalse");
        FHBox39.setFlexHflex("ftTrue");
        FHBox39.setScrollable(false);
        FHBox39.setBoxShadowConfigHorizontalLength(10);
        FHBox39.setBoxShadowConfigVerticalLength(10);
        FHBox39.setBoxShadowConfigBlurRadius(5);
        FHBox39.setBoxShadowConfigSpreadRadius(0);
        FHBox39.setBoxShadowConfigShadowColor("clBlack");
        FHBox39.setBoxShadowConfigOpacity(75);
        FHBox39.setVAlign("tvTop");
        FVBox10.addChildren(FHBox39);
        FHBox39.applyProperties();
    }

    public TFHBox FHBox40 = new TFHBox();

    private void init_FHBox40() {
        FHBox40.setName("FHBox40");
        FHBox40.setLeft(0);
        FHBox40.setTop(0);
        FHBox40.setWidth(120);
        FHBox40.setHeight(25);
        FHBox40.setBorderStyle("stNone");
        FHBox40.setPaddingTop(5);
        FHBox40.setPaddingLeft(0);
        FHBox40.setPaddingRight(0);
        FHBox40.setPaddingBottom(0);
        FHBox40.setMarginTop(0);
        FHBox40.setMarginLeft(0);
        FHBox40.setMarginRight(0);
        FHBox40.setMarginBottom(0);
        FHBox40.setSpacing(1);
        FHBox40.setFlexVflex("ftFalse");
        FHBox40.setFlexHflex("ftFalse");
        FHBox40.setScrollable(false);
        FHBox40.setBoxShadowConfigHorizontalLength(10);
        FHBox40.setBoxShadowConfigVerticalLength(10);
        FHBox40.setBoxShadowConfigBlurRadius(5);
        FHBox40.setBoxShadowConfigSpreadRadius(0);
        FHBox40.setBoxShadowConfigShadowColor("clBlack");
        FHBox40.setBoxShadowConfigOpacity(75);
        FHBox40.setVAlign("tvTop");
        FHBox39.addChildren(FHBox40);
        FHBox40.applyProperties();
    }

    public TFHBox FHBox41 = new TFHBox();

    private void init_FHBox41() {
        FHBox41.setName("FHBox41");
        FHBox41.setLeft(0);
        FHBox41.setTop(0);
        FHBox41.setWidth(9);
        FHBox41.setHeight(17);
        FHBox41.setBorderStyle("stNone");
        FHBox41.setPaddingTop(0);
        FHBox41.setPaddingLeft(0);
        FHBox41.setPaddingRight(0);
        FHBox41.setPaddingBottom(0);
        FHBox41.setMarginTop(0);
        FHBox41.setMarginLeft(0);
        FHBox41.setMarginRight(0);
        FHBox41.setMarginBottom(0);
        FHBox41.setSpacing(1);
        FHBox41.setFlexVflex("ftTrue");
        FHBox41.setFlexHflex("ftTrue");
        FHBox41.setScrollable(false);
        FHBox41.setBoxShadowConfigHorizontalLength(10);
        FHBox41.setBoxShadowConfigVerticalLength(10);
        FHBox41.setBoxShadowConfigBlurRadius(5);
        FHBox41.setBoxShadowConfigSpreadRadius(0);
        FHBox41.setBoxShadowConfigShadowColor("clBlack");
        FHBox41.setBoxShadowConfigOpacity(75);
        FHBox41.setVAlign("tvTop");
        FHBox40.addChildren(FHBox41);
        FHBox41.applyProperties();
    }

    public TFLabel FLabel9 = new TFLabel();

    private void init_FLabel9() {
        FLabel9.setName("FLabel9");
        FLabel9.setLeft(9);
        FLabel9.setTop(0);
        FLabel9.setWidth(91);
        FLabel9.setHeight(13);
        FLabel9.setCaption("Reprov. Orc. Parts");
        FLabel9.setFontColor("clWindowText");
        FLabel9.setFontSize(-11);
        FLabel9.setFontName("Tahoma");
        FLabel9.setFontStyle("[]");
        FLabel9.setVerticalAlignment("taVerticalCenter");
        FLabel9.setWordBreak(false);
        FHBox40.addChildren(FLabel9);
        FLabel9.applyProperties();
    }

    public TFCombo cbbTemplateVendedorReprov = new TFCombo();

    private void init_cbbTemplateVendedorReprov() {
        cbbTemplateVendedorReprov.setName("cbbTemplateVendedorReprov");
        cbbTemplateVendedorReprov.setLeft(120);
        cbbTemplateVendedorReprov.setTop(0);
        cbbTemplateVendedorReprov.setWidth(245);
        cbbTemplateVendedorReprov.setHeight(21);
        cbbTemplateVendedorReprov.setTable(tbLeadzapMenu);
        cbbTemplateVendedorReprov.setLookupTable(tbTemplateLeadReprov);
        cbbTemplateVendedorReprov.setFieldName("ID_TEMPLATE_VENDEDOR_PARTS_REP");
        cbbTemplateVendedorReprov.setLookupKey("ID_EMAIL_MODELO");
        cbbTemplateVendedorReprov.setLookupDesc("MODELO");
        cbbTemplateVendedorReprov.setFlex(true);
        cbbTemplateVendedorReprov.setReadOnly(true);
        cbbTemplateVendedorReprov.setRequired(false);
        cbbTemplateVendedorReprov.setPrompt("Selecione");
        cbbTemplateVendedorReprov.setConstraintCheckWhen("cwImmediate");
        cbbTemplateVendedorReprov.setConstraintCheckType("ctExpression");
        cbbTemplateVendedorReprov.setConstraintFocusOnError(false);
        cbbTemplateVendedorReprov.setConstraintEnableUI(true);
        cbbTemplateVendedorReprov.setConstraintEnabled(false);
        cbbTemplateVendedorReprov.setConstraintFormCheck(true);
        cbbTemplateVendedorReprov.setClearOnDelKey(true);
        cbbTemplateVendedorReprov.setUseClearButton(false);
        cbbTemplateVendedorReprov.setHideClearButtonOnNullValue(false);
        FHBox39.addChildren(cbbTemplateVendedorReprov);
        cbbTemplateVendedorReprov.applyProperties();
        addValidatable(cbbTemplateVendedorReprov);
    }

    public TFTabsheet tabMenu = new TFTabsheet();

    private void init_tabMenu() {
        tabMenu.setName("tabMenu");
        tabMenu.setCaption("Itens do Menu");
        tabMenu.setVisible(true);
        tabMenu.setClosable(false);
        pgChatbot.addChildren(tabMenu);
        tabMenu.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(873);
        FVBox4.setHeight(557);
        FVBox4.setAlign("alClient");
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(5);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        tabMenu.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFGrid gridItensMenu = new TFGrid();

    private void init_gridItensMenu() {
        gridItensMenu.setName("gridItensMenu");
        gridItensMenu.setLeft(0);
        gridItensMenu.setTop(0);
        gridItensMenu.setWidth(867);
        gridItensMenu.setHeight(176);
        gridItensMenu.setTable(tbLeadzapItem);
        gridItensMenu.setFlexVflex("ftTrue");
        gridItensMenu.setFlexHflex("ftTrue");
        gridItensMenu.setPagingEnabled(true);
        gridItensMenu.setFrozenColumns(0);
        gridItensMenu.setShowFooter(false);
        gridItensMenu.setShowHeader(true);
        gridItensMenu.setMultiSelection(false);
        gridItensMenu.setGroupingEnabled(false);
        gridItensMenu.setGroupingExpanded(false);
        gridItensMenu.setGroupingShowFooter(false);
        gridItensMenu.setCrosstabEnabled(false);
        gridItensMenu.setCrosstabGroupType("cgtConcat");
        gridItensMenu.setEditionEnabled(false);
        gridItensMenu.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_ITEM");
        item0.setTitleCaption("Id. Item");
        item0.setWidth(80);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridItensMenu.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("ID_MENU");
        item1.setTitleCaption("Id. Menu");
        item1.setWidth(79);
        item1.setVisible(false);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridItensMenu.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("SEQUENCIA");
        item2.setTitleCaption("Seq\u00FC\u00EAncia");
        item2.setWidth(92);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridItensMenu.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("DESCRICAO");
        item3.setTitleCaption("Descri\u00E7\u00E3o");
        item3.setWidth(90);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridItensMenu.getColumns().add(item3);
        FVBox4.addChildren(gridItensMenu);
        gridItensMenu.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(177);
        FHBox6.setWidth(866);
        FHBox6.setHeight(273);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(2);
        FHBox6.setPaddingLeft(3);
        FHBox6.setPaddingRight(3);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(5);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FVBox4.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(444);
        FVBox5.setHeight(268);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(438);
        FHBox11.setHeight(41);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox5.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnNovoItem = new TFButton();

    private void init_btnNovoItem() {
        btnNovoItem.setName("btnNovoItem");
        btnNovoItem.setLeft(0);
        btnNovoItem.setTop(0);
        btnNovoItem.setWidth(48);
        btnNovoItem.setHeight(35);
        btnNovoItem.setHint("Novo Item");
        btnNovoItem.setFontColor("clWindowText");
        btnNovoItem.setFontSize(-11);
        btnNovoItem.setFontName("Tahoma");
        btnNovoItem.setFontStyle("[]");
        btnNovoItem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoItemClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnNovoItem", "OnClick");
        });
        btnNovoItem.setImageId(6);
        btnNovoItem.setColor("clBtnFace");
        btnNovoItem.setAccess(false);
        btnNovoItem.setIconReverseDirection(false);
        FHBox11.addChildren(btnNovoItem);
        btnNovoItem.applyProperties();
    }

    public TFButton btnAlterarItem = new TFButton();

    private void init_btnAlterarItem() {
        btnAlterarItem.setName("btnAlterarItem");
        btnAlterarItem.setLeft(48);
        btnAlterarItem.setTop(0);
        btnAlterarItem.setWidth(48);
        btnAlterarItem.setHeight(35);
        btnAlterarItem.setHint("Alterar Item");
        btnAlterarItem.setFontColor("clWindowText");
        btnAlterarItem.setFontSize(-11);
        btnAlterarItem.setFontName("Tahoma");
        btnAlterarItem.setFontStyle("[]");
        btnAlterarItem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarItemClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnAlterarItem", "OnClick");
        });
        btnAlterarItem.setImageId(7);
        btnAlterarItem.setColor("clBtnFace");
        btnAlterarItem.setAccess(false);
        btnAlterarItem.setIconReverseDirection(false);
        FHBox11.addChildren(btnAlterarItem);
        btnAlterarItem.applyProperties();
    }

    public TFButton btnExcluirItem = new TFButton();

    private void init_btnExcluirItem() {
        btnExcluirItem.setName("btnExcluirItem");
        btnExcluirItem.setLeft(96);
        btnExcluirItem.setTop(0);
        btnExcluirItem.setWidth(48);
        btnExcluirItem.setHeight(35);
        btnExcluirItem.setHint("Excluir Item");
        btnExcluirItem.setFontColor("clWindowText");
        btnExcluirItem.setFontSize(-11);
        btnExcluirItem.setFontName("Tahoma");
        btnExcluirItem.setFontStyle("[]");
        btnExcluirItem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirItemClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnExcluirItem", "OnClick");
        });
        btnExcluirItem.setImageId(8);
        btnExcluirItem.setColor("clBtnFace");
        btnExcluirItem.setAccess(false);
        btnExcluirItem.setIconReverseDirection(false);
        FHBox11.addChildren(btnExcluirItem);
        btnExcluirItem.applyProperties();
    }

    public TFButton btnSalvarItem = new TFButton();

    private void init_btnSalvarItem() {
        btnSalvarItem.setName("btnSalvarItem");
        btnSalvarItem.setLeft(144);
        btnSalvarItem.setTop(0);
        btnSalvarItem.setWidth(48);
        btnSalvarItem.setHeight(35);
        btnSalvarItem.setHint("Salvar Item");
        btnSalvarItem.setFontColor("clWindowText");
        btnSalvarItem.setFontSize(-11);
        btnSalvarItem.setFontName("Tahoma");
        btnSalvarItem.setFontStyle("[]");
        btnSalvarItem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarItemClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnSalvarItem", "OnClick");
        });
        btnSalvarItem.setImageId(4);
        btnSalvarItem.setColor("clBtnFace");
        btnSalvarItem.setAccess(false);
        btnSalvarItem.setIconReverseDirection(false);
        FHBox11.addChildren(btnSalvarItem);
        btnSalvarItem.applyProperties();
    }

    public TFButton btnCancelarItem = new TFButton();

    private void init_btnCancelarItem() {
        btnCancelarItem.setName("btnCancelarItem");
        btnCancelarItem.setLeft(192);
        btnCancelarItem.setTop(0);
        btnCancelarItem.setWidth(48);
        btnCancelarItem.setHeight(35);
        btnCancelarItem.setHint("Cancelar Item");
        btnCancelarItem.setFontColor("clWindowText");
        btnCancelarItem.setFontSize(-11);
        btnCancelarItem.setFontName("Tahoma");
        btnCancelarItem.setFontStyle("[]");
        btnCancelarItem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarItemClick(event);
            processarFlow("FrmLeadzapReceptivo", "btnCancelarItem", "OnClick");
        });
        btnCancelarItem.setImageId(9);
        btnCancelarItem.setColor("clBtnFace");
        btnCancelarItem.setAccess(false);
        btnCancelarItem.setIconReverseDirection(false);
        FHBox11.addChildren(btnCancelarItem);
        btnCancelarItem.applyProperties();
    }

    public TFPageControl pgItem = new TFPageControl();

    private void init_pgItem() {
        pgItem.setName("pgItem");
        pgItem.setLeft(0);
        pgItem.setTop(42);
        pgItem.setWidth(442);
        pgItem.setHeight(221);
        pgItem.setTabPosition("tpTop");
        pgItem.setFlexVflex("ftFalse");
        pgItem.setFlexHflex("ftTrue");
        pgItem.setRenderStyle("rsTabbed");
        pgItem.applyProperties();
        FVBox5.addChildren(pgItem);
    }

    public TFTabsheet pgItemtabCadastro = new TFTabsheet();

    private void init_pgItemtabCadastro() {
        pgItemtabCadastro.setName("pgItemtabCadastro");
        pgItemtabCadastro.setCaption("Menu");
        pgItemtabCadastro.setVisible(true);
        pgItemtabCadastro.setClosable(false);
        pgItem.addChildren(pgItemtabCadastro);
        pgItemtabCadastro.applyProperties();
    }

    public TFVBox FVBoxPgItemPrincipalCadastro = new TFVBox();

    private void init_FVBoxPgItemPrincipalCadastro() {
        FVBoxPgItemPrincipalCadastro.setName("FVBoxPgItemPrincipalCadastro");
        FVBoxPgItemPrincipalCadastro.setLeft(0);
        FVBoxPgItemPrincipalCadastro.setTop(0);
        FVBoxPgItemPrincipalCadastro.setWidth(434);
        FVBoxPgItemPrincipalCadastro.setHeight(193);
        FVBoxPgItemPrincipalCadastro.setAlign("alClient");
        FVBoxPgItemPrincipalCadastro.setBorderStyle("stNone");
        FVBoxPgItemPrincipalCadastro.setPaddingTop(3);
        FVBoxPgItemPrincipalCadastro.setPaddingLeft(0);
        FVBoxPgItemPrincipalCadastro.setPaddingRight(5);
        FVBoxPgItemPrincipalCadastro.setPaddingBottom(3);
        FVBoxPgItemPrincipalCadastro.setMarginTop(0);
        FVBoxPgItemPrincipalCadastro.setMarginLeft(0);
        FVBoxPgItemPrincipalCadastro.setMarginRight(0);
        FVBoxPgItemPrincipalCadastro.setMarginBottom(0);
        FVBoxPgItemPrincipalCadastro.setSpacing(1);
        FVBoxPgItemPrincipalCadastro.setFlexVflex("ftTrue");
        FVBoxPgItemPrincipalCadastro.setFlexHflex("ftTrue");
        FVBoxPgItemPrincipalCadastro.setScrollable(false);
        FVBoxPgItemPrincipalCadastro.setBoxShadowConfigHorizontalLength(10);
        FVBoxPgItemPrincipalCadastro.setBoxShadowConfigVerticalLength(10);
        FVBoxPgItemPrincipalCadastro.setBoxShadowConfigBlurRadius(5);
        FVBoxPgItemPrincipalCadastro.setBoxShadowConfigSpreadRadius(0);
        FVBoxPgItemPrincipalCadastro.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPgItemPrincipalCadastro.setBoxShadowConfigOpacity(75);
        pgItemtabCadastro.addChildren(FVBoxPgItemPrincipalCadastro);
        FVBoxPgItemPrincipalCadastro.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(0);
        FHBox27.setTop(0);
        FHBox27.setWidth(422);
        FHBox27.setHeight(41);
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(0);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(5);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftTrue");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        FVBoxPgItemPrincipalCadastro.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(0);
        FHBox28.setTop(0);
        FHBox28.setWidth(77);
        FHBox28.setHeight(37);
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(5);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(0);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(1);
        FHBox28.setFlexVflex("ftFalse");
        FHBox28.setFlexHflex("ftFalse");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvTop");
        FHBox27.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(0);
        FHBox29.setWidth(9);
        FHBox29.setHeight(31);
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(1);
        FHBox29.setFlexVflex("ftTrue");
        FHBox29.setFlexHflex("ftTrue");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        FHBox28.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(9);
        FLabel1.setTop(0);
        FLabel1.setWidth(49);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Sequ\u00EAncia");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FHBox28.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFInteger edtSequencia = new TFInteger();

    private void init_edtSequencia() {
        edtSequencia.setName("edtSequencia");
        edtSequencia.setLeft(77);
        edtSequencia.setTop(0);
        edtSequencia.setWidth(121);
        edtSequencia.setHeight(24);
        edtSequencia.setTable(tbLeadzapItem);
        edtSequencia.setFieldName("SEQUENCIA");
        edtSequencia.setFlex(false);
        edtSequencia.setRequired(false);
        edtSequencia.setConstraintCheckWhen("cwImmediate");
        edtSequencia.setConstraintCheckType("ctExpression");
        edtSequencia.setConstraintFocusOnError(false);
        edtSequencia.setConstraintEnableUI(true);
        edtSequencia.setConstraintEnabled(false);
        edtSequencia.setConstraintFormCheck(true);
        edtSequencia.setMaxlength(0);
        edtSequencia.setFontColor("clWindowText");
        edtSequencia.setFontSize(-13);
        edtSequencia.setFontName("Tahoma");
        edtSequencia.setFontStyle("[]");
        edtSequencia.setAlignment("taRightJustify");
        FHBox27.addChildren(edtSequencia);
        edtSequencia.applyProperties();
        addValidatable(edtSequencia);
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(42);
        FHBox12.setWidth(422);
        FHBox12.setHeight(41);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(5);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FVBoxPgItemPrincipalCadastro.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(0);
        FHBox13.setWidth(77);
        FHBox13.setHeight(37);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(5);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FHBox12.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(0);
        FHBox19.setWidth(9);
        FHBox19.setHeight(31);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftTrue");
        FHBox19.setFlexHflex("ftTrue");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FHBox13.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFLabel lblDescricaoItem = new TFLabel();

    private void init_lblDescricaoItem() {
        lblDescricaoItem.setName("lblDescricaoItem");
        lblDescricaoItem.setLeft(9);
        lblDescricaoItem.setTop(0);
        lblDescricaoItem.setWidth(46);
        lblDescricaoItem.setHeight(13);
        lblDescricaoItem.setCaption("Descri\u00E7\u00E3o");
        lblDescricaoItem.setFontColor("clWindowText");
        lblDescricaoItem.setFontSize(-11);
        lblDescricaoItem.setFontName("Tahoma");
        lblDescricaoItem.setFontStyle("[]");
        lblDescricaoItem.setVerticalAlignment("taVerticalCenter");
        lblDescricaoItem.setWordBreak(false);
        FHBox13.addChildren(lblDescricaoItem);
        lblDescricaoItem.applyProperties();
    }

    public TFString edtDescricaoItem = new TFString();

    private void init_edtDescricaoItem() {
        edtDescricaoItem.setName("edtDescricaoItem");
        edtDescricaoItem.setLeft(77);
        edtDescricaoItem.setTop(0);
        edtDescricaoItem.setWidth(121);
        edtDescricaoItem.setHeight(24);
        edtDescricaoItem.setTable(tbLeadzapItem);
        edtDescricaoItem.setFieldName("DESCRICAO");
        edtDescricaoItem.setFlex(true);
        edtDescricaoItem.setRequired(false);
        edtDescricaoItem.setConstraintCheckWhen("cwImmediate");
        edtDescricaoItem.setConstraintCheckType("ctExpression");
        edtDescricaoItem.setConstraintFocusOnError(false);
        edtDescricaoItem.setConstraintEnableUI(true);
        edtDescricaoItem.setConstraintEnabled(false);
        edtDescricaoItem.setConstraintFormCheck(true);
        edtDescricaoItem.setCharCase("ccNormal");
        edtDescricaoItem.setPwd(false);
        edtDescricaoItem.setMaxlength(0);
        edtDescricaoItem.setFontColor("clWindowText");
        edtDescricaoItem.setFontSize(-13);
        edtDescricaoItem.setFontName("Tahoma");
        edtDescricaoItem.setFontStyle("[]");
        edtDescricaoItem.setSaveLiteralCharacter(false);
        edtDescricaoItem.applyProperties();
        FHBox12.addChildren(edtDescricaoItem);
        addValidatable(edtDescricaoItem);
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(84);
        FHBox21.setWidth(422);
        FHBox21.setHeight(41);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(5);
        FHBox21.setFlexVflex("ftFalse");
        FHBox21.setFlexHflex("ftTrue");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        FVBoxPgItemPrincipalCadastro.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(0);
        FHBox22.setTop(0);
        FHBox22.setWidth(77);
        FHBox22.setHeight(37);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(5);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftFalse");
        FHBox22.setFlexHflex("ftFalse");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        FHBox21.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(0);
        FHBox23.setTop(0);
        FHBox23.setWidth(9);
        FHBox23.setHeight(31);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(0);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(1);
        FHBox23.setFlexVflex("ftTrue");
        FHBox23.setFlexHflex("ftTrue");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        FHBox22.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFLabel lblArea = new TFLabel();

    private void init_lblArea() {
        lblArea.setName("lblArea");
        lblArea.setLeft(9);
        lblArea.setTop(0);
        lblArea.setWidth(23);
        lblArea.setHeight(13);
        lblArea.setCaption("Area");
        lblArea.setFontColor("clWindowText");
        lblArea.setFontSize(-11);
        lblArea.setFontName("Tahoma");
        lblArea.setFontStyle("[]");
        lblArea.setVerticalAlignment("taVerticalCenter");
        lblArea.setWordBreak(false);
        FHBox22.addChildren(lblArea);
        lblArea.applyProperties();
    }

    public TFCombo cbbArea = new TFCombo();

    private void init_cbbArea() {
        cbbArea.setName("cbbArea");
        cbbArea.setLeft(77);
        cbbArea.setTop(0);
        cbbArea.setWidth(145);
        cbbArea.setHeight(21);
        cbbArea.setTable(tbLeadzapItem);
        cbbArea.setLookupTable(tbLeadzapArea);
        cbbArea.setFieldName("ID_AREA");
        cbbArea.setLookupKey("ID_AREA");
        cbbArea.setLookupDesc("DESCRICAO");
        cbbArea.setFlex(true);
        cbbArea.setReadOnly(true);
        cbbArea.setRequired(false);
        cbbArea.setPrompt("Selecione");
        cbbArea.setConstraintCheckWhen("cwImmediate");
        cbbArea.setConstraintCheckType("ctExpression");
        cbbArea.setConstraintFocusOnError(false);
        cbbArea.setConstraintEnableUI(true);
        cbbArea.setConstraintEnabled(false);
        cbbArea.setConstraintFormCheck(true);
        cbbArea.setClearOnDelKey(true);
        cbbArea.setUseClearButton(false);
        cbbArea.setHideClearButtonOnNullValue(false);
        cbbArea.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbAreaChange(event);
            processarFlow("FrmLeadzapReceptivo", "cbbArea", "OnChange");
        });
        FHBox21.addChildren(cbbArea);
        cbbArea.applyProperties();
        addValidatable(cbbArea);
    }

    public TFHBox hboxTipoEvento = new TFHBox();

    private void init_hboxTipoEvento() {
        hboxTipoEvento.setName("hboxTipoEvento");
        hboxTipoEvento.setLeft(0);
        hboxTipoEvento.setTop(126);
        hboxTipoEvento.setWidth(422);
        hboxTipoEvento.setHeight(41);
        hboxTipoEvento.setBorderStyle("stNone");
        hboxTipoEvento.setPaddingTop(0);
        hboxTipoEvento.setPaddingLeft(0);
        hboxTipoEvento.setPaddingRight(0);
        hboxTipoEvento.setPaddingBottom(0);
        hboxTipoEvento.setMarginTop(0);
        hboxTipoEvento.setMarginLeft(0);
        hboxTipoEvento.setMarginRight(0);
        hboxTipoEvento.setMarginBottom(0);
        hboxTipoEvento.setSpacing(5);
        hboxTipoEvento.setFlexVflex("ftFalse");
        hboxTipoEvento.setFlexHflex("ftTrue");
        hboxTipoEvento.setScrollable(false);
        hboxTipoEvento.setBoxShadowConfigHorizontalLength(10);
        hboxTipoEvento.setBoxShadowConfigVerticalLength(10);
        hboxTipoEvento.setBoxShadowConfigBlurRadius(5);
        hboxTipoEvento.setBoxShadowConfigSpreadRadius(0);
        hboxTipoEvento.setBoxShadowConfigShadowColor("clBlack");
        hboxTipoEvento.setBoxShadowConfigOpacity(75);
        hboxTipoEvento.setVAlign("tvTop");
        FVBoxPgItemPrincipalCadastro.addChildren(hboxTipoEvento);
        hboxTipoEvento.applyProperties();
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(0);
        FHBox25.setTop(0);
        FHBox25.setWidth(77);
        FHBox25.setHeight(37);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(5);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(0);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(1);
        FHBox25.setFlexVflex("ftFalse");
        FHBox25.setFlexHflex("ftFalse");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvTop");
        hboxTipoEvento.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(0);
        FHBox26.setTop(0);
        FHBox26.setWidth(9);
        FHBox26.setHeight(31);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(0);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(0);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(1);
        FHBox26.setFlexVflex("ftTrue");
        FHBox26.setFlexHflex("ftTrue");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvTop");
        FHBox25.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFLabel lblTipoEvento = new TFLabel();

    private void init_lblTipoEvento() {
        lblTipoEvento.setName("lblTipoEvento");
        lblTipoEvento.setLeft(9);
        lblTipoEvento.setTop(0);
        lblTipoEvento.setWidth(57);
        lblTipoEvento.setHeight(13);
        lblTipoEvento.setCaption("Tipo Evento");
        lblTipoEvento.setFontColor("clWindowText");
        lblTipoEvento.setFontSize(-11);
        lblTipoEvento.setFontName("Tahoma");
        lblTipoEvento.setFontStyle("[]");
        lblTipoEvento.setVerticalAlignment("taVerticalCenter");
        lblTipoEvento.setWordBreak(false);
        FHBox25.addChildren(lblTipoEvento);
        lblTipoEvento.applyProperties();
    }

    public TFCombo cbbTipoEvento = new TFCombo();

    private void init_cbbTipoEvento() {
        cbbTipoEvento.setName("cbbTipoEvento");
        cbbTipoEvento.setLeft(77);
        cbbTipoEvento.setTop(0);
        cbbTipoEvento.setWidth(145);
        cbbTipoEvento.setHeight(21);
        cbbTipoEvento.setTable(tbLeadzapItem);
        cbbTipoEvento.setLookupTable(tbEventosTipo);
        cbbTipoEvento.setFieldName("COD_TIPO_EVENTO");
        cbbTipoEvento.setLookupKey("COD_TIPO_EVENTO");
        cbbTipoEvento.setLookupDesc("DESC_TIPO_EVENTO");
        cbbTipoEvento.setFlex(true);
        cbbTipoEvento.setReadOnly(true);
        cbbTipoEvento.setRequired(false);
        cbbTipoEvento.setPrompt("Selecione");
        cbbTipoEvento.setConstraintCheckWhen("cwImmediate");
        cbbTipoEvento.setConstraintCheckType("ctExpression");
        cbbTipoEvento.setConstraintFocusOnError(false);
        cbbTipoEvento.setConstraintEnableUI(true);
        cbbTipoEvento.setConstraintEnabled(false);
        cbbTipoEvento.setConstraintFormCheck(true);
        cbbTipoEvento.setClearOnDelKey(true);
        cbbTipoEvento.setUseClearButton(false);
        cbbTipoEvento.setHideClearButtonOnNullValue(false);
        hboxTipoEvento.addChildren(cbbTipoEvento);
        cbbTipoEvento.applyProperties();
        addValidatable(cbbTipoEvento);
    }

    public TFTabsheet pgItemtabTemplates = new TFTabsheet();

    private void init_pgItemtabTemplates() {
        pgItemtabTemplates.setName("pgItemtabTemplates");
        pgItemtabTemplates.setCaption("Templates");
        pgItemtabTemplates.setVisible(true);
        pgItemtabTemplates.setClosable(false);
        pgItem.addChildren(pgItemtabTemplates);
        pgItemtabTemplates.applyProperties();
    }

    public TFVBox FVBoxPrincipalTemplatesPgItem = new TFVBox();

    private void init_FVBoxPrincipalTemplatesPgItem() {
        FVBoxPrincipalTemplatesPgItem.setName("FVBoxPrincipalTemplatesPgItem");
        FVBoxPrincipalTemplatesPgItem.setLeft(0);
        FVBoxPrincipalTemplatesPgItem.setTop(0);
        FVBoxPrincipalTemplatesPgItem.setWidth(434);
        FVBoxPrincipalTemplatesPgItem.setHeight(193);
        FVBoxPrincipalTemplatesPgItem.setAlign("alClient");
        FVBoxPrincipalTemplatesPgItem.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItem.setPaddingTop(3);
        FVBoxPrincipalTemplatesPgItem.setPaddingLeft(5);
        FVBoxPrincipalTemplatesPgItem.setPaddingRight(5);
        FVBoxPrincipalTemplatesPgItem.setPaddingBottom(3);
        FVBoxPrincipalTemplatesPgItem.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItem.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItem.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItem.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItem.setSpacing(1);
        FVBoxPrincipalTemplatesPgItem.setFlexVflex("ftTrue");
        FVBoxPrincipalTemplatesPgItem.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItem.setScrollable(false);
        FVBoxPrincipalTemplatesPgItem.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItem.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItem.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItem.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItem.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItem.setBoxShadowConfigOpacity(75);
        pgItemtabTemplates.addChildren(FVBoxPrincipalTemplatesPgItem);
        FVBoxPrincipalTemplatesPgItem.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemItem1 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemItem1() {
        FVBoxPrincipalTemplatesPgItemItem1.setName("FVBoxPrincipalTemplatesPgItemItem1");
        FVBoxPrincipalTemplatesPgItemItem1.setLeft(0);
        FVBoxPrincipalTemplatesPgItemItem1.setTop(0);
        FVBoxPrincipalTemplatesPgItemItem1.setWidth(425);
        FVBoxPrincipalTemplatesPgItemItem1.setHeight(41);
        FVBoxPrincipalTemplatesPgItemItem1.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemItem1.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemItem1.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemItem1.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemItem1.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemItem1.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemItem1.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemItem1.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemItem1.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemItem1.setSpacing(5);
        FVBoxPrincipalTemplatesPgItemItem1.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemItem1.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemItem1.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemItem1.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemItem1.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemItem1.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemItem1.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemItem1.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemItem1.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemItem1.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItem.addChildren(FVBoxPrincipalTemplatesPgItemItem1);
        FVBoxPrincipalTemplatesPgItemItem1.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemLabel1 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemLabel1() {
        FVBoxPrincipalTemplatesPgItemLabel1.setName("FVBoxPrincipalTemplatesPgItemLabel1");
        FVBoxPrincipalTemplatesPgItemLabel1.setLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setTop(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setWidth(177);
        FVBoxPrincipalTemplatesPgItemLabel1.setHeight(37);
        FVBoxPrincipalTemplatesPgItemLabel1.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemLabel1.setPaddingTop(5);
        FVBoxPrincipalTemplatesPgItemLabel1.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemLabel1.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel1.setFlexHflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel1.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemLabel1.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel1.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel1.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemLabel1.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemLabel1.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemLabel1.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemLabel1.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemItem1.addChildren(FVBoxPrincipalTemplatesPgItemLabel1);
        FVBoxPrincipalTemplatesPgItemLabel1.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemRecuo1 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemRecuo1() {
        FVBoxPrincipalTemplatesPgItemRecuo1.setName("FVBoxPrincipalTemplatesPgItemRecuo1");
        FVBoxPrincipalTemplatesPgItemRecuo1.setLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setWidth(9);
        FVBoxPrincipalTemplatesPgItemRecuo1.setHeight(31);
        FVBoxPrincipalTemplatesPgItemRecuo1.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemRecuo1.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemRecuo1.setFlexVflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo1.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo1.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemRecuo1.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo1.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo1.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemRecuo1.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemRecuo1.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemRecuo1.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemRecuo1.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemLabel1.addChildren(FVBoxPrincipalTemplatesPgItemRecuo1);
        FVBoxPrincipalTemplatesPgItemRecuo1.applyProperties();
    }

    public TFLabel FLabelPrincipalTemplatesPgItem1 = new TFLabel();

    private void init_FLabelPrincipalTemplatesPgItem1() {
        FLabelPrincipalTemplatesPgItem1.setName("FLabelPrincipalTemplatesPgItem1");
        FLabelPrincipalTemplatesPgItem1.setLeft(9);
        FLabelPrincipalTemplatesPgItem1.setTop(0);
        FLabelPrincipalTemplatesPgItem1.setWidth(106);
        FLabelPrincipalTemplatesPgItem1.setHeight(13);
        FLabelPrincipalTemplatesPgItem1.setCaption("Boas Vindas Vendedor");
        FLabelPrincipalTemplatesPgItem1.setFontColor("clWindowText");
        FLabelPrincipalTemplatesPgItem1.setFontSize(-11);
        FLabelPrincipalTemplatesPgItem1.setFontName("Tahoma");
        FLabelPrincipalTemplatesPgItem1.setFontStyle("[]");
        FLabelPrincipalTemplatesPgItem1.setVerticalAlignment("taVerticalCenter");
        FLabelPrincipalTemplatesPgItem1.setWordBreak(false);
        FVBoxPrincipalTemplatesPgItemLabel1.addChildren(FLabelPrincipalTemplatesPgItem1);
        FLabelPrincipalTemplatesPgItem1.applyProperties();
    }

    public TFCombo cbbIdTemplateBoasVIndasVendedor = new TFCombo();

    private void init_cbbIdTemplateBoasVIndasVendedor() {
        cbbIdTemplateBoasVIndasVendedor.setName("cbbIdTemplateBoasVIndasVendedor");
        cbbIdTemplateBoasVIndasVendedor.setLeft(177);
        cbbIdTemplateBoasVIndasVendedor.setTop(0);
        cbbIdTemplateBoasVIndasVendedor.setWidth(145);
        cbbIdTemplateBoasVIndasVendedor.setHeight(21);
        cbbIdTemplateBoasVIndasVendedor.setTable(tbLeadzapItem);
        cbbIdTemplateBoasVIndasVendedor.setLookupTable(tbTemplateLead);
        cbbIdTemplateBoasVIndasVendedor.setFieldName("ID_TEMPATE_VEND_CONTATO");
        cbbIdTemplateBoasVIndasVendedor.setLookupKey("ID_EMAIL_MODELO");
        cbbIdTemplateBoasVIndasVendedor.setLookupDesc("MODELO");
        cbbIdTemplateBoasVIndasVendedor.setFlex(true);
        cbbIdTemplateBoasVIndasVendedor.setReadOnly(true);
        cbbIdTemplateBoasVIndasVendedor.setRequired(false);
        cbbIdTemplateBoasVIndasVendedor.setPrompt("Selecione o Template");
        cbbIdTemplateBoasVIndasVendedor.setConstraintCheckWhen("cwImmediate");
        cbbIdTemplateBoasVIndasVendedor.setConstraintCheckType("ctExpression");
        cbbIdTemplateBoasVIndasVendedor.setConstraintFocusOnError(false);
        cbbIdTemplateBoasVIndasVendedor.setConstraintEnableUI(true);
        cbbIdTemplateBoasVIndasVendedor.setConstraintEnabled(false);
        cbbIdTemplateBoasVIndasVendedor.setConstraintFormCheck(true);
        cbbIdTemplateBoasVIndasVendedor.setClearOnDelKey(true);
        cbbIdTemplateBoasVIndasVendedor.setUseClearButton(false);
        cbbIdTemplateBoasVIndasVendedor.setHideClearButtonOnNullValue(false);
        FVBoxPrincipalTemplatesPgItemItem1.addChildren(cbbIdTemplateBoasVIndasVendedor);
        cbbIdTemplateBoasVIndasVendedor.applyProperties();
        addValidatable(cbbIdTemplateBoasVIndasVendedor);
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemItem2 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemItem2() {
        FVBoxPrincipalTemplatesPgItemItem2.setName("FVBoxPrincipalTemplatesPgItemItem2");
        FVBoxPrincipalTemplatesPgItemItem2.setLeft(0);
        FVBoxPrincipalTemplatesPgItemItem2.setTop(42);
        FVBoxPrincipalTemplatesPgItemItem2.setWidth(425);
        FVBoxPrincipalTemplatesPgItemItem2.setHeight(41);
        FVBoxPrincipalTemplatesPgItemItem2.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemItem2.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemItem2.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemItem2.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemItem2.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemItem2.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemItem2.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemItem2.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemItem2.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemItem2.setSpacing(5);
        FVBoxPrincipalTemplatesPgItemItem2.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemItem2.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemItem2.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemItem2.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemItem2.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemItem2.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemItem2.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemItem2.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemItem2.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemItem2.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItem.addChildren(FVBoxPrincipalTemplatesPgItemItem2);
        FVBoxPrincipalTemplatesPgItemItem2.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemLabel2 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemLabel2() {
        FVBoxPrincipalTemplatesPgItemLabel2.setName("FVBoxPrincipalTemplatesPgItemLabel2");
        FVBoxPrincipalTemplatesPgItemLabel2.setLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setTop(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setWidth(177);
        FVBoxPrincipalTemplatesPgItemLabel2.setHeight(37);
        FVBoxPrincipalTemplatesPgItemLabel2.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemLabel2.setPaddingTop(5);
        FVBoxPrincipalTemplatesPgItemLabel2.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemLabel2.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel2.setFlexHflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel2.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemLabel2.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel2.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel2.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemLabel2.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemLabel2.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemLabel2.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemLabel2.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemItem2.addChildren(FVBoxPrincipalTemplatesPgItemLabel2);
        FVBoxPrincipalTemplatesPgItemLabel2.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemRecuo2 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemRecuo2() {
        FVBoxPrincipalTemplatesPgItemRecuo2.setName("FVBoxPrincipalTemplatesPgItemRecuo2");
        FVBoxPrincipalTemplatesPgItemRecuo2.setLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setWidth(9);
        FVBoxPrincipalTemplatesPgItemRecuo2.setHeight(31);
        FVBoxPrincipalTemplatesPgItemRecuo2.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemRecuo2.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemRecuo2.setFlexVflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo2.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo2.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemRecuo2.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo2.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo2.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemRecuo2.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemRecuo2.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemRecuo2.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemRecuo2.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemLabel2.addChildren(FVBoxPrincipalTemplatesPgItemRecuo2);
        FVBoxPrincipalTemplatesPgItemRecuo2.applyProperties();
    }

    public TFLabel FLabelPrincipalTemplatesPgItem2 = new TFLabel();

    private void init_FLabelPrincipalTemplatesPgItem2() {
        FLabelPrincipalTemplatesPgItem2.setName("FLabelPrincipalTemplatesPgItem2");
        FLabelPrincipalTemplatesPgItem2.setLeft(9);
        FLabelPrincipalTemplatesPgItem2.setTop(0);
        FLabelPrincipalTemplatesPgItem2.setWidth(106);
        FLabelPrincipalTemplatesPgItem2.setHeight(13);
        FLabelPrincipalTemplatesPgItem2.setCaption("Boas Vindas Consultor");
        FLabelPrincipalTemplatesPgItem2.setFontColor("clWindowText");
        FLabelPrincipalTemplatesPgItem2.setFontSize(-11);
        FLabelPrincipalTemplatesPgItem2.setFontName("Tahoma");
        FLabelPrincipalTemplatesPgItem2.setFontStyle("[]");
        FLabelPrincipalTemplatesPgItem2.setVerticalAlignment("taVerticalCenter");
        FLabelPrincipalTemplatesPgItem2.setWordBreak(false);
        FVBoxPrincipalTemplatesPgItemLabel2.addChildren(FLabelPrincipalTemplatesPgItem2);
        FLabelPrincipalTemplatesPgItem2.applyProperties();
    }

    public TFCombo cbbIdTemplateBoasVIndasConsultor = new TFCombo();

    private void init_cbbIdTemplateBoasVIndasConsultor() {
        cbbIdTemplateBoasVIndasConsultor.setName("cbbIdTemplateBoasVIndasConsultor");
        cbbIdTemplateBoasVIndasConsultor.setLeft(177);
        cbbIdTemplateBoasVIndasConsultor.setTop(0);
        cbbIdTemplateBoasVIndasConsultor.setWidth(145);
        cbbIdTemplateBoasVIndasConsultor.setHeight(21);
        cbbIdTemplateBoasVIndasConsultor.setTable(tbLeadzapItem);
        cbbIdTemplateBoasVIndasConsultor.setLookupTable(tbTemplateLead);
        cbbIdTemplateBoasVIndasConsultor.setFieldName("ID_TEMPATE_ATEND_CONTATO");
        cbbIdTemplateBoasVIndasConsultor.setLookupKey("ID_EMAIL_MODELO");
        cbbIdTemplateBoasVIndasConsultor.setLookupDesc("MODELO");
        cbbIdTemplateBoasVIndasConsultor.setFlex(true);
        cbbIdTemplateBoasVIndasConsultor.setReadOnly(true);
        cbbIdTemplateBoasVIndasConsultor.setRequired(false);
        cbbIdTemplateBoasVIndasConsultor.setPrompt("Selecione o Template");
        cbbIdTemplateBoasVIndasConsultor.setConstraintCheckWhen("cwImmediate");
        cbbIdTemplateBoasVIndasConsultor.setConstraintCheckType("ctExpression");
        cbbIdTemplateBoasVIndasConsultor.setConstraintFocusOnError(false);
        cbbIdTemplateBoasVIndasConsultor.setConstraintEnableUI(true);
        cbbIdTemplateBoasVIndasConsultor.setConstraintEnabled(false);
        cbbIdTemplateBoasVIndasConsultor.setConstraintFormCheck(true);
        cbbIdTemplateBoasVIndasConsultor.setClearOnDelKey(true);
        cbbIdTemplateBoasVIndasConsultor.setUseClearButton(false);
        cbbIdTemplateBoasVIndasConsultor.setHideClearButtonOnNullValue(false);
        cbbIdTemplateBoasVIndasConsultor.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbIdTemplateJaExisteAtendimentoVendedorChange(event);
            processarFlow("FrmLeadzapReceptivo", "cbbIdTemplateBoasVIndasConsultor", "OnChange");
        });
        FVBoxPrincipalTemplatesPgItemItem2.addChildren(cbbIdTemplateBoasVIndasConsultor);
        cbbIdTemplateBoasVIndasConsultor.applyProperties();
        addValidatable(cbbIdTemplateBoasVIndasConsultor);
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemItem3 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemItem3() {
        FVBoxPrincipalTemplatesPgItemItem3.setName("FVBoxPrincipalTemplatesPgItemItem3");
        FVBoxPrincipalTemplatesPgItemItem3.setLeft(0);
        FVBoxPrincipalTemplatesPgItemItem3.setTop(84);
        FVBoxPrincipalTemplatesPgItemItem3.setWidth(425);
        FVBoxPrincipalTemplatesPgItemItem3.setHeight(41);
        FVBoxPrincipalTemplatesPgItemItem3.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemItem3.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemItem3.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemItem3.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemItem3.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemItem3.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemItem3.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemItem3.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemItem3.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemItem3.setSpacing(5);
        FVBoxPrincipalTemplatesPgItemItem3.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemItem3.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemItem3.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemItem3.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemItem3.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemItem3.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemItem3.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemItem3.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemItem3.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemItem3.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItem.addChildren(FVBoxPrincipalTemplatesPgItemItem3);
        FVBoxPrincipalTemplatesPgItemItem3.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemLabel3 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemLabel3() {
        FVBoxPrincipalTemplatesPgItemLabel3.setName("FVBoxPrincipalTemplatesPgItemLabel3");
        FVBoxPrincipalTemplatesPgItemLabel3.setLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setTop(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setWidth(177);
        FVBoxPrincipalTemplatesPgItemLabel3.setHeight(37);
        FVBoxPrincipalTemplatesPgItemLabel3.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemLabel3.setPaddingTop(5);
        FVBoxPrincipalTemplatesPgItemLabel3.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemLabel3.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel3.setFlexHflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel3.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemLabel3.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel3.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel3.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemLabel3.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemLabel3.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemLabel3.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemLabel3.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemItem3.addChildren(FVBoxPrincipalTemplatesPgItemLabel3);
        FVBoxPrincipalTemplatesPgItemLabel3.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemRecuo3 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemRecuo3() {
        FVBoxPrincipalTemplatesPgItemRecuo3.setName("FVBoxPrincipalTemplatesPgItemRecuo3");
        FVBoxPrincipalTemplatesPgItemRecuo3.setLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setWidth(9);
        FVBoxPrincipalTemplatesPgItemRecuo3.setHeight(31);
        FVBoxPrincipalTemplatesPgItemRecuo3.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemRecuo3.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemRecuo3.setFlexVflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo3.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo3.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemRecuo3.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo3.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo3.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemRecuo3.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemRecuo3.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemRecuo3.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemRecuo3.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemLabel3.addChildren(FVBoxPrincipalTemplatesPgItemRecuo3);
        FVBoxPrincipalTemplatesPgItemRecuo3.applyProperties();
    }

    public TFLabel FLabelPrincipalTemplatesPgItem3 = new TFLabel();

    private void init_FLabelPrincipalTemplatesPgItem3() {
        FLabelPrincipalTemplatesPgItem3.setName("FLabelPrincipalTemplatesPgItem3");
        FLabelPrincipalTemplatesPgItem3.setLeft(9);
        FLabelPrincipalTemplatesPgItem3.setTop(0);
        FLabelPrincipalTemplatesPgItem3.setWidth(155);
        FLabelPrincipalTemplatesPgItem3.setHeight(13);
        FLabelPrincipalTemplatesPgItem3.setCaption("J\u00E1 existe atendimento Vendedor");
        FLabelPrincipalTemplatesPgItem3.setFontColor("clWindowText");
        FLabelPrincipalTemplatesPgItem3.setFontSize(-11);
        FLabelPrincipalTemplatesPgItem3.setFontName("Tahoma");
        FLabelPrincipalTemplatesPgItem3.setFontStyle("[]");
        FLabelPrincipalTemplatesPgItem3.setVerticalAlignment("taVerticalCenter");
        FLabelPrincipalTemplatesPgItem3.setWordBreak(false);
        FVBoxPrincipalTemplatesPgItemLabel3.addChildren(FLabelPrincipalTemplatesPgItem3);
        FLabelPrincipalTemplatesPgItem3.applyProperties();
    }

    public TFCombo cbbIdTemplateJaExisteAtendimentoVendedor = new TFCombo();

    private void init_cbbIdTemplateJaExisteAtendimentoVendedor() {
        cbbIdTemplateJaExisteAtendimentoVendedor.setName("cbbIdTemplateJaExisteAtendimentoVendedor");
        cbbIdTemplateJaExisteAtendimentoVendedor.setLeft(177);
        cbbIdTemplateJaExisteAtendimentoVendedor.setTop(0);
        cbbIdTemplateJaExisteAtendimentoVendedor.setWidth(145);
        cbbIdTemplateJaExisteAtendimentoVendedor.setHeight(21);
        cbbIdTemplateJaExisteAtendimentoVendedor.setTable(tbLeadzapItem);
        cbbIdTemplateJaExisteAtendimentoVendedor.setLookupTable(tbTemplateLead);
        cbbIdTemplateJaExisteAtendimentoVendedor.setFieldName("ID_TEMPATE_VEND_EXIST_ATD");
        cbbIdTemplateJaExisteAtendimentoVendedor.setLookupKey("ID_EMAIL_MODELO");
        cbbIdTemplateJaExisteAtendimentoVendedor.setLookupDesc("MODELO");
        cbbIdTemplateJaExisteAtendimentoVendedor.setFlex(true);
        cbbIdTemplateJaExisteAtendimentoVendedor.setReadOnly(true);
        cbbIdTemplateJaExisteAtendimentoVendedor.setRequired(false);
        cbbIdTemplateJaExisteAtendimentoVendedor.setPrompt("Selecione o Template");
        cbbIdTemplateJaExisteAtendimentoVendedor.setConstraintCheckWhen("cwImmediate");
        cbbIdTemplateJaExisteAtendimentoVendedor.setConstraintCheckType("ctExpression");
        cbbIdTemplateJaExisteAtendimentoVendedor.setConstraintFocusOnError(false);
        cbbIdTemplateJaExisteAtendimentoVendedor.setConstraintEnableUI(true);
        cbbIdTemplateJaExisteAtendimentoVendedor.setConstraintEnabled(false);
        cbbIdTemplateJaExisteAtendimentoVendedor.setConstraintFormCheck(true);
        cbbIdTemplateJaExisteAtendimentoVendedor.setClearOnDelKey(true);
        cbbIdTemplateJaExisteAtendimentoVendedor.setUseClearButton(false);
        cbbIdTemplateJaExisteAtendimentoVendedor.setHideClearButtonOnNullValue(false);
        FVBoxPrincipalTemplatesPgItemItem3.addChildren(cbbIdTemplateJaExisteAtendimentoVendedor);
        cbbIdTemplateJaExisteAtendimentoVendedor.applyProperties();
        addValidatable(cbbIdTemplateJaExisteAtendimentoVendedor);
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemItem4 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemItem4() {
        FVBoxPrincipalTemplatesPgItemItem4.setName("FVBoxPrincipalTemplatesPgItemItem4");
        FVBoxPrincipalTemplatesPgItemItem4.setLeft(0);
        FVBoxPrincipalTemplatesPgItemItem4.setTop(126);
        FVBoxPrincipalTemplatesPgItemItem4.setWidth(425);
        FVBoxPrincipalTemplatesPgItemItem4.setHeight(41);
        FVBoxPrincipalTemplatesPgItemItem4.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemItem4.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemItem4.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemItem4.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemItem4.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemItem4.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemItem4.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemItem4.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemItem4.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemItem4.setSpacing(5);
        FVBoxPrincipalTemplatesPgItemItem4.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemItem4.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemItem4.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemItem4.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemItem4.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemItem4.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemItem4.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemItem4.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemItem4.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemItem4.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItem.addChildren(FVBoxPrincipalTemplatesPgItemItem4);
        FVBoxPrincipalTemplatesPgItemItem4.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemLabel4 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemLabel4() {
        FVBoxPrincipalTemplatesPgItemLabel4.setName("FVBoxPrincipalTemplatesPgItemLabel4");
        FVBoxPrincipalTemplatesPgItemLabel4.setLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setTop(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setWidth(177);
        FVBoxPrincipalTemplatesPgItemLabel4.setHeight(37);
        FVBoxPrincipalTemplatesPgItemLabel4.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemLabel4.setPaddingTop(5);
        FVBoxPrincipalTemplatesPgItemLabel4.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemLabel4.setFlexVflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel4.setFlexHflex("ftFalse");
        FVBoxPrincipalTemplatesPgItemLabel4.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemLabel4.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel4.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemLabel4.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemLabel4.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemLabel4.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemLabel4.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemLabel4.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemItem4.addChildren(FVBoxPrincipalTemplatesPgItemLabel4);
        FVBoxPrincipalTemplatesPgItemLabel4.applyProperties();
    }

    public TFHBox FVBoxPrincipalTemplatesPgItemRecuo4 = new TFHBox();

    private void init_FVBoxPrincipalTemplatesPgItemRecuo4() {
        FVBoxPrincipalTemplatesPgItemRecuo4.setName("FVBoxPrincipalTemplatesPgItemRecuo4");
        FVBoxPrincipalTemplatesPgItemRecuo4.setLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setWidth(9);
        FVBoxPrincipalTemplatesPgItemRecuo4.setHeight(31);
        FVBoxPrincipalTemplatesPgItemRecuo4.setBorderStyle("stNone");
        FVBoxPrincipalTemplatesPgItemRecuo4.setPaddingTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setPaddingLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setPaddingRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setPaddingBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setMarginTop(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setMarginLeft(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setMarginRight(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setMarginBottom(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setSpacing(1);
        FVBoxPrincipalTemplatesPgItemRecuo4.setFlexVflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo4.setFlexHflex("ftTrue");
        FVBoxPrincipalTemplatesPgItemRecuo4.setScrollable(false);
        FVBoxPrincipalTemplatesPgItemRecuo4.setBoxShadowConfigHorizontalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo4.setBoxShadowConfigVerticalLength(10);
        FVBoxPrincipalTemplatesPgItemRecuo4.setBoxShadowConfigBlurRadius(5);
        FVBoxPrincipalTemplatesPgItemRecuo4.setBoxShadowConfigSpreadRadius(0);
        FVBoxPrincipalTemplatesPgItemRecuo4.setBoxShadowConfigShadowColor("clBlack");
        FVBoxPrincipalTemplatesPgItemRecuo4.setBoxShadowConfigOpacity(75);
        FVBoxPrincipalTemplatesPgItemRecuo4.setVAlign("tvTop");
        FVBoxPrincipalTemplatesPgItemLabel4.addChildren(FVBoxPrincipalTemplatesPgItemRecuo4);
        FVBoxPrincipalTemplatesPgItemRecuo4.applyProperties();
    }

    public TFLabel FLabelPrincipalTemplatesPgItem4 = new TFLabel();

    private void init_FLabelPrincipalTemplatesPgItem4() {
        FLabelPrincipalTemplatesPgItem4.setName("FLabelPrincipalTemplatesPgItem4");
        FLabelPrincipalTemplatesPgItem4.setLeft(9);
        FLabelPrincipalTemplatesPgItem4.setTop(0);
        FLabelPrincipalTemplatesPgItem4.setWidth(155);
        FLabelPrincipalTemplatesPgItem4.setHeight(13);
        FLabelPrincipalTemplatesPgItem4.setCaption("J\u00E1 existe atendimento Consultor");
        FLabelPrincipalTemplatesPgItem4.setFontColor("clWindowText");
        FLabelPrincipalTemplatesPgItem4.setFontSize(-11);
        FLabelPrincipalTemplatesPgItem4.setFontName("Tahoma");
        FLabelPrincipalTemplatesPgItem4.setFontStyle("[]");
        FLabelPrincipalTemplatesPgItem4.setVerticalAlignment("taVerticalCenter");
        FLabelPrincipalTemplatesPgItem4.setWordBreak(false);
        FVBoxPrincipalTemplatesPgItemLabel4.addChildren(FLabelPrincipalTemplatesPgItem4);
        FLabelPrincipalTemplatesPgItem4.applyProperties();
    }

    public TFCombo cbbIdTemplateJaExisteAtendimentoConsultor = new TFCombo();

    private void init_cbbIdTemplateJaExisteAtendimentoConsultor() {
        cbbIdTemplateJaExisteAtendimentoConsultor.setName("cbbIdTemplateJaExisteAtendimentoConsultor");
        cbbIdTemplateJaExisteAtendimentoConsultor.setLeft(177);
        cbbIdTemplateJaExisteAtendimentoConsultor.setTop(0);
        cbbIdTemplateJaExisteAtendimentoConsultor.setWidth(145);
        cbbIdTemplateJaExisteAtendimentoConsultor.setHeight(21);
        cbbIdTemplateJaExisteAtendimentoConsultor.setTable(tbLeadzapItem);
        cbbIdTemplateJaExisteAtendimentoConsultor.setLookupTable(tbTemplateLead);
        cbbIdTemplateJaExisteAtendimentoConsultor.setFieldName("ID_TEMPATE_ATEND_EXIST_ATD");
        cbbIdTemplateJaExisteAtendimentoConsultor.setLookupKey("ID_EMAIL_MODELO");
        cbbIdTemplateJaExisteAtendimentoConsultor.setLookupDesc("MODELO");
        cbbIdTemplateJaExisteAtendimentoConsultor.setFlex(true);
        cbbIdTemplateJaExisteAtendimentoConsultor.setReadOnly(true);
        cbbIdTemplateJaExisteAtendimentoConsultor.setRequired(false);
        cbbIdTemplateJaExisteAtendimentoConsultor.setPrompt("Selecione o Template");
        cbbIdTemplateJaExisteAtendimentoConsultor.setConstraintCheckWhen("cwImmediate");
        cbbIdTemplateJaExisteAtendimentoConsultor.setConstraintCheckType("ctExpression");
        cbbIdTemplateJaExisteAtendimentoConsultor.setConstraintFocusOnError(false);
        cbbIdTemplateJaExisteAtendimentoConsultor.setConstraintEnableUI(true);
        cbbIdTemplateJaExisteAtendimentoConsultor.setConstraintEnabled(false);
        cbbIdTemplateJaExisteAtendimentoConsultor.setConstraintFormCheck(true);
        cbbIdTemplateJaExisteAtendimentoConsultor.setClearOnDelKey(true);
        cbbIdTemplateJaExisteAtendimentoConsultor.setUseClearButton(false);
        cbbIdTemplateJaExisteAtendimentoConsultor.setHideClearButtonOnNullValue(false);
        FVBoxPrincipalTemplatesPgItemItem4.addChildren(cbbIdTemplateJaExisteAtendimentoConsultor);
        cbbIdTemplateJaExisteAtendimentoConsultor.applyProperties();
        addValidatable(cbbIdTemplateJaExisteAtendimentoConsultor);
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(444);
        FVBox6.setTop(0);
        FVBox6.setWidth(420);
        FVBox6.setHeight(268);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(7);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFGroupbox groupBoxAtendimento = new TFGroupbox();

    private void init_groupBoxAtendimento() {
        groupBoxAtendimento.setName("groupBoxAtendimento");
        groupBoxAtendimento.setLeft(0);
        groupBoxAtendimento.setTop(0);
        groupBoxAtendimento.setWidth(414);
        groupBoxAtendimento.setHeight(197);
        groupBoxAtendimento.setCaption("Atendimento");
        groupBoxAtendimento.setFontColor("clWindowText");
        groupBoxAtendimento.setFontSize(-11);
        groupBoxAtendimento.setFontName("Tahoma");
        groupBoxAtendimento.setFontStyle("[]");
        groupBoxAtendimento.setFlexVflex("ftTrue");
        groupBoxAtendimento.setFlexHflex("ftTrue");
        groupBoxAtendimento.setScrollable(false);
        groupBoxAtendimento.setClosable(false);
        groupBoxAtendimento.setClosed(false);
        groupBoxAtendimento.setOrient("coHorizontal");
        groupBoxAtendimento.setStyle("grpLine");
        groupBoxAtendimento.setHeaderImageId(0);
        FVBox6.addChildren(groupBoxAtendimento);
        groupBoxAtendimento.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(7);
        FVBox8.setTop(23);
        FVBox8.setWidth(407);
        FVBox8.setHeight(164);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(5);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftTrue");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        groupBoxAtendimento.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFHBox FHBox30 = new TFHBox();

    private void init_FHBox30() {
        FHBox30.setName("FHBox30");
        FHBox30.setLeft(0);
        FHBox30.setTop(0);
        FHBox30.setWidth(402);
        FHBox30.setHeight(54);
        FHBox30.setBorderStyle("stNone");
        FHBox30.setPaddingTop(0);
        FHBox30.setPaddingLeft(0);
        FHBox30.setPaddingRight(0);
        FHBox30.setPaddingBottom(0);
        FHBox30.setMarginTop(0);
        FHBox30.setMarginLeft(0);
        FHBox30.setMarginRight(0);
        FHBox30.setMarginBottom(0);
        FHBox30.setSpacing(5);
        FHBox30.setFlexVflex("ftFalse");
        FHBox30.setFlexHflex("ftTrue");
        FHBox30.setScrollable(false);
        FHBox30.setBoxShadowConfigHorizontalLength(10);
        FHBox30.setBoxShadowConfigVerticalLength(10);
        FHBox30.setBoxShadowConfigBlurRadius(5);
        FHBox30.setBoxShadowConfigSpreadRadius(0);
        FHBox30.setBoxShadowConfigShadowColor("clBlack");
        FHBox30.setBoxShadowConfigOpacity(75);
        FHBox30.setVAlign("tvTop");
        FVBox8.addChildren(FHBox30);
        FHBox30.applyProperties();
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(0);
        FHBox31.setTop(0);
        FHBox31.setWidth(118);
        FHBox31.setHeight(37);
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(5);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(1);
        FHBox31.setFlexVflex("ftFalse");
        FHBox31.setFlexHflex("ftFalse");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        FHBox30.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFHBox FHBox32 = new TFHBox();

    private void init_FHBox32() {
        FHBox32.setName("FHBox32");
        FHBox32.setLeft(0);
        FHBox32.setTop(0);
        FHBox32.setWidth(9);
        FHBox32.setHeight(31);
        FHBox32.setBorderStyle("stNone");
        FHBox32.setPaddingTop(0);
        FHBox32.setPaddingLeft(0);
        FHBox32.setPaddingRight(0);
        FHBox32.setPaddingBottom(0);
        FHBox32.setMarginTop(0);
        FHBox32.setMarginLeft(0);
        FHBox32.setMarginRight(0);
        FHBox32.setMarginBottom(0);
        FHBox32.setSpacing(1);
        FHBox32.setFlexVflex("ftTrue");
        FHBox32.setFlexHflex("ftTrue");
        FHBox32.setScrollable(false);
        FHBox32.setBoxShadowConfigHorizontalLength(10);
        FHBox32.setBoxShadowConfigVerticalLength(10);
        FHBox32.setBoxShadowConfigBlurRadius(5);
        FHBox32.setBoxShadowConfigSpreadRadius(0);
        FHBox32.setBoxShadowConfigShadowColor("clBlack");
        FHBox32.setBoxShadowConfigOpacity(75);
        FHBox32.setVAlign("tvTop");
        FHBox31.addChildren(FHBox32);
        FHBox32.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(9);
        FLabel4.setTop(0);
        FLabel4.setWidth(102);
        FLabel4.setHeight(13);
        FLabel4.setCaption("Time que vai Atender");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FHBox31.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(118);
        FVBox7.setTop(0);
        FVBox7.setWidth(273);
        FVBox7.setHeight(48);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox30.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFCombo cbbTime = new TFCombo();

    private void init_cbbTime() {
        cbbTime.setName("cbbTime");
        cbbTime.setLeft(0);
        cbbTime.setTop(0);
        cbbTime.setWidth(145);
        cbbTime.setHeight(21);
        cbbTime.setTable(tbLeadzapItem);
        cbbTime.setLookupTable(tbTime);
        cbbTime.setFieldName("ID_TIME");
        cbbTime.setLookupKey("ID_TIME");
        cbbTime.setLookupDesc("DESCRICAO");
        cbbTime.setFlex(true);
        cbbTime.setReadOnly(true);
        cbbTime.setRequired(false);
        cbbTime.setPrompt("Selecione");
        cbbTime.setConstraintCheckWhen("cwImmediate");
        cbbTime.setConstraintCheckType("ctExpression");
        cbbTime.setConstraintFocusOnError(false);
        cbbTime.setConstraintEnableUI(true);
        cbbTime.setConstraintEnabled(false);
        cbbTime.setConstraintFormCheck(true);
        cbbTime.setClearOnDelKey(true);
        cbbTime.setUseClearButton(false);
        cbbTime.setHideClearButtonOnNullValue(false);
        cbbTime.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbTimeChange(event);
            processarFlow("FrmLeadzapReceptivo", "cbbTime", "OnChange");
        });
        FVBox7.addChildren(cbbTime);
        cbbTime.applyProperties();
        addValidatable(cbbTime);
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(22);
        FLabel6.setWidth(299);
        FLabel6.setHeight(13);
        FLabel6.setCaption("(A empresa do vendedor escolhido ser\u00E1 a empresa do evento)");
        FLabel6.setFontColor("clRed");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FVBox7.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(55);
        FLabel5.setWidth(161);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Exce\u00E7\u00F5es(Somente para Ve\u00EDculos)");
        FLabel5.setFontColor("clRed");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FVBox8.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFCheckBox chkUltimoAgenteEvento = new TFCheckBox();

    private void init_chkUltimoAgenteEvento() {
        chkUltimoAgenteEvento.setName("chkUltimoAgenteEvento");
        chkUltimoAgenteEvento.setLeft(0);
        chkUltimoAgenteEvento.setTop(69);
        chkUltimoAgenteEvento.setWidth(398);
        chkUltimoAgenteEvento.setHeight(17);
        chkUltimoAgenteEvento.setCaption("Pega o agente do ultimo evento encerrado, se achar, ignorando a fila");
        chkUltimoAgenteEvento.setFontColor("clWindowText");
        chkUltimoAgenteEvento.setFontSize(-11);
        chkUltimoAgenteEvento.setFontName("Tahoma");
        chkUltimoAgenteEvento.setFontStyle("[]");
        chkUltimoAgenteEvento.setTable(tbLeadzapItem);
        chkUltimoAgenteEvento.setFieldName("PREF_ULT_AGENTE");
        chkUltimoAgenteEvento.setCheckedValue("S");
        chkUltimoAgenteEvento.setUncheckedValue("N");
        chkUltimoAgenteEvento.setVerticalAlignment("taAlignTop");
        FVBox8.addChildren(chkUltimoAgenteEvento);
        chkUltimoAgenteEvento.applyProperties();
    }

    public TFCheckBox chkEventoPerdido = new TFCheckBox();

    private void init_chkEventoPerdido() {
        chkEventoPerdido.setName("chkEventoPerdido");
        chkEventoPerdido.setLeft(0);
        chkEventoPerdido.setTop(87);
        chkEventoPerdido.setWidth(396);
        chkEventoPerdido.setHeight(17);
        chkEventoPerdido.setCaption("Se achar evento perdido, reabre e utiliza. o Agente do evento fica respons\u00E1vel, ignorando a fila");
        chkEventoPerdido.setFontColor("clWindowText");
        chkEventoPerdido.setFontSize(-11);
        chkEventoPerdido.setFontName("Tahoma");
        chkEventoPerdido.setFontStyle("[]");
        chkEventoPerdido.setTable(tbLeadzapItem);
        chkEventoPerdido.setFieldName("EVENTO_PERDIDO_REABRE");
        chkEventoPerdido.setCheckedValue("S");
        chkEventoPerdido.setUncheckedValue("N");
        chkEventoPerdido.setVerticalAlignment("taAlignTop");
        FVBox8.addChildren(chkEventoPerdido);
        chkEventoPerdido.applyProperties();
    }

    public TFCheckBox chkCentralUnica = new TFCheckBox();

    private void init_chkCentralUnica() {
        chkCentralUnica.setName("chkCentralUnica");
        chkCentralUnica.setLeft(0);
        chkCentralUnica.setTop(105);
        chkCentralUnica.setWidth(396);
        chkCentralUnica.setHeight(17);
        chkCentralUnica.setHint("Direciona atendimento para qualqer Vendedor do time, caso contr\u00E1+rio vai direcionar para Vendedor do time mas que seja da mesma Empresa selecionada.");
        chkCentralUnica.setCaption("Central Unica");
        chkCentralUnica.setFontColor("clWindowText");
        chkCentralUnica.setFontSize(-11);
        chkCentralUnica.setFontName("Tahoma");
        chkCentralUnica.setFontStyle("[]");
        chkCentralUnica.setTable(tbLeadzapItem);
        chkCentralUnica.setFieldName("CENTRAL_UNICA");
        chkCentralUnica.setCheckedValue("S");
        chkCentralUnica.setUncheckedValue("N");
        chkCentralUnica.setVerticalAlignment("taAlignTop");
        FVBox8.addChildren(chkCentralUnica);
        chkCentralUnica.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnConsultarClick(final Event<Object> event) {
        if (btnConsultar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void pgChatbotChange(final Event<Object> event);

    public void btnNovoItemClick(final Event<Object> event) {
        if (btnNovoItem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovoItem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarItemClick(final Event<Object> event) {
        if (btnAlterarItem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarItem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirItemClick(final Event<Object> event) {
        if (btnExcluirItem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirItem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarItemClick(final Event<Object> event) {
        if (btnSalvarItem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarItem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarItemClick(final Event<Object> event) {
        if (btnCancelarItem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarItem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbbAreaChange(final Event<Object> event);

    public abstract void cbbIdTemplateJaExisteAtendimentoVendedorChange(final Event<Object> event);

    public abstract void cbbTimeChange(final Event<Object> event);

    public abstract void tbLeadzapItemAfterScroll(final Event<Object> event);

    public abstract void tbLeadzapMenuAfterScroll(final Event<Object> event);

    public abstract void tbLeadzapSetorMembroTimeMaxRow(final Event<Object> event);

}