package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmDadosSintegra extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.DadosSintegraRNA rn = null;

    public FrmDadosSintegra() {
        try {
            rn = (freedom.bytecode.rn.DadosSintegraRNA) getRN(freedom.bytecode.rn.wizard.DadosSintegraRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbDadosCadastraisReceita();
        init_tbDadosCadastraisSintegra();
        init_tbDadosFisicos();
        init_tbClientes();
        init_tbDadosJuridicos();
        init_tbLeadsPendenciaCadIntegracao();
        init_tbSchemaAtual();
        init_tbPessoaFisica();
        init_tbEnderecoPessoaJuridica();
        init_tbEnderecoSintegra();
        init_tbEnderecoClienteDiverso();
        init_tbCidades();
        init_tbClienteEnderecoInscricao();
        init_vBoxDados();
        init_FHBox1();
        init_btnVoltar();
        init_FHBox2();
        init_btnConsultaAPIIntegracao();
        init_edtLog();
        init_FHBox5();
        init_FVBox1();
        init_FHBox6();
        init_FLabel1();
        init_lblCpfCnpj();
        init_FHBox7();
        init_FLabel2();
        init_lblInscricao();
        init_vBoxStatusCadastro();
        init_FHBox10();
        init_lblSituacaoCadastral();
        init_FHBox12();
        init_lblCadastroIrregular();
        init_hBoxSituacaoCadReceitaFederal();
        init_lblSituacaoCadReceitaFederal();
        init_lblSituacaoReceitaFederal();
        init_lblSituacaoCadReceitaFederalFim();
        init_hBoxSituacaoCadSintegra();
        init_lblSituacaoCadSintegra();
        init_lblSituacaoSintegra();
        init_lblSituacaoCadSintegraFim();
        init_lblSintegraMultiIe();
        init_pgcDados();
        init_tbsReceita();
        init_vBoxTabDadosReceita();
        init_grdDadosReceita();
        init_vBoxDadosReceitaDetalhes();
        init_hBoxDadosReceitaDescricao();
        init_lblDadosReceitaDescricao();
        init_vBoxCadastroReceita();
        init_hBoxCadastrosReceita();
        init_lblCadastroReceita();
        init_edtCadastroReceita();
        init_vBoxCadastroReceitaNbs();
        init_hBoxCadastrosReceitaNbs();
        init_lblCadastroReceitaNbs();
        init_edtCadastroReceitaNbs();
        init_FHBox3();
        init_tbsSintegra();
        init_FVBox3();
        init_grdDadosSintegra();
        init_vBoxDadosSintegraDetalhes();
        init_hBoxDadosSintegraDescricao();
        init_lblDadosSintegraDescricao();
        init_vBoxCadastroSintegra();
        init_hBoxCadastradosSintegra();
        init_lblCadastroSintegra();
        init_edtCadastroSintegra();
        init_vBoxCadastroSintegraNbs();
        init_hBoxCadastradosSintegraNbs();
        init_FLabel5();
        init_edtCadastroSintegraNbs();
        init_FHBox4();
        init_FrmDadosSintegra();
    }

    public BUSCA_DADOS_CADASTRAIS tbDadosCadastraisReceita;

    private void init_tbDadosCadastraisReceita() {
        tbDadosCadastraisReceita = rn.tbDadosCadastraisReceita;
        tbDadosCadastraisReceita.setName("tbDadosCadastraisReceita");
        tbDadosCadastraisReceita.setMaxRowCount(200);
        tbDadosCadastraisReceita.setWKey("5300716;53001");
        tbDadosCadastraisReceita.setRatioBatchSize(20);
        getTables().put(tbDadosCadastraisReceita, "tbDadosCadastraisReceita");
        tbDadosCadastraisReceita.applyProperties();
    }

    public BUSCA_DADOS_CADASTRAIS tbDadosCadastraisSintegra;

    private void init_tbDadosCadastraisSintegra() {
        tbDadosCadastraisSintegra = rn.tbDadosCadastraisSintegra;
        tbDadosCadastraisSintegra.setName("tbDadosCadastraisSintegra");
        tbDadosCadastraisSintegra.setMaxRowCount(200);
        tbDadosCadastraisSintegra.setWKey("5300716;53002");
        tbDadosCadastraisSintegra.setRatioBatchSize(20);
        getTables().put(tbDadosCadastraisSintegra, "tbDadosCadastraisSintegra");
        tbDadosCadastraisSintegra.applyProperties();
    }

    public DADOS_FISICOS tbDadosFisicos;

    private void init_tbDadosFisicos() {
        tbDadosFisicos = rn.tbDadosFisicos;
        tbDadosFisicos.setName("tbDadosFisicos");
        tbDadosFisicos.setMaxRowCount(200);
        tbDadosFisicos.setWKey("5300716;53003");
        tbDadosFisicos.setRatioBatchSize(20);
        getTables().put(tbDadosFisicos, "tbDadosFisicos");
        tbDadosFisicos.applyProperties();
    }

    public CLIENTES tbClientes;

    private void init_tbClientes() {
        tbClientes = rn.tbClientes;
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("5300716;53004");
        tbClientes.setRatioBatchSize(20);
        getTables().put(tbClientes, "tbClientes");
        tbClientes.applyProperties();
    }

    public DADOS_JURIDICOS tbDadosJuridicos;

    private void init_tbDadosJuridicos() {
        tbDadosJuridicos = rn.tbDadosJuridicos;
        tbDadosJuridicos.setName("tbDadosJuridicos");
        tbDadosJuridicos.setMaxRowCount(200);
        tbDadosJuridicos.setWKey("5300716;53005");
        tbDadosJuridicos.setRatioBatchSize(20);
        getTables().put(tbDadosJuridicos, "tbDadosJuridicos");
        tbDadosJuridicos.applyProperties();
    }

    public LEADS_PENDENCIA_CAD_INTEGRACAO tbLeadsPendenciaCadIntegracao;

    private void init_tbLeadsPendenciaCadIntegracao() {
        tbLeadsPendenciaCadIntegracao = rn.tbLeadsPendenciaCadIntegracao;
        tbLeadsPendenciaCadIntegracao.setName("tbLeadsPendenciaCadIntegracao");
        tbLeadsPendenciaCadIntegracao.setMaxRowCount(200);
        tbLeadsPendenciaCadIntegracao.setWKey("5300716;53006");
        tbLeadsPendenciaCadIntegracao.setRatioBatchSize(20);
        getTables().put(tbLeadsPendenciaCadIntegracao, "tbLeadsPendenciaCadIntegracao");
        tbLeadsPendenciaCadIntegracao.applyProperties();
    }

    public SCHEMA_ATUAL tbSchemaAtual;

    private void init_tbSchemaAtual() {
        tbSchemaAtual = rn.tbSchemaAtual;
        tbSchemaAtual.setName("tbSchemaAtual");
        tbSchemaAtual.setMaxRowCount(200);
        tbSchemaAtual.setWKey("5300716;53007");
        tbSchemaAtual.setRatioBatchSize(20);
        getTables().put(tbSchemaAtual, "tbSchemaAtual");
        tbSchemaAtual.applyProperties();
    }

    public CONSULTA_NBS_PESSOA_FISICA tbPessoaFisica;

    private void init_tbPessoaFisica() {
        tbPessoaFisica = rn.tbPessoaFisica;
        tbPessoaFisica.setName("tbPessoaFisica");
        tbPessoaFisica.setMaxRowCount(200);
        tbPessoaFisica.setWKey("5300716;53008");
        tbPessoaFisica.setRatioBatchSize(20);
        getTables().put(tbPessoaFisica, "tbPessoaFisica");
        tbPessoaFisica.applyProperties();
    }

    public CONSULTA_NBS_PESSOA_JURIDICA tbEnderecoPessoaJuridica;

    private void init_tbEnderecoPessoaJuridica() {
        tbEnderecoPessoaJuridica = rn.tbEnderecoPessoaJuridica;
        tbEnderecoPessoaJuridica.setName("tbEnderecoPessoaJuridica");
        tbEnderecoPessoaJuridica.setMaxRowCount(200);
        tbEnderecoPessoaJuridica.setWKey("5300716;53009");
        tbEnderecoPessoaJuridica.setRatioBatchSize(20);
        getTables().put(tbEnderecoPessoaJuridica, "tbEnderecoPessoaJuridica");
        tbEnderecoPessoaJuridica.applyProperties();
    }

    public CONSULTA_NBS_SINTEGRA_DADOS tbEnderecoSintegra;

    private void init_tbEnderecoSintegra() {
        tbEnderecoSintegra = rn.tbEnderecoSintegra;
        tbEnderecoSintegra.setName("tbEnderecoSintegra");
        tbEnderecoSintegra.setMaxRowCount(200);
        tbEnderecoSintegra.setWKey("5300716;530010");
        tbEnderecoSintegra.setRatioBatchSize(20);
        getTables().put(tbEnderecoSintegra, "tbEnderecoSintegra");
        tbEnderecoSintegra.applyProperties();
    }

    public CLIENTE_DIVERSO tbEnderecoClienteDiverso;

    private void init_tbEnderecoClienteDiverso() {
        tbEnderecoClienteDiverso = rn.tbEnderecoClienteDiverso;
        tbEnderecoClienteDiverso.setName("tbEnderecoClienteDiverso");
        tbEnderecoClienteDiverso.setMaxRowCount(200);
        tbEnderecoClienteDiverso.setWKey("5300716;530011");
        tbEnderecoClienteDiverso.setRatioBatchSize(20);
        getTables().put(tbEnderecoClienteDiverso, "tbEnderecoClienteDiverso");
        tbEnderecoClienteDiverso.applyProperties();
    }

    public CIDADES tbCidades;

    private void init_tbCidades() {
        tbCidades = rn.tbCidades;
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("5300716;530012");
        tbCidades.setRatioBatchSize(20);
        getTables().put(tbCidades, "tbCidades");
        tbCidades.applyProperties();
    }

    public CLIENTE_ENDERECO_INSCRICAO tbClienteEnderecoInscricao;

    private void init_tbClienteEnderecoInscricao() {
        tbClienteEnderecoInscricao = rn.tbClienteEnderecoInscricao;
        tbClienteEnderecoInscricao.setName("tbClienteEnderecoInscricao");
        tbClienteEnderecoInscricao.setMaxRowCount(200);
        tbClienteEnderecoInscricao.setWKey("5300716;530013");
        tbClienteEnderecoInscricao.setRatioBatchSize(20);
        getTables().put(tbClienteEnderecoInscricao, "tbClienteEnderecoInscricao");
        tbClienteEnderecoInscricao.applyProperties();
    }

    protected TFForm FrmDadosSintegra = this;
    private void init_FrmDadosSintegra() {
        FrmDadosSintegra.setName("FrmDadosSintegra");
        FrmDadosSintegra.setCaption("Dados Retornados");
        FrmDadosSintegra.setClientHeight(497);
        FrmDadosSintegra.setClientWidth(786);
        FrmDadosSintegra.setColor("clBtnFace");
        FrmDadosSintegra.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmDadosSintegra", "FrmDadosSintegra", "OnCreate");
        });
        FrmDadosSintegra.setWOrigem("EhMain");
        FrmDadosSintegra.setWKey("5300716");
        FrmDadosSintegra.setSpacing(0);
        FrmDadosSintegra.applyProperties();
    }

    public TFVBox vBoxDados = new TFVBox();

    private void init_vBoxDados() {
        vBoxDados.setName("vBoxDados");
        vBoxDados.setLeft(0);
        vBoxDados.setTop(0);
        vBoxDados.setWidth(786);
        vBoxDados.setHeight(497);
        vBoxDados.setAlign("alClient");
        vBoxDados.setBorderStyle("stNone");
        vBoxDados.setPaddingTop(0);
        vBoxDados.setPaddingLeft(0);
        vBoxDados.setPaddingRight(0);
        vBoxDados.setPaddingBottom(0);
        vBoxDados.setMarginTop(0);
        vBoxDados.setMarginLeft(0);
        vBoxDados.setMarginRight(0);
        vBoxDados.setMarginBottom(0);
        vBoxDados.setSpacing(1);
        vBoxDados.setFlexVflex("ftTrue");
        vBoxDados.setFlexHflex("ftTrue");
        vBoxDados.setScrollable(false);
        vBoxDados.setBoxShadowConfigHorizontalLength(10);
        vBoxDados.setBoxShadowConfigVerticalLength(10);
        vBoxDados.setBoxShadowConfigBlurRadius(5);
        vBoxDados.setBoxShadowConfigSpreadRadius(0);
        vBoxDados.setBoxShadowConfigShadowColor("clBlack");
        vBoxDados.setBoxShadowConfigOpacity(75);
        FrmDadosSintegra.addChildren(vBoxDados);
        vBoxDados.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(755);
        FHBox1.setHeight(63);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(5);
        FHBox1.setPaddingLeft(8);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxDados.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(68);
        btnVoltar.setHeight(51);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmDadosSintegra", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox1.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(68);
        FHBox2.setTop(0);
        FHBox2.setWidth(9);
        FHBox2.setHeight(51);
        FHBox2.setAlign("alLeft");
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FHBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnConsultaAPIIntegracao = new TFButton();

    private void init_btnConsultaAPIIntegracao() {
        btnConsultaAPIIntegracao.setName("btnConsultaAPIIntegracao");
        btnConsultaAPIIntegracao.setLeft(77);
        btnConsultaAPIIntegracao.setTop(0);
        btnConsultaAPIIntegracao.setWidth(82);
        btnConsultaAPIIntegracao.setHeight(51);
        btnConsultaAPIIntegracao.setHint("Consultar integra\u00E7\u00E3o dados Receita e Sintegra. Acesso: K0618");
        btnConsultaAPIIntegracao.setAlign("alLeft");
        btnConsultaAPIIntegracao.setCaption("Consultar API");
        btnConsultaAPIIntegracao.setFontColor("clWindowText");
        btnConsultaAPIIntegracao.setFontSize(-11);
        btnConsultaAPIIntegracao.setFontName("Tahoma");
        btnConsultaAPIIntegracao.setFontStyle("[]");
        btnConsultaAPIIntegracao.setLayout("blGlyphTop");
        btnConsultaAPIIntegracao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConsultaAPIIntegracaoClick(event);
            processarFlow("FrmDadosSintegra", "btnConsultaAPIIntegracao", "OnClick");
        });
        btnConsultaAPIIntegracao.setImageId(7000112);
        btnConsultaAPIIntegracao.setColor("clBtnFace");
        btnConsultaAPIIntegracao.setAccess(false);
        btnConsultaAPIIntegracao.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultaAPIIntegracao);
        btnConsultaAPIIntegracao.applyProperties();
    }

    public TFString edtLog = new TFString();

    private void init_edtLog() {
        edtLog.setName("edtLog");
        edtLog.setLeft(159);
        edtLog.setTop(0);
        edtLog.setWidth(49);
        edtLog.setHeight(24);
        edtLog.setFlex(false);
        edtLog.setRequired(false);
        edtLog.setConstraintCheckWhen("cwImmediate");
        edtLog.setConstraintCheckType("ctExpression");
        edtLog.setConstraintFocusOnError(false);
        edtLog.setConstraintEnableUI(true);
        edtLog.setConstraintEnabled(false);
        edtLog.setConstraintFormCheck(true);
        edtLog.setCharCase("ccNormal");
        edtLog.setPwd(false);
        edtLog.setMaxlength(0);
        edtLog.setVisible(false);
        edtLog.setFontColor("clWindowText");
        edtLog.setFontSize(-13);
        edtLog.setFontName("Tahoma");
        edtLog.setFontStyle("[]");
        edtLog.setSaveLiteralCharacter(false);
        edtLog.applyProperties();
        FHBox1.addChildren(edtLog);
        addValidatable(edtLog);
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(208);
        FHBox5.setTop(0);
        FHBox5.setWidth(115);
        FHBox5.setHeight(54);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox1.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(323);
        FVBox1.setTop(0);
        FVBox1.setWidth(176);
        FVBox1.setHeight(52);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(168);
        FHBox6.setHeight(21);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FVBox1.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(61);
        FLabel1.setHeight(13);
        FLabel1.setCaption("CPF/CNPJ:   ");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FHBox6.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFLabel lblCpfCnpj = new TFLabel();

    private void init_lblCpfCnpj() {
        lblCpfCnpj.setName("lblCpfCnpj");
        lblCpfCnpj.setLeft(61);
        lblCpfCnpj.setTop(0);
        lblCpfCnpj.setWidth(45);
        lblCpfCnpj.setHeight(13);
        lblCpfCnpj.setCaption("000.000");
        lblCpfCnpj.setFontColor("clWindowText");
        lblCpfCnpj.setFontSize(-11);
        lblCpfCnpj.setFontName("Tahoma");
        lblCpfCnpj.setFontStyle("[fsBold]");
        lblCpfCnpj.setVerticalAlignment("taVerticalCenter");
        lblCpfCnpj.setWordBreak(false);
        FHBox6.addChildren(lblCpfCnpj);
        lblCpfCnpj.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(22);
        FHBox7.setWidth(169);
        FHBox7.setHeight(21);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox1.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(53);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Inscri\u00E7\u00E3o:  ");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FHBox7.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFLabel lblInscricao = new TFLabel();

    private void init_lblInscricao() {
        lblInscricao.setName("lblInscricao");
        lblInscricao.setLeft(53);
        lblInscricao.setTop(0);
        lblInscricao.setWidth(45);
        lblInscricao.setHeight(13);
        lblInscricao.setCaption("000.000");
        lblInscricao.setFontColor("clWindowText");
        lblInscricao.setFontSize(-11);
        lblInscricao.setFontName("Tahoma");
        lblInscricao.setFontStyle("[fsBold]");
        lblInscricao.setVerticalAlignment("taVerticalCenter");
        lblInscricao.setWordBreak(false);
        FHBox7.addChildren(lblInscricao);
        lblInscricao.applyProperties();
    }

    public TFVBox vBoxStatusCadastro = new TFVBox();

    private void init_vBoxStatusCadastro() {
        vBoxStatusCadastro.setName("vBoxStatusCadastro");
        vBoxStatusCadastro.setLeft(499);
        vBoxStatusCadastro.setTop(0);
        vBoxStatusCadastro.setWidth(207);
        vBoxStatusCadastro.setHeight(62);
        vBoxStatusCadastro.setBorderStyle("stNone");
        vBoxStatusCadastro.setPaddingTop(0);
        vBoxStatusCadastro.setPaddingLeft(0);
        vBoxStatusCadastro.setPaddingRight(0);
        vBoxStatusCadastro.setPaddingBottom(0);
        vBoxStatusCadastro.setVisible(false);
        vBoxStatusCadastro.setMarginTop(0);
        vBoxStatusCadastro.setMarginLeft(0);
        vBoxStatusCadastro.setMarginRight(0);
        vBoxStatusCadastro.setMarginBottom(0);
        vBoxStatusCadastro.setSpacing(1);
        vBoxStatusCadastro.setFlexVflex("ftFalse");
        vBoxStatusCadastro.setFlexHflex("ftFalse");
        vBoxStatusCadastro.setScrollable(false);
        vBoxStatusCadastro.setBoxShadowConfigHorizontalLength(10);
        vBoxStatusCadastro.setBoxShadowConfigVerticalLength(10);
        vBoxStatusCadastro.setBoxShadowConfigBlurRadius(5);
        vBoxStatusCadastro.setBoxShadowConfigSpreadRadius(0);
        vBoxStatusCadastro.setBoxShadowConfigShadowColor("clBlack");
        vBoxStatusCadastro.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(vBoxStatusCadastro);
        vBoxStatusCadastro.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(203);
        FHBox10.setHeight(20);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFLabel lblSituacaoCadastral = new TFLabel();

    private void init_lblSituacaoCadastral() {
        lblSituacaoCadastral.setName("lblSituacaoCadastral");
        lblSituacaoCadastral.setLeft(0);
        lblSituacaoCadastral.setTop(0);
        lblSituacaoCadastral.setWidth(90);
        lblSituacaoCadastral.setHeight(13);
        lblSituacaoCadastral.setCaption("Situa\u00E7\u00E3o Cadastral");
        lblSituacaoCadastral.setFontColor("clWindowText");
        lblSituacaoCadastral.setFontSize(-11);
        lblSituacaoCadastral.setFontName("Tahoma");
        lblSituacaoCadastral.setFontStyle("[]");
        lblSituacaoCadastral.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadastral.setWordBreak(false);
        FHBox10.addChildren(lblSituacaoCadastral);
        lblSituacaoCadastral.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(90);
        FHBox12.setTop(0);
        FHBox12.setWidth(5);
        FHBox12.setHeight(15);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FHBox10.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFLabel lblCadastroIrregular = new TFLabel();

    private void init_lblCadastroIrregular() {
        lblCadastroIrregular.setName("lblCadastroIrregular");
        lblCadastroIrregular.setLeft(95);
        lblCadastroIrregular.setTop(0);
        lblCadastroIrregular.setWidth(71);
        lblCadastroIrregular.setHeight(14);
        lblCadastroIrregular.setCaption("IRREGULAR");
        lblCadastroIrregular.setFontColor("clRed");
        lblCadastroIrregular.setFontSize(-12);
        lblCadastroIrregular.setFontName("Tahoma");
        lblCadastroIrregular.setFontStyle("[fsBold]");
        lblCadastroIrregular.setVisible(false);
        lblCadastroIrregular.setVerticalAlignment("taVerticalCenter");
        lblCadastroIrregular.setWordBreak(false);
        FHBox10.addChildren(lblCadastroIrregular);
        lblCadastroIrregular.applyProperties();
    }

    public TFHBox hBoxSituacaoCadReceitaFederal = new TFHBox();

    private void init_hBoxSituacaoCadReceitaFederal() {
        hBoxSituacaoCadReceitaFederal.setName("hBoxSituacaoCadReceitaFederal");
        hBoxSituacaoCadReceitaFederal.setLeft(0);
        hBoxSituacaoCadReceitaFederal.setTop(21);
        hBoxSituacaoCadReceitaFederal.setWidth(203);
        hBoxSituacaoCadReceitaFederal.setHeight(18);
        hBoxSituacaoCadReceitaFederal.setBorderStyle("stNone");
        hBoxSituacaoCadReceitaFederal.setPaddingTop(-1);
        hBoxSituacaoCadReceitaFederal.setPaddingLeft(0);
        hBoxSituacaoCadReceitaFederal.setPaddingRight(0);
        hBoxSituacaoCadReceitaFederal.setPaddingBottom(0);
        hBoxSituacaoCadReceitaFederal.setVisible(false);
        hBoxSituacaoCadReceitaFederal.setMarginTop(0);
        hBoxSituacaoCadReceitaFederal.setMarginLeft(0);
        hBoxSituacaoCadReceitaFederal.setMarginRight(0);
        hBoxSituacaoCadReceitaFederal.setMarginBottom(0);
        hBoxSituacaoCadReceitaFederal.setSpacing(1);
        hBoxSituacaoCadReceitaFederal.setFlexVflex("ftFalse");
        hBoxSituacaoCadReceitaFederal.setFlexHflex("ftFalse");
        hBoxSituacaoCadReceitaFederal.setScrollable(false);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadReceitaFederal.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadReceitaFederal.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadReceitaFederal);
        hBoxSituacaoCadReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoCadReceitaFederal = new TFLabel();

    private void init_lblSituacaoCadReceitaFederal() {
        lblSituacaoCadReceitaFederal.setName("lblSituacaoCadReceitaFederal");
        lblSituacaoCadReceitaFederal.setLeft(0);
        lblSituacaoCadReceitaFederal.setTop(0);
        lblSituacaoCadReceitaFederal.setWidth(82);
        lblSituacaoCadReceitaFederal.setHeight(13);
        lblSituacaoCadReceitaFederal.setCaption("Receita Federal (");
        lblSituacaoCadReceitaFederal.setFontColor("clWindowText");
        lblSituacaoCadReceitaFederal.setFontSize(-11);
        lblSituacaoCadReceitaFederal.setFontName("Tahoma");
        lblSituacaoCadReceitaFederal.setFontStyle("[]");
        lblSituacaoCadReceitaFederal.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadReceitaFederal.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoCadReceitaFederal);
        lblSituacaoCadReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoReceitaFederal = new TFLabel();

    private void init_lblSituacaoReceitaFederal() {
        lblSituacaoReceitaFederal.setName("lblSituacaoReceitaFederal");
        lblSituacaoReceitaFederal.setLeft(82);
        lblSituacaoReceitaFederal.setTop(0);
        lblSituacaoReceitaFederal.setWidth(35);
        lblSituacaoReceitaFederal.setHeight(13);
        lblSituacaoReceitaFederal.setCaption("ATIVO");
        lblSituacaoReceitaFederal.setFontColor("clWindowText");
        lblSituacaoReceitaFederal.setFontSize(-11);
        lblSituacaoReceitaFederal.setFontName("Tahoma");
        lblSituacaoReceitaFederal.setFontStyle("[fsBold]");
        lblSituacaoReceitaFederal.setVerticalAlignment("taVerticalCenter");
        lblSituacaoReceitaFederal.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoReceitaFederal);
        lblSituacaoReceitaFederal.applyProperties();
    }

    public TFLabel lblSituacaoCadReceitaFederalFim = new TFLabel();

    private void init_lblSituacaoCadReceitaFederalFim() {
        lblSituacaoCadReceitaFederalFim.setName("lblSituacaoCadReceitaFederalFim");
        lblSituacaoCadReceitaFederalFim.setLeft(117);
        lblSituacaoCadReceitaFederalFim.setTop(0);
        lblSituacaoCadReceitaFederalFim.setWidth(4);
        lblSituacaoCadReceitaFederalFim.setHeight(13);
        lblSituacaoCadReceitaFederalFim.setCaption(")");
        lblSituacaoCadReceitaFederalFim.setFontColor("clWindowText");
        lblSituacaoCadReceitaFederalFim.setFontSize(-11);
        lblSituacaoCadReceitaFederalFim.setFontName("Tahoma");
        lblSituacaoCadReceitaFederalFim.setFontStyle("[]");
        lblSituacaoCadReceitaFederalFim.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadReceitaFederalFim.setWordBreak(false);
        hBoxSituacaoCadReceitaFederal.addChildren(lblSituacaoCadReceitaFederalFim);
        lblSituacaoCadReceitaFederalFim.applyProperties();
    }

    public TFHBox hBoxSituacaoCadSintegra = new TFHBox();

    private void init_hBoxSituacaoCadSintegra() {
        hBoxSituacaoCadSintegra.setName("hBoxSituacaoCadSintegra");
        hBoxSituacaoCadSintegra.setLeft(0);
        hBoxSituacaoCadSintegra.setTop(40);
        hBoxSituacaoCadSintegra.setWidth(201);
        hBoxSituacaoCadSintegra.setHeight(18);
        hBoxSituacaoCadSintegra.setBorderStyle("stNone");
        hBoxSituacaoCadSintegra.setPaddingTop(-1);
        hBoxSituacaoCadSintegra.setPaddingLeft(0);
        hBoxSituacaoCadSintegra.setPaddingRight(0);
        hBoxSituacaoCadSintegra.setPaddingBottom(0);
        hBoxSituacaoCadSintegra.setVisible(false);
        hBoxSituacaoCadSintegra.setMarginTop(0);
        hBoxSituacaoCadSintegra.setMarginLeft(0);
        hBoxSituacaoCadSintegra.setMarginRight(0);
        hBoxSituacaoCadSintegra.setMarginBottom(0);
        hBoxSituacaoCadSintegra.setSpacing(1);
        hBoxSituacaoCadSintegra.setFlexVflex("ftFalse");
        hBoxSituacaoCadSintegra.setFlexHflex("ftFalse");
        hBoxSituacaoCadSintegra.setScrollable(false);
        hBoxSituacaoCadSintegra.setBoxShadowConfigHorizontalLength(10);
        hBoxSituacaoCadSintegra.setBoxShadowConfigVerticalLength(10);
        hBoxSituacaoCadSintegra.setBoxShadowConfigBlurRadius(5);
        hBoxSituacaoCadSintegra.setBoxShadowConfigSpreadRadius(0);
        hBoxSituacaoCadSintegra.setBoxShadowConfigShadowColor("clBlack");
        hBoxSituacaoCadSintegra.setBoxShadowConfigOpacity(75);
        hBoxSituacaoCadSintegra.setVAlign("tvTop");
        vBoxStatusCadastro.addChildren(hBoxSituacaoCadSintegra);
        hBoxSituacaoCadSintegra.applyProperties();
    }

    public TFLabel lblSituacaoCadSintegra = new TFLabel();

    private void init_lblSituacaoCadSintegra() {
        lblSituacaoCadSintegra.setName("lblSituacaoCadSintegra");
        lblSituacaoCadSintegra.setLeft(0);
        lblSituacaoCadSintegra.setTop(0);
        lblSituacaoCadSintegra.setWidth(47);
        lblSituacaoCadSintegra.setHeight(13);
        lblSituacaoCadSintegra.setCaption("Sintegra (");
        lblSituacaoCadSintegra.setFontColor("clWindowText");
        lblSituacaoCadSintegra.setFontSize(-11);
        lblSituacaoCadSintegra.setFontName("Tahoma");
        lblSituacaoCadSintegra.setFontStyle("[]");
        lblSituacaoCadSintegra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSituacaoCadSintegraClick(event);
            processarFlow("FrmDadosSintegra", "lblSituacaoCadSintegra", "OnClick");
        });
        lblSituacaoCadSintegra.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadSintegra.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoCadSintegra);
        lblSituacaoCadSintegra.applyProperties();
    }

    public TFLabel lblSituacaoSintegra = new TFLabel();

    private void init_lblSituacaoSintegra() {
        lblSituacaoSintegra.setName("lblSituacaoSintegra");
        lblSituacaoSintegra.setLeft(47);
        lblSituacaoSintegra.setTop(0);
        lblSituacaoSintegra.setWidth(57);
        lblSituacaoSintegra.setHeight(13);
        lblSituacaoSintegra.setCaption("Habilitada");
        lblSituacaoSintegra.setFontColor("clWindowText");
        lblSituacaoSintegra.setFontSize(-11);
        lblSituacaoSintegra.setFontName("Tahoma");
        lblSituacaoSintegra.setFontStyle("[fsBold]");
        lblSituacaoSintegra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSituacaoSintegraClick(event);
            processarFlow("FrmDadosSintegra", "lblSituacaoSintegra", "OnClick");
        });
        lblSituacaoSintegra.setVerticalAlignment("taVerticalCenter");
        lblSituacaoSintegra.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoSintegra);
        lblSituacaoSintegra.applyProperties();
    }

    public TFLabel lblSituacaoCadSintegraFim = new TFLabel();

    private void init_lblSituacaoCadSintegraFim() {
        lblSituacaoCadSintegraFim.setName("lblSituacaoCadSintegraFim");
        lblSituacaoCadSintegraFim.setLeft(104);
        lblSituacaoCadSintegraFim.setTop(0);
        lblSituacaoCadSintegraFim.setWidth(4);
        lblSituacaoCadSintegraFim.setHeight(13);
        lblSituacaoCadSintegraFim.setCaption(")");
        lblSituacaoCadSintegraFim.setFontColor("clWindowText");
        lblSituacaoCadSintegraFim.setFontSize(-11);
        lblSituacaoCadSintegraFim.setFontName("Tahoma");
        lblSituacaoCadSintegraFim.setFontStyle("[]");
        lblSituacaoCadSintegraFim.setVerticalAlignment("taVerticalCenter");
        lblSituacaoCadSintegraFim.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSituacaoCadSintegraFim);
        lblSituacaoCadSintegraFim.applyProperties();
    }

    public TFLabel lblSintegraMultiIe = new TFLabel();

    private void init_lblSintegraMultiIe() {
        lblSintegraMultiIe.setName("lblSintegraMultiIe");
        lblSintegraMultiIe.setLeft(108);
        lblSintegraMultiIe.setTop(0);
        lblSintegraMultiIe.setWidth(37);
        lblSintegraMultiIe.setHeight(13);
        lblSintegraMultiIe.setCaption(" [+... ] ");
        lblSintegraMultiIe.setFontColor("clWindowText");
        lblSintegraMultiIe.setFontSize(-11);
        lblSintegraMultiIe.setFontName("Tahoma");
        lblSintegraMultiIe.setFontStyle("[fsBold]");
        lblSintegraMultiIe.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSintegraMultiIeClick(event);
            processarFlow("FrmDadosSintegra", "lblSintegraMultiIe", "OnClick");
        });
        lblSintegraMultiIe.setVerticalAlignment("taVerticalCenter");
        lblSintegraMultiIe.setWordBreak(false);
        hBoxSituacaoCadSintegra.addChildren(lblSintegraMultiIe);
        lblSintegraMultiIe.applyProperties();
    }

    public TFPageControl pgcDados = new TFPageControl();

    private void init_pgcDados() {
        pgcDados.setName("pgcDados");
        pgcDados.setLeft(0);
        pgcDados.setTop(64);
        pgcDados.setWidth(759);
        pgcDados.setHeight(423);
        pgcDados.setAlign("alClient");
        pgcDados.setTabPosition("tpTop");
        pgcDados.setFlexVflex("ftTrue");
        pgcDados.setFlexHflex("ftTrue");
        pgcDados.setRenderStyle("rsTabbed");
        pgcDados.applyProperties();
        vBoxDados.addChildren(pgcDados);
    }

    public TFTabsheet tbsReceita = new TFTabsheet();

    private void init_tbsReceita() {
        tbsReceita.setName("tbsReceita");
        tbsReceita.setCaption("Receita");
        tbsReceita.setVisible(true);
        tbsReceita.setClosable(false);
        pgcDados.addChildren(tbsReceita);
        tbsReceita.applyProperties();
    }

    public TFVBox vBoxTabDadosReceita = new TFVBox();

    private void init_vBoxTabDadosReceita() {
        vBoxTabDadosReceita.setName("vBoxTabDadosReceita");
        vBoxTabDadosReceita.setLeft(2);
        vBoxTabDadosReceita.setTop(6);
        vBoxTabDadosReceita.setWidth(749);
        vBoxTabDadosReceita.setHeight(340);
        vBoxTabDadosReceita.setBorderStyle("stNone");
        vBoxTabDadosReceita.setPaddingTop(0);
        vBoxTabDadosReceita.setPaddingLeft(0);
        vBoxTabDadosReceita.setPaddingRight(0);
        vBoxTabDadosReceita.setPaddingBottom(0);
        vBoxTabDadosReceita.setMarginTop(0);
        vBoxTabDadosReceita.setMarginLeft(0);
        vBoxTabDadosReceita.setMarginRight(0);
        vBoxTabDadosReceita.setMarginBottom(0);
        vBoxTabDadosReceita.setSpacing(1);
        vBoxTabDadosReceita.setFlexVflex("ftTrue");
        vBoxTabDadosReceita.setFlexHflex("ftTrue");
        vBoxTabDadosReceita.setScrollable(false);
        vBoxTabDadosReceita.setBoxShadowConfigHorizontalLength(10);
        vBoxTabDadosReceita.setBoxShadowConfigVerticalLength(10);
        vBoxTabDadosReceita.setBoxShadowConfigBlurRadius(5);
        vBoxTabDadosReceita.setBoxShadowConfigSpreadRadius(0);
        vBoxTabDadosReceita.setBoxShadowConfigShadowColor("clBlack");
        vBoxTabDadosReceita.setBoxShadowConfigOpacity(75);
        tbsReceita.addChildren(vBoxTabDadosReceita);
        vBoxTabDadosReceita.applyProperties();
    }

    public TFGrid grdDadosReceita = new TFGrid();

    private void init_grdDadosReceita() {
        grdDadosReceita.setName("grdDadosReceita");
        grdDadosReceita.setLeft(0);
        grdDadosReceita.setTop(0);
        grdDadosReceita.setWidth(741);
        grdDadosReceita.setHeight(120);
        grdDadosReceita.setTable(tbDadosCadastraisReceita);
        grdDadosReceita.setFlexVflex("ftTrue");
        grdDadosReceita.setFlexHflex("ftTrue");
        grdDadosReceita.setPagingEnabled(false);
        grdDadosReceita.setFrozenColumns(0);
        grdDadosReceita.setShowFooter(false);
        grdDadosReceita.setShowHeader(true);
        grdDadosReceita.setMultiSelection(false);
        grdDadosReceita.setGroupingEnabled(false);
        grdDadosReceita.setGroupingExpanded(false);
        grdDadosReceita.setGroupingShowFooter(false);
        grdDadosReceita.setCrosstabEnabled(false);
        grdDadosReceita.setCrosstabGroupType("cgtConcat");
        grdDadosReceita.setEditionEnabled(false);
        grdDadosReceita.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(200);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdDadosReceita.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("CAD_PUBLICO");
        item1.setTitleCaption("Dados Retornados");
        item1.setWidth(200);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdDadosReceita.getColumns().add(item1);
        vBoxTabDadosReceita.addChildren(grdDadosReceita);
        grdDadosReceita.applyProperties();
    }

    public TFVBox vBoxDadosReceitaDetalhes = new TFVBox();

    private void init_vBoxDadosReceitaDetalhes() {
        vBoxDadosReceitaDetalhes.setName("vBoxDadosReceitaDetalhes");
        vBoxDadosReceitaDetalhes.setLeft(0);
        vBoxDadosReceitaDetalhes.setTop(121);
        vBoxDadosReceitaDetalhes.setWidth(751);
        vBoxDadosReceitaDetalhes.setHeight(126);
        vBoxDadosReceitaDetalhes.setBorderStyle("stNone");
        vBoxDadosReceitaDetalhes.setPaddingTop(0);
        vBoxDadosReceitaDetalhes.setPaddingLeft(8);
        vBoxDadosReceitaDetalhes.setPaddingRight(8);
        vBoxDadosReceitaDetalhes.setPaddingBottom(0);
        vBoxDadosReceitaDetalhes.setMarginTop(0);
        vBoxDadosReceitaDetalhes.setMarginLeft(0);
        vBoxDadosReceitaDetalhes.setMarginRight(0);
        vBoxDadosReceitaDetalhes.setMarginBottom(0);
        vBoxDadosReceitaDetalhes.setSpacing(1);
        vBoxDadosReceitaDetalhes.setFlexVflex("ftFalse");
        vBoxDadosReceitaDetalhes.setFlexHflex("ftTrue");
        vBoxDadosReceitaDetalhes.setScrollable(false);
        vBoxDadosReceitaDetalhes.setBoxShadowConfigHorizontalLength(10);
        vBoxDadosReceitaDetalhes.setBoxShadowConfigVerticalLength(10);
        vBoxDadosReceitaDetalhes.setBoxShadowConfigBlurRadius(5);
        vBoxDadosReceitaDetalhes.setBoxShadowConfigSpreadRadius(0);
        vBoxDadosReceitaDetalhes.setBoxShadowConfigShadowColor("clBlack");
        vBoxDadosReceitaDetalhes.setBoxShadowConfigOpacity(75);
        vBoxTabDadosReceita.addChildren(vBoxDadosReceitaDetalhes);
        vBoxDadosReceitaDetalhes.applyProperties();
    }

    public TFHBox hBoxDadosReceitaDescricao = new TFHBox();

    private void init_hBoxDadosReceitaDescricao() {
        hBoxDadosReceitaDescricao.setName("hBoxDadosReceitaDescricao");
        hBoxDadosReceitaDescricao.setLeft(0);
        hBoxDadosReceitaDescricao.setTop(0);
        hBoxDadosReceitaDescricao.setWidth(633);
        hBoxDadosReceitaDescricao.setHeight(20);
        hBoxDadosReceitaDescricao.setBorderStyle("stNone");
        hBoxDadosReceitaDescricao.setPaddingTop(0);
        hBoxDadosReceitaDescricao.setPaddingLeft(0);
        hBoxDadosReceitaDescricao.setPaddingRight(0);
        hBoxDadosReceitaDescricao.setPaddingBottom(0);
        hBoxDadosReceitaDescricao.setMarginTop(0);
        hBoxDadosReceitaDescricao.setMarginLeft(0);
        hBoxDadosReceitaDescricao.setMarginRight(0);
        hBoxDadosReceitaDescricao.setMarginBottom(0);
        hBoxDadosReceitaDescricao.setSpacing(1);
        hBoxDadosReceitaDescricao.setFlexVflex("ftFalse");
        hBoxDadosReceitaDescricao.setFlexHflex("ftFalse");
        hBoxDadosReceitaDescricao.setScrollable(false);
        hBoxDadosReceitaDescricao.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosReceitaDescricao.setBoxShadowConfigVerticalLength(10);
        hBoxDadosReceitaDescricao.setBoxShadowConfigBlurRadius(5);
        hBoxDadosReceitaDescricao.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosReceitaDescricao.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosReceitaDescricao.setBoxShadowConfigOpacity(75);
        hBoxDadosReceitaDescricao.setVAlign("tvTop");
        vBoxDadosReceitaDetalhes.addChildren(hBoxDadosReceitaDescricao);
        hBoxDadosReceitaDescricao.applyProperties();
    }

    public TFLabel lblDadosReceitaDescricao = new TFLabel();

    private void init_lblDadosReceitaDescricao() {
        lblDadosReceitaDescricao.setName("lblDadosReceitaDescricao");
        lblDadosReceitaDescricao.setLeft(0);
        lblDadosReceitaDescricao.setTop(0);
        lblDadosReceitaDescricao.setWidth(122);
        lblDadosReceitaDescricao.setHeight(13);
        lblDadosReceitaDescricao.setCaption("lblDadosReceitaDescricao");
        lblDadosReceitaDescricao.setFontColor("clWindowText");
        lblDadosReceitaDescricao.setFontSize(-11);
        lblDadosReceitaDescricao.setFontName("Tahoma");
        lblDadosReceitaDescricao.setFontStyle("[]");
        lblDadosReceitaDescricao.setFieldName("DESCRICAO");
        lblDadosReceitaDescricao.setTable(tbDadosCadastraisReceita);
        lblDadosReceitaDescricao.setVerticalAlignment("taVerticalCenter");
        lblDadosReceitaDescricao.setWordBreak(false);
        hBoxDadosReceitaDescricao.addChildren(lblDadosReceitaDescricao);
        lblDadosReceitaDescricao.applyProperties();
    }

    public TFVBox vBoxCadastroReceita = new TFVBox();

    private void init_vBoxCadastroReceita() {
        vBoxCadastroReceita.setName("vBoxCadastroReceita");
        vBoxCadastroReceita.setLeft(0);
        vBoxCadastroReceita.setTop(21);
        vBoxCadastroReceita.setWidth(738);
        vBoxCadastroReceita.setHeight(51);
        vBoxCadastroReceita.setBorderStyle("stNone");
        vBoxCadastroReceita.setPaddingTop(0);
        vBoxCadastroReceita.setPaddingLeft(0);
        vBoxCadastroReceita.setPaddingRight(0);
        vBoxCadastroReceita.setPaddingBottom(0);
        vBoxCadastroReceita.setMarginTop(0);
        vBoxCadastroReceita.setMarginLeft(0);
        vBoxCadastroReceita.setMarginRight(0);
        vBoxCadastroReceita.setMarginBottom(0);
        vBoxCadastroReceita.setSpacing(1);
        vBoxCadastroReceita.setFlexVflex("ftFalse");
        vBoxCadastroReceita.setFlexHflex("ftFalse");
        vBoxCadastroReceita.setScrollable(false);
        vBoxCadastroReceita.setBoxShadowConfigHorizontalLength(10);
        vBoxCadastroReceita.setBoxShadowConfigVerticalLength(10);
        vBoxCadastroReceita.setBoxShadowConfigBlurRadius(5);
        vBoxCadastroReceita.setBoxShadowConfigSpreadRadius(0);
        vBoxCadastroReceita.setBoxShadowConfigShadowColor("clBlack");
        vBoxCadastroReceita.setBoxShadowConfigOpacity(75);
        vBoxDadosReceitaDetalhes.addChildren(vBoxCadastroReceita);
        vBoxCadastroReceita.applyProperties();
    }

    public TFHBox hBoxCadastrosReceita = new TFHBox();

    private void init_hBoxCadastrosReceita() {
        hBoxCadastrosReceita.setName("hBoxCadastrosReceita");
        hBoxCadastrosReceita.setLeft(0);
        hBoxCadastrosReceita.setTop(0);
        hBoxCadastrosReceita.setWidth(185);
        hBoxCadastrosReceita.setHeight(18);
        hBoxCadastrosReceita.setBorderStyle("stNone");
        hBoxCadastrosReceita.setColor("clSilver");
        hBoxCadastrosReceita.setPaddingTop(0);
        hBoxCadastrosReceita.setPaddingLeft(0);
        hBoxCadastrosReceita.setPaddingRight(0);
        hBoxCadastrosReceita.setPaddingBottom(0);
        hBoxCadastrosReceita.setMarginTop(0);
        hBoxCadastrosReceita.setMarginLeft(0);
        hBoxCadastrosReceita.setMarginRight(0);
        hBoxCadastrosReceita.setMarginBottom(0);
        hBoxCadastrosReceita.setSpacing(1);
        hBoxCadastrosReceita.setFlexVflex("ftFalse");
        hBoxCadastrosReceita.setFlexHflex("ftTrue");
        hBoxCadastrosReceita.setScrollable(false);
        hBoxCadastrosReceita.setBoxShadowConfigHorizontalLength(10);
        hBoxCadastrosReceita.setBoxShadowConfigVerticalLength(10);
        hBoxCadastrosReceita.setBoxShadowConfigBlurRadius(5);
        hBoxCadastrosReceita.setBoxShadowConfigSpreadRadius(0);
        hBoxCadastrosReceita.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadastrosReceita.setBoxShadowConfigOpacity(75);
        hBoxCadastrosReceita.setVAlign("tvTop");
        vBoxCadastroReceita.addChildren(hBoxCadastrosReceita);
        hBoxCadastrosReceita.applyProperties();
    }

    public TFLabel lblCadastroReceita = new TFLabel();

    private void init_lblCadastroReceita() {
        lblCadastroReceita.setName("lblCadastroReceita");
        lblCadastroReceita.setLeft(0);
        lblCadastroReceita.setTop(0);
        lblCadastroReceita.setWidth(98);
        lblCadastroReceita.setHeight(13);
        lblCadastroReceita.setCaption("Cadastro da Receita");
        lblCadastroReceita.setFontColor("clWindowText");
        lblCadastroReceita.setFontSize(-11);
        lblCadastroReceita.setFontName("Tahoma");
        lblCadastroReceita.setFontStyle("[]");
        lblCadastroReceita.setVerticalAlignment("taVerticalCenter");
        lblCadastroReceita.setWordBreak(false);
        hBoxCadastrosReceita.addChildren(lblCadastroReceita);
        lblCadastroReceita.applyProperties();
    }

    public TFString edtCadastroReceita = new TFString();

    private void init_edtCadastroReceita() {
        edtCadastroReceita.setName("edtCadastroReceita");
        edtCadastroReceita.setLeft(0);
        edtCadastroReceita.setTop(19);
        edtCadastroReceita.setWidth(313);
        edtCadastroReceita.setHeight(24);
        edtCadastroReceita.setTable(tbDadosCadastraisReceita);
        edtCadastroReceita.setFieldName("CAD_PUBLICO");
        edtCadastroReceita.setFlex(true);
        edtCadastroReceita.setRequired(false);
        edtCadastroReceita.setConstraintCheckWhen("cwImmediate");
        edtCadastroReceita.setConstraintCheckType("ctExpression");
        edtCadastroReceita.setConstraintFocusOnError(false);
        edtCadastroReceita.setConstraintEnableUI(true);
        edtCadastroReceita.setConstraintEnabled(false);
        edtCadastroReceita.setConstraintFormCheck(true);
        edtCadastroReceita.setCharCase("ccNormal");
        edtCadastroReceita.setPwd(false);
        edtCadastroReceita.setMaxlength(0);
        edtCadastroReceita.setEnabled(false);
        edtCadastroReceita.setFontColor("clWindowText");
        edtCadastroReceita.setFontSize(-13);
        edtCadastroReceita.setFontName("Tahoma");
        edtCadastroReceita.setFontStyle("[]");
        edtCadastroReceita.setSaveLiteralCharacter(false);
        edtCadastroReceita.applyProperties();
        vBoxCadastroReceita.addChildren(edtCadastroReceita);
        addValidatable(edtCadastroReceita);
    }

    public TFVBox vBoxCadastroReceitaNbs = new TFVBox();

    private void init_vBoxCadastroReceitaNbs() {
        vBoxCadastroReceitaNbs.setName("vBoxCadastroReceitaNbs");
        vBoxCadastroReceitaNbs.setLeft(0);
        vBoxCadastroReceitaNbs.setTop(73);
        vBoxCadastroReceitaNbs.setWidth(738);
        vBoxCadastroReceitaNbs.setHeight(49);
        vBoxCadastroReceitaNbs.setBorderStyle("stNone");
        vBoxCadastroReceitaNbs.setPaddingTop(0);
        vBoxCadastroReceitaNbs.setPaddingLeft(0);
        vBoxCadastroReceitaNbs.setPaddingRight(0);
        vBoxCadastroReceitaNbs.setPaddingBottom(0);
        vBoxCadastroReceitaNbs.setMarginTop(0);
        vBoxCadastroReceitaNbs.setMarginLeft(0);
        vBoxCadastroReceitaNbs.setMarginRight(0);
        vBoxCadastroReceitaNbs.setMarginBottom(0);
        vBoxCadastroReceitaNbs.setSpacing(1);
        vBoxCadastroReceitaNbs.setFlexVflex("ftFalse");
        vBoxCadastroReceitaNbs.setFlexHflex("ftFalse");
        vBoxCadastroReceitaNbs.setScrollable(false);
        vBoxCadastroReceitaNbs.setBoxShadowConfigHorizontalLength(10);
        vBoxCadastroReceitaNbs.setBoxShadowConfigVerticalLength(10);
        vBoxCadastroReceitaNbs.setBoxShadowConfigBlurRadius(5);
        vBoxCadastroReceitaNbs.setBoxShadowConfigSpreadRadius(0);
        vBoxCadastroReceitaNbs.setBoxShadowConfigShadowColor("clBlack");
        vBoxCadastroReceitaNbs.setBoxShadowConfigOpacity(75);
        vBoxDadosReceitaDetalhes.addChildren(vBoxCadastroReceitaNbs);
        vBoxCadastroReceitaNbs.applyProperties();
    }

    public TFHBox hBoxCadastrosReceitaNbs = new TFHBox();

    private void init_hBoxCadastrosReceitaNbs() {
        hBoxCadastrosReceitaNbs.setName("hBoxCadastrosReceitaNbs");
        hBoxCadastrosReceitaNbs.setLeft(0);
        hBoxCadastrosReceitaNbs.setTop(0);
        hBoxCadastrosReceitaNbs.setWidth(185);
        hBoxCadastrosReceitaNbs.setHeight(18);
        hBoxCadastrosReceitaNbs.setBorderStyle("stNone");
        hBoxCadastrosReceitaNbs.setColor("clSilver");
        hBoxCadastrosReceitaNbs.setPaddingTop(0);
        hBoxCadastrosReceitaNbs.setPaddingLeft(0);
        hBoxCadastrosReceitaNbs.setPaddingRight(0);
        hBoxCadastrosReceitaNbs.setPaddingBottom(0);
        hBoxCadastrosReceitaNbs.setMarginTop(0);
        hBoxCadastrosReceitaNbs.setMarginLeft(0);
        hBoxCadastrosReceitaNbs.setMarginRight(0);
        hBoxCadastrosReceitaNbs.setMarginBottom(0);
        hBoxCadastrosReceitaNbs.setSpacing(1);
        hBoxCadastrosReceitaNbs.setFlexVflex("ftFalse");
        hBoxCadastrosReceitaNbs.setFlexHflex("ftTrue");
        hBoxCadastrosReceitaNbs.setScrollable(false);
        hBoxCadastrosReceitaNbs.setBoxShadowConfigHorizontalLength(10);
        hBoxCadastrosReceitaNbs.setBoxShadowConfigVerticalLength(10);
        hBoxCadastrosReceitaNbs.setBoxShadowConfigBlurRadius(5);
        hBoxCadastrosReceitaNbs.setBoxShadowConfigSpreadRadius(0);
        hBoxCadastrosReceitaNbs.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadastrosReceitaNbs.setBoxShadowConfigOpacity(75);
        hBoxCadastrosReceitaNbs.setVAlign("tvTop");
        vBoxCadastroReceitaNbs.addChildren(hBoxCadastrosReceitaNbs);
        hBoxCadastrosReceitaNbs.applyProperties();
    }

    public TFLabel lblCadastroReceitaNbs = new TFLabel();

    private void init_lblCadastroReceitaNbs() {
        lblCadastroReceitaNbs.setName("lblCadastroReceitaNbs");
        lblCadastroReceitaNbs.setLeft(0);
        lblCadastroReceitaNbs.setTop(0);
        lblCadastroReceitaNbs.setWidth(138);
        lblCadastroReceitaNbs.setHeight(13);
        lblCadastroReceitaNbs.setCaption("Cadastrados no Sistema NBS");
        lblCadastroReceitaNbs.setFontColor("clWindowText");
        lblCadastroReceitaNbs.setFontSize(-11);
        lblCadastroReceitaNbs.setFontName("Tahoma");
        lblCadastroReceitaNbs.setFontStyle("[]");
        lblCadastroReceitaNbs.setVerticalAlignment("taVerticalCenter");
        lblCadastroReceitaNbs.setWordBreak(false);
        hBoxCadastrosReceitaNbs.addChildren(lblCadastroReceitaNbs);
        lblCadastroReceitaNbs.applyProperties();
    }

    public TFString edtCadastroReceitaNbs = new TFString();

    private void init_edtCadastroReceitaNbs() {
        edtCadastroReceitaNbs.setName("edtCadastroReceitaNbs");
        edtCadastroReceitaNbs.setLeft(0);
        edtCadastroReceitaNbs.setTop(19);
        edtCadastroReceitaNbs.setWidth(313);
        edtCadastroReceitaNbs.setHeight(24);
        edtCadastroReceitaNbs.setTable(tbDadosCadastraisReceita);
        edtCadastroReceitaNbs.setFieldName("CAD_NBS");
        edtCadastroReceitaNbs.setFlex(true);
        edtCadastroReceitaNbs.setRequired(false);
        edtCadastroReceitaNbs.setConstraintCheckWhen("cwImmediate");
        edtCadastroReceitaNbs.setConstraintCheckType("ctExpression");
        edtCadastroReceitaNbs.setConstraintFocusOnError(false);
        edtCadastroReceitaNbs.setConstraintEnableUI(true);
        edtCadastroReceitaNbs.setConstraintEnabled(false);
        edtCadastroReceitaNbs.setConstraintFormCheck(true);
        edtCadastroReceitaNbs.setCharCase("ccNormal");
        edtCadastroReceitaNbs.setPwd(false);
        edtCadastroReceitaNbs.setMaxlength(0);
        edtCadastroReceitaNbs.setEnabled(false);
        edtCadastroReceitaNbs.setFontColor("clWindowText");
        edtCadastroReceitaNbs.setFontSize(-13);
        edtCadastroReceitaNbs.setFontName("Tahoma");
        edtCadastroReceitaNbs.setFontStyle("[]");
        edtCadastroReceitaNbs.setSaveLiteralCharacter(false);
        edtCadastroReceitaNbs.applyProperties();
        vBoxCadastroReceitaNbs.addChildren(edtCadastroReceitaNbs);
        addValidatable(edtCadastroReceitaNbs);
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(248);
        FHBox3.setWidth(185);
        FHBox3.setHeight(13);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        vBoxTabDadosReceita.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFTabsheet tbsSintegra = new TFTabsheet();

    private void init_tbsSintegra() {
        tbsSintegra.setName("tbsSintegra");
        tbsSintegra.setCaption("Sintegra");
        tbsSintegra.setVisible(true);
        tbsSintegra.setClosable(false);
        pgcDados.addChildren(tbsSintegra);
        tbsSintegra.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(2);
        FVBox3.setTop(6);
        FVBox3.setWidth(749);
        FVBox3.setHeight(340);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        tbsSintegra.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFGrid grdDadosSintegra = new TFGrid();

    private void init_grdDadosSintegra() {
        grdDadosSintegra.setName("grdDadosSintegra");
        grdDadosSintegra.setLeft(0);
        grdDadosSintegra.setTop(0);
        grdDadosSintegra.setWidth(741);
        grdDadosSintegra.setHeight(120);
        grdDadosSintegra.setTable(tbDadosCadastraisSintegra);
        grdDadosSintegra.setFlexVflex("ftTrue");
        grdDadosSintegra.setFlexHflex("ftTrue");
        grdDadosSintegra.setPagingEnabled(false);
        grdDadosSintegra.setFrozenColumns(0);
        grdDadosSintegra.setShowFooter(false);
        grdDadosSintegra.setShowHeader(true);
        grdDadosSintegra.setMultiSelection(false);
        grdDadosSintegra.setGroupingEnabled(false);
        grdDadosSintegra.setGroupingExpanded(false);
        grdDadosSintegra.setGroupingShowFooter(false);
        grdDadosSintegra.setCrosstabEnabled(false);
        grdDadosSintegra.setCrosstabGroupType("cgtConcat");
        grdDadosSintegra.setEditionEnabled(false);
        grdDadosSintegra.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(200);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdDadosSintegra.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("CAD_PUBLICO");
        item1.setTitleCaption("Dados Retornados");
        item1.setWidth(200);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdDadosSintegra.getColumns().add(item1);
        FVBox3.addChildren(grdDadosSintegra);
        grdDadosSintegra.applyProperties();
    }

    public TFVBox vBoxDadosSintegraDetalhes = new TFVBox();

    private void init_vBoxDadosSintegraDetalhes() {
        vBoxDadosSintegraDetalhes.setName("vBoxDadosSintegraDetalhes");
        vBoxDadosSintegraDetalhes.setLeft(0);
        vBoxDadosSintegraDetalhes.setTop(121);
        vBoxDadosSintegraDetalhes.setWidth(751);
        vBoxDadosSintegraDetalhes.setHeight(126);
        vBoxDadosSintegraDetalhes.setBorderStyle("stSingleLine");
        vBoxDadosSintegraDetalhes.setPaddingTop(0);
        vBoxDadosSintegraDetalhes.setPaddingLeft(8);
        vBoxDadosSintegraDetalhes.setPaddingRight(8);
        vBoxDadosSintegraDetalhes.setPaddingBottom(0);
        vBoxDadosSintegraDetalhes.setMarginTop(0);
        vBoxDadosSintegraDetalhes.setMarginLeft(0);
        vBoxDadosSintegraDetalhes.setMarginRight(0);
        vBoxDadosSintegraDetalhes.setMarginBottom(0);
        vBoxDadosSintegraDetalhes.setSpacing(1);
        vBoxDadosSintegraDetalhes.setFlexVflex("ftFalse");
        vBoxDadosSintegraDetalhes.setFlexHflex("ftTrue");
        vBoxDadosSintegraDetalhes.setScrollable(false);
        vBoxDadosSintegraDetalhes.setBoxShadowConfigHorizontalLength(10);
        vBoxDadosSintegraDetalhes.setBoxShadowConfigVerticalLength(10);
        vBoxDadosSintegraDetalhes.setBoxShadowConfigBlurRadius(5);
        vBoxDadosSintegraDetalhes.setBoxShadowConfigSpreadRadius(0);
        vBoxDadosSintegraDetalhes.setBoxShadowConfigShadowColor("clBlack");
        vBoxDadosSintegraDetalhes.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(vBoxDadosSintegraDetalhes);
        vBoxDadosSintegraDetalhes.applyProperties();
    }

    public TFHBox hBoxDadosSintegraDescricao = new TFHBox();

    private void init_hBoxDadosSintegraDescricao() {
        hBoxDadosSintegraDescricao.setName("hBoxDadosSintegraDescricao");
        hBoxDadosSintegraDescricao.setLeft(0);
        hBoxDadosSintegraDescricao.setTop(0);
        hBoxDadosSintegraDescricao.setWidth(633);
        hBoxDadosSintegraDescricao.setHeight(20);
        hBoxDadosSintegraDescricao.setBorderStyle("stNone");
        hBoxDadosSintegraDescricao.setPaddingTop(0);
        hBoxDadosSintegraDescricao.setPaddingLeft(0);
        hBoxDadosSintegraDescricao.setPaddingRight(0);
        hBoxDadosSintegraDescricao.setPaddingBottom(0);
        hBoxDadosSintegraDescricao.setMarginTop(0);
        hBoxDadosSintegraDescricao.setMarginLeft(0);
        hBoxDadosSintegraDescricao.setMarginRight(0);
        hBoxDadosSintegraDescricao.setMarginBottom(0);
        hBoxDadosSintegraDescricao.setSpacing(1);
        hBoxDadosSintegraDescricao.setFlexVflex("ftFalse");
        hBoxDadosSintegraDescricao.setFlexHflex("ftFalse");
        hBoxDadosSintegraDescricao.setScrollable(false);
        hBoxDadosSintegraDescricao.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosSintegraDescricao.setBoxShadowConfigVerticalLength(10);
        hBoxDadosSintegraDescricao.setBoxShadowConfigBlurRadius(5);
        hBoxDadosSintegraDescricao.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosSintegraDescricao.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosSintegraDescricao.setBoxShadowConfigOpacity(75);
        hBoxDadosSintegraDescricao.setVAlign("tvTop");
        vBoxDadosSintegraDetalhes.addChildren(hBoxDadosSintegraDescricao);
        hBoxDadosSintegraDescricao.applyProperties();
    }

    public TFLabel lblDadosSintegraDescricao = new TFLabel();

    private void init_lblDadosSintegraDescricao() {
        lblDadosSintegraDescricao.setName("lblDadosSintegraDescricao");
        lblDadosSintegraDescricao.setLeft(0);
        lblDadosSintegraDescricao.setTop(0);
        lblDadosSintegraDescricao.setWidth(122);
        lblDadosSintegraDescricao.setHeight(13);
        lblDadosSintegraDescricao.setCaption("lblDadosReceitaDescricao");
        lblDadosSintegraDescricao.setFontColor("clWindowText");
        lblDadosSintegraDescricao.setFontSize(-11);
        lblDadosSintegraDescricao.setFontName("Tahoma");
        lblDadosSintegraDescricao.setFontStyle("[]");
        lblDadosSintegraDescricao.setFieldName("DESCRICAO");
        lblDadosSintegraDescricao.setTable(tbDadosCadastraisSintegra);
        lblDadosSintegraDescricao.setVerticalAlignment("taVerticalCenter");
        lblDadosSintegraDescricao.setWordBreak(false);
        hBoxDadosSintegraDescricao.addChildren(lblDadosSintegraDescricao);
        lblDadosSintegraDescricao.applyProperties();
    }

    public TFVBox vBoxCadastroSintegra = new TFVBox();

    private void init_vBoxCadastroSintegra() {
        vBoxCadastroSintegra.setName("vBoxCadastroSintegra");
        vBoxCadastroSintegra.setLeft(0);
        vBoxCadastroSintegra.setTop(21);
        vBoxCadastroSintegra.setWidth(738);
        vBoxCadastroSintegra.setHeight(51);
        vBoxCadastroSintegra.setBorderStyle("stNone");
        vBoxCadastroSintegra.setPaddingTop(0);
        vBoxCadastroSintegra.setPaddingLeft(0);
        vBoxCadastroSintegra.setPaddingRight(0);
        vBoxCadastroSintegra.setPaddingBottom(0);
        vBoxCadastroSintegra.setMarginTop(0);
        vBoxCadastroSintegra.setMarginLeft(0);
        vBoxCadastroSintegra.setMarginRight(0);
        vBoxCadastroSintegra.setMarginBottom(0);
        vBoxCadastroSintegra.setSpacing(1);
        vBoxCadastroSintegra.setFlexVflex("ftFalse");
        vBoxCadastroSintegra.setFlexHflex("ftFalse");
        vBoxCadastroSintegra.setScrollable(false);
        vBoxCadastroSintegra.setBoxShadowConfigHorizontalLength(10);
        vBoxCadastroSintegra.setBoxShadowConfigVerticalLength(10);
        vBoxCadastroSintegra.setBoxShadowConfigBlurRadius(5);
        vBoxCadastroSintegra.setBoxShadowConfigSpreadRadius(0);
        vBoxCadastroSintegra.setBoxShadowConfigShadowColor("clBlack");
        vBoxCadastroSintegra.setBoxShadowConfigOpacity(75);
        vBoxDadosSintegraDetalhes.addChildren(vBoxCadastroSintegra);
        vBoxCadastroSintegra.applyProperties();
    }

    public TFHBox hBoxCadastradosSintegra = new TFHBox();

    private void init_hBoxCadastradosSintegra() {
        hBoxCadastradosSintegra.setName("hBoxCadastradosSintegra");
        hBoxCadastradosSintegra.setLeft(0);
        hBoxCadastradosSintegra.setTop(0);
        hBoxCadastradosSintegra.setWidth(185);
        hBoxCadastradosSintegra.setHeight(18);
        hBoxCadastradosSintegra.setBorderStyle("stNone");
        hBoxCadastradosSintegra.setColor("clSilver");
        hBoxCadastradosSintegra.setPaddingTop(0);
        hBoxCadastradosSintegra.setPaddingLeft(0);
        hBoxCadastradosSintegra.setPaddingRight(0);
        hBoxCadastradosSintegra.setPaddingBottom(0);
        hBoxCadastradosSintegra.setMarginTop(0);
        hBoxCadastradosSintegra.setMarginLeft(0);
        hBoxCadastradosSintegra.setMarginRight(0);
        hBoxCadastradosSintegra.setMarginBottom(0);
        hBoxCadastradosSintegra.setSpacing(1);
        hBoxCadastradosSintegra.setFlexVflex("ftFalse");
        hBoxCadastradosSintegra.setFlexHflex("ftTrue");
        hBoxCadastradosSintegra.setScrollable(false);
        hBoxCadastradosSintegra.setBoxShadowConfigHorizontalLength(10);
        hBoxCadastradosSintegra.setBoxShadowConfigVerticalLength(10);
        hBoxCadastradosSintegra.setBoxShadowConfigBlurRadius(5);
        hBoxCadastradosSintegra.setBoxShadowConfigSpreadRadius(0);
        hBoxCadastradosSintegra.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadastradosSintegra.setBoxShadowConfigOpacity(75);
        hBoxCadastradosSintegra.setVAlign("tvTop");
        vBoxCadastroSintegra.addChildren(hBoxCadastradosSintegra);
        hBoxCadastradosSintegra.applyProperties();
    }

    public TFLabel lblCadastroSintegra = new TFLabel();

    private void init_lblCadastroSintegra() {
        lblCadastroSintegra.setName("lblCadastroSintegra");
        lblCadastroSintegra.setLeft(0);
        lblCadastroSintegra.setTop(0);
        lblCadastroSintegra.setWidth(102);
        lblCadastroSintegra.setHeight(13);
        lblCadastroSintegra.setCaption("Cadastro do Sintegra");
        lblCadastroSintegra.setFontColor("clWindowText");
        lblCadastroSintegra.setFontSize(-11);
        lblCadastroSintegra.setFontName("Tahoma");
        lblCadastroSintegra.setFontStyle("[]");
        lblCadastroSintegra.setVerticalAlignment("taVerticalCenter");
        lblCadastroSintegra.setWordBreak(false);
        hBoxCadastradosSintegra.addChildren(lblCadastroSintegra);
        lblCadastroSintegra.applyProperties();
    }

    public TFString edtCadastroSintegra = new TFString();

    private void init_edtCadastroSintegra() {
        edtCadastroSintegra.setName("edtCadastroSintegra");
        edtCadastroSintegra.setLeft(0);
        edtCadastroSintegra.setTop(19);
        edtCadastroSintegra.setWidth(313);
        edtCadastroSintegra.setHeight(24);
        edtCadastroSintegra.setTable(tbDadosCadastraisSintegra);
        edtCadastroSintegra.setFieldName("CAD_PUBLICO");
        edtCadastroSintegra.setFlex(true);
        edtCadastroSintegra.setRequired(false);
        edtCadastroSintegra.setConstraintCheckWhen("cwImmediate");
        edtCadastroSintegra.setConstraintCheckType("ctExpression");
        edtCadastroSintegra.setConstraintFocusOnError(false);
        edtCadastroSintegra.setConstraintEnableUI(true);
        edtCadastroSintegra.setConstraintEnabled(false);
        edtCadastroSintegra.setConstraintFormCheck(true);
        edtCadastroSintegra.setCharCase("ccNormal");
        edtCadastroSintegra.setPwd(false);
        edtCadastroSintegra.setMaxlength(0);
        edtCadastroSintegra.setEnabled(false);
        edtCadastroSintegra.setFontColor("clWindowText");
        edtCadastroSintegra.setFontSize(-13);
        edtCadastroSintegra.setFontName("Tahoma");
        edtCadastroSintegra.setFontStyle("[]");
        edtCadastroSintegra.setSaveLiteralCharacter(false);
        edtCadastroSintegra.applyProperties();
        vBoxCadastroSintegra.addChildren(edtCadastroSintegra);
        addValidatable(edtCadastroSintegra);
    }

    public TFVBox vBoxCadastroSintegraNbs = new TFVBox();

    private void init_vBoxCadastroSintegraNbs() {
        vBoxCadastroSintegraNbs.setName("vBoxCadastroSintegraNbs");
        vBoxCadastroSintegraNbs.setLeft(0);
        vBoxCadastroSintegraNbs.setTop(73);
        vBoxCadastroSintegraNbs.setWidth(738);
        vBoxCadastroSintegraNbs.setHeight(49);
        vBoxCadastroSintegraNbs.setBorderStyle("stNone");
        vBoxCadastroSintegraNbs.setPaddingTop(0);
        vBoxCadastroSintegraNbs.setPaddingLeft(0);
        vBoxCadastroSintegraNbs.setPaddingRight(0);
        vBoxCadastroSintegraNbs.setPaddingBottom(0);
        vBoxCadastroSintegraNbs.setMarginTop(0);
        vBoxCadastroSintegraNbs.setMarginLeft(0);
        vBoxCadastroSintegraNbs.setMarginRight(0);
        vBoxCadastroSintegraNbs.setMarginBottom(0);
        vBoxCadastroSintegraNbs.setSpacing(1);
        vBoxCadastroSintegraNbs.setFlexVflex("ftFalse");
        vBoxCadastroSintegraNbs.setFlexHflex("ftFalse");
        vBoxCadastroSintegraNbs.setScrollable(false);
        vBoxCadastroSintegraNbs.setBoxShadowConfigHorizontalLength(10);
        vBoxCadastroSintegraNbs.setBoxShadowConfigVerticalLength(10);
        vBoxCadastroSintegraNbs.setBoxShadowConfigBlurRadius(5);
        vBoxCadastroSintegraNbs.setBoxShadowConfigSpreadRadius(0);
        vBoxCadastroSintegraNbs.setBoxShadowConfigShadowColor("clBlack");
        vBoxCadastroSintegraNbs.setBoxShadowConfigOpacity(75);
        vBoxDadosSintegraDetalhes.addChildren(vBoxCadastroSintegraNbs);
        vBoxCadastroSintegraNbs.applyProperties();
    }

    public TFHBox hBoxCadastradosSintegraNbs = new TFHBox();

    private void init_hBoxCadastradosSintegraNbs() {
        hBoxCadastradosSintegraNbs.setName("hBoxCadastradosSintegraNbs");
        hBoxCadastradosSintegraNbs.setLeft(0);
        hBoxCadastradosSintegraNbs.setTop(0);
        hBoxCadastradosSintegraNbs.setWidth(185);
        hBoxCadastradosSintegraNbs.setHeight(18);
        hBoxCadastradosSintegraNbs.setBorderStyle("stNone");
        hBoxCadastradosSintegraNbs.setColor("clSilver");
        hBoxCadastradosSintegraNbs.setPaddingTop(0);
        hBoxCadastradosSintegraNbs.setPaddingLeft(0);
        hBoxCadastradosSintegraNbs.setPaddingRight(0);
        hBoxCadastradosSintegraNbs.setPaddingBottom(0);
        hBoxCadastradosSintegraNbs.setMarginTop(0);
        hBoxCadastradosSintegraNbs.setMarginLeft(0);
        hBoxCadastradosSintegraNbs.setMarginRight(0);
        hBoxCadastradosSintegraNbs.setMarginBottom(0);
        hBoxCadastradosSintegraNbs.setSpacing(1);
        hBoxCadastradosSintegraNbs.setFlexVflex("ftFalse");
        hBoxCadastradosSintegraNbs.setFlexHflex("ftTrue");
        hBoxCadastradosSintegraNbs.setScrollable(false);
        hBoxCadastradosSintegraNbs.setBoxShadowConfigHorizontalLength(10);
        hBoxCadastradosSintegraNbs.setBoxShadowConfigVerticalLength(10);
        hBoxCadastradosSintegraNbs.setBoxShadowConfigBlurRadius(5);
        hBoxCadastradosSintegraNbs.setBoxShadowConfigSpreadRadius(0);
        hBoxCadastradosSintegraNbs.setBoxShadowConfigShadowColor("clBlack");
        hBoxCadastradosSintegraNbs.setBoxShadowConfigOpacity(75);
        hBoxCadastradosSintegraNbs.setVAlign("tvTop");
        vBoxCadastroSintegraNbs.addChildren(hBoxCadastradosSintegraNbs);
        hBoxCadastradosSintegraNbs.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(138);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Cadastrados no Sistema NBS");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        hBoxCadastradosSintegraNbs.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFString edtCadastroSintegraNbs = new TFString();

    private void init_edtCadastroSintegraNbs() {
        edtCadastroSintegraNbs.setName("edtCadastroSintegraNbs");
        edtCadastroSintegraNbs.setLeft(0);
        edtCadastroSintegraNbs.setTop(19);
        edtCadastroSintegraNbs.setWidth(313);
        edtCadastroSintegraNbs.setHeight(24);
        edtCadastroSintegraNbs.setTable(tbDadosCadastraisSintegra);
        edtCadastroSintegraNbs.setFieldName("CAD_NBS");
        edtCadastroSintegraNbs.setFlex(true);
        edtCadastroSintegraNbs.setRequired(false);
        edtCadastroSintegraNbs.setConstraintCheckWhen("cwImmediate");
        edtCadastroSintegraNbs.setConstraintCheckType("ctExpression");
        edtCadastroSintegraNbs.setConstraintFocusOnError(false);
        edtCadastroSintegraNbs.setConstraintEnableUI(true);
        edtCadastroSintegraNbs.setConstraintEnabled(false);
        edtCadastroSintegraNbs.setConstraintFormCheck(true);
        edtCadastroSintegraNbs.setCharCase("ccNormal");
        edtCadastroSintegraNbs.setPwd(false);
        edtCadastroSintegraNbs.setMaxlength(0);
        edtCadastroSintegraNbs.setEnabled(false);
        edtCadastroSintegraNbs.setFontColor("clWindowText");
        edtCadastroSintegraNbs.setFontSize(-13);
        edtCadastroSintegraNbs.setFontName("Tahoma");
        edtCadastroSintegraNbs.setFontStyle("[]");
        edtCadastroSintegraNbs.setSaveLiteralCharacter(false);
        edtCadastroSintegraNbs.applyProperties();
        vBoxCadastroSintegraNbs.addChildren(edtCadastroSintegraNbs);
        addValidatable(edtCadastroSintegraNbs);
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(248);
        FHBox4.setWidth(185);
        FHBox4.setHeight(13);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox3.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnConsultaAPIIntegracaoClick(final Event<Object> event) {
        if (btnConsultaAPIIntegracao.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultaAPIIntegracao");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void lblSituacaoCadSintegraClick(final Event<Object> event);

    public abstract void lblSituacaoSintegraClick(final Event<Object> event);

    public abstract void lblSintegraMultiIeClick(final Event<Object> event);

}