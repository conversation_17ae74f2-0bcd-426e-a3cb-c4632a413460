package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAutenticaUsuario extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AutenticaUsuarioRNA rn = null;

    public FrmAutenticaUsuario() {
        try {
            rn = (freedom.bytecode.rn.AutenticaUsuarioRNA) getRN(freedom.bytecode.rn.wizard.AutenticaUsuarioRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxIdentificaUsuario();
        init_FHBox11();
        init_btnVoltar();
        init_btnAceitar();
        init_hboxMsg();
        init_lblMsg();
        init_FVBox3();
        init_FLabel1();
        init_edtUsuario();
        init_FVBox1();
        init_FLabel2();
        init_edtSenha();
        init_vBoxMotivo();
        init_lblMotivo();
        init_edtMotivo();
        init_FrmAutenticaUsuario();
    }

    protected TFForm FrmAutenticaUsuario = this;
    private void init_FrmAutenticaUsuario() {
        FrmAutenticaUsuario.setName("FrmAutenticaUsuario");
        FrmAutenticaUsuario.setCaption("Identifica\u00E7\u00E3o do Usu\u00E1rio");
        FrmAutenticaUsuario.setClientHeight(301);
        FrmAutenticaUsuario.setClientWidth(334);
        FrmAutenticaUsuario.setColor("clBtnFace");
        FrmAutenticaUsuario.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAutenticaUsuario", "FrmAutenticaUsuario", "OnCreate");
        });
        FrmAutenticaUsuario.setWKey("4600654");
        FrmAutenticaUsuario.setSpacing(0);
        FrmAutenticaUsuario.applyProperties();
    }

    public TFVBox vBoxIdentificaUsuario = new TFVBox();

    private void init_vBoxIdentificaUsuario() {
        vBoxIdentificaUsuario.setName("vBoxIdentificaUsuario");
        vBoxIdentificaUsuario.setLeft(0);
        vBoxIdentificaUsuario.setTop(0);
        vBoxIdentificaUsuario.setWidth(334);
        vBoxIdentificaUsuario.setHeight(301);
        vBoxIdentificaUsuario.setAlign("alClient");
        vBoxIdentificaUsuario.setBorderStyle("stNone");
        vBoxIdentificaUsuario.setPaddingTop(5);
        vBoxIdentificaUsuario.setPaddingLeft(5);
        vBoxIdentificaUsuario.setPaddingRight(5);
        vBoxIdentificaUsuario.setPaddingBottom(5);
        vBoxIdentificaUsuario.setMarginTop(0);
        vBoxIdentificaUsuario.setMarginLeft(0);
        vBoxIdentificaUsuario.setMarginRight(0);
        vBoxIdentificaUsuario.setMarginBottom(0);
        vBoxIdentificaUsuario.setSpacing(3);
        vBoxIdentificaUsuario.setFlexVflex("ftTrue");
        vBoxIdentificaUsuario.setFlexHflex("ftTrue");
        vBoxIdentificaUsuario.setScrollable(false);
        vBoxIdentificaUsuario.setBoxShadowConfigHorizontalLength(10);
        vBoxIdentificaUsuario.setBoxShadowConfigVerticalLength(10);
        vBoxIdentificaUsuario.setBoxShadowConfigBlurRadius(5);
        vBoxIdentificaUsuario.setBoxShadowConfigSpreadRadius(0);
        vBoxIdentificaUsuario.setBoxShadowConfigShadowColor("clBlack");
        vBoxIdentificaUsuario.setBoxShadowConfigOpacity(75);
        FrmAutenticaUsuario.addChildren(vBoxIdentificaUsuario);
        vBoxIdentificaUsuario.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(329);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxIdentificaUsuario.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAutenticaUsuario", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(60);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-13);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmAutenticaUsuario", "btnAceitar", "OnClick");
        });
        btnAceitar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515"
 + "0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3"
 + "1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7"
 + "95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC"
 + "101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D"
 + "1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537"
 + "A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC"
 + "C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB"
 + "482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB"
 + "1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D"
 + "C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F"
 + "8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082");
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox11.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFHBox hboxMsg = new TFHBox();

    private void init_hboxMsg() {
        hboxMsg.setName("hboxMsg");
        hboxMsg.setLeft(0);
        hboxMsg.setTop(62);
        hboxMsg.setWidth(329);
        hboxMsg.setHeight(41);
        hboxMsg.setBorderStyle("stNone");
        hboxMsg.setPaddingTop(0);
        hboxMsg.setPaddingLeft(0);
        hboxMsg.setPaddingRight(0);
        hboxMsg.setPaddingBottom(0);
        hboxMsg.setMarginTop(0);
        hboxMsg.setMarginLeft(0);
        hboxMsg.setMarginRight(0);
        hboxMsg.setMarginBottom(0);
        hboxMsg.setSpacing(1);
        hboxMsg.setFlexVflex("ftMin");
        hboxMsg.setFlexHflex("ftTrue");
        hboxMsg.setScrollable(false);
        hboxMsg.setBoxShadowConfigHorizontalLength(10);
        hboxMsg.setBoxShadowConfigVerticalLength(10);
        hboxMsg.setBoxShadowConfigBlurRadius(5);
        hboxMsg.setBoxShadowConfigSpreadRadius(0);
        hboxMsg.setBoxShadowConfigShadowColor("clBlack");
        hboxMsg.setBoxShadowConfigOpacity(75);
        hboxMsg.setVAlign("tvTop");
        vBoxIdentificaUsuario.addChildren(hboxMsg);
        hboxMsg.applyProperties();
    }

    public TFLabel lblMsg = new TFLabel();

    private void init_lblMsg() {
        lblMsg.setName("lblMsg");
        lblMsg.setLeft(0);
        lblMsg.setTop(0);
        lblMsg.setWidth(36);
        lblMsg.setHeight(16);
        lblMsg.setCaption("lblMsg");
        lblMsg.setFontColor("clRed");
        lblMsg.setFontSize(-13);
        lblMsg.setFontName("Tahoma");
        lblMsg.setFontStyle("[]");
        lblMsg.setVerticalAlignment("taVerticalCenter");
        lblMsg.setWordBreak(false);
        hboxMsg.addChildren(lblMsg);
        lblMsg.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(104);
        FVBox3.setWidth(185);
        FVBox3.setHeight(60);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(3);
        FVBox3.setFlexVflex("ftMin");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        vBoxIdentificaUsuario.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(40);
        FLabel1.setHeight(13);
        FLabel1.setAlign("alLeft");
        FLabel1.setCaption("Usu\u00E1rio:");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox3.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFString edtUsuario = new TFString();

    private void init_edtUsuario() {
        edtUsuario.setName("edtUsuario");
        edtUsuario.setLeft(0);
        edtUsuario.setTop(14);
        edtUsuario.setWidth(121);
        edtUsuario.setHeight(24);
        edtUsuario.setFlex(false);
        edtUsuario.setRequired(false);
        edtUsuario.setConstraintCheckWhen("cwImmediate");
        edtUsuario.setConstraintCheckType("ctExpression");
        edtUsuario.setConstraintFocusOnError(false);
        edtUsuario.setConstraintEnableUI(true);
        edtUsuario.setConstraintEnabled(false);
        edtUsuario.setConstraintFormCheck(true);
        edtUsuario.setCharCase("ccUpper");
        edtUsuario.setPwd(false);
        edtUsuario.setMaxlength(0);
        edtUsuario.setAlign("alLeft");
        edtUsuario.setFontColor("clWindowText");
        edtUsuario.setFontSize(-13);
        edtUsuario.setFontName("Tahoma");
        edtUsuario.setFontStyle("[]");
        edtUsuario.setSaveLiteralCharacter(false);
        edtUsuario.applyProperties();
        FVBox3.addChildren(edtUsuario);
        addValidatable(edtUsuario);
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(165);
        FVBox1.setWidth(185);
        FVBox1.setHeight(60);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(3);
        FVBox1.setFlexVflex("ftMin");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        vBoxIdentificaUsuario.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(34);
        FLabel2.setHeight(13);
        FLabel2.setAlign("alLeft");
        FLabel2.setCaption("Senha:");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox1.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFString edtSenha = new TFString();

    private void init_edtSenha() {
        edtSenha.setName("edtSenha");
        edtSenha.setLeft(0);
        edtSenha.setTop(14);
        edtSenha.setWidth(121);
        edtSenha.setHeight(24);
        edtSenha.setFlex(false);
        edtSenha.setRequired(false);
        edtSenha.setConstraintCheckWhen("cwImmediate");
        edtSenha.setConstraintCheckType("ctExpression");
        edtSenha.setConstraintFocusOnError(false);
        edtSenha.setConstraintEnableUI(true);
        edtSenha.setConstraintEnabled(false);
        edtSenha.setConstraintFormCheck(true);
        edtSenha.setCharCase("ccNormal");
        edtSenha.setPwd(true);
        edtSenha.setMaxlength(0);
        edtSenha.setAlign("alLeft");
        edtSenha.setFontColor("clWindowText");
        edtSenha.setFontSize(-13);
        edtSenha.setFontName("Tahoma");
        edtSenha.setFontStyle("[]");
        edtSenha.setSaveLiteralCharacter(false);
        edtSenha.applyProperties();
        FVBox1.addChildren(edtSenha);
        addValidatable(edtSenha);
    }

    public TFVBox vBoxMotivo = new TFVBox();

    private void init_vBoxMotivo() {
        vBoxMotivo.setName("vBoxMotivo");
        vBoxMotivo.setLeft(0);
        vBoxMotivo.setTop(226);
        vBoxMotivo.setWidth(185);
        vBoxMotivo.setHeight(60);
        vBoxMotivo.setBorderStyle("stNone");
        vBoxMotivo.setPaddingTop(0);
        vBoxMotivo.setPaddingLeft(0);
        vBoxMotivo.setPaddingRight(0);
        vBoxMotivo.setPaddingBottom(0);
        vBoxMotivo.setVisible(false);
        vBoxMotivo.setMarginTop(0);
        vBoxMotivo.setMarginLeft(0);
        vBoxMotivo.setMarginRight(0);
        vBoxMotivo.setMarginBottom(0);
        vBoxMotivo.setSpacing(3);
        vBoxMotivo.setFlexVflex("ftMin");
        vBoxMotivo.setFlexHflex("ftTrue");
        vBoxMotivo.setScrollable(false);
        vBoxMotivo.setBoxShadowConfigHorizontalLength(10);
        vBoxMotivo.setBoxShadowConfigVerticalLength(10);
        vBoxMotivo.setBoxShadowConfigBlurRadius(5);
        vBoxMotivo.setBoxShadowConfigSpreadRadius(0);
        vBoxMotivo.setBoxShadowConfigShadowColor("clBlack");
        vBoxMotivo.setBoxShadowConfigOpacity(75);
        vBoxIdentificaUsuario.addChildren(vBoxMotivo);
        vBoxMotivo.applyProperties();
    }

    public TFLabel lblMotivo = new TFLabel();

    private void init_lblMotivo() {
        lblMotivo.setName("lblMotivo");
        lblMotivo.setLeft(0);
        lblMotivo.setTop(0);
        lblMotivo.setWidth(32);
        lblMotivo.setHeight(13);
        lblMotivo.setAlign("alLeft");
        lblMotivo.setCaption("Motivo");
        lblMotivo.setFontColor("clWindowText");
        lblMotivo.setFontSize(-11);
        lblMotivo.setFontName("Tahoma");
        lblMotivo.setFontStyle("[]");
        lblMotivo.setVerticalAlignment("taVerticalCenter");
        lblMotivo.setWordBreak(false);
        vBoxMotivo.addChildren(lblMotivo);
        lblMotivo.applyProperties();
    }

    public TFString edtMotivo = new TFString();

    private void init_edtMotivo() {
        edtMotivo.setName("edtMotivo");
        edtMotivo.setLeft(0);
        edtMotivo.setTop(14);
        edtMotivo.setWidth(121);
        edtMotivo.setHeight(24);
        edtMotivo.setFlex(true);
        edtMotivo.setRequired(false);
        edtMotivo.setConstraintCheckWhen("cwImmediate");
        edtMotivo.setConstraintCheckType("ctExpression");
        edtMotivo.setConstraintFocusOnError(false);
        edtMotivo.setConstraintEnableUI(true);
        edtMotivo.setConstraintEnabled(false);
        edtMotivo.setConstraintFormCheck(true);
        edtMotivo.setCharCase("ccNormal");
        edtMotivo.setPwd(true);
        edtMotivo.setMaxlength(0);
        edtMotivo.setAlign("alLeft");
        edtMotivo.setFontColor("clWindowText");
        edtMotivo.setFontSize(-13);
        edtMotivo.setFontName("Tahoma");
        edtMotivo.setFontStyle("[]");
        edtMotivo.setSaveLiteralCharacter(false);
        edtMotivo.applyProperties();
        vBoxMotivo.addChildren(edtMotivo);
        addValidatable(edtMotivo);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}