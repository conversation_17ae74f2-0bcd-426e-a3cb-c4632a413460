package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmHomeCliente extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.HomeClienteRNA rn = null;

    public FrmHomeCliente() {
        try {
            rn = (freedom.bytecode.rn.HomeClienteRNA) getRN(freedom.bytecode.rn.wizard.HomeClienteRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbUserInformation();
        init_tbMenu();
        init_tbSistemaAcesso();
        init_tbClienteDiverso();
        init_tbCrmPartsFilaDaVez();
        init_tbClienteSelecionarEmpresas();
        init_tbMapa();
        init_popMenuLogin();
        init_mmPerfil();
        init_mmAlterarSenha();
        init_mmHelp();
        init_FMenuItem1();
        init_mmSair();
        init_imageList();
        init_FMenuItem2();
        init_FMenuItem3();
        init_FMenuItem4();
        init_FMenuItem5();
        init_FMenuItem6();
        init_FMenuItem7();
        init_FMenuItem8();
        init_FMenuItem9();
        init_Pesquisar();
        init_FMenuItem10();
        init_borderPanel();
        init_pnlTop();
        init_FHBox2();
        init_FVBox2();
        init_FHBox3();
        init_FGridPanel2();
        init_imgLogo();
        init_lblSistema();
        init_hbBoxVersao();
        init_FHBox9();
        init_lblVersao();
        init_FHBox4();
        init_FVBox3();
        init_FHBox5();
        init_hBoxPesquisas();
        init_gpPesqCodigo();
        init_FHBox8();
        init_imgBuscar();
        init_edtBuscarCodigo();
        init_btnSearchCodigo();
        init_vBoxNovoImage();
        init_iconClassNovo();
        init_FVBox1();
        init_FHBox7();
        init_FHBox1();
        init_imageUsuario();
        init_pnlCenter();
        init_pgctrlPrincipal();
        init_tabHome();
        init_FrmHomeCliente();
    }

    public USER_INFORMATION tbUserInformation;

    private void init_tbUserInformation() {
        tbUserInformation = rn.tbUserInformation;
        tbUserInformation.setName("tbUserInformation");
        tbUserInformation.setMaxRowCount(200);
        tbUserInformation.setWKey("340057;34001");
        tbUserInformation.setDeltaMode("dmChanged");
        getTables().put(tbUserInformation, "tbUserInformation");
        tbUserInformation.applyProperties();
    }

    public DBMENU tbMenu;

    private void init_tbMenu() {
        tbMenu = rn.tbMenu;
        tbMenu.setName("tbMenu");
        tbMenu.setMaxRowCount(200);
        tbMenu.setWKey("340057;34002");
        tbMenu.setDeltaMode("dmChanged");
        getTables().put(tbMenu, "tbMenu");
        tbMenu.applyProperties();
    }

    public SISTEMA_ACESSO tbSistemaAcesso;

    private void init_tbSistemaAcesso() {
        tbSistemaAcesso = rn.tbSistemaAcesso;
        tbSistemaAcesso.setName("tbSistemaAcesso");
        tbSistemaAcesso.setMaxRowCount(200);
        tbSistemaAcesso.setWKey("340057;34003");
        tbSistemaAcesso.setDeltaMode("dmChanged");
        getTables().put(tbSistemaAcesso, "tbSistemaAcesso");
        tbSistemaAcesso.applyProperties();
    }

    public CLIENTE_DIVERSO tbClienteDiverso;

    private void init_tbClienteDiverso() {
        tbClienteDiverso = rn.tbClienteDiverso;
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("340057;34005");
        tbClienteDiverso.setDeltaMode("dmChanged");
        getTables().put(tbClienteDiverso, "tbClienteDiverso");
        tbClienteDiverso.applyProperties();
    }

    public VW_CRM_PARTS_FILA_DA_VEZ tbCrmPartsFilaDaVez;

    private void init_tbCrmPartsFilaDaVez() {
        tbCrmPartsFilaDaVez = rn.tbCrmPartsFilaDaVez;
        tbCrmPartsFilaDaVez.setName("tbCrmPartsFilaDaVez");
        tbCrmPartsFilaDaVez.setMaxRowCount(200);
        tbCrmPartsFilaDaVez.setWKey("340057;34006");
        tbCrmPartsFilaDaVez.setDeltaMode("dmChanged");
        getTables().put(tbCrmPartsFilaDaVez, "tbCrmPartsFilaDaVez");
        tbCrmPartsFilaDaVez.applyProperties();
    }

    public CLIENTE_SELECIONAR_EMPRESAS tbClienteSelecionarEmpresas;

    private void init_tbClienteSelecionarEmpresas() {
        tbClienteSelecionarEmpresas = rn.tbClienteSelecionarEmpresas;
        tbClienteSelecionarEmpresas.setName("tbClienteSelecionarEmpresas");
        tbClienteSelecionarEmpresas.setMaxRowCount(200);
        tbClienteSelecionarEmpresas.setWKey("340057;34007");
        tbClienteSelecionarEmpresas.setDeltaMode("dmChanged");
        getTables().put(tbClienteSelecionarEmpresas, "tbClienteSelecionarEmpresas");
        tbClienteSelecionarEmpresas.applyProperties();
    }

    public ORC_MAPA tbMapa;

    private void init_tbMapa() {
        tbMapa = rn.tbMapa;
        tbMapa.setName("tbMapa");
        tbMapa.setMaxRowCount(200);
        tbMapa.setWKey("340057;34008");
        tbMapa.setDeltaMode("dmChanged");
        getTables().put(tbMapa, "tbMapa");
        tbMapa.applyProperties();
    }

    public TFPopupMenu popMenuLogin = new TFPopupMenu();

    private void init_popMenuLogin() {
        popMenuLogin.setName("popMenuLogin");
        FrmHomeCliente.addChildren(popMenuLogin);
        popMenuLogin.applyProperties();
    }

    public TFMenuItem mmPerfil = new TFMenuItem();

    private void init_mmPerfil() {
        mmPerfil.setName("mmPerfil");
        mmPerfil.setCaption("Perfil");
        mmPerfil.setImageIndex(310061);
        mmPerfil.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmPerfilClick(event);
            processarFlow("FrmHomeCliente", "mmPerfil", "OnClick");
        });
        mmPerfil.setAccess(false);
        mmPerfil.setCheckmark(false);
        popMenuLogin.addChildren(mmPerfil);
        mmPerfil.applyProperties();
    }

    public TFMenuItem mmAlterarSenha = new TFMenuItem();

    private void init_mmAlterarSenha() {
        mmAlterarSenha.setName("mmAlterarSenha");
        mmAlterarSenha.setCaption("Alterar Senha");
        mmAlterarSenha.setImageIndex(310067);
        mmAlterarSenha.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmAlterarSenhaClick(event);
            processarFlow("FrmHomeCliente", "mmAlterarSenha", "OnClick");
        });
        mmAlterarSenha.setAccess(false);
        mmAlterarSenha.setCheckmark(false);
        popMenuLogin.addChildren(mmAlterarSenha);
        mmAlterarSenha.applyProperties();
    }

    public TFMenuItem mmHelp = new TFMenuItem();

    private void init_mmHelp() {
        mmHelp.setName("mmHelp");
        mmHelp.setCaption("Help");
        mmHelp.setImageIndex(7000109);
        mmHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmHelpClick(event);
            processarFlow("FrmHomeCliente", "mmHelp", "OnClick");
        });
        mmHelp.setAccess(false);
        mmHelp.setCheckmark(false);
        popMenuLogin.addChildren(mmHelp);
        mmHelp.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("-");
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        popMenuLogin.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem mmSair = new TFMenuItem();

    private void init_mmSair() {
        mmSair.setName("mmSair");
        mmSair.setCaption("Sair");
        mmSair.setImageIndex(310060);
        mmSair.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSairClick(event);
            processarFlow("FrmHomeCliente", "mmSair", "OnClick");
        });
        mmSair.setAccess(false);
        mmSair.setCheckmark(false);
        popMenuLogin.addChildren(mmSair);
        mmSair.applyProperties();
    }

    public TFPopupMenu imageList = new TFPopupMenu();

    private void init_imageList() {
        imageList.setName("imageList");
        FrmHomeCliente.addChildren(imageList);
        imageList.applyProperties();
    }

    public TFMenuItem FMenuItem2 = new TFMenuItem();

    private void init_FMenuItem2() {
        FMenuItem2.setName("FMenuItem2");
        FMenuItem2.setCaption("ImagemUsuario");
        FMenuItem2.setImageIndex(700090);
        FMenuItem2.setAccess(false);
        FMenuItem2.setCheckmark(false);
        imageList.addChildren(FMenuItem2);
        FMenuItem2.applyProperties();
    }

    public TFMenuItem FMenuItem3 = new TFMenuItem();

    private void init_FMenuItem3() {
        FMenuItem3.setName("FMenuItem3");
        FMenuItem3.setCaption("ImagemSair");
        FMenuItem3.setImageIndex(310065);
        FMenuItem3.setAccess(false);
        FMenuItem3.setCheckmark(false);
        imageList.addChildren(FMenuItem3);
        FMenuItem3.applyProperties();
    }

    public TFMenuItem FMenuItem4 = new TFMenuItem();

    private void init_FMenuItem4() {
        FMenuItem4.setName("FMenuItem4");
        FMenuItem4.setCaption("Calendar");
        FMenuItem4.setImageIndex(310062);
        FMenuItem4.setAccess(false);
        FMenuItem4.setCheckmark(false);
        imageList.addChildren(FMenuItem4);
        FMenuItem4.applyProperties();
    }

    public TFMenuItem FMenuItem5 = new TFMenuItem();

    private void init_FMenuItem5() {
        FMenuItem5.setName("FMenuItem5");
        FMenuItem5.setCaption("Globo");
        FMenuItem5.setImageIndex(310063);
        FMenuItem5.setAccess(false);
        FMenuItem5.setCheckmark(false);
        imageList.addChildren(FMenuItem5);
        FMenuItem5.applyProperties();
    }

    public TFMenuItem FMenuItem6 = new TFMenuItem();

    private void init_FMenuItem6() {
        FMenuItem6.setName("FMenuItem6");
        FMenuItem6.setCaption("User Cicle");
        FMenuItem6.setImageIndex(310064);
        FMenuItem6.setAccess(false);
        FMenuItem6.setCheckmark(false);
        imageList.addChildren(FMenuItem6);
        FMenuItem6.applyProperties();
    }

    public TFMenuItem FMenuItem7 = new TFMenuItem();

    private void init_FMenuItem7() {
        FMenuItem7.setName("FMenuItem7");
        FMenuItem7.setCaption("Account Logout Black");
        FMenuItem7.setImageIndex(310065);
        FMenuItem7.setAccess(false);
        FMenuItem7.setCheckmark(false);
        imageList.addChildren(FMenuItem7);
        FMenuItem7.applyProperties();
    }

    public TFMenuItem FMenuItem8 = new TFMenuItem();

    private void init_FMenuItem8() {
        FMenuItem8.setName("FMenuItem8");
        FMenuItem8.setCaption("Version");
        FMenuItem8.setImageIndex(310066);
        FMenuItem8.setAccess(false);
        FMenuItem8.setCheckmark(false);
        imageList.addChildren(FMenuItem8);
        FMenuItem8.applyProperties();
    }

    public TFMenuItem FMenuItem9 = new TFMenuItem();

    private void init_FMenuItem9() {
        FMenuItem9.setName("FMenuItem9");
        FMenuItem9.setCaption("Alterar Senha");
        FMenuItem9.setImageIndex(310067);
        FMenuItem9.setAccess(false);
        FMenuItem9.setCheckmark(false);
        imageList.addChildren(FMenuItem9);
        FMenuItem9.applyProperties();
    }

    public TFMenuItem Pesquisar = new TFMenuItem();

    private void init_Pesquisar() {
        Pesquisar.setName("Pesquisar");
        Pesquisar.setCaption("Pesquiisar");
        Pesquisar.setImageIndex(700089);
        Pesquisar.setAccess(false);
        Pesquisar.setCheckmark(false);
        imageList.addChildren(Pesquisar);
        Pesquisar.applyProperties();
    }

    public TFMenuItem FMenuItem10 = new TFMenuItem();

    private void init_FMenuItem10() {
        FMenuItem10.setName("FMenuItem10");
        FMenuItem10.setCaption("LogoNbs");
        FMenuItem10.setImageIndex(310038);
        FMenuItem10.setAccess(false);
        FMenuItem10.setCheckmark(false);
        imageList.addChildren(FMenuItem10);
        FMenuItem10.applyProperties();
    }

    protected TFForm FrmHomeCliente = this;
    private void init_FrmHomeCliente() {
        FrmHomeCliente.setName("FrmHomeCliente");
        FrmHomeCliente.setCaption("Home");
        FrmHomeCliente.setClientHeight(494);
        FrmHomeCliente.setClientWidth(1248);
        FrmHomeCliente.setColor("clBtnFace");
        FrmHomeCliente.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmHomeCliente", "FrmHomeCliente", "OnCreate");
        });
        FrmHomeCliente.setWKey("340057");
        FrmHomeCliente.setSpacing(2);
        FrmHomeCliente.applyProperties();
    }

    public TFBorderPanel borderPanel = new TFBorderPanel();

    private void init_borderPanel() {
        borderPanel.setName("borderPanel");
        borderPanel.setLeft(0);
        borderPanel.setTop(0);
        borderPanel.setWidth(1248);
        borderPanel.setHeight(494);
        borderPanel.setNorth("pnlTop");
        borderPanel.setCenter("pnlCenter");
        borderPanel.setFlexVflex("ftTrue");
        borderPanel.setFlexHflex("ftTrue");
        borderPanel.setNorthCollapsible(false);
        borderPanel.setNorthSplittable(false);
        borderPanel.setNorthOpen(true);
        borderPanel.setSouthOpen(true);
        borderPanel.setEastOpen(true);
        borderPanel.setWestOpen(true);
        borderPanel.setSouthCollapsible(false);
        borderPanel.setSouthSplittable(false);
        borderPanel.setEastCollapsible(true);
        borderPanel.setEastSplittable(true);
        borderPanel.setWestCollapsible(true);
        borderPanel.setWestSplittable(true);
        borderPanel.setNorthSizePercent(0);
        borderPanel.setSouthSizePercent(0);
        borderPanel.setEastSizePercent(0);
        borderPanel.setWestSizePercent(0);
        FrmHomeCliente.addChildren(borderPanel);
        borderPanel.applyProperties();
    }

    public TFVBox pnlTop = new TFVBox();

    private void init_pnlTop() {
        pnlTop.setName("pnlTop");
        pnlTop.setLeft(0);
        pnlTop.setTop(0);
        pnlTop.setWidth(1244);
        pnlTop.setHeight(75);
        pnlTop.setBorderStyle("stBoxShadow");
        pnlTop.setPaddingTop(0);
        pnlTop.setPaddingLeft(0);
        pnlTop.setPaddingRight(0);
        pnlTop.setPaddingBottom(0);
        pnlTop.setMarginTop(0);
        pnlTop.setMarginLeft(1);
        pnlTop.setMarginRight(1);
        pnlTop.setMarginBottom(1);
        pnlTop.setSpacing(0);
        pnlTop.setFlexVflex("ftFalse");
        pnlTop.setFlexHflex("ftTrue");
        pnlTop.setScrollable(false);
        pnlTop.setBoxShadowConfigHorizontalLength(5);
        pnlTop.setBoxShadowConfigVerticalLength(0);
        pnlTop.setBoxShadowConfigBlurRadius(30);
        pnlTop.setBoxShadowConfigSpreadRadius(0);
        pnlTop.setBoxShadowConfigShadowColor("clBlack");
        pnlTop.setBoxShadowConfigOpacity(8);
        borderPanel.addChildren(pnlTop);
        pnlTop.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(1241);
        FHBox2.setHeight(64);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        pnlTop.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(248);
        FVBox2.setHeight(60);
        FVBox2.setBorderStyle("stBoxShadow");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(5);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(5);
        FVBox2.setBoxShadowConfigVerticalLength(5);
        FVBox2.setBoxShadowConfigBlurRadius(64);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(8);
        FHBox2.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(185);
        FHBox3.setHeight(16);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FVBox2.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFGridPanel FGridPanel2 = new TFGridPanel();

    private void init_FGridPanel2() {
        FGridPanel2.setName("FGridPanel2");
        FGridPanel2.setLeft(0);
        FGridPanel2.setTop(17);
        FGridPanel2.setWidth(242);
        FGridPanel2.setHeight(30);
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(35.580564301561310000);
        FGridPanel2.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(64.419435698438690000);
        FGridPanel2.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("imgLogo");
        item2.setRow(0);
        FGridPanel2.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("lblSistema");
        item3.setRow(0);
        FGridPanel2.getControlCollection().add(item3);
        TFGridPanelRow item4 = new TFGridPanelRow();
        item4.setValue(100.000000000000000000);
        FGridPanel2.getRowCollection().add(item4);
        TFGridPanelRow item5 = new TFGridPanelRow();
        item5.setSizeStyle("ssAuto");
        FGridPanel2.getRowCollection().add(item5);
        FGridPanel2.setFlexVflex("ftFalse");
        FGridPanel2.setFlexHflex("ftTrue");
        FGridPanel2.setAllRowFlex(false);
        FGridPanel2.setColumnTabOrder(false);
        FVBox2.addChildren(FGridPanel2);
        FGridPanel2.applyProperties();
    }

    public TFImage imgLogo = new TFImage();

    private void init_imgLogo() {
        imgLogo.setName("imgLogo");
        imgLogo.setLeft(41);
        imgLogo.setTop(1);
        imgLogo.setWidth(45);
        imgLogo.setHeight(28);
        imgLogo.setAlign("alRight");
        imgLogo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            imgLogoClick(event);
            processarFlow("FrmHomeCliente", "imgLogo", "OnClick");
        });
        imgLogo.setImageSrc("/images/crmparts310038.png");
        imgLogo.setBoxSize(0);
        imgLogo.setGrayScaleOnDisable(false);
        FGridPanel2.addChildren(imgLogo);
        imgLogo.applyProperties();
    }

    public TFLabel lblSistema = new TFLabel();

    private void init_lblSistema() {
        lblSistema.setName("lblSistema");
        lblSistema.setLeft(86);
        lblSistema.setTop(1);
        lblSistema.setWidth(133);
        lblSistema.setHeight(33);
        lblSistema.setAlign("alLeft");
        lblSistema.setCaption("CRMParts");
        lblSistema.setFontColor("clWindowText");
        lblSistema.setFontSize(-27);
        lblSistema.setFontName("Tahoma");
        lblSistema.setFontStyle("[fsBold]");
        lblSistema.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblSistemaClick(event);
            processarFlow("FrmHomeCliente", "lblSistema", "OnClick");
        });
        lblSistema.setVerticalAlignment("taVerticalCenter");
        FGridPanel2.addChildren(lblSistema);
        lblSistema.applyProperties();
    }

    public TFHBox hbBoxVersao = new TFHBox();

    private void init_hbBoxVersao() {
        hbBoxVersao.setName("hbBoxVersao");
        hbBoxVersao.setLeft(0);
        hbBoxVersao.setTop(48);
        hbBoxVersao.setWidth(242);
        hbBoxVersao.setHeight(16);
        hbBoxVersao.setBorderStyle("stNone");
        hbBoxVersao.setPaddingTop(0);
        hbBoxVersao.setPaddingLeft(0);
        hbBoxVersao.setPaddingRight(26);
        hbBoxVersao.setPaddingBottom(0);
        hbBoxVersao.setMarginTop(0);
        hbBoxVersao.setMarginLeft(0);
        hbBoxVersao.setMarginRight(0);
        hbBoxVersao.setMarginBottom(0);
        hbBoxVersao.setSpacing(1);
        hbBoxVersao.setFlexVflex("ftFalse");
        hbBoxVersao.setFlexHflex("ftFalse");
        hbBoxVersao.setScrollable(false);
        hbBoxVersao.setBoxShadowConfigHorizontalLength(10);
        hbBoxVersao.setBoxShadowConfigVerticalLength(10);
        hbBoxVersao.setBoxShadowConfigBlurRadius(5);
        hbBoxVersao.setBoxShadowConfigSpreadRadius(0);
        hbBoxVersao.setBoxShadowConfigShadowColor("clBlack");
        hbBoxVersao.setBoxShadowConfigOpacity(75);
        FVBox2.addChildren(hbBoxVersao);
        hbBoxVersao.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(16);
        FHBox9.setHeight(8);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        hbBoxVersao.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFLabel lblVersao = new TFLabel();

    private void init_lblVersao() {
        lblVersao.setName("lblVersao");
        lblVersao.setLeft(16);
        lblVersao.setTop(0);
        lblVersao.setWidth(42);
        lblVersao.setHeight(13);
        lblVersao.setAlign("alRight");
        lblVersao.setCaption("Vers\u00E3o:");
        lblVersao.setFontColor("clBlack");
        lblVersao.setFontSize(-11);
        lblVersao.setFontName("Tahoma");
        lblVersao.setFontStyle("[fsBold]");
        lblVersao.setVerticalAlignment("taVerticalCenter");
        hbBoxVersao.addChildren(lblVersao);
        lblVersao.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(248);
        FHBox4.setTop(0);
        FHBox4.setWidth(987);
        FHBox4.setHeight(62);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftTrue");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(10);
        FHBox2.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(900);
        FVBox3.setHeight(61);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(64);
        FHBox5.setHeight(20);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox hBoxPesquisas = new TFHBox();

    private void init_hBoxPesquisas() {
        hBoxPesquisas.setName("hBoxPesquisas");
        hBoxPesquisas.setLeft(0);
        hBoxPesquisas.setTop(21);
        hBoxPesquisas.setWidth(894);
        hBoxPesquisas.setHeight(32);
        hBoxPesquisas.setBorderStyle("stNone");
        hBoxPesquisas.setPaddingTop(0);
        hBoxPesquisas.setPaddingLeft(0);
        hBoxPesquisas.setPaddingRight(0);
        hBoxPesquisas.setPaddingBottom(0);
        hBoxPesquisas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxPesquisasClick(event);
            processarFlow("FrmHomeCliente", "hBoxPesquisas", "OnClick");
        });
        hBoxPesquisas.setMarginTop(0);
        hBoxPesquisas.setMarginLeft(0);
        hBoxPesquisas.setMarginRight(0);
        hBoxPesquisas.setMarginBottom(0);
        hBoxPesquisas.setSpacing(1);
        hBoxPesquisas.setFlexVflex("ftFalse");
        hBoxPesquisas.setFlexHflex("ftTrue");
        hBoxPesquisas.setScrollable(false);
        hBoxPesquisas.setBoxShadowConfigHorizontalLength(10);
        hBoxPesquisas.setBoxShadowConfigVerticalLength(10);
        hBoxPesquisas.setBoxShadowConfigBlurRadius(5);
        hBoxPesquisas.setBoxShadowConfigSpreadRadius(0);
        hBoxPesquisas.setBoxShadowConfigShadowColor("clBlack");
        hBoxPesquisas.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(hBoxPesquisas);
        hBoxPesquisas.applyProperties();
    }

    public TFGridPanel gpPesqCodigo = new TFGridPanel();

    private void init_gpPesqCodigo() {
        gpPesqCodigo.setName("gpPesqCodigo");
        gpPesqCodigo.setLeft(0);
        gpPesqCodigo.setTop(0);
        gpPesqCodigo.setWidth(593);
        gpPesqCodigo.setHeight(36);
        TFGridPanelColumn item6 = new TFGridPanelColumn();
        item6.setSizeStyle("ssAbsolute");
        item6.setValue(20.000000000000000000);
        gpPesqCodigo.getColumnCollection().add(item6);
        TFGridPanelColumn item7 = new TFGridPanelColumn();
        item7.setSizeStyle("ssAbsolute");
        item7.setValue(24.000000000000000000);
        gpPesqCodigo.getColumnCollection().add(item7);
        TFGridPanelColumn item8 = new TFGridPanelColumn();
        item8.setSizeStyle("ssAuto");
        item8.setValue(50.000000000000000000);
        gpPesqCodigo.getColumnCollection().add(item8);
        TFGridPanelColumn item9 = new TFGridPanelColumn();
        item9.setValue(100.000000000000000000);
        gpPesqCodigo.getColumnCollection().add(item9);
        TFGridPanelColumn item10 = new TFGridPanelColumn();
        item10.setSizeStyle("ssAbsolute");
        item10.setValue(50.000000000000000000);
        gpPesqCodigo.getColumnCollection().add(item10);
        TFControlItem item11 = new TFControlItem();
        item11.setColumn(0);
        item11.setControl("FHBox8");
        item11.setRow(0);
        gpPesqCodigo.getControlCollection().add(item11);
        TFControlItem item12 = new TFControlItem();
        item12.setColumn(1);
        item12.setControl("imgBuscar");
        item12.setRow(0);
        gpPesqCodigo.getControlCollection().add(item12);
        TFControlItem item13 = new TFControlItem();
        item13.setColumn(2);
        item13.setControl("edtBuscarCodigo");
        item13.setRow(0);
        gpPesqCodigo.getControlCollection().add(item13);
        TFControlItem item14 = new TFControlItem();
        item14.setColumn(3);
        item14.setControl("btnSearchCodigo");
        item14.setRow(0);
        gpPesqCodigo.getControlCollection().add(item14);
        TFControlItem item15 = new TFControlItem();
        item15.setColumn(4);
        item15.setControl("vBoxNovoImage");
        item15.setRow(0);
        gpPesqCodigo.getControlCollection().add(item15);
        TFGridPanelRow item16 = new TFGridPanelRow();
        item16.setSizeStyle("ssAuto");
        gpPesqCodigo.getRowCollection().add(item16);
        TFGridPanelRow item17 = new TFGridPanelRow();
        item17.setSizeStyle("ssAuto");
        gpPesqCodigo.getRowCollection().add(item17);
        gpPesqCodigo.setFlexVflex("ftFalse");
        gpPesqCodigo.setFlexHflex("ftTrue");
        gpPesqCodigo.setAllRowFlex(false);
        gpPesqCodigo.setColumnTabOrder(false);
        hBoxPesquisas.addChildren(gpPesqCodigo);
        gpPesqCodigo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(1);
        FHBox8.setTop(17);
        FHBox8.setWidth(20);
        FHBox8.setHeight(8);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        gpPesqCodigo.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFImage imgBuscar = new TFImage();

    private void init_imgBuscar() {
        imgBuscar.setName("imgBuscar");
        imgBuscar.setLeft(21);
        imgBuscar.setTop(9);
        imgBuscar.setWidth(24);
        imgBuscar.setHeight(25);
        imgBuscar.setImageSrc("/images/700089.png");
        imgBuscar.setBoxSize(0);
        imgBuscar.setGrayScaleOnDisable(false);
        gpPesqCodigo.addChildren(imgBuscar);
        imgBuscar.applyProperties();
    }

    public TFString edtBuscarCodigo = new TFString();

    private void init_edtBuscarCodigo() {
        edtBuscarCodigo.setName("edtBuscarCodigo");
        edtBuscarCodigo.setLeft(45);
        edtBuscarCodigo.setTop(1);
        edtBuscarCodigo.setWidth(324);
        edtBuscarCodigo.setHeight(41);
        edtBuscarCodigo.setFlex(false);
        edtBuscarCodigo.setRequired(false);
        edtBuscarCodigo.setPrompt("Buscar por C\u00F3digo/Descri\u00E7\u00E3o Item");
        edtBuscarCodigo.setConstraintCheckWhen("cwImmediate");
        edtBuscarCodigo.setConstraintCheckType("ctExpression");
        edtBuscarCodigo.setConstraintFocusOnError(false);
        edtBuscarCodigo.setConstraintEnableUI(true);
        edtBuscarCodigo.setConstraintEnabled(false);
        edtBuscarCodigo.setConstraintFormCheck(true);
        edtBuscarCodigo.setCharCase("ccNormal");
        edtBuscarCodigo.setPwd(false);
        edtBuscarCodigo.setMaxlength(0);
        edtBuscarCodigo.setAlign("alLeft");
        edtBuscarCodigo.setFontColor("clWindowText");
        edtBuscarCodigo.setFontSize(-13);
        edtBuscarCodigo.setFontName("Tahoma");
        edtBuscarCodigo.setFontStyle("[]");
        edtBuscarCodigo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtBuscarCodigoEnter(event);
            processarFlow("FrmHomeCliente", "edtBuscarCodigo", "OnEnter");
        });
        edtBuscarCodigo.addEventListener("onChanging", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtBuscarCodigoChanging(event);
            processarFlow("FrmHomeCliente", "edtBuscarCodigo", "OnChanging");
        });
        edtBuscarCodigo.setSaveLiteralCharacter(false);
        edtBuscarCodigo.applyProperties();
        gpPesqCodigo.addChildren(edtBuscarCodigo);
        addValidatable(edtBuscarCodigo);
    }

    public TFButton btnSearchCodigo = new TFButton();

    private void init_btnSearchCodigo() {
        btnSearchCodigo.setName("btnSearchCodigo");
        btnSearchCodigo.setLeft(369);
        btnSearchCodigo.setTop(1);
        btnSearchCodigo.setWidth(75);
        btnSearchCodigo.setHeight(41);
        btnSearchCodigo.setAlign("alLeft");
        btnSearchCodigo.setCaption("Buscar");
        btnSearchCodigo.setFontColor("clWindowText");
        btnSearchCodigo.setFontSize(-11);
        btnSearchCodigo.setFontName("Tahoma");
        btnSearchCodigo.setFontStyle("[]");
        btnSearchCodigo.setVisible(false);
        btnSearchCodigo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSearchCodigoClick(event);
            processarFlow("FrmHomeCliente", "btnSearchCodigo", "OnClick");
        });
        btnSearchCodigo.setImageId(0);
        btnSearchCodigo.setColor("clBtnFace");
        btnSearchCodigo.setAccess(false);
        btnSearchCodigo.setIconReverseDirection(false);
        gpPesqCodigo.addChildren(btnSearchCodigo);
        btnSearchCodigo.applyProperties();
    }

    public TFVBox vBoxNovoImage = new TFVBox();

    private void init_vBoxNovoImage() {
        vBoxNovoImage.setName("vBoxNovoImage");
        vBoxNovoImage.setLeft(542);
        vBoxNovoImage.setTop(1);
        vBoxNovoImage.setWidth(44);
        vBoxNovoImage.setHeight(41);
        vBoxNovoImage.setAlign("alLeft");
        vBoxNovoImage.setBorderStyle("stNone");
        vBoxNovoImage.setPaddingTop(6);
        vBoxNovoImage.setPaddingLeft(0);
        vBoxNovoImage.setPaddingRight(0);
        vBoxNovoImage.setPaddingBottom(0);
        vBoxNovoImage.setMarginTop(0);
        vBoxNovoImage.setMarginLeft(0);
        vBoxNovoImage.setMarginRight(0);
        vBoxNovoImage.setMarginBottom(0);
        vBoxNovoImage.setSpacing(1);
        vBoxNovoImage.setFlexVflex("ftTrue");
        vBoxNovoImage.setFlexHflex("ftTrue");
        vBoxNovoImage.setScrollable(false);
        vBoxNovoImage.setBoxShadowConfigHorizontalLength(10);
        vBoxNovoImage.setBoxShadowConfigVerticalLength(10);
        vBoxNovoImage.setBoxShadowConfigBlurRadius(5);
        vBoxNovoImage.setBoxShadowConfigSpreadRadius(0);
        vBoxNovoImage.setBoxShadowConfigShadowColor("clBlack");
        vBoxNovoImage.setBoxShadowConfigOpacity(75);
        gpPesqCodigo.addChildren(vBoxNovoImage);
        vBoxNovoImage.applyProperties();
    }

    public TFIconClass iconClassNovo = new TFIconClass();

    private void init_iconClassNovo() {
        iconClassNovo.setName("iconClassNovo");
        iconClassNovo.setLeft(0);
        iconClassNovo.setTop(0);
        iconClassNovo.setHint("Novo Or\u00E7amento");
        iconClassNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassNovoClick(event);
            processarFlow("FrmHomeCliente", "iconClassNovo", "OnClick");
        });
        iconClassNovo.setIconClass("user-plus");
        iconClassNovo.setSize(26);
        iconClassNovo.setColor("6776679");
        vBoxNovoImage.addChildren(iconClassNovo);
        iconClassNovo.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(900);
        FVBox1.setTop(0);
        FVBox1.setWidth(77);
        FVBox1.setHeight(61);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(64);
        FHBox7.setHeight(22);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(23);
        FHBox1.setWidth(72);
        FHBox1.setHeight(34);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFImage imageUsuario = new TFImage();

    private void init_imageUsuario() {
        imageUsuario.setName("imageUsuario");
        imageUsuario.setLeft(0);
        imageUsuario.setTop(0);
        imageUsuario.setWidth(30);
        imageUsuario.setHeight(30);
        imageUsuario.setHint("Usu\u00E1rio");
        imageUsuario.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            imageUsuarioClick(event);
            processarFlow("FrmHomeCliente", "imageUsuario", "OnClick");
        });
        imageUsuario.setImageSrc("/images/700090.png");
        imageUsuario.setBoxSize(0);
        imageUsuario.setGrayScaleOnDisable(false);
        FHBox1.addChildren(imageUsuario);
        imageUsuario.applyProperties();
    }

    public TFVBox pnlCenter = new TFVBox();

    private void init_pnlCenter() {
        pnlCenter.setName("pnlCenter");
        pnlCenter.setLeft(2);
        pnlCenter.setTop(76);
        pnlCenter.setWidth(572);
        pnlCenter.setHeight(389);
        pnlCenter.setBorderStyle("stNone");
        pnlCenter.setPaddingTop(0);
        pnlCenter.setPaddingLeft(1);
        pnlCenter.setPaddingRight(0);
        pnlCenter.setPaddingBottom(0);
        pnlCenter.setMarginTop(0);
        pnlCenter.setMarginLeft(0);
        pnlCenter.setMarginRight(0);
        pnlCenter.setMarginBottom(0);
        pnlCenter.setSpacing(1);
        pnlCenter.setFlexVflex("ftTrue");
        pnlCenter.setFlexHflex("ftTrue");
        pnlCenter.setScrollable(false);
        pnlCenter.setBoxShadowConfigHorizontalLength(10);
        pnlCenter.setBoxShadowConfigVerticalLength(10);
        pnlCenter.setBoxShadowConfigBlurRadius(5);
        pnlCenter.setBoxShadowConfigSpreadRadius(0);
        pnlCenter.setBoxShadowConfigShadowColor("clBlack");
        pnlCenter.setBoxShadowConfigOpacity(75);
        borderPanel.addChildren(pnlCenter);
        pnlCenter.applyProperties();
    }

    public TFPageControl pgctrlPrincipal = new TFPageControl();

    private void init_pgctrlPrincipal() {
        pgctrlPrincipal.setName("pgctrlPrincipal");
        pgctrlPrincipal.setLeft(0);
        pgctrlPrincipal.setTop(0);
        pgctrlPrincipal.setWidth(555);
        pgctrlPrincipal.setHeight(351);
        pgctrlPrincipal.setTabPosition("tpTop");
        pgctrlPrincipal.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgctrlPrincipalChange(event);
            processarFlow("FrmHomeCliente", "pgctrlPrincipal", "OnChange");
        });
        pgctrlPrincipal.setFlexVflex("ftTrue");
        pgctrlPrincipal.setFlexHflex("ftTrue");
        pgctrlPrincipal.setRenderStyle("rsTabbed");
        pgctrlPrincipal.applyProperties();
        pnlCenter.addChildren(pgctrlPrincipal);
    }

    public TFTabsheet tabHome = new TFTabsheet();

    private void init_tabHome() {
        tabHome.setName("tabHome");
        tabHome.setCaption("Or\u00E7amentos do cliente: ");
        tabHome.setClosable(false);
        pgctrlPrincipal.addChildren(tabHome);
        tabHome.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void imgLogoClick(final Event<Object> event);

    public abstract void lblSistemaClick(final Event<Object> event);

    public abstract void hBoxPesquisasClick(final Event<Object> event);

    public abstract void edtBuscarCodigoEnter(final Event<Object> event);

    public abstract void edtBuscarCodigoChanging(final Event<Object> event);

    public void btnSearchCodigoClick(final Event<Object> event) {
        if (btnSearchCodigo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSearchCodigo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void iconClassNovoClick(final Event<Object> event);

    public abstract void imageUsuarioClick(final Event<Object> event);

    public abstract void pgctrlPrincipalChange(final Event<Object> event);

    public abstract void mmPerfilClick(final Event<Object> event);

    public abstract void mmAlterarSenhaClick(final Event<Object> event);

    public abstract void mmHelpClick(final Event<Object> event);

    public abstract void mmSairClick(final Event<Object> event);

}