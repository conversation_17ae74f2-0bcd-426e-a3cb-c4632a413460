package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPesquisaTransportadora extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PesquisaTransportadoraRNA rn = null;

    public FrmPesquisaTransportadora() {
        try {
            rn = (freedom.bytecode.rn.PesquisaTransportadoraRNA) getRN(freedom.bytecode.rn.wizard.PesquisaTransportadoraRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbTransportadoras();
        init_FVBox1();
        init_FHBox1();
        init_hBoxLinha01();
        init_FHBox7();
        init_btnVoltar();
        init_FHBox9();
        init_btnPesquisar();
        init_FHBox14();
        init_btnAceitar();
        init_FHBox8();
        init_FHBox10();
        init_FHBox2();
        init_FHBox3();
        init_vBoxDescricao();
        init_lblDescricao();
        init_edtDescricao();
        init_FHBox4();
        init_vBoxCNPJCPF();
        init_lblCNPJCPF();
        init_edtCNPJCPF();
        init_FHBox5();
        init_vBoxCodigo();
        init_lblCodigo();
        init_edtCodigo();
        init_FHBox6();
        init_FHBox11();
        init_FHBox12();
        init_FHBox15();
        init_gridTransportadora();
        init_FHBox16();
        init_FHBox13();
        init_scTransportadoras();
        init_FrmPesquisaTransportadora();
    }

    public TRANSPORTADORAS tbTransportadoras;

    private void init_tbTransportadoras() {
        tbTransportadoras = rn.tbTransportadoras;
        tbTransportadoras.setName("tbTransportadoras");
        tbTransportadoras.setMaxRowCount(200);
        tbTransportadoras.setWKey("1610882;16101");
        tbTransportadoras.setRatioBatchSize(20);
        getTables().put(tbTransportadoras, "tbTransportadoras");
        tbTransportadoras.applyProperties();
    }

    protected TFForm FrmPesquisaTransportadora = this;
    private void init_FrmPesquisaTransportadora() {
        FrmPesquisaTransportadora.setName("FrmPesquisaTransportadora");
        FrmPesquisaTransportadora.setCaption("Pesquisar transportadora");
        FrmPesquisaTransportadora.setClientHeight(295);
        FrmPesquisaTransportadora.setClientWidth(584);
        FrmPesquisaTransportadora.setColor("clBtnFace");
        FrmPesquisaTransportadora.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmPesquisaTransportadora", "FrmPesquisaTransportadora", "OnCreate");
        });
        FrmPesquisaTransportadora.setWOrigem("EhMain");
        FrmPesquisaTransportadora.setWKey("1610882");
        FrmPesquisaTransportadora.setSpacing(0);
        FrmPesquisaTransportadora.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(584);
        FVBox1.setHeight(295);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmPesquisaTransportadora.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(570);
        FHBox1.setHeight(5);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox hBoxLinha01 = new TFHBox();

    private void init_hBoxLinha01() {
        hBoxLinha01.setName("hBoxLinha01");
        hBoxLinha01.setLeft(0);
        hBoxLinha01.setTop(6);
        hBoxLinha01.setWidth(570);
        hBoxLinha01.setHeight(63);
        hBoxLinha01.setBorderStyle("stNone");
        hBoxLinha01.setPaddingTop(0);
        hBoxLinha01.setPaddingLeft(0);
        hBoxLinha01.setPaddingRight(0);
        hBoxLinha01.setPaddingBottom(0);
        hBoxLinha01.setMarginTop(0);
        hBoxLinha01.setMarginLeft(0);
        hBoxLinha01.setMarginRight(0);
        hBoxLinha01.setMarginBottom(0);
        hBoxLinha01.setSpacing(1);
        hBoxLinha01.setFlexVflex("ftMin");
        hBoxLinha01.setFlexHflex("ftTrue");
        hBoxLinha01.setScrollable(false);
        hBoxLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha01.setBoxShadowConfigOpacity(75);
        hBoxLinha01.setVAlign("tvTop");
        FVBox1.addChildren(hBoxLinha01);
        hBoxLinha01.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(5);
        FHBox7.setHeight(20);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(5);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(53);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmPesquisaTransportadora", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(70);
        FHBox9.setTop(0);
        FHBox9.setWidth(5);
        FHBox9.setHeight(20);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(75);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(65);
        btnPesquisar.setHeight(53);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmPesquisaTransportadora", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(13);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(140);
        FHBox14.setTop(0);
        FHBox14.setWidth(5);
        FHBox14.setHeight(20);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(145);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(65);
        btnAceitar.setHeight(53);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmPesquisaTransportadora", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(4600376);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(210);
        FHBox8.setTop(0);
        FHBox8.setWidth(5);
        FHBox8.setHeight(20);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(70);
        FHBox10.setWidth(570);
        FHBox10.setHeight(5);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FVBox1.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(76);
        FHBox2.setWidth(570);
        FHBox2.setHeight(60);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(5);
        FHBox3.setHeight(20);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox2.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox vBoxDescricao = new TFVBox();

    private void init_vBoxDescricao() {
        vBoxDescricao.setName("vBoxDescricao");
        vBoxDescricao.setLeft(5);
        vBoxDescricao.setTop(0);
        vBoxDescricao.setWidth(200);
        vBoxDescricao.setHeight(50);
        vBoxDescricao.setBorderStyle("stNone");
        vBoxDescricao.setPaddingTop(0);
        vBoxDescricao.setPaddingLeft(0);
        vBoxDescricao.setPaddingRight(0);
        vBoxDescricao.setPaddingBottom(0);
        vBoxDescricao.setMarginTop(0);
        vBoxDescricao.setMarginLeft(0);
        vBoxDescricao.setMarginRight(0);
        vBoxDescricao.setMarginBottom(0);
        vBoxDescricao.setSpacing(1);
        vBoxDescricao.setFlexVflex("ftMin");
        vBoxDescricao.setFlexHflex("ftTrue");
        vBoxDescricao.setScrollable(false);
        vBoxDescricao.setBoxShadowConfigHorizontalLength(10);
        vBoxDescricao.setBoxShadowConfigVerticalLength(10);
        vBoxDescricao.setBoxShadowConfigBlurRadius(5);
        vBoxDescricao.setBoxShadowConfigSpreadRadius(0);
        vBoxDescricao.setBoxShadowConfigShadowColor("clBlack");
        vBoxDescricao.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(vBoxDescricao);
        vBoxDescricao.applyProperties();
    }

    public TFLabel lblDescricao = new TFLabel();

    private void init_lblDescricao() {
        lblDescricao.setName("lblDescricao");
        lblDescricao.setLeft(0);
        lblDescricao.setTop(0);
        lblDescricao.setWidth(46);
        lblDescricao.setHeight(13);
        lblDescricao.setCaption("Descri\u00E7\u00E3o");
        lblDescricao.setFontColor("clWindowText");
        lblDescricao.setFontSize(-11);
        lblDescricao.setFontName("Tahoma");
        lblDescricao.setFontStyle("[]");
        lblDescricao.setVerticalAlignment("taVerticalCenter");
        lblDescricao.setWordBreak(false);
        vBoxDescricao.addChildren(lblDescricao);
        lblDescricao.applyProperties();
    }

    public TFString edtDescricao = new TFString();

    private void init_edtDescricao() {
        edtDescricao.setName("edtDescricao");
        edtDescricao.setLeft(0);
        edtDescricao.setTop(14);
        edtDescricao.setWidth(190);
        edtDescricao.setHeight(24);
        edtDescricao.setHint("Descri\u00E7\u00E3o");
        edtDescricao.setHelpCaption("Descri\u00E7\u00E3o");
        edtDescricao.setFlex(true);
        edtDescricao.setRequired(false);
        edtDescricao.setPrompt("Descri\u00E7\u00E3o");
        edtDescricao.setConstraintCheckWhen("cwImmediate");
        edtDescricao.setConstraintCheckType("ctExpression");
        edtDescricao.setConstraintFocusOnError(false);
        edtDescricao.setConstraintEnableUI(true);
        edtDescricao.setConstraintEnabled(false);
        edtDescricao.setConstraintFormCheck(true);
        edtDescricao.setCharCase("ccNormal");
        edtDescricao.setPwd(false);
        edtDescricao.setMaxlength(0);
        edtDescricao.setFontColor("clWindowText");
        edtDescricao.setFontSize(-13);
        edtDescricao.setFontName("Tahoma");
        edtDescricao.setFontStyle("[]");
        edtDescricao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtDescricaoEnter(event);
            processarFlow("FrmPesquisaTransportadora", "edtDescricao", "OnEnter");
        });
        edtDescricao.setSaveLiteralCharacter(false);
        edtDescricao.applyProperties();
        vBoxDescricao.addChildren(edtDescricao);
        addValidatable(edtDescricao);
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(205);
        FHBox4.setTop(0);
        FHBox4.setWidth(5);
        FHBox4.setHeight(20);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox2.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox vBoxCNPJCPF = new TFVBox();

    private void init_vBoxCNPJCPF() {
        vBoxCNPJCPF.setName("vBoxCNPJCPF");
        vBoxCNPJCPF.setLeft(210);
        vBoxCNPJCPF.setTop(0);
        vBoxCNPJCPF.setWidth(130);
        vBoxCNPJCPF.setHeight(50);
        vBoxCNPJCPF.setBorderStyle("stNone");
        vBoxCNPJCPF.setPaddingTop(0);
        vBoxCNPJCPF.setPaddingLeft(0);
        vBoxCNPJCPF.setPaddingRight(0);
        vBoxCNPJCPF.setPaddingBottom(0);
        vBoxCNPJCPF.setMarginTop(0);
        vBoxCNPJCPF.setMarginLeft(0);
        vBoxCNPJCPF.setMarginRight(0);
        vBoxCNPJCPF.setMarginBottom(0);
        vBoxCNPJCPF.setSpacing(1);
        vBoxCNPJCPF.setFlexVflex("ftMin");
        vBoxCNPJCPF.setFlexHflex("ftMin");
        vBoxCNPJCPF.setScrollable(false);
        vBoxCNPJCPF.setBoxShadowConfigHorizontalLength(10);
        vBoxCNPJCPF.setBoxShadowConfigVerticalLength(10);
        vBoxCNPJCPF.setBoxShadowConfigBlurRadius(5);
        vBoxCNPJCPF.setBoxShadowConfigSpreadRadius(0);
        vBoxCNPJCPF.setBoxShadowConfigShadowColor("clBlack");
        vBoxCNPJCPF.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(vBoxCNPJCPF);
        vBoxCNPJCPF.applyProperties();
    }

    public TFLabel lblCNPJCPF = new TFLabel();

    private void init_lblCNPJCPF() {
        lblCNPJCPF.setName("lblCNPJCPF");
        lblCNPJCPF.setLeft(0);
        lblCNPJCPF.setTop(0);
        lblCNPJCPF.setWidth(48);
        lblCNPJCPF.setHeight(13);
        lblCNPJCPF.setCaption("CNPJ/CPF");
        lblCNPJCPF.setFontColor("clWindowText");
        lblCNPJCPF.setFontSize(-11);
        lblCNPJCPF.setFontName("Tahoma");
        lblCNPJCPF.setFontStyle("[]");
        lblCNPJCPF.setVerticalAlignment("taVerticalCenter");
        lblCNPJCPF.setWordBreak(false);
        vBoxCNPJCPF.addChildren(lblCNPJCPF);
        lblCNPJCPF.applyProperties();
    }

    public TFString edtCNPJCPF = new TFString();

    private void init_edtCNPJCPF() {
        edtCNPJCPF.setName("edtCNPJCPF");
        edtCNPJCPF.setLeft(0);
        edtCNPJCPF.setTop(14);
        edtCNPJCPF.setWidth(120);
        edtCNPJCPF.setHeight(24);
        edtCNPJCPF.setHint("CNPJ/CPF");
        edtCNPJCPF.setHelpCaption("CNPJ/CPF");
        edtCNPJCPF.setFlex(true);
        edtCNPJCPF.setRequired(false);
        edtCNPJCPF.setPrompt("CNPJ/CPF");
        edtCNPJCPF.setConstraintCheckWhen("cwImmediate");
        edtCNPJCPF.setConstraintCheckType("ctExpression");
        edtCNPJCPF.setConstraintFocusOnError(false);
        edtCNPJCPF.setConstraintEnableUI(true);
        edtCNPJCPF.setConstraintEnabled(false);
        edtCNPJCPF.setConstraintFormCheck(true);
        edtCNPJCPF.setCharCase("ccNormal");
        edtCNPJCPF.setPwd(false);
        edtCNPJCPF.setMaxlength(0);
        edtCNPJCPF.setFontColor("clWindowText");
        edtCNPJCPF.setFontSize(-13);
        edtCNPJCPF.setFontName("Tahoma");
        edtCNPJCPF.setFontStyle("[]");
        edtCNPJCPF.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCNPJCPFChange(event);
            processarFlow("FrmPesquisaTransportadora", "edtCNPJCPF", "OnChange");
        });
        edtCNPJCPF.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCNPJCPFEnter(event);
            processarFlow("FrmPesquisaTransportadora", "edtCNPJCPF", "OnEnter");
        });
        edtCNPJCPF.setSaveLiteralCharacter(false);
        edtCNPJCPF.applyProperties();
        vBoxCNPJCPF.addChildren(edtCNPJCPF);
        addValidatable(edtCNPJCPF);
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(340);
        FHBox5.setTop(0);
        FHBox5.setWidth(5);
        FHBox5.setHeight(20);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox2.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFVBox vBoxCodigo = new TFVBox();

    private void init_vBoxCodigo() {
        vBoxCodigo.setName("vBoxCodigo");
        vBoxCodigo.setLeft(345);
        vBoxCodigo.setTop(0);
        vBoxCodigo.setWidth(110);
        vBoxCodigo.setHeight(50);
        vBoxCodigo.setBorderStyle("stNone");
        vBoxCodigo.setPaddingTop(0);
        vBoxCodigo.setPaddingLeft(0);
        vBoxCodigo.setPaddingRight(0);
        vBoxCodigo.setPaddingBottom(0);
        vBoxCodigo.setMarginTop(0);
        vBoxCodigo.setMarginLeft(0);
        vBoxCodigo.setMarginRight(0);
        vBoxCodigo.setMarginBottom(0);
        vBoxCodigo.setSpacing(1);
        vBoxCodigo.setFlexVflex("ftMin");
        vBoxCodigo.setFlexHflex("ftMin");
        vBoxCodigo.setScrollable(false);
        vBoxCodigo.setBoxShadowConfigHorizontalLength(10);
        vBoxCodigo.setBoxShadowConfigVerticalLength(10);
        vBoxCodigo.setBoxShadowConfigBlurRadius(5);
        vBoxCodigo.setBoxShadowConfigSpreadRadius(0);
        vBoxCodigo.setBoxShadowConfigShadowColor("clBlack");
        vBoxCodigo.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(vBoxCodigo);
        vBoxCodigo.applyProperties();
    }

    public TFLabel lblCodigo = new TFLabel();

    private void init_lblCodigo() {
        lblCodigo.setName("lblCodigo");
        lblCodigo.setLeft(0);
        lblCodigo.setTop(0);
        lblCodigo.setWidth(33);
        lblCodigo.setHeight(13);
        lblCodigo.setCaption("C\u00F3digo");
        lblCodigo.setFontColor("clWindowText");
        lblCodigo.setFontSize(-11);
        lblCodigo.setFontName("Tahoma");
        lblCodigo.setFontStyle("[]");
        lblCodigo.setVerticalAlignment("taVerticalCenter");
        lblCodigo.setWordBreak(false);
        vBoxCodigo.addChildren(lblCodigo);
        lblCodigo.applyProperties();
    }

    public TFString edtCodigo = new TFString();

    private void init_edtCodigo() {
        edtCodigo.setName("edtCodigo");
        edtCodigo.setLeft(0);
        edtCodigo.setTop(14);
        edtCodigo.setWidth(100);
        edtCodigo.setHeight(24);
        edtCodigo.setHint("C\u00F3digo");
        edtCodigo.setHelpCaption("C\u00F3digo");
        edtCodigo.setFlex(true);
        edtCodigo.setRequired(false);
        edtCodigo.setPrompt("C\u00F3digo");
        edtCodigo.setConstraintCheckWhen("cwImmediate");
        edtCodigo.setConstraintCheckType("ctExpression");
        edtCodigo.setConstraintFocusOnError(false);
        edtCodigo.setConstraintEnableUI(true);
        edtCodigo.setConstraintEnabled(false);
        edtCodigo.setConstraintFormCheck(true);
        edtCodigo.setCharCase("ccNormal");
        edtCodigo.setPwd(false);
        edtCodigo.setMaxlength(0);
        edtCodigo.setFontColor("clWindowText");
        edtCodigo.setFontSize(-13);
        edtCodigo.setFontName("Tahoma");
        edtCodigo.setFontStyle("[]");
        edtCodigo.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCodigoChange(event);
            processarFlow("FrmPesquisaTransportadora", "edtCodigo", "OnChange");
        });
        edtCodigo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCodigoEnter(event);
            processarFlow("FrmPesquisaTransportadora", "edtCodigo", "OnEnter");
        });
        edtCodigo.setSaveLiteralCharacter(false);
        edtCodigo.applyProperties();
        vBoxCodigo.addChildren(edtCodigo);
        addValidatable(edtCodigo);
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(455);
        FHBox6.setTop(0);
        FHBox6.setWidth(5);
        FHBox6.setHeight(20);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox2.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(137);
        FHBox11.setWidth(570);
        FHBox11.setHeight(5);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox1.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(143);
        FHBox12.setWidth(570);
        FHBox12.setHeight(130);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftTrue");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FVBox1.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(0);
        FHBox15.setWidth(5);
        FHBox15.setHeight(20);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FHBox12.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFGrid gridTransportadora = new TFGrid();

    private void init_gridTransportadora() {
        gridTransportadora.setName("gridTransportadora");
        gridTransportadora.setLeft(5);
        gridTransportadora.setTop(0);
        gridTransportadora.setWidth(500);
        gridTransportadora.setHeight(120);
        gridTransportadora.setTable(tbTransportadoras);
        gridTransportadora.setFlexVflex("ftTrue");
        gridTransportadora.setFlexHflex("ftTrue");
        gridTransportadora.setPagingEnabled(false);
        gridTransportadora.setFrozenColumns(0);
        gridTransportadora.setShowFooter(false);
        gridTransportadora.setShowHeader(true);
        gridTransportadora.setMultiSelection(false);
        gridTransportadora.setGroupingEnabled(false);
        gridTransportadora.setGroupingExpanded(false);
        gridTransportadora.setGroupingShowFooter(false);
        gridTransportadora.setCrosstabEnabled(false);
        gridTransportadora.setCrosstabGroupType("cgtConcat");
        gridTransportadora.setEditionEnabled(false);
        gridTransportadora.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO_INITCAP");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(200);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridTransportadora.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("CNPJ_CPF");
        item1.setTitleCaption("CNPJ/CPF");
        item1.setWidth(130);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridTransportadora.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("COD_TRANSPORTADORA");
        item2.setTitleCaption("C\u00F3digo");
        item2.setWidth(60);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridTransportadora.getColumns().add(item2);
        FHBox12.addChildren(gridTransportadora);
        gridTransportadora.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(505);
        FHBox16.setTop(0);
        FHBox16.setWidth(5);
        FHBox16.setHeight(20);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftFalse");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FHBox12.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(274);
        FHBox13.setWidth(570);
        FHBox13.setHeight(5);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FVBox1.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFSchema scTransportadoras;

    private void init_scTransportadoras() {
        scTransportadoras = rn.scTransportadoras;
        scTransportadoras.setName("scTransportadoras");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbTransportadoras);
        scTransportadoras.getTables().add(item0);
        scTransportadoras.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtDescricaoEnter(final Event<Object> event);

    public abstract void edtCNPJCPFChange(final Event<Object> event);

    public abstract void edtCNPJCPFEnter(final Event<Object> event);

    public abstract void edtCodigoChange(final Event<Object> event);

    public abstract void edtCodigoEnter(final Event<Object> event);

}