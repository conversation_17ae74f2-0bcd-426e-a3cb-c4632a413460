package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmSelecionarEmpresa extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.SelecionarEmpresaRNA rn = null;

    public FrmSelecionarEmpresa() {
        try {
            rn = (freedom.bytecode.rn.SelecionarEmpresaRNA) getRN(freedom.bytecode.rn.wizard.SelecionarEmpresaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbLeadsEmpresasUsuarios();
        init_tbEmpresasUsuarios();
        init_tbLeadsEmpresasUsuariosUf();
        init_tbLeadsEmpresasUsuariosCidade();
        init_tbTipoMidia();
        init_tbMidia();
        init_FVBox1();
        init_FHBox11();
        init_btnVoltar();
        init_btnAceitar();
        init_btnPesquisar();
        init_FVBox2();
        init_FHBox1();
        init_hBoxFiltroOS();
        init_FHBox3();
        init_hbSelEmpAtivo();
        init_FVBox75();
        init_lblTodos();
        init_FVBox77();
        init_hbSelEmpReceptivo();
        init_FVBox16();
        init_lblAtrasado();
        init_FVBox18();
        init_hbSelEmpPassivo();
        init_FVBox3();
        init_lblPassivo();
        init_FVBox4();
        init_FHBox4();
        init_FHBox2();
        init_FHBoxFiltroEmpresa();
        init_edEmpresa();
        init_cmbUF();
        init_cmbCidade();
        init_gridEmpresas();
        init_FHBox6();
        init_FHBox5();
        init_FHBox8();
        init_FLabel1();
        init_FHBox7();
        init_hboxMidia();
        init_cbbTipoMidia();
        init_lblObrigatorio();
        init_cbbMidia();
        init_lblObrigatorio1();
        init_FrmSelecionarEmpresa();
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios;

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios = rn.tbLeadsEmpresasUsuarios;
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsEmpresasUsuariosAfterScroll(event);
            processarFlow("FrmSelecionarEmpresa", "tbLeadsEmpresasUsuarios", "OnAfterScroll");
        });
        tbLeadsEmpresasUsuarios.setWKey("430061;43001");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbLeadsEmpresasUsuarios, "tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("430061;43002");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public LEADS_EMPRESAS_USUARIOS_UF tbLeadsEmpresasUsuariosUf;

    private void init_tbLeadsEmpresasUsuariosUf() {
        tbLeadsEmpresasUsuariosUf = rn.tbLeadsEmpresasUsuariosUf;
        tbLeadsEmpresasUsuariosUf.setName("tbLeadsEmpresasUsuariosUf");
        tbLeadsEmpresasUsuariosUf.setMaxRowCount(200);
        tbLeadsEmpresasUsuariosUf.setWKey("430061;22204");
        tbLeadsEmpresasUsuariosUf.setRatioBatchSize(20);
        getTables().put(tbLeadsEmpresasUsuariosUf, "tbLeadsEmpresasUsuariosUf");
        tbLeadsEmpresasUsuariosUf.applyProperties();
    }

    public LEADS_EMPRESAS_USUARIOS_CIDADE tbLeadsEmpresasUsuariosCidade;

    private void init_tbLeadsEmpresasUsuariosCidade() {
        tbLeadsEmpresasUsuariosCidade = rn.tbLeadsEmpresasUsuariosCidade;
        tbLeadsEmpresasUsuariosCidade.setName("tbLeadsEmpresasUsuariosCidade");
        tbLeadsEmpresasUsuariosCidade.setMaxRowCount(200);
        tbLeadsEmpresasUsuariosCidade.setWKey("430061;22205");
        tbLeadsEmpresasUsuariosCidade.setRatioBatchSize(20);
        getTables().put(tbLeadsEmpresasUsuariosCidade, "tbLeadsEmpresasUsuariosCidade");
        tbLeadsEmpresasUsuariosCidade.applyProperties();
    }

    public CRM_TIPO_MIDIA tbTipoMidia;

    private void init_tbTipoMidia() {
        tbTipoMidia = rn.tbTipoMidia;
        tbTipoMidia.setName("tbTipoMidia");
        tbTipoMidia.setMaxRowCount(200);
        tbTipoMidia.setWKey("430061;41101");
        tbTipoMidia.setRatioBatchSize(20);
        getTables().put(tbTipoMidia, "tbTipoMidia");
        tbTipoMidia.applyProperties();
    }

    public MIDIA tbMidia;

    private void init_tbMidia() {
        tbMidia = rn.tbMidia;
        tbMidia.setName("tbMidia");
        tbMidia.setMaxRowCount(200);
        tbMidia.setWKey("430061;41102");
        tbMidia.setRatioBatchSize(20);
        getTables().put(tbMidia, "tbMidia");
        tbMidia.applyProperties();
    }

    protected TFForm FrmSelecionarEmpresa = this;
    private void init_FrmSelecionarEmpresa() {
        FrmSelecionarEmpresa.setName("FrmSelecionarEmpresa");
        FrmSelecionarEmpresa.setCaption("Selecione uma empresa");
        FrmSelecionarEmpresa.setClientHeight(484);
        FrmSelecionarEmpresa.setClientWidth(532);
        FrmSelecionarEmpresa.setColor("clBtnFace");
        FrmSelecionarEmpresa.setWOrigem("EhMain");
        FrmSelecionarEmpresa.setWKey("430061");
        FrmSelecionarEmpresa.setSpacing(0);
        FrmSelecionarEmpresa.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(532);
        FVBox1.setHeight(484);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmSelecionarEmpresa.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(534);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox1.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmSelecionarEmpresa", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(60);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmSelecionarEmpresa", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox11.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(120);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(60);
        btnPesquisar.setHeight(56);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setAlign("alLeft");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmSelecionarEmpresa", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(27001);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        FHBox11.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(180);
        FVBox2.setTop(0);
        FVBox2.setWidth(346);
        FVBox2.setHeight(56);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox11.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(76);
        FHBox1.setHeight(8);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftTrue");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox2.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox hBoxFiltroOS = new TFHBox();

    private void init_hBoxFiltroOS() {
        hBoxFiltroOS.setName("hBoxFiltroOS");
        hBoxFiltroOS.setLeft(0);
        hBoxFiltroOS.setTop(9);
        hBoxFiltroOS.setWidth(339);
        hBoxFiltroOS.setHeight(27);
        hBoxFiltroOS.setBorderStyle("stNone");
        hBoxFiltroOS.setPaddingTop(0);
        hBoxFiltroOS.setPaddingLeft(0);
        hBoxFiltroOS.setPaddingRight(0);
        hBoxFiltroOS.setPaddingBottom(0);
        hBoxFiltroOS.setMarginTop(0);
        hBoxFiltroOS.setMarginLeft(0);
        hBoxFiltroOS.setMarginRight(0);
        hBoxFiltroOS.setMarginBottom(0);
        hBoxFiltroOS.setSpacing(0);
        hBoxFiltroOS.setFlexVflex("ftFalse");
        hBoxFiltroOS.setFlexHflex("ftTrue");
        hBoxFiltroOS.setScrollable(false);
        hBoxFiltroOS.setBoxShadowConfigHorizontalLength(10);
        hBoxFiltroOS.setBoxShadowConfigVerticalLength(10);
        hBoxFiltroOS.setBoxShadowConfigBlurRadius(5);
        hBoxFiltroOS.setBoxShadowConfigSpreadRadius(0);
        hBoxFiltroOS.setBoxShadowConfigShadowColor("clBlack");
        hBoxFiltroOS.setBoxShadowConfigOpacity(75);
        hBoxFiltroOS.setVAlign("tvTop");
        FVBox2.addChildren(hBoxFiltroOS);
        hBoxFiltroOS.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(44);
        FHBox3.setHeight(16);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftTrue");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        hBoxFiltroOS.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox hbSelEmpAtivo = new TFHBox();

    private void init_hbSelEmpAtivo() {
        hbSelEmpAtivo.setName("hbSelEmpAtivo");
        hbSelEmpAtivo.setLeft(44);
        hbSelEmpAtivo.setTop(0);
        hbSelEmpAtivo.setWidth(89);
        hbSelEmpAtivo.setHeight(23);
        hbSelEmpAtivo.setBorderStyle("stNone");
        hbSelEmpAtivo.setColor("clSilver");
        hbSelEmpAtivo.setPaddingTop(2);
        hbSelEmpAtivo.setPaddingLeft(0);
        hbSelEmpAtivo.setPaddingRight(0);
        hbSelEmpAtivo.setPaddingBottom(0);
        hbSelEmpAtivo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbSelEmpAtivoClick(event);
            processarFlow("FrmSelecionarEmpresa", "hbSelEmpAtivo", "OnClick");
        });
        hbSelEmpAtivo.setMarginTop(0);
        hbSelEmpAtivo.setMarginLeft(0);
        hbSelEmpAtivo.setMarginRight(0);
        hbSelEmpAtivo.setMarginBottom(0);
        hbSelEmpAtivo.setSpacing(0);
        hbSelEmpAtivo.setFlexVflex("ftTrue");
        hbSelEmpAtivo.setFlexHflex("ftFalse");
        hbSelEmpAtivo.setScrollable(false);
        hbSelEmpAtivo.setBoxShadowConfigHorizontalLength(10);
        hbSelEmpAtivo.setBoxShadowConfigVerticalLength(10);
        hbSelEmpAtivo.setBoxShadowConfigBlurRadius(5);
        hbSelEmpAtivo.setBoxShadowConfigSpreadRadius(0);
        hbSelEmpAtivo.setBoxShadowConfigShadowColor("clBlack");
        hbSelEmpAtivo.setBoxShadowConfigOpacity(75);
        hbSelEmpAtivo.setVAlign("tvTop");
        hBoxFiltroOS.addChildren(hbSelEmpAtivo);
        hbSelEmpAtivo.applyProperties();
    }

    public TFVBox FVBox75 = new TFVBox();

    private void init_FVBox75() {
        FVBox75.setName("FVBox75");
        FVBox75.setLeft(0);
        FVBox75.setTop(0);
        FVBox75.setWidth(16);
        FVBox75.setHeight(20);
        FVBox75.setBorderStyle("stNone");
        FVBox75.setPaddingTop(0);
        FVBox75.setPaddingLeft(0);
        FVBox75.setPaddingRight(0);
        FVBox75.setPaddingBottom(0);
        FVBox75.setMarginTop(0);
        FVBox75.setMarginLeft(0);
        FVBox75.setMarginRight(0);
        FVBox75.setMarginBottom(0);
        FVBox75.setSpacing(1);
        FVBox75.setFlexVflex("ftTrue");
        FVBox75.setFlexHflex("ftTrue");
        FVBox75.setScrollable(false);
        FVBox75.setBoxShadowConfigHorizontalLength(10);
        FVBox75.setBoxShadowConfigVerticalLength(10);
        FVBox75.setBoxShadowConfigBlurRadius(5);
        FVBox75.setBoxShadowConfigSpreadRadius(0);
        FVBox75.setBoxShadowConfigShadowColor("clBlack");
        FVBox75.setBoxShadowConfigOpacity(75);
        hbSelEmpAtivo.addChildren(FVBox75);
        FVBox75.applyProperties();
    }

    public TFLabel lblTodos = new TFLabel();

    private void init_lblTodos() {
        lblTodos.setName("lblTodos");
        lblTodos.setLeft(16);
        lblTodos.setTop(0);
        lblTodos.setWidth(28);
        lblTodos.setHeight(14);
        lblTodos.setCaption("Ativo");
        lblTodos.setFontColor("clWindowText");
        lblTodos.setFontSize(-12);
        lblTodos.setFontName("Tahoma");
        lblTodos.setFontStyle("[]");
        lblTodos.setVerticalAlignment("taVerticalCenter");
        lblTodos.setWordBreak(false);
        hbSelEmpAtivo.addChildren(lblTodos);
        lblTodos.applyProperties();
    }

    public TFVBox FVBox77 = new TFVBox();

    private void init_FVBox77() {
        FVBox77.setName("FVBox77");
        FVBox77.setLeft(44);
        FVBox77.setTop(0);
        FVBox77.setWidth(12);
        FVBox77.setHeight(19);
        FVBox77.setBorderStyle("stNone");
        FVBox77.setPaddingTop(0);
        FVBox77.setPaddingLeft(0);
        FVBox77.setPaddingRight(0);
        FVBox77.setPaddingBottom(0);
        FVBox77.setMarginTop(0);
        FVBox77.setMarginLeft(0);
        FVBox77.setMarginRight(0);
        FVBox77.setMarginBottom(0);
        FVBox77.setSpacing(1);
        FVBox77.setFlexVflex("ftTrue");
        FVBox77.setFlexHflex("ftTrue");
        FVBox77.setScrollable(false);
        FVBox77.setBoxShadowConfigHorizontalLength(10);
        FVBox77.setBoxShadowConfigVerticalLength(10);
        FVBox77.setBoxShadowConfigBlurRadius(5);
        FVBox77.setBoxShadowConfigSpreadRadius(0);
        FVBox77.setBoxShadowConfigShadowColor("clBlack");
        FVBox77.setBoxShadowConfigOpacity(75);
        hbSelEmpAtivo.addChildren(FVBox77);
        FVBox77.applyProperties();
    }

    public TFHBox hbSelEmpReceptivo = new TFHBox();

    private void init_hbSelEmpReceptivo() {
        hbSelEmpReceptivo.setName("hbSelEmpReceptivo");
        hbSelEmpReceptivo.setLeft(133);
        hbSelEmpReceptivo.setTop(0);
        hbSelEmpReceptivo.setWidth(89);
        hbSelEmpReceptivo.setHeight(23);
        hbSelEmpReceptivo.setBorderStyle("stNone");
        hbSelEmpReceptivo.setPaddingTop(2);
        hbSelEmpReceptivo.setPaddingLeft(0);
        hbSelEmpReceptivo.setPaddingRight(0);
        hbSelEmpReceptivo.setPaddingBottom(0);
        hbSelEmpReceptivo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbSelEmpReceptivoClick(event);
            processarFlow("FrmSelecionarEmpresa", "hbSelEmpReceptivo", "OnClick");
        });
        hbSelEmpReceptivo.setMarginTop(0);
        hbSelEmpReceptivo.setMarginLeft(0);
        hbSelEmpReceptivo.setMarginRight(0);
        hbSelEmpReceptivo.setMarginBottom(0);
        hbSelEmpReceptivo.setSpacing(0);
        hbSelEmpReceptivo.setFlexVflex("ftTrue");
        hbSelEmpReceptivo.setFlexHflex("ftFalse");
        hbSelEmpReceptivo.setScrollable(false);
        hbSelEmpReceptivo.setBoxShadowConfigHorizontalLength(10);
        hbSelEmpReceptivo.setBoxShadowConfigVerticalLength(10);
        hbSelEmpReceptivo.setBoxShadowConfigBlurRadius(5);
        hbSelEmpReceptivo.setBoxShadowConfigSpreadRadius(0);
        hbSelEmpReceptivo.setBoxShadowConfigShadowColor("clBlack");
        hbSelEmpReceptivo.setBoxShadowConfigOpacity(75);
        hbSelEmpReceptivo.setVAlign("tvTop");
        hBoxFiltroOS.addChildren(hbSelEmpReceptivo);
        hbSelEmpReceptivo.applyProperties();
    }

    public TFVBox FVBox16 = new TFVBox();

    private void init_FVBox16() {
        FVBox16.setName("FVBox16");
        FVBox16.setLeft(0);
        FVBox16.setTop(0);
        FVBox16.setWidth(16);
        FVBox16.setHeight(20);
        FVBox16.setBorderStyle("stNone");
        FVBox16.setPaddingTop(0);
        FVBox16.setPaddingLeft(0);
        FVBox16.setPaddingRight(0);
        FVBox16.setPaddingBottom(0);
        FVBox16.setMarginTop(0);
        FVBox16.setMarginLeft(0);
        FVBox16.setMarginRight(0);
        FVBox16.setMarginBottom(0);
        FVBox16.setSpacing(1);
        FVBox16.setFlexVflex("ftTrue");
        FVBox16.setFlexHflex("ftTrue");
        FVBox16.setScrollable(false);
        FVBox16.setBoxShadowConfigHorizontalLength(10);
        FVBox16.setBoxShadowConfigVerticalLength(10);
        FVBox16.setBoxShadowConfigBlurRadius(5);
        FVBox16.setBoxShadowConfigSpreadRadius(0);
        FVBox16.setBoxShadowConfigShadowColor("clBlack");
        FVBox16.setBoxShadowConfigOpacity(75);
        hbSelEmpReceptivo.addChildren(FVBox16);
        FVBox16.applyProperties();
    }

    public TFLabel lblAtrasado = new TFLabel();

    private void init_lblAtrasado() {
        lblAtrasado.setName("lblAtrasado");
        lblAtrasado.setLeft(16);
        lblAtrasado.setTop(0);
        lblAtrasado.setWidth(54);
        lblAtrasado.setHeight(14);
        lblAtrasado.setCaption("Receptivo");
        lblAtrasado.setFontColor("clWindowText");
        lblAtrasado.setFontSize(-12);
        lblAtrasado.setFontName("Tahoma");
        lblAtrasado.setFontStyle("[]");
        lblAtrasado.setVerticalAlignment("taVerticalCenter");
        lblAtrasado.setWordBreak(false);
        hbSelEmpReceptivo.addChildren(lblAtrasado);
        lblAtrasado.applyProperties();
    }

    public TFVBox FVBox18 = new TFVBox();

    private void init_FVBox18() {
        FVBox18.setName("FVBox18");
        FVBox18.setLeft(70);
        FVBox18.setTop(0);
        FVBox18.setWidth(12);
        FVBox18.setHeight(19);
        FVBox18.setBorderStyle("stNone");
        FVBox18.setPaddingTop(0);
        FVBox18.setPaddingLeft(0);
        FVBox18.setPaddingRight(0);
        FVBox18.setPaddingBottom(0);
        FVBox18.setMarginTop(0);
        FVBox18.setMarginLeft(0);
        FVBox18.setMarginRight(0);
        FVBox18.setMarginBottom(0);
        FVBox18.setSpacing(1);
        FVBox18.setFlexVflex("ftTrue");
        FVBox18.setFlexHflex("ftTrue");
        FVBox18.setScrollable(false);
        FVBox18.setBoxShadowConfigHorizontalLength(10);
        FVBox18.setBoxShadowConfigVerticalLength(10);
        FVBox18.setBoxShadowConfigBlurRadius(5);
        FVBox18.setBoxShadowConfigSpreadRadius(0);
        FVBox18.setBoxShadowConfigShadowColor("clBlack");
        FVBox18.setBoxShadowConfigOpacity(75);
        hbSelEmpReceptivo.addChildren(FVBox18);
        FVBox18.applyProperties();
    }

    public TFHBox hbSelEmpPassivo = new TFHBox();

    private void init_hbSelEmpPassivo() {
        hbSelEmpPassivo.setName("hbSelEmpPassivo");
        hbSelEmpPassivo.setLeft(222);
        hbSelEmpPassivo.setTop(0);
        hbSelEmpPassivo.setWidth(89);
        hbSelEmpPassivo.setHeight(23);
        hbSelEmpPassivo.setBorderStyle("stNone");
        hbSelEmpPassivo.setPaddingTop(2);
        hbSelEmpPassivo.setPaddingLeft(0);
        hbSelEmpPassivo.setPaddingRight(0);
        hbSelEmpPassivo.setPaddingBottom(0);
        hbSelEmpPassivo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbSelEmpPassivoClick(event);
            processarFlow("FrmSelecionarEmpresa", "hbSelEmpPassivo", "OnClick");
        });
        hbSelEmpPassivo.setMarginTop(0);
        hbSelEmpPassivo.setMarginLeft(0);
        hbSelEmpPassivo.setMarginRight(0);
        hbSelEmpPassivo.setMarginBottom(0);
        hbSelEmpPassivo.setSpacing(0);
        hbSelEmpPassivo.setFlexVflex("ftTrue");
        hbSelEmpPassivo.setFlexHflex("ftFalse");
        hbSelEmpPassivo.setScrollable(false);
        hbSelEmpPassivo.setBoxShadowConfigHorizontalLength(10);
        hbSelEmpPassivo.setBoxShadowConfigVerticalLength(10);
        hbSelEmpPassivo.setBoxShadowConfigBlurRadius(5);
        hbSelEmpPassivo.setBoxShadowConfigSpreadRadius(0);
        hbSelEmpPassivo.setBoxShadowConfigShadowColor("clBlack");
        hbSelEmpPassivo.setBoxShadowConfigOpacity(75);
        hbSelEmpPassivo.setVAlign("tvTop");
        hBoxFiltroOS.addChildren(hbSelEmpPassivo);
        hbSelEmpPassivo.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(16);
        FVBox3.setHeight(20);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        hbSelEmpPassivo.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel lblPassivo = new TFLabel();

    private void init_lblPassivo() {
        lblPassivo.setName("lblPassivo");
        lblPassivo.setLeft(16);
        lblPassivo.setTop(0);
        lblPassivo.setWidth(48);
        lblPassivo.setHeight(14);
        lblPassivo.setCaption("Passante");
        lblPassivo.setFontColor("clWindowText");
        lblPassivo.setFontSize(-12);
        lblPassivo.setFontName("Tahoma");
        lblPassivo.setFontStyle("[]");
        lblPassivo.setVerticalAlignment("taVerticalCenter");
        lblPassivo.setWordBreak(false);
        hbSelEmpPassivo.addChildren(lblPassivo);
        lblPassivo.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(64);
        FVBox4.setTop(0);
        FVBox4.setWidth(12);
        FVBox4.setHeight(19);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hbSelEmpPassivo.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(311);
        FHBox4.setTop(0);
        FHBox4.setWidth(44);
        FHBox4.setHeight(15);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftTrue");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        hBoxFiltroOS.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(37);
        FHBox2.setWidth(76);
        FHBox2.setHeight(8);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox2.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox FHBoxFiltroEmpresa = new TFHBox();

    private void init_FHBoxFiltroEmpresa() {
        FHBoxFiltroEmpresa.setName("FHBoxFiltroEmpresa");
        FHBoxFiltroEmpresa.setLeft(0);
        FHBoxFiltroEmpresa.setTop(62);
        FHBoxFiltroEmpresa.setWidth(521);
        FHBoxFiltroEmpresa.setHeight(41);
        FHBoxFiltroEmpresa.setBorderStyle("stNone");
        FHBoxFiltroEmpresa.setPaddingTop(0);
        FHBoxFiltroEmpresa.setPaddingLeft(0);
        FHBoxFiltroEmpresa.setPaddingRight(0);
        FHBoxFiltroEmpresa.setPaddingBottom(0);
        FHBoxFiltroEmpresa.setMarginTop(0);
        FHBoxFiltroEmpresa.setMarginLeft(0);
        FHBoxFiltroEmpresa.setMarginRight(0);
        FHBoxFiltroEmpresa.setMarginBottom(0);
        FHBoxFiltroEmpresa.setSpacing(5);
        FHBoxFiltroEmpresa.setFlexVflex("ftFalse");
        FHBoxFiltroEmpresa.setFlexHflex("ftTrue");
        FHBoxFiltroEmpresa.setScrollable(false);
        FHBoxFiltroEmpresa.setBoxShadowConfigHorizontalLength(10);
        FHBoxFiltroEmpresa.setBoxShadowConfigVerticalLength(10);
        FHBoxFiltroEmpresa.setBoxShadowConfigBlurRadius(5);
        FHBoxFiltroEmpresa.setBoxShadowConfigSpreadRadius(0);
        FHBoxFiltroEmpresa.setBoxShadowConfigShadowColor("clBlack");
        FHBoxFiltroEmpresa.setBoxShadowConfigOpacity(75);
        FHBoxFiltroEmpresa.setVAlign("tvTop");
        FVBox1.addChildren(FHBoxFiltroEmpresa);
        FHBoxFiltroEmpresa.applyProperties();
    }

    public TFString edEmpresa = new TFString();

    private void init_edEmpresa() {
        edEmpresa.setName("edEmpresa");
        edEmpresa.setLeft(0);
        edEmpresa.setTop(0);
        edEmpresa.setWidth(115);
        edEmpresa.setHeight(24);
        edEmpresa.setFlex(true);
        edEmpresa.setRequired(false);
        edEmpresa.setPrompt("Nome empresa");
        edEmpresa.setConstraintCheckWhen("cwImmediate");
        edEmpresa.setConstraintCheckType("ctExpression");
        edEmpresa.setConstraintFocusOnError(false);
        edEmpresa.setConstraintEnableUI(true);
        edEmpresa.setConstraintEnabled(false);
        edEmpresa.setConstraintFormCheck(true);
        edEmpresa.setCharCase("ccNormal");
        edEmpresa.setPwd(false);
        edEmpresa.setMaxlength(0);
        edEmpresa.setFontColor("clWindowText");
        edEmpresa.setFontSize(-13);
        edEmpresa.setFontName("Tahoma");
        edEmpresa.setFontStyle("[]");
        edEmpresa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edEmpresaEnter(event);
            processarFlow("FrmSelecionarEmpresa", "edEmpresa", "OnEnter");
        });
        edEmpresa.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edEmpresaExit(event);
            processarFlow("FrmSelecionarEmpresa", "edEmpresa", "OnExit");
        });
        edEmpresa.setSaveLiteralCharacter(false);
        edEmpresa.applyProperties();
        FHBoxFiltroEmpresa.addChildren(edEmpresa);
        addValidatable(edEmpresa);
    }

    public TFCombo cmbUF = new TFCombo();

    private void init_cmbUF() {
        cmbUF.setName("cmbUF");
        cmbUF.setLeft(115);
        cmbUF.setTop(0);
        cmbUF.setWidth(135);
        cmbUF.setHeight(21);
        cmbUF.setLookupTable(tbLeadsEmpresasUsuariosUf);
        cmbUF.setLookupKey("ESTADO");
        cmbUF.setLookupDesc("ESTADO");
        cmbUF.setFlex(false);
        cmbUF.setReadOnly(true);
        cmbUF.setRequired(false);
        cmbUF.setPrompt("UF");
        cmbUF.setConstraintCheckWhen("cwImmediate");
        cmbUF.setConstraintCheckType("ctExpression");
        cmbUF.setConstraintFocusOnError(false);
        cmbUF.setConstraintEnableUI(true);
        cmbUF.setConstraintEnabled(false);
        cmbUF.setConstraintFormCheck(true);
        cmbUF.setClearOnDelKey(false);
        cmbUF.setUseClearButton(true);
        cmbUF.setHideClearButtonOnNullValue(false);
        cmbUF.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbUFChange(event);
            processarFlow("FrmSelecionarEmpresa", "cmbUF", "OnChange");
        });
        cmbUF.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbUFClearClick(event);
            processarFlow("FrmSelecionarEmpresa", "cmbUF", "OnClearClick");
        });
        FHBoxFiltroEmpresa.addChildren(cmbUF);
        cmbUF.applyProperties();
        addValidatable(cmbUF);
    }

    public TFCombo cmbCidade = new TFCombo();

    private void init_cmbCidade() {
        cmbCidade.setName("cmbCidade");
        cmbCidade.setLeft(250);
        cmbCidade.setTop(0);
        cmbCidade.setWidth(192);
        cmbCidade.setHeight(21);
        cmbCidade.setLookupTable(tbLeadsEmpresasUsuariosCidade);
        cmbCidade.setLookupKey("CIDADE");
        cmbCidade.setLookupDesc("CIDADE");
        cmbCidade.setFlex(false);
        cmbCidade.setReadOnly(true);
        cmbCidade.setRequired(false);
        cmbCidade.setPrompt("Cidade");
        cmbCidade.setConstraintCheckWhen("cwImmediate");
        cmbCidade.setConstraintCheckType("ctExpression");
        cmbCidade.setConstraintFocusOnError(false);
        cmbCidade.setConstraintEnableUI(true);
        cmbCidade.setConstraintEnabled(false);
        cmbCidade.setConstraintFormCheck(true);
        cmbCidade.setClearOnDelKey(false);
        cmbCidade.setUseClearButton(true);
        cmbCidade.setHideClearButtonOnNullValue(false);
        cmbCidade.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbCidadeChange(event);
            processarFlow("FrmSelecionarEmpresa", "cmbCidade", "OnChange");
        });
        cmbCidade.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbCidadeClearClick(event);
            processarFlow("FrmSelecionarEmpresa", "cmbCidade", "OnClearClick");
        });
        FHBoxFiltroEmpresa.addChildren(cmbCidade);
        cmbCidade.applyProperties();
        addValidatable(cmbCidade);
    }

    public TFGrid gridEmpresas = new TFGrid();

    private void init_gridEmpresas() {
        gridEmpresas.setName("gridEmpresas");
        gridEmpresas.setLeft(0);
        gridEmpresas.setTop(104);
        gridEmpresas.setWidth(521);
        gridEmpresas.setHeight(270);
        gridEmpresas.setTable(tbLeadsEmpresasUsuarios);
        gridEmpresas.setFlexVflex("ftTrue");
        gridEmpresas.setFlexHflex("ftTrue");
        gridEmpresas.setPagingEnabled(false);
        gridEmpresas.setFrozenColumns(0);
        gridEmpresas.setShowFooter(false);
        gridEmpresas.setShowHeader(true);
        gridEmpresas.setMultiSelection(false);
        gridEmpresas.setGroupingEnabled(false);
        gridEmpresas.setGroupingExpanded(false);
        gridEmpresas.setGroupingShowFooter(false);
        gridEmpresas.setCrosstabEnabled(false);
        gridEmpresas.setCrosstabGroupType("cgtConcat");
        gridEmpresas.setEditionEnabled(false);
        gridEmpresas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("EMPRESA");
        item0.setTitleCaption("Empresa");
        item0.setWidth(220);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridEmpresas.getColumns().add(item0);
        FVBox1.addChildren(gridEmpresas);
        gridEmpresas.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(375);
        FHBox6.setWidth(76);
        FHBox6.setHeight(8);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FVBox1.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(384);
        FHBox5.setWidth(418);
        FHBox5.setHeight(29);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox1.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(8);
        FHBox8.setHeight(24);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox5.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(8);
        FLabel1.setTop(0);
        FLabel1.setWidth(164);
        FLabel1.setHeight(19);
        FLabel1.setCaption("labelUfCidadeBairro");
        FLabel1.setFontColor("clHighlight");
        FLabel1.setFontSize(-16);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setFieldName("LOCALIDADE");
        FLabel1.setTable(tbLeadsEmpresasUsuarios);
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FHBox5.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(414);
        FHBox7.setWidth(76);
        FHBox7.setHeight(8);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox1.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox hboxMidia = new TFHBox();

    private void init_hboxMidia() {
        hboxMidia.setName("hboxMidia");
        hboxMidia.setLeft(0);
        hboxMidia.setTop(423);
        hboxMidia.setWidth(527);
        hboxMidia.setHeight(41);
        hboxMidia.setBorderStyle("stNone");
        hboxMidia.setPaddingTop(0);
        hboxMidia.setPaddingLeft(0);
        hboxMidia.setPaddingRight(0);
        hboxMidia.setPaddingBottom(0);
        hboxMidia.setMarginTop(0);
        hboxMidia.setMarginLeft(0);
        hboxMidia.setMarginRight(0);
        hboxMidia.setMarginBottom(0);
        hboxMidia.setSpacing(5);
        hboxMidia.setFlexVflex("ftMin");
        hboxMidia.setFlexHflex("ftTrue");
        hboxMidia.setScrollable(false);
        hboxMidia.setBoxShadowConfigHorizontalLength(10);
        hboxMidia.setBoxShadowConfigVerticalLength(10);
        hboxMidia.setBoxShadowConfigBlurRadius(5);
        hboxMidia.setBoxShadowConfigSpreadRadius(0);
        hboxMidia.setBoxShadowConfigShadowColor("clBlack");
        hboxMidia.setBoxShadowConfigOpacity(75);
        hboxMidia.setVAlign("tvTop");
        FVBox1.addChildren(hboxMidia);
        hboxMidia.applyProperties();
    }

    public TFCombo cbbTipoMidia = new TFCombo();

    private void init_cbbTipoMidia() {
        cbbTipoMidia.setName("cbbTipoMidia");
        cbbTipoMidia.setLeft(0);
        cbbTipoMidia.setTop(0);
        cbbTipoMidia.setWidth(201);
        cbbTipoMidia.setHeight(21);
        cbbTipoMidia.setLookupTable(tbTipoMidia);
        cbbTipoMidia.setLookupKey("COD_TIPO");
        cbbTipoMidia.setLookupDesc("TIPO_MIDIA");
        cbbTipoMidia.setFlex(true);
        cbbTipoMidia.setReadOnly(true);
        cbbTipoMidia.setRequired(false);
        cbbTipoMidia.setPrompt("Tipo M\u00EDdia");
        cbbTipoMidia.setConstraintCheckWhen("cwImmediate");
        cbbTipoMidia.setConstraintCheckType("ctExpression");
        cbbTipoMidia.setConstraintFocusOnError(false);
        cbbTipoMidia.setConstraintEnableUI(true);
        cbbTipoMidia.setConstraintEnabled(false);
        cbbTipoMidia.setConstraintFormCheck(true);
        cbbTipoMidia.setClearOnDelKey(true);
        cbbTipoMidia.setUseClearButton(true);
        cbbTipoMidia.setHideClearButtonOnNullValue(false);
        cbbTipoMidia.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbTipoMidiaChange(event);
            processarFlow("FrmSelecionarEmpresa", "cbbTipoMidia", "OnChange");
        });
        cbbTipoMidia.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbTipoMidiaClearClick(event);
            processarFlow("FrmSelecionarEmpresa", "cbbTipoMidia", "OnClearClick");
        });
        hboxMidia.addChildren(cbbTipoMidia);
        cbbTipoMidia.applyProperties();
        addValidatable(cbbTipoMidia);
    }

    public TFLabel lblObrigatorio = new TFLabel();

    private void init_lblObrigatorio() {
        lblObrigatorio.setName("lblObrigatorio");
        lblObrigatorio.setLeft(201);
        lblObrigatorio.setTop(0);
        lblObrigatorio.setWidth(12);
        lblObrigatorio.setHeight(23);
        lblObrigatorio.setCaption("*");
        lblObrigatorio.setFontColor("clRed");
        lblObrigatorio.setFontSize(-19);
        lblObrigatorio.setFontName("Tahoma");
        lblObrigatorio.setFontStyle("[fsBold]");
        lblObrigatorio.setVerticalAlignment("taVerticalCenter");
        lblObrigatorio.setWordBreak(false);
        hboxMidia.addChildren(lblObrigatorio);
        lblObrigatorio.applyProperties();
    }

    public TFCombo cbbMidia = new TFCombo();

    private void init_cbbMidia() {
        cbbMidia.setName("cbbMidia");
        cbbMidia.setLeft(213);
        cbbMidia.setTop(0);
        cbbMidia.setWidth(201);
        cbbMidia.setHeight(21);
        cbbMidia.setLookupTable(tbMidia);
        cbbMidia.setLookupKey("COD_MIDIA");
        cbbMidia.setLookupDesc("DESCRICAO");
        cbbMidia.setFlex(true);
        cbbMidia.setReadOnly(true);
        cbbMidia.setRequired(false);
        cbbMidia.setPrompt("M\u00EDdia");
        cbbMidia.setConstraintCheckWhen("cwImmediate");
        cbbMidia.setConstraintCheckType("ctExpression");
        cbbMidia.setConstraintFocusOnError(false);
        cbbMidia.setConstraintEnableUI(true);
        cbbMidia.setConstraintEnabled(false);
        cbbMidia.setConstraintFormCheck(true);
        cbbMidia.setClearOnDelKey(true);
        cbbMidia.setUseClearButton(true);
        cbbMidia.setHideClearButtonOnNullValue(false);
        hboxMidia.addChildren(cbbMidia);
        cbbMidia.applyProperties();
        addValidatable(cbbMidia);
    }

    public TFLabel lblObrigatorio1 = new TFLabel();

    private void init_lblObrigatorio1() {
        lblObrigatorio1.setName("lblObrigatorio1");
        lblObrigatorio1.setLeft(414);
        lblObrigatorio1.setTop(0);
        lblObrigatorio1.setWidth(12);
        lblObrigatorio1.setHeight(23);
        lblObrigatorio1.setCaption("*");
        lblObrigatorio1.setFontColor("clRed");
        lblObrigatorio1.setFontSize(-19);
        lblObrigatorio1.setFontName("Tahoma");
        lblObrigatorio1.setFontStyle("[fsBold]");
        lblObrigatorio1.setVerticalAlignment("taVerticalCenter");
        lblObrigatorio1.setWordBreak(false);
        hboxMidia.addChildren(lblObrigatorio1);
        lblObrigatorio1.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void hbSelEmpAtivoClick(final Event<Object> event);

    public abstract void hbSelEmpReceptivoClick(final Event<Object> event);

    public abstract void hbSelEmpPassivoClick(final Event<Object> event);

    public abstract void edEmpresaEnter(final Event<Object> event);

    public abstract void edEmpresaExit(final Event<Object> event);

    public abstract void cmbUFChange(final Event<Object> event);

    public abstract void cmbUFClearClick(final Event<Object> event);

    public abstract void cmbCidadeChange(final Event<Object> event);

    public abstract void cmbCidadeClearClick(final Event<Object> event);

    public abstract void cbbTipoMidiaChange(final Event<Object> event);

    public abstract void cbbTipoMidiaClearClick(final Event<Object> event);

    public abstract void tbLeadsEmpresasUsuariosAfterScroll(final Event<Object> event);

}