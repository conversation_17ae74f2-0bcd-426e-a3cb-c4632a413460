package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAlterarDadosEmail extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AlterarDadosEmailRNA rn = null;

    public FrmAlterarDadosEmail() {
        try {
            rn = (freedom.bytecode.rn.AlterarDadosEmailRNA) getRN(freedom.bytecode.rn.wizard.AlterarDadosEmailRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresasUsuarios();
        init_grbUsuario();
        init_FVBox1();
        init_FGridPanel1();
        init_FLabel4();
        init_FLabel2();
        init_FLabel3();
        init_FLabel1();
        init_edtEmail();
        init_edtSenhaAntiga();
        init_edtSenha();
        init_edtConfimarSenha();
        init_FHBox1();
        init_FHBox2();
        init_btnSalvar();
        init_FHBox3();
        init_btnCancelar();
        init_FHBox4();
        init_sc();
        init_FrmAlterarDadosEmail();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("430090;43001");
        tbEmpresasUsuarios.setDeltaMode("dmAll");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    protected TFForm FrmAlterarDadosEmail = this;
    private void init_FrmAlterarDadosEmail() {
        FrmAlterarDadosEmail.setName("FrmAlterarDadosEmail");
        FrmAlterarDadosEmail.setCaption("Altera\u00E7\u00E3o de e-mail e senha do usu\u00E1rio");
        FrmAlterarDadosEmail.setClientHeight(236);
        FrmAlterarDadosEmail.setClientWidth(542);
        FrmAlterarDadosEmail.setColor("clBtnFace");
        FrmAlterarDadosEmail.setWKey("430090");
        FrmAlterarDadosEmail.setSpacing(0);
        FrmAlterarDadosEmail.applyProperties();
    }

    public TFGroupbox grbUsuario = new TFGroupbox();

    private void init_grbUsuario() {
        grbUsuario.setName("grbUsuario");
        grbUsuario.setLeft(0);
        grbUsuario.setTop(0);
        grbUsuario.setWidth(542);
        grbUsuario.setHeight(236);
        grbUsuario.setAlign("alClient");
        grbUsuario.setCaption("Usu\u00E1rio:");
        grbUsuario.setFontColor("clWindowText");
        grbUsuario.setFontSize(-11);
        grbUsuario.setFontName("Tahoma");
        grbUsuario.setFontStyle("[]");
        grbUsuario.setFlexVflex("ftTrue");
        grbUsuario.setFlexHflex("ftTrue");
        grbUsuario.setScrollable(false);
        grbUsuario.setClosable(false);
        grbUsuario.setClosed(false);
        grbUsuario.setOrient("coHorizontal");
        grbUsuario.setStyle("grp3D");
        grbUsuario.setHeaderImageId(0);
        FrmAlterarDadosEmail.addChildren(grbUsuario);
        grbUsuario.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(2);
        FVBox1.setTop(15);
        FVBox1.setWidth(538);
        FVBox1.setHeight(219);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        grbUsuario.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGridPanel FGridPanel1 = new TFGridPanel();

    private void init_FGridPanel1() {
        FGridPanel1.setName("FGridPanel1");
        FGridPanel1.setLeft(0);
        FGridPanel1.setTop(0);
        FGridPanel1.setWidth(538);
        FGridPanel1.setHeight(156);
        FGridPanel1.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(90.000000000000000000);
        FGridPanel1.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        FGridPanel1.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("FLabel1");
        item2.setRow(0);
        FGridPanel1.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("edtEmail");
        item3.setRow(0);
        FGridPanel1.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("FLabel2");
        item4.setRow(1);
        FGridPanel1.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("edtSenhaAntiga");
        item5.setRow(1);
        FGridPanel1.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(0);
        item6.setControl("FLabel3");
        item6.setRow(2);
        FGridPanel1.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(1);
        item7.setControl("edtSenha");
        item7.setRow(2);
        FGridPanel1.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(0);
        item8.setControl("FLabel4");
        item8.setRow(3);
        FGridPanel1.getControlCollection().add(item8);
        TFControlItem item9 = new TFControlItem();
        item9.setColumn(1);
        item9.setControl("edtConfimarSenha");
        item9.setRow(3);
        FGridPanel1.getControlCollection().add(item9);
        TFGridPanelRow item10 = new TFGridPanelRow();
        item10.setSizeStyle("ssAuto");
        FGridPanel1.getRowCollection().add(item10);
        TFGridPanelRow item11 = new TFGridPanelRow();
        item11.setSizeStyle("ssAuto");
        FGridPanel1.getRowCollection().add(item11);
        TFGridPanelRow item12 = new TFGridPanelRow();
        item12.setSizeStyle("ssAuto");
        FGridPanel1.getRowCollection().add(item12);
        TFGridPanelRow item13 = new TFGridPanelRow();
        item13.setSizeStyle("ssAuto");
        FGridPanel1.getRowCollection().add(item13);
        FGridPanel1.setFlexVflex("ftTrue");
        FGridPanel1.setFlexHflex("ftTrue");
        FGridPanel1.setAllRowFlex(true);
        FGridPanel1.setColumnTabOrder(false);
        FVBox1.addChildren(FGridPanel1);
        FGridPanel1.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(11);
        FLabel4.setTop(73);
        FLabel4.setWidth(80);
        FLabel4.setHeight(24);
        FLabel4.setAlign("alRight");
        FLabel4.setCaption("Confirmar Senha");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FGridPanel1.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(28);
        FLabel2.setTop(25);
        FLabel2.setWidth(63);
        FLabel2.setHeight(24);
        FLabel2.setAlign("alRight");
        FLabel2.setCaption("Senha antiga");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FGridPanel1.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(31);
        FLabel3.setTop(49);
        FLabel3.setWidth(60);
        FLabel3.setHeight(24);
        FLabel3.setAlign("alRight");
        FLabel3.setCaption("e-mail senha");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FGridPanel1.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(63);
        FLabel1.setTop(1);
        FLabel1.setWidth(28);
        FLabel1.setHeight(24);
        FLabel1.setAlign("alRight");
        FLabel1.setCaption("e-mail");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FGridPanel1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFString edtEmail = new TFString();

    private void init_edtEmail() {
        edtEmail.setName("edtEmail");
        edtEmail.setLeft(91);
        edtEmail.setTop(1);
        edtEmail.setWidth(188);
        edtEmail.setHeight(24);
        edtEmail.setTable(tbEmpresasUsuarios);
        edtEmail.setFieldName("EMAIL");
        edtEmail.setFlex(true);
        edtEmail.setRequired(true);
        edtEmail.setConstraintCheckWhen("cwImmediate");
        edtEmail.setConstraintCheckType("ctExpression");
        edtEmail.setConstraintFocusOnError(false);
        edtEmail.setConstraintEnableUI(true);
        edtEmail.setConstraintEnabled(false);
        edtEmail.setConstraintFormCheck(true);
        edtEmail.setCharCase("ccNormal");
        edtEmail.setPwd(false);
        edtEmail.setMaxlength(0);
        edtEmail.setFontColor("clWindowText");
        edtEmail.setFontSize(-13);
        edtEmail.setFontName("Tahoma");
        edtEmail.setFontStyle("[]");
        edtEmail.setSaveLiteralCharacter(false);
        edtEmail.applyProperties();
        FGridPanel1.addChildren(edtEmail);
        addValidatable(edtEmail);
    }

    public TFString edtSenhaAntiga = new TFString();

    private void init_edtSenhaAntiga() {
        edtSenhaAntiga.setName("edtSenhaAntiga");
        edtSenhaAntiga.setLeft(91);
        edtSenhaAntiga.setTop(25);
        edtSenhaAntiga.setWidth(188);
        edtSenhaAntiga.setHeight(24);
        edtSenhaAntiga.setFlex(true);
        edtSenhaAntiga.setRequired(true);
        edtSenhaAntiga.setConstraintCheckWhen("cwImmediate");
        edtSenhaAntiga.setConstraintCheckType("ctExpression");
        edtSenhaAntiga.setConstraintFocusOnError(false);
        edtSenhaAntiga.setConstraintEnableUI(true);
        edtSenhaAntiga.setConstraintEnabled(false);
        edtSenhaAntiga.setConstraintFormCheck(true);
        edtSenhaAntiga.setCharCase("ccNormal");
        edtSenhaAntiga.setPwd(true);
        edtSenhaAntiga.setMaxlength(0);
        edtSenhaAntiga.setEnabled(false);
        edtSenhaAntiga.setFontColor("clWindowText");
        edtSenhaAntiga.setFontSize(-13);
        edtSenhaAntiga.setFontName("Tahoma");
        edtSenhaAntiga.setFontStyle("[]");
        edtSenhaAntiga.setSaveLiteralCharacter(false);
        edtSenhaAntiga.applyProperties();
        FGridPanel1.addChildren(edtSenhaAntiga);
        addValidatable(edtSenhaAntiga);
    }

    public TFString edtSenha = new TFString();

    private void init_edtSenha() {
        edtSenha.setName("edtSenha");
        edtSenha.setLeft(91);
        edtSenha.setTop(49);
        edtSenha.setWidth(188);
        edtSenha.setHeight(24);
        edtSenha.setFlex(true);
        edtSenha.setRequired(true);
        edtSenha.setConstraintCheckWhen("cwImmediate");
        edtSenha.setConstraintCheckType("ctExpression");
        edtSenha.setConstraintFocusOnError(false);
        edtSenha.setConstraintEnableUI(true);
        edtSenha.setConstraintEnabled(false);
        edtSenha.setConstraintFormCheck(true);
        edtSenha.setCharCase("ccNormal");
        edtSenha.setPwd(true);
        edtSenha.setMaxlength(0);
        edtSenha.setFontColor("clWindowText");
        edtSenha.setFontSize(-13);
        edtSenha.setFontName("Tahoma");
        edtSenha.setFontStyle("[]");
        edtSenha.setSaveLiteralCharacter(false);
        edtSenha.applyProperties();
        FGridPanel1.addChildren(edtSenha);
        addValidatable(edtSenha);
    }

    public TFString edtConfimarSenha = new TFString();

    private void init_edtConfimarSenha() {
        edtConfimarSenha.setName("edtConfimarSenha");
        edtConfimarSenha.setLeft(91);
        edtConfimarSenha.setTop(73);
        edtConfimarSenha.setWidth(188);
        edtConfimarSenha.setHeight(24);
        edtConfimarSenha.setFlex(true);
        edtConfimarSenha.setRequired(true);
        edtConfimarSenha.setConstraintCheckWhen("cwImmediate");
        edtConfimarSenha.setConstraintCheckType("ctExpression");
        edtConfimarSenha.setConstraintFocusOnError(false);
        edtConfimarSenha.setConstraintEnableUI(true);
        edtConfimarSenha.setConstraintEnabled(false);
        edtConfimarSenha.setConstraintFormCheck(true);
        edtConfimarSenha.setCharCase("ccNormal");
        edtConfimarSenha.setPwd(true);
        edtConfimarSenha.setMaxlength(0);
        edtConfimarSenha.setFontColor("clWindowText");
        edtConfimarSenha.setFontSize(-13);
        edtConfimarSenha.setFontName("Tahoma");
        edtConfimarSenha.setFontStyle("[]");
        edtConfimarSenha.setSaveLiteralCharacter(false);
        edtConfimarSenha.applyProperties();
        FGridPanel1.addChildren(edtConfimarSenha);
        addValidatable(edtConfimarSenha);
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(157);
        FHBox1.setWidth(533);
        FHBox1.setHeight(52);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(168);
        FHBox2.setHeight(24);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(168);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(88);
        btnSalvar.setHeight(40);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAlterarDadosEmail", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001374944415478DA6364A031608431FEFFFF2F09A4F449D4FF1D88CF33"
 + "32327EC26B01D4F00740CC468623419654002D9984CF020F20B51D88D700F141"
 + "206E00626134B5CB80F838946D09C451503D1A40AC08C48D404B1A0859900B54"
 + "3405C8BF03642BA3A9F504CAED40570FC4AB81783F106B62B384620BA0EAC571"
 + "5942150BA062C896E4C3E2846A16205972028841B43850EE332916608B64140B"
 + "A0665500A976207600CA1DC465C152A80184800550FD49340B7280D464988F71"
 + "59C002645B0331271EC36F03D5DE451724CA02225C8E130C0E0B880C2264002A"
 + "2E8E02F5FE21D602622319192C03EA8D26D60250321560809449C40090BA0F40"
 + "BD2AA458C000D24064B8C3D58F5A40B105C8150E030369910CA3ED813804DD02"
 + "4AAA4C6CE017102B002D784E69A58F0B5C04190E0E362A1988130000D0053128"
 + "1A34B73E0000000049454E44AE426082");
        btnSalvar.setImageId(310032);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(256);
        FHBox3.setTop(0);
        FHBox3.setWidth(8);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(264);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(88);
        btnCancelar.setHeight(40);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmAlterarDadosEmail", "btnCancelar", "OnClick");
        });
        btnCancelar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000017E4944415478DA6364A031601CB5802C0BFE8786323F7DCD53C1C2F6"
 + "6782C4AEC55FF11970D7258D9FEDCF9F4C1907B92EC686867F042D8018CEBB04"
 + "C88A00720F022DF1C665C95BCF68BEEFDFD9760199E64093E64BDBCBA7A05B82"
 + "61C153C7C4BAFFFF191A9184B05A826238CCB0FF8C65D207E775E3B500E465F6"
 + "DFBF763130329AE1B2046AF84E20D30249CD1136064E4FB103D3BE108C03B025"
 + "7F7EEF06324DD12D6165FEC78C6938E35136060E0F74C3715A80CF1220660362"
 + "4B620CC76B0108DC774810606360DCFD9F81C104AB82FF0CC7D8FF307A881E9D"
 + "F719971904F3014E4B88309C280B5E5B27F1FE64FDBF13355820C1852F091365"
 + "011EC389B604A705580D07060B2323031B5A70E1B5849168C381A985FD3783E7"
 + "17D67FCC58E204A7258CA4180E8B501C118FD512228A0AECE91C9B25C0E02B97"
 + "DE3FBF0BAF05E0C2EE15CF32A04C18038EEC8FD592FF0C8BA41DE51309167660"
 + "4B1C1A589E313EAC62FDCFD987CB70644B58191873A41DE4DB882AAEA90D462D"
 + "200800F164D519910AF68B0000000049454E44AE426082");
        btnCancelar.setImageId(310030);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(352);
        FHBox4.setTop(0);
        FHBox4.setWidth(177);
        FHBox4.setHeight(24);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftTrue");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item14 = new TFSchemaItem();
        item14.setTable(tbEmpresasUsuarios);
        sc.getTables().add(item14);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}