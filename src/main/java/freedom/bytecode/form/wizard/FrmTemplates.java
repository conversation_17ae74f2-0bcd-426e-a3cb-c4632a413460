package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmTemplates extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.TemplatesRNA rn = null;

    public FrmTemplates() {
        try {
            rn = (freedom.bytecode.rn.TemplatesRNA) getRN(freedom.bytecode.rn.wizard.TemplatesRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmailModelo();
        init_tbEmailModeloTag();
        init_tbCadastroWhatsapp();
        init_tbNbsapiMessageTemplate();
        init_filtroAvancado();
        init_popMenuPrincipal();
        init_menuItemAbreTabelaAux();
        init_menuHabilitaNavegacao();
        init_menuSelecaoMultipla();
        init_FMenuItem1();
        init_menuItemConfgGrid();
        init_menuItemExportPdf();
        init_menuItemExportExcel();
        init_menuItemHelp();
        init_gridConfig();
        init_FHBox6();
        init_FHBox1();
        init_btnConsultar();
        init_btnFiltroAvancado();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnSalvarContinuar();
        init_btnCancelar();
        init_FHBox3();
        init_btnAnterior();
        init_btnProximo();
        init_FHBox8();
        init_btnEnviarAlterarTemplate();
        init_FHBox5();
        init_btnAceitar();
        init_btnMais();
        init_FHBox4();
        init_lblMensagem();
        init_pgPrincipal();
        init_tabListagem();
        init_FVBox1();
        init_grpBoxFiltro();
        init_gpFiltroPrincipal();
        init_lfAplicacao();
        init_efAplicacao();
        init_lfDepartamento();
        init_efDepartamento();
        init_gridPrincipal();
        init_tabCadastro();
        init_FVBox2();
        init_grpBoxPrincipal();
        init_FGridPanel2();
        init_FLabel1();
        init_lbIdTemplate44002();
        init_lbEhChat44002();
        init_lbTpArquivoAnexoMensagem44002();
        init_lbDepartamento44002();
        init_lbAplicacao44002();
        init_lbAssunto44002();
        init_lbModelo44002();
        init_lbPrivado44002();
        init_edModelo44002();
        init_edPrivado44002();
        init_edAplicacao44002();
        init_edDepartamento44002();
        init_edAssunto44002();
        init_edTpArquivoAnexoMensagem44002();
        init_edIdTemplate44002();
        init_edtCelTemplateAprov();
        init_edEhChat44002();
        init_tsMensagem();
        init_gbDetalhe44003();
        init_gridPanelDetailEmpty44003();
        init_FHBox2();
        init_FVBox3();
        init_edMensagem();
        init_FVBox4();
        init_edCatTag();
        init_FGrid1();
        init_scCrmEmailModelo();
        init_FrmTemplates();
    }

    public CRM_EMAIL_MODELO tbEmailModelo;

    private void init_tbEmailModelo() {
        tbEmailModelo = rn.tbEmailModelo;
        tbEmailModelo.setName("tbEmailModelo");
        tbEmailModelo.setMaxRowCount(200);
        tbEmailModelo.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbEmailModeloAfterScroll(event);
            processarFlow("FrmTemplates", "tbEmailModelo", "OnAfterScroll");
        });
        tbEmailModelo.setWKey("440092;44002");
        tbEmailModelo.setRatioBatchSize(20);
        getTables().put(tbEmailModelo, "tbEmailModelo");
        tbEmailModelo.applyProperties();
    }

    public CRM_EMAIL_MODELO_TAG tbEmailModeloTag;

    private void init_tbEmailModeloTag() {
        tbEmailModeloTag = rn.tbEmailModeloTag;
        tbEmailModeloTag.setName("tbEmailModeloTag");
        tbEmailModeloTag.setMaxRowCount(200);
        tbEmailModeloTag.addEventListener("onAfterOpen", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbEmailModeloTagAfterOpen(event);
            processarFlow("FrmTemplates", "tbEmailModeloTag", "OnAfterOpen");
        });
        tbEmailModeloTag.setWKey("440092;44003");
        tbEmailModeloTag.setRatioBatchSize(20);
        getTables().put(tbEmailModeloTag, "tbEmailModeloTag");
        tbEmailModeloTag.applyProperties();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp;

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp = rn.tbCadastroWhatsapp;
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("440092;46001");
        tbCadastroWhatsapp.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsapp, "tbCadastroWhatsapp");
        tbCadastroWhatsapp.applyProperties();
    }

    public NBSAPI_MESSAGE_TEMPLATE tbNbsapiMessageTemplate;

    private void init_tbNbsapiMessageTemplate() {
        tbNbsapiMessageTemplate = rn.tbNbsapiMessageTemplate;
        tbNbsapiMessageTemplate.setName("tbNbsapiMessageTemplate");
        tbNbsapiMessageTemplate.setMaxRowCount(200);
        tbNbsapiMessageTemplate.setWKey("440092;46002");
        tbNbsapiMessageTemplate.setRatioBatchSize(20);
        getTables().put(tbNbsapiMessageTemplate, "tbNbsapiMessageTemplate");
        tbNbsapiMessageTemplate.applyProperties();
    }

    public TFFilterWindow filtroAvancado = new TFFilterWindow();

    private void init_filtroAvancado() {
        filtroAvancado.setName("filtroAvancado");
        filtroAvancado.setWidth(450);
        filtroAvancado.setHeight(400);
        filtroAvancado.setCaption("Filtro");
        filtroAvancado.setColumns(2);
        filtroAvancado.setTable(tbEmailModelo);
        filtroAvancado.setFilterStyle("fsAddCondition");
        filtroAvancado.applyProperties();
    }

    public TFPopupMenu popMenuPrincipal = new TFPopupMenu();

    private void init_popMenuPrincipal() {
        popMenuPrincipal.setName("popMenuPrincipal");
        FrmTemplates.addChildren(popMenuPrincipal);
        popMenuPrincipal.applyProperties();
    }

    public TFMenuItem menuItemAbreTabelaAux = new TFMenuItem();

    private void init_menuItemAbreTabelaAux() {
        menuItemAbreTabelaAux.setName("menuItemAbreTabelaAux");
        menuItemAbreTabelaAux.setCaption("Abre Tabela Auxiliares");
        menuItemAbreTabelaAux.setHint("For\u00E7a a abertura das tabela auxiliares e lookup, visto que usuario pode abrir o cadastro em um aba independente e alterar o registro");
        menuItemAbreTabelaAux.setImageIndex(200022);
        menuItemAbreTabelaAux.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemAbreTabelaAuxClick(event);
            processarFlow("FrmTemplates", "menuItemAbreTabelaAux", "OnClick");
        });
        menuItemAbreTabelaAux.setAccess(false);
        menuItemAbreTabelaAux.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemAbreTabelaAux);
        menuItemAbreTabelaAux.applyProperties();
    }

    public TFMenuItem menuHabilitaNavegacao = new TFMenuItem();

    private void init_menuHabilitaNavegacao() {
        menuHabilitaNavegacao.setName("menuHabilitaNavegacao");
        menuHabilitaNavegacao.setCaption("Habilitar Navega\u00E7\u00E3o Durante Edi\u00E7\u00E3o");
        menuHabilitaNavegacao.setHint("Quando habilita a navega\u00E7\u00E3o durante a edi\u00E7\u00E3o (Inclus\u00E3o/Altera\u00E7\u00E3o) ao mover para frente ou para tr\u00E1s o registro atual \u00E9 automaticamente salvo");
        menuHabilitaNavegacao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuHabilitaNavegacaoClick(event);
            processarFlow("FrmTemplates", "menuHabilitaNavegacao", "OnClick");
        });
        menuHabilitaNavegacao.setAccess(true);
        menuHabilitaNavegacao.setCheckmark(true);
        popMenuPrincipal.addChildren(menuHabilitaNavegacao);
        menuHabilitaNavegacao.applyProperties();
    }

    public TFMenuItem menuSelecaoMultipla = new TFMenuItem();

    private void init_menuSelecaoMultipla() {
        menuSelecaoMultipla.setName("menuSelecaoMultipla");
        menuSelecaoMultipla.setCaption("Selecionar Multiplos Registros");
        menuSelecaoMultipla.setHint("Permite altera\u00E7\u00E3o/exclus\u00E3o de todos os registros selecionados");
        menuSelecaoMultipla.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuSelecaoMultiplaClick(event);
            processarFlow("FrmTemplates", "menuSelecaoMultipla", "OnClick");
        });
        menuSelecaoMultipla.setAccess(true);
        menuSelecaoMultipla.setCheckmark(true);
        popMenuPrincipal.addChildren(menuSelecaoMultipla);
        menuSelecaoMultipla.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("Grid");
        FMenuItem1.setImageIndex(22006);
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        popMenuPrincipal.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem menuItemConfgGrid = new TFMenuItem();

    private void init_menuItemConfgGrid() {
        menuItemConfgGrid.setName("menuItemConfgGrid");
        menuItemConfgGrid.setCaption("Configurar Colunas da Grid");
        menuItemConfgGrid.setImageIndex(200021);
        menuItemConfgGrid.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemConfgGridClick(event);
            processarFlow("FrmTemplates", "menuItemConfgGrid", "OnClick");
        });
        menuItemConfgGrid.setAccess(true);
        menuItemConfgGrid.setCheckmark(false);
        FMenuItem1.addChildren(menuItemConfgGrid);
        menuItemConfgGrid.applyProperties();
    }

    public TFMenuItem menuItemExportPdf = new TFMenuItem();

    private void init_menuItemExportPdf() {
        menuItemExportPdf.setName("menuItemExportPdf");
        menuItemExportPdf.setCaption("Exportar PDF");
        menuItemExportPdf.setImageIndex(22005);
        menuItemExportPdf.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportPdfClick(event);
            processarFlow("FrmTemplates", "menuItemExportPdf", "OnClick");
        });
        menuItemExportPdf.setAccess(true);
        menuItemExportPdf.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportPdf);
        menuItemExportPdf.applyProperties();
    }

    public TFMenuItem menuItemExportExcel = new TFMenuItem();

    private void init_menuItemExportExcel() {
        menuItemExportExcel.setName("menuItemExportExcel");
        menuItemExportExcel.setCaption("Exportar para Excel");
        menuItemExportExcel.setImageIndex(22004);
        menuItemExportExcel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportExcelClick(event);
            processarFlow("FrmTemplates", "menuItemExportExcel", "OnClick");
        });
        menuItemExportExcel.setAccess(true);
        menuItemExportExcel.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportExcel);
        menuItemExportExcel.applyProperties();
    }

    public TFMenuItem menuItemHelp = new TFMenuItem();

    private void init_menuItemHelp() {
        menuItemHelp.setName("menuItemHelp");
        menuItemHelp.setCaption("Help");
        menuItemHelp.setHint("Help da Tela");
        menuItemHelp.setImageIndex(11);
        menuItemHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemHelpClick(event);
            processarFlow("FrmTemplates", "menuItemHelp", "OnClick");
        });
        menuItemHelp.setAccess(false);
        menuItemHelp.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemHelp);
        menuItemHelp.applyProperties();
    }

    public TFGridConfigWindow gridConfig = new TFGridConfigWindow();

    private void init_gridConfig() {
        gridConfig.setName("gridConfig");
        gridConfig.setWidth(500);
        gridConfig.setHeight(500);
        gridConfig.setCaption("Configura\u00E7\u00E3o de Grid");
        gridConfig.setGrid(gridPrincipal);
        gridConfig.applyProperties();
    }

    protected TFForm FrmTemplates = this;
    private void init_FrmTemplates() {
        FrmTemplates.setName("FrmTemplates");
        FrmTemplates.setAlign("alTop");
        FrmTemplates.setCaption("Templates");
        FrmTemplates.setClientHeight(511);
        FrmTemplates.setClientWidth(981);
        FrmTemplates.setColor("clWhite");
        FrmTemplates.setWKey("440092");
        TFShortcutKeyItem item0 = new TFShortcutKeyItem();
        item0.setModifier("smCtrl");
        item0.setKey("sk1");
        item0.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionPesquisar(event);
            processarFlow("FrmTemplates", "item0", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item0);
        TFShortcutKeyItem item1 = new TFShortcutKeyItem();
        item1.setModifier("smCtrl");
        item1.setKey("sk2");
        item1.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionIncluir(event);
            processarFlow("FrmTemplates", "item1", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item1);
        TFShortcutKeyItem item2 = new TFShortcutKeyItem();
        item2.setModifier("smCtrl");
        item2.setKey("sk3");
        item2.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionAlterar(event);
            processarFlow("FrmTemplates", "item2", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item2);
        TFShortcutKeyItem item3 = new TFShortcutKeyItem();
        item3.setModifier("smCtrl");
        item3.setKey("sk4");
        item3.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionExcluir(event);
            processarFlow("FrmTemplates", "item3", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item3);
        TFShortcutKeyItem item4 = new TFShortcutKeyItem();
        item4.setModifier("smCtrl");
        item4.setKey("sk5");
        item4.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionSalvar(event);
            processarFlow("FrmTemplates", "item4", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item4);
        TFShortcutKeyItem item5 = new TFShortcutKeyItem();
        item5.setModifier("smCtrl");
        item5.setKey("sk6");
        item5.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionCancelar(event);
            processarFlow("FrmTemplates", "item5", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item5);
        TFShortcutKeyItem item6 = new TFShortcutKeyItem();
        item6.setModifier("smCtrl");
        item6.setKey("sk7");
        item6.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionAnterior(event);
            processarFlow("FrmTemplates", "item6", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item6);
        TFShortcutKeyItem item7 = new TFShortcutKeyItem();
        item7.setModifier("smCtrl");
        item7.setKey("sk8");
        item7.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionProximo(event);
            processarFlow("FrmTemplates", "item7", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item7);
        TFShortcutKeyItem item8 = new TFShortcutKeyItem();
        item8.setModifier("smCtrl");
        item8.setKey("sk9");
        item8.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionAceitar(event);
            processarFlow("FrmTemplates", "item8", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item8);
        TFShortcutKeyItem item9 = new TFShortcutKeyItem();
        item9.setModifier("smCtrl");
        item9.setKey("sk0");
        item9.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTemplateskeyActionSalvarContinuar(event);
            processarFlow("FrmTemplates", "item9", "OnKeyAction");
        });
        FrmTemplates.getShortcutKeys().add(item9);
        FrmTemplates.setSpacing(0);
        FrmTemplates.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridConfig.loadConfig();
        });
        FrmTemplates.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(981);
        FHBox6.setHeight(68);
        FHBox6.setAlign("alTop");
        FHBox6.setBorderStyle("stNone");
        FHBox6.setColor("16514043");
        FHBox6.setPaddingTop(5);
        FHBox6.setPaddingLeft(2);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(5);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FrmTemplates.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(829);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(2);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FHBox6.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnConsultar = new TFButton();

    private void init_btnConsultar() {
        btnConsultar.setName("btnConsultar");
        btnConsultar.setLeft(0);
        btnConsultar.setTop(0);
        btnConsultar.setWidth(65);
        btnConsultar.setHeight(53);
        btnConsultar.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultar.setCaption("Pesquisar");
        btnConsultar.setFontColor("clWindowText");
        btnConsultar.setFontSize(-11);
        btnConsultar.setFontName("Tahoma");
        btnConsultar.setFontStyle("[]");
        btnConsultar.setLayout("blGlyphTop");
        btnConsultar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConsultarClick(event);
            processarFlow("FrmTemplates", "btnConsultar", "OnClick");
        });
        btnConsultar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000004BD4944415478DA9D955948636714C7CF4D62E292C47D19199BAB1444"
 + "A6A363C1818A2B8856EA8E4A952A06D427A14F3EF4D127518A880BB850B562A5"
 + "3EF8A80F76AC11DC1DC4655C06B154A341E3BEC6254BCFF932B9F5AA38A51F5C"
 + "6EEE77BFFBFDBEF33FFF73C2D96C36E8E9E991A5A6A6FEE1E4E49400FF614824"
 + "12B8BABA5A191E1E7E5D5050607D6E2D4780C1C1C1A0F8F8789DABAB6B083D7F"
 + "6ED09ACBCB4B30180CBD333333A5C5C5C5A66701FDFDFD7C4A4A8A4EA9546AAC"
 + "56EB6701777777603299E0FAFA1A767777FB2A2A2A7E181B1B333F0B484E4ED6"
 + "A954AA470092E3E1B8BDBD85A3A323F0F4F4848B8B0B585F5FEF89898929319B"
 + "CD9627017D7D7D3CE640A756AB4500DA9C9E777676D86965322928952AF0F2F2"
 + "02A3D108EEEEEE80B2C2F1F1316C6C6CFC3E35355582D1DC3C02F4F6F6F26969"
 + "690280E338766D6EFE0D6B6B1F715325787878B03993E91AE47227C068D90114"
 + "0A05A039E0F0F010B6B6B67A878686CA6A6A6AAE440074119F9E9EAEC3133100"
 + "7DB8BABA0A7ABD1EA2A2A284CD68D0FBD3D353D8DBDB03373737364F92C96432"
 + "72161E682D373B3BBB5F04E8EEEEE633323274784A0D4DD2C70B0B8B909090C0"
 + "64A1A48AC2E6242CC9676767A056AB00B567CEB2582CB0B2B2529A9898F88B08"
 + "D0D5D5C5676666EA30690C303E3E0E1A0D0F010101F8A15D32FAF85F07DBD8EF"
 + "F3F30BA0C028428A8CF2B4B0B0508207FB5504E8ECEC64004C9E864EF5E1C312"
 + "BC79F3F5275938B0E7050400DDE9D5CDCD0DD6C305F8FBFB83542A65CF5817DA"
 + "B8B8B82E11A0A3A38301BCBDBD35240F260B5EBDFA0A37E2D8C60F87D56A63F3"
 + "24CDC181115EBC08C0643B33C0ECECAC168B560C686F6FE773727204805EBF0D"
 + "21215F3269E8641209774F1A1B6E6CD79B40C7C7461601D995724511600EC480"
 + "B6B63601400E595D5D83D0D030269154CA319908466BE9A28D2D167BBD6C6DFD"
 + "05C1C1C1E0E2E2C200580BDAA4A42431A0A5A58501C84564B7F7EF67D1F75E98"
 + "E860FCE89639C901A08DCDE63B94C405DB840125DA87F0F0701611D975626242"
 + "8B452B06343737F3B9B9B9AC0EE8D494B8999959CCC36B78F93250482C398A0A"
 + "9D6C6A306C836EE41D7C9BFA1DF8FAFAB21A2000F6242D16AD18D0D0D0C0E7E7"
 + "E70B952C972BB0320F606969097C7CBC2128E80BB4A29A45717E7E0EDBDB7A98"
 + "9E9E86136C116161A110F5F61BCC831F6B19A3A3A35A54E331202F2F4F879529"
 + "F422B95CCE4E849509FBFBFB9FA2B0319DA9C991EEF3F3F3D0DADA0A111111A0"
 + "D56A51520D0C0C0C68510D31A0A9A989E5009D2000689E7A8CA305D04573944C"
 + "2A2C85428E4DD000D5D5D530323202E81C282A2A026767E7EF232323FB4480FA"
 + "FAFA4711382034C8AA8E06E8680974276B52CFAAADAD85C9C949888E8EA64EFB"
 + "23E6B44104A8ABAB7B1260EF3B1C3CF72F47D12C2F2F4355551559140203038D"
 + "58800598BF3F05406363239F9595A5C3F034CF6DC63D51D63447AD7C7171112A"
 + "2B2B616E6E8EF2777572729285397CC700480F292F2F9F469BFA383AE34399EE"
 + "3F3F7CEF806015435959193306CAB889EFDE32406C6CACAAB0B0F0673F3FBF30"
 + "94E89664B257ACF55EF55A858B06E5C171C7F7368448D11437F8E7F51BE6A314"
 + "FBD23CAEFD89739C0617C8F0E64CBD0CFEDF20FD2CB8DF354AED850630E1FFB6"
 + "E91F70B7FB1897F803840000000049454E44AE426082");
        btnConsultar.setImageId(13);
        btnConsultar.setColor("clBtnFace");
        btnConsultar.setAccess(true);
        btnConsultar.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultar);
        btnConsultar.applyProperties();
    }

    public TFButton btnFiltroAvancado = new TFButton();

    private void init_btnFiltroAvancado() {
        btnFiltroAvancado.setName("btnFiltroAvancado");
        btnFiltroAvancado.setLeft(65);
        btnFiltroAvancado.setTop(0);
        btnFiltroAvancado.setWidth(65);
        btnFiltroAvancado.setHeight(53);
        btnFiltroAvancado.setHint("Filtro Avan\u00E7ado");
        btnFiltroAvancado.setCaption("Filtro");
        btnFiltroAvancado.setFontColor("clWindowText");
        btnFiltroAvancado.setFontSize(-11);
        btnFiltroAvancado.setFontName("Tahoma");
        btnFiltroAvancado.setFontStyle("[]");
        btnFiltroAvancado.setLayout("blGlyphTop");
        btnFiltroAvancado.setVisible(false);
        btnFiltroAvancado.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFiltroAvancadoClick(event);
            processarFlow("FrmTemplates", "btnFiltroAvancado", "OnClick");
        });
        btnFiltroAvancado.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000031A4944415478DAB5965F4853511CC7BFE7EEBF9A492624B94C61FDC1"
 + "97D47C49374B8D42130CA187E825C269F4474C29CDA6A82DFF408886948BE8A1"
 + "F02D08FC43915A6E1A98FF7AD0974C48F620A8858973DBDD766FE7DE6928BA31"
 + "977DE1F23BF79EF3BB9FF33BE77E774678AAB1B131E4E6E6626E6E0EBBA1E8E8"
 + "687476762239391964747494D7DFBC8399B07848F747819348C0C964E0A534CA"
 + "657049156064843E9782A77D8288C703C6E306E7E221733BC1B02E10B7075287"
 + "030C8DAEC579C4DB67616A6B06A134DE76320F69A56588A049EBE21826E01933"
 + "1C27C6553A0956886E82AFCDF5508D7781D07B7E4F9111CA634970ADDA825E16"
 + "875C898B1366E42F7C47C7D1447413159CAD656B804B65901E3A0EB8D9A0014B"
 + "8A10548F74A25AA340C9AC042D5171E05FD57801AACC02280FC46F18FE9B5E7B"
 + "77DC0E77DB10B73C8F99C8182CCE5961EF7BEE05E8F20B1079F80808EB08BA02"
 + "412E460227DD875042F0F3C73798DFAC01EA1F37233125054EC7BF01D6A5502A"
 + "313132828AD2122FE0E5EB0EA46AD361B7AFEE0A40A50AC190650057AF5CF602"
 + "DE52539CC9C8142BF0B858486472B43F7B8A9EAE6EB0ACD3EFCBE47205B22FE4"
 + "A0B0E8FADF5CA1828FFD7DC8A3E615011F7AFB90959981E5553BE41206AC8743"
 + "A3F12106078770BBB8D82FA0B5A5056969A9305455897982C24354E8EDEBC7D9"
 + "AC4C2F60C06C864EABDD926C32993035358516FA92ED544CE1090909D0EBF55B"
 + "FACC160BD2753AFF004195959562341A8D013DDF1660A600AD0F80A0C2C24268"
 + "341A9C484CA26B2EC397E1614C4F4FA3BDBDDD678E85027481020465676743AD"
 + "568B6DABD58A9E9E1EBFE3770C10AAA8A9AD15DBD57443FDCD3E68C0BDF272B1"
 + "DDD8D0B03380BF4D0E1610F057B411207C8E076362025AA24D8075A3399CBE5D"
 + "3B353929FA4290923AB5B1A9C9E758A542B1D968DDEFDE23E3743A56A893FD89"
 + "1EDF7850510E756CACF8D34008D9765C187572FFA701E49C3FE73D326FDDBD8F"
 + "1BFA6B58F8B58440C4713C1886F8EC8FDA178136D30B3C697AE43DF40D060392"
 + "4EE9101A1606C2B90382F8AC9291C2B6B282F1CF66D4D5D581FCEFBF2D7F0022"
 + "9575683CD811980000000049454E44AE426082");
        btnFiltroAvancado.setImageId(50002);
        btnFiltroAvancado.setColor("clBtnFace");
        btnFiltroAvancado.setAccess(true);
        btnFiltroAvancado.setIconReverseDirection(false);
        FHBox1.addChildren(btnFiltroAvancado);
        btnFiltroAvancado.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(130);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(53);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmTemplates", "btnNovo", "OnClick");
        });
        btnNovo.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000046A4944415478DA8D967F681B6518C7BFEF5DDAA5AE6E36D58EC2DA4E"
 + "BA5A1D42BBE93A990A56F6873237D81036D0BF1404C76C374BA508ADCC8D29FE"
 + "530A8AEC0FC58242152B4341879B0C6BE956D0DDADBF67D35FA95996D4A5499A"
 + "E4EE92BBBCBEEF2517EFB254FB1CC7BD6FDE7B9FCFF3E37D9E0B414E4856446C"
 + "440420B33FE3C64B78C3B5C5F54AADA7761757E10BFB26F5B8FE1516F1293E86"
 + "62EAB56D7BB4BBBBFB2CA5D4A98C10C74B5C7C311FFACBFBF71E78EE40DDF1C6"
 + "E3B85F2CE79BC89A11C7C0AD017A65ECCA14D238882E2CD9F736AFAE46244555"
 + "210884E63CE29ED9C750D20A7DFEA75652595B490F3DFC027119693CE56E3501"
 + "D7D5ABD0C512FAC3C22532228F4C62062D0E402412BDA16A1C20C05268BFB9F4"
 + "C97DF820781E2F3F7E18D56E116E4DC3B18A77CDB5AF57CF43DDB40901D5C0B7"
 + "13DF43FB45EB7200A2D198A4A534D36AA18807462683677F7C86CE5578C9139B"
 + "EBE80EB748B6A674BC5EF3396571249FF95E43B4D445175583FC9158827A4D1B"
 + "7300626B6B528A594404A16888946412DB2F6DA7C6E634E9F3F4D23D35BB89C8"
 + "343C56DD6486683A70130605BDB12C91F6F069646248D9014D6B1C904EE74322"
 + "1484281A8DA26EA80E99521D7D15BDD85DD30C0ED855DD6C2A980AC81C006959"
 + "46FB2A03A8D01D80783C2E738060599DCD45DE0366009EFE6D3F9D77CF916DCC"
 + "D29DF7816CCB005D0D574D0F3E9C6D455000F52641824C339DC6AC139048C869"
 + "0B209859F817C6C67CAD73B8935E485F20656CFF8E7206601A3E7A240B78E74F"
 + "0660E0C5388859045FE0130720914848BAAE6743C2AC178A9C22694AC2D1DB47"
 + "11D00228D3010F2BBAF71A7BCDB533B74E23CC3C525C6C12C66D9CC461072099"
 + "4CCA268025397F8A6C632EAAAAD2C1E141D22576D1A01A2424068869981E1825"
 + "CCAD2D6C1C43109DECBA834127405124230BC85B6FD544AE9D98CF582C86A1D1"
 + "21F427FA315A368A10BBB854B16B9FB20F175FBD78121ABEE4AF3A008AA2C886"
 + "6138AC66009A6B5516C49CA75229EAF7FBC9FCFC3CC2E1B0F99BC7E3417D7D3D"
 + "6D686868612DE7F7C25ED4C4DA849CE1005E68B9245BA7C8E6813537956658F1"
 + "B1DB1C8BA2681AE472B9F6B0DFE47B002CBE32AF56C106B014167A603D7973A4"
 + "59C91921D0D2D29275009A26710F0A7B91657D612E7838F9A160E132C75C4457"
 + "092A1ED85A1CA0699ACC5D261BF08029A6CC63C2AB3B1289507640080B0D8647"
 + "AE5D7FBBBDED0803DE591720D87A911D60CDD966C20E040D8556CC24B339ADAA"
 + "7A884C4C4ECCB4BDD5768841BDF9CF490140CAB098162B30FB98BD87BB77C3F0"
 + "7ABDA87CB0123BEBEB71F9F2CFCB6F9E38F1A2FF2FFF944DE7BD1EF0A4157C64"
 + "1CA78867931524F12D2FD3E9E919D2B2F7498C8F8FF9DB4F9D3AE89DF5DE4481"
 + "3801A9944C6D39B0626E07F023C99A22999B9BA7C15088A84A62AEA3A3E3C8C2"
 + "C2E2388A482140CA01D60D11CF11036061C987A1A15FE573EF9F39B6B2F2F72C"
 + "D6113BA0B1A7A7E75C7E8190A21B780853BA81403014FDEE9B81B3AC852FE13F"
 + "84148C37F6B725C762B7F17F2FFD034C50719467FC49DB0000000049454E44AE"
 + "426082");
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(195);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(53);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmTemplates", "btnAlterar", "OnClick");
        });
        btnAlterar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000005094944415478DAAD956B6C536518C79FE7F4D0DDC82ED95A262BB0AC"
 + "026EC16D201035121308514CCC844020881335442E11818D6BD84C4087CB3E90"
 + "4C037E21A21F34442111A209E3E218868B894047071B3BDD4AB76EACDDADD773"
 + "DAD39EC7F7F4E65A36C2079FE4DCCFFBFF3DB7F77D11FE476B5DB76259B63FFD"
 + "3CA728D7291CDEB9B0A5C58AF18F1A8D465F5757F70511258F42047C0EF190B7"
 + "1B372F685B1FFAAD38CF67B2A35796074196DF9838D63836362E8892041C8784"
 + "5163FA49F72A8C20F214BDAA03C3012729E6B53830E8849AC634DA6A1BC5E93E"
 + "1F8478FE4212607CDCD52D0554000709C189473CA2A86EE459098B106CDF00A2"
 + "DB0A1F7CA5815E7B0066F276D89F0964EAC4F3490097CB2D04828188D7DC2411"
 + "40FC59D56657A0308AE6ADA011EFD2278D1C9A1E89A048763ABD5F0E8B226F5A"
 + "7B48AE4A02B83D1E211860008E9B3C459100309122FFA32398215E81DA131C5D"
 + "BA3982A2D701273EF75361BEC6B6FA90BC667084BA26024A3C2A40961329E126"
 + "4953BCE852DF0F90E6FA198EFF9A09A7CF09E0F7B9E068F528AC7CAD0056EE74"
 + "7CD865A38BECB7B12480D7EBB5A8002EEE75B4164F1539E8BC82BCE35B3A7B43"
 + "87479A6F422020D1F6779EE0A6B73361A4A0892A977DBA9CE9FDC30E6F32C0E7"
 + "B3C8710017A9C27FB01820E4BE4FD0DF88B78599B4A3BE05032CA5554B07A876"
 + "830687B26A000B56D3A2458B5E657A77D4EE4D02F87C3E21140A453D65DEA7A6"
 + "88151094C70D2038F2E1A37D17C1EDF6C2EBF39CD0B04586E1B48DE09E5E0D85"
 + "8585307FFEFC054CEF41BCD31200BFDF6F89005891135D14BB57641784ACC760"
 + "D89341D57BAFAA3D4F65063736EFF0823F633959B9EDA88ACF993387F47AFD3C"
 + "455184A701A22884A38084F7EA9C002508416B13484184CDFB5BA14BB083215F"
 + "84933B4781CF29877BFE5DA0D3BF002525259108D2D3D3E73280E52980288A96"
 + "70389C1C010205ACC7D93904DBEBAFC1AD3B56CA9B2EE3C9CF86495F68C06BCE"
 + "5D9057308B8C4623161717435A5A1AF13C3F0540922C8A0A50275AACC801DB77"
 + "C4830B0F345E873F5A7B2143AB50F3362796CDCDA1DFADDB3033D7A87A4EA5A5"
 + "A5989595A5EA4C0D901820AC2891CE510192FD0C68C55BD474CA8C3F9E1F60EF"
 + "098E7D3C4ACB17F378AEB39A28A31459CEA1A2A282727373631173A4D54E9B02"
 + "1008086A04F1B568C0FC13FCD9F23D349C1A501D83DAB52E787F6518CE9AAB60"
 + "9C2AA1A8A8082A2B2B41A7D301138CAECAFC34C8CBCD991CC07ADAA2FEA8A6C8"
 + "EF7A8243D656103AFFA6A3CD5771D52B63B06F830417CC6F92E05EAA760C9597"
 + "97A34EAF07AFC743AC4190A506FEBA71B375EF9EDDEF058341D7940016010D0A"
 + "6DE8B0B68067E401897E172E9CD5079DC3F3C034B28A66CF9E8D068381B2B3B3"
 + "B1BFDF0EAC31586BEAB0A3C3DC5E5353BBCAE9740E24B6931480A0B00D476DD1"
 + "53DF1C80B2223658F683A29D0B9A9C25A037BC0CFA193340ABD5B249E6819E9E"
 + "1EC82FC887178D46B87CF99275F79E3D2B846EA13769BF4A8D40DDD11E5BADB4"
 + "7ACD1ADCBC7E196CDCB4857267BC84ACFDD45D0FD8776213126D7D7DF4F06127"
 + "2E5DB218CCE6FBB683070FBE6532B577A6EE74C98060D0422C45A67B7749C3F3"
 + "5851519958ECA27B0DAAC524B628A2C5D243430E074A92BFABEEF0E1773B3A1E"
 + "74C324960A10285AE4E4253A261E03000340EF631BB4B55DBBDDD4F8F53A5687"
 + "7E98C226028AEAEBEBBF8CBF8B0BA79A9AC260280C83438EF173BF9C69F0B8DD"
 + "0E7886A5AA70F0FC46B1E399F62F6A8BA82D8608FAC10000000049454E44AE42"
 + "6082");
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(260);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(53);
        btnExcluir.setHint("Exclui o Registro Selecionado  (CRTL+ 4)");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmTemplates", "btnExcluir", "OnClick");
        });
        btnExcluir.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000040C4944415478DADD955D4C5B6518C79FF79C7EAC2D1F1DED6A0714DB"
 + "B51DA5C810506250468A0A9A4CB3E885268652A98BD178B3A98BD311B3B8442F"
 + "8CEECEEC032C5DA66E5113B338B31AD814CDB61841663F28E3F0D1A1B44821DB"
 + "282DB5F0FA9C0354C533D4446FF626BDE8F33ECFFFF77CF52D81FFF9905B13F0"
 + "31CB166B1A1A7C098EFB76361279B685D22531BF0E0046ABD31DCEAFA9B9377E"
 + "FE7CD3E3C9E4C4DF023E0230B0003DF6E75A2CB14BDFC3745FD09300703F0DF0"
 + "27C86114DF880C4D85D555F8A083FADF39C2A143E31300576F0A380150CC009C"
 + "2B7DD4612EBBDB417ECD53C08523EFD1E9C048D72C425E58811C42F14DBC7899"
 + "C955E772838C4869E8876FC8E007A787D1C1F114C08428C00B10B438AA6D96EA"
 + "06A0999584F50570E9B8078682A3DE30423268AA44717399D159FB244ACDCC09"
 + "6E4C6E0E8C0CF541F8D417834E00BB28E07D80A31B8DBA672A1F6AA68B2976F5"
 + "8EB2C53AE2FBE414044363C7794385EDF696C69D8FD1A5E86CD6476E2A24FD27"
 + "3F847870F418B6739728E02D4C4483D9151837B5DAB7D793F475A10A4AD18F31"
 + "6D86D3673E0706BFEE687E982C45A6E86ABC5CAFA6C1733D241E8E786E60957B"
 + "FE30AFBF0CF96016A26DB5DD55030BF1B4605FC48FC45228E016B94904F15406"
 + "94E64208747F09712EDAC58BEF5DB30CA26B7A002105FC108D1A57A9DD4653D1"
 + "A4E02744124255C5C504144ACAE46748A8FB6B981E8E797012EE7D6BC46F0AE0"
 + "4FFB32A4536BD4386D362BCC47AE0391CB8162D689F171906A59984CA6213E3E"
 + "EB45F1B67611F17501FB119087001D02AC1B242431185BBD127AAFB2E9E99554"
 + "864C8D4D772511F0FABF01BCB22CDE719B51EBAA5248E9426892D09539085BC5"
 + "378ADF1C7B11E9C72A62A3BF7810E27EE39FB4E8657EA5515C6FD2B9EA1532C8"
 + "0427848CF9C84479091F4095810861562A91A0AD3795A151EE67BE12F79BEB0D"
 + "798F42C6E426D31D9BCD45ADCD0A29807F2CDB931BDBACB03B9110420EA95490"
 + "7B79E8F7C06D5BE12C56121D1EEF526ED9E2DECB71E26BFA5A91F69841AD76EF"
 + "94B0543210CEFE88AEDD594E5E44F16B57C6BC7CA4DA6A72BE9BA3A279FDFEAC"
 + "4F067DCECA3600A3D674B6F87C6E51C0FE125DB0B6B2A2ACF1423F85E919E16E"
 + "AEAA82BE944890A9A1112FF6BE2DB3BCAE9D855BCD2D6FE7A888AAEFF272915A"
 + "0DB9D850073F7E3710DA1D89883F15ED4AB9819D5FE8B9E7FEEDE6FA8100CC97"
 + "14C1C1C43C70E1613EF3B6332BFD6DC2394910622BB538F7A994A08CFC04BD95"
 + "77C0C5EEAF38562673B4A7D3E28F1D7F70DD0C68ECA97EA4C9E21F19077F20DC"
 + "859D6FFB6CCDF07620241F2155F6D2D6BA8A72EA3BF92987B36A3CB0DE73BD7A"
 + "5E35E98D542EF5A526677AE373C95DDEC525D11D6F6559A6D660386A379BEF0B"
 + "F9FD0F3C1F8B5D5DEB738BFE27FF97E7376E298F288718CA1E0000000049454E"
 + "44AE426082");
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(true);
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(325);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmTemplates", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934"
 + "31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2"
 + "D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE"
 + "6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5"
 + "6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013"
 + "090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF"
 + "349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E"
 + "8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871"
 + "175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39"
 + "FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622"
 + "F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673"
 + "BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E"
 + "01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146"
 + "0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219"
 + "506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6"
 + "0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991"
 + "CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313"
 + "9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0"
 + "39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D"
 + "AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7"
 + "C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743"
 + "FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE"
 + "E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660"
 + "474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF"
 + "D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61"
 + "33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E"
 + "0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE"
 + "F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF"
 + "921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5"
 + "A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E"
 + "9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163"
 + "8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09"
 + "C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C"
 + "D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E"
 + "23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA"
 + "FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E"
 + "9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1"
 + "5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A"
 + "25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2"
 + "FF02B065C443D9FE4B070000000049454E44AE426082");
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnSalvarContinuar = new TFButton();

    private void init_btnSalvarContinuar() {
        btnSalvarContinuar.setName("btnSalvarContinuar");
        btnSalvarContinuar.setLeft(390);
        btnSalvarContinuar.setTop(0);
        btnSalvarContinuar.setWidth(65);
        btnSalvarContinuar.setHeight(53);
        btnSalvarContinuar.setHint("Salvar e Continuar");
        btnSalvarContinuar.setCaption("Salvar Cont.");
        btnSalvarContinuar.setFontColor("clWindowText");
        btnSalvarContinuar.setFontSize(-11);
        btnSalvarContinuar.setFontName("Tahoma");
        btnSalvarContinuar.setFontStyle("[]");
        btnSalvarContinuar.setLayout("blGlyphTop");
        btnSalvarContinuar.setVisible(false);
        btnSalvarContinuar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarContinuarClick(event);
            processarFlow("FrmTemplates", "btnSalvarContinuar", "OnClick");
        });
        btnSalvarContinuar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000005FE4944415478DAB5557954546514BFF7BD99371B30808A9080DA2925"
 + "72D70C4134528F8A0CB8800B02117A723905B479424AFBC7E568E49AB96B2EA5"
 + "A0861B5A4A258887724549CD1DB344901950646698E1DDBEEFC14C8EF96F6FCE"
 + "B7BC79F7BBBF7B7F77F910FEE7079F7E999B9333CB6AB5060882083DFB0E80F0"
 + "1EBD4052A900058109B21FB221B4AD4C9E48E693B2125B5B5A9C50597909CE9F"
 + "39CDF62D6034FAD47A00A4A7BD25D7D4D4604848088D8C1F8FD1D16F802020B9"
 + "0CE113D34D7C76DEBE4996AD1BD130280A8C23479120494C90A0B8F8273A5CB8"
 + "5791D76834E4013035395976389C60F4F181D8B18930E0F54878627378BACC4E"
 + "381B6F83B13C131AF37560BF750FECE13DC12F6F1518FD7CA0FCD44938B8AF40"
 + "919524C993A28CF4B7A96D4BA6099370E48861200A82DB033EB7D8EAA8E9EC18"
 + "7C50FD372D592861C65D0BC8B24C21595918FCD97C38FAE3713AB8375F91574B"
 + "123D4B911B20216932C68E1A01AAA700E4161B349C1E4FB6FAAB98BA40A48AAB"
 + "161CA0AB870C3B52506C0AF6FD7A1D1C67141DD8B31BDB3CF004484B49955DFB"
 + "84A429101139D84D110FA4743B1B8CF21998B15484D2736600472D6CCF4508F4"
 + "0D87C6F01DD0B173089CFDB50C0EEED9AD9C51ABD59E14A5244FA55665440913"
 + "93312626C61D64C7AD25E0653D0239EB880A8BEF6363430DADFFC889FD7B0543"
 + "43E836320684A1A4D1C2C9921374686FAB070CC0D383299326BB298A4F9A8283"
 + "87B02C4224AA29403F6B3EAC2954C19A9D97C95C578B8BA75B69F4605FBCE7BF"
 + "1AB4ED0790AFAF2FCB5E11CACB4A18C0AEE7034C4A9AE8A6C89438057ABD1609"
 + "B2A50C5E1276C3E1DFBC2177E92F60369BE1BD8406C830E9E012E582CD6B08F8"
 + "F9B7039DDE001AB50895E7CAA168EFAEE75394342191DA08A7B8A4641CD82708"
 + "829DDBE9C26D3F9CF94921D431E5F1031FD2A7E96AFC439E4116AD09FDDB75E0"
 + "00849C4B566C15A7CBE8C8BE568A542A95A70713C68E737B3028AA174C8F75C2"
 + "FD7A1F48CBCA87EA0735D0AF6B0DACCC44385B3D102A1A4CE0ED6354AC174515"
 + "389D4EA57AEF5CBF02D72A2F283A54CF7A30363E41F140AF219A37BB2B7AB7EF"
 + "CE94EFA29BB7EE62A8FF43D8F4B1032AAADAD3B2FDA128491A32E8F5C869E03E"
 + "F344680BA03BADFFE3417C9C89542CEDE74C0BA5B09E9138EDC31D70BEE21AF9"
 + "6A2DB8654E1358AC46C8D9124282A841A69CB45A2D2AA5CDB9E10DAA8D5ED75E"
 + "258A9E00A6D8587A67821F0C1B9D48D9B9DBB0B8E4224842236DFCA0017D7C0C"
 + "90B5B63338643D797979A1B7B7373F8C326F76AC92193DC82A5AA96AF63CDF83"
 + "0D0B47C96919B360FEE29DB0E7D03976DA062B669BA1EF2BDEB0F54C22A02604"
 + "020303212C2C0CDAA8510C6E6E6E86ECCC4CE05D98EF3B060428DF4451FC3706"
 + "37BEEF96D6A9CFBBDFACDE5202EBBEBDC08C72E2E7290F212E4AA2B9DBBBE343"
 + "EB0BA0D7EB212535950C060323A1B5005D567F363717B9429BCDA67C6F0368F5"
 + "60F7CA9962FF7E817F959695765CB0A14A39303BCE8C33E209369546D1B22D95"
 + "4A3019A7B071F3664531A38458E6F09567102D5AB05001B03300D6A63D014A0B"
 + "E60DEDD0E5E59F4B4E14C3971B4E8269603DCC4F6F868DC7BAC0657304FC70E4"
 + "2878791994D8AD58B54A4949575ABAF6CBF3F2DC14F136CD87126F0E70AA70DE"
 + "72B564CF7C54F73B563FA885913D6ED0AE62C4EFCABA41F49021B4AF600FEA74"
 + "3AB035DBE18BBC3C97E5E47038902B672B7DB57295E201F74652AB5BD397D188"
 + "C9E36284E1D15DEFBC18F438D8D9DC88758FBDE058E99F547EB101A30647C318"
 + "531CBD9F958DDC325125C2C2458B1400AE88678E0B60FDDAB528320F3830A788"
 + "DD23AD00C3864644D45A1A4FC54619AF5EB96EDE7FE7BE90C5BE680526101119"
 + "096F0E1FA624818B92A6A6260F8A5C63D3FA0DFC7252686100CA19AE03BB76EE"
 + "946AA97FA466C129B2DA1D1DF53A5DB6BF9FDF50CE67EFDEBDD5E13D5E55B18E"
 + "2A22FF839D610A049959C9222DF30B8E5FCF2C739A0FEE3F60B3DB6DBCD590A8"
 + "882ACF031E372D9336F0BAE00D900D5F36749CCFF0F0706D5050908AF12FF0AA"
 + "E59738BFA518BF322B2299BDB7B07787C562B11615153DA9AAAA929F2A2B9E6D"
 + "B67F007731E966AC4002910000000049454E44AE426082");
        btnSalvarContinuar.setImageId(22001);
        btnSalvarContinuar.setColor("clBtnFace");
        btnSalvarContinuar.setAccess(false);
        btnSalvarContinuar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvarContinuar);
        btnSalvarContinuar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(455);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(53);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmTemplates", "btnCancelar", "OnClick");
        });
        btnCancelar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000004EF4944415478DAAD566B4C1C55143E7766F6BD145020744BA98F1608"
 + "A820DA508BB42A84B62A264D6A24D158E323C646230D4D7D24901AA8466BD336"
 + "1A9AF84B1B7F580BD49ADA6A0B518A3450915D0BE9F2461E5BDEEC93DD9DDD9D"
 + "39DE997D53B49A7893D9993B73EFF9CEF79DC75D02A141828385FF6120A2402F"
 + "94EDC6BCCFA9A9A9A90BBD8F0E42E216DD6E5027A1BEBEBE4614C5FE95000556"
 + "ABCDE8F17A8161088618491B629FA50D28D30DDD4346306C4BA150A05EAF2FA4"
 + "00A65B006C367B8F9797001888188CBDC28C827623F358431CC7814EA75B1DC0"
 + "6E7718791F2F7BCDACC200C273695FF81E048930E05816B55AEDEA000EA7D3E8"
 + "E32900C3AC2E5150E38844244C254622960268349A28C08F8D1FAA76EE799FA7"
 + "4BF31D0E0AE0F7CB12D8E60660B4F7023817478165782A9B0F44810351544342"
 + "F206D8545801A9190510C5080E495EB55A1D05B8D6F2F96C47EB570D750DC673"
 + "13169B716176047A5A8E6362A28BE43D940177A45136E00AC551DAA143EB0221"
 + "E6EE29B42D2A49D1930721393D3BC24662A852A9A200674F96FAD3D6A6B2478E"
 + "5F3A7DF8D32F2BCD9D0DB07D470E26ACF1DC427FC51CDDCB6AD27E6100EECADF"
 + "8BB95BF64400944A6514A0E9B392C0CE8A3CE6BAC9014EA7171E29310043FCE1"
 + "8AF91799CFC1CF1747213DFB79C82FA994258B03683C511C28DF95CBCA9910AD"
 + "29D94BBFC30A9EA9110CCCCF11F00BC0A8B4A04C31A026732361944C8419028B"
 + "DF37F79392678F41C6BD05486B210A70E6D816A1AC3C8F50B783CB25B55104E7"
 + "C4EFE0991905AF4B8489291A0587086A4E80BB937D90A055803E771BA80DEBE9"
 + "6251F6C8ED0538FBC322BCFEC177F10CBE3DBA5978A22C9701963A42931FA863"
 + "CE9B9DE85F9E2137CC2C589C45985F5C49925232C0BE340B3D6D67F04EFB4FE4"
 + "E1756ED46FDA4A546B374418B75D9D82ACC76AF181A21D51806F3E29141F2FCD"
 + "22C0D17CE780F83C13E075F6A1CB4DC8B5EE62D8FB6E13320C1B0EA06CACFD7C"
 + "0359F8E53D2C48779384FBCA81516BE5A2732C0BD03164C0970E7E1105387528"
 + "4B2C2DA39A6A54489454204E04419CC280384EFABA11F4490770EBD307223545"
 + "42557BA5E9301AACCD44AB53D1B8645255790262004EB75A71FF89AE2840C3FE"
 + "F4E5CD391E0D9BB006B83549C0E913A847B460540140CE02FD9D023CB8FD2418"
 + "EEA9801810989BE8034BFB1148532D8222391330E00614BC70F1AA055EFDA8A3"
 + "90266010E0D02BEB9EE179B1C43CEE4D7BF9B5B75E945293EEA7B210228544AD"
 + "51D2185490C494BC38068EA5691C683D4A52D91910153A14033C61580E4EB5CC"
 + "77D6379CDF2D8A3813D78B6885E77B3CBC89520B363BB9DB31B2E6B1ED5A9A0B"
 + "8240E66F8EE0E46F5F9380CB029C3A1193330A48D790A7FFCDAA772A68D31C8E"
 + "9C0F311593CFF3BC51A469C7D0A4908A82A51EC5CA127EA6EB60F07A1B4DE50E"
 + "48DBB80DD6E73C0A972F5F9A7C63DFBE5D9629CB8DB80368058049AA01EA238A"
 + "4280704A0D65C6C6F619E938C4C5B949326EFE1597A607C9FD252F80B177D0F2"
 + "7655D553C343C37FACACF378009FCF442D53003F9548411562E32492E2E3E3DD"
 + "689D1F23B39366B4D9DDC4EA4B1AA9AEAEDE3D36F667EF6A8D64258011E51848"
 + "DE32D1534C960C2566B465FBC1659F86C9F161B8D26536D5D57DFCDCFCFCC2D0"
 + "DF75AA5880ECDADADAFAC887D8B3048310F22F95D0E7F3C0CCDC9CBDB9F15C9D"
 + "D3E91AFFA75648563CFF97BF2D12A270BB457F0173456D3788187BC400000000"
 + "49454E44AE426082");
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(520);
        FHBox3.setTop(0);
        FHBox3.setWidth(26);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setVisible(false);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnAnterior = new TFButton();

    private void init_btnAnterior() {
        btnAnterior.setName("btnAnterior");
        btnAnterior.setLeft(546);
        btnAnterior.setTop(0);
        btnAnterior.setWidth(65);
        btnAnterior.setHeight(53);
        btnAnterior.setHint("Registro Anterior, Estando em Modo de Inclus\u00E3o/Altera\u00E7\u00E3o Salva Antes de Mover o Registro (CRTL+ 7)");
        btnAnterior.setFontColor("clWindowText");
        btnAnterior.setFontSize(-11);
        btnAnterior.setFontName("Tahoma");
        btnAnterior.setFontStyle("[]");
        btnAnterior.setLayout("blGlyphTop");
        btnAnterior.setVisible(false);
        btnAnterior.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAnteriorClick(event);
            processarFlow("FrmTemplates", "btnAnterior", "OnClick");
        });
        btnAnterior.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000002D14944415478DACD95DF4B544114C7BF737FBAEBDE9D9556D6D0BCF5"
 + "52909804B5ED635B2F56C646620FF9EB21080A0DA27F22ACA0170BDF7AD035B4"
 + "4042C2CC82B61F141A6248A5695A8A3F42CD5FB96DDEB9D3EC2E428FEBBA4267"
 + "F8DE732F73673E73CE99B997609B8DFCDF8052108FC77339581C3CDED3DB53B1"
 + "D2B1C232073805A718DD180A866A63D118BA5F7553BBCB5EC90CA00CA6AAA86D"
 + "55A12A7FF1816234DD6BE243C343144F900140198EE6B8735A2BCB2B7DDE5C2F"
 + "613643F841988F8E8C52746D0520F20D0575668179337432A42ABA02C619E280"
 + "CEEE4E8C0F8F533C4E17703A99EF92FD25B581400036B189989A5BDC4A441079"
 + "1BE1139F27281EA5033883425993DB0307037E73B70926897593448BAF3F71ED"
 + "FFD0CFA706A7283A360B388BA0D37086FDC57E9F410D3085C1922D3099C541D8"
 + "68635FC6303D304DF13045C0DE4BFBC8E8CF917A4A69439159A449591261AA98"
 + "2A292E4044880B483C5598FFB6C067DFCF50B4A70828BD7EE2D6CCD2CC55D556"
 + "60EB3698CE92CA62E03A0774315027E032872DD9581C5BC2EC3B01684B115075"
 + "B7FA7664F0E595C9AFDF095CE012958810C43DB88B73DB298AEC605C80E23B0B"
 + "DAA4CEFFF4C628EEA70838722D20F5CDF5D5C9D94A035F65DABABE4E4045875B"
 + "C800174A80E1105E13018D3878EC4D94A275B345AE2641D5ABB5CA44F2FD56A2"
 + "4840A898D82DC6B9857709EF001C83D93CFAE21745389D6D5A0D53CE55DB1D1E"
 + "C7A15575391905FD47E2D9D96F60AD6785A225DD83765E1C34AFDCE8DE65D4AE"
 + "395761191649444291489D6B80F2D5A7CB02C0B7F0A92817EFFAA47A638FFB86"
 + "E58D69516734598B2C11C4780E5F7EBE48D1BC15C0865D94820E333BAC15C8BE"
 + "256D11F122D3F91D588A2C640810B70BC454F3B3DA3D85EEC373FC07722C2F5F"
 + "783D974140DC6A8893E4297776EEF1D578741D9F9E8D51DE92A91FCE869D2344"
 + "CD57EBF2728D63D31F172BAC6666651690826D3BE02F3B28322890C2D21D0000"
 + "000049454E44AE426082");
        btnAnterior.setImageId(14);
        btnAnterior.setColor("clBtnFace");
        btnAnterior.setAccess(false);
        btnAnterior.setIconReverseDirection(false);
        FHBox1.addChildren(btnAnterior);
        btnAnterior.applyProperties();
    }

    public TFButton btnProximo = new TFButton();

    private void init_btnProximo() {
        btnProximo.setName("btnProximo");
        btnProximo.setLeft(611);
        btnProximo.setTop(0);
        btnProximo.setWidth(65);
        btnProximo.setHeight(53);
        btnProximo.setHint("Pr\u00F3ximo Registro   (CRTL+ 8)");
        btnProximo.setFontColor("clWindowText");
        btnProximo.setFontSize(-11);
        btnProximo.setFontName("Tahoma");
        btnProximo.setFontStyle("[]");
        btnProximo.setLayout("blGlyphTop");
        btnProximo.setVisible(false);
        btnProximo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnProximoClick(event);
            processarFlow("FrmTemplates", "btnProximo", "OnClick");
        });
        btnProximo.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000002D74944415478DAC595EB4B145118C69F7766D4352F5B9BD18D0417CC"
 + "BE18B54A164B451218D2F910949054104597DD3E86FF40FD0559081588117E90"
 + "58DB218B02B13E15155D28A4A012B58DB5746D2FCDCE65674EC7DD8ABE58BBBA"
 + "D1393CBCCC303CBF77DEF75C08FF78D07F03B83BDC756D4D6D57D547EA397D50"
 + "BF5F7400316281FD81F083E70FACD7EF5E9F85838BB80D5EBC12ED056B6F6D57"
 + "77EFD8CD87EE0E61F8E1709F781BC42D68C50130B0567FABEADFEAE78AACD0E8"
 + "E8284277428FAD8C75404026160FD807E66FF1877D1B7D902539ABD8971842B7"
 + "4253F164BC5340461607E8006B6E6E5637D46FE0326527E66646CFF07BC3F7CC"
 + "4834D2051BDD18FA735FE607748235FA1AD5DADA5AAE9092B3E7428ECC255BA2"
 + "672F9FF137EFDEF489E6077173FEBECC0F3802D6E06B08D7ACACC16FE690ED9C"
 + "145B412412C18BB72F9ED886DD8110C60B031C05F3367955D7721717E6A4380A"
 + "E48C3C272E5BA260562E6A490DAFC65F451D83771AFDFA48FE80E360755BBCE1"
 + "D29A12488E04B208DCE4209D400641D22528A6928D30C4F72E326BDDEB0E0D9E"
 + "1DBC911FE00458E9E632D55C667058A03953392D83BE11A714114F72EE241CE2"
 + "710ED990F9FAC6066B47FDF6C33DC77A06F2039C2256B6AD4C3556E85C6448D9"
 + "36A6849262D524C4732217CB1C17EC254EB424AD74A67BB5024A749A58F9AE72"
 + "35BD5AE3C29C8431B28AFF008858419594368C27CEACD87CD70B6D7280D8923D"
 + "15616DAD483B9135CEE92B20276594DB15487D4EF561D609A27F21CB3448AC72"
 + "4F959A5A95F8997116E04A8955952835BF4DA6BA1073BA31B0D08D764662D53B"
 + "ABD584E7EBAF125569D5B03ED953FA47ED202EF3451E1502E06E591A8EBB6220"
 + "93E0D197233E967A9A891A0770958FE763FE971249CCB3C9A32629CEDD190F66"
 + "DECF5CE3D37600BDBC48C77550664DDB1AD4B1E96933F661A60BD3CE05F4F3E2"
 + "5D38252715EF1AEFB22B9313B3E79D4B99BCEA5DD81F1469FC73C077B6D24728"
 + "09C6F8B50000000049454E44AE426082");
        btnProximo.setImageId(15);
        btnProximo.setColor("clBtnFace");
        btnProximo.setAccess(false);
        btnProximo.setIconReverseDirection(false);
        FHBox1.addChildren(btnProximo);
        btnProximo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(676);
        FHBox8.setTop(0);
        FHBox8.setWidth(32);
        FHBox8.setHeight(32);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setVisible(false);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox1.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFButton btnEnviarAlterarTemplate = new TFButton();

    private void init_btnEnviarAlterarTemplate() {
        btnEnviarAlterarTemplate.setName("btnEnviarAlterarTemplate");
        btnEnviarAlterarTemplate.setLeft(708);
        btnEnviarAlterarTemplate.setTop(0);
        btnEnviarAlterarTemplate.setWidth(97);
        btnEnviarAlterarTemplate.setHeight(53);
        btnEnviarAlterarTemplate.setCaption("Template Zenvia");
        btnEnviarAlterarTemplate.setEnabled(false);
        btnEnviarAlterarTemplate.setFontColor("clWindowText");
        btnEnviarAlterarTemplate.setFontSize(-11);
        btnEnviarAlterarTemplate.setFontName("Tahoma");
        btnEnviarAlterarTemplate.setFontStyle("[]");
        btnEnviarAlterarTemplate.setLayout("blGlyphTop");
        btnEnviarAlterarTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnEnviarAlterarTemplateClick(event);
            processarFlow("FrmTemplates", "btnEnviarAlterarTemplate", "OnClick");
        });
        btnEnviarAlterarTemplate.setPngImageData("89504E470D0A1A0A0000000D494844520000001C0000002408060000009A245A"
 + "54000003284944415478DAED965948554118C7CF6D3322A23D22EB212C88F421"
 + "69075BB04C2A945202310B9268790A820A41A18DF22968277AA92C31092AA38D"
 + "B255C22CA3BDB7AC4CDB108B4A31CC7E7FEE08D374B4738DA2070FFCF8EEF996"
 + "F33F3367E69B1B6A6969F1FEE515EA14EC14FC2B82E40CC32C8299100B434CE8"
 + "033C862B703C140ABDF82341627AF0565802DDE1333C801A933214E2A00F3443"
 + "116C40F855C482F8E7610E437F2881DD1A090FFBE6E475C54C835590065F6039"
 + "79458105F12DC31C805A584A716990EF43DD14CC1118096BA9DBF15B4133B253"
 + "E6DB2451F436889855AF19390B136131F5C7DA14E4F720CC5368840924D70614"
 + "E98189862A6ABE1BD1722FBCB8E2ECC5E40AEEC3AC8464922E0414EB85B901F1"
 + "F01066505B877F32BFCBBCF0EACDF845D08C4EABEB1A09732298C23D98D5962B"
 + "87FA6D26A685A385340ADF73575023D30853099E0E28D605D3003D2C7701F559"
 + "269E80B96EBF842DA8B749817E041B2318613E669DE5CAA57E8BF542EFE12EBE"
 + "2457F011A689407C503153D70DF311F42DBF9AE9ABB1E2DA52A3F145BB826F30"
 + "9504E64628B81193676EF3A8DFECC48F62D2F147B982DA02F709244720B61EB3"
 + "DDDC6A1B24F87422EDC385F87BBA82EA91CD04C67560647A59EDDBD73E79A566"
 + "9A87BB82857A13E84BB0C12A88B54691E9857BA5FAEA0AE3D37D2235E53E62EA"
 + "B35A3415AD5BCD16CCC61C8405044F1A5F3AA600A2CC33D4EE54106BEED5F6D2"
 + "C82F6B6316A663AE7AE11324DF15543BAA865B04138D4FA7C4FC3666F53264B5"
 + "D7FEA82FD6002086BCAA9F044DC21D33A531E63E17B3C9794E3DC8BF577DB31D"
 + "B1A9989B50485E66ABDF1EA1E6FB139C2321DDF874E8AE8154937F06F613AFF7"
 + "DAB9A81B80B90D03BD70F37EE9273806F3C4F3D94B915C3C47223A9EC643867B"
 + "10DB82EAE8DA3329249574504C8BE4108CD0CCF09C9D6E8E2DA8A5AF8D3C1B06"
 + "83F663BCB115B00B2EF290264744D3AE3F573A3152CC67C926EF84DF4BD982E7"
 + "31EEB1A465FF0C26813A85F6A7CEBC77A005A303762CF4067518EDE51CBF06E0"
 + "27A815AAC47B50E985FB6AB589E9ACD4B936CB08E83BA969D779E1BD79098A83"
 + "FC43F83FFF08770A760ADAD70F74D9BBC87E5C60800000000049454E44AE4260"
 + "82");
        btnEnviarAlterarTemplate.setImageId(430027);
        btnEnviarAlterarTemplate.setColor("clBtnFace");
        btnEnviarAlterarTemplate.setAccess(false);
        btnEnviarAlterarTemplate.setIconReverseDirection(false);
        FHBox1.addChildren(btnEnviarAlterarTemplate);
        btnEnviarAlterarTemplate.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(829);
        FHBox5.setTop(0);
        FHBox5.setWidth(146);
        FHBox5.setHeight(60);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(2);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(3);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftMin");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox6.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(0);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(65);
        btnAceitar.setHeight(53);
        btnAceitar.setHint("Aceita o Registro Selecionado para Outro Formulario  (CRTL+ 9)");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.setVisible(false);
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmTemplates", "btnAceitar", "OnClick");
        });
        btnAceitar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000002C64944415478DAB594DF4B145114C7CF9D3BEEAEAB39B8FE58DD69D5"
 + "25480A322822535490122D948C24A2C71E7AE8A1E8C522C220CA7FA05EA2B7C0"
 + "87840C0CCD0C1FC244A25F043D485B8EAE291968D9EEECCEECCE9DCEEC8E35FE"
 + "58D7183D70E6CE9D33F3FDDC7BCE994B609B8D647AA1E278F72421C4912EAEEB"
 + "BA2A0D5C0BD801E8ADCD87012129414D3354FFC6FB87DF0002882DC099F60698"
 + "082D26E7D1A802B1989ABCAFDD2742CF9351FB80D36DF5F0696A21398F446220"
 + "23C4B0C6037E78D43F661F70F2441D4CCCA4762023206202EAAA44E81B1CB70F"
 + "686DA981E0EC520A2023404E01AAF796C2D3E1D7F6012D4D4740FAFE3B952214"
 + "3720861DACF4C2D0C81614B9E96835847E844D00D6C0DC41D5AE221879F96EF3"
 + "0014EBC2E112F6245D7E4609E435D41F82B945D94C91928418B6A7DC0363E31F"
 + "41D3616985AAAE87F0DA82E0D06A00C57E7F2E08B93595BB032E4A2918EDAF32"
 + "807034BEA606854236ECC8E68D9F2D39FFF92B02C12FD3C64A6A51FCC3BA2942"
 + "881B21E3DE9282CA40C0EF584261C6FEC5AD3B308CC315140A2E6089384C7C96"
 + "649DE9C7507C6CC31A20A40097FE5E148B4B4B4A4B7859D5D202841C0770C020"
 + "1894A228DE86E22F365564845420E4ED4EBF2FBFB0C84394442A0D5104844D40"
 + "8E8B071E1526BF4A51C6F47328DEF75F5D8490FD0819F59789B942BE80C524C9"
 + "A3228C3F9BD341C1C1014C4D4EA338BB80E20F3376511A482342FAC532D19D27"
 + "0820C714506271C8A23A7C9B9E9199C63A51FC6EBAEF33024CC859C2710FBC3E"
 + "9FDBE97281AA28303F372B6B0976471AB87A7BA36F498618673A296FBE758566"
 + "396F788ABDEE85F9799925D4FBD2E0F54E8C318B67041873BACA397324654D37"
 + "BB39DE795ED7D49EA9A1AECB16616D1D5F035816E22D4E2D23058EA7BEDA8B1D"
 + "B3AFEEF562E3272C6209CB68F59500CCB5065B64CBE7D3EAA3423FD5D16C5BFC"
 + "71EFB3F4806DDDC176D81F0F015C28B7AC831E0000000049454E44AE426082");
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox5.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnMais = new TFButton();

    private void init_btnMais() {
        btnMais.setName("btnMais");
        btnMais.setLeft(65);
        btnMais.setTop(0);
        btnMais.setWidth(65);
        btnMais.setHeight(53);
        btnMais.setHint("Mais Op\u00E7\u00F5es");
        btnMais.setFontColor("clWindowText");
        btnMais.setFontSize(-21);
        btnMais.setFontName("Tahoma");
        btnMais.setFontStyle("[]");
        btnMais.setLayout("blGlyphTop");
        btnMais.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnMaisClick(event);
            processarFlow("FrmTemplates", "btnMais", "OnClick");
        });
        btnMais.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000000444944415478DA6364A031601C161684D3DA82FF508CCCC7C5461623"
 + "463D983199D63EA02918B5607058F07EC85B4053306AC1E0B080E6851DCD8B6B"
 + "9A5738340543DF020000C715C7D877D4660000000049454E44AE426082");
        btnMais.setImageId(22002);
        btnMais.setColor("clBtnFace");
        btnMais.setAccess(false);
        btnMais.setIconReverseDirection(false);
        FHBox5.addChildren(btnMais);
        btnMais.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(3);
        FHBox4.setTop(69);
        FHBox4.setWidth(970);
        FHBox4.setHeight(23);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FrmTemplates.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblMensagem = new TFLabel();

    private void init_lblMensagem() {
        lblMensagem.setName("lblMensagem");
        lblMensagem.setLeft(0);
        lblMensagem.setTop(0);
        lblMensagem.setWidth(78);
        lblMensagem.setHeight(16);
        lblMensagem.setCaption("Mensagem....");
        lblMensagem.setFontColor("clNavy");
        lblMensagem.setFontSize(-13);
        lblMensagem.setFontName("Tahoma");
        lblMensagem.setFontStyle("[]");
        lblMensagem.setVerticalAlignment("taAlignTop");
        lblMensagem.setWordBreak(false);
        FHBox4.addChildren(lblMensagem);
        lblMensagem.applyProperties();
    }

    public TFPageControl pgPrincipal = new TFPageControl();

    private void init_pgPrincipal() {
        pgPrincipal.setName("pgPrincipal");
        pgPrincipal.setLeft(3);
        pgPrincipal.setTop(91);
        pgPrincipal.setWidth(974);
        pgPrincipal.setHeight(410);
        pgPrincipal.setTabPosition("tpTop");
        pgPrincipal.setFlexVflex("ftTrue");
        pgPrincipal.setFlexHflex("ftTrue");
        pgPrincipal.setRenderStyle("rsTabbed");
        pgPrincipal.applyProperties();
        FrmTemplates.addChildren(pgPrincipal);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setClosable(false);
        pgPrincipal.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(966);
        FVBox1.setHeight(382);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setColor("clWhite");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        tabListagem.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGroupbox grpBoxFiltro = new TFGroupbox();

    private void init_grpBoxFiltro() {
        grpBoxFiltro.setName("grpBoxFiltro");
        grpBoxFiltro.setLeft(0);
        grpBoxFiltro.setTop(0);
        grpBoxFiltro.setWidth(960);
        grpBoxFiltro.setHeight(74);
        grpBoxFiltro.setCaption("Filtro R\u00E1pido");
        grpBoxFiltro.setFontColor("clWindowText");
        grpBoxFiltro.setFontSize(-11);
        grpBoxFiltro.setFontName("Tahoma");
        grpBoxFiltro.setFontStyle("[]");
        grpBoxFiltro.setFlexVflex("ftMin");
        grpBoxFiltro.setFlexHflex("ftTrue");
        grpBoxFiltro.setScrollable(false);
        grpBoxFiltro.setClosable(true);
        grpBoxFiltro.setClosed(false);
        grpBoxFiltro.setOrient("coHorizontal");
        grpBoxFiltro.setStyle("grp3D");
        grpBoxFiltro.setHeaderImageId(0);
        FVBox1.addChildren(grpBoxFiltro);
        grpBoxFiltro.applyProperties();
    }

    public TFGridPanel gpFiltroPrincipal = new TFGridPanel();

    private void init_gpFiltroPrincipal() {
        gpFiltroPrincipal.setName("gpFiltroPrincipal");
        gpFiltroPrincipal.setLeft(2);
        gpFiltroPrincipal.setTop(15);
        gpFiltroPrincipal.setWidth(956);
        gpFiltroPrincipal.setHeight(47);
        gpFiltroPrincipal.setAlign("alTop");
        TFGridPanelColumn item10 = new TFGridPanelColumn();
        item10.setSizeStyle("ssAbsolute");
        item10.setValue(220.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item10);
        TFGridPanelColumn item11 = new TFGridPanelColumn();
        item11.setSizeStyle("ssAbsolute");
        item11.setValue(220.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item11);
        TFGridPanelColumn item12 = new TFGridPanelColumn();
        item12.setValue(50.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item12);
        TFGridPanelColumn item13 = new TFGridPanelColumn();
        item13.setValue(50.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item13);
        TFControlItem item14 = new TFControlItem();
        item14.setColumn(0);
        item14.setControl("lfAplicacao");
        item14.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item14);
        TFControlItem item15 = new TFControlItem();
        item15.setColumn(0);
        item15.setControl("efAplicacao");
        item15.setRow(1);
        gpFiltroPrincipal.getControlCollection().add(item15);
        TFControlItem item16 = new TFControlItem();
        item16.setColumn(1);
        item16.setControl("lfDepartamento");
        item16.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item16);
        TFControlItem item17 = new TFControlItem();
        item17.setColumn(1);
        item17.setControl("efDepartamento");
        item17.setRow(1);
        gpFiltroPrincipal.getControlCollection().add(item17);
        TFGridPanelRow item18 = new TFGridPanelRow();
        item18.setSizeStyle("ssAbsolute");
        item18.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item18);
        TFGridPanelRow item19 = new TFGridPanelRow();
        item19.setSizeStyle("ssAbsolute");
        item19.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item19);
        gpFiltroPrincipal.setFlexVflex("ftTrue");
        gpFiltroPrincipal.setFlexHflex("ftTrue");
        gpFiltroPrincipal.setAllRowFlex(true);
        gpFiltroPrincipal.setColumnTabOrder(false);
        grpBoxFiltro.addChildren(gpFiltroPrincipal);
        gpFiltroPrincipal.applyProperties();
    }

    public TFLabel lfAplicacao = new TFLabel();

    private void init_lfAplicacao() {
        lfAplicacao.setName("lfAplicacao");
        lfAplicacao.setLeft(1);
        lfAplicacao.setTop(1);
        lfAplicacao.setWidth(45);
        lfAplicacao.setHeight(21);
        lfAplicacao.setAlign("alLeft");
        lfAplicacao.setCaption("Aplica\u00E7\u00E3o");
        lfAplicacao.setFontColor("clWindowText");
        lfAplicacao.setFontSize(-11);
        lfAplicacao.setFontName("Tahoma");
        lfAplicacao.setFontStyle("[]");
        lfAplicacao.setVerticalAlignment("taAlignBottom");
        lfAplicacao.setWordBreak(false);
        gpFiltroPrincipal.addChildren(lfAplicacao);
        lfAplicacao.applyProperties();
    }

    public TFCombo efAplicacao = new TFCombo();

    private void init_efAplicacao() {
        efAplicacao.setName("efAplicacao");
        efAplicacao.setLeft(1);
        efAplicacao.setTop(22);
        efAplicacao.setWidth(200);
        efAplicacao.setHeight(21);
        efAplicacao.setHint("Filtra pelo Aplica\u00E7\u00E3o e-mail");
        efAplicacao.setFlex(false);
        efAplicacao.setListOptions("Email=E;Whatsapp=W;Ambos=A");
        efAplicacao.setReadOnly(true);
        efAplicacao.setRequired(false);
        efAplicacao.setPrompt("Selecione");
        efAplicacao.setConstraintCheckWhen("cwImmediate");
        efAplicacao.setConstraintCheckType("ctExpression");
        efAplicacao.setConstraintFocusOnError(false);
        efAplicacao.setConstraintEnableUI(true);
        efAplicacao.setConstraintEnabled(false);
        efAplicacao.setConstraintFormCheck(true);
        efAplicacao.setClearOnDelKey(true);
        efAplicacao.setUseClearButton(false);
        efAplicacao.setHideClearButtonOnNullValue(false);
        efAplicacao.setAlign("alLeft");
        efAplicacao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            efAplicacaoEnter(event);
            processarFlow("FrmTemplates", "efAplicacao", "OnEnter");
        });
        gpFiltroPrincipal.addChildren(efAplicacao);
        efAplicacao.applyProperties();
        addValidatable(efAplicacao);
    }

    public TFLabel lfDepartamento = new TFLabel();

    private void init_lfDepartamento() {
        lfDepartamento.setName("lfDepartamento");
        lfDepartamento.setLeft(221);
        lfDepartamento.setTop(1);
        lfDepartamento.setWidth(69);
        lfDepartamento.setHeight(21);
        lfDepartamento.setAlign("alLeft");
        lfDepartamento.setCaption("Departamento");
        lfDepartamento.setFontColor("clWindowText");
        lfDepartamento.setFontSize(-11);
        lfDepartamento.setFontName("Tahoma");
        lfDepartamento.setFontStyle("[]");
        lfDepartamento.setVerticalAlignment("taAlignBottom");
        lfDepartamento.setWordBreak(false);
        gpFiltroPrincipal.addChildren(lfDepartamento);
        lfDepartamento.applyProperties();
    }

    public TFCombo efDepartamento = new TFCombo();

    private void init_efDepartamento() {
        efDepartamento.setName("efDepartamento");
        efDepartamento.setLeft(221);
        efDepartamento.setTop(22);
        efDepartamento.setWidth(200);
        efDepartamento.setHeight(21);
        efDepartamento.setHint("Filtra pelo Departamento e-mail");
        efDepartamento.setFlex(false);
        efDepartamento.setListOptions("Pe\u00E7as=P;Servi\u00E7os=S;Ve\u00EDculos=V");
        efDepartamento.setReadOnly(true);
        efDepartamento.setRequired(false);
        efDepartamento.setPrompt("Selecione");
        efDepartamento.setConstraintCheckWhen("cwImmediate");
        efDepartamento.setConstraintCheckType("ctExpression");
        efDepartamento.setConstraintFocusOnError(false);
        efDepartamento.setConstraintEnableUI(true);
        efDepartamento.setConstraintEnabled(false);
        efDepartamento.setConstraintFormCheck(true);
        efDepartamento.setClearOnDelKey(true);
        efDepartamento.setUseClearButton(false);
        efDepartamento.setHideClearButtonOnNullValue(false);
        efDepartamento.setAlign("alLeft");
        efDepartamento.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            efDepartamentoEnter(event);
            processarFlow("FrmTemplates", "efDepartamento", "OnEnter");
        });
        gpFiltroPrincipal.addChildren(efDepartamento);
        efDepartamento.applyProperties();
        addValidatable(efDepartamento);
    }

    public TFGrid gridPrincipal = new TFGrid();

    private void init_gridPrincipal() {
        gridPrincipal.setName("gridPrincipal");
        gridPrincipal.setLeft(0);
        gridPrincipal.setTop(75);
        gridPrincipal.setWidth(956);
        gridPrincipal.setHeight(355);
        gridPrincipal.setTable(tbEmailModelo);
        gridPrincipal.setFlexVflex("ftTrue");
        gridPrincipal.setFlexHflex("ftTrue");
        gridPrincipal.setPagingEnabled(true);
        gridPrincipal.setFrozenColumns(0);
        gridPrincipal.setShowFooter(false);
        gridPrincipal.setShowHeader(true);
        gridPrincipal.setMultiSelection(false);
        gridPrincipal.setGroupingEnabled(false);
        gridPrincipal.setGroupingExpanded(false);
        gridPrincipal.setGroupingShowFooter(false);
        gridPrincipal.setCrosstabEnabled(false);
        gridPrincipal.setCrosstabGroupType("cgtConcat");
        gridPrincipal.setEditionEnabled(false);
        gridPrincipal.setNoBorder(false);
        TFGridColumn item20 = new TFGridColumn();
        item20.setTitleCaption("Alt.");
        item20.setWidth(30);
        item20.setVisible(false);
        item20.setPrecision(0);
        item20.setTextAlign("taCenter");
        item20.setFieldType("ftString");
        item20.setFlexRatio(0);
        item20.setSort(false);
        item20.setImageHeader(0);
        item20.setWrap(false);
        item20.setFlex(false);
        TFImageExpression item21 = new TFImageExpression();
        item21.setExpression("*");
        item21.setEvalType("etExpression");
        item21.setImageId(7);
        item21.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageAlterar(event);
            processarFlow("FrmTemplates", "item21", "OnClick");
        });
        item20.getImages().add(item21);
        item20.setCharCase("ccNormal");
        item20.setBlobConfigMimeType("bmtText");
        item20.setBlobConfigShowType("btImageViewer");
        item20.setShowLabel(true);
        item20.setEditorEditType("etTFString");
        item20.setEditorPrecision(0);
        item20.setEditorMaxLength(100);
        item20.setEditorLookupFilterKey(0);
        item20.setEditorLookupFilterDesc(0);
        item20.setEditorPopupHeight(400);
        item20.setEditorPopupWidth(400);
        item20.setEditorCharCase("ccNormal");
        item20.setEditorEnabled(false);
        item20.setEditorReadOnly(false);
        item20.setHiperLink(false);
        item20.setEditorConstraintCheckWhen("cwImmediate");
        item20.setEditorConstraintCheckType("ctExpression");
        item20.setEditorConstraintFocusOnError(false);
        item20.setEditorConstraintEnableUI(true);
        item20.setEditorConstraintEnabled(false);
        item20.setEmpty(false);
        item20.setMobileOptsShowMobile(false);
        item20.setMobileOptsOrder(0);
        item20.setBoxSize(0);
        item20.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item20);
        TFGridColumn item22 = new TFGridColumn();
        item22.setTitleCaption("Exc.");
        item22.setWidth(30);
        item22.setVisible(false);
        item22.setPrecision(0);
        item22.setTextAlign("taCenter");
        item22.setFieldType("ftString");
        item22.setFlexRatio(0);
        item22.setSort(false);
        item22.setImageHeader(0);
        item22.setWrap(false);
        item22.setFlex(false);
        TFImageExpression item23 = new TFImageExpression();
        item23.setExpression("*");
        item23.setEvalType("etExpression");
        item23.setImageId(8);
        item23.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageDelete(event);
            processarFlow("FrmTemplates", "item23", "OnClick");
        });
        item22.getImages().add(item23);
        item22.setCharCase("ccNormal");
        item22.setBlobConfigMimeType("bmtText");
        item22.setBlobConfigShowType("btImageViewer");
        item22.setShowLabel(true);
        item22.setEditorEditType("etTFString");
        item22.setEditorPrecision(0);
        item22.setEditorMaxLength(100);
        item22.setEditorLookupFilterKey(0);
        item22.setEditorLookupFilterDesc(0);
        item22.setEditorPopupHeight(400);
        item22.setEditorPopupWidth(400);
        item22.setEditorCharCase("ccNormal");
        item22.setEditorEnabled(false);
        item22.setEditorReadOnly(false);
        item22.setHiperLink(false);
        item22.setEditorConstraintCheckWhen("cwImmediate");
        item22.setEditorConstraintCheckType("ctExpression");
        item22.setEditorConstraintFocusOnError(false);
        item22.setEditorConstraintEnableUI(true);
        item22.setEditorConstraintEnabled(false);
        item22.setEmpty(false);
        item22.setMobileOptsShowMobile(false);
        item22.setMobileOptsOrder(0);
        item22.setBoxSize(0);
        item22.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item22);
        TFGridColumn item24 = new TFGridColumn();
        item24.setFieldName("MODELO");
        item24.setTitleCaption("Modelo");
        item24.setWidth(200);
        item24.setVisible(true);
        item24.setPrecision(0);
        item24.setTextAlign("taLeft");
        item24.setFieldType("ftMemo");
        item24.setFlexRatio(0);
        item24.setSort(true);
        item24.setImageHeader(0);
        item24.setWrap(false);
        item24.setFlex(true);
        item24.setCharCase("ccNormal");
        item24.setBlobConfigMimeType("bmtText");
        item24.setBlobConfigShowType("btImageViewer");
        item24.setShowLabel(true);
        item24.setEditorEditType("etTFString");
        item24.setEditorPrecision(0);
        item24.setEditorMaxLength(100);
        item24.setEditorLookupFilterKey(0);
        item24.setEditorLookupFilterDesc(0);
        item24.setEditorPopupHeight(400);
        item24.setEditorPopupWidth(400);
        item24.setEditorCharCase("ccNormal");
        item24.setEditorEnabled(false);
        item24.setEditorReadOnly(false);
        item24.setHiperLink(false);
        item24.setHint("Modelo e-mail");
        item24.setEditorConstraintCheckWhen("cwImmediate");
        item24.setEditorConstraintCheckType("ctExpression");
        item24.setEditorConstraintFocusOnError(false);
        item24.setEditorConstraintEnableUI(true);
        item24.setEditorConstraintEnabled(false);
        item24.setEmpty(false);
        item24.setMobileOptsShowMobile(false);
        item24.setMobileOptsOrder(0);
        item24.setBoxSize(0);
        item24.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item24);
        TFGridColumn item25 = new TFGridColumn();
        item25.setFieldName("APLICACAO");
        item25.setTitleCaption("Aplica\u00E7\u00E3o");
        item25.setWidth(200);
        item25.setVisible(true);
        item25.setPrecision(0);
        item25.setTextAlign("taLeft");
        item25.setFieldType("ftCombo");
        item25.setFlexRatio(0);
        item25.setSort(true);
        item25.setImageHeader(0);
        item25.setWrap(false);
        item25.setFlex(true);
        item25.setCharCase("ccNormal");
        item25.setBlobConfigMimeType("bmtText");
        item25.setBlobConfigShowType("btImageViewer");
        item25.setShowLabel(true);
        item25.setEditorEditType("etTFString");
        item25.setEditorPrecision(0);
        item25.setEditorMaxLength(100);
        item25.setEditorLookupFilterKey(0);
        item25.setEditorLookupFilterDesc(0);
        item25.setEditorPopupHeight(400);
        item25.setEditorPopupWidth(400);
        item25.setEditorCharCase("ccNormal");
        item25.setEditorEnabled(false);
        item25.setEditorReadOnly(false);
        item25.setListOptions("Email=E;Whatsapp=W;Ambos=A");
        item25.setHiperLink(false);
        item25.setHint("Aplica\u00E7\u00E3o e-mail.  Op\u00E7\u00F5es: Email=E;Whatsapp=W;Ambos=A");
        item25.setEditorConstraintCheckWhen("cwImmediate");
        item25.setEditorConstraintCheckType("ctExpression");
        item25.setEditorConstraintFocusOnError(false);
        item25.setEditorConstraintEnableUI(true);
        item25.setEditorConstraintEnabled(false);
        item25.setEmpty(false);
        item25.setMobileOptsShowMobile(false);
        item25.setMobileOptsOrder(0);
        item25.setBoxSize(0);
        item25.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item25);
        TFGridColumn item26 = new TFGridColumn();
        item26.setFieldName("STATUS_ZENVIA");
        item26.setTitleCaption("Status Zenvia");
        item26.setWidth(200);
        item26.setVisible(true);
        item26.setPrecision(0);
        item26.setTextAlign("taLeft");
        item26.setFieldType("ftString");
        item26.setFlexRatio(0);
        item26.setSort(true);
        item26.setImageHeader(0);
        item26.setWrap(false);
        item26.setFlex(false);
        item26.setCharCase("ccNormal");
        item26.setBlobConfigMimeType("bmtText");
        item26.setBlobConfigShowType("btImageViewer");
        item26.setShowLabel(true);
        item26.setEditorEditType("etTFString");
        item26.setEditorPrecision(0);
        item26.setEditorMaxLength(100);
        item26.setEditorLookupFilterKey(0);
        item26.setEditorLookupFilterDesc(0);
        item26.setEditorPopupHeight(400);
        item26.setEditorPopupWidth(400);
        item26.setEditorCharCase("ccNormal");
        item26.setEditorEnabled(false);
        item26.setEditorReadOnly(false);
        item26.setHiperLink(false);
        item26.setEditorConstraintCheckWhen("cwImmediate");
        item26.setEditorConstraintCheckType("ctExpression");
        item26.setEditorConstraintFocusOnError(false);
        item26.setEditorConstraintEnableUI(true);
        item26.setEditorConstraintEnabled(false);
        item26.setEmpty(false);
        item26.setMobileOptsShowMobile(false);
        item26.setMobileOptsOrder(0);
        item26.setBoxSize(0);
        item26.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item26);
        FVBox1.addChildren(gridPrincipal);
        gridPrincipal.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setClosable(false);
        pgPrincipal.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(966);
        FVBox2.setHeight(382);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setColor("clWhite");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFGroupbox grpBoxPrincipal = new TFGroupbox();

    private void init_grpBoxPrincipal() {
        grpBoxPrincipal.setName("grpBoxPrincipal");
        grpBoxPrincipal.setLeft(0);
        grpBoxPrincipal.setTop(0);
        grpBoxPrincipal.setWidth(729);
        grpBoxPrincipal.setHeight(319);
        grpBoxPrincipal.setCaption("Email Modelo");
        grpBoxPrincipal.setFontColor("clWindowText");
        grpBoxPrincipal.setFontSize(-11);
        grpBoxPrincipal.setFontName("Tahoma");
        grpBoxPrincipal.setFontStyle("[]");
        grpBoxPrincipal.setFlexVflex("ftMin");
        grpBoxPrincipal.setFlexHflex("ftTrue");
        grpBoxPrincipal.setScrollable(true);
        grpBoxPrincipal.setClosable(true);
        grpBoxPrincipal.setClosed(false);
        grpBoxPrincipal.setOrient("coVertical");
        grpBoxPrincipal.setStyle("grp3D");
        grpBoxPrincipal.setHeaderImageId(0);
        FVBox2.addChildren(grpBoxPrincipal);
        grpBoxPrincipal.applyProperties();
    }

    public TFGridPanel FGridPanel2 = new TFGridPanel();

    private void init_FGridPanel2() {
        FGridPanel2.setName("FGridPanel2");
        FGridPanel2.setLeft(2);
        FGridPanel2.setTop(15);
        FGridPanel2.setWidth(725);
        FGridPanel2.setHeight(273);
        FGridPanel2.setAlign("alTop");
        TFGridPanelColumn item27 = new TFGridPanelColumn();
        item27.setSizeStyle("ssAbsolute");
        item27.setValue(145.000000000000000000);
        FGridPanel2.getColumnCollection().add(item27);
        TFGridPanelColumn item28 = new TFGridPanelColumn();
        item28.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item28);
        TFControlItem item29 = new TFControlItem();
        item29.setColumn(0);
        item29.setControl("lbModelo44002");
        item29.setRow(0);
        FGridPanel2.getControlCollection().add(item29);
        TFControlItem item30 = new TFControlItem();
        item30.setColumn(1);
        item30.setControl("edModelo44002");
        item30.setRow(0);
        FGridPanel2.getControlCollection().add(item30);
        TFControlItem item31 = new TFControlItem();
        item31.setColumn(0);
        item31.setControl("lbPrivado44002");
        item31.setRow(1);
        FGridPanel2.getControlCollection().add(item31);
        TFControlItem item32 = new TFControlItem();
        item32.setColumn(1);
        item32.setControl("edPrivado44002");
        item32.setRow(1);
        FGridPanel2.getControlCollection().add(item32);
        TFControlItem item33 = new TFControlItem();
        item33.setColumn(0);
        item33.setControl("lbAplicacao44002");
        item33.setRow(2);
        FGridPanel2.getControlCollection().add(item33);
        TFControlItem item34 = new TFControlItem();
        item34.setColumn(1);
        item34.setControl("edAplicacao44002");
        item34.setRow(2);
        FGridPanel2.getControlCollection().add(item34);
        TFControlItem item35 = new TFControlItem();
        item35.setColumn(0);
        item35.setControl("lbDepartamento44002");
        item35.setRow(3);
        FGridPanel2.getControlCollection().add(item35);
        TFControlItem item36 = new TFControlItem();
        item36.setColumn(1);
        item36.setControl("edDepartamento44002");
        item36.setRow(3);
        FGridPanel2.getControlCollection().add(item36);
        TFControlItem item37 = new TFControlItem();
        item37.setColumn(0);
        item37.setControl("lbAssunto44002");
        item37.setRow(4);
        FGridPanel2.getControlCollection().add(item37);
        TFControlItem item38 = new TFControlItem();
        item38.setColumn(1);
        item38.setControl("edAssunto44002");
        item38.setRow(4);
        FGridPanel2.getControlCollection().add(item38);
        TFControlItem item39 = new TFControlItem();
        item39.setColumn(0);
        item39.setControl("lbTpArquivoAnexoMensagem44002");
        item39.setRow(5);
        FGridPanel2.getControlCollection().add(item39);
        TFControlItem item40 = new TFControlItem();
        item40.setColumn(1);
        item40.setControl("edTpArquivoAnexoMensagem44002");
        item40.setRow(5);
        FGridPanel2.getControlCollection().add(item40);
        TFControlItem item41 = new TFControlItem();
        item41.setColumn(0);
        item41.setControl("lbIdTemplate44002");
        item41.setRow(6);
        FGridPanel2.getControlCollection().add(item41);
        TFControlItem item42 = new TFControlItem();
        item42.setColumn(1);
        item42.setControl("edIdTemplate44002");
        item42.setRow(6);
        FGridPanel2.getControlCollection().add(item42);
        TFControlItem item43 = new TFControlItem();
        item43.setColumn(0);
        item43.setControl("lbEhChat44002");
        item43.setRow(8);
        FGridPanel2.getControlCollection().add(item43);
        TFControlItem item44 = new TFControlItem();
        item44.setColumn(1);
        item44.setControl("edEhChat44002");
        item44.setRow(8);
        FGridPanel2.getControlCollection().add(item44);
        TFControlItem item45 = new TFControlItem();
        item45.setColumn(1);
        item45.setControl("edtCelTemplateAprov");
        item45.setRow(7);
        FGridPanel2.getControlCollection().add(item45);
        TFControlItem item46 = new TFControlItem();
        item46.setColumn(0);
        item46.setControl("FLabel1");
        item46.setRow(7);
        FGridPanel2.getControlCollection().add(item46);
        TFGridPanelRow item47 = new TFGridPanelRow();
        item47.setSizeStyle("ssAbsolute");
        item47.setValue(42.000000000000000000);
        FGridPanel2.getRowCollection().add(item47);
        TFGridPanelRow item48 = new TFGridPanelRow();
        item48.setSizeStyle("ssAbsolute");
        item48.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item48);
        TFGridPanelRow item49 = new TFGridPanelRow();
        item49.setSizeStyle("ssAbsolute");
        item49.setValue(21.000000000000000000);
        FGridPanel2.getRowCollection().add(item49);
        TFGridPanelRow item50 = new TFGridPanelRow();
        item50.setSizeStyle("ssAbsolute");
        item50.setValue(21.000000000000000000);
        FGridPanel2.getRowCollection().add(item50);
        TFGridPanelRow item51 = new TFGridPanelRow();
        item51.setSizeStyle("ssAbsolute");
        item51.setValue(42.000000000000000000);
        FGridPanel2.getRowCollection().add(item51);
        TFGridPanelRow item52 = new TFGridPanelRow();
        item52.setSizeStyle("ssAbsolute");
        item52.setValue(21.000000000000000000);
        FGridPanel2.getRowCollection().add(item52);
        TFGridPanelRow item53 = new TFGridPanelRow();
        item53.setSizeStyle("ssAbsolute");
        item53.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item53);
        TFGridPanelRow item54 = new TFGridPanelRow();
        item54.setSizeStyle("ssAbsolute");
        item54.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item54);
        TFGridPanelRow item55 = new TFGridPanelRow();
        item55.setSizeStyle("ssAbsolute");
        item55.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item55);
        FGridPanel2.setFlexVflex("ftFalse");
        FGridPanel2.setFlexHflex("ftTrue");
        FGridPanel2.setAllRowFlex(true);
        FGridPanel2.setColumnTabOrder(false);
        grpBoxPrincipal.addChildren(FGridPanel2);
        FGridPanel2.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(22);
        FLabel1.setTop(186);
        FLabel1.setWidth(124);
        FLabel1.setHeight(19);
        FLabel1.setAlign("alRight");
        FLabel1.setCaption("Celular Aprovou Template");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FGridPanel2.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFLabel lbIdTemplate44002 = new TFLabel();

    private void init_lbIdTemplate44002() {
        lbIdTemplate44002.setName("lbIdTemplate44002");
        lbIdTemplate44002.setLeft(45);
        lbIdTemplate44002.setTop(167);
        lbIdTemplate44002.setWidth(101);
        lbIdTemplate44002.setHeight(19);
        lbIdTemplate44002.setAlign("alRight");
        lbIdTemplate44002.setCaption("Id. Template(Zenvia)");
        lbIdTemplate44002.setFontColor("clWindowText");
        lbIdTemplate44002.setFontSize(-11);
        lbIdTemplate44002.setFontName("Tahoma");
        lbIdTemplate44002.setFontStyle("[]");
        lbIdTemplate44002.setVerticalAlignment("taVerticalCenter");
        lbIdTemplate44002.setWordBreak(false);
        FGridPanel2.addChildren(lbIdTemplate44002);
        lbIdTemplate44002.applyProperties();
    }

    public TFLabel lbEhChat44002 = new TFLabel();

    private void init_lbEhChat44002() {
        lbEhChat44002.setName("lbEhChat44002");
        lbEhChat44002.setLeft(47);
        lbEhChat44002.setTop(205);
        lbEhChat44002.setWidth(99);
        lbEhChat44002.setHeight(19);
        lbEhChat44002.setAlign("alRight");
        lbEhChat44002.setCaption("\u00C9 Template de Chat?");
        lbEhChat44002.setFontColor("clWindowText");
        lbEhChat44002.setFontSize(-11);
        lbEhChat44002.setFontName("Tahoma");
        lbEhChat44002.setFontStyle("[]");
        lbEhChat44002.setVerticalAlignment("taVerticalCenter");
        lbEhChat44002.setWordBreak(false);
        FGridPanel2.addChildren(lbEhChat44002);
        lbEhChat44002.applyProperties();
    }

    public TFLabel lbTpArquivoAnexoMensagem44002 = new TFLabel();

    private void init_lbTpArquivoAnexoMensagem44002() {
        lbTpArquivoAnexoMensagem44002.setName("lbTpArquivoAnexoMensagem44002");
        lbTpArquivoAnexoMensagem44002.setLeft(52);
        lbTpArquivoAnexoMensagem44002.setTop(146);
        lbTpArquivoAnexoMensagem44002.setWidth(94);
        lbTpArquivoAnexoMensagem44002.setHeight(21);
        lbTpArquivoAnexoMensagem44002.setAlign("alRight");
        lbTpArquivoAnexoMensagem44002.setCaption("Tipo Arquivo Anexo");
        lbTpArquivoAnexoMensagem44002.setFontColor("clWindowText");
        lbTpArquivoAnexoMensagem44002.setFontSize(-11);
        lbTpArquivoAnexoMensagem44002.setFontName("Tahoma");
        lbTpArquivoAnexoMensagem44002.setFontStyle("[]");
        lbTpArquivoAnexoMensagem44002.setVerticalAlignment("taVerticalCenter");
        lbTpArquivoAnexoMensagem44002.setWordBreak(false);
        FGridPanel2.addChildren(lbTpArquivoAnexoMensagem44002);
        lbTpArquivoAnexoMensagem44002.applyProperties();
    }

    public TFLabel lbDepartamento44002 = new TFLabel();

    private void init_lbDepartamento44002() {
        lbDepartamento44002.setName("lbDepartamento44002");
        lbDepartamento44002.setLeft(77);
        lbDepartamento44002.setTop(83);
        lbDepartamento44002.setWidth(69);
        lbDepartamento44002.setHeight(21);
        lbDepartamento44002.setAlign("alRight");
        lbDepartamento44002.setCaption("Departamento");
        lbDepartamento44002.setFontColor("clWindowText");
        lbDepartamento44002.setFontSize(-11);
        lbDepartamento44002.setFontName("Tahoma");
        lbDepartamento44002.setFontStyle("[]");
        lbDepartamento44002.setVerticalAlignment("taVerticalCenter");
        lbDepartamento44002.setWordBreak(false);
        FGridPanel2.addChildren(lbDepartamento44002);
        lbDepartamento44002.applyProperties();
    }

    public TFLabel lbAplicacao44002 = new TFLabel();

    private void init_lbAplicacao44002() {
        lbAplicacao44002.setName("lbAplicacao44002");
        lbAplicacao44002.setLeft(101);
        lbAplicacao44002.setTop(62);
        lbAplicacao44002.setWidth(45);
        lbAplicacao44002.setHeight(21);
        lbAplicacao44002.setAlign("alRight");
        lbAplicacao44002.setCaption("Aplica\u00E7\u00E3o");
        lbAplicacao44002.setFontColor("clWindowText");
        lbAplicacao44002.setFontSize(-11);
        lbAplicacao44002.setFontName("Tahoma");
        lbAplicacao44002.setFontStyle("[]");
        lbAplicacao44002.setVerticalAlignment("taVerticalCenter");
        lbAplicacao44002.setWordBreak(false);
        FGridPanel2.addChildren(lbAplicacao44002);
        lbAplicacao44002.applyProperties();
    }

    public TFLabel lbAssunto44002 = new TFLabel();

    private void init_lbAssunto44002() {
        lbAssunto44002.setName("lbAssunto44002");
        lbAssunto44002.setLeft(101);
        lbAssunto44002.setTop(104);
        lbAssunto44002.setWidth(45);
        lbAssunto44002.setHeight(42);
        lbAssunto44002.setAlign("alRight");
        lbAssunto44002.setCaption("Assunto*");
        lbAssunto44002.setFontColor("clWindowText");
        lbAssunto44002.setFontSize(-11);
        lbAssunto44002.setFontName("Tahoma");
        lbAssunto44002.setFontStyle("[]");
        lbAssunto44002.setVerticalAlignment("taAlignTop");
        lbAssunto44002.setWordBreak(false);
        FGridPanel2.addChildren(lbAssunto44002);
        lbAssunto44002.applyProperties();
    }

    public TFLabel lbModelo44002 = new TFLabel();

    private void init_lbModelo44002() {
        lbModelo44002.setName("lbModelo44002");
        lbModelo44002.setLeft(106);
        lbModelo44002.setTop(1);
        lbModelo44002.setWidth(40);
        lbModelo44002.setHeight(42);
        lbModelo44002.setAlign("alRight");
        lbModelo44002.setCaption("Modelo*");
        lbModelo44002.setFontColor("clWindowText");
        lbModelo44002.setFontSize(-11);
        lbModelo44002.setFontName("Tahoma");
        lbModelo44002.setFontStyle("[]");
        lbModelo44002.setVerticalAlignment("taVerticalCenter");
        lbModelo44002.setWordBreak(false);
        FGridPanel2.addChildren(lbModelo44002);
        lbModelo44002.applyProperties();
    }

    public TFLabel lbPrivado44002 = new TFLabel();

    private void init_lbPrivado44002() {
        lbPrivado44002.setName("lbPrivado44002");
        lbPrivado44002.setLeft(110);
        lbPrivado44002.setTop(43);
        lbPrivado44002.setWidth(36);
        lbPrivado44002.setHeight(19);
        lbPrivado44002.setAlign("alRight");
        lbPrivado44002.setCaption("Privado");
        lbPrivado44002.setFontColor("clWindowText");
        lbPrivado44002.setFontSize(-11);
        lbPrivado44002.setFontName("Tahoma");
        lbPrivado44002.setFontStyle("[]");
        lbPrivado44002.setVerticalAlignment("taVerticalCenter");
        lbPrivado44002.setWordBreak(false);
        FGridPanel2.addChildren(lbPrivado44002);
        lbPrivado44002.applyProperties();
    }

    public TFMemo edModelo44002 = new TFMemo();

    private void init_edModelo44002() {
        edModelo44002.setName("edModelo44002");
        edModelo44002.setLeft(146);
        edModelo44002.setTop(1);
        edModelo44002.setWidth(400);
        edModelo44002.setHeight(42);
        edModelo44002.setHint("Modelo e-mail");
        edModelo44002.setAlign("alLeft");
        edModelo44002.setCharCase("ccNormal");
        edModelo44002.setFontColor("clWindowText");
        edModelo44002.setFontSize(-11);
        edModelo44002.setFontName("Tahoma");
        edModelo44002.setFontStyle("[]");
        edModelo44002.setMaxlength(4000);
        edModelo44002.setFieldName("MODELO");
        edModelo44002.setTable(tbEmailModelo);
        edModelo44002.setFlexVflex("ftFalse");
        edModelo44002.setFlexHflex("ftTrue");
        edModelo44002.setHelpCaption("Modelo*");
        edModelo44002.setConstraintExpression("value is null");
        edModelo44002.setConstraintMessage("Campo Modelo*, preenchimento \u00E9 obrigat\u00F3rio");
        edModelo44002.setConstraintCheckWhen("cwImmediate");
        edModelo44002.setConstraintCheckType("ctExpression");
        edModelo44002.setConstraintFocusOnError(false);
        edModelo44002.setConstraintGroupName("grpTbemailmodelo");
        edModelo44002.setConstraintEnableUI(true);
        edModelo44002.setConstraintEnabled(true);
        edModelo44002.setConstraintFormCheck(true);
        FGridPanel2.addChildren(edModelo44002);
        edModelo44002.applyProperties();
        addValidatable(edModelo44002);
    }

    public TFCheckBox edPrivado44002 = new TFCheckBox();

    private void init_edPrivado44002() {
        edPrivado44002.setName("edPrivado44002");
        edPrivado44002.setLeft(146);
        edPrivado44002.setTop(43);
        edPrivado44002.setWidth(16);
        edPrivado44002.setHeight(19);
        edPrivado44002.setHint("Privado e-mail.  Op\u00E7\u00F5es: Sim=S; N\u00E3o=N");
        edPrivado44002.setAlign("alLeft");
        edPrivado44002.setFontColor("clWindowText");
        edPrivado44002.setFontSize(-11);
        edPrivado44002.setFontName("Tahoma");
        edPrivado44002.setFontStyle("[]");
        edPrivado44002.setTable(tbEmailModelo);
        edPrivado44002.setFieldName("PRIVADO");
        edPrivado44002.setCheckedValue("S");
        edPrivado44002.setUncheckedValue("N");
        edPrivado44002.setHelpCaption("Privado");
        edPrivado44002.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(edPrivado44002);
        edPrivado44002.applyProperties();
    }

    public TFCombo edAplicacao44002 = new TFCombo();

    private void init_edAplicacao44002() {
        edAplicacao44002.setName("edAplicacao44002");
        edAplicacao44002.setLeft(146);
        edAplicacao44002.setTop(62);
        edAplicacao44002.setWidth(200);
        edAplicacao44002.setHeight(21);
        edAplicacao44002.setHint("Aplica\u00E7\u00E3o e-mail.  Op\u00E7\u00F5es: Email=E;Whatsapp=W;Ambos=A");
        edAplicacao44002.setTable(tbEmailModelo);
        edAplicacao44002.setFieldName("APLICACAO");
        edAplicacao44002.setFlex(true);
        edAplicacao44002.setListOptions("Email=E;Whatsapp=W;Ambos=A");
        edAplicacao44002.setHelpCaption("Aplica\u00E7\u00E3o");
        edAplicacao44002.setReadOnly(false);
        edAplicacao44002.setRequired(false);
        edAplicacao44002.setPrompt("Selecione");
        edAplicacao44002.setConstraintCheckWhen("cwImmediate");
        edAplicacao44002.setConstraintCheckType("ctExpression");
        edAplicacao44002.setConstraintFocusOnError(false);
        edAplicacao44002.setConstraintEnableUI(true);
        edAplicacao44002.setConstraintEnabled(false);
        edAplicacao44002.setConstraintFormCheck(true);
        edAplicacao44002.setClearOnDelKey(true);
        edAplicacao44002.setUseClearButton(false);
        edAplicacao44002.setHideClearButtonOnNullValue(false);
        edAplicacao44002.setAlign("alLeft");
        FGridPanel2.addChildren(edAplicacao44002);
        edAplicacao44002.applyProperties();
        addValidatable(edAplicacao44002);
    }

    public TFCombo edDepartamento44002 = new TFCombo();

    private void init_edDepartamento44002() {
        edDepartamento44002.setName("edDepartamento44002");
        edDepartamento44002.setLeft(146);
        edDepartamento44002.setTop(83);
        edDepartamento44002.setWidth(200);
        edDepartamento44002.setHeight(21);
        edDepartamento44002.setHint("Departamento e-mail.  Op\u00E7\u00F5es: Pe\u00E7as=P;Servi\u00E7os=S;Ve\u00EDculos=V");
        edDepartamento44002.setTable(tbEmailModelo);
        edDepartamento44002.setFieldName("DEPARTAMENTO");
        edDepartamento44002.setFlex(true);
        edDepartamento44002.setListOptions("Pe\u00E7as=P;Servi\u00E7os=S;Ve\u00EDculos=V");
        edDepartamento44002.setHelpCaption("Departamento");
        edDepartamento44002.setReadOnly(false);
        edDepartamento44002.setRequired(false);
        edDepartamento44002.setPrompt("Selecione");
        edDepartamento44002.setConstraintCheckWhen("cwImmediate");
        edDepartamento44002.setConstraintCheckType("ctExpression");
        edDepartamento44002.setConstraintFocusOnError(false);
        edDepartamento44002.setConstraintEnableUI(true);
        edDepartamento44002.setConstraintEnabled(false);
        edDepartamento44002.setConstraintFormCheck(true);
        edDepartamento44002.setClearOnDelKey(true);
        edDepartamento44002.setUseClearButton(false);
        edDepartamento44002.setHideClearButtonOnNullValue(false);
        edDepartamento44002.setAlign("alLeft");
        FGridPanel2.addChildren(edDepartamento44002);
        edDepartamento44002.applyProperties();
        addValidatable(edDepartamento44002);
    }

    public TFMemo edAssunto44002 = new TFMemo();

    private void init_edAssunto44002() {
        edAssunto44002.setName("edAssunto44002");
        edAssunto44002.setLeft(146);
        edAssunto44002.setTop(104);
        edAssunto44002.setWidth(400);
        edAssunto44002.setHeight(42);
        edAssunto44002.setHint("Assunto");
        edAssunto44002.setAlign("alLeft");
        edAssunto44002.setCharCase("ccNormal");
        edAssunto44002.setFontColor("clWindowText");
        edAssunto44002.setFontSize(-11);
        edAssunto44002.setFontName("Tahoma");
        edAssunto44002.setFontStyle("[]");
        edAssunto44002.setMaxlength(200);
        edAssunto44002.setFieldName("ASSUNTO");
        edAssunto44002.setTable(tbEmailModelo);
        edAssunto44002.setFlexVflex("ftFalse");
        edAssunto44002.setFlexHflex("ftTrue");
        edAssunto44002.setHelpCaption("Assunto*");
        edAssunto44002.setConstraintExpression("value is null");
        edAssunto44002.setConstraintMessage("Campo Assunto*, preenchimento \u00E9 obrigat\u00F3rio");
        edAssunto44002.setConstraintCheckWhen("cwImmediate");
        edAssunto44002.setConstraintCheckType("ctExpression");
        edAssunto44002.setConstraintFocusOnError(false);
        edAssunto44002.setConstraintGroupName("grpTbemailmodelo");
        edAssunto44002.setConstraintEnableUI(true);
        edAssunto44002.setConstraintEnabled(true);
        edAssunto44002.setConstraintFormCheck(true);
        FGridPanel2.addChildren(edAssunto44002);
        edAssunto44002.applyProperties();
        addValidatable(edAssunto44002);
    }

    public TFCombo edTpArquivoAnexoMensagem44002 = new TFCombo();

    private void init_edTpArquivoAnexoMensagem44002() {
        edTpArquivoAnexoMensagem44002.setName("edTpArquivoAnexoMensagem44002");
        edTpArquivoAnexoMensagem44002.setLeft(146);
        edTpArquivoAnexoMensagem44002.setTop(146);
        edTpArquivoAnexoMensagem44002.setWidth(200);
        edTpArquivoAnexoMensagem44002.setHeight(21);
        edTpArquivoAnexoMensagem44002.setHint("Tp arquivo anexo mensagem.  Op\u00E7\u00F5es: Sim=S; N\u00E3o=N");
        edTpArquivoAnexoMensagem44002.setTable(tbEmailModelo);
        edTpArquivoAnexoMensagem44002.setFieldName("TP_ARQUIVO_ANEXO_MENSAGEM");
        edTpArquivoAnexoMensagem44002.setFlex(true);
        edTpArquivoAnexoMensagem44002.setListOptions("Sim=S; N\u00E3o=N");
        edTpArquivoAnexoMensagem44002.setHelpCaption("Tipo Arquivo Anexo");
        edTpArquivoAnexoMensagem44002.setReadOnly(false);
        edTpArquivoAnexoMensagem44002.setRequired(false);
        edTpArquivoAnexoMensagem44002.setPrompt("Selecione");
        edTpArquivoAnexoMensagem44002.setConstraintCheckWhen("cwImmediate");
        edTpArquivoAnexoMensagem44002.setConstraintCheckType("ctExpression");
        edTpArquivoAnexoMensagem44002.setConstraintFocusOnError(false);
        edTpArquivoAnexoMensagem44002.setConstraintEnableUI(true);
        edTpArquivoAnexoMensagem44002.setConstraintEnabled(false);
        edTpArquivoAnexoMensagem44002.setConstraintFormCheck(true);
        edTpArquivoAnexoMensagem44002.setClearOnDelKey(true);
        edTpArquivoAnexoMensagem44002.setUseClearButton(false);
        edTpArquivoAnexoMensagem44002.setHideClearButtonOnNullValue(false);
        edTpArquivoAnexoMensagem44002.setAlign("alLeft");
        FGridPanel2.addChildren(edTpArquivoAnexoMensagem44002);
        edTpArquivoAnexoMensagem44002.applyProperties();
        addValidatable(edTpArquivoAnexoMensagem44002);
    }

    public TFString edIdTemplate44002 = new TFString();

    private void init_edIdTemplate44002() {
        edIdTemplate44002.setName("edIdTemplate44002");
        edIdTemplate44002.setLeft(146);
        edIdTemplate44002.setTop(167);
        edIdTemplate44002.setWidth(400);
        edIdTemplate44002.setHeight(19);
        edIdTemplate44002.setHint("Id. Template de integra\u00E7\u00E3o entre os templates NBS/Zenvia");
        edIdTemplate44002.setTable(tbEmailModelo);
        edIdTemplate44002.setFieldName("ID_TEMPLATE");
        edIdTemplate44002.setHelpCaption("Id. Template(Zenvia)");
        edIdTemplate44002.setFlex(false);
        edIdTemplate44002.setRequired(false);
        edIdTemplate44002.setConstraintCheckWhen("cwImmediate");
        edIdTemplate44002.setConstraintCheckType("ctExpression");
        edIdTemplate44002.setConstraintFocusOnError(false);
        edIdTemplate44002.setConstraintEnableUI(true);
        edIdTemplate44002.setConstraintEnabled(false);
        edIdTemplate44002.setConstraintFormCheck(true);
        edIdTemplate44002.setCharCase("ccNormal");
        edIdTemplate44002.setPwd(false);
        edIdTemplate44002.setMaxlength(500);
        edIdTemplate44002.setAlign("alLeft");
        edIdTemplate44002.setFontColor("clWindowText");
        edIdTemplate44002.setFontSize(-13);
        edIdTemplate44002.setFontName("Tahoma");
        edIdTemplate44002.setFontStyle("[]");
        edIdTemplate44002.setSaveLiteralCharacter(false);
        edIdTemplate44002.applyProperties();
        FGridPanel2.addChildren(edIdTemplate44002);
        addValidatable(edIdTemplate44002);
    }

    public TFString edtCelTemplateAprov = new TFString();

    private void init_edtCelTemplateAprov() {
        edtCelTemplateAprov.setName("edtCelTemplateAprov");
        edtCelTemplateAprov.setLeft(146);
        edtCelTemplateAprov.setTop(186);
        edtCelTemplateAprov.setWidth(200);
        edtCelTemplateAprov.setHeight(19);
        edtCelTemplateAprov.setTable(tbEmailModelo);
        edtCelTemplateAprov.setFieldName("CELULAR_APROVOU_TEMPLATE");
        edtCelTemplateAprov.setFlex(false);
        edtCelTemplateAprov.setRequired(false);
        edtCelTemplateAprov.setConstraintCheckWhen("cwImmediate");
        edtCelTemplateAprov.setConstraintCheckType("ctExpression");
        edtCelTemplateAprov.setConstraintFocusOnError(false);
        edtCelTemplateAprov.setConstraintEnableUI(true);
        edtCelTemplateAprov.setConstraintEnabled(false);
        edtCelTemplateAprov.setConstraintFormCheck(true);
        edtCelTemplateAprov.setCharCase("ccNormal");
        edtCelTemplateAprov.setPwd(false);
        edtCelTemplateAprov.setMaxlength(0);
        edtCelTemplateAprov.setAlign("alLeft");
        edtCelTemplateAprov.setEnabled(false);
        edtCelTemplateAprov.setFontColor("clWindowText");
        edtCelTemplateAprov.setFontSize(-13);
        edtCelTemplateAprov.setFontName("Tahoma");
        edtCelTemplateAprov.setFontStyle("[]");
        edtCelTemplateAprov.setSaveLiteralCharacter(false);
        edtCelTemplateAprov.applyProperties();
        FGridPanel2.addChildren(edtCelTemplateAprov);
        addValidatable(edtCelTemplateAprov);
    }

    public TFCheckBox edEhChat44002 = new TFCheckBox();

    private void init_edEhChat44002() {
        edEhChat44002.setName("edEhChat44002");
        edEhChat44002.setLeft(146);
        edEhChat44002.setTop(205);
        edEhChat44002.setWidth(16);
        edEhChat44002.setHeight(19);
        edEhChat44002.setHint("Identifica se o template \u00E9 de chat.");
        edEhChat44002.setAlign("alLeft");
        edEhChat44002.setFontColor("clWindowText");
        edEhChat44002.setFontSize(-11);
        edEhChat44002.setFontName("Tahoma");
        edEhChat44002.setFontStyle("[]");
        edEhChat44002.setTable(tbEmailModelo);
        edEhChat44002.setFieldName("EH_CHAT");
        edEhChat44002.setHelpCaption("\u00C9 Template de Chat?");
        edEhChat44002.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(edEhChat44002);
        edEhChat44002.applyProperties();
    }

    public TFTabsheet tsMensagem = new TFTabsheet();

    private void init_tsMensagem() {
        tsMensagem.setName("tsMensagem");
        tsMensagem.setCaption("Mensagem");
        tsMensagem.setClosable(false);
        pgPrincipal.addChildren(tsMensagem);
        tsMensagem.applyProperties();
    }

    public TFVBox gbDetalhe44003 = new TFVBox();

    private void init_gbDetalhe44003() {
        gbDetalhe44003.setName("gbDetalhe44003");
        gbDetalhe44003.setLeft(0);
        gbDetalhe44003.setTop(0);
        gbDetalhe44003.setWidth(966);
        gbDetalhe44003.setHeight(382);
        gbDetalhe44003.setAlign("alClient");
        gbDetalhe44003.setBorderStyle("stNone");
        gbDetalhe44003.setPaddingTop(0);
        gbDetalhe44003.setPaddingLeft(0);
        gbDetalhe44003.setPaddingRight(0);
        gbDetalhe44003.setPaddingBottom(0);
        gbDetalhe44003.setMarginTop(0);
        gbDetalhe44003.setMarginLeft(0);
        gbDetalhe44003.setMarginRight(0);
        gbDetalhe44003.setMarginBottom(0);
        gbDetalhe44003.setSpacing(1);
        gbDetalhe44003.setFlexVflex("ftTrue");
        gbDetalhe44003.setFlexHflex("ftTrue");
        gbDetalhe44003.setScrollable(false);
        gbDetalhe44003.setBoxShadowConfigHorizontalLength(10);
        gbDetalhe44003.setBoxShadowConfigVerticalLength(10);
        gbDetalhe44003.setBoxShadowConfigBlurRadius(5);
        gbDetalhe44003.setBoxShadowConfigSpreadRadius(0);
        gbDetalhe44003.setBoxShadowConfigShadowColor("clBlack");
        gbDetalhe44003.setBoxShadowConfigOpacity(75);
        tsMensagem.addChildren(gbDetalhe44003);
        gbDetalhe44003.applyProperties();
    }

    public TFGridPanel gridPanelDetailEmpty44003 = new TFGridPanel();

    private void init_gridPanelDetailEmpty44003() {
        gridPanelDetailEmpty44003.setName("gridPanelDetailEmpty44003");
        gridPanelDetailEmpty44003.setLeft(0);
        gridPanelDetailEmpty44003.setTop(0);
        gridPanelDetailEmpty44003.setWidth(0);
        gridPanelDetailEmpty44003.setHeight(26);
        TFGridPanelColumn item56 = new TFGridPanelColumn();
        item56.setValue(50.000000000000000000);
        gridPanelDetailEmpty44003.getColumnCollection().add(item56);
        TFGridPanelColumn item57 = new TFGridPanelColumn();
        item57.setValue(50.000000000000000000);
        gridPanelDetailEmpty44003.getColumnCollection().add(item57);
        TFGridPanelRow item58 = new TFGridPanelRow();
        item58.setSizeStyle("ssAbsolute");
        item58.setValue(21.000000000000000000);
        gridPanelDetailEmpty44003.getRowCollection().add(item58);
        gridPanelDetailEmpty44003.setVisible(false);
        gridPanelDetailEmpty44003.setFlexVflex("ftTrue");
        gridPanelDetailEmpty44003.setFlexHflex("ftTrue");
        gridPanelDetailEmpty44003.setAllRowFlex(false);
        gridPanelDetailEmpty44003.setColumnTabOrder(false);
        gbDetalhe44003.addChildren(gridPanelDetailEmpty44003);
        gridPanelDetailEmpty44003.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(27);
        FHBox2.setWidth(897);
        FHBox2.setHeight(326);
        FHBox2.setAlign("alClient");
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(5);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        gbDetalhe44003.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(665);
        FVBox3.setHeight(318);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFMemo edMensagem = new TFMemo();

    private void init_edMensagem() {
        edMensagem.setName("edMensagem");
        edMensagem.setLeft(0);
        edMensagem.setTop(0);
        edMensagem.setWidth(185);
        edMensagem.setHeight(89);
        edMensagem.setCharCase("ccNormal");
        edMensagem.setFontColor("clWindowText");
        edMensagem.setFontSize(-11);
        edMensagem.setFontName("Tahoma");
        edMensagem.setFontStyle("[]");
        edMensagem.setMaxlength(0);
        edMensagem.setReadOnly(true);
        edMensagem.setFieldName("MENSAGEM");
        edMensagem.setTable(tbEmailModelo);
        edMensagem.setFlexVflex("ftTrue");
        edMensagem.setFlexHflex("ftTrue");
        edMensagem.setConstraintCheckWhen("cwImmediate");
        edMensagem.setConstraintCheckType("ctExpression");
        edMensagem.setConstraintFocusOnError(false);
        edMensagem.setConstraintEnableUI(true);
        edMensagem.setConstraintEnabled(false);
        edMensagem.setConstraintFormCheck(true);
        FVBox3.addChildren(edMensagem);
        edMensagem.applyProperties();
        addValidatable(edMensagem);
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(665);
        FVBox4.setTop(0);
        FVBox4.setWidth(247);
        FVBox4.setHeight(241);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(5);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFCombo edCatTag = new TFCombo();

    private void init_edCatTag() {
        edCatTag.setName("edCatTag");
        edCatTag.setLeft(0);
        edCatTag.setTop(0);
        edCatTag.setWidth(234);
        edCatTag.setHeight(21);
        edCatTag.setFlex(true);
        edCatTag.setListOptions("Gen\u00E9rico=G;P\u00F3s-Vendas=S");
        edCatTag.setReadOnly(true);
        edCatTag.setRequired(false);
        edCatTag.setPrompt("Selecione");
        edCatTag.setConstraintCheckWhen("cwImmediate");
        edCatTag.setConstraintCheckType("ctExpression");
        edCatTag.setConstraintFocusOnError(false);
        edCatTag.setConstraintEnableUI(true);
        edCatTag.setConstraintEnabled(false);
        edCatTag.setConstraintFormCheck(true);
        edCatTag.setClearOnDelKey(true);
        edCatTag.setUseClearButton(false);
        edCatTag.setHideClearButtonOnNullValue(false);
        edCatTag.setEnabled(false);
        edCatTag.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edCatTagChange(event);
            processarFlow("FrmTemplates", "edCatTag", "OnChange");
        });
        FVBox4.addChildren(edCatTag);
        edCatTag.applyProperties();
        addValidatable(edCatTag);
    }

    public TFGrid FGrid1 = new TFGrid();

    private void init_FGrid1() {
        FGrid1.setName("FGrid1");
        FGrid1.setLeft(0);
        FGrid1.setTop(22);
        FGrid1.setWidth(236);
        FGrid1.setHeight(120);
        FGrid1.setEnabled(false);
        FGrid1.setTable(tbEmailModeloTag);
        FGrid1.setFlexVflex("ftTrue");
        FGrid1.setFlexHflex("ftTrue");
        FGrid1.setPagingEnabled(true);
        FGrid1.setFrozenColumns(0);
        FGrid1.setShowFooter(false);
        FGrid1.setShowHeader(false);
        FGrid1.setMultiSelection(false);
        FGrid1.setGroupingEnabled(false);
        FGrid1.setGroupingExpanded(false);
        FGrid1.setGroupingShowFooter(false);
        FGrid1.setCrosstabEnabled(false);
        FGrid1.setCrosstabGroupType("cgtConcat");
        FGrid1.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FGrid1DoubleClick(event);
            processarFlow("FrmTemplates", "FGrid1", "OnDoubleClick");
        });
        FGrid1.setEditionEnabled(false);
        FGrid1.setNoBorder(false);
        TFGridColumn item59 = new TFGridColumn();
        item59.setFieldName("TAG");
        item59.setTitleCaption("Tag");
        item59.setWidth(187);
        item59.setVisible(true);
        item59.setPrecision(0);
        item59.setTextAlign("taLeft");
        item59.setFieldType("ftString");
        item59.setFlexRatio(0);
        item59.setSort(false);
        item59.setImageHeader(0);
        item59.setWrap(false);
        item59.setFlex(true);
        item59.setCharCase("ccNormal");
        item59.setBlobConfigMimeType("bmtText");
        item59.setBlobConfigShowType("btImageViewer");
        item59.setShowLabel(true);
        item59.setEditorEditType("etTFString");
        item59.setEditorPrecision(0);
        item59.setEditorMaxLength(100);
        item59.setEditorLookupFilterKey(0);
        item59.setEditorLookupFilterDesc(0);
        item59.setEditorPopupHeight(400);
        item59.setEditorPopupWidth(400);
        item59.setEditorCharCase("ccNormal");
        item59.setEditorEnabled(false);
        item59.setEditorReadOnly(false);
        item59.setHiperLink(false);
        item59.setEditorConstraintCheckWhen("cwImmediate");
        item59.setEditorConstraintCheckType("ctExpression");
        item59.setEditorConstraintFocusOnError(false);
        item59.setEditorConstraintEnableUI(true);
        item59.setEditorConstraintEnabled(false);
        item59.setEmpty(false);
        item59.setMobileOptsShowMobile(false);
        item59.setMobileOptsOrder(0);
        item59.setBoxSize(0);
        item59.setImageSrcType("istSource");
        FGrid1.getColumns().add(item59);
        FVBox4.addChildren(FGrid1);
        FGrid1.applyProperties();
    }

    public TFSchema scCrmEmailModelo;

    private void init_scCrmEmailModelo() {
        scCrmEmailModelo = rn.scCrmEmailModelo;
        scCrmEmailModelo.setName("scCrmEmailModelo");
        TFSchemaItem item60 = new TFSchemaItem();
        item60.setTable(tbEmailModelo);
        scCrmEmailModelo.getTables().add(item60);
        TFSchemaItem item61 = new TFSchemaItem();
        item61.setTable(tbNbsapiMessageTemplate);
        scCrmEmailModelo.getTables().add(item61);
        scCrmEmailModelo.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FrmTemplateskeyActionPesquisar(final Event<Object> event);

    public abstract void FrmTemplateskeyActionIncluir(final Event<Object> event);

    public abstract void FrmTemplateskeyActionAlterar(final Event<Object> event);

    public abstract void FrmTemplateskeyActionExcluir(final Event<Object> event);

    public abstract void FrmTemplateskeyActionSalvar(final Event<Object> event);

    public abstract void FrmTemplateskeyActionCancelar(final Event<Object> event);

    public abstract void FrmTemplateskeyActionAnterior(final Event<Object> event);

    public abstract void FrmTemplateskeyActionProximo(final Event<Object> event);

    public abstract void FrmTemplateskeyActionAceitar(final Event<Object> event);

    public abstract void FrmTemplateskeyActionSalvarContinuar(final Event<Object> event);

    public abstract void efAplicacaoEnter(final Event<Object> event);

    public abstract void efDepartamentoEnter(final Event<Object> event);

    public abstract void gridPrincipalClickImageAlterar(final Event<Object> event);

    public abstract void gridPrincipalClickImageDelete(final Event<Object> event);

    public abstract void edCatTagChange(final Event<Object> event);

    public abstract void FGrid1DoubleClick(final Event<Object> event);

    public void btnConsultarClick(final Event<Object> event) {
        if (btnConsultar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnFiltroAvancadoClick(final Event<Object> event) {
        if (btnFiltroAvancado.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnFiltroAvancado");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarContinuarClick(final Event<Object> event) {
        if (btnSalvarContinuar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarContinuar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAnteriorClick(final Event<Object> event) {
        if (btnAnterior.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAnterior");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnProximoClick(final Event<Object> event) {
        if (btnProximo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnProximo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnEnviarAlterarTemplateClick(final Event<Object> event) {
        if (btnEnviarAlterarTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnEnviarAlterarTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnMaisClick(final Event<Object> event) {
        if (btnMais.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnMais");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void menuItemAbreTabelaAuxClick(final Event<Object> event);

    public abstract void menuHabilitaNavegacaoClick(final Event<Object> event);

    public abstract void menuSelecaoMultiplaClick(final Event<Object> event);

    public abstract void menuItemConfgGridClick(final Event<Object> event);

    public abstract void menuItemExportPdfClick(final Event<Object> event);

    public abstract void menuItemExportExcelClick(final Event<Object> event);

    public abstract void menuItemHelpClick(final Event<Object> event);

    public abstract void tbEmailModeloAfterScroll(final Event<Object> event);

    public abstract void tbEmailModeloTagAfterOpen(final Event<Object> event);

}