package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPainelIndicadores extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PainelIndicadoresRNA rn = null;

    public FrmPainelIndicadores() {
        try {
            rn = (freedom.bytecode.rn.PainelIndicadoresRNA) getRN(freedom.bytecode.rn.wizard.PainelIndicadoresRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbComboGrupo();
        init_tbListaPainel();
        init_tbPainel();
        init_tbPainelAcessoFuncao();
        init_tbPainelIndicador();
        init_tbGridIndicadores();
        init_tbGridAcessoFuncao();
        init_tbPainelSemaforo();
        init_FPopupMenu1();
        init_FMenuItem1();
        init_FMenuItem2();
        init_FMenuItem3();
        init_FMenuItem4();
        init_FMenuItem5();
        init_FMenuItem6();
        init_FMenuItem7();
        init_FMenuItem8();
        init_vboxPrincipal();
        init_pgIndicadores();
        init_tabLista();
        init_FHBox11();
        init_vboxLista();
        init_FHBox2();
        init_cbbGrupoLista();
        init_hboxAtivoInativo();
        init_hboxAtivo();
        init_FHBox5();
        init_FLabel1();
        init_FHBox6();
        init_hboxInativo();
        init_FHBox8();
        init_FLabel2();
        init_FHBox9();
        init_gridListaPainel();
        init_FVBox1();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_tabIndicadores();
        init_vboxIndicadores();
        init_FHBox4();
        init_FLabel4();
        init_lblPainel();
        init_FHBox10();
        init_gridIndicadores();
        init_FVBox2();
        init_btnNovoIndicadores();
        init_btnExcluirIndicadores();
        init_btnOrderAcima();
        init_btnOrderAbaixo();
        init_tabAcessoFuncao();
        init_vboxAcessoFuncao();
        init_FHBox7();
        init_FLabel5();
        init_FLabel6();
        init_FHBox3();
        init_FGrid1();
        init_FVBox5();
        init_btnNovoAcessoFunc();
        init_btnExcluirAcessoFunc();
        init_FrmPainelIndicadores();
    }

    public BSC_COMBO_GRUPO tbComboGrupo;

    private void init_tbComboGrupo() {
        tbComboGrupo = rn.tbComboGrupo;
        tbComboGrupo.setName("tbComboGrupo");
        tbComboGrupo.setMaxRowCount(200);
        tbComboGrupo.setWKey("382028;38201");
        tbComboGrupo.setRatioBatchSize(20);
        getTables().put(tbComboGrupo, "tbComboGrupo");
        tbComboGrupo.applyProperties();
    }

    public BSC_LISTA_PAINEL tbListaPainel;

    private void init_tbListaPainel() {
        tbListaPainel = rn.tbListaPainel;
        tbListaPainel.setName("tbListaPainel");
        tbListaPainel.setMaxRowCount(200);
        tbListaPainel.setWKey("382028;38202");
        tbListaPainel.setRatioBatchSize(20);
        getTables().put(tbListaPainel, "tbListaPainel");
        tbListaPainel.applyProperties();
    }

    public BSC_PAINEL tbPainel;

    private void init_tbPainel() {
        tbPainel = rn.tbPainel;
        tbPainel.setName("tbPainel");
        tbPainel.setMaxRowCount(200);
        tbPainel.setWKey("382028;38203");
        tbPainel.setRatioBatchSize(20);
        getTables().put(tbPainel, "tbPainel");
        tbPainel.applyProperties();
    }

    public BSC_PAINEL_ACESSO_FUNCAO tbPainelAcessoFuncao;

    private void init_tbPainelAcessoFuncao() {
        tbPainelAcessoFuncao = rn.tbPainelAcessoFuncao;
        tbPainelAcessoFuncao.setName("tbPainelAcessoFuncao");
        tbPainelAcessoFuncao.setMaxRowCount(200);
        tbPainelAcessoFuncao.setWKey("382028;38204");
        tbPainelAcessoFuncao.setRatioBatchSize(20);
        getTables().put(tbPainelAcessoFuncao, "tbPainelAcessoFuncao");
        tbPainelAcessoFuncao.applyProperties();
    }

    public BSC_PAINEL_INDICADOR tbPainelIndicador;

    private void init_tbPainelIndicador() {
        tbPainelIndicador = rn.tbPainelIndicador;
        tbPainelIndicador.setName("tbPainelIndicador");
        tbPainelIndicador.setMaxRowCount(200);
        tbPainelIndicador.setWKey("382028;38205");
        tbPainelIndicador.setRatioBatchSize(20);
        getTables().put(tbPainelIndicador, "tbPainelIndicador");
        tbPainelIndicador.applyProperties();
    }

    public BSC_GRID_INDICADORES tbGridIndicadores;

    private void init_tbGridIndicadores() {
        tbGridIndicadores = rn.tbGridIndicadores;
        tbGridIndicadores.setName("tbGridIndicadores");
        tbGridIndicadores.setMaxRowCount(200);
        tbGridIndicadores.setWKey("382028;38207");
        tbGridIndicadores.setRatioBatchSize(20);
        getTables().put(tbGridIndicadores, "tbGridIndicadores");
        tbGridIndicadores.applyProperties();
    }

    public BSC_GRID_ACESSO_FUNCAO tbGridAcessoFuncao;

    private void init_tbGridAcessoFuncao() {
        tbGridAcessoFuncao = rn.tbGridAcessoFuncao;
        tbGridAcessoFuncao.setName("tbGridAcessoFuncao");
        tbGridAcessoFuncao.setMaxRowCount(200);
        tbGridAcessoFuncao.setWKey("382028;38208");
        tbGridAcessoFuncao.setRatioBatchSize(20);
        getTables().put(tbGridAcessoFuncao, "tbGridAcessoFuncao");
        tbGridAcessoFuncao.applyProperties();
    }

    public BSC_PAINEL_SEMAFORO tbPainelSemaforo;

    private void init_tbPainelSemaforo() {
        tbPainelSemaforo = rn.tbPainelSemaforo;
        tbPainelSemaforo.setName("tbPainelSemaforo");
        tbPainelSemaforo.setMaxRowCount(200);
        tbPainelSemaforo.setWKey("382028;38209");
        tbPainelSemaforo.setRatioBatchSize(20);
        getTables().put(tbPainelSemaforo, "tbPainelSemaforo");
        tbPainelSemaforo.applyProperties();
    }

    public TFPopupMenu FPopupMenu1 = new TFPopupMenu();

    private void init_FPopupMenu1() {
        FPopupMenu1.setName("FPopupMenu1");
        FrmPainelIndicadores.addChildren(FPopupMenu1);
        FPopupMenu1.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("Novo");
        FMenuItem1.setImageIndex(6);
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem FMenuItem2 = new TFMenuItem();

    private void init_FMenuItem2() {
        FMenuItem2.setName("FMenuItem2");
        FMenuItem2.setCaption("Alterar");
        FMenuItem2.setImageIndex(7);
        FMenuItem2.setAccess(false);
        FMenuItem2.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem2);
        FMenuItem2.applyProperties();
    }

    public TFMenuItem FMenuItem3 = new TFMenuItem();

    private void init_FMenuItem3() {
        FMenuItem3.setName("FMenuItem3");
        FMenuItem3.setCaption("Pesquisar");
        FMenuItem3.setImageIndex(13);
        FMenuItem3.setAccess(false);
        FMenuItem3.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem3);
        FMenuItem3.applyProperties();
    }

    public TFMenuItem FMenuItem4 = new TFMenuItem();

    private void init_FMenuItem4() {
        FMenuItem4.setName("FMenuItem4");
        FMenuItem4.setCaption("Excluir");
        FMenuItem4.setImageIndex(8);
        FMenuItem4.setAccess(false);
        FMenuItem4.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem4);
        FMenuItem4.applyProperties();
    }

    public TFMenuItem FMenuItem5 = new TFMenuItem();

    private void init_FMenuItem5() {
        FMenuItem5.setName("FMenuItem5");
        FMenuItem5.setCaption("Salvar");
        FMenuItem5.setImageIndex(4);
        FMenuItem5.setAccess(false);
        FMenuItem5.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem5);
        FMenuItem5.applyProperties();
    }

    public TFMenuItem FMenuItem6 = new TFMenuItem();

    private void init_FMenuItem6() {
        FMenuItem6.setName("FMenuItem6");
        FMenuItem6.setCaption("Cancelar");
        FMenuItem6.setImageIndex(9);
        FMenuItem6.setAccess(false);
        FMenuItem6.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem6);
        FMenuItem6.applyProperties();
    }

    public TFMenuItem FMenuItem7 = new TFMenuItem();

    private void init_FMenuItem7() {
        FMenuItem7.setName("FMenuItem7");
        FMenuItem7.setCaption("subir");
        FMenuItem7.setImageIndex(382022);
        FMenuItem7.setAccess(false);
        FMenuItem7.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem7);
        FMenuItem7.applyProperties();
    }

    public TFMenuItem FMenuItem8 = new TFMenuItem();

    private void init_FMenuItem8() {
        FMenuItem8.setName("FMenuItem8");
        FMenuItem8.setCaption("descer");
        FMenuItem8.setImageIndex(4600304);
        FMenuItem8.setAccess(false);
        FMenuItem8.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem8);
        FMenuItem8.applyProperties();
    }

    protected TFForm FrmPainelIndicadores = this;
    private void init_FrmPainelIndicadores() {
        FrmPainelIndicadores.setName("FrmPainelIndicadores");
        FrmPainelIndicadores.setCaption("Indicadores");
        FrmPainelIndicadores.setClientHeight(663);
        FrmPainelIndicadores.setClientWidth(1014);
        FrmPainelIndicadores.setColor("clBtnFace");
        FrmPainelIndicadores.setWKey("382028");
        FrmPainelIndicadores.setSpacing(0);
        FrmPainelIndicadores.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(1014);
        vboxPrincipal.setHeight(663);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(5);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(5);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(5);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmPainelIndicadores.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFPageControl pgIndicadores = new TFPageControl();

    private void init_pgIndicadores() {
        pgIndicadores.setName("pgIndicadores");
        pgIndicadores.setLeft(0);
        pgIndicadores.setTop(0);
        pgIndicadores.setWidth(1009);
        pgIndicadores.setHeight(658);
        pgIndicadores.setTabPosition("tpTop");
        pgIndicadores.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgIndicadoresChange(event);
            processarFlow("FrmPainelIndicadores", "pgIndicadores", "OnChange");
        });
        pgIndicadores.setFlexVflex("ftTrue");
        pgIndicadores.setFlexHflex("ftTrue");
        pgIndicadores.setRenderStyle("rsTabbed");
        pgIndicadores.applyProperties();
        vboxPrincipal.addChildren(pgIndicadores);
    }

    public TFTabsheet tabLista = new TFTabsheet();

    private void init_tabLista() {
        tabLista.setName("tabLista");
        tabLista.setCaption("Painel");
        tabLista.setVisible(true);
        tabLista.setClosable(false);
        pgIndicadores.addChildren(tabLista);
        tabLista.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(1001);
        FHBox11.setHeight(630);
        FHBox11.setAlign("alClient");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(5);
        FHBox11.setPaddingLeft(5);
        FHBox11.setPaddingRight(5);
        FHBox11.setPaddingBottom(5);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftTrue");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        tabLista.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFVBox vboxLista = new TFVBox();

    private void init_vboxLista() {
        vboxLista.setName("vboxLista");
        vboxLista.setLeft(0);
        vboxLista.setTop(0);
        vboxLista.setWidth(509);
        vboxLista.setHeight(530);
        vboxLista.setBorderStyle("stNone");
        vboxLista.setPaddingTop(0);
        vboxLista.setPaddingLeft(0);
        vboxLista.setPaddingRight(0);
        vboxLista.setPaddingBottom(0);
        vboxLista.setMarginTop(0);
        vboxLista.setMarginLeft(0);
        vboxLista.setMarginRight(0);
        vboxLista.setMarginBottom(0);
        vboxLista.setSpacing(5);
        vboxLista.setFlexVflex("ftTrue");
        vboxLista.setFlexHflex("ftTrue");
        vboxLista.setScrollable(false);
        vboxLista.setBoxShadowConfigHorizontalLength(10);
        vboxLista.setBoxShadowConfigVerticalLength(10);
        vboxLista.setBoxShadowConfigBlurRadius(5);
        vboxLista.setBoxShadowConfigSpreadRadius(0);
        vboxLista.setBoxShadowConfigShadowColor("clBlack");
        vboxLista.setBoxShadowConfigOpacity(75);
        FHBox11.addChildren(vboxLista);
        vboxLista.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(475);
        FHBox2.setHeight(49);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vboxLista.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFCombo cbbGrupoLista = new TFCombo();

    private void init_cbbGrupoLista() {
        cbbGrupoLista.setName("cbbGrupoLista");
        cbbGrupoLista.setLeft(0);
        cbbGrupoLista.setTop(0);
        cbbGrupoLista.setWidth(145);
        cbbGrupoLista.setHeight(21);
        cbbGrupoLista.setLookupTable(tbComboGrupo);
        cbbGrupoLista.setLookupKey("ID");
        cbbGrupoLista.setLookupDesc("DESCRICAO_GRUPO");
        cbbGrupoLista.setFlex(true);
        cbbGrupoLista.setReadOnly(true);
        cbbGrupoLista.setRequired(false);
        cbbGrupoLista.setPrompt("Selecione");
        cbbGrupoLista.setConstraintCheckWhen("cwImmediate");
        cbbGrupoLista.setConstraintCheckType("ctExpression");
        cbbGrupoLista.setConstraintFocusOnError(false);
        cbbGrupoLista.setConstraintEnableUI(true);
        cbbGrupoLista.setConstraintEnabled(false);
        cbbGrupoLista.setConstraintFormCheck(true);
        cbbGrupoLista.setClearOnDelKey(true);
        cbbGrupoLista.setUseClearButton(true);
        cbbGrupoLista.setHideClearButtonOnNullValue(false);
        cbbGrupoLista.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbGrupoListaChange(event);
            processarFlow("FrmPainelIndicadores", "cbbGrupoLista", "OnChange");
        });
        cbbGrupoLista.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbGrupoListaClearClick(event);
            processarFlow("FrmPainelIndicadores", "cbbGrupoLista", "OnClearClick");
        });
        FHBox2.addChildren(cbbGrupoLista);
        cbbGrupoLista.applyProperties();
        addValidatable(cbbGrupoLista);
    }

    public TFHBox hboxAtivoInativo = new TFHBox();

    private void init_hboxAtivoInativo() {
        hboxAtivoInativo.setName("hboxAtivoInativo");
        hboxAtivoInativo.setLeft(145);
        hboxAtivoInativo.setTop(0);
        hboxAtivoInativo.setWidth(170);
        hboxAtivoInativo.setHeight(37);
        hboxAtivoInativo.setBorderStyle("stNone");
        hboxAtivoInativo.setPaddingTop(0);
        hboxAtivoInativo.setPaddingLeft(0);
        hboxAtivoInativo.setPaddingRight(0);
        hboxAtivoInativo.setPaddingBottom(0);
        hboxAtivoInativo.setMarginTop(0);
        hboxAtivoInativo.setMarginLeft(0);
        hboxAtivoInativo.setMarginRight(0);
        hboxAtivoInativo.setMarginBottom(0);
        hboxAtivoInativo.setSpacing(1);
        hboxAtivoInativo.setFlexVflex("ftFalse");
        hboxAtivoInativo.setFlexHflex("ftFalse");
        hboxAtivoInativo.setScrollable(false);
        hboxAtivoInativo.setBoxShadowConfigHorizontalLength(10);
        hboxAtivoInativo.setBoxShadowConfigVerticalLength(10);
        hboxAtivoInativo.setBoxShadowConfigBlurRadius(5);
        hboxAtivoInativo.setBoxShadowConfigSpreadRadius(0);
        hboxAtivoInativo.setBoxShadowConfigShadowColor("clBlack");
        hboxAtivoInativo.setBoxShadowConfigOpacity(75);
        hboxAtivoInativo.setVAlign("tvTop");
        FHBox2.addChildren(hboxAtivoInativo);
        hboxAtivoInativo.applyProperties();
    }

    public TFHBox hboxAtivo = new TFHBox();

    private void init_hboxAtivo() {
        hboxAtivo.setName("hboxAtivo");
        hboxAtivo.setLeft(0);
        hboxAtivo.setTop(0);
        hboxAtivo.setWidth(80);
        hboxAtivo.setHeight(32);
        hboxAtivo.setBorderStyle("stNone");
        hboxAtivo.setPaddingTop(5);
        hboxAtivo.setPaddingLeft(0);
        hboxAtivo.setPaddingRight(0);
        hboxAtivo.setPaddingBottom(0);
        hboxAtivo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxAtivoClick(event);
            processarFlow("FrmPainelIndicadores", "hboxAtivo", "OnClick");
        });
        hboxAtivo.setMarginTop(0);
        hboxAtivo.setMarginLeft(0);
        hboxAtivo.setMarginRight(0);
        hboxAtivo.setMarginBottom(0);
        hboxAtivo.setSpacing(1);
        hboxAtivo.setFlexVflex("ftTrue");
        hboxAtivo.setFlexHflex("ftTrue");
        hboxAtivo.setScrollable(false);
        hboxAtivo.setBoxShadowConfigHorizontalLength(10);
        hboxAtivo.setBoxShadowConfigVerticalLength(10);
        hboxAtivo.setBoxShadowConfigBlurRadius(5);
        hboxAtivo.setBoxShadowConfigSpreadRadius(0);
        hboxAtivo.setBoxShadowConfigShadowColor("clBlack");
        hboxAtivo.setBoxShadowConfigOpacity(75);
        hboxAtivo.setVAlign("tvTop");
        hboxAtivoInativo.addChildren(hboxAtivo);
        hboxAtivo.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(13);
        FHBox5.setHeight(26);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftMin");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        hboxAtivo.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(13);
        FLabel1.setTop(0);
        FLabel1.setWidth(35);
        FLabel1.setHeight(16);
        FLabel1.setCaption("Ativo");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-13);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        hboxAtivo.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(48);
        FHBox6.setTop(0);
        FHBox6.setWidth(13);
        FHBox6.setHeight(26);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftMin");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        hboxAtivo.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox hboxInativo = new TFHBox();

    private void init_hboxInativo() {
        hboxInativo.setName("hboxInativo");
        hboxInativo.setLeft(80);
        hboxInativo.setTop(0);
        hboxInativo.setWidth(80);
        hboxInativo.setHeight(32);
        hboxInativo.setBorderStyle("stNone");
        hboxInativo.setPaddingTop(5);
        hboxInativo.setPaddingLeft(0);
        hboxInativo.setPaddingRight(0);
        hboxInativo.setPaddingBottom(0);
        hboxInativo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxInativoClick(event);
            processarFlow("FrmPainelIndicadores", "hboxInativo", "OnClick");
        });
        hboxInativo.setMarginTop(0);
        hboxInativo.setMarginLeft(0);
        hboxInativo.setMarginRight(0);
        hboxInativo.setMarginBottom(0);
        hboxInativo.setSpacing(1);
        hboxInativo.setFlexVflex("ftTrue");
        hboxInativo.setFlexHflex("ftTrue");
        hboxInativo.setScrollable(false);
        hboxInativo.setBoxShadowConfigHorizontalLength(10);
        hboxInativo.setBoxShadowConfigVerticalLength(10);
        hboxInativo.setBoxShadowConfigBlurRadius(5);
        hboxInativo.setBoxShadowConfigSpreadRadius(0);
        hboxInativo.setBoxShadowConfigShadowColor("clBlack");
        hboxInativo.setBoxShadowConfigOpacity(75);
        hboxInativo.setVAlign("tvTop");
        hboxAtivoInativo.addChildren(hboxInativo);
        hboxInativo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(13);
        FHBox8.setHeight(26);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftMin");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        hboxInativo.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(13);
        FLabel2.setTop(0);
        FLabel2.setWidth(46);
        FLabel2.setHeight(16);
        FLabel2.setCaption("Inativo");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-13);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        hboxInativo.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(59);
        FHBox9.setTop(0);
        FHBox9.setWidth(13);
        FHBox9.setHeight(26);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftMin");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        hboxInativo.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFGrid gridListaPainel = new TFGrid();

    private void init_gridListaPainel() {
        gridListaPainel.setName("gridListaPainel");
        gridListaPainel.setLeft(0);
        gridListaPainel.setTop(50);
        gridListaPainel.setWidth(474);
        gridListaPainel.setHeight(304);
        gridListaPainel.setTable(tbListaPainel);
        gridListaPainel.setFlexVflex("ftTrue");
        gridListaPainel.setFlexHflex("ftTrue");
        gridListaPainel.setPagingEnabled(false);
        gridListaPainel.setFrozenColumns(0);
        gridListaPainel.setShowFooter(false);
        gridListaPainel.setShowHeader(true);
        gridListaPainel.setMultiSelection(false);
        gridListaPainel.setGroupingEnabled(false);
        gridListaPainel.setGroupingExpanded(false);
        gridListaPainel.setGroupingShowFooter(false);
        gridListaPainel.setCrosstabEnabled(false);
        gridListaPainel.setCrosstabGroupType("cgtConcat");
        gridListaPainel.setEditionEnabled(false);
        gridListaPainel.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID");
        item0.setTitleCaption("Id");
        item0.setWidth(70);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridListaPainel.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Painel");
        item1.setWidth(40);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridListaPainel.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("DESCRICAO_GRUPO");
        item2.setTitleCaption("Grupo");
        item2.setWidth(140);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridListaPainel.getColumns().add(item2);
        vboxLista.addChildren(gridListaPainel);
        gridListaPainel.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(509);
        FVBox1.setTop(0);
        FVBox1.setWidth(80);
        FVBox1.setHeight(254);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox11.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(0);
        btnNovo.setTop(0);
        btnNovo.setWidth(75);
        btnNovo.setHeight(60);
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmPainelIndicadores", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(false);
        btnNovo.setIconReverseDirection(false);
        FVBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(0);
        btnAlterar.setTop(61);
        btnAlterar.setWidth(75);
        btnAlterar.setHeight(60);
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmPainelIndicadores", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);
        FVBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(0);
        btnExcluir.setTop(122);
        btnExcluir.setWidth(75);
        btnExcluir.setHeight(60);
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmPainelIndicadores", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        FVBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFTabsheet tabIndicadores = new TFTabsheet();

    private void init_tabIndicadores() {
        tabIndicadores.setName("tabIndicadores");
        tabIndicadores.setCaption("Indicadores");
        tabIndicadores.setVisible(true);
        tabIndicadores.setClosable(false);
        pgIndicadores.addChildren(tabIndicadores);
        tabIndicadores.applyProperties();
    }

    public TFVBox vboxIndicadores = new TFVBox();

    private void init_vboxIndicadores() {
        vboxIndicadores.setName("vboxIndicadores");
        vboxIndicadores.setLeft(0);
        vboxIndicadores.setTop(0);
        vboxIndicadores.setWidth(1001);
        vboxIndicadores.setHeight(630);
        vboxIndicadores.setAlign("alClient");
        vboxIndicadores.setBorderStyle("stNone");
        vboxIndicadores.setPaddingTop(0);
        vboxIndicadores.setPaddingLeft(5);
        vboxIndicadores.setPaddingRight(5);
        vboxIndicadores.setPaddingBottom(5);
        vboxIndicadores.setMarginTop(0);
        vboxIndicadores.setMarginLeft(0);
        vboxIndicadores.setMarginRight(0);
        vboxIndicadores.setMarginBottom(0);
        vboxIndicadores.setSpacing(3);
        vboxIndicadores.setFlexVflex("ftTrue");
        vboxIndicadores.setFlexHflex("ftTrue");
        vboxIndicadores.setScrollable(false);
        vboxIndicadores.setBoxShadowConfigHorizontalLength(10);
        vboxIndicadores.setBoxShadowConfigVerticalLength(10);
        vboxIndicadores.setBoxShadowConfigBlurRadius(5);
        vboxIndicadores.setBoxShadowConfigSpreadRadius(0);
        vboxIndicadores.setBoxShadowConfigShadowColor("clBlack");
        vboxIndicadores.setBoxShadowConfigOpacity(75);
        tabIndicadores.addChildren(vboxIndicadores);
        vboxIndicadores.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(677);
        FHBox4.setHeight(75);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(25);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(5);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        vboxIndicadores.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(187);
        FLabel4.setHeight(23);
        FLabel4.setCaption("Indicadores do Painel:");
        FLabel4.setFontColor("clBlack");
        FLabel4.setFontSize(-19);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FHBox4.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFLabel lblPainel = new TFLabel();

    private void init_lblPainel() {
        lblPainel.setName("lblPainel");
        lblPainel.setLeft(187);
        lblPainel.setTop(0);
        lblPainel.setWidth(167);
        lblPainel.setHeight(23);
        lblPainel.setCaption("Selecione um Painel");
        lblPainel.setFontColor("15174738");
        lblPainel.setFontSize(-19);
        lblPainel.setFontName("Tahoma");
        lblPainel.setFontStyle("[]");
        lblPainel.setFieldName("DESCRICAO");
        lblPainel.setTable(tbListaPainel);
        lblPainel.setVerticalAlignment("taVerticalCenter");
        lblPainel.setWordBreak(false);
        FHBox4.addChildren(lblPainel);
        lblPainel.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(76);
        FHBox10.setWidth(676);
        FHBox10.setHeight(301);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(5);
        FHBox10.setFlexVflex("ftTrue");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        vboxIndicadores.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFGrid gridIndicadores = new TFGrid();

    private void init_gridIndicadores() {
        gridIndicadores.setName("gridIndicadores");
        gridIndicadores.setLeft(0);
        gridIndicadores.setTop(0);
        gridIndicadores.setWidth(496);
        gridIndicadores.setHeight(252);
        gridIndicadores.setTable(tbGridIndicadores);
        gridIndicadores.setFlexVflex("ftTrue");
        gridIndicadores.setFlexHflex("ftTrue");
        gridIndicadores.setPagingEnabled(false);
        gridIndicadores.setFrozenColumns(0);
        gridIndicadores.setShowFooter(false);
        gridIndicadores.setShowHeader(true);
        gridIndicadores.setMultiSelection(false);
        gridIndicadores.setGroupingEnabled(false);
        gridIndicadores.setGroupingExpanded(false);
        gridIndicadores.setGroupingShowFooter(false);
        gridIndicadores.setCrosstabEnabled(false);
        gridIndicadores.setCrosstabGroupType("cgtConcat");
        gridIndicadores.setEditionEnabled(false);
        gridIndicadores.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID");
        item0.setTitleCaption("Id");
        item0.setWidth(70);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Indicador");
        item1.setWidth(120);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("DISPLAY_ORDER");
        item2.setTitleCaption("Ordem");
        item2.setWidth(65);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridIndicadores.getColumns().add(item2);
        FHBox10.addChildren(gridIndicadores);
        gridIndicadores.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(496);
        FVBox2.setTop(0);
        FVBox2.setWidth(80);
        FVBox2.setHeight(254);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(5);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFButton btnNovoIndicadores = new TFButton();

    private void init_btnNovoIndicadores() {
        btnNovoIndicadores.setName("btnNovoIndicadores");
        btnNovoIndicadores.setLeft(0);
        btnNovoIndicadores.setTop(0);
        btnNovoIndicadores.setWidth(75);
        btnNovoIndicadores.setHeight(60);
        btnNovoIndicadores.setCaption("Novo");
        btnNovoIndicadores.setFontColor("clWindowText");
        btnNovoIndicadores.setFontSize(-11);
        btnNovoIndicadores.setFontName("Tahoma");
        btnNovoIndicadores.setFontStyle("[]");
        btnNovoIndicadores.setLayout("blGlyphTop");
        btnNovoIndicadores.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoIndicadoresClick(event);
            processarFlow("FrmPainelIndicadores", "btnNovoIndicadores", "OnClick");
        });
        btnNovoIndicadores.setImageId(6);
        btnNovoIndicadores.setColor("clBtnFace");
        btnNovoIndicadores.setAccess(false);
        btnNovoIndicadores.setIconReverseDirection(false);
        FVBox2.addChildren(btnNovoIndicadores);
        btnNovoIndicadores.applyProperties();
    }

    public TFButton btnExcluirIndicadores = new TFButton();

    private void init_btnExcluirIndicadores() {
        btnExcluirIndicadores.setName("btnExcluirIndicadores");
        btnExcluirIndicadores.setLeft(0);
        btnExcluirIndicadores.setTop(61);
        btnExcluirIndicadores.setWidth(75);
        btnExcluirIndicadores.setHeight(60);
        btnExcluirIndicadores.setCaption("Excluir");
        btnExcluirIndicadores.setFontColor("clWindowText");
        btnExcluirIndicadores.setFontSize(-11);
        btnExcluirIndicadores.setFontName("Tahoma");
        btnExcluirIndicadores.setFontStyle("[]");
        btnExcluirIndicadores.setLayout("blGlyphTop");
        btnExcluirIndicadores.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirIndicadoresClick(event);
            processarFlow("FrmPainelIndicadores", "btnExcluirIndicadores", "OnClick");
        });
        btnExcluirIndicadores.setImageId(8);
        btnExcluirIndicadores.setColor("clBtnFace");
        btnExcluirIndicadores.setAccess(false);
        btnExcluirIndicadores.setIconReverseDirection(false);
        FVBox2.addChildren(btnExcluirIndicadores);
        btnExcluirIndicadores.applyProperties();
    }

    public TFButton btnOrderAcima = new TFButton();

    private void init_btnOrderAcima() {
        btnOrderAcima.setName("btnOrderAcima");
        btnOrderAcima.setLeft(0);
        btnOrderAcima.setTop(122);
        btnOrderAcima.setWidth(75);
        btnOrderAcima.setHeight(60);
        btnOrderAcima.setHint("Alterar Ordem Acima");
        btnOrderAcima.setFontColor("clWindowText");
        btnOrderAcima.setFontSize(-11);
        btnOrderAcima.setFontName("Tahoma");
        btnOrderAcima.setFontStyle("[]");
        btnOrderAcima.setLayout("blGlyphTop");
        btnOrderAcima.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnOrderAcimaClick(event);
            processarFlow("FrmPainelIndicadores", "btnOrderAcima", "OnClick");
        });
        btnOrderAcima.setImageId(382022);
        btnOrderAcima.setColor("clBtnFace");
        btnOrderAcima.setAccess(false);
        btnOrderAcima.setIconReverseDirection(false);
        FVBox2.addChildren(btnOrderAcima);
        btnOrderAcima.applyProperties();
    }

    public TFButton btnOrderAbaixo = new TFButton();

    private void init_btnOrderAbaixo() {
        btnOrderAbaixo.setName("btnOrderAbaixo");
        btnOrderAbaixo.setLeft(0);
        btnOrderAbaixo.setTop(183);
        btnOrderAbaixo.setWidth(75);
        btnOrderAbaixo.setHeight(60);
        btnOrderAbaixo.setHint("Alterar Ordem Abaixo");
        btnOrderAbaixo.setFontColor("clWindowText");
        btnOrderAbaixo.setFontSize(-11);
        btnOrderAbaixo.setFontName("Tahoma");
        btnOrderAbaixo.setFontStyle("[]");
        btnOrderAbaixo.setLayout("blGlyphTop");
        btnOrderAbaixo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnOrderAbaixoClick(event);
            processarFlow("FrmPainelIndicadores", "btnOrderAbaixo", "OnClick");
        });
        btnOrderAbaixo.setImageId(4600304);
        btnOrderAbaixo.setColor("clBtnFace");
        btnOrderAbaixo.setAccess(false);
        btnOrderAbaixo.setIconReverseDirection(false);
        FVBox2.addChildren(btnOrderAbaixo);
        btnOrderAbaixo.applyProperties();
    }

    public TFTabsheet tabAcessoFuncao = new TFTabsheet();

    private void init_tabAcessoFuncao() {
        tabAcessoFuncao.setName("tabAcessoFuncao");
        tabAcessoFuncao.setCaption("Acesso por Fun\u00E7\u00E3o");
        tabAcessoFuncao.setVisible(true);
        tabAcessoFuncao.setClosable(false);
        pgIndicadores.addChildren(tabAcessoFuncao);
        tabAcessoFuncao.applyProperties();
    }

    public TFVBox vboxAcessoFuncao = new TFVBox();

    private void init_vboxAcessoFuncao() {
        vboxAcessoFuncao.setName("vboxAcessoFuncao");
        vboxAcessoFuncao.setLeft(0);
        vboxAcessoFuncao.setTop(0);
        vboxAcessoFuncao.setWidth(1001);
        vboxAcessoFuncao.setHeight(630);
        vboxAcessoFuncao.setAlign("alClient");
        vboxAcessoFuncao.setBorderStyle("stNone");
        vboxAcessoFuncao.setPaddingTop(0);
        vboxAcessoFuncao.setPaddingLeft(5);
        vboxAcessoFuncao.setPaddingRight(5);
        vboxAcessoFuncao.setPaddingBottom(5);
        vboxAcessoFuncao.setMarginTop(0);
        vboxAcessoFuncao.setMarginLeft(0);
        vboxAcessoFuncao.setMarginRight(0);
        vboxAcessoFuncao.setMarginBottom(0);
        vboxAcessoFuncao.setSpacing(3);
        vboxAcessoFuncao.setFlexVflex("ftTrue");
        vboxAcessoFuncao.setFlexHflex("ftTrue");
        vboxAcessoFuncao.setScrollable(false);
        vboxAcessoFuncao.setBoxShadowConfigHorizontalLength(10);
        vboxAcessoFuncao.setBoxShadowConfigVerticalLength(10);
        vboxAcessoFuncao.setBoxShadowConfigBlurRadius(5);
        vboxAcessoFuncao.setBoxShadowConfigSpreadRadius(0);
        vboxAcessoFuncao.setBoxShadowConfigShadowColor("clBlack");
        vboxAcessoFuncao.setBoxShadowConfigOpacity(75);
        tabAcessoFuncao.addChildren(vboxAcessoFuncao);
        vboxAcessoFuncao.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(677);
        FHBox7.setHeight(75);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(25);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(5);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vboxAcessoFuncao.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(150);
        FLabel5.setHeight(23);
        FLabel5.setCaption("Fun\u00E7\u00E3o do Painel:");
        FLabel5.setFontColor("clBlack");
        FLabel5.setFontSize(-19);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FHBox7.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(150);
        FLabel6.setTop(0);
        FLabel6.setWidth(167);
        FLabel6.setHeight(23);
        FLabel6.setCaption("Selecione um Painel");
        FLabel6.setFontColor("15174738");
        FLabel6.setFontSize(-19);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setFieldName("DESCRICAO");
        FLabel6.setTable(tbListaPainel);
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox7.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(76);
        FHBox3.setWidth(676);
        FHBox3.setHeight(301);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(5);
        FHBox3.setFlexVflex("ftTrue");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        vboxAcessoFuncao.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFGrid FGrid1 = new TFGrid();

    private void init_FGrid1() {
        FGrid1.setName("FGrid1");
        FGrid1.setLeft(0);
        FGrid1.setTop(0);
        FGrid1.setWidth(496);
        FGrid1.setHeight(252);
        FGrid1.setTable(tbGridAcessoFuncao);
        FGrid1.setFlexVflex("ftTrue");
        FGrid1.setFlexHflex("ftTrue");
        FGrid1.setPagingEnabled(false);
        FGrid1.setFrozenColumns(0);
        FGrid1.setShowFooter(false);
        FGrid1.setShowHeader(true);
        FGrid1.setMultiSelection(false);
        FGrid1.setGroupingEnabled(false);
        FGrid1.setGroupingExpanded(false);
        FGrid1.setGroupingShowFooter(false);
        FGrid1.setCrosstabEnabled(false);
        FGrid1.setCrosstabGroupType("cgtConcat");
        FGrid1.setEditionEnabled(false);
        FGrid1.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID");
        item0.setTitleCaption("Id");
        item0.setWidth(80);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        FGrid1.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("FUNCAO");
        item1.setTitleCaption("Fun\u00E7\u00E3o");
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        FGrid1.getColumns().add(item1);
        FHBox3.addChildren(FGrid1);
        FGrid1.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(496);
        FVBox5.setTop(0);
        FVBox5.setWidth(80);
        FVBox5.setHeight(254);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(5);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFButton btnNovoAcessoFunc = new TFButton();

    private void init_btnNovoAcessoFunc() {
        btnNovoAcessoFunc.setName("btnNovoAcessoFunc");
        btnNovoAcessoFunc.setLeft(0);
        btnNovoAcessoFunc.setTop(0);
        btnNovoAcessoFunc.setWidth(75);
        btnNovoAcessoFunc.setHeight(60);
        btnNovoAcessoFunc.setCaption("Novo");
        btnNovoAcessoFunc.setFontColor("clWindowText");
        btnNovoAcessoFunc.setFontSize(-11);
        btnNovoAcessoFunc.setFontName("Tahoma");
        btnNovoAcessoFunc.setFontStyle("[]");
        btnNovoAcessoFunc.setLayout("blGlyphTop");
        btnNovoAcessoFunc.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoAcessoFuncClick(event);
            processarFlow("FrmPainelIndicadores", "btnNovoAcessoFunc", "OnClick");
        });
        btnNovoAcessoFunc.setImageId(6);
        btnNovoAcessoFunc.setColor("clBtnFace");
        btnNovoAcessoFunc.setAccess(false);
        btnNovoAcessoFunc.setIconReverseDirection(false);
        FVBox5.addChildren(btnNovoAcessoFunc);
        btnNovoAcessoFunc.applyProperties();
    }

    public TFButton btnExcluirAcessoFunc = new TFButton();

    private void init_btnExcluirAcessoFunc() {
        btnExcluirAcessoFunc.setName("btnExcluirAcessoFunc");
        btnExcluirAcessoFunc.setLeft(0);
        btnExcluirAcessoFunc.setTop(61);
        btnExcluirAcessoFunc.setWidth(75);
        btnExcluirAcessoFunc.setHeight(60);
        btnExcluirAcessoFunc.setCaption("Excluir");
        btnExcluirAcessoFunc.setFontColor("clWindowText");
        btnExcluirAcessoFunc.setFontSize(-11);
        btnExcluirAcessoFunc.setFontName("Tahoma");
        btnExcluirAcessoFunc.setFontStyle("[]");
        btnExcluirAcessoFunc.setLayout("blGlyphTop");
        btnExcluirAcessoFunc.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirAcessoFuncClick(event);
            processarFlow("FrmPainelIndicadores", "btnExcluirAcessoFunc", "OnClick");
        });
        btnExcluirAcessoFunc.setImageId(8);
        btnExcluirAcessoFunc.setColor("clBtnFace");
        btnExcluirAcessoFunc.setAccess(false);
        btnExcluirAcessoFunc.setIconReverseDirection(false);
        FVBox5.addChildren(btnExcluirAcessoFunc);
        btnExcluirAcessoFunc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void pgIndicadoresChange(final Event<Object> event);

    public abstract void cbbGrupoListaChange(final Event<Object> event);

    public abstract void cbbGrupoListaClearClick(final Event<Object> event);

    public abstract void hboxAtivoClick(final Event<Object> event);

    public abstract void hboxInativoClick(final Event<Object> event);

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoIndicadoresClick(final Event<Object> event) {
        if (btnNovoIndicadores.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovoIndicadores");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirIndicadoresClick(final Event<Object> event) {
        if (btnExcluirIndicadores.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirIndicadores");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnOrderAcimaClick(final Event<Object> event) {
        if (btnOrderAcima.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnOrderAcima");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnOrderAbaixoClick(final Event<Object> event) {
        if (btnOrderAbaixo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnOrderAbaixo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoAcessoFuncClick(final Event<Object> event) {
        if (btnNovoAcessoFunc.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovoAcessoFunc");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirAcessoFuncClick(final Event<Object> event) {
        if (btnExcluirAcessoFunc.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirAcessoFunc");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}