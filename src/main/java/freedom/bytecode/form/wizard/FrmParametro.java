package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmParametro extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ParametroRNA rn = null;

    public FrmParametro() {
        try {
            rn = (freedom.bytecode.rn.ParametroRNA) getRN(freedom.bytecode.rn.wizard.ParametroRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCrmParmFluxo();
        init_tbParametroSistema();
        init_tbParametroGrupo();
        init_tbParametroSys();
        init_tbParmSys();
        init_tbParmSys2();
        init_tbParmSys3();
        init_tbEmpresas();
        init_tbParamEmpresaValues();
        init_tbEmpresasTemp();
        init_tbParmEmpresaComboBox();
        init_popupMenuGrid();
        init_menuItemSelTodas();
        init_menuItemNenhum();
        init_FHBox1();
        init_FGridPanel1();
        init_cbbParmSistema();
        init_cbbParmGrupo();
        init_edtDescricao();
        init_FHBox7();
        init_FVBox1();
        init_chkSmtParSemValorDef();
        init_FVBox4();
        init_icoAtualizar();
        init_hBoxSeparador002();
        init_hBoxSeparador003();
        init_hvBoxHelp();
        init_iconClassHelp();
        init_FHBox8();
        init_FHBox9();
        init_FVBox5();
        init_gridParametro();
        init_FVBox6();
        init_FHBox2();
        init_FVBox8();
        init_FLabel3();
        init_lblTabelaDelphi();
        init_FVBox9();
        init_FLabel5();
        init_lblCampoDelphi();
        init_FVBox7();
        init_memObs();
        init_FVBox2();
        init_FHBox10();
        init_gridValuesParm();
        init_FVBox3();
        init_chkValor();
        init_edtValorDate();
        init_edtValorDecimal();
        init_edtValorString();
        init_edtValorInteger();
        init_edtValorHora();
        init_cboValor();
        init_memoValorString();
        init_hBoxFImageParam();
        init_FImageParamEspaco1();
        init_FImageParam();
        init_FImageParamEspaco2();
        init_FGridPanel2();
        init_btnAlterar();
        init_FHBox5();
        init_hBoxSeparador001();
        init_hBoxFixarEmpresa();
        init_chkFixarEmpresa();
        init_hBoxSeparador004();
        init_vBoxImageButtonFind();
        init_btnFind();
        init_hboxSelecaoGenerica();
        init_btnSelecaoGenerica();
        init_hboxSelecaoGenericaLimpar();
        init_btnSelecaoGenericaLimpar();
        init_sc();
        init_FrmParametro();
    }

    public CRM_PARM_FLUXO tbCrmParmFluxo;

    private void init_tbCrmParmFluxo() {
        tbCrmParmFluxo = rn.tbCrmParmFluxo;
        tbCrmParmFluxo.setName("tbCrmParmFluxo");
        tbCrmParmFluxo.setMaxRowCount(0);
        tbCrmParmFluxo.setWKey("7000119;70001");
        tbCrmParmFluxo.setRatioBatchSize(20);
        getTables().put(tbCrmParmFluxo, "tbCrmParmFluxo");
        tbCrmParmFluxo.applyProperties();
    }

    public CP_PARAMETRO_SISTEMA tbParametroSistema;

    private void init_tbParametroSistema() {
        tbParametroSistema = rn.tbParametroSistema;
        tbParametroSistema.setName("tbParametroSistema");
        tbParametroSistema.setMaxRowCount(200);
        tbParametroSistema.setWKey("7000119;70002");
        tbParametroSistema.setRatioBatchSize(20);
        getTables().put(tbParametroSistema, "tbParametroSistema");
        tbParametroSistema.applyProperties();
    }

    public CP_PARAMETRO_GRUPO tbParametroGrupo;

    private void init_tbParametroGrupo() {
        tbParametroGrupo = rn.tbParametroGrupo;
        tbParametroGrupo.setName("tbParametroGrupo");
        tbParametroGrupo.setMaxRowCount(200);
        tbParametroGrupo.setWKey("7000119;70003");
        tbParametroGrupo.setRatioBatchSize(20);
        getTables().put(tbParametroGrupo, "tbParametroGrupo");
        tbParametroGrupo.applyProperties();
    }

    public CP_PARAMETRO_SYS tbParametroSys;

    private void init_tbParametroSys() {
        tbParametroSys = rn.tbParametroSys;
        tbParametroSys.setName("tbParametroSys");
        tbParametroSys.setMaxRowCount(0);
        tbParametroSys.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbParametroSysAfterScroll(event);
            processarFlow("FrmParametro", "tbParametroSys", "OnAfterScroll");
        });
        tbParametroSys.setWKey("7000119;70004");
        tbParametroSys.setRatioBatchSize(20);
        getTables().put(tbParametroSys, "tbParametroSys");
        tbParametroSys.applyProperties();
    }

    public PARM_SYS tbParmSys;

    private void init_tbParmSys() {
        tbParmSys = rn.tbParmSys;
        tbParmSys.setName("tbParmSys");
        tbParmSys.setMaxRowCount(0);
        tbParmSys.setWKey("7000119;70005");
        tbParmSys.setRatioBatchSize(20);
        getTables().put(tbParmSys, "tbParmSys");
        tbParmSys.applyProperties();
    }

    public PARM_SYS2 tbParmSys2;

    private void init_tbParmSys2() {
        tbParmSys2 = rn.tbParmSys2;
        tbParmSys2.setName("tbParmSys2");
        tbParmSys2.setMaxRowCount(0);
        tbParmSys2.setWKey("7000119;70006");
        tbParmSys2.setRatioBatchSize(20);
        getTables().put(tbParmSys2, "tbParmSys2");
        tbParmSys2.applyProperties();
    }

    public PARM_SYS3 tbParmSys3;

    private void init_tbParmSys3() {
        tbParmSys3 = rn.tbParmSys3;
        tbParmSys3.setName("tbParmSys3");
        tbParmSys3.setMaxRowCount(0);
        tbParmSys3.setWKey("7000119;700010");
        tbParmSys3.setRatioBatchSize(20);
        getTables().put(tbParmSys3, "tbParmSys3");
        tbParmSys3.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(0);
        tbEmpresas.setWKey("7000119;70007");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public PARM_SYS tbParamEmpresaValues;

    private void init_tbParamEmpresaValues() {
        tbParamEmpresaValues = rn.tbParamEmpresaValues;
        tbParamEmpresaValues.setName("tbParamEmpresaValues");
        TFTableField item0 = new TFTableField();
        item0.setName("CHECK");
        item0.setCalculated(true);
        item0.setUpdatable(false);
        item0.setPrimaryKey(false);
        item0.setFieldType("ftString");
        item0.setJSONConfigNullOnEmpty(false);
        item0.setCaption("CHECK");
        tbParamEmpresaValues.getFieldDefs().add(item0);
        TFTableField item1 = new TFTableField();
        item1.setName("COD_EMPRESA");
        item1.setCalculated(true);
        item1.setUpdatable(false);
        item1.setPrimaryKey(false);
        item1.setFieldType("ftInteger");
        item1.setJSONConfigNullOnEmpty(false);
        item1.setCaption("COD_EMPRESA");
        tbParamEmpresaValues.getFieldDefs().add(item1);
        TFTableField item2 = new TFTableField();
        item2.setName("NOME_EMPRESA");
        item2.setCalculated(true);
        item2.setUpdatable(false);
        item2.setPrimaryKey(false);
        item2.setFieldType("ftString");
        item2.setJSONConfigNullOnEmpty(false);
        item2.setCaption("NOME_EMPRESA");
        tbParamEmpresaValues.getFieldDefs().add(item2);
        TFTableField item3 = new TFTableField();
        item3.setName("VALOR");
        item3.setCalculated(true);
        item3.setUpdatable(false);
        item3.setPrimaryKey(false);
        item3.setFieldType("ftString");
        item3.setJSONConfigNullOnEmpty(false);
        item3.setCaption("VALOR");
        tbParamEmpresaValues.getFieldDefs().add(item3);
        TFTableField item4 = new TFTableField();
        item4.setName("DESCRICAO");
        item4.setCalculated(true);
        item4.setUpdatable(false);
        item4.setPrimaryKey(false);
        item4.setFieldType("ftString");
        item4.setJSONConfigNullOnEmpty(false);
        item4.setCaption("DESCRICAO");
        tbParamEmpresaValues.getFieldDefs().add(item4);
        tbParamEmpresaValues.setMaxRowCount(0);
        tbParamEmpresaValues.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbParamEmpresaValuesAfterScroll(event);
            processarFlow("FrmParametro", "tbParamEmpresaValues", "OnAfterScroll");
        });
        tbParamEmpresaValues.setWKey("7000119;70008");
        tbParamEmpresaValues.setRatioBatchSize(20);
        getTables().put(tbParamEmpresaValues, "tbParamEmpresaValues");
        tbParamEmpresaValues.applyProperties();
    }

    public EMPRESAS tbEmpresasTemp;

    private void init_tbEmpresasTemp() {
        tbEmpresasTemp = rn.tbEmpresasTemp;
        tbEmpresasTemp.setName("tbEmpresasTemp");
        tbEmpresasTemp.setMaxRowCount(0);
        tbEmpresasTemp.setWKey("7000119;70009");
        tbEmpresasTemp.setRatioBatchSize(20);
        getTables().put(tbEmpresasTemp, "tbEmpresasTemp");
        tbEmpresasTemp.applyProperties();
    }

    public SELECAO_GENERICA tbParmEmpresaComboBox;

    private void init_tbParmEmpresaComboBox() {
        tbParmEmpresaComboBox = rn.tbParmEmpresaComboBox;
        tbParmEmpresaComboBox.setName("tbParmEmpresaComboBox");
        tbParmEmpresaComboBox.setMaxRowCount(200);
        tbParmEmpresaComboBox.setWKey("7000119;41802");
        tbParmEmpresaComboBox.setRatioBatchSize(20);
        getTables().put(tbParmEmpresaComboBox, "tbParmEmpresaComboBox");
        tbParmEmpresaComboBox.applyProperties();
    }

    public TFPopupMenu popupMenuGrid = new TFPopupMenu();

    private void init_popupMenuGrid() {
        popupMenuGrid.setName("popupMenuGrid");
        FrmParametro.addChildren(popupMenuGrid);
        popupMenuGrid.applyProperties();
    }

    public TFMenuItem menuItemSelTodas = new TFMenuItem();

    private void init_menuItemSelTodas() {
        menuItemSelTodas.setName("menuItemSelTodas");
        menuItemSelTodas.setCaption("Selecionar Todas");
        menuItemSelTodas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemSelTodasClick(event);
            processarFlow("FrmParametro", "menuItemSelTodas", "OnClick");
        });
        menuItemSelTodas.setAccess(false);
        menuItemSelTodas.setCheckmark(false);
        popupMenuGrid.addChildren(menuItemSelTodas);
        menuItemSelTodas.applyProperties();
    }

    public TFMenuItem menuItemNenhum = new TFMenuItem();

    private void init_menuItemNenhum() {
        menuItemNenhum.setName("menuItemNenhum");
        menuItemNenhum.setCaption("Selecionar Nenhum");
        menuItemNenhum.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemNenhumClick(event);
            processarFlow("FrmParametro", "menuItemNenhum", "OnClick");
        });
        menuItemNenhum.setAccess(false);
        menuItemNenhum.setCheckmark(false);
        popupMenuGrid.addChildren(menuItemNenhum);
        menuItemNenhum.applyProperties();
    }

    protected TFForm FrmParametro = this;
    private void init_FrmParametro() {
        FrmParametro.setName("FrmParametro");
        FrmParametro.setCaption("Par\u00E2metro");
        FrmParametro.setClientHeight(635);
        FrmParametro.setClientWidth(1057);
        FrmParametro.setColor("clBtnFace");
        FrmParametro.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmParametro", "FrmParametro", "OnCreate");
        });
        FrmParametro.setWOrigem("EhMain");
        FrmParametro.setWKey("7000119");
        FrmParametro.setSpacing(0);
        FrmParametro.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(1057);
        FHBox1.setHeight(51);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(5);
        FHBox1.setPaddingLeft(2);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FrmParametro.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFGridPanel FGridPanel1 = new TFGridPanel();

    private void init_FGridPanel1() {
        FGridPanel1.setName("FGridPanel1");
        FGridPanel1.setLeft(0);
        FGridPanel1.setTop(0);
        FGridPanel1.setWidth(879);
        FGridPanel1.setHeight(32);
        FGridPanel1.setAlign("alClient");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(14.814188601104500000);
        FGridPanel1.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(23.475987602562270000);
        FGridPanel1.getColumnCollection().add(item1);
        TFGridPanelColumn item2 = new TFGridPanelColumn();
        item2.setValue(30.675767762047940000);
        FGridPanel1.getColumnCollection().add(item2);
        TFGridPanelColumn item3 = new TFGridPanelColumn();
        item3.setValue(31.034056034285290000);
        FGridPanel1.getColumnCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("cbbParmSistema");
        item4.setRow(0);
        FGridPanel1.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("cbbParmGrupo");
        item5.setRow(0);
        FGridPanel1.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(2);
        item6.setControl("edtDescricao");
        item6.setRow(0);
        FGridPanel1.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(3);
        item7.setControl("FHBox7");
        item7.setRow(0);
        FGridPanel1.getControlCollection().add(item7);
        TFGridPanelRow item8 = new TFGridPanelRow();
        item8.setSizeStyle("ssAuto");
        FGridPanel1.getRowCollection().add(item8);
        FGridPanel1.setFlexVflex("ftTrue");
        FGridPanel1.setFlexHflex("ftTrue");
        FGridPanel1.setAllRowFlex(false);
        FGridPanel1.setColumnTabOrder(false);
        FHBox1.addChildren(FGridPanel1);
        FGridPanel1.applyProperties();
    }

    public TFCombo cbbParmSistema = new TFCombo();

    private void init_cbbParmSistema() {
        cbbParmSistema.setName("cbbParmSistema");
        cbbParmSistema.setLeft(1);
        cbbParmSistema.setTop(1);
        cbbParmSistema.setWidth(90);
        cbbParmSistema.setHeight(21);
        cbbParmSistema.setLookupTable(tbParametroSistema);
        cbbParmSistema.setLookupKey("ID_SISTEMA");
        cbbParmSistema.setLookupDesc("DESCRICAO");
        cbbParmSistema.setFlex(true);
        cbbParmSistema.setReadOnly(false);
        cbbParmSistema.setRequired(false);
        cbbParmSistema.setPrompt("Sistema");
        cbbParmSistema.setConstraintCheckWhen("cwImmediate");
        cbbParmSistema.setConstraintCheckType("ctExpression");
        cbbParmSistema.setConstraintFocusOnError(false);
        cbbParmSistema.setConstraintEnableUI(true);
        cbbParmSistema.setConstraintEnabled(false);
        cbbParmSistema.setConstraintFormCheck(true);
        cbbParmSistema.setClearOnDelKey(true);
        cbbParmSistema.setUseClearButton(true);
        cbbParmSistema.setHideClearButtonOnNullValue(false);
        cbbParmSistema.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbParmSistemaChange(event);
            processarFlow("FrmParametro", "cbbParmSistema", "OnChange");
        });
        cbbParmSistema.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbParmSistemaClearClick(event);
            processarFlow("FrmParametro", "cbbParmSistema", "OnClearClick");
        });
        FGridPanel1.addChildren(cbbParmSistema);
        cbbParmSistema.applyProperties();
        addValidatable(cbbParmSistema);
    }

    public TFCombo cbbParmGrupo = new TFCombo();

    private void init_cbbParmGrupo() {
        cbbParmGrupo.setName("cbbParmGrupo");
        cbbParmGrupo.setLeft(130);
        cbbParmGrupo.setTop(1);
        cbbParmGrupo.setWidth(143);
        cbbParmGrupo.setHeight(21);
        cbbParmGrupo.setLookupTable(tbParametroGrupo);
        cbbParmGrupo.setLookupKey("ID_GRUPO");
        cbbParmGrupo.setLookupDesc("DESCRICAO");
        cbbParmGrupo.setFlex(true);
        cbbParmGrupo.setReadOnly(false);
        cbbParmGrupo.setRequired(false);
        cbbParmGrupo.setPrompt("Grupo");
        cbbParmGrupo.setConstraintCheckWhen("cwImmediate");
        cbbParmGrupo.setConstraintCheckType("ctExpression");
        cbbParmGrupo.setConstraintFocusOnError(false);
        cbbParmGrupo.setConstraintEnableUI(true);
        cbbParmGrupo.setConstraintEnabled(false);
        cbbParmGrupo.setConstraintFormCheck(true);
        cbbParmGrupo.setClearOnDelKey(true);
        cbbParmGrupo.setUseClearButton(true);
        cbbParmGrupo.setHideClearButtonOnNullValue(false);
        cbbParmGrupo.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbParmGrupoChange(event);
            processarFlow("FrmParametro", "cbbParmGrupo", "OnChange");
        });
        cbbParmGrupo.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbParmGrupoClearClick(event);
            processarFlow("FrmParametro", "cbbParmGrupo", "OnClearClick");
        });
        FGridPanel1.addChildren(cbbParmGrupo);
        cbbParmGrupo.applyProperties();
        addValidatable(cbbParmGrupo);
    }

    public TFString edtDescricao = new TFString();

    private void init_edtDescricao() {
        edtDescricao.setName("edtDescricao");
        edtDescricao.setLeft(335);
        edtDescricao.setTop(1);
        edtDescricao.setWidth(239);
        edtDescricao.setHeight(24);
        edtDescricao.setFlex(true);
        edtDescricao.setRequired(false);
        edtDescricao.setPrompt("Pesquisar por Descri\u00E7\u00E3o/Campo");
        edtDescricao.setConstraintCheckWhen("cwImmediate");
        edtDescricao.setConstraintCheckType("ctExpression");
        edtDescricao.setConstraintFocusOnError(false);
        edtDescricao.setConstraintEnableUI(true);
        edtDescricao.setConstraintEnabled(false);
        edtDescricao.setConstraintFormCheck(true);
        edtDescricao.setCharCase("ccNormal");
        edtDescricao.setPwd(false);
        edtDescricao.setMaxlength(0);
        edtDescricao.setFontColor("clWindowText");
        edtDescricao.setFontSize(-13);
        edtDescricao.setFontName("Tahoma");
        edtDescricao.setFontStyle("[]");
        edtDescricao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtDescricaoEnter(event);
            processarFlow("FrmParametro", "edtDescricao", "OnEnter");
        });
        edtDescricao.addEventListener("onChanging", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtDescricaoChanging(event);
            processarFlow("FrmParametro", "edtDescricao", "OnChanging");
        });
        edtDescricao.setSaveLiteralCharacter(false);
        edtDescricao.applyProperties();
        FGridPanel1.addChildren(edtDescricao);
        addValidatable(edtDescricao);
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(604);
        FHBox7.setTop(1);
        FHBox7.setWidth(269);
        FHBox7.setHeight(32);
        FHBox7.setAlign("alLeft");
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftTrue");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FGridPanel1.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(230);
        FVBox1.setHeight(28);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(3);
        FVBox1.setPaddingLeft(2);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFCheckBox chkSmtParSemValorDef = new TFCheckBox();

    private void init_chkSmtParSemValorDef() {
        chkSmtParSemValorDef.setName("chkSmtParSemValorDef");
        chkSmtParSemValorDef.setLeft(0);
        chkSmtParSemValorDef.setTop(0);
        chkSmtParSemValorDef.setWidth(212);
        chkSmtParSemValorDef.setHeight(24);
        chkSmtParSemValorDef.setAlign("alLeft");
        chkSmtParSemValorDef.setCaption("Somente Par\u00E2metro sem valor definido");
        chkSmtParSemValorDef.setFontColor("clWindowText");
        chkSmtParSemValorDef.setFontSize(-11);
        chkSmtParSemValorDef.setFontName("Tahoma");
        chkSmtParSemValorDef.setFontStyle("[]");
        chkSmtParSemValorDef.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkSmtParSemValorDefCheck(event);
            processarFlow("FrmParametro", "chkSmtParSemValorDef", "OnCheck");
        });
        chkSmtParSemValorDef.setVerticalAlignment("taVerticalCenter");
        FVBox1.addChildren(chkSmtParSemValorDef);
        chkSmtParSemValorDef.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(230);
        FVBox4.setTop(0);
        FVBox4.setWidth(28);
        FVBox4.setHeight(28);
        FVBox4.setAlign("alLeft");
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(3);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFIconClass icoAtualizar = new TFIconClass();

    private void init_icoAtualizar() {
        icoAtualizar.setName("icoAtualizar");
        icoAtualizar.setLeft(0);
        icoAtualizar.setTop(0);
        icoAtualizar.setHint("Atualizar");
        icoAtualizar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoAtualizarClick(event);
            processarFlow("FrmParametro", "icoAtualizar", "OnClick");
        });
        icoAtualizar.setIconClass("refresh");
        icoAtualizar.setSize(22);
        icoAtualizar.setColor("clBlack");
        FVBox4.addChildren(icoAtualizar);
        icoAtualizar.applyProperties();
    }

    public TFHBox hBoxSeparador002 = new TFHBox();

    private void init_hBoxSeparador002() {
        hBoxSeparador002.setName("hBoxSeparador002");
        hBoxSeparador002.setLeft(879);
        hBoxSeparador002.setTop(0);
        hBoxSeparador002.setWidth(10);
        hBoxSeparador002.setHeight(32);
        hBoxSeparador002.setAlign("alLeft");
        hBoxSeparador002.setBorderStyle("stNone");
        hBoxSeparador002.setPaddingTop(0);
        hBoxSeparador002.setPaddingLeft(0);
        hBoxSeparador002.setPaddingRight(0);
        hBoxSeparador002.setPaddingBottom(0);
        hBoxSeparador002.setVisible(false);
        hBoxSeparador002.setMarginTop(0);
        hBoxSeparador002.setMarginLeft(0);
        hBoxSeparador002.setMarginRight(0);
        hBoxSeparador002.setMarginBottom(0);
        hBoxSeparador002.setSpacing(1);
        hBoxSeparador002.setFlexVflex("ftFalse");
        hBoxSeparador002.setFlexHflex("ftFalse");
        hBoxSeparador002.setScrollable(false);
        hBoxSeparador002.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador002.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador002.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador002.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador002.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador002.setBoxShadowConfigOpacity(75);
        hBoxSeparador002.setVAlign("tvTop");
        FHBox1.addChildren(hBoxSeparador002);
        hBoxSeparador002.applyProperties();
    }

    public TFHBox hBoxSeparador003 = new TFHBox();

    private void init_hBoxSeparador003() {
        hBoxSeparador003.setName("hBoxSeparador003");
        hBoxSeparador003.setLeft(889);
        hBoxSeparador003.setTop(0);
        hBoxSeparador003.setWidth(25);
        hBoxSeparador003.setHeight(32);
        hBoxSeparador003.setAlign("alLeft");
        hBoxSeparador003.setBorderStyle("stNone");
        hBoxSeparador003.setPaddingTop(0);
        hBoxSeparador003.setPaddingLeft(0);
        hBoxSeparador003.setPaddingRight(0);
        hBoxSeparador003.setPaddingBottom(0);
        hBoxSeparador003.setMarginTop(0);
        hBoxSeparador003.setMarginLeft(0);
        hBoxSeparador003.setMarginRight(0);
        hBoxSeparador003.setMarginBottom(0);
        hBoxSeparador003.setSpacing(1);
        hBoxSeparador003.setFlexVflex("ftTrue");
        hBoxSeparador003.setFlexHflex("ftFalse");
        hBoxSeparador003.setScrollable(false);
        hBoxSeparador003.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador003.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador003.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador003.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador003.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador003.setBoxShadowConfigOpacity(75);
        hBoxSeparador003.setVAlign("tvTop");
        FHBox1.addChildren(hBoxSeparador003);
        hBoxSeparador003.applyProperties();
    }

    public TFVBox hvBoxHelp = new TFVBox();

    private void init_hvBoxHelp() {
        hvBoxHelp.setName("hvBoxHelp");
        hvBoxHelp.setLeft(914);
        hvBoxHelp.setTop(0);
        hvBoxHelp.setWidth(33);
        hvBoxHelp.setHeight(35);
        hvBoxHelp.setAlign("alRight");
        hvBoxHelp.setBorderStyle("stNone");
        hvBoxHelp.setPaddingTop(5);
        hvBoxHelp.setPaddingLeft(0);
        hvBoxHelp.setPaddingRight(0);
        hvBoxHelp.setPaddingBottom(0);
        hvBoxHelp.setMarginTop(0);
        hvBoxHelp.setMarginLeft(0);
        hvBoxHelp.setMarginRight(0);
        hvBoxHelp.setMarginBottom(0);
        hvBoxHelp.setSpacing(1);
        hvBoxHelp.setFlexVflex("ftFalse");
        hvBoxHelp.setFlexHflex("ftFalse");
        hvBoxHelp.setScrollable(false);
        hvBoxHelp.setBoxShadowConfigHorizontalLength(10);
        hvBoxHelp.setBoxShadowConfigVerticalLength(10);
        hvBoxHelp.setBoxShadowConfigBlurRadius(5);
        hvBoxHelp.setBoxShadowConfigSpreadRadius(0);
        hvBoxHelp.setBoxShadowConfigShadowColor("clBlack");
        hvBoxHelp.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(hvBoxHelp);
        hvBoxHelp.applyProperties();
    }

    public TFIconClass iconClassHelp = new TFIconClass();

    private void init_iconClassHelp() {
        iconClassHelp.setName("iconClassHelp");
        iconClassHelp.setLeft(0);
        iconClassHelp.setTop(0);
        iconClassHelp.setHint("Help");
        iconClassHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassHelpClick(event);
            processarFlow("FrmParametro", "iconClassHelp", "OnClick");
        });
        iconClassHelp.setIconClass("question-circle");
        iconClassHelp.setSize(26);
        iconClassHelp.setColor("clRed");
        hvBoxHelp.addChildren(iconClassHelp);
        iconClassHelp.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(51);
        FHBox8.setWidth(1057);
        FHBox8.setHeight(584);
        FHBox8.setAlign("alClient");
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(5);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftTrue");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FrmParametro.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(390);
        FHBox9.setHeight(590);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(3);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftTrue");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FHBox8.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(385);
        FVBox5.setHeight(584);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFGrid gridParametro = new TFGrid();

    private void init_gridParametro() {
        gridParametro.setName("gridParametro");
        gridParametro.setLeft(0);
        gridParametro.setTop(0);
        gridParametro.setWidth(384);
        gridParametro.setHeight(455);
        gridParametro.setAlign("alClient");
        gridParametro.setTable(tbParametroSys);
        gridParametro.setFlexVflex("ftTrue");
        gridParametro.setFlexHflex("ftTrue");
        gridParametro.setPagingEnabled(false);
        gridParametro.setFrozenColumns(0);
        gridParametro.setShowFooter(false);
        gridParametro.setShowHeader(true);
        gridParametro.setMultiSelection(false);
        gridParametro.setGroupingEnabled(false);
        gridParametro.setGroupingExpanded(false);
        gridParametro.setGroupingShowFooter(false);
        gridParametro.setCrosstabEnabled(false);
        gridParametro.setCrosstabGroupType("cgtConcat");
        gridParametro.setEditionEnabled(false);
        gridParametro.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("GRUPO");
        item0.setTitleCaption("Grupo");
        item0.setWidth(218);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridParametro.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(400);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridParametro.getColumns().add(item1);
        FVBox5.addChildren(gridParametro);
        gridParametro.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(456);
        FVBox6.setWidth(380);
        FVBox6.setHeight(104);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(4);
        FVBox6.setMarginTop(10);
        FVBox6.setMarginLeft(5);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftMin");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FVBox5.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(374);
        FHBox2.setHeight(44);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox6.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(0);
        FVBox8.setWidth(205);
        FVBox8.setHeight(33);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftFalse");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(32);
        FLabel3.setHeight(13);
        FLabel3.setCaption("Tabela");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FVBox8.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFLabel lblTabelaDelphi = new TFLabel();

    private void init_lblTabelaDelphi() {
        lblTabelaDelphi.setName("lblTabelaDelphi");
        lblTabelaDelphi.setLeft(0);
        lblTabelaDelphi.setTop(14);
        lblTabelaDelphi.setWidth(80);
        lblTabelaDelphi.setHeight(14);
        lblTabelaDelphi.setCaption("lblTabelaDelphi");
        lblTabelaDelphi.setFontColor("clBlue");
        lblTabelaDelphi.setFontSize(-12);
        lblTabelaDelphi.setFontName("Tahoma");
        lblTabelaDelphi.setFontStyle("[]");
        lblTabelaDelphi.setFieldName("TABELA_DELPHI");
        lblTabelaDelphi.setTable(tbParametroSys);
        lblTabelaDelphi.setVerticalAlignment("taVerticalCenter");
        lblTabelaDelphi.setWordBreak(false);
        FVBox8.addChildren(lblTabelaDelphi);
        lblTabelaDelphi.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(205);
        FVBox9.setTop(0);
        FVBox9.setWidth(164);
        FVBox9.setHeight(33);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftTrue");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(33);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Campo");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FVBox9.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFLabel lblCampoDelphi = new TFLabel();

    private void init_lblCampoDelphi() {
        lblCampoDelphi.setName("lblCampoDelphi");
        lblCampoDelphi.setLeft(0);
        lblCampoDelphi.setTop(14);
        lblCampoDelphi.setWidth(81);
        lblCampoDelphi.setHeight(14);
        lblCampoDelphi.setCaption("lblCampoDelphi");
        lblCampoDelphi.setFontColor("clBlue");
        lblCampoDelphi.setFontSize(-12);
        lblCampoDelphi.setFontName("Tahoma");
        lblCampoDelphi.setFontStyle("[]");
        lblCampoDelphi.setFieldName("CAMPO_DELPHI");
        lblCampoDelphi.setTable(tbParametroSys);
        lblCampoDelphi.setVerticalAlignment("taVerticalCenter");
        lblCampoDelphi.setWordBreak(false);
        FVBox9.addChildren(lblCampoDelphi);
        lblCampoDelphi.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(45);
        FVBox7.setWidth(374);
        FVBox7.setHeight(54);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftFalse");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FVBox6.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFMemo memObs = new TFMemo();

    private void init_memObs() {
        memObs.setName("memObs");
        memObs.setLeft(0);
        memObs.setTop(0);
        memObs.setWidth(368);
        memObs.setHeight(49);
        memObs.setCharCase("ccNormal");
        memObs.setColor("clBtnFace");
        memObs.setFontColor("clBlue");
        memObs.setFontSize(-11);
        memObs.setFontName("Tahoma");
        memObs.setFontStyle("[]");
        memObs.setMaxlength(0);
        memObs.setFieldName("OBSERVACAO");
        memObs.setTable(tbParametroSys);
        memObs.setFlexVflex("ftFalse");
        memObs.setFlexHflex("ftTrue");
        memObs.setConstraintCheckWhen("cwImmediate");
        memObs.setConstraintCheckType("ctExpression");
        memObs.setConstraintFocusOnError(false);
        memObs.setConstraintEnableUI(true);
        memObs.setConstraintEnabled(false);
        memObs.setConstraintFormCheck(true);
        memObs.setRequired(false);
        FVBox7.addChildren(memObs);
        memObs.applyProperties();
        addValidatable(memObs);
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(390);
        FVBox2.setTop(0);
        FVBox2.setWidth(665);
        FVBox2.setHeight(591);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(659);
        FHBox10.setHeight(586);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftTrue");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FVBox2.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFGrid gridValuesParm = new TFGrid();

    private void init_gridValuesParm() {
        gridValuesParm.setName("gridValuesParm");
        gridValuesParm.setLeft(0);
        gridValuesParm.setTop(0);
        gridValuesParm.setWidth(343);
        gridValuesParm.setHeight(577);
        gridValuesParm.setTable(tbParamEmpresaValues);
        gridValuesParm.setFlexVflex("ftTrue");
        gridValuesParm.setFlexHflex("ftTrue");
        gridValuesParm.setPagingEnabled(false);
        gridValuesParm.setFrozenColumns(0);
        gridValuesParm.setShowFooter(false);
        gridValuesParm.setShowHeader(true);
        gridValuesParm.setMultiSelection(false);
        gridValuesParm.setGroupingEnabled(false);
        gridValuesParm.setGroupingExpanded(false);
        gridValuesParm.setGroupingShowFooter(false);
        gridValuesParm.setCrosstabEnabled(false);
        gridValuesParm.setCrosstabGroupType("cgtConcat");
        gridValuesParm.setEditionEnabled(true);
        gridValuesParm.setContextMenu(popupMenuGrid);
        gridValuesParm.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("CHECK");
        item0.setTitleCaption("#");
        item0.setWidth(42);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taRight");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("CHECK = 'S'");
        item1.setEvalType("etExpression");
        item1.setImageId(7000105);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridValuesParmClickCheck(event);
            processarFlow("FrmParametro", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("CHECK = 'N'");
        item2.setEvalType("etExpression");
        item2.setImageId(7000106);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridValuesParmClickUnCheck(event);
            processarFlow("FrmParametro", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorSelectedValue("S");
        item0.setEditorUnselectedValue("N");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridValuesParm.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("COD_EMPRESA");
        item3.setTitleCaption("CE");
        item3.setWidth(42);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridValuesParm.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("NOME_EMPRESA");
        item4.setTitleCaption("Empresa");
        item4.setWidth(131);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridValuesParm.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DESCRICAO");
        item5.setTitleCaption("Valor");
        item5.setWidth(110);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridValuesParm.getColumns().add(item5);
        FHBox10.addChildren(gridValuesParm);
        gridValuesParm.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(343);
        FVBox3.setTop(0);
        FVBox3.setWidth(204);
        FVBox3.setHeight(554);
        FVBox3.setAlign("alTop");
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(2);
        FVBox3.setPaddingRight(2);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(4);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftMin");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFCheckBox chkValor = new TFCheckBox();

    private void init_chkValor() {
        chkValor.setName("chkValor");
        chkValor.setLeft(0);
        chkValor.setTop(0);
        chkValor.setWidth(97);
        chkValor.setHeight(17);
        chkValor.setCaption("Ativo?");
        chkValor.setFontColor("clWindowText");
        chkValor.setFontSize(-11);
        chkValor.setFontName("Tahoma");
        chkValor.setFontStyle("[]");
        chkValor.setVisible(false);
        chkValor.setTable(tbParamEmpresaValues);
        chkValor.setFieldName("VALOR");
        chkValor.setCheckedValue("S");
        chkValor.setUncheckedValue("N");
        chkValor.setVerticalAlignment("taAlignTop");
        FVBox3.addChildren(chkValor);
        chkValor.applyProperties();
    }

    public TFDate edtValorDate = new TFDate();

    private void init_edtValorDate() {
        edtValorDate.setName("edtValorDate");
        edtValorDate.setLeft(0);
        edtValorDate.setTop(18);
        edtValorDate.setWidth(190);
        edtValorDate.setHeight(24);
        edtValorDate.setTable(tbParamEmpresaValues);
        edtValorDate.setFieldName("VALOR");
        edtValorDate.setFlex(true);
        edtValorDate.setRequired(false);
        edtValorDate.setConstraintCheckWhen("cwImmediate");
        edtValorDate.setConstraintCheckType("ctExpression");
        edtValorDate.setConstraintFocusOnError(false);
        edtValorDate.setConstraintEnableUI(true);
        edtValorDate.setConstraintEnabled(false);
        edtValorDate.setConstraintFormCheck(true);
        edtValorDate.setFormat("dd/MM/yyyy");
        edtValorDate.setShowCheckBox(false);
        edtValorDate.setVisible(false);
        FVBox3.addChildren(edtValorDate);
        edtValorDate.applyProperties();
        addValidatable(edtValorDate);
    }

    public TFDecimal edtValorDecimal = new TFDecimal();

    private void init_edtValorDecimal() {
        edtValorDecimal.setName("edtValorDecimal");
        edtValorDecimal.setLeft(0);
        edtValorDecimal.setTop(43);
        edtValorDecimal.setWidth(190);
        edtValorDecimal.setHeight(24);
        edtValorDecimal.setTable(tbParamEmpresaValues);
        edtValorDecimal.setFieldName("VALOR");
        edtValorDecimal.setFlex(true);
        edtValorDecimal.setRequired(false);
        edtValorDecimal.setConstraintCheckWhen("cwImmediate");
        edtValorDecimal.setConstraintCheckType("ctExpression");
        edtValorDecimal.setConstraintFocusOnError(false);
        edtValorDecimal.setConstraintEnableUI(true);
        edtValorDecimal.setConstraintEnabled(false);
        edtValorDecimal.setConstraintFormCheck(true);
        edtValorDecimal.setMaxlength(0);
        edtValorDecimal.setPrecision(0);
        edtValorDecimal.setVisible(false);
        edtValorDecimal.setFontColor("clWindowText");
        edtValorDecimal.setFontSize(-13);
        edtValorDecimal.setFontName("Tahoma");
        edtValorDecimal.setFontStyle("[]");
        edtValorDecimal.setAlignment("taRightJustify");
        FVBox3.addChildren(edtValorDecimal);
        edtValorDecimal.applyProperties();
        addValidatable(edtValorDecimal);
    }

    public TFString edtValorString = new TFString();

    private void init_edtValorString() {
        edtValorString.setName("edtValorString");
        edtValorString.setLeft(0);
        edtValorString.setTop(68);
        edtValorString.setWidth(190);
        edtValorString.setHeight(24);
        edtValorString.setTable(tbParamEmpresaValues);
        edtValorString.setFieldName("VALOR");
        edtValorString.setFlex(true);
        edtValorString.setRequired(false);
        edtValorString.setConstraintCheckWhen("cwImmediate");
        edtValorString.setConstraintCheckType("ctExpression");
        edtValorString.setConstraintFocusOnError(false);
        edtValorString.setConstraintEnableUI(true);
        edtValorString.setConstraintEnabled(false);
        edtValorString.setConstraintFormCheck(true);
        edtValorString.setCharCase("ccNormal");
        edtValorString.setPwd(false);
        edtValorString.setMaxlength(0);
        edtValorString.setVisible(false);
        edtValorString.setFontColor("clWindowText");
        edtValorString.setFontSize(-13);
        edtValorString.setFontName("Tahoma");
        edtValorString.setFontStyle("[]");
        edtValorString.setSaveLiteralCharacter(true);
        edtValorString.applyProperties();
        FVBox3.addChildren(edtValorString);
        addValidatable(edtValorString);
    }

    public TFInteger edtValorInteger = new TFInteger();

    private void init_edtValorInteger() {
        edtValorInteger.setName("edtValorInteger");
        edtValorInteger.setLeft(0);
        edtValorInteger.setTop(93);
        edtValorInteger.setWidth(190);
        edtValorInteger.setHeight(24);
        edtValorInteger.setTable(tbParamEmpresaValues);
        edtValorInteger.setFieldName("VALOR");
        edtValorInteger.setFlex(true);
        edtValorInteger.setRequired(false);
        edtValorInteger.setConstraintCheckWhen("cwImmediate");
        edtValorInteger.setConstraintCheckType("ctExpression");
        edtValorInteger.setConstraintFocusOnError(false);
        edtValorInteger.setConstraintEnableUI(true);
        edtValorInteger.setConstraintEnabled(false);
        edtValorInteger.setConstraintFormCheck(true);
        edtValorInteger.setMaxlength(0);
        edtValorInteger.setVisible(false);
        edtValorInteger.setFontColor("clWindowText");
        edtValorInteger.setFontSize(-13);
        edtValorInteger.setFontName("Tahoma");
        edtValorInteger.setFontStyle("[]");
        edtValorInteger.setAlignment("taRightJustify");
        FVBox3.addChildren(edtValorInteger);
        edtValorInteger.applyProperties();
        addValidatable(edtValorInteger);
    }

    public TFTime edtValorHora = new TFTime();

    private void init_edtValorHora() {
        edtValorHora.setName("edtValorHora");
        edtValorHora.setLeft(0);
        edtValorHora.setTop(118);
        edtValorHora.setWidth(100);
        edtValorHora.setHeight(24);
        edtValorHora.setTable(tbParamEmpresaValues);
        edtValorHora.setFieldName("VALOR");
        edtValorHora.setFlex(false);
        edtValorHora.setRequired(false);
        edtValorHora.setConstraintCheckWhen("cwImmediate");
        edtValorHora.setConstraintCheckType("ctExpression");
        edtValorHora.setConstraintFocusOnError(false);
        edtValorHora.setConstraintEnableUI(true);
        edtValorHora.setConstraintEnabled(false);
        edtValorHora.setConstraintFormCheck(true);
        edtValorHora.setFormat("HH:mm");
        edtValorHora.setVisible(false);
        edtValorHora.setFontColor("clWindowText");
        edtValorHora.setFontSize(-13);
        edtValorHora.setFontName("Tahoma");
        edtValorHora.setFontStyle("[]");
        FVBox3.addChildren(edtValorHora);
        edtValorHora.applyProperties();
        addValidatable(edtValorHora);
    }

    public TFCombo cboValor = new TFCombo();

    private void init_cboValor() {
        cboValor.setName("cboValor");
        cboValor.setLeft(0);
        cboValor.setTop(143);
        cboValor.setWidth(190);
        cboValor.setHeight(21);
        cboValor.setTable(tbParamEmpresaValues);
        cboValor.setFieldName("VALOR");
        cboValor.setFlex(true);
        cboValor.setReadOnly(true);
        cboValor.setRequired(false);
        cboValor.setPrompt("Selecione");
        cboValor.setConstraintCheckWhen("cwImmediate");
        cboValor.setConstraintCheckType("ctExpression");
        cboValor.setConstraintFocusOnError(false);
        cboValor.setConstraintEnableUI(true);
        cboValor.setConstraintEnabled(false);
        cboValor.setConstraintFormCheck(true);
        cboValor.setClearOnDelKey(false);
        cboValor.setUseClearButton(true);
        cboValor.setHideClearButtonOnNullValue(false);
        cboValor.setVisible(false);
        cboValor.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboValorClearClick(event);
            processarFlow("FrmParametro", "cboValor", "OnClearClick");
        });
        FVBox3.addChildren(cboValor);
        cboValor.applyProperties();
        addValidatable(cboValor);
    }

    public TFMemo memoValorString = new TFMemo();

    private void init_memoValorString() {
        memoValorString.setName("memoValorString");
        memoValorString.setLeft(0);
        memoValorString.setTop(165);
        memoValorString.setWidth(190);
        memoValorString.setHeight(100);
        memoValorString.setCharCase("ccNormal");
        memoValorString.setFontColor("clWindowText");
        memoValorString.setFontSize(-11);
        memoValorString.setFontName("Tahoma");
        memoValorString.setFontStyle("[]");
        memoValorString.setMaxlength(0);
        memoValorString.setVisible(false);
        memoValorString.setFieldName("VALOR");
        memoValorString.setTable(tbParamEmpresaValues);
        memoValorString.setFlexVflex("ftTrue");
        memoValorString.setFlexHflex("ftTrue");
        memoValorString.setConstraintCheckWhen("cwImmediate");
        memoValorString.setConstraintCheckType("ctExpression");
        memoValorString.setConstraintFocusOnError(false);
        memoValorString.setConstraintEnableUI(true);
        memoValorString.setConstraintEnabled(false);
        memoValorString.setConstraintFormCheck(true);
        memoValorString.setRequired(false);
        FVBox3.addChildren(memoValorString);
        memoValorString.applyProperties();
        addValidatable(memoValorString);
    }

    public TFHBox hBoxFImageParam = new TFHBox();

    private void init_hBoxFImageParam() {
        hBoxFImageParam.setName("hBoxFImageParam");
        hBoxFImageParam.setLeft(0);
        hBoxFImageParam.setTop(266);
        hBoxFImageParam.setWidth(157);
        hBoxFImageParam.setHeight(97);
        hBoxFImageParam.setBorderStyle("stNone");
        hBoxFImageParam.setPaddingTop(0);
        hBoxFImageParam.setPaddingLeft(0);
        hBoxFImageParam.setPaddingRight(0);
        hBoxFImageParam.setPaddingBottom(0);
        hBoxFImageParam.setMarginTop(0);
        hBoxFImageParam.setMarginLeft(0);
        hBoxFImageParam.setMarginRight(0);
        hBoxFImageParam.setMarginBottom(0);
        hBoxFImageParam.setSpacing(1);
        hBoxFImageParam.setFlexVflex("ftTrue");
        hBoxFImageParam.setFlexHflex("ftTrue");
        hBoxFImageParam.setScrollable(false);
        hBoxFImageParam.setBoxShadowConfigHorizontalLength(10);
        hBoxFImageParam.setBoxShadowConfigVerticalLength(10);
        hBoxFImageParam.setBoxShadowConfigBlurRadius(5);
        hBoxFImageParam.setBoxShadowConfigSpreadRadius(0);
        hBoxFImageParam.setBoxShadowConfigShadowColor("clBlack");
        hBoxFImageParam.setBoxShadowConfigOpacity(75);
        hBoxFImageParam.setVAlign("tvTop");
        FVBox3.addChildren(hBoxFImageParam);
        hBoxFImageParam.applyProperties();
    }

    public TFVBox FImageParamEspaco1 = new TFVBox();

    private void init_FImageParamEspaco1() {
        FImageParamEspaco1.setName("FImageParamEspaco1");
        FImageParamEspaco1.setLeft(0);
        FImageParamEspaco1.setTop(0);
        FImageParamEspaco1.setWidth(17);
        FImageParamEspaco1.setHeight(41);
        FImageParamEspaco1.setBorderStyle("stNone");
        FImageParamEspaco1.setPaddingTop(0);
        FImageParamEspaco1.setPaddingLeft(0);
        FImageParamEspaco1.setPaddingRight(0);
        FImageParamEspaco1.setPaddingBottom(0);
        FImageParamEspaco1.setMarginTop(0);
        FImageParamEspaco1.setMarginLeft(0);
        FImageParamEspaco1.setMarginRight(0);
        FImageParamEspaco1.setMarginBottom(0);
        FImageParamEspaco1.setSpacing(1);
        FImageParamEspaco1.setFlexVflex("ftFalse");
        FImageParamEspaco1.setFlexHflex("ftTrue");
        FImageParamEspaco1.setScrollable(false);
        FImageParamEspaco1.setBoxShadowConfigHorizontalLength(10);
        FImageParamEspaco1.setBoxShadowConfigVerticalLength(10);
        FImageParamEspaco1.setBoxShadowConfigBlurRadius(5);
        FImageParamEspaco1.setBoxShadowConfigSpreadRadius(0);
        FImageParamEspaco1.setBoxShadowConfigShadowColor("clBlack");
        FImageParamEspaco1.setBoxShadowConfigOpacity(75);
        hBoxFImageParam.addChildren(FImageParamEspaco1);
        FImageParamEspaco1.applyProperties();
    }

    public TFImage FImageParam = new TFImage();

    private void init_FImageParam() {
        FImageParam.setName("FImageParam");
        FImageParam.setLeft(17);
        FImageParam.setTop(0);
        FImageParam.setWidth(108);
        FImageParam.setHeight(92);
        FImageParam.setTable(tbParamEmpresaValues);
        FImageParam.setFieldName("VALOR");
        FImageParam.setBoxSize(0);
        FImageParam.setGrayScaleOnDisable(false);
        FImageParam.setFlexVflex("ftFalse");
        FImageParam.setFlexHflex("ftFalse");
        FImageParam.setImageId(0);
        hBoxFImageParam.addChildren(FImageParam);
        FImageParam.applyProperties();
    }

    public TFVBox FImageParamEspaco2 = new TFVBox();

    private void init_FImageParamEspaco2() {
        FImageParamEspaco2.setName("FImageParamEspaco2");
        FImageParamEspaco2.setLeft(125);
        FImageParamEspaco2.setTop(0);
        FImageParamEspaco2.setWidth(17);
        FImageParamEspaco2.setHeight(41);
        FImageParamEspaco2.setBorderStyle("stNone");
        FImageParamEspaco2.setPaddingTop(0);
        FImageParamEspaco2.setPaddingLeft(0);
        FImageParamEspaco2.setPaddingRight(0);
        FImageParamEspaco2.setPaddingBottom(0);
        FImageParamEspaco2.setMarginTop(0);
        FImageParamEspaco2.setMarginLeft(0);
        FImageParamEspaco2.setMarginRight(0);
        FImageParamEspaco2.setMarginBottom(0);
        FImageParamEspaco2.setSpacing(1);
        FImageParamEspaco2.setFlexVflex("ftFalse");
        FImageParamEspaco2.setFlexHflex("ftTrue");
        FImageParamEspaco2.setScrollable(false);
        FImageParamEspaco2.setBoxShadowConfigHorizontalLength(10);
        FImageParamEspaco2.setBoxShadowConfigVerticalLength(10);
        FImageParamEspaco2.setBoxShadowConfigBlurRadius(5);
        FImageParamEspaco2.setBoxShadowConfigSpreadRadius(0);
        FImageParamEspaco2.setBoxShadowConfigShadowColor("clBlack");
        FImageParamEspaco2.setBoxShadowConfigOpacity(75);
        hBoxFImageParam.addChildren(FImageParamEspaco2);
        FImageParamEspaco2.applyProperties();
    }

    public TFGridPanel FGridPanel2 = new TFGridPanel();

    private void init_FGridPanel2() {
        FGridPanel2.setName("FGridPanel2");
        FGridPanel2.setLeft(0);
        FGridPanel2.setTop(364);
        FGridPanel2.setWidth(146);
        FGridPanel2.setHeight(44);
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAuto");
        item0.setValue(50.000000000000000000);
        FGridPanel2.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("btnAlterar");
        item2.setRow(0);
        FGridPanel2.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("FHBox5");
        item3.setRow(0);
        FGridPanel2.getControlCollection().add(item3);
        TFGridPanelRow item4 = new TFGridPanelRow();
        item4.setSizeStyle("ssAuto");
        FGridPanel2.getRowCollection().add(item4);
        FGridPanel2.setFlexVflex("ftFalse");
        FGridPanel2.setFlexHflex("ftFalse");
        FGridPanel2.setAllRowFlex(false);
        FGridPanel2.setColumnTabOrder(false);
        FVBox3.addChildren(FGridPanel2);
        FGridPanel2.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setUploadMime("image/*");
        btnAlterar.setLeft(1);
        btnAlterar.setTop(1);
        btnAlterar.setWidth(76);
        btnAlterar.setHeight(32);
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmParametro", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(0);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);        FGridPanel2.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(77);
        FHBox5.setTop(1);
        FHBox5.setWidth(68);
        FHBox5.setHeight(32);
        FHBox5.setAlign("alClient");
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(4);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftTrue");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FGridPanel2.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox hBoxSeparador001 = new TFHBox();

    private void init_hBoxSeparador001() {
        hBoxSeparador001.setName("hBoxSeparador001");
        hBoxSeparador001.setLeft(0);
        hBoxSeparador001.setTop(409);
        hBoxSeparador001.setWidth(190);
        hBoxSeparador001.setHeight(96);
        hBoxSeparador001.setBorderStyle("stNone");
        hBoxSeparador001.setPaddingTop(6);
        hBoxSeparador001.setPaddingLeft(10);
        hBoxSeparador001.setPaddingRight(0);
        hBoxSeparador001.setPaddingBottom(0);
        hBoxSeparador001.setMarginTop(0);
        hBoxSeparador001.setMarginLeft(0);
        hBoxSeparador001.setMarginRight(0);
        hBoxSeparador001.setMarginBottom(0);
        hBoxSeparador001.setSpacing(1);
        hBoxSeparador001.setFlexVflex("ftTrue");
        hBoxSeparador001.setFlexHflex("ftFalse");
        hBoxSeparador001.setScrollable(false);
        hBoxSeparador001.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador001.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador001.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador001.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador001.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador001.setBoxShadowConfigOpacity(75);
        hBoxSeparador001.setVAlign("tvTop");
        FVBox3.addChildren(hBoxSeparador001);
        hBoxSeparador001.applyProperties();
    }

    public TFHBox hBoxFixarEmpresa = new TFHBox();

    private void init_hBoxFixarEmpresa() {
        hBoxFixarEmpresa.setName("hBoxFixarEmpresa");
        hBoxFixarEmpresa.setLeft(0);
        hBoxFixarEmpresa.setTop(506);
        hBoxFixarEmpresa.setWidth(190);
        hBoxFixarEmpresa.setHeight(32);
        hBoxFixarEmpresa.setAlign("alLeft");
        hBoxFixarEmpresa.setBorderStyle("stNone");
        hBoxFixarEmpresa.setPaddingTop(6);
        hBoxFixarEmpresa.setPaddingLeft(0);
        hBoxFixarEmpresa.setPaddingRight(0);
        hBoxFixarEmpresa.setPaddingBottom(0);
        hBoxFixarEmpresa.setMarginTop(0);
        hBoxFixarEmpresa.setMarginLeft(0);
        hBoxFixarEmpresa.setMarginRight(0);
        hBoxFixarEmpresa.setMarginBottom(0);
        hBoxFixarEmpresa.setSpacing(0);
        hBoxFixarEmpresa.setFlexVflex("ftFalse");
        hBoxFixarEmpresa.setFlexHflex("ftFalse");
        hBoxFixarEmpresa.setScrollable(false);
        hBoxFixarEmpresa.setBoxShadowConfigHorizontalLength(10);
        hBoxFixarEmpresa.setBoxShadowConfigVerticalLength(10);
        hBoxFixarEmpresa.setBoxShadowConfigBlurRadius(5);
        hBoxFixarEmpresa.setBoxShadowConfigSpreadRadius(0);
        hBoxFixarEmpresa.setBoxShadowConfigShadowColor("clBlack");
        hBoxFixarEmpresa.setBoxShadowConfigOpacity(75);
        hBoxFixarEmpresa.setVAlign("tvTop");
        FVBox3.addChildren(hBoxFixarEmpresa);
        hBoxFixarEmpresa.applyProperties();
    }

    public TFCheckBox chkFixarEmpresa = new TFCheckBox();

    private void init_chkFixarEmpresa() {
        chkFixarEmpresa.setName("chkFixarEmpresa");
        chkFixarEmpresa.setLeft(0);
        chkFixarEmpresa.setTop(0);
        chkFixarEmpresa.setWidth(184);
        chkFixarEmpresa.setHeight(24);
        chkFixarEmpresa.setAlign("alLeft");
        chkFixarEmpresa.setCaption(" Fixar Empresas Selecionadas?");
        chkFixarEmpresa.setFontColor("clWindowText");
        chkFixarEmpresa.setFontSize(-11);
        chkFixarEmpresa.setFontName("Tahoma");
        chkFixarEmpresa.setFontStyle("[fsUnderline]");
        chkFixarEmpresa.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkFixarEmpresaCheck(event);
            processarFlow("FrmParametro", "chkFixarEmpresa", "OnCheck");
        });
        chkFixarEmpresa.setVerticalAlignment("taVerticalCenter");
        hBoxFixarEmpresa.addChildren(chkFixarEmpresa);
        chkFixarEmpresa.applyProperties();
    }

    public TFHBox hBoxSeparador004 = new TFHBox();

    private void init_hBoxSeparador004() {
        hBoxSeparador004.setName("hBoxSeparador004");
        hBoxSeparador004.setLeft(0);
        hBoxSeparador004.setTop(539);
        hBoxSeparador004.setWidth(190);
        hBoxSeparador004.setHeight(8);
        hBoxSeparador004.setAlign("alBottom");
        hBoxSeparador004.setBorderStyle("stNone");
        hBoxSeparador004.setPaddingTop(6);
        hBoxSeparador004.setPaddingLeft(10);
        hBoxSeparador004.setPaddingRight(0);
        hBoxSeparador004.setPaddingBottom(0);
        hBoxSeparador004.setMarginTop(0);
        hBoxSeparador004.setMarginLeft(0);
        hBoxSeparador004.setMarginRight(0);
        hBoxSeparador004.setMarginBottom(0);
        hBoxSeparador004.setSpacing(1);
        hBoxSeparador004.setFlexVflex("ftFalse");
        hBoxSeparador004.setFlexHflex("ftFalse");
        hBoxSeparador004.setScrollable(false);
        hBoxSeparador004.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador004.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador004.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador004.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador004.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador004.setBoxShadowConfigOpacity(75);
        hBoxSeparador004.setVAlign("tvTop");
        FVBox3.addChildren(hBoxSeparador004);
        hBoxSeparador004.applyProperties();
    }

    public TFVBox vBoxImageButtonFind = new TFVBox();

    private void init_vBoxImageButtonFind() {
        vBoxImageButtonFind.setName("vBoxImageButtonFind");
        vBoxImageButtonFind.setLeft(547);
        vBoxImageButtonFind.setTop(0);
        vBoxImageButtonFind.setWidth(32);
        vBoxImageButtonFind.setHeight(32);
        vBoxImageButtonFind.setBorderStyle("stNone");
        vBoxImageButtonFind.setPaddingTop(1);
        vBoxImageButtonFind.setPaddingLeft(0);
        vBoxImageButtonFind.setPaddingRight(0);
        vBoxImageButtonFind.setPaddingBottom(0);
        vBoxImageButtonFind.setVisible(false);
        vBoxImageButtonFind.setMarginTop(0);
        vBoxImageButtonFind.setMarginLeft(0);
        vBoxImageButtonFind.setMarginRight(0);
        vBoxImageButtonFind.setMarginBottom(0);
        vBoxImageButtonFind.setSpacing(1);
        vBoxImageButtonFind.setFlexVflex("ftFalse");
        vBoxImageButtonFind.setFlexHflex("ftFalse");
        vBoxImageButtonFind.setScrollable(false);
        vBoxImageButtonFind.setBoxShadowConfigHorizontalLength(10);
        vBoxImageButtonFind.setBoxShadowConfigVerticalLength(10);
        vBoxImageButtonFind.setBoxShadowConfigBlurRadius(5);
        vBoxImageButtonFind.setBoxShadowConfigSpreadRadius(0);
        vBoxImageButtonFind.setBoxShadowConfigShadowColor("clBlack");
        vBoxImageButtonFind.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(vBoxImageButtonFind);
        vBoxImageButtonFind.applyProperties();
    }

    public TFIconClass btnFind = new TFIconClass();

    private void init_btnFind() {
        btnFind.setName("btnFind");
        btnFind.setLeft(0);
        btnFind.setTop(0);
        btnFind.setVisible(false);
        btnFind.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFindClick(event);
            processarFlow("FrmParametro", "btnFind", "OnClick");
        });
        btnFind.setIconClass("search");
        btnFind.setSize(18);
        btnFind.setColor("clBlack");
        vBoxImageButtonFind.addChildren(btnFind);
        btnFind.applyProperties();
    }

    public TFVBox hboxSelecaoGenerica = new TFVBox();

    private void init_hboxSelecaoGenerica() {
        hboxSelecaoGenerica.setName("hboxSelecaoGenerica");
        hboxSelecaoGenerica.setLeft(579);
        hboxSelecaoGenerica.setTop(0);
        hboxSelecaoGenerica.setWidth(32);
        hboxSelecaoGenerica.setHeight(32);
        hboxSelecaoGenerica.setBorderStyle("stNone");
        hboxSelecaoGenerica.setPaddingTop(5);
        hboxSelecaoGenerica.setPaddingLeft(5);
        hboxSelecaoGenerica.setPaddingRight(5);
        hboxSelecaoGenerica.setPaddingBottom(5);
        hboxSelecaoGenerica.setMarginTop(0);
        hboxSelecaoGenerica.setMarginLeft(0);
        hboxSelecaoGenerica.setMarginRight(0);
        hboxSelecaoGenerica.setMarginBottom(0);
        hboxSelecaoGenerica.setSpacing(1);
        hboxSelecaoGenerica.setFlexVflex("ftFalse");
        hboxSelecaoGenerica.setFlexHflex("ftFalse");
        hboxSelecaoGenerica.setScrollable(false);
        hboxSelecaoGenerica.setBoxShadowConfigHorizontalLength(10);
        hboxSelecaoGenerica.setBoxShadowConfigVerticalLength(10);
        hboxSelecaoGenerica.setBoxShadowConfigBlurRadius(5);
        hboxSelecaoGenerica.setBoxShadowConfigSpreadRadius(0);
        hboxSelecaoGenerica.setBoxShadowConfigShadowColor("clBlack");
        hboxSelecaoGenerica.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(hboxSelecaoGenerica);
        hboxSelecaoGenerica.applyProperties();
    }

    public TFIconClass btnSelecaoGenerica = new TFIconClass();

    private void init_btnSelecaoGenerica() {
        btnSelecaoGenerica.setName("btnSelecaoGenerica");
        btnSelecaoGenerica.setLeft(0);
        btnSelecaoGenerica.setTop(0);
        btnSelecaoGenerica.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSelecaoGenericaClick(event);
            processarFlow("FrmParametro", "btnSelecaoGenerica", "OnClick");
        });
        btnSelecaoGenerica.setIconClass("search");
        btnSelecaoGenerica.setSize(18);
        btnSelecaoGenerica.setColor("clBlack");
        hboxSelecaoGenerica.addChildren(btnSelecaoGenerica);
        btnSelecaoGenerica.applyProperties();
    }

    public TFVBox hboxSelecaoGenericaLimpar = new TFVBox();

    private void init_hboxSelecaoGenericaLimpar() {
        hboxSelecaoGenericaLimpar.setName("hboxSelecaoGenericaLimpar");
        hboxSelecaoGenericaLimpar.setLeft(611);
        hboxSelecaoGenericaLimpar.setTop(0);
        hboxSelecaoGenericaLimpar.setWidth(32);
        hboxSelecaoGenericaLimpar.setHeight(32);
        hboxSelecaoGenericaLimpar.setBorderStyle("stNone");
        hboxSelecaoGenericaLimpar.setPaddingTop(5);
        hboxSelecaoGenericaLimpar.setPaddingLeft(5);
        hboxSelecaoGenericaLimpar.setPaddingRight(5);
        hboxSelecaoGenericaLimpar.setPaddingBottom(5);
        hboxSelecaoGenericaLimpar.setMarginTop(0);
        hboxSelecaoGenericaLimpar.setMarginLeft(0);
        hboxSelecaoGenericaLimpar.setMarginRight(0);
        hboxSelecaoGenericaLimpar.setMarginBottom(0);
        hboxSelecaoGenericaLimpar.setSpacing(1);
        hboxSelecaoGenericaLimpar.setFlexVflex("ftFalse");
        hboxSelecaoGenericaLimpar.setFlexHflex("ftFalse");
        hboxSelecaoGenericaLimpar.setScrollable(false);
        hboxSelecaoGenericaLimpar.setBoxShadowConfigHorizontalLength(10);
        hboxSelecaoGenericaLimpar.setBoxShadowConfigVerticalLength(10);
        hboxSelecaoGenericaLimpar.setBoxShadowConfigBlurRadius(5);
        hboxSelecaoGenericaLimpar.setBoxShadowConfigSpreadRadius(0);
        hboxSelecaoGenericaLimpar.setBoxShadowConfigShadowColor("clBlack");
        hboxSelecaoGenericaLimpar.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(hboxSelecaoGenericaLimpar);
        hboxSelecaoGenericaLimpar.applyProperties();
    }

    public TFIconClass btnSelecaoGenericaLimpar = new TFIconClass();

    private void init_btnSelecaoGenericaLimpar() {
        btnSelecaoGenericaLimpar.setName("btnSelecaoGenericaLimpar");
        btnSelecaoGenericaLimpar.setLeft(0);
        btnSelecaoGenericaLimpar.setTop(0);
        btnSelecaoGenericaLimpar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSelecaoGenericaLimparClick(event);
            processarFlow("FrmParametro", "btnSelecaoGenericaLimpar", "OnClick");
        });
        btnSelecaoGenericaLimpar.setIconClass("trash");
        btnSelecaoGenericaLimpar.setSize(18);
        btnSelecaoGenericaLimpar.setColor("clBlack");
        hboxSelecaoGenericaLimpar.addChildren(btnSelecaoGenericaLimpar);
        btnSelecaoGenericaLimpar.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbCrmParmFluxo);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbParmSys);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbParmSys2);
        sc.getTables().add(item2);
        TFSchemaItem item3 = new TFSchemaItem();
        item3.setTable(tbParmSys3);
        sc.getTables().add(item3);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void cbbParmSistemaChange(final Event<Object> event);

    public abstract void cbbParmSistemaClearClick(final Event<Object> event);

    public abstract void cbbParmGrupoChange(final Event<Object> event);

    public abstract void cbbParmGrupoClearClick(final Event<Object> event);

    public abstract void edtDescricaoEnter(final Event<Object> event);

    public abstract void edtDescricaoChanging(final Event<Object> event);

    public abstract void chkSmtParSemValorDefCheck(final Event<Object> event);

    public abstract void icoAtualizarClick(final Event<Object> event);

    public abstract void iconClassHelpClick(final Event<Object> event);

    public abstract void gridValuesParmClickCheck(final Event<Object> event);

    public abstract void gridValuesParmClickUnCheck(final Event<Object> event);

    public abstract void cboValorClearClick(final Event<Object> event);

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void chkFixarEmpresaCheck(final Event<Object> event);

    public abstract void btnFindClick(final Event<Object> event);

    public abstract void btnSelecaoGenericaClick(final Event<Object> event);

    public abstract void btnSelecaoGenericaLimparClick(final Event<Object> event);

    public abstract void menuItemSelTodasClick(final Event<Object> event);

    public abstract void menuItemNenhumClick(final Event<Object> event);

    public abstract void tbParametroSysAfterScroll(final Event<Object> event);

    public abstract void tbParamEmpresaValuesAfterScroll(final Event<Object> event);

}