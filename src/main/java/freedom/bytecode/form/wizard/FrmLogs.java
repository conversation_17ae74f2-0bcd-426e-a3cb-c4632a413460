package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmLogs extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.LogsRNA rn = null;

    public FrmLogs() {
        try {
            rn = (freedom.bytecode.rn.LogsRNA) getRN(freedom.bytecode.rn.wizard.LogsRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCadRapClienteLogs();
        init_tbGridCadRapCliLogs();
        init_vBoxLogsClienteContainer();
        init_hBoxLogsTop();
        init_btnVoltar();
        init_pgControlLogsCliente();
        init_tabLogs();
        init_hBoxLogsContainer();
        init_vBoxLogsEdits();
        init_hBoxLogsCadastro();
        init_vBoxLogsCadastro();
        init_lblLogsCadastro();
        init_edtLogsCadastro();
        init_hBoxLogsValidade();
        init_vBoxLogsValidade();
        init_lblLogsValidade();
        init_edtLogsValidade();
        init_hBoxLogsDataConsultaCrivo();
        init_vBoxLogsDataConsultaCrivo();
        init_lblLogsDataConsultaCrivo();
        init_edtLogsDataConsultaCrivo();
        init_hBoxLogsValidadeConsultaCrivo();
        init_vBoxLogsValidadeConsultaCrivo();
        init_lblLogsValidadeConsultaCrivo();
        init_edtLogsValidadeConsultaCrivo();
        init_hBoxLogsRespCadastro();
        init_vBoxLogsRespCadastro();
        init_lblLogsRespCadastro();
        init_edtLogsRespCadastro();
        init_hBoxLogsRespUltimaAlteracao();
        init_vBoxLogsRespUltimaAlteracao();
        init_lblLogsRespUltimaAlteracao();
        init_edtLogsRespUltimaAlteracao();
        init_hBoxLogsUltimaAlteracao();
        init_vBoxLogsUltimaAlteracao();
        init_lblLogsUltimaAlteracao();
        init_edtLogsUltimaAlteracao();
        init_vBoxGrid();
        init_gridLogs();
        init_tabOutros();
        init_FrmLogs();
    }

    public CAD_RAP_CLIENTE_LOGS tbCadRapClienteLogs;

    private void init_tbCadRapClienteLogs() {
        tbCadRapClienteLogs = rn.tbCadRapClienteLogs;
        tbCadRapClienteLogs.setName("tbCadRapClienteLogs");
        tbCadRapClienteLogs.setMaxRowCount(200);
        tbCadRapClienteLogs.setWKey("13801;13801");
        tbCadRapClienteLogs.setRatioBatchSize(20);
        getTables().put(tbCadRapClienteLogs, "tbCadRapClienteLogs");
        tbCadRapClienteLogs.applyProperties();
    }

    public CAD_RAP_CLIENTE_LOGS tbGridCadRapCliLogs;

    private void init_tbGridCadRapCliLogs() {
        tbGridCadRapCliLogs = rn.tbGridCadRapCliLogs;
        tbGridCadRapCliLogs.setName("tbGridCadRapCliLogs");
        tbGridCadRapCliLogs.setMaxRowCount(200);
        tbGridCadRapCliLogs.setWKey("13801;13802");
        tbGridCadRapCliLogs.setRatioBatchSize(20);
        getTables().put(tbGridCadRapCliLogs, "tbGridCadRapCliLogs");
        tbGridCadRapCliLogs.applyProperties();
    }

    protected TFForm FrmLogs = this;
    private void init_FrmLogs() {
        FrmLogs.setName("FrmLogs");
        FrmLogs.setHint("Logs");
        FrmLogs.setCaption("Logs Cliente");
        FrmLogs.setClientHeight(565);
        FrmLogs.setClientWidth(625);
        FrmLogs.setColor("clBtnFace");
        FrmLogs.setWKey("13801");
        FrmLogs.setSpacing(0);
        FrmLogs.applyProperties();
    }

    public TFVBox vBoxLogsClienteContainer = new TFVBox();

    private void init_vBoxLogsClienteContainer() {
        vBoxLogsClienteContainer.setName("vBoxLogsClienteContainer");
        vBoxLogsClienteContainer.setLeft(0);
        vBoxLogsClienteContainer.setTop(0);
        vBoxLogsClienteContainer.setWidth(625);
        vBoxLogsClienteContainer.setHeight(565);
        vBoxLogsClienteContainer.setAlign("alClient");
        vBoxLogsClienteContainer.setBorderStyle("stNone");
        vBoxLogsClienteContainer.setPaddingTop(0);
        vBoxLogsClienteContainer.setPaddingLeft(0);
        vBoxLogsClienteContainer.setPaddingRight(0);
        vBoxLogsClienteContainer.setPaddingBottom(0);
        vBoxLogsClienteContainer.setMarginTop(0);
        vBoxLogsClienteContainer.setMarginLeft(0);
        vBoxLogsClienteContainer.setMarginRight(0);
        vBoxLogsClienteContainer.setMarginBottom(0);
        vBoxLogsClienteContainer.setSpacing(1);
        vBoxLogsClienteContainer.setFlexVflex("ftFalse");
        vBoxLogsClienteContainer.setFlexHflex("ftFalse");
        vBoxLogsClienteContainer.setScrollable(false);
        vBoxLogsClienteContainer.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsClienteContainer.setBoxShadowConfigVerticalLength(10);
        vBoxLogsClienteContainer.setBoxShadowConfigBlurRadius(5);
        vBoxLogsClienteContainer.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsClienteContainer.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsClienteContainer.setBoxShadowConfigOpacity(75);
        FrmLogs.addChildren(vBoxLogsClienteContainer);
        vBoxLogsClienteContainer.applyProperties();
    }

    public TFHBox hBoxLogsTop = new TFHBox();

    private void init_hBoxLogsTop() {
        hBoxLogsTop.setName("hBoxLogsTop");
        hBoxLogsTop.setLeft(0);
        hBoxLogsTop.setTop(0);
        hBoxLogsTop.setWidth(545);
        hBoxLogsTop.setHeight(61);
        hBoxLogsTop.setAlign("alTop");
        hBoxLogsTop.setBorderStyle("stNone");
        hBoxLogsTop.setPaddingTop(0);
        hBoxLogsTop.setPaddingLeft(0);
        hBoxLogsTop.setPaddingRight(0);
        hBoxLogsTop.setPaddingBottom(0);
        hBoxLogsTop.setMarginTop(5);
        hBoxLogsTop.setMarginLeft(10);
        hBoxLogsTop.setMarginRight(0);
        hBoxLogsTop.setMarginBottom(0);
        hBoxLogsTop.setSpacing(1);
        hBoxLogsTop.setFlexVflex("ftFalse");
        hBoxLogsTop.setFlexHflex("ftFalse");
        hBoxLogsTop.setScrollable(false);
        hBoxLogsTop.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsTop.setBoxShadowConfigVerticalLength(10);
        hBoxLogsTop.setBoxShadowConfigBlurRadius(5);
        hBoxLogsTop.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsTop.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsTop.setBoxShadowConfigOpacity(75);
        hBoxLogsTop.setVAlign("tvTop");
        vBoxLogsClienteContainer.addChildren(hBoxLogsTop);
        hBoxLogsTop.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(61);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmLogs", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxLogsTop.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFPageControl pgControlLogsCliente = new TFPageControl();

    private void init_pgControlLogsCliente() {
        pgControlLogsCliente.setName("pgControlLogsCliente");
        pgControlLogsCliente.setLeft(0);
        pgControlLogsCliente.setTop(62);
        pgControlLogsCliente.setWidth(617);
        pgControlLogsCliente.setHeight(489);
        pgControlLogsCliente.setTabPosition("tpTop");
        pgControlLogsCliente.setFlexVflex("ftFalse");
        pgControlLogsCliente.setFlexHflex("ftFalse");
        pgControlLogsCliente.setRenderStyle("rsTabbed");
        pgControlLogsCliente.applyProperties();
        vBoxLogsClienteContainer.addChildren(pgControlLogsCliente);
    }

    public TFTabsheet tabLogs = new TFTabsheet();

    private void init_tabLogs() {
        tabLogs.setName("tabLogs");
        tabLogs.setCaption("Logs");
        tabLogs.setFontColor("clWindowText");
        tabLogs.setFontSize(-11);
        tabLogs.setFontName("Tahoma");
        tabLogs.setFontStyle("[]");
        tabLogs.setClosable(false);
        pgControlLogsCliente.addChildren(tabLogs);
        tabLogs.applyProperties();
    }

    public TFHBox hBoxLogsContainer = new TFHBox();

    private void init_hBoxLogsContainer() {
        hBoxLogsContainer.setName("hBoxLogsContainer");
        hBoxLogsContainer.setLeft(0);
        hBoxLogsContainer.setTop(60);
        hBoxLogsContainer.setWidth(606);
        hBoxLogsContainer.setHeight(401);
        hBoxLogsContainer.setBorderStyle("stNone");
        hBoxLogsContainer.setPaddingTop(0);
        hBoxLogsContainer.setPaddingLeft(0);
        hBoxLogsContainer.setPaddingRight(0);
        hBoxLogsContainer.setPaddingBottom(0);
        hBoxLogsContainer.setMarginTop(0);
        hBoxLogsContainer.setMarginLeft(0);
        hBoxLogsContainer.setMarginRight(0);
        hBoxLogsContainer.setMarginBottom(0);
        hBoxLogsContainer.setSpacing(1);
        hBoxLogsContainer.setFlexVflex("ftMin");
        hBoxLogsContainer.setFlexHflex("ftTrue");
        hBoxLogsContainer.setScrollable(false);
        hBoxLogsContainer.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsContainer.setBoxShadowConfigVerticalLength(10);
        hBoxLogsContainer.setBoxShadowConfigBlurRadius(5);
        hBoxLogsContainer.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsContainer.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsContainer.setBoxShadowConfigOpacity(75);
        hBoxLogsContainer.setVAlign("tvTop");
        tabLogs.addChildren(hBoxLogsContainer);
        hBoxLogsContainer.applyProperties();
    }

    public TFVBox vBoxLogsEdits = new TFVBox();

    private void init_vBoxLogsEdits() {
        vBoxLogsEdits.setName("vBoxLogsEdits");
        vBoxLogsEdits.setLeft(0);
        vBoxLogsEdits.setTop(0);
        vBoxLogsEdits.setWidth(210);
        vBoxLogsEdits.setHeight(391);
        vBoxLogsEdits.setBorderStyle("stNone");
        vBoxLogsEdits.setPaddingTop(0);
        vBoxLogsEdits.setPaddingLeft(0);
        vBoxLogsEdits.setPaddingRight(0);
        vBoxLogsEdits.setPaddingBottom(0);
        vBoxLogsEdits.setMarginTop(0);
        vBoxLogsEdits.setMarginLeft(10);
        vBoxLogsEdits.setMarginRight(0);
        vBoxLogsEdits.setMarginBottom(0);
        vBoxLogsEdits.setSpacing(1);
        vBoxLogsEdits.setFlexVflex("ftMin");
        vBoxLogsEdits.setFlexHflex("ftFalse");
        vBoxLogsEdits.setScrollable(false);
        vBoxLogsEdits.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsEdits.setBoxShadowConfigVerticalLength(10);
        vBoxLogsEdits.setBoxShadowConfigBlurRadius(5);
        vBoxLogsEdits.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsEdits.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsEdits.setBoxShadowConfigOpacity(75);
        hBoxLogsContainer.addChildren(vBoxLogsEdits);
        vBoxLogsEdits.applyProperties();
    }

    public TFHBox hBoxLogsCadastro = new TFHBox();

    private void init_hBoxLogsCadastro() {
        hBoxLogsCadastro.setName("hBoxLogsCadastro");
        hBoxLogsCadastro.setLeft(0);
        hBoxLogsCadastro.setTop(0);
        hBoxLogsCadastro.setWidth(140);
        hBoxLogsCadastro.setHeight(53);
        hBoxLogsCadastro.setBorderStyle("stNone");
        hBoxLogsCadastro.setPaddingTop(0);
        hBoxLogsCadastro.setPaddingLeft(0);
        hBoxLogsCadastro.setPaddingRight(0);
        hBoxLogsCadastro.setPaddingBottom(0);
        hBoxLogsCadastro.setMarginTop(0);
        hBoxLogsCadastro.setMarginLeft(0);
        hBoxLogsCadastro.setMarginRight(0);
        hBoxLogsCadastro.setMarginBottom(0);
        hBoxLogsCadastro.setSpacing(1);
        hBoxLogsCadastro.setFlexVflex("ftFalse");
        hBoxLogsCadastro.setFlexHflex("ftFalse");
        hBoxLogsCadastro.setScrollable(false);
        hBoxLogsCadastro.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsCadastro.setBoxShadowConfigVerticalLength(10);
        hBoxLogsCadastro.setBoxShadowConfigBlurRadius(5);
        hBoxLogsCadastro.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsCadastro.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsCadastro.setBoxShadowConfigOpacity(75);
        hBoxLogsCadastro.setVAlign("tvTop");
        vBoxLogsEdits.addChildren(hBoxLogsCadastro);
        hBoxLogsCadastro.applyProperties();
    }

    public TFVBox vBoxLogsCadastro = new TFVBox();

    private void init_vBoxLogsCadastro() {
        vBoxLogsCadastro.setName("vBoxLogsCadastro");
        vBoxLogsCadastro.setLeft(0);
        vBoxLogsCadastro.setTop(0);
        vBoxLogsCadastro.setWidth(135);
        vBoxLogsCadastro.setHeight(49);
        vBoxLogsCadastro.setBorderStyle("stNone");
        vBoxLogsCadastro.setPaddingTop(0);
        vBoxLogsCadastro.setPaddingLeft(0);
        vBoxLogsCadastro.setPaddingRight(0);
        vBoxLogsCadastro.setPaddingBottom(0);
        vBoxLogsCadastro.setMarginTop(0);
        vBoxLogsCadastro.setMarginLeft(0);
        vBoxLogsCadastro.setMarginRight(0);
        vBoxLogsCadastro.setMarginBottom(0);
        vBoxLogsCadastro.setSpacing(1);
        vBoxLogsCadastro.setFlexVflex("ftFalse");
        vBoxLogsCadastro.setFlexHflex("ftFalse");
        vBoxLogsCadastro.setScrollable(false);
        vBoxLogsCadastro.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsCadastro.setBoxShadowConfigVerticalLength(10);
        vBoxLogsCadastro.setBoxShadowConfigBlurRadius(5);
        vBoxLogsCadastro.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsCadastro.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsCadastro.setBoxShadowConfigOpacity(75);
        hBoxLogsCadastro.addChildren(vBoxLogsCadastro);
        vBoxLogsCadastro.applyProperties();
    }

    public TFLabel lblLogsCadastro = new TFLabel();

    private void init_lblLogsCadastro() {
        lblLogsCadastro.setName("lblLogsCadastro");
        lblLogsCadastro.setLeft(0);
        lblLogsCadastro.setTop(0);
        lblLogsCadastro.setWidth(44);
        lblLogsCadastro.setHeight(13);
        lblLogsCadastro.setCaption("Cadastro");
        lblLogsCadastro.setFontColor("clWindowText");
        lblLogsCadastro.setFontSize(-11);
        lblLogsCadastro.setFontName("Tahoma");
        lblLogsCadastro.setFontStyle("[]");
        lblLogsCadastro.setVerticalAlignment("taVerticalCenter");
        lblLogsCadastro.setWordBreak(false);
        vBoxLogsCadastro.addChildren(lblLogsCadastro);
        lblLogsCadastro.applyProperties();
    }

    public TFDate edtLogsCadastro = new TFDate();

    private void init_edtLogsCadastro() {
        edtLogsCadastro.setName("edtLogsCadastro");
        edtLogsCadastro.setLeft(0);
        edtLogsCadastro.setTop(14);
        edtLogsCadastro.setWidth(130);
        edtLogsCadastro.setHeight(24);
        edtLogsCadastro.setTable(tbCadRapClienteLogs);
        edtLogsCadastro.setFieldName("CADASTRO");
        edtLogsCadastro.setFlex(true);
        edtLogsCadastro.setRequired(false);
        edtLogsCadastro.setConstraintCheckWhen("cwImmediate");
        edtLogsCadastro.setConstraintCheckType("ctExpression");
        edtLogsCadastro.setConstraintFocusOnError(false);
        edtLogsCadastro.setConstraintEnableUI(true);
        edtLogsCadastro.setConstraintEnabled(false);
        edtLogsCadastro.setConstraintFormCheck(true);
        edtLogsCadastro.setFormat("dd/MM/yyyy");
        edtLogsCadastro.setShowCheckBox(false);
        edtLogsCadastro.setEnabled(false);
        vBoxLogsCadastro.addChildren(edtLogsCadastro);
        edtLogsCadastro.applyProperties();
        addValidatable(edtLogsCadastro);
    }

    public TFHBox hBoxLogsValidade = new TFHBox();

    private void init_hBoxLogsValidade() {
        hBoxLogsValidade.setName("hBoxLogsValidade");
        hBoxLogsValidade.setLeft(0);
        hBoxLogsValidade.setTop(54);
        hBoxLogsValidade.setWidth(140);
        hBoxLogsValidade.setHeight(53);
        hBoxLogsValidade.setBorderStyle("stNone");
        hBoxLogsValidade.setPaddingTop(0);
        hBoxLogsValidade.setPaddingLeft(0);
        hBoxLogsValidade.setPaddingRight(0);
        hBoxLogsValidade.setPaddingBottom(0);
        hBoxLogsValidade.setMarginTop(0);
        hBoxLogsValidade.setMarginLeft(0);
        hBoxLogsValidade.setMarginRight(0);
        hBoxLogsValidade.setMarginBottom(0);
        hBoxLogsValidade.setSpacing(1);
        hBoxLogsValidade.setFlexVflex("ftFalse");
        hBoxLogsValidade.setFlexHflex("ftFalse");
        hBoxLogsValidade.setScrollable(false);
        hBoxLogsValidade.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsValidade.setBoxShadowConfigVerticalLength(10);
        hBoxLogsValidade.setBoxShadowConfigBlurRadius(5);
        hBoxLogsValidade.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsValidade.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsValidade.setBoxShadowConfigOpacity(75);
        hBoxLogsValidade.setVAlign("tvTop");
        vBoxLogsEdits.addChildren(hBoxLogsValidade);
        hBoxLogsValidade.applyProperties();
    }

    public TFVBox vBoxLogsValidade = new TFVBox();

    private void init_vBoxLogsValidade() {
        vBoxLogsValidade.setName("vBoxLogsValidade");
        vBoxLogsValidade.setLeft(0);
        vBoxLogsValidade.setTop(0);
        vBoxLogsValidade.setWidth(135);
        vBoxLogsValidade.setHeight(49);
        vBoxLogsValidade.setBorderStyle("stNone");
        vBoxLogsValidade.setPaddingTop(0);
        vBoxLogsValidade.setPaddingLeft(0);
        vBoxLogsValidade.setPaddingRight(0);
        vBoxLogsValidade.setPaddingBottom(0);
        vBoxLogsValidade.setMarginTop(0);
        vBoxLogsValidade.setMarginLeft(0);
        vBoxLogsValidade.setMarginRight(0);
        vBoxLogsValidade.setMarginBottom(0);
        vBoxLogsValidade.setSpacing(1);
        vBoxLogsValidade.setFlexVflex("ftFalse");
        vBoxLogsValidade.setFlexHflex("ftFalse");
        vBoxLogsValidade.setScrollable(false);
        vBoxLogsValidade.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsValidade.setBoxShadowConfigVerticalLength(10);
        vBoxLogsValidade.setBoxShadowConfigBlurRadius(5);
        vBoxLogsValidade.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsValidade.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsValidade.setBoxShadowConfigOpacity(75);
        hBoxLogsValidade.addChildren(vBoxLogsValidade);
        vBoxLogsValidade.applyProperties();
    }

    public TFLabel lblLogsValidade = new TFLabel();

    private void init_lblLogsValidade() {
        lblLogsValidade.setName("lblLogsValidade");
        lblLogsValidade.setLeft(0);
        lblLogsValidade.setTop(0);
        lblLogsValidade.setWidth(40);
        lblLogsValidade.setHeight(13);
        lblLogsValidade.setCaption("Validade");
        lblLogsValidade.setFontColor("clWindowText");
        lblLogsValidade.setFontSize(-11);
        lblLogsValidade.setFontName("Tahoma");
        lblLogsValidade.setFontStyle("[]");
        lblLogsValidade.setVerticalAlignment("taVerticalCenter");
        lblLogsValidade.setWordBreak(false);
        vBoxLogsValidade.addChildren(lblLogsValidade);
        lblLogsValidade.applyProperties();
    }

    public TFDate edtLogsValidade = new TFDate();

    private void init_edtLogsValidade() {
        edtLogsValidade.setName("edtLogsValidade");
        edtLogsValidade.setLeft(0);
        edtLogsValidade.setTop(14);
        edtLogsValidade.setWidth(130);
        edtLogsValidade.setHeight(24);
        edtLogsValidade.setTable(tbCadRapClienteLogs);
        edtLogsValidade.setFieldName("VALIDADE");
        edtLogsValidade.setFlex(true);
        edtLogsValidade.setRequired(false);
        edtLogsValidade.setConstraintCheckWhen("cwImmediate");
        edtLogsValidade.setConstraintCheckType("ctExpression");
        edtLogsValidade.setConstraintFocusOnError(false);
        edtLogsValidade.setConstraintEnableUI(true);
        edtLogsValidade.setConstraintEnabled(false);
        edtLogsValidade.setConstraintFormCheck(true);
        edtLogsValidade.setFormat("dd/MM/yyyy");
        edtLogsValidade.setShowCheckBox(false);
        edtLogsValidade.setEnabled(false);
        vBoxLogsValidade.addChildren(edtLogsValidade);
        edtLogsValidade.applyProperties();
        addValidatable(edtLogsValidade);
    }

    public TFHBox hBoxLogsDataConsultaCrivo = new TFHBox();

    private void init_hBoxLogsDataConsultaCrivo() {
        hBoxLogsDataConsultaCrivo.setName("hBoxLogsDataConsultaCrivo");
        hBoxLogsDataConsultaCrivo.setLeft(0);
        hBoxLogsDataConsultaCrivo.setTop(108);
        hBoxLogsDataConsultaCrivo.setWidth(140);
        hBoxLogsDataConsultaCrivo.setHeight(53);
        hBoxLogsDataConsultaCrivo.setBorderStyle("stNone");
        hBoxLogsDataConsultaCrivo.setPaddingTop(0);
        hBoxLogsDataConsultaCrivo.setPaddingLeft(0);
        hBoxLogsDataConsultaCrivo.setPaddingRight(0);
        hBoxLogsDataConsultaCrivo.setPaddingBottom(0);
        hBoxLogsDataConsultaCrivo.setVisible(false);
        hBoxLogsDataConsultaCrivo.setMarginTop(0);
        hBoxLogsDataConsultaCrivo.setMarginLeft(0);
        hBoxLogsDataConsultaCrivo.setMarginRight(0);
        hBoxLogsDataConsultaCrivo.setMarginBottom(0);
        hBoxLogsDataConsultaCrivo.setSpacing(1);
        hBoxLogsDataConsultaCrivo.setFlexVflex("ftFalse");
        hBoxLogsDataConsultaCrivo.setFlexHflex("ftFalse");
        hBoxLogsDataConsultaCrivo.setScrollable(false);
        hBoxLogsDataConsultaCrivo.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsDataConsultaCrivo.setBoxShadowConfigVerticalLength(10);
        hBoxLogsDataConsultaCrivo.setBoxShadowConfigBlurRadius(5);
        hBoxLogsDataConsultaCrivo.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsDataConsultaCrivo.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsDataConsultaCrivo.setBoxShadowConfigOpacity(75);
        hBoxLogsDataConsultaCrivo.setVAlign("tvTop");
        vBoxLogsEdits.addChildren(hBoxLogsDataConsultaCrivo);
        hBoxLogsDataConsultaCrivo.applyProperties();
    }

    public TFVBox vBoxLogsDataConsultaCrivo = new TFVBox();

    private void init_vBoxLogsDataConsultaCrivo() {
        vBoxLogsDataConsultaCrivo.setName("vBoxLogsDataConsultaCrivo");
        vBoxLogsDataConsultaCrivo.setLeft(0);
        vBoxLogsDataConsultaCrivo.setTop(0);
        vBoxLogsDataConsultaCrivo.setWidth(135);
        vBoxLogsDataConsultaCrivo.setHeight(49);
        vBoxLogsDataConsultaCrivo.setBorderStyle("stNone");
        vBoxLogsDataConsultaCrivo.setPaddingTop(0);
        vBoxLogsDataConsultaCrivo.setPaddingLeft(0);
        vBoxLogsDataConsultaCrivo.setPaddingRight(0);
        vBoxLogsDataConsultaCrivo.setPaddingBottom(0);
        vBoxLogsDataConsultaCrivo.setMarginTop(0);
        vBoxLogsDataConsultaCrivo.setMarginLeft(0);
        vBoxLogsDataConsultaCrivo.setMarginRight(0);
        vBoxLogsDataConsultaCrivo.setMarginBottom(0);
        vBoxLogsDataConsultaCrivo.setSpacing(1);
        vBoxLogsDataConsultaCrivo.setFlexVflex("ftFalse");
        vBoxLogsDataConsultaCrivo.setFlexHflex("ftFalse");
        vBoxLogsDataConsultaCrivo.setScrollable(false);
        vBoxLogsDataConsultaCrivo.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsDataConsultaCrivo.setBoxShadowConfigVerticalLength(10);
        vBoxLogsDataConsultaCrivo.setBoxShadowConfigBlurRadius(5);
        vBoxLogsDataConsultaCrivo.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsDataConsultaCrivo.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsDataConsultaCrivo.setBoxShadowConfigOpacity(75);
        hBoxLogsDataConsultaCrivo.addChildren(vBoxLogsDataConsultaCrivo);
        vBoxLogsDataConsultaCrivo.applyProperties();
    }

    public TFLabel lblLogsDataConsultaCrivo = new TFLabel();

    private void init_lblLogsDataConsultaCrivo() {
        lblLogsDataConsultaCrivo.setName("lblLogsDataConsultaCrivo");
        lblLogsDataConsultaCrivo.setLeft(0);
        lblLogsDataConsultaCrivo.setTop(0);
        lblLogsDataConsultaCrivo.setWidth(99);
        lblLogsDataConsultaCrivo.setHeight(13);
        lblLogsDataConsultaCrivo.setCaption("Data Consulta Crivo ");
        lblLogsDataConsultaCrivo.setFontColor("clWindowText");
        lblLogsDataConsultaCrivo.setFontSize(-11);
        lblLogsDataConsultaCrivo.setFontName("Tahoma");
        lblLogsDataConsultaCrivo.setFontStyle("[]");
        lblLogsDataConsultaCrivo.setVerticalAlignment("taVerticalCenter");
        lblLogsDataConsultaCrivo.setWordBreak(false);
        vBoxLogsDataConsultaCrivo.addChildren(lblLogsDataConsultaCrivo);
        lblLogsDataConsultaCrivo.applyProperties();
    }

    public TFDate edtLogsDataConsultaCrivo = new TFDate();

    private void init_edtLogsDataConsultaCrivo() {
        edtLogsDataConsultaCrivo.setName("edtLogsDataConsultaCrivo");
        edtLogsDataConsultaCrivo.setLeft(0);
        edtLogsDataConsultaCrivo.setTop(14);
        edtLogsDataConsultaCrivo.setWidth(130);
        edtLogsDataConsultaCrivo.setHeight(24);
        edtLogsDataConsultaCrivo.setTable(tbCadRapClienteLogs);
        edtLogsDataConsultaCrivo.setFieldName("DT_CONSULTA_CRIVO");
        edtLogsDataConsultaCrivo.setFlex(true);
        edtLogsDataConsultaCrivo.setRequired(false);
        edtLogsDataConsultaCrivo.setConstraintCheckWhen("cwImmediate");
        edtLogsDataConsultaCrivo.setConstraintCheckType("ctExpression");
        edtLogsDataConsultaCrivo.setConstraintFocusOnError(false);
        edtLogsDataConsultaCrivo.setConstraintEnableUI(true);
        edtLogsDataConsultaCrivo.setConstraintEnabled(false);
        edtLogsDataConsultaCrivo.setConstraintFormCheck(true);
        edtLogsDataConsultaCrivo.setFormat("dd/MM/yyyy");
        edtLogsDataConsultaCrivo.setShowCheckBox(false);
        edtLogsDataConsultaCrivo.setEnabled(false);
        vBoxLogsDataConsultaCrivo.addChildren(edtLogsDataConsultaCrivo);
        edtLogsDataConsultaCrivo.applyProperties();
        addValidatable(edtLogsDataConsultaCrivo);
    }

    public TFHBox hBoxLogsValidadeConsultaCrivo = new TFHBox();

    private void init_hBoxLogsValidadeConsultaCrivo() {
        hBoxLogsValidadeConsultaCrivo.setName("hBoxLogsValidadeConsultaCrivo");
        hBoxLogsValidadeConsultaCrivo.setLeft(0);
        hBoxLogsValidadeConsultaCrivo.setTop(162);
        hBoxLogsValidadeConsultaCrivo.setWidth(140);
        hBoxLogsValidadeConsultaCrivo.setHeight(53);
        hBoxLogsValidadeConsultaCrivo.setBorderStyle("stNone");
        hBoxLogsValidadeConsultaCrivo.setPaddingTop(0);
        hBoxLogsValidadeConsultaCrivo.setPaddingLeft(0);
        hBoxLogsValidadeConsultaCrivo.setPaddingRight(0);
        hBoxLogsValidadeConsultaCrivo.setPaddingBottom(0);
        hBoxLogsValidadeConsultaCrivo.setVisible(false);
        hBoxLogsValidadeConsultaCrivo.setMarginTop(0);
        hBoxLogsValidadeConsultaCrivo.setMarginLeft(0);
        hBoxLogsValidadeConsultaCrivo.setMarginRight(0);
        hBoxLogsValidadeConsultaCrivo.setMarginBottom(0);
        hBoxLogsValidadeConsultaCrivo.setSpacing(1);
        hBoxLogsValidadeConsultaCrivo.setFlexVflex("ftFalse");
        hBoxLogsValidadeConsultaCrivo.setFlexHflex("ftFalse");
        hBoxLogsValidadeConsultaCrivo.setScrollable(false);
        hBoxLogsValidadeConsultaCrivo.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsValidadeConsultaCrivo.setBoxShadowConfigVerticalLength(10);
        hBoxLogsValidadeConsultaCrivo.setBoxShadowConfigBlurRadius(5);
        hBoxLogsValidadeConsultaCrivo.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsValidadeConsultaCrivo.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsValidadeConsultaCrivo.setBoxShadowConfigOpacity(75);
        hBoxLogsValidadeConsultaCrivo.setVAlign("tvTop");
        vBoxLogsEdits.addChildren(hBoxLogsValidadeConsultaCrivo);
        hBoxLogsValidadeConsultaCrivo.applyProperties();
    }

    public TFVBox vBoxLogsValidadeConsultaCrivo = new TFVBox();

    private void init_vBoxLogsValidadeConsultaCrivo() {
        vBoxLogsValidadeConsultaCrivo.setName("vBoxLogsValidadeConsultaCrivo");
        vBoxLogsValidadeConsultaCrivo.setLeft(0);
        vBoxLogsValidadeConsultaCrivo.setTop(0);
        vBoxLogsValidadeConsultaCrivo.setWidth(135);
        vBoxLogsValidadeConsultaCrivo.setHeight(49);
        vBoxLogsValidadeConsultaCrivo.setBorderStyle("stNone");
        vBoxLogsValidadeConsultaCrivo.setPaddingTop(0);
        vBoxLogsValidadeConsultaCrivo.setPaddingLeft(0);
        vBoxLogsValidadeConsultaCrivo.setPaddingRight(0);
        vBoxLogsValidadeConsultaCrivo.setPaddingBottom(0);
        vBoxLogsValidadeConsultaCrivo.setMarginTop(0);
        vBoxLogsValidadeConsultaCrivo.setMarginLeft(0);
        vBoxLogsValidadeConsultaCrivo.setMarginRight(0);
        vBoxLogsValidadeConsultaCrivo.setMarginBottom(0);
        vBoxLogsValidadeConsultaCrivo.setSpacing(1);
        vBoxLogsValidadeConsultaCrivo.setFlexVflex("ftFalse");
        vBoxLogsValidadeConsultaCrivo.setFlexHflex("ftFalse");
        vBoxLogsValidadeConsultaCrivo.setScrollable(false);
        vBoxLogsValidadeConsultaCrivo.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsValidadeConsultaCrivo.setBoxShadowConfigVerticalLength(10);
        vBoxLogsValidadeConsultaCrivo.setBoxShadowConfigBlurRadius(5);
        vBoxLogsValidadeConsultaCrivo.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsValidadeConsultaCrivo.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsValidadeConsultaCrivo.setBoxShadowConfigOpacity(75);
        hBoxLogsValidadeConsultaCrivo.addChildren(vBoxLogsValidadeConsultaCrivo);
        vBoxLogsValidadeConsultaCrivo.applyProperties();
    }

    public TFLabel lblLogsValidadeConsultaCrivo = new TFLabel();

    private void init_lblLogsValidadeConsultaCrivo() {
        lblLogsValidadeConsultaCrivo.setName("lblLogsValidadeConsultaCrivo");
        lblLogsValidadeConsultaCrivo.setLeft(0);
        lblLogsValidadeConsultaCrivo.setTop(0);
        lblLogsValidadeConsultaCrivo.setWidth(116);
        lblLogsValidadeConsultaCrivo.setHeight(13);
        lblLogsValidadeConsultaCrivo.setCaption("Validade Consulta Crivo ");
        lblLogsValidadeConsultaCrivo.setFontColor("clWindowText");
        lblLogsValidadeConsultaCrivo.setFontSize(-11);
        lblLogsValidadeConsultaCrivo.setFontName("Tahoma");
        lblLogsValidadeConsultaCrivo.setFontStyle("[]");
        lblLogsValidadeConsultaCrivo.setVerticalAlignment("taVerticalCenter");
        lblLogsValidadeConsultaCrivo.setWordBreak(false);
        vBoxLogsValidadeConsultaCrivo.addChildren(lblLogsValidadeConsultaCrivo);
        lblLogsValidadeConsultaCrivo.applyProperties();
    }

    public TFDate edtLogsValidadeConsultaCrivo = new TFDate();

    private void init_edtLogsValidadeConsultaCrivo() {
        edtLogsValidadeConsultaCrivo.setName("edtLogsValidadeConsultaCrivo");
        edtLogsValidadeConsultaCrivo.setLeft(0);
        edtLogsValidadeConsultaCrivo.setTop(14);
        edtLogsValidadeConsultaCrivo.setWidth(130);
        edtLogsValidadeConsultaCrivo.setHeight(24);
        edtLogsValidadeConsultaCrivo.setTable(tbCadRapClienteLogs);
        edtLogsValidadeConsultaCrivo.setFieldName("VALIDADE_CONSULTA_CRIVO");
        edtLogsValidadeConsultaCrivo.setFlex(true);
        edtLogsValidadeConsultaCrivo.setRequired(false);
        edtLogsValidadeConsultaCrivo.setConstraintCheckWhen("cwImmediate");
        edtLogsValidadeConsultaCrivo.setConstraintCheckType("ctExpression");
        edtLogsValidadeConsultaCrivo.setConstraintFocusOnError(false);
        edtLogsValidadeConsultaCrivo.setConstraintEnableUI(true);
        edtLogsValidadeConsultaCrivo.setConstraintEnabled(false);
        edtLogsValidadeConsultaCrivo.setConstraintFormCheck(true);
        edtLogsValidadeConsultaCrivo.setFormat("dd/MM/yyyy");
        edtLogsValidadeConsultaCrivo.setShowCheckBox(false);
        edtLogsValidadeConsultaCrivo.setEnabled(false);
        vBoxLogsValidadeConsultaCrivo.addChildren(edtLogsValidadeConsultaCrivo);
        edtLogsValidadeConsultaCrivo.applyProperties();
        addValidatable(edtLogsValidadeConsultaCrivo);
    }

    public TFHBox hBoxLogsRespCadastro = new TFHBox();

    private void init_hBoxLogsRespCadastro() {
        hBoxLogsRespCadastro.setName("hBoxLogsRespCadastro");
        hBoxLogsRespCadastro.setLeft(0);
        hBoxLogsRespCadastro.setTop(216);
        hBoxLogsRespCadastro.setWidth(205);
        hBoxLogsRespCadastro.setHeight(53);
        hBoxLogsRespCadastro.setBorderStyle("stNone");
        hBoxLogsRespCadastro.setPaddingTop(0);
        hBoxLogsRespCadastro.setPaddingLeft(0);
        hBoxLogsRespCadastro.setPaddingRight(0);
        hBoxLogsRespCadastro.setPaddingBottom(0);
        hBoxLogsRespCadastro.setMarginTop(0);
        hBoxLogsRespCadastro.setMarginLeft(0);
        hBoxLogsRespCadastro.setMarginRight(0);
        hBoxLogsRespCadastro.setMarginBottom(0);
        hBoxLogsRespCadastro.setSpacing(1);
        hBoxLogsRespCadastro.setFlexVflex("ftFalse");
        hBoxLogsRespCadastro.setFlexHflex("ftTrue");
        hBoxLogsRespCadastro.setScrollable(false);
        hBoxLogsRespCadastro.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsRespCadastro.setBoxShadowConfigVerticalLength(10);
        hBoxLogsRespCadastro.setBoxShadowConfigBlurRadius(5);
        hBoxLogsRespCadastro.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsRespCadastro.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsRespCadastro.setBoxShadowConfigOpacity(75);
        hBoxLogsRespCadastro.setVAlign("tvTop");
        vBoxLogsEdits.addChildren(hBoxLogsRespCadastro);
        hBoxLogsRespCadastro.applyProperties();
    }

    public TFVBox vBoxLogsRespCadastro = new TFVBox();

    private void init_vBoxLogsRespCadastro() {
        vBoxLogsRespCadastro.setName("vBoxLogsRespCadastro");
        vBoxLogsRespCadastro.setLeft(0);
        vBoxLogsRespCadastro.setTop(0);
        vBoxLogsRespCadastro.setWidth(200);
        vBoxLogsRespCadastro.setHeight(49);
        vBoxLogsRespCadastro.setBorderStyle("stNone");
        vBoxLogsRespCadastro.setPaddingTop(0);
        vBoxLogsRespCadastro.setPaddingLeft(0);
        vBoxLogsRespCadastro.setPaddingRight(0);
        vBoxLogsRespCadastro.setPaddingBottom(0);
        vBoxLogsRespCadastro.setMarginTop(0);
        vBoxLogsRespCadastro.setMarginLeft(0);
        vBoxLogsRespCadastro.setMarginRight(0);
        vBoxLogsRespCadastro.setMarginBottom(0);
        vBoxLogsRespCadastro.setSpacing(1);
        vBoxLogsRespCadastro.setFlexVflex("ftFalse");
        vBoxLogsRespCadastro.setFlexHflex("ftFalse");
        vBoxLogsRespCadastro.setScrollable(false);
        vBoxLogsRespCadastro.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsRespCadastro.setBoxShadowConfigVerticalLength(10);
        vBoxLogsRespCadastro.setBoxShadowConfigBlurRadius(5);
        vBoxLogsRespCadastro.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsRespCadastro.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsRespCadastro.setBoxShadowConfigOpacity(75);
        hBoxLogsRespCadastro.addChildren(vBoxLogsRespCadastro);
        vBoxLogsRespCadastro.applyProperties();
    }

    public TFLabel lblLogsRespCadastro = new TFLabel();

    private void init_lblLogsRespCadastro() {
        lblLogsRespCadastro.setName("lblLogsRespCadastro");
        lblLogsRespCadastro.setLeft(0);
        lblLogsRespCadastro.setTop(0);
        lblLogsRespCadastro.setWidth(111);
        lblLogsRespCadastro.setHeight(13);
        lblLogsRespCadastro.setCaption("Respons\u00E1vel Cadastro ");
        lblLogsRespCadastro.setFontColor("clWindowText");
        lblLogsRespCadastro.setFontSize(-11);
        lblLogsRespCadastro.setFontName("Tahoma");
        lblLogsRespCadastro.setFontStyle("[]");
        lblLogsRespCadastro.setVerticalAlignment("taVerticalCenter");
        lblLogsRespCadastro.setWordBreak(false);
        vBoxLogsRespCadastro.addChildren(lblLogsRespCadastro);
        lblLogsRespCadastro.applyProperties();
    }

    public TFString edtLogsRespCadastro = new TFString();

    private void init_edtLogsRespCadastro() {
        edtLogsRespCadastro.setName("edtLogsRespCadastro");
        edtLogsRespCadastro.setLeft(0);
        edtLogsRespCadastro.setTop(14);
        edtLogsRespCadastro.setWidth(193);
        edtLogsRespCadastro.setHeight(24);
        edtLogsRespCadastro.setTable(tbCadRapClienteLogs);
        edtLogsRespCadastro.setFieldName("RESPONSAVEL_CADASTRO");
        edtLogsRespCadastro.setFlex(false);
        edtLogsRespCadastro.setRequired(false);
        edtLogsRespCadastro.setConstraintCheckWhen("cwImmediate");
        edtLogsRespCadastro.setConstraintCheckType("ctExpression");
        edtLogsRespCadastro.setConstraintFocusOnError(false);
        edtLogsRespCadastro.setConstraintEnableUI(true);
        edtLogsRespCadastro.setConstraintEnabled(false);
        edtLogsRespCadastro.setConstraintFormCheck(true);
        edtLogsRespCadastro.setCharCase("ccNormal");
        edtLogsRespCadastro.setPwd(false);
        edtLogsRespCadastro.setMaxlength(0);
        edtLogsRespCadastro.setEnabled(false);
        edtLogsRespCadastro.setFontColor("clWindowText");
        edtLogsRespCadastro.setFontSize(-13);
        edtLogsRespCadastro.setFontName("Tahoma");
        edtLogsRespCadastro.setFontStyle("[]");
        edtLogsRespCadastro.setSaveLiteralCharacter(false);
        edtLogsRespCadastro.applyProperties();
        vBoxLogsRespCadastro.addChildren(edtLogsRespCadastro);
        addValidatable(edtLogsRespCadastro);
    }

    public TFHBox hBoxLogsRespUltimaAlteracao = new TFHBox();

    private void init_hBoxLogsRespUltimaAlteracao() {
        hBoxLogsRespUltimaAlteracao.setName("hBoxLogsRespUltimaAlteracao");
        hBoxLogsRespUltimaAlteracao.setLeft(0);
        hBoxLogsRespUltimaAlteracao.setTop(270);
        hBoxLogsRespUltimaAlteracao.setWidth(205);
        hBoxLogsRespUltimaAlteracao.setHeight(53);
        hBoxLogsRespUltimaAlteracao.setBorderStyle("stNone");
        hBoxLogsRespUltimaAlteracao.setPaddingTop(0);
        hBoxLogsRespUltimaAlteracao.setPaddingLeft(0);
        hBoxLogsRespUltimaAlteracao.setPaddingRight(0);
        hBoxLogsRespUltimaAlteracao.setPaddingBottom(0);
        hBoxLogsRespUltimaAlteracao.setMarginTop(0);
        hBoxLogsRespUltimaAlteracao.setMarginLeft(0);
        hBoxLogsRespUltimaAlteracao.setMarginRight(0);
        hBoxLogsRespUltimaAlteracao.setMarginBottom(0);
        hBoxLogsRespUltimaAlteracao.setSpacing(1);
        hBoxLogsRespUltimaAlteracao.setFlexVflex("ftFalse");
        hBoxLogsRespUltimaAlteracao.setFlexHflex("ftTrue");
        hBoxLogsRespUltimaAlteracao.setScrollable(false);
        hBoxLogsRespUltimaAlteracao.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsRespUltimaAlteracao.setBoxShadowConfigVerticalLength(10);
        hBoxLogsRespUltimaAlteracao.setBoxShadowConfigBlurRadius(5);
        hBoxLogsRespUltimaAlteracao.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsRespUltimaAlteracao.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsRespUltimaAlteracao.setBoxShadowConfigOpacity(75);
        hBoxLogsRespUltimaAlteracao.setVAlign("tvTop");
        vBoxLogsEdits.addChildren(hBoxLogsRespUltimaAlteracao);
        hBoxLogsRespUltimaAlteracao.applyProperties();
    }

    public TFVBox vBoxLogsRespUltimaAlteracao = new TFVBox();

    private void init_vBoxLogsRespUltimaAlteracao() {
        vBoxLogsRespUltimaAlteracao.setName("vBoxLogsRespUltimaAlteracao");
        vBoxLogsRespUltimaAlteracao.setLeft(0);
        vBoxLogsRespUltimaAlteracao.setTop(0);
        vBoxLogsRespUltimaAlteracao.setWidth(200);
        vBoxLogsRespUltimaAlteracao.setHeight(49);
        vBoxLogsRespUltimaAlteracao.setBorderStyle("stNone");
        vBoxLogsRespUltimaAlteracao.setPaddingTop(0);
        vBoxLogsRespUltimaAlteracao.setPaddingLeft(0);
        vBoxLogsRespUltimaAlteracao.setPaddingRight(0);
        vBoxLogsRespUltimaAlteracao.setPaddingBottom(0);
        vBoxLogsRespUltimaAlteracao.setMarginTop(0);
        vBoxLogsRespUltimaAlteracao.setMarginLeft(0);
        vBoxLogsRespUltimaAlteracao.setMarginRight(0);
        vBoxLogsRespUltimaAlteracao.setMarginBottom(0);
        vBoxLogsRespUltimaAlteracao.setSpacing(1);
        vBoxLogsRespUltimaAlteracao.setFlexVflex("ftFalse");
        vBoxLogsRespUltimaAlteracao.setFlexHflex("ftFalse");
        vBoxLogsRespUltimaAlteracao.setScrollable(false);
        vBoxLogsRespUltimaAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsRespUltimaAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxLogsRespUltimaAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxLogsRespUltimaAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsRespUltimaAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsRespUltimaAlteracao.setBoxShadowConfigOpacity(75);
        hBoxLogsRespUltimaAlteracao.addChildren(vBoxLogsRespUltimaAlteracao);
        vBoxLogsRespUltimaAlteracao.applyProperties();
    }

    public TFLabel lblLogsRespUltimaAlteracao = new TFLabel();

    private void init_lblLogsRespUltimaAlteracao() {
        lblLogsRespUltimaAlteracao.setName("lblLogsRespUltimaAlteracao");
        lblLogsRespUltimaAlteracao.setLeft(0);
        lblLogsRespUltimaAlteracao.setTop(0);
        lblLogsRespUltimaAlteracao.setWidth(145);
        lblLogsRespUltimaAlteracao.setHeight(13);
        lblLogsRespUltimaAlteracao.setCaption("Respons\u00E1vel \u00DAltima Altera\u00E7\u00E3o ");
        lblLogsRespUltimaAlteracao.setFontColor("clWindowText");
        lblLogsRespUltimaAlteracao.setFontSize(-11);
        lblLogsRespUltimaAlteracao.setFontName("Tahoma");
        lblLogsRespUltimaAlteracao.setFontStyle("[]");
        lblLogsRespUltimaAlteracao.setVerticalAlignment("taVerticalCenter");
        lblLogsRespUltimaAlteracao.setWordBreak(false);
        vBoxLogsRespUltimaAlteracao.addChildren(lblLogsRespUltimaAlteracao);
        lblLogsRespUltimaAlteracao.applyProperties();
    }

    public TFString edtLogsRespUltimaAlteracao = new TFString();

    private void init_edtLogsRespUltimaAlteracao() {
        edtLogsRespUltimaAlteracao.setName("edtLogsRespUltimaAlteracao");
        edtLogsRespUltimaAlteracao.setLeft(0);
        edtLogsRespUltimaAlteracao.setTop(14);
        edtLogsRespUltimaAlteracao.setWidth(193);
        edtLogsRespUltimaAlteracao.setHeight(24);
        edtLogsRespUltimaAlteracao.setTable(tbCadRapClienteLogs);
        edtLogsRespUltimaAlteracao.setFieldName("QUEM_ALTEROU");
        edtLogsRespUltimaAlteracao.setFlex(false);
        edtLogsRespUltimaAlteracao.setRequired(false);
        edtLogsRespUltimaAlteracao.setConstraintCheckWhen("cwImmediate");
        edtLogsRespUltimaAlteracao.setConstraintCheckType("ctExpression");
        edtLogsRespUltimaAlteracao.setConstraintFocusOnError(false);
        edtLogsRespUltimaAlteracao.setConstraintEnableUI(true);
        edtLogsRespUltimaAlteracao.setConstraintEnabled(false);
        edtLogsRespUltimaAlteracao.setConstraintFormCheck(true);
        edtLogsRespUltimaAlteracao.setCharCase("ccNormal");
        edtLogsRespUltimaAlteracao.setPwd(false);
        edtLogsRespUltimaAlteracao.setMaxlength(0);
        edtLogsRespUltimaAlteracao.setEnabled(false);
        edtLogsRespUltimaAlteracao.setFontColor("clWindowText");
        edtLogsRespUltimaAlteracao.setFontSize(-13);
        edtLogsRespUltimaAlteracao.setFontName("Tahoma");
        edtLogsRespUltimaAlteracao.setFontStyle("[]");
        edtLogsRespUltimaAlteracao.setSaveLiteralCharacter(false);
        edtLogsRespUltimaAlteracao.applyProperties();
        vBoxLogsRespUltimaAlteracao.addChildren(edtLogsRespUltimaAlteracao);
        addValidatable(edtLogsRespUltimaAlteracao);
    }

    public TFHBox hBoxLogsUltimaAlteracao = new TFHBox();

    private void init_hBoxLogsUltimaAlteracao() {
        hBoxLogsUltimaAlteracao.setName("hBoxLogsUltimaAlteracao");
        hBoxLogsUltimaAlteracao.setLeft(0);
        hBoxLogsUltimaAlteracao.setTop(324);
        hBoxLogsUltimaAlteracao.setWidth(205);
        hBoxLogsUltimaAlteracao.setHeight(53);
        hBoxLogsUltimaAlteracao.setBorderStyle("stNone");
        hBoxLogsUltimaAlteracao.setPaddingTop(0);
        hBoxLogsUltimaAlteracao.setPaddingLeft(0);
        hBoxLogsUltimaAlteracao.setPaddingRight(0);
        hBoxLogsUltimaAlteracao.setPaddingBottom(0);
        hBoxLogsUltimaAlteracao.setMarginTop(0);
        hBoxLogsUltimaAlteracao.setMarginLeft(0);
        hBoxLogsUltimaAlteracao.setMarginRight(0);
        hBoxLogsUltimaAlteracao.setMarginBottom(0);
        hBoxLogsUltimaAlteracao.setSpacing(1);
        hBoxLogsUltimaAlteracao.setFlexVflex("ftFalse");
        hBoxLogsUltimaAlteracao.setFlexHflex("ftFalse");
        hBoxLogsUltimaAlteracao.setScrollable(false);
        hBoxLogsUltimaAlteracao.setBoxShadowConfigHorizontalLength(10);
        hBoxLogsUltimaAlteracao.setBoxShadowConfigVerticalLength(10);
        hBoxLogsUltimaAlteracao.setBoxShadowConfigBlurRadius(5);
        hBoxLogsUltimaAlteracao.setBoxShadowConfigSpreadRadius(0);
        hBoxLogsUltimaAlteracao.setBoxShadowConfigShadowColor("clBlack");
        hBoxLogsUltimaAlteracao.setBoxShadowConfigOpacity(75);
        hBoxLogsUltimaAlteracao.setVAlign("tvTop");
        vBoxLogsEdits.addChildren(hBoxLogsUltimaAlteracao);
        hBoxLogsUltimaAlteracao.applyProperties();
    }

    public TFVBox vBoxLogsUltimaAlteracao = new TFVBox();

    private void init_vBoxLogsUltimaAlteracao() {
        vBoxLogsUltimaAlteracao.setName("vBoxLogsUltimaAlteracao");
        vBoxLogsUltimaAlteracao.setLeft(0);
        vBoxLogsUltimaAlteracao.setTop(0);
        vBoxLogsUltimaAlteracao.setWidth(200);
        vBoxLogsUltimaAlteracao.setHeight(49);
        vBoxLogsUltimaAlteracao.setBorderStyle("stNone");
        vBoxLogsUltimaAlteracao.setPaddingTop(0);
        vBoxLogsUltimaAlteracao.setPaddingLeft(0);
        vBoxLogsUltimaAlteracao.setPaddingRight(0);
        vBoxLogsUltimaAlteracao.setPaddingBottom(0);
        vBoxLogsUltimaAlteracao.setMarginTop(0);
        vBoxLogsUltimaAlteracao.setMarginLeft(0);
        vBoxLogsUltimaAlteracao.setMarginRight(0);
        vBoxLogsUltimaAlteracao.setMarginBottom(0);
        vBoxLogsUltimaAlteracao.setSpacing(1);
        vBoxLogsUltimaAlteracao.setFlexVflex("ftFalse");
        vBoxLogsUltimaAlteracao.setFlexHflex("ftFalse");
        vBoxLogsUltimaAlteracao.setScrollable(false);
        vBoxLogsUltimaAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxLogsUltimaAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxLogsUltimaAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxLogsUltimaAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxLogsUltimaAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxLogsUltimaAlteracao.setBoxShadowConfigOpacity(75);
        hBoxLogsUltimaAlteracao.addChildren(vBoxLogsUltimaAlteracao);
        vBoxLogsUltimaAlteracao.applyProperties();
    }

    public TFLabel lblLogsUltimaAlteracao = new TFLabel();

    private void init_lblLogsUltimaAlteracao() {
        lblLogsUltimaAlteracao.setName("lblLogsUltimaAlteracao");
        lblLogsUltimaAlteracao.setLeft(0);
        lblLogsUltimaAlteracao.setTop(0);
        lblLogsUltimaAlteracao.setWidth(78);
        lblLogsUltimaAlteracao.setHeight(13);
        lblLogsUltimaAlteracao.setCaption("Ultima Altera\u00E7\u00E3o");
        lblLogsUltimaAlteracao.setFontColor("clWindowText");
        lblLogsUltimaAlteracao.setFontSize(-11);
        lblLogsUltimaAlteracao.setFontName("Tahoma");
        lblLogsUltimaAlteracao.setFontStyle("[]");
        lblLogsUltimaAlteracao.setVerticalAlignment("taVerticalCenter");
        lblLogsUltimaAlteracao.setWordBreak(false);
        vBoxLogsUltimaAlteracao.addChildren(lblLogsUltimaAlteracao);
        lblLogsUltimaAlteracao.applyProperties();
    }

    public TFString edtLogsUltimaAlteracao = new TFString();

    private void init_edtLogsUltimaAlteracao() {
        edtLogsUltimaAlteracao.setName("edtLogsUltimaAlteracao");
        edtLogsUltimaAlteracao.setLeft(0);
        edtLogsUltimaAlteracao.setTop(14);
        edtLogsUltimaAlteracao.setWidth(193);
        edtLogsUltimaAlteracao.setHeight(24);
        edtLogsUltimaAlteracao.setFlex(false);
        edtLogsUltimaAlteracao.setRequired(false);
        edtLogsUltimaAlteracao.setConstraintCheckWhen("cwImmediate");
        edtLogsUltimaAlteracao.setConstraintCheckType("ctExpression");
        edtLogsUltimaAlteracao.setConstraintFocusOnError(false);
        edtLogsUltimaAlteracao.setConstraintEnableUI(true);
        edtLogsUltimaAlteracao.setConstraintEnabled(false);
        edtLogsUltimaAlteracao.setConstraintFormCheck(true);
        edtLogsUltimaAlteracao.setCharCase("ccNormal");
        edtLogsUltimaAlteracao.setPwd(false);
        edtLogsUltimaAlteracao.setMaxlength(0);
        edtLogsUltimaAlteracao.setEnabled(false);
        edtLogsUltimaAlteracao.setFontColor("clWindowText");
        edtLogsUltimaAlteracao.setFontSize(-13);
        edtLogsUltimaAlteracao.setFontName("Tahoma");
        edtLogsUltimaAlteracao.setFontStyle("[]");
        edtLogsUltimaAlteracao.setSaveLiteralCharacter(false);
        edtLogsUltimaAlteracao.applyProperties();
        vBoxLogsUltimaAlteracao.addChildren(edtLogsUltimaAlteracao);
        addValidatable(edtLogsUltimaAlteracao);
    }

    public TFVBox vBoxGrid = new TFVBox();

    private void init_vBoxGrid() {
        vBoxGrid.setName("vBoxGrid");
        vBoxGrid.setLeft(210);
        vBoxGrid.setTop(0);
        vBoxGrid.setWidth(381);
        vBoxGrid.setHeight(381);
        vBoxGrid.setBorderStyle("stNone");
        vBoxGrid.setPaddingTop(0);
        vBoxGrid.setPaddingLeft(0);
        vBoxGrid.setPaddingRight(0);
        vBoxGrid.setPaddingBottom(0);
        vBoxGrid.setMarginTop(5);
        vBoxGrid.setMarginLeft(0);
        vBoxGrid.setMarginRight(10);
        vBoxGrid.setMarginBottom(10);
        vBoxGrid.setSpacing(1);
        vBoxGrid.setFlexVflex("ftMin");
        vBoxGrid.setFlexHflex("ftFalse");
        vBoxGrid.setScrollable(false);
        vBoxGrid.setBoxShadowConfigHorizontalLength(10);
        vBoxGrid.setBoxShadowConfigVerticalLength(10);
        vBoxGrid.setBoxShadowConfigBlurRadius(5);
        vBoxGrid.setBoxShadowConfigSpreadRadius(0);
        vBoxGrid.setBoxShadowConfigShadowColor("clBlack");
        vBoxGrid.setBoxShadowConfigOpacity(75);
        hBoxLogsContainer.addChildren(vBoxGrid);
        vBoxGrid.applyProperties();
    }

    public TFGrid gridLogs = new TFGrid();

    private void init_gridLogs() {
        gridLogs.setName("gridLogs");
        gridLogs.setLeft(0);
        gridLogs.setTop(0);
        gridLogs.setWidth(368);
        gridLogs.setHeight(370);
        gridLogs.setAlign("alClient");
        gridLogs.setTable(tbGridCadRapCliLogs);
        gridLogs.setFlexVflex("ftFalse");
        gridLogs.setFlexHflex("ftTrue");
        gridLogs.setPagingEnabled(false);
        gridLogs.setFrozenColumns(0);
        gridLogs.setShowFooter(false);
        gridLogs.setShowHeader(true);
        gridLogs.setMultiSelection(false);
        gridLogs.setGroupingEnabled(false);
        gridLogs.setGroupingExpanded(false);
        gridLogs.setGroupingShowFooter(false);
        gridLogs.setCrosstabEnabled(false);
        gridLogs.setCrosstabGroupType("cgtConcat");
        gridLogs.setEditionEnabled(false);
        gridLogs.setNoBorder(false);
        TFGridColumn gridLogsDATA_ALTERACAO = new TFGridColumn();
        gridLogsDATA_ALTERACAO.setFieldName("DATA_ALTERACAO");
        gridLogsDATA_ALTERACAO.setTitleCaption("Data da Altera\u00E7\u00E3o");
        gridLogsDATA_ALTERACAO.setWidth(150);
        gridLogsDATA_ALTERACAO.setVisible(true);
        gridLogsDATA_ALTERACAO.setPrecision(0);
        gridLogsDATA_ALTERACAO.setTextAlign("taLeft");
        gridLogsDATA_ALTERACAO.setFieldType("ftDateTime");
        gridLogsDATA_ALTERACAO.setFlexRatio(0);
        gridLogsDATA_ALTERACAO.setSort(false);
        gridLogsDATA_ALTERACAO.setImageHeader(0);
        gridLogsDATA_ALTERACAO.setWrap(false);
        gridLogsDATA_ALTERACAO.setFlex(false);
        gridLogsDATA_ALTERACAO.setCharCase("ccNormal");
        gridLogsDATA_ALTERACAO.setBlobConfigMimeType("bmtText");
        gridLogsDATA_ALTERACAO.setBlobConfigShowType("btImageViewer");
        gridLogsDATA_ALTERACAO.setShowLabel(true);
        gridLogsDATA_ALTERACAO.setEditorEditType("etTFDate");
        gridLogsDATA_ALTERACAO.setEditorPrecision(0);
        gridLogsDATA_ALTERACAO.setEditorMaxLength(100);
        gridLogsDATA_ALTERACAO.setEditorLookupFilterKey(0);
        gridLogsDATA_ALTERACAO.setEditorLookupFilterDesc(0);
        gridLogsDATA_ALTERACAO.setEditorPopupHeight(400);
        gridLogsDATA_ALTERACAO.setEditorPopupWidth(400);
        gridLogsDATA_ALTERACAO.setEditorCharCase("ccNormal");
        gridLogsDATA_ALTERACAO.setEditorEnabled(false);
        gridLogsDATA_ALTERACAO.setEditorReadOnly(false);
        gridLogsDATA_ALTERACAO.setCheckedValue("S");
        gridLogsDATA_ALTERACAO.setUncheckedValue("N");
        gridLogsDATA_ALTERACAO.setHiperLink(false);
        gridLogsDATA_ALTERACAO.setEditorConstraintCheckWhen("cwImmediate");
        gridLogsDATA_ALTERACAO.setEditorConstraintCheckType("ctExpression");
        gridLogsDATA_ALTERACAO.setEditorConstraintFocusOnError(false);
        gridLogsDATA_ALTERACAO.setEditorConstraintEnableUI(true);
        gridLogsDATA_ALTERACAO.setEditorConstraintEnabled(false);
        gridLogsDATA_ALTERACAO.setEmpty(false);
        gridLogsDATA_ALTERACAO.setMobileOptsShowMobile(false);
        gridLogsDATA_ALTERACAO.setMobileOptsOrder(0);
        gridLogsDATA_ALTERACAO.setBoxSize(0);
        gridLogsDATA_ALTERACAO.setImageSrcType("istSource");
        gridLogs.getColumns().add(gridLogsDATA_ALTERACAO);
        TFGridColumn gridLogsQUEM_ALTEROU = new TFGridColumn();
        gridLogsQUEM_ALTEROU.setFieldName("QUEM_ALTEROU");
        gridLogsQUEM_ALTEROU.setTitleCaption("Quem Alterou");
        gridLogsQUEM_ALTEROU.setWidth(150);
        gridLogsQUEM_ALTEROU.setVisible(true);
        gridLogsQUEM_ALTEROU.setPrecision(0);
        gridLogsQUEM_ALTEROU.setTextAlign("taLeft");
        gridLogsQUEM_ALTEROU.setFieldType("ftString");
        gridLogsQUEM_ALTEROU.setFlexRatio(0);
        gridLogsQUEM_ALTEROU.setSort(false);
        gridLogsQUEM_ALTEROU.setImageHeader(0);
        gridLogsQUEM_ALTEROU.setWrap(false);
        gridLogsQUEM_ALTEROU.setFlex(false);
        gridLogsQUEM_ALTEROU.setCharCase("ccNormal");
        gridLogsQUEM_ALTEROU.setBlobConfigMimeType("bmtText");
        gridLogsQUEM_ALTEROU.setBlobConfigShowType("btImageViewer");
        gridLogsQUEM_ALTEROU.setShowLabel(true);
        gridLogsQUEM_ALTEROU.setEditorEditType("etTFString");
        gridLogsQUEM_ALTEROU.setEditorPrecision(0);
        gridLogsQUEM_ALTEROU.setEditorMaxLength(100);
        gridLogsQUEM_ALTEROU.setEditorLookupFilterKey(0);
        gridLogsQUEM_ALTEROU.setEditorLookupFilterDesc(0);
        gridLogsQUEM_ALTEROU.setEditorPopupHeight(400);
        gridLogsQUEM_ALTEROU.setEditorPopupWidth(400);
        gridLogsQUEM_ALTEROU.setEditorCharCase("ccNormal");
        gridLogsQUEM_ALTEROU.setEditorEnabled(false);
        gridLogsQUEM_ALTEROU.setEditorReadOnly(false);
        gridLogsQUEM_ALTEROU.setCheckedValue("S");
        gridLogsQUEM_ALTEROU.setUncheckedValue("N");
        gridLogsQUEM_ALTEROU.setHiperLink(false);
        gridLogsQUEM_ALTEROU.setEditorConstraintCheckWhen("cwImmediate");
        gridLogsQUEM_ALTEROU.setEditorConstraintCheckType("ctExpression");
        gridLogsQUEM_ALTEROU.setEditorConstraintFocusOnError(false);
        gridLogsQUEM_ALTEROU.setEditorConstraintEnableUI(true);
        gridLogsQUEM_ALTEROU.setEditorConstraintEnabled(false);
        gridLogsQUEM_ALTEROU.setEmpty(false);
        gridLogsQUEM_ALTEROU.setMobileOptsShowMobile(false);
        gridLogsQUEM_ALTEROU.setMobileOptsOrder(0);
        gridLogsQUEM_ALTEROU.setBoxSize(0);
        gridLogsQUEM_ALTEROU.setImageSrcType("istSource");
        gridLogs.getColumns().add(gridLogsQUEM_ALTEROU);
        vBoxGrid.addChildren(gridLogs);
        gridLogs.applyProperties();
    }

    public TFTabsheet tabOutros = new TFTabsheet();

    private void init_tabOutros() {
        tabOutros.setName("tabOutros");
        tabOutros.setCaption("Outros");
        tabOutros.setFontColor("clWindowText");
        tabOutros.setFontSize(-11);
        tabOutros.setFontName("Tahoma");
        tabOutros.setFontStyle("[]");
        tabOutros.setClosable(false);
        pgControlLogsCliente.addChildren(tabOutros);
        tabOutros.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}