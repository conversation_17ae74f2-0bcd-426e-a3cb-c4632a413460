/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Cliente - Versao: 1.0.6.28
   Class Main  : Templates
   Analista    : EMERSON
   Data Created: 11/12/2019 14:28:44
   Data Changed: 04/03/2022 17:20:35
  -------------------------------------------------------------------------- */

package freedom.bytecode.form.wizard;

import freedom.bytecode.rn.TemplatesRNA;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.grid.TFGridExporter;
import freedom.client.controls.impl.treegrid.TFTreeGridExporter;
import freedom.client.controls.IBaseComponent;
import freedom.client.event.Event;
import freedom.client.controls.IFocusable;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.RowState;
import freedom.data.TableState;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.impl.Row;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;


public abstract class FrmTemplatesW extends FrmTemplates {

    private static final long serialVersionUID = 20130827081850L;
    public TableState oper = TableState.QUERYING; 

    public FrmTemplatesW() {
        lblMensagem.setCaption("");
        habilitaComp(false);
        
		tabListagem.setFontSize(-20);
		tabCadastro.setFontSize(-20);

        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao Abrir Tabelas Auxiliares")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnFiltroAvancado.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled && !menuSelecaoMultipla.isChecked());
        btnAlterar.setEnabled(!enabled && !tbEmailModelo.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbEmailModelo.isEmpty());
        if (! menuHabilitaNavegacao.isChecked()) {                                       // menu popup habilitar navegação
            btnProximo.setEnabled(!enabled && !tbEmailModelo.isEmpty());
            btnAnterior.setEnabled(!enabled && !tbEmailModelo.isEmpty());
        }
        btnAceitar.setEnabled(!enabled && !tbEmailModelo.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);
        btnSalvarContinuar.setEnabled(enabled && !menuSelecaoMultipla.isChecked());
        menuSelecaoMultipla.setVisible(!enabled);
        
        edModelo44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));
        edPrivado44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));
        edAplicacao44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));
        edDepartamento44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));
        edAssunto44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));
        edTpArquivoAnexoMensagem44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));
        edIdTemplate44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));
        edEhChat44002.setEnabled(enabled && ( ! tbEmailModelo.isEmpty()  || tbEmailModelo.getState() == TableState.INSERTING));

        
    }

    @Override
    public void btnConsultarClick(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }
    
    @Override
    public void btnFiltroAvancadoClick(Event<Object> event) {
        filtroAvancado.doModal();
    }    

    @Override
    public void btnNovoClick(final Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            if (menuSelecaoMultipla.isChecked()) {
                onSalvarMultiplo();
            } else {
                onSalvar();
            }
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    public void executaFiltroPrincipal() throws Exception {
        tbEmailModelo.clearFilters();
        if (! efAplicacao.getValue().equals(null)) {
           tbEmailModelo.setFilterAPLICACAO(efAplicacao.getValue());
        }
        if (! efDepartamento.getValue().equals(null)) {
           tbEmailModelo.setFilterDEPARTAMENTO(efDepartamento.getValue());
        }

    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        if (menuHabilitaNavegacao.isChecked()) {
            btnProximo.setEnabled(true);
            btnAnterior.setEnabled(true);
        } else {
            btnProximo.setEnabled(btnNovo.isEnabled());
            btnAnterior.setEnabled(btnNovo.isEnabled());
        }
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {

        boolean checkedMenu = menuSelecaoMultipla.isChecked();
        gridPrincipal.setMultiSelection(checkedMenu);

        // tratamento das abas visto que pode mexer somente na tabela master
        for (int i = 2; i <= pgPrincipal.getPageCount()-1; i++) {
             pgPrincipal.selectTab(i);
             pgPrincipal.getSelectedTab().setVisible(!checkedMenu);
        }

        // opções da barra de ferramenta
        btnNovo.setEnabled(! checkedMenu && btnAlterar.isEnabled());
        btnSalvarContinuar.setEnabled(! checkedMenu && btnAlterar.isEnabled());


        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()+30);
            // desregistra o marter table dos componentes
                        edModelo44002.setTable(null);
            edModelo44002.setValue(null);
            edPrivado44002.setTable(null);
            edPrivado44002.setValue(null);
            edAplicacao44002.setTable(null);
            edAplicacao44002.setValue(null);
            edDepartamento44002.setTable(null);
            edDepartamento44002.setValue(null);
            edAssunto44002.setTable(null);
            edAssunto44002.setValue(null);
            edTpArquivoAnexoMensagem44002.setTable(null);
            edTpArquivoAnexoMensagem44002.setValue(null);
            edIdTemplate44002.setTable(null);
            edIdTemplate44002.setValue(null);
            edEhChat44002.setTable(null);
            edEhChat44002.setValue(null);

            gridPrincipal.clearSelection();
        } else {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()-30);
            // registra o master table para os componentes
                        edModelo44002.setTable(tbEmailModelo);
            edPrivado44002.setTable(tbEmailModelo);
            edAplicacao44002.setTable(tbEmailModelo);
            edDepartamento44002.setTable(tbEmailModelo);
            edAssunto44002.setTable(tbEmailModelo);
            edTpArquivoAnexoMensagem44002.setTable(tbEmailModelo);
            edIdTemplate44002.setTable(tbEmailModelo);
            edEhChat44002.setTable(tbEmailModelo);

        }
        pgPrincipal.selectTab(0);
    }

    @Override
    public void FrmTemplateskeyActionPesquisar(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionIncluir(Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionExcluir(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionSalvar(Event<Object> event) {
        try {
            onSalvar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionSalvarContinuar(Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionCancelar(Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionAnterior(Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionProximo(Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmTemplateskeyActionAceitar(Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }    
    
    @Override
    public void btnMaisClick(Event<Object> event) {
        popMenuPrincipal.open(this);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(Event<Object> event) {
        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Aviso")
                  .message("Tabela Auxiliares Foram Reabertas")
                  .showInformation();
        }
    }

    @Override
    public void menuItemConfgGridClick(Event<Object> event) {
        gridConfig.doModal();
    }

    @Override
    public void menuItemHelpClick(Event<Object> event) {
        FormUtil.redirect("help/FrmTemplates.zul", true);
    }

    protected void onConsultar() throws Exception {
        
        tbEmailModelo.close();
        executaFiltroPrincipal();
        
        tbEmailModelo.open();
        habilitaComp(false);

        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.clearSelection();
        }

        if (tbEmailModelo.isEmpty()) {
            Dialog.create()
                      .title("Aviso")
                      .message("Registro Não Encontrado...")
                      .showInformation();
        }
    }

    protected void onAnterior() throws Exception {

        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbEmailModelo.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnAnterior);
        }

        if (tbEmailModelo.bof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o Primeiro Registro")
                    .showInformation();
        } else {
            tbEmailModelo.prior();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onProximo() throws Exception {
        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbEmailModelo.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnProximo);
        }

        if (tbEmailModelo.eof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o último registgro")
                    .showInformation();
        } else {
            tbEmailModelo.next();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onIncluir() throws Exception {
        oper = TableState.INSERTING; 
        
        rn.incluir(); 
        
        
        pgPrincipal.selectTab(1);
        edModelo44002.setFocus();
        habilitaComp(true);
        lblMensagem.setCaption("Incluindo...");
    }

    protected void onAlterar() throws Exception {
        oper = TableState.MODIFYING;

        if (menuSelecaoMultipla.isChecked()) {
            lblMensagem.setCaption("ATENÇÃO: Alterando multiplos registros. Será alterado todos os registros selecionados...");
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } else {
            if (!tbEmailModelo.isEmpty()) {
                rn.alterar();
                if (pgPrincipal.getSelectedIndex() == 0)  {
                   pgPrincipal.selectTab(1);
                }                
                habilitaComp(true);
                edModelo44002.setFocus();
                lblMensagem.setCaption("Alterando "+tbEmailModelo.getID_EMAIL_MODELO().asString()+"...");
            } else {
                Dialog.create()
                      .title("Erro ao editar")
                      .message("Selecione um registro antes de editar")
                      .showError();
            }
        }
    }

    protected void onExcluir() throws DataException {
        if (!tbEmailModelo.isEmpty()) {
           oper = TableState.DELETING; 
           String titulo;
           String mensagem;
           if (menuSelecaoMultipla.isChecked()) {
               titulo = "Exclusão Multipla";
               mensagem = "ATENÇÃO: Serão excluido(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?";
           } else {
               titulo = "Exclusão de Registro";
               mensagem = "Confirma a exclusão do registro selecionado?";
           }

            Dialog.create()
                    .title(titulo)
                    .message(mensagem)
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                try {
                                    tbEmailModelo.disableControls();
                                    tbEmailModelo.disableMasterTable();
                                    if (menuSelecaoMultipla.isChecked()) {
                                        for (int bm : gridPrincipal.getSelectedIndices(false)) {
                                            tbEmailModelo.gotoBookmark(bm);
                                            rn.excluiTableMaster();
                                        }
                                    } else {
                                        rn.excluiTableMaster();
                                    }

                                    try {
                                        rn.excluir();                                        
                                        
                                        habilitaComp(false);
                                    } catch (DataException e) {
                                        throw e;
                                    }
                                } finally {
                                    tbEmailModelo.enableControls();
                                    tbEmailModelo.enableMasterTable();
                                    oper = TableState.QUERYING;
                                }
                            } catch (DataException ex) {
                                Dialog.create()
                                    .title("Erro ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex);

                                try {
                                    tbEmailModelo.cancelUpdates();
                                } catch (DataException ex1) {
                                    Dialog.create()
                                    .title("Erro no CancelUpdates ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex1);
                                }

                            }
                        }
                    });
        } else {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message("Selecione um registro antes de excluir")
                    .showError();
        }
    }

    protected void onSalvarMultiplo() throws DataException {

        Dialog.create()
            .title("Alteração Multipla")
            .message("ATENÇÃO: Serão alterado(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?")
            .confirmSimNao((String dialogResult) -> {
                if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                     try {
                           tbEmailModelo.disableControls();
                           int lastBookmark = tbEmailModelo.getBookmark();
                           try {
                               for (int bm : gridPrincipal.getSelectedIndices(true)) {
                                   tbEmailModelo.gotoBookmark(bm);
                                   tbEmailModelo.edit();
                                   
                                   if ( ! edModelo44002.getValue().isNull()) {
                                       tbEmailModelo.setMODELO(edModelo44002.getValue());
                                   }
                                   if ( ! edPrivado44002.getValue().isNull()) {
                                       tbEmailModelo.setPRIVADO(edPrivado44002.getValue());
                                   }
                                   if ( ! edAplicacao44002.getValue().isNull()) {
                                       tbEmailModelo.setAPLICACAO(edAplicacao44002.getValue());
                                   }
                                   if ( ! edDepartamento44002.getValue().isNull()) {
                                       tbEmailModelo.setDEPARTAMENTO(edDepartamento44002.getValue());
                                   }
                                   if ( ! edAssunto44002.getValue().isNull()) {
                                       tbEmailModelo.setASSUNTO(edAssunto44002.getValue());
                                   }
                                   if ( ! edTpArquivoAnexoMensagem44002.getValue().isNull()) {
                                       tbEmailModelo.setTP_ARQUIVO_ANEXO_MENSAGEM(edTpArquivoAnexoMensagem44002.getValue());
                                   }
                                   if ( ! edIdTemplate44002.getValue().isNull()) {
                                       tbEmailModelo.setID_TEMPLATE(edIdTemplate44002.getValue());
                                   }
                                   if ( ! edEhChat44002.getValue().isNull()) {
                                       tbEmailModelo.setEH_CHAT(edEhChat44002.getValue());
                                   }

                                   tbEmailModelo.post();
                               }

                               onSalvar();

                           } finally {
                               tbEmailModelo.close();
                               tbEmailModelo.open();
                               // tbEmailModelo.gotoBookmark(lastBookmark);
                               tbEmailModelo.enableControls();
                               gridPrincipal.clearSelection();
                           }

                     } catch (DataException e) {
                         Dialog.create()
                               .title("Erro ao salvar")
                               .message(e.getMessage())
                               .showException(e);
                    }
                }
        });
    }

    protected void onSalvar() throws DataException {
        // executa a validação das constraint dos objetos edition
        check();
        if (!getErrorMap().isEmpty()) {
            StringBuilder strBuilder = new StringBuilder();

            getErrorMap().values().stream().forEach((s) -> {
                strBuilder.append(s).append("\n");
            });        

            // manda o focu para o primeiro objeto que deu erro de constraint
            ((IFocusable)getErrorMap().keySet().iterator().next()).setFocus();

            Dialog.create()
                  .title("Erro ao validar")
                  .message("Existe validação(s) pendente...\n" + strBuilder.toString())
                  .showError();

            return;
        }

        // seta Calc. Update
        setCalcUpdate();

        // executar o metodo salvar na RN
        rn.salvar();

        // atualiza o registro
        tbEmailModelo.refreshRecord();
        
        habilitaComp(false);
        oper = TableState.QUERYING;
        lblMensagem.setCaption("");
    }

    protected void onSalvarContinuar() throws DataException {
        try {
            TableState st = tbEmailModelo.getState();
            onSalvar();
            if (st == TableState.INSERTING) {
                onIncluir();
            } else if (st == TableState.MODIFYING) {
                onAlterar();
            }
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar a edição")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void onCancelar() throws DataException {
        habilitaComp(false);
        rn.cancelar();
        
        oper = TableState.QUERYING; 
        lblMensagem.setCaption("Registro Selecionado: "+tbEmailModelo.getID_EMAIL_MODELO().asString());
    }
    
    protected void onAceitar() throws Exception {
        if (FormUtil.isExternalCall()) {
            // passa os parametros para a resposta ao VB
            FormUtil.externalCall(tbEmailModelo.getField("ID_PESSOA").asString());
        } else {
            close();
        }
    }
    
    protected void onAbreTabelaAux() throws DataException {
        ISession s = SessionFactory.getInstance().getSession();
        try {                
            s.open();
            
            tbEmailModelo.setSession(s);
            tbEmailModelo.refreshRecord();
            tbEmailModelo.setSession(null);
        } finally {
            if (s != null) {
                s.close();
            }
        }
    }
    
    protected void setCalcUpdate() throws DataException {
        
        postTable();
    }

    private void postTable() throws DataException {
        tbEmailModelo.post();
    }

    public void loadFormPk(Integer idEmailModelo ) throws DataException {
        tbEmailModelo.close(); 
        tbEmailModelo.clearFilters();
        
        if (idEmailModelo > 0) {
            tbEmailModelo.addFilter("ID_EMAIL_MODELO");
            tbEmailModelo.addParam("ID_EMAIL_MODELO", idEmailModelo);
        } else return;
        
        tbEmailModelo.open();
        habilitaComp(false);          // se tem registro habilita botões da barra de ferramenta  
    }         

    // retorna true se o master esta sendo editado, pode ser usado para verificar se o form esta 
    // habilitado edição
    public boolean masterIsEditing() {
       return (tbEmailModelo.getState() != TableState.QUERYING);
    }
    
    
    @Override
    public void tbEmailModeloAfterScroll(final Event<Object> event) {
        lblMensagem.setCaption("Selecionado: "+tbEmailModelo.getID_EMAIL_MODELO().asString());

    }

        
    
    @Override
    public void efAplicacaoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
    @Override
    public void efDepartamentoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
        
            
    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void gridPrincipalClickImageAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuItemExportPdfClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportPdf(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportExcelClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportExcel(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }




    protected final void habilitaComp44003(Boolean enabled) {
        
        
    }


	public void onIncluir44003() {
        try {            
            rn.incluir44003(); 
            habilitaComp44003(true);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao adicionar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onAlterar44003() {
        try {
            if (!tbEmailModeloTag.isEmpty()) {
                rn.alterar44003();
                
                habilitaComp(true);
            } else {
                Dialog.create()
                        .title("Erro ao editar detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onExcluir44003() {
        try {
            if (!tbEmailModeloTag.isEmpty()) {
                
                rn.excluir44003();
            } else {
                Dialog.create()
                        .title("Erro ao excluir detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onCancelar44003() {
        try {
            rn.cancelar44003();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao cancelar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }
    
    public void onConfirmar44003() {
        try {
            rn.confirmar44003();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao confirmar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

}

