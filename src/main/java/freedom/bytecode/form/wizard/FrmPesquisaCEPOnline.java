package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPesquisaCEPOnline extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PesquisaCEPOnlineRNA rn = null;

    public FrmPesquisaCEPOnline() {
        try {
            rn = (freedom.bytecode.rn.PesquisaCEPOnlineRNA) getRN(freedom.bytecode.rn.wizard.PesquisaCEPOnlineRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxPrincipal();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnAceitar();
        init_hBoxPesquisa();
        init_vBoxCEPAPesquisar();
        init_lblCEP();
        init_edtCEP();
        init_hBoxBtnPesquisarCEP();
        init_btnPesquisarCEP();
        init_vBoxDadosDoCEP();
        init_hBoxDadosDoCEPCabecalho();
        init_hBoxDadosDoCEPCabecalhoTipo();
        init_lblTipoCabecalho();
        init_lblValorCabecalho();
        init_hBoxEnderecosCEP();
        init_hBoxDadosDoCEPTipoCEP();
        init_lblTipoCEP();
        init_lblValorCEP();
        init_hBoxEnderecosLogradouro();
        init_hBoxDadosDoCEPTipoLogradouro();
        init_lblTipoLogradouro();
        init_lblValorLogradouro();
        init_hBoxEnderecosComplemento();
        init_hBoxDadosDoCEPTipoComplemento();
        init_lblTipoComplemento();
        init_lblValorComplemento();
        init_hBoxEnderecosUnidade();
        init_hBoxDadosDoCEPTipoUnidade();
        init_lblTipoUnidade();
        init_lblValorUnidade();
        init_hBoxEnderecosBairro();
        init_hBoxDadosDoCEPTipoBairro();
        init_lblTipoBairro();
        init_lblValorBairro();
        init_hBoxEnderecosLocalidade();
        init_hBoxDadosDoCEPTipoLocalidade();
        init_lblTipoLocalidade();
        init_lblValorLocalidade();
        init_hBoxEnderecosUF();
        init_hBoxDadosDoCEPTipoUF();
        init_lblTipoUF();
        init_lblValorUF();
        init_hBoxEnderecosEstado();
        init_hBoxDadosDoCEPTipoEstado();
        init_lblTipoEstado();
        init_lblValorEstado();
        init_hBoxEnderecosRegiao();
        init_hBoxDadosDoCEPTipoRegiao();
        init_lblTipoRegiao();
        init_lblValorRegiao();
        init_hBoxEnderecosIBGE();
        init_hBoxDadosDoCEPTipoIBGE();
        init_lblTipoIBGE();
        init_lblValorIBGE();
        init_hBoxEnderecosGIA();
        init_hBoxDadosDoCEPTipoGIA();
        init_lblTipoGIA();
        init_lblValorGIA();
        init_hBoxEnderecosDDD();
        init_hBoxDadosDoCEPTipoDDD();
        init_lblTipoDDD();
        init_lblValorDDD();
        init_hBoxEnderecosSIAFI();
        init_hBoxDadosDoCEPTipoSIAFI();
        init_lblTipoSIAFI();
        init_lblValorSIAFI();
        init_FrmPesquisaCEPOnline();
    }

    protected TFForm FrmPesquisaCEPOnline = this;
    private void init_FrmPesquisaCEPOnline() {
        FrmPesquisaCEPOnline.setName("FrmPesquisaCEPOnline");
        FrmPesquisaCEPOnline.setCaption("Pesquisa CEP OnLine [Integra\u00E7\u00E3o API ViaCEP]");
        FrmPesquisaCEPOnline.setClientHeight(431);
        FrmPesquisaCEPOnline.setClientWidth(384);
        FrmPesquisaCEPOnline.setColor("clBtnFace");
        FrmPesquisaCEPOnline.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmPesquisaCEPOnline", "FrmPesquisaCEPOnline", "OnCreate");
        });
        FrmPesquisaCEPOnline.setWOrigem("EhMain");
        FrmPesquisaCEPOnline.setWKey("52301461");
        FrmPesquisaCEPOnline.setSpacing(0);
        FrmPesquisaCEPOnline.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(384);
        vBoxPrincipal.setHeight(431);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(5);
        vBoxPrincipal.setPaddingLeft(5);
        vBoxPrincipal.setPaddingRight(5);
        vBoxPrincipal.setPaddingBottom(5);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(5);
        vBoxPrincipal.setFlexVflex("ftFalse");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmPesquisaCEPOnline.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(130);
        hBoxBotoes.setHeight(61);
        hBoxBotoes.setAlign("alTop");
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(55);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-16);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarFrmPesquisaCEPOnlineClick(event);
            processarFlow("FrmPesquisaCEPOnline", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(60);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(55);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-16);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.setVisible(false);
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarFrmPesquisaCEPOnlineClick(event);
            processarFlow("FrmPesquisaCEPOnline", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFHBox hBoxPesquisa = new TFHBox();

    private void init_hBoxPesquisa() {
        hBoxPesquisa.setName("hBoxPesquisa");
        hBoxPesquisa.setLeft(0);
        hBoxPesquisa.setTop(62);
        hBoxPesquisa.setWidth(170);
        hBoxPesquisa.setHeight(60);
        hBoxPesquisa.setBorderStyle("stNone");
        hBoxPesquisa.setPaddingTop(0);
        hBoxPesquisa.setPaddingLeft(0);
        hBoxPesquisa.setPaddingRight(0);
        hBoxPesquisa.setPaddingBottom(0);
        hBoxPesquisa.setMarginTop(0);
        hBoxPesquisa.setMarginLeft(0);
        hBoxPesquisa.setMarginRight(0);
        hBoxPesquisa.setMarginBottom(0);
        hBoxPesquisa.setSpacing(5);
        hBoxPesquisa.setFlexVflex("ftMin");
        hBoxPesquisa.setFlexHflex("ftTrue");
        hBoxPesquisa.setScrollable(false);
        hBoxPesquisa.setBoxShadowConfigHorizontalLength(10);
        hBoxPesquisa.setBoxShadowConfigVerticalLength(10);
        hBoxPesquisa.setBoxShadowConfigBlurRadius(5);
        hBoxPesquisa.setBoxShadowConfigSpreadRadius(0);
        hBoxPesquisa.setBoxShadowConfigShadowColor("clBlack");
        hBoxPesquisa.setBoxShadowConfigOpacity(75);
        hBoxPesquisa.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxPesquisa);
        hBoxPesquisa.applyProperties();
    }

    public TFVBox vBoxCEPAPesquisar = new TFVBox();

    private void init_vBoxCEPAPesquisar() {
        vBoxCEPAPesquisar.setName("vBoxCEPAPesquisar");
        vBoxCEPAPesquisar.setLeft(0);
        vBoxCEPAPesquisar.setTop(0);
        vBoxCEPAPesquisar.setWidth(130);
        vBoxCEPAPesquisar.setHeight(50);
        vBoxCEPAPesquisar.setBorderStyle("stNone");
        vBoxCEPAPesquisar.setPaddingTop(0);
        vBoxCEPAPesquisar.setPaddingLeft(0);
        vBoxCEPAPesquisar.setPaddingRight(0);
        vBoxCEPAPesquisar.setPaddingBottom(0);
        vBoxCEPAPesquisar.setMarginTop(0);
        vBoxCEPAPesquisar.setMarginLeft(0);
        vBoxCEPAPesquisar.setMarginRight(0);
        vBoxCEPAPesquisar.setMarginBottom(0);
        vBoxCEPAPesquisar.setSpacing(5);
        vBoxCEPAPesquisar.setFlexVflex("ftMin");
        vBoxCEPAPesquisar.setFlexHflex("ftTrue");
        vBoxCEPAPesquisar.setScrollable(false);
        vBoxCEPAPesquisar.setBoxShadowConfigHorizontalLength(10);
        vBoxCEPAPesquisar.setBoxShadowConfigVerticalLength(10);
        vBoxCEPAPesquisar.setBoxShadowConfigBlurRadius(5);
        vBoxCEPAPesquisar.setBoxShadowConfigSpreadRadius(0);
        vBoxCEPAPesquisar.setBoxShadowConfigShadowColor("clBlack");
        vBoxCEPAPesquisar.setBoxShadowConfigOpacity(75);
        hBoxPesquisa.addChildren(vBoxCEPAPesquisar);
        vBoxCEPAPesquisar.applyProperties();
    }

    public TFLabel lblCEP = new TFLabel();

    private void init_lblCEP() {
        lblCEP.setName("lblCEP");
        lblCEP.setLeft(0);
        lblCEP.setTop(0);
        lblCEP.setWidth(23);
        lblCEP.setHeight(14);
        lblCEP.setCaption("CEP");
        lblCEP.setFontColor("clWindowText");
        lblCEP.setFontSize(-12);
        lblCEP.setFontName("Tahoma");
        lblCEP.setFontStyle("[fsBold]");
        lblCEP.setVerticalAlignment("taVerticalCenter");
        lblCEP.setWordBreak(false);
        vBoxCEPAPesquisar.addChildren(lblCEP);
        lblCEP.applyProperties();
    }

    public TFString edtCEP = new TFString();

    private void init_edtCEP() {
        edtCEP.setName("edtCEP");
        edtCEP.setLeft(0);
        edtCEP.setTop(15);
        edtCEP.setWidth(120);
        edtCEP.setHeight(24);
        edtCEP.setHint("CEP");
        edtCEP.setHelpCaption("CEP");
        edtCEP.setFlex(true);
        edtCEP.setRequired(false);
        edtCEP.setPrompt("CEP");
        edtCEP.setConstraintCheckWhen("cwImmediate");
        edtCEP.setConstraintCheckType("ctExpression");
        edtCEP.setConstraintFocusOnError(false);
        edtCEP.setConstraintEnableUI(true);
        edtCEP.setConstraintEnabled(false);
        edtCEP.setConstraintFormCheck(true);
        edtCEP.setCharCase("ccNormal");
        edtCEP.setPwd(false);
        edtCEP.setMaxlength(8);
        edtCEP.setFontColor("clWindowText");
        edtCEP.setFontSize(-13);
        edtCEP.setFontName("Tahoma");
        edtCEP.setFontStyle("[]");
        edtCEP.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCEPFrmPesquisaCEPOnlineEnter(event);
            processarFlow("FrmPesquisaCEPOnline", "edtCEP", "OnEnter");
        });
        edtCEP.setSaveLiteralCharacter(false);
        edtCEP.applyProperties();
        vBoxCEPAPesquisar.addChildren(edtCEP);
        addValidatable(edtCEP);
    }

    public TFHBox hBoxBtnPesquisarCEP = new TFHBox();

    private void init_hBoxBtnPesquisarCEP() {
        hBoxBtnPesquisarCEP.setName("hBoxBtnPesquisarCEP");
        hBoxBtnPesquisarCEP.setLeft(130);
        hBoxBtnPesquisarCEP.setTop(0);
        hBoxBtnPesquisarCEP.setWidth(30);
        hBoxBtnPesquisarCEP.setHeight(30);
        hBoxBtnPesquisarCEP.setBorderStyle("stNone");
        hBoxBtnPesquisarCEP.setPaddingTop(0);
        hBoxBtnPesquisarCEP.setPaddingLeft(0);
        hBoxBtnPesquisarCEP.setPaddingRight(0);
        hBoxBtnPesquisarCEP.setPaddingBottom(0);
        hBoxBtnPesquisarCEP.setMarginTop(20);
        hBoxBtnPesquisarCEP.setMarginLeft(0);
        hBoxBtnPesquisarCEP.setMarginRight(0);
        hBoxBtnPesquisarCEP.setMarginBottom(0);
        hBoxBtnPesquisarCEP.setSpacing(5);
        hBoxBtnPesquisarCEP.setFlexVflex("ftMin");
        hBoxBtnPesquisarCEP.setFlexHflex("ftMin");
        hBoxBtnPesquisarCEP.setScrollable(false);
        hBoxBtnPesquisarCEP.setBoxShadowConfigHorizontalLength(10);
        hBoxBtnPesquisarCEP.setBoxShadowConfigVerticalLength(10);
        hBoxBtnPesquisarCEP.setBoxShadowConfigBlurRadius(5);
        hBoxBtnPesquisarCEP.setBoxShadowConfigSpreadRadius(0);
        hBoxBtnPesquisarCEP.setBoxShadowConfigShadowColor("clBlack");
        hBoxBtnPesquisarCEP.setBoxShadowConfigOpacity(75);
        hBoxBtnPesquisarCEP.setVAlign("tvTop");
        hBoxPesquisa.addChildren(hBoxBtnPesquisarCEP);
        hBoxBtnPesquisarCEP.applyProperties();
    }

    public TFButton btnPesquisarCEP = new TFButton();

    private void init_btnPesquisarCEP() {
        btnPesquisarCEP.setName("btnPesquisarCEP");
        btnPesquisarCEP.setLeft(0);
        btnPesquisarCEP.setTop(0);
        btnPesquisarCEP.setWidth(25);
        btnPesquisarCEP.setHeight(25);
        btnPesquisarCEP.setHint("Pesquisar CEP");
        btnPesquisarCEP.setFontColor("clWindowText");
        btnPesquisarCEP.setFontSize(-11);
        btnPesquisarCEP.setFontName("Tahoma");
        btnPesquisarCEP.setFontStyle("[]");
        btnPesquisarCEP.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarCEPFrmPesquisaCEPOnlineClick(event);
            processarFlow("FrmPesquisaCEPOnline", "btnPesquisarCEP", "OnClick");
        });
        btnPesquisarCEP.setImageId(0);
        btnPesquisarCEP.setColor("clBtnFace");
        btnPesquisarCEP.setAccess(false);
        btnPesquisarCEP.setIconClass("search");
        btnPesquisarCEP.setIconReverseDirection(false);
        hBoxBtnPesquisarCEP.addChildren(btnPesquisarCEP);
        btnPesquisarCEP.applyProperties();
    }

    public TFVBox vBoxDadosDoCEP = new TFVBox();

    private void init_vBoxDadosDoCEP() {
        vBoxDadosDoCEP.setName("vBoxDadosDoCEP");
        vBoxDadosDoCEP.setLeft(0);
        vBoxDadosDoCEP.setTop(123);
        vBoxDadosDoCEP.setWidth(370);
        vBoxDadosDoCEP.setHeight(361);
        vBoxDadosDoCEP.setBorderStyle("stNone");
        vBoxDadosDoCEP.setPaddingTop(0);
        vBoxDadosDoCEP.setPaddingLeft(0);
        vBoxDadosDoCEP.setPaddingRight(0);
        vBoxDadosDoCEP.setPaddingBottom(0);
        vBoxDadosDoCEP.setMarginTop(0);
        vBoxDadosDoCEP.setMarginLeft(0);
        vBoxDadosDoCEP.setMarginRight(0);
        vBoxDadosDoCEP.setMarginBottom(0);
        vBoxDadosDoCEP.setSpacing(1);
        vBoxDadosDoCEP.setFlexVflex("ftMin");
        vBoxDadosDoCEP.setFlexHflex("ftTrue");
        vBoxDadosDoCEP.setScrollable(false);
        vBoxDadosDoCEP.setBoxShadowConfigHorizontalLength(10);
        vBoxDadosDoCEP.setBoxShadowConfigVerticalLength(10);
        vBoxDadosDoCEP.setBoxShadowConfigBlurRadius(5);
        vBoxDadosDoCEP.setBoxShadowConfigSpreadRadius(0);
        vBoxDadosDoCEP.setBoxShadowConfigShadowColor("clBlack");
        vBoxDadosDoCEP.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxDadosDoCEP);
        vBoxDadosDoCEP.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPCabecalho = new TFHBox();

    private void init_hBoxDadosDoCEPCabecalho() {
        hBoxDadosDoCEPCabecalho.setName("hBoxDadosDoCEPCabecalho");
        hBoxDadosDoCEPCabecalho.setLeft(0);
        hBoxDadosDoCEPCabecalho.setTop(0);
        hBoxDadosDoCEPCabecalho.setWidth(360);
        hBoxDadosDoCEPCabecalho.setHeight(25);
        hBoxDadosDoCEPCabecalho.setBorderStyle("stNone");
        hBoxDadosDoCEPCabecalho.setPaddingTop(0);
        hBoxDadosDoCEPCabecalho.setPaddingLeft(0);
        hBoxDadosDoCEPCabecalho.setPaddingRight(0);
        hBoxDadosDoCEPCabecalho.setPaddingBottom(0);
        hBoxDadosDoCEPCabecalho.setMarginTop(0);
        hBoxDadosDoCEPCabecalho.setMarginLeft(0);
        hBoxDadosDoCEPCabecalho.setMarginRight(0);
        hBoxDadosDoCEPCabecalho.setMarginBottom(0);
        hBoxDadosDoCEPCabecalho.setSpacing(5);
        hBoxDadosDoCEPCabecalho.setFlexVflex("ftMin");
        hBoxDadosDoCEPCabecalho.setFlexHflex("ftTrue");
        hBoxDadosDoCEPCabecalho.setScrollable(false);
        hBoxDadosDoCEPCabecalho.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPCabecalho.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPCabecalho.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPCabecalho.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPCabecalho.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPCabecalho.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPCabecalho.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxDadosDoCEPCabecalho);
        hBoxDadosDoCEPCabecalho.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPCabecalhoTipo = new TFHBox();

    private void init_hBoxDadosDoCEPCabecalhoTipo() {
        hBoxDadosDoCEPCabecalhoTipo.setName("hBoxDadosDoCEPCabecalhoTipo");
        hBoxDadosDoCEPCabecalhoTipo.setLeft(0);
        hBoxDadosDoCEPCabecalhoTipo.setTop(0);
        hBoxDadosDoCEPCabecalhoTipo.setWidth(80);
        hBoxDadosDoCEPCabecalhoTipo.setHeight(20);
        hBoxDadosDoCEPCabecalhoTipo.setBorderStyle("stNone");
        hBoxDadosDoCEPCabecalhoTipo.setPaddingTop(0);
        hBoxDadosDoCEPCabecalhoTipo.setPaddingLeft(0);
        hBoxDadosDoCEPCabecalhoTipo.setPaddingRight(0);
        hBoxDadosDoCEPCabecalhoTipo.setPaddingBottom(0);
        hBoxDadosDoCEPCabecalhoTipo.setMarginTop(0);
        hBoxDadosDoCEPCabecalhoTipo.setMarginLeft(0);
        hBoxDadosDoCEPCabecalhoTipo.setMarginRight(0);
        hBoxDadosDoCEPCabecalhoTipo.setMarginBottom(0);
        hBoxDadosDoCEPCabecalhoTipo.setSpacing(1);
        hBoxDadosDoCEPCabecalhoTipo.setFlexVflex("ftMin");
        hBoxDadosDoCEPCabecalhoTipo.setFlexHflex("ftFalse");
        hBoxDadosDoCEPCabecalhoTipo.setScrollable(false);
        hBoxDadosDoCEPCabecalhoTipo.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPCabecalhoTipo.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPCabecalhoTipo.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPCabecalhoTipo.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPCabecalhoTipo.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPCabecalhoTipo.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPCabecalhoTipo.setVAlign("tvTop");
        hBoxDadosDoCEPCabecalho.addChildren(hBoxDadosDoCEPCabecalhoTipo);
        hBoxDadosDoCEPCabecalhoTipo.applyProperties();
    }

    public TFLabel lblTipoCabecalho = new TFLabel();

    private void init_lblTipoCabecalho() {
        lblTipoCabecalho.setName("lblTipoCabecalho");
        lblTipoCabecalho.setLeft(0);
        lblTipoCabecalho.setTop(0);
        lblTipoCabecalho.setWidth(26);
        lblTipoCabecalho.setHeight(14);
        lblTipoCabecalho.setCaption("Tipo");
        lblTipoCabecalho.setFontColor("clWindowText");
        lblTipoCabecalho.setFontSize(-12);
        lblTipoCabecalho.setFontName("Tahoma");
        lblTipoCabecalho.setFontStyle("[fsBold]");
        lblTipoCabecalho.setVerticalAlignment("taVerticalCenter");
        lblTipoCabecalho.setWordBreak(false);
        hBoxDadosDoCEPCabecalhoTipo.addChildren(lblTipoCabecalho);
        lblTipoCabecalho.applyProperties();
    }

    public TFLabel lblValorCabecalho = new TFLabel();

    private void init_lblValorCabecalho() {
        lblValorCabecalho.setName("lblValorCabecalho");
        lblValorCabecalho.setLeft(80);
        lblValorCabecalho.setTop(0);
        lblValorCabecalho.setWidth(31);
        lblValorCabecalho.setHeight(14);
        lblValorCabecalho.setCaption("Valor");
        lblValorCabecalho.setFontColor("clWindowText");
        lblValorCabecalho.setFontSize(-12);
        lblValorCabecalho.setFontName("Tahoma");
        lblValorCabecalho.setFontStyle("[fsBold]");
        lblValorCabecalho.setVerticalAlignment("taVerticalCenter");
        lblValorCabecalho.setWordBreak(false);
        hBoxDadosDoCEPCabecalho.addChildren(lblValorCabecalho);
        lblValorCabecalho.applyProperties();
    }

    public TFHBox hBoxEnderecosCEP = new TFHBox();

    private void init_hBoxEnderecosCEP() {
        hBoxEnderecosCEP.setName("hBoxEnderecosCEP");
        hBoxEnderecosCEP.setLeft(0);
        hBoxEnderecosCEP.setTop(26);
        hBoxEnderecosCEP.setWidth(360);
        hBoxEnderecosCEP.setHeight(25);
        hBoxEnderecosCEP.setBorderStyle("stNone");
        hBoxEnderecosCEP.setPaddingTop(0);
        hBoxEnderecosCEP.setPaddingLeft(0);
        hBoxEnderecosCEP.setPaddingRight(0);
        hBoxEnderecosCEP.setPaddingBottom(0);
        hBoxEnderecosCEP.setMarginTop(0);
        hBoxEnderecosCEP.setMarginLeft(0);
        hBoxEnderecosCEP.setMarginRight(0);
        hBoxEnderecosCEP.setMarginBottom(0);
        hBoxEnderecosCEP.setSpacing(5);
        hBoxEnderecosCEP.setFlexVflex("ftMin");
        hBoxEnderecosCEP.setFlexHflex("ftTrue");
        hBoxEnderecosCEP.setScrollable(false);
        hBoxEnderecosCEP.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosCEP.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosCEP.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosCEP.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosCEP.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosCEP.setBoxShadowConfigOpacity(75);
        hBoxEnderecosCEP.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosCEP);
        hBoxEnderecosCEP.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoCEP = new TFHBox();

    private void init_hBoxDadosDoCEPTipoCEP() {
        hBoxDadosDoCEPTipoCEP.setName("hBoxDadosDoCEPTipoCEP");
        hBoxDadosDoCEPTipoCEP.setLeft(0);
        hBoxDadosDoCEPTipoCEP.setTop(0);
        hBoxDadosDoCEPTipoCEP.setWidth(80);
        hBoxDadosDoCEPTipoCEP.setHeight(20);
        hBoxDadosDoCEPTipoCEP.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoCEP.setPaddingTop(0);
        hBoxDadosDoCEPTipoCEP.setPaddingLeft(0);
        hBoxDadosDoCEPTipoCEP.setPaddingRight(0);
        hBoxDadosDoCEPTipoCEP.setPaddingBottom(0);
        hBoxDadosDoCEPTipoCEP.setMarginTop(0);
        hBoxDadosDoCEPTipoCEP.setMarginLeft(0);
        hBoxDadosDoCEPTipoCEP.setMarginRight(0);
        hBoxDadosDoCEPTipoCEP.setMarginBottom(0);
        hBoxDadosDoCEPTipoCEP.setSpacing(1);
        hBoxDadosDoCEPTipoCEP.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoCEP.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoCEP.setScrollable(false);
        hBoxDadosDoCEPTipoCEP.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoCEP.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoCEP.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoCEP.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoCEP.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoCEP.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoCEP.setVAlign("tvTop");
        hBoxEnderecosCEP.addChildren(hBoxDadosDoCEPTipoCEP);
        hBoxDadosDoCEPTipoCEP.applyProperties();
    }

    public TFLabel lblTipoCEP = new TFLabel();

    private void init_lblTipoCEP() {
        lblTipoCEP.setName("lblTipoCEP");
        lblTipoCEP.setLeft(0);
        lblTipoCEP.setTop(0);
        lblTipoCEP.setWidth(19);
        lblTipoCEP.setHeight(13);
        lblTipoCEP.setCaption("CEP");
        lblTipoCEP.setFontColor("clWindowText");
        lblTipoCEP.setFontSize(-11);
        lblTipoCEP.setFontName("Tahoma");
        lblTipoCEP.setFontStyle("[]");
        lblTipoCEP.setVerticalAlignment("taVerticalCenter");
        lblTipoCEP.setWordBreak(false);
        hBoxDadosDoCEPTipoCEP.addChildren(lblTipoCEP);
        lblTipoCEP.applyProperties();
    }

    public TFLabel lblValorCEP = new TFLabel();

    private void init_lblValorCEP() {
        lblValorCEP.setName("lblValorCEP");
        lblValorCEP.setLeft(80);
        lblValorCEP.setTop(0);
        lblValorCEP.setWidth(19);
        lblValorCEP.setHeight(13);
        lblValorCEP.setCaption("CEP");
        lblValorCEP.setColor("clBtnFace");
        lblValorCEP.setFontColor("clWindowText");
        lblValorCEP.setFontSize(-11);
        lblValorCEP.setFontName("Tahoma");
        lblValorCEP.setFontStyle("[]");
        lblValorCEP.setVisible(false);
        lblValorCEP.setVerticalAlignment("taVerticalCenter");
        lblValorCEP.setWordBreak(false);
        hBoxEnderecosCEP.addChildren(lblValorCEP);
        lblValorCEP.applyProperties();
    }

    public TFHBox hBoxEnderecosLogradouro = new TFHBox();

    private void init_hBoxEnderecosLogradouro() {
        hBoxEnderecosLogradouro.setName("hBoxEnderecosLogradouro");
        hBoxEnderecosLogradouro.setLeft(0);
        hBoxEnderecosLogradouro.setTop(52);
        hBoxEnderecosLogradouro.setWidth(360);
        hBoxEnderecosLogradouro.setHeight(25);
        hBoxEnderecosLogradouro.setBorderStyle("stNone");
        hBoxEnderecosLogradouro.setPaddingTop(0);
        hBoxEnderecosLogradouro.setPaddingLeft(0);
        hBoxEnderecosLogradouro.setPaddingRight(0);
        hBoxEnderecosLogradouro.setPaddingBottom(0);
        hBoxEnderecosLogradouro.setMarginTop(0);
        hBoxEnderecosLogradouro.setMarginLeft(0);
        hBoxEnderecosLogradouro.setMarginRight(0);
        hBoxEnderecosLogradouro.setMarginBottom(0);
        hBoxEnderecosLogradouro.setSpacing(5);
        hBoxEnderecosLogradouro.setFlexVflex("ftMin");
        hBoxEnderecosLogradouro.setFlexHflex("ftTrue");
        hBoxEnderecosLogradouro.setScrollable(false);
        hBoxEnderecosLogradouro.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosLogradouro.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosLogradouro.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosLogradouro.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosLogradouro.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosLogradouro.setBoxShadowConfigOpacity(75);
        hBoxEnderecosLogradouro.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosLogradouro);
        hBoxEnderecosLogradouro.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoLogradouro = new TFHBox();

    private void init_hBoxDadosDoCEPTipoLogradouro() {
        hBoxDadosDoCEPTipoLogradouro.setName("hBoxDadosDoCEPTipoLogradouro");
        hBoxDadosDoCEPTipoLogradouro.setLeft(0);
        hBoxDadosDoCEPTipoLogradouro.setTop(0);
        hBoxDadosDoCEPTipoLogradouro.setWidth(80);
        hBoxDadosDoCEPTipoLogradouro.setHeight(20);
        hBoxDadosDoCEPTipoLogradouro.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoLogradouro.setPaddingTop(0);
        hBoxDadosDoCEPTipoLogradouro.setPaddingLeft(0);
        hBoxDadosDoCEPTipoLogradouro.setPaddingRight(0);
        hBoxDadosDoCEPTipoLogradouro.setPaddingBottom(0);
        hBoxDadosDoCEPTipoLogradouro.setMarginTop(0);
        hBoxDadosDoCEPTipoLogradouro.setMarginLeft(0);
        hBoxDadosDoCEPTipoLogradouro.setMarginRight(0);
        hBoxDadosDoCEPTipoLogradouro.setMarginBottom(0);
        hBoxDadosDoCEPTipoLogradouro.setSpacing(1);
        hBoxDadosDoCEPTipoLogradouro.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoLogradouro.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoLogradouro.setScrollable(false);
        hBoxDadosDoCEPTipoLogradouro.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoLogradouro.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoLogradouro.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoLogradouro.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoLogradouro.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoLogradouro.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoLogradouro.setVAlign("tvTop");
        hBoxEnderecosLogradouro.addChildren(hBoxDadosDoCEPTipoLogradouro);
        hBoxDadosDoCEPTipoLogradouro.applyProperties();
    }

    public TFLabel lblTipoLogradouro = new TFLabel();

    private void init_lblTipoLogradouro() {
        lblTipoLogradouro.setName("lblTipoLogradouro");
        lblTipoLogradouro.setLeft(0);
        lblTipoLogradouro.setTop(0);
        lblTipoLogradouro.setWidth(55);
        lblTipoLogradouro.setHeight(13);
        lblTipoLogradouro.setCaption("Logradouro");
        lblTipoLogradouro.setFontColor("clWindowText");
        lblTipoLogradouro.setFontSize(-11);
        lblTipoLogradouro.setFontName("Tahoma");
        lblTipoLogradouro.setFontStyle("[]");
        lblTipoLogradouro.setVerticalAlignment("taVerticalCenter");
        lblTipoLogradouro.setWordBreak(false);
        hBoxDadosDoCEPTipoLogradouro.addChildren(lblTipoLogradouro);
        lblTipoLogradouro.applyProperties();
    }

    public TFLabel lblValorLogradouro = new TFLabel();

    private void init_lblValorLogradouro() {
        lblValorLogradouro.setName("lblValorLogradouro");
        lblValorLogradouro.setLeft(80);
        lblValorLogradouro.setTop(0);
        lblValorLogradouro.setWidth(55);
        lblValorLogradouro.setHeight(13);
        lblValorLogradouro.setCaption("Logradouro");
        lblValorLogradouro.setColor("clBtnFace");
        lblValorLogradouro.setFontColor("clWindowText");
        lblValorLogradouro.setFontSize(-11);
        lblValorLogradouro.setFontName("Tahoma");
        lblValorLogradouro.setFontStyle("[]");
        lblValorLogradouro.setVisible(false);
        lblValorLogradouro.setVerticalAlignment("taVerticalCenter");
        lblValorLogradouro.setWordBreak(false);
        hBoxEnderecosLogradouro.addChildren(lblValorLogradouro);
        lblValorLogradouro.applyProperties();
    }

    public TFHBox hBoxEnderecosComplemento = new TFHBox();

    private void init_hBoxEnderecosComplemento() {
        hBoxEnderecosComplemento.setName("hBoxEnderecosComplemento");
        hBoxEnderecosComplemento.setLeft(0);
        hBoxEnderecosComplemento.setTop(78);
        hBoxEnderecosComplemento.setWidth(360);
        hBoxEnderecosComplemento.setHeight(25);
        hBoxEnderecosComplemento.setBorderStyle("stNone");
        hBoxEnderecosComplemento.setPaddingTop(0);
        hBoxEnderecosComplemento.setPaddingLeft(0);
        hBoxEnderecosComplemento.setPaddingRight(0);
        hBoxEnderecosComplemento.setPaddingBottom(0);
        hBoxEnderecosComplemento.setMarginTop(0);
        hBoxEnderecosComplemento.setMarginLeft(0);
        hBoxEnderecosComplemento.setMarginRight(0);
        hBoxEnderecosComplemento.setMarginBottom(0);
        hBoxEnderecosComplemento.setSpacing(5);
        hBoxEnderecosComplemento.setFlexVflex("ftMin");
        hBoxEnderecosComplemento.setFlexHflex("ftTrue");
        hBoxEnderecosComplemento.setScrollable(false);
        hBoxEnderecosComplemento.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosComplemento.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosComplemento.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosComplemento.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosComplemento.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosComplemento.setBoxShadowConfigOpacity(75);
        hBoxEnderecosComplemento.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosComplemento);
        hBoxEnderecosComplemento.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoComplemento = new TFHBox();

    private void init_hBoxDadosDoCEPTipoComplemento() {
        hBoxDadosDoCEPTipoComplemento.setName("hBoxDadosDoCEPTipoComplemento");
        hBoxDadosDoCEPTipoComplemento.setLeft(0);
        hBoxDadosDoCEPTipoComplemento.setTop(0);
        hBoxDadosDoCEPTipoComplemento.setWidth(80);
        hBoxDadosDoCEPTipoComplemento.setHeight(20);
        hBoxDadosDoCEPTipoComplemento.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoComplemento.setPaddingTop(0);
        hBoxDadosDoCEPTipoComplemento.setPaddingLeft(0);
        hBoxDadosDoCEPTipoComplemento.setPaddingRight(0);
        hBoxDadosDoCEPTipoComplemento.setPaddingBottom(0);
        hBoxDadosDoCEPTipoComplemento.setMarginTop(0);
        hBoxDadosDoCEPTipoComplemento.setMarginLeft(0);
        hBoxDadosDoCEPTipoComplemento.setMarginRight(0);
        hBoxDadosDoCEPTipoComplemento.setMarginBottom(0);
        hBoxDadosDoCEPTipoComplemento.setSpacing(1);
        hBoxDadosDoCEPTipoComplemento.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoComplemento.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoComplemento.setScrollable(false);
        hBoxDadosDoCEPTipoComplemento.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoComplemento.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoComplemento.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoComplemento.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoComplemento.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoComplemento.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoComplemento.setVAlign("tvTop");
        hBoxEnderecosComplemento.addChildren(hBoxDadosDoCEPTipoComplemento);
        hBoxDadosDoCEPTipoComplemento.applyProperties();
    }

    public TFLabel lblTipoComplemento = new TFLabel();

    private void init_lblTipoComplemento() {
        lblTipoComplemento.setName("lblTipoComplemento");
        lblTipoComplemento.setLeft(0);
        lblTipoComplemento.setTop(0);
        lblTipoComplemento.setWidth(65);
        lblTipoComplemento.setHeight(13);
        lblTipoComplemento.setCaption("Complemento");
        lblTipoComplemento.setFontColor("clWindowText");
        lblTipoComplemento.setFontSize(-11);
        lblTipoComplemento.setFontName("Tahoma");
        lblTipoComplemento.setFontStyle("[]");
        lblTipoComplemento.setVerticalAlignment("taVerticalCenter");
        lblTipoComplemento.setWordBreak(false);
        hBoxDadosDoCEPTipoComplemento.addChildren(lblTipoComplemento);
        lblTipoComplemento.applyProperties();
    }

    public TFLabel lblValorComplemento = new TFLabel();

    private void init_lblValorComplemento() {
        lblValorComplemento.setName("lblValorComplemento");
        lblValorComplemento.setLeft(80);
        lblValorComplemento.setTop(0);
        lblValorComplemento.setWidth(65);
        lblValorComplemento.setHeight(13);
        lblValorComplemento.setCaption("Complemento");
        lblValorComplemento.setColor("clBtnFace");
        lblValorComplemento.setFontColor("clWindowText");
        lblValorComplemento.setFontSize(-11);
        lblValorComplemento.setFontName("Tahoma");
        lblValorComplemento.setFontStyle("[]");
        lblValorComplemento.setVisible(false);
        lblValorComplemento.setVerticalAlignment("taVerticalCenter");
        lblValorComplemento.setWordBreak(false);
        hBoxEnderecosComplemento.addChildren(lblValorComplemento);
        lblValorComplemento.applyProperties();
    }

    public TFHBox hBoxEnderecosUnidade = new TFHBox();

    private void init_hBoxEnderecosUnidade() {
        hBoxEnderecosUnidade.setName("hBoxEnderecosUnidade");
        hBoxEnderecosUnidade.setLeft(0);
        hBoxEnderecosUnidade.setTop(104);
        hBoxEnderecosUnidade.setWidth(360);
        hBoxEnderecosUnidade.setHeight(25);
        hBoxEnderecosUnidade.setBorderStyle("stNone");
        hBoxEnderecosUnidade.setPaddingTop(0);
        hBoxEnderecosUnidade.setPaddingLeft(0);
        hBoxEnderecosUnidade.setPaddingRight(0);
        hBoxEnderecosUnidade.setPaddingBottom(0);
        hBoxEnderecosUnidade.setMarginTop(0);
        hBoxEnderecosUnidade.setMarginLeft(0);
        hBoxEnderecosUnidade.setMarginRight(0);
        hBoxEnderecosUnidade.setMarginBottom(0);
        hBoxEnderecosUnidade.setSpacing(5);
        hBoxEnderecosUnidade.setFlexVflex("ftMin");
        hBoxEnderecosUnidade.setFlexHflex("ftTrue");
        hBoxEnderecosUnidade.setScrollable(false);
        hBoxEnderecosUnidade.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosUnidade.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosUnidade.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosUnidade.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosUnidade.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosUnidade.setBoxShadowConfigOpacity(75);
        hBoxEnderecosUnidade.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosUnidade);
        hBoxEnderecosUnidade.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoUnidade = new TFHBox();

    private void init_hBoxDadosDoCEPTipoUnidade() {
        hBoxDadosDoCEPTipoUnidade.setName("hBoxDadosDoCEPTipoUnidade");
        hBoxDadosDoCEPTipoUnidade.setLeft(0);
        hBoxDadosDoCEPTipoUnidade.setTop(0);
        hBoxDadosDoCEPTipoUnidade.setWidth(80);
        hBoxDadosDoCEPTipoUnidade.setHeight(20);
        hBoxDadosDoCEPTipoUnidade.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoUnidade.setPaddingTop(0);
        hBoxDadosDoCEPTipoUnidade.setPaddingLeft(0);
        hBoxDadosDoCEPTipoUnidade.setPaddingRight(0);
        hBoxDadosDoCEPTipoUnidade.setPaddingBottom(0);
        hBoxDadosDoCEPTipoUnidade.setMarginTop(0);
        hBoxDadosDoCEPTipoUnidade.setMarginLeft(0);
        hBoxDadosDoCEPTipoUnidade.setMarginRight(0);
        hBoxDadosDoCEPTipoUnidade.setMarginBottom(0);
        hBoxDadosDoCEPTipoUnidade.setSpacing(1);
        hBoxDadosDoCEPTipoUnidade.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoUnidade.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoUnidade.setScrollable(false);
        hBoxDadosDoCEPTipoUnidade.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoUnidade.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoUnidade.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoUnidade.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoUnidade.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoUnidade.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoUnidade.setVAlign("tvTop");
        hBoxEnderecosUnidade.addChildren(hBoxDadosDoCEPTipoUnidade);
        hBoxDadosDoCEPTipoUnidade.applyProperties();
    }

    public TFLabel lblTipoUnidade = new TFLabel();

    private void init_lblTipoUnidade() {
        lblTipoUnidade.setName("lblTipoUnidade");
        lblTipoUnidade.setLeft(0);
        lblTipoUnidade.setTop(0);
        lblTipoUnidade.setWidth(39);
        lblTipoUnidade.setHeight(13);
        lblTipoUnidade.setCaption("Unidade");
        lblTipoUnidade.setFontColor("clWindowText");
        lblTipoUnidade.setFontSize(-11);
        lblTipoUnidade.setFontName("Tahoma");
        lblTipoUnidade.setFontStyle("[]");
        lblTipoUnidade.setVerticalAlignment("taVerticalCenter");
        lblTipoUnidade.setWordBreak(false);
        hBoxDadosDoCEPTipoUnidade.addChildren(lblTipoUnidade);
        lblTipoUnidade.applyProperties();
    }

    public TFLabel lblValorUnidade = new TFLabel();

    private void init_lblValorUnidade() {
        lblValorUnidade.setName("lblValorUnidade");
        lblValorUnidade.setLeft(80);
        lblValorUnidade.setTop(0);
        lblValorUnidade.setWidth(39);
        lblValorUnidade.setHeight(13);
        lblValorUnidade.setCaption("Unidade");
        lblValorUnidade.setColor("clBtnFace");
        lblValorUnidade.setFontColor("clWindowText");
        lblValorUnidade.setFontSize(-11);
        lblValorUnidade.setFontName("Tahoma");
        lblValorUnidade.setFontStyle("[]");
        lblValorUnidade.setVisible(false);
        lblValorUnidade.setVerticalAlignment("taVerticalCenter");
        lblValorUnidade.setWordBreak(false);
        hBoxEnderecosUnidade.addChildren(lblValorUnidade);
        lblValorUnidade.applyProperties();
    }

    public TFHBox hBoxEnderecosBairro = new TFHBox();

    private void init_hBoxEnderecosBairro() {
        hBoxEnderecosBairro.setName("hBoxEnderecosBairro");
        hBoxEnderecosBairro.setLeft(0);
        hBoxEnderecosBairro.setTop(130);
        hBoxEnderecosBairro.setWidth(360);
        hBoxEnderecosBairro.setHeight(25);
        hBoxEnderecosBairro.setBorderStyle("stNone");
        hBoxEnderecosBairro.setPaddingTop(0);
        hBoxEnderecosBairro.setPaddingLeft(0);
        hBoxEnderecosBairro.setPaddingRight(0);
        hBoxEnderecosBairro.setPaddingBottom(0);
        hBoxEnderecosBairro.setMarginTop(0);
        hBoxEnderecosBairro.setMarginLeft(0);
        hBoxEnderecosBairro.setMarginRight(0);
        hBoxEnderecosBairro.setMarginBottom(0);
        hBoxEnderecosBairro.setSpacing(5);
        hBoxEnderecosBairro.setFlexVflex("ftMin");
        hBoxEnderecosBairro.setFlexHflex("ftTrue");
        hBoxEnderecosBairro.setScrollable(false);
        hBoxEnderecosBairro.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosBairro.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosBairro.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosBairro.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosBairro.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosBairro.setBoxShadowConfigOpacity(75);
        hBoxEnderecosBairro.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosBairro);
        hBoxEnderecosBairro.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoBairro = new TFHBox();

    private void init_hBoxDadosDoCEPTipoBairro() {
        hBoxDadosDoCEPTipoBairro.setName("hBoxDadosDoCEPTipoBairro");
        hBoxDadosDoCEPTipoBairro.setLeft(0);
        hBoxDadosDoCEPTipoBairro.setTop(0);
        hBoxDadosDoCEPTipoBairro.setWidth(80);
        hBoxDadosDoCEPTipoBairro.setHeight(20);
        hBoxDadosDoCEPTipoBairro.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoBairro.setPaddingTop(0);
        hBoxDadosDoCEPTipoBairro.setPaddingLeft(0);
        hBoxDadosDoCEPTipoBairro.setPaddingRight(0);
        hBoxDadosDoCEPTipoBairro.setPaddingBottom(0);
        hBoxDadosDoCEPTipoBairro.setMarginTop(0);
        hBoxDadosDoCEPTipoBairro.setMarginLeft(0);
        hBoxDadosDoCEPTipoBairro.setMarginRight(0);
        hBoxDadosDoCEPTipoBairro.setMarginBottom(0);
        hBoxDadosDoCEPTipoBairro.setSpacing(1);
        hBoxDadosDoCEPTipoBairro.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoBairro.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoBairro.setScrollable(false);
        hBoxDadosDoCEPTipoBairro.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoBairro.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoBairro.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoBairro.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoBairro.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoBairro.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoBairro.setVAlign("tvTop");
        hBoxEnderecosBairro.addChildren(hBoxDadosDoCEPTipoBairro);
        hBoxDadosDoCEPTipoBairro.applyProperties();
    }

    public TFLabel lblTipoBairro = new TFLabel();

    private void init_lblTipoBairro() {
        lblTipoBairro.setName("lblTipoBairro");
        lblTipoBairro.setLeft(0);
        lblTipoBairro.setTop(0);
        lblTipoBairro.setWidth(28);
        lblTipoBairro.setHeight(13);
        lblTipoBairro.setCaption("Bairro");
        lblTipoBairro.setFontColor("clWindowText");
        lblTipoBairro.setFontSize(-11);
        lblTipoBairro.setFontName("Tahoma");
        lblTipoBairro.setFontStyle("[]");
        lblTipoBairro.setVerticalAlignment("taVerticalCenter");
        lblTipoBairro.setWordBreak(false);
        hBoxDadosDoCEPTipoBairro.addChildren(lblTipoBairro);
        lblTipoBairro.applyProperties();
    }

    public TFLabel lblValorBairro = new TFLabel();

    private void init_lblValorBairro() {
        lblValorBairro.setName("lblValorBairro");
        lblValorBairro.setLeft(80);
        lblValorBairro.setTop(0);
        lblValorBairro.setWidth(28);
        lblValorBairro.setHeight(13);
        lblValorBairro.setCaption("Bairro");
        lblValorBairro.setColor("clBtnFace");
        lblValorBairro.setFontColor("clWindowText");
        lblValorBairro.setFontSize(-11);
        lblValorBairro.setFontName("Tahoma");
        lblValorBairro.setFontStyle("[]");
        lblValorBairro.setVisible(false);
        lblValorBairro.setVerticalAlignment("taVerticalCenter");
        lblValorBairro.setWordBreak(false);
        hBoxEnderecosBairro.addChildren(lblValorBairro);
        lblValorBairro.applyProperties();
    }

    public TFHBox hBoxEnderecosLocalidade = new TFHBox();

    private void init_hBoxEnderecosLocalidade() {
        hBoxEnderecosLocalidade.setName("hBoxEnderecosLocalidade");
        hBoxEnderecosLocalidade.setLeft(0);
        hBoxEnderecosLocalidade.setTop(156);
        hBoxEnderecosLocalidade.setWidth(360);
        hBoxEnderecosLocalidade.setHeight(25);
        hBoxEnderecosLocalidade.setBorderStyle("stNone");
        hBoxEnderecosLocalidade.setPaddingTop(0);
        hBoxEnderecosLocalidade.setPaddingLeft(0);
        hBoxEnderecosLocalidade.setPaddingRight(0);
        hBoxEnderecosLocalidade.setPaddingBottom(0);
        hBoxEnderecosLocalidade.setMarginTop(0);
        hBoxEnderecosLocalidade.setMarginLeft(0);
        hBoxEnderecosLocalidade.setMarginRight(0);
        hBoxEnderecosLocalidade.setMarginBottom(0);
        hBoxEnderecosLocalidade.setSpacing(5);
        hBoxEnderecosLocalidade.setFlexVflex("ftMin");
        hBoxEnderecosLocalidade.setFlexHflex("ftTrue");
        hBoxEnderecosLocalidade.setScrollable(false);
        hBoxEnderecosLocalidade.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosLocalidade.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosLocalidade.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosLocalidade.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosLocalidade.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosLocalidade.setBoxShadowConfigOpacity(75);
        hBoxEnderecosLocalidade.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosLocalidade);
        hBoxEnderecosLocalidade.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoLocalidade = new TFHBox();

    private void init_hBoxDadosDoCEPTipoLocalidade() {
        hBoxDadosDoCEPTipoLocalidade.setName("hBoxDadosDoCEPTipoLocalidade");
        hBoxDadosDoCEPTipoLocalidade.setLeft(0);
        hBoxDadosDoCEPTipoLocalidade.setTop(0);
        hBoxDadosDoCEPTipoLocalidade.setWidth(80);
        hBoxDadosDoCEPTipoLocalidade.setHeight(20);
        hBoxDadosDoCEPTipoLocalidade.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoLocalidade.setPaddingTop(0);
        hBoxDadosDoCEPTipoLocalidade.setPaddingLeft(0);
        hBoxDadosDoCEPTipoLocalidade.setPaddingRight(0);
        hBoxDadosDoCEPTipoLocalidade.setPaddingBottom(0);
        hBoxDadosDoCEPTipoLocalidade.setMarginTop(0);
        hBoxDadosDoCEPTipoLocalidade.setMarginLeft(0);
        hBoxDadosDoCEPTipoLocalidade.setMarginRight(0);
        hBoxDadosDoCEPTipoLocalidade.setMarginBottom(0);
        hBoxDadosDoCEPTipoLocalidade.setSpacing(1);
        hBoxDadosDoCEPTipoLocalidade.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoLocalidade.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoLocalidade.setScrollable(false);
        hBoxDadosDoCEPTipoLocalidade.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoLocalidade.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoLocalidade.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoLocalidade.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoLocalidade.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoLocalidade.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoLocalidade.setVAlign("tvTop");
        hBoxEnderecosLocalidade.addChildren(hBoxDadosDoCEPTipoLocalidade);
        hBoxDadosDoCEPTipoLocalidade.applyProperties();
    }

    public TFLabel lblTipoLocalidade = new TFLabel();

    private void init_lblTipoLocalidade() {
        lblTipoLocalidade.setName("lblTipoLocalidade");
        lblTipoLocalidade.setLeft(0);
        lblTipoLocalidade.setTop(0);
        lblTipoLocalidade.setWidth(50);
        lblTipoLocalidade.setHeight(13);
        lblTipoLocalidade.setCaption("Localidade");
        lblTipoLocalidade.setFontColor("clWindowText");
        lblTipoLocalidade.setFontSize(-11);
        lblTipoLocalidade.setFontName("Tahoma");
        lblTipoLocalidade.setFontStyle("[]");
        lblTipoLocalidade.setVerticalAlignment("taVerticalCenter");
        lblTipoLocalidade.setWordBreak(false);
        hBoxDadosDoCEPTipoLocalidade.addChildren(lblTipoLocalidade);
        lblTipoLocalidade.applyProperties();
    }

    public TFLabel lblValorLocalidade = new TFLabel();

    private void init_lblValorLocalidade() {
        lblValorLocalidade.setName("lblValorLocalidade");
        lblValorLocalidade.setLeft(80);
        lblValorLocalidade.setTop(0);
        lblValorLocalidade.setWidth(50);
        lblValorLocalidade.setHeight(13);
        lblValorLocalidade.setCaption("Localidade");
        lblValorLocalidade.setColor("clBtnFace");
        lblValorLocalidade.setFontColor("clWindowText");
        lblValorLocalidade.setFontSize(-11);
        lblValorLocalidade.setFontName("Tahoma");
        lblValorLocalidade.setFontStyle("[]");
        lblValorLocalidade.setVisible(false);
        lblValorLocalidade.setVerticalAlignment("taVerticalCenter");
        lblValorLocalidade.setWordBreak(false);
        hBoxEnderecosLocalidade.addChildren(lblValorLocalidade);
        lblValorLocalidade.applyProperties();
    }

    public TFHBox hBoxEnderecosUF = new TFHBox();

    private void init_hBoxEnderecosUF() {
        hBoxEnderecosUF.setName("hBoxEnderecosUF");
        hBoxEnderecosUF.setLeft(0);
        hBoxEnderecosUF.setTop(182);
        hBoxEnderecosUF.setWidth(360);
        hBoxEnderecosUF.setHeight(25);
        hBoxEnderecosUF.setBorderStyle("stNone");
        hBoxEnderecosUF.setPaddingTop(0);
        hBoxEnderecosUF.setPaddingLeft(0);
        hBoxEnderecosUF.setPaddingRight(0);
        hBoxEnderecosUF.setPaddingBottom(0);
        hBoxEnderecosUF.setMarginTop(0);
        hBoxEnderecosUF.setMarginLeft(0);
        hBoxEnderecosUF.setMarginRight(0);
        hBoxEnderecosUF.setMarginBottom(0);
        hBoxEnderecosUF.setSpacing(5);
        hBoxEnderecosUF.setFlexVflex("ftMin");
        hBoxEnderecosUF.setFlexHflex("ftTrue");
        hBoxEnderecosUF.setScrollable(false);
        hBoxEnderecosUF.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosUF.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosUF.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosUF.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosUF.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosUF.setBoxShadowConfigOpacity(75);
        hBoxEnderecosUF.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosUF);
        hBoxEnderecosUF.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoUF = new TFHBox();

    private void init_hBoxDadosDoCEPTipoUF() {
        hBoxDadosDoCEPTipoUF.setName("hBoxDadosDoCEPTipoUF");
        hBoxDadosDoCEPTipoUF.setLeft(0);
        hBoxDadosDoCEPTipoUF.setTop(0);
        hBoxDadosDoCEPTipoUF.setWidth(80);
        hBoxDadosDoCEPTipoUF.setHeight(20);
        hBoxDadosDoCEPTipoUF.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoUF.setPaddingTop(0);
        hBoxDadosDoCEPTipoUF.setPaddingLeft(0);
        hBoxDadosDoCEPTipoUF.setPaddingRight(0);
        hBoxDadosDoCEPTipoUF.setPaddingBottom(0);
        hBoxDadosDoCEPTipoUF.setMarginTop(0);
        hBoxDadosDoCEPTipoUF.setMarginLeft(0);
        hBoxDadosDoCEPTipoUF.setMarginRight(0);
        hBoxDadosDoCEPTipoUF.setMarginBottom(0);
        hBoxDadosDoCEPTipoUF.setSpacing(1);
        hBoxDadosDoCEPTipoUF.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoUF.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoUF.setScrollable(false);
        hBoxDadosDoCEPTipoUF.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoUF.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoUF.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoUF.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoUF.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoUF.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoUF.setVAlign("tvTop");
        hBoxEnderecosUF.addChildren(hBoxDadosDoCEPTipoUF);
        hBoxDadosDoCEPTipoUF.applyProperties();
    }

    public TFLabel lblTipoUF = new TFLabel();

    private void init_lblTipoUF() {
        lblTipoUF.setName("lblTipoUF");
        lblTipoUF.setLeft(0);
        lblTipoUF.setTop(0);
        lblTipoUF.setWidth(13);
        lblTipoUF.setHeight(13);
        lblTipoUF.setCaption("UF");
        lblTipoUF.setFontColor("clWindowText");
        lblTipoUF.setFontSize(-11);
        lblTipoUF.setFontName("Tahoma");
        lblTipoUF.setFontStyle("[]");
        lblTipoUF.setVerticalAlignment("taVerticalCenter");
        lblTipoUF.setWordBreak(false);
        hBoxDadosDoCEPTipoUF.addChildren(lblTipoUF);
        lblTipoUF.applyProperties();
    }

    public TFLabel lblValorUF = new TFLabel();

    private void init_lblValorUF() {
        lblValorUF.setName("lblValorUF");
        lblValorUF.setLeft(80);
        lblValorUF.setTop(0);
        lblValorUF.setWidth(13);
        lblValorUF.setHeight(13);
        lblValorUF.setCaption("UF");
        lblValorUF.setColor("clBtnFace");
        lblValorUF.setFontColor("clWindowText");
        lblValorUF.setFontSize(-11);
        lblValorUF.setFontName("Tahoma");
        lblValorUF.setFontStyle("[]");
        lblValorUF.setVisible(false);
        lblValorUF.setVerticalAlignment("taVerticalCenter");
        lblValorUF.setWordBreak(false);
        hBoxEnderecosUF.addChildren(lblValorUF);
        lblValorUF.applyProperties();
    }

    public TFHBox hBoxEnderecosEstado = new TFHBox();

    private void init_hBoxEnderecosEstado() {
        hBoxEnderecosEstado.setName("hBoxEnderecosEstado");
        hBoxEnderecosEstado.setLeft(0);
        hBoxEnderecosEstado.setTop(208);
        hBoxEnderecosEstado.setWidth(360);
        hBoxEnderecosEstado.setHeight(25);
        hBoxEnderecosEstado.setBorderStyle("stNone");
        hBoxEnderecosEstado.setPaddingTop(0);
        hBoxEnderecosEstado.setPaddingLeft(0);
        hBoxEnderecosEstado.setPaddingRight(0);
        hBoxEnderecosEstado.setPaddingBottom(0);
        hBoxEnderecosEstado.setMarginTop(0);
        hBoxEnderecosEstado.setMarginLeft(0);
        hBoxEnderecosEstado.setMarginRight(0);
        hBoxEnderecosEstado.setMarginBottom(0);
        hBoxEnderecosEstado.setSpacing(5);
        hBoxEnderecosEstado.setFlexVflex("ftMin");
        hBoxEnderecosEstado.setFlexHflex("ftTrue");
        hBoxEnderecosEstado.setScrollable(false);
        hBoxEnderecosEstado.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosEstado.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosEstado.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosEstado.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosEstado.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosEstado.setBoxShadowConfigOpacity(75);
        hBoxEnderecosEstado.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosEstado);
        hBoxEnderecosEstado.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoEstado = new TFHBox();

    private void init_hBoxDadosDoCEPTipoEstado() {
        hBoxDadosDoCEPTipoEstado.setName("hBoxDadosDoCEPTipoEstado");
        hBoxDadosDoCEPTipoEstado.setLeft(0);
        hBoxDadosDoCEPTipoEstado.setTop(0);
        hBoxDadosDoCEPTipoEstado.setWidth(80);
        hBoxDadosDoCEPTipoEstado.setHeight(20);
        hBoxDadosDoCEPTipoEstado.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoEstado.setPaddingTop(0);
        hBoxDadosDoCEPTipoEstado.setPaddingLeft(0);
        hBoxDadosDoCEPTipoEstado.setPaddingRight(0);
        hBoxDadosDoCEPTipoEstado.setPaddingBottom(0);
        hBoxDadosDoCEPTipoEstado.setMarginTop(0);
        hBoxDadosDoCEPTipoEstado.setMarginLeft(0);
        hBoxDadosDoCEPTipoEstado.setMarginRight(0);
        hBoxDadosDoCEPTipoEstado.setMarginBottom(0);
        hBoxDadosDoCEPTipoEstado.setSpacing(1);
        hBoxDadosDoCEPTipoEstado.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoEstado.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoEstado.setScrollable(false);
        hBoxDadosDoCEPTipoEstado.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoEstado.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoEstado.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoEstado.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoEstado.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoEstado.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoEstado.setVAlign("tvTop");
        hBoxEnderecosEstado.addChildren(hBoxDadosDoCEPTipoEstado);
        hBoxDadosDoCEPTipoEstado.applyProperties();
    }

    public TFLabel lblTipoEstado = new TFLabel();

    private void init_lblTipoEstado() {
        lblTipoEstado.setName("lblTipoEstado");
        lblTipoEstado.setLeft(0);
        lblTipoEstado.setTop(0);
        lblTipoEstado.setWidth(33);
        lblTipoEstado.setHeight(13);
        lblTipoEstado.setCaption("Estado");
        lblTipoEstado.setFontColor("clWindowText");
        lblTipoEstado.setFontSize(-11);
        lblTipoEstado.setFontName("Tahoma");
        lblTipoEstado.setFontStyle("[]");
        lblTipoEstado.setVerticalAlignment("taVerticalCenter");
        lblTipoEstado.setWordBreak(false);
        hBoxDadosDoCEPTipoEstado.addChildren(lblTipoEstado);
        lblTipoEstado.applyProperties();
    }

    public TFLabel lblValorEstado = new TFLabel();

    private void init_lblValorEstado() {
        lblValorEstado.setName("lblValorEstado");
        lblValorEstado.setLeft(80);
        lblValorEstado.setTop(0);
        lblValorEstado.setWidth(33);
        lblValorEstado.setHeight(13);
        lblValorEstado.setCaption("Estado");
        lblValorEstado.setColor("clBtnFace");
        lblValorEstado.setFontColor("clWindowText");
        lblValorEstado.setFontSize(-11);
        lblValorEstado.setFontName("Tahoma");
        lblValorEstado.setFontStyle("[]");
        lblValorEstado.setVisible(false);
        lblValorEstado.setVerticalAlignment("taVerticalCenter");
        lblValorEstado.setWordBreak(false);
        hBoxEnderecosEstado.addChildren(lblValorEstado);
        lblValorEstado.applyProperties();
    }

    public TFHBox hBoxEnderecosRegiao = new TFHBox();

    private void init_hBoxEnderecosRegiao() {
        hBoxEnderecosRegiao.setName("hBoxEnderecosRegiao");
        hBoxEnderecosRegiao.setLeft(0);
        hBoxEnderecosRegiao.setTop(234);
        hBoxEnderecosRegiao.setWidth(360);
        hBoxEnderecosRegiao.setHeight(25);
        hBoxEnderecosRegiao.setBorderStyle("stNone");
        hBoxEnderecosRegiao.setPaddingTop(0);
        hBoxEnderecosRegiao.setPaddingLeft(0);
        hBoxEnderecosRegiao.setPaddingRight(0);
        hBoxEnderecosRegiao.setPaddingBottom(0);
        hBoxEnderecosRegiao.setMarginTop(0);
        hBoxEnderecosRegiao.setMarginLeft(0);
        hBoxEnderecosRegiao.setMarginRight(0);
        hBoxEnderecosRegiao.setMarginBottom(0);
        hBoxEnderecosRegiao.setSpacing(5);
        hBoxEnderecosRegiao.setFlexVflex("ftMin");
        hBoxEnderecosRegiao.setFlexHflex("ftTrue");
        hBoxEnderecosRegiao.setScrollable(false);
        hBoxEnderecosRegiao.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosRegiao.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosRegiao.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosRegiao.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosRegiao.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosRegiao.setBoxShadowConfigOpacity(75);
        hBoxEnderecosRegiao.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosRegiao);
        hBoxEnderecosRegiao.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoRegiao = new TFHBox();

    private void init_hBoxDadosDoCEPTipoRegiao() {
        hBoxDadosDoCEPTipoRegiao.setName("hBoxDadosDoCEPTipoRegiao");
        hBoxDadosDoCEPTipoRegiao.setLeft(0);
        hBoxDadosDoCEPTipoRegiao.setTop(0);
        hBoxDadosDoCEPTipoRegiao.setWidth(80);
        hBoxDadosDoCEPTipoRegiao.setHeight(20);
        hBoxDadosDoCEPTipoRegiao.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoRegiao.setPaddingTop(0);
        hBoxDadosDoCEPTipoRegiao.setPaddingLeft(0);
        hBoxDadosDoCEPTipoRegiao.setPaddingRight(0);
        hBoxDadosDoCEPTipoRegiao.setPaddingBottom(0);
        hBoxDadosDoCEPTipoRegiao.setMarginTop(0);
        hBoxDadosDoCEPTipoRegiao.setMarginLeft(0);
        hBoxDadosDoCEPTipoRegiao.setMarginRight(0);
        hBoxDadosDoCEPTipoRegiao.setMarginBottom(0);
        hBoxDadosDoCEPTipoRegiao.setSpacing(1);
        hBoxDadosDoCEPTipoRegiao.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoRegiao.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoRegiao.setScrollable(false);
        hBoxDadosDoCEPTipoRegiao.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoRegiao.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoRegiao.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoRegiao.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoRegiao.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoRegiao.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoRegiao.setVAlign("tvTop");
        hBoxEnderecosRegiao.addChildren(hBoxDadosDoCEPTipoRegiao);
        hBoxDadosDoCEPTipoRegiao.applyProperties();
    }

    public TFLabel lblTipoRegiao = new TFLabel();

    private void init_lblTipoRegiao() {
        lblTipoRegiao.setName("lblTipoRegiao");
        lblTipoRegiao.setLeft(0);
        lblTipoRegiao.setTop(0);
        lblTipoRegiao.setWidth(33);
        lblTipoRegiao.setHeight(13);
        lblTipoRegiao.setCaption("Regi\u00E3o");
        lblTipoRegiao.setFontColor("clWindowText");
        lblTipoRegiao.setFontSize(-11);
        lblTipoRegiao.setFontName("Tahoma");
        lblTipoRegiao.setFontStyle("[]");
        lblTipoRegiao.setVerticalAlignment("taVerticalCenter");
        lblTipoRegiao.setWordBreak(false);
        hBoxDadosDoCEPTipoRegiao.addChildren(lblTipoRegiao);
        lblTipoRegiao.applyProperties();
    }

    public TFLabel lblValorRegiao = new TFLabel();

    private void init_lblValorRegiao() {
        lblValorRegiao.setName("lblValorRegiao");
        lblValorRegiao.setLeft(80);
        lblValorRegiao.setTop(0);
        lblValorRegiao.setWidth(33);
        lblValorRegiao.setHeight(13);
        lblValorRegiao.setCaption("Regi\u00E3o");
        lblValorRegiao.setColor("clBtnFace");
        lblValorRegiao.setFontColor("clWindowText");
        lblValorRegiao.setFontSize(-11);
        lblValorRegiao.setFontName("Tahoma");
        lblValorRegiao.setFontStyle("[]");
        lblValorRegiao.setVisible(false);
        lblValorRegiao.setVerticalAlignment("taVerticalCenter");
        lblValorRegiao.setWordBreak(false);
        hBoxEnderecosRegiao.addChildren(lblValorRegiao);
        lblValorRegiao.applyProperties();
    }

    public TFHBox hBoxEnderecosIBGE = new TFHBox();

    private void init_hBoxEnderecosIBGE() {
        hBoxEnderecosIBGE.setName("hBoxEnderecosIBGE");
        hBoxEnderecosIBGE.setLeft(0);
        hBoxEnderecosIBGE.setTop(260);
        hBoxEnderecosIBGE.setWidth(360);
        hBoxEnderecosIBGE.setHeight(25);
        hBoxEnderecosIBGE.setBorderStyle("stNone");
        hBoxEnderecosIBGE.setPaddingTop(0);
        hBoxEnderecosIBGE.setPaddingLeft(0);
        hBoxEnderecosIBGE.setPaddingRight(0);
        hBoxEnderecosIBGE.setPaddingBottom(0);
        hBoxEnderecosIBGE.setMarginTop(0);
        hBoxEnderecosIBGE.setMarginLeft(0);
        hBoxEnderecosIBGE.setMarginRight(0);
        hBoxEnderecosIBGE.setMarginBottom(0);
        hBoxEnderecosIBGE.setSpacing(5);
        hBoxEnderecosIBGE.setFlexVflex("ftMin");
        hBoxEnderecosIBGE.setFlexHflex("ftTrue");
        hBoxEnderecosIBGE.setScrollable(false);
        hBoxEnderecosIBGE.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosIBGE.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosIBGE.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosIBGE.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosIBGE.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosIBGE.setBoxShadowConfigOpacity(75);
        hBoxEnderecosIBGE.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosIBGE);
        hBoxEnderecosIBGE.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoIBGE = new TFHBox();

    private void init_hBoxDadosDoCEPTipoIBGE() {
        hBoxDadosDoCEPTipoIBGE.setName("hBoxDadosDoCEPTipoIBGE");
        hBoxDadosDoCEPTipoIBGE.setLeft(0);
        hBoxDadosDoCEPTipoIBGE.setTop(0);
        hBoxDadosDoCEPTipoIBGE.setWidth(80);
        hBoxDadosDoCEPTipoIBGE.setHeight(20);
        hBoxDadosDoCEPTipoIBGE.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoIBGE.setPaddingTop(0);
        hBoxDadosDoCEPTipoIBGE.setPaddingLeft(0);
        hBoxDadosDoCEPTipoIBGE.setPaddingRight(0);
        hBoxDadosDoCEPTipoIBGE.setPaddingBottom(0);
        hBoxDadosDoCEPTipoIBGE.setMarginTop(0);
        hBoxDadosDoCEPTipoIBGE.setMarginLeft(0);
        hBoxDadosDoCEPTipoIBGE.setMarginRight(0);
        hBoxDadosDoCEPTipoIBGE.setMarginBottom(0);
        hBoxDadosDoCEPTipoIBGE.setSpacing(1);
        hBoxDadosDoCEPTipoIBGE.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoIBGE.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoIBGE.setScrollable(false);
        hBoxDadosDoCEPTipoIBGE.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoIBGE.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoIBGE.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoIBGE.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoIBGE.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoIBGE.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoIBGE.setVAlign("tvTop");
        hBoxEnderecosIBGE.addChildren(hBoxDadosDoCEPTipoIBGE);
        hBoxDadosDoCEPTipoIBGE.applyProperties();
    }

    public TFLabel lblTipoIBGE = new TFLabel();

    private void init_lblTipoIBGE() {
        lblTipoIBGE.setName("lblTipoIBGE");
        lblTipoIBGE.setLeft(0);
        lblTipoIBGE.setTop(0);
        lblTipoIBGE.setWidth(23);
        lblTipoIBGE.setHeight(13);
        lblTipoIBGE.setCaption("IBGE");
        lblTipoIBGE.setFontColor("clWindowText");
        lblTipoIBGE.setFontSize(-11);
        lblTipoIBGE.setFontName("Tahoma");
        lblTipoIBGE.setFontStyle("[]");
        lblTipoIBGE.setVerticalAlignment("taVerticalCenter");
        lblTipoIBGE.setWordBreak(false);
        hBoxDadosDoCEPTipoIBGE.addChildren(lblTipoIBGE);
        lblTipoIBGE.applyProperties();
    }

    public TFLabel lblValorIBGE = new TFLabel();

    private void init_lblValorIBGE() {
        lblValorIBGE.setName("lblValorIBGE");
        lblValorIBGE.setLeft(80);
        lblValorIBGE.setTop(0);
        lblValorIBGE.setWidth(23);
        lblValorIBGE.setHeight(13);
        lblValorIBGE.setCaption("IBGE");
        lblValorIBGE.setColor("clBtnFace");
        lblValorIBGE.setFontColor("clWindowText");
        lblValorIBGE.setFontSize(-11);
        lblValorIBGE.setFontName("Tahoma");
        lblValorIBGE.setFontStyle("[]");
        lblValorIBGE.setVisible(false);
        lblValorIBGE.setVerticalAlignment("taVerticalCenter");
        lblValorIBGE.setWordBreak(false);
        hBoxEnderecosIBGE.addChildren(lblValorIBGE);
        lblValorIBGE.applyProperties();
    }

    public TFHBox hBoxEnderecosGIA = new TFHBox();

    private void init_hBoxEnderecosGIA() {
        hBoxEnderecosGIA.setName("hBoxEnderecosGIA");
        hBoxEnderecosGIA.setLeft(0);
        hBoxEnderecosGIA.setTop(286);
        hBoxEnderecosGIA.setWidth(360);
        hBoxEnderecosGIA.setHeight(25);
        hBoxEnderecosGIA.setBorderStyle("stNone");
        hBoxEnderecosGIA.setPaddingTop(0);
        hBoxEnderecosGIA.setPaddingLeft(0);
        hBoxEnderecosGIA.setPaddingRight(0);
        hBoxEnderecosGIA.setPaddingBottom(0);
        hBoxEnderecosGIA.setMarginTop(0);
        hBoxEnderecosGIA.setMarginLeft(0);
        hBoxEnderecosGIA.setMarginRight(0);
        hBoxEnderecosGIA.setMarginBottom(0);
        hBoxEnderecosGIA.setSpacing(5);
        hBoxEnderecosGIA.setFlexVflex("ftMin");
        hBoxEnderecosGIA.setFlexHflex("ftTrue");
        hBoxEnderecosGIA.setScrollable(false);
        hBoxEnderecosGIA.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosGIA.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosGIA.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosGIA.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosGIA.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosGIA.setBoxShadowConfigOpacity(75);
        hBoxEnderecosGIA.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosGIA);
        hBoxEnderecosGIA.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoGIA = new TFHBox();

    private void init_hBoxDadosDoCEPTipoGIA() {
        hBoxDadosDoCEPTipoGIA.setName("hBoxDadosDoCEPTipoGIA");
        hBoxDadosDoCEPTipoGIA.setLeft(0);
        hBoxDadosDoCEPTipoGIA.setTop(0);
        hBoxDadosDoCEPTipoGIA.setWidth(80);
        hBoxDadosDoCEPTipoGIA.setHeight(20);
        hBoxDadosDoCEPTipoGIA.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoGIA.setPaddingTop(0);
        hBoxDadosDoCEPTipoGIA.setPaddingLeft(0);
        hBoxDadosDoCEPTipoGIA.setPaddingRight(0);
        hBoxDadosDoCEPTipoGIA.setPaddingBottom(0);
        hBoxDadosDoCEPTipoGIA.setMarginTop(0);
        hBoxDadosDoCEPTipoGIA.setMarginLeft(0);
        hBoxDadosDoCEPTipoGIA.setMarginRight(0);
        hBoxDadosDoCEPTipoGIA.setMarginBottom(0);
        hBoxDadosDoCEPTipoGIA.setSpacing(1);
        hBoxDadosDoCEPTipoGIA.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoGIA.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoGIA.setScrollable(false);
        hBoxDadosDoCEPTipoGIA.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoGIA.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoGIA.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoGIA.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoGIA.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoGIA.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoGIA.setVAlign("tvTop");
        hBoxEnderecosGIA.addChildren(hBoxDadosDoCEPTipoGIA);
        hBoxDadosDoCEPTipoGIA.applyProperties();
    }

    public TFLabel lblTipoGIA = new TFLabel();

    private void init_lblTipoGIA() {
        lblTipoGIA.setName("lblTipoGIA");
        lblTipoGIA.setLeft(0);
        lblTipoGIA.setTop(0);
        lblTipoGIA.setWidth(18);
        lblTipoGIA.setHeight(13);
        lblTipoGIA.setCaption("GIA");
        lblTipoGIA.setFontColor("clWindowText");
        lblTipoGIA.setFontSize(-11);
        lblTipoGIA.setFontName("Tahoma");
        lblTipoGIA.setFontStyle("[]");
        lblTipoGIA.setVerticalAlignment("taVerticalCenter");
        lblTipoGIA.setWordBreak(false);
        hBoxDadosDoCEPTipoGIA.addChildren(lblTipoGIA);
        lblTipoGIA.applyProperties();
    }

    public TFLabel lblValorGIA = new TFLabel();

    private void init_lblValorGIA() {
        lblValorGIA.setName("lblValorGIA");
        lblValorGIA.setLeft(80);
        lblValorGIA.setTop(0);
        lblValorGIA.setWidth(18);
        lblValorGIA.setHeight(13);
        lblValorGIA.setCaption("GIA");
        lblValorGIA.setColor("clBtnFace");
        lblValorGIA.setFontColor("clWindowText");
        lblValorGIA.setFontSize(-11);
        lblValorGIA.setFontName("Tahoma");
        lblValorGIA.setFontStyle("[]");
        lblValorGIA.setVisible(false);
        lblValorGIA.setVerticalAlignment("taVerticalCenter");
        lblValorGIA.setWordBreak(false);
        hBoxEnderecosGIA.addChildren(lblValorGIA);
        lblValorGIA.applyProperties();
    }

    public TFHBox hBoxEnderecosDDD = new TFHBox();

    private void init_hBoxEnderecosDDD() {
        hBoxEnderecosDDD.setName("hBoxEnderecosDDD");
        hBoxEnderecosDDD.setLeft(0);
        hBoxEnderecosDDD.setTop(312);
        hBoxEnderecosDDD.setWidth(360);
        hBoxEnderecosDDD.setHeight(25);
        hBoxEnderecosDDD.setBorderStyle("stNone");
        hBoxEnderecosDDD.setPaddingTop(0);
        hBoxEnderecosDDD.setPaddingLeft(0);
        hBoxEnderecosDDD.setPaddingRight(0);
        hBoxEnderecosDDD.setPaddingBottom(0);
        hBoxEnderecosDDD.setMarginTop(0);
        hBoxEnderecosDDD.setMarginLeft(0);
        hBoxEnderecosDDD.setMarginRight(0);
        hBoxEnderecosDDD.setMarginBottom(0);
        hBoxEnderecosDDD.setSpacing(5);
        hBoxEnderecosDDD.setFlexVflex("ftMin");
        hBoxEnderecosDDD.setFlexHflex("ftTrue");
        hBoxEnderecosDDD.setScrollable(false);
        hBoxEnderecosDDD.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosDDD.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosDDD.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosDDD.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosDDD.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosDDD.setBoxShadowConfigOpacity(75);
        hBoxEnderecosDDD.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosDDD);
        hBoxEnderecosDDD.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoDDD = new TFHBox();

    private void init_hBoxDadosDoCEPTipoDDD() {
        hBoxDadosDoCEPTipoDDD.setName("hBoxDadosDoCEPTipoDDD");
        hBoxDadosDoCEPTipoDDD.setLeft(0);
        hBoxDadosDoCEPTipoDDD.setTop(0);
        hBoxDadosDoCEPTipoDDD.setWidth(80);
        hBoxDadosDoCEPTipoDDD.setHeight(20);
        hBoxDadosDoCEPTipoDDD.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoDDD.setPaddingTop(0);
        hBoxDadosDoCEPTipoDDD.setPaddingLeft(0);
        hBoxDadosDoCEPTipoDDD.setPaddingRight(0);
        hBoxDadosDoCEPTipoDDD.setPaddingBottom(0);
        hBoxDadosDoCEPTipoDDD.setMarginTop(0);
        hBoxDadosDoCEPTipoDDD.setMarginLeft(0);
        hBoxDadosDoCEPTipoDDD.setMarginRight(0);
        hBoxDadosDoCEPTipoDDD.setMarginBottom(0);
        hBoxDadosDoCEPTipoDDD.setSpacing(1);
        hBoxDadosDoCEPTipoDDD.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoDDD.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoDDD.setScrollable(false);
        hBoxDadosDoCEPTipoDDD.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoDDD.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoDDD.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoDDD.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoDDD.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoDDD.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoDDD.setVAlign("tvTop");
        hBoxEnderecosDDD.addChildren(hBoxDadosDoCEPTipoDDD);
        hBoxDadosDoCEPTipoDDD.applyProperties();
    }

    public TFLabel lblTipoDDD = new TFLabel();

    private void init_lblTipoDDD() {
        lblTipoDDD.setName("lblTipoDDD");
        lblTipoDDD.setLeft(0);
        lblTipoDDD.setTop(0);
        lblTipoDDD.setWidth(21);
        lblTipoDDD.setHeight(13);
        lblTipoDDD.setCaption("DDD");
        lblTipoDDD.setFontColor("clWindowText");
        lblTipoDDD.setFontSize(-11);
        lblTipoDDD.setFontName("Tahoma");
        lblTipoDDD.setFontStyle("[]");
        lblTipoDDD.setVerticalAlignment("taVerticalCenter");
        lblTipoDDD.setWordBreak(false);
        hBoxDadosDoCEPTipoDDD.addChildren(lblTipoDDD);
        lblTipoDDD.applyProperties();
    }

    public TFLabel lblValorDDD = new TFLabel();

    private void init_lblValorDDD() {
        lblValorDDD.setName("lblValorDDD");
        lblValorDDD.setLeft(80);
        lblValorDDD.setTop(0);
        lblValorDDD.setWidth(21);
        lblValorDDD.setHeight(13);
        lblValorDDD.setCaption("DDD");
        lblValorDDD.setColor("clBtnFace");
        lblValorDDD.setFontColor("clWindowText");
        lblValorDDD.setFontSize(-11);
        lblValorDDD.setFontName("Tahoma");
        lblValorDDD.setFontStyle("[]");
        lblValorDDD.setVisible(false);
        lblValorDDD.setVerticalAlignment("taVerticalCenter");
        lblValorDDD.setWordBreak(false);
        hBoxEnderecosDDD.addChildren(lblValorDDD);
        lblValorDDD.applyProperties();
    }

    public TFHBox hBoxEnderecosSIAFI = new TFHBox();

    private void init_hBoxEnderecosSIAFI() {
        hBoxEnderecosSIAFI.setName("hBoxEnderecosSIAFI");
        hBoxEnderecosSIAFI.setLeft(0);
        hBoxEnderecosSIAFI.setTop(338);
        hBoxEnderecosSIAFI.setWidth(360);
        hBoxEnderecosSIAFI.setHeight(25);
        hBoxEnderecosSIAFI.setBorderStyle("stNone");
        hBoxEnderecosSIAFI.setPaddingTop(0);
        hBoxEnderecosSIAFI.setPaddingLeft(0);
        hBoxEnderecosSIAFI.setPaddingRight(0);
        hBoxEnderecosSIAFI.setPaddingBottom(0);
        hBoxEnderecosSIAFI.setMarginTop(0);
        hBoxEnderecosSIAFI.setMarginLeft(0);
        hBoxEnderecosSIAFI.setMarginRight(0);
        hBoxEnderecosSIAFI.setMarginBottom(0);
        hBoxEnderecosSIAFI.setSpacing(5);
        hBoxEnderecosSIAFI.setFlexVflex("ftMin");
        hBoxEnderecosSIAFI.setFlexHflex("ftTrue");
        hBoxEnderecosSIAFI.setScrollable(false);
        hBoxEnderecosSIAFI.setBoxShadowConfigHorizontalLength(10);
        hBoxEnderecosSIAFI.setBoxShadowConfigVerticalLength(10);
        hBoxEnderecosSIAFI.setBoxShadowConfigBlurRadius(5);
        hBoxEnderecosSIAFI.setBoxShadowConfigSpreadRadius(0);
        hBoxEnderecosSIAFI.setBoxShadowConfigShadowColor("clBlack");
        hBoxEnderecosSIAFI.setBoxShadowConfigOpacity(75);
        hBoxEnderecosSIAFI.setVAlign("tvTop");
        vBoxDadosDoCEP.addChildren(hBoxEnderecosSIAFI);
        hBoxEnderecosSIAFI.applyProperties();
    }

    public TFHBox hBoxDadosDoCEPTipoSIAFI = new TFHBox();

    private void init_hBoxDadosDoCEPTipoSIAFI() {
        hBoxDadosDoCEPTipoSIAFI.setName("hBoxDadosDoCEPTipoSIAFI");
        hBoxDadosDoCEPTipoSIAFI.setLeft(0);
        hBoxDadosDoCEPTipoSIAFI.setTop(0);
        hBoxDadosDoCEPTipoSIAFI.setWidth(80);
        hBoxDadosDoCEPTipoSIAFI.setHeight(20);
        hBoxDadosDoCEPTipoSIAFI.setBorderStyle("stNone");
        hBoxDadosDoCEPTipoSIAFI.setPaddingTop(0);
        hBoxDadosDoCEPTipoSIAFI.setPaddingLeft(0);
        hBoxDadosDoCEPTipoSIAFI.setPaddingRight(0);
        hBoxDadosDoCEPTipoSIAFI.setPaddingBottom(0);
        hBoxDadosDoCEPTipoSIAFI.setMarginTop(0);
        hBoxDadosDoCEPTipoSIAFI.setMarginLeft(0);
        hBoxDadosDoCEPTipoSIAFI.setMarginRight(0);
        hBoxDadosDoCEPTipoSIAFI.setMarginBottom(0);
        hBoxDadosDoCEPTipoSIAFI.setSpacing(1);
        hBoxDadosDoCEPTipoSIAFI.setFlexVflex("ftMin");
        hBoxDadosDoCEPTipoSIAFI.setFlexHflex("ftFalse");
        hBoxDadosDoCEPTipoSIAFI.setScrollable(false);
        hBoxDadosDoCEPTipoSIAFI.setBoxShadowConfigHorizontalLength(10);
        hBoxDadosDoCEPTipoSIAFI.setBoxShadowConfigVerticalLength(10);
        hBoxDadosDoCEPTipoSIAFI.setBoxShadowConfigBlurRadius(5);
        hBoxDadosDoCEPTipoSIAFI.setBoxShadowConfigSpreadRadius(0);
        hBoxDadosDoCEPTipoSIAFI.setBoxShadowConfigShadowColor("clBlack");
        hBoxDadosDoCEPTipoSIAFI.setBoxShadowConfigOpacity(75);
        hBoxDadosDoCEPTipoSIAFI.setVAlign("tvTop");
        hBoxEnderecosSIAFI.addChildren(hBoxDadosDoCEPTipoSIAFI);
        hBoxDadosDoCEPTipoSIAFI.applyProperties();
    }

    public TFLabel lblTipoSIAFI = new TFLabel();

    private void init_lblTipoSIAFI() {
        lblTipoSIAFI.setName("lblTipoSIAFI");
        lblTipoSIAFI.setLeft(0);
        lblTipoSIAFI.setTop(0);
        lblTipoSIAFI.setWidth(27);
        lblTipoSIAFI.setHeight(13);
        lblTipoSIAFI.setCaption("SIAFI");
        lblTipoSIAFI.setFontColor("clWindowText");
        lblTipoSIAFI.setFontSize(-11);
        lblTipoSIAFI.setFontName("Tahoma");
        lblTipoSIAFI.setFontStyle("[]");
        lblTipoSIAFI.setVerticalAlignment("taVerticalCenter");
        lblTipoSIAFI.setWordBreak(false);
        hBoxDadosDoCEPTipoSIAFI.addChildren(lblTipoSIAFI);
        lblTipoSIAFI.applyProperties();
    }

    public TFLabel lblValorSIAFI = new TFLabel();

    private void init_lblValorSIAFI() {
        lblValorSIAFI.setName("lblValorSIAFI");
        lblValorSIAFI.setLeft(80);
        lblValorSIAFI.setTop(0);
        lblValorSIAFI.setWidth(27);
        lblValorSIAFI.setHeight(13);
        lblValorSIAFI.setCaption("SIAFI");
        lblValorSIAFI.setColor("clBtnFace");
        lblValorSIAFI.setFontColor("clWindowText");
        lblValorSIAFI.setFontSize(-11);
        lblValorSIAFI.setFontName("Tahoma");
        lblValorSIAFI.setFontStyle("[]");
        lblValorSIAFI.setVisible(false);
        lblValorSIAFI.setVerticalAlignment("taVerticalCenter");
        lblValorSIAFI.setWordBreak(false);
        hBoxEnderecosSIAFI.addChildren(lblValorSIAFI);
        lblValorSIAFI.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarFrmPesquisaCEPOnlineClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarFrmPesquisaCEPOnlineClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtCEPFrmPesquisaCEPOnlineEnter(final Event<Object> event);

    public void btnPesquisarCEPFrmPesquisaCEPOnlineClick(final Event<Object> event) {
        if (btnPesquisarCEP.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisarCEP");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}