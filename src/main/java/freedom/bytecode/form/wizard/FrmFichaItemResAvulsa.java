package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmFichaItemResAvulsa extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.FichaItemResAvulsaRNA rn = null;

    public FrmFichaItemResAvulsa() {
        try {
            rn = (freedom.bytecode.rn.FichaItemResAvulsaRNA) getRN(freedom.bytecode.rn.wizard.FichaItemResAvulsaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEstoque();
        init_tbItensReservasPendencias();
        init_tbEmpresasUsuarios();
        init_tbFornecedorEstoqueItem();
        init_tbItens();
        init_tbLeadsEmpresasUsuarios();
        init_vBoxPrincipal();
        init_hBoxBotoes();
        init_btnSalvar();
        init_vboxEmpresas();
        init_lblEmpresas();
        init_cboEmpresas();
        init_hBoxCodDesc();
        init_vBoxCodigo();
        init_lblCodigo();
        init_edtCodigo();
        init_vBoxRefresh();
        init_hBoxRefreshSeparador01();
        init_icoPesquisarItem();
        init_vBoxDescricao();
        init_lblDescricao();
        init_edtDescricao();
        init_vBoxFornecedor();
        init_lblFornecedor();
        init_cboFornecedor();
        init_hBoxQtdEstqReserv();
        init_vBoxQuantidade();
        init_lblQuantidade();
        init_edtQuantidade();
        init_vBoxEstoque();
        init_lblEstoque();
        init_edtEstoque();
        init_vBoxReservado();
        init_lblReservado();
        init_edtReservado();
        init_hBoxNomeTelefone();
        init_vBoxNomeCliente();
        init_lblNomeCliente();
        init_edtNomeCliente();
        init_vBoxConsultarCliente();
        init_hBoxConsultarCliente();
        init_btnPesquisarCliente();
        init_vBoxTelefone();
        init_lblTelefone();
        init_edtTelefone();
        init_vBoxObservacao();
        init_lblObservacao();
        init_edtObservacao();
        init_scFornecedorEstoqueItem();
        init_FrmFichaItemResAvulsa();
    }

    public ESTOQUE tbEstoque;

    private void init_tbEstoque() {
        tbEstoque = rn.tbEstoque;
        tbEstoque.setName("tbEstoque");
        tbEstoque.setMaxRowCount(200);
        tbEstoque.setWKey("310046;31001");
        tbEstoque.setRatioBatchSize(20);
        getTables().put(tbEstoque, "tbEstoque");
        tbEstoque.applyProperties();
    }

    public ITENS_RESERVAS_PENDENCIAS tbItensReservasPendencias;

    private void init_tbItensReservasPendencias() {
        tbItensReservasPendencias = rn.tbItensReservasPendencias;
        tbItensReservasPendencias.setName("tbItensReservasPendencias");
        tbItensReservasPendencias.setMaxRowCount(200);
        tbItensReservasPendencias.setWKey("310046;31002");
        tbItensReservasPendencias.setRatioBatchSize(20);
        getTables().put(tbItensReservasPendencias, "tbItensReservasPendencias");
        tbItensReservasPendencias.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("310046;31003");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public FORNECEDOR_ESTOQUE_ITEM tbFornecedorEstoqueItem;

    private void init_tbFornecedorEstoqueItem() {
        tbFornecedorEstoqueItem = rn.tbFornecedorEstoqueItem;
        tbFornecedorEstoqueItem.setName("tbFornecedorEstoqueItem");
        tbFornecedorEstoqueItem.setMaxRowCount(200);
        tbFornecedorEstoqueItem.setWKey("310046;53001");
        tbFornecedorEstoqueItem.setRatioBatchSize(20);
        getTables().put(tbFornecedorEstoqueItem, "tbFornecedorEstoqueItem");
        tbFornecedorEstoqueItem.applyProperties();
    }

    public ITENS tbItens;

    private void init_tbItens() {
        tbItens = rn.tbItens;
        tbItens.setName("tbItens");
        tbItens.setMaxRowCount(200);
        tbItens.setWKey("310046;53003");
        tbItens.setRatioBatchSize(20);
        getTables().put(tbItens, "tbItens");
        tbItens.applyProperties();
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios;

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios = rn.tbLeadsEmpresasUsuarios;
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsEmpresasUsuariosAfterScroll(event);
            processarFlow("FrmFichaItemResAvulsa", "tbLeadsEmpresasUsuarios", "OnAfterScroll");
        });
        tbLeadsEmpresasUsuarios.setWKey("310046;53004");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbLeadsEmpresasUsuarios, "tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.applyProperties();
    }

    protected TFForm FrmFichaItemResAvulsa = this;
    private void init_FrmFichaItemResAvulsa() {
        FrmFichaItemResAvulsa.setName("FrmFichaItemResAvulsa");
        FrmFichaItemResAvulsa.setCaption("Incluir Reserva Avulsa");
        FrmFichaItemResAvulsa.setClientHeight(411);
        FrmFichaItemResAvulsa.setClientWidth(494);
        FrmFichaItemResAvulsa.setColor("clBtnFace");
        FrmFichaItemResAvulsa.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmFichaItemResAvulsa", "FrmFichaItemResAvulsa", "OnCreate");
        });
        FrmFichaItemResAvulsa.setWOrigem("EhMain");
        FrmFichaItemResAvulsa.setWKey("310046");
        FrmFichaItemResAvulsa.setSpacing(0);
        FrmFichaItemResAvulsa.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(494);
        vBoxPrincipal.setHeight(411);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stSingleLine");
        vBoxPrincipal.setPaddingTop(5);
        vBoxPrincipal.setPaddingLeft(5);
        vBoxPrincipal.setPaddingRight(5);
        vBoxPrincipal.setPaddingBottom(5);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(5);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmFichaItemResAvulsa.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(485);
        hBoxBotoes.setHeight(61);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmFichaItemResAvulsa", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox vboxEmpresas = new TFVBox();

    private void init_vboxEmpresas() {
        vboxEmpresas.setName("vboxEmpresas");
        vboxEmpresas.setLeft(0);
        vboxEmpresas.setTop(62);
        vboxEmpresas.setWidth(210);
        vboxEmpresas.setHeight(40);
        vboxEmpresas.setBorderStyle("stNone");
        vboxEmpresas.setPaddingTop(0);
        vboxEmpresas.setPaddingLeft(0);
        vboxEmpresas.setPaddingRight(0);
        vboxEmpresas.setPaddingBottom(0);
        vboxEmpresas.setMarginTop(0);
        vboxEmpresas.setMarginLeft(0);
        vboxEmpresas.setMarginRight(0);
        vboxEmpresas.setMarginBottom(0);
        vboxEmpresas.setSpacing(1);
        vboxEmpresas.setFlexVflex("ftMin");
        vboxEmpresas.setFlexHflex("ftTrue");
        vboxEmpresas.setScrollable(false);
        vboxEmpresas.setBoxShadowConfigHorizontalLength(10);
        vboxEmpresas.setBoxShadowConfigVerticalLength(10);
        vboxEmpresas.setBoxShadowConfigBlurRadius(5);
        vboxEmpresas.setBoxShadowConfigSpreadRadius(0);
        vboxEmpresas.setBoxShadowConfigShadowColor("clBlack");
        vboxEmpresas.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vboxEmpresas);
        vboxEmpresas.applyProperties();
    }

    public TFLabel lblEmpresas = new TFLabel();

    private void init_lblEmpresas() {
        lblEmpresas.setName("lblEmpresas");
        lblEmpresas.setLeft(0);
        lblEmpresas.setTop(0);
        lblEmpresas.setWidth(46);
        lblEmpresas.setHeight(13);
        lblEmpresas.setCaption("Empresas");
        lblEmpresas.setFontColor("clWindowText");
        lblEmpresas.setFontSize(-11);
        lblEmpresas.setFontName("Tahoma");
        lblEmpresas.setFontStyle("[]");
        lblEmpresas.setVerticalAlignment("taVerticalCenter");
        lblEmpresas.setWordBreak(false);
        vboxEmpresas.addChildren(lblEmpresas);
        lblEmpresas.applyProperties();
    }

    public TFCombo cboEmpresas = new TFCombo();

    private void init_cboEmpresas() {
        cboEmpresas.setName("cboEmpresas");
        cboEmpresas.setLeft(0);
        cboEmpresas.setTop(14);
        cboEmpresas.setWidth(200);
        cboEmpresas.setHeight(21);
        cboEmpresas.setHint("Empresas");
        cboEmpresas.setLookupTable(tbLeadsEmpresasUsuarios);
        cboEmpresas.setLookupKey("COD_EMPRESA");
        cboEmpresas.setLookupDesc("EMPRESA");
        cboEmpresas.setFlex(true);
        cboEmpresas.setHelpCaption("Empresas");
        cboEmpresas.setReadOnly(true);
        cboEmpresas.setRequired(false);
        cboEmpresas.setPrompt("Empresas");
        cboEmpresas.setConstraintCheckWhen("cwImmediate");
        cboEmpresas.setConstraintCheckType("ctExpression");
        cboEmpresas.setConstraintFocusOnError(false);
        cboEmpresas.setConstraintEnableUI(true);
        cboEmpresas.setConstraintEnabled(false);
        cboEmpresas.setConstraintFormCheck(true);
        cboEmpresas.setClearOnDelKey(true);
        cboEmpresas.setUseClearButton(false);
        cboEmpresas.setHideClearButtonOnNullValue(false);
        cboEmpresas.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresasChange(event);
            processarFlow("FrmFichaItemResAvulsa", "cboEmpresas", "OnChange");
        });
        cboEmpresas.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresasEnter(event);
            processarFlow("FrmFichaItemResAvulsa", "cboEmpresas", "OnEnter");
        });
        vboxEmpresas.addChildren(cboEmpresas);
        cboEmpresas.applyProperties();
        addValidatable(cboEmpresas);
    }

    public TFHBox hBoxCodDesc = new TFHBox();

    private void init_hBoxCodDesc() {
        hBoxCodDesc.setName("hBoxCodDesc");
        hBoxCodDesc.setLeft(0);
        hBoxCodDesc.setTop(103);
        hBoxCodDesc.setWidth(485);
        hBoxCodDesc.setHeight(50);
        hBoxCodDesc.setBorderStyle("stNone");
        hBoxCodDesc.setPaddingTop(0);
        hBoxCodDesc.setPaddingLeft(0);
        hBoxCodDesc.setPaddingRight(0);
        hBoxCodDesc.setPaddingBottom(0);
        hBoxCodDesc.setMarginTop(0);
        hBoxCodDesc.setMarginLeft(0);
        hBoxCodDesc.setMarginRight(0);
        hBoxCodDesc.setMarginBottom(0);
        hBoxCodDesc.setSpacing(5);
        hBoxCodDesc.setFlexVflex("ftMin");
        hBoxCodDesc.setFlexHflex("ftTrue");
        hBoxCodDesc.setScrollable(false);
        hBoxCodDesc.setBoxShadowConfigHorizontalLength(10);
        hBoxCodDesc.setBoxShadowConfigVerticalLength(10);
        hBoxCodDesc.setBoxShadowConfigBlurRadius(5);
        hBoxCodDesc.setBoxShadowConfigSpreadRadius(0);
        hBoxCodDesc.setBoxShadowConfigShadowColor("clBlack");
        hBoxCodDesc.setBoxShadowConfigOpacity(75);
        hBoxCodDesc.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxCodDesc);
        hBoxCodDesc.applyProperties();
    }

    public TFVBox vBoxCodigo = new TFVBox();

    private void init_vBoxCodigo() {
        vBoxCodigo.setName("vBoxCodigo");
        vBoxCodigo.setLeft(0);
        vBoxCodigo.setTop(0);
        vBoxCodigo.setWidth(181);
        vBoxCodigo.setHeight(45);
        vBoxCodigo.setBorderStyle("stNone");
        vBoxCodigo.setPaddingTop(0);
        vBoxCodigo.setPaddingLeft(0);
        vBoxCodigo.setPaddingRight(0);
        vBoxCodigo.setPaddingBottom(0);
        vBoxCodigo.setMarginTop(0);
        vBoxCodigo.setMarginLeft(0);
        vBoxCodigo.setMarginRight(0);
        vBoxCodigo.setMarginBottom(0);
        vBoxCodigo.setSpacing(1);
        vBoxCodigo.setFlexVflex("ftMin");
        vBoxCodigo.setFlexHflex("ftFalse");
        vBoxCodigo.setScrollable(false);
        vBoxCodigo.setBoxShadowConfigHorizontalLength(10);
        vBoxCodigo.setBoxShadowConfigVerticalLength(10);
        vBoxCodigo.setBoxShadowConfigBlurRadius(5);
        vBoxCodigo.setBoxShadowConfigSpreadRadius(0);
        vBoxCodigo.setBoxShadowConfigShadowColor("clBlack");
        vBoxCodigo.setBoxShadowConfigOpacity(75);
        hBoxCodDesc.addChildren(vBoxCodigo);
        vBoxCodigo.applyProperties();
    }

    public TFLabel lblCodigo = new TFLabel();

    private void init_lblCodigo() {
        lblCodigo.setName("lblCodigo");
        lblCodigo.setLeft(0);
        lblCodigo.setTop(0);
        lblCodigo.setWidth(33);
        lblCodigo.setHeight(13);
        lblCodigo.setCaption("C\u00F3digo");
        lblCodigo.setFontColor("clWindowText");
        lblCodigo.setFontSize(-11);
        lblCodigo.setFontName("Tahoma");
        lblCodigo.setFontStyle("[]");
        lblCodigo.setVerticalAlignment("taVerticalCenter");
        lblCodigo.setWordBreak(false);
        vBoxCodigo.addChildren(lblCodigo);
        lblCodigo.applyProperties();
    }

    public TFString edtCodigo = new TFString();

    private void init_edtCodigo() {
        edtCodigo.setName("edtCodigo");
        edtCodigo.setLeft(0);
        edtCodigo.setTop(14);
        edtCodigo.setWidth(170);
        edtCodigo.setHeight(24);
        edtCodigo.setHint("C\u00F3digo");
        edtCodigo.setTable(tbItensReservasPendencias);
        edtCodigo.setFieldName("COD_ITEM");
        edtCodigo.setHelpCaption("C\u00F3digo");
        edtCodigo.setFlex(true);
        edtCodigo.setRequired(false);
        edtCodigo.setPrompt("C\u00F3digo");
        edtCodigo.setConstraintCheckWhen("cwImmediate");
        edtCodigo.setConstraintCheckType("ctExpression");
        edtCodigo.setConstraintFocusOnError(false);
        edtCodigo.setConstraintEnableUI(true);
        edtCodigo.setConstraintEnabled(false);
        edtCodigo.setConstraintFormCheck(true);
        edtCodigo.setCharCase("ccNormal");
        edtCodigo.setPwd(false);
        edtCodigo.setMaxlength(0);
        edtCodigo.setAlign("alClient");
        edtCodigo.setFontColor("clWindowText");
        edtCodigo.setFontSize(-13);
        edtCodigo.setFontName("Tahoma");
        edtCodigo.setFontStyle("[]");
        edtCodigo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCodigoEnter(event);
            processarFlow("FrmFichaItemResAvulsa", "edtCodigo", "OnEnter");
        });
        edtCodigo.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCodigoExit(event);
            processarFlow("FrmFichaItemResAvulsa", "edtCodigo", "OnExit");
        });
        edtCodigo.setSaveLiteralCharacter(false);
        edtCodigo.applyProperties();
        vBoxCodigo.addChildren(edtCodigo);
        addValidatable(edtCodigo);
    }

    public TFVBox vBoxRefresh = new TFVBox();

    private void init_vBoxRefresh() {
        vBoxRefresh.setName("vBoxRefresh");
        vBoxRefresh.setLeft(181);
        vBoxRefresh.setTop(0);
        vBoxRefresh.setWidth(32);
        vBoxRefresh.setHeight(40);
        vBoxRefresh.setBorderStyle("stNone");
        vBoxRefresh.setPaddingTop(5);
        vBoxRefresh.setPaddingLeft(0);
        vBoxRefresh.setPaddingRight(0);
        vBoxRefresh.setPaddingBottom(0);
        vBoxRefresh.setMarginTop(0);
        vBoxRefresh.setMarginLeft(0);
        vBoxRefresh.setMarginRight(0);
        vBoxRefresh.setMarginBottom(0);
        vBoxRefresh.setSpacing(1);
        vBoxRefresh.setFlexVflex("ftFalse");
        vBoxRefresh.setFlexHflex("ftMin");
        vBoxRefresh.setScrollable(false);
        vBoxRefresh.setBoxShadowConfigHorizontalLength(10);
        vBoxRefresh.setBoxShadowConfigVerticalLength(10);
        vBoxRefresh.setBoxShadowConfigBlurRadius(5);
        vBoxRefresh.setBoxShadowConfigSpreadRadius(0);
        vBoxRefresh.setBoxShadowConfigShadowColor("clBlack");
        vBoxRefresh.setBoxShadowConfigOpacity(75);
        hBoxCodDesc.addChildren(vBoxRefresh);
        vBoxRefresh.applyProperties();
    }

    public TFHBox hBoxRefreshSeparador01 = new TFHBox();

    private void init_hBoxRefreshSeparador01() {
        hBoxRefreshSeparador01.setName("hBoxRefreshSeparador01");
        hBoxRefreshSeparador01.setLeft(0);
        hBoxRefreshSeparador01.setTop(0);
        hBoxRefreshSeparador01.setWidth(10);
        hBoxRefreshSeparador01.setHeight(11);
        hBoxRefreshSeparador01.setBorderStyle("stNone");
        hBoxRefreshSeparador01.setPaddingTop(0);
        hBoxRefreshSeparador01.setPaddingLeft(0);
        hBoxRefreshSeparador01.setPaddingRight(0);
        hBoxRefreshSeparador01.setPaddingBottom(0);
        hBoxRefreshSeparador01.setMarginTop(0);
        hBoxRefreshSeparador01.setMarginLeft(0);
        hBoxRefreshSeparador01.setMarginRight(0);
        hBoxRefreshSeparador01.setMarginBottom(0);
        hBoxRefreshSeparador01.setSpacing(1);
        hBoxRefreshSeparador01.setFlexVflex("ftFalse");
        hBoxRefreshSeparador01.setFlexHflex("ftFalse");
        hBoxRefreshSeparador01.setScrollable(false);
        hBoxRefreshSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxRefreshSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxRefreshSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxRefreshSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxRefreshSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxRefreshSeparador01.setBoxShadowConfigOpacity(75);
        hBoxRefreshSeparador01.setVAlign("tvTop");
        vBoxRefresh.addChildren(hBoxRefreshSeparador01);
        hBoxRefreshSeparador01.applyProperties();
    }

    public TFIconClass icoPesquisarItem = new TFIconClass();

    private void init_icoPesquisarItem() {
        icoPesquisarItem.setName("icoPesquisarItem");
        icoPesquisarItem.setLeft(0);
        icoPesquisarItem.setTop(12);
        icoPesquisarItem.setHint("Atualizar");
        icoPesquisarItem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoPesquisarItemClick(event);
            processarFlow("FrmFichaItemResAvulsa", "icoPesquisarItem", "OnClick");
        });
        icoPesquisarItem.setIconClass("refresh");
        icoPesquisarItem.setSize(22);
        icoPesquisarItem.setColor("clBlack");
        vBoxRefresh.addChildren(icoPesquisarItem);
        icoPesquisarItem.applyProperties();
    }

    public TFVBox vBoxDescricao = new TFVBox();

    private void init_vBoxDescricao() {
        vBoxDescricao.setName("vBoxDescricao");
        vBoxDescricao.setLeft(213);
        vBoxDescricao.setTop(0);
        vBoxDescricao.setWidth(260);
        vBoxDescricao.setHeight(45);
        vBoxDescricao.setBorderStyle("stNone");
        vBoxDescricao.setPaddingTop(0);
        vBoxDescricao.setPaddingLeft(0);
        vBoxDescricao.setPaddingRight(0);
        vBoxDescricao.setPaddingBottom(0);
        vBoxDescricao.setMarginTop(0);
        vBoxDescricao.setMarginLeft(0);
        vBoxDescricao.setMarginRight(0);
        vBoxDescricao.setMarginBottom(0);
        vBoxDescricao.setSpacing(1);
        vBoxDescricao.setFlexVflex("ftMin");
        vBoxDescricao.setFlexHflex("ftTrue");
        vBoxDescricao.setScrollable(false);
        vBoxDescricao.setBoxShadowConfigHorizontalLength(10);
        vBoxDescricao.setBoxShadowConfigVerticalLength(10);
        vBoxDescricao.setBoxShadowConfigBlurRadius(5);
        vBoxDescricao.setBoxShadowConfigSpreadRadius(0);
        vBoxDescricao.setBoxShadowConfigShadowColor("clBlack");
        vBoxDescricao.setBoxShadowConfigOpacity(75);
        hBoxCodDesc.addChildren(vBoxDescricao);
        vBoxDescricao.applyProperties();
    }

    public TFLabel lblDescricao = new TFLabel();

    private void init_lblDescricao() {
        lblDescricao.setName("lblDescricao");
        lblDescricao.setLeft(0);
        lblDescricao.setTop(0);
        lblDescricao.setWidth(46);
        lblDescricao.setHeight(13);
        lblDescricao.setCaption("Descri\u00E7\u00E3o");
        lblDescricao.setFontColor("clWindowText");
        lblDescricao.setFontSize(-11);
        lblDescricao.setFontName("Tahoma");
        lblDescricao.setFontStyle("[]");
        lblDescricao.setVerticalAlignment("taVerticalCenter");
        lblDescricao.setWordBreak(false);
        vBoxDescricao.addChildren(lblDescricao);
        lblDescricao.applyProperties();
    }

    public TFString edtDescricao = new TFString();

    private void init_edtDescricao() {
        edtDescricao.setName("edtDescricao");
        edtDescricao.setLeft(0);
        edtDescricao.setTop(14);
        edtDescricao.setWidth(235);
        edtDescricao.setHeight(24);
        edtDescricao.setHint("Descri\u00E7\u00E3o");
        edtDescricao.setTable(tbItensReservasPendencias);
        edtDescricao.setHelpCaption("Descri\u00E7\u00E3o");
        edtDescricao.setFlex(true);
        edtDescricao.setRequired(false);
        edtDescricao.setPrompt("Descri\u00E7\u00E3o");
        edtDescricao.setConstraintCheckWhen("cwImmediate");
        edtDescricao.setConstraintCheckType("ctExpression");
        edtDescricao.setConstraintFocusOnError(false);
        edtDescricao.setConstraintEnableUI(true);
        edtDescricao.setConstraintEnabled(false);
        edtDescricao.setConstraintFormCheck(true);
        edtDescricao.setCharCase("ccNormal");
        edtDescricao.setPwd(false);
        edtDescricao.setMaxlength(0);
        edtDescricao.setEnabled(false);
        edtDescricao.setFontColor("clWindowText");
        edtDescricao.setFontSize(-13);
        edtDescricao.setFontName("Tahoma");
        edtDescricao.setFontStyle("[]");
        edtDescricao.setSaveLiteralCharacter(false);
        edtDescricao.applyProperties();
        vBoxDescricao.addChildren(edtDescricao);
        addValidatable(edtDescricao);
    }

    public TFVBox vBoxFornecedor = new TFVBox();

    private void init_vBoxFornecedor() {
        vBoxFornecedor.setName("vBoxFornecedor");
        vBoxFornecedor.setLeft(0);
        vBoxFornecedor.setTop(154);
        vBoxFornecedor.setWidth(199);
        vBoxFornecedor.setHeight(40);
        vBoxFornecedor.setBorderStyle("stNone");
        vBoxFornecedor.setPaddingTop(0);
        vBoxFornecedor.setPaddingLeft(0);
        vBoxFornecedor.setPaddingRight(0);
        vBoxFornecedor.setPaddingBottom(0);
        vBoxFornecedor.setMarginTop(0);
        vBoxFornecedor.setMarginLeft(0);
        vBoxFornecedor.setMarginRight(0);
        vBoxFornecedor.setMarginBottom(0);
        vBoxFornecedor.setSpacing(1);
        vBoxFornecedor.setFlexVflex("ftMin");
        vBoxFornecedor.setFlexHflex("ftTrue");
        vBoxFornecedor.setScrollable(false);
        vBoxFornecedor.setBoxShadowConfigHorizontalLength(10);
        vBoxFornecedor.setBoxShadowConfigVerticalLength(10);
        vBoxFornecedor.setBoxShadowConfigBlurRadius(5);
        vBoxFornecedor.setBoxShadowConfigSpreadRadius(0);
        vBoxFornecedor.setBoxShadowConfigShadowColor("clBlack");
        vBoxFornecedor.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxFornecedor);
        vBoxFornecedor.applyProperties();
    }

    public TFLabel lblFornecedor = new TFLabel();

    private void init_lblFornecedor() {
        lblFornecedor.setName("lblFornecedor");
        lblFornecedor.setLeft(0);
        lblFornecedor.setTop(0);
        lblFornecedor.setWidth(55);
        lblFornecedor.setHeight(13);
        lblFornecedor.setCaption("Fornecedor");
        lblFornecedor.setFontColor("clWindowText");
        lblFornecedor.setFontSize(-11);
        lblFornecedor.setFontName("Tahoma");
        lblFornecedor.setFontStyle("[]");
        lblFornecedor.setVerticalAlignment("taVerticalCenter");
        lblFornecedor.setWordBreak(false);
        vBoxFornecedor.addChildren(lblFornecedor);
        lblFornecedor.applyProperties();
    }

    public TFCombo cboFornecedor = new TFCombo();

    private void init_cboFornecedor() {
        cboFornecedor.setName("cboFornecedor");
        cboFornecedor.setLeft(0);
        cboFornecedor.setTop(14);
        cboFornecedor.setWidth(189);
        cboFornecedor.setHeight(21);
        cboFornecedor.setHint("Fornecedor");
        cboFornecedor.setLookupTable(tbFornecedorEstoqueItem);
        cboFornecedor.setLookupKey("COD_FORNECEDOR");
        cboFornecedor.setLookupDesc("NOME_FORNECEDOR");
        cboFornecedor.setFlex(true);
        cboFornecedor.setHelpCaption("Fornecedor");
        cboFornecedor.setReadOnly(true);
        cboFornecedor.setRequired(false);
        cboFornecedor.setPrompt("Fornecedor");
        cboFornecedor.setConstraintCheckWhen("cwImmediate");
        cboFornecedor.setConstraintCheckType("ctExpression");
        cboFornecedor.setConstraintFocusOnError(false);
        cboFornecedor.setConstraintEnableUI(true);
        cboFornecedor.setConstraintEnabled(false);
        cboFornecedor.setConstraintFormCheck(true);
        cboFornecedor.setClearOnDelKey(false);
        cboFornecedor.setUseClearButton(false);
        cboFornecedor.setHideClearButtonOnNullValue(true);
        cboFornecedor.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFornecedorChange(event);
            processarFlow("FrmFichaItemResAvulsa", "cboFornecedor", "OnChange");
        });
        cboFornecedor.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFornecedorEnter(event);
            processarFlow("FrmFichaItemResAvulsa", "cboFornecedor", "OnEnter");
        });
        vBoxFornecedor.addChildren(cboFornecedor);
        cboFornecedor.applyProperties();
        addValidatable(cboFornecedor);
    }

    public TFHBox hBoxQtdEstqReserv = new TFHBox();

    private void init_hBoxQtdEstqReserv() {
        hBoxQtdEstqReserv.setName("hBoxQtdEstqReserv");
        hBoxQtdEstqReserv.setLeft(0);
        hBoxQtdEstqReserv.setTop(195);
        hBoxQtdEstqReserv.setWidth(485);
        hBoxQtdEstqReserv.setHeight(50);
        hBoxQtdEstqReserv.setBorderStyle("stNone");
        hBoxQtdEstqReserv.setPaddingTop(0);
        hBoxQtdEstqReserv.setPaddingLeft(0);
        hBoxQtdEstqReserv.setPaddingRight(0);
        hBoxQtdEstqReserv.setPaddingBottom(0);
        hBoxQtdEstqReserv.setMarginTop(0);
        hBoxQtdEstqReserv.setMarginLeft(0);
        hBoxQtdEstqReserv.setMarginRight(0);
        hBoxQtdEstqReserv.setMarginBottom(0);
        hBoxQtdEstqReserv.setSpacing(5);
        hBoxQtdEstqReserv.setFlexVflex("ftMin");
        hBoxQtdEstqReserv.setFlexHflex("ftTrue");
        hBoxQtdEstqReserv.setScrollable(false);
        hBoxQtdEstqReserv.setBoxShadowConfigHorizontalLength(10);
        hBoxQtdEstqReserv.setBoxShadowConfigVerticalLength(10);
        hBoxQtdEstqReserv.setBoxShadowConfigBlurRadius(5);
        hBoxQtdEstqReserv.setBoxShadowConfigSpreadRadius(0);
        hBoxQtdEstqReserv.setBoxShadowConfigShadowColor("clBlack");
        hBoxQtdEstqReserv.setBoxShadowConfigOpacity(75);
        hBoxQtdEstqReserv.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxQtdEstqReserv);
        hBoxQtdEstqReserv.applyProperties();
    }

    public TFVBox vBoxQuantidade = new TFVBox();

    private void init_vBoxQuantidade() {
        vBoxQuantidade.setName("vBoxQuantidade");
        vBoxQuantidade.setLeft(0);
        vBoxQuantidade.setTop(0);
        vBoxQuantidade.setWidth(110);
        vBoxQuantidade.setHeight(45);
        vBoxQuantidade.setBorderStyle("stNone");
        vBoxQuantidade.setPaddingTop(0);
        vBoxQuantidade.setPaddingLeft(0);
        vBoxQuantidade.setPaddingRight(0);
        vBoxQuantidade.setPaddingBottom(0);
        vBoxQuantidade.setMarginTop(0);
        vBoxQuantidade.setMarginLeft(0);
        vBoxQuantidade.setMarginRight(0);
        vBoxQuantidade.setMarginBottom(0);
        vBoxQuantidade.setSpacing(1);
        vBoxQuantidade.setFlexVflex("ftMin");
        vBoxQuantidade.setFlexHflex("ftTrue");
        vBoxQuantidade.setScrollable(false);
        vBoxQuantidade.setBoxShadowConfigHorizontalLength(10);
        vBoxQuantidade.setBoxShadowConfigVerticalLength(10);
        vBoxQuantidade.setBoxShadowConfigBlurRadius(5);
        vBoxQuantidade.setBoxShadowConfigSpreadRadius(0);
        vBoxQuantidade.setBoxShadowConfigShadowColor("clBlack");
        vBoxQuantidade.setBoxShadowConfigOpacity(75);
        hBoxQtdEstqReserv.addChildren(vBoxQuantidade);
        vBoxQuantidade.applyProperties();
    }

    public TFLabel lblQuantidade = new TFLabel();

    private void init_lblQuantidade() {
        lblQuantidade.setName("lblQuantidade");
        lblQuantidade.setLeft(0);
        lblQuantidade.setTop(0);
        lblQuantidade.setWidth(56);
        lblQuantidade.setHeight(13);
        lblQuantidade.setCaption("Quantidade");
        lblQuantidade.setFontColor("clWindowText");
        lblQuantidade.setFontSize(-11);
        lblQuantidade.setFontName("Tahoma");
        lblQuantidade.setFontStyle("[]");
        lblQuantidade.setVerticalAlignment("taVerticalCenter");
        lblQuantidade.setWordBreak(false);
        vBoxQuantidade.addChildren(lblQuantidade);
        lblQuantidade.applyProperties();
    }

    public TFInteger edtQuantidade = new TFInteger();

    private void init_edtQuantidade() {
        edtQuantidade.setName("edtQuantidade");
        edtQuantidade.setLeft(0);
        edtQuantidade.setTop(14);
        edtQuantidade.setWidth(100);
        edtQuantidade.setHeight(24);
        edtQuantidade.setHint("Quantidade");
        edtQuantidade.setTable(tbItensReservasPendencias);
        edtQuantidade.setFieldName("QTDE");
        edtQuantidade.setHelpCaption("Quantidade");
        edtQuantidade.setFlex(true);
        edtQuantidade.setRequired(false);
        edtQuantidade.setPrompt("Quantidade");
        edtQuantidade.setConstraintCheckWhen("cwImmediate");
        edtQuantidade.setConstraintCheckType("ctExpression");
        edtQuantidade.setConstraintFocusOnError(false);
        edtQuantidade.setConstraintEnableUI(true);
        edtQuantidade.setConstraintEnabled(false);
        edtQuantidade.setConstraintFormCheck(true);
        edtQuantidade.setMaxlength(0);
        edtQuantidade.setFontColor("clWindowText");
        edtQuantidade.setFontSize(-13);
        edtQuantidade.setFontName("Tahoma");
        edtQuantidade.setFontStyle("[]");
        edtQuantidade.setAlignment("taRightJustify");
        edtQuantidade.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtQuantidadeEnter(event);
            processarFlow("FrmFichaItemResAvulsa", "edtQuantidade", "OnEnter");
        });
        vBoxQuantidade.addChildren(edtQuantidade);
        edtQuantidade.applyProperties();
        addValidatable(edtQuantidade);
    }

    public TFVBox vBoxEstoque = new TFVBox();

    private void init_vBoxEstoque() {
        vBoxEstoque.setName("vBoxEstoque");
        vBoxEstoque.setLeft(110);
        vBoxEstoque.setTop(0);
        vBoxEstoque.setWidth(110);
        vBoxEstoque.setHeight(45);
        vBoxEstoque.setBorderStyle("stNone");
        vBoxEstoque.setPaddingTop(0);
        vBoxEstoque.setPaddingLeft(0);
        vBoxEstoque.setPaddingRight(0);
        vBoxEstoque.setPaddingBottom(0);
        vBoxEstoque.setMarginTop(0);
        vBoxEstoque.setMarginLeft(0);
        vBoxEstoque.setMarginRight(0);
        vBoxEstoque.setMarginBottom(0);
        vBoxEstoque.setSpacing(1);
        vBoxEstoque.setFlexVflex("ftMin");
        vBoxEstoque.setFlexHflex("ftTrue");
        vBoxEstoque.setScrollable(false);
        vBoxEstoque.setBoxShadowConfigHorizontalLength(10);
        vBoxEstoque.setBoxShadowConfigVerticalLength(10);
        vBoxEstoque.setBoxShadowConfigBlurRadius(5);
        vBoxEstoque.setBoxShadowConfigSpreadRadius(0);
        vBoxEstoque.setBoxShadowConfigShadowColor("clBlack");
        vBoxEstoque.setBoxShadowConfigOpacity(75);
        hBoxQtdEstqReserv.addChildren(vBoxEstoque);
        vBoxEstoque.applyProperties();
    }

    public TFLabel lblEstoque = new TFLabel();

    private void init_lblEstoque() {
        lblEstoque.setName("lblEstoque");
        lblEstoque.setLeft(0);
        lblEstoque.setTop(0);
        lblEstoque.setWidth(39);
        lblEstoque.setHeight(13);
        lblEstoque.setCaption("Estoque");
        lblEstoque.setFontColor("clWindowText");
        lblEstoque.setFontSize(-11);
        lblEstoque.setFontName("Tahoma");
        lblEstoque.setFontStyle("[]");
        lblEstoque.setVerticalAlignment("taVerticalCenter");
        lblEstoque.setWordBreak(false);
        vBoxEstoque.addChildren(lblEstoque);
        lblEstoque.applyProperties();
    }

    public TFInteger edtEstoque = new TFInteger();

    private void init_edtEstoque() {
        edtEstoque.setName("edtEstoque");
        edtEstoque.setLeft(0);
        edtEstoque.setTop(14);
        edtEstoque.setWidth(100);
        edtEstoque.setHeight(24);
        edtEstoque.setHint("Estoque");
        edtEstoque.setTable(tbEstoque);
        edtEstoque.setFieldName("QTDE");
        edtEstoque.setHelpCaption("Estoque");
        edtEstoque.setFlex(true);
        edtEstoque.setRequired(false);
        edtEstoque.setPrompt("Estoque");
        edtEstoque.setConstraintCheckWhen("cwImmediate");
        edtEstoque.setConstraintCheckType("ctExpression");
        edtEstoque.setConstraintFocusOnError(false);
        edtEstoque.setConstraintEnableUI(true);
        edtEstoque.setConstraintEnabled(false);
        edtEstoque.setConstraintFormCheck(true);
        edtEstoque.setMaxlength(0);
        edtEstoque.setEnabled(false);
        edtEstoque.setFontColor("clWindowText");
        edtEstoque.setFontSize(-13);
        edtEstoque.setFontName("Tahoma");
        edtEstoque.setFontStyle("[]");
        edtEstoque.setColor("clBtnFace");
        edtEstoque.setAlignment("taRightJustify");
        vBoxEstoque.addChildren(edtEstoque);
        edtEstoque.applyProperties();
        addValidatable(edtEstoque);
    }

    public TFVBox vBoxReservado = new TFVBox();

    private void init_vBoxReservado() {
        vBoxReservado.setName("vBoxReservado");
        vBoxReservado.setLeft(220);
        vBoxReservado.setTop(0);
        vBoxReservado.setWidth(110);
        vBoxReservado.setHeight(45);
        vBoxReservado.setBorderStyle("stNone");
        vBoxReservado.setPaddingTop(0);
        vBoxReservado.setPaddingLeft(0);
        vBoxReservado.setPaddingRight(0);
        vBoxReservado.setPaddingBottom(0);
        vBoxReservado.setMarginTop(0);
        vBoxReservado.setMarginLeft(0);
        vBoxReservado.setMarginRight(0);
        vBoxReservado.setMarginBottom(0);
        vBoxReservado.setSpacing(1);
        vBoxReservado.setFlexVflex("ftMin");
        vBoxReservado.setFlexHflex("ftTrue");
        vBoxReservado.setScrollable(false);
        vBoxReservado.setBoxShadowConfigHorizontalLength(10);
        vBoxReservado.setBoxShadowConfigVerticalLength(10);
        vBoxReservado.setBoxShadowConfigBlurRadius(5);
        vBoxReservado.setBoxShadowConfigSpreadRadius(0);
        vBoxReservado.setBoxShadowConfigShadowColor("clBlack");
        vBoxReservado.setBoxShadowConfigOpacity(75);
        hBoxQtdEstqReserv.addChildren(vBoxReservado);
        vBoxReservado.applyProperties();
    }

    public TFLabel lblReservado = new TFLabel();

    private void init_lblReservado() {
        lblReservado.setName("lblReservado");
        lblReservado.setLeft(0);
        lblReservado.setTop(0);
        lblReservado.setWidth(52);
        lblReservado.setHeight(13);
        lblReservado.setCaption("Reservado");
        lblReservado.setFontColor("clWindowText");
        lblReservado.setFontSize(-11);
        lblReservado.setFontName("Tahoma");
        lblReservado.setFontStyle("[]");
        lblReservado.setVerticalAlignment("taVerticalCenter");
        lblReservado.setWordBreak(false);
        vBoxReservado.addChildren(lblReservado);
        lblReservado.applyProperties();
    }

    public TFInteger edtReservado = new TFInteger();

    private void init_edtReservado() {
        edtReservado.setName("edtReservado");
        edtReservado.setLeft(0);
        edtReservado.setTop(14);
        edtReservado.setWidth(100);
        edtReservado.setHeight(24);
        edtReservado.setHint("Reservado");
        edtReservado.setTable(tbEstoque);
        edtReservado.setFieldName("RESERVADO");
        edtReservado.setHelpCaption("Reservado");
        edtReservado.setFlex(true);
        edtReservado.setRequired(false);
        edtReservado.setPrompt("Reservado");
        edtReservado.setConstraintCheckWhen("cwImmediate");
        edtReservado.setConstraintCheckType("ctExpression");
        edtReservado.setConstraintFocusOnError(false);
        edtReservado.setConstraintEnableUI(true);
        edtReservado.setConstraintEnabled(false);
        edtReservado.setConstraintFormCheck(true);
        edtReservado.setMaxlength(0);
        edtReservado.setEnabled(false);
        edtReservado.setFontColor("clWindowText");
        edtReservado.setFontSize(-13);
        edtReservado.setFontName("Tahoma");
        edtReservado.setFontStyle("[]");
        edtReservado.setColor("clBtnFace");
        edtReservado.setAlignment("taRightJustify");
        vBoxReservado.addChildren(edtReservado);
        edtReservado.applyProperties();
        addValidatable(edtReservado);
    }

    public TFHBox hBoxNomeTelefone = new TFHBox();

    private void init_hBoxNomeTelefone() {
        hBoxNomeTelefone.setName("hBoxNomeTelefone");
        hBoxNomeTelefone.setLeft(0);
        hBoxNomeTelefone.setTop(246);
        hBoxNomeTelefone.setWidth(485);
        hBoxNomeTelefone.setHeight(50);
        hBoxNomeTelefone.setBorderStyle("stNone");
        hBoxNomeTelefone.setPaddingTop(0);
        hBoxNomeTelefone.setPaddingLeft(0);
        hBoxNomeTelefone.setPaddingRight(0);
        hBoxNomeTelefone.setPaddingBottom(0);
        hBoxNomeTelefone.setMarginTop(0);
        hBoxNomeTelefone.setMarginLeft(0);
        hBoxNomeTelefone.setMarginRight(0);
        hBoxNomeTelefone.setMarginBottom(0);
        hBoxNomeTelefone.setSpacing(5);
        hBoxNomeTelefone.setFlexVflex("ftMin");
        hBoxNomeTelefone.setFlexHflex("ftTrue");
        hBoxNomeTelefone.setScrollable(false);
        hBoxNomeTelefone.setBoxShadowConfigHorizontalLength(10);
        hBoxNomeTelefone.setBoxShadowConfigVerticalLength(10);
        hBoxNomeTelefone.setBoxShadowConfigBlurRadius(5);
        hBoxNomeTelefone.setBoxShadowConfigSpreadRadius(0);
        hBoxNomeTelefone.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomeTelefone.setBoxShadowConfigOpacity(75);
        hBoxNomeTelefone.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxNomeTelefone);
        hBoxNomeTelefone.applyProperties();
    }

    public TFVBox vBoxNomeCliente = new TFVBox();

    private void init_vBoxNomeCliente() {
        vBoxNomeCliente.setName("vBoxNomeCliente");
        vBoxNomeCliente.setLeft(0);
        vBoxNomeCliente.setTop(0);
        vBoxNomeCliente.setWidth(279);
        vBoxNomeCliente.setHeight(45);
        vBoxNomeCliente.setBorderStyle("stNone");
        vBoxNomeCliente.setPaddingTop(0);
        vBoxNomeCliente.setPaddingLeft(0);
        vBoxNomeCliente.setPaddingRight(0);
        vBoxNomeCliente.setPaddingBottom(0);
        vBoxNomeCliente.setMarginTop(0);
        vBoxNomeCliente.setMarginLeft(0);
        vBoxNomeCliente.setMarginRight(0);
        vBoxNomeCliente.setMarginBottom(0);
        vBoxNomeCliente.setSpacing(1);
        vBoxNomeCliente.setFlexVflex("ftMin");
        vBoxNomeCliente.setFlexHflex("ftTrue");
        vBoxNomeCliente.setScrollable(false);
        vBoxNomeCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeCliente.setBoxShadowConfigVerticalLength(10);
        vBoxNomeCliente.setBoxShadowConfigBlurRadius(5);
        vBoxNomeCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeCliente.setBoxShadowConfigOpacity(75);
        hBoxNomeTelefone.addChildren(vBoxNomeCliente);
        vBoxNomeCliente.applyProperties();
    }

    public TFLabel lblNomeCliente = new TFLabel();

    private void init_lblNomeCliente() {
        lblNomeCliente.setName("lblNomeCliente");
        lblNomeCliente.setLeft(0);
        lblNomeCliente.setTop(0);
        lblNomeCliente.setWidth(76);
        lblNomeCliente.setHeight(13);
        lblNomeCliente.setCaption("Nome do cliente");
        lblNomeCliente.setFontColor("clWindowText");
        lblNomeCliente.setFontSize(-11);
        lblNomeCliente.setFontName("Tahoma");
        lblNomeCliente.setFontStyle("[]");
        lblNomeCliente.setVerticalAlignment("taVerticalCenter");
        lblNomeCliente.setWordBreak(false);
        vBoxNomeCliente.addChildren(lblNomeCliente);
        lblNomeCliente.applyProperties();
    }

    public TFString edtNomeCliente = new TFString();

    private void init_edtNomeCliente() {
        edtNomeCliente.setName("edtNomeCliente");
        edtNomeCliente.setLeft(0);
        edtNomeCliente.setTop(14);
        edtNomeCliente.setWidth(274);
        edtNomeCliente.setHeight(24);
        edtNomeCliente.setHint("Nome do cliente\r\n\r\nSe o par\u00E2metro \"PARM_SYS3.CRMPARTS_OBR_PESQ_CLI_RES_AVUL\" tiver o valor \"S\" este campo ser\u00E1 desabilitado, obrigando a utiliza\u00E7\u00E3o do bot\u00E3o \"Pesquisar cliente\".");
        edtNomeCliente.setTable(tbItensReservasPendencias);
        edtNomeCliente.setFieldName("CLIENTE_NOME");
        edtNomeCliente.setHelpCaption("Nome do cliente");
        edtNomeCliente.setFlex(true);
        edtNomeCliente.setRequired(false);
        edtNomeCliente.setPrompt("Nome do cliente");
        edtNomeCliente.setConstraintCheckWhen("cwImmediate");
        edtNomeCliente.setConstraintCheckType("ctExpression");
        edtNomeCliente.setConstraintFocusOnError(false);
        edtNomeCliente.setConstraintEnableUI(true);
        edtNomeCliente.setConstraintEnabled(false);
        edtNomeCliente.setConstraintFormCheck(true);
        edtNomeCliente.setCharCase("ccNormal");
        edtNomeCliente.setPwd(false);
        edtNomeCliente.setMaxlength(150);
        edtNomeCliente.setFontColor("clWindowText");
        edtNomeCliente.setFontSize(-13);
        edtNomeCliente.setFontName("Tahoma");
        edtNomeCliente.setFontStyle("[]");
        edtNomeCliente.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtNomeClienteEnter(event);
            processarFlow("FrmFichaItemResAvulsa", "edtNomeCliente", "OnEnter");
        });
        edtNomeCliente.setSaveLiteralCharacter(false);
        edtNomeCliente.applyProperties();
        vBoxNomeCliente.addChildren(edtNomeCliente);
        addValidatable(edtNomeCliente);
    }

    public TFVBox vBoxConsultarCliente = new TFVBox();

    private void init_vBoxConsultarCliente() {
        vBoxConsultarCliente.setName("vBoxConsultarCliente");
        vBoxConsultarCliente.setLeft(279);
        vBoxConsultarCliente.setTop(0);
        vBoxConsultarCliente.setWidth(30);
        vBoxConsultarCliente.setHeight(45);
        vBoxConsultarCliente.setBorderStyle("stNone");
        vBoxConsultarCliente.setPaddingTop(0);
        vBoxConsultarCliente.setPaddingLeft(0);
        vBoxConsultarCliente.setPaddingRight(0);
        vBoxConsultarCliente.setPaddingBottom(0);
        vBoxConsultarCliente.setMarginTop(0);
        vBoxConsultarCliente.setMarginLeft(0);
        vBoxConsultarCliente.setMarginRight(0);
        vBoxConsultarCliente.setMarginBottom(0);
        vBoxConsultarCliente.setSpacing(1);
        vBoxConsultarCliente.setFlexVflex("ftMin");
        vBoxConsultarCliente.setFlexHflex("ftMin");
        vBoxConsultarCliente.setScrollable(false);
        vBoxConsultarCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxConsultarCliente.setBoxShadowConfigVerticalLength(10);
        vBoxConsultarCliente.setBoxShadowConfigBlurRadius(5);
        vBoxConsultarCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxConsultarCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxConsultarCliente.setBoxShadowConfigOpacity(75);
        hBoxNomeTelefone.addChildren(vBoxConsultarCliente);
        vBoxConsultarCliente.applyProperties();
    }

    public TFHBox hBoxConsultarCliente = new TFHBox();

    private void init_hBoxConsultarCliente() {
        hBoxConsultarCliente.setName("hBoxConsultarCliente");
        hBoxConsultarCliente.setLeft(0);
        hBoxConsultarCliente.setTop(0);
        hBoxConsultarCliente.setWidth(20);
        hBoxConsultarCliente.setHeight(17);
        hBoxConsultarCliente.setBorderStyle("stNone");
        hBoxConsultarCliente.setPaddingTop(0);
        hBoxConsultarCliente.setPaddingLeft(0);
        hBoxConsultarCliente.setPaddingRight(0);
        hBoxConsultarCliente.setPaddingBottom(0);
        hBoxConsultarCliente.setMarginTop(0);
        hBoxConsultarCliente.setMarginLeft(0);
        hBoxConsultarCliente.setMarginRight(0);
        hBoxConsultarCliente.setMarginBottom(0);
        hBoxConsultarCliente.setSpacing(1);
        hBoxConsultarCliente.setFlexVflex("ftFalse");
        hBoxConsultarCliente.setFlexHflex("ftFalse");
        hBoxConsultarCliente.setScrollable(false);
        hBoxConsultarCliente.setBoxShadowConfigHorizontalLength(10);
        hBoxConsultarCliente.setBoxShadowConfigVerticalLength(10);
        hBoxConsultarCliente.setBoxShadowConfigBlurRadius(5);
        hBoxConsultarCliente.setBoxShadowConfigSpreadRadius(0);
        hBoxConsultarCliente.setBoxShadowConfigShadowColor("clBlack");
        hBoxConsultarCliente.setBoxShadowConfigOpacity(75);
        hBoxConsultarCliente.setVAlign("tvTop");
        vBoxConsultarCliente.addChildren(hBoxConsultarCliente);
        hBoxConsultarCliente.applyProperties();
    }

    public TFButton btnPesquisarCliente = new TFButton();

    private void init_btnPesquisarCliente() {
        btnPesquisarCliente.setName("btnPesquisarCliente");
        btnPesquisarCliente.setLeft(0);
        btnPesquisarCliente.setTop(18);
        btnPesquisarCliente.setWidth(25);
        btnPesquisarCliente.setHeight(25);
        btnPesquisarCliente.setHint("Pesquisar cliente");
        btnPesquisarCliente.setFontColor("clWindowText");
        btnPesquisarCliente.setFontSize(-11);
        btnPesquisarCliente.setFontName("Tahoma");
        btnPesquisarCliente.setFontStyle("[]");
        btnPesquisarCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClienteClick(event);
            processarFlow("FrmFichaItemResAvulsa", "btnPesquisarCliente", "OnClick");
        });
        btnPesquisarCliente.setImageId(0);
        btnPesquisarCliente.setColor("clBtnFace");
        btnPesquisarCliente.setAccess(false);
        btnPesquisarCliente.setIconClass("search");
        btnPesquisarCliente.setIconReverseDirection(false);
        vBoxConsultarCliente.addChildren(btnPesquisarCliente);
        btnPesquisarCliente.applyProperties();
    }

    public TFVBox vBoxTelefone = new TFVBox();

    private void init_vBoxTelefone() {
        vBoxTelefone.setName("vBoxTelefone");
        vBoxTelefone.setLeft(309);
        vBoxTelefone.setTop(0);
        vBoxTelefone.setWidth(170);
        vBoxTelefone.setHeight(45);
        vBoxTelefone.setBorderStyle("stNone");
        vBoxTelefone.setPaddingTop(0);
        vBoxTelefone.setPaddingLeft(0);
        vBoxTelefone.setPaddingRight(0);
        vBoxTelefone.setPaddingBottom(0);
        vBoxTelefone.setMarginTop(0);
        vBoxTelefone.setMarginLeft(0);
        vBoxTelefone.setMarginRight(0);
        vBoxTelefone.setMarginBottom(0);
        vBoxTelefone.setSpacing(1);
        vBoxTelefone.setFlexVflex("ftMin");
        vBoxTelefone.setFlexHflex("ftMin");
        vBoxTelefone.setScrollable(false);
        vBoxTelefone.setBoxShadowConfigHorizontalLength(10);
        vBoxTelefone.setBoxShadowConfigVerticalLength(10);
        vBoxTelefone.setBoxShadowConfigBlurRadius(5);
        vBoxTelefone.setBoxShadowConfigSpreadRadius(0);
        vBoxTelefone.setBoxShadowConfigShadowColor("clBlack");
        vBoxTelefone.setBoxShadowConfigOpacity(75);
        hBoxNomeTelefone.addChildren(vBoxTelefone);
        vBoxTelefone.applyProperties();
    }

    public TFLabel lblTelefone = new TFLabel();

    private void init_lblTelefone() {
        lblTelefone.setName("lblTelefone");
        lblTelefone.setLeft(0);
        lblTelefone.setTop(0);
        lblTelefone.setWidth(42);
        lblTelefone.setHeight(13);
        lblTelefone.setCaption("Telefone");
        lblTelefone.setFontColor("clWindowText");
        lblTelefone.setFontSize(-11);
        lblTelefone.setFontName("Tahoma");
        lblTelefone.setFontStyle("[]");
        lblTelefone.setVerticalAlignment("taVerticalCenter");
        lblTelefone.setWordBreak(false);
        vBoxTelefone.addChildren(lblTelefone);
        lblTelefone.applyProperties();
    }

    public TFString edtTelefone = new TFString();

    private void init_edtTelefone() {
        edtTelefone.setName("edtTelefone");
        edtTelefone.setLeft(0);
        edtTelefone.setTop(14);
        edtTelefone.setWidth(140);
        edtTelefone.setHeight(24);
        edtTelefone.setHint("Telefone");
        edtTelefone.setTable(tbItensReservasPendencias);
        edtTelefone.setFieldName("CLIENTE_TELEFONE");
        edtTelefone.setHelpCaption("Telefone");
        edtTelefone.setFlex(false);
        edtTelefone.setRequired(false);
        edtTelefone.setPrompt("Telefone");
        edtTelefone.setConstraintCheckWhen("cwImmediate");
        edtTelefone.setConstraintCheckType("ctExpression");
        edtTelefone.setConstraintFocusOnError(false);
        edtTelefone.setConstraintEnableUI(true);
        edtTelefone.setConstraintEnabled(false);
        edtTelefone.setConstraintFormCheck(true);
        edtTelefone.setCharCase("ccNormal");
        edtTelefone.setPwd(false);
        edtTelefone.setMask("(99) 99999-999?9");
        edtTelefone.setMaxlength(0);
        edtTelefone.setFontColor("clWindowText");
        edtTelefone.setFontSize(-13);
        edtTelefone.setFontName("Tahoma");
        edtTelefone.setFontStyle("[]");
        edtTelefone.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtTelefoneChange(event);
            processarFlow("FrmFichaItemResAvulsa", "edtTelefone", "OnChange");
        });
        edtTelefone.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtTelefoneEnter(event);
            processarFlow("FrmFichaItemResAvulsa", "edtTelefone", "OnEnter");
        });
        edtTelefone.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtTelefoneExit(event);
            processarFlow("FrmFichaItemResAvulsa", "edtTelefone", "OnExit");
        });
        edtTelefone.setSaveLiteralCharacter(false);
        edtTelefone.applyProperties();
        vBoxTelefone.addChildren(edtTelefone);
        addValidatable(edtTelefone);
    }

    public TFVBox vBoxObservacao = new TFVBox();

    private void init_vBoxObservacao() {
        vBoxObservacao.setName("vBoxObservacao");
        vBoxObservacao.setLeft(0);
        vBoxObservacao.setTop(297);
        vBoxObservacao.setWidth(485);
        vBoxObservacao.setHeight(45);
        vBoxObservacao.setBorderStyle("stNone");
        vBoxObservacao.setPaddingTop(0);
        vBoxObservacao.setPaddingLeft(0);
        vBoxObservacao.setPaddingRight(0);
        vBoxObservacao.setPaddingBottom(0);
        vBoxObservacao.setMarginTop(0);
        vBoxObservacao.setMarginLeft(0);
        vBoxObservacao.setMarginRight(0);
        vBoxObservacao.setMarginBottom(0);
        vBoxObservacao.setSpacing(5);
        vBoxObservacao.setFlexVflex("ftMin");
        vBoxObservacao.setFlexHflex("ftTrue");
        vBoxObservacao.setScrollable(false);
        vBoxObservacao.setBoxShadowConfigHorizontalLength(10);
        vBoxObservacao.setBoxShadowConfigVerticalLength(10);
        vBoxObservacao.setBoxShadowConfigBlurRadius(5);
        vBoxObservacao.setBoxShadowConfigSpreadRadius(0);
        vBoxObservacao.setBoxShadowConfigShadowColor("clBlack");
        vBoxObservacao.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxObservacao);
        vBoxObservacao.applyProperties();
    }

    public TFLabel lblObservacao = new TFLabel();

    private void init_lblObservacao() {
        lblObservacao.setName("lblObservacao");
        lblObservacao.setLeft(0);
        lblObservacao.setTop(0);
        lblObservacao.setWidth(58);
        lblObservacao.setHeight(13);
        lblObservacao.setCaption("Observa\u00E7\u00E3o");
        lblObservacao.setFontColor("clWindowText");
        lblObservacao.setFontSize(-11);
        lblObservacao.setFontName("Tahoma");
        lblObservacao.setFontStyle("[]");
        lblObservacao.setVerticalAlignment("taVerticalCenter");
        lblObservacao.setWordBreak(false);
        vBoxObservacao.addChildren(lblObservacao);
        lblObservacao.applyProperties();
    }

    public TFString edtObservacao = new TFString();

    private void init_edtObservacao() {
        edtObservacao.setName("edtObservacao");
        edtObservacao.setLeft(0);
        edtObservacao.setTop(14);
        edtObservacao.setWidth(455);
        edtObservacao.setHeight(24);
        edtObservacao.setHint("Observa\u00E7\u00E3o");
        edtObservacao.setTable(tbItensReservasPendencias);
        edtObservacao.setFieldName("OBSERVACAO");
        edtObservacao.setHelpCaption("Observa\u00E7\u00E3o");
        edtObservacao.setFlex(true);
        edtObservacao.setRequired(false);
        edtObservacao.setPrompt("Observa\u00E7\u00E3o");
        edtObservacao.setConstraintCheckWhen("cwImmediate");
        edtObservacao.setConstraintCheckType("ctExpression");
        edtObservacao.setConstraintFocusOnError(false);
        edtObservacao.setConstraintEnableUI(true);
        edtObservacao.setConstraintEnabled(false);
        edtObservacao.setConstraintFormCheck(true);
        edtObservacao.setCharCase("ccNormal");
        edtObservacao.setPwd(false);
        edtObservacao.setMaxlength(200);
        edtObservacao.setFontColor("clWindowText");
        edtObservacao.setFontSize(-13);
        edtObservacao.setFontName("Tahoma");
        edtObservacao.setFontStyle("[]");
        edtObservacao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtObservacaoEnter(event);
            processarFlow("FrmFichaItemResAvulsa", "edtObservacao", "OnEnter");
        });
        edtObservacao.setSaveLiteralCharacter(false);
        edtObservacao.applyProperties();
        vBoxObservacao.addChildren(edtObservacao);
        addValidatable(edtObservacao);
    }

    public TFSchema scFornecedorEstoqueItem;

    private void init_scFornecedorEstoqueItem() {
        scFornecedorEstoqueItem = rn.scFornecedorEstoqueItem;
        scFornecedorEstoqueItem.setName("scFornecedorEstoqueItem");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbFornecedorEstoqueItem);
        scFornecedorEstoqueItem.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbItens);
        scFornecedorEstoqueItem.getTables().add(item1);
        scFornecedorEstoqueItem.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cboEmpresasChange(final Event<Object> event);

    public abstract void cboEmpresasEnter(final Event<Object> event);

    public abstract void edtCodigoEnter(final Event<Object> event);

    public abstract void edtCodigoExit(final Event<Object> event);

    public abstract void icoPesquisarItemClick(final Event<Object> event);

    public abstract void cboFornecedorChange(final Event<Object> event);

    public abstract void cboFornecedorEnter(final Event<Object> event);

    public abstract void edtQuantidadeEnter(final Event<Object> event);

    public abstract void edtNomeClienteEnter(final Event<Object> event);

    public void btnPesquisarClienteClick(final Event<Object> event) {
        if (btnPesquisarCliente.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisarCliente");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtTelefoneChange(final Event<Object> event);

    public abstract void edtTelefoneEnter(final Event<Object> event);

    public abstract void edtTelefoneExit(final Event<Object> event);

    public abstract void edtObservacaoEnter(final Event<Object> event);

    public abstract void tbLeadsEmpresasUsuariosAfterScroll(final Event<Object> event);

}