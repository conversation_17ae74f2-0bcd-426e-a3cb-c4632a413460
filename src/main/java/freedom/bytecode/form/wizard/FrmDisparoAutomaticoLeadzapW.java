/* -------------------------------------------------------------------------
   Projeto Freedom - Cliente - Versao: 1.0.4.50
   Class Main  : DisparoAutomaticoLeadzap
   Analista    : EMERSON
   Data Created: 10/12/2019 13:39:16
   Data Changed: 10/02/2020 11:18:27
  -------------------------------------------------------------------------- */

package freedom.bytecode.form.wizard;

import freedom.bytecode.rn.DisparoAutomaticoLeadzapRNA;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.grid.TFGridExporter;
import freedom.client.controls.impl.treegrid.TFTreeGridExporter;
import freedom.client.controls.IBaseComponent;
import freedom.client.event.Event;
import freedom.client.controls.IFocusable;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.RowState;
import freedom.data.TableState;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.impl.Row;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;


public abstract class FrmDisparoAutomaticoLeadzapW extends FrmDisparoAutomaticoLeadzap {

    private static final long serialVersionUID = 20130827081850L;
    public TableState oper = TableState.QUERYING; 

    public FrmDisparoAutomaticoLeadzapW() {
        lblMensagem.setCaption("");
        habilitaComp(false);
        
		tabListagem.setFontSize(-20);
		tabCadastro.setFontSize(-20);

        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao Abrir Tabelas Auxiliares")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnFiltroAvancado.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled && !menuSelecaoMultipla.isChecked());
        btnAlterar.setEnabled(!enabled && !tbDisparo.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbDisparo.isEmpty());
        if (! menuHabilitaNavegacao.isChecked()) {                                       // menu popup habilitar navegação
            btnProximo.setEnabled(!enabled && !tbDisparo.isEmpty());
            btnAnterior.setEnabled(!enabled && !tbDisparo.isEmpty());
        }
        btnAceitar.setEnabled(!enabled && !tbDisparo.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);
        btnSalvarContinuar.setEnabled(enabled && !menuSelecaoMultipla.isChecked());
        menuSelecaoMultipla.setVisible(!enabled);
        
        edDescricao44001.setEnabled(enabled && ( ! tbDisparo.isEmpty()  || tbDisparo.getState() == TableState.INSERTING));
        edTemplate44001.setEnabled(enabled && ( ! tbDisparo.isEmpty()  || tbDisparo.getState() == TableState.INSERTING));
        edIdCelular44001.setEnabled(enabled && ( ! tbDisparo.isEmpty()  || tbDisparo.getState() == TableState.INSERTING));

        
    }

    @Override
    public void btnConsultarClick(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }
    
    @Override
    public void btnFiltroAvancadoClick(Event<Object> event) {
        filtroAvancado.doModal();
    }    

    @Override
    public void btnNovoClick(final Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            if (menuSelecaoMultipla.isChecked()) {
                onSalvarMultiplo();
            } else {
                onSalvar();
            }
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    public void executaFiltroPrincipal() throws Exception {
        tbDisparo.clearFilters();
        if (! efDescricao.getValue().asString().isEmpty()) {
           tbDisparo.setFilterDESCRICAO(efDescricao.getValue());
        }
        if (efAtivo.isChecked()) {
           tbDisparo.setFilterATIVO(efAtivo.getValue());
        }

    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        if (menuHabilitaNavegacao.isChecked()) {
            btnProximo.setEnabled(true);
            btnAnterior.setEnabled(true);
        } else {
            btnProximo.setEnabled(btnNovo.isEnabled());
            btnAnterior.setEnabled(btnNovo.isEnabled());
        }
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {

        boolean checkedMenu = menuSelecaoMultipla.isChecked();
        gridPrincipal.setMultiSelection(checkedMenu);

        // tratamento das abas visto que pode mexer somente na tabela master
        for (int i = 2; i <= pgPrincipal.getPageCount()-1; i++) {
             pgPrincipal.selectTab(i);
             pgPrincipal.getSelectedTab().setVisible(!checkedMenu);
        }

        // opções da barra de ferramenta
        btnNovo.setEnabled(! checkedMenu && btnAlterar.isEnabled());
        btnSalvarContinuar.setEnabled(! checkedMenu && btnAlterar.isEnabled());


        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()+30);
            // desregistra o marter table dos componentes
                        edDescricao44001.setTable(null);
            edDescricao44001.setValue(null);
            edTemplate44001.setTable(null);
            edTemplate44001.setValue(null);
            edIdCelular44001.setTable(null);
            edIdCelular44001.setValue(null);

            gridPrincipal.clearSelection();
        } else {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()-30);
            // registra o master table para os componentes
                        edDescricao44001.setTable(tbDisparo);
            edTemplate44001.setTable(tbDisparo);
            edIdCelular44001.setTable(tbDisparo);

        }
        pgPrincipal.selectTab(0);
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionPesquisar(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionIncluir(Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionExcluir(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionSalvar(Event<Object> event) {
        try {
            onSalvar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionSalvarContinuar(Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionCancelar(Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionAnterior(Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionProximo(Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmDisparoAutomaticoLeadzapkeyActionAceitar(Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }    
    
    @Override
    public void btnMaisClick(Event<Object> event) {
        popMenuPrincipal.open(this);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(Event<Object> event) {
        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Aviso")
                  .message("Tabela Auxiliares Foram Reabertas")
                  .showInformation();
        }
    }

    @Override
    public void menuItemConfgGridClick(Event<Object> event) {
        gridConfig.doModal();
    }

    @Override
    public void menuItemHelpClick(Event<Object> event) {
        FormUtil.redirect("help/FrmDisparoAutomaticoLeadzap.zul", true);
    }

    protected void onConsultar() throws Exception {
        
        tbDisparo.close();
        executaFiltroPrincipal();
        tbDisparo.setOrderBy("DESCRICAO");
        tbDisparo.open();
        habilitaComp(false);

        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.clearSelection();
        }

        if (tbDisparo.isEmpty()) {
            Dialog.create()
                      .title("Aviso")
                      .message("Registro Não Encontrado...")
                      .showInformation();
        }
    }

    protected void onAnterior() throws Exception {

        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbDisparo.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnAnterior);
        }

        if (tbDisparo.bof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o Primeiro Registro")
                    .showInformation();
        } else {
            tbDisparo.prior();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onProximo() throws Exception {
        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbDisparo.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnProximo);
        }

        if (tbDisparo.eof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o último registgro")
                    .showInformation();
        } else {
            tbDisparo.next();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onIncluir() throws Exception {
        oper = TableState.INSERTING; 
        
        rn.incluir(); 
        
        
        pgPrincipal.selectTab(1);
        edDescricao44001.setFocus();
        habilitaComp(true);
        lblMensagem.setCaption("Incluindo...");
    }

    protected void onAlterar() throws Exception {
        oper = TableState.MODIFYING;

        if (menuSelecaoMultipla.isChecked()) {
            lblMensagem.setCaption("ATENÇÃO: Alterando multiplos registros. Será alterado todos os registros selecionados...");
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } else {
            if (!tbDisparo.isEmpty()) {
                rn.alterar();
                if (pgPrincipal.getSelectedIndex() == 0)  {
                   pgPrincipal.selectTab(1);
                }                
                habilitaComp(true);
                edDescricao44001.setFocus();
                lblMensagem.setCaption("Alterando "+tbDisparo.getDESCRICAO().asString()+"...");
            } else {
                Dialog.create()
                      .title("Erro ao editar")
                      .message("Selecione um registro antes de editar")
                      .showError();
            }
        }
    }

    protected void onExcluir() throws DataException {
        if (!tbDisparo.isEmpty()) {
           oper = TableState.DELETING; 
           String titulo;
           String mensagem;
           if (menuSelecaoMultipla.isChecked()) {
               titulo = "Exclusão Multipla";
               mensagem = "ATENÇÃO: Serão excluido(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?";
           } else {
               titulo = "Exclusão de Registro";
               mensagem = "Confirma a exclusão do registro selecionado?";
           }

            Dialog.create()
                    .title(titulo)
                    .message(mensagem)
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                try {
                                    tbDisparo.disableControls();
                                    tbDisparo.disableMasterTable();
                                    if (menuSelecaoMultipla.isChecked()) {
                                        for (int bm : gridPrincipal.getSelectedIndices(false)) {
                                            tbDisparo.gotoBookmark(bm);
                                            rn.excluiTableMaster();
                                        }
                                    } else {
                                        rn.excluiTableMaster();
                                    }

                                    try {
                                        rn.excluir();                                        
                                        
                                        habilitaComp(false);
                                    } catch (DataException e) {
                                        throw e;
                                    }
                                } finally {
                                    tbDisparo.enableControls();
                                    tbDisparo.enableMasterTable();
                                    oper = TableState.QUERYING;
                                }
                            } catch (DataException ex) {
                                Dialog.create()
                                    .title("Erro ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex);

                                try {
                                    tbDisparo.cancelUpdates();
                                } catch (DataException ex1) {
                                    Dialog.create()
                                    .title("Erro no CancelUpdates ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex1);
                                }

                            }
                        }
                    });
        } else {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message("Selecione um registro antes de excluir")
                    .showError();
        }
    }

    protected void onSalvarMultiplo() throws DataException {

        Dialog.create()
            .title("Alteração Multipla")
            .message("ATENÇÃO: Serão alterado(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?")
            .confirmSimNao((String dialogResult) -> {
                if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                     try {
                           tbDisparo.disableControls();
                           int lastBookmark = tbDisparo.getBookmark();
                           try {
                               for (int bm : gridPrincipal.getSelectedIndices(true)) {
                                   tbDisparo.gotoBookmark(bm);
                                   tbDisparo.edit();
                                   
                                   if ( ! edDescricao44001.getValue().isNull()) {
                                       tbDisparo.setDESCRICAO(edDescricao44001.getValue());
                                   }
                                   if ( ! edTemplate44001.getValue().isNull()) {
                                       tbDisparo.setTEMPLATE(edTemplate44001.getValue());
                                   }
                                   if ( ! edIdCelular44001.getValue().isNull()) {
                                       tbDisparo.setID_CELULAR(edIdCelular44001.getValue());
                                   }

                                   tbDisparo.post();
                               }

                               onSalvar();

                           } finally {
                               tbDisparo.close();
                               tbDisparo.open();
                               // tbDisparo.gotoBookmark(lastBookmark);
                               tbDisparo.enableControls();
                               gridPrincipal.clearSelection();
                           }

                     } catch (DataException e) {
                         Dialog.create()
                               .title("Erro ao salvar")
                               .message(e.getMessage())
                               .showException(e);
                    }
                }
        });
    }

    protected void onSalvar() throws DataException {
        // executa a validação das constraint dos objetos edition
        check();
        if (!getErrorMap().isEmpty()) {
            StringBuilder strBuilder = new StringBuilder();

            getErrorMap().values().stream().forEach((s) -> {
                strBuilder.append(s).append("\n");
            });        

            // manda o focu para o primeiro objeto que deu erro de constraint
            ((IFocusable)getErrorMap().keySet().iterator().next()).setFocus();

            Dialog.create()
                  .title("Erro ao validar")
                  .message("Existe validação(s) pendente...\n" + strBuilder.toString())
                  .showError();

            return;
        }

        // seta Calc. Update
        setCalcUpdate();

        // executar o metodo salvar na RN
        rn.salvar();

        // atualiza o registro
        tbDisparo.refreshRecord();
        
        habilitaComp(false);
        oper = TableState.QUERYING;
        lblMensagem.setCaption("");
    }

    protected void onSalvarContinuar() throws DataException {
        try {
            TableState st = tbDisparo.getState();
            onSalvar();
            if (st == TableState.INSERTING) {
                onIncluir();
            } else if (st == TableState.MODIFYING) {
                onAlterar();
            }
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar a edição")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void onCancelar() throws DataException {
        habilitaComp(false);
        rn.cancelar();
        
        oper = TableState.QUERYING; 
        lblMensagem.setCaption("Registro Selecionado: "+tbDisparo.getDESCRICAO().asString());
    }
    
    protected void onAceitar() throws Exception {
        if (FormUtil.isExternalCall()) {
            // passa os parametros para a resposta ao VB
            FormUtil.externalCall(tbDisparo.getField("ID_PESSOA").asString());
        } else {
            close();
        }
    }
    
    protected void onAbreTabelaAux() throws DataException {
        ISession s = SessionFactory.getInstance().getSession();
        try {                
            s.open();
            tbEmailModelo.close();
            tbEmailModelo.open();
            tbCadastroWhatsapp.setOrderBy("DESCRICAO");
            tbCadastroWhatsapp.close();
            tbCadastroWhatsapp.open();
            tbDisparo.setSession(s);
            tbDisparo.refreshRecord();
            tbDisparo.setSession(null);
        } finally {
            if (s != null) {
                s.close();
            }
        }
    }
    
    protected void setCalcUpdate() throws DataException {
        
        postTable();
    }

    private void postTable() throws DataException {
        tbDisparo.post();
        tbDisparoChatbot.post();
    }

    public void loadFormPk(Integer idDisparo ) throws DataException {
        tbDisparo.close(); 
        tbDisparo.clearFilters();
        
        if (idDisparo > 0) {
            tbDisparo.addFilter("ID_DISPARO");
            tbDisparo.addParam("ID_DISPARO", idDisparo);
        } else return;
        
        tbDisparo.open();
        habilitaComp(false);          // se tem registro habilita botões da barra de ferramenta  
    }         

    // retorna true se o master esta sendo editado, pode ser usado para verificar se o form esta 
    // habilitado edição
    public boolean masterIsEditing() {
       return (tbDisparo.getState() != TableState.QUERYING);
    }
    
    
    @Override
    public void tbDisparoAfterScroll(final Event<Object> event) {
        lblMensagem.setCaption("Selecionado: "+tbDisparo.getDESCRICAO().asString());

    }

        
    
    @Override
    public void efDescricaoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
        
            
    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void gridPrincipalClickImageAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuItemExportPdfClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportPdf(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportExcelClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportExcel(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }




    protected final void habilitaComp46001(Boolean enabled) {
        
        
    }


	public void onIncluir46001() {
        try {            
            rn.incluir46001(); 
            habilitaComp46001(true);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao adicionar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onAlterar46001() {
        try {
            if (!tbDisparoChatbot.isEmpty()) {
                rn.alterar46001();
                
                habilitaComp(true);
            } else {
                Dialog.create()
                        .title("Erro ao editar detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onExcluir46001() {
        try {
            if (!tbDisparoChatbot.isEmpty()) {
                
                rn.excluir46001();
            } else {
                Dialog.create()
                        .title("Erro ao excluir detalhe")
                        .message("Selecione um registro antes de editar")
                        .showError();
            }
        } catch (DataException e) {
            Dialog.create()
                    .title("Erro ao editar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }


    public void onCancelar46001() {
        try {
            rn.cancelar46001();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao cancelar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }
    
    public void onConfirmar46001() {
        try {
            rn.confirmar46001();
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao confirmar detalhe")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

}

