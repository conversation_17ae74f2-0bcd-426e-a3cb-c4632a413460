package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmTipoDocumento extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.TipoDocumentoRNA rn = null;

    public FrmTipoDocumento() {
        try {
            rn = (freedom.bytecode.rn.TipoDocumentoRNA) getRN(freedom.bytecode.rn.wizard.TipoDocumentoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbTipoDocumento();
        init_tbTipoDocumentoID();
        init_vboxTipoDocumento();
        init_FHBox6();
        init_FHBox1();
        init_btnConsultar();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnCancelar();
        init_pgPrincipal();
        init_tabListagem();
        init_FVBox1();
        init_grpBoxFiltro();
        init_gpFiltroPrincipal();
        init_FLabel2();
        init_cbbFilAtivo();
        init_gridPrincipal();
        init_tabCadastro();
        init_FVBox2();
        init_grpBoxPrincipal();
        init_FGridPanel2();
        init_lblPermitirObrigarAss();
        init_lblProdutivoAssina();
        init_lblConsultor();
        init_lblGerente();
        init_lblIdTipoDocumento();
        init_lblClienteAssina();
        init_lblDescricao();
        init_lblAtivo();
        init_edIdTipoDoc();
        init_edtDescricao();
        init_chkAtivo();
        init_cbbObrigarAssinatura();
        init_chkClienteAssina();
        init_chkProdutivoAssina();
        init_chkConsultorAssina();
        init_chkGerenteAssina();
        init_FrmTipoDocumento();
    }

    public CRM_TIPO_DOCUMENTO tbTipoDocumento;

    private void init_tbTipoDocumento() {
        tbTipoDocumento = rn.tbTipoDocumento;
        tbTipoDocumento.setName("tbTipoDocumento");
        tbTipoDocumento.setMaxRowCount(200);
        tbTipoDocumento.setWKey("43702;43701");
        tbTipoDocumento.setRatioBatchSize(20);
        getTables().put(tbTipoDocumento, "tbTipoDocumento");
        tbTipoDocumento.applyProperties();
    }

    public CRM_TIPO_DOCUMENTO tbTipoDocumentoID;

    private void init_tbTipoDocumentoID() {
        tbTipoDocumentoID = rn.tbTipoDocumentoID;
        tbTipoDocumentoID.setName("tbTipoDocumentoID");
        tbTipoDocumentoID.setMaxRowCount(200);
        tbTipoDocumentoID.setWKey("43702;43702");
        tbTipoDocumentoID.setRatioBatchSize(20);
        getTables().put(tbTipoDocumentoID, "tbTipoDocumentoID");
        tbTipoDocumentoID.applyProperties();
    }

    protected TFForm FrmTipoDocumento = this;
    private void init_FrmTipoDocumento() {
        FrmTipoDocumento.setName("FrmTipoDocumento");
        FrmTipoDocumento.setCaption("Tipos Documento");
        FrmTipoDocumento.setClientHeight(566);
        FrmTipoDocumento.setClientWidth(915);
        FrmTipoDocumento.setColor("clBtnFace");
        FrmTipoDocumento.setWOrigem("EhMain");
        FrmTipoDocumento.setWKey("43702");
        FrmTipoDocumento.setSpacing(0);
        FrmTipoDocumento.applyProperties();
    }

    public TFVBox vboxTipoDocumento = new TFVBox();

    private void init_vboxTipoDocumento() {
        vboxTipoDocumento.setName("vboxTipoDocumento");
        vboxTipoDocumento.setLeft(0);
        vboxTipoDocumento.setTop(0);
        vboxTipoDocumento.setWidth(915);
        vboxTipoDocumento.setHeight(566);
        vboxTipoDocumento.setAlign("alClient");
        vboxTipoDocumento.setBorderStyle("stNone");
        vboxTipoDocumento.setPaddingTop(0);
        vboxTipoDocumento.setPaddingLeft(0);
        vboxTipoDocumento.setPaddingRight(0);
        vboxTipoDocumento.setPaddingBottom(0);
        vboxTipoDocumento.setMarginTop(0);
        vboxTipoDocumento.setMarginLeft(0);
        vboxTipoDocumento.setMarginRight(0);
        vboxTipoDocumento.setMarginBottom(0);
        vboxTipoDocumento.setSpacing(1);
        vboxTipoDocumento.setFlexVflex("ftTrue");
        vboxTipoDocumento.setFlexHflex("ftTrue");
        vboxTipoDocumento.setScrollable(false);
        vboxTipoDocumento.setBoxShadowConfigHorizontalLength(10);
        vboxTipoDocumento.setBoxShadowConfigVerticalLength(10);
        vboxTipoDocumento.setBoxShadowConfigBlurRadius(5);
        vboxTipoDocumento.setBoxShadowConfigSpreadRadius(0);
        vboxTipoDocumento.setBoxShadowConfigShadowColor("clBlack");
        vboxTipoDocumento.setBoxShadowConfigOpacity(75);
        FrmTipoDocumento.addChildren(vboxTipoDocumento);
        vboxTipoDocumento.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(981);
        FHBox6.setHeight(68);
        FHBox6.setAlign("alTop");
        FHBox6.setBorderStyle("stNone");
        FHBox6.setColor("16514043");
        FHBox6.setPaddingTop(5);
        FHBox6.setPaddingLeft(2);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(5);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vboxTipoDocumento.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(908);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(2);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FHBox6.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnConsultar = new TFButton();

    private void init_btnConsultar() {
        btnConsultar.setName("btnConsultar");
        btnConsultar.setLeft(0);
        btnConsultar.setTop(0);
        btnConsultar.setWidth(65);
        btnConsultar.setHeight(53);
        btnConsultar.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultar.setCaption("Pesquisar");
        btnConsultar.setFontColor("clWindowText");
        btnConsultar.setFontSize(-11);
        btnConsultar.setFontName("Tahoma");
        btnConsultar.setFontStyle("[]");
        btnConsultar.setLayout("blGlyphTop");
        btnConsultar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConsultarClick(event);
            processarFlow("FrmTipoDocumento", "btnConsultar", "OnClick");
        });
        btnConsultar.setImageId(13);
        btnConsultar.setColor("clBtnFace");
        btnConsultar.setAccess(true);
        btnConsultar.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultar);
        btnConsultar.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(65);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(53);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmTipoDocumento", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(130);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(53);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmTipoDocumento", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(195);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(53);
        btnExcluir.setHint("Exclui o Registro Selecionado  (CRTL+ 4)");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.setVisible(false);
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmTipoDocumento", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(true);
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(260);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmTipoDocumento", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(325);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(53);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmTipoDocumento", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFPageControl pgPrincipal = new TFPageControl();

    private void init_pgPrincipal() {
        pgPrincipal.setName("pgPrincipal");
        pgPrincipal.setLeft(0);
        pgPrincipal.setTop(69);
        pgPrincipal.setWidth(974);
        pgPrincipal.setHeight(499);
        pgPrincipal.setTabPosition("tpTop");
        pgPrincipal.setFlexVflex("ftTrue");
        pgPrincipal.setFlexHflex("ftTrue");
        pgPrincipal.setRenderStyle("rsTabbed");
        pgPrincipal.applyProperties();
        vboxTipoDocumento.addChildren(pgPrincipal);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setVisible(true);
        tabListagem.setClosable(false);
        pgPrincipal.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(966);
        FVBox1.setHeight(471);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setColor("clWhite");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        tabListagem.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGroupbox grpBoxFiltro = new TFGroupbox();

    private void init_grpBoxFiltro() {
        grpBoxFiltro.setName("grpBoxFiltro");
        grpBoxFiltro.setLeft(0);
        grpBoxFiltro.setTop(0);
        grpBoxFiltro.setWidth(960);
        grpBoxFiltro.setHeight(74);
        grpBoxFiltro.setCaption("Filtro R\u00E1pido");
        grpBoxFiltro.setFontColor("clWindowText");
        grpBoxFiltro.setFontSize(-11);
        grpBoxFiltro.setFontName("Tahoma");
        grpBoxFiltro.setFontStyle("[]");
        grpBoxFiltro.setFlexVflex("ftMin");
        grpBoxFiltro.setFlexHflex("ftTrue");
        grpBoxFiltro.setScrollable(false);
        grpBoxFiltro.setClosable(true);
        grpBoxFiltro.setClosed(false);
        grpBoxFiltro.setOrient("coHorizontal");
        grpBoxFiltro.setStyle("grp3D");
        grpBoxFiltro.setHeaderImageId(0);
        FVBox1.addChildren(grpBoxFiltro);
        grpBoxFiltro.applyProperties();
    }

    public TFGridPanel gpFiltroPrincipal = new TFGridPanel();

    private void init_gpFiltroPrincipal() {
        gpFiltroPrincipal.setName("gpFiltroPrincipal");
        gpFiltroPrincipal.setLeft(2);
        gpFiltroPrincipal.setTop(15);
        gpFiltroPrincipal.setWidth(956);
        gpFiltroPrincipal.setHeight(47);
        gpFiltroPrincipal.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(100.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("FLabel2");
        item2.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("cbbFilAtivo");
        item3.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item3);
        TFGridPanelRow item4 = new TFGridPanelRow();
        item4.setSizeStyle("ssAbsolute");
        item4.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item4);
        TFGridPanelRow item5 = new TFGridPanelRow();
        item5.setSizeStyle("ssAbsolute");
        item5.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item5);
        gpFiltroPrincipal.setFlexVflex("ftTrue");
        gpFiltroPrincipal.setFlexHflex("ftTrue");
        gpFiltroPrincipal.setAllRowFlex(true);
        gpFiltroPrincipal.setColumnTabOrder(false);
        grpBoxFiltro.addChildren(gpFiltroPrincipal);
        gpFiltroPrincipal.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(76);
        FLabel2.setTop(1);
        FLabel2.setWidth(25);
        FLabel2.setHeight(21);
        FLabel2.setAlign("alRight");
        FLabel2.setCaption("Ativo");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        gpFiltroPrincipal.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cbbFilAtivo = new TFCombo();

    private void init_cbbFilAtivo() {
        cbbFilAtivo.setName("cbbFilAtivo");
        cbbFilAtivo.setLeft(455);
        cbbFilAtivo.setTop(1);
        cbbFilAtivo.setWidth(145);
        cbbFilAtivo.setHeight(21);
        cbbFilAtivo.setFlex(false);
        cbbFilAtivo.setListOptions("Sim=S;N\u00E3o=N;Todos=T");
        cbbFilAtivo.setReadOnly(true);
        cbbFilAtivo.setRequired(true);
        cbbFilAtivo.setPrompt("Selecione");
        cbbFilAtivo.setConstraintCheckWhen("cwImmediate");
        cbbFilAtivo.setConstraintCheckType("ctExpression");
        cbbFilAtivo.setConstraintFocusOnError(false);
        cbbFilAtivo.setConstraintEnableUI(true);
        cbbFilAtivo.setConstraintEnabled(false);
        cbbFilAtivo.setConstraintFormCheck(true);
        cbbFilAtivo.setClearOnDelKey(true);
        cbbFilAtivo.setUseClearButton(false);
        cbbFilAtivo.setHideClearButtonOnNullValue(false);
        gpFiltroPrincipal.addChildren(cbbFilAtivo);
        cbbFilAtivo.applyProperties();
        addValidatable(cbbFilAtivo);
    }

    public TFGrid gridPrincipal = new TFGrid();

    private void init_gridPrincipal() {
        gridPrincipal.setName("gridPrincipal");
        gridPrincipal.setLeft(0);
        gridPrincipal.setTop(75);
        gridPrincipal.setWidth(956);
        gridPrincipal.setHeight(355);
        gridPrincipal.setTable(tbTipoDocumento);
        gridPrincipal.setFlexVflex("ftTrue");
        gridPrincipal.setFlexHflex("ftTrue");
        gridPrincipal.setPagingEnabled(true);
        gridPrincipal.setFrozenColumns(0);
        gridPrincipal.setShowFooter(false);
        gridPrincipal.setShowHeader(true);
        gridPrincipal.setMultiSelection(false);
        gridPrincipal.setGroupingEnabled(false);
        gridPrincipal.setGroupingExpanded(false);
        gridPrincipal.setGroupingShowFooter(false);
        gridPrincipal.setCrosstabEnabled(false);
        gridPrincipal.setCrosstabGroupType("cgtConcat");
        gridPrincipal.setEditionEnabled(false);
        gridPrincipal.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_TIPO_DOCUMENTO");
        item0.setTitleCaption("Id.");
        item0.setWidth(60);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(319);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("ATIVO");
        item2.setTitleCaption("Ativo");
        item2.setWidth(91);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftCheckBox");
        item2.setFlexRatio(0);
        item2.setSort(true);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item2);
        FVBox1.addChildren(gridPrincipal);
        gridPrincipal.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        pgPrincipal.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(966);
        FVBox2.setHeight(471);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(5);
        FVBox2.setPaddingLeft(5);
        FVBox2.setPaddingRight(5);
        FVBox2.setPaddingBottom(5);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFGroupbox grpBoxPrincipal = new TFGroupbox();

    private void init_grpBoxPrincipal() {
        grpBoxPrincipal.setName("grpBoxPrincipal");
        grpBoxPrincipal.setLeft(0);
        grpBoxPrincipal.setTop(0);
        grpBoxPrincipal.setWidth(729);
        grpBoxPrincipal.setHeight(210);
        grpBoxPrincipal.setCaption("Tipo Documento");
        grpBoxPrincipal.setFontColor("clWindowText");
        grpBoxPrincipal.setFontSize(-11);
        grpBoxPrincipal.setFontName("Tahoma");
        grpBoxPrincipal.setFontStyle("[]");
        grpBoxPrincipal.setFlexVflex("ftMin");
        grpBoxPrincipal.setFlexHflex("ftTrue");
        grpBoxPrincipal.setScrollable(true);
        grpBoxPrincipal.setClosable(false);
        grpBoxPrincipal.setClosed(false);
        grpBoxPrincipal.setOrient("coVertical");
        grpBoxPrincipal.setStyle("grp3D");
        grpBoxPrincipal.setHeaderImageId(0);
        FVBox2.addChildren(grpBoxPrincipal);
        grpBoxPrincipal.applyProperties();
    }

    public TFGridPanel FGridPanel2 = new TFGridPanel();

    private void init_FGridPanel2() {
        FGridPanel2.setName("FGridPanel2");
        FGridPanel2.setLeft(2);
        FGridPanel2.setTop(15);
        FGridPanel2.setWidth(725);
        FGridPanel2.setHeight(169);
        FGridPanel2.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("lblIdTipoDocumento");
        item2.setRow(0);
        FGridPanel2.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("edIdTipoDoc");
        item3.setRow(0);
        FGridPanel2.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("lblDescricao");
        item4.setRow(1);
        FGridPanel2.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("edtDescricao");
        item5.setRow(1);
        FGridPanel2.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(0);
        item6.setControl("lblAtivo");
        item6.setRow(2);
        FGridPanel2.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(1);
        item7.setControl("chkAtivo");
        item7.setRow(2);
        FGridPanel2.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(0);
        item8.setControl("lblPermitirObrigarAss");
        item8.setRow(3);
        FGridPanel2.getControlCollection().add(item8);
        TFControlItem item9 = new TFControlItem();
        item9.setColumn(0);
        item9.setControl("lblClienteAssina");
        item9.setRow(4);
        FGridPanel2.getControlCollection().add(item9);
        TFControlItem item10 = new TFControlItem();
        item10.setColumn(1);
        item10.setControl("chkClienteAssina");
        item10.setRow(4);
        FGridPanel2.getControlCollection().add(item10);
        TFControlItem item11 = new TFControlItem();
        item11.setColumn(0);
        item11.setControl("lblProdutivoAssina");
        item11.setRow(5);
        FGridPanel2.getControlCollection().add(item11);
        TFControlItem item12 = new TFControlItem();
        item12.setColumn(0);
        item12.setControl("lblConsultor");
        item12.setRow(6);
        FGridPanel2.getControlCollection().add(item12);
        TFControlItem item13 = new TFControlItem();
        item13.setColumn(0);
        item13.setControl("lblGerente");
        item13.setRow(7);
        FGridPanel2.getControlCollection().add(item13);
        TFControlItem item14 = new TFControlItem();
        item14.setColumn(1);
        item14.setControl("chkProdutivoAssina");
        item14.setRow(5);
        FGridPanel2.getControlCollection().add(item14);
        TFControlItem item15 = new TFControlItem();
        item15.setColumn(1);
        item15.setControl("chkConsultorAssina");
        item15.setRow(6);
        FGridPanel2.getControlCollection().add(item15);
        TFControlItem item16 = new TFControlItem();
        item16.setColumn(1);
        item16.setControl("chkGerenteAssina");
        item16.setRow(7);
        FGridPanel2.getControlCollection().add(item16);
        TFControlItem item17 = new TFControlItem();
        item17.setColumn(1);
        item17.setControl("cbbObrigarAssinatura");
        item17.setRow(3);
        FGridPanel2.getControlCollection().add(item17);
        TFGridPanelRow item18 = new TFGridPanelRow();
        item18.setSizeStyle("ssAbsolute");
        item18.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item18);
        TFGridPanelRow item19 = new TFGridPanelRow();
        item19.setSizeStyle("ssAbsolute");
        item19.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item19);
        TFGridPanelRow item20 = new TFGridPanelRow();
        item20.setSizeStyle("ssAbsolute");
        item20.setValue(21.000000000000000000);
        FGridPanel2.getRowCollection().add(item20);
        TFGridPanelRow item21 = new TFGridPanelRow();
        item21.setSizeStyle("ssAbsolute");
        item21.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item21);
        TFGridPanelRow item22 = new TFGridPanelRow();
        item22.setSizeStyle("ssAbsolute");
        item22.setValue(21.000000000000000000);
        FGridPanel2.getRowCollection().add(item22);
        TFGridPanelRow item23 = new TFGridPanelRow();
        item23.setSizeStyle("ssAbsolute");
        item23.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item23);
        TFGridPanelRow item24 = new TFGridPanelRow();
        item24.setSizeStyle("ssAbsolute");
        item24.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item24);
        TFGridPanelRow item25 = new TFGridPanelRow();
        item25.setSizeStyle("ssAbsolute");
        item25.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item25);
        FGridPanel2.setFlexVflex("ftFalse");
        FGridPanel2.setFlexHflex("ftTrue");
        FGridPanel2.setAllRowFlex(true);
        FGridPanel2.setColumnTabOrder(false);
        grpBoxPrincipal.addChildren(FGridPanel2);
        FGridPanel2.applyProperties();
    }

    public TFLabel lblPermitirObrigarAss = new TFLabel();

    private void init_lblPermitirObrigarAss() {
        lblPermitirObrigarAss.setName("lblPermitirObrigarAss");
        lblPermitirObrigarAss.setLeft(11);
        lblPermitirObrigarAss.setTop(60);
        lblPermitirObrigarAss.setWidth(90);
        lblPermitirObrigarAss.setHeight(19);
        lblPermitirObrigarAss.setAlign("alRight");
        lblPermitirObrigarAss.setCaption("Obrigar Assinatura");
        lblPermitirObrigarAss.setFontColor("clWindowText");
        lblPermitirObrigarAss.setFontSize(-11);
        lblPermitirObrigarAss.setFontName("Tahoma");
        lblPermitirObrigarAss.setFontStyle("[]");
        lblPermitirObrigarAss.setVerticalAlignment("taVerticalCenter");
        lblPermitirObrigarAss.setWordBreak(false);
        FGridPanel2.addChildren(lblPermitirObrigarAss);
        lblPermitirObrigarAss.applyProperties();
    }

    public TFLabel lblProdutivoAssina = new TFLabel();

    private void init_lblProdutivoAssina() {
        lblProdutivoAssina.setName("lblProdutivoAssina");
        lblProdutivoAssina.setLeft(21);
        lblProdutivoAssina.setTop(100);
        lblProdutivoAssina.setWidth(80);
        lblProdutivoAssina.setHeight(19);
        lblProdutivoAssina.setAlign("alRight");
        lblProdutivoAssina.setCaption("Produtivo Assina");
        lblProdutivoAssina.setFontColor("clWindowText");
        lblProdutivoAssina.setFontSize(-11);
        lblProdutivoAssina.setFontName("Tahoma");
        lblProdutivoAssina.setFontStyle("[]");
        lblProdutivoAssina.setVerticalAlignment("taVerticalCenter");
        lblProdutivoAssina.setWordBreak(false);
        FGridPanel2.addChildren(lblProdutivoAssina);
        lblProdutivoAssina.applyProperties();
    }

    public TFLabel lblConsultor = new TFLabel();

    private void init_lblConsultor() {
        lblConsultor.setName("lblConsultor");
        lblConsultor.setLeft(21);
        lblConsultor.setTop(119);
        lblConsultor.setWidth(80);
        lblConsultor.setHeight(19);
        lblConsultor.setAlign("alRight");
        lblConsultor.setCaption("Consultor Assina");
        lblConsultor.setFontColor("clWindowText");
        lblConsultor.setFontSize(-11);
        lblConsultor.setFontName("Tahoma");
        lblConsultor.setFontStyle("[]");
        lblConsultor.setVerticalAlignment("taVerticalCenter");
        lblConsultor.setWordBreak(false);
        FGridPanel2.addChildren(lblConsultor);
        lblConsultor.applyProperties();
    }

    public TFLabel lblGerente = new TFLabel();

    private void init_lblGerente() {
        lblGerente.setName("lblGerente");
        lblGerente.setLeft(28);
        lblGerente.setTop(138);
        lblGerente.setWidth(73);
        lblGerente.setHeight(19);
        lblGerente.setAlign("alRight");
        lblGerente.setCaption("Gerente Assina");
        lblGerente.setFontColor("clWindowText");
        lblGerente.setFontSize(-11);
        lblGerente.setFontName("Tahoma");
        lblGerente.setFontStyle("[]");
        lblGerente.setVerticalAlignment("taVerticalCenter");
        lblGerente.setWordBreak(false);
        FGridPanel2.addChildren(lblGerente);
        lblGerente.applyProperties();
    }

    public TFLabel lblIdTipoDocumento = new TFLabel();

    private void init_lblIdTipoDocumento() {
        lblIdTipoDocumento.setName("lblIdTipoDocumento");
        lblIdTipoDocumento.setLeft(30);
        lblIdTipoDocumento.setTop(1);
        lblIdTipoDocumento.setWidth(71);
        lblIdTipoDocumento.setHeight(19);
        lblIdTipoDocumento.setAlign("alRight");
        lblIdTipoDocumento.setCaption("Id. Documento");
        lblIdTipoDocumento.setFontColor("clWindowText");
        lblIdTipoDocumento.setFontSize(-11);
        lblIdTipoDocumento.setFontName("Tahoma");
        lblIdTipoDocumento.setFontStyle("[]");
        lblIdTipoDocumento.setVerticalAlignment("taVerticalCenter");
        lblIdTipoDocumento.setWordBreak(false);
        FGridPanel2.addChildren(lblIdTipoDocumento);
        lblIdTipoDocumento.applyProperties();
    }

    public TFLabel lblClienteAssina = new TFLabel();

    private void init_lblClienteAssina() {
        lblClienteAssina.setName("lblClienteAssina");
        lblClienteAssina.setLeft(34);
        lblClienteAssina.setTop(79);
        lblClienteAssina.setWidth(67);
        lblClienteAssina.setHeight(21);
        lblClienteAssina.setAlign("alRight");
        lblClienteAssina.setCaption("Cliente Assina");
        lblClienteAssina.setFontColor("clWindowText");
        lblClienteAssina.setFontSize(-11);
        lblClienteAssina.setFontName("Tahoma");
        lblClienteAssina.setFontStyle("[]");
        lblClienteAssina.setVerticalAlignment("taVerticalCenter");
        lblClienteAssina.setWordBreak(false);
        FGridPanel2.addChildren(lblClienteAssina);
        lblClienteAssina.applyProperties();
    }

    public TFLabel lblDescricao = new TFLabel();

    private void init_lblDescricao() {
        lblDescricao.setName("lblDescricao");
        lblDescricao.setLeft(55);
        lblDescricao.setTop(20);
        lblDescricao.setWidth(46);
        lblDescricao.setHeight(19);
        lblDescricao.setAlign("alRight");
        lblDescricao.setCaption("Descri\u00E7\u00E3o");
        lblDescricao.setFontColor("clWindowText");
        lblDescricao.setFontSize(-11);
        lblDescricao.setFontName("Tahoma");
        lblDescricao.setFontStyle("[]");
        lblDescricao.setVerticalAlignment("taVerticalCenter");
        lblDescricao.setWordBreak(false);
        FGridPanel2.addChildren(lblDescricao);
        lblDescricao.applyProperties();
    }

    public TFLabel lblAtivo = new TFLabel();

    private void init_lblAtivo() {
        lblAtivo.setName("lblAtivo");
        lblAtivo.setLeft(76);
        lblAtivo.setTop(39);
        lblAtivo.setWidth(25);
        lblAtivo.setHeight(21);
        lblAtivo.setAlign("alRight");
        lblAtivo.setCaption("Ativo");
        lblAtivo.setFontColor("clWindowText");
        lblAtivo.setFontSize(-11);
        lblAtivo.setFontName("Tahoma");
        lblAtivo.setFontStyle("[]");
        lblAtivo.setVerticalAlignment("taVerticalCenter");
        lblAtivo.setWordBreak(false);
        FGridPanel2.addChildren(lblAtivo);
        lblAtivo.applyProperties();
    }

    public TFInteger edIdTipoDoc = new TFInteger();

    private void init_edIdTipoDoc() {
        edIdTipoDoc.setName("edIdTipoDoc");
        edIdTipoDoc.setLeft(101);
        edIdTipoDoc.setTop(1);
        edIdTipoDoc.setWidth(80);
        edIdTipoDoc.setHeight(19);
        edIdTipoDoc.setTable(tbTipoDocumento);
        edIdTipoDoc.setFieldName("ID_TIPO_DOCUMENTO");
        edIdTipoDoc.setHelpCaption("Id. Tipo Documento");
        edIdTipoDoc.setFlex(false);
        edIdTipoDoc.setRequired(false);
        edIdTipoDoc.setConstraintCheckWhen("cwImmediate");
        edIdTipoDoc.setConstraintCheckType("ctExpression");
        edIdTipoDoc.setConstraintFocusOnError(false);
        edIdTipoDoc.setConstraintEnableUI(true);
        edIdTipoDoc.setConstraintEnabled(false);
        edIdTipoDoc.setConstraintFormCheck(true);
        edIdTipoDoc.setMaxlength(0);
        edIdTipoDoc.setAlign("alLeft");
        edIdTipoDoc.setFontColor("clWindowText");
        edIdTipoDoc.setFontSize(-13);
        edIdTipoDoc.setFontName("Tahoma");
        edIdTipoDoc.setFontStyle("[]");
        edIdTipoDoc.setAlignment("taRightJustify");
        FGridPanel2.addChildren(edIdTipoDoc);
        edIdTipoDoc.applyProperties();
        addValidatable(edIdTipoDoc);
    }

    public TFString edtDescricao = new TFString();

    private void init_edtDescricao() {
        edtDescricao.setName("edtDescricao");
        edtDescricao.setLeft(101);
        edtDescricao.setTop(20);
        edtDescricao.setWidth(580);
        edtDescricao.setHeight(19);
        edtDescricao.setTable(tbTipoDocumento);
        edtDescricao.setFieldName("DESCRICAO");
        edtDescricao.setHelpCaption("Descri\u00E7\u00E3o");
        edtDescricao.setFlex(true);
        edtDescricao.setRequired(true);
        edtDescricao.setConstraintExpression("value is null or trim(value) = ''");
        edtDescricao.setConstraintMessage("Campo Descri\u00E7\u00E3o, preenchimento \u00E9 obrigat\u00F3rio");
        edtDescricao.setConstraintCheckWhen("cwImmediate");
        edtDescricao.setConstraintCheckType("ctExpression");
        edtDescricao.setConstraintFocusOnError(false);
        edtDescricao.setConstraintGroupName("grpTbtime");
        edtDescricao.setConstraintEnableUI(true);
        edtDescricao.setConstraintEnabled(true);
        edtDescricao.setConstraintFormCheck(true);
        edtDescricao.setCharCase("ccNormal");
        edtDescricao.setPwd(false);
        edtDescricao.setMaxlength(100);
        edtDescricao.setAlign("alLeft");
        edtDescricao.setFontColor("clWindowText");
        edtDescricao.setFontSize(-13);
        edtDescricao.setFontName("Tahoma");
        edtDescricao.setFontStyle("[]");
        edtDescricao.setSaveLiteralCharacter(false);
        edtDescricao.applyProperties();
        FGridPanel2.addChildren(edtDescricao);
        addValidatable(edtDescricao);
    }

    public TFCheckBox chkAtivo = new TFCheckBox();

    private void init_chkAtivo() {
        chkAtivo.setName("chkAtivo");
        chkAtivo.setLeft(101);
        chkAtivo.setTop(39);
        chkAtivo.setWidth(16);
        chkAtivo.setHeight(21);
        chkAtivo.setAlign("alLeft");
        chkAtivo.setFontColor("clWindowText");
        chkAtivo.setFontSize(-11);
        chkAtivo.setFontName("Tahoma");
        chkAtivo.setFontStyle("[]");
        chkAtivo.setTable(tbTipoDocumento);
        chkAtivo.setFieldName("ATIVO");
        chkAtivo.setUncheckedValue("N");
        chkAtivo.setHelpCaption("Ativo");
        chkAtivo.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(chkAtivo);
        chkAtivo.applyProperties();
    }

    public TFCombo cbbObrigarAssinatura = new TFCombo();

    private void init_cbbObrigarAssinatura() {
        cbbObrigarAssinatura.setName("cbbObrigarAssinatura");
        cbbObrigarAssinatura.setLeft(101);
        cbbObrigarAssinatura.setTop(60);
        cbbObrigarAssinatura.setWidth(145);
        cbbObrigarAssinatura.setHeight(21);
        cbbObrigarAssinatura.setTable(tbTipoDocumento);
        cbbObrigarAssinatura.setFieldName("PERMITIR_OBRIGAR_ASSINATURA");
        cbbObrigarAssinatura.setFlex(false);
        cbbObrigarAssinatura.setListOptions("N\u00E3o permite assinar=N; Permite assinar=P; Obriga Assinar=O");
        cbbObrigarAssinatura.setReadOnly(true);
        cbbObrigarAssinatura.setRequired(false);
        cbbObrigarAssinatura.setPrompt("Selecione");
        cbbObrigarAssinatura.setConstraintCheckWhen("cwImmediate");
        cbbObrigarAssinatura.setConstraintCheckType("ctExpression");
        cbbObrigarAssinatura.setConstraintFocusOnError(false);
        cbbObrigarAssinatura.setConstraintEnableUI(true);
        cbbObrigarAssinatura.setConstraintEnabled(false);
        cbbObrigarAssinatura.setConstraintFormCheck(true);
        cbbObrigarAssinatura.setClearOnDelKey(true);
        cbbObrigarAssinatura.setUseClearButton(false);
        cbbObrigarAssinatura.setHideClearButtonOnNullValue(false);
        FGridPanel2.addChildren(cbbObrigarAssinatura);
        cbbObrigarAssinatura.applyProperties();
        addValidatable(cbbObrigarAssinatura);
    }

    public TFCheckBox chkClienteAssina = new TFCheckBox();

    private void init_chkClienteAssina() {
        chkClienteAssina.setName("chkClienteAssina");
        chkClienteAssina.setLeft(364);
        chkClienteAssina.setTop(81);
        chkClienteAssina.setWidth(97);
        chkClienteAssina.setHeight(17);
        chkClienteAssina.setFontColor("clWindowText");
        chkClienteAssina.setFontSize(-11);
        chkClienteAssina.setFontName("Tahoma");
        chkClienteAssina.setFontStyle("[]");
        chkClienteAssina.setTable(tbTipoDocumento);
        chkClienteAssina.setFieldName("CLIENTE_ASSINA");
        chkClienteAssina.setCheckedValue("S");
        chkClienteAssina.setUncheckedValue("N");
        chkClienteAssina.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(chkClienteAssina);
        chkClienteAssina.applyProperties();
    }

    public TFCheckBox chkProdutivoAssina = new TFCheckBox();

    private void init_chkProdutivoAssina() {
        chkProdutivoAssina.setName("chkProdutivoAssina");
        chkProdutivoAssina.setLeft(364);
        chkProdutivoAssina.setTop(101);
        chkProdutivoAssina.setWidth(97);
        chkProdutivoAssina.setHeight(17);
        chkProdutivoAssina.setFontColor("clWindowText");
        chkProdutivoAssina.setFontSize(-11);
        chkProdutivoAssina.setFontName("Tahoma");
        chkProdutivoAssina.setFontStyle("[]");
        chkProdutivoAssina.setTable(tbTipoDocumento);
        chkProdutivoAssina.setFieldName("PRODUTIVO_ASSINA");
        chkProdutivoAssina.setCheckedValue("S");
        chkProdutivoAssina.setUncheckedValue("N");
        chkProdutivoAssina.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(chkProdutivoAssina);
        chkProdutivoAssina.applyProperties();
    }

    public TFCheckBox chkConsultorAssina = new TFCheckBox();

    private void init_chkConsultorAssina() {
        chkConsultorAssina.setName("chkConsultorAssina");
        chkConsultorAssina.setLeft(364);
        chkConsultorAssina.setTop(120);
        chkConsultorAssina.setWidth(97);
        chkConsultorAssina.setHeight(17);
        chkConsultorAssina.setFontColor("clWindowText");
        chkConsultorAssina.setFontSize(-11);
        chkConsultorAssina.setFontName("Tahoma");
        chkConsultorAssina.setFontStyle("[]");
        chkConsultorAssina.setTable(tbTipoDocumento);
        chkConsultorAssina.setFieldName("CONSULTOR_ASSINA");
        chkConsultorAssina.setCheckedValue("S");
        chkConsultorAssina.setUncheckedValue("N");
        chkConsultorAssina.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(chkConsultorAssina);
        chkConsultorAssina.applyProperties();
    }

    public TFCheckBox chkGerenteAssina = new TFCheckBox();

    private void init_chkGerenteAssina() {
        chkGerenteAssina.setName("chkGerenteAssina");
        chkGerenteAssina.setLeft(364);
        chkGerenteAssina.setTop(139);
        chkGerenteAssina.setWidth(97);
        chkGerenteAssina.setHeight(17);
        chkGerenteAssina.setFontColor("clWindowText");
        chkGerenteAssina.setFontSize(-11);
        chkGerenteAssina.setFontName("Tahoma");
        chkGerenteAssina.setFontStyle("[]");
        chkGerenteAssina.setTable(tbTipoDocumento);
        chkGerenteAssina.setFieldName("GERENTE_ASSINA");
        chkGerenteAssina.setCheckedValue("S");
        chkGerenteAssina.setUncheckedValue("N");
        chkGerenteAssina.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(chkGerenteAssina);
        chkGerenteAssina.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnConsultarClick(final Event<Object> event) {
        if (btnConsultar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}