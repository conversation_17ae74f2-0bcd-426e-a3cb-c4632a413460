package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClientesFlagsServ extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClientesFlagsServRNA rn = null;

    public FrmClientesFlagsServ() {
        try {
            rn = (freedom.bytecode.rn.ClientesFlagsServRNA) getRN(freedom.bytecode.rn.wizard.ClientesFlagsServRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteFlagGrupo();
        init_tbClienteDiverso();
        init_tbClienteCpagarIcmsSubEmp();
        init_tbClienteIssEmpresa();
        init_tbClienteIsentoIssEmpresa();
        init_tbAcessorioEmp();
        init_tbIndustriaEmp();
        init_tbFornecDescontaIcmsstEmp();
        init_tbClienteIgnoraPjReterIss();
        init_tbFornecSubIcmsEmp();
        init_tbFornecIvaEmp();
        init_tbClienteFlag();
        init_tbClientes();
        init_tbClientesEnderecoIe();
        init_vboxPrincipal();
        init_hboxCabecalho();
        init_lblNomeCliente();
        init_FHBox1();
        init_lblCodCliente();
        init_hboxPesquisa();
        init_FVBox2();
        init_cbbClienteFlagGrupo();
        init_edtPesquisaDescricao();
        init_btnPesquisar();
        init_hboxDetalhe();
        init_gridClienteFlag();
        init_FMemo1();
        init_hboxAltera();
        init_FVBox4();
        init_lblCampo();
        init_edtCampo();
        init_FVBox5();
        init_lblNovoValor();
        init_edtValorString();
        init_cbbValor();
        init_edtValorDecimal();
        init_chkValor();
        init_edtValorInteger();
        init_edtValorHora();
        init_edtValorDate();
        init_FImageParam();
        init_FHBox2();
        init_btnAlterar();
        init_sc();
        init_FrmClientesFlagsServ();
    }

    public CLIENTE_FLAG_GRUPO tbClienteFlagGrupo;

    private void init_tbClienteFlagGrupo() {
        tbClienteFlagGrupo = rn.tbClienteFlagGrupo;
        tbClienteFlagGrupo.setName("tbClienteFlagGrupo");
        tbClienteFlagGrupo.setMaxRowCount(200);
        tbClienteFlagGrupo.setWKey("4600445;46002");
        tbClienteFlagGrupo.setRatioBatchSize(20);
        getTables().put(tbClienteFlagGrupo, "tbClienteFlagGrupo");
        tbClienteFlagGrupo.applyProperties();
    }

    public CLIENTE_DIVERSO tbClienteDiverso;

    private void init_tbClienteDiverso() {
        tbClienteDiverso = rn.tbClienteDiverso;
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("4600445;46003");
        tbClienteDiverso.setRatioBatchSize(20);
        getTables().put(tbClienteDiverso, "tbClienteDiverso");
        tbClienteDiverso.applyProperties();
    }

    public CLIENTE_CPAGAR_ICMS_SUB_EMP tbClienteCpagarIcmsSubEmp;

    private void init_tbClienteCpagarIcmsSubEmp() {
        tbClienteCpagarIcmsSubEmp = rn.tbClienteCpagarIcmsSubEmp;
        tbClienteCpagarIcmsSubEmp.setName("tbClienteCpagarIcmsSubEmp");
        tbClienteCpagarIcmsSubEmp.setMaxRowCount(200);
        tbClienteCpagarIcmsSubEmp.setWKey("4600445;46004");
        tbClienteCpagarIcmsSubEmp.setRatioBatchSize(20);
        getTables().put(tbClienteCpagarIcmsSubEmp, "tbClienteCpagarIcmsSubEmp");
        tbClienteCpagarIcmsSubEmp.applyProperties();
    }

    public CLIENTE_ISS_EMPRESA tbClienteIssEmpresa;

    private void init_tbClienteIssEmpresa() {
        tbClienteIssEmpresa = rn.tbClienteIssEmpresa;
        tbClienteIssEmpresa.setName("tbClienteIssEmpresa");
        tbClienteIssEmpresa.setMaxRowCount(200);
        tbClienteIssEmpresa.setWKey("4600445;46005");
        tbClienteIssEmpresa.setRatioBatchSize(20);
        getTables().put(tbClienteIssEmpresa, "tbClienteIssEmpresa");
        tbClienteIssEmpresa.applyProperties();
    }

    public CLIENTE_ISENTO_ISS_EMPRESA tbClienteIsentoIssEmpresa;

    private void init_tbClienteIsentoIssEmpresa() {
        tbClienteIsentoIssEmpresa = rn.tbClienteIsentoIssEmpresa;
        tbClienteIsentoIssEmpresa.setName("tbClienteIsentoIssEmpresa");
        tbClienteIsentoIssEmpresa.setMaxRowCount(200);
        tbClienteIsentoIssEmpresa.setWKey("4600445;46006");
        tbClienteIsentoIssEmpresa.setRatioBatchSize(20);
        getTables().put(tbClienteIsentoIssEmpresa, "tbClienteIsentoIssEmpresa");
        tbClienteIsentoIssEmpresa.applyProperties();
    }

    public ACESSORIO_EMP tbAcessorioEmp;

    private void init_tbAcessorioEmp() {
        tbAcessorioEmp = rn.tbAcessorioEmp;
        tbAcessorioEmp.setName("tbAcessorioEmp");
        tbAcessorioEmp.setMaxRowCount(200);
        tbAcessorioEmp.setWKey("4600445;46007");
        tbAcessorioEmp.setRatioBatchSize(20);
        getTables().put(tbAcessorioEmp, "tbAcessorioEmp");
        tbAcessorioEmp.applyProperties();
    }

    public INDUSTRIA_EMP tbIndustriaEmp;

    private void init_tbIndustriaEmp() {
        tbIndustriaEmp = rn.tbIndustriaEmp;
        tbIndustriaEmp.setName("tbIndustriaEmp");
        tbIndustriaEmp.setMaxRowCount(200);
        tbIndustriaEmp.setWKey("4600445;46008");
        tbIndustriaEmp.setRatioBatchSize(20);
        getTables().put(tbIndustriaEmp, "tbIndustriaEmp");
        tbIndustriaEmp.applyProperties();
    }

    public FORNEC_DESCONTA_ICMSST_EMP tbFornecDescontaIcmsstEmp;

    private void init_tbFornecDescontaIcmsstEmp() {
        tbFornecDescontaIcmsstEmp = rn.tbFornecDescontaIcmsstEmp;
        tbFornecDescontaIcmsstEmp.setName("tbFornecDescontaIcmsstEmp");
        tbFornecDescontaIcmsstEmp.setMaxRowCount(200);
        tbFornecDescontaIcmsstEmp.setWKey("4600445;46009");
        tbFornecDescontaIcmsstEmp.setRatioBatchSize(20);
        getTables().put(tbFornecDescontaIcmsstEmp, "tbFornecDescontaIcmsstEmp");
        tbFornecDescontaIcmsstEmp.applyProperties();
    }

    public CLIENTE_IGNORA_PJ_RETER_ISS tbClienteIgnoraPjReterIss;

    private void init_tbClienteIgnoraPjReterIss() {
        tbClienteIgnoraPjReterIss = rn.tbClienteIgnoraPjReterIss;
        tbClienteIgnoraPjReterIss.setName("tbClienteIgnoraPjReterIss");
        tbClienteIgnoraPjReterIss.setMaxRowCount(200);
        tbClienteIgnoraPjReterIss.setWKey("4600445;460010");
        tbClienteIgnoraPjReterIss.setRatioBatchSize(20);
        getTables().put(tbClienteIgnoraPjReterIss, "tbClienteIgnoraPjReterIss");
        tbClienteIgnoraPjReterIss.applyProperties();
    }

    public FORNEC_SUB_ICMS_EMP tbFornecSubIcmsEmp;

    private void init_tbFornecSubIcmsEmp() {
        tbFornecSubIcmsEmp = rn.tbFornecSubIcmsEmp;
        tbFornecSubIcmsEmp.setName("tbFornecSubIcmsEmp");
        tbFornecSubIcmsEmp.setMaxRowCount(200);
        tbFornecSubIcmsEmp.setWKey("4600445;460011");
        tbFornecSubIcmsEmp.setRatioBatchSize(20);
        getTables().put(tbFornecSubIcmsEmp, "tbFornecSubIcmsEmp");
        tbFornecSubIcmsEmp.applyProperties();
    }

    public FORNEC_IVA_EMP tbFornecIvaEmp;

    private void init_tbFornecIvaEmp() {
        tbFornecIvaEmp = rn.tbFornecIvaEmp;
        tbFornecIvaEmp.setName("tbFornecIvaEmp");
        tbFornecIvaEmp.setMaxRowCount(200);
        tbFornecIvaEmp.setWKey("4600445;460012");
        tbFornecIvaEmp.setRatioBatchSize(20);
        getTables().put(tbFornecIvaEmp, "tbFornecIvaEmp");
        tbFornecIvaEmp.applyProperties();
    }

    public CLIENTE_FLAG tbClienteFlag;

    private void init_tbClienteFlag() {
        tbClienteFlag = rn.tbClienteFlag;
        tbClienteFlag.setName("tbClienteFlag");
        TFTableField item17 = new TFTableField();
        item17.setName("VALOR");
        item17.setCalculated(true);
        item17.setUpdatable(false);
        item17.setPrimaryKey(false);
        item17.setFieldType("ftString");
        item17.setJSONConfigNullOnEmpty(false);
        item17.setCaption("VALOR");
        tbClienteFlag.getFieldDefs().add(item17);
        TFTableField item18 = new TFTableField();
        item18.setName("VALOR_GRID");
        item18.setCalculated(true);
        item18.setUpdatable(false);
        item18.setPrimaryKey(false);
        item18.setFieldType("ftString");
        item18.setJSONConfigNullOnEmpty(false);
        item18.setCaption("VALOR_GRID");
        tbClienteFlag.getFieldDefs().add(item18);
        tbClienteFlag.setMaxRowCount(200);
        tbClienteFlag.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbClienteFlagAfterScroll(event);
            processarFlow("FrmClientesFlagsServ", "tbClienteFlag", "OnAfterScroll");
        });
        tbClienteFlag.setWKey("4600445;460013");
        tbClienteFlag.setRatioBatchSize(20);
        getTables().put(tbClienteFlag, "tbClienteFlag");
        tbClienteFlag.applyProperties();
    }

    public CLIENTES tbClientes;

    private void init_tbClientes() {
        tbClientes = rn.tbClientes;
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("4600445;460014");
        tbClientes.setRatioBatchSize(20);
        getTables().put(tbClientes, "tbClientes");
        tbClientes.applyProperties();
    }

    public CLIENTES_ENDERECO_IE tbClientesEnderecoIe;

    private void init_tbClientesEnderecoIe() {
        tbClientesEnderecoIe = rn.tbClientesEnderecoIe;
        tbClientesEnderecoIe.setName("tbClientesEnderecoIe");
        tbClientesEnderecoIe.setMaxRowCount(200);
        tbClientesEnderecoIe.setWKey("4600445;460015");
        tbClientesEnderecoIe.setRatioBatchSize(20);
        getTables().put(tbClientesEnderecoIe, "tbClientesEnderecoIe");
        tbClientesEnderecoIe.applyProperties();
    }

    protected TFForm FrmClientesFlagsServ = this;
    private void init_FrmClientesFlagsServ() {
        FrmClientesFlagsServ.setName("FrmClientesFlagsServ");
        FrmClientesFlagsServ.setCaption("Clientes Flags");
        FrmClientesFlagsServ.setClientHeight(565);
        FrmClientesFlagsServ.setClientWidth(875);
        FrmClientesFlagsServ.setColor("clBtnFace");
        FrmClientesFlagsServ.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmClientesFlagsServ", "FrmClientesFlagsServ", "OnCreate");
        });
        FrmClientesFlagsServ.setWOrigem("EhMain");
        FrmClientesFlagsServ.setWKey("4600445");
        FrmClientesFlagsServ.setSpacing(0);
        FrmClientesFlagsServ.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(875);
        vboxPrincipal.setHeight(565);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(0);
        vboxPrincipal.setPaddingLeft(0);
        vboxPrincipal.setPaddingRight(0);
        vboxPrincipal.setPaddingBottom(0);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(1);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmClientesFlagsServ.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox hboxCabecalho = new TFHBox();

    private void init_hboxCabecalho() {
        hboxCabecalho.setName("hboxCabecalho");
        hboxCabecalho.setLeft(0);
        hboxCabecalho.setTop(0);
        hboxCabecalho.setWidth(1005);
        hboxCabecalho.setHeight(35);
        hboxCabecalho.setBorderStyle("stNone");
        hboxCabecalho.setPaddingTop(3);
        hboxCabecalho.setPaddingLeft(8);
        hboxCabecalho.setPaddingRight(8);
        hboxCabecalho.setPaddingBottom(3);
        hboxCabecalho.setMarginTop(10);
        hboxCabecalho.setMarginLeft(0);
        hboxCabecalho.setMarginRight(0);
        hboxCabecalho.setMarginBottom(0);
        hboxCabecalho.setSpacing(0);
        hboxCabecalho.setFlexVflex("ftFalse");
        hboxCabecalho.setFlexHflex("ftTrue");
        hboxCabecalho.setScrollable(true);
        hboxCabecalho.setBoxShadowConfigHorizontalLength(10);
        hboxCabecalho.setBoxShadowConfigVerticalLength(10);
        hboxCabecalho.setBoxShadowConfigBlurRadius(5);
        hboxCabecalho.setBoxShadowConfigSpreadRadius(0);
        hboxCabecalho.setBoxShadowConfigShadowColor("clBlack");
        hboxCabecalho.setBoxShadowConfigOpacity(75);
        hboxCabecalho.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxCabecalho);
        hboxCabecalho.applyProperties();
    }

    public TFLabel lblNomeCliente = new TFLabel();

    private void init_lblNomeCliente() {
        lblNomeCliente.setName("lblNomeCliente");
        lblNomeCliente.setLeft(0);
        lblNomeCliente.setTop(0);
        lblNomeCliente.setWidth(197);
        lblNomeCliente.setHeight(24);
        lblNomeCliente.setCaption("Antonio Carlos Orione");
        lblNomeCliente.setFontColor("13392431");
        lblNomeCliente.setFontSize(-19);
        lblNomeCliente.setFontName("Trebuchet MS");
        lblNomeCliente.setFontStyle("[fsBold]");
        lblNomeCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblClienteClick(event);
            processarFlow("FrmClientesFlagsServ", "lblNomeCliente", "OnClick");
        });
        lblNomeCliente.setFieldName("NOME");
        lblNomeCliente.setTable(tbClienteDiverso);
        lblNomeCliente.setVerticalAlignment("taVerticalCenter");
        lblNomeCliente.setWordBreak(false);
        hboxCabecalho.addChildren(lblNomeCliente);
        lblNomeCliente.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(197);
        FHBox1.setTop(0);
        FHBox1.setWidth(13);
        FHBox1.setHeight(20);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        hboxCabecalho.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFLabel lblCodCliente = new TFLabel();

    private void init_lblCodCliente() {
        lblCodCliente.setName("lblCodCliente");
        lblCodCliente.setLeft(210);
        lblCodCliente.setTop(0);
        lblCodCliente.setWidth(142);
        lblCodCliente.setHeight(24);
        lblCodCliente.setCaption("993.313.301-20");
        lblCodCliente.setFontColor("13392431");
        lblCodCliente.setFontSize(-19);
        lblCodCliente.setFontName("Trebuchet MS");
        lblCodCliente.setFontStyle("[fsBold]");
        lblCodCliente.setFieldName("CNPJ_CPF");
        lblCodCliente.setTable(tbClienteDiverso);
        lblCodCliente.setVerticalAlignment("taVerticalCenter");
        lblCodCliente.setWordBreak(false);
        hboxCabecalho.addChildren(lblCodCliente);
        lblCodCliente.applyProperties();
    }

    public TFHBox hboxPesquisa = new TFHBox();

    private void init_hboxPesquisa() {
        hboxPesquisa.setName("hboxPesquisa");
        hboxPesquisa.setLeft(0);
        hboxPesquisa.setTop(36);
        hboxPesquisa.setWidth(870);
        hboxPesquisa.setHeight(80);
        hboxPesquisa.setBorderStyle("stNone");
        hboxPesquisa.setPaddingTop(5);
        hboxPesquisa.setPaddingLeft(5);
        hboxPesquisa.setPaddingRight(5);
        hboxPesquisa.setPaddingBottom(5);
        hboxPesquisa.setMarginTop(0);
        hboxPesquisa.setMarginLeft(0);
        hboxPesquisa.setMarginRight(0);
        hboxPesquisa.setMarginBottom(0);
        hboxPesquisa.setSpacing(8);
        hboxPesquisa.setFlexVflex("ftFalse");
        hboxPesquisa.setFlexHflex("ftTrue");
        hboxPesquisa.setScrollable(false);
        hboxPesquisa.setBoxShadowConfigHorizontalLength(10);
        hboxPesquisa.setBoxShadowConfigVerticalLength(10);
        hboxPesquisa.setBoxShadowConfigBlurRadius(5);
        hboxPesquisa.setBoxShadowConfigSpreadRadius(0);
        hboxPesquisa.setBoxShadowConfigShadowColor("clBlack");
        hboxPesquisa.setBoxShadowConfigOpacity(75);
        hboxPesquisa.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxPesquisa);
        hboxPesquisa.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(729);
        FVBox2.setHeight(74);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(6);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hboxPesquisa.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFCombo cbbClienteFlagGrupo = new TFCombo();

    private void init_cbbClienteFlagGrupo() {
        cbbClienteFlagGrupo.setName("cbbClienteFlagGrupo");
        cbbClienteFlagGrupo.setLeft(0);
        cbbClienteFlagGrupo.setTop(0);
        cbbClienteFlagGrupo.setWidth(292);
        cbbClienteFlagGrupo.setHeight(21);
        cbbClienteFlagGrupo.setLookupTable(tbClienteFlagGrupo);
        cbbClienteFlagGrupo.setLookupKey("ID_GRUPO");
        cbbClienteFlagGrupo.setLookupDesc("DESCRICAO");
        cbbClienteFlagGrupo.setFlex(true);
        cbbClienteFlagGrupo.setReadOnly(true);
        cbbClienteFlagGrupo.setRequired(false);
        cbbClienteFlagGrupo.setPrompt("Selecione o Grupo");
        cbbClienteFlagGrupo.setConstraintCheckWhen("cwImmediate");
        cbbClienteFlagGrupo.setConstraintCheckType("ctExpression");
        cbbClienteFlagGrupo.setConstraintFocusOnError(false);
        cbbClienteFlagGrupo.setConstraintEnableUI(true);
        cbbClienteFlagGrupo.setConstraintEnabled(false);
        cbbClienteFlagGrupo.setConstraintFormCheck(true);
        cbbClienteFlagGrupo.setClearOnDelKey(true);
        cbbClienteFlagGrupo.setUseClearButton(true);
        cbbClienteFlagGrupo.setHideClearButtonOnNullValue(false);
        FVBox2.addChildren(cbbClienteFlagGrupo);
        cbbClienteFlagGrupo.applyProperties();
        addValidatable(cbbClienteFlagGrupo);
    }

    public TFString edtPesquisaDescricao = new TFString();

    private void init_edtPesquisaDescricao() {
        edtPesquisaDescricao.setName("edtPesquisaDescricao");
        edtPesquisaDescricao.setLeft(0);
        edtPesquisaDescricao.setTop(22);
        edtPesquisaDescricao.setWidth(293);
        edtPesquisaDescricao.setHeight(24);
        edtPesquisaDescricao.setFlex(true);
        edtPesquisaDescricao.setRequired(false);
        edtPesquisaDescricao.setConstraintCheckWhen("cwImmediate");
        edtPesquisaDescricao.setConstraintCheckType("ctExpression");
        edtPesquisaDescricao.setConstraintFocusOnError(false);
        edtPesquisaDescricao.setConstraintEnableUI(true);
        edtPesquisaDescricao.setConstraintEnabled(false);
        edtPesquisaDescricao.setConstraintFormCheck(true);
        edtPesquisaDescricao.setCharCase("ccNormal");
        edtPesquisaDescricao.setPwd(false);
        edtPesquisaDescricao.setMaxlength(0);
        edtPesquisaDescricao.setFontColor("clWindowText");
        edtPesquisaDescricao.setFontSize(-13);
        edtPesquisaDescricao.setFontName("Tahoma");
        edtPesquisaDescricao.setFontStyle("[]");
        edtPesquisaDescricao.setSaveLiteralCharacter(false);
        edtPesquisaDescricao.applyProperties();
        FVBox2.addChildren(edtPesquisaDescricao);
        addValidatable(edtPesquisaDescricao);
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(729);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(74);
        btnPesquisar.setHeight(70);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setAlign("alLeft");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-13);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmClientesFlagsServ", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(27001);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        hboxPesquisa.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFVBox hboxDetalhe = new TFVBox();

    private void init_hboxDetalhe() {
        hboxDetalhe.setName("hboxDetalhe");
        hboxDetalhe.setLeft(0);
        hboxDetalhe.setTop(117);
        hboxDetalhe.setWidth(871);
        hboxDetalhe.setHeight(269);
        hboxDetalhe.setBorderStyle("stNone");
        hboxDetalhe.setPaddingTop(6);
        hboxDetalhe.setPaddingLeft(5);
        hboxDetalhe.setPaddingRight(5);
        hboxDetalhe.setPaddingBottom(0);
        hboxDetalhe.setMarginTop(0);
        hboxDetalhe.setMarginLeft(0);
        hboxDetalhe.setMarginRight(0);
        hboxDetalhe.setMarginBottom(0);
        hboxDetalhe.setSpacing(5);
        hboxDetalhe.setFlexVflex("ftTrue");
        hboxDetalhe.setFlexHflex("ftTrue");
        hboxDetalhe.setScrollable(false);
        hboxDetalhe.setBoxShadowConfigHorizontalLength(10);
        hboxDetalhe.setBoxShadowConfigVerticalLength(10);
        hboxDetalhe.setBoxShadowConfigBlurRadius(5);
        hboxDetalhe.setBoxShadowConfigSpreadRadius(0);
        hboxDetalhe.setBoxShadowConfigShadowColor("clBlack");
        hboxDetalhe.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(hboxDetalhe);
        hboxDetalhe.applyProperties();
    }

    public TFGrid gridClienteFlag = new TFGrid();

    private void init_gridClienteFlag() {
        gridClienteFlag.setName("gridClienteFlag");
        gridClienteFlag.setLeft(0);
        gridClienteFlag.setTop(0);
        gridClienteFlag.setWidth(865);
        gridClienteFlag.setHeight(284);
        gridClienteFlag.setTable(tbClienteFlag);
        gridClienteFlag.setFlexVflex("ftTrue");
        gridClienteFlag.setFlexHflex("ftTrue");
        gridClienteFlag.setPagingEnabled(true);
        gridClienteFlag.setFrozenColumns(0);
        gridClienteFlag.setShowFooter(false);
        gridClienteFlag.setShowHeader(true);
        gridClienteFlag.setMultiSelection(false);
        gridClienteFlag.setGroupingEnabled(false);
        gridClienteFlag.setGroupingExpanded(false);
        gridClienteFlag.setGroupingShowFooter(false);
        gridClienteFlag.setCrosstabEnabled(false);
        gridClienteFlag.setCrosstabGroupType("cgtConcat");
        gridClienteFlag.setEditionEnabled(false);
        gridClienteFlag.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(34);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("POR_EMPRESA = 'S'");
        item1.setEvalType("etExpression");
        item1.setImageId(7000213);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridClienteFlagclientesFlagsEmpClick(event);
            processarFlow("FrmClientesFlagsServ", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("GRUPO");
        item2.setTitleCaption("Grupo");
        item2.setWidth(40);
        item2.setVisible(false);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("DESCRICAO");
        item3.setTitleCaption("Descri\u00E7\u00E3o");
        item3.setWidth(40);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("POR_EMPRESA");
        item4.setTitleCaption("Por Empresa");
        item4.setWidth(40);
        item4.setVisible(false);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("TABELA_DELPHI");
        item5.setTitleCaption("Tabela Delphi");
        item5.setWidth(40);
        item5.setVisible(false);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("CAMPO_DELPHI");
        item6.setTitleCaption("Campo Delphi");
        item6.setWidth(40);
        item6.setVisible(false);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("OBJETO");
        item7.setTitleCaption("Objeto");
        item7.setWidth(40);
        item7.setVisible(false);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("HELP");
        item8.setTitleCaption("Help");
        item8.setWidth(40);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("ID");
        item9.setTitleCaption("Id.");
        item9.setWidth(40);
        item9.setVisible(false);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item9);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("VALOR_GRID");
        TFFontExpression item11 = new TFFontExpression();
        item11.setExpression("VALOR_GRID = 'Sim'");
        item11.setEvalType("etExpression");
        item11.setFontColor("clGreen");
        item11.setFontSize(-11);
        item11.setFontName("Tahoma");
        item11.setFontStyle("[]");
        item10.getFont().add(item11);
        TFFontExpression item12 = new TFFontExpression();
        item12.setExpression("VALOR_GRID = 'N\u00E3o'");
        item12.setEvalType("etExpression");
        item12.setFontColor("clRed");
        item12.setFontSize(-11);
        item12.setFontName("Tahoma");
        item12.setFontStyle("[]");
        item10.getFont().add(item12);
        item10.setTitleCaption("Valor");
        item10.setWidth(180);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setCheckedValue("S");
        item10.setUncheckedValue("N");
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        gridClienteFlag.getColumns().add(item10);
        hboxDetalhe.addChildren(gridClienteFlag);
        gridClienteFlag.applyProperties();
    }

    public TFMemo FMemo1 = new TFMemo();

    private void init_FMemo1() {
        FMemo1.setName("FMemo1");
        FMemo1.setLeft(0);
        FMemo1.setTop(285);
        FMemo1.setWidth(864);
        FMemo1.setHeight(73);
        FMemo1.setCharCase("ccNormal");
        FMemo1.setEnabled(false);
        FMemo1.setFontColor("clWindowText");
        FMemo1.setFontSize(-11);
        FMemo1.setFontName("Tahoma");
        FMemo1.setFontStyle("[]");
        FMemo1.setMaxlength(0);
        FMemo1.setFieldName("OBSERVACAO");
        FMemo1.setTable(tbClienteFlag);
        FMemo1.setFlexVflex("ftFalse");
        FMemo1.setFlexHflex("ftTrue");
        FMemo1.setConstraintCheckWhen("cwImmediate");
        FMemo1.setConstraintCheckType("ctExpression");
        FMemo1.setConstraintFocusOnError(false);
        FMemo1.setConstraintEnableUI(true);
        FMemo1.setConstraintEnabled(false);
        FMemo1.setConstraintFormCheck(true);
        FMemo1.setRequired(false);
        hboxDetalhe.addChildren(FMemo1);
        FMemo1.applyProperties();
        addValidatable(FMemo1);
    }

    public TFHBox hboxAltera = new TFHBox();

    private void init_hboxAltera() {
        hboxAltera.setName("hboxAltera");
        hboxAltera.setLeft(0);
        hboxAltera.setTop(387);
        hboxAltera.setWidth(870);
        hboxAltera.setHeight(82);
        hboxAltera.setBorderStyle("stNone");
        hboxAltera.setPaddingTop(0);
        hboxAltera.setPaddingLeft(5);
        hboxAltera.setPaddingRight(5);
        hboxAltera.setPaddingBottom(0);
        hboxAltera.setMarginTop(0);
        hboxAltera.setMarginLeft(0);
        hboxAltera.setMarginRight(0);
        hboxAltera.setMarginBottom(0);
        hboxAltera.setSpacing(5);
        hboxAltera.setFlexVflex("ftFalse");
        hboxAltera.setFlexHflex("ftTrue");
        hboxAltera.setScrollable(false);
        hboxAltera.setBoxShadowConfigHorizontalLength(10);
        hboxAltera.setBoxShadowConfigVerticalLength(10);
        hboxAltera.setBoxShadowConfigBlurRadius(5);
        hboxAltera.setBoxShadowConfigSpreadRadius(0);
        hboxAltera.setBoxShadowConfigShadowColor("clBlack");
        hboxAltera.setBoxShadowConfigOpacity(75);
        hboxAltera.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxAltera);
        hboxAltera.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(430);
        FVBox4.setHeight(65);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(5);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hboxAltera.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel lblCampo = new TFLabel();

    private void init_lblCampo() {
        lblCampo.setName("lblCampo");
        lblCampo.setLeft(0);
        lblCampo.setTop(0);
        lblCampo.setWidth(33);
        lblCampo.setHeight(13);
        lblCampo.setCaption("Campo");
        lblCampo.setFontColor("clWindowText");
        lblCampo.setFontSize(-11);
        lblCampo.setFontName("Tahoma");
        lblCampo.setFontStyle("[]");
        lblCampo.setVerticalAlignment("taVerticalCenter");
        lblCampo.setWordBreak(false);
        FVBox4.addChildren(lblCampo);
        lblCampo.applyProperties();
    }

    public TFString edtCampo = new TFString();

    private void init_edtCampo() {
        edtCampo.setName("edtCampo");
        edtCampo.setLeft(0);
        edtCampo.setTop(14);
        edtCampo.setWidth(317);
        edtCampo.setHeight(24);
        edtCampo.setTable(tbClienteFlag);
        edtCampo.setFieldName("CAMPO_DELPHI");
        edtCampo.setFlex(true);
        edtCampo.setRequired(false);
        edtCampo.setConstraintCheckWhen("cwImmediate");
        edtCampo.setConstraintCheckType("ctExpression");
        edtCampo.setConstraintFocusOnError(false);
        edtCampo.setConstraintEnableUI(true);
        edtCampo.setConstraintEnabled(false);
        edtCampo.setConstraintFormCheck(true);
        edtCampo.setCharCase("ccNormal");
        edtCampo.setPwd(false);
        edtCampo.setMaxlength(0);
        edtCampo.setEnabled(false);
        edtCampo.setFontColor("clWindowText");
        edtCampo.setFontSize(-13);
        edtCampo.setFontName("Tahoma");
        edtCampo.setFontStyle("[]");
        edtCampo.setSaveLiteralCharacter(false);
        edtCampo.applyProperties();
        FVBox4.addChildren(edtCampo);
        addValidatable(edtCampo);
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(430);
        FVBox5.setTop(0);
        FVBox5.setWidth(250);
        FVBox5.setHeight(65);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(5);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        hboxAltera.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel lblNovoValor = new TFLabel();

    private void init_lblNovoValor() {
        lblNovoValor.setName("lblNovoValor");
        lblNovoValor.setLeft(0);
        lblNovoValor.setTop(0);
        lblNovoValor.setWidth(52);
        lblNovoValor.setHeight(13);
        lblNovoValor.setCaption("Novo Valor");
        lblNovoValor.setFontColor("clWindowText");
        lblNovoValor.setFontSize(-11);
        lblNovoValor.setFontName("Tahoma");
        lblNovoValor.setFontStyle("[]");
        lblNovoValor.setVerticalAlignment("taVerticalCenter");
        lblNovoValor.setWordBreak(false);
        FVBox5.addChildren(lblNovoValor);
        lblNovoValor.applyProperties();
    }

    public TFString edtValorString = new TFString();

    private void init_edtValorString() {
        edtValorString.setName("edtValorString");
        edtValorString.setLeft(0);
        edtValorString.setTop(14);
        edtValorString.setWidth(121);
        edtValorString.setHeight(24);
        edtValorString.setTable(tbClienteFlag);
        edtValorString.setFieldName("VALOR");
        edtValorString.setFlex(true);
        edtValorString.setRequired(false);
        edtValorString.setConstraintCheckWhen("cwImmediate");
        edtValorString.setConstraintCheckType("ctExpression");
        edtValorString.setConstraintFocusOnError(false);
        edtValorString.setConstraintEnableUI(true);
        edtValorString.setConstraintEnabled(false);
        edtValorString.setConstraintFormCheck(true);
        edtValorString.setCharCase("ccNormal");
        edtValorString.setPwd(false);
        edtValorString.setMaxlength(0);
        edtValorString.setFontColor("clWindowText");
        edtValorString.setFontSize(-13);
        edtValorString.setFontName("Tahoma");
        edtValorString.setFontStyle("[]");
        edtValorString.setSaveLiteralCharacter(false);
        edtValorString.applyProperties();
        FVBox5.addChildren(edtValorString);
        addValidatable(edtValorString);
    }

    public TFCombo cbbValor = new TFCombo();

    private void init_cbbValor() {
        cbbValor.setName("cbbValor");
        cbbValor.setLeft(0);
        cbbValor.setTop(39);
        cbbValor.setWidth(145);
        cbbValor.setHeight(21);
        cbbValor.setTable(tbClienteFlag);
        cbbValor.setFieldName("VALOR");
        cbbValor.setFlex(true);
        cbbValor.setReadOnly(true);
        cbbValor.setRequired(false);
        cbbValor.setPrompt("Selecione");
        cbbValor.setConstraintCheckWhen("cwImmediate");
        cbbValor.setConstraintCheckType("ctExpression");
        cbbValor.setConstraintFocusOnError(false);
        cbbValor.setConstraintEnableUI(true);
        cbbValor.setConstraintEnabled(false);
        cbbValor.setConstraintFormCheck(true);
        cbbValor.setClearOnDelKey(true);
        cbbValor.setUseClearButton(true);
        cbbValor.setHideClearButtonOnNullValue(false);
        FVBox5.addChildren(cbbValor);
        cbbValor.applyProperties();
        addValidatable(cbbValor);
    }

    public TFDecimal edtValorDecimal = new TFDecimal();

    private void init_edtValorDecimal() {
        edtValorDecimal.setName("edtValorDecimal");
        edtValorDecimal.setLeft(0);
        edtValorDecimal.setTop(61);
        edtValorDecimal.setWidth(121);
        edtValorDecimal.setHeight(24);
        edtValorDecimal.setTable(tbClienteFlag);
        edtValorDecimal.setFieldName("VALOR");
        edtValorDecimal.setFlex(true);
        edtValorDecimal.setRequired(false);
        edtValorDecimal.setConstraintCheckWhen("cwImmediate");
        edtValorDecimal.setConstraintCheckType("ctExpression");
        edtValorDecimal.setConstraintFocusOnError(false);
        edtValorDecimal.setConstraintEnableUI(true);
        edtValorDecimal.setConstraintEnabled(false);
        edtValorDecimal.setConstraintFormCheck(true);
        edtValorDecimal.setMaxlength(0);
        edtValorDecimal.setPrecision(0);
        edtValorDecimal.setFontColor("clWindowText");
        edtValorDecimal.setFontSize(-13);
        edtValorDecimal.setFontName("Tahoma");
        edtValorDecimal.setFontStyle("[]");
        edtValorDecimal.setAlignment("taRightJustify");
        FVBox5.addChildren(edtValorDecimal);
        edtValorDecimal.applyProperties();
        addValidatable(edtValorDecimal);
    }

    public TFCheckBox chkValor = new TFCheckBox();

    private void init_chkValor() {
        chkValor.setName("chkValor");
        chkValor.setLeft(0);
        chkValor.setTop(86);
        chkValor.setWidth(97);
        chkValor.setHeight(17);
        chkValor.setCaption("Ativo?");
        chkValor.setFontColor("clWindowText");
        chkValor.setFontSize(-11);
        chkValor.setFontName("Tahoma");
        chkValor.setFontStyle("[]");
        chkValor.setTable(tbClienteFlag);
        chkValor.setFieldName("VALOR");
        chkValor.setCheckedValue("S");
        chkValor.setUncheckedValue("N");
        chkValor.setVerticalAlignment("taAlignTop");
        FVBox5.addChildren(chkValor);
        chkValor.applyProperties();
    }

    public TFInteger edtValorInteger = new TFInteger();

    private void init_edtValorInteger() {
        edtValorInteger.setName("edtValorInteger");
        edtValorInteger.setLeft(0);
        edtValorInteger.setTop(104);
        edtValorInteger.setWidth(121);
        edtValorInteger.setHeight(24);
        edtValorInteger.setTable(tbClienteFlag);
        edtValorInteger.setFieldName("VALOR");
        edtValorInteger.setFlex(true);
        edtValorInteger.setRequired(false);
        edtValorInteger.setConstraintCheckWhen("cwImmediate");
        edtValorInteger.setConstraintCheckType("ctExpression");
        edtValorInteger.setConstraintFocusOnError(false);
        edtValorInteger.setConstraintEnableUI(true);
        edtValorInteger.setConstraintEnabled(false);
        edtValorInteger.setConstraintFormCheck(true);
        edtValorInteger.setMaxlength(0);
        edtValorInteger.setFontColor("clWindowText");
        edtValorInteger.setFontSize(-13);
        edtValorInteger.setFontName("Tahoma");
        edtValorInteger.setFontStyle("[]");
        edtValorInteger.setAlignment("taRightJustify");
        FVBox5.addChildren(edtValorInteger);
        edtValorInteger.applyProperties();
        addValidatable(edtValorInteger);
    }

    public TFTime edtValorHora = new TFTime();

    private void init_edtValorHora() {
        edtValorHora.setName("edtValorHora");
        edtValorHora.setLeft(0);
        edtValorHora.setTop(129);
        edtValorHora.setWidth(148);
        edtValorHora.setHeight(24);
        edtValorHora.setTable(tbClienteFlag);
        edtValorHora.setFieldName("VALOR");
        edtValorHora.setFlex(true);
        edtValorHora.setRequired(false);
        edtValorHora.setConstraintCheckWhen("cwImmediate");
        edtValorHora.setConstraintCheckType("ctExpression");
        edtValorHora.setConstraintFocusOnError(false);
        edtValorHora.setConstraintEnableUI(true);
        edtValorHora.setConstraintEnabled(false);
        edtValorHora.setConstraintFormCheck(true);
        edtValorHora.setFormat("HH:mm");
        edtValorHora.setVisible(false);
        edtValorHora.setFontColor("clWindowText");
        edtValorHora.setFontSize(-13);
        edtValorHora.setFontName("Tahoma");
        edtValorHora.setFontStyle("[]");
        FVBox5.addChildren(edtValorHora);
        edtValorHora.applyProperties();
        addValidatable(edtValorHora);
    }

    public TFDate edtValorDate = new TFDate();

    private void init_edtValorDate() {
        edtValorDate.setName("edtValorDate");
        edtValorDate.setLeft(0);
        edtValorDate.setTop(154);
        edtValorDate.setWidth(148);
        edtValorDate.setHeight(24);
        edtValorDate.setTable(tbClienteFlag);
        edtValorDate.setFieldName("VALOR");
        edtValorDate.setFlex(true);
        edtValorDate.setRequired(false);
        edtValorDate.setConstraintCheckWhen("cwImmediate");
        edtValorDate.setConstraintCheckType("ctExpression");
        edtValorDate.setConstraintFocusOnError(false);
        edtValorDate.setConstraintEnableUI(true);
        edtValorDate.setConstraintEnabled(false);
        edtValorDate.setConstraintFormCheck(true);
        edtValorDate.setFormat("dd/MM/yyyy");
        edtValorDate.setShowCheckBox(false);
        edtValorDate.setVisible(false);
        FVBox5.addChildren(edtValorDate);
        edtValorDate.applyProperties();
        addValidatable(edtValorDate);
    }

    public TFImage FImageParam = new TFImage();

    private void init_FImageParam() {
        FImageParam.setName("FImageParam");
        FImageParam.setLeft(0);
        FImageParam.setTop(179);
        FImageParam.setWidth(108);
        FImageParam.setHeight(92);
        FImageParam.setVisible(false);
        FImageParam.setTable(tbClienteFlag);
        FImageParam.setFieldName("VALOR");
        FImageParam.setBoxSize(0);
        FImageParam.setGrayScaleOnDisable(false);
        FImageParam.setFlexVflex("ftFalse");
        FImageParam.setFlexHflex("ftFalse");
        FImageParam.setImageId(0);
        FVBox5.addChildren(FImageParam);
        FImageParam.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(680);
        FHBox2.setTop(0);
        FHBox2.setWidth(98);
        FHBox2.setHeight(58);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(5);
        FHBox2.setPaddingLeft(5);
        FHBox2.setPaddingRight(5);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hboxAltera.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(0);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(95);
        btnAlterar.setHeight(55);
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmClientesFlagsServ", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(0);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);
        FHBox2.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteDiverso);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbClienteCpagarIcmsSubEmp);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbClienteIssEmpresa);
        sc.getTables().add(item2);
        TFSchemaItem item3 = new TFSchemaItem();
        item3.setTable(tbClienteIsentoIssEmpresa);
        sc.getTables().add(item3);
        TFSchemaItem item4 = new TFSchemaItem();
        item4.setTable(tbAcessorioEmp);
        sc.getTables().add(item4);
        TFSchemaItem item5 = new TFSchemaItem();
        item5.setTable(tbIndustriaEmp);
        sc.getTables().add(item5);
        TFSchemaItem item6 = new TFSchemaItem();
        item6.setTable(tbFornecDescontaIcmsstEmp);
        sc.getTables().add(item6);
        TFSchemaItem item7 = new TFSchemaItem();
        item7.setTable(tbClienteIgnoraPjReterIss);
        sc.getTables().add(item7);
        TFSchemaItem item8 = new TFSchemaItem();
        item8.setTable(tbFornecSubIcmsEmp);
        sc.getTables().add(item8);
        TFSchemaItem item9 = new TFSchemaItem();
        item9.setTable(tbFornecIvaEmp);
        sc.getTables().add(item9);
        TFSchemaItem item10 = new TFSchemaItem();
        item10.setTable(tbClienteFlag);
        sc.getTables().add(item10);
        TFSchemaItem item11 = new TFSchemaItem();
        item11.setTable(tbClientes);
        sc.getTables().add(item11);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void lblClienteClick(final Event<Object> event);

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridClienteFlagclientesFlagsEmpClick(final Event<Object> event);

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void tbClienteFlagAfterScroll(final Event<Object> event);

}