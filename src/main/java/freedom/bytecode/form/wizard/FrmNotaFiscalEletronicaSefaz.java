package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmNotaFiscalEletronicaSefaz extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.NotaFiscalEletronicaSefazRNA rn = null;

    public FrmNotaFiscalEletronicaSefaz() {
        try {
            rn = (freedom.bytecode.rn.NotaFiscalEletronicaSefazRNA) getRN(freedom.bytecode.rn.wizard.NotaFiscalEletronicaSefazRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCrmpartsNfeMovimento();
        init_tbMovimento();
        init_tbLeadsNfeMensagem();
        init_timerNfe();
        init_timerPdf();
        init_ListaImagem();
        init_LogoNFE();
        init_vBoxNFe();
        init_pgcNFe();
        init_tbsAutorizarNfe();
        init_hBoxNotaFiscal();
        init_imgNFe();
        init_vBoxAguardar();
        init_FHBox6();
        init_FHBox8();
        init_FHBox14();
        init_lblNotaFiscalNr();
        init_hBoxStatus();
        init_FHBox10();
        init_lblStatusNfe();
        init_hBoxStatusPdf();
        init_FHBox2();
        init_lblStatusPdf();
        init_FHBox9();
        init_hBoxBotoes();
        init_FHBox12();
        init_btnReenviar();
        init_btnGerarFilaPdf();
        init_btnDownloadXML();
        init_btnTentarDepois();
        init_tbsMensagens();
        init_FrmNotaFiscalEletronicaSefaz();
    }

    public CRMPARTS_NFE_MOVIMENTO tbCrmpartsNfeMovimento;

    private void init_tbCrmpartsNfeMovimento() {
        tbCrmpartsNfeMovimento = rn.tbCrmpartsNfeMovimento;
        tbCrmpartsNfeMovimento.setName("tbCrmpartsNfeMovimento");
        tbCrmpartsNfeMovimento.setMaxRowCount(200);
        tbCrmpartsNfeMovimento.setWKey("5300656;53001");
        tbCrmpartsNfeMovimento.setRatioBatchSize(20);
        getTables().put(tbCrmpartsNfeMovimento, "tbCrmpartsNfeMovimento");
        tbCrmpartsNfeMovimento.applyProperties();
    }

    public NFE_MOVIMENTO tbMovimento;

    private void init_tbMovimento() {
        tbMovimento = rn.tbMovimento;
        tbMovimento.setName("tbMovimento");
        tbMovimento.setMaxRowCount(200);
        tbMovimento.setWKey("5300656;53002");
        tbMovimento.setRatioBatchSize(20);
        getTables().put(tbMovimento, "tbMovimento");
        tbMovimento.applyProperties();
    }

    public LEADS_NFE_MENSAGEM tbLeadsNfeMensagem;

    private void init_tbLeadsNfeMensagem() {
        tbLeadsNfeMensagem = rn.tbLeadsNfeMensagem;
        tbLeadsNfeMensagem.setName("tbLeadsNfeMensagem");
        tbLeadsNfeMensagem.setMaxRowCount(200);
        tbLeadsNfeMensagem.setWKey("5300656;53003");
        tbLeadsNfeMensagem.setRatioBatchSize(20);
        getTables().put(tbLeadsNfeMensagem, "tbLeadsNfeMensagem");
        tbLeadsNfeMensagem.applyProperties();
    }

    public TFTimer timerNfe = new TFTimer();

    private void init_timerNfe() {
        timerNfe.setName("timerNfe");
        timerNfe.setEnabled(false);
        timerNfe.setInterval(0);
        timerNfe.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            timerNfeTimer(event);
            processarFlow("FrmNotaFiscalEletronicaSefaz", "timerNfe", "OnTimer");
        });
        timerNfe.setRepeats(false);
        FrmNotaFiscalEletronicaSefaz.addChildren(timerNfe);
        timerNfe.applyProperties();
    }

    public TFTimer timerPdf = new TFTimer();

    private void init_timerPdf() {
        timerPdf.setName("timerPdf");
        timerPdf.setEnabled(false);
        timerPdf.setInterval(0);
        timerPdf.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            timerPdfTimer(event);
            processarFlow("FrmNotaFiscalEletronicaSefaz", "timerPdf", "OnTimer");
        });
        timerPdf.setRepeats(false);
        FrmNotaFiscalEletronicaSefaz.addChildren(timerPdf);
        timerPdf.applyProperties();
    }

    public TFPopupMenu ListaImagem = new TFPopupMenu();

    private void init_ListaImagem() {
        ListaImagem.setName("ListaImagem");
        FrmNotaFiscalEletronicaSefaz.addChildren(ListaImagem);
        ListaImagem.applyProperties();
    }

    public TFMenuItem LogoNFE = new TFMenuItem();

    private void init_LogoNFE() {
        LogoNFE.setName("LogoNFE");
        LogoNFE.setCaption("LogoNFE");
        LogoNFE.setImageIndex(310025);
        LogoNFE.setAccess(false);
        LogoNFE.setCheckmark(false);
        ListaImagem.addChildren(LogoNFE);
        LogoNFE.applyProperties();
    }

    protected TFForm FrmNotaFiscalEletronicaSefaz = this;
    private void init_FrmNotaFiscalEletronicaSefaz() {
        FrmNotaFiscalEletronicaSefaz.setName("FrmNotaFiscalEletronicaSefaz");
        FrmNotaFiscalEletronicaSefaz.setCaption("Nota Fiscal Eletr\u00F4nica");
        FrmNotaFiscalEletronicaSefaz.setClientHeight(277);
        FrmNotaFiscalEletronicaSefaz.setClientWidth(604);
        FrmNotaFiscalEletronicaSefaz.setColor("clBtnFace");
        FrmNotaFiscalEletronicaSefaz.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmNotaFiscalEletronicaSefaz", "FrmNotaFiscalEletronicaSefaz", "OnCreate");
        });
        FrmNotaFiscalEletronicaSefaz.setWOrigem("EhMain");
        FrmNotaFiscalEletronicaSefaz.setWKey("5300656");
        FrmNotaFiscalEletronicaSefaz.setSpacing(0);
        FrmNotaFiscalEletronicaSefaz.applyProperties();
    }

    public TFVBox vBoxNFe = new TFVBox();

    private void init_vBoxNFe() {
        vBoxNFe.setName("vBoxNFe");
        vBoxNFe.setLeft(0);
        vBoxNFe.setTop(0);
        vBoxNFe.setWidth(604);
        vBoxNFe.setHeight(277);
        vBoxNFe.setAlign("alClient");
        vBoxNFe.setBorderStyle("stNone");
        vBoxNFe.setPaddingTop(0);
        vBoxNFe.setPaddingLeft(0);
        vBoxNFe.setPaddingRight(0);
        vBoxNFe.setPaddingBottom(0);
        vBoxNFe.setMarginTop(0);
        vBoxNFe.setMarginLeft(0);
        vBoxNFe.setMarginRight(0);
        vBoxNFe.setMarginBottom(0);
        vBoxNFe.setSpacing(1);
        vBoxNFe.setFlexVflex("ftFalse");
        vBoxNFe.setFlexHflex("ftTrue");
        vBoxNFe.setScrollable(false);
        vBoxNFe.setBoxShadowConfigHorizontalLength(10);
        vBoxNFe.setBoxShadowConfigVerticalLength(10);
        vBoxNFe.setBoxShadowConfigBlurRadius(5);
        vBoxNFe.setBoxShadowConfigSpreadRadius(0);
        vBoxNFe.setBoxShadowConfigShadowColor("clBlack");
        vBoxNFe.setBoxShadowConfigOpacity(75);
        FrmNotaFiscalEletronicaSefaz.addChildren(vBoxNFe);
        vBoxNFe.applyProperties();
    }

    public TFPageControl pgcNFe = new TFPageControl();

    private void init_pgcNFe() {
        pgcNFe.setName("pgcNFe");
        pgcNFe.setLeft(0);
        pgcNFe.setTop(0);
        pgcNFe.setWidth(581);
        pgcNFe.setHeight(236);
        pgcNFe.setAlign("alClient");
        pgcNFe.setTabPosition("tpTop");
        pgcNFe.setFlexVflex("ftTrue");
        pgcNFe.setFlexHflex("ftTrue");
        pgcNFe.setRenderStyle("rsTabbed");
        pgcNFe.applyProperties();
        vBoxNFe.addChildren(pgcNFe);
    }

    public TFTabsheet tbsAutorizarNfe = new TFTabsheet();

    private void init_tbsAutorizarNfe() {
        tbsAutorizarNfe.setName("tbsAutorizarNfe");
        tbsAutorizarNfe.setCaption("Emitir NF-e");
        tbsAutorizarNfe.setVisible(true);
        tbsAutorizarNfe.setClosable(false);
        pgcNFe.addChildren(tbsAutorizarNfe);
        tbsAutorizarNfe.applyProperties();
    }

    public TFHBox hBoxNotaFiscal = new TFHBox();

    private void init_hBoxNotaFiscal() {
        hBoxNotaFiscal.setName("hBoxNotaFiscal");
        hBoxNotaFiscal.setLeft(0);
        hBoxNotaFiscal.setTop(0);
        hBoxNotaFiscal.setWidth(573);
        hBoxNotaFiscal.setHeight(208);
        hBoxNotaFiscal.setAlign("alClient");
        hBoxNotaFiscal.setBorderStyle("stNone");
        hBoxNotaFiscal.setPaddingTop(0);
        hBoxNotaFiscal.setPaddingLeft(8);
        hBoxNotaFiscal.setPaddingRight(0);
        hBoxNotaFiscal.setPaddingBottom(0);
        hBoxNotaFiscal.setMarginTop(0);
        hBoxNotaFiscal.setMarginLeft(0);
        hBoxNotaFiscal.setMarginRight(0);
        hBoxNotaFiscal.setMarginBottom(0);
        hBoxNotaFiscal.setSpacing(1);
        hBoxNotaFiscal.setFlexVflex("ftTrue");
        hBoxNotaFiscal.setFlexHflex("ftTrue");
        hBoxNotaFiscal.setScrollable(false);
        hBoxNotaFiscal.setBoxShadowConfigHorizontalLength(10);
        hBoxNotaFiscal.setBoxShadowConfigVerticalLength(10);
        hBoxNotaFiscal.setBoxShadowConfigBlurRadius(5);
        hBoxNotaFiscal.setBoxShadowConfigSpreadRadius(0);
        hBoxNotaFiscal.setBoxShadowConfigShadowColor("clBlack");
        hBoxNotaFiscal.setBoxShadowConfigOpacity(75);
        hBoxNotaFiscal.setVAlign("tvTop");
        tbsAutorizarNfe.addChildren(hBoxNotaFiscal);
        hBoxNotaFiscal.applyProperties();
    }

    public TFImage imgNFe = new TFImage();

    private void init_imgNFe() {
        imgNFe.setName("imgNFe");
        imgNFe.setLeft(0);
        imgNFe.setTop(0);
        imgNFe.setWidth(81);
        imgNFe.setHeight(77);
        imgNFe.setImageSrc("/images/crmparts310025.png");
        imgNFe.setBoxSize(0);
        imgNFe.setGrayScaleOnDisable(false);
        imgNFe.setFlexVflex("ftFalse");
        imgNFe.setFlexHflex("ftFalse");
        hBoxNotaFiscal.addChildren(imgNFe);
        imgNFe.applyProperties();
    }

    public TFVBox vBoxAguardar = new TFVBox();

    private void init_vBoxAguardar() {
        vBoxAguardar.setName("vBoxAguardar");
        vBoxAguardar.setLeft(81);
        vBoxAguardar.setTop(0);
        vBoxAguardar.setWidth(461);
        vBoxAguardar.setHeight(201);
        vBoxAguardar.setBorderStyle("stNone");
        vBoxAguardar.setPaddingTop(0);
        vBoxAguardar.setPaddingLeft(0);
        vBoxAguardar.setPaddingRight(0);
        vBoxAguardar.setPaddingBottom(0);
        vBoxAguardar.setMarginTop(0);
        vBoxAguardar.setMarginLeft(0);
        vBoxAguardar.setMarginRight(0);
        vBoxAguardar.setMarginBottom(0);
        vBoxAguardar.setSpacing(1);
        vBoxAguardar.setFlexVflex("ftTrue");
        vBoxAguardar.setFlexHflex("ftTrue");
        vBoxAguardar.setScrollable(false);
        vBoxAguardar.setBoxShadowConfigHorizontalLength(10);
        vBoxAguardar.setBoxShadowConfigVerticalLength(10);
        vBoxAguardar.setBoxShadowConfigBlurRadius(5);
        vBoxAguardar.setBoxShadowConfigSpreadRadius(0);
        vBoxAguardar.setBoxShadowConfigShadowColor("clBlack");
        vBoxAguardar.setBoxShadowConfigOpacity(75);
        hBoxNotaFiscal.addChildren(vBoxAguardar);
        vBoxAguardar.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(185);
        FHBox6.setHeight(19);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxAguardar.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(20);
        FHBox8.setWidth(449);
        FHBox8.setHeight(30);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        vBoxAguardar.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(0);
        FHBox14.setWidth(10);
        FHBox14.setHeight(20);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        FHBox8.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFLabel lblNotaFiscalNr = new TFLabel();

    private void init_lblNotaFiscalNr() {
        lblNotaFiscalNr.setName("lblNotaFiscalNr");
        lblNotaFiscalNr.setLeft(10);
        lblNotaFiscalNr.setTop(0);
        lblNotaFiscalNr.setWidth(169);
        lblNotaFiscalNr.setHeight(23);
        lblNotaFiscalNr.setCaption("Nota fiscal nr. 150/1");
        lblNotaFiscalNr.setFontColor("clWindowText");
        lblNotaFiscalNr.setFontSize(-19);
        lblNotaFiscalNr.setFontName("Tahoma");
        lblNotaFiscalNr.setFontStyle("[]");
        lblNotaFiscalNr.setVerticalAlignment("taVerticalCenter");
        lblNotaFiscalNr.setWordBreak(false);
        FHBox8.addChildren(lblNotaFiscalNr);
        lblNotaFiscalNr.applyProperties();
    }

    public TFHBox hBoxStatus = new TFHBox();

    private void init_hBoxStatus() {
        hBoxStatus.setName("hBoxStatus");
        hBoxStatus.setLeft(0);
        hBoxStatus.setTop(51);
        hBoxStatus.setWidth(446);
        hBoxStatus.setHeight(22);
        hBoxStatus.setBorderStyle("stNone");
        hBoxStatus.setPaddingTop(0);
        hBoxStatus.setPaddingLeft(0);
        hBoxStatus.setPaddingRight(0);
        hBoxStatus.setPaddingBottom(0);
        hBoxStatus.setMarginTop(0);
        hBoxStatus.setMarginLeft(0);
        hBoxStatus.setMarginRight(0);
        hBoxStatus.setMarginBottom(0);
        hBoxStatus.setSpacing(1);
        hBoxStatus.setFlexVflex("ftFalse");
        hBoxStatus.setFlexHflex("ftTrue");
        hBoxStatus.setScrollable(false);
        hBoxStatus.setBoxShadowConfigHorizontalLength(10);
        hBoxStatus.setBoxShadowConfigVerticalLength(10);
        hBoxStatus.setBoxShadowConfigBlurRadius(5);
        hBoxStatus.setBoxShadowConfigSpreadRadius(0);
        hBoxStatus.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatus.setBoxShadowConfigOpacity(75);
        hBoxStatus.setVAlign("tvTop");
        vBoxAguardar.addChildren(hBoxStatus);
        hBoxStatus.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(10);
        FHBox10.setHeight(20);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        hBoxStatus.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFLabel lblStatusNfe = new TFLabel();

    private void init_lblStatusNfe() {
        lblStatusNfe.setName("lblStatusNfe");
        lblStatusNfe.setLeft(10);
        lblStatusNfe.setTop(0);
        lblStatusNfe.setWidth(131);
        lblStatusNfe.setHeight(16);
        lblStatusNfe.setCaption("[00:00] Processando...");
        lblStatusNfe.setFontColor("clWindowText");
        lblStatusNfe.setFontSize(-13);
        lblStatusNfe.setFontName("Tahoma");
        lblStatusNfe.setFontStyle("[]");
        lblStatusNfe.setVerticalAlignment("taVerticalCenter");
        lblStatusNfe.setWordBreak(false);
        hBoxStatus.addChildren(lblStatusNfe);
        lblStatusNfe.applyProperties();
    }

    public TFHBox hBoxStatusPdf = new TFHBox();

    private void init_hBoxStatusPdf() {
        hBoxStatusPdf.setName("hBoxStatusPdf");
        hBoxStatusPdf.setLeft(0);
        hBoxStatusPdf.setTop(74);
        hBoxStatusPdf.setWidth(446);
        hBoxStatusPdf.setHeight(22);
        hBoxStatusPdf.setBorderStyle("stNone");
        hBoxStatusPdf.setPaddingTop(0);
        hBoxStatusPdf.setPaddingLeft(0);
        hBoxStatusPdf.setPaddingRight(0);
        hBoxStatusPdf.setPaddingBottom(0);
        hBoxStatusPdf.setVisible(false);
        hBoxStatusPdf.setMarginTop(0);
        hBoxStatusPdf.setMarginLeft(0);
        hBoxStatusPdf.setMarginRight(0);
        hBoxStatusPdf.setMarginBottom(0);
        hBoxStatusPdf.setSpacing(1);
        hBoxStatusPdf.setFlexVflex("ftFalse");
        hBoxStatusPdf.setFlexHflex("ftTrue");
        hBoxStatusPdf.setScrollable(false);
        hBoxStatusPdf.setBoxShadowConfigHorizontalLength(10);
        hBoxStatusPdf.setBoxShadowConfigVerticalLength(10);
        hBoxStatusPdf.setBoxShadowConfigBlurRadius(5);
        hBoxStatusPdf.setBoxShadowConfigSpreadRadius(0);
        hBoxStatusPdf.setBoxShadowConfigShadowColor("clBlack");
        hBoxStatusPdf.setBoxShadowConfigOpacity(75);
        hBoxStatusPdf.setVAlign("tvTop");
        vBoxAguardar.addChildren(hBoxStatusPdf);
        hBoxStatusPdf.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(10);
        FHBox2.setHeight(20);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxStatusPdf.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFLabel lblStatusPdf = new TFLabel();

    private void init_lblStatusPdf() {
        lblStatusPdf.setName("lblStatusPdf");
        lblStatusPdf.setLeft(10);
        lblStatusPdf.setTop(0);
        lblStatusPdf.setWidth(131);
        lblStatusPdf.setHeight(16);
        lblStatusPdf.setCaption("[00:00] Processando...");
        lblStatusPdf.setFontColor("clWindowText");
        lblStatusPdf.setFontSize(-13);
        lblStatusPdf.setFontName("Tahoma");
        lblStatusPdf.setFontStyle("[]");
        lblStatusPdf.setVerticalAlignment("taVerticalCenter");
        lblStatusPdf.setWordBreak(false);
        hBoxStatusPdf.addChildren(lblStatusPdf);
        lblStatusPdf.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(97);
        FHBox9.setWidth(454);
        FHBox9.setHeight(50);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        vBoxAguardar.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(148);
        hBoxBotoes.setWidth(456);
        hBoxBotoes.setHeight(41);
        hBoxBotoes.setAlign("alTop");
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(5);
        hBoxBotoes.setPaddingLeft(10);
        hBoxBotoes.setPaddingRight(25);
        hBoxBotoes.setPaddingBottom(10);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftFalse");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxAguardar.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(0);
        FHBox12.setWidth(50);
        FHBox12.setHeight(25);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        hBoxBotoes.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFButton btnReenviar = new TFButton();

    private void init_btnReenviar() {
        btnReenviar.setName("btnReenviar");
        btnReenviar.setLeft(50);
        btnReenviar.setTop(0);
        btnReenviar.setWidth(99);
        btnReenviar.setHeight(30);
        btnReenviar.setHint("Reenviar NF-e para a Sefaz");
        btnReenviar.setCaption("Reenviar");
        btnReenviar.setEnabled(false);
        btnReenviar.setFontColor("clWindowText");
        btnReenviar.setFontSize(-11);
        btnReenviar.setFontName("Tahoma");
        btnReenviar.setFontStyle("[]");
        btnReenviar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnReenviarClick(event);
            processarFlow("FrmNotaFiscalEletronicaSefaz", "btnReenviar", "OnClick");
        });
        btnReenviar.setImageId(0);
        btnReenviar.setColor("clBtnFace");
        btnReenviar.setAccess(false);
        btnReenviar.setIconClass("refresh");
        btnReenviar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnReenviar);
        btnReenviar.applyProperties();
    }

    public TFButton btnGerarFilaPdf = new TFButton();

    private void init_btnGerarFilaPdf() {
        btnGerarFilaPdf.setName("btnGerarFilaPdf");
        btnGerarFilaPdf.setLeft(149);
        btnGerarFilaPdf.setTop(0);
        btnGerarFilaPdf.setWidth(99);
        btnGerarFilaPdf.setHeight(30);
        btnGerarFilaPdf.setHint("Gerar Pdf ");
        btnGerarFilaPdf.setCaption("Gerar PDF");
        btnGerarFilaPdf.setEnabled(false);
        btnGerarFilaPdf.setFontColor("clWindowText");
        btnGerarFilaPdf.setFontSize(-11);
        btnGerarFilaPdf.setFontName("Tahoma");
        btnGerarFilaPdf.setFontStyle("[]");
        btnGerarFilaPdf.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnGerarFilaPdfClick(event);
            processarFlow("FrmNotaFiscalEletronicaSefaz", "btnGerarFilaPdf", "OnClick");
        });
        btnGerarFilaPdf.setImageId(0);
        btnGerarFilaPdf.setColor("clBtnFace");
        btnGerarFilaPdf.setAccess(false);
        btnGerarFilaPdf.setIconClass("file-pdf-o");
        btnGerarFilaPdf.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnGerarFilaPdf);
        btnGerarFilaPdf.applyProperties();
    }

    public TFButton btnDownloadXML = new TFButton();

    private void init_btnDownloadXML() {
        btnDownloadXML.setName("btnDownloadXML");
        btnDownloadXML.setLeft(248);
        btnDownloadXML.setTop(0);
        btnDownloadXML.setWidth(99);
        btnDownloadXML.setHeight(30);
        btnDownloadXML.setHint("Download XML");
        btnDownloadXML.setCaption("Download XML");
        btnDownloadXML.setFontColor("clWindowText");
        btnDownloadXML.setFontSize(-11);
        btnDownloadXML.setFontName("Tahoma");
        btnDownloadXML.setFontStyle("[]");
        btnDownloadXML.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnDownloadXMLClick(event);
            processarFlow("FrmNotaFiscalEletronicaSefaz", "btnDownloadXML", "OnClick");
        });
        btnDownloadXML.setImageId(3930484);
        btnDownloadXML.setColor("clBtnFace");
        btnDownloadXML.setAccess(false);
        btnDownloadXML.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnDownloadXML);
        btnDownloadXML.applyProperties();
    }

    public TFButton btnTentarDepois = new TFButton();

    private void init_btnTentarDepois() {
        btnTentarDepois.setName("btnTentarDepois");
        btnTentarDepois.setLeft(347);
        btnTentarDepois.setTop(0);
        btnTentarDepois.setWidth(99);
        btnTentarDepois.setHeight(30);
        btnTentarDepois.setHint("Adiar resolu\u00E7\u00E3o da pend\u00EAncia junto a Sefaz.");
        btnTentarDepois.setCaption("Tentar depois");
        btnTentarDepois.setFontColor("clRed");
        btnTentarDepois.setFontSize(-11);
        btnTentarDepois.setFontName("Tahoma");
        btnTentarDepois.setFontStyle("[]");
        btnTentarDepois.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnTentarDepoisClick(event);
            processarFlow("FrmNotaFiscalEletronicaSefaz", "btnTentarDepois", "OnClick");
        });
        btnTentarDepois.setImageId(0);
        btnTentarDepois.setColor("clBtnFace");
        btnTentarDepois.setAccess(false);
        btnTentarDepois.setIconClass("reply");
        btnTentarDepois.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnTentarDepois);
        btnTentarDepois.applyProperties();
    }

    public TFTabsheet tbsMensagens = new TFTabsheet();

    private void init_tbsMensagens() {
        tbsMensagens.setName("tbsMensagens");
        tbsMensagens.setCaption("Mensagens");
        tbsMensagens.setVisible(true);
        tbsMensagens.setClosable(false);
        pgcNFe.addChildren(tbsMensagens);
        tbsMensagens.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnReenviarClick(final Event<Object> event) {
        if (btnReenviar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnReenviar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnGerarFilaPdfClick(final Event<Object> event) {
        if (btnGerarFilaPdf.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnGerarFilaPdf");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnDownloadXMLClick(final Event<Object> event) {
        if (btnDownloadXML.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnDownloadXML");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnTentarDepoisClick(final Event<Object> event) {
        if (btnTentarDepois.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnTentarDepois");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void timerNfeTimer(final Event<Object> event);

    public abstract void timerPdfTimer(final Event<Object> event);

}