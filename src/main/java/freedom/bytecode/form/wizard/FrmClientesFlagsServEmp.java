package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClientesFlagsServEmp extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClientesFlagsServEmpRNA rn = null;

    public FrmClientesFlagsServEmp() {
        try {
            rn = (freedom.bytecode.rn.ClientesFlagsServEmpRNA) getRN(freedom.bytecode.rn.wizard.ClientesFlagsServEmpRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteCpagarIcmsSubEmp();
        init_tbAcessorioEmp();
        init_tbIndustriaEmp();
        init_tbClienteDiverso();
        init_tbFornecDescontaIcmsstEmp();
        init_tbClienteIssEmpresa();
        init_tbClienteIsentoIssEmpresa();
        init_tbClienteIgnoraPjReterIss();
        init_tbFornecSubIcmsEmp();
        init_tbFornecIvaEmp();
        init_tbClienteflagempresa();
        init_tbEmpresas();
        init_vboxPrincipal();
        init_vboxGrid();
        init_gridClienteFlagEmpresa();
        init_hboxAltera();
        init_FVBox5();
        init_lblNovoValor();
        init_edtValorDecimal();
        init_chkValor();
        init_FHBox2();
        init_btnAlterar();
        init_sc();
        init_FrmClientesFlagsServEmp();
    }

    public CLIENTE_CPAGAR_ICMS_SUB_EMP tbClienteCpagarIcmsSubEmp;

    private void init_tbClienteCpagarIcmsSubEmp() {
        tbClienteCpagarIcmsSubEmp = rn.tbClienteCpagarIcmsSubEmp;
        tbClienteCpagarIcmsSubEmp.setName("tbClienteCpagarIcmsSubEmp");
        tbClienteCpagarIcmsSubEmp.setMaxRowCount(200);
        tbClienteCpagarIcmsSubEmp.setWKey("4600452;46001");
        tbClienteCpagarIcmsSubEmp.setRatioBatchSize(20);
        getTables().put(tbClienteCpagarIcmsSubEmp, "tbClienteCpagarIcmsSubEmp");
        tbClienteCpagarIcmsSubEmp.applyProperties();
    }

    public ACESSORIO_EMP tbAcessorioEmp;

    private void init_tbAcessorioEmp() {
        tbAcessorioEmp = rn.tbAcessorioEmp;
        tbAcessorioEmp.setName("tbAcessorioEmp");
        tbAcessorioEmp.setMaxRowCount(200);
        tbAcessorioEmp.setWKey("4600452;46002");
        tbAcessorioEmp.setRatioBatchSize(20);
        getTables().put(tbAcessorioEmp, "tbAcessorioEmp");
        tbAcessorioEmp.applyProperties();
    }

    public INDUSTRIA_EMP tbIndustriaEmp;

    private void init_tbIndustriaEmp() {
        tbIndustriaEmp = rn.tbIndustriaEmp;
        tbIndustriaEmp.setName("tbIndustriaEmp");
        tbIndustriaEmp.setMaxRowCount(200);
        tbIndustriaEmp.setWKey("4600452;46003");
        tbIndustriaEmp.setRatioBatchSize(20);
        getTables().put(tbIndustriaEmp, "tbIndustriaEmp");
        tbIndustriaEmp.applyProperties();
    }

    public CLIENTE_DIVERSO tbClienteDiverso;

    private void init_tbClienteDiverso() {
        tbClienteDiverso = rn.tbClienteDiverso;
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("4600452;46004");
        tbClienteDiverso.setRatioBatchSize(20);
        getTables().put(tbClienteDiverso, "tbClienteDiverso");
        tbClienteDiverso.applyProperties();
    }

    public FORNEC_DESCONTA_ICMSST_EMP tbFornecDescontaIcmsstEmp;

    private void init_tbFornecDescontaIcmsstEmp() {
        tbFornecDescontaIcmsstEmp = rn.tbFornecDescontaIcmsstEmp;
        tbFornecDescontaIcmsstEmp.setName("tbFornecDescontaIcmsstEmp");
        tbFornecDescontaIcmsstEmp.setMaxRowCount(200);
        tbFornecDescontaIcmsstEmp.setWKey("4600452;46005");
        tbFornecDescontaIcmsstEmp.setRatioBatchSize(20);
        getTables().put(tbFornecDescontaIcmsstEmp, "tbFornecDescontaIcmsstEmp");
        tbFornecDescontaIcmsstEmp.applyProperties();
    }

    public CLIENTE_ISS_EMPRESA tbClienteIssEmpresa;

    private void init_tbClienteIssEmpresa() {
        tbClienteIssEmpresa = rn.tbClienteIssEmpresa;
        tbClienteIssEmpresa.setName("tbClienteIssEmpresa");
        tbClienteIssEmpresa.setMaxRowCount(200);
        tbClienteIssEmpresa.setWKey("4600452;46006");
        tbClienteIssEmpresa.setRatioBatchSize(20);
        getTables().put(tbClienteIssEmpresa, "tbClienteIssEmpresa");
        tbClienteIssEmpresa.applyProperties();
    }

    public CLIENTE_ISENTO_ISS_EMPRESA tbClienteIsentoIssEmpresa;

    private void init_tbClienteIsentoIssEmpresa() {
        tbClienteIsentoIssEmpresa = rn.tbClienteIsentoIssEmpresa;
        tbClienteIsentoIssEmpresa.setName("tbClienteIsentoIssEmpresa");
        tbClienteIsentoIssEmpresa.setMaxRowCount(200);
        tbClienteIsentoIssEmpresa.setWKey("4600452;46007");
        tbClienteIsentoIssEmpresa.setRatioBatchSize(20);
        getTables().put(tbClienteIsentoIssEmpresa, "tbClienteIsentoIssEmpresa");
        tbClienteIsentoIssEmpresa.applyProperties();
    }

    public CLIENTE_IGNORA_PJ_RETER_ISS tbClienteIgnoraPjReterIss;

    private void init_tbClienteIgnoraPjReterIss() {
        tbClienteIgnoraPjReterIss = rn.tbClienteIgnoraPjReterIss;
        tbClienteIgnoraPjReterIss.setName("tbClienteIgnoraPjReterIss");
        tbClienteIgnoraPjReterIss.setMaxRowCount(200);
        tbClienteIgnoraPjReterIss.setWKey("4600452;46008");
        tbClienteIgnoraPjReterIss.setRatioBatchSize(20);
        getTables().put(tbClienteIgnoraPjReterIss, "tbClienteIgnoraPjReterIss");
        tbClienteIgnoraPjReterIss.applyProperties();
    }

    public FORNEC_SUB_ICMS_EMP tbFornecSubIcmsEmp;

    private void init_tbFornecSubIcmsEmp() {
        tbFornecSubIcmsEmp = rn.tbFornecSubIcmsEmp;
        tbFornecSubIcmsEmp.setName("tbFornecSubIcmsEmp");
        tbFornecSubIcmsEmp.setMaxRowCount(200);
        tbFornecSubIcmsEmp.setWKey("4600452;46009");
        tbFornecSubIcmsEmp.setRatioBatchSize(20);
        getTables().put(tbFornecSubIcmsEmp, "tbFornecSubIcmsEmp");
        tbFornecSubIcmsEmp.applyProperties();
    }

    public FORNEC_IVA_EMP tbFornecIvaEmp;

    private void init_tbFornecIvaEmp() {
        tbFornecIvaEmp = rn.tbFornecIvaEmp;
        tbFornecIvaEmp.setName("tbFornecIvaEmp");
        tbFornecIvaEmp.setMaxRowCount(200);
        tbFornecIvaEmp.setWKey("4600452;460010");
        tbFornecIvaEmp.setRatioBatchSize(20);
        getTables().put(tbFornecIvaEmp, "tbFornecIvaEmp");
        tbFornecIvaEmp.applyProperties();
    }

    public CLIENTEFLAGEMPRESA tbClienteflagempresa;

    private void init_tbClienteflagempresa() {
        tbClienteflagempresa = rn.tbClienteflagempresa;
        tbClienteflagempresa.setName("tbClienteflagempresa");
        tbClienteflagempresa.setMaxRowCount(200);
        tbClienteflagempresa.setWKey("4600452;460011");
        tbClienteflagempresa.setRatioBatchSize(20);
        getTables().put(tbClienteflagempresa, "tbClienteflagempresa");
        tbClienteflagempresa.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("4600452;460013");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    protected TFForm FrmClientesFlagsServEmp = this;
    private void init_FrmClientesFlagsServEmp() {
        FrmClientesFlagsServEmp.setName("FrmClientesFlagsServEmp");
        FrmClientesFlagsServEmp.setCaption(" Clientes Flags Empresa");
        FrmClientesFlagsServEmp.setClientHeight(392);
        FrmClientesFlagsServEmp.setClientWidth(434);
        FrmClientesFlagsServEmp.setColor("clBtnFace");
        FrmClientesFlagsServEmp.setWKey("4600452");
        FrmClientesFlagsServEmp.setSpacing(0);
        FrmClientesFlagsServEmp.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(434);
        vboxPrincipal.setHeight(392);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(0);
        vboxPrincipal.setPaddingLeft(0);
        vboxPrincipal.setPaddingRight(0);
        vboxPrincipal.setPaddingBottom(0);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(1);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmClientesFlagsServEmp.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFVBox vboxGrid = new TFVBox();

    private void init_vboxGrid() {
        vboxGrid.setName("vboxGrid");
        vboxGrid.setLeft(0);
        vboxGrid.setTop(0);
        vboxGrid.setWidth(652);
        vboxGrid.setHeight(280);
        vboxGrid.setBorderStyle("stNone");
        vboxGrid.setPaddingTop(10);
        vboxGrid.setPaddingLeft(10);
        vboxGrid.setPaddingRight(10);
        vboxGrid.setPaddingBottom(10);
        vboxGrid.setMarginTop(0);
        vboxGrid.setMarginLeft(0);
        vboxGrid.setMarginRight(0);
        vboxGrid.setMarginBottom(0);
        vboxGrid.setSpacing(1);
        vboxGrid.setFlexVflex("ftTrue");
        vboxGrid.setFlexHflex("ftTrue");
        vboxGrid.setScrollable(false);
        vboxGrid.setBoxShadowConfigHorizontalLength(10);
        vboxGrid.setBoxShadowConfigVerticalLength(10);
        vboxGrid.setBoxShadowConfigBlurRadius(5);
        vboxGrid.setBoxShadowConfigSpreadRadius(0);
        vboxGrid.setBoxShadowConfigShadowColor("clBlack");
        vboxGrid.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(vboxGrid);
        vboxGrid.applyProperties();
    }

    public TFGrid gridClienteFlagEmpresa = new TFGrid();

    private void init_gridClienteFlagEmpresa() {
        gridClienteFlagEmpresa.setName("gridClienteFlagEmpresa");
        gridClienteFlagEmpresa.setLeft(0);
        gridClienteFlagEmpresa.setTop(0);
        gridClienteFlagEmpresa.setWidth(650);
        gridClienteFlagEmpresa.setHeight(324);
        gridClienteFlagEmpresa.setTable(tbClienteflagempresa);
        gridClienteFlagEmpresa.setFlexVflex("ftTrue");
        gridClienteFlagEmpresa.setFlexHflex("ftTrue");
        gridClienteFlagEmpresa.setPagingEnabled(false);
        gridClienteFlagEmpresa.setFrozenColumns(0);
        gridClienteFlagEmpresa.setShowFooter(false);
        gridClienteFlagEmpresa.setShowHeader(true);
        gridClienteFlagEmpresa.setMultiSelection(false);
        gridClienteFlagEmpresa.setGroupingEnabled(false);
        gridClienteFlagEmpresa.setGroupingExpanded(false);
        gridClienteFlagEmpresa.setGroupingShowFooter(false);
        gridClienteFlagEmpresa.setCrosstabEnabled(false);
        gridClienteFlagEmpresa.setCrosstabGroupType("cgtConcat");
        gridClienteFlagEmpresa.setEditionEnabled(false);
        gridClienteFlagEmpresa.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_EMPRESA");
        item0.setTitleCaption("C\u00F3d. Empresa");
        item0.setWidth(40);
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridClienteFlagEmpresa.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("COD_CLIENTE");
        item1.setTitleCaption("C\u00F3d. Cliente");
        item1.setWidth(40);
        item1.setVisible(false);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridClienteFlagEmpresa.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("EMPRESA");
        item2.setTitleCaption("Empresas Associadas");
        item2.setWidth(40);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("COD_EMPRESA = 0");
        item3.setEvalType("etExpression");
        item3.setImageId(7000213);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridClienteFlagEmpresaSelEmpresaClick(event);
            processarFlow("FrmClientesFlagsServEmp", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridClienteFlagEmpresa.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("VALOR_GRID");
        item4.setTitleCaption("Valor");
        item4.setWidth(120);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridClienteFlagEmpresa.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("VALOR");
        item5.setTitleCaption("Valor");
        item5.setWidth(120);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridClienteFlagEmpresa.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName(" ");
        item6.setWidth(44);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taCenter");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        TFImageExpression item7 = new TFImageExpression();
        item7.setExpression("cod_empresa > 0");
        item7.setEvalType("etExpression");
        item7.setImageId(8);
        item7.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridClienteFlagEmpresaExcluirEmpresaGrid(event);
            processarFlow("FrmClientesFlagsServEmp", "item7", "OnClick");
        });
        item6.getImages().add(item7);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridClienteFlagEmpresa.getColumns().add(item6);
        vboxGrid.addChildren(gridClienteFlagEmpresa);
        gridClienteFlagEmpresa.applyProperties();
    }

    public TFHBox hboxAltera = new TFHBox();

    private void init_hboxAltera() {
        hboxAltera.setName("hboxAltera");
        hboxAltera.setLeft(0);
        hboxAltera.setTop(281);
        hboxAltera.setWidth(655);
        hboxAltera.setHeight(65);
        hboxAltera.setBorderStyle("stNone");
        hboxAltera.setPaddingTop(0);
        hboxAltera.setPaddingLeft(10);
        hboxAltera.setPaddingRight(10);
        hboxAltera.setPaddingBottom(0);
        hboxAltera.setMarginTop(0);
        hboxAltera.setMarginLeft(0);
        hboxAltera.setMarginRight(0);
        hboxAltera.setMarginBottom(0);
        hboxAltera.setSpacing(5);
        hboxAltera.setFlexVflex("ftFalse");
        hboxAltera.setFlexHflex("ftTrue");
        hboxAltera.setScrollable(false);
        hboxAltera.setBoxShadowConfigHorizontalLength(10);
        hboxAltera.setBoxShadowConfigVerticalLength(10);
        hboxAltera.setBoxShadowConfigBlurRadius(5);
        hboxAltera.setBoxShadowConfigSpreadRadius(0);
        hboxAltera.setBoxShadowConfigShadowColor("clBlack");
        hboxAltera.setBoxShadowConfigOpacity(75);
        hboxAltera.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxAltera);
        hboxAltera.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(520);
        FVBox5.setHeight(65);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(5);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        hboxAltera.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel lblNovoValor = new TFLabel();

    private void init_lblNovoValor() {
        lblNovoValor.setName("lblNovoValor");
        lblNovoValor.setLeft(0);
        lblNovoValor.setTop(0);
        lblNovoValor.setWidth(52);
        lblNovoValor.setHeight(13);
        lblNovoValor.setCaption("Novo Valor");
        lblNovoValor.setFontColor("clWindowText");
        lblNovoValor.setFontSize(-11);
        lblNovoValor.setFontName("Tahoma");
        lblNovoValor.setFontStyle("[]");
        lblNovoValor.setVerticalAlignment("taVerticalCenter");
        lblNovoValor.setWordBreak(false);
        FVBox5.addChildren(lblNovoValor);
        lblNovoValor.applyProperties();
    }

    public TFDecimal edtValorDecimal = new TFDecimal();

    private void init_edtValorDecimal() {
        edtValorDecimal.setName("edtValorDecimal");
        edtValorDecimal.setLeft(0);
        edtValorDecimal.setTop(14);
        edtValorDecimal.setWidth(121);
        edtValorDecimal.setHeight(24);
        edtValorDecimal.setTable(tbClienteflagempresa);
        edtValorDecimal.setFieldName("VALOR");
        edtValorDecimal.setFlex(true);
        edtValorDecimal.setRequired(false);
        edtValorDecimal.setConstraintCheckWhen("cwImmediate");
        edtValorDecimal.setConstraintCheckType("ctExpression");
        edtValorDecimal.setConstraintFocusOnError(false);
        edtValorDecimal.setConstraintEnableUI(true);
        edtValorDecimal.setConstraintEnabled(false);
        edtValorDecimal.setConstraintFormCheck(true);
        edtValorDecimal.setMaxlength(0);
        edtValorDecimal.setPrecision(0);
        edtValorDecimal.setFontColor("clWindowText");
        edtValorDecimal.setFontSize(-13);
        edtValorDecimal.setFontName("Tahoma");
        edtValorDecimal.setFontStyle("[]");
        edtValorDecimal.setAlignment("taRightJustify");
        FVBox5.addChildren(edtValorDecimal);
        edtValorDecimal.applyProperties();
        addValidatable(edtValorDecimal);
    }

    public TFCheckBox chkValor = new TFCheckBox();

    private void init_chkValor() {
        chkValor.setName("chkValor");
        chkValor.setLeft(0);
        chkValor.setTop(39);
        chkValor.setWidth(97);
        chkValor.setHeight(17);
        chkValor.setCaption("Ativo?");
        chkValor.setFontColor("clWindowText");
        chkValor.setFontSize(-11);
        chkValor.setFontName("Tahoma");
        chkValor.setFontStyle("[]");
        chkValor.setTable(tbClienteflagempresa);
        chkValor.setFieldName("VALOR");
        chkValor.setCheckedValue("S");
        chkValor.setUncheckedValue("N");
        chkValor.setVerticalAlignment("taAlignTop");
        FVBox5.addChildren(chkValor);
        chkValor.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(520);
        FHBox2.setTop(0);
        FHBox2.setWidth(97);
        FHBox2.setHeight(65);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(5);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hboxAltera.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(0);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(95);
        btnAlterar.setHeight(53);
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmClientesFlagsServEmp", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(0);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);
        FHBox2.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item8 = new TFSchemaItem();
        item8.setTable(tbClienteCpagarIcmsSubEmp);
        sc.getTables().add(item8);
        TFSchemaItem item9 = new TFSchemaItem();
        item9.setTable(tbAcessorioEmp);
        sc.getTables().add(item9);
        TFSchemaItem item10 = new TFSchemaItem();
        item10.setTable(tbIndustriaEmp);
        sc.getTables().add(item10);
        TFSchemaItem item11 = new TFSchemaItem();
        item11.setTable(tbClienteDiverso);
        sc.getTables().add(item11);
        TFSchemaItem item12 = new TFSchemaItem();
        item12.setTable(tbFornecDescontaIcmsstEmp);
        sc.getTables().add(item12);
        TFSchemaItem item13 = new TFSchemaItem();
        item13.setTable(tbClienteIssEmpresa);
        sc.getTables().add(item13);
        TFSchemaItem item14 = new TFSchemaItem();
        item14.setTable(tbClienteIsentoIssEmpresa);
        sc.getTables().add(item14);
        TFSchemaItem item15 = new TFSchemaItem();
        item15.setTable(tbClienteIgnoraPjReterIss);
        sc.getTables().add(item15);
        TFSchemaItem item16 = new TFSchemaItem();
        item16.setTable(tbFornecSubIcmsEmp);
        sc.getTables().add(item16);
        TFSchemaItem item17 = new TFSchemaItem();
        item17.setTable(tbFornecIvaEmp);
        sc.getTables().add(item17);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void gridClienteFlagEmpresaSelEmpresaClick(final Event<Object> event);

    public abstract void gridClienteFlagEmpresaExcluirEmpresaGrid(final Event<Object> event);

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}