package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmMarkupPrincipal extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.MarkupPrincipalRNA rn = null;

    public FrmMarkupPrincipal() {
        try {
            rn = (freedom.bytecode.rn.MarkupPrincipalRNA) getRN(freedom.bytecode.rn.wizard.MarkupPrincipalRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbMarkupModelo();
        init_tbMarkup();
        init_tbMarkupTipo();
        init_tbMarkup1();
        init_tbParmFluxo();
        init_FVBox1();
        init_FHBox1();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnCancelar();
        init_FHBox3();
        init_PageControlMurkup();
        init_tabListagemMarkupModelo();
        init_FVBox2();
        init_FHBox2();
        init_FVBox4();
        init_lfIdMarkup();
        init_efIdMarkup();
        init_FVBox5();
        init_lfDescricao();
        init_efDescricao();
        init_gridPrincipal();
        init_tabCadastroMarkupModelo();
        init_FVBox3();
        init_FGroupbox2();
        init_FVBox9();
        init_FHBox16();
        init_FHBox17();
        init_FHBox18();
        init_FLabel4();
        init_edDescricaoMarkupModelo();
        init_FHBox19();
        init_FHBox20();
        init_FHBox21();
        init_FLabel6();
        init_edtipoCustoMarkupModelo();
        init_tabMarkup();
        init_FVBox6();
        init_GridMarkup();
        init_FHBox11();
        init_btnAlterarMarkup();
        init_btnSalvarMarkup();
        init_btnCancelarMarkup();
        init_FVBox7();
        init_FHBox14();
        init_FHBox27();
        init_FHBox28();
        init_FHBox29();
        init_FLabel5();
        init_edIdmarkupTipoMarkup();
        init_FHBox10();
        init_FHBox12();
        init_FHBox13();
        init_FLabel3();
        init_edTipoComissaoMarkup();
        init_FHBox15();
        init_FHBox4();
        init_FHBox5();
        init_FHBox6();
        init_FLabel1();
        init_edValorFixoMarkup();
        init_FHBox7();
        init_FHBox8();
        init_FHBox9();
        init_FLabel2();
        init_edValorPercentualMarkup();
        init_sc();
        init_FrmMarkupPrincipal();
    }

    public CP_MARKUP_MODELO tbMarkupModelo;

    private void init_tbMarkupModelo() {
        tbMarkupModelo = rn.tbMarkupModelo;
        tbMarkupModelo.setName("tbMarkupModelo");
        tbMarkupModelo.setMaxRowCount(200);
        tbMarkupModelo.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbMarkupModeloAfterScroll(event);
            processarFlow("FrmMarkupPrincipal", "tbMarkupModelo", "OnAfterScroll");
        });
        tbMarkupModelo.setWKey("4600650;46004");
        tbMarkupModelo.setRatioBatchSize(20);
        getTables().put(tbMarkupModelo, "tbMarkupModelo");
        tbMarkupModelo.applyProperties();
    }

    public CP_MARKUP tbMarkup;

    private void init_tbMarkup() {
        tbMarkup = rn.tbMarkup;
        tbMarkup.setName("tbMarkup");
        tbMarkup.setMaxRowCount(200);
        tbMarkup.setWKey("4600650;46002");
        tbMarkup.setRatioBatchSize(20);
        getTables().put(tbMarkup, "tbMarkup");
        tbMarkup.applyProperties();
    }

    public CP_MARKUP_TIPO tbMarkupTipo;

    private void init_tbMarkupTipo() {
        tbMarkupTipo = rn.tbMarkupTipo;
        tbMarkupTipo.setName("tbMarkupTipo");
        tbMarkupTipo.setMaxRowCount(200);
        tbMarkupTipo.setWKey("4600650;46003");
        tbMarkupTipo.setRatioBatchSize(20);
        getTables().put(tbMarkupTipo, "tbMarkupTipo");
        tbMarkupTipo.applyProperties();
    }

    public CP_MARKUP tbMarkup1;

    private void init_tbMarkup1() {
        tbMarkup1 = rn.tbMarkup1;
        tbMarkup1.setName("tbMarkup1");
        tbMarkup1.setMaxRowCount(200);
        tbMarkup1.setWKey("4600650;46005");
        tbMarkup1.setRatioBatchSize(20);
        getTables().put(tbMarkup1, "tbMarkup1");
        tbMarkup1.applyProperties();
    }

    public CRM_PARM_FLUXO tbParmFluxo;

    private void init_tbParmFluxo() {
        tbParmFluxo = rn.tbParmFluxo;
        tbParmFluxo.setName("tbParmFluxo");
        tbParmFluxo.setMaxRowCount(200);
        tbParmFluxo.setWKey("4600650;46007");
        tbParmFluxo.setRatioBatchSize(20);
        getTables().put(tbParmFluxo, "tbParmFluxo");
        tbParmFluxo.applyProperties();
    }

    protected TFForm FrmMarkupPrincipal = this;
    private void init_FrmMarkupPrincipal() {
        FrmMarkupPrincipal.setName("FrmMarkupPrincipal");
        FrmMarkupPrincipal.setCaption("Markup Cadastro");
        FrmMarkupPrincipal.setClientHeight(482);
        FrmMarkupPrincipal.setClientWidth(887);
        FrmMarkupPrincipal.setColor("clBtnFace");
        FrmMarkupPrincipal.setWKey("4600650");
        FrmMarkupPrincipal.setSpacing(0);
        FrmMarkupPrincipal.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(887);
        FVBox1.setHeight(482);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(3);
        FVBox1.setPaddingLeft(3);
        FVBox1.setPaddingRight(3);
        FVBox1.setPaddingBottom(3);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmMarkupPrincipal.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(884);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(0);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(53);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmMarkupPrincipal", "btnNovo", "OnClick");
        });
        btnNovo.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000046A4944415478DA8D967F681B6518C7BFEF5DDAA5AE6E36D58EC2DA4E"
 + "BA5A1D42BBE93A990A56F6873237D81036D0BF1404C76C374BA508ADCC8D29FE"
 + "530A8AEC0FC58242152B4341879B0C6BE956D0DDADBF67D35FA95996D4A5499A"
 + "E4EE92BBBCBEEF2517EFB254FB1CC7BD6FDE7B9FCFF3E37D9E0B414E4856446C"
 + "440420B33FE3C64B78C3B5C5F54AADA7761757E10BFB26F5B8FE1516F1293E86"
 + "62EAB56D7BB4BBBBFB2CA5D4A98C10C74B5C7C311FFACBFBF71E78EE40DDF1C6"
 + "E3B85F2CE79BC89A11C7C0AD017A65ECCA14D238882E2CD9F736AFAE46244555"
 + "210884E63CE29ED9C750D20A7DFEA75652595B490F3DFC027119693CE56E3501"
 + "D7D5ABD0C512FAC3C22532228F4C62062D0E402412BDA16A1C20C05268BFB9F4"
 + "C97DF820781E2F3F7E18D56E116E4DC3B18A77CDB5AF57CF43DDB40901D5C0B7"
 + "13DF43FB45EB7200A2D198A4A534D36AA18807462683677F7C86CE5578C9139B"
 + "EBE80EB748B6A674BC5EF3396571249FF95E43B4D445175583FC9158827A4D1B"
 + "7300626B6B528A594404A16888946412DB2F6DA7C6E634E9F3F4D23D35BB89C8"
 + "343C56DD6486683A70130605BDB12C91F6F069646248D9014D6B1C904EE74322"
 + "1484281A8DA26EA80E99521D7D15BDD85DD30C0ED855DD6C2A980AC81C006959"
 + "46FB2A03A8D01D80783C2E738060599DCD45DE0366009EFE6D3F9D77CF916DCC"
 + "D29DF7816CCB005D0D574D0F3E9C6D455000F52641824C339DC6AC139048C869"
 + "0B209859F817C6C67CAD73B8935E485F20656CFF8E7206601A3E7A240B78E74F"
 + "0660E0C5388859045FE0130720914848BAAE6743C2AC178A9C22694AC2D1DB47"
 + "11D00228D3010F2BBAF71A7BCDB533B74E23CC3C525C6C12C66D9CC461072099"
 + "4CCA268025397F8A6C632EAAAAD2C1E141D22576D1A01A2424068869981E1825"
 + "CCAD2D6C1C43109DECBA834127405124230BC85B6FD544AE9D98CF582C86A1D1"
 + "21F427FA315A368A10BBB854B16B9FB20F175FBD78121ABEE4AF3A008AA2C886"
 + "6138AC66009A6B5516C49CA75229EAF7FBC9FCFC3CC2E1B0F99BC7E3417D7D3D"
 + "6D686868612DE7F7C25ED4C4DA849CE1005E68B9245BA7C8E6813537956658F1"
 + "B1DB1C8BA2681AE472B9F6B0DFE47B002CBE32AF56C106B014167A603D7973A4"
 + "59C91921D0D2D29275009A26710F0A7B91657D612E7838F9A160E132C75C4457"
 + "092A1ED85A1CA0699ACC5D261BF08029A6CC63C2AB3B1289507640080B0D8647"
 + "AE5D7FBBBDED0803DE591720D87A911D60CDD966C20E040D8556CC24B339ADAA"
 + "7A884C4C4ECCB4BDD5768841BDF9CF490140CAB098162B30FB98BD87BB77C3F0"
 + "7ABDA87CB0123BEBEB71F9F2CFCB6F9E38F1A2FF2FFF944DE7BD1EF0A4157C64"
 + "1CA78867931524F12D2FD3E9E919D2B2F7498C8F8FF9DB4F9D3AE89DF5DE4481"
 + "3801A9944C6D39B0626E07F023C99A22999B9BA7C15088A84A62AEA3A3E3C8C2"
 + "C2E2388A482140CA01D60D11CF11036061C987A1A15FE573EF9F39B6B2F2F72C"
 + "D6113BA0B1A7A7E75C7E8190A21B780853BA81403014FDEE9B81B3AC852FE13F"
 + "84148C37F6B725C762B7F17F2FFD034C50719467FC49DB0000000049454E44AE"
 + "426082");
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(65);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(53);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmMarkupPrincipal", "btnAlterar", "OnClick");
        });
        btnAlterar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000005094944415478DAAD956B6C536518C79FE7F4D0DDC82ED95A262BB0AC"
 + "026EC16D201035121308514CCC844020881335442E11818D6BD84C4087CB3E90"
 + "4C037E21A21F34442111A209E3E218868B894047071B3BDD4AB76EACDDADD773"
 + "DAD39EC7F7F4E65A36C2079FE4DCCFFBFF3DB7F77D11FE476B5DB76259B63FFD"
 + "3CA728D7291CDEB9B0A5C58AF18F1A8D465F5757F70511258F42047C0EF190B7"
 + "1B372F685B1FFAAD38CF67B2A35796074196DF9838D63836362E8892041C8784"
 + "5163FA49F72A8C20F214BDAA03C3012729E6B53830E8849AC634DA6A1BC5E93E"
 + "1F8478FE4212607CDCD52D0554000709C189473CA2A86EE459098B106CDF00A2"
 + "DB0A1F7CA5815E7B0066F276D89F0964EAC4F3490097CB2D04828188D7DC2411"
 + "40FC59D56657A0308AE6ADA011EFD2278D1C9A1E89A048763ABD5F0E8B226F5A"
 + "7B48AE4A02B83D1E211860008E9B3C459100309122FFA32398215E81DA131C5D"
 + "BA3982A2D701273EF75361BEC6B6FA90BC667084BA26024A3C2A40961329E126"
 + "4953BCE852DF0F90E6FA198EFF9A09A7CF09E0F7B9E068F528AC7CAD0056EE74"
 + "7CD865A38BECB7B12480D7EBB5A8002EEE75B4164F1539E8BC82BCE35B3A7B43"
 + "87479A6F422020D1F6779EE0A6B73361A4A0892A977DBA9CE9FDC30E6F32C0E7"
 + "B3C8710017A9C27FB01820E4BE4FD0DF88B78599B4A3BE05032CA5554B07A876"
 + "830687B26A000B56D3A2458B5E657A77D4EE4D02F87C3E21140A453D65DEA7A6"
 + "88151094C70D2038F2E1A37D17C1EDF6C2EBF39CD0B04586E1B48DE09E5E0D85"
 + "8585307FFEFC054CEF41BCD31200BFDF6F89005891135D14BB57641784ACC760"
 + "D89341D57BAFAA3D4F65063736EFF0823F633959B9EDA88ACF993387F47AFD3C"
 + "455184A701A22884A38084F7EA9C002508416B13484184CDFB5BA14BB083215F"
 + "84933B4781CF29877BFE5DA0D3BF002525259108D2D3D3E73280E52980288A96"
 + "70389C1C010205ACC7D93904DBEBAFC1AD3B56CA9B2EE3C9CF86495F68C06BCE"
 + "5D9057308B8C4623161717435A5A1AF13C3F0540922C8A0A50275AACC801DB77"
 + "C4830B0F345E873F5A7B2143AB50F3362796CDCDA1DFADDB3033D7A87A4EA5A5"
 + "A5989595A5EA4C0D901820AC2891CE510192FD0C68C55BD474CA8C3F9E1F60EF"
 + "098E7D3C4ACB17F378AEB39A28A31459CEA1A2A282727373631173A4D54E9B02"
 + "1008086A04F1B568C0FC13FCD9F23D349C1A501D83DAB52E787F6518CE9AAB60"
 + "9C2AA1A8A8082A2B2B41A7D301138CAECAFC34C8CBCD991CC07ADAA2FEA8A6C8"
 + "EF7A8243D656103AFFA6A3CD5771D52B63B06F830417CC6F92E05EAA760C9597"
 + "97A34EAF07AFC743AC4190A506FEBA71B375EF9EDDEF058341D7940016010D0A"
 + "6DE8B0B68067E401897E172E9CD5079DC3F3C034B28A66CF9E8D068381B2B3B3"
 + "B1BFDF0EAC31586BEAB0A3C3DC5E5353BBCAE9740E24B6931480A0B00D476DD1"
 + "53DF1C80B2223658F683A29D0B9A9C25A037BC0CFA193340ABD5B249E6819E9E"
 + "1EC82FC887178D46B87CF99275F79E3D2B846EA13769BF4A8D40DDD11E5BADB4"
 + "7ACD1ADCBC7E196CDCB4857267BC84ACFDD45D0FD8776213126D7D7DF4F06127"
 + "2E5DB218CCE6FBB683070FBE6532B577A6EE74C98060D0422C45A67B7749C3F3"
 + "5851519958ECA27B0DAAC524B628A2C5D243430E074A92BFABEEF0E1773B3A1E"
 + "74C324960A10285AE4E4253A261E03000340EF631BB4B55DBBDDD4F8F53A5687"
 + "7E98C226028AEAEBEBBF8CBF8B0BA79A9AC260280C83438EF173BF9C69F0B8DD"
 + "0E7886A5AA70F0FC46B1E399F62F6A8BA82D8608FAC10000000049454E44AE42"
 + "6082");
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(130);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(53);
        btnExcluir.setHint("Excluir  (CRTL+ 5)");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmMarkupPrincipal", "btnExcluir", "OnClick");
        });
        btnExcluir.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000016D4944415478DAEDD6CD2B055118C7F173950D29B150364A62435E36"
 + "0A795F58B3C142FC0752B2F2BA932C6D2C9562C15E79B90965E3A56CA49494A2"
 + "48E9DAA8E1FB34CFD4699A193323BBFBD4A7B973CF39F7377316CFB919F3CF95"
 + "8939AF09D5A8D2FB07DCE3EAAF01FD58464BC8F80566B09F26A00307B8C50A4E"
 + "F1AC631568C7346AD1A7E38902F650A3DBF31132A744B7E90E0351013D984581"
 + "35D686577D83A8AA4339CEACEF1CCCE1C40B90ED5842191A718D7793AC4AADB5"
 + "6FFE00AFBA71A46F944D1810B8F6B700B98E6142C727F18535BD5FC7863C69DA"
 + "8005CC5BF3B2D63CA96F2CEABC7C403E201F1033403AE72586B06BDC7E228BBD"
 + "2678A8D75EBD3A3A2E7D6C103B6836D641E40F901F7A316EC392F65B892EE3B6"
 + "0329698A85FAA452E338C6A371DB7B8371CF0A272C406A049BC6ED2FB23DE7F8"
 + "34C1558456DD26091FC5963D21ECC019C6AABE419C7AC214B6FD0351275A313A"
 + "51AF9F832A871BDDA65CD084B8FF2A52D70F1A0B8E192C1E0DF5000000004945"
 + "4E44AE426082");
        btnExcluir.setImageId(4600235);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(195);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmMarkupPrincipal", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934"
 + "31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2"
 + "D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE"
 + "6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5"
 + "6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013"
 + "090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF"
 + "349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E"
 + "8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871"
 + "175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39"
 + "FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622"
 + "F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673"
 + "BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E"
 + "01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146"
 + "0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219"
 + "506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6"
 + "0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991"
 + "CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313"
 + "9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0"
 + "39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D"
 + "AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7"
 + "C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743"
 + "FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE"
 + "E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660"
 + "474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF"
 + "D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61"
 + "33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E"
 + "0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE"
 + "F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF"
 + "921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5"
 + "A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E"
 + "9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163"
 + "8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09"
 + "C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C"
 + "D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E"
 + "23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA"
 + "FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E"
 + "9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1"
 + "5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A"
 + "25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2"
 + "FF02B065C443D9FE4B070000000049454E44AE426082");
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(260);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(53);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmMarkupPrincipal", "btnCancelar", "OnClick");
        });
        btnCancelar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000004EF4944415478DAAD566B4C1C55143E7766F6BD145020744BA98F1608"
 + "A820DA508BB42A84B62A264D6A24D158E323C646230D4D7D24901AA8466BD336"
 + "1A9AF84B1B7F580BD49ADA6A0B518A3450915D0BE9F2461E5BDEEC93DD9DDD9D"
 + "39DE997D53B49A7893D9993B73EFF9CEF79DC75D02A141828385FF6120A2402F"
 + "94EDC6BCCFA9A9A9A90BBD8F0E42E216DD6E5027A1BEBEBE4614C5FE95000556"
 + "ABCDE8F17A8161088618491B629FA50D28D30DDD4346306C4BA150A05EAF2FA4"
 + "00A65B006C367B8F9797001888188CBDC28C827623F358431CC7814EA75B1DC0"
 + "6E7718791F2F7BCDACC200C273695FF81E048930E05816B55AEDEA000EA7D3E8"
 + "E32900C3AC2E5150E38844244C254622960268349A28C08F8D1FAA76EE799FA7"
 + "4BF31D0E0AE0F7CB12D8E60660B4F7023817478165782A9B0F44810351544342"
 + "F206D8545801A9190510C5080E495EB55A1D05B8D6F2F96C47EB570D750DC673"
 + "13169B716176047A5A8E6362A28BE43D940177A45136E00AC551DAA143EB0221"
 + "E6EE29B42D2A49D1930721393D3BC24662A852A9A200674F96FAD3D6A6B2478E"
 + "5F3A7DF8D32F2BCD9D0DB07D470E26ACF1DC427FC51CDDCB6AD27E6100EECADF"
 + "8BB95BF64400944A6514A0E9B392C0CE8A3CE6BAC9014EA7171E29310043FCE1"
 + "8AF91799CFC1CF1747213DFB79C82FA994258B03683C511C28DF95CBCA9910AD"
 + "29D94BBFC30A9EA9110CCCCF11F00BC0A8B4A04C31A026732361944C8419028B"
 + "DF37F79392678F41C6BD05486B210A70E6D816A1AC3C8F50B783CB25B55104E7"
 + "C4EFE0991905AF4B8489291A0587086A4E80BB937D90A055803E771BA80DEBE9"
 + "6251F6C8ED0538FBC322BCFEC177F10CBE3DBA5978A22C9701963A42931FA863"
 + "CE9B9DE85F9E2137CC2C589C45985F5C49925232C0BE340B3D6D67F04EFB4FE4"
 + "E1756ED46FDA4A546B374418B75D9D82ACC76AF181A21D51806F3E29141F2FCD"
 + "22C0D17CE780F83C13E075F6A1CB4DC8B5EE62D8FB6E13320C1B0EA06CACFD7C"
 + "0359F8E53D2C48779384FBCA81516BE5A2732C0BD03164C0970E7E1105387528"
 + "4B2C2DA39A6A54489454204E04419CC280384EFABA11F4490770EBD307223545"
 + "42557BA5E9301AACCD44AB53D1B8645255790262004EB75A71FF89AE2840C3FE"
 + "F4E5CD391E0D9BB006B83549C0E913A847B460540140CE02FD9D023CB8FD2418"
 + "EEA9801810989BE8034BFB1148532D8222391330E00614BC70F1AA055EFDA8A3"
 + "90266010E0D02BEB9EE179B1C43CEE4D7BF9B5B75E945293EEA7B210228544AD"
 + "51D2185490C494BC38068EA5691C683D4A52D91910153A14033C61580E4EB5CC"
 + "77D6379CDF2D8A3813D78B6885E77B3CBC89520B363BB9DB31B2E6B1ED5A9A0B"
 + "8240E66F8EE0E46F5F9380CB029C3A1193330A48D790A7FFCDAA772A68D31C8E"
 + "9C0F311593CFF3BC51A469C7D0A4908A82A51EC5CA127EA6EB60F07A1B4DE50E"
 + "48DBB80DD6E73C0A972F5F9A7C63DFBE5D9629CB8DB80368058049AA01EA238A"
 + "4280704A0D65C6C6F619E938C4C5B949326EFE1597A607C9FD252F80B177D0F2"
 + "7655D553C343C37FACACF378009FCF442D53003F9548411562E32492E2E3E3DD"
 + "689D1F23B39366B4D9DDC4EA4B1AA9AEAEDE3D36F667EF6A8D64258011E51848"
 + "DE32D1534C960C2566B465FBC1659F86C9F161B8D26536D5D57DFCDCFCFCC2D0"
 + "DF75AA5880ECDADADAFAC887D8B3048310F22F95D0E7F3C0CCDC9CBDB9F15C9D"
 + "D3E91AFFA75648563CFF97BF2D12A270BB457F0173456D3788187BC400000000"
 + "49454E44AE426082");
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(325);
        FHBox3.setTop(0);
        FHBox3.setWidth(26);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setVisible(false);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFPageControl PageControlMurkup = new TFPageControl();

    private void init_PageControlMurkup() {
        PageControlMurkup.setName("PageControlMurkup");
        PageControlMurkup.setLeft(0);
        PageControlMurkup.setTop(61);
        PageControlMurkup.setWidth(883);
        PageControlMurkup.setHeight(409);
        PageControlMurkup.setAlign("alClient");
        PageControlMurkup.setTabPosition("tpTop");
        PageControlMurkup.setFlexVflex("ftTrue");
        PageControlMurkup.setFlexHflex("ftTrue");
        PageControlMurkup.setRenderStyle("rsTabbed");
        PageControlMurkup.applyProperties();
        FVBox1.addChildren(PageControlMurkup);
    }

    public TFTabsheet tabListagemMarkupModelo = new TFTabsheet();

    private void init_tabListagemMarkupModelo() {
        tabListagemMarkupModelo.setName("tabListagemMarkupModelo");
        tabListagemMarkupModelo.setCaption("Listagem");
        tabListagemMarkupModelo.setClosable(false);
        PageControlMurkup.addChildren(tabListagemMarkupModelo);
        tabListagemMarkupModelo.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(877);
        FVBox2.setHeight(398);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(5);
        FVBox2.setPaddingLeft(5);
        FVBox2.setPaddingRight(5);
        FVBox2.setPaddingBottom(5);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(0);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabListagemMarkupModelo.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(713);
        FHBox2.setHeight(56);
        FHBox2.setAlign("alClient");
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(10);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox2.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(86);
        FVBox4.setHeight(50);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel lfIdMarkup = new TFLabel();

    private void init_lfIdMarkup() {
        lfIdMarkup.setName("lfIdMarkup");
        lfIdMarkup.setLeft(0);
        lfIdMarkup.setTop(0);
        lfIdMarkup.setWidth(52);
        lfIdMarkup.setHeight(13);
        lfIdMarkup.setAlign("alLeft");
        lfIdMarkup.setCaption("Id. Markup");
        lfIdMarkup.setFontColor("clWindowText");
        lfIdMarkup.setFontSize(-11);
        lfIdMarkup.setFontName("Tahoma");
        lfIdMarkup.setFontStyle("[]");
        lfIdMarkup.setVerticalAlignment("taAlignBottom");
        lfIdMarkup.setWordBreak(false);
        FVBox4.addChildren(lfIdMarkup);
        lfIdMarkup.applyProperties();
    }

    public TFInteger efIdMarkup = new TFInteger();

    private void init_efIdMarkup() {
        efIdMarkup.setName("efIdMarkup");
        efIdMarkup.setLeft(0);
        efIdMarkup.setTop(14);
        efIdMarkup.setWidth(76);
        efIdMarkup.setHeight(24);
        efIdMarkup.setHint("Filtra pelo Id. markup sequence");
        efIdMarkup.setFlex(false);
        efIdMarkup.setRequired(false);
        efIdMarkup.setConstraintCheckWhen("cwImmediate");
        efIdMarkup.setConstraintCheckType("ctExpression");
        efIdMarkup.setConstraintFocusOnError(false);
        efIdMarkup.setConstraintEnableUI(true);
        efIdMarkup.setConstraintEnabled(false);
        efIdMarkup.setConstraintFormCheck(true);
        efIdMarkup.setMaxlength(0);
        efIdMarkup.setAlign("alLeft");
        efIdMarkup.setFontColor("clWindowText");
        efIdMarkup.setFontSize(-13);
        efIdMarkup.setFontName("Tahoma");
        efIdMarkup.setFontStyle("[]");
        efIdMarkup.setAlignment("taRightJustify");
        efIdMarkup.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            filtrarGrid(event);
            processarFlow("FrmMarkupPrincipal", "efIdMarkup", "OnEnter");
        });
        FVBox4.addChildren(efIdMarkup);
        efIdMarkup.applyProperties();
        addValidatable(efIdMarkup);
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(86);
        FVBox5.setTop(0);
        FVBox5.setWidth(513);
        FVBox5.setHeight(50);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel lfDescricao = new TFLabel();

    private void init_lfDescricao() {
        lfDescricao.setName("lfDescricao");
        lfDescricao.setLeft(0);
        lfDescricao.setTop(0);
        lfDescricao.setWidth(46);
        lfDescricao.setHeight(13);
        lfDescricao.setAlign("alLeft");
        lfDescricao.setCaption("Descri\u00E7\u00E3o");
        lfDescricao.setFontColor("clWindowText");
        lfDescricao.setFontSize(-11);
        lfDescricao.setFontName("Tahoma");
        lfDescricao.setFontStyle("[]");
        lfDescricao.setVerticalAlignment("taAlignBottom");
        lfDescricao.setWordBreak(false);
        FVBox5.addChildren(lfDescricao);
        lfDescricao.applyProperties();
    }

    public TFString efDescricao = new TFString();

    private void init_efDescricao() {
        efDescricao.setName("efDescricao");
        efDescricao.setLeft(0);
        efDescricao.setTop(14);
        efDescricao.setWidth(500);
        efDescricao.setHeight(24);
        efDescricao.setHint("Filtra pelo Descri\u00E7\u00E3o do modelo de markup");
        efDescricao.setFlex(false);
        efDescricao.setRequired(false);
        efDescricao.setConstraintCheckWhen("cwImmediate");
        efDescricao.setConstraintCheckType("ctExpression");
        efDescricao.setConstraintFocusOnError(false);
        efDescricao.setConstraintEnableUI(true);
        efDescricao.setConstraintEnabled(false);
        efDescricao.setConstraintFormCheck(true);
        efDescricao.setCharCase("ccNormal");
        efDescricao.setPwd(false);
        efDescricao.setMaxlength(0);
        efDescricao.setAlign("alLeft");
        efDescricao.setFontColor("clWindowText");
        efDescricao.setFontSize(-13);
        efDescricao.setFontName("Tahoma");
        efDescricao.setFontStyle("[]");
        efDescricao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            filtrarGrid(event);
            processarFlow("FrmMarkupPrincipal", "efDescricao", "OnEnter");
        });
        efDescricao.setSaveLiteralCharacter(false);
        efDescricao.applyProperties();
        FVBox5.addChildren(efDescricao);
        addValidatable(efDescricao);
    }

    public TFGrid gridPrincipal = new TFGrid();

    private void init_gridPrincipal() {
        gridPrincipal.setName("gridPrincipal");
        gridPrincipal.setLeft(0);
        gridPrincipal.setTop(57);
        gridPrincipal.setWidth(710);
        gridPrincipal.setHeight(120);
        gridPrincipal.setTable(tbMarkupModelo);
        gridPrincipal.setFlexVflex("ftTrue");
        gridPrincipal.setFlexHflex("ftTrue");
        gridPrincipal.setPagingEnabled(false);
        gridPrincipal.setFrozenColumns(0);
        gridPrincipal.setShowFooter(false);
        gridPrincipal.setShowHeader(true);
        gridPrincipal.setMultiSelection(false);
        gridPrincipal.setGroupingEnabled(false);
        gridPrincipal.setGroupingExpanded(false);
        gridPrincipal.setGroupingShowFooter(false);
        gridPrincipal.setCrosstabEnabled(false);
        gridPrincipal.setCrosstabGroupType("cgtConcat");
        gridPrincipal.setEditionEnabled(false);
        gridPrincipal.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_MARKUP");
        item0.setTitleCaption("Id. Markup");
        item0.setWidth(105);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(480);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item1);
        FVBox2.addChildren(gridPrincipal);
        gridPrincipal.applyProperties();
    }

    public TFTabsheet tabCadastroMarkupModelo = new TFTabsheet();

    private void init_tabCadastroMarkupModelo() {
        tabCadastroMarkupModelo.setName("tabCadastroMarkupModelo");
        tabCadastroMarkupModelo.setCaption("Modelo");
        tabCadastroMarkupModelo.setClosable(false);
        PageControlMurkup.addChildren(tabCadastroMarkupModelo);
        tabCadastroMarkupModelo.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(875);
        FVBox3.setHeight(381);
        FVBox3.setAlign("alClient");
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(5);
        FVBox3.setPaddingLeft(5);
        FVBox3.setPaddingRight(5);
        FVBox3.setPaddingBottom(5);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(5);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        tabCadastroMarkupModelo.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFGroupbox FGroupbox2 = new TFGroupbox();

    private void init_FGroupbox2() {
        FGroupbox2.setName("FGroupbox2");
        FGroupbox2.setLeft(0);
        FGroupbox2.setTop(0);
        FGroupbox2.setWidth(772);
        FGroupbox2.setHeight(109);
        FGroupbox2.setCaption("Markup Modelo");
        FGroupbox2.setFontColor("clWindowText");
        FGroupbox2.setFontSize(-11);
        FGroupbox2.setFontName("Tahoma");
        FGroupbox2.setFontStyle("[]");
        FGroupbox2.setFlexVflex("ftMin");
        FGroupbox2.setFlexHflex("ftTrue");
        FGroupbox2.setScrollable(false);
        FGroupbox2.setClosable(false);
        FGroupbox2.setClosed(false);
        FGroupbox2.setOrient("coHorizontal");
        FGroupbox2.setStyle("grp3D");
        FGroupbox2.setHeaderImageId(0);
        FVBox3.addChildren(FGroupbox2);
        FGroupbox2.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(2);
        FVBox9.setTop(15);
        FVBox9.setWidth(768);
        FVBox9.setHeight(92);
        FVBox9.setAlign("alClient");
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(5);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftTrue");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FGroupbox2.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(0);
        FHBox16.setWidth(665);
        FHBox16.setHeight(41);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(5);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftTrue");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FVBox9.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(0);
        FHBox17.setWidth(118);
        FHBox17.setHeight(37);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(5);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftFalse");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FHBox16.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(0);
        FHBox18.setWidth(9);
        FHBox18.setHeight(31);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftTrue");
        FHBox18.setFlexHflex("ftTrue");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        FHBox17.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(9);
        FLabel4.setTop(0);
        FLabel4.setWidth(46);
        FLabel4.setHeight(13);
        FLabel4.setCaption("Descri\u00E7\u00E3o");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FHBox17.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFString edDescricaoMarkupModelo = new TFString();

    private void init_edDescricaoMarkupModelo() {
        edDescricaoMarkupModelo.setName("edDescricaoMarkupModelo");
        edDescricaoMarkupModelo.setLeft(118);
        edDescricaoMarkupModelo.setTop(0);
        edDescricaoMarkupModelo.setWidth(461);
        edDescricaoMarkupModelo.setHeight(24);
        edDescricaoMarkupModelo.setHint("Descri\u00E7\u00E3o do modelo de markup");
        edDescricaoMarkupModelo.setTable(tbMarkupModelo);
        edDescricaoMarkupModelo.setFieldName("DESCRICAO");
        edDescricaoMarkupModelo.setFlex(true);
        edDescricaoMarkupModelo.setRequired(false);
        edDescricaoMarkupModelo.setConstraintCheckWhen("cwImmediate");
        edDescricaoMarkupModelo.setConstraintCheckType("ctExpression");
        edDescricaoMarkupModelo.setConstraintFocusOnError(false);
        edDescricaoMarkupModelo.setConstraintEnableUI(true);
        edDescricaoMarkupModelo.setConstraintEnabled(false);
        edDescricaoMarkupModelo.setConstraintFormCheck(true);
        edDescricaoMarkupModelo.setCharCase("ccNormal");
        edDescricaoMarkupModelo.setPwd(false);
        edDescricaoMarkupModelo.setMaxlength(0);
        edDescricaoMarkupModelo.setFontColor("clWindowText");
        edDescricaoMarkupModelo.setFontSize(-13);
        edDescricaoMarkupModelo.setFontName("Tahoma");
        edDescricaoMarkupModelo.setFontStyle("[]");
        edDescricaoMarkupModelo.setSaveLiteralCharacter(false);
        edDescricaoMarkupModelo.applyProperties();
        FHBox16.addChildren(edDescricaoMarkupModelo);
        addValidatable(edDescricaoMarkupModelo);
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(42);
        FHBox19.setWidth(665);
        FHBox19.setHeight(41);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(5);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftTrue");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FVBox9.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(0);
        FHBox20.setTop(0);
        FHBox20.setWidth(118);
        FHBox20.setHeight(37);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(5);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftFalse");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FHBox19.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(0);
        FHBox21.setWidth(9);
        FHBox21.setHeight(31);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftTrue");
        FHBox21.setFlexHflex("ftTrue");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        FHBox20.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(9);
        FLabel6.setTop(0);
        FLabel6.setWidth(51);
        FLabel6.setHeight(13);
        FLabel6.setCaption("Tipo Custo");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox20.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFCombo edtipoCustoMarkupModelo = new TFCombo();

    private void init_edtipoCustoMarkupModelo() {
        edtipoCustoMarkupModelo.setName("edtipoCustoMarkupModelo");
        edtipoCustoMarkupModelo.setLeft(118);
        edtipoCustoMarkupModelo.setTop(0);
        edtipoCustoMarkupModelo.setWidth(199);
        edtipoCustoMarkupModelo.setHeight(21);
        edtipoCustoMarkupModelo.setHint("Tipo custo.  Op\u00E7\u00F5es: Contabil=C; Forncedor=F");
        edtipoCustoMarkupModelo.setTable(tbMarkupModelo);
        edtipoCustoMarkupModelo.setFieldName("TIPO_CUSTO");
        edtipoCustoMarkupModelo.setFlex(false);
        edtipoCustoMarkupModelo.setListOptions("Contabil=C; Forncedor=F");
        edtipoCustoMarkupModelo.setReadOnly(true);
        edtipoCustoMarkupModelo.setRequired(false);
        edtipoCustoMarkupModelo.setPrompt("Selecione");
        edtipoCustoMarkupModelo.setConstraintCheckWhen("cwImmediate");
        edtipoCustoMarkupModelo.setConstraintCheckType("ctExpression");
        edtipoCustoMarkupModelo.setConstraintFocusOnError(false);
        edtipoCustoMarkupModelo.setConstraintEnableUI(true);
        edtipoCustoMarkupModelo.setConstraintEnabled(false);
        edtipoCustoMarkupModelo.setConstraintFormCheck(true);
        edtipoCustoMarkupModelo.setClearOnDelKey(false);
        edtipoCustoMarkupModelo.setUseClearButton(false);
        edtipoCustoMarkupModelo.setHideClearButtonOnNullValue(false);
        FHBox19.addChildren(edtipoCustoMarkupModelo);
        edtipoCustoMarkupModelo.applyProperties();
        addValidatable(edtipoCustoMarkupModelo);
    }

    public TFTabsheet tabMarkup = new TFTabsheet();

    private void init_tabMarkup() {
        tabMarkup.setName("tabMarkup");
        tabMarkup.setCaption("Markup");
        tabMarkup.setClosable(false);
        PageControlMurkup.addChildren(tabMarkup);
        tabMarkup.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(875);
        FVBox6.setHeight(381);
        FVBox6.setAlign("alClient");
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(5);
        FVBox6.setPaddingLeft(5);
        FVBox6.setPaddingRight(5);
        FVBox6.setPaddingBottom(5);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(5);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        tabMarkup.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFGrid GridMarkup = new TFGrid();

    private void init_GridMarkup() {
        GridMarkup.setName("GridMarkup");
        GridMarkup.setLeft(0);
        GridMarkup.setTop(0);
        GridMarkup.setWidth(773);
        GridMarkup.setHeight(81);
        GridMarkup.setTable(tbMarkup);
        GridMarkup.setFlexVflex("ftTrue");
        GridMarkup.setFlexHflex("ftTrue");
        GridMarkup.setPagingEnabled(false);
        GridMarkup.setFrozenColumns(0);
        GridMarkup.setShowFooter(false);
        GridMarkup.setShowHeader(true);
        GridMarkup.setMultiSelection(false);
        GridMarkup.setGroupingEnabled(false);
        GridMarkup.setGroupingExpanded(false);
        GridMarkup.setGroupingShowFooter(false);
        GridMarkup.setCrosstabEnabled(false);
        GridMarkup.setCrosstabGroupType("cgtConcat");
        GridMarkup.setEditionEnabled(false);
        GridMarkup.setNoBorder(false);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("ID_MARKUP_TIPO");
        item2.setTitleCaption("Descri\u00E7\u00E3o item");
        item2.setWidth(210);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorLookupDesc("DESCRICAO");
        item2.setEditorLookupKey("ID_MARKUP_TIPO");
        item2.setEditorLookupTable(tbMarkupTipo);
        item2.setEditorEditType("etTFCombo");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        GridMarkup.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("VALOR_FIXO");
        item3.setTitleCaption("Valor Fixo");
        item3.setWidth(120);
        item3.setVisible(true);
        item3.setPrecision(2);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftDecimal");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("R$ ,##0.00");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDecimal");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFDecimal");
        item3.setEditorPrecision(2);
        item3.setEditorMask("R$ ,##0.00");
        item3.setEditorMaxLength(10);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(true);
        item3.setEditorReadOnly(false);
        item3.setHiperLink(false);
        item3.setHint("Valor numerico");
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        GridMarkup.getColumns().add(item3);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("VALOR_PERCENTUAL");
        item5.setTitleCaption("Valor Percentual");
        item5.setWidth(169);
        item5.setVisible(true);
        item5.setPrecision(2);
        item5.setTextAlign("taRight");
        item5.setFieldType("ftDecimal");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFMaskExpression item6 = new TFMaskExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setMask("##0,00%");
        item6.setPadLength(0);
        item6.setPadDirection("pdNone");
        item6.setMaskType("mtDecimal");
        item5.getMasks().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFDecimal");
        item5.setEditorPrecision(2);
        item5.setEditorMask("##0,00%");
        item5.setEditorMaxLength(4);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(true);
        item5.setEditorReadOnly(false);
        item5.setHiperLink(false);
        item5.setHint("Valor percentual.");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        GridMarkup.getColumns().add(item5);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("TIPO_COMISSAO");
        item7.setTitleCaption("Tipo Comiss\u00E3o");
        item7.setWidth(164);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftCombo");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorLookupListOptions("Sobre o lucro=L; Sobre a venda=V");
        item7.setEditorEditType("etTFCombo");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(1);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(true);
        item7.setEditorReadOnly(false);
        item7.setListOptions("Sobre o lucro=L; Sobre a venda=V");
        item7.setHiperLink(false);
        item7.setHint("Tipo comiss\u00E3o, Sobre o Lucro .  Op\u00E7\u00F5es: Sobre o lucro=L; Sobre a venda=V");
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        GridMarkup.getColumns().add(item7);
        FVBox6.addChildren(GridMarkup);
        GridMarkup.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(82);
        FHBox11.setWidth(438);
        FHBox11.setHeight(41);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox6.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnAlterarMarkup = new TFButton();

    private void init_btnAlterarMarkup() {
        btnAlterarMarkup.setName("btnAlterarMarkup");
        btnAlterarMarkup.setLeft(0);
        btnAlterarMarkup.setTop(0);
        btnAlterarMarkup.setWidth(48);
        btnAlterarMarkup.setHeight(35);
        btnAlterarMarkup.setHint("Alterar Item");
        btnAlterarMarkup.setFontColor("clWindowText");
        btnAlterarMarkup.setFontSize(-11);
        btnAlterarMarkup.setFontName("Tahoma");
        btnAlterarMarkup.setFontStyle("[]");
        btnAlterarMarkup.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarMarkupClick(event);
            processarFlow("FrmMarkupPrincipal", "btnAlterarMarkup", "OnClick");
        });
        btnAlterarMarkup.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA"
 + "5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31"
 + "ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E"
 + "C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E"
 + "86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD"
 + "2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74"
 + "EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2"
 + "30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7"
 + "38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584"
 + "AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979"
 + "0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875"
 + "08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80"
 + "6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618"
 + "713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524"
 + "87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5"
 + "B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045"
 + "4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36"
 + "5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A"
 + "7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5"
 + "42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF"
 + "984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102"
 + "DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B"
 + "1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2"
 + "DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E"
 + "166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42"
 + "6082");
        btnAlterarMarkup.setImageId(7);
        btnAlterarMarkup.setColor("clBtnFace");
        btnAlterarMarkup.setAccess(false);
        btnAlterarMarkup.setIconReverseDirection(false);
        FHBox11.addChildren(btnAlterarMarkup);
        btnAlterarMarkup.applyProperties();
    }

    public TFButton btnSalvarMarkup = new TFButton();

    private void init_btnSalvarMarkup() {
        btnSalvarMarkup.setName("btnSalvarMarkup");
        btnSalvarMarkup.setLeft(48);
        btnSalvarMarkup.setTop(0);
        btnSalvarMarkup.setWidth(48);
        btnSalvarMarkup.setHeight(35);
        btnSalvarMarkup.setHint("Salvar Item");
        btnSalvarMarkup.setFontColor("clWindowText");
        btnSalvarMarkup.setFontSize(-11);
        btnSalvarMarkup.setFontName("Tahoma");
        btnSalvarMarkup.setFontStyle("[]");
        btnSalvarMarkup.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarMarkupClick(event);
            processarFlow("FrmMarkupPrincipal", "btnSalvarMarkup", "OnClick");
        });
        btnSalvarMarkup.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3"
 + "0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220"
 + "AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950"
 + "4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5"
 + "5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37"
 + "35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067"
 + "603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7"
 + "29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B"
 + "DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA"
 + "B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B"
 + "A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194"
 + "B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002"
 + "EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715"
 + "87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B"
 + "AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9"
 + "32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66"
 + "5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E"
 + "4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05"
 + "05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF"
 + "0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76"
 + "093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0"
 + "C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65"
 + "C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70"
 + "9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D"
 + "80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12"
 + "B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8"
 + "6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087"
 + "94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF"
 + "0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34"
 + "2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2"
 + "134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000"
 + "0049454E44AE426082");
        btnSalvarMarkup.setImageId(4);
        btnSalvarMarkup.setColor("clBtnFace");
        btnSalvarMarkup.setAccess(false);
        btnSalvarMarkup.setIconReverseDirection(false);
        FHBox11.addChildren(btnSalvarMarkup);
        btnSalvarMarkup.applyProperties();
    }

    public TFButton btnCancelarMarkup = new TFButton();

    private void init_btnCancelarMarkup() {
        btnCancelarMarkup.setName("btnCancelarMarkup");
        btnCancelarMarkup.setLeft(96);
        btnCancelarMarkup.setTop(0);
        btnCancelarMarkup.setWidth(48);
        btnCancelarMarkup.setHeight(35);
        btnCancelarMarkup.setHint("Cancelar Item");
        btnCancelarMarkup.setFontColor("clWindowText");
        btnCancelarMarkup.setFontSize(-11);
        btnCancelarMarkup.setFontName("Tahoma");
        btnCancelarMarkup.setFontStyle("[]");
        btnCancelarMarkup.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarMarkupClick(event);
            processarFlow("FrmMarkupPrincipal", "btnCancelarMarkup", "OnClick");
        });
        btnCancelarMarkup.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14"
 + "8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912"
 + "1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3"
 + "FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965"
 + "29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B"
 + "D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422"
 + "C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F"
 + "6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B"
 + "81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700"
 + "558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45"
 + "1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D"
 + "E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E"
 + "1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848"
 + "DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655"
 + "BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240"
 + "F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D"
 + "1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850"
 + "7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945"
 + "0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433"
 + "D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F"
 + "B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382"
 + "CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6"
 + "8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC"
 + "EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924"
 + "C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903"
 + "98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0"
 + "90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36"
 + "8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000"
 + "49454E44AE426082");
        btnCancelarMarkup.setImageId(9);
        btnCancelarMarkup.setColor("clBtnFace");
        btnCancelarMarkup.setAccess(false);
        btnCancelarMarkup.setIconReverseDirection(false);
        FHBox11.addChildren(btnCancelarMarkup);
        btnCancelarMarkup.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(124);
        FVBox7.setWidth(771);
        FVBox7.setHeight(100);
        FVBox7.setAlign("alClient");
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(5);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftMin");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FVBox6.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(0);
        FHBox14.setWidth(753);
        FHBox14.setHeight(45);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        FVBox7.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(0);
        FHBox27.setTop(0);
        FHBox27.setWidth(364);
        FHBox27.setHeight(41);
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(0);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(5);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftTrue");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        FHBox14.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(0);
        FHBox28.setTop(0);
        FHBox28.setWidth(118);
        FHBox28.setHeight(37);
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(5);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(0);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(1);
        FHBox28.setFlexVflex("ftFalse");
        FHBox28.setFlexHflex("ftFalse");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvTop");
        FHBox27.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(0);
        FHBox29.setWidth(9);
        FHBox29.setHeight(31);
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(1);
        FHBox29.setFlexVflex("ftTrue");
        FHBox29.setFlexHflex("ftTrue");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        FHBox28.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(9);
        FLabel5.setTop(0);
        FLabel5.setWidth(69);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Descri\u00E7\u00E3o item");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FHBox28.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFCombo edIdmarkupTipoMarkup = new TFCombo();

    private void init_edIdmarkupTipoMarkup() {
        edIdmarkupTipoMarkup.setName("edIdmarkupTipoMarkup");
        edIdmarkupTipoMarkup.setLeft(118);
        edIdmarkupTipoMarkup.setTop(0);
        edIdmarkupTipoMarkup.setWidth(237);
        edIdmarkupTipoMarkup.setHeight(21);
        edIdmarkupTipoMarkup.setTable(tbMarkup);
        edIdmarkupTipoMarkup.setLookupTable(tbMarkupTipo);
        edIdmarkupTipoMarkup.setFieldName("ID_MARKUP_TIPO");
        edIdmarkupTipoMarkup.setLookupKey("ID_MARKUP_TIPO");
        edIdmarkupTipoMarkup.setLookupDesc("DESCRICAO");
        edIdmarkupTipoMarkup.setFlex(false);
        edIdmarkupTipoMarkup.setReadOnly(true);
        edIdmarkupTipoMarkup.setRequired(false);
        edIdmarkupTipoMarkup.setPrompt("Selecione");
        edIdmarkupTipoMarkup.setConstraintCheckWhen("cwImmediate");
        edIdmarkupTipoMarkup.setConstraintCheckType("ctExpression");
        edIdmarkupTipoMarkup.setConstraintFocusOnError(false);
        edIdmarkupTipoMarkup.setConstraintEnableUI(true);
        edIdmarkupTipoMarkup.setConstraintEnabled(false);
        edIdmarkupTipoMarkup.setConstraintFormCheck(true);
        edIdmarkupTipoMarkup.setClearOnDelKey(false);
        edIdmarkupTipoMarkup.setUseClearButton(false);
        edIdmarkupTipoMarkup.setHideClearButtonOnNullValue(false);
        FHBox27.addChildren(edIdmarkupTipoMarkup);
        edIdmarkupTipoMarkup.applyProperties();
        addValidatable(edIdmarkupTipoMarkup);
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(364);
        FHBox10.setTop(0);
        FHBox10.setWidth(364);
        FHBox10.setHeight(41);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(5);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox14.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(0);
        FHBox12.setWidth(94);
        FHBox12.setHeight(37);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(5);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FHBox10.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(0);
        FHBox13.setWidth(9);
        FHBox13.setHeight(31);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftTrue");
        FHBox13.setFlexHflex("ftTrue");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FHBox12.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(9);
        FLabel3.setTop(0);
        FLabel3.setWidth(68);
        FLabel3.setHeight(13);
        FLabel3.setCaption("Tipo Comiss\u00E3o");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FHBox12.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFCombo edTipoComissaoMarkup = new TFCombo();

    private void init_edTipoComissaoMarkup() {
        edTipoComissaoMarkup.setName("edTipoComissaoMarkup");
        edTipoComissaoMarkup.setLeft(94);
        edTipoComissaoMarkup.setTop(0);
        edTipoComissaoMarkup.setWidth(237);
        edTipoComissaoMarkup.setHeight(21);
        edTipoComissaoMarkup.setHint("Tipo comiss\u00E3o, Sobre o Lucro .  Op\u00E7\u00F5es: Sobre o lucro=L; Sobre a venda=V");
        edTipoComissaoMarkup.setTable(tbMarkup);
        edTipoComissaoMarkup.setFieldName("TIPO_COMISSAO");
        edTipoComissaoMarkup.setFlex(false);
        edTipoComissaoMarkup.setListOptions("Sobre o lucro=L; Sobre a venda=V");
        edTipoComissaoMarkup.setReadOnly(true);
        edTipoComissaoMarkup.setRequired(false);
        edTipoComissaoMarkup.setPrompt("Selecione");
        edTipoComissaoMarkup.setConstraintExpression("value=\"\"");
        edTipoComissaoMarkup.setConstraintMessage("tipo comiss\u00E3o vazio");
        edTipoComissaoMarkup.setConstraintCheckWhen("cwImmediate");
        edTipoComissaoMarkup.setConstraintCheckType("ctExpression");
        edTipoComissaoMarkup.setConstraintFocusOnError(false);
        edTipoComissaoMarkup.setConstraintEnableUI(true);
        edTipoComissaoMarkup.setConstraintEnabled(false);
        edTipoComissaoMarkup.setConstraintFormCheck(true);
        edTipoComissaoMarkup.setClearOnDelKey(false);
        edTipoComissaoMarkup.setUseClearButton(false);
        edTipoComissaoMarkup.setHideClearButtonOnNullValue(false);
        FHBox10.addChildren(edTipoComissaoMarkup);
        edTipoComissaoMarkup.applyProperties();
        addValidatable(edTipoComissaoMarkup);
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(46);
        FHBox15.setWidth(752);
        FHBox15.setHeight(45);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FVBox7.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(364);
        FHBox4.setHeight(41);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(5);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox15.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(118);
        FHBox5.setHeight(37);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(5);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox4.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(9);
        FHBox6.setHeight(31);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftTrue");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox5.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(9);
        FLabel1.setTop(0);
        FLabel1.setWidth(47);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Valor Fixo");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FHBox5.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFInteger edValorFixoMarkup = new TFInteger();

    private void init_edValorFixoMarkup() {
        edValorFixoMarkup.setName("edValorFixoMarkup");
        edValorFixoMarkup.setLeft(118);
        edValorFixoMarkup.setTop(0);
        edValorFixoMarkup.setWidth(121);
        edValorFixoMarkup.setHeight(24);
        edValorFixoMarkup.setTable(tbMarkup);
        edValorFixoMarkup.setFieldName("VALOR_FIXO");
        edValorFixoMarkup.setFlex(false);
        edValorFixoMarkup.setRequired(false);
        edValorFixoMarkup.setConstraintCheckWhen("cwImmediate");
        edValorFixoMarkup.setConstraintCheckType("ctExpression");
        edValorFixoMarkup.setConstraintFocusOnError(false);
        edValorFixoMarkup.setConstraintEnableUI(true);
        edValorFixoMarkup.setConstraintEnabled(false);
        edValorFixoMarkup.setConstraintFormCheck(true);
        edValorFixoMarkup.setMaxlength(0);
        edValorFixoMarkup.setFontColor("clWindowText");
        edValorFixoMarkup.setFontSize(-13);
        edValorFixoMarkup.setFontName("Tahoma");
        edValorFixoMarkup.setFontStyle("[]");
        edValorFixoMarkup.setAlignment("taRightJustify");
        FHBox4.addChildren(edValorFixoMarkup);
        edValorFixoMarkup.applyProperties();
        addValidatable(edValorFixoMarkup);
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(364);
        FHBox7.setTop(0);
        FHBox7.setWidth(364);
        FHBox7.setHeight(41);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(5);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FHBox15.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(95);
        FHBox8.setHeight(37);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(5);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox7.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(9);
        FHBox9.setHeight(31);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftTrue");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FHBox8.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(9);
        FLabel2.setTop(0);
        FLabel2.setWidth(78);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Valor Percentual");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FHBox8.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFInteger edValorPercentualMarkup = new TFInteger();

    private void init_edValorPercentualMarkup() {
        edValorPercentualMarkup.setName("edValorPercentualMarkup");
        edValorPercentualMarkup.setLeft(95);
        edValorPercentualMarkup.setTop(0);
        edValorPercentualMarkup.setWidth(121);
        edValorPercentualMarkup.setHeight(24);
        edValorPercentualMarkup.setTable(tbMarkup);
        edValorPercentualMarkup.setFieldName("VALOR_PERCENTUAL");
        edValorPercentualMarkup.setFlex(false);
        edValorPercentualMarkup.setRequired(false);
        edValorPercentualMarkup.setConstraintCheckWhen("cwImmediate");
        edValorPercentualMarkup.setConstraintCheckType("ctExpression");
        edValorPercentualMarkup.setConstraintFocusOnError(false);
        edValorPercentualMarkup.setConstraintEnableUI(true);
        edValorPercentualMarkup.setConstraintEnabled(false);
        edValorPercentualMarkup.setConstraintFormCheck(true);
        edValorPercentualMarkup.setMaxlength(0);
        edValorPercentualMarkup.setFontColor("clWindowText");
        edValorPercentualMarkup.setFontSize(-13);
        edValorPercentualMarkup.setFontName("Tahoma");
        edValorPercentualMarkup.setFontStyle("[]");
        edValorPercentualMarkup.setAlignment("taRightJustify");
        FHBox7.addChildren(edValorPercentualMarkup);
        edValorPercentualMarkup.applyProperties();
        addValidatable(edValorPercentualMarkup);
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item11 = new TFSchemaItem();
        item11.setTable(tbMarkup);
        sc.getTables().add(item11);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void filtrarGrid(final Event<Object> event);

    public void btnAlterarMarkupClick(final Event<Object> event) {
        if (btnAlterarMarkup.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarMarkup");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarMarkupClick(final Event<Object> event) {
        if (btnSalvarMarkup.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarMarkup");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarMarkupClick(final Event<Object> event) {
        if (btnCancelarMarkup.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarMarkup");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void tbMarkupModeloAfterScroll(final Event<Object> event);

}