package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmResultCreditoCorporativo extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ResultCreditoCorporativoRNA rn = null;

    public FrmResultCreditoCorporativo() {
        try {
            rn = (freedom.bytecode.rn.ResultCreditoCorporativoRNA) getRN(freedom.bytecode.rn.wizard.ResultCreditoCorporativoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbResultCC();
        init_vBoxPrincipal();
        init_FHBox6();
        init_hBoxAcao();
        init_FHBox3();
        init_btnVoltar();
        init_FHBox2();
        init_FHBox7();
        init_hBoxTitulo();
        init_FHBox4();
        init_FLabel1();
        init_FHBox5();
        init_vBoxGrid();
        init_grdResultado();
        init_hBoxDetalheResult();
        init_FHBox1();
        init_FrmResultCreditoCorporativo();
    }

    public LIMITE_CREDITO_CORPORATIVO tbResultCC;

    private void init_tbResultCC() {
        tbResultCC = rn.tbResultCC;
        tbResultCC.setName("tbResultCC");
        tbResultCC.setMaxRowCount(200);
        tbResultCC.setWKey("171025;17101");
        tbResultCC.setRatioBatchSize(20);
        getTables().put(tbResultCC, "tbResultCC");
        tbResultCC.applyProperties();
    }

    protected TFForm FrmResultCreditoCorporativo = this;
    private void init_FrmResultCreditoCorporativo() {
        FrmResultCreditoCorporativo.setName("FrmResultCreditoCorporativo");
        FrmResultCreditoCorporativo.setCaption("Cr\u00E9dito Corporativo");
        FrmResultCreditoCorporativo.setClientHeight(447);
        FrmResultCreditoCorporativo.setClientWidth(556);
        FrmResultCreditoCorporativo.setColor("clBtnFace");
        FrmResultCreditoCorporativo.setWKey("171025");
        FrmResultCreditoCorporativo.setSpacing(0);
        FrmResultCreditoCorporativo.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(556);
        vBoxPrincipal.setHeight(447);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(3);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftFalse");
        vBoxPrincipal.setFlexHflex("ftFalse");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmResultCreditoCorporativo.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(500);
        FHBox6.setHeight(2);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox hBoxAcao = new TFHBox();

    private void init_hBoxAcao() {
        hBoxAcao.setName("hBoxAcao");
        hBoxAcao.setLeft(0);
        hBoxAcao.setTop(3);
        hBoxAcao.setWidth(515);
        hBoxAcao.setHeight(60);
        hBoxAcao.setBorderStyle("stNone");
        hBoxAcao.setPaddingTop(3);
        hBoxAcao.setPaddingLeft(3);
        hBoxAcao.setPaddingRight(3);
        hBoxAcao.setPaddingBottom(0);
        hBoxAcao.setMarginTop(0);
        hBoxAcao.setMarginLeft(0);
        hBoxAcao.setMarginRight(0);
        hBoxAcao.setMarginBottom(0);
        hBoxAcao.setSpacing(1);
        hBoxAcao.setFlexVflex("ftMin");
        hBoxAcao.setFlexHflex("ftTrue");
        hBoxAcao.setScrollable(false);
        hBoxAcao.setBoxShadowConfigHorizontalLength(10);
        hBoxAcao.setBoxShadowConfigVerticalLength(10);
        hBoxAcao.setBoxShadowConfigBlurRadius(5);
        hBoxAcao.setBoxShadowConfigSpreadRadius(0);
        hBoxAcao.setBoxShadowConfigShadowColor("clBlack");
        hBoxAcao.setBoxShadowConfigOpacity(75);
        hBoxAcao.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxAcao);
        hBoxAcao.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(5);
        FHBox3.setHeight(15);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        hBoxAcao.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(5);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(50);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmResultCreditoCorporativo", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxAcao.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(70);
        FHBox2.setTop(0);
        FHBox2.setWidth(5);
        FHBox2.setHeight(15);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxAcao.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(64);
        FHBox7.setWidth(500);
        FHBox7.setHeight(2);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox hBoxTitulo = new TFHBox();

    private void init_hBoxTitulo() {
        hBoxTitulo.setName("hBoxTitulo");
        hBoxTitulo.setLeft(0);
        hBoxTitulo.setTop(67);
        hBoxTitulo.setWidth(545);
        hBoxTitulo.setHeight(22);
        hBoxTitulo.setBorderStyle("stSingleLine");
        hBoxTitulo.setColor("clSkyBlue");
        hBoxTitulo.setPaddingTop(0);
        hBoxTitulo.setPaddingLeft(3);
        hBoxTitulo.setPaddingRight(3);
        hBoxTitulo.setPaddingBottom(0);
        hBoxTitulo.setMarginTop(0);
        hBoxTitulo.setMarginLeft(0);
        hBoxTitulo.setMarginRight(0);
        hBoxTitulo.setMarginBottom(0);
        hBoxTitulo.setSpacing(1);
        hBoxTitulo.setFlexVflex("ftFalse");
        hBoxTitulo.setFlexHflex("ftTrue");
        hBoxTitulo.setScrollable(false);
        hBoxTitulo.setBoxShadowConfigHorizontalLength(10);
        hBoxTitulo.setBoxShadowConfigVerticalLength(10);
        hBoxTitulo.setBoxShadowConfigBlurRadius(5);
        hBoxTitulo.setBoxShadowConfigSpreadRadius(0);
        hBoxTitulo.setBoxShadowConfigShadowColor("clBlack");
        hBoxTitulo.setBoxShadowConfigOpacity(75);
        hBoxTitulo.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxTitulo);
        hBoxTitulo.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(3);
        FHBox4.setHeight(15);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setColor("clSkyBlue");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        hBoxTitulo.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(3);
        FLabel1.setTop(0);
        FLabel1.setWidth(125);
        FLabel1.setHeight(13);
        FLabel1.setAlign("alClient");
        FLabel1.setCaption("Limite Cr\u00E9dito Corporativo");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        hBoxTitulo.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(128);
        FHBox5.setTop(0);
        FHBox5.setWidth(3);
        FHBox5.setHeight(15);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setColor("clSkyBlue");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        hBoxTitulo.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFVBox vBoxGrid = new TFVBox();

    private void init_vBoxGrid() {
        vBoxGrid.setName("vBoxGrid");
        vBoxGrid.setLeft(0);
        vBoxGrid.setTop(90);
        vBoxGrid.setWidth(513);
        vBoxGrid.setHeight(225);
        vBoxGrid.setBorderStyle("stNone");
        vBoxGrid.setPaddingTop(0);
        vBoxGrid.setPaddingLeft(3);
        vBoxGrid.setPaddingRight(3);
        vBoxGrid.setPaddingBottom(3);
        vBoxGrid.setMarginTop(0);
        vBoxGrid.setMarginLeft(0);
        vBoxGrid.setMarginRight(0);
        vBoxGrid.setMarginBottom(0);
        vBoxGrid.setSpacing(1);
        vBoxGrid.setFlexVflex("ftTrue");
        vBoxGrid.setFlexHflex("ftTrue");
        vBoxGrid.setScrollable(false);
        vBoxGrid.setBoxShadowConfigHorizontalLength(10);
        vBoxGrid.setBoxShadowConfigVerticalLength(10);
        vBoxGrid.setBoxShadowConfigBlurRadius(5);
        vBoxGrid.setBoxShadowConfigSpreadRadius(0);
        vBoxGrid.setBoxShadowConfigShadowColor("clBlack");
        vBoxGrid.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxGrid);
        vBoxGrid.applyProperties();
    }

    public TFGrid grdResultado = new TFGrid();

    private void init_grdResultado() {
        grdResultado.setName("grdResultado");
        grdResultado.setLeft(0);
        grdResultado.setTop(0);
        grdResultado.setWidth(320);
        grdResultado.setHeight(120);
        grdResultado.setTable(tbResultCC);
        grdResultado.setFlexVflex("ftTrue");
        grdResultado.setFlexHflex("ftTrue");
        grdResultado.setPagingEnabled(false);
        grdResultado.setFrozenColumns(0);
        grdResultado.setShowFooter(false);
        grdResultado.setShowHeader(false);
        grdResultado.setMultiSelection(false);
        grdResultado.setGroupingEnabled(false);
        grdResultado.setGroupingExpanded(false);
        grdResultado.setGroupingShowFooter(false);
        grdResultado.setCrosstabEnabled(false);
        grdResultado.setCrosstabGroupType("cgtConcat");
        grdResultado.setEditionEnabled(false);
        grdResultado.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(199);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdResultado.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("VALOR");
        item1.setTitleCaption("Valor");
        item1.setWidth(40);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdResultado.getColumns().add(item1);
        vBoxGrid.addChildren(grdResultado);
        grdResultado.applyProperties();
    }

    public TFHBox hBoxDetalheResult = new TFHBox();

    private void init_hBoxDetalheResult() {
        hBoxDetalheResult.setName("hBoxDetalheResult");
        hBoxDetalheResult.setLeft(0);
        hBoxDetalheResult.setTop(121);
        hBoxDetalheResult.setWidth(508);
        hBoxDetalheResult.setHeight(85);
        hBoxDetalheResult.setBorderStyle("stNone");
        hBoxDetalheResult.setPaddingTop(0);
        hBoxDetalheResult.setPaddingLeft(0);
        hBoxDetalheResult.setPaddingRight(0);
        hBoxDetalheResult.setPaddingBottom(0);
        hBoxDetalheResult.setVisible(false);
        hBoxDetalheResult.setMarginTop(0);
        hBoxDetalheResult.setMarginLeft(0);
        hBoxDetalheResult.setMarginRight(0);
        hBoxDetalheResult.setMarginBottom(0);
        hBoxDetalheResult.setSpacing(1);
        hBoxDetalheResult.setFlexVflex("ftFalse");
        hBoxDetalheResult.setFlexHflex("ftTrue");
        hBoxDetalheResult.setScrollable(false);
        hBoxDetalheResult.setBoxShadowConfigHorizontalLength(10);
        hBoxDetalheResult.setBoxShadowConfigVerticalLength(10);
        hBoxDetalheResult.setBoxShadowConfigBlurRadius(5);
        hBoxDetalheResult.setBoxShadowConfigSpreadRadius(0);
        hBoxDetalheResult.setBoxShadowConfigShadowColor("clBlack");
        hBoxDetalheResult.setBoxShadowConfigOpacity(75);
        hBoxDetalheResult.setVAlign("tvTop");
        vBoxGrid.addChildren(hBoxDetalheResult);
        hBoxDetalheResult.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(316);
        FHBox1.setWidth(500);
        FHBox1.setHeight(16);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setColor("clInfoBk");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}