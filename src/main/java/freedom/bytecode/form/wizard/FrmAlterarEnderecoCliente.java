package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAlterarEnderecoCliente extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AlterarEnderecoClienteRNA rn = null;

    public FrmAlterarEnderecoCliente() {
        try {
            rn = (freedom.bytecode.rn.AlterarEnderecoClienteRNA) getRN(freedom.bytecode.rn.wizard.AlterarEnderecoClienteRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbAlteraEnderecoCliente();
        init_tbUf();
        init_tbCidades();
        init_hboxEditarLead();
        init_btnSalvar();
        init_FVBox4();
        init_btnCancelar();
        init_hboxDadosClientes();
        init_gridAlterarDadosBasicos();
        init_email();
        init_lblUFCidadeCEP();
        init_lblTpEndereco();
        init_lblRuaNumero();
        init_lblContato();
        init_lblBairro();
        init_edtTPEndereco();
        init_hBoxUFCidadeCEP();
        init_cbUF();
        init_FVBox5();
        init_cbCidade();
        init_FVBox1();
        init_edtCEP();
        init_edtBairro();
        init_hBoxRuaNumero();
        init_edtRua();
        init_FVBox2();
        init_edtNumero();
        init_FHBox1();
        init_edtComplemento();
        init_FVBox3();
        init_edtCxPostal();
        init_edtContato();
        init_FrmAlterarEnderecoCliente();
    }

    public ALTERA_ENDERECO_CLIENTE tbAlteraEnderecoCliente;

    private void init_tbAlteraEnderecoCliente() {
        tbAlteraEnderecoCliente = rn.tbAlteraEnderecoCliente;
        tbAlteraEnderecoCliente.setName("tbAlteraEnderecoCliente");
        tbAlteraEnderecoCliente.setMaxRowCount(200);
        tbAlteraEnderecoCliente.setWKey("430063;43001");
        tbAlteraEnderecoCliente.setRatioBatchSize(20);
        getTables().put(tbAlteraEnderecoCliente, "tbAlteraEnderecoCliente");
        tbAlteraEnderecoCliente.applyProperties();
    }

    public UF tbUf;

    private void init_tbUf() {
        tbUf = rn.tbUf;
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(200);
        tbUf.setWKey("430063;43002");
        tbUf.setRatioBatchSize(20);
        getTables().put(tbUf, "tbUf");
        tbUf.applyProperties();
    }

    public CIDADES tbCidades;

    private void init_tbCidades() {
        tbCidades = rn.tbCidades;
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("430063;43003");
        tbCidades.setRatioBatchSize(20);
        getTables().put(tbCidades, "tbCidades");
        tbCidades.applyProperties();
    }

    protected TFForm FrmAlterarEnderecoCliente = this;
    private void init_FrmAlterarEnderecoCliente() {
        FrmAlterarEnderecoCliente.setName("FrmAlterarEnderecoCliente");
        FrmAlterarEnderecoCliente.setCaption("Alterar Endere\u00E7o do Cliente");
        FrmAlterarEnderecoCliente.setClientHeight(295);
        FrmAlterarEnderecoCliente.setClientWidth(656);
        FrmAlterarEnderecoCliente.setColor("clBtnFace");
        FrmAlterarEnderecoCliente.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAlterarEnderecoCliente", "FrmAlterarEnderecoCliente", "OnCreate");
        });
        FrmAlterarEnderecoCliente.setWOrigem("EhMain");
        FrmAlterarEnderecoCliente.setWKey("430063");
        FrmAlterarEnderecoCliente.setSpacing(0);
        FrmAlterarEnderecoCliente.applyProperties();
    }

    public TFHBox hboxEditarLead = new TFHBox();

    private void init_hboxEditarLead() {
        hboxEditarLead.setName("hboxEditarLead");
        hboxEditarLead.setLeft(0);
        hboxEditarLead.setTop(0);
        hboxEditarLead.setWidth(656);
        hboxEditarLead.setHeight(61);
        hboxEditarLead.setAlign("alTop");
        hboxEditarLead.setBorderStyle("stNone");
        hboxEditarLead.setPaddingTop(3);
        hboxEditarLead.setPaddingLeft(5);
        hboxEditarLead.setPaddingRight(0);
        hboxEditarLead.setPaddingBottom(0);
        hboxEditarLead.setMarginTop(0);
        hboxEditarLead.setMarginLeft(0);
        hboxEditarLead.setMarginRight(0);
        hboxEditarLead.setMarginBottom(0);
        hboxEditarLead.setSpacing(1);
        hboxEditarLead.setFlexVflex("ftFalse");
        hboxEditarLead.setFlexHflex("ftTrue");
        hboxEditarLead.setScrollable(false);
        hboxEditarLead.setBoxShadowConfigHorizontalLength(10);
        hboxEditarLead.setBoxShadowConfigVerticalLength(10);
        hboxEditarLead.setBoxShadowConfigBlurRadius(5);
        hboxEditarLead.setBoxShadowConfigSpreadRadius(0);
        hboxEditarLead.setBoxShadowConfigShadowColor("clBlack");
        hboxEditarLead.setBoxShadowConfigOpacity(75);
        hboxEditarLead.setVAlign("tvTop");
        FrmAlterarEnderecoCliente.addChildren(hboxEditarLead);
        hboxEditarLead.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(60);
        btnSalvar.setHeight(56);
        btnSalvar.setHint("Salvar Altera\u00E7\u00F5es no Endere\u00E7o do Cliente");
        btnSalvar.setAlign("alLeft");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAlterarEnderecoCliente", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(700080);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hboxEditarLead.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(60);
        FVBox4.setTop(0);
        FVBox4.setWidth(5);
        FVBox4.setHeight(55);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hboxEditarLead.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(65);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(60);
        btnCancelar.setHeight(56);
        btnCancelar.setHint("Cancelar Altera\u00E7\u00E3o de Endere\u00E7o do Cliente");
        btnCancelar.setAlign("alLeft");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmAlterarEnderecoCliente", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(700098);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hboxEditarLead.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFVBox hboxDadosClientes = new TFVBox();

    private void init_hboxDadosClientes() {
        hboxDadosClientes.setName("hboxDadosClientes");
        hboxDadosClientes.setLeft(0);
        hboxDadosClientes.setTop(61);
        hboxDadosClientes.setWidth(656);
        hboxDadosClientes.setHeight(234);
        hboxDadosClientes.setAlign("alClient");
        hboxDadosClientes.setBorderStyle("stNone");
        hboxDadosClientes.setPaddingTop(5);
        hboxDadosClientes.setPaddingLeft(5);
        hboxDadosClientes.setPaddingRight(5);
        hboxDadosClientes.setPaddingBottom(0);
        hboxDadosClientes.setMarginTop(0);
        hboxDadosClientes.setMarginLeft(0);
        hboxDadosClientes.setMarginRight(0);
        hboxDadosClientes.setMarginBottom(0);
        hboxDadosClientes.setSpacing(1);
        hboxDadosClientes.setFlexVflex("ftTrue");
        hboxDadosClientes.setFlexHflex("ftTrue");
        hboxDadosClientes.setScrollable(false);
        hboxDadosClientes.setBoxShadowConfigHorizontalLength(10);
        hboxDadosClientes.setBoxShadowConfigVerticalLength(10);
        hboxDadosClientes.setBoxShadowConfigBlurRadius(5);
        hboxDadosClientes.setBoxShadowConfigSpreadRadius(0);
        hboxDadosClientes.setBoxShadowConfigShadowColor("clBlack");
        hboxDadosClientes.setBoxShadowConfigOpacity(75);
        FrmAlterarEnderecoCliente.addChildren(hboxDadosClientes);
        hboxDadosClientes.applyProperties();
    }

    public TFGridPanel gridAlterarDadosBasicos = new TFGridPanel();

    private void init_gridAlterarDadosBasicos() {
        gridAlterarDadosBasicos.setName("gridAlterarDadosBasicos");
        gridAlterarDadosBasicos.setLeft(0);
        gridAlterarDadosBasicos.setTop(0);
        gridAlterarDadosBasicos.setWidth(646);
        gridAlterarDadosBasicos.setHeight(227);
        gridAlterarDadosBasicos.setAlign("alClient");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(145.000000000000000000);
        gridAlterarDadosBasicos.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("lblTpEndereco");
        item2.setRow(0);
        gridAlterarDadosBasicos.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("edtTPEndereco");
        item3.setRow(0);
        gridAlterarDadosBasicos.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("lblUFCidadeCEP");
        item4.setRow(1);
        gridAlterarDadosBasicos.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(0);
        item5.setControl("email");
        item5.setRow(4);
        gridAlterarDadosBasicos.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(1);
        item6.setControl("edtBairro");
        item6.setRow(2);
        gridAlterarDadosBasicos.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(0);
        item7.setControl("lblBairro");
        item7.setRow(2);
        gridAlterarDadosBasicos.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(1);
        item8.setControl("hBoxUFCidadeCEP");
        item8.setRow(1);
        gridAlterarDadosBasicos.getControlCollection().add(item8);
        TFControlItem item9 = new TFControlItem();
        item9.setColumn(0);
        item9.setControl("lblRuaNumero");
        item9.setRow(3);
        gridAlterarDadosBasicos.getControlCollection().add(item9);
        TFControlItem item10 = new TFControlItem();
        item10.setColumn(0);
        item10.setControl("lblContato");
        item10.setRow(5);
        gridAlterarDadosBasicos.getControlCollection().add(item10);
        TFControlItem item11 = new TFControlItem();
        item11.setColumn(1);
        item11.setControl("hBoxRuaNumero");
        item11.setRow(3);
        gridAlterarDadosBasicos.getControlCollection().add(item11);
        TFControlItem item12 = new TFControlItem();
        item12.setColumn(1);
        item12.setControl("FHBox1");
        item12.setRow(4);
        gridAlterarDadosBasicos.getControlCollection().add(item12);
        TFControlItem item13 = new TFControlItem();
        item13.setColumn(1);
        item13.setControl("edtContato");
        item13.setRow(5);
        gridAlterarDadosBasicos.getControlCollection().add(item13);
        TFGridPanelRow item14 = new TFGridPanelRow();
        item14.setSizeStyle("ssAuto");
        gridAlterarDadosBasicos.getRowCollection().add(item14);
        TFGridPanelRow item15 = new TFGridPanelRow();
        item15.setSizeStyle("ssAuto");
        item15.setValue(28.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item15);
        TFGridPanelRow item16 = new TFGridPanelRow();
        item16.setSizeStyle("ssAuto");
        item16.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item16);
        TFGridPanelRow item17 = new TFGridPanelRow();
        item17.setSizeStyle("ssAuto");
        item17.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item17);
        TFGridPanelRow item18 = new TFGridPanelRow();
        item18.setSizeStyle("ssAuto");
        item18.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item18);
        TFGridPanelRow item19 = new TFGridPanelRow();
        item19.setSizeStyle("ssAuto");
        gridAlterarDadosBasicos.getRowCollection().add(item19);
        TFGridPanelRow item20 = new TFGridPanelRow();
        item20.setValue(100.000000000000000000);
        gridAlterarDadosBasicos.getRowCollection().add(item20);
        gridAlterarDadosBasicos.setFlexVflex("ftTrue");
        gridAlterarDadosBasicos.setFlexHflex("ftTrue");
        gridAlterarDadosBasicos.setAllRowFlex(false);
        gridAlterarDadosBasicos.setColumnTabOrder(false);
        hboxDadosClientes.addChildren(gridAlterarDadosBasicos);
        gridAlterarDadosBasicos.applyProperties();
    }

    public TFLabel email = new TFLabel();

    private void init_email() {
        email.setName("email");
        email.setLeft(9);
        email.setTop(131);
        email.setWidth(137);
        email.setHeight(41);
        email.setAlign("alRight");
        email.setCaption("Complemento/Cx.Postal");
        email.setFontColor("clWindowText");
        email.setFontSize(-11);
        email.setFontName("Tahoma");
        email.setFontStyle("[fsBold]");
        email.setVerticalAlignment("taVerticalCenter");
        email.setWordBreak(false);
        gridAlterarDadosBasicos.addChildren(email);
        email.applyProperties();
    }

    public TFLabel lblUFCidadeCEP = new TFLabel();

    private void init_lblUFCidadeCEP() {
        lblUFCidadeCEP.setName("lblUFCidadeCEP");
        lblUFCidadeCEP.setLeft(62);
        lblUFCidadeCEP.setTop(25);
        lblUFCidadeCEP.setWidth(84);
        lblUFCidadeCEP.setHeight(41);
        lblUFCidadeCEP.setAlign("alRight");
        lblUFCidadeCEP.setCaption("UF/Cidade/CEP");
        lblUFCidadeCEP.setFontColor("clWindowText");
        lblUFCidadeCEP.setFontSize(-11);
        lblUFCidadeCEP.setFontName("Tahoma");
        lblUFCidadeCEP.setFontStyle("[fsBold]");
        lblUFCidadeCEP.setVerticalAlignment("taVerticalCenter");
        lblUFCidadeCEP.setWordBreak(false);
        gridAlterarDadosBasicos.addChildren(lblUFCidadeCEP);
        lblUFCidadeCEP.applyProperties();
    }

    public TFLabel lblTpEndereco = new TFLabel();

    private void init_lblTpEndereco() {
        lblTpEndereco.setName("lblTpEndereco");
        lblTpEndereco.setLeft(67);
        lblTpEndereco.setTop(1);
        lblTpEndereco.setWidth(79);
        lblTpEndereco.setHeight(24);
        lblTpEndereco.setAlign("alRight");
        lblTpEndereco.setCaption("Tipo Endere\u00E7o");
        lblTpEndereco.setFontColor("clWindowText");
        lblTpEndereco.setFontSize(-11);
        lblTpEndereco.setFontName("Tahoma");
        lblTpEndereco.setFontStyle("[fsBold]");
        lblTpEndereco.setVerticalAlignment("taVerticalCenter");
        lblTpEndereco.setWordBreak(false);
        gridAlterarDadosBasicos.addChildren(lblTpEndereco);
        lblTpEndereco.applyProperties();
    }

    public TFLabel lblRuaNumero = new TFLabel();

    private void init_lblRuaNumero() {
        lblRuaNumero.setName("lblRuaNumero");
        lblRuaNumero.setLeft(74);
        lblRuaNumero.setTop(90);
        lblRuaNumero.setWidth(72);
        lblRuaNumero.setHeight(41);
        lblRuaNumero.setAlign("alRight");
        lblRuaNumero.setCaption("Rua/N\u00FAmero");
        lblRuaNumero.setFontColor("clWindowText");
        lblRuaNumero.setFontSize(-11);
        lblRuaNumero.setFontName("Tahoma");
        lblRuaNumero.setFontStyle("[fsBold]");
        lblRuaNumero.setVerticalAlignment("taVerticalCenter");
        lblRuaNumero.setWordBreak(false);
        gridAlterarDadosBasicos.addChildren(lblRuaNumero);
        lblRuaNumero.applyProperties();
    }

    public TFLabel lblContato = new TFLabel();

    private void init_lblContato() {
        lblContato.setName("lblContato");
        lblContato.setLeft(101);
        lblContato.setTop(172);
        lblContato.setWidth(45);
        lblContato.setHeight(24);
        lblContato.setAlign("alRight");
        lblContato.setCaption("Contato");
        lblContato.setFontColor("clWindowText");
        lblContato.setFontSize(-11);
        lblContato.setFontName("Tahoma");
        lblContato.setFontStyle("[fsBold]");
        lblContato.setVerticalAlignment("taVerticalCenter");
        lblContato.setWordBreak(false);
        gridAlterarDadosBasicos.addChildren(lblContato);
        lblContato.applyProperties();
    }

    public TFLabel lblBairro = new TFLabel();

    private void init_lblBairro() {
        lblBairro.setName("lblBairro");
        lblBairro.setLeft(112);
        lblBairro.setTop(66);
        lblBairro.setWidth(34);
        lblBairro.setHeight(24);
        lblBairro.setAlign("alRight");
        lblBairro.setCaption("Bairro");
        lblBairro.setFontColor("clWindowText");
        lblBairro.setFontSize(-11);
        lblBairro.setFontName("Tahoma");
        lblBairro.setFontStyle("[fsBold]");
        lblBairro.setVerticalAlignment("taVerticalCenter");
        lblBairro.setWordBreak(false);
        gridAlterarDadosBasicos.addChildren(lblBairro);
        lblBairro.applyProperties();
    }

    public TFString edtTPEndereco = new TFString();

    private void init_edtTPEndereco() {
        edtTPEndereco.setName("edtTPEndereco");
        edtTPEndereco.setLeft(146);
        edtTPEndereco.setTop(1);
        edtTPEndereco.setWidth(305);
        edtTPEndereco.setHeight(24);
        edtTPEndereco.setTable(tbAlteraEnderecoCliente);
        edtTPEndereco.setFieldName("TIPO_ENDERECO");
        edtTPEndereco.setFlex(true);
        edtTPEndereco.setRequired(false);
        edtTPEndereco.setConstraintCheckWhen("cwImmediate");
        edtTPEndereco.setConstraintCheckType("ctExpression");
        edtTPEndereco.setConstraintFocusOnError(false);
        edtTPEndereco.setConstraintEnableUI(true);
        edtTPEndereco.setConstraintEnabled(false);
        edtTPEndereco.setConstraintFormCheck(true);
        edtTPEndereco.setCharCase("ccNormal");
        edtTPEndereco.setPwd(false);
        edtTPEndereco.setMaxlength(0);
        edtTPEndereco.setAlign("alLeft");
        edtTPEndereco.setEnabled(false);
        edtTPEndereco.setFontColor("clWindowText");
        edtTPEndereco.setFontSize(-13);
        edtTPEndereco.setFontName("Tahoma");
        edtTPEndereco.setFontStyle("[]");
        edtTPEndereco.setSaveLiteralCharacter(false);
        edtTPEndereco.applyProperties();
        gridAlterarDadosBasicos.addChildren(edtTPEndereco);
        addValidatable(edtTPEndereco);
    }

    public TFHBox hBoxUFCidadeCEP = new TFHBox();

    private void init_hBoxUFCidadeCEP() {
        hBoxUFCidadeCEP.setName("hBoxUFCidadeCEP");
        hBoxUFCidadeCEP.setLeft(146);
        hBoxUFCidadeCEP.setTop(25);
        hBoxUFCidadeCEP.setWidth(485);
        hBoxUFCidadeCEP.setHeight(41);
        hBoxUFCidadeCEP.setAlign("alLeft");
        hBoxUFCidadeCEP.setBorderStyle("stNone");
        hBoxUFCidadeCEP.setPaddingTop(0);
        hBoxUFCidadeCEP.setPaddingLeft(0);
        hBoxUFCidadeCEP.setPaddingRight(0);
        hBoxUFCidadeCEP.setPaddingBottom(0);
        hBoxUFCidadeCEP.setMarginTop(0);
        hBoxUFCidadeCEP.setMarginLeft(0);
        hBoxUFCidadeCEP.setMarginRight(0);
        hBoxUFCidadeCEP.setMarginBottom(0);
        hBoxUFCidadeCEP.setSpacing(1);
        hBoxUFCidadeCEP.setFlexVflex("ftTrue");
        hBoxUFCidadeCEP.setFlexHflex("ftTrue");
        hBoxUFCidadeCEP.setScrollable(false);
        hBoxUFCidadeCEP.setBoxShadowConfigHorizontalLength(10);
        hBoxUFCidadeCEP.setBoxShadowConfigVerticalLength(10);
        hBoxUFCidadeCEP.setBoxShadowConfigBlurRadius(5);
        hBoxUFCidadeCEP.setBoxShadowConfigSpreadRadius(0);
        hBoxUFCidadeCEP.setBoxShadowConfigShadowColor("clBlack");
        hBoxUFCidadeCEP.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEP.setVAlign("tvTop");
        gridAlterarDadosBasicos.addChildren(hBoxUFCidadeCEP);
        hBoxUFCidadeCEP.applyProperties();
    }

    public TFCombo cbUF = new TFCombo();

    private void init_cbUF() {
        cbUF.setName("cbUF");
        cbUF.setLeft(0);
        cbUF.setTop(0);
        cbUF.setWidth(126);
        cbUF.setHeight(21);
        cbUF.setTable(tbAlteraEnderecoCliente);
        cbUF.setLookupTable(tbUf);
        cbUF.setFieldName("UF");
        cbUF.setLookupKey("UF");
        cbUF.setLookupDesc("UF");
        cbUF.setFlex(false);
        cbUF.setReadOnly(true);
        cbUF.setRequired(false);
        cbUF.setPrompt("Selecione");
        cbUF.setConstraintCheckWhen("cwImmediate");
        cbUF.setConstraintCheckType("ctExpression");
        cbUF.setConstraintFocusOnError(false);
        cbUF.setConstraintEnableUI(true);
        cbUF.setConstraintEnabled(false);
        cbUF.setConstraintFormCheck(true);
        cbUF.setClearOnDelKey(false);
        cbUF.setUseClearButton(true);
        cbUF.setHideClearButtonOnNullValue(true);
        cbUF.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbUFChange(event);
            processarFlow("FrmAlterarEnderecoCliente", "cbUF", "OnChange");
        });
        cbUF.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbUFClearClick(event);
            processarFlow("FrmAlterarEnderecoCliente", "cbUF", "OnClearClick");
        });
        hBoxUFCidadeCEP.addChildren(cbUF);
        cbUF.applyProperties();
        addValidatable(cbUF);
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(126);
        FVBox5.setTop(0);
        FVBox5.setWidth(5);
        FVBox5.setHeight(24);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEP.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFCombo cbCidade = new TFCombo();

    private void init_cbCidade() {
        cbCidade.setName("cbCidade");
        cbCidade.setLeft(131);
        cbCidade.setTop(0);
        cbCidade.setWidth(245);
        cbCidade.setHeight(21);
        cbCidade.setTable(tbAlteraEnderecoCliente);
        cbCidade.setLookupTable(tbCidades);
        cbCidade.setFieldName("COD_CIDADES");
        cbCidade.setLookupKey("COD_CIDADES");
        cbCidade.setLookupDesc("DESCRICAO");
        cbCidade.setFlex(true);
        cbCidade.setReadOnly(true);
        cbCidade.setRequired(false);
        cbCidade.setPrompt("Selecione");
        cbCidade.setConstraintCheckWhen("cwImmediate");
        cbCidade.setConstraintCheckType("ctExpression");
        cbCidade.setConstraintFocusOnError(false);
        cbCidade.setConstraintEnableUI(true);
        cbCidade.setConstraintEnabled(false);
        cbCidade.setConstraintFormCheck(true);
        cbCidade.setClearOnDelKey(false);
        cbCidade.setUseClearButton(true);
        cbCidade.setHideClearButtonOnNullValue(true);
        cbCidade.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbCidadeChange(event);
            processarFlow("FrmAlterarEnderecoCliente", "cbCidade", "OnChange");
        });
        cbCidade.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbCidadeClearClick(event);
            processarFlow("FrmAlterarEnderecoCliente", "cbCidade", "OnClearClick");
        });
        hBoxUFCidadeCEP.addChildren(cbCidade);
        cbCidade.applyProperties();
        addValidatable(cbCidade);
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(376);
        FVBox1.setTop(0);
        FVBox1.setWidth(5);
        FVBox1.setHeight(24);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        hBoxUFCidadeCEP.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFString edtCEP = new TFString();

    private void init_edtCEP() {
        edtCEP.setName("edtCEP");
        edtCEP.setLeft(381);
        edtCEP.setTop(0);
        edtCEP.setWidth(80);
        edtCEP.setHeight(24);
        edtCEP.setTable(tbAlteraEnderecoCliente);
        edtCEP.setFieldName("CEP");
        edtCEP.setFlex(false);
        edtCEP.setRequired(false);
        edtCEP.setConstraintCheckWhen("cwImmediate");
        edtCEP.setConstraintCheckType("ctExpression");
        edtCEP.setConstraintFocusOnError(false);
        edtCEP.setConstraintEnableUI(true);
        edtCEP.setConstraintEnabled(false);
        edtCEP.setConstraintFormCheck(true);
        edtCEP.setCharCase("ccNormal");
        edtCEP.setPwd(false);
        edtCEP.setMaxlength(0);
        edtCEP.setEnabled(false);
        edtCEP.setFontColor("clWindowText");
        edtCEP.setFontSize(-13);
        edtCEP.setFontName("Tahoma");
        edtCEP.setFontStyle("[]");
        edtCEP.setSaveLiteralCharacter(false);
        edtCEP.applyProperties();
        hBoxUFCidadeCEP.addChildren(edtCEP);
        addValidatable(edtCEP);
    }

    public TFString edtBairro = new TFString();

    private void init_edtBairro() {
        edtBairro.setName("edtBairro");
        edtBairro.setLeft(146);
        edtBairro.setTop(66);
        edtBairro.setWidth(424);
        edtBairro.setHeight(24);
        edtBairro.setTable(tbAlteraEnderecoCliente);
        edtBairro.setFieldName("BAIRRO");
        edtBairro.setFlex(true);
        edtBairro.setRequired(false);
        edtBairro.setConstraintCheckWhen("cwImmediate");
        edtBairro.setConstraintCheckType("ctExpression");
        edtBairro.setConstraintFocusOnError(false);
        edtBairro.setConstraintEnableUI(true);
        edtBairro.setConstraintEnabled(false);
        edtBairro.setConstraintFormCheck(true);
        edtBairro.setCharCase("ccNormal");
        edtBairro.setPwd(false);
        edtBairro.setMaxlength(30);
        edtBairro.setFontColor("clWindowText");
        edtBairro.setFontSize(-13);
        edtBairro.setFontName("Tahoma");
        edtBairro.setFontStyle("[]");
        edtBairro.setSaveLiteralCharacter(false);
        edtBairro.applyProperties();
        gridAlterarDadosBasicos.addChildren(edtBairro);
        addValidatable(edtBairro);
    }

    public TFHBox hBoxRuaNumero = new TFHBox();

    private void init_hBoxRuaNumero() {
        hBoxRuaNumero.setName("hBoxRuaNumero");
        hBoxRuaNumero.setLeft(146);
        hBoxRuaNumero.setTop(90);
        hBoxRuaNumero.setWidth(433);
        hBoxRuaNumero.setHeight(41);
        hBoxRuaNumero.setAlign("alLeft");
        hBoxRuaNumero.setBorderStyle("stNone");
        hBoxRuaNumero.setPaddingTop(0);
        hBoxRuaNumero.setPaddingLeft(0);
        hBoxRuaNumero.setPaddingRight(0);
        hBoxRuaNumero.setPaddingBottom(0);
        hBoxRuaNumero.setMarginTop(0);
        hBoxRuaNumero.setMarginLeft(0);
        hBoxRuaNumero.setMarginRight(0);
        hBoxRuaNumero.setMarginBottom(0);
        hBoxRuaNumero.setSpacing(1);
        hBoxRuaNumero.setFlexVflex("ftTrue");
        hBoxRuaNumero.setFlexHflex("ftTrue");
        hBoxRuaNumero.setScrollable(false);
        hBoxRuaNumero.setBoxShadowConfigHorizontalLength(10);
        hBoxRuaNumero.setBoxShadowConfigVerticalLength(10);
        hBoxRuaNumero.setBoxShadowConfigBlurRadius(5);
        hBoxRuaNumero.setBoxShadowConfigSpreadRadius(0);
        hBoxRuaNumero.setBoxShadowConfigShadowColor("clBlack");
        hBoxRuaNumero.setBoxShadowConfigOpacity(75);
        hBoxRuaNumero.setVAlign("tvTop");
        gridAlterarDadosBasicos.addChildren(hBoxRuaNumero);
        hBoxRuaNumero.applyProperties();
    }

    public TFString edtRua = new TFString();

    private void init_edtRua() {
        edtRua.setName("edtRua");
        edtRua.setLeft(0);
        edtRua.setTop(0);
        edtRua.setWidth(331);
        edtRua.setHeight(24);
        edtRua.setTable(tbAlteraEnderecoCliente);
        edtRua.setFieldName("RUA");
        edtRua.setFlex(true);
        edtRua.setRequired(false);
        edtRua.setConstraintCheckWhen("cwImmediate");
        edtRua.setConstraintCheckType("ctExpression");
        edtRua.setConstraintFocusOnError(false);
        edtRua.setConstraintEnableUI(true);
        edtRua.setConstraintEnabled(false);
        edtRua.setConstraintFormCheck(true);
        edtRua.setCharCase("ccNormal");
        edtRua.setPwd(false);
        edtRua.setMaxlength(50);
        edtRua.setFontColor("clWindowText");
        edtRua.setFontSize(-13);
        edtRua.setFontName("Tahoma");
        edtRua.setFontStyle("[]");
        edtRua.setSaveLiteralCharacter(false);
        edtRua.applyProperties();
        hBoxRuaNumero.addChildren(edtRua);
        addValidatable(edtRua);
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(331);
        FVBox2.setTop(0);
        FVBox2.setWidth(5);
        FVBox2.setHeight(24);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hBoxRuaNumero.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFString edtNumero = new TFString();

    private void init_edtNumero() {
        edtNumero.setName("edtNumero");
        edtNumero.setLeft(336);
        edtNumero.setTop(0);
        edtNumero.setWidth(80);
        edtNumero.setHeight(24);
        edtNumero.setTable(tbAlteraEnderecoCliente);
        edtNumero.setFieldName("NUMERO");
        edtNumero.setFlex(false);
        edtNumero.setRequired(false);
        edtNumero.setConstraintCheckWhen("cwImmediate");
        edtNumero.setConstraintCheckType("ctExpression");
        edtNumero.setConstraintFocusOnError(false);
        edtNumero.setConstraintEnableUI(true);
        edtNumero.setConstraintEnabled(false);
        edtNumero.setConstraintFormCheck(true);
        edtNumero.setCharCase("ccNormal");
        edtNumero.setPwd(false);
        edtNumero.setMaxlength(5);
        edtNumero.setFontColor("clWindowText");
        edtNumero.setFontSize(-13);
        edtNumero.setFontName("Tahoma");
        edtNumero.setFontStyle("[]");
        edtNumero.setSaveLiteralCharacter(false);
        edtNumero.applyProperties();
        hBoxRuaNumero.addChildren(edtNumero);
        addValidatable(edtNumero);
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(146);
        FHBox1.setTop(131);
        FHBox1.setWidth(433);
        FHBox1.setHeight(41);
        FHBox1.setAlign("alLeft");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftTrue");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        gridAlterarDadosBasicos.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFString edtComplemento = new TFString();

    private void init_edtComplemento() {
        edtComplemento.setName("edtComplemento");
        edtComplemento.setLeft(0);
        edtComplemento.setTop(0);
        edtComplemento.setWidth(331);
        edtComplemento.setHeight(24);
        edtComplemento.setTable(tbAlteraEnderecoCliente);
        edtComplemento.setFieldName("COMPLEMENTO");
        edtComplemento.setFlex(true);
        edtComplemento.setRequired(false);
        edtComplemento.setConstraintCheckWhen("cwImmediate");
        edtComplemento.setConstraintCheckType("ctExpression");
        edtComplemento.setConstraintFocusOnError(false);
        edtComplemento.setConstraintEnableUI(true);
        edtComplemento.setConstraintEnabled(false);
        edtComplemento.setConstraintFormCheck(true);
        edtComplemento.setCharCase("ccNormal");
        edtComplemento.setPwd(false);
        edtComplemento.setMaxlength(40);
        edtComplemento.setFontColor("clWindowText");
        edtComplemento.setFontSize(-13);
        edtComplemento.setFontName("Tahoma");
        edtComplemento.setFontStyle("[]");
        edtComplemento.setSaveLiteralCharacter(false);
        edtComplemento.applyProperties();
        FHBox1.addChildren(edtComplemento);
        addValidatable(edtComplemento);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(331);
        FVBox3.setTop(0);
        FVBox3.setWidth(5);
        FVBox3.setHeight(24);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFString edtCxPostal = new TFString();

    private void init_edtCxPostal() {
        edtCxPostal.setName("edtCxPostal");
        edtCxPostal.setLeft(336);
        edtCxPostal.setTop(0);
        edtCxPostal.setWidth(80);
        edtCxPostal.setHeight(24);
        edtCxPostal.setTable(tbAlteraEnderecoCliente);
        edtCxPostal.setFieldName("CX_POSTAL");
        edtCxPostal.setFlex(false);
        edtCxPostal.setRequired(false);
        edtCxPostal.setConstraintCheckWhen("cwImmediate");
        edtCxPostal.setConstraintCheckType("ctExpression");
        edtCxPostal.setConstraintFocusOnError(false);
        edtCxPostal.setConstraintEnableUI(true);
        edtCxPostal.setConstraintEnabled(false);
        edtCxPostal.setConstraintFormCheck(true);
        edtCxPostal.setCharCase("ccNormal");
        edtCxPostal.setPwd(false);
        edtCxPostal.setMaxlength(10);
        edtCxPostal.setFontColor("clWindowText");
        edtCxPostal.setFontSize(-13);
        edtCxPostal.setFontName("Tahoma");
        edtCxPostal.setFontStyle("[]");
        edtCxPostal.setSaveLiteralCharacter(false);
        edtCxPostal.applyProperties();
        FHBox1.addChildren(edtCxPostal);
        addValidatable(edtCxPostal);
    }

    public TFString edtContato = new TFString();

    private void init_edtContato() {
        edtContato.setName("edtContato");
        edtContato.setLeft(146);
        edtContato.setTop(172);
        edtContato.setWidth(315);
        edtContato.setHeight(24);
        edtContato.setTable(tbAlteraEnderecoCliente);
        edtContato.setFieldName("CONTATO");
        edtContato.setFlex(true);
        edtContato.setRequired(false);
        edtContato.setConstraintCheckWhen("cwImmediate");
        edtContato.setConstraintCheckType("ctExpression");
        edtContato.setConstraintFocusOnError(false);
        edtContato.setConstraintEnableUI(true);
        edtContato.setConstraintEnabled(false);
        edtContato.setConstraintFormCheck(true);
        edtContato.setCharCase("ccNormal");
        edtContato.setPwd(false);
        edtContato.setMaxlength(40);
        edtContato.setAlign("alLeft");
        edtContato.setFontColor("clWindowText");
        edtContato.setFontSize(-13);
        edtContato.setFontName("Tahoma");
        edtContato.setFontStyle("[]");
        edtContato.setSaveLiteralCharacter(false);
        edtContato.applyProperties();
        gridAlterarDadosBasicos.addChildren(edtContato);
        addValidatable(edtContato);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbUFChange(final Event<Object> event);

    public abstract void cbUFClearClick(final Event<Object> event);

    public abstract void cbCidadeChange(final Event<Object> event);

    public abstract void cbCidadeClearClick(final Event<Object> event);

}