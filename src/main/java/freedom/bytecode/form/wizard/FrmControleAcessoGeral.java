package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmControleAcessoGeral extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ControleAcessoGeralRNA rn = null;

    public FrmControleAcessoGeral() {
        try {
            rn = (freedom.bytecode.rn.ControleAcessoGeralRNA) getRN(freedom.bytecode.rn.wizard.ControleAcessoGeralRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbControleAcesso();
        init_tbEmpresasFuncoes();
        init_tbSistemaAcessoFuncao();
        init_tbPerfilUsuarios();
        init_tbEmpresasUsuarios();
        init_tbUsuarioFoto();
        init_FPopupMenu1();
        init_FMenuItem1();
        init_FMenuItem2();
        init_popGridAgentesFuncao();
        init_mmExportarExcelGridAgentesFuncao();
        init_FVBox1();
        init_FHBox7();
        init_FVBox10();
        init_hb();
        init_FHBox9();
        init_FVBox7();
        init_FLabel1();
        init_cbbEmpresasFuncoes();
        init_hBoxCodAcessoDescricao();
        init_edtPesquisa();
        init_hBoxCodAcessoDescricaoSeparador01();
        init_iconClassLimpar();
        init_FVBox11();
        init_vBoxIconClassRefresh();
        init_btnRefresh();
        init_FHBox8();
        init_iconClassHelp();
        init_grpBoxAgentesFuncao();
        init_FVBox2();
        init_hBoxGrade();
        init_gridAgentesFuncao();
        init_hBoxGradeSeparador01();
        init_vBoxGradeBtnPesquisaAvancada();
        init_hBoxGradeBtnPesquisaAvancadaSeparador01();
        init_btnPesquisaAvancadaAgentesFuncao();
        init_hBoxGradeBtnPesquisaAvancadaSeparador02();
        init_btnLimparPesquisaAvancada();
        init_hBoxGradeSeparador02();
        init_FVBox3();
        init_FHBox2();
        init_btnEditar();
        init_FVBox4();
        init_btnSalvar();
        init_FVBox5();
        init_btnCancelar();
        init_FVBox6();
        init_btnEditarPerfil();
        init_bBoxEdicao();
        init_bBoxEdicaoSeparador01();
        init_vBoxCamposAgentesFuncao();
        init_hBoxCamposAgentesFuncaoSeparador01();
        init_hBoxFone();
        init_vBoxFone();
        init_hBoxFoneSeparador01();
        init_FLabel2();
        init_edtFone();
        init_hBoxCamposAgentesFuncaoSeparador02();
        init_hBoxRamal();
        init_vBoxRamal();
        init_hBoxRamalSeparador01();
        init_lblRamal();
        init_edtRamal();
        init_hBoxCamposAgentesFuncaoSeparador03();
        init_hBoxEmail();
        init_vBoxEmail();
        init_hBoxEmailSeparador01();
        init_FLabel3();
        init_edtEmail();
        init_hBoxCamposAgentesFuncaoSeparador04();
        init_hBoxTipoVendedor();
        init_vBoxTipoVendedor();
        init_hBoxTipoVendedorSeparador01();
        init_FLabel4();
        init_cbbTipoVendedor();
        init_hBoxCamposAgentesFuncaoSeparador05();
        init_bBoxEdicaoSeparador02();
        init_vBoxFoto();
        init_imageFoto();
        init_lblLimparFoto();
        init_bBoxEdicaoSeparador03();
        init_FGroupbox1();
        init_treeGridAcessos();
        init_FHBox4();
        init_FLabel6();
        init_FVBox13();
        init_FHBox5();
        init_FIconClass5();
        init_FVBox8();
        init_FLabel7();
        init_FHBox6();
        init_FIconClass6();
        init_FVBox9();
        init_FLabel8();
        init_sc();
        init_FrmControleAcessoGeral();
    }

    public CONTROLE_ACESSO tbControleAcesso;

    private void init_tbControleAcesso() {
        tbControleAcesso = rn.tbControleAcesso;
        tbControleAcesso.setName("tbControleAcesso");
        tbControleAcesso.setMaxRowCount(0);
        tbControleAcesso.setWKey("7000188;70002");
        tbControleAcesso.setRatioBatchSize(20);
        getTables().put(tbControleAcesso, "tbControleAcesso");
        tbControleAcesso.applyProperties();
    }

    public EMPRESAS_FUNCOES tbEmpresasFuncoes;

    private void init_tbEmpresasFuncoes() {
        tbEmpresasFuncoes = rn.tbEmpresasFuncoes;
        tbEmpresasFuncoes.setName("tbEmpresasFuncoes");
        tbEmpresasFuncoes.setMaxRowCount(0);
        tbEmpresasFuncoes.setWKey("7000188;70003");
        tbEmpresasFuncoes.setRatioBatchSize(20);
        getTables().put(tbEmpresasFuncoes, "tbEmpresasFuncoes");
        tbEmpresasFuncoes.applyProperties();
    }

    public SISTEMA_ACESSO_FUNCAO tbSistemaAcessoFuncao;

    private void init_tbSistemaAcessoFuncao() {
        tbSistemaAcessoFuncao = rn.tbSistemaAcessoFuncao;
        tbSistemaAcessoFuncao.setName("tbSistemaAcessoFuncao");
        tbSistemaAcessoFuncao.setMaxRowCount(0);
        tbSistemaAcessoFuncao.setWKey("7000188;70004");
        tbSistemaAcessoFuncao.setRatioBatchSize(20);
        getTables().put(tbSistemaAcessoFuncao, "tbSistemaAcessoFuncao");
        tbSistemaAcessoFuncao.applyProperties();
    }

    public PERFIL tbPerfilUsuarios;

    private void init_tbPerfilUsuarios() {
        tbPerfilUsuarios = rn.tbPerfilUsuarios;
        tbPerfilUsuarios.setName("tbPerfilUsuarios");
        tbPerfilUsuarios.setMaxRowCount(0);
        tbPerfilUsuarios.setWKey("7000188;70005");
        tbPerfilUsuarios.setRatioBatchSize(20);
        getTables().put(tbPerfilUsuarios, "tbPerfilUsuarios");
        tbPerfilUsuarios.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(0);
        tbEmpresasUsuarios.setWKey("7000188;70006");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public USUARIO_FOTO tbUsuarioFoto;

    private void init_tbUsuarioFoto() {
        tbUsuarioFoto = rn.tbUsuarioFoto;
        tbUsuarioFoto.setName("tbUsuarioFoto");
        tbUsuarioFoto.setMaxRowCount(0);
        tbUsuarioFoto.setWKey("7000188;70007");
        tbUsuarioFoto.setRatioBatchSize(20);
        getTables().put(tbUsuarioFoto, "tbUsuarioFoto");
        tbUsuarioFoto.applyProperties();
    }

    public TFPopupMenu FPopupMenu1 = new TFPopupMenu();

    private void init_FPopupMenu1() {
        FPopupMenu1.setName("FPopupMenu1");
        FrmControleAcessoGeral.addChildren(FPopupMenu1);
        FPopupMenu1.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("Flag Red");
        FMenuItem1.setImageIndex(7000254);
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem FMenuItem2 = new TFMenuItem();

    private void init_FMenuItem2() {
        FMenuItem2.setName("FMenuItem2");
        FMenuItem2.setCaption("Flag Green");
        FMenuItem2.setImageIndex(7000255);
        FMenuItem2.setAccess(false);
        FMenuItem2.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem2);
        FMenuItem2.applyProperties();
    }

    public TFPopupMenu popGridAgentesFuncao = new TFPopupMenu();

    private void init_popGridAgentesFuncao() {
        popGridAgentesFuncao.setName("popGridAgentesFuncao");
        FrmControleAcessoGeral.addChildren(popGridAgentesFuncao);
        popGridAgentesFuncao.applyProperties();
    }

    public TFMenuItem mmExportarExcelGridAgentesFuncao = new TFMenuItem();

    private void init_mmExportarExcelGridAgentesFuncao() {
        mmExportarExcelGridAgentesFuncao.setName("mmExportarExcelGridAgentesFuncao");
        mmExportarExcelGridAgentesFuncao.setCaption("Exportar Excel");
        mmExportarExcelGridAgentesFuncao.setHint("Exportar Excel");
        mmExportarExcelGridAgentesFuncao.setImageIndex(22004);
        mmExportarExcelGridAgentesFuncao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmExportarExcelGridAgentesFuncaoClick(event);
            processarFlow("FrmControleAcessoGeral", "mmExportarExcelGridAgentesFuncao", "OnClick");
        });
        mmExportarExcelGridAgentesFuncao.setAccess(false);
        mmExportarExcelGridAgentesFuncao.setCheckmark(false);
        popGridAgentesFuncao.addChildren(mmExportarExcelGridAgentesFuncao);
        mmExportarExcelGridAgentesFuncao.applyProperties();
    }

    protected TFForm FrmControleAcessoGeral = this;
    private void init_FrmControleAcessoGeral() {
        FrmControleAcessoGeral.setName("FrmControleAcessoGeral");
        FrmControleAcessoGeral.setCaption("Controle Acesso");
        FrmControleAcessoGeral.setClientHeight(617);
        FrmControleAcessoGeral.setClientWidth(1020);
        FrmControleAcessoGeral.setColor("clBtnFace");
        FrmControleAcessoGeral.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmControleAcessoGeral", "FrmControleAcessoGeral", "OnCreate");
        });
        FrmControleAcessoGeral.setWOrigem("EhMain");
        FrmControleAcessoGeral.setWKey("7000188");
        FrmControleAcessoGeral.setSpacing(0);
        FrmControleAcessoGeral.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(1020);
        FVBox1.setHeight(617);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmControleAcessoGeral.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(1018);
        FHBox7.setHeight(109);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftMin");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox1.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(0);
        FVBox10.setTop(0);
        FVBox10.setWidth(878);
        FVBox10.setHeight(97);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(0);
        FVBox10.setPaddingLeft(0);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(1);
        FVBox10.setFlexVflex("ftMin");
        FVBox10.setFlexHflex("ftTrue");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFHBox hb = new TFHBox();

    private void init_hb() {
        hb.setName("hb");
        hb.setLeft(0);
        hb.setTop(0);
        hb.setWidth(873);
        hb.setHeight(44);
        hb.setAlign("alTop");
        hb.setBorderStyle("stNone");
        hb.setPaddingTop(0);
        hb.setPaddingLeft(0);
        hb.setPaddingRight(0);
        hb.setPaddingBottom(0);
        hb.setMarginTop(0);
        hb.setMarginLeft(0);
        hb.setMarginRight(0);
        hb.setMarginBottom(0);
        hb.setSpacing(5);
        hb.setFlexVflex("ftMin");
        hb.setFlexHflex("ftTrue");
        hb.setScrollable(false);
        hb.setBoxShadowConfigHorizontalLength(10);
        hb.setBoxShadowConfigVerticalLength(10);
        hb.setBoxShadowConfigBlurRadius(5);
        hb.setBoxShadowConfigSpreadRadius(0);
        hb.setBoxShadowConfigShadowColor("clBlack");
        hb.setBoxShadowConfigOpacity(75);
        hb.setVAlign("tvTop");
        FVBox10.addChildren(hb);
        hb.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(453);
        FHBox9.setHeight(41);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        hb.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(0);
        FVBox7.setWidth(48);
        FVBox7.setHeight(40);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(8);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(3);
        FVBox7.setPaddingBottom(0);
        FVBox7.setVisible(false);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftFalse");
        FVBox7.setFlexHflex("ftFalse");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(35);
        FLabel1.setHeight(13);
        FLabel1.setAlign("alRight");
        FLabel1.setCaption("Fun\u00E7\u00E3o");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVisible(false);
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox7.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cbbEmpresasFuncoes = new TFCombo();

    private void init_cbbEmpresasFuncoes() {
        cbbEmpresasFuncoes.setName("cbbEmpresasFuncoes");
        cbbEmpresasFuncoes.setLeft(48);
        cbbEmpresasFuncoes.setTop(0);
        cbbEmpresasFuncoes.setWidth(395);
        cbbEmpresasFuncoes.setHeight(21);
        cbbEmpresasFuncoes.setLookupTable(tbEmpresasFuncoes);
        cbbEmpresasFuncoes.setLookupKey("COD_FUNCAO");
        cbbEmpresasFuncoes.setLookupDesc("DESCRICAO");
        cbbEmpresasFuncoes.setFlex(true);
        cbbEmpresasFuncoes.setReadOnly(true);
        cbbEmpresasFuncoes.setRequired(false);
        cbbEmpresasFuncoes.setPrompt("Selecione a Fun\u00E7\u00E3o");
        cbbEmpresasFuncoes.setConstraintCheckWhen("cwImmediate");
        cbbEmpresasFuncoes.setConstraintCheckType("ctExpression");
        cbbEmpresasFuncoes.setConstraintFocusOnError(false);
        cbbEmpresasFuncoes.setConstraintEnableUI(true);
        cbbEmpresasFuncoes.setConstraintEnabled(false);
        cbbEmpresasFuncoes.setConstraintFormCheck(true);
        cbbEmpresasFuncoes.setClearOnDelKey(true);
        cbbEmpresasFuncoes.setUseClearButton(false);
        cbbEmpresasFuncoes.setHideClearButtonOnNullValue(false);
        cbbEmpresasFuncoes.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbEmpresasFuncoesChange(event);
            processarFlow("FrmControleAcessoGeral", "cbbEmpresasFuncoes", "OnChange");
        });
        FHBox9.addChildren(cbbEmpresasFuncoes);
        cbbEmpresasFuncoes.applyProperties();
        addValidatable(cbbEmpresasFuncoes);
    }

    public TFHBox hBoxCodAcessoDescricao = new TFHBox();

    private void init_hBoxCodAcessoDescricao() {
        hBoxCodAcessoDescricao.setName("hBoxCodAcessoDescricao");
        hBoxCodAcessoDescricao.setLeft(0);
        hBoxCodAcessoDescricao.setTop(45);
        hBoxCodAcessoDescricao.setWidth(873);
        hBoxCodAcessoDescricao.setHeight(41);
        hBoxCodAcessoDescricao.setBorderStyle("stNone");
        hBoxCodAcessoDescricao.setPaddingTop(0);
        hBoxCodAcessoDescricao.setPaddingLeft(0);
        hBoxCodAcessoDescricao.setPaddingRight(0);
        hBoxCodAcessoDescricao.setPaddingBottom(0);
        hBoxCodAcessoDescricao.setMarginTop(0);
        hBoxCodAcessoDescricao.setMarginLeft(0);
        hBoxCodAcessoDescricao.setMarginRight(0);
        hBoxCodAcessoDescricao.setMarginBottom(0);
        hBoxCodAcessoDescricao.setSpacing(1);
        hBoxCodAcessoDescricao.setFlexVflex("ftMin");
        hBoxCodAcessoDescricao.setFlexHflex("ftTrue");
        hBoxCodAcessoDescricao.setScrollable(false);
        hBoxCodAcessoDescricao.setBoxShadowConfigHorizontalLength(10);
        hBoxCodAcessoDescricao.setBoxShadowConfigVerticalLength(10);
        hBoxCodAcessoDescricao.setBoxShadowConfigBlurRadius(5);
        hBoxCodAcessoDescricao.setBoxShadowConfigSpreadRadius(0);
        hBoxCodAcessoDescricao.setBoxShadowConfigShadowColor("clBlack");
        hBoxCodAcessoDescricao.setBoxShadowConfigOpacity(75);
        hBoxCodAcessoDescricao.setVAlign("tvTop");
        FVBox10.addChildren(hBoxCodAcessoDescricao);
        hBoxCodAcessoDescricao.applyProperties();
    }

    public TFString edtPesquisa = new TFString();

    private void init_edtPesquisa() {
        edtPesquisa.setName("edtPesquisa");
        edtPesquisa.setLeft(0);
        edtPesquisa.setTop(0);
        edtPesquisa.setWidth(453);
        edtPesquisa.setHeight(24);
        edtPesquisa.setFlex(true);
        edtPesquisa.setRequired(false);
        edtPesquisa.setPrompt("C\u00F3d Acesso / Descri\u00E7\u00E3o");
        edtPesquisa.setConstraintCheckWhen("cwImmediate");
        edtPesquisa.setConstraintCheckType("ctExpression");
        edtPesquisa.setConstraintFocusOnError(false);
        edtPesquisa.setConstraintEnableUI(true);
        edtPesquisa.setConstraintEnabled(false);
        edtPesquisa.setConstraintFormCheck(true);
        edtPesquisa.setCharCase("ccNormal");
        edtPesquisa.setPwd(false);
        edtPesquisa.setMaxlength(0);
        edtPesquisa.setFontColor("clWindowText");
        edtPesquisa.setFontSize(-13);
        edtPesquisa.setFontName("Tahoma");
        edtPesquisa.setFontStyle("[]");
        edtPesquisa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtPesquisaEnter(event);
            processarFlow("FrmControleAcessoGeral", "edtPesquisa", "OnEnter");
        });
        edtPesquisa.setSaveLiteralCharacter(false);
        edtPesquisa.applyProperties();
        hBoxCodAcessoDescricao.addChildren(edtPesquisa);
        addValidatable(edtPesquisa);
    }

    public TFHBox hBoxCodAcessoDescricaoSeparador01 = new TFHBox();

    private void init_hBoxCodAcessoDescricaoSeparador01() {
        hBoxCodAcessoDescricaoSeparador01.setName("hBoxCodAcessoDescricaoSeparador01");
        hBoxCodAcessoDescricaoSeparador01.setLeft(453);
        hBoxCodAcessoDescricaoSeparador01.setTop(0);
        hBoxCodAcessoDescricaoSeparador01.setWidth(5);
        hBoxCodAcessoDescricaoSeparador01.setHeight(20);
        hBoxCodAcessoDescricaoSeparador01.setBorderStyle("stNone");
        hBoxCodAcessoDescricaoSeparador01.setPaddingTop(0);
        hBoxCodAcessoDescricaoSeparador01.setPaddingLeft(0);
        hBoxCodAcessoDescricaoSeparador01.setPaddingRight(0);
        hBoxCodAcessoDescricaoSeparador01.setPaddingBottom(0);
        hBoxCodAcessoDescricaoSeparador01.setMarginTop(0);
        hBoxCodAcessoDescricaoSeparador01.setMarginLeft(0);
        hBoxCodAcessoDescricaoSeparador01.setMarginRight(0);
        hBoxCodAcessoDescricaoSeparador01.setMarginBottom(0);
        hBoxCodAcessoDescricaoSeparador01.setSpacing(1);
        hBoxCodAcessoDescricaoSeparador01.setFlexVflex("ftFalse");
        hBoxCodAcessoDescricaoSeparador01.setFlexHflex("ftFalse");
        hBoxCodAcessoDescricaoSeparador01.setScrollable(false);
        hBoxCodAcessoDescricaoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxCodAcessoDescricaoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxCodAcessoDescricaoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxCodAcessoDescricaoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxCodAcessoDescricaoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxCodAcessoDescricaoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxCodAcessoDescricaoSeparador01.setVAlign("tvTop");
        hBoxCodAcessoDescricao.addChildren(hBoxCodAcessoDescricaoSeparador01);
        hBoxCodAcessoDescricaoSeparador01.applyProperties();
    }

    public TFIconClass iconClassLimpar = new TFIconClass();

    private void init_iconClassLimpar() {
        iconClassLimpar.setName("iconClassLimpar");
        iconClassLimpar.setLeft(458);
        iconClassLimpar.setTop(0);
        iconClassLimpar.setHint("Limpar");
        iconClassLimpar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassLimparClick(event);
            processarFlow("FrmControleAcessoGeral", "iconClassLimpar", "OnClick");
        });
        iconClassLimpar.setIconClass("trash");
        iconClassLimpar.setSize(26);
        iconClassLimpar.setColor("clBlack");
        hBoxCodAcessoDescricao.addChildren(iconClassLimpar);
        iconClassLimpar.applyProperties();
    }

    public TFVBox FVBox11 = new TFVBox();

    private void init_FVBox11() {
        FVBox11.setName("FVBox11");
        FVBox11.setLeft(878);
        FVBox11.setTop(0);
        FVBox11.setWidth(79);
        FVBox11.setHeight(88);
        FVBox11.setBorderStyle("stNone");
        FVBox11.setPaddingTop(0);
        FVBox11.setPaddingLeft(0);
        FVBox11.setPaddingRight(0);
        FVBox11.setPaddingBottom(0);
        FVBox11.setMarginTop(0);
        FVBox11.setMarginLeft(0);
        FVBox11.setMarginRight(0);
        FVBox11.setMarginBottom(0);
        FVBox11.setSpacing(1);
        FVBox11.setFlexVflex("ftFalse");
        FVBox11.setFlexHflex("ftFalse");
        FVBox11.setScrollable(false);
        FVBox11.setBoxShadowConfigHorizontalLength(10);
        FVBox11.setBoxShadowConfigVerticalLength(10);
        FVBox11.setBoxShadowConfigBlurRadius(5);
        FVBox11.setBoxShadowConfigSpreadRadius(0);
        FVBox11.setBoxShadowConfigShadowColor("clBlack");
        FVBox11.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox11);
        FVBox11.applyProperties();
    }

    public TFVBox vBoxIconClassRefresh = new TFVBox();

    private void init_vBoxIconClassRefresh() {
        vBoxIconClassRefresh.setName("vBoxIconClassRefresh");
        vBoxIconClassRefresh.setLeft(0);
        vBoxIconClassRefresh.setTop(0);
        vBoxIconClassRefresh.setWidth(78);
        vBoxIconClassRefresh.setHeight(85);
        vBoxIconClassRefresh.setAlign("alClient");
        vBoxIconClassRefresh.setBorderStyle("stNone");
        vBoxIconClassRefresh.setPaddingTop(2);
        vBoxIconClassRefresh.setPaddingLeft(7);
        vBoxIconClassRefresh.setPaddingRight(0);
        vBoxIconClassRefresh.setPaddingBottom(0);
        vBoxIconClassRefresh.setMarginTop(0);
        vBoxIconClassRefresh.setMarginLeft(0);
        vBoxIconClassRefresh.setMarginRight(0);
        vBoxIconClassRefresh.setMarginBottom(0);
        vBoxIconClassRefresh.setSpacing(1);
        vBoxIconClassRefresh.setFlexVflex("ftFalse");
        vBoxIconClassRefresh.setFlexHflex("ftFalse");
        vBoxIconClassRefresh.setScrollable(false);
        vBoxIconClassRefresh.setBoxShadowConfigHorizontalLength(10);
        vBoxIconClassRefresh.setBoxShadowConfigVerticalLength(10);
        vBoxIconClassRefresh.setBoxShadowConfigBlurRadius(5);
        vBoxIconClassRefresh.setBoxShadowConfigSpreadRadius(0);
        vBoxIconClassRefresh.setBoxShadowConfigShadowColor("clBlack");
        vBoxIconClassRefresh.setBoxShadowConfigOpacity(75);
        FVBox11.addChildren(vBoxIconClassRefresh);
        vBoxIconClassRefresh.applyProperties();
    }

    public TFButton btnRefresh = new TFButton();

    private void init_btnRefresh() {
        btnRefresh.setName("btnRefresh");
        btnRefresh.setLeft(0);
        btnRefresh.setTop(0);
        btnRefresh.setWidth(69);
        btnRefresh.setHeight(76);
        btnRefresh.setHint("Atualizar");
        btnRefresh.setFontColor("clWindowText");
        btnRefresh.setFontSize(-11);
        btnRefresh.setFontName("Tahoma");
        btnRefresh.setFontStyle("[]");
        btnRefresh.setLayout("blGlyphTop");
        btnRefresh.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnRefreshClick(event);
            processarFlow("FrmControleAcessoGeral", "btnRefresh", "OnClick");
        });
        btnRefresh.setImageId(700092);
        btnRefresh.setColor("clBtnFace");
        btnRefresh.setAccess(false);
        btnRefresh.setIconReverseDirection(false);
        vBoxIconClassRefresh.addChildren(btnRefresh);
        btnRefresh.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(957);
        FHBox8.setTop(0);
        FHBox8.setWidth(45);
        FHBox8.setHeight(41);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(2);
        FHBox8.setPaddingLeft(7);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setVisible(false);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox7.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFIconClass iconClassHelp = new TFIconClass();

    private void init_iconClassHelp() {
        iconClassHelp.setName("iconClassHelp");
        iconClassHelp.setLeft(0);
        iconClassHelp.setTop(0);
        iconClassHelp.setHint("Help");
        iconClassHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassHelpClick(event);
            processarFlow("FrmControleAcessoGeral", "iconClassHelp", "OnClick");
        });
        iconClassHelp.setIconClass("question-circle");
        iconClassHelp.setSize(26);
        iconClassHelp.setColor("clRed");
        FHBox8.addChildren(iconClassHelp);
        iconClassHelp.applyProperties();
    }

    public TFGroupbox grpBoxAgentesFuncao = new TFGroupbox();

    private void init_grpBoxAgentesFuncao() {
        grpBoxAgentesFuncao.setName("grpBoxAgentesFuncao");
        grpBoxAgentesFuncao.setLeft(0);
        grpBoxAgentesFuncao.setTop(110);
        grpBoxAgentesFuncao.setWidth(1133);
        grpBoxAgentesFuncao.setHeight(352);
        grpBoxAgentesFuncao.setCaption("Agentes da Fun\u00E7\u00E3o");
        grpBoxAgentesFuncao.setFontColor("clWindowText");
        grpBoxAgentesFuncao.setFontSize(-11);
        grpBoxAgentesFuncao.setFontName("Tahoma");
        grpBoxAgentesFuncao.setFontStyle("[]");
        grpBoxAgentesFuncao.setFlexVflex("ftFalse");
        grpBoxAgentesFuncao.setFlexHflex("ftTrue");
        grpBoxAgentesFuncao.setScrollable(false);
        grpBoxAgentesFuncao.setClosable(true);
        grpBoxAgentesFuncao.setClosed(true);
        grpBoxAgentesFuncao.setOrient("coHorizontal");
        grpBoxAgentesFuncao.setStyle("grp3D");
        grpBoxAgentesFuncao.setHeaderImageId(31001);
        FVBox1.addChildren(grpBoxAgentesFuncao);
        grpBoxAgentesFuncao.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(2);
        FVBox2.setTop(15);
        FVBox2.setWidth(1129);
        FVBox2.setHeight(335);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftMin");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        grpBoxAgentesFuncao.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox hBoxGrade = new TFHBox();

    private void init_hBoxGrade() {
        hBoxGrade.setName("hBoxGrade");
        hBoxGrade.setLeft(0);
        hBoxGrade.setTop(0);
        hBoxGrade.setWidth(1129);
        hBoxGrade.setHeight(163);
        hBoxGrade.setAlign("alTop");
        hBoxGrade.setBorderStyle("stNone");
        hBoxGrade.setPaddingTop(0);
        hBoxGrade.setPaddingLeft(0);
        hBoxGrade.setPaddingRight(0);
        hBoxGrade.setPaddingBottom(0);
        hBoxGrade.setMarginTop(0);
        hBoxGrade.setMarginLeft(0);
        hBoxGrade.setMarginRight(0);
        hBoxGrade.setMarginBottom(0);
        hBoxGrade.setSpacing(1);
        hBoxGrade.setFlexVflex("ftTrue");
        hBoxGrade.setFlexHflex("ftTrue");
        hBoxGrade.setScrollable(false);
        hBoxGrade.setBoxShadowConfigHorizontalLength(10);
        hBoxGrade.setBoxShadowConfigVerticalLength(10);
        hBoxGrade.setBoxShadowConfigBlurRadius(5);
        hBoxGrade.setBoxShadowConfigSpreadRadius(0);
        hBoxGrade.setBoxShadowConfigShadowColor("clBlack");
        hBoxGrade.setBoxShadowConfigOpacity(75);
        hBoxGrade.setVAlign("tvTop");
        FVBox2.addChildren(hBoxGrade);
        hBoxGrade.applyProperties();
    }

    public TFGrid gridAgentesFuncao = new TFGrid();

    private void init_gridAgentesFuncao() {
        gridAgentesFuncao.setName("gridAgentesFuncao");
        gridAgentesFuncao.setLeft(0);
        gridAgentesFuncao.setTop(0);
        gridAgentesFuncao.setWidth(624);
        gridAgentesFuncao.setHeight(154);
        gridAgentesFuncao.setTable(tbPerfilUsuarios);
        gridAgentesFuncao.setFlexVflex("ftFalse");
        gridAgentesFuncao.setFlexHflex("ftTrue");
        gridAgentesFuncao.setPagingEnabled(false);
        gridAgentesFuncao.setFrozenColumns(0);
        gridAgentesFuncao.setShowFooter(false);
        gridAgentesFuncao.setShowHeader(true);
        gridAgentesFuncao.setMultiSelection(false);
        gridAgentesFuncao.setGroupingEnabled(false);
        gridAgentesFuncao.setGroupingExpanded(false);
        gridAgentesFuncao.setGroupingShowFooter(false);
        gridAgentesFuncao.setCrosstabEnabled(false);
        gridAgentesFuncao.setCrosstabGroupType("cgtConcat");
        gridAgentesFuncao.setEditionEnabled(false);
        gridAgentesFuncao.setContextMenu(popGridAgentesFuncao);
        gridAgentesFuncao.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME");
        item0.setTitleCaption("Login");
        item0.setWidth(100);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NOME_COMPLETO");
        item1.setTitleCaption("Nome Completo");
        item1.setWidth(200);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("CPF");
        item2.setWidth(90);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(true);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("EMPRESA_NOME_CODIGO");
        item3.setTitleCaption("Empresa");
        item3.setWidth(200);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DEPARTAMENTO_DESCRICAO_CODIGO");
        item4.setTitleCaption("Departamento");
        item4.setWidth(200);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DIVISAO_DESCRICAO_CODIGO");
        item5.setTitleCaption("Divis\u00E3o");
        item5.setWidth(200);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("ATIVO");
        item6.setTitleCaption("Ativo");
        item6.setWidth(40);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taCenter");
        item6.setFieldType("ftCheckBox");
        item6.setFlexRatio(0);
        item6.setSort(true);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("EMAIL");
        item7.setTitleCaption("E-mail");
        item7.setWidth(200);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(true);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("CAD_BD");
        item8.setTitleCaption("Cad. BD");
        item8.setWidth(60);
        item8.setVisible(true);
        item8.setPrecision(0);
        item8.setTextAlign("taCenter");
        item8.setFieldType("ftCheckBox");
        item8.setFlexRatio(0);
        item8.setSort(true);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(false);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("TROCAR_SENHA_LOGAR");
        item9.setTitleCaption("Trocar Senha");
        item9.setWidth(90);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taCenter");
        item9.setFieldType("ftCheckBox");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridAgentesFuncao.getColumns().add(item9);
        hBoxGrade.addChildren(gridAgentesFuncao);
        gridAgentesFuncao.applyProperties();
    }

    public TFHBox hBoxGradeSeparador01 = new TFHBox();

    private void init_hBoxGradeSeparador01() {
        hBoxGradeSeparador01.setName("hBoxGradeSeparador01");
        hBoxGradeSeparador01.setLeft(624);
        hBoxGradeSeparador01.setTop(0);
        hBoxGradeSeparador01.setWidth(5);
        hBoxGradeSeparador01.setHeight(150);
        hBoxGradeSeparador01.setBorderStyle("stNone");
        hBoxGradeSeparador01.setPaddingTop(0);
        hBoxGradeSeparador01.setPaddingLeft(0);
        hBoxGradeSeparador01.setPaddingRight(0);
        hBoxGradeSeparador01.setPaddingBottom(0);
        hBoxGradeSeparador01.setMarginTop(0);
        hBoxGradeSeparador01.setMarginLeft(0);
        hBoxGradeSeparador01.setMarginRight(0);
        hBoxGradeSeparador01.setMarginBottom(0);
        hBoxGradeSeparador01.setSpacing(1);
        hBoxGradeSeparador01.setFlexVflex("ftFalse");
        hBoxGradeSeparador01.setFlexHflex("ftFalse");
        hBoxGradeSeparador01.setScrollable(false);
        hBoxGradeSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxGradeSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxGradeSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxGradeSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxGradeSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxGradeSeparador01.setBoxShadowConfigOpacity(75);
        hBoxGradeSeparador01.setVAlign("tvTop");
        hBoxGrade.addChildren(hBoxGradeSeparador01);
        hBoxGradeSeparador01.applyProperties();
    }

    public TFVBox vBoxGradeBtnPesquisaAvancada = new TFVBox();

    private void init_vBoxGradeBtnPesquisaAvancada() {
        vBoxGradeBtnPesquisaAvancada.setName("vBoxGradeBtnPesquisaAvancada");
        vBoxGradeBtnPesquisaAvancada.setLeft(629);
        vBoxGradeBtnPesquisaAvancada.setTop(0);
        vBoxGradeBtnPesquisaAvancada.setWidth(25);
        vBoxGradeBtnPesquisaAvancada.setHeight(150);
        vBoxGradeBtnPesquisaAvancada.setBorderStyle("stNone");
        vBoxGradeBtnPesquisaAvancada.setPaddingTop(0);
        vBoxGradeBtnPesquisaAvancada.setPaddingLeft(0);
        vBoxGradeBtnPesquisaAvancada.setPaddingRight(0);
        vBoxGradeBtnPesquisaAvancada.setPaddingBottom(0);
        vBoxGradeBtnPesquisaAvancada.setMarginTop(0);
        vBoxGradeBtnPesquisaAvancada.setMarginLeft(0);
        vBoxGradeBtnPesquisaAvancada.setMarginRight(0);
        vBoxGradeBtnPesquisaAvancada.setMarginBottom(0);
        vBoxGradeBtnPesquisaAvancada.setSpacing(1);
        vBoxGradeBtnPesquisaAvancada.setFlexVflex("ftFalse");
        vBoxGradeBtnPesquisaAvancada.setFlexHflex("ftFalse");
        vBoxGradeBtnPesquisaAvancada.setScrollable(false);
        vBoxGradeBtnPesquisaAvancada.setBoxShadowConfigHorizontalLength(10);
        vBoxGradeBtnPesquisaAvancada.setBoxShadowConfigVerticalLength(10);
        vBoxGradeBtnPesquisaAvancada.setBoxShadowConfigBlurRadius(5);
        vBoxGradeBtnPesquisaAvancada.setBoxShadowConfigSpreadRadius(0);
        vBoxGradeBtnPesquisaAvancada.setBoxShadowConfigShadowColor("clBlack");
        vBoxGradeBtnPesquisaAvancada.setBoxShadowConfigOpacity(75);
        hBoxGrade.addChildren(vBoxGradeBtnPesquisaAvancada);
        vBoxGradeBtnPesquisaAvancada.applyProperties();
    }

    public TFHBox hBoxGradeBtnPesquisaAvancadaSeparador01 = new TFHBox();

    private void init_hBoxGradeBtnPesquisaAvancadaSeparador01() {
        hBoxGradeBtnPesquisaAvancadaSeparador01.setName("hBoxGradeBtnPesquisaAvancadaSeparador01");
        hBoxGradeBtnPesquisaAvancadaSeparador01.setLeft(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setTop(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setWidth(20);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setHeight(60);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setBorderStyle("stNone");
        hBoxGradeBtnPesquisaAvancadaSeparador01.setPaddingTop(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setPaddingLeft(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setPaddingRight(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setPaddingBottom(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setMarginTop(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setMarginLeft(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setMarginRight(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setMarginBottom(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setSpacing(1);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setFlexVflex("ftFalse");
        hBoxGradeBtnPesquisaAvancadaSeparador01.setFlexHflex("ftFalse");
        hBoxGradeBtnPesquisaAvancadaSeparador01.setScrollable(false);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxGradeBtnPesquisaAvancadaSeparador01.setBoxShadowConfigOpacity(75);
        hBoxGradeBtnPesquisaAvancadaSeparador01.setVAlign("tvTop");
        vBoxGradeBtnPesquisaAvancada.addChildren(hBoxGradeBtnPesquisaAvancadaSeparador01);
        hBoxGradeBtnPesquisaAvancadaSeparador01.applyProperties();
    }

    public TFButton btnPesquisaAvancadaAgentesFuncao = new TFButton();

    private void init_btnPesquisaAvancadaAgentesFuncao() {
        btnPesquisaAvancadaAgentesFuncao.setName("btnPesquisaAvancadaAgentesFuncao");
        btnPesquisaAvancadaAgentesFuncao.setLeft(0);
        btnPesquisaAvancadaAgentesFuncao.setTop(61);
        btnPesquisaAvancadaAgentesFuncao.setWidth(20);
        btnPesquisaAvancadaAgentesFuncao.setHeight(20);
        btnPesquisaAvancadaAgentesFuncao.setHint("Pesquisa avan\u00E7ada");
        btnPesquisaAvancadaAgentesFuncao.setFontColor("clWindowText");
        btnPesquisaAvancadaAgentesFuncao.setFontSize(-11);
        btnPesquisaAvancadaAgentesFuncao.setFontName("Tahoma");
        btnPesquisaAvancadaAgentesFuncao.setFontStyle("[]");
        btnPesquisaAvancadaAgentesFuncao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisaAvancadaAgentesFuncaoClick(event);
            processarFlow("FrmControleAcessoGeral", "btnPesquisaAvancadaAgentesFuncao", "OnClick");
        });
        btnPesquisaAvancadaAgentesFuncao.setImageId(5300469);
        btnPesquisaAvancadaAgentesFuncao.setColor("clWindow");
        btnPesquisaAvancadaAgentesFuncao.setAccess(false);
        btnPesquisaAvancadaAgentesFuncao.setIconReverseDirection(false);
        vBoxGradeBtnPesquisaAvancada.addChildren(btnPesquisaAvancadaAgentesFuncao);
        btnPesquisaAvancadaAgentesFuncao.applyProperties();
    }

    public TFHBox hBoxGradeBtnPesquisaAvancadaSeparador02 = new TFHBox();

    private void init_hBoxGradeBtnPesquisaAvancadaSeparador02() {
        hBoxGradeBtnPesquisaAvancadaSeparador02.setName("hBoxGradeBtnPesquisaAvancadaSeparador02");
        hBoxGradeBtnPesquisaAvancadaSeparador02.setLeft(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setTop(82);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setWidth(20);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setHeight(5);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setBorderStyle("stNone");
        hBoxGradeBtnPesquisaAvancadaSeparador02.setPaddingTop(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setPaddingLeft(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setPaddingRight(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setPaddingBottom(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setMarginTop(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setMarginLeft(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setMarginRight(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setMarginBottom(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setSpacing(1);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setFlexVflex("ftFalse");
        hBoxGradeBtnPesquisaAvancadaSeparador02.setFlexHflex("ftFalse");
        hBoxGradeBtnPesquisaAvancadaSeparador02.setScrollable(false);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxGradeBtnPesquisaAvancadaSeparador02.setBoxShadowConfigOpacity(75);
        hBoxGradeBtnPesquisaAvancadaSeparador02.setVAlign("tvTop");
        vBoxGradeBtnPesquisaAvancada.addChildren(hBoxGradeBtnPesquisaAvancadaSeparador02);
        hBoxGradeBtnPesquisaAvancadaSeparador02.applyProperties();
    }

    public TFButton btnLimparPesquisaAvancada = new TFButton();

    private void init_btnLimparPesquisaAvancada() {
        btnLimparPesquisaAvancada.setName("btnLimparPesquisaAvancada");
        btnLimparPesquisaAvancada.setLeft(0);
        btnLimparPesquisaAvancada.setTop(88);
        btnLimparPesquisaAvancada.setWidth(20);
        btnLimparPesquisaAvancada.setHeight(20);
        btnLimparPesquisaAvancada.setHint("Limpar pesquisa avan\u00E7ada");
        btnLimparPesquisaAvancada.setFontColor("clWindowText");
        btnLimparPesquisaAvancada.setFontSize(-11);
        btnLimparPesquisaAvancada.setFontName("Tahoma");
        btnLimparPesquisaAvancada.setFontStyle("[]");
        btnLimparPesquisaAvancada.setVisible(false);
        btnLimparPesquisaAvancada.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimparPesquisaAvancadaClick(event);
            processarFlow("FrmControleAcessoGeral", "btnLimparPesquisaAvancada", "OnClick");
        });
        btnLimparPesquisaAvancada.setImageId(4600235);
        btnLimparPesquisaAvancada.setColor("clWindow");
        btnLimparPesquisaAvancada.setAccess(false);
        btnLimparPesquisaAvancada.setIconReverseDirection(false);
        vBoxGradeBtnPesquisaAvancada.addChildren(btnLimparPesquisaAvancada);
        btnLimparPesquisaAvancada.applyProperties();
    }

    public TFHBox hBoxGradeSeparador02 = new TFHBox();

    private void init_hBoxGradeSeparador02() {
        hBoxGradeSeparador02.setName("hBoxGradeSeparador02");
        hBoxGradeSeparador02.setLeft(654);
        hBoxGradeSeparador02.setTop(0);
        hBoxGradeSeparador02.setWidth(5);
        hBoxGradeSeparador02.setHeight(150);
        hBoxGradeSeparador02.setBorderStyle("stNone");
        hBoxGradeSeparador02.setPaddingTop(0);
        hBoxGradeSeparador02.setPaddingLeft(0);
        hBoxGradeSeparador02.setPaddingRight(0);
        hBoxGradeSeparador02.setPaddingBottom(0);
        hBoxGradeSeparador02.setMarginTop(0);
        hBoxGradeSeparador02.setMarginLeft(0);
        hBoxGradeSeparador02.setMarginRight(0);
        hBoxGradeSeparador02.setMarginBottom(0);
        hBoxGradeSeparador02.setSpacing(1);
        hBoxGradeSeparador02.setFlexVflex("ftFalse");
        hBoxGradeSeparador02.setFlexHflex("ftFalse");
        hBoxGradeSeparador02.setScrollable(false);
        hBoxGradeSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxGradeSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxGradeSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxGradeSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxGradeSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxGradeSeparador02.setBoxShadowConfigOpacity(75);
        hBoxGradeSeparador02.setVAlign("tvTop");
        hBoxGrade.addChildren(hBoxGradeSeparador02);
        hBoxGradeSeparador02.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(164);
        FVBox3.setWidth(973);
        FVBox3.setHeight(191);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(5);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftMin");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FVBox2.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(187);
        FHBox2.setHeight(45);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(5);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox3.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnEditar = new TFButton();

    private void init_btnEditar() {
        btnEditar.setName("btnEditar");
        btnEditar.setLeft(0);
        btnEditar.setTop(0);
        btnEditar.setWidth(40);
        btnEditar.setHeight(40);
        btnEditar.setFontColor("clWindowText");
        btnEditar.setFontSize(-11);
        btnEditar.setFontName("Tahoma");
        btnEditar.setFontStyle("[]");
        btnEditar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnEditarClick(event);
            processarFlow("FrmControleAcessoGeral", "btnEditar", "OnClick");
        });
        btnEditar.setImageId(7000136);
        btnEditar.setColor("clBtnFace");
        btnEditar.setAccess(false);
        btnEditar.setIconReverseDirection(false);
        FHBox2.addChildren(btnEditar);
        btnEditar.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(40);
        FVBox4.setTop(0);
        FVBox4.setWidth(5);
        FVBox4.setHeight(40);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(45);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(40);
        btnSalvar.setHeight(40);
        btnSalvar.setHint("Salvar");
        btnSalvar.setEnabled(false);
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmControleAcessoGeral", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(310032);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox2.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(85);
        FVBox5.setTop(0);
        FVBox5.setWidth(5);
        FVBox5.setHeight(40);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(90);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(40);
        btnCancelar.setHeight(40);
        btnCancelar.setHint("Cancelar");
        btnCancelar.setEnabled(false);
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmControleAcessoGeral", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(310035);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox2.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(130);
        FVBox6.setTop(0);
        FVBox6.setWidth(5);
        FVBox6.setHeight(40);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftFalse");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFButton btnEditarPerfil = new TFButton();

    private void init_btnEditarPerfil() {
        btnEditarPerfil.setName("btnEditarPerfil");
        btnEditarPerfil.setLeft(135);
        btnEditarPerfil.setTop(0);
        btnEditarPerfil.setWidth(40);
        btnEditarPerfil.setHeight(40);
        btnEditarPerfil.setHint("Editar perfil");
        btnEditarPerfil.setFontColor("clWindowText");
        btnEditarPerfil.setFontSize(-11);
        btnEditarPerfil.setFontName("Tahoma");
        btnEditarPerfil.setFontStyle("[]");
        btnEditarPerfil.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnEditarPerfilClick(event);
            processarFlow("FrmControleAcessoGeral", "btnEditarPerfil", "OnClick");
        });
        btnEditarPerfil.setImageId(0);
        btnEditarPerfil.setColor("clBtnFace");
        btnEditarPerfil.setAccess(false);
        btnEditarPerfil.setIconClass("user");
        btnEditarPerfil.setIconReverseDirection(false);
        FHBox2.addChildren(btnEditarPerfil);
        btnEditarPerfil.applyProperties();
    }

    public TFHBox bBoxEdicao = new TFHBox();

    private void init_bBoxEdicao() {
        bBoxEdicao.setName("bBoxEdicao");
        bBoxEdicao.setLeft(0);
        bBoxEdicao.setTop(46);
        bBoxEdicao.setWidth(896);
        bBoxEdicao.setHeight(139);
        bBoxEdicao.setBorderStyle("stNone");
        bBoxEdicao.setPaddingTop(0);
        bBoxEdicao.setPaddingLeft(0);
        bBoxEdicao.setPaddingRight(0);
        bBoxEdicao.setPaddingBottom(0);
        bBoxEdicao.setMarginTop(0);
        bBoxEdicao.setMarginLeft(0);
        bBoxEdicao.setMarginRight(0);
        bBoxEdicao.setMarginBottom(0);
        bBoxEdicao.setSpacing(1);
        bBoxEdicao.setFlexVflex("ftMin");
        bBoxEdicao.setFlexHflex("ftTrue");
        bBoxEdicao.setScrollable(false);
        bBoxEdicao.setBoxShadowConfigHorizontalLength(10);
        bBoxEdicao.setBoxShadowConfigVerticalLength(10);
        bBoxEdicao.setBoxShadowConfigBlurRadius(5);
        bBoxEdicao.setBoxShadowConfigSpreadRadius(0);
        bBoxEdicao.setBoxShadowConfigShadowColor("clBlack");
        bBoxEdicao.setBoxShadowConfigOpacity(75);
        bBoxEdicao.setVAlign("tvTop");
        FVBox3.addChildren(bBoxEdicao);
        bBoxEdicao.applyProperties();
    }

    public TFVBox bBoxEdicaoSeparador01 = new TFVBox();

    private void init_bBoxEdicaoSeparador01() {
        bBoxEdicaoSeparador01.setName("bBoxEdicaoSeparador01");
        bBoxEdicaoSeparador01.setLeft(0);
        bBoxEdicaoSeparador01.setTop(0);
        bBoxEdicaoSeparador01.setWidth(5);
        bBoxEdicaoSeparador01.setHeight(68);
        bBoxEdicaoSeparador01.setBorderStyle("stNone");
        bBoxEdicaoSeparador01.setPaddingTop(0);
        bBoxEdicaoSeparador01.setPaddingLeft(0);
        bBoxEdicaoSeparador01.setPaddingRight(0);
        bBoxEdicaoSeparador01.setPaddingBottom(0);
        bBoxEdicaoSeparador01.setMarginTop(0);
        bBoxEdicaoSeparador01.setMarginLeft(0);
        bBoxEdicaoSeparador01.setMarginRight(0);
        bBoxEdicaoSeparador01.setMarginBottom(0);
        bBoxEdicaoSeparador01.setSpacing(1);
        bBoxEdicaoSeparador01.setFlexVflex("ftFalse");
        bBoxEdicaoSeparador01.setFlexHflex("ftFalse");
        bBoxEdicaoSeparador01.setScrollable(false);
        bBoxEdicaoSeparador01.setBoxShadowConfigHorizontalLength(10);
        bBoxEdicaoSeparador01.setBoxShadowConfigVerticalLength(10);
        bBoxEdicaoSeparador01.setBoxShadowConfigBlurRadius(5);
        bBoxEdicaoSeparador01.setBoxShadowConfigSpreadRadius(0);
        bBoxEdicaoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        bBoxEdicaoSeparador01.setBoxShadowConfigOpacity(75);
        bBoxEdicao.addChildren(bBoxEdicaoSeparador01);
        bBoxEdicaoSeparador01.applyProperties();
    }

    public TFVBox vBoxCamposAgentesFuncao = new TFVBox();

    private void init_vBoxCamposAgentesFuncao() {
        vBoxCamposAgentesFuncao.setName("vBoxCamposAgentesFuncao");
        vBoxCamposAgentesFuncao.setLeft(5);
        vBoxCamposAgentesFuncao.setTop(0);
        vBoxCamposAgentesFuncao.setWidth(350);
        vBoxCamposAgentesFuncao.setHeight(120);
        vBoxCamposAgentesFuncao.setBorderStyle("stNone");
        vBoxCamposAgentesFuncao.setPaddingTop(0);
        vBoxCamposAgentesFuncao.setPaddingLeft(0);
        vBoxCamposAgentesFuncao.setPaddingRight(0);
        vBoxCamposAgentesFuncao.setPaddingBottom(0);
        vBoxCamposAgentesFuncao.setMarginTop(0);
        vBoxCamposAgentesFuncao.setMarginLeft(0);
        vBoxCamposAgentesFuncao.setMarginRight(0);
        vBoxCamposAgentesFuncao.setMarginBottom(0);
        vBoxCamposAgentesFuncao.setSpacing(1);
        vBoxCamposAgentesFuncao.setFlexVflex("ftMin");
        vBoxCamposAgentesFuncao.setFlexHflex("ftFalse");
        vBoxCamposAgentesFuncao.setScrollable(false);
        vBoxCamposAgentesFuncao.setBoxShadowConfigHorizontalLength(10);
        vBoxCamposAgentesFuncao.setBoxShadowConfigVerticalLength(10);
        vBoxCamposAgentesFuncao.setBoxShadowConfigBlurRadius(5);
        vBoxCamposAgentesFuncao.setBoxShadowConfigSpreadRadius(0);
        vBoxCamposAgentesFuncao.setBoxShadowConfigShadowColor("clBlack");
        vBoxCamposAgentesFuncao.setBoxShadowConfigOpacity(75);
        bBoxEdicao.addChildren(vBoxCamposAgentesFuncao);
        vBoxCamposAgentesFuncao.applyProperties();
    }

    public TFHBox hBoxCamposAgentesFuncaoSeparador01 = new TFHBox();

    private void init_hBoxCamposAgentesFuncaoSeparador01() {
        hBoxCamposAgentesFuncaoSeparador01.setName("hBoxCamposAgentesFuncaoSeparador01");
        hBoxCamposAgentesFuncaoSeparador01.setLeft(0);
        hBoxCamposAgentesFuncaoSeparador01.setTop(0);
        hBoxCamposAgentesFuncaoSeparador01.setWidth(340);
        hBoxCamposAgentesFuncaoSeparador01.setHeight(5);
        hBoxCamposAgentesFuncaoSeparador01.setBorderStyle("stNone");
        hBoxCamposAgentesFuncaoSeparador01.setPaddingTop(0);
        hBoxCamposAgentesFuncaoSeparador01.setPaddingLeft(0);
        hBoxCamposAgentesFuncaoSeparador01.setPaddingRight(0);
        hBoxCamposAgentesFuncaoSeparador01.setPaddingBottom(0);
        hBoxCamposAgentesFuncaoSeparador01.setMarginTop(0);
        hBoxCamposAgentesFuncaoSeparador01.setMarginLeft(0);
        hBoxCamposAgentesFuncaoSeparador01.setMarginRight(0);
        hBoxCamposAgentesFuncaoSeparador01.setMarginBottom(0);
        hBoxCamposAgentesFuncaoSeparador01.setSpacing(1);
        hBoxCamposAgentesFuncaoSeparador01.setFlexVflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador01.setFlexHflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador01.setScrollable(false);
        hBoxCamposAgentesFuncaoSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxCamposAgentesFuncaoSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxCamposAgentesFuncaoSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxCamposAgentesFuncaoSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxCamposAgentesFuncaoSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxCamposAgentesFuncaoSeparador01.setBoxShadowConfigOpacity(75);
        hBoxCamposAgentesFuncaoSeparador01.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxCamposAgentesFuncaoSeparador01);
        hBoxCamposAgentesFuncaoSeparador01.applyProperties();
    }

    public TFHBox hBoxFone = new TFHBox();

    private void init_hBoxFone() {
        hBoxFone.setName("hBoxFone");
        hBoxFone.setLeft(0);
        hBoxFone.setTop(6);
        hBoxFone.setWidth(340);
        hBoxFone.setHeight(35);
        hBoxFone.setBorderStyle("stNone");
        hBoxFone.setPaddingTop(0);
        hBoxFone.setPaddingLeft(0);
        hBoxFone.setPaddingRight(0);
        hBoxFone.setPaddingBottom(0);
        hBoxFone.setMarginTop(0);
        hBoxFone.setMarginLeft(0);
        hBoxFone.setMarginRight(0);
        hBoxFone.setMarginBottom(0);
        hBoxFone.setSpacing(1);
        hBoxFone.setFlexVflex("ftMin");
        hBoxFone.setFlexHflex("ftTrue");
        hBoxFone.setScrollable(false);
        hBoxFone.setBoxShadowConfigHorizontalLength(10);
        hBoxFone.setBoxShadowConfigVerticalLength(10);
        hBoxFone.setBoxShadowConfigBlurRadius(5);
        hBoxFone.setBoxShadowConfigSpreadRadius(0);
        hBoxFone.setBoxShadowConfigShadowColor("clBlack");
        hBoxFone.setBoxShadowConfigOpacity(75);
        hBoxFone.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxFone);
        hBoxFone.applyProperties();
    }

    public TFVBox vBoxFone = new TFVBox();

    private void init_vBoxFone() {
        vBoxFone.setName("vBoxFone");
        vBoxFone.setLeft(0);
        vBoxFone.setTop(0);
        vBoxFone.setWidth(80);
        vBoxFone.setHeight(30);
        vBoxFone.setBorderStyle("stNone");
        vBoxFone.setPaddingTop(0);
        vBoxFone.setPaddingLeft(0);
        vBoxFone.setPaddingRight(0);
        vBoxFone.setPaddingBottom(0);
        vBoxFone.setMarginTop(0);
        vBoxFone.setMarginLeft(0);
        vBoxFone.setMarginRight(0);
        vBoxFone.setMarginBottom(0);
        vBoxFone.setSpacing(1);
        vBoxFone.setFlexVflex("ftMin");
        vBoxFone.setFlexHflex("ftFalse");
        vBoxFone.setScrollable(false);
        vBoxFone.setBoxShadowConfigHorizontalLength(10);
        vBoxFone.setBoxShadowConfigVerticalLength(10);
        vBoxFone.setBoxShadowConfigBlurRadius(5);
        vBoxFone.setBoxShadowConfigSpreadRadius(0);
        vBoxFone.setBoxShadowConfigShadowColor("clBlack");
        vBoxFone.setBoxShadowConfigOpacity(75);
        hBoxFone.addChildren(vBoxFone);
        vBoxFone.applyProperties();
    }

    public TFHBox hBoxFoneSeparador01 = new TFHBox();

    private void init_hBoxFoneSeparador01() {
        hBoxFoneSeparador01.setName("hBoxFoneSeparador01");
        hBoxFoneSeparador01.setLeft(0);
        hBoxFoneSeparador01.setTop(0);
        hBoxFoneSeparador01.setWidth(20);
        hBoxFoneSeparador01.setHeight(5);
        hBoxFoneSeparador01.setBorderStyle("stNone");
        hBoxFoneSeparador01.setPaddingTop(0);
        hBoxFoneSeparador01.setPaddingLeft(0);
        hBoxFoneSeparador01.setPaddingRight(0);
        hBoxFoneSeparador01.setPaddingBottom(0);
        hBoxFoneSeparador01.setMarginTop(0);
        hBoxFoneSeparador01.setMarginLeft(0);
        hBoxFoneSeparador01.setMarginRight(0);
        hBoxFoneSeparador01.setMarginBottom(0);
        hBoxFoneSeparador01.setSpacing(1);
        hBoxFoneSeparador01.setFlexVflex("ftFalse");
        hBoxFoneSeparador01.setFlexHflex("ftFalse");
        hBoxFoneSeparador01.setScrollable(false);
        hBoxFoneSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxFoneSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxFoneSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxFoneSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxFoneSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxFoneSeparador01.setBoxShadowConfigOpacity(75);
        hBoxFoneSeparador01.setVAlign("tvTop");
        vBoxFone.addChildren(hBoxFoneSeparador01);
        hBoxFoneSeparador01.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(6);
        FLabel2.setWidth(24);
        FLabel2.setHeight(13);
        FLabel2.setAlign("alLeft");
        FLabel2.setCaption("Fone");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        vBoxFone.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFString edtFone = new TFString();

    private void init_edtFone() {
        edtFone.setName("edtFone");
        edtFone.setLeft(80);
        edtFone.setTop(0);
        edtFone.setWidth(200);
        edtFone.setHeight(24);
        edtFone.setTable(tbPerfilUsuarios);
        edtFone.setFieldName("FONE");
        edtFone.setFlex(true);
        edtFone.setRequired(false);
        edtFone.setConstraintCheckWhen("cwImmediate");
        edtFone.setConstraintCheckType("ctExpression");
        edtFone.setConstraintFocusOnError(false);
        edtFone.setConstraintEnableUI(true);
        edtFone.setConstraintEnabled(false);
        edtFone.setConstraintFormCheck(true);
        edtFone.setCharCase("ccNormal");
        edtFone.setPwd(false);
        edtFone.setMaxlength(0);
        edtFone.setEnabled(false);
        edtFone.setFontColor("clWindowText");
        edtFone.setFontSize(-13);
        edtFone.setFontName("Tahoma");
        edtFone.setFontStyle("[]");
        edtFone.setAlignment("taRightJustify");
        edtFone.setSaveLiteralCharacter(false);
        edtFone.applyProperties();
        hBoxFone.addChildren(edtFone);
        addValidatable(edtFone);
    }

    public TFHBox hBoxCamposAgentesFuncaoSeparador02 = new TFHBox();

    private void init_hBoxCamposAgentesFuncaoSeparador02() {
        hBoxCamposAgentesFuncaoSeparador02.setName("hBoxCamposAgentesFuncaoSeparador02");
        hBoxCamposAgentesFuncaoSeparador02.setLeft(0);
        hBoxCamposAgentesFuncaoSeparador02.setTop(42);
        hBoxCamposAgentesFuncaoSeparador02.setWidth(340);
        hBoxCamposAgentesFuncaoSeparador02.setHeight(5);
        hBoxCamposAgentesFuncaoSeparador02.setBorderStyle("stNone");
        hBoxCamposAgentesFuncaoSeparador02.setPaddingTop(0);
        hBoxCamposAgentesFuncaoSeparador02.setPaddingLeft(0);
        hBoxCamposAgentesFuncaoSeparador02.setPaddingRight(0);
        hBoxCamposAgentesFuncaoSeparador02.setPaddingBottom(0);
        hBoxCamposAgentesFuncaoSeparador02.setMarginTop(0);
        hBoxCamposAgentesFuncaoSeparador02.setMarginLeft(0);
        hBoxCamposAgentesFuncaoSeparador02.setMarginRight(0);
        hBoxCamposAgentesFuncaoSeparador02.setMarginBottom(0);
        hBoxCamposAgentesFuncaoSeparador02.setSpacing(1);
        hBoxCamposAgentesFuncaoSeparador02.setFlexVflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador02.setFlexHflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador02.setScrollable(false);
        hBoxCamposAgentesFuncaoSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxCamposAgentesFuncaoSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxCamposAgentesFuncaoSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxCamposAgentesFuncaoSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxCamposAgentesFuncaoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxCamposAgentesFuncaoSeparador02.setBoxShadowConfigOpacity(75);
        hBoxCamposAgentesFuncaoSeparador02.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxCamposAgentesFuncaoSeparador02);
        hBoxCamposAgentesFuncaoSeparador02.applyProperties();
    }

    public TFHBox hBoxRamal = new TFHBox();

    private void init_hBoxRamal() {
        hBoxRamal.setName("hBoxRamal");
        hBoxRamal.setLeft(0);
        hBoxRamal.setTop(48);
        hBoxRamal.setWidth(340);
        hBoxRamal.setHeight(35);
        hBoxRamal.setBorderStyle("stNone");
        hBoxRamal.setPaddingTop(0);
        hBoxRamal.setPaddingLeft(0);
        hBoxRamal.setPaddingRight(0);
        hBoxRamal.setPaddingBottom(0);
        hBoxRamal.setMarginTop(0);
        hBoxRamal.setMarginLeft(0);
        hBoxRamal.setMarginRight(0);
        hBoxRamal.setMarginBottom(0);
        hBoxRamal.setSpacing(1);
        hBoxRamal.setFlexVflex("ftMin");
        hBoxRamal.setFlexHflex("ftTrue");
        hBoxRamal.setScrollable(false);
        hBoxRamal.setBoxShadowConfigHorizontalLength(10);
        hBoxRamal.setBoxShadowConfigVerticalLength(10);
        hBoxRamal.setBoxShadowConfigBlurRadius(5);
        hBoxRamal.setBoxShadowConfigSpreadRadius(0);
        hBoxRamal.setBoxShadowConfigShadowColor("clBlack");
        hBoxRamal.setBoxShadowConfigOpacity(75);
        hBoxRamal.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxRamal);
        hBoxRamal.applyProperties();
    }

    public TFVBox vBoxRamal = new TFVBox();

    private void init_vBoxRamal() {
        vBoxRamal.setName("vBoxRamal");
        vBoxRamal.setLeft(0);
        vBoxRamal.setTop(0);
        vBoxRamal.setWidth(80);
        vBoxRamal.setHeight(30);
        vBoxRamal.setBorderStyle("stNone");
        vBoxRamal.setPaddingTop(0);
        vBoxRamal.setPaddingLeft(0);
        vBoxRamal.setPaddingRight(0);
        vBoxRamal.setPaddingBottom(0);
        vBoxRamal.setMarginTop(0);
        vBoxRamal.setMarginLeft(0);
        vBoxRamal.setMarginRight(0);
        vBoxRamal.setMarginBottom(0);
        vBoxRamal.setSpacing(1);
        vBoxRamal.setFlexVflex("ftMin");
        vBoxRamal.setFlexHflex("ftFalse");
        vBoxRamal.setScrollable(false);
        vBoxRamal.setBoxShadowConfigHorizontalLength(10);
        vBoxRamal.setBoxShadowConfigVerticalLength(10);
        vBoxRamal.setBoxShadowConfigBlurRadius(5);
        vBoxRamal.setBoxShadowConfigSpreadRadius(0);
        vBoxRamal.setBoxShadowConfigShadowColor("clBlack");
        vBoxRamal.setBoxShadowConfigOpacity(75);
        hBoxRamal.addChildren(vBoxRamal);
        vBoxRamal.applyProperties();
    }

    public TFHBox hBoxRamalSeparador01 = new TFHBox();

    private void init_hBoxRamalSeparador01() {
        hBoxRamalSeparador01.setName("hBoxRamalSeparador01");
        hBoxRamalSeparador01.setLeft(0);
        hBoxRamalSeparador01.setTop(0);
        hBoxRamalSeparador01.setWidth(20);
        hBoxRamalSeparador01.setHeight(5);
        hBoxRamalSeparador01.setBorderStyle("stNone");
        hBoxRamalSeparador01.setPaddingTop(0);
        hBoxRamalSeparador01.setPaddingLeft(0);
        hBoxRamalSeparador01.setPaddingRight(0);
        hBoxRamalSeparador01.setPaddingBottom(0);
        hBoxRamalSeparador01.setMarginTop(0);
        hBoxRamalSeparador01.setMarginLeft(0);
        hBoxRamalSeparador01.setMarginRight(0);
        hBoxRamalSeparador01.setMarginBottom(0);
        hBoxRamalSeparador01.setSpacing(1);
        hBoxRamalSeparador01.setFlexVflex("ftFalse");
        hBoxRamalSeparador01.setFlexHflex("ftFalse");
        hBoxRamalSeparador01.setScrollable(false);
        hBoxRamalSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxRamalSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxRamalSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxRamalSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxRamalSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxRamalSeparador01.setBoxShadowConfigOpacity(75);
        hBoxRamalSeparador01.setVAlign("tvTop");
        vBoxRamal.addChildren(hBoxRamalSeparador01);
        hBoxRamalSeparador01.applyProperties();
    }

    public TFLabel lblRamal = new TFLabel();

    private void init_lblRamal() {
        lblRamal.setName("lblRamal");
        lblRamal.setLeft(0);
        lblRamal.setTop(6);
        lblRamal.setWidth(29);
        lblRamal.setHeight(13);
        lblRamal.setAlign("alLeft");
        lblRamal.setCaption("Ramal");
        lblRamal.setFontColor("clWindowText");
        lblRamal.setFontSize(-11);
        lblRamal.setFontName("Tahoma");
        lblRamal.setFontStyle("[]");
        lblRamal.setVerticalAlignment("taVerticalCenter");
        lblRamal.setWordBreak(false);
        vBoxRamal.addChildren(lblRamal);
        lblRamal.applyProperties();
    }

    public TFString edtRamal = new TFString();

    private void init_edtRamal() {
        edtRamal.setName("edtRamal");
        edtRamal.setLeft(80);
        edtRamal.setTop(0);
        edtRamal.setWidth(200);
        edtRamal.setHeight(24);
        edtRamal.setTable(tbPerfilUsuarios);
        edtRamal.setFieldName("RAMAL");
        edtRamal.setFlex(true);
        edtRamal.setRequired(false);
        edtRamal.setConstraintCheckWhen("cwImmediate");
        edtRamal.setConstraintCheckType("ctExpression");
        edtRamal.setConstraintFocusOnError(false);
        edtRamal.setConstraintEnableUI(true);
        edtRamal.setConstraintEnabled(false);
        edtRamal.setConstraintFormCheck(true);
        edtRamal.setCharCase("ccNormal");
        edtRamal.setPwd(false);
        edtRamal.setMaxlength(0);
        edtRamal.setEnabled(false);
        edtRamal.setFontColor("clWindowText");
        edtRamal.setFontSize(-13);
        edtRamal.setFontName("Tahoma");
        edtRamal.setFontStyle("[]");
        edtRamal.setAlignment("taRightJustify");
        edtRamal.setSaveLiteralCharacter(false);
        edtRamal.applyProperties();
        hBoxRamal.addChildren(edtRamal);
        addValidatable(edtRamal);
    }

    public TFHBox hBoxCamposAgentesFuncaoSeparador03 = new TFHBox();

    private void init_hBoxCamposAgentesFuncaoSeparador03() {
        hBoxCamposAgentesFuncaoSeparador03.setName("hBoxCamposAgentesFuncaoSeparador03");
        hBoxCamposAgentesFuncaoSeparador03.setLeft(0);
        hBoxCamposAgentesFuncaoSeparador03.setTop(84);
        hBoxCamposAgentesFuncaoSeparador03.setWidth(340);
        hBoxCamposAgentesFuncaoSeparador03.setHeight(5);
        hBoxCamposAgentesFuncaoSeparador03.setBorderStyle("stNone");
        hBoxCamposAgentesFuncaoSeparador03.setPaddingTop(0);
        hBoxCamposAgentesFuncaoSeparador03.setPaddingLeft(0);
        hBoxCamposAgentesFuncaoSeparador03.setPaddingRight(0);
        hBoxCamposAgentesFuncaoSeparador03.setPaddingBottom(0);
        hBoxCamposAgentesFuncaoSeparador03.setMarginTop(0);
        hBoxCamposAgentesFuncaoSeparador03.setMarginLeft(0);
        hBoxCamposAgentesFuncaoSeparador03.setMarginRight(0);
        hBoxCamposAgentesFuncaoSeparador03.setMarginBottom(0);
        hBoxCamposAgentesFuncaoSeparador03.setSpacing(1);
        hBoxCamposAgentesFuncaoSeparador03.setFlexVflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador03.setFlexHflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador03.setScrollable(false);
        hBoxCamposAgentesFuncaoSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxCamposAgentesFuncaoSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxCamposAgentesFuncaoSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxCamposAgentesFuncaoSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxCamposAgentesFuncaoSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxCamposAgentesFuncaoSeparador03.setBoxShadowConfigOpacity(75);
        hBoxCamposAgentesFuncaoSeparador03.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxCamposAgentesFuncaoSeparador03);
        hBoxCamposAgentesFuncaoSeparador03.applyProperties();
    }

    public TFHBox hBoxEmail = new TFHBox();

    private void init_hBoxEmail() {
        hBoxEmail.setName("hBoxEmail");
        hBoxEmail.setLeft(0);
        hBoxEmail.setTop(90);
        hBoxEmail.setWidth(340);
        hBoxEmail.setHeight(35);
        hBoxEmail.setBorderStyle("stNone");
        hBoxEmail.setPaddingTop(0);
        hBoxEmail.setPaddingLeft(0);
        hBoxEmail.setPaddingRight(0);
        hBoxEmail.setPaddingBottom(0);
        hBoxEmail.setMarginTop(0);
        hBoxEmail.setMarginLeft(0);
        hBoxEmail.setMarginRight(0);
        hBoxEmail.setMarginBottom(0);
        hBoxEmail.setSpacing(1);
        hBoxEmail.setFlexVflex("ftMin");
        hBoxEmail.setFlexHflex("ftTrue");
        hBoxEmail.setScrollable(false);
        hBoxEmail.setBoxShadowConfigHorizontalLength(10);
        hBoxEmail.setBoxShadowConfigVerticalLength(10);
        hBoxEmail.setBoxShadowConfigBlurRadius(5);
        hBoxEmail.setBoxShadowConfigSpreadRadius(0);
        hBoxEmail.setBoxShadowConfigShadowColor("clBlack");
        hBoxEmail.setBoxShadowConfigOpacity(75);
        hBoxEmail.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxEmail);
        hBoxEmail.applyProperties();
    }

    public TFVBox vBoxEmail = new TFVBox();

    private void init_vBoxEmail() {
        vBoxEmail.setName("vBoxEmail");
        vBoxEmail.setLeft(0);
        vBoxEmail.setTop(0);
        vBoxEmail.setWidth(80);
        vBoxEmail.setHeight(30);
        vBoxEmail.setBorderStyle("stNone");
        vBoxEmail.setPaddingTop(0);
        vBoxEmail.setPaddingLeft(0);
        vBoxEmail.setPaddingRight(0);
        vBoxEmail.setPaddingBottom(0);
        vBoxEmail.setMarginTop(0);
        vBoxEmail.setMarginLeft(0);
        vBoxEmail.setMarginRight(0);
        vBoxEmail.setMarginBottom(0);
        vBoxEmail.setSpacing(1);
        vBoxEmail.setFlexVflex("ftMin");
        vBoxEmail.setFlexHflex("ftFalse");
        vBoxEmail.setScrollable(false);
        vBoxEmail.setBoxShadowConfigHorizontalLength(10);
        vBoxEmail.setBoxShadowConfigVerticalLength(10);
        vBoxEmail.setBoxShadowConfigBlurRadius(5);
        vBoxEmail.setBoxShadowConfigSpreadRadius(0);
        vBoxEmail.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmail.setBoxShadowConfigOpacity(75);
        hBoxEmail.addChildren(vBoxEmail);
        vBoxEmail.applyProperties();
    }

    public TFHBox hBoxEmailSeparador01 = new TFHBox();

    private void init_hBoxEmailSeparador01() {
        hBoxEmailSeparador01.setName("hBoxEmailSeparador01");
        hBoxEmailSeparador01.setLeft(0);
        hBoxEmailSeparador01.setTop(0);
        hBoxEmailSeparador01.setWidth(20);
        hBoxEmailSeparador01.setHeight(5);
        hBoxEmailSeparador01.setBorderStyle("stNone");
        hBoxEmailSeparador01.setPaddingTop(0);
        hBoxEmailSeparador01.setPaddingLeft(0);
        hBoxEmailSeparador01.setPaddingRight(0);
        hBoxEmailSeparador01.setPaddingBottom(0);
        hBoxEmailSeparador01.setMarginTop(0);
        hBoxEmailSeparador01.setMarginLeft(0);
        hBoxEmailSeparador01.setMarginRight(0);
        hBoxEmailSeparador01.setMarginBottom(0);
        hBoxEmailSeparador01.setSpacing(1);
        hBoxEmailSeparador01.setFlexVflex("ftFalse");
        hBoxEmailSeparador01.setFlexHflex("ftFalse");
        hBoxEmailSeparador01.setScrollable(false);
        hBoxEmailSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxEmailSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxEmailSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxEmailSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxEmailSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxEmailSeparador01.setBoxShadowConfigOpacity(75);
        hBoxEmailSeparador01.setVAlign("tvTop");
        vBoxEmail.addChildren(hBoxEmailSeparador01);
        hBoxEmailSeparador01.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(6);
        FLabel3.setWidth(28);
        FLabel3.setHeight(13);
        FLabel3.setAlign("alLeft");
        FLabel3.setCaption("E-mail");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        vBoxEmail.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFString edtEmail = new TFString();

    private void init_edtEmail() {
        edtEmail.setName("edtEmail");
        edtEmail.setLeft(80);
        edtEmail.setTop(0);
        edtEmail.setWidth(200);
        edtEmail.setHeight(24);
        edtEmail.setTable(tbPerfilUsuarios);
        edtEmail.setFieldName("EMAIL");
        edtEmail.setFlex(true);
        edtEmail.setRequired(false);
        edtEmail.setConstraintCheckWhen("cwImmediate");
        edtEmail.setConstraintCheckType("ctExpression");
        edtEmail.setConstraintFocusOnError(false);
        edtEmail.setConstraintEnableUI(true);
        edtEmail.setConstraintEnabled(false);
        edtEmail.setConstraintFormCheck(true);
        edtEmail.setCharCase("ccNormal");
        edtEmail.setPwd(false);
        edtEmail.setMaxlength(0);
        edtEmail.setEnabled(false);
        edtEmail.setFontColor("clWindowText");
        edtEmail.setFontSize(-13);
        edtEmail.setFontName("Tahoma");
        edtEmail.setFontStyle("[]");
        edtEmail.setSaveLiteralCharacter(false);
        edtEmail.applyProperties();
        hBoxEmail.addChildren(edtEmail);
        addValidatable(edtEmail);
    }

    public TFHBox hBoxCamposAgentesFuncaoSeparador04 = new TFHBox();

    private void init_hBoxCamposAgentesFuncaoSeparador04() {
        hBoxCamposAgentesFuncaoSeparador04.setName("hBoxCamposAgentesFuncaoSeparador04");
        hBoxCamposAgentesFuncaoSeparador04.setLeft(0);
        hBoxCamposAgentesFuncaoSeparador04.setTop(126);
        hBoxCamposAgentesFuncaoSeparador04.setWidth(340);
        hBoxCamposAgentesFuncaoSeparador04.setHeight(5);
        hBoxCamposAgentesFuncaoSeparador04.setBorderStyle("stNone");
        hBoxCamposAgentesFuncaoSeparador04.setPaddingTop(0);
        hBoxCamposAgentesFuncaoSeparador04.setPaddingLeft(0);
        hBoxCamposAgentesFuncaoSeparador04.setPaddingRight(0);
        hBoxCamposAgentesFuncaoSeparador04.setPaddingBottom(0);
        hBoxCamposAgentesFuncaoSeparador04.setMarginTop(0);
        hBoxCamposAgentesFuncaoSeparador04.setMarginLeft(0);
        hBoxCamposAgentesFuncaoSeparador04.setMarginRight(0);
        hBoxCamposAgentesFuncaoSeparador04.setMarginBottom(0);
        hBoxCamposAgentesFuncaoSeparador04.setSpacing(1);
        hBoxCamposAgentesFuncaoSeparador04.setFlexVflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador04.setFlexHflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador04.setScrollable(false);
        hBoxCamposAgentesFuncaoSeparador04.setBoxShadowConfigHorizontalLength(10);
        hBoxCamposAgentesFuncaoSeparador04.setBoxShadowConfigVerticalLength(10);
        hBoxCamposAgentesFuncaoSeparador04.setBoxShadowConfigBlurRadius(5);
        hBoxCamposAgentesFuncaoSeparador04.setBoxShadowConfigSpreadRadius(0);
        hBoxCamposAgentesFuncaoSeparador04.setBoxShadowConfigShadowColor("clBlack");
        hBoxCamposAgentesFuncaoSeparador04.setBoxShadowConfigOpacity(75);
        hBoxCamposAgentesFuncaoSeparador04.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxCamposAgentesFuncaoSeparador04);
        hBoxCamposAgentesFuncaoSeparador04.applyProperties();
    }

    public TFHBox hBoxTipoVendedor = new TFHBox();

    private void init_hBoxTipoVendedor() {
        hBoxTipoVendedor.setName("hBoxTipoVendedor");
        hBoxTipoVendedor.setLeft(0);
        hBoxTipoVendedor.setTop(132);
        hBoxTipoVendedor.setWidth(340);
        hBoxTipoVendedor.setHeight(35);
        hBoxTipoVendedor.setBorderStyle("stNone");
        hBoxTipoVendedor.setPaddingTop(0);
        hBoxTipoVendedor.setPaddingLeft(0);
        hBoxTipoVendedor.setPaddingRight(0);
        hBoxTipoVendedor.setPaddingBottom(0);
        hBoxTipoVendedor.setMarginTop(0);
        hBoxTipoVendedor.setMarginLeft(0);
        hBoxTipoVendedor.setMarginRight(0);
        hBoxTipoVendedor.setMarginBottom(0);
        hBoxTipoVendedor.setSpacing(1);
        hBoxTipoVendedor.setFlexVflex("ftMin");
        hBoxTipoVendedor.setFlexHflex("ftTrue");
        hBoxTipoVendedor.setScrollable(false);
        hBoxTipoVendedor.setBoxShadowConfigHorizontalLength(10);
        hBoxTipoVendedor.setBoxShadowConfigVerticalLength(10);
        hBoxTipoVendedor.setBoxShadowConfigBlurRadius(5);
        hBoxTipoVendedor.setBoxShadowConfigSpreadRadius(0);
        hBoxTipoVendedor.setBoxShadowConfigShadowColor("clBlack");
        hBoxTipoVendedor.setBoxShadowConfigOpacity(75);
        hBoxTipoVendedor.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxTipoVendedor);
        hBoxTipoVendedor.applyProperties();
    }

    public TFVBox vBoxTipoVendedor = new TFVBox();

    private void init_vBoxTipoVendedor() {
        vBoxTipoVendedor.setName("vBoxTipoVendedor");
        vBoxTipoVendedor.setLeft(0);
        vBoxTipoVendedor.setTop(0);
        vBoxTipoVendedor.setWidth(80);
        vBoxTipoVendedor.setHeight(30);
        vBoxTipoVendedor.setBorderStyle("stNone");
        vBoxTipoVendedor.setPaddingTop(0);
        vBoxTipoVendedor.setPaddingLeft(0);
        vBoxTipoVendedor.setPaddingRight(0);
        vBoxTipoVendedor.setPaddingBottom(0);
        vBoxTipoVendedor.setMarginTop(0);
        vBoxTipoVendedor.setMarginLeft(0);
        vBoxTipoVendedor.setMarginRight(0);
        vBoxTipoVendedor.setMarginBottom(0);
        vBoxTipoVendedor.setSpacing(1);
        vBoxTipoVendedor.setFlexVflex("ftMin");
        vBoxTipoVendedor.setFlexHflex("ftFalse");
        vBoxTipoVendedor.setScrollable(false);
        vBoxTipoVendedor.setBoxShadowConfigHorizontalLength(10);
        vBoxTipoVendedor.setBoxShadowConfigVerticalLength(10);
        vBoxTipoVendedor.setBoxShadowConfigBlurRadius(5);
        vBoxTipoVendedor.setBoxShadowConfigSpreadRadius(0);
        vBoxTipoVendedor.setBoxShadowConfigShadowColor("clBlack");
        vBoxTipoVendedor.setBoxShadowConfigOpacity(75);
        hBoxTipoVendedor.addChildren(vBoxTipoVendedor);
        vBoxTipoVendedor.applyProperties();
    }

    public TFHBox hBoxTipoVendedorSeparador01 = new TFHBox();

    private void init_hBoxTipoVendedorSeparador01() {
        hBoxTipoVendedorSeparador01.setName("hBoxTipoVendedorSeparador01");
        hBoxTipoVendedorSeparador01.setLeft(0);
        hBoxTipoVendedorSeparador01.setTop(0);
        hBoxTipoVendedorSeparador01.setWidth(20);
        hBoxTipoVendedorSeparador01.setHeight(5);
        hBoxTipoVendedorSeparador01.setBorderStyle("stNone");
        hBoxTipoVendedorSeparador01.setPaddingTop(0);
        hBoxTipoVendedorSeparador01.setPaddingLeft(0);
        hBoxTipoVendedorSeparador01.setPaddingRight(0);
        hBoxTipoVendedorSeparador01.setPaddingBottom(0);
        hBoxTipoVendedorSeparador01.setMarginTop(0);
        hBoxTipoVendedorSeparador01.setMarginLeft(0);
        hBoxTipoVendedorSeparador01.setMarginRight(0);
        hBoxTipoVendedorSeparador01.setMarginBottom(0);
        hBoxTipoVendedorSeparador01.setSpacing(1);
        hBoxTipoVendedorSeparador01.setFlexVflex("ftFalse");
        hBoxTipoVendedorSeparador01.setFlexHflex("ftFalse");
        hBoxTipoVendedorSeparador01.setScrollable(false);
        hBoxTipoVendedorSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxTipoVendedorSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxTipoVendedorSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxTipoVendedorSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxTipoVendedorSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxTipoVendedorSeparador01.setBoxShadowConfigOpacity(75);
        hBoxTipoVendedorSeparador01.setVAlign("tvTop");
        vBoxTipoVendedor.addChildren(hBoxTipoVendedorSeparador01);
        hBoxTipoVendedorSeparador01.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(6);
        FLabel4.setWidth(69);
        FLabel4.setHeight(13);
        FLabel4.setAlign("alLeft");
        FLabel4.setCaption("Tipo Vendedor");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        vBoxTipoVendedor.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFCombo cbbTipoVendedor = new TFCombo();

    private void init_cbbTipoVendedor() {
        cbbTipoVendedor.setName("cbbTipoVendedor");
        cbbTipoVendedor.setLeft(80);
        cbbTipoVendedor.setTop(0);
        cbbTipoVendedor.setWidth(200);
        cbbTipoVendedor.setHeight(21);
        cbbTipoVendedor.setTable(tbPerfilUsuarios);
        cbbTipoVendedor.setFieldName("TIPO_VENDEDOR");
        cbbTipoVendedor.setFlex(true);
        cbbTipoVendedor.setListOptions("Novos=N;Seminovos=A;Todos=T");
        cbbTipoVendedor.setReadOnly(true);
        cbbTipoVendedor.setRequired(false);
        cbbTipoVendedor.setPrompt("Selecione");
        cbbTipoVendedor.setConstraintCheckWhen("cwImmediate");
        cbbTipoVendedor.setConstraintCheckType("ctExpression");
        cbbTipoVendedor.setConstraintFocusOnError(false);
        cbbTipoVendedor.setConstraintEnableUI(true);
        cbbTipoVendedor.setConstraintEnabled(false);
        cbbTipoVendedor.setConstraintFormCheck(true);
        cbbTipoVendedor.setClearOnDelKey(true);
        cbbTipoVendedor.setUseClearButton(false);
        cbbTipoVendedor.setHideClearButtonOnNullValue(false);
        cbbTipoVendedor.setEnabled(false);
        hBoxTipoVendedor.addChildren(cbbTipoVendedor);
        cbbTipoVendedor.applyProperties();
        addValidatable(cbbTipoVendedor);
    }

    public TFHBox hBoxCamposAgentesFuncaoSeparador05 = new TFHBox();

    private void init_hBoxCamposAgentesFuncaoSeparador05() {
        hBoxCamposAgentesFuncaoSeparador05.setName("hBoxCamposAgentesFuncaoSeparador05");
        hBoxCamposAgentesFuncaoSeparador05.setLeft(0);
        hBoxCamposAgentesFuncaoSeparador05.setTop(168);
        hBoxCamposAgentesFuncaoSeparador05.setWidth(340);
        hBoxCamposAgentesFuncaoSeparador05.setHeight(5);
        hBoxCamposAgentesFuncaoSeparador05.setBorderStyle("stNone");
        hBoxCamposAgentesFuncaoSeparador05.setPaddingTop(0);
        hBoxCamposAgentesFuncaoSeparador05.setPaddingLeft(0);
        hBoxCamposAgentesFuncaoSeparador05.setPaddingRight(0);
        hBoxCamposAgentesFuncaoSeparador05.setPaddingBottom(0);
        hBoxCamposAgentesFuncaoSeparador05.setMarginTop(0);
        hBoxCamposAgentesFuncaoSeparador05.setMarginLeft(0);
        hBoxCamposAgentesFuncaoSeparador05.setMarginRight(0);
        hBoxCamposAgentesFuncaoSeparador05.setMarginBottom(0);
        hBoxCamposAgentesFuncaoSeparador05.setSpacing(1);
        hBoxCamposAgentesFuncaoSeparador05.setFlexVflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador05.setFlexHflex("ftFalse");
        hBoxCamposAgentesFuncaoSeparador05.setScrollable(false);
        hBoxCamposAgentesFuncaoSeparador05.setBoxShadowConfigHorizontalLength(10);
        hBoxCamposAgentesFuncaoSeparador05.setBoxShadowConfigVerticalLength(10);
        hBoxCamposAgentesFuncaoSeparador05.setBoxShadowConfigBlurRadius(5);
        hBoxCamposAgentesFuncaoSeparador05.setBoxShadowConfigSpreadRadius(0);
        hBoxCamposAgentesFuncaoSeparador05.setBoxShadowConfigShadowColor("clBlack");
        hBoxCamposAgentesFuncaoSeparador05.setBoxShadowConfigOpacity(75);
        hBoxCamposAgentesFuncaoSeparador05.setVAlign("tvTop");
        vBoxCamposAgentesFuncao.addChildren(hBoxCamposAgentesFuncaoSeparador05);
        hBoxCamposAgentesFuncaoSeparador05.applyProperties();
    }

    public TFVBox bBoxEdicaoSeparador02 = new TFVBox();

    private void init_bBoxEdicaoSeparador02() {
        bBoxEdicaoSeparador02.setName("bBoxEdicaoSeparador02");
        bBoxEdicaoSeparador02.setLeft(355);
        bBoxEdicaoSeparador02.setTop(0);
        bBoxEdicaoSeparador02.setWidth(5);
        bBoxEdicaoSeparador02.setHeight(68);
        bBoxEdicaoSeparador02.setBorderStyle("stNone");
        bBoxEdicaoSeparador02.setPaddingTop(0);
        bBoxEdicaoSeparador02.setPaddingLeft(0);
        bBoxEdicaoSeparador02.setPaddingRight(0);
        bBoxEdicaoSeparador02.setPaddingBottom(0);
        bBoxEdicaoSeparador02.setMarginTop(0);
        bBoxEdicaoSeparador02.setMarginLeft(0);
        bBoxEdicaoSeparador02.setMarginRight(0);
        bBoxEdicaoSeparador02.setMarginBottom(0);
        bBoxEdicaoSeparador02.setSpacing(1);
        bBoxEdicaoSeparador02.setFlexVflex("ftFalse");
        bBoxEdicaoSeparador02.setFlexHflex("ftFalse");
        bBoxEdicaoSeparador02.setScrollable(false);
        bBoxEdicaoSeparador02.setBoxShadowConfigHorizontalLength(10);
        bBoxEdicaoSeparador02.setBoxShadowConfigVerticalLength(10);
        bBoxEdicaoSeparador02.setBoxShadowConfigBlurRadius(5);
        bBoxEdicaoSeparador02.setBoxShadowConfigSpreadRadius(0);
        bBoxEdicaoSeparador02.setBoxShadowConfigShadowColor("clBlack");
        bBoxEdicaoSeparador02.setBoxShadowConfigOpacity(75);
        bBoxEdicao.addChildren(bBoxEdicaoSeparador02);
        bBoxEdicaoSeparador02.applyProperties();
    }

    public TFVBox vBoxFoto = new TFVBox();

    private void init_vBoxFoto() {
        vBoxFoto.setName("vBoxFoto");
        vBoxFoto.setLeft(360);
        vBoxFoto.setTop(0);
        vBoxFoto.setWidth(147);
        vBoxFoto.setHeight(135);
        vBoxFoto.setBorderStyle("stNone");
        vBoxFoto.setPaddingTop(0);
        vBoxFoto.setPaddingLeft(0);
        vBoxFoto.setPaddingRight(0);
        vBoxFoto.setPaddingBottom(0);
        vBoxFoto.setMarginTop(0);
        vBoxFoto.setMarginLeft(0);
        vBoxFoto.setMarginRight(0);
        vBoxFoto.setMarginBottom(0);
        vBoxFoto.setSpacing(1);
        vBoxFoto.setFlexVflex("ftFalse");
        vBoxFoto.setFlexHflex("ftMin");
        vBoxFoto.setScrollable(false);
        vBoxFoto.setBoxShadowConfigHorizontalLength(10);
        vBoxFoto.setBoxShadowConfigVerticalLength(10);
        vBoxFoto.setBoxShadowConfigBlurRadius(5);
        vBoxFoto.setBoxShadowConfigSpreadRadius(0);
        vBoxFoto.setBoxShadowConfigShadowColor("clBlack");
        vBoxFoto.setBoxShadowConfigOpacity(75);
        bBoxEdicao.addChildren(vBoxFoto);
        vBoxFoto.applyProperties();
    }

    public TFImage imageFoto = new TFImage();

    private void init_imageFoto() {
        imageFoto.setName("imageFoto");
        imageFoto.setLeft(0);
        imageFoto.setTop(0);
        imageFoto.setWidth(124);
        imageFoto.setHeight(115);
        imageFoto.setAlign("alClient");
        imageFoto.setEnabled(false);
        imageFoto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            imageFotoClick(event);
            processarFlow("FrmControleAcessoGeral", "imageFoto", "OnClick");
        });
        imageFoto.setTable(tbPerfilUsuarios);
        imageFoto.setFieldName("FOTO");
        imageFoto.setBoxSize(0);
        imageFoto.setGrayScaleOnDisable(false);
        imageFoto.setFlexVflex("ftFalse");
        imageFoto.setFlexHflex("ftFalse");
        imageFoto.setImageId(0);
        vBoxFoto.addChildren(imageFoto);
        imageFoto.applyProperties();
    }

    public TFLabel lblLimparFoto = new TFLabel();

    private void init_lblLimparFoto() {
        lblLimparFoto.setName("lblLimparFoto");
        lblLimparFoto.setLeft(0);
        lblLimparFoto.setTop(116);
        lblLimparFoto.setWidth(56);
        lblLimparFoto.setHeight(13);
        lblLimparFoto.setCaption("Limpar Foto");
        lblLimparFoto.setFontColor("clWindowText");
        lblLimparFoto.setFontSize(-11);
        lblLimparFoto.setFontName("Tahoma");
        lblLimparFoto.setFontStyle("[]");
        lblLimparFoto.setVisible(false);
        lblLimparFoto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblLimparFotoClick(event);
            processarFlow("FrmControleAcessoGeral", "lblLimparFoto", "OnClick");
        });
        lblLimparFoto.setVerticalAlignment("taVerticalCenter");
        lblLimparFoto.setWordBreak(false);
        vBoxFoto.addChildren(lblLimparFoto);
        lblLimparFoto.applyProperties();
    }

    public TFVBox bBoxEdicaoSeparador03 = new TFVBox();

    private void init_bBoxEdicaoSeparador03() {
        bBoxEdicaoSeparador03.setName("bBoxEdicaoSeparador03");
        bBoxEdicaoSeparador03.setLeft(507);
        bBoxEdicaoSeparador03.setTop(0);
        bBoxEdicaoSeparador03.setWidth(5);
        bBoxEdicaoSeparador03.setHeight(68);
        bBoxEdicaoSeparador03.setBorderStyle("stNone");
        bBoxEdicaoSeparador03.setPaddingTop(0);
        bBoxEdicaoSeparador03.setPaddingLeft(0);
        bBoxEdicaoSeparador03.setPaddingRight(0);
        bBoxEdicaoSeparador03.setPaddingBottom(0);
        bBoxEdicaoSeparador03.setMarginTop(0);
        bBoxEdicaoSeparador03.setMarginLeft(0);
        bBoxEdicaoSeparador03.setMarginRight(0);
        bBoxEdicaoSeparador03.setMarginBottom(0);
        bBoxEdicaoSeparador03.setSpacing(1);
        bBoxEdicaoSeparador03.setFlexVflex("ftFalse");
        bBoxEdicaoSeparador03.setFlexHflex("ftFalse");
        bBoxEdicaoSeparador03.setScrollable(false);
        bBoxEdicaoSeparador03.setBoxShadowConfigHorizontalLength(10);
        bBoxEdicaoSeparador03.setBoxShadowConfigVerticalLength(10);
        bBoxEdicaoSeparador03.setBoxShadowConfigBlurRadius(5);
        bBoxEdicaoSeparador03.setBoxShadowConfigSpreadRadius(0);
        bBoxEdicaoSeparador03.setBoxShadowConfigShadowColor("clBlack");
        bBoxEdicaoSeparador03.setBoxShadowConfigOpacity(75);
        bBoxEdicao.addChildren(bBoxEdicaoSeparador03);
        bBoxEdicaoSeparador03.applyProperties();
    }

    public TFGroupbox FGroupbox1 = new TFGroupbox();

    private void init_FGroupbox1() {
        FGroupbox1.setName("FGroupbox1");
        FGroupbox1.setLeft(0);
        FGroupbox1.setTop(463);
        FGroupbox1.setWidth(1073);
        FGroupbox1.setHeight(174);
        FGroupbox1.setFontColor("clWindowText");
        FGroupbox1.setFontSize(-11);
        FGroupbox1.setFontName("Tahoma");
        FGroupbox1.setFontStyle("[]");
        FGroupbox1.setFlexVflex("ftTrue");
        FGroupbox1.setFlexHflex("ftTrue");
        FGroupbox1.setScrollable(false);
        FGroupbox1.setClosable(false);
        FGroupbox1.setClosed(false);
        FGroupbox1.setOrient("coHorizontal");
        FGroupbox1.setStyle("grp3D");
        FGroupbox1.setHeaderImageId(0);
        FVBox1.addChildren(FGroupbox1);
        FGroupbox1.applyProperties();
    }

    public TFTreeGrid treeGridAcessos = new TFTreeGrid();

    private void init_treeGridAcessos() {
        treeGridAcessos.setName("treeGridAcessos");
        treeGridAcessos.setLeft(2);
        treeGridAcessos.setTop(15);
        treeGridAcessos.setWidth(412);
        treeGridAcessos.setHeight(153);
        treeGridAcessos.setTable(tbControleAcesso);
        treeGridAcessos.setFlexVflex("ftTrue");
        treeGridAcessos.setFlexHflex("ftTrue");
        treeGridAcessos.setPagingEnabled(false);
        treeGridAcessos.setFrozenColumns(0);
        treeGridAcessos.setShowHeader(true);
        treeGridAcessos.setMultiSelection(false);
        treeGridAcessos.setKeyField("COD_ACESSO");
        treeGridAcessos.setParentField("COD_ACESSO_PAI");
        treeGridAcessos.setExpanded(false);
        treeGridAcessos.setEnablePopup(false);
        treeGridAcessos.setEditionEnabled(false);
        treeGridAcessos.setNoBorder(false);
        treeGridAcessos.setOpenOnSelect(true);
        TFTreeGridColumn item0 = new TFTreeGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Acessos");
        item0.setWidth(253);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorMaxLength(100);
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        treeGridAcessos.getColumns().add(item0);
        TFTreeGridColumn item1 = new TFTreeGridColumn();
        item1.setWidth(60);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taCenter");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("COD_ACESSO_FUNCAO IS NOT NULL");
        item2.setEvalType("etExpression");
        item2.setImageId(7000118);
        item1.getImages().add(item2);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("COD_ACESSO_FUNCAO IS NULL");
        item3.setEvalType("etExpression");
        item3.setImageId(7000117);
        item1.getImages().add(item3);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(false);
        item1.setEditorEditType("etTFString");
        item1.setEditorMaxLength(100);
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setHiperLink(false);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            treeGridAcessosAlterarAcessoClick(event);
            processarFlow("FrmControleAcessoGeral", "item1", "OnClick");
        });
        treeGridAcessos.getColumns().add(item1);
        FGroupbox1.addChildren(treeGridAcessos);
        treeGridAcessos.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(638);
        FHBox4.setWidth(1072);
        FHBox4.setHeight(28);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(5);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox1.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(0);
        FLabel6.setWidth(48);
        FLabel6.setHeight(13);
        FLabel6.setCaption("Legenda");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[fsBold]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox4.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFVBox FVBox13 = new TFVBox();

    private void init_FVBox13() {
        FVBox13.setName("FVBox13");
        FVBox13.setLeft(48);
        FVBox13.setTop(0);
        FVBox13.setWidth(5);
        FVBox13.setHeight(20);
        FVBox13.setBorderStyle("stNone");
        FVBox13.setPaddingTop(0);
        FVBox13.setPaddingLeft(0);
        FVBox13.setPaddingRight(0);
        FVBox13.setPaddingBottom(0);
        FVBox13.setMarginTop(0);
        FVBox13.setMarginLeft(0);
        FVBox13.setMarginRight(0);
        FVBox13.setMarginBottom(0);
        FVBox13.setSpacing(1);
        FVBox13.setFlexVflex("ftFalse");
        FVBox13.setFlexHflex("ftFalse");
        FVBox13.setScrollable(false);
        FVBox13.setBoxShadowConfigHorizontalLength(10);
        FVBox13.setBoxShadowConfigVerticalLength(10);
        FVBox13.setBoxShadowConfigBlurRadius(5);
        FVBox13.setBoxShadowConfigSpreadRadius(0);
        FVBox13.setBoxShadowConfigShadowColor("clBlack");
        FVBox13.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(FVBox13);
        FVBox13.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(53);
        FHBox5.setTop(0);
        FHBox5.setWidth(112);
        FHBox5.setHeight(24);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(5);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox4.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFIconClass FIconClass5 = new TFIconClass();

    private void init_FIconClass5() {
        FIconClass5.setName("FIconClass5");
        FIconClass5.setLeft(0);
        FIconClass5.setTop(0);
        FIconClass5.setHint("Help");
        FIconClass5.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassHelpClick(event);
            processarFlow("FrmControleAcessoGeral", "FIconClass5", "OnClick");
        });
        FIconClass5.setIconClass("thumbs-o-up");
        FIconClass5.setSize(18);
        FIconClass5.setColor("clTeal");
        FHBox5.addChildren(FIconClass5);
        FIconClass5.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(16);
        FVBox8.setTop(0);
        FVBox8.setWidth(5);
        FVBox8.setHeight(20);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftFalse");
        FVBox8.setFlexHflex("ftFalse");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FHBox5.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(21);
        FLabel7.setTop(0);
        FLabel7.setWidth(67);
        FLabel7.setHeight(13);
        FLabel7.setCaption("Tem acesso");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[fsBold]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        FHBox5.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(165);
        FHBox6.setTop(0);
        FHBox6.setWidth(178);
        FHBox6.setHeight(24);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(5);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox4.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFIconClass FIconClass6 = new TFIconClass();

    private void init_FIconClass6() {
        FIconClass6.setName("FIconClass6");
        FIconClass6.setLeft(0);
        FIconClass6.setTop(0);
        FIconClass6.setHint("Help");
        FIconClass6.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassHelpClick(event);
            processarFlow("FrmControleAcessoGeral", "FIconClass6", "OnClick");
        });
        FIconClass6.setIconClass("thumbs-o-down");
        FIconClass6.setSize(18);
        FIconClass6.setColor("clRed");
        FHBox6.addChildren(FIconClass6);
        FIconClass6.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(16);
        FVBox9.setTop(0);
        FVBox9.setWidth(5);
        FVBox9.setHeight(20);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftFalse");
        FVBox9.setFlexHflex("ftFalse");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(21);
        FLabel8.setTop(0);
        FLabel8.setWidth(91);
        FLabel8.setHeight(13);
        FLabel8.setCaption("N\u00E3o Tem acesso");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[fsBold]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FHBox6.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbSistemaAcessoFuncao);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbEmpresasUsuarios);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbUsuarioFoto);
        sc.getTables().add(item2);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void cbbEmpresasFuncoesChange(final Event<Object> event);

    public abstract void edtPesquisaEnter(final Event<Object> event);

    public abstract void iconClassLimparClick(final Event<Object> event);

    public void btnRefreshClick(final Event<Object> event) {
        if (btnRefresh.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnRefresh");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void iconClassHelpClick(final Event<Object> event);

    public void btnPesquisaAvancadaAgentesFuncaoClick(final Event<Object> event) {
        if (btnPesquisaAvancadaAgentesFuncao.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisaAvancadaAgentesFuncao");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnLimparPesquisaAvancadaClick(final Event<Object> event) {
        if (btnLimparPesquisaAvancada.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimparPesquisaAvancada");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnEditarClick(final Event<Object> event) {
        if (btnEditar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnEditar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnEditarPerfilClick(final Event<Object> event) {
        if (btnEditarPerfil.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnEditarPerfil");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void imageFotoClick(final Event<Object> event);

    public abstract void lblLimparFotoClick(final Event<Object> event);

    public abstract void treeGridAcessosAlterarAcessoClick(final Event<Object> event);

    public abstract void mmExportarExcelGridAgentesFuncaoClick(final Event<Object> event);

}