package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAlterarQtde extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AlterarQtdeRNA rn = null;

    public FrmAlterarQtde() {
        try {
            rn = (freedom.bytecode.rn.AlterarQtdeRNA) getRN(freedom.bytecode.rn.wizard.AlterarQtdeRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FVBox1();
        init_FHBox1();
        init_btnCancelar();
        init_btnOk();
        init_FVBox2();
        init_lblCapAlterarQtde();
        init_edtQtde();
        init_FrmAlterarQtde();
    }

    protected TFForm FrmAlterarQtde = this;
    private void init_FrmAlterarQtde() {
        FrmAlterarQtde.setName("FrmAlterarQtde");
        FrmAlterarQtde.setCaption("Form Padr\u00E3o Alterar Quantidade");
        FrmAlterarQtde.setClientHeight(162);
        FrmAlterarQtde.setClientWidth(364);
        FrmAlterarQtde.setColor("clBtnFace");
        FrmAlterarQtde.setWKey("4600397");
        FrmAlterarQtde.setSpacing(0);
        FrmAlterarQtde.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(364);
        FVBox1.setHeight(162);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmAlterarQtde.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(357);
        FHBox1.setHeight(63);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(0);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(56);
        btnCancelar.setCaption("Voltar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmAlterarQtde", "btnCancelar", "OnClick");
        });
        btnCancelar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnCancelar.setImageId(700081);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFButton btnOk = new TFButton();

    private void init_btnOk() {
        btnOk.setName("btnOk");
        btnOk.setLeft(65);
        btnOk.setTop(0);
        btnOk.setWidth(65);
        btnOk.setHeight(56);
        btnOk.setCaption("Aceitar");
        btnOk.setFontColor("clWindowText");
        btnOk.setFontSize(-11);
        btnOk.setFontName("Tahoma");
        btnOk.setFontStyle("[]");
        btnOk.setLayout("blGlyphTop");
        btnOk.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnOkClick(event);
            processarFlow("FrmAlterarQtde", "btnOk", "OnClick");
        });
        btnOk.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515"
 + "0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3"
 + "1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7"
 + "95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC"
 + "101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D"
 + "1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537"
 + "A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC"
 + "C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB"
 + "482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB"
 + "1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D"
 + "C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F"
 + "8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082");
        btnOk.setImageId(700088);
        btnOk.setColor("clBtnFace");
        btnOk.setAccess(false);
        btnOk.setIconReverseDirection(false);
        FHBox1.addChildren(btnOk);
        btnOk.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(64);
        FVBox2.setWidth(358);
        FVBox2.setHeight(93);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel lblCapAlterarQtde = new TFLabel();

    private void init_lblCapAlterarQtde() {
        lblCapAlterarQtde.setName("lblCapAlterarQtde");
        lblCapAlterarQtde.setLeft(0);
        lblCapAlterarQtde.setTop(0);
        lblCapAlterarQtde.setWidth(60);
        lblCapAlterarQtde.setHeight(13);
        lblCapAlterarQtde.setCaption("Alterar Qtde");
        lblCapAlterarQtde.setFontColor("clWindowText");
        lblCapAlterarQtde.setFontSize(-11);
        lblCapAlterarQtde.setFontName("Tahoma");
        lblCapAlterarQtde.setFontStyle("[]");
        lblCapAlterarQtde.setVerticalAlignment("taVerticalCenter");
        lblCapAlterarQtde.setWordBreak(false);
        FVBox2.addChildren(lblCapAlterarQtde);
        lblCapAlterarQtde.applyProperties();
    }

    public TFInteger edtQtde = new TFInteger();

    private void init_edtQtde() {
        edtQtde.setName("edtQtde");
        edtQtde.setLeft(0);
        edtQtde.setTop(14);
        edtQtde.setWidth(121);
        edtQtde.setHeight(24);
        edtQtde.setFlex(false);
        edtQtde.setRequired(false);
        edtQtde.setConstraintCheckWhen("cwImmediate");
        edtQtde.setConstraintCheckType("ctExpression");
        edtQtde.setConstraintFocusOnError(false);
        edtQtde.setConstraintEnableUI(true);
        edtQtde.setConstraintEnabled(false);
        edtQtde.setConstraintFormCheck(true);
        edtQtde.setMaxlength(0);
        edtQtde.setFontColor("clWindowText");
        edtQtde.setFontSize(-13);
        edtQtde.setFontName("Tahoma");
        edtQtde.setFontStyle("[]");
        edtQtde.setAlignment("taRightJustify");
        FVBox2.addChildren(edtQtde);
        edtQtde.applyProperties();
        addValidatable(edtQtde);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnOkClick(final Event<Object> event) {
        if (btnOk.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnOk");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}