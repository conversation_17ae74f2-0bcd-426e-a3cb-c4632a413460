package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmLimiteFinanceiro extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.LimiteFinanceiroRNA rn = null;

    public FrmLimiteFinanceiro() {
        try {
            rn = (freedom.bytecode.rn.LimiteFinanceiroRNA) getRN(freedom.bytecode.rn.wizard.LimiteFinanceiroRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbLimiteCliente();
        init_FVBox1();
        init_FHBox1();
        init_btnVoltar();
        init_FHBox2();
        init_hBoxBloqueado();
        init_FHBox22();
        init_FHBox23();
        init_FCheckBox1();
        init_hBoxMotivoBloqueio();
        init_FHBox14();
        init_FHBox15();
        init_FLabel8();
        init_edtMotivoBloqueio();
        init_FHBox4();
        init_FHBox5();
        init_FHBox6();
        init_FLabel1();
        init_edtLimiteCredito();
        init_FHBox7();
        init_FHBox8();
        init_FHBox9();
        init_FLabel2();
        init_edtAdicional();
        init_FHBox10();
        init_FHBox11();
        init_FHBox12();
        init_FLabel3();
        init_edtJaUtilizado();
        init_FHBox25();
        init_FHBox26();
        init_FHBox27();
        init_FLabel7();
        init_edtDisponivel();
        init_FHBox16();
        init_FHBox3();
        init_FVBox2();
        init_FHBox13();
        init_lblCadastroIrregular();
        init_hBoxDataVencCadastro();
        init_FHBox18();
        init_FHBox19();
        init_FLabel5();
        init_edtDataVencCadastro();
        init_hBoxDataVencLimite();
        init_FHBox34();
        init_FHBox35();
        init_FLabel6();
        init_edtDataVencLimite();
        init_FrmLimiteFinanceiro();
    }

    public LEADS_CONSULTA_FINAN_CLIENTE tbLimiteCliente;

    private void init_tbLimiteCliente() {
        tbLimiteCliente = rn.tbLimiteCliente;
        tbLimiteCliente.setName("tbLimiteCliente");
        tbLimiteCliente.setMaxRowCount(200);
        tbLimiteCliente.setWKey("5300783;53001");
        tbLimiteCliente.setRatioBatchSize(20);
        getTables().put(tbLimiteCliente, "tbLimiteCliente");
        tbLimiteCliente.applyProperties();
    }

    protected TFForm FrmLimiteFinanceiro = this;
    private void init_FrmLimiteFinanceiro() {
        FrmLimiteFinanceiro.setName("FrmLimiteFinanceiro");
        FrmLimiteFinanceiro.setCaption("Limite de Cr\u00E9dito Cliente");
        FrmLimiteFinanceiro.setClientHeight(350);
        FrmLimiteFinanceiro.setClientWidth(302);
        FrmLimiteFinanceiro.setColor("clBtnFace");
        FrmLimiteFinanceiro.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmLimiteFinanceiro", "FrmLimiteFinanceiro", "OnCreate");
        });
        FrmLimiteFinanceiro.setWKey("5300783");
        FrmLimiteFinanceiro.setSpacing(0);
        FrmLimiteFinanceiro.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(302);
        FVBox1.setHeight(350);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(10);
        FVBox1.setPaddingRight(10);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmLimiteFinanceiro.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(297);
        FHBox1.setHeight(62);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmLimiteFinanceiro", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox1.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(60);
        FHBox2.setTop(0);
        FHBox2.setWidth(24);
        FHBox2.setHeight(41);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FHBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox hBoxBloqueado = new TFHBox();

    private void init_hBoxBloqueado() {
        hBoxBloqueado.setName("hBoxBloqueado");
        hBoxBloqueado.setLeft(84);
        hBoxBloqueado.setTop(0);
        hBoxBloqueado.setWidth(197);
        hBoxBloqueado.setHeight(30);
        hBoxBloqueado.setBorderStyle("stNone");
        hBoxBloqueado.setPaddingTop(6);
        hBoxBloqueado.setPaddingLeft(0);
        hBoxBloqueado.setPaddingRight(0);
        hBoxBloqueado.setPaddingBottom(0);
        hBoxBloqueado.setMarginTop(0);
        hBoxBloqueado.setMarginLeft(0);
        hBoxBloqueado.setMarginRight(0);
        hBoxBloqueado.setMarginBottom(0);
        hBoxBloqueado.setSpacing(1);
        hBoxBloqueado.setFlexVflex("ftFalse");
        hBoxBloqueado.setFlexHflex("ftTrue");
        hBoxBloqueado.setScrollable(false);
        hBoxBloqueado.setBoxShadowConfigHorizontalLength(10);
        hBoxBloqueado.setBoxShadowConfigVerticalLength(10);
        hBoxBloqueado.setBoxShadowConfigBlurRadius(5);
        hBoxBloqueado.setBoxShadowConfigSpreadRadius(0);
        hBoxBloqueado.setBoxShadowConfigShadowColor("clBlack");
        hBoxBloqueado.setBoxShadowConfigOpacity(75);
        hBoxBloqueado.setVAlign("tvTop");
        FHBox1.addChildren(hBoxBloqueado);
        hBoxBloqueado.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(0);
        FHBox22.setTop(0);
        FHBox22.setWidth(54);
        FHBox22.setHeight(23);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(5);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(5);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftFalse");
        FHBox22.setFlexHflex("ftFalse");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        hBoxBloqueado.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(0);
        FHBox23.setTop(0);
        FHBox23.setWidth(13);
        FHBox23.setHeight(18);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(0);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(1);
        FHBox23.setFlexVflex("ftFalse");
        FHBox23.setFlexHflex("ftTrue");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        FHBox22.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFCheckBox FCheckBox1 = new TFCheckBox();

    private void init_FCheckBox1() {
        FCheckBox1.setName("FCheckBox1");
        FCheckBox1.setLeft(54);
        FCheckBox1.setTop(0);
        FCheckBox1.setWidth(71);
        FCheckBox1.setHeight(17);
        FCheckBox1.setCaption("Bloqueado");
        FCheckBox1.setEnabled(false);
        FCheckBox1.setFontColor("clWindowText");
        FCheckBox1.setFontSize(-11);
        FCheckBox1.setFontName("Tahoma");
        FCheckBox1.setFontStyle("[]");
        FCheckBox1.setTable(tbLimiteCliente);
        FCheckBox1.setFieldName("BLOQUEADO");
        FCheckBox1.setCheckedValue("S");
        FCheckBox1.setVerticalAlignment("taAlignTop");
        hBoxBloqueado.addChildren(FCheckBox1);
        FCheckBox1.applyProperties();
    }

    public TFHBox hBoxMotivoBloqueio = new TFHBox();

    private void init_hBoxMotivoBloqueio() {
        hBoxMotivoBloqueio.setName("hBoxMotivoBloqueio");
        hBoxMotivoBloqueio.setLeft(0);
        hBoxMotivoBloqueio.setTop(63);
        hBoxMotivoBloqueio.setWidth(292);
        hBoxMotivoBloqueio.setHeight(35);
        hBoxMotivoBloqueio.setBorderStyle("stNone");
        hBoxMotivoBloqueio.setPaddingTop(0);
        hBoxMotivoBloqueio.setPaddingLeft(0);
        hBoxMotivoBloqueio.setPaddingRight(0);
        hBoxMotivoBloqueio.setPaddingBottom(0);
        hBoxMotivoBloqueio.setMarginTop(-20);
        hBoxMotivoBloqueio.setMarginLeft(0);
        hBoxMotivoBloqueio.setMarginRight(0);
        hBoxMotivoBloqueio.setMarginBottom(0);
        hBoxMotivoBloqueio.setSpacing(1);
        hBoxMotivoBloqueio.setFlexVflex("ftFalse");
        hBoxMotivoBloqueio.setFlexHflex("ftTrue");
        hBoxMotivoBloqueio.setScrollable(false);
        hBoxMotivoBloqueio.setBoxShadowConfigHorizontalLength(10);
        hBoxMotivoBloqueio.setBoxShadowConfigVerticalLength(10);
        hBoxMotivoBloqueio.setBoxShadowConfigBlurRadius(5);
        hBoxMotivoBloqueio.setBoxShadowConfigSpreadRadius(0);
        hBoxMotivoBloqueio.setBoxShadowConfigShadowColor("clBlack");
        hBoxMotivoBloqueio.setBoxShadowConfigOpacity(75);
        hBoxMotivoBloqueio.setVAlign("tvTop");
        FVBox1.addChildren(hBoxMotivoBloqueio);
        hBoxMotivoBloqueio.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(0);
        FHBox14.setWidth(150);
        FHBox14.setHeight(30);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(5);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(5);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        hBoxMotivoBloqueio.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(0);
        FHBox15.setWidth(13);
        FHBox15.setHeight(26);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftTrue");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FHBox14.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(13);
        FLabel8.setTop(0);
        FLabel8.setWidth(75);
        FLabel8.setHeight(13);
        FLabel8.setCaption("Motivo Bloqueio");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FHBox14.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFString edtMotivoBloqueio = new TFString();

    private void init_edtMotivoBloqueio() {
        edtMotivoBloqueio.setName("edtMotivoBloqueio");
        edtMotivoBloqueio.setLeft(150);
        edtMotivoBloqueio.setTop(0);
        edtMotivoBloqueio.setWidth(121);
        edtMotivoBloqueio.setHeight(24);
        edtMotivoBloqueio.setTable(tbLimiteCliente);
        edtMotivoBloqueio.setFieldName("MOTIVO");
        edtMotivoBloqueio.setFlex(true);
        edtMotivoBloqueio.setRequired(false);
        edtMotivoBloqueio.setConstraintCheckWhen("cwImmediate");
        edtMotivoBloqueio.setConstraintCheckType("ctExpression");
        edtMotivoBloqueio.setConstraintFocusOnError(false);
        edtMotivoBloqueio.setConstraintEnableUI(true);
        edtMotivoBloqueio.setConstraintEnabled(false);
        edtMotivoBloqueio.setConstraintFormCheck(true);
        edtMotivoBloqueio.setCharCase("ccNormal");
        edtMotivoBloqueio.setPwd(false);
        edtMotivoBloqueio.setMaxlength(0);
        edtMotivoBloqueio.setEnabled(false);
        edtMotivoBloqueio.setFontColor("clWindowText");
        edtMotivoBloqueio.setFontSize(-13);
        edtMotivoBloqueio.setFontName("Tahoma");
        edtMotivoBloqueio.setFontStyle("[]");
        edtMotivoBloqueio.setSaveLiteralCharacter(false);
        edtMotivoBloqueio.applyProperties();
        hBoxMotivoBloqueio.addChildren(edtMotivoBloqueio);
        addValidatable(edtMotivoBloqueio);
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(99);
        FHBox4.setWidth(292);
        FHBox4.setHeight(32);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox1.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(150);
        FHBox5.setHeight(29);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(5);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(5);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox4.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(13);
        FHBox6.setHeight(26);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox5.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(13);
        FLabel1.setTop(0);
        FLabel1.setWidth(76);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Limite de C\u00E9dito");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FHBox5.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFDecimal edtLimiteCredito = new TFDecimal();

    private void init_edtLimiteCredito() {
        edtLimiteCredito.setName("edtLimiteCredito");
        edtLimiteCredito.setLeft(150);
        edtLimiteCredito.setTop(0);
        edtLimiteCredito.setWidth(130);
        edtLimiteCredito.setHeight(24);
        edtLimiteCredito.setTable(tbLimiteCliente);
        edtLimiteCredito.setFieldName("LIMITE_CREDITO");
        edtLimiteCredito.setFlex(false);
        edtLimiteCredito.setRequired(false);
        edtLimiteCredito.setConstraintCheckWhen("cwImmediate");
        edtLimiteCredito.setConstraintCheckType("ctExpression");
        edtLimiteCredito.setConstraintFocusOnError(false);
        edtLimiteCredito.setConstraintEnableUI(true);
        edtLimiteCredito.setConstraintEnabled(false);
        edtLimiteCredito.setConstraintFormCheck(true);
        edtLimiteCredito.setMaxlength(0);
        edtLimiteCredito.setPrecision(0);
        edtLimiteCredito.setFormat("R$ ,##0.00");
        edtLimiteCredito.setEnabled(false);
        edtLimiteCredito.setFontColor("clWindowText");
        edtLimiteCredito.setFontSize(-13);
        edtLimiteCredito.setFontName("Tahoma");
        edtLimiteCredito.setFontStyle("[]");
        edtLimiteCredito.setAlignment("taRightJustify");
        FHBox4.addChildren(edtLimiteCredito);
        edtLimiteCredito.applyProperties();
        addValidatable(edtLimiteCredito);
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(132);
        FHBox7.setWidth(292);
        FHBox7.setHeight(32);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox1.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(150);
        FHBox8.setHeight(30);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(5);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(5);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox7.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(13);
        FHBox9.setHeight(26);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FHBox8.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(13);
        FLabel2.setTop(0);
        FLabel2.setWidth(61);
        FLabel2.setHeight(13);
        FLabel2.setCaption("(+) Adicional");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FHBox8.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFDecimal edtAdicional = new TFDecimal();

    private void init_edtAdicional() {
        edtAdicional.setName("edtAdicional");
        edtAdicional.setLeft(150);
        edtAdicional.setTop(0);
        edtAdicional.setWidth(130);
        edtAdicional.setHeight(24);
        edtAdicional.setTable(tbLimiteCliente);
        edtAdicional.setFieldName("VALOR_COMPL_LIMITE_CREDITO");
        edtAdicional.setFlex(false);
        edtAdicional.setRequired(false);
        edtAdicional.setConstraintCheckWhen("cwImmediate");
        edtAdicional.setConstraintCheckType("ctExpression");
        edtAdicional.setConstraintFocusOnError(false);
        edtAdicional.setConstraintEnableUI(true);
        edtAdicional.setConstraintEnabled(false);
        edtAdicional.setConstraintFormCheck(true);
        edtAdicional.setMaxlength(0);
        edtAdicional.setPrecision(0);
        edtAdicional.setFormat("R$ ,##0.00");
        edtAdicional.setEnabled(false);
        edtAdicional.setFontColor("clWindowText");
        edtAdicional.setFontSize(-13);
        edtAdicional.setFontName("Tahoma");
        edtAdicional.setFontStyle("[]");
        edtAdicional.setAlignment("taRightJustify");
        FHBox7.addChildren(edtAdicional);
        edtAdicional.applyProperties();
        addValidatable(edtAdicional);
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(165);
        FHBox10.setWidth(292);
        FHBox10.setHeight(32);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FVBox1.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(150);
        FHBox11.setHeight(30);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(5);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(5);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FHBox10.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(0);
        FHBox12.setWidth(13);
        FHBox12.setHeight(26);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FHBox11.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(13);
        FLabel3.setTop(0);
        FLabel3.setWidth(69);
        FLabel3.setHeight(13);
        FLabel3.setCaption("(-) J\u00E1 Utilizado");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FHBox11.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFDecimal edtJaUtilizado = new TFDecimal();

    private void init_edtJaUtilizado() {
        edtJaUtilizado.setName("edtJaUtilizado");
        edtJaUtilizado.setLeft(150);
        edtJaUtilizado.setTop(0);
        edtJaUtilizado.setWidth(130);
        edtJaUtilizado.setHeight(24);
        edtJaUtilizado.setTable(tbLimiteCliente);
        edtJaUtilizado.setFieldName("LIMITE_UTILIZADO");
        edtJaUtilizado.setFlex(false);
        edtJaUtilizado.setRequired(false);
        edtJaUtilizado.setConstraintCheckWhen("cwImmediate");
        edtJaUtilizado.setConstraintCheckType("ctExpression");
        edtJaUtilizado.setConstraintFocusOnError(false);
        edtJaUtilizado.setConstraintEnableUI(true);
        edtJaUtilizado.setConstraintEnabled(false);
        edtJaUtilizado.setConstraintFormCheck(true);
        edtJaUtilizado.setMaxlength(0);
        edtJaUtilizado.setPrecision(0);
        edtJaUtilizado.setFormat("R$ ,##0.00");
        edtJaUtilizado.setEnabled(false);
        edtJaUtilizado.setFontColor("clWindowText");
        edtJaUtilizado.setFontSize(-13);
        edtJaUtilizado.setFontName("Tahoma");
        edtJaUtilizado.setFontStyle("[]");
        edtJaUtilizado.setAlignment("taRightJustify");
        FHBox10.addChildren(edtJaUtilizado);
        edtJaUtilizado.applyProperties();
        addValidatable(edtJaUtilizado);
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(0);
        FHBox25.setTop(198);
        FHBox25.setWidth(292);
        FHBox25.setHeight(32);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(0);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(0);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(1);
        FHBox25.setFlexVflex("ftFalse");
        FHBox25.setFlexHflex("ftTrue");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvTop");
        FVBox1.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(0);
        FHBox26.setTop(0);
        FHBox26.setWidth(150);
        FHBox26.setHeight(30);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(5);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(5);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(1);
        FHBox26.setFlexVflex("ftFalse");
        FHBox26.setFlexHflex("ftFalse");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvTop");
        FHBox25.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(0);
        FHBox27.setTop(0);
        FHBox27.setWidth(13);
        FHBox27.setHeight(26);
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(0);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(1);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftTrue");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        FHBox26.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(13);
        FLabel7.setTop(0);
        FLabel7.setWidth(48);
        FLabel7.setHeight(13);
        FLabel7.setCaption("Dispon\u00EDvel");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        FHBox26.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFDecimal edtDisponivel = new TFDecimal();

    private void init_edtDisponivel() {
        edtDisponivel.setName("edtDisponivel");
        edtDisponivel.setLeft(150);
        edtDisponivel.setTop(0);
        edtDisponivel.setWidth(130);
        edtDisponivel.setHeight(24);
        edtDisponivel.setTable(tbLimiteCliente);
        edtDisponivel.setFieldName("LIMITE_DISPONIVEL");
        edtDisponivel.setFlex(false);
        edtDisponivel.setRequired(false);
        edtDisponivel.setConstraintCheckWhen("cwImmediate");
        edtDisponivel.setConstraintCheckType("ctExpression");
        edtDisponivel.setConstraintFocusOnError(false);
        edtDisponivel.setConstraintEnableUI(true);
        edtDisponivel.setConstraintEnabled(false);
        edtDisponivel.setConstraintFormCheck(true);
        edtDisponivel.setMaxlength(0);
        edtDisponivel.setPrecision(0);
        edtDisponivel.setFormat("R$ ,##0.00");
        edtDisponivel.setEnabled(false);
        edtDisponivel.setFontColor("clWindowText");
        edtDisponivel.setFontSize(-13);
        edtDisponivel.setFontName("Tahoma");
        edtDisponivel.setFontStyle("[]");
        edtDisponivel.setAlignment("taRightJustify");
        FHBox25.addChildren(edtDisponivel);
        edtDisponivel.applyProperties();
        addValidatable(edtDisponivel);
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(231);
        FHBox16.setWidth(292);
        FHBox16.setHeight(31);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftTrue");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FVBox1.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(74);
        FHBox3.setHeight(26);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox16.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(74);
        FVBox2.setTop(0);
        FVBox2.setWidth(272);
        FVBox2.setHeight(26);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox16.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(0);
        FHBox13.setWidth(24);
        FHBox13.setHeight(4);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FVBox2.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFLabel lblCadastroIrregular = new TFLabel();

    private void init_lblCadastroIrregular() {
        lblCadastroIrregular.setName("lblCadastroIrregular");
        lblCadastroIrregular.setLeft(0);
        lblCadastroIrregular.setTop(5);
        lblCadastroIrregular.setWidth(146);
        lblCadastroIrregular.setHeight(16);
        lblCadastroIrregular.setCaption("CADASTRO IRREGULAR");
        lblCadastroIrregular.setFontColor("clRed");
        lblCadastroIrregular.setFontSize(-13);
        lblCadastroIrregular.setFontName("Tahoma");
        lblCadastroIrregular.setFontStyle("[fsBold]");
        lblCadastroIrregular.setVisible(false);
        lblCadastroIrregular.setVerticalAlignment("taVerticalCenter");
        lblCadastroIrregular.setWordBreak(false);
        FVBox2.addChildren(lblCadastroIrregular);
        lblCadastroIrregular.applyProperties();
    }

    public TFHBox hBoxDataVencCadastro = new TFHBox();

    private void init_hBoxDataVencCadastro() {
        hBoxDataVencCadastro.setName("hBoxDataVencCadastro");
        hBoxDataVencCadastro.setLeft(0);
        hBoxDataVencCadastro.setTop(263);
        hBoxDataVencCadastro.setWidth(292);
        hBoxDataVencCadastro.setHeight(28);
        hBoxDataVencCadastro.setBorderStyle("stNone");
        hBoxDataVencCadastro.setPaddingTop(0);
        hBoxDataVencCadastro.setPaddingLeft(0);
        hBoxDataVencCadastro.setPaddingRight(0);
        hBoxDataVencCadastro.setPaddingBottom(0);
        hBoxDataVencCadastro.setMarginTop(0);
        hBoxDataVencCadastro.setMarginLeft(0);
        hBoxDataVencCadastro.setMarginRight(0);
        hBoxDataVencCadastro.setMarginBottom(0);
        hBoxDataVencCadastro.setSpacing(1);
        hBoxDataVencCadastro.setFlexVflex("ftFalse");
        hBoxDataVencCadastro.setFlexHflex("ftTrue");
        hBoxDataVencCadastro.setScrollable(false);
        hBoxDataVencCadastro.setBoxShadowConfigHorizontalLength(10);
        hBoxDataVencCadastro.setBoxShadowConfigVerticalLength(10);
        hBoxDataVencCadastro.setBoxShadowConfigBlurRadius(5);
        hBoxDataVencCadastro.setBoxShadowConfigSpreadRadius(0);
        hBoxDataVencCadastro.setBoxShadowConfigShadowColor("clBlack");
        hBoxDataVencCadastro.setBoxShadowConfigOpacity(75);
        hBoxDataVencCadastro.setVAlign("tvTop");
        FVBox1.addChildren(hBoxDataVencCadastro);
        hBoxDataVencCadastro.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(0);
        FHBox18.setWidth(150);
        FHBox18.setHeight(30);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(5);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(5);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftFalse");
        FHBox18.setFlexHflex("ftFalse");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        hBoxDataVencCadastro.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(0);
        FHBox19.setWidth(13);
        FHBox19.setHeight(26);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftTrue");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FHBox18.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(13);
        FLabel5.setTop(0);
        FLabel5.setWidth(128);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Data Vencimento Cadastro");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FHBox18.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFString edtDataVencCadastro = new TFString();

    private void init_edtDataVencCadastro() {
        edtDataVencCadastro.setName("edtDataVencCadastro");
        edtDataVencCadastro.setLeft(150);
        edtDataVencCadastro.setTop(0);
        edtDataVencCadastro.setWidth(121);
        edtDataVencCadastro.setHeight(24);
        edtDataVencCadastro.setTable(tbLimiteCliente);
        edtDataVencCadastro.setFieldName("DATA_VENC_CADASTRO");
        edtDataVencCadastro.setFlex(true);
        edtDataVencCadastro.setRequired(false);
        edtDataVencCadastro.setConstraintCheckWhen("cwImmediate");
        edtDataVencCadastro.setConstraintCheckType("ctExpression");
        edtDataVencCadastro.setConstraintFocusOnError(false);
        edtDataVencCadastro.setConstraintEnableUI(true);
        edtDataVencCadastro.setConstraintEnabled(false);
        edtDataVencCadastro.setConstraintFormCheck(true);
        edtDataVencCadastro.setCharCase("ccNormal");
        edtDataVencCadastro.setPwd(false);
        edtDataVencCadastro.setMaxlength(0);
        edtDataVencCadastro.setEnabled(false);
        edtDataVencCadastro.setFontColor("clWindowText");
        edtDataVencCadastro.setFontSize(-13);
        edtDataVencCadastro.setFontName("Tahoma");
        edtDataVencCadastro.setFontStyle("[]");
        edtDataVencCadastro.setColor("clWhite");
        edtDataVencCadastro.setSaveLiteralCharacter(false);
        edtDataVencCadastro.applyProperties();
        hBoxDataVencCadastro.addChildren(edtDataVencCadastro);
        addValidatable(edtDataVencCadastro);
    }

    public TFHBox hBoxDataVencLimite = new TFHBox();

    private void init_hBoxDataVencLimite() {
        hBoxDataVencLimite.setName("hBoxDataVencLimite");
        hBoxDataVencLimite.setLeft(0);
        hBoxDataVencLimite.setTop(292);
        hBoxDataVencLimite.setWidth(292);
        hBoxDataVencLimite.setHeight(31);
        hBoxDataVencLimite.setBorderStyle("stNone");
        hBoxDataVencLimite.setPaddingTop(0);
        hBoxDataVencLimite.setPaddingLeft(0);
        hBoxDataVencLimite.setPaddingRight(0);
        hBoxDataVencLimite.setPaddingBottom(0);
        hBoxDataVencLimite.setMarginTop(0);
        hBoxDataVencLimite.setMarginLeft(0);
        hBoxDataVencLimite.setMarginRight(0);
        hBoxDataVencLimite.setMarginBottom(0);
        hBoxDataVencLimite.setSpacing(1);
        hBoxDataVencLimite.setFlexVflex("ftFalse");
        hBoxDataVencLimite.setFlexHflex("ftTrue");
        hBoxDataVencLimite.setScrollable(false);
        hBoxDataVencLimite.setBoxShadowConfigHorizontalLength(10);
        hBoxDataVencLimite.setBoxShadowConfigVerticalLength(10);
        hBoxDataVencLimite.setBoxShadowConfigBlurRadius(5);
        hBoxDataVencLimite.setBoxShadowConfigSpreadRadius(0);
        hBoxDataVencLimite.setBoxShadowConfigShadowColor("clBlack");
        hBoxDataVencLimite.setBoxShadowConfigOpacity(75);
        hBoxDataVencLimite.setVAlign("tvTop");
        FVBox1.addChildren(hBoxDataVencLimite);
        hBoxDataVencLimite.applyProperties();
    }

    public TFHBox FHBox34 = new TFHBox();

    private void init_FHBox34() {
        FHBox34.setName("FHBox34");
        FHBox34.setLeft(0);
        FHBox34.setTop(0);
        FHBox34.setWidth(150);
        FHBox34.setHeight(30);
        FHBox34.setBorderStyle("stNone");
        FHBox34.setPaddingTop(5);
        FHBox34.setPaddingLeft(0);
        FHBox34.setPaddingRight(5);
        FHBox34.setPaddingBottom(0);
        FHBox34.setMarginTop(0);
        FHBox34.setMarginLeft(0);
        FHBox34.setMarginRight(0);
        FHBox34.setMarginBottom(0);
        FHBox34.setSpacing(1);
        FHBox34.setFlexVflex("ftFalse");
        FHBox34.setFlexHflex("ftFalse");
        FHBox34.setScrollable(false);
        FHBox34.setBoxShadowConfigHorizontalLength(10);
        FHBox34.setBoxShadowConfigVerticalLength(10);
        FHBox34.setBoxShadowConfigBlurRadius(5);
        FHBox34.setBoxShadowConfigSpreadRadius(0);
        FHBox34.setBoxShadowConfigShadowColor("clBlack");
        FHBox34.setBoxShadowConfigOpacity(75);
        FHBox34.setVAlign("tvTop");
        hBoxDataVencLimite.addChildren(FHBox34);
        FHBox34.applyProperties();
    }

    public TFHBox FHBox35 = new TFHBox();

    private void init_FHBox35() {
        FHBox35.setName("FHBox35");
        FHBox35.setLeft(0);
        FHBox35.setTop(0);
        FHBox35.setWidth(13);
        FHBox35.setHeight(26);
        FHBox35.setBorderStyle("stNone");
        FHBox35.setPaddingTop(0);
        FHBox35.setPaddingLeft(0);
        FHBox35.setPaddingRight(0);
        FHBox35.setPaddingBottom(0);
        FHBox35.setMarginTop(0);
        FHBox35.setMarginLeft(0);
        FHBox35.setMarginRight(0);
        FHBox35.setMarginBottom(0);
        FHBox35.setSpacing(1);
        FHBox35.setFlexVflex("ftFalse");
        FHBox35.setFlexHflex("ftTrue");
        FHBox35.setScrollable(false);
        FHBox35.setBoxShadowConfigHorizontalLength(10);
        FHBox35.setBoxShadowConfigVerticalLength(10);
        FHBox35.setBoxShadowConfigBlurRadius(5);
        FHBox35.setBoxShadowConfigSpreadRadius(0);
        FHBox35.setBoxShadowConfigShadowColor("clBlack");
        FHBox35.setBoxShadowConfigOpacity(75);
        FHBox35.setVAlign("tvTop");
        FHBox34.addChildren(FHBox35);
        FHBox35.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(13);
        FLabel6.setTop(0);
        FLabel6.setWidth(111);
        FLabel6.setHeight(13);
        FLabel6.setCaption("Data Vencimento Limite");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox34.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFString edtDataVencLimite = new TFString();

    private void init_edtDataVencLimite() {
        edtDataVencLimite.setName("edtDataVencLimite");
        edtDataVencLimite.setLeft(150);
        edtDataVencLimite.setTop(0);
        edtDataVencLimite.setWidth(121);
        edtDataVencLimite.setHeight(24);
        edtDataVencLimite.setTable(tbLimiteCliente);
        edtDataVencLimite.setFieldName("DATA_VENC_LIMITE");
        edtDataVencLimite.setFlex(true);
        edtDataVencLimite.setRequired(false);
        edtDataVencLimite.setConstraintCheckWhen("cwImmediate");
        edtDataVencLimite.setConstraintCheckType("ctExpression");
        edtDataVencLimite.setConstraintFocusOnError(false);
        edtDataVencLimite.setConstraintEnableUI(true);
        edtDataVencLimite.setConstraintEnabled(false);
        edtDataVencLimite.setConstraintFormCheck(true);
        edtDataVencLimite.setCharCase("ccNormal");
        edtDataVencLimite.setPwd(false);
        edtDataVencLimite.setMaxlength(0);
        edtDataVencLimite.setEnabled(false);
        edtDataVencLimite.setFontColor("clWindowText");
        edtDataVencLimite.setFontSize(-13);
        edtDataVencLimite.setFontName("Tahoma");
        edtDataVencLimite.setFontStyle("[]");
        edtDataVencLimite.setSaveLiteralCharacter(false);
        edtDataVencLimite.applyProperties();
        hBoxDataVencLimite.addChildren(edtDataVencLimite);
        addValidatable(edtDataVencLimite);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}