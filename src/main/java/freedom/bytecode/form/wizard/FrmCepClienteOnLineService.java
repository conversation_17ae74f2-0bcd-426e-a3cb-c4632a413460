package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCepClienteOnLineService extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CepClienteOnLineServiceRNA rn = null;

    public FrmCepClienteOnLineService() {
        try {
            rn = (freedom.bytecode.rn.CepClienteOnLineServiceRNA) getRN(freedom.bytecode.rn.wizard.CepClienteOnLineServiceRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteTpEnd();
        init_tbClientesEndereco();
        init_tbClientesEnderecoIe();
        init_tbClientesEnderecoTemp();
        init_tbClientesEnderecoIeTemp();
        init_tbCidades();
        init_vBoxTela();
        init_FHBox11();
        init_FHBox7();
        init_btnVoltar();
        init_btnSalvar();
        init_vBoxEnderecos();
        init_FHBox1();
        init_FVBox1();
        init_FLabel1();
        init_cbbTipoEndereco();
        init_FVBox2();
        init_FLabel2();
        init_edtCep();
        init_FHBox2();
        init_vBoxApagarCep();
        init_iconApagarNome();
        init_btnCep();
        init_FHBox3();
        init_FVBox6();
        init_FHBox4();
        init_FHBox5();
        init_FLabel3();
        init_FHBox6();
        init_FLabel14();
        init_FHBox8();
        init_FLabel4();
        init_FHBox9();
        init_FHBox10();
        init_FLabel6();
        init_FHBox12();
        init_lblCepAtual();
        init_FHBox13();
        init_lblCepCorreio();
        init_FHBox14();
        init_FHBox15();
        init_FLabel7();
        init_FHBox16();
        init_lblUfAtual();
        init_FHBox17();
        init_lblUfCorreio();
        init_FHBox18();
        init_FHBox19();
        init_FLabel8();
        init_FHBox20();
        init_lblCidadeAtual();
        init_FHBox21();
        init_lblCidadeCorreio();
        init_FHBox22();
        init_FHBox23();
        init_FLabel9();
        init_FHBox24();
        init_lblLogradouroAtual();
        init_FHBox25();
        init_lblLogradouroCorreio();
        init_FHBox26();
        init_FHBox27();
        init_FLabel11();
        init_FHBox28();
        init_lblBairroAtual();
        init_FHBox29();
        init_lblBairroCorreio();
        init_FHBox30();
        init_FHBox31();
        init_FLabel12();
        init_FHBox32();
        init_lblComplementoAtual();
        init_FHBox33();
        init_lblComplementoCorreio();
        init_FCoachmark();
        init_FrmCepClienteOnLineService();
    }

    public CLIENTE_TP_END tbClienteTpEnd;

    private void init_tbClienteTpEnd() {
        tbClienteTpEnd = rn.tbClienteTpEnd;
        tbClienteTpEnd.setName("tbClienteTpEnd");
        tbClienteTpEnd.setMaxRowCount(200);
        tbClienteTpEnd.setWKey("4600715;46001");
        tbClienteTpEnd.setRatioBatchSize(20);
        getTables().put(tbClienteTpEnd, "tbClienteTpEnd");
        tbClienteTpEnd.applyProperties();
    }

    public CLIENTES_ENDERECO tbClientesEndereco;

    private void init_tbClientesEndereco() {
        tbClientesEndereco = rn.tbClientesEndereco;
        tbClientesEndereco.setName("tbClientesEndereco");
        tbClientesEndereco.setMaxRowCount(200);
        tbClientesEndereco.setWKey("4600715;46002");
        tbClientesEndereco.setRatioBatchSize(20);
        getTables().put(tbClientesEndereco, "tbClientesEndereco");
        tbClientesEndereco.applyProperties();
    }

    public CLIENTES_ENDERECO_IE tbClientesEnderecoIe;

    private void init_tbClientesEnderecoIe() {
        tbClientesEnderecoIe = rn.tbClientesEnderecoIe;
        tbClientesEnderecoIe.setName("tbClientesEnderecoIe");
        tbClientesEnderecoIe.setMaxRowCount(200);
        tbClientesEnderecoIe.setWKey("4600715;46003");
        tbClientesEnderecoIe.setRatioBatchSize(20);
        getTables().put(tbClientesEnderecoIe, "tbClientesEnderecoIe");
        tbClientesEnderecoIe.applyProperties();
    }

    public DUAL tbClientesEnderecoTemp;

    private void init_tbClientesEnderecoTemp() {
        tbClientesEnderecoTemp = rn.tbClientesEnderecoTemp;
        tbClientesEnderecoTemp.setName("tbClientesEnderecoTemp");
        TFTableField item6 = new TFTableField();
        item6.setName("COD_CLIENTE");
        item6.setCalculated(true);
        item6.setUpdatable(false);
        item6.setPrimaryKey(false);
        item6.setFieldType("ftDecimal");
        item6.setJSONConfigNullOnEmpty(false);
        item6.setCaption("COD_CLIENTE");
        tbClientesEnderecoTemp.getFieldDefs().add(item6);
        TFTableField item7 = new TFTableField();
        item7.setName("CEP");
        item7.setCalculated(true);
        item7.setUpdatable(false);
        item7.setPrimaryKey(false);
        item7.setFieldType("ftString");
        item7.setJSONConfigNullOnEmpty(false);
        item7.setCaption("CEP");
        tbClientesEnderecoTemp.getFieldDefs().add(item7);
        TFTableField item8 = new TFTableField();
        item8.setName("UF");
        item8.setCalculated(true);
        item8.setUpdatable(false);
        item8.setPrimaryKey(false);
        item8.setFieldType("ftString");
        item8.setJSONConfigNullOnEmpty(false);
        item8.setCaption("UF");
        tbClientesEnderecoTemp.getFieldDefs().add(item8);
        TFTableField item9 = new TFTableField();
        item9.setName("RUA");
        item9.setCalculated(true);
        item9.setUpdatable(false);
        item9.setPrimaryKey(false);
        item9.setFieldType("ftString");
        item9.setJSONConfigNullOnEmpty(false);
        item9.setCaption("RUA");
        tbClientesEnderecoTemp.getFieldDefs().add(item9);
        TFTableField item10 = new TFTableField();
        item10.setName("CIDADE");
        item10.setCalculated(true);
        item10.setUpdatable(false);
        item10.setPrimaryKey(false);
        item10.setFieldType("ftString");
        item10.setJSONConfigNullOnEmpty(false);
        item10.setCaption("CIDADE");
        tbClientesEnderecoTemp.getFieldDefs().add(item10);
        TFTableField item11 = new TFTableField();
        item11.setName("BAIRRO");
        item11.setCalculated(true);
        item11.setUpdatable(false);
        item11.setPrimaryKey(false);
        item11.setFieldType("ftString");
        item11.setJSONConfigNullOnEmpty(false);
        item11.setCaption("BAIRRO");
        tbClientesEnderecoTemp.getFieldDefs().add(item11);
        TFTableField item12 = new TFTableField();
        item12.setName("COMPLEMENTO");
        item12.setCalculated(true);
        item12.setUpdatable(false);
        item12.setPrimaryKey(false);
        item12.setFieldType("ftString");
        item12.setJSONConfigNullOnEmpty(false);
        item12.setCaption("COMPLEMENTO");
        tbClientesEnderecoTemp.getFieldDefs().add(item12);
        TFTableField item13 = new TFTableField();
        item13.setName("CODCIDADEIBGE");
        item13.setCalculated(true);
        item13.setUpdatable(false);
        item13.setPrimaryKey(false);
        item13.setFieldType("ftInteger");
        item13.setJSONConfigNullOnEmpty(false);
        item13.setCaption("CODCIDADEIBGE");
        tbClientesEnderecoTemp.getFieldDefs().add(item13);
        tbClientesEnderecoTemp.setMaxRowCount(200);
        tbClientesEnderecoTemp.setWKey("4600715;46004");
        tbClientesEnderecoTemp.setRatioBatchSize(20);
        getTables().put(tbClientesEnderecoTemp, "tbClientesEnderecoTemp");
        tbClientesEnderecoTemp.applyProperties();
    }

    public DUAL tbClientesEnderecoIeTemp;

    private void init_tbClientesEnderecoIeTemp() {
        tbClientesEnderecoIeTemp = rn.tbClientesEnderecoIeTemp;
        tbClientesEnderecoIeTemp.setName("tbClientesEnderecoIeTemp");
        tbClientesEnderecoIeTemp.setMaxRowCount(200);
        tbClientesEnderecoIeTemp.setWKey("4600715;46005");
        tbClientesEnderecoIeTemp.setRatioBatchSize(20);
        getTables().put(tbClientesEnderecoIeTemp, "tbClientesEnderecoIeTemp");
        tbClientesEnderecoIeTemp.applyProperties();
    }

    public CIDADES tbCidades;

    private void init_tbCidades() {
        tbCidades = rn.tbCidades;
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("4600715;46006");
        tbCidades.setRatioBatchSize(20);
        getTables().put(tbCidades, "tbCidades");
        tbCidades.applyProperties();
    }

    protected TFForm FrmCepClienteOnLineService = this;
    private void init_FrmCepClienteOnLineService() {
        FrmCepClienteOnLineService.setName("FrmCepClienteOnLineService");
        FrmCepClienteOnLineService.setCaption("CEP do Cliente - OnLine [Integra\u00E7\u00E3o API ViaCEP]");
        FrmCepClienteOnLineService.setClientHeight(342);
        FrmCepClienteOnLineService.setClientWidth(484);
        FrmCepClienteOnLineService.setColor("clBtnFace");
        FrmCepClienteOnLineService.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmCepClienteOnLineService", "FrmCepClienteOnLineService", "OnCreate");
        });
        FrmCepClienteOnLineService.setWKey("4600715");
        FrmCepClienteOnLineService.setSpacing(0);
        FrmCepClienteOnLineService.applyProperties();
    }

    public TFVBox vBoxTela = new TFVBox();

    private void init_vBoxTela() {
        vBoxTela.setName("vBoxTela");
        vBoxTela.setLeft(0);
        vBoxTela.setTop(0);
        vBoxTela.setWidth(484);
        vBoxTela.setHeight(342);
        vBoxTela.setAlign("alClient");
        vBoxTela.setBorderStyle("stNone");
        vBoxTela.setPaddingTop(5);
        vBoxTela.setPaddingLeft(5);
        vBoxTela.setPaddingRight(5);
        vBoxTela.setPaddingBottom(5);
        vBoxTela.setMarginTop(0);
        vBoxTela.setMarginLeft(0);
        vBoxTela.setMarginRight(0);
        vBoxTela.setMarginBottom(0);
        vBoxTela.setSpacing(1);
        vBoxTela.setFlexVflex("ftFalse");
        vBoxTela.setFlexHflex("ftTrue");
        vBoxTela.setScrollable(false);
        vBoxTela.setBoxShadowConfigHorizontalLength(10);
        vBoxTela.setBoxShadowConfigVerticalLength(10);
        vBoxTela.setBoxShadowConfigBlurRadius(5);
        vBoxTela.setBoxShadowConfigSpreadRadius(0);
        vBoxTela.setBoxShadowConfigShadowColor("clBlack");
        vBoxTela.setBoxShadowConfigOpacity(75);
        FrmCepClienteOnLineService.addChildren(vBoxTela);
        vBoxTela.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(468);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(5);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxTela.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(2);
        FHBox7.setHeight(50);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FHBox11.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(2);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-16);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmCepClienteOnLineService", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(62);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(66);
        btnSalvar.setHeight(56);
        btnSalvar.setHint("Salvar Altera\u00E7\u00F5es Aceitas");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-16);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCepClienteOnLineService", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3"
 + "0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220"
 + "AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950"
 + "4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5"
 + "5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37"
 + "35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067"
 + "603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7"
 + "29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B"
 + "DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA"
 + "B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B"
 + "A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194"
 + "B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002"
 + "EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715"
 + "87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B"
 + "AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9"
 + "32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66"
 + "5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E"
 + "4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05"
 + "05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF"
 + "0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76"
 + "093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0"
 + "C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65"
 + "C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70"
 + "9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D"
 + "80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12"
 + "B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8"
 + "6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087"
 + "94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF"
 + "0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34"
 + "2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2"
 + "134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000"
 + "0049454E44AE426082");
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox11.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox vBoxEnderecos = new TFVBox();

    private void init_vBoxEnderecos() {
        vBoxEnderecos.setName("vBoxEnderecos");
        vBoxEnderecos.setLeft(0);
        vBoxEnderecos.setTop(62);
        vBoxEnderecos.setWidth(468);
        vBoxEnderecos.setHeight(260);
        vBoxEnderecos.setBorderStyle("stNone");
        vBoxEnderecos.setPaddingTop(5);
        vBoxEnderecos.setPaddingLeft(5);
        vBoxEnderecos.setPaddingRight(5);
        vBoxEnderecos.setPaddingBottom(0);
        vBoxEnderecos.setMarginTop(0);
        vBoxEnderecos.setMarginLeft(0);
        vBoxEnderecos.setMarginRight(0);
        vBoxEnderecos.setMarginBottom(0);
        vBoxEnderecos.setSpacing(1);
        vBoxEnderecos.setFlexVflex("ftFalse");
        vBoxEnderecos.setFlexHflex("ftTrue");
        vBoxEnderecos.setScrollable(false);
        vBoxEnderecos.setBoxShadowConfigHorizontalLength(10);
        vBoxEnderecos.setBoxShadowConfigVerticalLength(10);
        vBoxEnderecos.setBoxShadowConfigBlurRadius(5);
        vBoxEnderecos.setBoxShadowConfigSpreadRadius(0);
        vBoxEnderecos.setBoxShadowConfigShadowColor("clBlack");
        vBoxEnderecos.setBoxShadowConfigOpacity(75);
        vBoxTela.addChildren(vBoxEnderecos);
        vBoxEnderecos.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(462);
        FHBox1.setHeight(63);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(4);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxEnderecos.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(161);
        FVBox1.setHeight(60);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(2);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(94);
        FLabel1.setHeight(14);
        FLabel1.setCaption("Endere\u00E7o na Os");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-12);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cbbTipoEndereco = new TFCombo();

    private void init_cbbTipoEndereco() {
        cbbTipoEndereco.setName("cbbTipoEndereco");
        cbbTipoEndereco.setLeft(0);
        cbbTipoEndereco.setTop(15);
        cbbTipoEndereco.setWidth(145);
        cbbTipoEndereco.setHeight(21);
        cbbTipoEndereco.setLookupTable(tbClienteTpEnd);
        cbbTipoEndereco.setLookupKey("TIPO_ENDERECO");
        cbbTipoEndereco.setLookupDesc("DESCRICAO");
        cbbTipoEndereco.setFlex(true);
        cbbTipoEndereco.setReadOnly(true);
        cbbTipoEndereco.setRequired(false);
        cbbTipoEndereco.setPrompt("Selecione");
        cbbTipoEndereco.setConstraintCheckWhen("cwImmediate");
        cbbTipoEndereco.setConstraintCheckType("ctExpression");
        cbbTipoEndereco.setConstraintFocusOnError(false);
        cbbTipoEndereco.setConstraintEnableUI(true);
        cbbTipoEndereco.setConstraintEnabled(false);
        cbbTipoEndereco.setConstraintFormCheck(true);
        cbbTipoEndereco.setClearOnDelKey(true);
        cbbTipoEndereco.setUseClearButton(false);
        cbbTipoEndereco.setHideClearButtonOnNullValue(false);
        cbbTipoEndereco.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbTipoEnderecoChange(event);
            processarFlow("FrmCepClienteOnLineService", "cbbTipoEndereco", "OnChange");
        });
        FVBox1.addChildren(cbbTipoEndereco);
        cbbTipoEndereco.applyProperties();
        addValidatable(cbbTipoEndereco);
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(161);
        FVBox2.setTop(0);
        FVBox2.setWidth(140);
        FVBox2.setHeight(60);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(2);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(96);
        FLabel2.setHeight(14);
        FLabel2.setCaption("CEP a Pesquisar");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-12);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox2.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFString edtCep = new TFString();

    private void init_edtCep() {
        edtCep.setName("edtCep");
        edtCep.setLeft(0);
        edtCep.setTop(15);
        edtCep.setWidth(121);
        edtCep.setHeight(24);
        edtCep.setFlex(true);
        edtCep.setRequired(false);
        edtCep.setConstraintCheckWhen("cwImmediate");
        edtCep.setConstraintCheckType("ctExpression");
        edtCep.setConstraintFocusOnError(false);
        edtCep.setConstraintEnableUI(true);
        edtCep.setConstraintEnabled(false);
        edtCep.setConstraintFormCheck(true);
        edtCep.setCharCase("ccNormal");
        edtCep.setPwd(false);
        edtCep.setMaxlength(0);
        edtCep.setFontColor("clWindowText");
        edtCep.setFontSize(-13);
        edtCep.setFontName("Tahoma");
        edtCep.setFontStyle("[]");
        edtCep.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCepEnter(event);
            processarFlow("FrmCepClienteOnLineService", "edtCep", "OnEnter");
        });
        edtCep.setSaveLiteralCharacter(false);
        edtCep.applyProperties();
        FVBox2.addChildren(edtCep);
        addValidatable(edtCep);
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(301);
        FHBox2.setTop(0);
        FHBox2.setWidth(120);
        FHBox2.setHeight(57);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(21);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FHBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox vBoxApagarCep = new TFVBox();

    private void init_vBoxApagarCep() {
        vBoxApagarCep.setName("vBoxApagarCep");
        vBoxApagarCep.setLeft(0);
        vBoxApagarCep.setTop(0);
        vBoxApagarCep.setWidth(42);
        vBoxApagarCep.setHeight(30);
        vBoxApagarCep.setAlign("alLeft");
        vBoxApagarCep.setBorderStyle("stNone");
        vBoxApagarCep.setPaddingTop(3);
        vBoxApagarCep.setPaddingLeft(10);
        vBoxApagarCep.setPaddingRight(0);
        vBoxApagarCep.setPaddingBottom(0);
        vBoxApagarCep.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxApagarNomeCliClick(event);
            processarFlow("FrmCepClienteOnLineService", "vBoxApagarCep", "OnClick");
        });
        vBoxApagarCep.setMarginTop(0);
        vBoxApagarCep.setMarginLeft(0);
        vBoxApagarCep.setMarginRight(0);
        vBoxApagarCep.setMarginBottom(0);
        vBoxApagarCep.setSpacing(1);
        vBoxApagarCep.setFlexVflex("ftTrue");
        vBoxApagarCep.setFlexHflex("ftFalse");
        vBoxApagarCep.setScrollable(false);
        vBoxApagarCep.setBoxShadowConfigHorizontalLength(10);
        vBoxApagarCep.setBoxShadowConfigVerticalLength(10);
        vBoxApagarCep.setBoxShadowConfigBlurRadius(5);
        vBoxApagarCep.setBoxShadowConfigSpreadRadius(0);
        vBoxApagarCep.setBoxShadowConfigShadowColor("clBlack");
        vBoxApagarCep.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(vBoxApagarCep);
        vBoxApagarCep.applyProperties();
    }

    public TFIconClass iconApagarNome = new TFIconClass();

    private void init_iconApagarNome() {
        iconApagarNome.setName("iconApagarNome");
        iconApagarNome.setLeft(0);
        iconApagarNome.setTop(0);
        iconApagarNome.setHint("Apagar nome cliente");
        iconApagarNome.setIconClass("trash");
        iconApagarNome.setSize(22);
        iconApagarNome.setColor("clBlack");
        vBoxApagarCep.addChildren(iconApagarNome);
        iconApagarNome.applyProperties();
    }

    public TFButton btnCep = new TFButton();

    private void init_btnCep() {
        btnCep.setName("btnCep");
        btnCep.setLeft(42);
        btnCep.setTop(0);
        btnCep.setWidth(75);
        btnCep.setHeight(30);
        btnCep.setCaption("CEP");
        btnCep.setFontColor("clWindowText");
        btnCep.setFontSize(-11);
        btnCep.setFontName("Tahoma");
        btnCep.setFontStyle("[]");
        btnCep.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCepClick(event);
            processarFlow("FrmCepClienteOnLineService", "btnCep", "OnClick");
        });
        btnCep.setPngImageData("89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF"
 + "610000015D4944415478DA95924D28445114C7DFF551A6A421CB215958CECA4A"
 + "B290241F1B6343C946D95A594C22CACECE82958F48598C598C689295B5B5AC26"
 + "24110A0B91E6F9DD3AB7CE3C6F5EBD5BBFFEEF9C7BEEBFF3CEBDC653CB183380"
 + "6C439B4AFFC02AACFBBEFFED0596918335C8114C78D5D733A431790A33D842E6"
 + "24B70C1BF001F5D00339488A49BBEE84B3A6032D493CC6E6C9BF368D69426EC5"
 + "64919A356D60832C9CB23152AD7FEAFA910B994982DAB233B841BB6090E47984"
 + "4103F225612BB5AFCEE0136D846E92571106B5C8AF84760EF7CEE012ED856992"
 + "071106CDC89B84496ADF9DC10CBA0B8F9072FF1662B084ACC00335293DC4845C"
 + "591D1C4A27E5C0E151A420E110FBC5E03BB02FD00DD076B200D7D002F330ACFC"
 + "32181C5718289333E9246CBDD8E9CBF73826F90A03755593300B69B057659FF8"
 + "26DCC13E4CE94E8C1763D981217B764E92EA8B65A04C76C0DE5E36B68132E984"
 + "D21FD0B779D2A82A25DE0000000049454E44AE426082");
        btnCep.setImageId(700085);
        btnCep.setColor("clBtnFace");
        btnCep.setAccess(false);
        btnCep.setIconReverseDirection(false);
        FHBox2.addChildren(btnCep);
        btnCep.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(64);
        FHBox3.setWidth(460);
        FHBox3.setHeight(185);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        vBoxEnderecos.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(387);
        FVBox6.setHeight(180);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(2);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(360);
        FHBox4.setHeight(22);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftMin");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox6.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(80);
        FHBox5.setHeight(20);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftMin");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox4.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(26);
        FLabel3.setHeight(14);
        FLabel3.setCaption("Tipo");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-12);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[fsBold]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FHBox5.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(80);
        FHBox6.setTop(0);
        FHBox6.setWidth(100);
        FHBox6.setHeight(20);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftMin");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox4.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFLabel FLabel14 = new TFLabel();

    private void init_FLabel14() {
        FLabel14.setName("FLabel14");
        FLabel14.setLeft(0);
        FLabel14.setTop(0);
        FLabel14.setWidth(33);
        FLabel14.setHeight(14);
        FLabel14.setCaption("Atual");
        FLabel14.setFontColor("clRed");
        FLabel14.setFontSize(-12);
        FLabel14.setFontName("Tahoma");
        FLabel14.setFontStyle("[fsBold]");
        FLabel14.setVerticalAlignment("taVerticalCenter");
        FLabel14.setWordBreak(false);
        FHBox6.addChildren(FLabel14);
        FLabel14.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(180);
        FHBox8.setTop(0);
        FHBox8.setWidth(100);
        FHBox8.setHeight(20);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftMin");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox4.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(44);
        FLabel4.setHeight(14);
        FLabel4.setCaption("Correio");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-12);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[fsBold]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FHBox8.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(23);
        FHBox9.setWidth(360);
        FHBox9.setHeight(22);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftMin");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox6.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(80);
        FHBox10.setHeight(20);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftMin");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox9.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(0);
        FLabel6.setWidth(19);
        FLabel6.setHeight(13);
        FLabel6.setCaption("CEP");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox10.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(80);
        FHBox12.setTop(0);
        FHBox12.setWidth(100);
        FHBox12.setHeight(20);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftMin");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FHBox9.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFLabel lblCepAtual = new TFLabel();

    private void init_lblCepAtual() {
        lblCepAtual.setName("lblCepAtual");
        lblCepAtual.setLeft(0);
        lblCepAtual.setTop(0);
        lblCepAtual.setWidth(19);
        lblCepAtual.setHeight(13);
        lblCepAtual.setCaption("CEP");
        lblCepAtual.setColor("clBtnFace");
        lblCepAtual.setFontColor("clRed");
        lblCepAtual.setFontSize(-11);
        lblCepAtual.setFontName("Tahoma");
        lblCepAtual.setFontStyle("[]");
        lblCepAtual.setVerticalAlignment("taVerticalCenter");
        lblCepAtual.setWordBreak(false);
        FHBox12.addChildren(lblCepAtual);
        lblCepAtual.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(180);
        FHBox13.setTop(0);
        FHBox13.setWidth(100);
        FHBox13.setHeight(20);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftMin");
        FHBox13.setFlexHflex("ftTrue");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FHBox9.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFLabel lblCepCorreio = new TFLabel();

    private void init_lblCepCorreio() {
        lblCepCorreio.setName("lblCepCorreio");
        lblCepCorreio.setLeft(0);
        lblCepCorreio.setTop(0);
        lblCepCorreio.setWidth(19);
        lblCepCorreio.setHeight(13);
        lblCepCorreio.setCaption("CEP");
        lblCepCorreio.setFontColor("clWindowText");
        lblCepCorreio.setFontSize(-11);
        lblCepCorreio.setFontName("Tahoma");
        lblCepCorreio.setFontStyle("[]");
        lblCepCorreio.setVerticalAlignment("taVerticalCenter");
        lblCepCorreio.setWordBreak(false);
        FHBox13.addChildren(lblCepCorreio);
        lblCepCorreio.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(46);
        FHBox14.setWidth(360);
        FHBox14.setHeight(22);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftMin");
        FHBox14.setFlexHflex("ftTrue");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        FVBox6.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(0);
        FHBox15.setWidth(80);
        FHBox15.setHeight(20);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftMin");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FHBox14.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(0);
        FLabel7.setTop(0);
        FLabel7.setWidth(13);
        FLabel7.setHeight(13);
        FLabel7.setCaption("UF");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        FHBox15.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(80);
        FHBox16.setTop(0);
        FHBox16.setWidth(100);
        FHBox16.setHeight(20);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftMin");
        FHBox16.setFlexHflex("ftTrue");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FHBox14.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFLabel lblUfAtual = new TFLabel();

    private void init_lblUfAtual() {
        lblUfAtual.setName("lblUfAtual");
        lblUfAtual.setLeft(0);
        lblUfAtual.setTop(0);
        lblUfAtual.setWidth(13);
        lblUfAtual.setHeight(13);
        lblUfAtual.setCaption("UF");
        lblUfAtual.setFontColor("clRed");
        lblUfAtual.setFontSize(-11);
        lblUfAtual.setFontName("Tahoma");
        lblUfAtual.setFontStyle("[]");
        lblUfAtual.setVerticalAlignment("taVerticalCenter");
        lblUfAtual.setWordBreak(false);
        FHBox16.addChildren(lblUfAtual);
        lblUfAtual.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(180);
        FHBox17.setTop(0);
        FHBox17.setWidth(100);
        FHBox17.setHeight(20);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftMin");
        FHBox17.setFlexHflex("ftTrue");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FHBox14.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFLabel lblUfCorreio = new TFLabel();

    private void init_lblUfCorreio() {
        lblUfCorreio.setName("lblUfCorreio");
        lblUfCorreio.setLeft(0);
        lblUfCorreio.setTop(0);
        lblUfCorreio.setWidth(13);
        lblUfCorreio.setHeight(13);
        lblUfCorreio.setCaption("UF");
        lblUfCorreio.setFontColor("clWindowText");
        lblUfCorreio.setFontSize(-11);
        lblUfCorreio.setFontName("Tahoma");
        lblUfCorreio.setFontStyle("[]");
        lblUfCorreio.setVerticalAlignment("taVerticalCenter");
        lblUfCorreio.setWordBreak(false);
        FHBox17.addChildren(lblUfCorreio);
        lblUfCorreio.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(69);
        FHBox18.setWidth(360);
        FHBox18.setHeight(22);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftMin");
        FHBox18.setFlexHflex("ftTrue");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        FVBox6.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(0);
        FHBox19.setWidth(80);
        FHBox19.setHeight(20);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftMin");
        FHBox19.setFlexHflex("ftFalse");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FHBox18.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(0);
        FLabel8.setTop(0);
        FLabel8.setWidth(33);
        FLabel8.setHeight(13);
        FLabel8.setCaption("Cidade");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FHBox19.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(80);
        FHBox20.setTop(0);
        FHBox20.setWidth(100);
        FHBox20.setHeight(20);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(0);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftMin");
        FHBox20.setFlexHflex("ftTrue");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FHBox18.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFLabel lblCidadeAtual = new TFLabel();

    private void init_lblCidadeAtual() {
        lblCidadeAtual.setName("lblCidadeAtual");
        lblCidadeAtual.setLeft(0);
        lblCidadeAtual.setTop(0);
        lblCidadeAtual.setWidth(33);
        lblCidadeAtual.setHeight(13);
        lblCidadeAtual.setCaption("Cidade");
        lblCidadeAtual.setFontColor("clRed");
        lblCidadeAtual.setFontSize(-11);
        lblCidadeAtual.setFontName("Tahoma");
        lblCidadeAtual.setFontStyle("[]");
        lblCidadeAtual.setVerticalAlignment("taVerticalCenter");
        lblCidadeAtual.setWordBreak(false);
        FHBox20.addChildren(lblCidadeAtual);
        lblCidadeAtual.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(180);
        FHBox21.setTop(0);
        FHBox21.setWidth(100);
        FHBox21.setHeight(20);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftMin");
        FHBox21.setFlexHflex("ftTrue");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        FHBox18.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFLabel lblCidadeCorreio = new TFLabel();

    private void init_lblCidadeCorreio() {
        lblCidadeCorreio.setName("lblCidadeCorreio");
        lblCidadeCorreio.setLeft(0);
        lblCidadeCorreio.setTop(0);
        lblCidadeCorreio.setWidth(33);
        lblCidadeCorreio.setHeight(13);
        lblCidadeCorreio.setCaption("Cidade");
        lblCidadeCorreio.setFontColor("clWindowText");
        lblCidadeCorreio.setFontSize(-11);
        lblCidadeCorreio.setFontName("Tahoma");
        lblCidadeCorreio.setFontStyle("[]");
        lblCidadeCorreio.setVerticalAlignment("taVerticalCenter");
        lblCidadeCorreio.setWordBreak(false);
        FHBox21.addChildren(lblCidadeCorreio);
        lblCidadeCorreio.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(0);
        FHBox22.setTop(92);
        FHBox22.setWidth(360);
        FHBox22.setHeight(22);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(0);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftMin");
        FHBox22.setFlexHflex("ftTrue");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        FVBox6.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(0);
        FHBox23.setTop(0);
        FHBox23.setWidth(80);
        FHBox23.setHeight(20);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(0);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(1);
        FHBox23.setFlexVflex("ftMin");
        FHBox23.setFlexHflex("ftFalse");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        FHBox22.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFLabel FLabel9 = new TFLabel();

    private void init_FLabel9() {
        FLabel9.setName("FLabel9");
        FLabel9.setLeft(0);
        FLabel9.setTop(0);
        FLabel9.setWidth(55);
        FLabel9.setHeight(13);
        FLabel9.setCaption("Logradouro");
        FLabel9.setFontColor("clWindowText");
        FLabel9.setFontSize(-11);
        FLabel9.setFontName("Tahoma");
        FLabel9.setFontStyle("[]");
        FLabel9.setVerticalAlignment("taVerticalCenter");
        FLabel9.setWordBreak(false);
        FHBox23.addChildren(FLabel9);
        FLabel9.applyProperties();
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(80);
        FHBox24.setTop(0);
        FHBox24.setWidth(100);
        FHBox24.setHeight(20);
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(0);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(1);
        FHBox24.setFlexVflex("ftMin");
        FHBox24.setFlexHflex("ftTrue");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvTop");
        FHBox22.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFLabel lblLogradouroAtual = new TFLabel();

    private void init_lblLogradouroAtual() {
        lblLogradouroAtual.setName("lblLogradouroAtual");
        lblLogradouroAtual.setLeft(0);
        lblLogradouroAtual.setTop(0);
        lblLogradouroAtual.setWidth(55);
        lblLogradouroAtual.setHeight(13);
        lblLogradouroAtual.setCaption("Logradouro");
        lblLogradouroAtual.setFontColor("clRed");
        lblLogradouroAtual.setFontSize(-11);
        lblLogradouroAtual.setFontName("Tahoma");
        lblLogradouroAtual.setFontStyle("[]");
        lblLogradouroAtual.setVerticalAlignment("taVerticalCenter");
        lblLogradouroAtual.setWordBreak(false);
        FHBox24.addChildren(lblLogradouroAtual);
        lblLogradouroAtual.applyProperties();
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(180);
        FHBox25.setTop(0);
        FHBox25.setWidth(100);
        FHBox25.setHeight(20);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(0);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(0);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(1);
        FHBox25.setFlexVflex("ftMin");
        FHBox25.setFlexHflex("ftTrue");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvTop");
        FHBox22.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFLabel lblLogradouroCorreio = new TFLabel();

    private void init_lblLogradouroCorreio() {
        lblLogradouroCorreio.setName("lblLogradouroCorreio");
        lblLogradouroCorreio.setLeft(0);
        lblLogradouroCorreio.setTop(0);
        lblLogradouroCorreio.setWidth(55);
        lblLogradouroCorreio.setHeight(13);
        lblLogradouroCorreio.setCaption("Logradouro");
        lblLogradouroCorreio.setFontColor("clWindowText");
        lblLogradouroCorreio.setFontSize(-11);
        lblLogradouroCorreio.setFontName("Tahoma");
        lblLogradouroCorreio.setFontStyle("[]");
        lblLogradouroCorreio.setVerticalAlignment("taVerticalCenter");
        lblLogradouroCorreio.setWordBreak(false);
        FHBox25.addChildren(lblLogradouroCorreio);
        lblLogradouroCorreio.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(0);
        FHBox26.setTop(115);
        FHBox26.setWidth(360);
        FHBox26.setHeight(22);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(0);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(0);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(1);
        FHBox26.setFlexVflex("ftMin");
        FHBox26.setFlexHflex("ftTrue");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvTop");
        FVBox6.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(0);
        FHBox27.setTop(0);
        FHBox27.setWidth(80);
        FHBox27.setHeight(20);
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(0);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(1);
        FHBox27.setFlexVflex("ftMin");
        FHBox27.setFlexHflex("ftFalse");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        FHBox26.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFLabel FLabel11 = new TFLabel();

    private void init_FLabel11() {
        FLabel11.setName("FLabel11");
        FLabel11.setLeft(0);
        FLabel11.setTop(0);
        FLabel11.setWidth(28);
        FLabel11.setHeight(13);
        FLabel11.setCaption("Bairro");
        FLabel11.setFontColor("clWindowText");
        FLabel11.setFontSize(-11);
        FLabel11.setFontName("Tahoma");
        FLabel11.setFontStyle("[]");
        FLabel11.setVerticalAlignment("taVerticalCenter");
        FLabel11.setWordBreak(false);
        FHBox27.addChildren(FLabel11);
        FLabel11.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(80);
        FHBox28.setTop(0);
        FHBox28.setWidth(100);
        FHBox28.setHeight(20);
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(0);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(0);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(1);
        FHBox28.setFlexVflex("ftMin");
        FHBox28.setFlexHflex("ftTrue");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvTop");
        FHBox26.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFLabel lblBairroAtual = new TFLabel();

    private void init_lblBairroAtual() {
        lblBairroAtual.setName("lblBairroAtual");
        lblBairroAtual.setLeft(0);
        lblBairroAtual.setTop(0);
        lblBairroAtual.setWidth(28);
        lblBairroAtual.setHeight(13);
        lblBairroAtual.setCaption("Bairro");
        lblBairroAtual.setFontColor("clRed");
        lblBairroAtual.setFontSize(-11);
        lblBairroAtual.setFontName("Tahoma");
        lblBairroAtual.setFontStyle("[]");
        lblBairroAtual.setVerticalAlignment("taVerticalCenter");
        lblBairroAtual.setWordBreak(false);
        FHBox28.addChildren(lblBairroAtual);
        lblBairroAtual.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(180);
        FHBox29.setTop(0);
        FHBox29.setWidth(100);
        FHBox29.setHeight(20);
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(1);
        FHBox29.setFlexVflex("ftMin");
        FHBox29.setFlexHflex("ftTrue");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        FHBox26.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFLabel lblBairroCorreio = new TFLabel();

    private void init_lblBairroCorreio() {
        lblBairroCorreio.setName("lblBairroCorreio");
        lblBairroCorreio.setLeft(0);
        lblBairroCorreio.setTop(0);
        lblBairroCorreio.setWidth(28);
        lblBairroCorreio.setHeight(13);
        lblBairroCorreio.setCaption("Bairro");
        lblBairroCorreio.setFontColor("clWindowText");
        lblBairroCorreio.setFontSize(-11);
        lblBairroCorreio.setFontName("Tahoma");
        lblBairroCorreio.setFontStyle("[]");
        lblBairroCorreio.setVerticalAlignment("taVerticalCenter");
        lblBairroCorreio.setWordBreak(false);
        FHBox29.addChildren(lblBairroCorreio);
        lblBairroCorreio.applyProperties();
    }

    public TFHBox FHBox30 = new TFHBox();

    private void init_FHBox30() {
        FHBox30.setName("FHBox30");
        FHBox30.setLeft(0);
        FHBox30.setTop(138);
        FHBox30.setWidth(360);
        FHBox30.setHeight(22);
        FHBox30.setBorderStyle("stNone");
        FHBox30.setPaddingTop(0);
        FHBox30.setPaddingLeft(0);
        FHBox30.setPaddingRight(0);
        FHBox30.setPaddingBottom(0);
        FHBox30.setMarginTop(0);
        FHBox30.setMarginLeft(0);
        FHBox30.setMarginRight(0);
        FHBox30.setMarginBottom(0);
        FHBox30.setSpacing(1);
        FHBox30.setFlexVflex("ftMin");
        FHBox30.setFlexHflex("ftTrue");
        FHBox30.setScrollable(false);
        FHBox30.setBoxShadowConfigHorizontalLength(10);
        FHBox30.setBoxShadowConfigVerticalLength(10);
        FHBox30.setBoxShadowConfigBlurRadius(5);
        FHBox30.setBoxShadowConfigSpreadRadius(0);
        FHBox30.setBoxShadowConfigShadowColor("clBlack");
        FHBox30.setBoxShadowConfigOpacity(75);
        FHBox30.setVAlign("tvTop");
        FVBox6.addChildren(FHBox30);
        FHBox30.applyProperties();
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(0);
        FHBox31.setTop(0);
        FHBox31.setWidth(80);
        FHBox31.setHeight(20);
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(0);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(1);
        FHBox31.setFlexVflex("ftMin");
        FHBox31.setFlexHflex("ftFalse");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        FHBox30.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFLabel FLabel12 = new TFLabel();

    private void init_FLabel12() {
        FLabel12.setName("FLabel12");
        FLabel12.setLeft(0);
        FLabel12.setTop(0);
        FLabel12.setWidth(65);
        FLabel12.setHeight(13);
        FLabel12.setCaption("Complemento");
        FLabel12.setFontColor("clWindowText");
        FLabel12.setFontSize(-11);
        FLabel12.setFontName("Tahoma");
        FLabel12.setFontStyle("[]");
        FLabel12.setVerticalAlignment("taVerticalCenter");
        FLabel12.setWordBreak(false);
        FHBox31.addChildren(FLabel12);
        FLabel12.applyProperties();
    }

    public TFHBox FHBox32 = new TFHBox();

    private void init_FHBox32() {
        FHBox32.setName("FHBox32");
        FHBox32.setLeft(80);
        FHBox32.setTop(0);
        FHBox32.setWidth(100);
        FHBox32.setHeight(20);
        FHBox32.setBorderStyle("stNone");
        FHBox32.setPaddingTop(0);
        FHBox32.setPaddingLeft(0);
        FHBox32.setPaddingRight(0);
        FHBox32.setPaddingBottom(0);
        FHBox32.setMarginTop(0);
        FHBox32.setMarginLeft(0);
        FHBox32.setMarginRight(0);
        FHBox32.setMarginBottom(0);
        FHBox32.setSpacing(1);
        FHBox32.setFlexVflex("ftMin");
        FHBox32.setFlexHflex("ftTrue");
        FHBox32.setScrollable(false);
        FHBox32.setBoxShadowConfigHorizontalLength(10);
        FHBox32.setBoxShadowConfigVerticalLength(10);
        FHBox32.setBoxShadowConfigBlurRadius(5);
        FHBox32.setBoxShadowConfigSpreadRadius(0);
        FHBox32.setBoxShadowConfigShadowColor("clBlack");
        FHBox32.setBoxShadowConfigOpacity(75);
        FHBox32.setVAlign("tvTop");
        FHBox30.addChildren(FHBox32);
        FHBox32.applyProperties();
    }

    public TFLabel lblComplementoAtual = new TFLabel();

    private void init_lblComplementoAtual() {
        lblComplementoAtual.setName("lblComplementoAtual");
        lblComplementoAtual.setLeft(0);
        lblComplementoAtual.setTop(0);
        lblComplementoAtual.setWidth(65);
        lblComplementoAtual.setHeight(13);
        lblComplementoAtual.setCaption("Complemento");
        lblComplementoAtual.setFontColor("clRed");
        lblComplementoAtual.setFontSize(-11);
        lblComplementoAtual.setFontName("Tahoma");
        lblComplementoAtual.setFontStyle("[]");
        lblComplementoAtual.setVerticalAlignment("taVerticalCenter");
        lblComplementoAtual.setWordBreak(false);
        FHBox32.addChildren(lblComplementoAtual);
        lblComplementoAtual.applyProperties();
    }

    public TFHBox FHBox33 = new TFHBox();

    private void init_FHBox33() {
        FHBox33.setName("FHBox33");
        FHBox33.setLeft(180);
        FHBox33.setTop(0);
        FHBox33.setWidth(100);
        FHBox33.setHeight(20);
        FHBox33.setBorderStyle("stNone");
        FHBox33.setPaddingTop(0);
        FHBox33.setPaddingLeft(0);
        FHBox33.setPaddingRight(0);
        FHBox33.setPaddingBottom(0);
        FHBox33.setMarginTop(0);
        FHBox33.setMarginLeft(0);
        FHBox33.setMarginRight(0);
        FHBox33.setMarginBottom(0);
        FHBox33.setSpacing(1);
        FHBox33.setFlexVflex("ftMin");
        FHBox33.setFlexHflex("ftTrue");
        FHBox33.setScrollable(false);
        FHBox33.setBoxShadowConfigHorizontalLength(10);
        FHBox33.setBoxShadowConfigVerticalLength(10);
        FHBox33.setBoxShadowConfigBlurRadius(5);
        FHBox33.setBoxShadowConfigSpreadRadius(0);
        FHBox33.setBoxShadowConfigShadowColor("clBlack");
        FHBox33.setBoxShadowConfigOpacity(75);
        FHBox33.setVAlign("tvTop");
        FHBox30.addChildren(FHBox33);
        FHBox33.applyProperties();
    }

    public TFLabel lblComplementoCorreio = new TFLabel();

    private void init_lblComplementoCorreio() {
        lblComplementoCorreio.setName("lblComplementoCorreio");
        lblComplementoCorreio.setLeft(0);
        lblComplementoCorreio.setTop(0);
        lblComplementoCorreio.setWidth(65);
        lblComplementoCorreio.setHeight(13);
        lblComplementoCorreio.setCaption("Complemento");
        lblComplementoCorreio.setFontColor("clWindowText");
        lblComplementoCorreio.setFontSize(-11);
        lblComplementoCorreio.setFontName("Tahoma");
        lblComplementoCorreio.setFontStyle("[]");
        lblComplementoCorreio.setVerticalAlignment("taVerticalCenter");
        lblComplementoCorreio.setWordBreak(false);
        FHBox33.addChildren(lblComplementoCorreio);
        lblComplementoCorreio.applyProperties();
    }

    public TFCoachmark FCoachmark = new TFCoachmark();

    private void init_FCoachmark() {
        FCoachmark.setName("FCoachmark");
        FCoachmark.setShowNextButton(false);
        FCoachmark.setShowPriorButton(false);
        TFCoachmarkItem item18 = new TFCoachmarkItem();
        item18.setTargetName("btnSalvar");
        item18.setPosition("poBefore_start");
        item18.setName("btnSalvar");
        FCoachmark.getItems().add(item18);
        TFCoachmarkItem item19 = new TFCoachmarkItem();
        item19.setTargetName("edtCep");
        item19.setPosition("poBefore_start");
        item19.setName("edtCep");
        FCoachmark.getItems().add(item19);
        FrmCepClienteOnLineService.addChildren(FCoachmark);
        FCoachmark.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbbTipoEnderecoChange(final Event<Object> event);

    public abstract void edtCepEnter(final Event<Object> event);

    public abstract void vBoxApagarNomeCliClick(final Event<Object> event);

    public void btnCepClick(final Event<Object> event) {
        if (btnCep.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCep");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}