package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAlterarSenha extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AlterarSenhaRNA rn = null;

    public FrmAlterarSenha() {
        try {
            rn = (freedom.bytecode.rn.AlterarSenhaRNA) getRN(freedom.bytecode.rn.wizard.AlterarSenhaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxPrincipal();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnSalvar();
        init_vBoxSenhaAtual();
        init_lblSenhaAtual();
        init_edtSenhaAtual();
        init_FVBox1();
        init_lblNovaSenha();
        init_edtNovaSenha();
        init_vBoxConfirmarSenha();
        init_lblConfirmarSenha();
        init_edtConfirmarSenha();
        init_FrmAlterarSenha();
    }

    protected TFForm FrmAlterarSenha = this;
    private void init_FrmAlterarSenha() {
        FrmAlterarSenha.setName("FrmAlterarSenha");
        FrmAlterarSenha.setCaption("Alterar Senha");
        FrmAlterarSenha.setClientHeight(261);
        FrmAlterarSenha.setClientWidth(484);
        FrmAlterarSenha.setColor("clBtnFace");
        FrmAlterarSenha.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAlterarSenha", "FrmAlterarSenha", "OnCreate");
        });
        FrmAlterarSenha.setWOrigem("EhMain");
        FrmAlterarSenha.setWKey("7000146");
        FrmAlterarSenha.setSpacing(0);
        FrmAlterarSenha.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(484);
        vBoxPrincipal.setHeight(261);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(5);
        vBoxPrincipal.setMarginLeft(5);
        vBoxPrincipal.setMarginRight(5);
        vBoxPrincipal.setMarginBottom(5);
        vBoxPrincipal.setSpacing(5);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmAlterarSenha.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(185);
        hBoxBotoes.setHeight(55);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(5);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(50);
        btnVoltar.setHeight(50);
        btnVoltar.setHint("Inclui um Novo Registro");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAlterarSenha", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(true);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(50);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(50);
        btnSalvar.setHeight(50);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAlterarSenha", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(700080);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox vBoxSenhaAtual = new TFVBox();

    private void init_vBoxSenhaAtual() {
        vBoxSenhaAtual.setName("vBoxSenhaAtual");
        vBoxSenhaAtual.setLeft(0);
        vBoxSenhaAtual.setTop(56);
        vBoxSenhaAtual.setWidth(185);
        vBoxSenhaAtual.setHeight(45);
        vBoxSenhaAtual.setBorderStyle("stNone");
        vBoxSenhaAtual.setPaddingTop(0);
        vBoxSenhaAtual.setPaddingLeft(0);
        vBoxSenhaAtual.setPaddingRight(0);
        vBoxSenhaAtual.setPaddingBottom(0);
        vBoxSenhaAtual.setMarginTop(0);
        vBoxSenhaAtual.setMarginLeft(0);
        vBoxSenhaAtual.setMarginRight(0);
        vBoxSenhaAtual.setMarginBottom(0);
        vBoxSenhaAtual.setSpacing(1);
        vBoxSenhaAtual.setFlexVflex("ftMin");
        vBoxSenhaAtual.setFlexHflex("ftTrue");
        vBoxSenhaAtual.setScrollable(false);
        vBoxSenhaAtual.setBoxShadowConfigHorizontalLength(10);
        vBoxSenhaAtual.setBoxShadowConfigVerticalLength(10);
        vBoxSenhaAtual.setBoxShadowConfigBlurRadius(5);
        vBoxSenhaAtual.setBoxShadowConfigSpreadRadius(0);
        vBoxSenhaAtual.setBoxShadowConfigShadowColor("clBlack");
        vBoxSenhaAtual.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxSenhaAtual);
        vBoxSenhaAtual.applyProperties();
    }

    public TFLabel lblSenhaAtual = new TFLabel();

    private void init_lblSenhaAtual() {
        lblSenhaAtual.setName("lblSenhaAtual");
        lblSenhaAtual.setLeft(0);
        lblSenhaAtual.setTop(0);
        lblSenhaAtual.setWidth(58);
        lblSenhaAtual.setHeight(13);
        lblSenhaAtual.setCaption("Senha Atual");
        lblSenhaAtual.setFontColor("clWindowText");
        lblSenhaAtual.setFontSize(-11);
        lblSenhaAtual.setFontName("Tahoma");
        lblSenhaAtual.setFontStyle("[]");
        lblSenhaAtual.setVerticalAlignment("taVerticalCenter");
        lblSenhaAtual.setWordBreak(false);
        vBoxSenhaAtual.addChildren(lblSenhaAtual);
        lblSenhaAtual.applyProperties();
    }

    public TFString edtSenhaAtual = new TFString();

    private void init_edtSenhaAtual() {
        edtSenhaAtual.setName("edtSenhaAtual");
        edtSenhaAtual.setLeft(0);
        edtSenhaAtual.setTop(14);
        edtSenhaAtual.setWidth(140);
        edtSenhaAtual.setHeight(24);
        edtSenhaAtual.setHint("Senha atual");
        edtSenhaAtual.setHelpCaption("Senha atual");
        edtSenhaAtual.setFlex(true);
        edtSenhaAtual.setRequired(false);
        edtSenhaAtual.setPrompt("Senha atual");
        edtSenhaAtual.setConstraintCheckWhen("cwImmediate");
        edtSenhaAtual.setConstraintCheckType("ctExpression");
        edtSenhaAtual.setConstraintFocusOnError(false);
        edtSenhaAtual.setConstraintEnableUI(true);
        edtSenhaAtual.setConstraintEnabled(false);
        edtSenhaAtual.setConstraintFormCheck(true);
        edtSenhaAtual.setCharCase("ccNormal");
        edtSenhaAtual.setPwd(true);
        edtSenhaAtual.setMaxlength(30);
        edtSenhaAtual.setFontColor("clWindowText");
        edtSenhaAtual.setFontSize(-13);
        edtSenhaAtual.setFontName("Tahoma");
        edtSenhaAtual.setFontStyle("[]");
        edtSenhaAtual.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtSenhaAtualEnter(event);
            processarFlow("FrmAlterarSenha", "edtSenhaAtual", "OnEnter");
        });
        edtSenhaAtual.setSaveLiteralCharacter(false);
        edtSenhaAtual.applyProperties();
        vBoxSenhaAtual.addChildren(edtSenhaAtual);
        addValidatable(edtSenhaAtual);
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(102);
        FVBox1.setWidth(185);
        FVBox1.setHeight(45);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftMin");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel lblNovaSenha = new TFLabel();

    private void init_lblNovaSenha() {
        lblNovaSenha.setName("lblNovaSenha");
        lblNovaSenha.setLeft(0);
        lblNovaSenha.setTop(0);
        lblNovaSenha.setWidth(58);
        lblNovaSenha.setHeight(13);
        lblNovaSenha.setCaption("Nova Senha");
        lblNovaSenha.setFontColor("clWindowText");
        lblNovaSenha.setFontSize(-11);
        lblNovaSenha.setFontName("Tahoma");
        lblNovaSenha.setFontStyle("[]");
        lblNovaSenha.setVerticalAlignment("taVerticalCenter");
        lblNovaSenha.setWordBreak(false);
        FVBox1.addChildren(lblNovaSenha);
        lblNovaSenha.applyProperties();
    }

    public TFString edtNovaSenha = new TFString();

    private void init_edtNovaSenha() {
        edtNovaSenha.setName("edtNovaSenha");
        edtNovaSenha.setLeft(0);
        edtNovaSenha.setTop(14);
        edtNovaSenha.setWidth(140);
        edtNovaSenha.setHeight(24);
        edtNovaSenha.setHint("Nova senha");
        edtNovaSenha.setHelpCaption("Nova senha");
        edtNovaSenha.setFlex(true);
        edtNovaSenha.setRequired(false);
        edtNovaSenha.setPrompt("Nova senha");
        edtNovaSenha.setConstraintCheckWhen("cwImmediate");
        edtNovaSenha.setConstraintCheckType("ctExpression");
        edtNovaSenha.setConstraintFocusOnError(false);
        edtNovaSenha.setConstraintEnableUI(true);
        edtNovaSenha.setConstraintEnabled(false);
        edtNovaSenha.setConstraintFormCheck(true);
        edtNovaSenha.setCharCase("ccNormal");
        edtNovaSenha.setPwd(true);
        edtNovaSenha.setMaxlength(30);
        edtNovaSenha.setFontColor("clWindowText");
        edtNovaSenha.setFontSize(-13);
        edtNovaSenha.setFontName("Tahoma");
        edtNovaSenha.setFontStyle("[]");
        edtNovaSenha.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtNovaSenhaEnter(event);
            processarFlow("FrmAlterarSenha", "edtNovaSenha", "OnEnter");
        });
        edtNovaSenha.setSaveLiteralCharacter(false);
        edtNovaSenha.applyProperties();
        FVBox1.addChildren(edtNovaSenha);
        addValidatable(edtNovaSenha);
    }

    public TFVBox vBoxConfirmarSenha = new TFVBox();

    private void init_vBoxConfirmarSenha() {
        vBoxConfirmarSenha.setName("vBoxConfirmarSenha");
        vBoxConfirmarSenha.setLeft(0);
        vBoxConfirmarSenha.setTop(148);
        vBoxConfirmarSenha.setWidth(185);
        vBoxConfirmarSenha.setHeight(45);
        vBoxConfirmarSenha.setBorderStyle("stNone");
        vBoxConfirmarSenha.setPaddingTop(0);
        vBoxConfirmarSenha.setPaddingLeft(0);
        vBoxConfirmarSenha.setPaddingRight(0);
        vBoxConfirmarSenha.setPaddingBottom(0);
        vBoxConfirmarSenha.setMarginTop(0);
        vBoxConfirmarSenha.setMarginLeft(0);
        vBoxConfirmarSenha.setMarginRight(0);
        vBoxConfirmarSenha.setMarginBottom(0);
        vBoxConfirmarSenha.setSpacing(1);
        vBoxConfirmarSenha.setFlexVflex("ftMin");
        vBoxConfirmarSenha.setFlexHflex("ftTrue");
        vBoxConfirmarSenha.setScrollable(false);
        vBoxConfirmarSenha.setBoxShadowConfigHorizontalLength(10);
        vBoxConfirmarSenha.setBoxShadowConfigVerticalLength(10);
        vBoxConfirmarSenha.setBoxShadowConfigBlurRadius(5);
        vBoxConfirmarSenha.setBoxShadowConfigSpreadRadius(0);
        vBoxConfirmarSenha.setBoxShadowConfigShadowColor("clBlack");
        vBoxConfirmarSenha.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxConfirmarSenha);
        vBoxConfirmarSenha.applyProperties();
    }

    public TFLabel lblConfirmarSenha = new TFLabel();

    private void init_lblConfirmarSenha() {
        lblConfirmarSenha.setName("lblConfirmarSenha");
        lblConfirmarSenha.setLeft(0);
        lblConfirmarSenha.setTop(0);
        lblConfirmarSenha.setWidth(80);
        lblConfirmarSenha.setHeight(13);
        lblConfirmarSenha.setCaption("Confirmar Senha");
        lblConfirmarSenha.setFontColor("clWindowText");
        lblConfirmarSenha.setFontSize(-11);
        lblConfirmarSenha.setFontName("Tahoma");
        lblConfirmarSenha.setFontStyle("[]");
        lblConfirmarSenha.setVerticalAlignment("taVerticalCenter");
        lblConfirmarSenha.setWordBreak(false);
        vBoxConfirmarSenha.addChildren(lblConfirmarSenha);
        lblConfirmarSenha.applyProperties();
    }

    public TFString edtConfirmarSenha = new TFString();

    private void init_edtConfirmarSenha() {
        edtConfirmarSenha.setName("edtConfirmarSenha");
        edtConfirmarSenha.setLeft(0);
        edtConfirmarSenha.setTop(14);
        edtConfirmarSenha.setWidth(140);
        edtConfirmarSenha.setHeight(24);
        edtConfirmarSenha.setHint("Confirmar Senha");
        edtConfirmarSenha.setHelpCaption("Confirmar Senha");
        edtConfirmarSenha.setFlex(true);
        edtConfirmarSenha.setRequired(false);
        edtConfirmarSenha.setPrompt("Confirmar Senha");
        edtConfirmarSenha.setConstraintCheckWhen("cwImmediate");
        edtConfirmarSenha.setConstraintCheckType("ctExpression");
        edtConfirmarSenha.setConstraintFocusOnError(false);
        edtConfirmarSenha.setConstraintEnableUI(true);
        edtConfirmarSenha.setConstraintEnabled(false);
        edtConfirmarSenha.setConstraintFormCheck(true);
        edtConfirmarSenha.setCharCase("ccNormal");
        edtConfirmarSenha.setPwd(true);
        edtConfirmarSenha.setMaxlength(30);
        edtConfirmarSenha.setAlign("alLeft");
        edtConfirmarSenha.setFontColor("clWindowText");
        edtConfirmarSenha.setFontSize(-13);
        edtConfirmarSenha.setFontName("Tahoma");
        edtConfirmarSenha.setFontStyle("[]");
        edtConfirmarSenha.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtConfirmarSenhaEnter(event);
            processarFlow("FrmAlterarSenha", "edtConfirmarSenha", "OnEnter");
        });
        edtConfirmarSenha.setSaveLiteralCharacter(false);
        edtConfirmarSenha.applyProperties();
        vBoxConfirmarSenha.addChildren(edtConfirmarSenha);
        addValidatable(edtConfirmarSenha);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtSenhaAtualEnter(final Event<Object> event);

    public abstract void edtNovaSenhaEnter(final Event<Object> event);

    public abstract void edtConfirmarSenhaEnter(final Event<Object> event);

}