package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmFrameChatWhatsApp extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.FrameChatWhatsAppRNA rn = null;

    public FrmFrameChatWhatsApp() {
        try {
            rn = (freedom.bytecode.rn.FrameChatWhatsAppRNA) getRN(freedom.bytecode.rn.wizard.FrameChatWhatsAppRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbConsultaNumberWhats();
        init_tbWhatsappAtendimento();
        init_tbKeyHashApiChat();
        init_tbCadastroWhatsapp();
        init_tbEventos();
        init_frameChat();
        init_sc();
        init_FrmFrameChatWhatsApp();
    }

    public CRM_CONSULTA_NUMBER_WHATS tbConsultaNumberWhats;

    private void init_tbConsultaNumberWhats() {
        tbConsultaNumberWhats = rn.tbConsultaNumberWhats;
        tbConsultaNumberWhats.setName("tbConsultaNumberWhats");
        tbConsultaNumberWhats.setMaxRowCount(200);
        tbConsultaNumberWhats.setWKey("4600408;46002");
        tbConsultaNumberWhats.setRatioBatchSize(20);
        getTables().put(tbConsultaNumberWhats, "tbConsultaNumberWhats");
        tbConsultaNumberWhats.applyProperties();
    }

    public CRM_WHATSAPP_ATENDIMENTO tbWhatsappAtendimento;

    private void init_tbWhatsappAtendimento() {
        tbWhatsappAtendimento = rn.tbWhatsappAtendimento;
        tbWhatsappAtendimento.setName("tbWhatsappAtendimento");
        tbWhatsappAtendimento.setMaxRowCount(200);
        tbWhatsappAtendimento.setWKey("4600408;46003");
        tbWhatsappAtendimento.setRatioBatchSize(20);
        getTables().put(tbWhatsappAtendimento, "tbWhatsappAtendimento");
        tbWhatsappAtendimento.applyProperties();
    }

    public GET_KEY_HASH_API_CHAT tbKeyHashApiChat;

    private void init_tbKeyHashApiChat() {
        tbKeyHashApiChat = rn.tbKeyHashApiChat;
        tbKeyHashApiChat.setName("tbKeyHashApiChat");
        tbKeyHashApiChat.setMaxRowCount(200);
        tbKeyHashApiChat.setWKey("4600408;46004");
        tbKeyHashApiChat.setRatioBatchSize(20);
        getTables().put(tbKeyHashApiChat, "tbKeyHashApiChat");
        tbKeyHashApiChat.applyProperties();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp;

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp = rn.tbCadastroWhatsapp;
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("4600408;46005");
        tbCadastroWhatsapp.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsapp, "tbCadastroWhatsapp");
        tbCadastroWhatsapp.applyProperties();
    }

    public CRM_EVENTOS tbEventos;

    private void init_tbEventos() {
        tbEventos = rn.tbEventos;
        tbEventos.setName("tbEventos");
        tbEventos.setMaxRowCount(200);
        tbEventos.setWKey("4600408;29601");
        tbEventos.setRatioBatchSize(20);
        getTables().put(tbEventos, "tbEventos");
        tbEventos.applyProperties();
    }

    protected TFForm FrmFrameChatWhatsApp = this;
    private void init_FrmFrameChatWhatsApp() {
        FrmFrameChatWhatsApp.setName("FrmFrameChatWhatsApp");
        FrmFrameChatWhatsApp.setCaption("WhatsApp");
        FrmFrameChatWhatsApp.setClientHeight(362);
        FrmFrameChatWhatsApp.setClientWidth(484);
        FrmFrameChatWhatsApp.setColor("clBtnFace");
        FrmFrameChatWhatsApp.setWOrigem("EhMain");
        FrmFrameChatWhatsApp.setWKey("4600408");
        FrmFrameChatWhatsApp.setSpacing(0);
        FrmFrameChatWhatsApp.applyProperties();
    }

    public TFFrame frameChat = new TFFrame();

    private void init_frameChat() {
        frameChat.setName("frameChat");
        frameChat.setLeft(0);
        frameChat.setTop(0);
        frameChat.setWidth(484);
        frameChat.setHeight(362);
        frameChat.setAlign("alClient");
        frameChat.setFlexVflex("ftTrue");
        frameChat.setFlexHflex("ftTrue");
        FrmFrameChatWhatsApp.addChildren(frameChat);
        frameChat.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbWhatsappAtendimento);
        sc.getTables().add(item0);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}