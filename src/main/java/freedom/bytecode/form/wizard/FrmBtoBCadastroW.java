/* -------------------------------------------------------------------------
   Projeto Freedom - Cliente - Versao: 1.0.3.14
   Class Main  : BtoBCadastro
   Analista    : EMERSON
   Data Created: 03/12/2018 14:44:50
   Data Changed: 30/12/1899 00:00:00
  -------------------------------------------------------------------------- */

package freedom.bytecode.form.wizard;

import freedom.bytecode.rn.BtoBCadastroRNA;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.grid.TFGridExporter;
import freedom.client.controls.impl.treegrid.TFTreeGridExporter;
import freedom.client.controls.IBaseComponent;
import freedom.client.event.Event;
import freedom.client.controls.IFocusable;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.RowState;
import freedom.data.TableState;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.impl.Row;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;


public abstract class FrmBtoBCadastroW extends FrmBtoBCadastro {

    private static final long serialVersionUID = 20130827081850L;
    public TableState oper = TableState.QUERYING; 

    public FrmBtoBCadastroW() {
        lblMensagem.setCaption("");
        habilitaComp(false);
        
		tabListagem.setFontSize(-20);
		tabCadastro.setFontSize(-20);

        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao Abrir Tabelas Auxiliares")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnFiltroAvancado.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled && !menuSelecaoMultipla.isChecked());
        btnAlterar.setEnabled(!enabled && !tbBtobLink.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbBtobLink.isEmpty());
        if (! menuHabilitaNavegacao.isChecked()) {                                       // menu popup habilitar navegação
            btnProximo.setEnabled(!enabled && !tbBtobLink.isEmpty());
            btnAnterior.setEnabled(!enabled && !tbBtobLink.isEmpty());
        }
        btnAceitar.setEnabled(!enabled && !tbBtobLink.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);
        btnSalvarContinuar.setEnabled(enabled && !menuSelecaoMultipla.isChecked());
        menuSelecaoMultipla.setVisible(!enabled);
        
        edIdLink34001.setEnabled(enabled && ( ! tbBtobLink.isEmpty()  || tbBtobLink.getState() == TableState.INSERTING));
        edNome34001.setEnabled(enabled && ( ! tbBtobLink.isEmpty()  || tbBtobLink.getState() == TableState.INSERTING));
        edLink34001.setEnabled(enabled && ( ! tbBtobLink.isEmpty()  || tbBtobLink.getState() == TableState.INSERTING));
        edResponsavel34001.setEnabled(enabled && ( ! tbBtobLink.isEmpty()  || tbBtobLink.getState() == TableState.INSERTING));
        edContatoFone34001.setEnabled(enabled && ( ! tbBtobLink.isEmpty()  || tbBtobLink.getState() == TableState.INSERTING));

        
    }

    @Override
    public void btnConsultarClick(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }
    
    @Override
    public void btnFiltroAvancadoClick(Event<Object> event) {
        filtroAvancado.doModal();
    }    

    @Override
    public void btnNovoClick(final Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            if (menuSelecaoMultipla.isChecked()) {
                onSalvarMultiplo();
            } else {
                onSalvar();
            }
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    public void executaFiltroPrincipal() {
        tbBtobLink.clearFilters();

    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        if (menuHabilitaNavegacao.isChecked()) {
            btnProximo.setEnabled(true);
            btnAnterior.setEnabled(true);
        } else {
            btnProximo.setEnabled(btnNovo.isEnabled());
            btnAnterior.setEnabled(btnNovo.isEnabled());
        }
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {

        boolean checkedMenu = menuSelecaoMultipla.isChecked();
        gridPrincipal.setMultiSelection(checkedMenu);

        // tratamento das abas visto que pode mexer somente na tabela master
        for (int i = 2; i <= pgPrincipal.getPageCount()-1; i++) {
             pgPrincipal.selectTab(i);
             pgPrincipal.getSelectedTab().setVisible(!checkedMenu);
        }

        // opções da barra de ferramenta
        btnNovo.setEnabled(! checkedMenu && btnAlterar.isEnabled());
        btnSalvarContinuar.setEnabled(! checkedMenu && btnAlterar.isEnabled());


        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()+30);
            // desregistra o marter table dos componentes
                        edIdLink34001.setTable(null);
            edIdLink34001.setValue(null);
            edNome34001.setTable(null);
            edNome34001.setValue(null);
            edLink34001.setTable(null);
            edLink34001.setValue(null);
            edResponsavel34001.setTable(null);
            edResponsavel34001.setValue(null);
            edContatoFone34001.setTable(null);
            edContatoFone34001.setValue(null);

            gridPrincipal.clearSelection();
        } else {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()-30);
            // registra o master table para os componentes
                        edIdLink34001.setTable(tbBtobLink);
            edNome34001.setTable(tbBtobLink);
            edLink34001.setTable(tbBtobLink);
            edResponsavel34001.setTable(tbBtobLink);
            edContatoFone34001.setTable(tbBtobLink);

        }
        pgPrincipal.selectTab(0);
    }

    @Override
    public void FrmBtoBCadastrokeyActionPesquisar(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionIncluir(Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionExcluir(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionSalvar(Event<Object> event) {
        try {
            onSalvar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionSalvarContinuar(Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionCancelar(Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionAnterior(Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionProximo(Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmBtoBCadastrokeyActionAceitar(Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }    
    
    @Override
    public void btnMaisClick(Event<Object> event) {
        popMenuPrincipal.open(this);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(Event<Object> event) {
        try {
            onAbreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Aviso")
                  .message("Tabela Auxiliares Foram Reabertas")
                  .showInformation();
        }
    }

    @Override
    public void menuItemConfgGridClick(Event<Object> event) {
        gridConfig.doModal();
    }

    @Override
    public void menuItemHelpClick(Event<Object> event) {
        FormUtil.redirect("help/FrmBtoBCadastro.zul", true);
    }

    protected void onConsultar() throws Exception {
        
        tbBtobLink.close();
        executaFiltroPrincipal();
        tbBtobLink.setOrderBy("NOME");
        tbBtobLink.open();
        habilitaComp(false);

        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.clearSelection();
        }

        if (tbBtobLink.isEmpty()) {
            Dialog.create()
                      .title("Aviso")
                      .message("Registro Não Encontrado...")
                      .showInformation();
        }
    }

    protected void onAnterior() throws Exception {

        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbBtobLink.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnAnterior);
        }

        if (tbBtobLink.bof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o Primeiro Registro")
                    .showInformation();
        } else {
            tbBtobLink.prior();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onProximo() throws Exception {
        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbBtobLink.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnProximo);
        }

        if (tbBtobLink.eof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o último registgro")
                    .showInformation();
        } else {
            tbBtobLink.next();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onIncluir() throws Exception {
        oper = TableState.INSERTING; 
        
        rn.incluir(); 
        
        
        pgPrincipal.selectTab(1);
        edIdLink34001.setFocus();
        habilitaComp(true);
        lblMensagem.setCaption("Incluindo...");
    }

    protected void onAlterar() throws Exception {
        oper = TableState.MODIFYING;

        if (menuSelecaoMultipla.isChecked()) {
            lblMensagem.setCaption("ATENÇÃO: Alterando multiplos registros. Será alterado todos os registros selecionados...");
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } else {
            if (!tbBtobLink.isEmpty()) {
                rn.alterar();
                if (pgPrincipal.getSelectedIndex() == 0)  {
                   pgPrincipal.selectTab(1);
                }                
                habilitaComp(true);
                edIdLink34001.setFocus();
                lblMensagem.setCaption("Alterando "+tbBtobLink.getNOME().asString() + ", " + tbBtobLink.getLINK().asString()+"...");
            } else {
                Dialog.create()
                      .title("Erro ao editar")
                      .message("Selecione um registro antes de editar")
                      .showError();
            }
        }
    }

    protected void onExcluir() throws DataException {
        if (!tbBtobLink.isEmpty()) {
           oper = TableState.DELETING; 
           String titulo;
           String mensagem;
           if (menuSelecaoMultipla.isChecked()) {
               titulo = "Exclusão Multipla";
               mensagem = "ATENÇÃO: Serão excluido(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?";
           } else {
               titulo = "Exclusão de Registro";
               mensagem = "Confirma a exclusão do registro selecionado?";
           }

            Dialog.create()
                    .title(titulo)
                    .message(mensagem)
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                try {
                                    tbBtobLink.disableControls();
                                    tbBtobLink.disableMasterTable();
                                    if (menuSelecaoMultipla.isChecked()) {
                                        for (int bm : gridPrincipal.getSelectedIndices(false)) {
                                            tbBtobLink.gotoBookmark(bm);
                                            rn.excluiTableMaster();
                                        }
                                    } else {
                                        rn.excluiTableMaster();
                                    }

                                    try {
                                        rn.excluir();                                        
                                        
                                        habilitaComp(false);
                                    } catch (DataException e) {
                                        throw e;
                                    }
                                } finally {
                                    tbBtobLink.enableControls();
                                    tbBtobLink.enableMasterTable();
                                    oper = TableState.QUERYING;
                                }
                            } catch (DataException ex) {
                                Dialog.create()
                                    .title("Erro ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex);

                                try {
                                    tbBtobLink.cancelUpdates();
                                } catch (DataException ex1) {
                                    Dialog.create()
                                    .title("Erro no CancelUpdates ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex1);
                                }

                            }
                        }
                    });
        } else {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message("Selecione um registro antes de excluir")
                    .showError();
        }
    }

    protected void onSalvarMultiplo() throws DataException {

        Dialog.create()
            .title("Alteração Multipla")
            .message("ATENÇÃO: Serão alterado(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?")
            .confirmSimNao((String dialogResult) -> {
                if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                     try {
                           tbBtobLink.disableControls();
                           int lastBookmark = tbBtobLink.getBookmark();
                           try {
                               for (int bm : gridPrincipal.getSelectedIndices(true)) {
                                   tbBtobLink.gotoBookmark(bm);
                                   tbBtobLink.edit();
                                   
                                   if ( ! edIdLink34001.getValue().isNull()) {
                                       tbBtobLink.setID_LINK(edIdLink34001.getValue());
                                   }
                                   if ( ! edNome34001.getValue().isNull()) {
                                       tbBtobLink.setNOME(edNome34001.getValue());
                                   }
                                   if ( ! edLink34001.getValue().isNull()) {
                                       tbBtobLink.setLINK(edLink34001.getValue());
                                   }
                                   if ( ! edResponsavel34001.getValue().isNull()) {
                                       tbBtobLink.setRESPONSAVEL(edResponsavel34001.getValue());
                                   }
                                   if ( ! edContatoFone34001.getValue().isNull()) {
                                       tbBtobLink.setCONTATO_FONE(edContatoFone34001.getValue());
                                   }

                                   tbBtobLink.post();
                               }

                               onSalvar();

                           } finally {
                               tbBtobLink.close();
                               tbBtobLink.open();
                               // tbBtobLink.gotoBookmark(lastBookmark);
                               tbBtobLink.enableControls();
                               gridPrincipal.clearSelection();
                           }

                     } catch (DataException e) {
                         Dialog.create()
                               .title("Erro ao salvar")
                               .message(e.getMessage())
                               .showException(e);
                    }
                }
        });
    }

    protected void onSalvar() throws DataException {
        // executa a validação das constraint dos objetos edition
        check();
        if (!getErrorMap().isEmpty()) {
            StringBuilder strBuilder = new StringBuilder();

            getErrorMap().values().stream().forEach((s) -> {
                strBuilder.append(s).append("\n");
            });        

            // manda o focu para o primeiro objeto que deu erro de constraint
            ((IFocusable)getErrorMap().keySet().iterator().next()).setFocus();

            Dialog.create()
                  .title("Erro ao validar")
                  .message("Existe validação(s) pendente...\n" + strBuilder.toString())
                  .showError();

            return;
        }

        // seta Calc. Update
        setCalcUpdate();

        // executar o metodo salvar na RN
        rn.salvar();

        // atualiza o registro
        tbBtobLink.refreshRecord();
        
        habilitaComp(false);
        oper = TableState.QUERYING;
        lblMensagem.setCaption("");
    }

    protected void onSalvarContinuar() throws DataException {
        try {
            TableState st = tbBtobLink.getState();
            onSalvar();
            if (st == TableState.INSERTING) {
                onIncluir();
            } else if (st == TableState.MODIFYING) {
                onAlterar();
            }
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar a edição")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void onCancelar() throws DataException {
        habilitaComp(false);
        rn.cancelar();
        
        oper = TableState.QUERYING; 
        lblMensagem.setCaption("Registro Selecionado: "+tbBtobLink.getNOME().asString() + ", " + tbBtobLink.getLINK().asString());
    }
    
    protected void onAceitar() throws Exception {
        if (FormUtil.isExternalCall()) {
            // passa os parametros para a resposta ao VB
            FormUtil.externalCall(tbBtobLink.getField("ID_PESSOA").asString());
        } else {
            close();
        }
    }
    
    protected void onAbreTabelaAux() throws DataException {
        ISession s = SessionFactory.getInstance().getSession();
        try {                
            s.open();
            
            tbBtobLink.setSession(s);
            tbBtobLink.refreshRecord();
            tbBtobLink.setSession(null);
        } finally {
            if (s != null) {
                s.close();
            }
        }
    }
    
    protected void setCalcUpdate() throws DataException {
        
        postTable();
    }

    private void postTable() throws DataException {
        tbBtobLink.post();
    }

    public void loadFormPk(Integer idLink ) throws DataException {
        tbBtobLink.close(); 
        tbBtobLink.clearFilters();
        
        if (idLink > 0) {
            tbBtobLink.addFilter("ID_LINK");
            tbBtobLink.addParam("ID_LINK", idLink);
        } else return;
        
        tbBtobLink.open();
        habilitaComp(false);          // se tem registro habilita botões da barra de ferramenta  
    }         

    // retorna true se o master esta sendo editado, pode ser usado para verificar se o form esta 
    // habilitado edição
    public boolean masterIsEditing() {
       return (tbBtobLink.getState() != TableState.QUERYING);
    }
    
    
    @Override
    public void tbBtobLinkAfterScroll(final Event<Object> event) {
        lblMensagem.setCaption("Selecionado: "+tbBtobLink.getNOME().asString() + ", " + tbBtobLink.getLINK().asString());

    }

        
            
        // Metodo responsavel por avisar o Usuario sobre a duplicidade da Pk 
    protected void onVerificaBtobLinkPk(IBaseComponent sender) throws DataException {
        if (! tbBtobLink.getField("ID_LINK").isNull() && 
            (tbBtobLink.getState() == TableState.INSERTING || 
            (tbBtobLink.getState() == TableState.MODIFYING && 
            ! tbBtobLink.getField("ID_LINK").oldValue().isNull() && 
            ! tbBtobLink.getField("ID_LINK").equals(tbBtobLink.getField("ID_LINK").oldValue()))) && 
            (tbBtobLink.obtem(tbBtobLink.getField("ID_LINK").asInteger()) != null)) {
                 Dialog.create().showNotificationInfo("Registro Já Cadastrado...", "end_after", 3000, sender);
        }
    }

    @Override
    public void edIdLink34001Exit(final Event<Object> event) {
        try {
            onVerificaBtobLinkPk(event.getTarget());
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao Acessar o Metodo Remoto RN verificaBtobLinkPk")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

        
    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void gridPrincipalClickImageAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuItemExportPdfClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportPdf(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportExcelClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportExcel(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }




}

