package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmGerencialPainel extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.GerencialPainelRNA rn = null;

    public FrmGerencialPainel() {
        try {
            rn = (freedom.bytecode.rn.GerencialPainelRNA) getRN(freedom.bytecode.rn.wizard.GerencialPainelRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbPainelGerencialUsuario();
        init_tbEmpresaCruzadasFuncao();
        init_tbGridGerencialIndicadores();
        init_tbAno();
        init_tbGraficoIndicadoresMes();
        init_tbGraficoIndicadoresDiario();
        init_tbComboQuebra();
        init_tbSelecaoGenerica();
        init_tbGraficoIndicadoresQuebra();
        init_tbSelecaoGenericaSelecionada();
        init_tbGraficoIndUlt12Mes();
        init_FPopupMenu1();
        init_FMenuItem4();
        init_FMenuItem3();
        init_FMenuItem2();
        init_FMenuItem1();
        init_FMenuItem5();
        init_FMenuItem6();
        init_FMenuItem7();
        init_FMenuItem8();
        init_vboxPrincipal();
        init_FHBox1();
        init_FVBox1();
        init_FHBox8();
        init_cbbPainel();
        init_btnPreferencia();
        init_FHBox3();
        init_cbbMes();
        init_cbbAno();
        init_cbbEmpresasCruzadas();
        init_btnAtualizar();
        init_templateGrid();
        init_hboxLinhaTemplate();
        init_FHBox4();
        init_lblDescricaoGrid();
        init_FHBox5();
        init_FVBox2();
        init_lblValor();
        init_FVBox3();
        init_FVBox12();
        init_gridGerencial();
        init_vboxGraficoMensal();
        init_FHBox9();
        init_FVBox13();
        init_FHBox10();
        init_hboxEstatistica();
        init_FVBox14();
        init_FLabel3();
        init_FVBox15();
        init_hboxUltimos12Meses();
        init_FVBox16();
        init_FLabel5();
        init_FVBox17();
        init_FVBox18();
        init_chartbarEstatistica();
        init_chartbarUltimos12Meses();
        init_vboxGraficoDiario();
        init_FHBox2();
        init_FVBox4();
        init_FHBox6();
        init_hboxMesAtual();
        init_FVBox6();
        init_FLabel1();
        init_FVBox7();
        init_hboxUltimos30Dias();
        init_FVBox8();
        init_FLabel2();
        init_FVBox9();
        init_FVBox5();
        init_chartLineDiario();
        init_vboxGraficoQuebra();
        init_FHBox7();
        init_FVBox10();
        init_cbbQuebra();
        init_chartPizzaQuebra();
        init_FrmGerencialPainel();
    }

    public PAINEL_GERENCIAL_USUARIO tbPainelGerencialUsuario;

    private void init_tbPainelGerencialUsuario() {
        tbPainelGerencialUsuario = rn.tbPainelGerencialUsuario;
        tbPainelGerencialUsuario.setName("tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.setMaxRowCount(200);
        tbPainelGerencialUsuario.setWKey("382032;38203");
        tbPainelGerencialUsuario.setRatioBatchSize(20);
        getTables().put(tbPainelGerencialUsuario, "tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.applyProperties();
    }

    public EMPRESA_CRUZADAS_FUNCAO tbEmpresaCruzadasFuncao;

    private void init_tbEmpresaCruzadasFuncao() {
        tbEmpresaCruzadasFuncao = rn.tbEmpresaCruzadasFuncao;
        tbEmpresaCruzadasFuncao.setName("tbEmpresaCruzadasFuncao");
        tbEmpresaCruzadasFuncao.setMaxRowCount(200);
        tbEmpresaCruzadasFuncao.setWKey("382032;38204");
        tbEmpresaCruzadasFuncao.setRatioBatchSize(20);
        getTables().put(tbEmpresaCruzadasFuncao, "tbEmpresaCruzadasFuncao");
        tbEmpresaCruzadasFuncao.applyProperties();
    }

    public GRID_GERENCIAL_INDICADORES tbGridGerencialIndicadores;

    private void init_tbGridGerencialIndicadores() {
        tbGridGerencialIndicadores = rn.tbGridGerencialIndicadores;
        tbGridGerencialIndicadores.setName("tbGridGerencialIndicadores");
        tbGridGerencialIndicadores.setMaxRowCount(200);
        tbGridGerencialIndicadores.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbGridGerencialIndicadoresAfterScroll(event);
            processarFlow("FrmGerencialPainel", "tbGridGerencialIndicadores", "OnAfterScroll");
        });
        tbGridGerencialIndicadores.setWKey("382032;38205");
        tbGridGerencialIndicadores.setRatioBatchSize(20);
        getTables().put(tbGridGerencialIndicadores, "tbGridGerencialIndicadores");
        tbGridGerencialIndicadores.applyProperties();
    }

    public ANO tbAno;

    private void init_tbAno() {
        tbAno = rn.tbAno;
        tbAno.setName("tbAno");
        tbAno.setMaxRowCount(200);
        tbAno.setWKey("382032;38206");
        tbAno.setRatioBatchSize(20);
        getTables().put(tbAno, "tbAno");
        tbAno.applyProperties();
    }

    public GRAFICO_INDICADORES_MES tbGraficoIndicadoresMes;

    private void init_tbGraficoIndicadoresMes() {
        tbGraficoIndicadoresMes = rn.tbGraficoIndicadoresMes;
        tbGraficoIndicadoresMes.setName("tbGraficoIndicadoresMes");
        tbGraficoIndicadoresMes.setMaxRowCount(200);
        tbGraficoIndicadoresMes.setWKey("382032;38207");
        tbGraficoIndicadoresMes.setRatioBatchSize(20);
        getTables().put(tbGraficoIndicadoresMes, "tbGraficoIndicadoresMes");
        tbGraficoIndicadoresMes.applyProperties();
    }

    public GRAFICO_INDICADORES_DIARIO tbGraficoIndicadoresDiario;

    private void init_tbGraficoIndicadoresDiario() {
        tbGraficoIndicadoresDiario = rn.tbGraficoIndicadoresDiario;
        tbGraficoIndicadoresDiario.setName("tbGraficoIndicadoresDiario");
        tbGraficoIndicadoresDiario.setMaxRowCount(200);
        tbGraficoIndicadoresDiario.setWKey("382032;38208");
        tbGraficoIndicadoresDiario.setRatioBatchSize(20);
        getTables().put(tbGraficoIndicadoresDiario, "tbGraficoIndicadoresDiario");
        tbGraficoIndicadoresDiario.applyProperties();
    }

    public BSC_COMBO_QUEBRA tbComboQuebra;

    private void init_tbComboQuebra() {
        tbComboQuebra = rn.tbComboQuebra;
        tbComboQuebra.setName("tbComboQuebra");
        tbComboQuebra.setMaxRowCount(200);
        tbComboQuebra.setWKey("382032;38209");
        tbComboQuebra.setRatioBatchSize(20);
        getTables().put(tbComboQuebra, "tbComboQuebra");
        tbComboQuebra.applyProperties();
    }

    public SELECAO_GENERICA tbSelecaoGenerica;

    private void init_tbSelecaoGenerica() {
        tbSelecaoGenerica = rn.tbSelecaoGenerica;
        tbSelecaoGenerica.setName("tbSelecaoGenerica");
        tbSelecaoGenerica.setMaxRowCount(200);
        tbSelecaoGenerica.setWKey("382032;382010");
        tbSelecaoGenerica.setRatioBatchSize(20);
        getTables().put(tbSelecaoGenerica, "tbSelecaoGenerica");
        tbSelecaoGenerica.applyProperties();
    }

    public GRAFICO_INDICADORES_QUEBRA tbGraficoIndicadoresQuebra;

    private void init_tbGraficoIndicadoresQuebra() {
        tbGraficoIndicadoresQuebra = rn.tbGraficoIndicadoresQuebra;
        tbGraficoIndicadoresQuebra.setName("tbGraficoIndicadoresQuebra");
        tbGraficoIndicadoresQuebra.setMaxRowCount(200);
        tbGraficoIndicadoresQuebra.setWKey("382032;382011");
        tbGraficoIndicadoresQuebra.setRatioBatchSize(20);
        getTables().put(tbGraficoIndicadoresQuebra, "tbGraficoIndicadoresQuebra");
        tbGraficoIndicadoresQuebra.applyProperties();
    }

    public SELECAO_GENERICA tbSelecaoGenericaSelecionada;

    private void init_tbSelecaoGenericaSelecionada() {
        tbSelecaoGenericaSelecionada = rn.tbSelecaoGenericaSelecionada;
        tbSelecaoGenericaSelecionada.setName("tbSelecaoGenericaSelecionada");
        tbSelecaoGenericaSelecionada.setMaxRowCount(200);
        tbSelecaoGenericaSelecionada.setWKey("382032;382012");
        tbSelecaoGenericaSelecionada.setRatioBatchSize(20);
        getTables().put(tbSelecaoGenericaSelecionada, "tbSelecaoGenericaSelecionada");
        tbSelecaoGenericaSelecionada.applyProperties();
    }

    public GRAFICO_IND_ULT_12_MES tbGraficoIndUlt12Mes;

    private void init_tbGraficoIndUlt12Mes() {
        tbGraficoIndUlt12Mes = rn.tbGraficoIndUlt12Mes;
        tbGraficoIndUlt12Mes.setName("tbGraficoIndUlt12Mes");
        tbGraficoIndUlt12Mes.setMaxRowCount(200);
        tbGraficoIndUlt12Mes.setWKey("382032;382013");
        tbGraficoIndUlt12Mes.setRatioBatchSize(20);
        getTables().put(tbGraficoIndUlt12Mes, "tbGraficoIndUlt12Mes");
        tbGraficoIndUlt12Mes.applyProperties();
    }

    public TFPopupMenu FPopupMenu1 = new TFPopupMenu();

    private void init_FPopupMenu1() {
        FPopupMenu1.setName("FPopupMenu1");
        FrmGerencialPainel.addChildren(FPopupMenu1);
        FPopupMenu1.applyProperties();
    }

    public TFMenuItem FMenuItem4 = new TFMenuItem();

    private void init_FMenuItem4() {
        FMenuItem4.setName("FMenuItem4");
        FMenuItem4.setCaption("Atualizar");
        FMenuItem4.setImageIndex(700092);
        FMenuItem4.setAccess(false);
        FMenuItem4.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem4);
        FMenuItem4.applyProperties();
    }

    public TFMenuItem FMenuItem3 = new TFMenuItem();

    private void init_FMenuItem3() {
        FMenuItem3.setName("FMenuItem3");
        FMenuItem3.setCaption("Filtro");
        FMenuItem3.setImageIndex(23501);
        FMenuItem3.setAccess(false);
        FMenuItem3.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem3);
        FMenuItem3.applyProperties();
    }

    public TFMenuItem FMenuItem2 = new TFMenuItem();

    private void init_FMenuItem2() {
        FMenuItem2.setName("FMenuItem2");
        FMenuItem2.setCaption("Voltar");
        FMenuItem2.setImageIndex(700081);
        FMenuItem2.setAccess(false);
        FMenuItem2.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem2);
        FMenuItem2.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("Salvar");
        FMenuItem1.setImageIndex(700088);
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem FMenuItem5 = new TFMenuItem();

    private void init_FMenuItem5() {
        FMenuItem5.setName("FMenuItem5");
        FMenuItem5.setCaption("Preferencia");
        FMenuItem5.setImageIndex(382021);
        FMenuItem5.setAccess(false);
        FMenuItem5.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem5);
        FMenuItem5.applyProperties();
    }

    public TFMenuItem FMenuItem6 = new TFMenuItem();

    private void init_FMenuItem6() {
        FMenuItem6.setName("FMenuItem6");
        FMenuItem6.setCaption("estrela cinza");
        FMenuItem6.setImageIndex(4300106);
        FMenuItem6.setAccess(false);
        FMenuItem6.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem6);
        FMenuItem6.applyProperties();
    }

    public TFMenuItem FMenuItem7 = new TFMenuItem();

    private void init_FMenuItem7() {
        FMenuItem7.setName("FMenuItem7");
        FMenuItem7.setCaption("estrela amarelo");
        FMenuItem7.setImageIndex(4300107);
        FMenuItem7.setAccess(false);
        FMenuItem7.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem7);
        FMenuItem7.applyProperties();
    }

    public TFMenuItem FMenuItem8 = new TFMenuItem();

    private void init_FMenuItem8() {
        FMenuItem8.setName("FMenuItem8");
        FMenuItem8.setCaption("estrela-gold");
        FMenuItem8.setImageIndex(382023);
        FMenuItem8.setAccess(false);
        FMenuItem8.setCheckmark(false);
        FPopupMenu1.addChildren(FMenuItem8);
        FMenuItem8.applyProperties();
    }

    protected TFForm FrmGerencialPainel = this;
    private void init_FrmGerencialPainel() {
        FrmGerencialPainel.setName("FrmGerencialPainel");
        FrmGerencialPainel.setCaption("Painel Gerencial");
        FrmGerencialPainel.setClientHeight(753);
        FrmGerencialPainel.setClientWidth(841);
        FrmGerencialPainel.setColor("clBtnFace");
        FrmGerencialPainel.setWOrigem("EhMain");
        FrmGerencialPainel.setWKey("382032");
        FrmGerencialPainel.setSpacing(0);
        FrmGerencialPainel.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(841);
        vboxPrincipal.setHeight(753);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(5);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(5);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(5);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmGerencialPainel.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(689);
        FHBox1.setHeight(75);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vboxPrincipal.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(606);
        FVBox1.setHeight(68);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftMin");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(460);
        FHBox8.setHeight(29);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(5);
        FHBox8.setFlexVflex("ftMin");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FVBox1.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFCombo cbbPainel = new TFCombo();

    private void init_cbbPainel() {
        cbbPainel.setName("cbbPainel");
        cbbPainel.setLeft(0);
        cbbPainel.setTop(0);
        cbbPainel.setWidth(281);
        cbbPainel.setHeight(21);
        cbbPainel.setLookupTable(tbPainelGerencialUsuario);
        cbbPainel.setFieldName("DESCRICAO");
        cbbPainel.setLookupKey("ID");
        cbbPainel.setLookupDesc("DESCRICAO");
        cbbPainel.setFlex(true);
        cbbPainel.setReadOnly(true);
        cbbPainel.setRequired(false);
        cbbPainel.setPrompt("Selecione");
        cbbPainel.setConstraintCheckWhen("cwImmediate");
        cbbPainel.setConstraintCheckType("ctExpression");
        cbbPainel.setConstraintFocusOnError(false);
        cbbPainel.setConstraintEnableUI(true);
        cbbPainel.setConstraintEnabled(false);
        cbbPainel.setConstraintFormCheck(true);
        cbbPainel.setClearOnDelKey(true);
        cbbPainel.setUseClearButton(false);
        cbbPainel.setHideClearButtonOnNullValue(false);
        cbbPainel.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbPainelChange(event);
            processarFlow("FrmGerencialPainel", "cbbPainel", "OnChange");
        });
        FHBox8.addChildren(cbbPainel);
        cbbPainel.applyProperties();
        addValidatable(cbbPainel);
    }

    public TFButton btnPreferencia = new TFButton();

    private void init_btnPreferencia() {
        btnPreferencia.setName("btnPreferencia");
        btnPreferencia.setLeft(281);
        btnPreferencia.setTop(0);
        btnPreferencia.setWidth(33);
        btnPreferencia.setHeight(33);
        btnPreferencia.setFontColor("clWindowText");
        btnPreferencia.setFontSize(-11);
        btnPreferencia.setFontName("Tahoma");
        btnPreferencia.setFontStyle("[]");
        btnPreferencia.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPreferenciaClick(event);
            processarFlow("FrmGerencialPainel", "btnPreferencia", "OnClick");
        });
        btnPreferencia.setImageId(382023);
        btnPreferencia.setColor("clBtnFace");
        btnPreferencia.setAccess(false);
        btnPreferencia.setIconReverseDirection(false);
        FHBox8.addChildren(btnPreferencia);
        btnPreferencia.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(30);
        FHBox3.setWidth(455);
        FHBox3.setHeight(30);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(5);
        FHBox3.setFlexVflex("ftMin");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFCombo cbbMes = new TFCombo();

    private void init_cbbMes() {
        cbbMes.setName("cbbMes");
        cbbMes.setLeft(0);
        cbbMes.setTop(0);
        cbbMes.setWidth(133);
        cbbMes.setHeight(21);
        cbbMes.setFlex(false);
        cbbMes.setListOptions("Janeiro=1;Fevereiro=2;Mar\u00E7o=3;Abril=4;Maio=5;Junho=6;Julho=7;Agosto=8;Setembro=9;Outubro=10;Novembro=11;Dezembro=12");
        cbbMes.setReadOnly(true);
        cbbMes.setRequired(false);
        cbbMes.setPrompt("Selecione");
        cbbMes.setConstraintCheckWhen("cwImmediate");
        cbbMes.setConstraintCheckType("ctExpression");
        cbbMes.setConstraintFocusOnError(false);
        cbbMes.setConstraintEnableUI(true);
        cbbMes.setConstraintEnabled(false);
        cbbMes.setConstraintFormCheck(true);
        cbbMes.setClearOnDelKey(true);
        cbbMes.setUseClearButton(false);
        cbbMes.setHideClearButtonOnNullValue(false);
        cbbMes.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbMesChange(event);
            processarFlow("FrmGerencialPainel", "cbbMes", "OnChange");
        });
        FHBox3.addChildren(cbbMes);
        cbbMes.applyProperties();
        addValidatable(cbbMes);
    }

    public TFCombo cbbAno = new TFCombo();

    private void init_cbbAno() {
        cbbAno.setName("cbbAno");
        cbbAno.setLeft(133);
        cbbAno.setTop(0);
        cbbAno.setWidth(100);
        cbbAno.setHeight(21);
        cbbAno.setLookupTable(tbAno);
        cbbAno.setLookupKey("ANO");
        cbbAno.setLookupDesc("ANO");
        cbbAno.setFlex(false);
        cbbAno.setReadOnly(true);
        cbbAno.setRequired(false);
        cbbAno.setPrompt("Selecione");
        cbbAno.setConstraintCheckWhen("cwImmediate");
        cbbAno.setConstraintCheckType("ctExpression");
        cbbAno.setConstraintFocusOnError(false);
        cbbAno.setConstraintEnableUI(true);
        cbbAno.setConstraintEnabled(false);
        cbbAno.setConstraintFormCheck(true);
        cbbAno.setClearOnDelKey(true);
        cbbAno.setUseClearButton(false);
        cbbAno.setHideClearButtonOnNullValue(false);
        cbbAno.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbAnoChange(event);
            processarFlow("FrmGerencialPainel", "cbbAno", "OnChange");
        });
        FHBox3.addChildren(cbbAno);
        cbbAno.applyProperties();
        addValidatable(cbbAno);
    }

    public TFCombo cbbEmpresasCruzadas = new TFCombo();

    private void init_cbbEmpresasCruzadas() {
        cbbEmpresasCruzadas.setName("cbbEmpresasCruzadas");
        cbbEmpresasCruzadas.setLeft(233);
        cbbEmpresasCruzadas.setTop(0);
        cbbEmpresasCruzadas.setWidth(145);
        cbbEmpresasCruzadas.setHeight(21);
        cbbEmpresasCruzadas.setLookupTable(tbEmpresaCruzadasFuncao);
        cbbEmpresasCruzadas.setLookupKey("CE");
        cbbEmpresasCruzadas.setLookupDesc("EMPRESA");
        cbbEmpresasCruzadas.setFlex(true);
        cbbEmpresasCruzadas.setReadOnly(true);
        cbbEmpresasCruzadas.setRequired(false);
        cbbEmpresasCruzadas.setPrompt("Todas Empresas Cruzadas");
        cbbEmpresasCruzadas.setConstraintCheckWhen("cwImmediate");
        cbbEmpresasCruzadas.setConstraintCheckType("ctExpression");
        cbbEmpresasCruzadas.setConstraintFocusOnError(false);
        cbbEmpresasCruzadas.setConstraintEnableUI(true);
        cbbEmpresasCruzadas.setConstraintEnabled(false);
        cbbEmpresasCruzadas.setConstraintFormCheck(true);
        cbbEmpresasCruzadas.setClearOnDelKey(true);
        cbbEmpresasCruzadas.setUseClearButton(true);
        cbbEmpresasCruzadas.setHideClearButtonOnNullValue(false);
        cbbEmpresasCruzadas.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbEmpresasCruzadasChange(event);
            processarFlow("FrmGerencialPainel", "cbbEmpresasCruzadas", "OnChange");
        });
        cbbEmpresasCruzadas.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbEmpresasCruzadasClearClick(event);
            processarFlow("FrmGerencialPainel", "cbbEmpresasCruzadas", "OnClearClick");
        });
        FHBox3.addChildren(cbbEmpresasCruzadas);
        cbbEmpresasCruzadas.applyProperties();
        addValidatable(cbbEmpresasCruzadas);
    }

    public TFButton btnAtualizar = new TFButton();

    private void init_btnAtualizar() {
        btnAtualizar.setName("btnAtualizar");
        btnAtualizar.setLeft(606);
        btnAtualizar.setTop(0);
        btnAtualizar.setWidth(64);
        btnAtualizar.setHeight(70);
        btnAtualizar.setFontColor("clWindowText");
        btnAtualizar.setFontSize(-11);
        btnAtualizar.setFontName("Tahoma");
        btnAtualizar.setFontStyle("[]");
        btnAtualizar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAtualizarClick(event);
            processarFlow("FrmGerencialPainel", "btnAtualizar", "OnClick");
        });
        btnAtualizar.setImageId(700092);
        btnAtualizar.setColor("clBtnFace");
        btnAtualizar.setAccess(false);
        btnAtualizar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAtualizar);
        btnAtualizar.applyProperties();
    }

    public TFVBox templateGrid = new TFVBox();

    private void init_templateGrid() {
        templateGrid.setName("templateGrid");
        templateGrid.setLeft(0);
        templateGrid.setTop(76);
        templateGrid.setWidth(690);
        templateGrid.setHeight(49);
        templateGrid.setBorderStyle("stNone");
        templateGrid.setPaddingTop(0);
        templateGrid.setPaddingLeft(0);
        templateGrid.setPaddingRight(0);
        templateGrid.setPaddingBottom(0);
        templateGrid.setVisible(false);
        templateGrid.setMarginTop(0);
        templateGrid.setMarginLeft(0);
        templateGrid.setMarginRight(0);
        templateGrid.setMarginBottom(0);
        templateGrid.setSpacing(0);
        templateGrid.setFlexVflex("ftTrue");
        templateGrid.setFlexHflex("ftTrue");
        templateGrid.setScrollable(false);
        templateGrid.setBoxShadowConfigHorizontalLength(10);
        templateGrid.setBoxShadowConfigVerticalLength(10);
        templateGrid.setBoxShadowConfigBlurRadius(5);
        templateGrid.setBoxShadowConfigSpreadRadius(0);
        templateGrid.setBoxShadowConfigShadowColor("clBlack");
        templateGrid.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(templateGrid);
        templateGrid.applyProperties();
    }

    public TFHBox hboxLinhaTemplate = new TFHBox();

    private void init_hboxLinhaTemplate() {
        hboxLinhaTemplate.setName("hboxLinhaTemplate");
        hboxLinhaTemplate.setLeft(0);
        hboxLinhaTemplate.setTop(0);
        hboxLinhaTemplate.setWidth(681);
        hboxLinhaTemplate.setHeight(41);
        hboxLinhaTemplate.setBorderStyle("stNone");
        hboxLinhaTemplate.setPaddingTop(0);
        hboxLinhaTemplate.setPaddingLeft(0);
        hboxLinhaTemplate.setPaddingRight(0);
        hboxLinhaTemplate.setPaddingBottom(0);
        hboxLinhaTemplate.setMarginTop(0);
        hboxLinhaTemplate.setMarginLeft(0);
        hboxLinhaTemplate.setMarginRight(0);
        hboxLinhaTemplate.setMarginBottom(0);
        hboxLinhaTemplate.setSpacing(0);
        hboxLinhaTemplate.setFlexVflex("ftMin");
        hboxLinhaTemplate.setFlexHflex("ftTrue");
        hboxLinhaTemplate.setScrollable(false);
        hboxLinhaTemplate.setBoxShadowConfigHorizontalLength(10);
        hboxLinhaTemplate.setBoxShadowConfigVerticalLength(10);
        hboxLinhaTemplate.setBoxShadowConfigBlurRadius(5);
        hboxLinhaTemplate.setBoxShadowConfigSpreadRadius(0);
        hboxLinhaTemplate.setBoxShadowConfigShadowColor("clBlack");
        hboxLinhaTemplate.setBoxShadowConfigOpacity(75);
        hboxLinhaTemplate.setVAlign("tvTop");
        templateGrid.addChildren(hboxLinhaTemplate);
        hboxLinhaTemplate.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(368);
        FHBox4.setHeight(33);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftMin");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        hboxLinhaTemplate.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblDescricaoGrid = new TFLabel();

    private void init_lblDescricaoGrid() {
        lblDescricaoGrid.setName("lblDescricaoGrid");
        lblDescricaoGrid.setLeft(0);
        lblDescricaoGrid.setTop(0);
        lblDescricaoGrid.setWidth(63);
        lblDescricaoGrid.setHeight(16);
        lblDescricaoGrid.setCaption("Descricao");
        lblDescricaoGrid.setFontColor("clWindowText");
        lblDescricaoGrid.setFontSize(-13);
        lblDescricaoGrid.setFontName("Tahoma");
        lblDescricaoGrid.setFontStyle("[fsBold]");
        lblDescricaoGrid.setFieldName("INDICADOR");
        lblDescricaoGrid.setTable(tbGridGerencialIndicadores);
        lblDescricaoGrid.setVerticalAlignment("taVerticalCenter");
        lblDescricaoGrid.setWordBreak(false);
        FHBox4.addChildren(lblDescricaoGrid);
        lblDescricaoGrid.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(368);
        FHBox5.setTop(0);
        FHBox5.setWidth(90);
        FHBox5.setHeight(34);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftMin");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        hboxLinhaTemplate.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(13);
        FVBox2.setHeight(26);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftMin");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox5.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel lblValor = new TFLabel();

    private void init_lblValor() {
        lblValor.setName("lblValor");
        lblValor.setLeft(13);
        lblValor.setTop(0);
        lblValor.setWidth(31);
        lblValor.setHeight(16);
        lblValor.setCaption("Qtde");
        lblValor.setFontColor("clWindowText");
        lblValor.setFontSize(-13);
        lblValor.setFontName("Tahoma");
        lblValor.setFontStyle("[fsBold]");
        lblValor.setFieldName("VALOR");
        lblValor.setTable(tbGridGerencialIndicadores);
        lblValor.setVerticalAlignment("taVerticalCenter");
        lblValor.setWordBreak(false);
        FHBox5.addChildren(lblValor);
        lblValor.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(44);
        FVBox3.setTop(0);
        FVBox3.setWidth(7);
        FVBox3.setHeight(26);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftMin");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox5.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFVBox FVBox12 = new TFVBox();

    private void init_FVBox12() {
        FVBox12.setName("FVBox12");
        FVBox12.setLeft(0);
        FVBox12.setTop(126);
        FVBox12.setWidth(759);
        FVBox12.setHeight(850);
        FVBox12.setBorderStyle("stNone");
        FVBox12.setPaddingTop(0);
        FVBox12.setPaddingLeft(0);
        FVBox12.setPaddingRight(5);
        FVBox12.setPaddingBottom(0);
        FVBox12.setMarginTop(0);
        FVBox12.setMarginLeft(0);
        FVBox12.setMarginRight(0);
        FVBox12.setMarginBottom(0);
        FVBox12.setSpacing(10);
        FVBox12.setFlexVflex("ftTrue");
        FVBox12.setFlexHflex("ftTrue");
        FVBox12.setScrollable(true);
        FVBox12.setBoxShadowConfigHorizontalLength(10);
        FVBox12.setBoxShadowConfigVerticalLength(10);
        FVBox12.setBoxShadowConfigBlurRadius(5);
        FVBox12.setBoxShadowConfigSpreadRadius(0);
        FVBox12.setBoxShadowConfigShadowColor("clBlack");
        FVBox12.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(FVBox12);
        FVBox12.applyProperties();
    }

    public TFGrid gridGerencial = new TFGrid();

    private void init_gridGerencial() {
        gridGerencial.setName("gridGerencial");
        gridGerencial.setLeft(0);
        gridGerencial.setTop(0);
        gridGerencial.setWidth(687);
        gridGerencial.setHeight(56);
        gridGerencial.setTable(tbGridGerencialIndicadores);
        gridGerencial.setFlexVflex("ftMin");
        gridGerencial.setFlexHflex("ftTrue");
        gridGerencial.setPagingEnabled(false);
        gridGerencial.setFrozenColumns(0);
        gridGerencial.setShowFooter(false);
        gridGerencial.setShowHeader(false);
        gridGerencial.setMultiSelection(false);
        gridGerencial.setGroupingEnabled(false);
        gridGerencial.setGroupingExpanded(false);
        gridGerencial.setGroupingShowFooter(false);
        gridGerencial.setCrosstabEnabled(false);
        gridGerencial.setCrosstabGroupType("cgtConcat");
        gridGerencial.setEditionEnabled(false);
        gridGerencial.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("INDICADOR");
        TFFontExpression item1 = new TFFontExpression();
        item1.setExpression("*");
        item1.setEvalType("etExpression");
        item1.setFontColor("clWindowText");
        item1.setFontSize(-16);
        item1.setFontName("Tahoma");
        item1.setFontStyle("[]");
        item0.getFont().add(item1);
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(150);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setEditorFilter(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        item0.addEventListener("onRenderTemplate", (EventListener<RenderTemplateEvent<Value>>) (RenderTemplateEvent<Value> event) -> {
            gridGerencialColumns0RenderTemplate(event);
            processarFlow("FrmGerencialPainel", "item0", "OnRenderTemplate");
        });
        gridGerencial.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("VALOR");
        TFFontExpression item3 = new TFFontExpression();
        item3.setExpression("*");
        item3.setEvalType("etExpression");
        item3.setFontColor("clWindowText");
        item3.setFontSize(-16);
        item3.setFontName("Tahoma");
        item3.setFontStyle("[]");
        item2.getFont().add(item3);
        item2.setTitleCaption("Valor");
        item2.setWidth(120);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setEditorFilter(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridGerencial.getColumns().add(item2);
        FVBox12.addChildren(gridGerencial);
        gridGerencial.applyProperties();
    }

    public TFVBox vboxGraficoMensal = new TFVBox();

    private void init_vboxGraficoMensal() {
        vboxGraficoMensal.setName("vboxGraficoMensal");
        vboxGraficoMensal.setLeft(0);
        vboxGraficoMensal.setTop(57);
        vboxGraficoMensal.setWidth(685);
        vboxGraficoMensal.setHeight(290);
        vboxGraficoMensal.setBorderStyle("stNone");
        vboxGraficoMensal.setPaddingTop(0);
        vboxGraficoMensal.setPaddingLeft(0);
        vboxGraficoMensal.setPaddingRight(0);
        vboxGraficoMensal.setPaddingBottom(0);
        vboxGraficoMensal.setMarginTop(0);
        vboxGraficoMensal.setMarginLeft(0);
        vboxGraficoMensal.setMarginRight(0);
        vboxGraficoMensal.setMarginBottom(0);
        vboxGraficoMensal.setSpacing(1);
        vboxGraficoMensal.setFlexVflex("ftFalse");
        vboxGraficoMensal.setFlexHflex("ftTrue");
        vboxGraficoMensal.setScrollable(false);
        vboxGraficoMensal.setBoxShadowConfigHorizontalLength(10);
        vboxGraficoMensal.setBoxShadowConfigVerticalLength(10);
        vboxGraficoMensal.setBoxShadowConfigBlurRadius(5);
        vboxGraficoMensal.setBoxShadowConfigSpreadRadius(0);
        vboxGraficoMensal.setBoxShadowConfigShadowColor("clBlack");
        vboxGraficoMensal.setBoxShadowConfigOpacity(75);
        FVBox12.addChildren(vboxGraficoMensal);
        vboxGraficoMensal.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(680);
        FHBox9.setHeight(41);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        vboxGraficoMensal.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFVBox FVBox13 = new TFVBox();

    private void init_FVBox13() {
        FVBox13.setName("FVBox13");
        FVBox13.setLeft(0);
        FVBox13.setTop(0);
        FVBox13.setWidth(9);
        FVBox13.setHeight(25);
        FVBox13.setBorderStyle("stNone");
        FVBox13.setPaddingTop(0);
        FVBox13.setPaddingLeft(0);
        FVBox13.setPaddingRight(0);
        FVBox13.setPaddingBottom(0);
        FVBox13.setMarginTop(0);
        FVBox13.setMarginLeft(0);
        FVBox13.setMarginRight(0);
        FVBox13.setMarginBottom(0);
        FVBox13.setSpacing(1);
        FVBox13.setFlexVflex("ftTrue");
        FVBox13.setFlexHflex("ftTrue");
        FVBox13.setScrollable(false);
        FVBox13.setBoxShadowConfigHorizontalLength(10);
        FVBox13.setBoxShadowConfigVerticalLength(10);
        FVBox13.setBoxShadowConfigBlurRadius(5);
        FVBox13.setBoxShadowConfigSpreadRadius(0);
        FVBox13.setBoxShadowConfigShadowColor("clBlack");
        FVBox13.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FVBox13);
        FVBox13.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(9);
        FHBox10.setTop(0);
        FHBox10.setWidth(340);
        FHBox10.setHeight(37);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox9.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFHBox hboxEstatistica = new TFHBox();

    private void init_hboxEstatistica() {
        hboxEstatistica.setName("hboxEstatistica");
        hboxEstatistica.setLeft(0);
        hboxEstatistica.setTop(0);
        hboxEstatistica.setWidth(160);
        hboxEstatistica.setHeight(33);
        hboxEstatistica.setBorderStyle("stNone");
        hboxEstatistica.setPaddingTop(5);
        hboxEstatistica.setPaddingLeft(0);
        hboxEstatistica.setPaddingRight(0);
        hboxEstatistica.setPaddingBottom(0);
        hboxEstatistica.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxEstatisticaClick(event);
            processarFlow("FrmGerencialPainel", "hboxEstatistica", "OnClick");
        });
        hboxEstatistica.setMarginTop(0);
        hboxEstatistica.setMarginLeft(0);
        hboxEstatistica.setMarginRight(0);
        hboxEstatistica.setMarginBottom(0);
        hboxEstatistica.setSpacing(1);
        hboxEstatistica.setFlexVflex("ftTrue");
        hboxEstatistica.setFlexHflex("ftTrue");
        hboxEstatistica.setScrollable(false);
        hboxEstatistica.setBoxShadowConfigHorizontalLength(10);
        hboxEstatistica.setBoxShadowConfigVerticalLength(10);
        hboxEstatistica.setBoxShadowConfigBlurRadius(5);
        hboxEstatistica.setBoxShadowConfigSpreadRadius(0);
        hboxEstatistica.setBoxShadowConfigShadowColor("clBlack");
        hboxEstatistica.setBoxShadowConfigOpacity(75);
        hboxEstatistica.setVAlign("tvTop");
        FHBox10.addChildren(hboxEstatistica);
        hboxEstatistica.applyProperties();
    }

    public TFVBox FVBox14 = new TFVBox();

    private void init_FVBox14() {
        FVBox14.setName("FVBox14");
        FVBox14.setLeft(0);
        FVBox14.setTop(0);
        FVBox14.setWidth(9);
        FVBox14.setHeight(25);
        FVBox14.setBorderStyle("stNone");
        FVBox14.setPaddingTop(0);
        FVBox14.setPaddingLeft(0);
        FVBox14.setPaddingRight(0);
        FVBox14.setPaddingBottom(0);
        FVBox14.setMarginTop(0);
        FVBox14.setMarginLeft(0);
        FVBox14.setMarginRight(0);
        FVBox14.setMarginBottom(0);
        FVBox14.setSpacing(1);
        FVBox14.setFlexVflex("ftTrue");
        FVBox14.setFlexHflex("ftTrue");
        FVBox14.setScrollable(false);
        FVBox14.setBoxShadowConfigHorizontalLength(10);
        FVBox14.setBoxShadowConfigVerticalLength(10);
        FVBox14.setBoxShadowConfigBlurRadius(5);
        FVBox14.setBoxShadowConfigSpreadRadius(0);
        FVBox14.setBoxShadowConfigShadowColor("clBlack");
        FVBox14.setBoxShadowConfigOpacity(75);
        hboxEstatistica.addChildren(FVBox14);
        FVBox14.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(9);
        FLabel3.setTop(0);
        FLabel3.setWidth(69);
        FLabel3.setHeight(14);
        FLabel3.setCaption("Estat\u00EDsticas");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-12);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[fsBold]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        hboxEstatistica.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFVBox FVBox15 = new TFVBox();

    private void init_FVBox15() {
        FVBox15.setName("FVBox15");
        FVBox15.setLeft(78);
        FVBox15.setTop(0);
        FVBox15.setWidth(9);
        FVBox15.setHeight(25);
        FVBox15.setBorderStyle("stNone");
        FVBox15.setPaddingTop(0);
        FVBox15.setPaddingLeft(0);
        FVBox15.setPaddingRight(0);
        FVBox15.setPaddingBottom(0);
        FVBox15.setMarginTop(0);
        FVBox15.setMarginLeft(0);
        FVBox15.setMarginRight(0);
        FVBox15.setMarginBottom(0);
        FVBox15.setSpacing(1);
        FVBox15.setFlexVflex("ftTrue");
        FVBox15.setFlexHflex("ftTrue");
        FVBox15.setScrollable(false);
        FVBox15.setBoxShadowConfigHorizontalLength(10);
        FVBox15.setBoxShadowConfigVerticalLength(10);
        FVBox15.setBoxShadowConfigBlurRadius(5);
        FVBox15.setBoxShadowConfigSpreadRadius(0);
        FVBox15.setBoxShadowConfigShadowColor("clBlack");
        FVBox15.setBoxShadowConfigOpacity(75);
        hboxEstatistica.addChildren(FVBox15);
        FVBox15.applyProperties();
    }

    public TFHBox hboxUltimos12Meses = new TFHBox();

    private void init_hboxUltimos12Meses() {
        hboxUltimos12Meses.setName("hboxUltimos12Meses");
        hboxUltimos12Meses.setLeft(160);
        hboxUltimos12Meses.setTop(0);
        hboxUltimos12Meses.setWidth(160);
        hboxUltimos12Meses.setHeight(33);
        hboxUltimos12Meses.setBorderStyle("stNone");
        hboxUltimos12Meses.setPaddingTop(5);
        hboxUltimos12Meses.setPaddingLeft(0);
        hboxUltimos12Meses.setPaddingRight(0);
        hboxUltimos12Meses.setPaddingBottom(0);
        hboxUltimos12Meses.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxUltimos12MesesClick(event);
            processarFlow("FrmGerencialPainel", "hboxUltimos12Meses", "OnClick");
        });
        hboxUltimos12Meses.setMarginTop(0);
        hboxUltimos12Meses.setMarginLeft(0);
        hboxUltimos12Meses.setMarginRight(0);
        hboxUltimos12Meses.setMarginBottom(0);
        hboxUltimos12Meses.setSpacing(1);
        hboxUltimos12Meses.setFlexVflex("ftTrue");
        hboxUltimos12Meses.setFlexHflex("ftTrue");
        hboxUltimos12Meses.setScrollable(false);
        hboxUltimos12Meses.setBoxShadowConfigHorizontalLength(10);
        hboxUltimos12Meses.setBoxShadowConfigVerticalLength(10);
        hboxUltimos12Meses.setBoxShadowConfigBlurRadius(5);
        hboxUltimos12Meses.setBoxShadowConfigSpreadRadius(0);
        hboxUltimos12Meses.setBoxShadowConfigShadowColor("clBlack");
        hboxUltimos12Meses.setBoxShadowConfigOpacity(75);
        hboxUltimos12Meses.setVAlign("tvTop");
        FHBox10.addChildren(hboxUltimos12Meses);
        hboxUltimos12Meses.applyProperties();
    }

    public TFVBox FVBox16 = new TFVBox();

    private void init_FVBox16() {
        FVBox16.setName("FVBox16");
        FVBox16.setLeft(0);
        FVBox16.setTop(0);
        FVBox16.setWidth(9);
        FVBox16.setHeight(25);
        FVBox16.setBorderStyle("stNone");
        FVBox16.setPaddingTop(0);
        FVBox16.setPaddingLeft(0);
        FVBox16.setPaddingRight(0);
        FVBox16.setPaddingBottom(0);
        FVBox16.setMarginTop(0);
        FVBox16.setMarginLeft(0);
        FVBox16.setMarginRight(0);
        FVBox16.setMarginBottom(0);
        FVBox16.setSpacing(1);
        FVBox16.setFlexVflex("ftTrue");
        FVBox16.setFlexHflex("ftTrue");
        FVBox16.setScrollable(false);
        FVBox16.setBoxShadowConfigHorizontalLength(10);
        FVBox16.setBoxShadowConfigVerticalLength(10);
        FVBox16.setBoxShadowConfigBlurRadius(5);
        FVBox16.setBoxShadowConfigSpreadRadius(0);
        FVBox16.setBoxShadowConfigShadowColor("clBlack");
        FVBox16.setBoxShadowConfigOpacity(75);
        hboxUltimos12Meses.addChildren(FVBox16);
        FVBox16.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(9);
        FLabel5.setTop(0);
        FLabel5.setWidth(106);
        FLabel5.setHeight(14);
        FLabel5.setCaption("\u00DAltimos 12 Meses");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-12);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[fsBold]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        hboxUltimos12Meses.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFVBox FVBox17 = new TFVBox();

    private void init_FVBox17() {
        FVBox17.setName("FVBox17");
        FVBox17.setLeft(115);
        FVBox17.setTop(0);
        FVBox17.setWidth(9);
        FVBox17.setHeight(25);
        FVBox17.setBorderStyle("stNone");
        FVBox17.setPaddingTop(0);
        FVBox17.setPaddingLeft(0);
        FVBox17.setPaddingRight(0);
        FVBox17.setPaddingBottom(0);
        FVBox17.setMarginTop(0);
        FVBox17.setMarginLeft(0);
        FVBox17.setMarginRight(0);
        FVBox17.setMarginBottom(0);
        FVBox17.setSpacing(1);
        FVBox17.setFlexVflex("ftTrue");
        FVBox17.setFlexHflex("ftTrue");
        FVBox17.setScrollable(false);
        FVBox17.setBoxShadowConfigHorizontalLength(10);
        FVBox17.setBoxShadowConfigVerticalLength(10);
        FVBox17.setBoxShadowConfigBlurRadius(5);
        FVBox17.setBoxShadowConfigSpreadRadius(0);
        FVBox17.setBoxShadowConfigShadowColor("clBlack");
        FVBox17.setBoxShadowConfigOpacity(75);
        hboxUltimos12Meses.addChildren(FVBox17);
        FVBox17.applyProperties();
    }

    public TFVBox FVBox18 = new TFVBox();

    private void init_FVBox18() {
        FVBox18.setName("FVBox18");
        FVBox18.setLeft(349);
        FVBox18.setTop(0);
        FVBox18.setWidth(9);
        FVBox18.setHeight(25);
        FVBox18.setBorderStyle("stNone");
        FVBox18.setPaddingTop(0);
        FVBox18.setPaddingLeft(0);
        FVBox18.setPaddingRight(0);
        FVBox18.setPaddingBottom(0);
        FVBox18.setMarginTop(0);
        FVBox18.setMarginLeft(0);
        FVBox18.setMarginRight(0);
        FVBox18.setMarginBottom(0);
        FVBox18.setSpacing(1);
        FVBox18.setFlexVflex("ftTrue");
        FVBox18.setFlexHflex("ftTrue");
        FVBox18.setScrollable(false);
        FVBox18.setBoxShadowConfigHorizontalLength(10);
        FVBox18.setBoxShadowConfigVerticalLength(10);
        FVBox18.setBoxShadowConfigBlurRadius(5);
        FVBox18.setBoxShadowConfigSpreadRadius(0);
        FVBox18.setBoxShadowConfigShadowColor("clBlack");
        FVBox18.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FVBox18);
        FVBox18.applyProperties();
    }

    public TFChartBar chartbarEstatistica = new TFChartBar();

    private void init_chartbarEstatistica() {
        chartbarEstatistica.setName("chartbarEstatistica");
        chartbarEstatistica.setLeft(0);
        chartbarEstatistica.setTop(42);
        chartbarEstatistica.setWidth(678);
        chartbarEstatistica.setHeight(106);
        chartbarEstatistica.setBuildIn3d(false);
        chartbarEstatistica.setShowLegend(false);
        chartbarEstatistica.setShowTooltip(true);
        chartbarEstatistica.setTable(tbGraficoIndicadoresMes);
        chartbarEstatistica.setTooltipFormat("{0}: ({1}, {2})");
        TFChartSerie item0 = new TFChartSerie();
        item0.setCaptionField("CAPTIONMESATUAL");
        item0.setXField("CAPTIONMESATUAL");
        item0.setYField("MES_ATUAL");
        chartbarEstatistica.getSeries().add(item0);
        TFChartSerie item1 = new TFChartSerie();
        item1.setCaptionField("CAPTIONMESANTERIOR");
        item1.setXField("CAPTIONMESANTERIOR");
        item1.setYField("MES_ANTERIOR");
        chartbarEstatistica.getSeries().add(item1);
        TFChartSerie item2 = new TFChartSerie();
        item2.setCaptionField("CAPTIONMESANOANTERIOR");
        item2.setXField("CAPTIONMESANOANTERIOR");
        item2.setYField("MM_ANO_ANTERIOR");
        chartbarEstatistica.getSeries().add(item2);
        TFChartSerie item3 = new TFChartSerie();
        item3.setCaptionField("CAPTIONMEDIA6MES");
        item3.setXField("CAPTIONMEDIA6MES");
        item3.setYField("MEDIA_6_MES");
        chartbarEstatistica.getSeries().add(item3);
        TFChartSerie item4 = new TFChartSerie();
        item4.setCaptionField("CAPTIONTENDENCIA");
        item4.setXField("CAPTIONTENDENCIA");
        item4.setYField("TENDENCIA_MES");
        chartbarEstatistica.getSeries().add(item4);
        chartbarEstatistica.setOrient("coVertical");
        chartbarEstatistica.setFlexVflex("ftTrue");
        chartbarEstatistica.setFlexHflex("ftTrue");
        chartbarEstatistica.setYAxisTickInterval(0);
        chartbarEstatistica.setXAxisTickInterval(0);
        chartbarEstatistica.setStacking(false);
        chartbarEstatistica.setShowDataLabel(true);
        chartbarEstatistica.setXLabelRotation(0);
        chartbarEstatistica.setYLabelRotation(0);
        chartbarEstatistica.setLegendAlign("center");
        chartbarEstatistica.setLegendVerticalAlign("bottom");
        vboxGraficoMensal.addChildren(chartbarEstatistica);
        chartbarEstatistica.applyProperties();
    }

    public TFChartBar chartbarUltimos12Meses = new TFChartBar();

    private void init_chartbarUltimos12Meses() {
        chartbarUltimos12Meses.setName("chartbarUltimos12Meses");
        chartbarUltimos12Meses.setLeft(0);
        chartbarUltimos12Meses.setTop(149);
        chartbarUltimos12Meses.setWidth(674);
        chartbarUltimos12Meses.setHeight(130);
        chartbarUltimos12Meses.setBuildIn3d(false);
        chartbarUltimos12Meses.setShowLegend(false);
        chartbarUltimos12Meses.setShowTooltip(true);
        chartbarUltimos12Meses.setTable(tbGraficoIndUlt12Mes);
        chartbarUltimos12Meses.setTooltipFormat("{0}: ({1}, {2})");
        TFChartSerie item0 = new TFChartSerie();
        item0.setCaptionField("COD_EMPRESA");
        item0.setXField("MES_ANO");
        item0.setYField("QTDE");
        chartbarUltimos12Meses.getSeries().add(item0);
        chartbarUltimos12Meses.setOrient("coVertical");
        chartbarUltimos12Meses.setFlexVflex("ftTrue");
        chartbarUltimos12Meses.setFlexHflex("ftTrue");
        chartbarUltimos12Meses.setYAxisTickInterval(0);
        chartbarUltimos12Meses.setXAxisTickInterval(0);
        chartbarUltimos12Meses.setStacking(false);
        chartbarUltimos12Meses.setShowDataLabel(true);
        chartbarUltimos12Meses.setXLabelRotation(0);
        chartbarUltimos12Meses.setYLabelRotation(0);
        chartbarUltimos12Meses.setLegendAlign("center");
        chartbarUltimos12Meses.setLegendVerticalAlign("bottom");
        vboxGraficoMensal.addChildren(chartbarUltimos12Meses);
        chartbarUltimos12Meses.applyProperties();
    }

    public TFVBox vboxGraficoDiario = new TFVBox();

    private void init_vboxGraficoDiario() {
        vboxGraficoDiario.setName("vboxGraficoDiario");
        vboxGraficoDiario.setLeft(0);
        vboxGraficoDiario.setTop(348);
        vboxGraficoDiario.setWidth(685);
        vboxGraficoDiario.setHeight(290);
        vboxGraficoDiario.setBorderStyle("stNone");
        vboxGraficoDiario.setPaddingTop(0);
        vboxGraficoDiario.setPaddingLeft(0);
        vboxGraficoDiario.setPaddingRight(0);
        vboxGraficoDiario.setPaddingBottom(0);
        vboxGraficoDiario.setMarginTop(0);
        vboxGraficoDiario.setMarginLeft(0);
        vboxGraficoDiario.setMarginRight(0);
        vboxGraficoDiario.setMarginBottom(0);
        vboxGraficoDiario.setSpacing(1);
        vboxGraficoDiario.setFlexVflex("ftFalse");
        vboxGraficoDiario.setFlexHflex("ftTrue");
        vboxGraficoDiario.setScrollable(false);
        vboxGraficoDiario.setBoxShadowConfigHorizontalLength(10);
        vboxGraficoDiario.setBoxShadowConfigVerticalLength(10);
        vboxGraficoDiario.setBoxShadowConfigBlurRadius(5);
        vboxGraficoDiario.setBoxShadowConfigSpreadRadius(0);
        vboxGraficoDiario.setBoxShadowConfigShadowColor("clBlack");
        vboxGraficoDiario.setBoxShadowConfigOpacity(75);
        FVBox12.addChildren(vboxGraficoDiario);
        vboxGraficoDiario.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(680);
        FHBox2.setHeight(41);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vboxGraficoDiario.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(9);
        FVBox4.setHeight(25);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(9);
        FHBox6.setTop(0);
        FHBox6.setWidth(340);
        FHBox6.setHeight(37);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox2.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox hboxMesAtual = new TFHBox();

    private void init_hboxMesAtual() {
        hboxMesAtual.setName("hboxMesAtual");
        hboxMesAtual.setLeft(0);
        hboxMesAtual.setTop(0);
        hboxMesAtual.setWidth(160);
        hboxMesAtual.setHeight(33);
        hboxMesAtual.setBorderStyle("stNone");
        hboxMesAtual.setPaddingTop(5);
        hboxMesAtual.setPaddingLeft(0);
        hboxMesAtual.setPaddingRight(0);
        hboxMesAtual.setPaddingBottom(0);
        hboxMesAtual.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxMesAtualClick(event);
            processarFlow("FrmGerencialPainel", "hboxMesAtual", "OnClick");
        });
        hboxMesAtual.setMarginTop(0);
        hboxMesAtual.setMarginLeft(0);
        hboxMesAtual.setMarginRight(0);
        hboxMesAtual.setMarginBottom(0);
        hboxMesAtual.setSpacing(1);
        hboxMesAtual.setFlexVflex("ftTrue");
        hboxMesAtual.setFlexHflex("ftTrue");
        hboxMesAtual.setScrollable(false);
        hboxMesAtual.setBoxShadowConfigHorizontalLength(10);
        hboxMesAtual.setBoxShadowConfigVerticalLength(10);
        hboxMesAtual.setBoxShadowConfigBlurRadius(5);
        hboxMesAtual.setBoxShadowConfigSpreadRadius(0);
        hboxMesAtual.setBoxShadowConfigShadowColor("clBlack");
        hboxMesAtual.setBoxShadowConfigOpacity(75);
        hboxMesAtual.setVAlign("tvTop");
        FHBox6.addChildren(hboxMesAtual);
        hboxMesAtual.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(9);
        FVBox6.setHeight(25);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        hboxMesAtual.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(9);
        FLabel1.setTop(0);
        FLabel1.setWidth(133);
        FLabel1.setHeight(14);
        FLabel1.setCaption("Ano/M\u00EAs Selecionado");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-12);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        hboxMesAtual.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(142);
        FVBox7.setTop(0);
        FVBox7.setWidth(9);
        FVBox7.setHeight(25);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        hboxMesAtual.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFHBox hboxUltimos30Dias = new TFHBox();

    private void init_hboxUltimos30Dias() {
        hboxUltimos30Dias.setName("hboxUltimos30Dias");
        hboxUltimos30Dias.setLeft(160);
        hboxUltimos30Dias.setTop(0);
        hboxUltimos30Dias.setWidth(160);
        hboxUltimos30Dias.setHeight(33);
        hboxUltimos30Dias.setBorderStyle("stNone");
        hboxUltimos30Dias.setPaddingTop(5);
        hboxUltimos30Dias.setPaddingLeft(0);
        hboxUltimos30Dias.setPaddingRight(0);
        hboxUltimos30Dias.setPaddingBottom(0);
        hboxUltimos30Dias.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxUltimos30DiasClick(event);
            processarFlow("FrmGerencialPainel", "hboxUltimos30Dias", "OnClick");
        });
        hboxUltimos30Dias.setMarginTop(0);
        hboxUltimos30Dias.setMarginLeft(0);
        hboxUltimos30Dias.setMarginRight(0);
        hboxUltimos30Dias.setMarginBottom(0);
        hboxUltimos30Dias.setSpacing(1);
        hboxUltimos30Dias.setFlexVflex("ftTrue");
        hboxUltimos30Dias.setFlexHflex("ftTrue");
        hboxUltimos30Dias.setScrollable(false);
        hboxUltimos30Dias.setBoxShadowConfigHorizontalLength(10);
        hboxUltimos30Dias.setBoxShadowConfigVerticalLength(10);
        hboxUltimos30Dias.setBoxShadowConfigBlurRadius(5);
        hboxUltimos30Dias.setBoxShadowConfigSpreadRadius(0);
        hboxUltimos30Dias.setBoxShadowConfigShadowColor("clBlack");
        hboxUltimos30Dias.setBoxShadowConfigOpacity(75);
        hboxUltimos30Dias.setVAlign("tvTop");
        FHBox6.addChildren(hboxUltimos30Dias);
        hboxUltimos30Dias.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(0);
        FVBox8.setWidth(9);
        FVBox8.setHeight(25);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftTrue");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        hboxUltimos30Dias.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(9);
        FLabel2.setTop(0);
        FLabel2.setWidth(93);
        FLabel2.setHeight(14);
        FLabel2.setCaption("\u00DAltimos 30 dias");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-12);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        hboxUltimos30Dias.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(102);
        FVBox9.setTop(0);
        FVBox9.setWidth(9);
        FVBox9.setHeight(25);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftTrue");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        hboxUltimos30Dias.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(349);
        FVBox5.setTop(0);
        FVBox5.setWidth(9);
        FVBox5.setHeight(25);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFChartLine chartLineDiario = new TFChartLine();

    private void init_chartLineDiario() {
        chartLineDiario.setName("chartLineDiario");
        chartLineDiario.setLeft(0);
        chartLineDiario.setTop(42);
        chartLineDiario.setWidth(685);
        chartLineDiario.setHeight(218);
        chartLineDiario.setBuildIn3d(false);
        chartLineDiario.setCategoryField("CAPTION_DIAMES");
        chartLineDiario.setShowLegend(false);
        chartLineDiario.setShowTooltip(true);
        chartLineDiario.setTable(tbGraficoIndicadoresDiario);
        TFChartSerie item0 = new TFChartSerie();
        item0.setCaptionField("COD_EMPRESA");
        item0.setXField("CAPTION_DIAMES");
        item0.setYField("QTDE");
        chartLineDiario.getSeries().add(item0);
        chartLineDiario.setShowTick(false);
        chartLineDiario.setFlexVflex("ftTrue");
        chartLineDiario.setFlexHflex("ftTrue");
        chartLineDiario.setYAxisTickInterval(0);
        chartLineDiario.setXAxisTickInterval(0);
        chartLineDiario.setShowDataLabel(true);
        chartLineDiario.setXLabelRotation(0);
        chartLineDiario.setYLabelRotation(0);
        vboxGraficoDiario.addChildren(chartLineDiario);
        chartLineDiario.applyProperties();
    }

    public TFVBox vboxGraficoQuebra = new TFVBox();

    private void init_vboxGraficoQuebra() {
        vboxGraficoQuebra.setName("vboxGraficoQuebra");
        vboxGraficoQuebra.setLeft(0);
        vboxGraficoQuebra.setTop(639);
        vboxGraficoQuebra.setWidth(686);
        vboxGraficoQuebra.setHeight(290);
        vboxGraficoQuebra.setBorderStyle("stNone");
        vboxGraficoQuebra.setPaddingTop(0);
        vboxGraficoQuebra.setPaddingLeft(0);
        vboxGraficoQuebra.setPaddingRight(0);
        vboxGraficoQuebra.setPaddingBottom(0);
        vboxGraficoQuebra.setVisible(false);
        vboxGraficoQuebra.setMarginTop(0);
        vboxGraficoQuebra.setMarginLeft(0);
        vboxGraficoQuebra.setMarginRight(0);
        vboxGraficoQuebra.setMarginBottom(0);
        vboxGraficoQuebra.setSpacing(1);
        vboxGraficoQuebra.setFlexVflex("ftFalse");
        vboxGraficoQuebra.setFlexHflex("ftTrue");
        vboxGraficoQuebra.setScrollable(false);
        vboxGraficoQuebra.setBoxShadowConfigHorizontalLength(10);
        vboxGraficoQuebra.setBoxShadowConfigVerticalLength(10);
        vboxGraficoQuebra.setBoxShadowConfigBlurRadius(5);
        vboxGraficoQuebra.setBoxShadowConfigSpreadRadius(0);
        vboxGraficoQuebra.setBoxShadowConfigShadowColor("clBlack");
        vboxGraficoQuebra.setBoxShadowConfigOpacity(75);
        FVBox12.addChildren(vboxGraficoQuebra);
        vboxGraficoQuebra.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(680);
        FHBox7.setHeight(53);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(4);
        FHBox7.setFlexVflex("ftMin");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vboxGraficoQuebra.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(0);
        FVBox10.setTop(0);
        FVBox10.setWidth(200);
        FVBox10.setHeight(48);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(0);
        FVBox10.setPaddingLeft(0);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(2);
        FVBox10.setFlexVflex("ftMin");
        FVBox10.setFlexHflex("ftTrue");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFCombo cbbQuebra = new TFCombo();

    private void init_cbbQuebra() {
        cbbQuebra.setName("cbbQuebra");
        cbbQuebra.setLeft(0);
        cbbQuebra.setTop(0);
        cbbQuebra.setWidth(145);
        cbbQuebra.setHeight(21);
        cbbQuebra.setLookupTable(tbComboQuebra);
        cbbQuebra.setLookupKey("ID_QUEBRA");
        cbbQuebra.setLookupDesc("DESCRICAO_QUEBRA");
        cbbQuebra.setFlex(true);
        cbbQuebra.setReadOnly(true);
        cbbQuebra.setRequired(false);
        cbbQuebra.setPrompt("Selecione");
        cbbQuebra.setConstraintCheckWhen("cwImmediate");
        cbbQuebra.setConstraintCheckType("ctExpression");
        cbbQuebra.setConstraintFocusOnError(false);
        cbbQuebra.setConstraintEnableUI(true);
        cbbQuebra.setConstraintEnabled(false);
        cbbQuebra.setConstraintFormCheck(true);
        cbbQuebra.setClearOnDelKey(true);
        cbbQuebra.setUseClearButton(false);
        cbbQuebra.setHideClearButtonOnNullValue(false);
        cbbQuebra.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbQuebraChange(event);
            processarFlow("FrmGerencialPainel", "cbbQuebra", "OnChange");
        });
        FVBox10.addChildren(cbbQuebra);
        cbbQuebra.applyProperties();
        addValidatable(cbbQuebra);
    }

    public TFChartPie chartPizzaQuebra = new TFChartPie();

    private void init_chartPizzaQuebra() {
        chartPizzaQuebra.setName("chartPizzaQuebra");
        chartPizzaQuebra.setLeft(0);
        chartPizzaQuebra.setTop(54);
        chartPizzaQuebra.setWidth(400);
        chartPizzaQuebra.setHeight(250);
        chartPizzaQuebra.setTable(tbGraficoIndicadoresQuebra);
        chartPizzaQuebra.setCaptionField("QUEBRA_DESCRICAO");
        chartPizzaQuebra.setCategoryField("QTDE");
        chartPizzaQuebra.setValueField("QTDE");
        chartPizzaQuebra.setShowLegend(true);
        chartPizzaQuebra.setBuildIn3d(false);
        chartPizzaQuebra.setShowLabel(true);
        chartPizzaQuebra.setShowTooltip(true);
        chartPizzaQuebra.setLabelFormat("{0} = {2}");
        chartPizzaQuebra.setFlexVflex("ftFalse");
        chartPizzaQuebra.setFlexHflex("ftTrue");
        vboxGraficoQuebra.addChildren(chartPizzaQuebra);
        chartPizzaQuebra.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void cbbPainelChange(final Event<Object> event);

    public void btnPreferenciaClick(final Event<Object> event) {
        if (btnPreferencia.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPreferencia");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbbMesChange(final Event<Object> event);

    public abstract void cbbAnoChange(final Event<Object> event);

    public abstract void cbbEmpresasCruzadasChange(final Event<Object> event);

    public abstract void cbbEmpresasCruzadasClearClick(final Event<Object> event);

    public void btnAtualizarClick(final Event<Object> event) {
        if (btnAtualizar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAtualizar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridGerencialColumns0RenderTemplate(final RenderTemplateEvent<Value> event);

    public abstract void hboxEstatisticaClick(final Event<Object> event);

    public abstract void hboxUltimos12MesesClick(final Event<Object> event);

    public abstract void hboxMesAtualClick(final Event<Object> event);

    public abstract void hboxUltimos30DiasClick(final Event<Object> event);

    public abstract void cbbQuebraChange(final Event<Object> event);

    public abstract void tbGridGerencialIndicadoresAfterScroll(final Event<Object> event);

}