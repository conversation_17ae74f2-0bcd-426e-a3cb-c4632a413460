package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadastroPainelIndicadores extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadastroPainelIndicadoresRNA rn = null;

    public FrmCadastroPainelIndicadores() {
        try {
            rn = (freedom.bytecode.rn.CadastroPainelIndicadoresRNA) getRN(freedom.bytecode.rn.wizard.CadastroPainelIndicadoresRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbPainel();
        init_tbComboGrupo();
        init_tbMaxIdPainel();
        init_FVBox1();
        init_FHBox1();
        init_btnSalvar();
        init_btnCancelar();
        init_FVBox2();
        init_FLabel1();
        init_edtNomePainel();
        init_FVBox3();
        init_FLabel2();
        init_cbbGrupo();
        init_FVBox4();
        init_chkboxAtivo();
        init_FrmCadastroPainelIndicadores();
    }

    public BSC_PAINEL tbPainel;

    private void init_tbPainel() {
        tbPainel = rn.tbPainel;
        tbPainel.setName("tbPainel");
        tbPainel.setMaxRowCount(200);
        tbPainel.setWKey("382029;38201");
        tbPainel.setRatioBatchSize(20);
        getTables().put(tbPainel, "tbPainel");
        tbPainel.applyProperties();
    }

    public BSC_COMBO_GRUPO tbComboGrupo;

    private void init_tbComboGrupo() {
        tbComboGrupo = rn.tbComboGrupo;
        tbComboGrupo.setName("tbComboGrupo");
        tbComboGrupo.setMaxRowCount(200);
        tbComboGrupo.setWKey("382029;38202");
        tbComboGrupo.setRatioBatchSize(20);
        getTables().put(tbComboGrupo, "tbComboGrupo");
        tbComboGrupo.applyProperties();
    }

    public GET_MAX_ID_PAINEL tbMaxIdPainel;

    private void init_tbMaxIdPainel() {
        tbMaxIdPainel = rn.tbMaxIdPainel;
        tbMaxIdPainel.setName("tbMaxIdPainel");
        tbMaxIdPainel.setMaxRowCount(200);
        tbMaxIdPainel.setWKey("382029;38203");
        tbMaxIdPainel.setRatioBatchSize(20);
        getTables().put(tbMaxIdPainel, "tbMaxIdPainel");
        tbMaxIdPainel.applyProperties();
    }

    protected TFForm FrmCadastroPainelIndicadores = this;
    private void init_FrmCadastroPainelIndicadores() {
        FrmCadastroPainelIndicadores.setName("FrmCadastroPainelIndicadores");
        FrmCadastroPainelIndicadores.setCaption("Novo Painel");
        FrmCadastroPainelIndicadores.setClientHeight(295);
        FrmCadastroPainelIndicadores.setClientWidth(482);
        FrmCadastroPainelIndicadores.setColor("clBtnFace");
        FrmCadastroPainelIndicadores.setWKey("382029");
        FrmCadastroPainelIndicadores.setSpacing(0);
        FrmCadastroPainelIndicadores.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(482);
        FVBox1.setHeight(295);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmCadastroPainelIndicadores.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(476);
        FHBox1.setHeight(70);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(75);
        btnSalvar.setHeight(60);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadastroPainelIndicadores", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(75);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(75);
        btnCancelar.setHeight(60);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmCadastroPainelIndicadores", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(71);
        FVBox2.setWidth(476);
        FVBox2.setHeight(57);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(2);
        FVBox2.setFlexVflex("ftMin");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(94);
        FLabel1.setHeight(14);
        FLabel1.setCaption("Nome do Painel");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-12);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox2.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFString edtNomePainel = new TFString();

    private void init_edtNomePainel() {
        edtNomePainel.setName("edtNomePainel");
        edtNomePainel.setLeft(0);
        edtNomePainel.setTop(15);
        edtNomePainel.setWidth(121);
        edtNomePainel.setHeight(24);
        edtNomePainel.setTable(tbPainel);
        edtNomePainel.setFieldName("DESCRICAO");
        edtNomePainel.setFlex(true);
        edtNomePainel.setRequired(false);
        edtNomePainel.setConstraintCheckWhen("cwImmediate");
        edtNomePainel.setConstraintCheckType("ctExpression");
        edtNomePainel.setConstraintFocusOnError(false);
        edtNomePainel.setConstraintEnableUI(true);
        edtNomePainel.setConstraintEnabled(false);
        edtNomePainel.setConstraintFormCheck(true);
        edtNomePainel.setCharCase("ccNormal");
        edtNomePainel.setPwd(false);
        edtNomePainel.setMaxlength(0);
        edtNomePainel.setFontColor("clWindowText");
        edtNomePainel.setFontSize(-13);
        edtNomePainel.setFontName("Tahoma");
        edtNomePainel.setFontStyle("[]");
        edtNomePainel.setSaveLiteralCharacter(false);
        edtNomePainel.applyProperties();
        FVBox2.addChildren(edtNomePainel);
        addValidatable(edtNomePainel);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(129);
        FVBox3.setWidth(476);
        FVBox3.setHeight(57);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(2);
        FVBox3.setFlexVflex("ftMin");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(37);
        FLabel2.setHeight(14);
        FLabel2.setCaption("Grupo");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-12);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox3.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cbbGrupo = new TFCombo();

    private void init_cbbGrupo() {
        cbbGrupo.setName("cbbGrupo");
        cbbGrupo.setLeft(0);
        cbbGrupo.setTop(15);
        cbbGrupo.setWidth(145);
        cbbGrupo.setHeight(21);
        cbbGrupo.setTable(tbPainel);
        cbbGrupo.setLookupTable(tbComboGrupo);
        cbbGrupo.setFieldName("ID_GRUPO");
        cbbGrupo.setLookupKey("ID");
        cbbGrupo.setLookupDesc("DESCRICAO_GRUPO");
        cbbGrupo.setFlex(true);
        cbbGrupo.setReadOnly(true);
        cbbGrupo.setRequired(false);
        cbbGrupo.setPrompt("Selecione");
        cbbGrupo.setConstraintCheckWhen("cwImmediate");
        cbbGrupo.setConstraintCheckType("ctExpression");
        cbbGrupo.setConstraintFocusOnError(false);
        cbbGrupo.setConstraintEnableUI(true);
        cbbGrupo.setConstraintEnabled(false);
        cbbGrupo.setConstraintFormCheck(true);
        cbbGrupo.setClearOnDelKey(true);
        cbbGrupo.setUseClearButton(false);
        cbbGrupo.setHideClearButtonOnNullValue(false);
        FVBox3.addChildren(cbbGrupo);
        cbbGrupo.applyProperties();
        addValidatable(cbbGrupo);
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(187);
        FVBox4.setWidth(476);
        FVBox4.setHeight(35);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(5);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(2);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFCheckBox chkboxAtivo = new TFCheckBox();

    private void init_chkboxAtivo() {
        chkboxAtivo.setName("chkboxAtivo");
        chkboxAtivo.setLeft(0);
        chkboxAtivo.setTop(0);
        chkboxAtivo.setWidth(97);
        chkboxAtivo.setHeight(17);
        chkboxAtivo.setCaption("Ativo");
        chkboxAtivo.setChecked(true);
        chkboxAtivo.setFontColor("clWindowText");
        chkboxAtivo.setFontSize(-11);
        chkboxAtivo.setFontName("Tahoma");
        chkboxAtivo.setFontStyle("[]");
        chkboxAtivo.setTable(tbPainel);
        chkboxAtivo.setFieldName("ATIVO");
        chkboxAtivo.setCheckedValue("S");
        chkboxAtivo.setUncheckedValue("N");
        chkboxAtivo.setVerticalAlignment("taAlignTop");
        FVBox4.addChildren(chkboxAtivo);
        chkboxAtivo.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}