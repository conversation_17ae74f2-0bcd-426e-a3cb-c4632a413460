package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadClienteContato extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadClienteContatoRNA rn = null;

    public FrmCadClienteContato() {
        try {
            rn = (freedom.bytecode.rn.CadClienteContatoRNA) getRN(freedom.bytecode.rn.wizard.CadClienteContatoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteContato();
        init_tbClienteContatoTipo();
        init_tbFiltroClienteContatoTipo();
        init_vBoxPrincipal();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnAceitar();
        init_btnPesquisar();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnCancelar();
        init_pgcPrincipal();
        init_tbsListagem();
        init_vBoxTbsListagem();
        init_hBoxFiltrosLinha01();
        init_vBoxFiltroContato();
        init_lblFiltroContato();
        init_edtFiltroContato();
        init_vBoxFiltroRespPesqFabr();
        init_lblFiltroResponsavelPesqFabr();
        init_cboFiltroResponsavelPesqFabr();
        init_vBoxFiltroAreaDeContato();
        init_lblFiltroAreaDeContato();
        init_cboFiltroAreaDeContato();
        init_iconLimparPesquisa();
        init_hBoxTbsListagemSeparador01();
        init_grdContatos();
        init_tbsCadastro();
        init_vBoxTbsCadastro();
        init_hBoxTbsCadastroLinha01();
        init_vBoxContato();
        init_lblContato();
        init_hBoxContato();
        init_edtContato();
        init_btnPesquisarContato();
        init_FHBox1();
        init_vBoxContatoSexo();
        init_lblSexo();
        init_cboContatoSexo();
        init_vBoxContatoDataNasc();
        init_lblNascimento();
        init_dtNascimento();
        init_FHBox2();
        init_vBoxAreaDeContato();
        init_lblAreaDeContato();
        init_hBoxAreaDeContato();
        init_cboAreaDeContato();
        init_icoAreaDeContato();
        init_vBoxContatoFuncao();
        init_lblFuncao();
        init_FHBox5();
        init_edtFuncao();
        init_iconLimparFuncao();
        init_FVBox19();
        init_FLabel8();
        init_FHBox3();
        init_vBoxContatoCpf();
        init_lblCPF();
        init_edtCPF();
        init_vBoxContatoRg();
        init_lblRG();
        init_edtRG();
        init_vBoxContatoOrgaoEmissor();
        init_lblOrgaoEmissor();
        init_edtOrgaoEmissor();
        init_vBoxContatoDataDeEmissao();
        init_lblDataDeEmissao();
        init_dtEmissao();
        init_hBoxTbsCadastroLinha02();
        init_vBoxContatoCnh();
        init_lblCNH();
        init_edtCNH();
        init_vBoxContatoPassaporteCarteira();
        init_lblPassaporteCarteira();
        init_edtPassaporteCarteira();
        init_FVBox1();
        init_FLabel1();
        init_hBoxTbsCadastroLinha03();
        init_vBoxContatoEmail();
        init_lblEmail();
        init_edtEmail();
        init_vBoxContatoWhatsapp();
        init_hBoxContatoWhatsappLbl();
        init_lblWhatsapp();
        init_icoWhatsapp();
        init_hBoxContatoWhatsapp();
        init_edtDDDWhatsapp();
        init_edtTelefoneWhatsapp();
        init_icoLimparTelefoneWhatsapp();
        init_hBoxTbsCadastroLinha04();
        init_vBoxContatoFoneRes();
        init_hBoxContatoFoneResLbl();
        init_lblResidencial();
        init_icoTelefone();
        init_hBoxContatoFoneRes();
        init_edtDDDResidencial();
        init_edtTelefoneResidencial();
        init_icoLimparTelefoneResidencial();
        init_vBoxContatoCelular();
        init_hBoxContatoCelularLbl();
        init_lblCelular();
        init_icoCelular();
        init_hBoxContatoCelular();
        init_edtDDDCelular();
        init_edtTelefoneCelular();
        init_icoLimparTelefoneCelular();
        init_FVBox2();
        init_FLabel2();
        init_hBoxTbsCadastroLinha05();
        init_vBoxContatoPoliticamenteExposto();
        init_lblTituloPoliticamenteExposto();
        init_cboPoliticamenteExposto();
        init_vBoxEhComprador();
        init_lblEhComprador();
        init_cboEhComprador();
        init_vBoxEhPreposto();
        init_lblEhPreposto();
        init_cboEhPreposto();
        init_FHBox4();
        init_vBoxEhResponsavelPelaGarantiaDaMontadora();
        init_lblResponsavelPelaGarantiaDaMontadora();
        init_cboResponsavelPelaGarantiaDaMontadora();
        init_vBosResponsavelPelaPesquisaDaFabrica();
        init_lblResponsavelPelaPesquisaDaFabrica();
        init_cboResponsavelPelaPesquisaDaFabrica();
        init_hBoxTbsCadastroLinha06();
        init_vBoxContatoTime();
        init_lblTime();
        init_FHBox6();
        init_edtTime();
        init_iconLimparTime();
        init_vBoxContatoHobby();
        init_lblHobby();
        init_FHBox7();
        init_edtHobby();
        init_iconLimparHobby();
        init_FrmCadClienteContato();
    }

    public BUSCA_CLIENTE_CONTATO tbClienteContato;

    private void init_tbClienteContato() {
        tbClienteContato = rn.tbClienteContato;
        tbClienteContato.setName("tbClienteContato");
        tbClienteContato.setMaxRowCount(200);
        tbClienteContato.setWKey("4600220;44802");
        tbClienteContato.setRatioBatchSize(20);
        getTables().put(tbClienteContato, "tbClienteContato");
        tbClienteContato.applyProperties();
    }

    public BUSCA_CLIENTE_CONTATO_TIPO tbClienteContatoTipo;

    private void init_tbClienteContatoTipo() {
        tbClienteContatoTipo = rn.tbClienteContatoTipo;
        tbClienteContatoTipo.setName("tbClienteContatoTipo");
        tbClienteContatoTipo.setMaxRowCount(200);
        tbClienteContatoTipo.setWKey("4600220;44803");
        tbClienteContatoTipo.setRatioBatchSize(20);
        getTables().put(tbClienteContatoTipo, "tbClienteContatoTipo");
        tbClienteContatoTipo.applyProperties();
    }

    public BUSCA_CLIENTE_CONTATO_TIPO tbFiltroClienteContatoTipo;

    private void init_tbFiltroClienteContatoTipo() {
        tbFiltroClienteContatoTipo = rn.tbFiltroClienteContatoTipo;
        tbFiltroClienteContatoTipo.setName("tbFiltroClienteContatoTipo");
        tbFiltroClienteContatoTipo.setMaxRowCount(200);
        tbFiltroClienteContatoTipo.setWKey("4600220;44804");
        tbFiltroClienteContatoTipo.setRatioBatchSize(20);
        getTables().put(tbFiltroClienteContatoTipo, "tbFiltroClienteContatoTipo");
        tbFiltroClienteContatoTipo.applyProperties();
    }

    protected TFForm FrmCadClienteContato = this;
    private void init_FrmCadClienteContato() {
        FrmCadClienteContato.setName("FrmCadClienteContato");
        FrmCadClienteContato.setCaption("Contatos da empresa");
        FrmCadClienteContato.setClientHeight(796);
        FrmCadClienteContato.setClientWidth(534);
        FrmCadClienteContato.setColor("clBtnFace");
        FrmCadClienteContato.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmCadClienteContato", "FrmCadClienteContato", "OnCreate");
        });
        FrmCadClienteContato.setWOrigem("EhMain");
        FrmCadClienteContato.setWKey("4600220");
        FrmCadClienteContato.setSpacing(0);
        FrmCadClienteContato.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(534);
        vBoxPrincipal.setHeight(796);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(5);
        vBoxPrincipal.setPaddingLeft(5);
        vBoxPrincipal.setPaddingRight(5);
        vBoxPrincipal.setPaddingBottom(5);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(3);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmCadClienteContato.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(514);
        hBoxBotoes.setHeight(60);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(5);
        hBoxBotoes.setMarginLeft(5);
        hBoxBotoes.setMarginRight(5);
        hBoxBotoes.setMarginBottom(5);
        hBoxBotoes.setSpacing(4);
        hBoxBotoes.setFlexVflex("ftFalse");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(55);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmCadClienteContato", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(60);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(55);
        btnAceitar.setHint("Aceita Registro Selecionado na Grid");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-13);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.setVisible(false);
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmCadClienteContato", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(120);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(60);
        btnPesquisar.setHeight(55);
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmCadClienteContato", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(13);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(180);
        btnNovo.setTop(0);
        btnNovo.setWidth(60);
        btnNovo.setHeight(55);
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmCadClienteContato", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(false);
        btnNovo.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(240);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(60);
        btnAlterar.setHeight(55);
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmCadClienteContato", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(300);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(60);
        btnExcluir.setHeight(55);
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmCadClienteContato", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(360);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(60);
        btnSalvar.setHeight(55);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(420);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(55);
        btnCancelar.setHeight(55);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmCadClienteContato", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFPageControl pgcPrincipal = new TFPageControl();

    private void init_pgcPrincipal() {
        pgcPrincipal.setName("pgcPrincipal");
        pgcPrincipal.setLeft(0);
        pgcPrincipal.setTop(61);
        pgcPrincipal.setWidth(520);
        pgcPrincipal.setHeight(742);
        pgcPrincipal.setTabPosition("tpTop");
        pgcPrincipal.setFlexVflex("ftTrue");
        pgcPrincipal.setFlexHflex("ftTrue");
        pgcPrincipal.setRenderStyle("rsTabbed");
        pgcPrincipal.applyProperties();
        vBoxPrincipal.addChildren(pgcPrincipal);
    }

    public TFTabsheet tbsListagem = new TFTabsheet();

    private void init_tbsListagem() {
        tbsListagem.setName("tbsListagem");
        tbsListagem.setCaption("Listagem");
        tbsListagem.setVisible(true);
        tbsListagem.setClosable(false);
        pgcPrincipal.addChildren(tbsListagem);
        tbsListagem.applyProperties();
    }

    public TFVBox vBoxTbsListagem = new TFVBox();

    private void init_vBoxTbsListagem() {
        vBoxTbsListagem.setName("vBoxTbsListagem");
        vBoxTbsListagem.setLeft(0);
        vBoxTbsListagem.setTop(0);
        vBoxTbsListagem.setWidth(512);
        vBoxTbsListagem.setHeight(714);
        vBoxTbsListagem.setAlign("alClient");
        vBoxTbsListagem.setBorderStyle("stNone");
        vBoxTbsListagem.setPaddingTop(0);
        vBoxTbsListagem.setPaddingLeft(0);
        vBoxTbsListagem.setPaddingRight(0);
        vBoxTbsListagem.setPaddingBottom(0);
        vBoxTbsListagem.setMarginTop(5);
        vBoxTbsListagem.setMarginLeft(5);
        vBoxTbsListagem.setMarginRight(5);
        vBoxTbsListagem.setMarginBottom(5);
        vBoxTbsListagem.setSpacing(0);
        vBoxTbsListagem.setFlexVflex("ftTrue");
        vBoxTbsListagem.setFlexHflex("ftTrue");
        vBoxTbsListagem.setScrollable(false);
        vBoxTbsListagem.setBoxShadowConfigHorizontalLength(10);
        vBoxTbsListagem.setBoxShadowConfigVerticalLength(10);
        vBoxTbsListagem.setBoxShadowConfigBlurRadius(5);
        vBoxTbsListagem.setBoxShadowConfigSpreadRadius(0);
        vBoxTbsListagem.setBoxShadowConfigShadowColor("clBlack");
        vBoxTbsListagem.setBoxShadowConfigOpacity(75);
        tbsListagem.addChildren(vBoxTbsListagem);
        vBoxTbsListagem.applyProperties();
    }

    public TFHBox hBoxFiltrosLinha01 = new TFHBox();

    private void init_hBoxFiltrosLinha01() {
        hBoxFiltrosLinha01.setName("hBoxFiltrosLinha01");
        hBoxFiltrosLinha01.setLeft(0);
        hBoxFiltrosLinha01.setTop(0);
        hBoxFiltrosLinha01.setWidth(466);
        hBoxFiltrosLinha01.setHeight(60);
        hBoxFiltrosLinha01.setBorderStyle("stNone");
        hBoxFiltrosLinha01.setPaddingTop(0);
        hBoxFiltrosLinha01.setPaddingLeft(0);
        hBoxFiltrosLinha01.setPaddingRight(0);
        hBoxFiltrosLinha01.setPaddingBottom(0);
        hBoxFiltrosLinha01.setMarginTop(0);
        hBoxFiltrosLinha01.setMarginLeft(0);
        hBoxFiltrosLinha01.setMarginRight(0);
        hBoxFiltrosLinha01.setMarginBottom(0);
        hBoxFiltrosLinha01.setSpacing(5);
        hBoxFiltrosLinha01.setFlexVflex("ftMin");
        hBoxFiltrosLinha01.setFlexHflex("ftTrue");
        hBoxFiltrosLinha01.setScrollable(false);
        hBoxFiltrosLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxFiltrosLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxFiltrosLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxFiltrosLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxFiltrosLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxFiltrosLinha01.setBoxShadowConfigOpacity(75);
        hBoxFiltrosLinha01.setVAlign("tvTop");
        vBoxTbsListagem.addChildren(hBoxFiltrosLinha01);
        hBoxFiltrosLinha01.applyProperties();
    }

    public TFVBox vBoxFiltroContato = new TFVBox();

    private void init_vBoxFiltroContato() {
        vBoxFiltroContato.setName("vBoxFiltroContato");
        vBoxFiltroContato.setLeft(0);
        vBoxFiltroContato.setTop(0);
        vBoxFiltroContato.setWidth(110);
        vBoxFiltroContato.setHeight(50);
        vBoxFiltroContato.setBorderStyle("stNone");
        vBoxFiltroContato.setPaddingTop(0);
        vBoxFiltroContato.setPaddingLeft(0);
        vBoxFiltroContato.setPaddingRight(0);
        vBoxFiltroContato.setPaddingBottom(0);
        vBoxFiltroContato.setMarginTop(0);
        vBoxFiltroContato.setMarginLeft(0);
        vBoxFiltroContato.setMarginRight(0);
        vBoxFiltroContato.setMarginBottom(0);
        vBoxFiltroContato.setSpacing(1);
        vBoxFiltroContato.setFlexVflex("ftMin");
        vBoxFiltroContato.setFlexHflex("ftTrue");
        vBoxFiltroContato.setScrollable(false);
        vBoxFiltroContato.setBoxShadowConfigHorizontalLength(10);
        vBoxFiltroContato.setBoxShadowConfigVerticalLength(10);
        vBoxFiltroContato.setBoxShadowConfigBlurRadius(5);
        vBoxFiltroContato.setBoxShadowConfigSpreadRadius(0);
        vBoxFiltroContato.setBoxShadowConfigShadowColor("clBlack");
        vBoxFiltroContato.setBoxShadowConfigOpacity(75);
        hBoxFiltrosLinha01.addChildren(vBoxFiltroContato);
        vBoxFiltroContato.applyProperties();
    }

    public TFLabel lblFiltroContato = new TFLabel();

    private void init_lblFiltroContato() {
        lblFiltroContato.setName("lblFiltroContato");
        lblFiltroContato.setLeft(0);
        lblFiltroContato.setTop(0);
        lblFiltroContato.setWidth(39);
        lblFiltroContato.setHeight(13);
        lblFiltroContato.setCaption("Contato");
        lblFiltroContato.setFontColor("clWindowText");
        lblFiltroContato.setFontSize(-11);
        lblFiltroContato.setFontName("Tahoma");
        lblFiltroContato.setFontStyle("[]");
        lblFiltroContato.setVisible(false);
        lblFiltroContato.setVerticalAlignment("taVerticalCenter");
        lblFiltroContato.setWordBreak(false);
        vBoxFiltroContato.addChildren(lblFiltroContato);
        lblFiltroContato.applyProperties();
    }

    public TFString edtFiltroContato = new TFString();

    private void init_edtFiltroContato() {
        edtFiltroContato.setName("edtFiltroContato");
        edtFiltroContato.setLeft(0);
        edtFiltroContato.setTop(14);
        edtFiltroContato.setWidth(100);
        edtFiltroContato.setHeight(24);
        edtFiltroContato.setHint("Contato");
        edtFiltroContato.setHelpCaption("Contato");
        edtFiltroContato.setFlex(true);
        edtFiltroContato.setRequired(false);
        edtFiltroContato.setPrompt("Contato / Email / Celular");
        edtFiltroContato.setConstraintCheckWhen("cwImmediate");
        edtFiltroContato.setConstraintCheckType("ctExpression");
        edtFiltroContato.setConstraintFocusOnError(false);
        edtFiltroContato.setConstraintEnableUI(true);
        edtFiltroContato.setConstraintEnabled(false);
        edtFiltroContato.setConstraintFormCheck(true);
        edtFiltroContato.setCharCase("ccNormal");
        edtFiltroContato.setPwd(false);
        edtFiltroContato.setMaxlength(0);
        edtFiltroContato.setFontColor("clWindowText");
        edtFiltroContato.setFontSize(-13);
        edtFiltroContato.setFontName("Tahoma");
        edtFiltroContato.setFontStyle("[]");
        edtFiltroContato.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmCadClienteContato", "edtFiltroContato", "OnEnter");
        });
        edtFiltroContato.setSaveLiteralCharacter(false);
        edtFiltroContato.applyProperties();
        vBoxFiltroContato.addChildren(edtFiltroContato);
        addValidatable(edtFiltroContato);
    }

    public TFVBox vBoxFiltroRespPesqFabr = new TFVBox();

    private void init_vBoxFiltroRespPesqFabr() {
        vBoxFiltroRespPesqFabr.setName("vBoxFiltroRespPesqFabr");
        vBoxFiltroRespPesqFabr.setLeft(110);
        vBoxFiltroRespPesqFabr.setTop(0);
        vBoxFiltroRespPesqFabr.setWidth(130);
        vBoxFiltroRespPesqFabr.setHeight(50);
        vBoxFiltroRespPesqFabr.setBorderStyle("stNone");
        vBoxFiltroRespPesqFabr.setPaddingTop(0);
        vBoxFiltroRespPesqFabr.setPaddingLeft(0);
        vBoxFiltroRespPesqFabr.setPaddingRight(0);
        vBoxFiltroRespPesqFabr.setPaddingBottom(0);
        vBoxFiltroRespPesqFabr.setVisible(false);
        vBoxFiltroRespPesqFabr.setMarginTop(0);
        vBoxFiltroRespPesqFabr.setMarginLeft(0);
        vBoxFiltroRespPesqFabr.setMarginRight(0);
        vBoxFiltroRespPesqFabr.setMarginBottom(0);
        vBoxFiltroRespPesqFabr.setSpacing(1);
        vBoxFiltroRespPesqFabr.setFlexVflex("ftMin");
        vBoxFiltroRespPesqFabr.setFlexHflex("ftMin");
        vBoxFiltroRespPesqFabr.setScrollable(false);
        vBoxFiltroRespPesqFabr.setBoxShadowConfigHorizontalLength(10);
        vBoxFiltroRespPesqFabr.setBoxShadowConfigVerticalLength(10);
        vBoxFiltroRespPesqFabr.setBoxShadowConfigBlurRadius(5);
        vBoxFiltroRespPesqFabr.setBoxShadowConfigSpreadRadius(0);
        vBoxFiltroRespPesqFabr.setBoxShadowConfigShadowColor("clBlack");
        vBoxFiltroRespPesqFabr.setBoxShadowConfigOpacity(75);
        hBoxFiltrosLinha01.addChildren(vBoxFiltroRespPesqFabr);
        vBoxFiltroRespPesqFabr.applyProperties();
    }

    public TFLabel lblFiltroResponsavelPesqFabr = new TFLabel();

    private void init_lblFiltroResponsavelPesqFabr() {
        lblFiltroResponsavelPesqFabr.setName("lblFiltroResponsavelPesqFabr");
        lblFiltroResponsavelPesqFabr.setLeft(0);
        lblFiltroResponsavelPesqFabr.setTop(0);
        lblFiltroResponsavelPesqFabr.setWidth(118);
        lblFiltroResponsavelPesqFabr.setHeight(13);
        lblFiltroResponsavelPesqFabr.setCaption("Respons\u00E1vel pesq. f\u00E1br.");
        lblFiltroResponsavelPesqFabr.setFontColor("clWindowText");
        lblFiltroResponsavelPesqFabr.setFontSize(-11);
        lblFiltroResponsavelPesqFabr.setFontName("Tahoma");
        lblFiltroResponsavelPesqFabr.setFontStyle("[]");
        lblFiltroResponsavelPesqFabr.setVerticalAlignment("taVerticalCenter");
        lblFiltroResponsavelPesqFabr.setWordBreak(false);
        vBoxFiltroRespPesqFabr.addChildren(lblFiltroResponsavelPesqFabr);
        lblFiltroResponsavelPesqFabr.applyProperties();
    }

    public TFCombo cboFiltroResponsavelPesqFabr = new TFCombo();

    private void init_cboFiltroResponsavelPesqFabr() {
        cboFiltroResponsavelPesqFabr.setName("cboFiltroResponsavelPesqFabr");
        cboFiltroResponsavelPesqFabr.setLeft(0);
        cboFiltroResponsavelPesqFabr.setTop(14);
        cboFiltroResponsavelPesqFabr.setWidth(120);
        cboFiltroResponsavelPesqFabr.setHeight(21);
        cboFiltroResponsavelPesqFabr.setHint("Respons\u00E1vel pela pesquisa da f\u00E1brica");
        cboFiltroResponsavelPesqFabr.setFlex(false);
        cboFiltroResponsavelPesqFabr.setListOptions("Sim=S;N\u00E3o=N");
        cboFiltroResponsavelPesqFabr.setHelpCaption("Respons\u00E1vel pela pesquisa da f\u00E1brica");
        cboFiltroResponsavelPesqFabr.setReadOnly(true);
        cboFiltroResponsavelPesqFabr.setRequired(true);
        cboFiltroResponsavelPesqFabr.setPrompt("Respons\u00E1vel pela pesquisa da f\u00E1brica");
        cboFiltroResponsavelPesqFabr.setConstraintCheckWhen("cwImmediate");
        cboFiltroResponsavelPesqFabr.setConstraintCheckType("ctExpression");
        cboFiltroResponsavelPesqFabr.setConstraintFocusOnError(false);
        cboFiltroResponsavelPesqFabr.setConstraintEnableUI(true);
        cboFiltroResponsavelPesqFabr.setConstraintEnabled(false);
        cboFiltroResponsavelPesqFabr.setConstraintFormCheck(true);
        cboFiltroResponsavelPesqFabr.setClearOnDelKey(false);
        cboFiltroResponsavelPesqFabr.setUseClearButton(true);
        cboFiltroResponsavelPesqFabr.setHideClearButtonOnNullValue(true);
        cboFiltroResponsavelPesqFabr.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmCadClienteContato", "cboFiltroResponsavelPesqFabr", "OnEnter");
        });
        vBoxFiltroRespPesqFabr.addChildren(cboFiltroResponsavelPesqFabr);
        cboFiltroResponsavelPesqFabr.applyProperties();
        addValidatable(cboFiltroResponsavelPesqFabr);
    }

    public TFVBox vBoxFiltroAreaDeContato = new TFVBox();

    private void init_vBoxFiltroAreaDeContato() {
        vBoxFiltroAreaDeContato.setName("vBoxFiltroAreaDeContato");
        vBoxFiltroAreaDeContato.setLeft(240);
        vBoxFiltroAreaDeContato.setTop(0);
        vBoxFiltroAreaDeContato.setWidth(130);
        vBoxFiltroAreaDeContato.setHeight(50);
        vBoxFiltroAreaDeContato.setBorderStyle("stNone");
        vBoxFiltroAreaDeContato.setPaddingTop(0);
        vBoxFiltroAreaDeContato.setPaddingLeft(0);
        vBoxFiltroAreaDeContato.setPaddingRight(0);
        vBoxFiltroAreaDeContato.setPaddingBottom(0);
        vBoxFiltroAreaDeContato.setVisible(false);
        vBoxFiltroAreaDeContato.setMarginTop(0);
        vBoxFiltroAreaDeContato.setMarginLeft(0);
        vBoxFiltroAreaDeContato.setMarginRight(0);
        vBoxFiltroAreaDeContato.setMarginBottom(0);
        vBoxFiltroAreaDeContato.setSpacing(1);
        vBoxFiltroAreaDeContato.setFlexVflex("ftMin");
        vBoxFiltroAreaDeContato.setFlexHflex("ftMin");
        vBoxFiltroAreaDeContato.setScrollable(false);
        vBoxFiltroAreaDeContato.setBoxShadowConfigHorizontalLength(10);
        vBoxFiltroAreaDeContato.setBoxShadowConfigVerticalLength(10);
        vBoxFiltroAreaDeContato.setBoxShadowConfigBlurRadius(5);
        vBoxFiltroAreaDeContato.setBoxShadowConfigSpreadRadius(0);
        vBoxFiltroAreaDeContato.setBoxShadowConfigShadowColor("clBlack");
        vBoxFiltroAreaDeContato.setBoxShadowConfigOpacity(75);
        hBoxFiltrosLinha01.addChildren(vBoxFiltroAreaDeContato);
        vBoxFiltroAreaDeContato.applyProperties();
    }

    public TFLabel lblFiltroAreaDeContato = new TFLabel();

    private void init_lblFiltroAreaDeContato() {
        lblFiltroAreaDeContato.setName("lblFiltroAreaDeContato");
        lblFiltroAreaDeContato.setLeft(0);
        lblFiltroAreaDeContato.setTop(0);
        lblFiltroAreaDeContato.setWidth(78);
        lblFiltroAreaDeContato.setHeight(13);
        lblFiltroAreaDeContato.setCaption("\u00C1rea de contato");
        lblFiltroAreaDeContato.setFontColor("clWindowText");
        lblFiltroAreaDeContato.setFontSize(-11);
        lblFiltroAreaDeContato.setFontName("Tahoma");
        lblFiltroAreaDeContato.setFontStyle("[]");
        lblFiltroAreaDeContato.setVerticalAlignment("taVerticalCenter");
        lblFiltroAreaDeContato.setWordBreak(false);
        vBoxFiltroAreaDeContato.addChildren(lblFiltroAreaDeContato);
        lblFiltroAreaDeContato.applyProperties();
    }

    public TFCombo cboFiltroAreaDeContato = new TFCombo();

    private void init_cboFiltroAreaDeContato() {
        cboFiltroAreaDeContato.setName("cboFiltroAreaDeContato");
        cboFiltroAreaDeContato.setLeft(0);
        cboFiltroAreaDeContato.setTop(14);
        cboFiltroAreaDeContato.setWidth(120);
        cboFiltroAreaDeContato.setHeight(21);
        cboFiltroAreaDeContato.setHint("\u00C1rea de contato");
        cboFiltroAreaDeContato.setLookupTable(tbFiltroClienteContatoTipo);
        cboFiltroAreaDeContato.setLookupKey("AREA_CONTATO");
        cboFiltroAreaDeContato.setLookupDesc("DESCRICAO_CODIGO");
        cboFiltroAreaDeContato.setFlex(false);
        cboFiltroAreaDeContato.setHelpCaption("\u00C1rea de contato");
        cboFiltroAreaDeContato.setReadOnly(true);
        cboFiltroAreaDeContato.setRequired(true);
        cboFiltroAreaDeContato.setPrompt("\u00C1rea de contato");
        cboFiltroAreaDeContato.setConstraintCheckWhen("cwImmediate");
        cboFiltroAreaDeContato.setConstraintCheckType("ctExpression");
        cboFiltroAreaDeContato.setConstraintFocusOnError(false);
        cboFiltroAreaDeContato.setConstraintEnableUI(true);
        cboFiltroAreaDeContato.setConstraintEnabled(false);
        cboFiltroAreaDeContato.setConstraintFormCheck(true);
        cboFiltroAreaDeContato.setClearOnDelKey(false);
        cboFiltroAreaDeContato.setUseClearButton(true);
        cboFiltroAreaDeContato.setHideClearButtonOnNullValue(true);
        cboFiltroAreaDeContato.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmCadClienteContato", "cboFiltroAreaDeContato", "OnEnter");
        });
        vBoxFiltroAreaDeContato.addChildren(cboFiltroAreaDeContato);
        cboFiltroAreaDeContato.applyProperties();
        addValidatable(cboFiltroAreaDeContato);
    }

    public TFIconClass iconLimparPesquisa = new TFIconClass();

    private void init_iconLimparPesquisa() {
        iconLimparPesquisa.setName("iconLimparPesquisa");
        iconLimparPesquisa.setLeft(370);
        iconLimparPesquisa.setTop(0);
        iconLimparPesquisa.setHint("Limpar Time");
        iconLimparPesquisa.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconLimparPesquisaClick(event);
            processarFlow("FrmCadClienteContato", "iconLimparPesquisa", "OnClick");
        });
        iconLimparPesquisa.setIconClass("trash");
        iconLimparPesquisa.setSize(25);
        iconLimparPesquisa.setColor("clBlack");
        hBoxFiltrosLinha01.addChildren(iconLimparPesquisa);
        iconLimparPesquisa.applyProperties();
    }

    public TFHBox hBoxTbsListagemSeparador01 = new TFHBox();

    private void init_hBoxTbsListagemSeparador01() {
        hBoxTbsListagemSeparador01.setName("hBoxTbsListagemSeparador01");
        hBoxTbsListagemSeparador01.setLeft(0);
        hBoxTbsListagemSeparador01.setTop(61);
        hBoxTbsListagemSeparador01.setWidth(100);
        hBoxTbsListagemSeparador01.setHeight(5);
        hBoxTbsListagemSeparador01.setBorderStyle("stNone");
        hBoxTbsListagemSeparador01.setPaddingTop(0);
        hBoxTbsListagemSeparador01.setPaddingLeft(0);
        hBoxTbsListagemSeparador01.setPaddingRight(0);
        hBoxTbsListagemSeparador01.setPaddingBottom(0);
        hBoxTbsListagemSeparador01.setMarginTop(0);
        hBoxTbsListagemSeparador01.setMarginLeft(0);
        hBoxTbsListagemSeparador01.setMarginRight(0);
        hBoxTbsListagemSeparador01.setMarginBottom(0);
        hBoxTbsListagemSeparador01.setSpacing(1);
        hBoxTbsListagemSeparador01.setFlexVflex("ftFalse");
        hBoxTbsListagemSeparador01.setFlexHflex("ftFalse");
        hBoxTbsListagemSeparador01.setScrollable(false);
        hBoxTbsListagemSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxTbsListagemSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxTbsListagemSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxTbsListagemSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxTbsListagemSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxTbsListagemSeparador01.setBoxShadowConfigOpacity(75);
        hBoxTbsListagemSeparador01.setVAlign("tvTop");
        vBoxTbsListagem.addChildren(hBoxTbsListagemSeparador01);
        hBoxTbsListagemSeparador01.applyProperties();
    }

    public TFGrid grdContatos = new TFGrid();

    private void init_grdContatos() {
        grdContatos.setName("grdContatos");
        grdContatos.setLeft(0);
        grdContatos.setTop(67);
        grdContatos.setWidth(488);
        grdContatos.setHeight(202);
        grdContatos.setAlign("alClient");
        grdContatos.setTable(tbClienteContato);
        grdContatos.setFlexVflex("ftTrue");
        grdContatos.setFlexHflex("ftTrue");
        grdContatos.setPagingEnabled(true);
        grdContatos.setFrozenColumns(0);
        grdContatos.setShowFooter(false);
        grdContatos.setShowHeader(true);
        grdContatos.setMultiSelection(false);
        grdContatos.setGroupingEnabled(false);
        grdContatos.setGroupingExpanded(false);
        grdContatos.setGroupingShowFooter(false);
        grdContatos.setCrosstabEnabled(false);
        grdContatos.setCrosstabGroupType("cgtConcat");
        grdContatos.setEditionEnabled(false);
        grdContatos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("CONTATO");
        item0.setTitleCaption("Contato");
        item0.setWidth(151);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdContatos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("CPF");
        item1.setWidth(90);
        item1.setVisible(false);
        item1.setPrecision(0);
        item1.setTextAlign("taRight");
        item1.setFieldType("ftInteger");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdContatos.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("PREFIXO_CEL");
        item2.setTitleCaption("DDD");
        item2.setWidth(45);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdContatos.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("CELULAR");
        item3.setTitleCaption("Celular");
        item3.setWidth(92);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdContatos.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("EMAIL");
        item4.setTitleCaption("E-mail");
        item4.setWidth(150);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdContatos.getColumns().add(item4);
        vBoxTbsListagem.addChildren(grdContatos);
        grdContatos.applyProperties();
    }

    public TFTabsheet tbsCadastro = new TFTabsheet();

    private void init_tbsCadastro() {
        tbsCadastro.setName("tbsCadastro");
        tbsCadastro.setCaption("Cadastro");
        tbsCadastro.setVisible(true);
        tbsCadastro.setClosable(false);
        pgcPrincipal.addChildren(tbsCadastro);
        tbsCadastro.applyProperties();
    }

    public TFVBox vBoxTbsCadastro = new TFVBox();

    private void init_vBoxTbsCadastro() {
        vBoxTbsCadastro.setName("vBoxTbsCadastro");
        vBoxTbsCadastro.setLeft(0);
        vBoxTbsCadastro.setTop(0);
        vBoxTbsCadastro.setWidth(512);
        vBoxTbsCadastro.setHeight(714);
        vBoxTbsCadastro.setAlign("alClient");
        vBoxTbsCadastro.setBorderStyle("stNone");
        vBoxTbsCadastro.setPaddingTop(0);
        vBoxTbsCadastro.setPaddingLeft(0);
        vBoxTbsCadastro.setPaddingRight(0);
        vBoxTbsCadastro.setPaddingBottom(0);
        vBoxTbsCadastro.setMarginTop(0);
        vBoxTbsCadastro.setMarginLeft(0);
        vBoxTbsCadastro.setMarginRight(0);
        vBoxTbsCadastro.setMarginBottom(0);
        vBoxTbsCadastro.setSpacing(5);
        vBoxTbsCadastro.setFlexVflex("ftTrue");
        vBoxTbsCadastro.setFlexHflex("ftTrue");
        vBoxTbsCadastro.setScrollable(true);
        vBoxTbsCadastro.setBoxShadowConfigHorizontalLength(10);
        vBoxTbsCadastro.setBoxShadowConfigVerticalLength(10);
        vBoxTbsCadastro.setBoxShadowConfigBlurRadius(5);
        vBoxTbsCadastro.setBoxShadowConfigSpreadRadius(0);
        vBoxTbsCadastro.setBoxShadowConfigShadowColor("clBlack");
        vBoxTbsCadastro.setBoxShadowConfigOpacity(75);
        tbsCadastro.addChildren(vBoxTbsCadastro);
        vBoxTbsCadastro.applyProperties();
    }

    public TFHBox hBoxTbsCadastroLinha01 = new TFHBox();

    private void init_hBoxTbsCadastroLinha01() {
        hBoxTbsCadastroLinha01.setName("hBoxTbsCadastroLinha01");
        hBoxTbsCadastroLinha01.setLeft(0);
        hBoxTbsCadastroLinha01.setTop(0);
        hBoxTbsCadastroLinha01.setWidth(500);
        hBoxTbsCadastroLinha01.setHeight(70);
        hBoxTbsCadastroLinha01.setBorderStyle("stNone");
        hBoxTbsCadastroLinha01.setPaddingTop(0);
        hBoxTbsCadastroLinha01.setPaddingLeft(0);
        hBoxTbsCadastroLinha01.setPaddingRight(0);
        hBoxTbsCadastroLinha01.setPaddingBottom(0);
        hBoxTbsCadastroLinha01.setMarginTop(0);
        hBoxTbsCadastroLinha01.setMarginLeft(5);
        hBoxTbsCadastroLinha01.setMarginRight(5);
        hBoxTbsCadastroLinha01.setMarginBottom(0);
        hBoxTbsCadastroLinha01.setSpacing(5);
        hBoxTbsCadastroLinha01.setFlexVflex("ftMin");
        hBoxTbsCadastroLinha01.setFlexHflex("ftTrue");
        hBoxTbsCadastroLinha01.setScrollable(false);
        hBoxTbsCadastroLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxTbsCadastroLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxTbsCadastroLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxTbsCadastroLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxTbsCadastroLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxTbsCadastroLinha01.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha01.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(hBoxTbsCadastroLinha01);
        hBoxTbsCadastroLinha01.applyProperties();
    }

    public TFVBox vBoxContato = new TFVBox();

    private void init_vBoxContato() {
        vBoxContato.setName("vBoxContato");
        vBoxContato.setLeft(0);
        vBoxContato.setTop(0);
        vBoxContato.setWidth(160);
        vBoxContato.setHeight(60);
        vBoxContato.setBorderStyle("stNone");
        vBoxContato.setPaddingTop(0);
        vBoxContato.setPaddingLeft(0);
        vBoxContato.setPaddingRight(0);
        vBoxContato.setPaddingBottom(0);
        vBoxContato.setMarginTop(0);
        vBoxContato.setMarginLeft(0);
        vBoxContato.setMarginRight(0);
        vBoxContato.setMarginBottom(0);
        vBoxContato.setSpacing(1);
        vBoxContato.setFlexVflex("ftMin");
        vBoxContato.setFlexHflex("ftTrue");
        vBoxContato.setScrollable(false);
        vBoxContato.setBoxShadowConfigHorizontalLength(10);
        vBoxContato.setBoxShadowConfigVerticalLength(10);
        vBoxContato.setBoxShadowConfigBlurRadius(5);
        vBoxContato.setBoxShadowConfigSpreadRadius(0);
        vBoxContato.setBoxShadowConfigShadowColor("clBlack");
        vBoxContato.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha01.addChildren(vBoxContato);
        vBoxContato.applyProperties();
    }

    public TFLabel lblContato = new TFLabel();

    private void init_lblContato() {
        lblContato.setName("lblContato");
        lblContato.setLeft(0);
        lblContato.setTop(0);
        lblContato.setWidth(44);
        lblContato.setHeight(16);
        lblContato.setCaption("Contato");
        lblContato.setFontColor("clWindowText");
        lblContato.setFontSize(-13);
        lblContato.setFontName("Tahoma");
        lblContato.setFontStyle("[]");
        lblContato.setVerticalAlignment("taVerticalCenter");
        lblContato.setWordBreak(false);
        vBoxContato.addChildren(lblContato);
        lblContato.applyProperties();
    }

    public TFHBox hBoxContato = new TFHBox();

    private void init_hBoxContato() {
        hBoxContato.setName("hBoxContato");
        hBoxContato.setLeft(0);
        hBoxContato.setTop(17);
        hBoxContato.setWidth(150);
        hBoxContato.setHeight(30);
        hBoxContato.setBorderStyle("stNone");
        hBoxContato.setPaddingTop(0);
        hBoxContato.setPaddingLeft(0);
        hBoxContato.setPaddingRight(0);
        hBoxContato.setPaddingBottom(0);
        hBoxContato.setMarginTop(0);
        hBoxContato.setMarginLeft(0);
        hBoxContato.setMarginRight(0);
        hBoxContato.setMarginBottom(0);
        hBoxContato.setSpacing(5);
        hBoxContato.setFlexVflex("ftMin");
        hBoxContato.setFlexHflex("ftTrue");
        hBoxContato.setScrollable(false);
        hBoxContato.setBoxShadowConfigHorizontalLength(10);
        hBoxContato.setBoxShadowConfigVerticalLength(10);
        hBoxContato.setBoxShadowConfigBlurRadius(5);
        hBoxContato.setBoxShadowConfigSpreadRadius(0);
        hBoxContato.setBoxShadowConfigShadowColor("clBlack");
        hBoxContato.setBoxShadowConfigOpacity(75);
        hBoxContato.setVAlign("tvTop");
        vBoxContato.addChildren(hBoxContato);
        hBoxContato.applyProperties();
    }

    public TFString edtContato = new TFString();

    private void init_edtContato() {
        edtContato.setName("edtContato");
        edtContato.setLeft(0);
        edtContato.setTop(0);
        edtContato.setWidth(110);
        edtContato.setHeight(24);
        edtContato.setHint("Contato");
        edtContato.setTable(tbClienteContato);
        edtContato.setFieldName("CONTATO");
        edtContato.setHelpCaption("Contato");
        edtContato.setFlex(true);
        edtContato.setRequired(false);
        edtContato.setPrompt("Contato");
        edtContato.setConstraintCheckWhen("cwImmediate");
        edtContato.setConstraintCheckType("ctExpression");
        edtContato.setConstraintFocusOnError(false);
        edtContato.setConstraintEnableUI(true);
        edtContato.setConstraintEnabled(false);
        edtContato.setConstraintFormCheck(true);
        edtContato.setCharCase("ccUpper");
        edtContato.setPwd(false);
        edtContato.setMaxlength(50);
        edtContato.setFontColor("clWindowText");
        edtContato.setFontSize(-13);
        edtContato.setFontName("Tahoma");
        edtContato.setFontStyle("[]");
        edtContato.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtContato", "OnEnter");
        });
        edtContato.setSaveLiteralCharacter(false);
        edtContato.applyProperties();
        hBoxContato.addChildren(edtContato);
        addValidatable(edtContato);
    }

    public TFButton btnPesquisarContato = new TFButton();

    private void init_btnPesquisarContato() {
        btnPesquisarContato.setName("btnPesquisarContato");
        btnPesquisarContato.setLeft(110);
        btnPesquisarContato.setTop(0);
        btnPesquisarContato.setWidth(30);
        btnPesquisarContato.setHeight(30);
        btnPesquisarContato.setHint("Pesquisar contato");
        btnPesquisarContato.setFontColor("clWindowText");
        btnPesquisarContato.setFontSize(-11);
        btnPesquisarContato.setFontName("Tahoma");
        btnPesquisarContato.setFontStyle("[]");
        btnPesquisarContato.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarContatoClick(event);
            processarFlow("FrmCadClienteContato", "btnPesquisarContato", "OnClick");
        });
        btnPesquisarContato.setImageId(0);
        btnPesquisarContato.setColor("clBtnFace");
        btnPesquisarContato.setAccess(false);
        btnPesquisarContato.setIconClass("search");
        btnPesquisarContato.setIconReverseDirection(false);
        hBoxContato.addChildren(btnPesquisarContato);
        btnPesquisarContato.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(71);
        FHBox1.setWidth(500);
        FHBox1.setHeight(61);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftMin");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox vBoxContatoSexo = new TFVBox();

    private void init_vBoxContatoSexo() {
        vBoxContatoSexo.setName("vBoxContatoSexo");
        vBoxContatoSexo.setLeft(0);
        vBoxContatoSexo.setTop(0);
        vBoxContatoSexo.setWidth(130);
        vBoxContatoSexo.setHeight(50);
        vBoxContatoSexo.setAlign("alLeft");
        vBoxContatoSexo.setBorderStyle("stNone");
        vBoxContatoSexo.setPaddingTop(0);
        vBoxContatoSexo.setPaddingLeft(0);
        vBoxContatoSexo.setPaddingRight(0);
        vBoxContatoSexo.setPaddingBottom(0);
        vBoxContatoSexo.setMarginTop(0);
        vBoxContatoSexo.setMarginLeft(0);
        vBoxContatoSexo.setMarginRight(0);
        vBoxContatoSexo.setMarginBottom(0);
        vBoxContatoSexo.setSpacing(0);
        vBoxContatoSexo.setFlexVflex("ftMin");
        vBoxContatoSexo.setFlexHflex("ftTrue");
        vBoxContatoSexo.setScrollable(false);
        vBoxContatoSexo.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoSexo.setBoxShadowConfigVerticalLength(10);
        vBoxContatoSexo.setBoxShadowConfigBlurRadius(5);
        vBoxContatoSexo.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoSexo.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoSexo.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(vBoxContatoSexo);
        vBoxContatoSexo.applyProperties();
    }

    public TFLabel lblSexo = new TFLabel();

    private void init_lblSexo() {
        lblSexo.setName("lblSexo");
        lblSexo.setLeft(0);
        lblSexo.setTop(0);
        lblSexo.setWidth(28);
        lblSexo.setHeight(16);
        lblSexo.setCaption("Sexo");
        lblSexo.setFontColor("clWindowText");
        lblSexo.setFontSize(-13);
        lblSexo.setFontName("Tahoma");
        lblSexo.setFontStyle("[]");
        lblSexo.setVerticalAlignment("taVerticalCenter");
        lblSexo.setWordBreak(false);
        vBoxContatoSexo.addChildren(lblSexo);
        lblSexo.applyProperties();
    }

    public TFCombo cboContatoSexo = new TFCombo();

    private void init_cboContatoSexo() {
        cboContatoSexo.setName("cboContatoSexo");
        cboContatoSexo.setLeft(0);
        cboContatoSexo.setTop(17);
        cboContatoSexo.setWidth(120);
        cboContatoSexo.setHeight(21);
        cboContatoSexo.setHint("Sexo");
        cboContatoSexo.setTable(tbClienteContato);
        cboContatoSexo.setFieldName("COD_SEXO");
        cboContatoSexo.setFlex(true);
        cboContatoSexo.setListOptions("Masculino=M;Feminino=F");
        cboContatoSexo.setHelpCaption("Sexo");
        cboContatoSexo.setReadOnly(true);
        cboContatoSexo.setRequired(false);
        cboContatoSexo.setPrompt("Sexo");
        cboContatoSexo.setConstraintCheckWhen("cwImmediate");
        cboContatoSexo.setConstraintCheckType("ctExpression");
        cboContatoSexo.setConstraintFocusOnError(false);
        cboContatoSexo.setConstraintEnableUI(true);
        cboContatoSexo.setConstraintEnabled(false);
        cboContatoSexo.setConstraintFormCheck(true);
        cboContatoSexo.setClearOnDelKey(true);
        cboContatoSexo.setUseClearButton(false);
        cboContatoSexo.setHideClearButtonOnNullValue(false);
        cboContatoSexo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "cboContatoSexo", "OnEnter");
        });
        vBoxContatoSexo.addChildren(cboContatoSexo);
        cboContatoSexo.applyProperties();
        addValidatable(cboContatoSexo);
    }

    public TFVBox vBoxContatoDataNasc = new TFVBox();

    private void init_vBoxContatoDataNasc() {
        vBoxContatoDataNasc.setName("vBoxContatoDataNasc");
        vBoxContatoDataNasc.setLeft(130);
        vBoxContatoDataNasc.setTop(0);
        vBoxContatoDataNasc.setWidth(160);
        vBoxContatoDataNasc.setHeight(50);
        vBoxContatoDataNasc.setAlign("alLeft");
        vBoxContatoDataNasc.setBorderStyle("stNone");
        vBoxContatoDataNasc.setPaddingTop(0);
        vBoxContatoDataNasc.setPaddingLeft(0);
        vBoxContatoDataNasc.setPaddingRight(0);
        vBoxContatoDataNasc.setPaddingBottom(0);
        vBoxContatoDataNasc.setMarginTop(0);
        vBoxContatoDataNasc.setMarginLeft(0);
        vBoxContatoDataNasc.setMarginRight(5);
        vBoxContatoDataNasc.setMarginBottom(0);
        vBoxContatoDataNasc.setSpacing(0);
        vBoxContatoDataNasc.setFlexVflex("ftMin");
        vBoxContatoDataNasc.setFlexHflex("ftTrue");
        vBoxContatoDataNasc.setScrollable(false);
        vBoxContatoDataNasc.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoDataNasc.setBoxShadowConfigVerticalLength(10);
        vBoxContatoDataNasc.setBoxShadowConfigBlurRadius(5);
        vBoxContatoDataNasc.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoDataNasc.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoDataNasc.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(vBoxContatoDataNasc);
        vBoxContatoDataNasc.applyProperties();
    }

    public TFLabel lblNascimento = new TFLabel();

    private void init_lblNascimento() {
        lblNascimento.setName("lblNascimento");
        lblNascimento.setLeft(0);
        lblNascimento.setTop(0);
        lblNascimento.setWidth(66);
        lblNascimento.setHeight(16);
        lblNascimento.setCaption("Nascimento");
        lblNascimento.setFontColor("clWindowText");
        lblNascimento.setFontSize(-13);
        lblNascimento.setFontName("Tahoma");
        lblNascimento.setFontStyle("[]");
        lblNascimento.setVerticalAlignment("taVerticalCenter");
        lblNascimento.setWordBreak(false);
        vBoxContatoDataNasc.addChildren(lblNascimento);
        lblNascimento.applyProperties();
    }

    public TFDate dtNascimento = new TFDate();

    private void init_dtNascimento() {
        dtNascimento.setName("dtNascimento");
        dtNascimento.setLeft(0);
        dtNascimento.setTop(17);
        dtNascimento.setWidth(150);
        dtNascimento.setHeight(24);
        dtNascimento.setHint("Nascimento");
        dtNascimento.setTable(tbClienteContato);
        dtNascimento.setFieldName("ANIVERSARIO");
        dtNascimento.setHelpCaption("Nascimento");
        dtNascimento.setFlex(false);
        dtNascimento.setRequired(false);
        dtNascimento.setPrompt("Nascimento");
        dtNascimento.setConstraintCheckWhen("cwImmediate");
        dtNascimento.setConstraintCheckType("ctExpression");
        dtNascimento.setConstraintFocusOnError(false);
        dtNascimento.setConstraintEnableUI(true);
        dtNascimento.setConstraintEnabled(false);
        dtNascimento.setConstraintFormCheck(true);
        dtNascimento.setFormat("dd/MM/yyyy");
        dtNascimento.setShowCheckBox(false);
        dtNascimento.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "dtNascimento", "OnEnter");
        });
        vBoxContatoDataNasc.addChildren(dtNascimento);
        dtNascimento.applyProperties();
        addValidatable(dtNascimento);
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(133);
        FHBox2.setWidth(500);
        FHBox2.setHeight(52);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(5);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox vBoxAreaDeContato = new TFVBox();

    private void init_vBoxAreaDeContato() {
        vBoxAreaDeContato.setName("vBoxAreaDeContato");
        vBoxAreaDeContato.setLeft(0);
        vBoxAreaDeContato.setTop(0);
        vBoxAreaDeContato.setWidth(190);
        vBoxAreaDeContato.setHeight(60);
        vBoxAreaDeContato.setAlign("alLeft");
        vBoxAreaDeContato.setBorderStyle("stNone");
        vBoxAreaDeContato.setPaddingTop(0);
        vBoxAreaDeContato.setPaddingLeft(0);
        vBoxAreaDeContato.setPaddingRight(0);
        vBoxAreaDeContato.setPaddingBottom(0);
        vBoxAreaDeContato.setMarginTop(0);
        vBoxAreaDeContato.setMarginLeft(0);
        vBoxAreaDeContato.setMarginRight(0);
        vBoxAreaDeContato.setMarginBottom(0);
        vBoxAreaDeContato.setSpacing(0);
        vBoxAreaDeContato.setFlexVflex("ftMin");
        vBoxAreaDeContato.setFlexHflex("ftTrue");
        vBoxAreaDeContato.setScrollable(false);
        vBoxAreaDeContato.setBoxShadowConfigHorizontalLength(10);
        vBoxAreaDeContato.setBoxShadowConfigVerticalLength(10);
        vBoxAreaDeContato.setBoxShadowConfigBlurRadius(5);
        vBoxAreaDeContato.setBoxShadowConfigSpreadRadius(0);
        vBoxAreaDeContato.setBoxShadowConfigShadowColor("clBlack");
        vBoxAreaDeContato.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(vBoxAreaDeContato);
        vBoxAreaDeContato.applyProperties();
    }

    public TFLabel lblAreaDeContato = new TFLabel();

    private void init_lblAreaDeContato() {
        lblAreaDeContato.setName("lblAreaDeContato");
        lblAreaDeContato.setLeft(0);
        lblAreaDeContato.setTop(0);
        lblAreaDeContato.setWidth(91);
        lblAreaDeContato.setHeight(16);
        lblAreaDeContato.setCaption("\u00C1rea de contato");
        lblAreaDeContato.setFontColor("clWindowText");
        lblAreaDeContato.setFontSize(-13);
        lblAreaDeContato.setFontName("Tahoma");
        lblAreaDeContato.setFontStyle("[]");
        lblAreaDeContato.setVerticalAlignment("taVerticalCenter");
        lblAreaDeContato.setWordBreak(false);
        vBoxAreaDeContato.addChildren(lblAreaDeContato);
        lblAreaDeContato.applyProperties();
    }

    public TFHBox hBoxAreaDeContato = new TFHBox();

    private void init_hBoxAreaDeContato() {
        hBoxAreaDeContato.setName("hBoxAreaDeContato");
        hBoxAreaDeContato.setLeft(0);
        hBoxAreaDeContato.setTop(17);
        hBoxAreaDeContato.setWidth(180);
        hBoxAreaDeContato.setHeight(30);
        hBoxAreaDeContato.setBorderStyle("stNone");
        hBoxAreaDeContato.setPaddingTop(0);
        hBoxAreaDeContato.setPaddingLeft(0);
        hBoxAreaDeContato.setPaddingRight(0);
        hBoxAreaDeContato.setPaddingBottom(0);
        hBoxAreaDeContato.setMarginTop(0);
        hBoxAreaDeContato.setMarginLeft(0);
        hBoxAreaDeContato.setMarginRight(0);
        hBoxAreaDeContato.setMarginBottom(0);
        hBoxAreaDeContato.setSpacing(5);
        hBoxAreaDeContato.setFlexVflex("ftMin");
        hBoxAreaDeContato.setFlexHflex("ftTrue");
        hBoxAreaDeContato.setScrollable(false);
        hBoxAreaDeContato.setBoxShadowConfigHorizontalLength(10);
        hBoxAreaDeContato.setBoxShadowConfigVerticalLength(10);
        hBoxAreaDeContato.setBoxShadowConfigBlurRadius(5);
        hBoxAreaDeContato.setBoxShadowConfigSpreadRadius(0);
        hBoxAreaDeContato.setBoxShadowConfigShadowColor("clBlack");
        hBoxAreaDeContato.setBoxShadowConfigOpacity(75);
        hBoxAreaDeContato.setVAlign("tvTop");
        vBoxAreaDeContato.addChildren(hBoxAreaDeContato);
        hBoxAreaDeContato.applyProperties();
    }

    public TFCombo cboAreaDeContato = new TFCombo();

    private void init_cboAreaDeContato() {
        cboAreaDeContato.setName("cboAreaDeContato");
        cboAreaDeContato.setLeft(0);
        cboAreaDeContato.setTop(0);
        cboAreaDeContato.setWidth(150);
        cboAreaDeContato.setHeight(21);
        cboAreaDeContato.setHint("\u00C1rea de contato");
        cboAreaDeContato.setTable(tbClienteContato);
        cboAreaDeContato.setLookupTable(tbClienteContatoTipo);
        cboAreaDeContato.setFieldName("AREA_CONTATO");
        cboAreaDeContato.setLookupKey("AREA_CONTATO");
        cboAreaDeContato.setLookupDesc("DESCRICAO_CODIGO");
        cboAreaDeContato.setFlex(true);
        cboAreaDeContato.setHelpCaption("\u00C1rea de contato");
        cboAreaDeContato.setReadOnly(true);
        cboAreaDeContato.setRequired(false);
        cboAreaDeContato.setPrompt("\u00C1rea de contato");
        cboAreaDeContato.setConstraintCheckWhen("cwImmediate");
        cboAreaDeContato.setConstraintCheckType("ctExpression");
        cboAreaDeContato.setConstraintFocusOnError(false);
        cboAreaDeContato.setConstraintEnableUI(true);
        cboAreaDeContato.setConstraintEnabled(false);
        cboAreaDeContato.setConstraintFormCheck(true);
        cboAreaDeContato.setClearOnDelKey(true);
        cboAreaDeContato.setUseClearButton(false);
        cboAreaDeContato.setHideClearButtonOnNullValue(false);
        cboAreaDeContato.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "cboAreaDeContato", "OnEnter");
        });
        hBoxAreaDeContato.addChildren(cboAreaDeContato);
        cboAreaDeContato.applyProperties();
        addValidatable(cboAreaDeContato);
    }

    public TFIconClass icoAreaDeContato = new TFIconClass();

    private void init_icoAreaDeContato() {
        icoAreaDeContato.setName("icoAreaDeContato");
        icoAreaDeContato.setLeft(150);
        icoAreaDeContato.setTop(0);
        icoAreaDeContato.setHint("\u00C1rea de contato");
        icoAreaDeContato.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoAreaDeContatoClick(event);
            processarFlow("FrmCadClienteContato", "icoAreaDeContato", "OnClick");
        });
        icoAreaDeContato.setIconClass("plus-square-o");
        icoAreaDeContato.setSize(30);
        icoAreaDeContato.setColor("clBlack");
        hBoxAreaDeContato.addChildren(icoAreaDeContato);
        icoAreaDeContato.applyProperties();
    }

    public TFVBox vBoxContatoFuncao = new TFVBox();

    private void init_vBoxContatoFuncao() {
        vBoxContatoFuncao.setName("vBoxContatoFuncao");
        vBoxContatoFuncao.setLeft(190);
        vBoxContatoFuncao.setTop(0);
        vBoxContatoFuncao.setWidth(164);
        vBoxContatoFuncao.setHeight(50);
        vBoxContatoFuncao.setBorderStyle("stNone");
        vBoxContatoFuncao.setPaddingTop(0);
        vBoxContatoFuncao.setPaddingLeft(0);
        vBoxContatoFuncao.setPaddingRight(0);
        vBoxContatoFuncao.setPaddingBottom(0);
        vBoxContatoFuncao.setMarginTop(0);
        vBoxContatoFuncao.setMarginLeft(0);
        vBoxContatoFuncao.setMarginRight(5);
        vBoxContatoFuncao.setMarginBottom(0);
        vBoxContatoFuncao.setSpacing(1);
        vBoxContatoFuncao.setFlexVflex("ftMin");
        vBoxContatoFuncao.setFlexHflex("ftTrue");
        vBoxContatoFuncao.setScrollable(false);
        vBoxContatoFuncao.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoFuncao.setBoxShadowConfigVerticalLength(10);
        vBoxContatoFuncao.setBoxShadowConfigBlurRadius(5);
        vBoxContatoFuncao.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoFuncao.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoFuncao.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(vBoxContatoFuncao);
        vBoxContatoFuncao.applyProperties();
    }

    public TFLabel lblFuncao = new TFLabel();

    private void init_lblFuncao() {
        lblFuncao.setName("lblFuncao");
        lblFuncao.setLeft(0);
        lblFuncao.setTop(0);
        lblFuncao.setWidth(41);
        lblFuncao.setHeight(16);
        lblFuncao.setCaption("Fun\u00E7\u00E3o");
        lblFuncao.setFontColor("clWindowText");
        lblFuncao.setFontSize(-13);
        lblFuncao.setFontName("Tahoma");
        lblFuncao.setFontStyle("[]");
        lblFuncao.setVerticalAlignment("taVerticalCenter");
        lblFuncao.setWordBreak(false);
        vBoxContatoFuncao.addChildren(lblFuncao);
        lblFuncao.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(17);
        FHBox5.setWidth(158);
        FHBox5.setHeight(31);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(3);
        FHBox5.setFlexVflex("ftMin");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        vBoxContatoFuncao.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFString edtFuncao = new TFString();

    private void init_edtFuncao() {
        edtFuncao.setName("edtFuncao");
        edtFuncao.setLeft(0);
        edtFuncao.setTop(0);
        edtFuncao.setWidth(110);
        edtFuncao.setHeight(24);
        edtFuncao.setHint("Fun\u00E7\u00E3o");
        edtFuncao.setTable(tbClienteContato);
        edtFuncao.setFieldName("FUNCAO");
        edtFuncao.setHelpCaption("Fun\u00E7\u00E3o");
        edtFuncao.setFlex(true);
        edtFuncao.setRequired(false);
        edtFuncao.setPrompt("Fun\u00E7\u00E3o");
        edtFuncao.setConstraintCheckWhen("cwImmediate");
        edtFuncao.setConstraintCheckType("ctExpression");
        edtFuncao.setConstraintFocusOnError(false);
        edtFuncao.setConstraintEnableUI(true);
        edtFuncao.setConstraintEnabled(false);
        edtFuncao.setConstraintFormCheck(true);
        edtFuncao.setCharCase("ccUpper");
        edtFuncao.setPwd(false);
        edtFuncao.setMaxlength(50);
        edtFuncao.setFontColor("clWindowText");
        edtFuncao.setFontSize(-13);
        edtFuncao.setFontName("Tahoma");
        edtFuncao.setFontStyle("[]");
        edtFuncao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtFuncao", "OnEnter");
        });
        edtFuncao.setSaveLiteralCharacter(false);
        edtFuncao.applyProperties();
        FHBox5.addChildren(edtFuncao);
        addValidatable(edtFuncao);
    }

    public TFIconClass iconLimparFuncao = new TFIconClass();

    private void init_iconLimparFuncao() {
        iconLimparFuncao.setName("iconLimparFuncao");
        iconLimparFuncao.setLeft(110);
        iconLimparFuncao.setTop(0);
        iconLimparFuncao.setHint("Limpar Fun\u00E7\u00E3o");
        iconLimparFuncao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconLimparFuncaoClick(event);
            processarFlow("FrmCadClienteContato", "iconLimparFuncao", "OnClick");
        });
        iconLimparFuncao.setIconClass("trash");
        iconLimparFuncao.setSize(25);
        iconLimparFuncao.setColor("clBlack");
        FHBox5.addChildren(iconLimparFuncao);
        iconLimparFuncao.applyProperties();
    }

    public TFVBox FVBox19 = new TFVBox();

    private void init_FVBox19() {
        FVBox19.setName("FVBox19");
        FVBox19.setLeft(0);
        FVBox19.setTop(186);
        FVBox19.setWidth(500);
        FVBox19.setHeight(25);
        FVBox19.setBorderStyle("stNone");
        FVBox19.setColor("15724527");
        FVBox19.setPaddingTop(3);
        FVBox19.setPaddingLeft(7);
        FVBox19.setPaddingRight(0);
        FVBox19.setPaddingBottom(0);
        FVBox19.setMarginTop(10);
        FVBox19.setMarginLeft(0);
        FVBox19.setMarginRight(0);
        FVBox19.setMarginBottom(0);
        FVBox19.setSpacing(0);
        FVBox19.setFlexVflex("ftFalse");
        FVBox19.setFlexHflex("ftTrue");
        FVBox19.setScrollable(false);
        FVBox19.setBoxShadowConfigHorizontalLength(10);
        FVBox19.setBoxShadowConfigVerticalLength(10);
        FVBox19.setBoxShadowConfigBlurRadius(5);
        FVBox19.setBoxShadowConfigSpreadRadius(0);
        FVBox19.setBoxShadowConfigShadowColor("clBlack");
        FVBox19.setBoxShadowConfigOpacity(75);
        vBoxTbsCadastro.addChildren(FVBox19);
        FVBox19.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(0);
        FLabel8.setTop(0);
        FLabel8.setWidth(88);
        FLabel8.setHeight(19);
        FLabel8.setCaption("Documentos");
        FLabel8.setFontColor("13392431");
        FLabel8.setFontSize(-16);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FVBox19.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(212);
        FHBox3.setWidth(500);
        FHBox3.setHeight(57);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(5);
        FHBox3.setFlexVflex("ftMin");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox vBoxContatoCpf = new TFVBox();

    private void init_vBoxContatoCpf() {
        vBoxContatoCpf.setName("vBoxContatoCpf");
        vBoxContatoCpf.setLeft(0);
        vBoxContatoCpf.setTop(0);
        vBoxContatoCpf.setWidth(120);
        vBoxContatoCpf.setHeight(50);
        vBoxContatoCpf.setBorderStyle("stNone");
        vBoxContatoCpf.setPaddingTop(0);
        vBoxContatoCpf.setPaddingLeft(0);
        vBoxContatoCpf.setPaddingRight(0);
        vBoxContatoCpf.setPaddingBottom(0);
        vBoxContatoCpf.setMarginTop(0);
        vBoxContatoCpf.setMarginLeft(0);
        vBoxContatoCpf.setMarginRight(5);
        vBoxContatoCpf.setMarginBottom(0);
        vBoxContatoCpf.setSpacing(1);
        vBoxContatoCpf.setFlexVflex("ftMin");
        vBoxContatoCpf.setFlexHflex("ftMin");
        vBoxContatoCpf.setScrollable(false);
        vBoxContatoCpf.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoCpf.setBoxShadowConfigVerticalLength(10);
        vBoxContatoCpf.setBoxShadowConfigBlurRadius(5);
        vBoxContatoCpf.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoCpf.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoCpf.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(vBoxContatoCpf);
        vBoxContatoCpf.applyProperties();
    }

    public TFLabel lblCPF = new TFLabel();

    private void init_lblCPF() {
        lblCPF.setName("lblCPF");
        lblCPF.setLeft(0);
        lblCPF.setTop(0);
        lblCPF.setWidth(22);
        lblCPF.setHeight(16);
        lblCPF.setCaption("CPF");
        lblCPF.setFontColor("clWindowText");
        lblCPF.setFontSize(-13);
        lblCPF.setFontName("Tahoma");
        lblCPF.setFontStyle("[]");
        lblCPF.setVerticalAlignment("taVerticalCenter");
        lblCPF.setWordBreak(false);
        vBoxContatoCpf.addChildren(lblCPF);
        lblCPF.applyProperties();
    }

    public TFString edtCPF = new TFString();

    private void init_edtCPF() {
        edtCPF.setName("edtCPF");
        edtCPF.setLeft(0);
        edtCPF.setTop(17);
        edtCPF.setWidth(110);
        edtCPF.setHeight(24);
        edtCPF.setHint("CPF");
        edtCPF.setTable(tbClienteContato);
        edtCPF.setFieldName("CPF");
        edtCPF.setHelpCaption("CPF");
        edtCPF.setFlex(false);
        edtCPF.setRequired(false);
        edtCPF.setPrompt("CPF");
        edtCPF.setConstraintCheckWhen("cwImmediate");
        edtCPF.setConstraintCheckType("ctExpression");
        edtCPF.setConstraintFocusOnError(false);
        edtCPF.setConstraintEnableUI(true);
        edtCPF.setConstraintEnabled(false);
        edtCPF.setConstraintFormCheck(true);
        edtCPF.setCharCase("ccUpper");
        edtCPF.setPwd(false);
        edtCPF.setMask("999.999.999-99");
        edtCPF.setMaxlength(0);
        edtCPF.setFontColor("clWindowText");
        edtCPF.setFontSize(-13);
        edtCPF.setFontName("Tahoma");
        edtCPF.setFontStyle("[]");
        edtCPF.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCPFChange(event);
            processarFlow("FrmCadClienteContato", "edtCPF", "OnChange");
        });
        edtCPF.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtCPF", "OnEnter");
        });
        edtCPF.setSaveLiteralCharacter(false);
        edtCPF.applyProperties();
        vBoxContatoCpf.addChildren(edtCPF);
        addValidatable(edtCPF);
    }

    public TFVBox vBoxContatoRg = new TFVBox();

    private void init_vBoxContatoRg() {
        vBoxContatoRg.setName("vBoxContatoRg");
        vBoxContatoRg.setLeft(120);
        vBoxContatoRg.setTop(0);
        vBoxContatoRg.setWidth(120);
        vBoxContatoRg.setHeight(50);
        vBoxContatoRg.setBorderStyle("stNone");
        vBoxContatoRg.setPaddingTop(0);
        vBoxContatoRg.setPaddingLeft(0);
        vBoxContatoRg.setPaddingRight(0);
        vBoxContatoRg.setPaddingBottom(0);
        vBoxContatoRg.setMarginTop(0);
        vBoxContatoRg.setMarginLeft(0);
        vBoxContatoRg.setMarginRight(5);
        vBoxContatoRg.setMarginBottom(0);
        vBoxContatoRg.setSpacing(1);
        vBoxContatoRg.setFlexVflex("ftMin");
        vBoxContatoRg.setFlexHflex("ftTrue");
        vBoxContatoRg.setScrollable(false);
        vBoxContatoRg.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoRg.setBoxShadowConfigVerticalLength(10);
        vBoxContatoRg.setBoxShadowConfigBlurRadius(5);
        vBoxContatoRg.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoRg.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoRg.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(vBoxContatoRg);
        vBoxContatoRg.applyProperties();
    }

    public TFLabel lblRG = new TFLabel();

    private void init_lblRG() {
        lblRG.setName("lblRG");
        lblRG.setLeft(0);
        lblRG.setTop(0);
        lblRG.setWidth(20);
        lblRG.setHeight(16);
        lblRG.setCaption("RG ");
        lblRG.setFontColor("clWindowText");
        lblRG.setFontSize(-13);
        lblRG.setFontName("Tahoma");
        lblRG.setFontStyle("[]");
        lblRG.setVerticalAlignment("taVerticalCenter");
        lblRG.setWordBreak(false);
        vBoxContatoRg.addChildren(lblRG);
        lblRG.applyProperties();
    }

    public TFString edtRG = new TFString();

    private void init_edtRG() {
        edtRG.setName("edtRG");
        edtRG.setLeft(0);
        edtRG.setTop(17);
        edtRG.setWidth(110);
        edtRG.setHeight(24);
        edtRG.setHint("RG");
        edtRG.setTable(tbClienteContato);
        edtRG.setFieldName("RG");
        edtRG.setHelpCaption("RG");
        edtRG.setFlex(true);
        edtRG.setRequired(false);
        edtRG.setPrompt("RG");
        edtRG.setConstraintCheckWhen("cwImmediate");
        edtRG.setConstraintCheckType("ctExpression");
        edtRG.setConstraintFocusOnError(false);
        edtRG.setConstraintEnableUI(true);
        edtRG.setConstraintEnabled(false);
        edtRG.setConstraintFormCheck(true);
        edtRG.setCharCase("ccUpper");
        edtRG.setPwd(false);
        edtRG.setMaxlength(20);
        edtRG.setFontColor("clWindowText");
        edtRG.setFontSize(-13);
        edtRG.setFontName("Tahoma");
        edtRG.setFontStyle("[]");
        edtRG.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtRG", "OnEnter");
        });
        edtRG.setSaveLiteralCharacter(false);
        edtRG.applyProperties();
        vBoxContatoRg.addChildren(edtRG);
        addValidatable(edtRG);
    }

    public TFVBox vBoxContatoOrgaoEmissor = new TFVBox();

    private void init_vBoxContatoOrgaoEmissor() {
        vBoxContatoOrgaoEmissor.setName("vBoxContatoOrgaoEmissor");
        vBoxContatoOrgaoEmissor.setLeft(240);
        vBoxContatoOrgaoEmissor.setTop(0);
        vBoxContatoOrgaoEmissor.setWidth(90);
        vBoxContatoOrgaoEmissor.setHeight(50);
        vBoxContatoOrgaoEmissor.setAlign("alLeft");
        vBoxContatoOrgaoEmissor.setBorderStyle("stNone");
        vBoxContatoOrgaoEmissor.setPaddingTop(0);
        vBoxContatoOrgaoEmissor.setPaddingLeft(0);
        vBoxContatoOrgaoEmissor.setPaddingRight(0);
        vBoxContatoOrgaoEmissor.setPaddingBottom(0);
        vBoxContatoOrgaoEmissor.setMarginTop(0);
        vBoxContatoOrgaoEmissor.setMarginLeft(0);
        vBoxContatoOrgaoEmissor.setMarginRight(5);
        vBoxContatoOrgaoEmissor.setMarginBottom(0);
        vBoxContatoOrgaoEmissor.setSpacing(0);
        vBoxContatoOrgaoEmissor.setFlexVflex("ftMin");
        vBoxContatoOrgaoEmissor.setFlexHflex("ftMin");
        vBoxContatoOrgaoEmissor.setScrollable(false);
        vBoxContatoOrgaoEmissor.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoOrgaoEmissor.setBoxShadowConfigVerticalLength(10);
        vBoxContatoOrgaoEmissor.setBoxShadowConfigBlurRadius(5);
        vBoxContatoOrgaoEmissor.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoOrgaoEmissor.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoOrgaoEmissor.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(vBoxContatoOrgaoEmissor);
        vBoxContatoOrgaoEmissor.applyProperties();
    }

    public TFLabel lblOrgaoEmissor = new TFLabel();

    private void init_lblOrgaoEmissor() {
        lblOrgaoEmissor.setName("lblOrgaoEmissor");
        lblOrgaoEmissor.setLeft(0);
        lblOrgaoEmissor.setTop(0);
        lblOrgaoEmissor.setWidth(88);
        lblOrgaoEmissor.setHeight(16);
        lblOrgaoEmissor.setCaption("Org\u00E3o emissor ");
        lblOrgaoEmissor.setFontColor("clWindowText");
        lblOrgaoEmissor.setFontSize(-13);
        lblOrgaoEmissor.setFontName("Tahoma");
        lblOrgaoEmissor.setFontStyle("[]");
        lblOrgaoEmissor.setVerticalAlignment("taVerticalCenter");
        lblOrgaoEmissor.setWordBreak(false);
        vBoxContatoOrgaoEmissor.addChildren(lblOrgaoEmissor);
        lblOrgaoEmissor.applyProperties();
    }

    public TFString edtOrgaoEmissor = new TFString();

    private void init_edtOrgaoEmissor() {
        edtOrgaoEmissor.setName("edtOrgaoEmissor");
        edtOrgaoEmissor.setLeft(0);
        edtOrgaoEmissor.setTop(17);
        edtOrgaoEmissor.setWidth(80);
        edtOrgaoEmissor.setHeight(24);
        edtOrgaoEmissor.setHint("\u00D3rg\u00E3o Emissor");
        edtOrgaoEmissor.setTable(tbClienteContato);
        edtOrgaoEmissor.setFieldName("ORGAO_EMISSOR");
        edtOrgaoEmissor.setHelpCaption("\u00D3rg\u00E3o Emissor");
        edtOrgaoEmissor.setFlex(false);
        edtOrgaoEmissor.setRequired(false);
        edtOrgaoEmissor.setPrompt("\u00D3rg\u00E3o Emissor");
        edtOrgaoEmissor.setConstraintCheckWhen("cwImmediate");
        edtOrgaoEmissor.setConstraintCheckType("ctExpression");
        edtOrgaoEmissor.setConstraintFocusOnError(false);
        edtOrgaoEmissor.setConstraintEnableUI(true);
        edtOrgaoEmissor.setConstraintEnabled(false);
        edtOrgaoEmissor.setConstraintFormCheck(true);
        edtOrgaoEmissor.setCharCase("ccUpper");
        edtOrgaoEmissor.setPwd(false);
        edtOrgaoEmissor.setMaxlength(6);
        edtOrgaoEmissor.setFontColor("clWindowText");
        edtOrgaoEmissor.setFontSize(-13);
        edtOrgaoEmissor.setFontName("Tahoma");
        edtOrgaoEmissor.setFontStyle("[]");
        edtOrgaoEmissor.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtOrgaoEmissor", "OnEnter");
        });
        edtOrgaoEmissor.setSaveLiteralCharacter(false);
        edtOrgaoEmissor.applyProperties();
        vBoxContatoOrgaoEmissor.addChildren(edtOrgaoEmissor);
        addValidatable(edtOrgaoEmissor);
    }

    public TFVBox vBoxContatoDataDeEmissao = new TFVBox();

    private void init_vBoxContatoDataDeEmissao() {
        vBoxContatoDataDeEmissao.setName("vBoxContatoDataDeEmissao");
        vBoxContatoDataDeEmissao.setLeft(330);
        vBoxContatoDataDeEmissao.setTop(0);
        vBoxContatoDataDeEmissao.setWidth(160);
        vBoxContatoDataDeEmissao.setHeight(50);
        vBoxContatoDataDeEmissao.setAlign("alLeft");
        vBoxContatoDataDeEmissao.setBorderStyle("stNone");
        vBoxContatoDataDeEmissao.setPaddingTop(0);
        vBoxContatoDataDeEmissao.setPaddingLeft(0);
        vBoxContatoDataDeEmissao.setPaddingRight(0);
        vBoxContatoDataDeEmissao.setPaddingBottom(0);
        vBoxContatoDataDeEmissao.setMarginTop(0);
        vBoxContatoDataDeEmissao.setMarginLeft(0);
        vBoxContatoDataDeEmissao.setMarginRight(5);
        vBoxContatoDataDeEmissao.setMarginBottom(0);
        vBoxContatoDataDeEmissao.setSpacing(0);
        vBoxContatoDataDeEmissao.setFlexVflex("ftMin");
        vBoxContatoDataDeEmissao.setFlexHflex("ftMin");
        vBoxContatoDataDeEmissao.setScrollable(false);
        vBoxContatoDataDeEmissao.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoDataDeEmissao.setBoxShadowConfigVerticalLength(10);
        vBoxContatoDataDeEmissao.setBoxShadowConfigBlurRadius(5);
        vBoxContatoDataDeEmissao.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoDataDeEmissao.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoDataDeEmissao.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(vBoxContatoDataDeEmissao);
        vBoxContatoDataDeEmissao.applyProperties();
    }

    public TFLabel lblDataDeEmissao = new TFLabel();

    private void init_lblDataDeEmissao() {
        lblDataDeEmissao.setName("lblDataDeEmissao");
        lblDataDeEmissao.setLeft(0);
        lblDataDeEmissao.setTop(0);
        lblDataDeEmissao.setWidth(99);
        lblDataDeEmissao.setHeight(16);
        lblDataDeEmissao.setCaption("Data de emiss\u00E3o ");
        lblDataDeEmissao.setFontColor("clWindowText");
        lblDataDeEmissao.setFontSize(-13);
        lblDataDeEmissao.setFontName("Tahoma");
        lblDataDeEmissao.setFontStyle("[]");
        lblDataDeEmissao.setVerticalAlignment("taVerticalCenter");
        lblDataDeEmissao.setWordBreak(false);
        vBoxContatoDataDeEmissao.addChildren(lblDataDeEmissao);
        lblDataDeEmissao.applyProperties();
    }

    public TFDate dtEmissao = new TFDate();

    private void init_dtEmissao() {
        dtEmissao.setName("dtEmissao");
        dtEmissao.setLeft(0);
        dtEmissao.setTop(17);
        dtEmissao.setWidth(150);
        dtEmissao.setHeight(24);
        dtEmissao.setHint("Data de emiss\u00E3o");
        dtEmissao.setTable(tbClienteContato);
        dtEmissao.setFieldName("DATA_EMISSAO");
        dtEmissao.setHelpCaption("Data de emiss\u00E3o");
        dtEmissao.setFlex(false);
        dtEmissao.setRequired(false);
        dtEmissao.setPrompt("Data de emiss\u00E3o");
        dtEmissao.setConstraintCheckWhen("cwImmediate");
        dtEmissao.setConstraintCheckType("ctExpression");
        dtEmissao.setConstraintFocusOnError(false);
        dtEmissao.setConstraintEnableUI(true);
        dtEmissao.setConstraintEnabled(false);
        dtEmissao.setConstraintFormCheck(true);
        dtEmissao.setFormat("dd/MM/yyyy");
        dtEmissao.setShowCheckBox(false);
        dtEmissao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "dtEmissao", "OnEnter");
        });
        vBoxContatoDataDeEmissao.addChildren(dtEmissao);
        dtEmissao.applyProperties();
        addValidatable(dtEmissao);
    }

    public TFHBox hBoxTbsCadastroLinha02 = new TFHBox();

    private void init_hBoxTbsCadastroLinha02() {
        hBoxTbsCadastroLinha02.setName("hBoxTbsCadastroLinha02");
        hBoxTbsCadastroLinha02.setLeft(0);
        hBoxTbsCadastroLinha02.setTop(270);
        hBoxTbsCadastroLinha02.setWidth(500);
        hBoxTbsCadastroLinha02.setHeight(60);
        hBoxTbsCadastroLinha02.setBorderStyle("stNone");
        hBoxTbsCadastroLinha02.setPaddingTop(0);
        hBoxTbsCadastroLinha02.setPaddingLeft(0);
        hBoxTbsCadastroLinha02.setPaddingRight(0);
        hBoxTbsCadastroLinha02.setPaddingBottom(0);
        hBoxTbsCadastroLinha02.setMarginTop(0);
        hBoxTbsCadastroLinha02.setMarginLeft(5);
        hBoxTbsCadastroLinha02.setMarginRight(5);
        hBoxTbsCadastroLinha02.setMarginBottom(0);
        hBoxTbsCadastroLinha02.setSpacing(5);
        hBoxTbsCadastroLinha02.setFlexVflex("ftMin");
        hBoxTbsCadastroLinha02.setFlexHflex("ftTrue");
        hBoxTbsCadastroLinha02.setScrollable(false);
        hBoxTbsCadastroLinha02.setBoxShadowConfigHorizontalLength(10);
        hBoxTbsCadastroLinha02.setBoxShadowConfigVerticalLength(10);
        hBoxTbsCadastroLinha02.setBoxShadowConfigBlurRadius(5);
        hBoxTbsCadastroLinha02.setBoxShadowConfigSpreadRadius(0);
        hBoxTbsCadastroLinha02.setBoxShadowConfigShadowColor("clBlack");
        hBoxTbsCadastroLinha02.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha02.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(hBoxTbsCadastroLinha02);
        hBoxTbsCadastroLinha02.applyProperties();
    }

    public TFVBox vBoxContatoCnh = new TFVBox();

    private void init_vBoxContatoCnh() {
        vBoxContatoCnh.setName("vBoxContatoCnh");
        vBoxContatoCnh.setLeft(0);
        vBoxContatoCnh.setTop(0);
        vBoxContatoCnh.setWidth(120);
        vBoxContatoCnh.setHeight(50);
        vBoxContatoCnh.setAlign("alLeft");
        vBoxContatoCnh.setBorderStyle("stNone");
        vBoxContatoCnh.setPaddingTop(0);
        vBoxContatoCnh.setPaddingLeft(0);
        vBoxContatoCnh.setPaddingRight(0);
        vBoxContatoCnh.setPaddingBottom(0);
        vBoxContatoCnh.setMarginTop(0);
        vBoxContatoCnh.setMarginLeft(0);
        vBoxContatoCnh.setMarginRight(5);
        vBoxContatoCnh.setMarginBottom(0);
        vBoxContatoCnh.setSpacing(0);
        vBoxContatoCnh.setFlexVflex("ftMin");
        vBoxContatoCnh.setFlexHflex("ftTrue");
        vBoxContatoCnh.setScrollable(false);
        vBoxContatoCnh.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoCnh.setBoxShadowConfigVerticalLength(10);
        vBoxContatoCnh.setBoxShadowConfigBlurRadius(5);
        vBoxContatoCnh.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoCnh.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoCnh.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha02.addChildren(vBoxContatoCnh);
        vBoxContatoCnh.applyProperties();
    }

    public TFLabel lblCNH = new TFLabel();

    private void init_lblCNH() {
        lblCNH.setName("lblCNH");
        lblCNH.setLeft(0);
        lblCNH.setTop(0);
        lblCNH.setWidth(24);
        lblCNH.setHeight(16);
        lblCNH.setCaption("CNH");
        lblCNH.setFontColor("clWindowText");
        lblCNH.setFontSize(-13);
        lblCNH.setFontName("Tahoma");
        lblCNH.setFontStyle("[]");
        lblCNH.setVerticalAlignment("taVerticalCenter");
        lblCNH.setWordBreak(false);
        vBoxContatoCnh.addChildren(lblCNH);
        lblCNH.applyProperties();
    }

    public TFString edtCNH = new TFString();

    private void init_edtCNH() {
        edtCNH.setName("edtCNH");
        edtCNH.setLeft(0);
        edtCNH.setTop(17);
        edtCNH.setWidth(110);
        edtCNH.setHeight(24);
        edtCNH.setHint("CNH");
        edtCNH.setTable(tbClienteContato);
        edtCNH.setFieldName("CNH");
        edtCNH.setHelpCaption("CNH");
        edtCNH.setFlex(true);
        edtCNH.setRequired(false);
        edtCNH.setPrompt("CNH");
        edtCNH.setConstraintCheckWhen("cwImmediate");
        edtCNH.setConstraintCheckType("ctExpression");
        edtCNH.setConstraintFocusOnError(false);
        edtCNH.setConstraintEnableUI(true);
        edtCNH.setConstraintEnabled(false);
        edtCNH.setConstraintFormCheck(true);
        edtCNH.setCharCase("ccUpper");
        edtCNH.setPwd(false);
        edtCNH.setMaxlength(11);
        edtCNH.setFontColor("clWindowText");
        edtCNH.setFontSize(-13);
        edtCNH.setFontName("Tahoma");
        edtCNH.setFontStyle("[]");
        edtCNH.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtCNH", "OnEnter");
        });
        edtCNH.setSaveLiteralCharacter(false);
        edtCNH.applyProperties();
        vBoxContatoCnh.addChildren(edtCNH);
        addValidatable(edtCNH);
    }

    public TFVBox vBoxContatoPassaporteCarteira = new TFVBox();

    private void init_vBoxContatoPassaporteCarteira() {
        vBoxContatoPassaporteCarteira.setName("vBoxContatoPassaporteCarteira");
        vBoxContatoPassaporteCarteira.setLeft(120);
        vBoxContatoPassaporteCarteira.setTop(0);
        vBoxContatoPassaporteCarteira.setWidth(130);
        vBoxContatoPassaporteCarteira.setHeight(50);
        vBoxContatoPassaporteCarteira.setAlign("alLeft");
        vBoxContatoPassaporteCarteira.setBorderStyle("stNone");
        vBoxContatoPassaporteCarteira.setPaddingTop(0);
        vBoxContatoPassaporteCarteira.setPaddingLeft(0);
        vBoxContatoPassaporteCarteira.setPaddingRight(0);
        vBoxContatoPassaporteCarteira.setPaddingBottom(0);
        vBoxContatoPassaporteCarteira.setMarginTop(0);
        vBoxContatoPassaporteCarteira.setMarginLeft(0);
        vBoxContatoPassaporteCarteira.setMarginRight(5);
        vBoxContatoPassaporteCarteira.setMarginBottom(0);
        vBoxContatoPassaporteCarteira.setSpacing(0);
        vBoxContatoPassaporteCarteira.setFlexVflex("ftMin");
        vBoxContatoPassaporteCarteira.setFlexHflex("ftTrue");
        vBoxContatoPassaporteCarteira.setScrollable(false);
        vBoxContatoPassaporteCarteira.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoPassaporteCarteira.setBoxShadowConfigVerticalLength(10);
        vBoxContatoPassaporteCarteira.setBoxShadowConfigBlurRadius(5);
        vBoxContatoPassaporteCarteira.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoPassaporteCarteira.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoPassaporteCarteira.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha02.addChildren(vBoxContatoPassaporteCarteira);
        vBoxContatoPassaporteCarteira.applyProperties();
    }

    public TFLabel lblPassaporteCarteira = new TFLabel();

    private void init_lblPassaporteCarteira() {
        lblPassaporteCarteira.setName("lblPassaporteCarteira");
        lblPassaporteCarteira.setLeft(0);
        lblPassaporteCarteira.setTop(0);
        lblPassaporteCarteira.setWidth(126);
        lblPassaporteCarteira.setHeight(16);
        lblPassaporteCarteira.setCaption("Passaporte / Carteira ");
        lblPassaporteCarteira.setFontColor("clWindowText");
        lblPassaporteCarteira.setFontSize(-13);
        lblPassaporteCarteira.setFontName("Tahoma");
        lblPassaporteCarteira.setFontStyle("[]");
        lblPassaporteCarteira.setVerticalAlignment("taVerticalCenter");
        lblPassaporteCarteira.setWordBreak(false);
        vBoxContatoPassaporteCarteira.addChildren(lblPassaporteCarteira);
        lblPassaporteCarteira.applyProperties();
    }

    public TFString edtPassaporteCarteira = new TFString();

    private void init_edtPassaporteCarteira() {
        edtPassaporteCarteira.setName("edtPassaporteCarteira");
        edtPassaporteCarteira.setLeft(0);
        edtPassaporteCarteira.setTop(17);
        edtPassaporteCarteira.setWidth(120);
        edtPassaporteCarteira.setHeight(24);
        edtPassaporteCarteira.setHint("Passaporte / Carteira");
        edtPassaporteCarteira.setTable(tbClienteContato);
        edtPassaporteCarteira.setFieldName("PASSAPORTE_CARTEIRACIVIL");
        edtPassaporteCarteira.setHelpCaption("Passaporte / Carteira");
        edtPassaporteCarteira.setFlex(true);
        edtPassaporteCarteira.setRequired(false);
        edtPassaporteCarteira.setPrompt("Passaporte / Carteira");
        edtPassaporteCarteira.setConstraintCheckWhen("cwImmediate");
        edtPassaporteCarteira.setConstraintCheckType("ctExpression");
        edtPassaporteCarteira.setConstraintFocusOnError(false);
        edtPassaporteCarteira.setConstraintEnableUI(true);
        edtPassaporteCarteira.setConstraintEnabled(false);
        edtPassaporteCarteira.setConstraintFormCheck(true);
        edtPassaporteCarteira.setCharCase("ccUpper");
        edtPassaporteCarteira.setPwd(false);
        edtPassaporteCarteira.setMaxlength(20);
        edtPassaporteCarteira.setFontColor("clWindowText");
        edtPassaporteCarteira.setFontSize(-13);
        edtPassaporteCarteira.setFontName("Tahoma");
        edtPassaporteCarteira.setFontStyle("[]");
        edtPassaporteCarteira.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtPassaporteCarteira", "OnEnter");
        });
        edtPassaporteCarteira.setSaveLiteralCharacter(false);
        edtPassaporteCarteira.applyProperties();
        vBoxContatoPassaporteCarteira.addChildren(edtPassaporteCarteira);
        addValidatable(edtPassaporteCarteira);
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(331);
        FVBox1.setWidth(500);
        FVBox1.setHeight(25);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setColor("15724527");
        FVBox1.setPaddingTop(3);
        FVBox1.setPaddingLeft(7);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(10);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(0);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        vBoxTbsCadastro.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(55);
        FLabel1.setHeight(19);
        FLabel1.setCaption("Contato");
        FLabel1.setFontColor("13392431");
        FLabel1.setFontSize(-16);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFHBox hBoxTbsCadastroLinha03 = new TFHBox();

    private void init_hBoxTbsCadastroLinha03() {
        hBoxTbsCadastroLinha03.setName("hBoxTbsCadastroLinha03");
        hBoxTbsCadastroLinha03.setLeft(0);
        hBoxTbsCadastroLinha03.setTop(357);
        hBoxTbsCadastroLinha03.setWidth(500);
        hBoxTbsCadastroLinha03.setHeight(60);
        hBoxTbsCadastroLinha03.setBorderStyle("stNone");
        hBoxTbsCadastroLinha03.setPaddingTop(0);
        hBoxTbsCadastroLinha03.setPaddingLeft(0);
        hBoxTbsCadastroLinha03.setPaddingRight(0);
        hBoxTbsCadastroLinha03.setPaddingBottom(0);
        hBoxTbsCadastroLinha03.setMarginTop(0);
        hBoxTbsCadastroLinha03.setMarginLeft(5);
        hBoxTbsCadastroLinha03.setMarginRight(5);
        hBoxTbsCadastroLinha03.setMarginBottom(0);
        hBoxTbsCadastroLinha03.setSpacing(5);
        hBoxTbsCadastroLinha03.setFlexVflex("ftMin");
        hBoxTbsCadastroLinha03.setFlexHflex("ftTrue");
        hBoxTbsCadastroLinha03.setScrollable(false);
        hBoxTbsCadastroLinha03.setBoxShadowConfigHorizontalLength(10);
        hBoxTbsCadastroLinha03.setBoxShadowConfigVerticalLength(10);
        hBoxTbsCadastroLinha03.setBoxShadowConfigBlurRadius(5);
        hBoxTbsCadastroLinha03.setBoxShadowConfigSpreadRadius(0);
        hBoxTbsCadastroLinha03.setBoxShadowConfigShadowColor("clBlack");
        hBoxTbsCadastroLinha03.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha03.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(hBoxTbsCadastroLinha03);
        hBoxTbsCadastroLinha03.applyProperties();
    }

    public TFVBox vBoxContatoEmail = new TFVBox();

    private void init_vBoxContatoEmail() {
        vBoxContatoEmail.setName("vBoxContatoEmail");
        vBoxContatoEmail.setLeft(0);
        vBoxContatoEmail.setTop(0);
        vBoxContatoEmail.setWidth(120);
        vBoxContatoEmail.setHeight(50);
        vBoxContatoEmail.setBorderStyle("stNone");
        vBoxContatoEmail.setPaddingTop(0);
        vBoxContatoEmail.setPaddingLeft(0);
        vBoxContatoEmail.setPaddingRight(0);
        vBoxContatoEmail.setPaddingBottom(0);
        vBoxContatoEmail.setMarginTop(0);
        vBoxContatoEmail.setMarginLeft(0);
        vBoxContatoEmail.setMarginRight(5);
        vBoxContatoEmail.setMarginBottom(0);
        vBoxContatoEmail.setSpacing(1);
        vBoxContatoEmail.setFlexVflex("ftMin");
        vBoxContatoEmail.setFlexHflex("ftTrue");
        vBoxContatoEmail.setScrollable(false);
        vBoxContatoEmail.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoEmail.setBoxShadowConfigVerticalLength(10);
        vBoxContatoEmail.setBoxShadowConfigBlurRadius(5);
        vBoxContatoEmail.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoEmail.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoEmail.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha03.addChildren(vBoxContatoEmail);
        vBoxContatoEmail.applyProperties();
    }

    public TFLabel lblEmail = new TFLabel();

    private void init_lblEmail() {
        lblEmail.setName("lblEmail");
        lblEmail.setLeft(0);
        lblEmail.setTop(0);
        lblEmail.setWidth(40);
        lblEmail.setHeight(16);
        lblEmail.setCaption("E-mail ");
        lblEmail.setFontColor("clWindowText");
        lblEmail.setFontSize(-13);
        lblEmail.setFontName("Tahoma");
        lblEmail.setFontStyle("[]");
        lblEmail.setVerticalAlignment("taVerticalCenter");
        lblEmail.setWordBreak(false);
        vBoxContatoEmail.addChildren(lblEmail);
        lblEmail.applyProperties();
    }

    public TFString edtEmail = new TFString();

    private void init_edtEmail() {
        edtEmail.setName("edtEmail");
        edtEmail.setLeft(0);
        edtEmail.setTop(17);
        edtEmail.setWidth(110);
        edtEmail.setHeight(24);
        edtEmail.setHint("E-mail");
        edtEmail.setTable(tbClienteContato);
        edtEmail.setFieldName("EMAIL");
        edtEmail.setHelpCaption("E-mail");
        edtEmail.setFlex(true);
        edtEmail.setRequired(false);
        edtEmail.setPrompt("E-mail");
        edtEmail.setConstraintCheckWhen("cwImmediate");
        edtEmail.setConstraintCheckType("ctExpression");
        edtEmail.setConstraintFocusOnError(false);
        edtEmail.setConstraintEnableUI(true);
        edtEmail.setConstraintEnabled(false);
        edtEmail.setConstraintFormCheck(true);
        edtEmail.setCharCase("ccLower");
        edtEmail.setPwd(false);
        edtEmail.setMaxlength(60);
        edtEmail.setFontColor("clWindowText");
        edtEmail.setFontSize(-13);
        edtEmail.setFontName("Tahoma");
        edtEmail.setFontStyle("[]");
        edtEmail.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtEmail", "OnEnter");
        });
        edtEmail.setSaveLiteralCharacter(false);
        edtEmail.applyProperties();
        vBoxContatoEmail.addChildren(edtEmail);
        addValidatable(edtEmail);
    }

    public TFVBox vBoxContatoWhatsapp = new TFVBox();

    private void init_vBoxContatoWhatsapp() {
        vBoxContatoWhatsapp.setName("vBoxContatoWhatsapp");
        vBoxContatoWhatsapp.setLeft(120);
        vBoxContatoWhatsapp.setTop(0);
        vBoxContatoWhatsapp.setWidth(200);
        vBoxContatoWhatsapp.setHeight(70);
        vBoxContatoWhatsapp.setAlign("alLeft");
        vBoxContatoWhatsapp.setBorderStyle("stNone");
        vBoxContatoWhatsapp.setPaddingTop(0);
        vBoxContatoWhatsapp.setPaddingLeft(0);
        vBoxContatoWhatsapp.setPaddingRight(0);
        vBoxContatoWhatsapp.setPaddingBottom(0);
        vBoxContatoWhatsapp.setMarginTop(0);
        vBoxContatoWhatsapp.setMarginLeft(0);
        vBoxContatoWhatsapp.setMarginRight(0);
        vBoxContatoWhatsapp.setMarginBottom(0);
        vBoxContatoWhatsapp.setSpacing(0);
        vBoxContatoWhatsapp.setFlexVflex("ftMin");
        vBoxContatoWhatsapp.setFlexHflex("ftTrue");
        vBoxContatoWhatsapp.setScrollable(false);
        vBoxContatoWhatsapp.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoWhatsapp.setBoxShadowConfigVerticalLength(10);
        vBoxContatoWhatsapp.setBoxShadowConfigBlurRadius(5);
        vBoxContatoWhatsapp.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoWhatsapp.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoWhatsapp.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha03.addChildren(vBoxContatoWhatsapp);
        vBoxContatoWhatsapp.applyProperties();
    }

    public TFHBox hBoxContatoWhatsappLbl = new TFHBox();

    private void init_hBoxContatoWhatsappLbl() {
        hBoxContatoWhatsappLbl.setName("hBoxContatoWhatsappLbl");
        hBoxContatoWhatsappLbl.setLeft(0);
        hBoxContatoWhatsappLbl.setTop(0);
        hBoxContatoWhatsappLbl.setWidth(190);
        hBoxContatoWhatsappLbl.setHeight(20);
        hBoxContatoWhatsappLbl.setBorderStyle("stNone");
        hBoxContatoWhatsappLbl.setPaddingTop(0);
        hBoxContatoWhatsappLbl.setPaddingLeft(0);
        hBoxContatoWhatsappLbl.setPaddingRight(0);
        hBoxContatoWhatsappLbl.setPaddingBottom(0);
        hBoxContatoWhatsappLbl.setMarginTop(0);
        hBoxContatoWhatsappLbl.setMarginLeft(0);
        hBoxContatoWhatsappLbl.setMarginRight(0);
        hBoxContatoWhatsappLbl.setMarginBottom(0);
        hBoxContatoWhatsappLbl.setSpacing(1);
        hBoxContatoWhatsappLbl.setFlexVflex("ftMin");
        hBoxContatoWhatsappLbl.setFlexHflex("ftMin");
        hBoxContatoWhatsappLbl.setScrollable(false);
        hBoxContatoWhatsappLbl.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoWhatsappLbl.setBoxShadowConfigVerticalLength(10);
        hBoxContatoWhatsappLbl.setBoxShadowConfigBlurRadius(5);
        hBoxContatoWhatsappLbl.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoWhatsappLbl.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoWhatsappLbl.setBoxShadowConfigOpacity(75);
        hBoxContatoWhatsappLbl.setVAlign("tvTop");
        vBoxContatoWhatsapp.addChildren(hBoxContatoWhatsappLbl);
        hBoxContatoWhatsappLbl.applyProperties();
    }

    public TFLabel lblWhatsapp = new TFLabel();

    private void init_lblWhatsapp() {
        lblWhatsapp.setName("lblWhatsapp");
        lblWhatsapp.setLeft(0);
        lblWhatsapp.setTop(0);
        lblWhatsapp.setWidth(57);
        lblWhatsapp.setHeight(16);
        lblWhatsapp.setCaption("Whatsapp");
        lblWhatsapp.setFontColor("clWindowText");
        lblWhatsapp.setFontSize(-13);
        lblWhatsapp.setFontName("Tahoma");
        lblWhatsapp.setFontStyle("[]");
        lblWhatsapp.setVerticalAlignment("taVerticalCenter");
        lblWhatsapp.setWordBreak(false);
        hBoxContatoWhatsappLbl.addChildren(lblWhatsapp);
        lblWhatsapp.applyProperties();
    }

    public TFIconClass icoWhatsapp = new TFIconClass();

    private void init_icoWhatsapp() {
        icoWhatsapp.setName("icoWhatsapp");
        icoWhatsapp.setLeft(57);
        icoWhatsapp.setTop(0);
        icoWhatsapp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoWhatsappClick(event);
            processarFlow("FrmCadClienteContato", "icoWhatsapp", "OnClick");
        });
        icoWhatsapp.setIconClass("whatsapp");
        icoWhatsapp.setSize(16);
        icoWhatsapp.setColor("clBlack");
        hBoxContatoWhatsappLbl.addChildren(icoWhatsapp);
        icoWhatsapp.applyProperties();
    }

    public TFHBox hBoxContatoWhatsapp = new TFHBox();

    private void init_hBoxContatoWhatsapp() {
        hBoxContatoWhatsapp.setName("hBoxContatoWhatsapp");
        hBoxContatoWhatsapp.setLeft(0);
        hBoxContatoWhatsapp.setTop(21);
        hBoxContatoWhatsapp.setWidth(190);
        hBoxContatoWhatsapp.setHeight(40);
        hBoxContatoWhatsapp.setBorderStyle("stNone");
        hBoxContatoWhatsapp.setPaddingTop(0);
        hBoxContatoWhatsapp.setPaddingLeft(0);
        hBoxContatoWhatsapp.setPaddingRight(0);
        hBoxContatoWhatsapp.setPaddingBottom(0);
        hBoxContatoWhatsapp.setMarginTop(0);
        hBoxContatoWhatsapp.setMarginLeft(0);
        hBoxContatoWhatsapp.setMarginRight(0);
        hBoxContatoWhatsapp.setMarginBottom(0);
        hBoxContatoWhatsapp.setSpacing(5);
        hBoxContatoWhatsapp.setFlexVflex("ftMin");
        hBoxContatoWhatsapp.setFlexHflex("ftTrue");
        hBoxContatoWhatsapp.setScrollable(false);
        hBoxContatoWhatsapp.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoWhatsapp.setBoxShadowConfigVerticalLength(10);
        hBoxContatoWhatsapp.setBoxShadowConfigBlurRadius(5);
        hBoxContatoWhatsapp.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoWhatsapp.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoWhatsapp.setBoxShadowConfigOpacity(75);
        hBoxContatoWhatsapp.setVAlign("tvTop");
        vBoxContatoWhatsapp.addChildren(hBoxContatoWhatsapp);
        hBoxContatoWhatsapp.applyProperties();
    }

    public TFInteger edtDDDWhatsapp = new TFInteger();

    private void init_edtDDDWhatsapp() {
        edtDDDWhatsapp.setName("edtDDDWhatsapp");
        edtDDDWhatsapp.setLeft(0);
        edtDDDWhatsapp.setTop(0);
        edtDDDWhatsapp.setWidth(50);
        edtDDDWhatsapp.setHeight(29);
        edtDDDWhatsapp.setHint("DDD Whatsapp");
        edtDDDWhatsapp.setTable(tbClienteContato);
        edtDDDWhatsapp.setFieldName("PREFIXO_WHATSAPP");
        edtDDDWhatsapp.setHelpCaption("DDD Whatsapp");
        edtDDDWhatsapp.setFlex(false);
        edtDDDWhatsapp.setRequired(false);
        edtDDDWhatsapp.setPrompt("DDD Whatsapp");
        edtDDDWhatsapp.setConstraintCheckWhen("cwImmediate");
        edtDDDWhatsapp.setConstraintCheckType("ctExpression");
        edtDDDWhatsapp.setConstraintFocusOnError(false);
        edtDDDWhatsapp.setConstraintEnableUI(true);
        edtDDDWhatsapp.setConstraintEnabled(false);
        edtDDDWhatsapp.setConstraintFormCheck(true);
        edtDDDWhatsapp.setMaxlength(0);
        edtDDDWhatsapp.setAlign("alClient");
        edtDDDWhatsapp.setFontColor("clWindowText");
        edtDDDWhatsapp.setFontSize(-17);
        edtDDDWhatsapp.setFontName("Tahoma");
        edtDDDWhatsapp.setFontStyle("[]");
        edtDDDWhatsapp.setAlignment("taRightJustify");
        edtDDDWhatsapp.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtDDDWhatsapp", "OnEnter");
        });
        hBoxContatoWhatsapp.addChildren(edtDDDWhatsapp);
        edtDDDWhatsapp.applyProperties();
        addValidatable(edtDDDWhatsapp);
    }

    public TFInteger edtTelefoneWhatsapp = new TFInteger();

    private void init_edtTelefoneWhatsapp() {
        edtTelefoneWhatsapp.setName("edtTelefoneWhatsapp");
        edtTelefoneWhatsapp.setLeft(50);
        edtTelefoneWhatsapp.setTop(0);
        edtTelefoneWhatsapp.setWidth(100);
        edtTelefoneWhatsapp.setHeight(29);
        edtTelefoneWhatsapp.setHint("Whatsapp");
        edtTelefoneWhatsapp.setTable(tbClienteContato);
        edtTelefoneWhatsapp.setFieldName("WHATSAPP");
        edtTelefoneWhatsapp.setHelpCaption("Whatsapp");
        edtTelefoneWhatsapp.setFlex(true);
        edtTelefoneWhatsapp.setRequired(false);
        edtTelefoneWhatsapp.setPrompt("Whatsapp");
        edtTelefoneWhatsapp.setConstraintCheckWhen("cwImmediate");
        edtTelefoneWhatsapp.setConstraintCheckType("ctExpression");
        edtTelefoneWhatsapp.setConstraintFocusOnError(false);
        edtTelefoneWhatsapp.setConstraintEnableUI(true);
        edtTelefoneWhatsapp.setConstraintEnabled(false);
        edtTelefoneWhatsapp.setConstraintFormCheck(true);
        edtTelefoneWhatsapp.setMaxlength(0);
        edtTelefoneWhatsapp.setAlign("alClient");
        edtTelefoneWhatsapp.setFontColor("clWindowText");
        edtTelefoneWhatsapp.setFontSize(-17);
        edtTelefoneWhatsapp.setFontName("Tahoma");
        edtTelefoneWhatsapp.setFontStyle("[]");
        edtTelefoneWhatsapp.setAlignment("taRightJustify");
        edtTelefoneWhatsapp.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtTelefoneWhatsapp", "OnEnter");
        });
        hBoxContatoWhatsapp.addChildren(edtTelefoneWhatsapp);
        edtTelefoneWhatsapp.applyProperties();
        addValidatable(edtTelefoneWhatsapp);
    }

    public TFIconClass icoLimparTelefoneWhatsapp = new TFIconClass();

    private void init_icoLimparTelefoneWhatsapp() {
        icoLimparTelefoneWhatsapp.setName("icoLimparTelefoneWhatsapp");
        icoLimparTelefoneWhatsapp.setLeft(150);
        icoLimparTelefoneWhatsapp.setTop(0);
        icoLimparTelefoneWhatsapp.setHint("Limpar Telefone Whatsapp");
        icoLimparTelefoneWhatsapp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoLimparTelefoneWhatsappClick(event);
            processarFlow("FrmCadClienteContato", "icoLimparTelefoneWhatsapp", "OnClick");
        });
        icoLimparTelefoneWhatsapp.setIconClass("trash");
        icoLimparTelefoneWhatsapp.setSize(25);
        icoLimparTelefoneWhatsapp.setColor("clBlack");
        hBoxContatoWhatsapp.addChildren(icoLimparTelefoneWhatsapp);
        icoLimparTelefoneWhatsapp.applyProperties();
    }

    public TFHBox hBoxTbsCadastroLinha04 = new TFHBox();

    private void init_hBoxTbsCadastroLinha04() {
        hBoxTbsCadastroLinha04.setName("hBoxTbsCadastroLinha04");
        hBoxTbsCadastroLinha04.setLeft(0);
        hBoxTbsCadastroLinha04.setTop(418);
        hBoxTbsCadastroLinha04.setWidth(500);
        hBoxTbsCadastroLinha04.setHeight(80);
        hBoxTbsCadastroLinha04.setBorderStyle("stNone");
        hBoxTbsCadastroLinha04.setPaddingTop(0);
        hBoxTbsCadastroLinha04.setPaddingLeft(0);
        hBoxTbsCadastroLinha04.setPaddingRight(0);
        hBoxTbsCadastroLinha04.setPaddingBottom(0);
        hBoxTbsCadastroLinha04.setMarginTop(0);
        hBoxTbsCadastroLinha04.setMarginLeft(5);
        hBoxTbsCadastroLinha04.setMarginRight(5);
        hBoxTbsCadastroLinha04.setMarginBottom(0);
        hBoxTbsCadastroLinha04.setSpacing(5);
        hBoxTbsCadastroLinha04.setFlexVflex("ftMin");
        hBoxTbsCadastroLinha04.setFlexHflex("ftTrue");
        hBoxTbsCadastroLinha04.setScrollable(false);
        hBoxTbsCadastroLinha04.setBoxShadowConfigHorizontalLength(10);
        hBoxTbsCadastroLinha04.setBoxShadowConfigVerticalLength(10);
        hBoxTbsCadastroLinha04.setBoxShadowConfigBlurRadius(5);
        hBoxTbsCadastroLinha04.setBoxShadowConfigSpreadRadius(0);
        hBoxTbsCadastroLinha04.setBoxShadowConfigShadowColor("clBlack");
        hBoxTbsCadastroLinha04.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha04.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(hBoxTbsCadastroLinha04);
        hBoxTbsCadastroLinha04.applyProperties();
    }

    public TFVBox vBoxContatoFoneRes = new TFVBox();

    private void init_vBoxContatoFoneRes() {
        vBoxContatoFoneRes.setName("vBoxContatoFoneRes");
        vBoxContatoFoneRes.setLeft(0);
        vBoxContatoFoneRes.setTop(0);
        vBoxContatoFoneRes.setWidth(200);
        vBoxContatoFoneRes.setHeight(70);
        vBoxContatoFoneRes.setAlign("alLeft");
        vBoxContatoFoneRes.setBorderStyle("stNone");
        vBoxContatoFoneRes.setPaddingTop(0);
        vBoxContatoFoneRes.setPaddingLeft(0);
        vBoxContatoFoneRes.setPaddingRight(0);
        vBoxContatoFoneRes.setPaddingBottom(0);
        vBoxContatoFoneRes.setMarginTop(0);
        vBoxContatoFoneRes.setMarginLeft(0);
        vBoxContatoFoneRes.setMarginRight(0);
        vBoxContatoFoneRes.setMarginBottom(0);
        vBoxContatoFoneRes.setSpacing(0);
        vBoxContatoFoneRes.setFlexVflex("ftMin");
        vBoxContatoFoneRes.setFlexHflex("ftTrue");
        vBoxContatoFoneRes.setScrollable(false);
        vBoxContatoFoneRes.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoFoneRes.setBoxShadowConfigVerticalLength(10);
        vBoxContatoFoneRes.setBoxShadowConfigBlurRadius(5);
        vBoxContatoFoneRes.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoFoneRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoFoneRes.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha04.addChildren(vBoxContatoFoneRes);
        vBoxContatoFoneRes.applyProperties();
    }

    public TFHBox hBoxContatoFoneResLbl = new TFHBox();

    private void init_hBoxContatoFoneResLbl() {
        hBoxContatoFoneResLbl.setName("hBoxContatoFoneResLbl");
        hBoxContatoFoneResLbl.setLeft(0);
        hBoxContatoFoneResLbl.setTop(0);
        hBoxContatoFoneResLbl.setWidth(190);
        hBoxContatoFoneResLbl.setHeight(20);
        hBoxContatoFoneResLbl.setBorderStyle("stNone");
        hBoxContatoFoneResLbl.setPaddingTop(0);
        hBoxContatoFoneResLbl.setPaddingLeft(0);
        hBoxContatoFoneResLbl.setPaddingRight(0);
        hBoxContatoFoneResLbl.setPaddingBottom(0);
        hBoxContatoFoneResLbl.setMarginTop(0);
        hBoxContatoFoneResLbl.setMarginLeft(0);
        hBoxContatoFoneResLbl.setMarginRight(0);
        hBoxContatoFoneResLbl.setMarginBottom(0);
        hBoxContatoFoneResLbl.setSpacing(1);
        hBoxContatoFoneResLbl.setFlexVflex("ftMin");
        hBoxContatoFoneResLbl.setFlexHflex("ftMin");
        hBoxContatoFoneResLbl.setScrollable(false);
        hBoxContatoFoneResLbl.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoFoneResLbl.setBoxShadowConfigVerticalLength(10);
        hBoxContatoFoneResLbl.setBoxShadowConfigBlurRadius(5);
        hBoxContatoFoneResLbl.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoFoneResLbl.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoFoneResLbl.setBoxShadowConfigOpacity(75);
        hBoxContatoFoneResLbl.setVAlign("tvTop");
        vBoxContatoFoneRes.addChildren(hBoxContatoFoneResLbl);
        hBoxContatoFoneResLbl.applyProperties();
    }

    public TFLabel lblResidencial = new TFLabel();

    private void init_lblResidencial() {
        lblResidencial.setName("lblResidencial");
        lblResidencial.setLeft(0);
        lblResidencial.setTop(0);
        lblResidencial.setWidth(64);
        lblResidencial.setHeight(16);
        lblResidencial.setCaption("Residencial");
        lblResidencial.setFontColor("clWindowText");
        lblResidencial.setFontSize(-13);
        lblResidencial.setFontName("Tahoma");
        lblResidencial.setFontStyle("[]");
        lblResidencial.setVerticalAlignment("taVerticalCenter");
        lblResidencial.setWordBreak(false);
        hBoxContatoFoneResLbl.addChildren(lblResidencial);
        lblResidencial.applyProperties();
    }

    public TFIconClass icoTelefone = new TFIconClass();

    private void init_icoTelefone() {
        icoTelefone.setName("icoTelefone");
        icoTelefone.setLeft(64);
        icoTelefone.setTop(0);
        icoTelefone.setIconClass("phone");
        icoTelefone.setSize(16);
        icoTelefone.setColor("clBlack");
        hBoxContatoFoneResLbl.addChildren(icoTelefone);
        icoTelefone.applyProperties();
    }

    public TFHBox hBoxContatoFoneRes = new TFHBox();

    private void init_hBoxContatoFoneRes() {
        hBoxContatoFoneRes.setName("hBoxContatoFoneRes");
        hBoxContatoFoneRes.setLeft(0);
        hBoxContatoFoneRes.setTop(21);
        hBoxContatoFoneRes.setWidth(190);
        hBoxContatoFoneRes.setHeight(40);
        hBoxContatoFoneRes.setBorderStyle("stNone");
        hBoxContatoFoneRes.setPaddingTop(0);
        hBoxContatoFoneRes.setPaddingLeft(0);
        hBoxContatoFoneRes.setPaddingRight(0);
        hBoxContatoFoneRes.setPaddingBottom(0);
        hBoxContatoFoneRes.setMarginTop(0);
        hBoxContatoFoneRes.setMarginLeft(0);
        hBoxContatoFoneRes.setMarginRight(0);
        hBoxContatoFoneRes.setMarginBottom(0);
        hBoxContatoFoneRes.setSpacing(5);
        hBoxContatoFoneRes.setFlexVflex("ftMin");
        hBoxContatoFoneRes.setFlexHflex("ftTrue");
        hBoxContatoFoneRes.setScrollable(false);
        hBoxContatoFoneRes.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoFoneRes.setBoxShadowConfigVerticalLength(10);
        hBoxContatoFoneRes.setBoxShadowConfigBlurRadius(5);
        hBoxContatoFoneRes.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoFoneRes.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoFoneRes.setBoxShadowConfigOpacity(75);
        hBoxContatoFoneRes.setVAlign("tvTop");
        vBoxContatoFoneRes.addChildren(hBoxContatoFoneRes);
        hBoxContatoFoneRes.applyProperties();
    }

    public TFInteger edtDDDResidencial = new TFInteger();

    private void init_edtDDDResidencial() {
        edtDDDResidencial.setName("edtDDDResidencial");
        edtDDDResidencial.setLeft(0);
        edtDDDResidencial.setTop(0);
        edtDDDResidencial.setWidth(50);
        edtDDDResidencial.setHeight(29);
        edtDDDResidencial.setHint("DDD Residencial");
        edtDDDResidencial.setTable(tbClienteContato);
        edtDDDResidencial.setFieldName("PREFIXO_RES");
        edtDDDResidencial.setHelpCaption("DDD Residencial");
        edtDDDResidencial.setFlex(false);
        edtDDDResidencial.setRequired(false);
        edtDDDResidencial.setPrompt("DDD Residencial");
        edtDDDResidencial.setConstraintCheckWhen("cwImmediate");
        edtDDDResidencial.setConstraintCheckType("ctExpression");
        edtDDDResidencial.setConstraintFocusOnError(false);
        edtDDDResidencial.setConstraintEnableUI(true);
        edtDDDResidencial.setConstraintEnabled(false);
        edtDDDResidencial.setConstraintFormCheck(true);
        edtDDDResidencial.setMaxlength(0);
        edtDDDResidencial.setAlign("alClient");
        edtDDDResidencial.setFontColor("clWindowText");
        edtDDDResidencial.setFontSize(-17);
        edtDDDResidencial.setFontName("Tahoma");
        edtDDDResidencial.setFontStyle("[]");
        edtDDDResidencial.setAlignment("taRightJustify");
        edtDDDResidencial.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtDDDResidencial", "OnEnter");
        });
        hBoxContatoFoneRes.addChildren(edtDDDResidencial);
        edtDDDResidencial.applyProperties();
        addValidatable(edtDDDResidencial);
    }

    public TFInteger edtTelefoneResidencial = new TFInteger();

    private void init_edtTelefoneResidencial() {
        edtTelefoneResidencial.setName("edtTelefoneResidencial");
        edtTelefoneResidencial.setLeft(50);
        edtTelefoneResidencial.setTop(0);
        edtTelefoneResidencial.setWidth(100);
        edtTelefoneResidencial.setHeight(29);
        edtTelefoneResidencial.setHint("Residencial");
        edtTelefoneResidencial.setTable(tbClienteContato);
        edtTelefoneResidencial.setFieldName("FONE");
        edtTelefoneResidencial.setHelpCaption("Residencial");
        edtTelefoneResidencial.setFlex(true);
        edtTelefoneResidencial.setRequired(false);
        edtTelefoneResidencial.setPrompt("Residencial");
        edtTelefoneResidencial.setConstraintCheckWhen("cwImmediate");
        edtTelefoneResidencial.setConstraintCheckType("ctExpression");
        edtTelefoneResidencial.setConstraintFocusOnError(false);
        edtTelefoneResidencial.setConstraintEnableUI(true);
        edtTelefoneResidencial.setConstraintEnabled(false);
        edtTelefoneResidencial.setConstraintFormCheck(true);
        edtTelefoneResidencial.setMaxlength(0);
        edtTelefoneResidencial.setAlign("alClient");
        edtTelefoneResidencial.setFontColor("clWindowText");
        edtTelefoneResidencial.setFontSize(-17);
        edtTelefoneResidencial.setFontName("Tahoma");
        edtTelefoneResidencial.setFontStyle("[]");
        edtTelefoneResidencial.setAlignment("taRightJustify");
        edtTelefoneResidencial.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtTelefoneResidencial", "OnEnter");
        });
        hBoxContatoFoneRes.addChildren(edtTelefoneResidencial);
        edtTelefoneResidencial.applyProperties();
        addValidatable(edtTelefoneResidencial);
    }

    public TFIconClass icoLimparTelefoneResidencial = new TFIconClass();

    private void init_icoLimparTelefoneResidencial() {
        icoLimparTelefoneResidencial.setName("icoLimparTelefoneResidencial");
        icoLimparTelefoneResidencial.setLeft(150);
        icoLimparTelefoneResidencial.setTop(0);
        icoLimparTelefoneResidencial.setHint("Limpar Telefone Residencial");
        icoLimparTelefoneResidencial.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoLimparTelefoneResidencialClick(event);
            processarFlow("FrmCadClienteContato", "icoLimparTelefoneResidencial", "OnClick");
        });
        icoLimparTelefoneResidencial.setIconClass("trash");
        icoLimparTelefoneResidencial.setSize(25);
        icoLimparTelefoneResidencial.setColor("clBlack");
        hBoxContatoFoneRes.addChildren(icoLimparTelefoneResidencial);
        icoLimparTelefoneResidencial.applyProperties();
    }

    public TFVBox vBoxContatoCelular = new TFVBox();

    private void init_vBoxContatoCelular() {
        vBoxContatoCelular.setName("vBoxContatoCelular");
        vBoxContatoCelular.setLeft(200);
        vBoxContatoCelular.setTop(0);
        vBoxContatoCelular.setWidth(200);
        vBoxContatoCelular.setHeight(70);
        vBoxContatoCelular.setAlign("alLeft");
        vBoxContatoCelular.setBorderStyle("stNone");
        vBoxContatoCelular.setPaddingTop(0);
        vBoxContatoCelular.setPaddingLeft(0);
        vBoxContatoCelular.setPaddingRight(0);
        vBoxContatoCelular.setPaddingBottom(0);
        vBoxContatoCelular.setMarginTop(0);
        vBoxContatoCelular.setMarginLeft(0);
        vBoxContatoCelular.setMarginRight(0);
        vBoxContatoCelular.setMarginBottom(0);
        vBoxContatoCelular.setSpacing(0);
        vBoxContatoCelular.setFlexVflex("ftMin");
        vBoxContatoCelular.setFlexHflex("ftTrue");
        vBoxContatoCelular.setScrollable(false);
        vBoxContatoCelular.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoCelular.setBoxShadowConfigVerticalLength(10);
        vBoxContatoCelular.setBoxShadowConfigBlurRadius(5);
        vBoxContatoCelular.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoCelular.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoCelular.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha04.addChildren(vBoxContatoCelular);
        vBoxContatoCelular.applyProperties();
    }

    public TFHBox hBoxContatoCelularLbl = new TFHBox();

    private void init_hBoxContatoCelularLbl() {
        hBoxContatoCelularLbl.setName("hBoxContatoCelularLbl");
        hBoxContatoCelularLbl.setLeft(0);
        hBoxContatoCelularLbl.setTop(0);
        hBoxContatoCelularLbl.setWidth(190);
        hBoxContatoCelularLbl.setHeight(20);
        hBoxContatoCelularLbl.setBorderStyle("stNone");
        hBoxContatoCelularLbl.setPaddingTop(0);
        hBoxContatoCelularLbl.setPaddingLeft(0);
        hBoxContatoCelularLbl.setPaddingRight(0);
        hBoxContatoCelularLbl.setPaddingBottom(0);
        hBoxContatoCelularLbl.setMarginTop(0);
        hBoxContatoCelularLbl.setMarginLeft(0);
        hBoxContatoCelularLbl.setMarginRight(0);
        hBoxContatoCelularLbl.setMarginBottom(0);
        hBoxContatoCelularLbl.setSpacing(1);
        hBoxContatoCelularLbl.setFlexVflex("ftMin");
        hBoxContatoCelularLbl.setFlexHflex("ftMin");
        hBoxContatoCelularLbl.setScrollable(false);
        hBoxContatoCelularLbl.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoCelularLbl.setBoxShadowConfigVerticalLength(10);
        hBoxContatoCelularLbl.setBoxShadowConfigBlurRadius(5);
        hBoxContatoCelularLbl.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoCelularLbl.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoCelularLbl.setBoxShadowConfigOpacity(75);
        hBoxContatoCelularLbl.setVAlign("tvTop");
        vBoxContatoCelular.addChildren(hBoxContatoCelularLbl);
        hBoxContatoCelularLbl.applyProperties();
    }

    public TFLabel lblCelular = new TFLabel();

    private void init_lblCelular() {
        lblCelular.setName("lblCelular");
        lblCelular.setLeft(0);
        lblCelular.setTop(0);
        lblCelular.setWidth(44);
        lblCelular.setHeight(16);
        lblCelular.setCaption("Celular ");
        lblCelular.setFontColor("clWindowText");
        lblCelular.setFontSize(-13);
        lblCelular.setFontName("Tahoma");
        lblCelular.setFontStyle("[]");
        lblCelular.setVerticalAlignment("taVerticalCenter");
        lblCelular.setWordBreak(false);
        hBoxContatoCelularLbl.addChildren(lblCelular);
        lblCelular.applyProperties();
    }

    public TFIconClass icoCelular = new TFIconClass();

    private void init_icoCelular() {
        icoCelular.setName("icoCelular");
        icoCelular.setLeft(44);
        icoCelular.setTop(0);
        icoCelular.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoCelularClick(event);
            processarFlow("FrmCadClienteContato", "icoCelular", "OnClick");
        });
        icoCelular.setIconClass("mobile");
        icoCelular.setSize(16);
        icoCelular.setColor("clBlack");
        hBoxContatoCelularLbl.addChildren(icoCelular);
        icoCelular.applyProperties();
    }

    public TFHBox hBoxContatoCelular = new TFHBox();

    private void init_hBoxContatoCelular() {
        hBoxContatoCelular.setName("hBoxContatoCelular");
        hBoxContatoCelular.setLeft(0);
        hBoxContatoCelular.setTop(21);
        hBoxContatoCelular.setWidth(190);
        hBoxContatoCelular.setHeight(40);
        hBoxContatoCelular.setBorderStyle("stNone");
        hBoxContatoCelular.setPaddingTop(0);
        hBoxContatoCelular.setPaddingLeft(0);
        hBoxContatoCelular.setPaddingRight(0);
        hBoxContatoCelular.setPaddingBottom(0);
        hBoxContatoCelular.setMarginTop(0);
        hBoxContatoCelular.setMarginLeft(0);
        hBoxContatoCelular.setMarginRight(0);
        hBoxContatoCelular.setMarginBottom(0);
        hBoxContatoCelular.setSpacing(5);
        hBoxContatoCelular.setFlexVflex("ftMin");
        hBoxContatoCelular.setFlexHflex("ftTrue");
        hBoxContatoCelular.setScrollable(false);
        hBoxContatoCelular.setBoxShadowConfigHorizontalLength(10);
        hBoxContatoCelular.setBoxShadowConfigVerticalLength(10);
        hBoxContatoCelular.setBoxShadowConfigBlurRadius(5);
        hBoxContatoCelular.setBoxShadowConfigSpreadRadius(0);
        hBoxContatoCelular.setBoxShadowConfigShadowColor("clBlack");
        hBoxContatoCelular.setBoxShadowConfigOpacity(75);
        hBoxContatoCelular.setVAlign("tvTop");
        vBoxContatoCelular.addChildren(hBoxContatoCelular);
        hBoxContatoCelular.applyProperties();
    }

    public TFInteger edtDDDCelular = new TFInteger();

    private void init_edtDDDCelular() {
        edtDDDCelular.setName("edtDDDCelular");
        edtDDDCelular.setLeft(0);
        edtDDDCelular.setTop(0);
        edtDDDCelular.setWidth(50);
        edtDDDCelular.setHeight(29);
        edtDDDCelular.setHint("DDD Celular");
        edtDDDCelular.setTable(tbClienteContato);
        edtDDDCelular.setFieldName("PREFIXO_CEL");
        edtDDDCelular.setHelpCaption("DDD Celular");
        edtDDDCelular.setFlex(false);
        edtDDDCelular.setRequired(false);
        edtDDDCelular.setPrompt("DDD Celular");
        edtDDDCelular.setConstraintCheckWhen("cwImmediate");
        edtDDDCelular.setConstraintCheckType("ctExpression");
        edtDDDCelular.setConstraintFocusOnError(false);
        edtDDDCelular.setConstraintEnableUI(true);
        edtDDDCelular.setConstraintEnabled(false);
        edtDDDCelular.setConstraintFormCheck(true);
        edtDDDCelular.setMaxlength(0);
        edtDDDCelular.setAlign("alClient");
        edtDDDCelular.setFontColor("clWindowText");
        edtDDDCelular.setFontSize(-17);
        edtDDDCelular.setFontName("Tahoma");
        edtDDDCelular.setFontStyle("[]");
        edtDDDCelular.setAlignment("taRightJustify");
        edtDDDCelular.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtDDDCelular", "OnEnter");
        });
        hBoxContatoCelular.addChildren(edtDDDCelular);
        edtDDDCelular.applyProperties();
        addValidatable(edtDDDCelular);
    }

    public TFInteger edtTelefoneCelular = new TFInteger();

    private void init_edtTelefoneCelular() {
        edtTelefoneCelular.setName("edtTelefoneCelular");
        edtTelefoneCelular.setLeft(50);
        edtTelefoneCelular.setTop(0);
        edtTelefoneCelular.setWidth(100);
        edtTelefoneCelular.setHeight(29);
        edtTelefoneCelular.setHint("Celular");
        edtTelefoneCelular.setTable(tbClienteContato);
        edtTelefoneCelular.setFieldName("CELULAR");
        edtTelefoneCelular.setHelpCaption("Celular");
        edtTelefoneCelular.setFlex(true);
        edtTelefoneCelular.setRequired(false);
        edtTelefoneCelular.setPrompt("Celular");
        edtTelefoneCelular.setConstraintCheckWhen("cwImmediate");
        edtTelefoneCelular.setConstraintCheckType("ctExpression");
        edtTelefoneCelular.setConstraintFocusOnError(false);
        edtTelefoneCelular.setConstraintEnableUI(true);
        edtTelefoneCelular.setConstraintEnabled(false);
        edtTelefoneCelular.setConstraintFormCheck(true);
        edtTelefoneCelular.setMaxlength(0);
        edtTelefoneCelular.setAlign("alClient");
        edtTelefoneCelular.setFontColor("clWindowText");
        edtTelefoneCelular.setFontSize(-17);
        edtTelefoneCelular.setFontName("Tahoma");
        edtTelefoneCelular.setFontStyle("[]");
        edtTelefoneCelular.setAlignment("taRightJustify");
        edtTelefoneCelular.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtTelefoneCelular", "OnEnter");
        });
        hBoxContatoCelular.addChildren(edtTelefoneCelular);
        edtTelefoneCelular.applyProperties();
        addValidatable(edtTelefoneCelular);
    }

    public TFIconClass icoLimparTelefoneCelular = new TFIconClass();

    private void init_icoLimparTelefoneCelular() {
        icoLimparTelefoneCelular.setName("icoLimparTelefoneCelular");
        icoLimparTelefoneCelular.setLeft(150);
        icoLimparTelefoneCelular.setTop(0);
        icoLimparTelefoneCelular.setHint("Limpar Telefone Celular");
        icoLimparTelefoneCelular.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoLimparTelefoneCelularClick(event);
            processarFlow("FrmCadClienteContato", "icoLimparTelefoneCelular", "OnClick");
        });
        icoLimparTelefoneCelular.setIconClass("trash");
        icoLimparTelefoneCelular.setSize(25);
        icoLimparTelefoneCelular.setColor("clBlack");
        hBoxContatoCelular.addChildren(icoLimparTelefoneCelular);
        icoLimparTelefoneCelular.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(499);
        FVBox2.setWidth(500);
        FVBox2.setHeight(25);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setColor("15724527");
        FVBox2.setPaddingTop(3);
        FVBox2.setPaddingLeft(7);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(10);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(0);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        vBoxTbsCadastro.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(52);
        FLabel2.setHeight(19);
        FLabel2.setCaption("Op\u00E7\u00F5es");
        FLabel2.setFontColor("13392431");
        FLabel2.setFontSize(-16);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox2.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFHBox hBoxTbsCadastroLinha05 = new TFHBox();

    private void init_hBoxTbsCadastroLinha05() {
        hBoxTbsCadastroLinha05.setName("hBoxTbsCadastroLinha05");
        hBoxTbsCadastroLinha05.setLeft(0);
        hBoxTbsCadastroLinha05.setTop(525);
        hBoxTbsCadastroLinha05.setWidth(500);
        hBoxTbsCadastroLinha05.setHeight(60);
        hBoxTbsCadastroLinha05.setBorderStyle("stNone");
        hBoxTbsCadastroLinha05.setPaddingTop(0);
        hBoxTbsCadastroLinha05.setPaddingLeft(0);
        hBoxTbsCadastroLinha05.setPaddingRight(0);
        hBoxTbsCadastroLinha05.setPaddingBottom(0);
        hBoxTbsCadastroLinha05.setMarginTop(0);
        hBoxTbsCadastroLinha05.setMarginLeft(5);
        hBoxTbsCadastroLinha05.setMarginRight(5);
        hBoxTbsCadastroLinha05.setMarginBottom(0);
        hBoxTbsCadastroLinha05.setSpacing(5);
        hBoxTbsCadastroLinha05.setFlexVflex("ftMin");
        hBoxTbsCadastroLinha05.setFlexHflex("ftTrue");
        hBoxTbsCadastroLinha05.setScrollable(false);
        hBoxTbsCadastroLinha05.setBoxShadowConfigHorizontalLength(10);
        hBoxTbsCadastroLinha05.setBoxShadowConfigVerticalLength(10);
        hBoxTbsCadastroLinha05.setBoxShadowConfigBlurRadius(5);
        hBoxTbsCadastroLinha05.setBoxShadowConfigSpreadRadius(0);
        hBoxTbsCadastroLinha05.setBoxShadowConfigShadowColor("clBlack");
        hBoxTbsCadastroLinha05.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha05.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(hBoxTbsCadastroLinha05);
        hBoxTbsCadastroLinha05.applyProperties();
    }

    public TFVBox vBoxContatoPoliticamenteExposto = new TFVBox();

    private void init_vBoxContatoPoliticamenteExposto() {
        vBoxContatoPoliticamenteExposto.setName("vBoxContatoPoliticamenteExposto");
        vBoxContatoPoliticamenteExposto.setLeft(0);
        vBoxContatoPoliticamenteExposto.setTop(0);
        vBoxContatoPoliticamenteExposto.setWidth(210);
        vBoxContatoPoliticamenteExposto.setHeight(50);
        vBoxContatoPoliticamenteExposto.setBorderStyle("stNone");
        vBoxContatoPoliticamenteExposto.setPaddingTop(0);
        vBoxContatoPoliticamenteExposto.setPaddingLeft(0);
        vBoxContatoPoliticamenteExposto.setPaddingRight(0);
        vBoxContatoPoliticamenteExposto.setPaddingBottom(0);
        vBoxContatoPoliticamenteExposto.setMarginTop(0);
        vBoxContatoPoliticamenteExposto.setMarginLeft(0);
        vBoxContatoPoliticamenteExposto.setMarginRight(5);
        vBoxContatoPoliticamenteExposto.setMarginBottom(0);
        vBoxContatoPoliticamenteExposto.setSpacing(1);
        vBoxContatoPoliticamenteExposto.setFlexVflex("ftMin");
        vBoxContatoPoliticamenteExposto.setFlexHflex("ftTrue");
        vBoxContatoPoliticamenteExposto.setScrollable(false);
        vBoxContatoPoliticamenteExposto.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoPoliticamenteExposto.setBoxShadowConfigVerticalLength(10);
        vBoxContatoPoliticamenteExposto.setBoxShadowConfigBlurRadius(5);
        vBoxContatoPoliticamenteExposto.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoPoliticamenteExposto.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoPoliticamenteExposto.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha05.addChildren(vBoxContatoPoliticamenteExposto);
        vBoxContatoPoliticamenteExposto.applyProperties();
    }

    public TFLabel lblTituloPoliticamenteExposto = new TFLabel();

    private void init_lblTituloPoliticamenteExposto() {
        lblTituloPoliticamenteExposto.setName("lblTituloPoliticamenteExposto");
        lblTituloPoliticamenteExposto.setLeft(0);
        lblTituloPoliticamenteExposto.setTop(0);
        lblTituloPoliticamenteExposto.setWidth(135);
        lblTituloPoliticamenteExposto.setHeight(16);
        lblTituloPoliticamenteExposto.setCaption("\u00C9 politicamente exposto");
        lblTituloPoliticamenteExposto.setFontColor("clWindowText");
        lblTituloPoliticamenteExposto.setFontSize(-13);
        lblTituloPoliticamenteExposto.setFontName("Tahoma");
        lblTituloPoliticamenteExposto.setFontStyle("[]");
        lblTituloPoliticamenteExposto.setVerticalAlignment("taVerticalCenter");
        lblTituloPoliticamenteExposto.setWordBreak(false);
        vBoxContatoPoliticamenteExposto.addChildren(lblTituloPoliticamenteExposto);
        lblTituloPoliticamenteExposto.applyProperties();
    }

    public TFCombo cboPoliticamenteExposto = new TFCombo();

    private void init_cboPoliticamenteExposto() {
        cboPoliticamenteExposto.setName("cboPoliticamenteExposto");
        cboPoliticamenteExposto.setLeft(0);
        cboPoliticamenteExposto.setTop(17);
        cboPoliticamenteExposto.setWidth(200);
        cboPoliticamenteExposto.setHeight(21);
        cboPoliticamenteExposto.setHint("\u00C9 Politicamente Exposto");
        cboPoliticamenteExposto.setTable(tbClienteContato);
        cboPoliticamenteExposto.setFieldName("POLITICAMENTE_EXPOSTO");
        cboPoliticamenteExposto.setFlex(true);
        cboPoliticamenteExposto.setListOptions("0-N\u00E3o \u00E9 PEP N\u00E3o possui informa\u00E7\u00E3o=0;1-\u00C9 PEP=1");
        cboPoliticamenteExposto.setHelpCaption("\u00C9 Politicamente Exposto");
        cboPoliticamenteExposto.setReadOnly(true);
        cboPoliticamenteExposto.setRequired(false);
        cboPoliticamenteExposto.setPrompt("\u00C9 Politicamente Exposto");
        cboPoliticamenteExposto.setConstraintCheckWhen("cwImmediate");
        cboPoliticamenteExposto.setConstraintCheckType("ctExpression");
        cboPoliticamenteExposto.setConstraintFocusOnError(false);
        cboPoliticamenteExposto.setConstraintEnableUI(true);
        cboPoliticamenteExposto.setConstraintEnabled(false);
        cboPoliticamenteExposto.setConstraintFormCheck(true);
        cboPoliticamenteExposto.setClearOnDelKey(true);
        cboPoliticamenteExposto.setUseClearButton(true);
        cboPoliticamenteExposto.setHideClearButtonOnNullValue(true);
        cboPoliticamenteExposto.setAlign("alLeft");
        cboPoliticamenteExposto.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "cboPoliticamenteExposto", "OnEnter");
        });
        vBoxContatoPoliticamenteExposto.addChildren(cboPoliticamenteExposto);
        cboPoliticamenteExposto.applyProperties();
        addValidatable(cboPoliticamenteExposto);
    }

    public TFVBox vBoxEhComprador = new TFVBox();

    private void init_vBoxEhComprador() {
        vBoxEhComprador.setName("vBoxEhComprador");
        vBoxEhComprador.setLeft(210);
        vBoxEhComprador.setTop(0);
        vBoxEhComprador.setWidth(110);
        vBoxEhComprador.setHeight(50);
        vBoxEhComprador.setBorderStyle("stNone");
        vBoxEhComprador.setPaddingTop(0);
        vBoxEhComprador.setPaddingLeft(0);
        vBoxEhComprador.setPaddingRight(0);
        vBoxEhComprador.setPaddingBottom(0);
        vBoxEhComprador.setMarginTop(0);
        vBoxEhComprador.setMarginLeft(0);
        vBoxEhComprador.setMarginRight(0);
        vBoxEhComprador.setMarginBottom(0);
        vBoxEhComprador.setSpacing(1);
        vBoxEhComprador.setFlexVflex("ftMin");
        vBoxEhComprador.setFlexHflex("ftMin");
        vBoxEhComprador.setScrollable(false);
        vBoxEhComprador.setBoxShadowConfigHorizontalLength(10);
        vBoxEhComprador.setBoxShadowConfigVerticalLength(10);
        vBoxEhComprador.setBoxShadowConfigBlurRadius(5);
        vBoxEhComprador.setBoxShadowConfigSpreadRadius(0);
        vBoxEhComprador.setBoxShadowConfigShadowColor("clBlack");
        vBoxEhComprador.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha05.addChildren(vBoxEhComprador);
        vBoxEhComprador.applyProperties();
    }

    public TFLabel lblEhComprador = new TFLabel();

    private void init_lblEhComprador() {
        lblEhComprador.setName("lblEhComprador");
        lblEhComprador.setLeft(0);
        lblEhComprador.setTop(0);
        lblEhComprador.setWidth(73);
        lblEhComprador.setHeight(16);
        lblEhComprador.setCaption("\u00C9 comprador");
        lblEhComprador.setFontColor("clWindowText");
        lblEhComprador.setFontSize(-13);
        lblEhComprador.setFontName("Tahoma");
        lblEhComprador.setFontStyle("[]");
        lblEhComprador.setVerticalAlignment("taVerticalCenter");
        lblEhComprador.setWordBreak(false);
        vBoxEhComprador.addChildren(lblEhComprador);
        lblEhComprador.applyProperties();
    }

    public TFCombo cboEhComprador = new TFCombo();

    private void init_cboEhComprador() {
        cboEhComprador.setName("cboEhComprador");
        cboEhComprador.setLeft(0);
        cboEhComprador.setTop(17);
        cboEhComprador.setWidth(100);
        cboEhComprador.setHeight(21);
        cboEhComprador.setHint("\u00C9 comprador");
        cboEhComprador.setTable(tbClienteContato);
        cboEhComprador.setFieldName("EH_COMPRADOR");
        cboEhComprador.setFlex(false);
        cboEhComprador.setListOptions("Sim=S;N\u00E3o=N");
        cboEhComprador.setHelpCaption("\u00C9 comprador");
        cboEhComprador.setReadOnly(true);
        cboEhComprador.setRequired(true);
        cboEhComprador.setPrompt("\u00C9 comprador");
        cboEhComprador.setConstraintCheckWhen("cwImmediate");
        cboEhComprador.setConstraintCheckType("ctExpression");
        cboEhComprador.setConstraintFocusOnError(false);
        cboEhComprador.setConstraintEnableUI(true);
        cboEhComprador.setConstraintEnabled(false);
        cboEhComprador.setConstraintFormCheck(true);
        cboEhComprador.setClearOnDelKey(false);
        cboEhComprador.setUseClearButton(false);
        cboEhComprador.setHideClearButtonOnNullValue(true);
        cboEhComprador.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "cboEhComprador", "OnEnter");
        });
        vBoxEhComprador.addChildren(cboEhComprador);
        cboEhComprador.applyProperties();
        addValidatable(cboEhComprador);
    }

    public TFVBox vBoxEhPreposto = new TFVBox();

    private void init_vBoxEhPreposto() {
        vBoxEhPreposto.setName("vBoxEhPreposto");
        vBoxEhPreposto.setLeft(320);
        vBoxEhPreposto.setTop(0);
        vBoxEhPreposto.setWidth(110);
        vBoxEhPreposto.setHeight(50);
        vBoxEhPreposto.setBorderStyle("stNone");
        vBoxEhPreposto.setPaddingTop(0);
        vBoxEhPreposto.setPaddingLeft(0);
        vBoxEhPreposto.setPaddingRight(0);
        vBoxEhPreposto.setPaddingBottom(0);
        vBoxEhPreposto.setMarginTop(0);
        vBoxEhPreposto.setMarginLeft(0);
        vBoxEhPreposto.setMarginRight(0);
        vBoxEhPreposto.setMarginBottom(0);
        vBoxEhPreposto.setSpacing(1);
        vBoxEhPreposto.setFlexVflex("ftMin");
        vBoxEhPreposto.setFlexHflex("ftMin");
        vBoxEhPreposto.setScrollable(false);
        vBoxEhPreposto.setBoxShadowConfigHorizontalLength(10);
        vBoxEhPreposto.setBoxShadowConfigVerticalLength(10);
        vBoxEhPreposto.setBoxShadowConfigBlurRadius(5);
        vBoxEhPreposto.setBoxShadowConfigSpreadRadius(0);
        vBoxEhPreposto.setBoxShadowConfigShadowColor("clBlack");
        vBoxEhPreposto.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha05.addChildren(vBoxEhPreposto);
        vBoxEhPreposto.applyProperties();
    }

    public TFLabel lblEhPreposto = new TFLabel();

    private void init_lblEhPreposto() {
        lblEhPreposto.setName("lblEhPreposto");
        lblEhPreposto.setLeft(0);
        lblEhPreposto.setTop(0);
        lblEhPreposto.setWidth(61);
        lblEhPreposto.setHeight(16);
        lblEhPreposto.setCaption("\u00C9 preposto");
        lblEhPreposto.setFontColor("clWindowText");
        lblEhPreposto.setFontSize(-13);
        lblEhPreposto.setFontName("Tahoma");
        lblEhPreposto.setFontStyle("[]");
        lblEhPreposto.setVerticalAlignment("taVerticalCenter");
        lblEhPreposto.setWordBreak(false);
        vBoxEhPreposto.addChildren(lblEhPreposto);
        lblEhPreposto.applyProperties();
    }

    public TFCombo cboEhPreposto = new TFCombo();

    private void init_cboEhPreposto() {
        cboEhPreposto.setName("cboEhPreposto");
        cboEhPreposto.setLeft(0);
        cboEhPreposto.setTop(17);
        cboEhPreposto.setWidth(100);
        cboEhPreposto.setHeight(21);
        cboEhPreposto.setHint("\u00C9 preposto");
        cboEhPreposto.setTable(tbClienteContato);
        cboEhPreposto.setFieldName("EH_PREPOSTO");
        cboEhPreposto.setFlex(false);
        cboEhPreposto.setListOptions("Sim=S;N\u00E3o=N");
        cboEhPreposto.setHelpCaption("\u00C9 preposto");
        cboEhPreposto.setReadOnly(true);
        cboEhPreposto.setRequired(true);
        cboEhPreposto.setPrompt("\u00C9 preposto");
        cboEhPreposto.setConstraintCheckWhen("cwImmediate");
        cboEhPreposto.setConstraintCheckType("ctExpression");
        cboEhPreposto.setConstraintFocusOnError(false);
        cboEhPreposto.setConstraintEnableUI(true);
        cboEhPreposto.setConstraintEnabled(false);
        cboEhPreposto.setConstraintFormCheck(true);
        cboEhPreposto.setClearOnDelKey(false);
        cboEhPreposto.setUseClearButton(false);
        cboEhPreposto.setHideClearButtonOnNullValue(true);
        cboEhPreposto.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "cboEhPreposto", "OnEnter");
        });
        vBoxEhPreposto.addChildren(cboEhPreposto);
        cboEhPreposto.applyProperties();
        addValidatable(cboEhPreposto);
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(586);
        FHBox4.setWidth(500);
        FHBox4.setHeight(53);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(5);
        FHBox4.setFlexVflex("ftMin");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox vBoxEhResponsavelPelaGarantiaDaMontadora = new TFVBox();

    private void init_vBoxEhResponsavelPelaGarantiaDaMontadora() {
        vBoxEhResponsavelPelaGarantiaDaMontadora.setName("vBoxEhResponsavelPelaGarantiaDaMontadora");
        vBoxEhResponsavelPelaGarantiaDaMontadora.setLeft(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setTop(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setWidth(240);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setHeight(50);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setBorderStyle("stNone");
        vBoxEhResponsavelPelaGarantiaDaMontadora.setPaddingTop(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setPaddingLeft(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setPaddingRight(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setPaddingBottom(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setMarginTop(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setMarginLeft(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setMarginRight(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setMarginBottom(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setSpacing(1);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setFlexVflex("ftMin");
        vBoxEhResponsavelPelaGarantiaDaMontadora.setFlexHflex("ftTrue");
        vBoxEhResponsavelPelaGarantiaDaMontadora.setScrollable(false);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setBoxShadowConfigHorizontalLength(10);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setBoxShadowConfigVerticalLength(10);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setBoxShadowConfigBlurRadius(5);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setBoxShadowConfigSpreadRadius(0);
        vBoxEhResponsavelPelaGarantiaDaMontadora.setBoxShadowConfigShadowColor("clBlack");
        vBoxEhResponsavelPelaGarantiaDaMontadora.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(vBoxEhResponsavelPelaGarantiaDaMontadora);
        vBoxEhResponsavelPelaGarantiaDaMontadora.applyProperties();
    }

    public TFLabel lblResponsavelPelaGarantiaDaMontadora = new TFLabel();

    private void init_lblResponsavelPelaGarantiaDaMontadora() {
        lblResponsavelPelaGarantiaDaMontadora.setName("lblResponsavelPelaGarantiaDaMontadora");
        lblResponsavelPelaGarantiaDaMontadora.setLeft(0);
        lblResponsavelPelaGarantiaDaMontadora.setTop(0);
        lblResponsavelPelaGarantiaDaMontadora.setWidth(234);
        lblResponsavelPelaGarantiaDaMontadora.setHeight(16);
        lblResponsavelPelaGarantiaDaMontadora.setCaption("Respons\u00E1vel pela garantia da montadora");
        lblResponsavelPelaGarantiaDaMontadora.setFontColor("clWindowText");
        lblResponsavelPelaGarantiaDaMontadora.setFontSize(-13);
        lblResponsavelPelaGarantiaDaMontadora.setFontName("Tahoma");
        lblResponsavelPelaGarantiaDaMontadora.setFontStyle("[]");
        lblResponsavelPelaGarantiaDaMontadora.setVerticalAlignment("taVerticalCenter");
        lblResponsavelPelaGarantiaDaMontadora.setWordBreak(false);
        vBoxEhResponsavelPelaGarantiaDaMontadora.addChildren(lblResponsavelPelaGarantiaDaMontadora);
        lblResponsavelPelaGarantiaDaMontadora.applyProperties();
    }

    public TFCombo cboResponsavelPelaGarantiaDaMontadora = new TFCombo();

    private void init_cboResponsavelPelaGarantiaDaMontadora() {
        cboResponsavelPelaGarantiaDaMontadora.setName("cboResponsavelPelaGarantiaDaMontadora");
        cboResponsavelPelaGarantiaDaMontadora.setLeft(0);
        cboResponsavelPelaGarantiaDaMontadora.setTop(17);
        cboResponsavelPelaGarantiaDaMontadora.setWidth(230);
        cboResponsavelPelaGarantiaDaMontadora.setHeight(21);
        cboResponsavelPelaGarantiaDaMontadora.setHint("Respons\u00E1vel pela garantia da montadora");
        cboResponsavelPelaGarantiaDaMontadora.setTable(tbClienteContato);
        cboResponsavelPelaGarantiaDaMontadora.setFieldName("RESP_GARANTIA_MONTADORA");
        cboResponsavelPelaGarantiaDaMontadora.setFlex(true);
        cboResponsavelPelaGarantiaDaMontadora.setListOptions("Sim=S;N\u00E3o=N");
        cboResponsavelPelaGarantiaDaMontadora.setHelpCaption("Respons\u00E1vel pela garantia da montadora");
        cboResponsavelPelaGarantiaDaMontadora.setReadOnly(true);
        cboResponsavelPelaGarantiaDaMontadora.setRequired(true);
        cboResponsavelPelaGarantiaDaMontadora.setPrompt("Respons\u00E1vel pela garantia da montadora");
        cboResponsavelPelaGarantiaDaMontadora.setConstraintCheckWhen("cwImmediate");
        cboResponsavelPelaGarantiaDaMontadora.setConstraintCheckType("ctExpression");
        cboResponsavelPelaGarantiaDaMontadora.setConstraintFocusOnError(false);
        cboResponsavelPelaGarantiaDaMontadora.setConstraintEnableUI(true);
        cboResponsavelPelaGarantiaDaMontadora.setConstraintEnabled(false);
        cboResponsavelPelaGarantiaDaMontadora.setConstraintFormCheck(true);
        cboResponsavelPelaGarantiaDaMontadora.setClearOnDelKey(false);
        cboResponsavelPelaGarantiaDaMontadora.setUseClearButton(false);
        cboResponsavelPelaGarantiaDaMontadora.setHideClearButtonOnNullValue(true);
        cboResponsavelPelaGarantiaDaMontadora.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "cboResponsavelPelaGarantiaDaMontadora", "OnEnter");
        });
        vBoxEhResponsavelPelaGarantiaDaMontadora.addChildren(cboResponsavelPelaGarantiaDaMontadora);
        cboResponsavelPelaGarantiaDaMontadora.applyProperties();
        addValidatable(cboResponsavelPelaGarantiaDaMontadora);
    }

    public TFVBox vBosResponsavelPelaPesquisaDaFabrica = new TFVBox();

    private void init_vBosResponsavelPelaPesquisaDaFabrica() {
        vBosResponsavelPelaPesquisaDaFabrica.setName("vBosResponsavelPelaPesquisaDaFabrica");
        vBosResponsavelPelaPesquisaDaFabrica.setLeft(240);
        vBosResponsavelPelaPesquisaDaFabrica.setTop(0);
        vBosResponsavelPelaPesquisaDaFabrica.setWidth(230);
        vBosResponsavelPelaPesquisaDaFabrica.setHeight(50);
        vBosResponsavelPelaPesquisaDaFabrica.setBorderStyle("stNone");
        vBosResponsavelPelaPesquisaDaFabrica.setPaddingTop(0);
        vBosResponsavelPelaPesquisaDaFabrica.setPaddingLeft(0);
        vBosResponsavelPelaPesquisaDaFabrica.setPaddingRight(0);
        vBosResponsavelPelaPesquisaDaFabrica.setPaddingBottom(0);
        vBosResponsavelPelaPesquisaDaFabrica.setMarginTop(0);
        vBosResponsavelPelaPesquisaDaFabrica.setMarginLeft(0);
        vBosResponsavelPelaPesquisaDaFabrica.setMarginRight(0);
        vBosResponsavelPelaPesquisaDaFabrica.setMarginBottom(0);
        vBosResponsavelPelaPesquisaDaFabrica.setSpacing(1);
        vBosResponsavelPelaPesquisaDaFabrica.setFlexVflex("ftMin");
        vBosResponsavelPelaPesquisaDaFabrica.setFlexHflex("ftTrue");
        vBosResponsavelPelaPesquisaDaFabrica.setScrollable(false);
        vBosResponsavelPelaPesquisaDaFabrica.setBoxShadowConfigHorizontalLength(10);
        vBosResponsavelPelaPesquisaDaFabrica.setBoxShadowConfigVerticalLength(10);
        vBosResponsavelPelaPesquisaDaFabrica.setBoxShadowConfigBlurRadius(5);
        vBosResponsavelPelaPesquisaDaFabrica.setBoxShadowConfigSpreadRadius(0);
        vBosResponsavelPelaPesquisaDaFabrica.setBoxShadowConfigShadowColor("clBlack");
        vBosResponsavelPelaPesquisaDaFabrica.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(vBosResponsavelPelaPesquisaDaFabrica);
        vBosResponsavelPelaPesquisaDaFabrica.applyProperties();
    }

    public TFLabel lblResponsavelPelaPesquisaDaFabrica = new TFLabel();

    private void init_lblResponsavelPelaPesquisaDaFabrica() {
        lblResponsavelPelaPesquisaDaFabrica.setName("lblResponsavelPelaPesquisaDaFabrica");
        lblResponsavelPelaPesquisaDaFabrica.setLeft(0);
        lblResponsavelPelaPesquisaDaFabrica.setTop(0);
        lblResponsavelPelaPesquisaDaFabrica.setWidth(214);
        lblResponsavelPelaPesquisaDaFabrica.setHeight(16);
        lblResponsavelPelaPesquisaDaFabrica.setCaption("Respons\u00E1vel pela pesquisa da f\u00E1brica");
        lblResponsavelPelaPesquisaDaFabrica.setFontColor("clWindowText");
        lblResponsavelPelaPesquisaDaFabrica.setFontSize(-13);
        lblResponsavelPelaPesquisaDaFabrica.setFontName("Tahoma");
        lblResponsavelPelaPesquisaDaFabrica.setFontStyle("[]");
        lblResponsavelPelaPesquisaDaFabrica.setVerticalAlignment("taVerticalCenter");
        lblResponsavelPelaPesquisaDaFabrica.setWordBreak(false);
        vBosResponsavelPelaPesquisaDaFabrica.addChildren(lblResponsavelPelaPesquisaDaFabrica);
        lblResponsavelPelaPesquisaDaFabrica.applyProperties();
    }

    public TFCombo cboResponsavelPelaPesquisaDaFabrica = new TFCombo();

    private void init_cboResponsavelPelaPesquisaDaFabrica() {
        cboResponsavelPelaPesquisaDaFabrica.setName("cboResponsavelPelaPesquisaDaFabrica");
        cboResponsavelPelaPesquisaDaFabrica.setLeft(0);
        cboResponsavelPelaPesquisaDaFabrica.setTop(17);
        cboResponsavelPelaPesquisaDaFabrica.setWidth(210);
        cboResponsavelPelaPesquisaDaFabrica.setHeight(21);
        cboResponsavelPelaPesquisaDaFabrica.setHint("Respons\u00E1vel pela pesquisa da f\u00E1brica");
        cboResponsavelPelaPesquisaDaFabrica.setTable(tbClienteContato);
        cboResponsavelPelaPesquisaDaFabrica.setFieldName("EH_PESQUISA");
        cboResponsavelPelaPesquisaDaFabrica.setFlex(true);
        cboResponsavelPelaPesquisaDaFabrica.setListOptions("Sim=S;N\u00E3o=N");
        cboResponsavelPelaPesquisaDaFabrica.setHelpCaption("Respons\u00E1vel pela pesquisa da f\u00E1brica");
        cboResponsavelPelaPesquisaDaFabrica.setReadOnly(true);
        cboResponsavelPelaPesquisaDaFabrica.setRequired(true);
        cboResponsavelPelaPesquisaDaFabrica.setPrompt("Respons\u00E1vel pela pesquisa da f\u00E1brica");
        cboResponsavelPelaPesquisaDaFabrica.setConstraintCheckWhen("cwImmediate");
        cboResponsavelPelaPesquisaDaFabrica.setConstraintCheckType("ctExpression");
        cboResponsavelPelaPesquisaDaFabrica.setConstraintFocusOnError(false);
        cboResponsavelPelaPesquisaDaFabrica.setConstraintEnableUI(true);
        cboResponsavelPelaPesquisaDaFabrica.setConstraintEnabled(false);
        cboResponsavelPelaPesquisaDaFabrica.setConstraintFormCheck(true);
        cboResponsavelPelaPesquisaDaFabrica.setClearOnDelKey(false);
        cboResponsavelPelaPesquisaDaFabrica.setUseClearButton(false);
        cboResponsavelPelaPesquisaDaFabrica.setHideClearButtonOnNullValue(true);
        cboResponsavelPelaPesquisaDaFabrica.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "cboResponsavelPelaPesquisaDaFabrica", "OnEnter");
        });
        vBosResponsavelPelaPesquisaDaFabrica.addChildren(cboResponsavelPelaPesquisaDaFabrica);
        cboResponsavelPelaPesquisaDaFabrica.applyProperties();
        addValidatable(cboResponsavelPelaPesquisaDaFabrica);
    }

    public TFHBox hBoxTbsCadastroLinha06 = new TFHBox();

    private void init_hBoxTbsCadastroLinha06() {
        hBoxTbsCadastroLinha06.setName("hBoxTbsCadastroLinha06");
        hBoxTbsCadastroLinha06.setLeft(0);
        hBoxTbsCadastroLinha06.setTop(640);
        hBoxTbsCadastroLinha06.setWidth(500);
        hBoxTbsCadastroLinha06.setHeight(60);
        hBoxTbsCadastroLinha06.setBorderStyle("stNone");
        hBoxTbsCadastroLinha06.setPaddingTop(0);
        hBoxTbsCadastroLinha06.setPaddingLeft(0);
        hBoxTbsCadastroLinha06.setPaddingRight(0);
        hBoxTbsCadastroLinha06.setPaddingBottom(0);
        hBoxTbsCadastroLinha06.setMarginTop(0);
        hBoxTbsCadastroLinha06.setMarginLeft(5);
        hBoxTbsCadastroLinha06.setMarginRight(5);
        hBoxTbsCadastroLinha06.setMarginBottom(0);
        hBoxTbsCadastroLinha06.setSpacing(5);
        hBoxTbsCadastroLinha06.setFlexVflex("ftMin");
        hBoxTbsCadastroLinha06.setFlexHflex("ftFalse");
        hBoxTbsCadastroLinha06.setScrollable(false);
        hBoxTbsCadastroLinha06.setBoxShadowConfigHorizontalLength(10);
        hBoxTbsCadastroLinha06.setBoxShadowConfigVerticalLength(10);
        hBoxTbsCadastroLinha06.setBoxShadowConfigBlurRadius(5);
        hBoxTbsCadastroLinha06.setBoxShadowConfigSpreadRadius(0);
        hBoxTbsCadastroLinha06.setBoxShadowConfigShadowColor("clBlack");
        hBoxTbsCadastroLinha06.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha06.setVAlign("tvTop");
        vBoxTbsCadastro.addChildren(hBoxTbsCadastroLinha06);
        hBoxTbsCadastroLinha06.applyProperties();
    }

    public TFVBox vBoxContatoTime = new TFVBox();

    private void init_vBoxContatoTime() {
        vBoxContatoTime.setName("vBoxContatoTime");
        vBoxContatoTime.setLeft(0);
        vBoxContatoTime.setTop(0);
        vBoxContatoTime.setWidth(181);
        vBoxContatoTime.setHeight(50);
        vBoxContatoTime.setBorderStyle("stNone");
        vBoxContatoTime.setPaddingTop(0);
        vBoxContatoTime.setPaddingLeft(0);
        vBoxContatoTime.setPaddingRight(0);
        vBoxContatoTime.setPaddingBottom(0);
        vBoxContatoTime.setMarginTop(0);
        vBoxContatoTime.setMarginLeft(0);
        vBoxContatoTime.setMarginRight(5);
        vBoxContatoTime.setMarginBottom(0);
        vBoxContatoTime.setSpacing(1);
        vBoxContatoTime.setFlexVflex("ftMin");
        vBoxContatoTime.setFlexHflex("ftTrue");
        vBoxContatoTime.setScrollable(false);
        vBoxContatoTime.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoTime.setBoxShadowConfigVerticalLength(10);
        vBoxContatoTime.setBoxShadowConfigBlurRadius(5);
        vBoxContatoTime.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoTime.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoTime.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha06.addChildren(vBoxContatoTime);
        vBoxContatoTime.applyProperties();
    }

    public TFLabel lblTime = new TFLabel();

    private void init_lblTime() {
        lblTime.setName("lblTime");
        lblTime.setLeft(0);
        lblTime.setTop(0);
        lblTime.setWidth(33);
        lblTime.setHeight(16);
        lblTime.setCaption("Time ");
        lblTime.setFontColor("clWindowText");
        lblTime.setFontSize(-13);
        lblTime.setFontName("Tahoma");
        lblTime.setFontStyle("[]");
        lblTime.setVerticalAlignment("taVerticalCenter");
        lblTime.setWordBreak(false);
        vBoxContatoTime.addChildren(lblTime);
        lblTime.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(17);
        FHBox6.setWidth(172);
        FHBox6.setHeight(31);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(3);
        FHBox6.setFlexVflex("ftMin");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxContatoTime.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFString edtTime = new TFString();

    private void init_edtTime() {
        edtTime.setName("edtTime");
        edtTime.setLeft(0);
        edtTime.setTop(0);
        edtTime.setWidth(110);
        edtTime.setHeight(24);
        edtTime.setHint("Time");
        edtTime.setTable(tbClienteContato);
        edtTime.setFieldName("TIME_ESPORTE");
        edtTime.setHelpCaption("Time");
        edtTime.setFlex(true);
        edtTime.setRequired(false);
        edtTime.setPrompt("Time");
        edtTime.setConstraintCheckWhen("cwImmediate");
        edtTime.setConstraintCheckType("ctExpression");
        edtTime.setConstraintFocusOnError(false);
        edtTime.setConstraintEnableUI(true);
        edtTime.setConstraintEnabled(false);
        edtTime.setConstraintFormCheck(true);
        edtTime.setCharCase("ccUpper");
        edtTime.setPwd(false);
        edtTime.setMaxlength(30);
        edtTime.setFontColor("clWindowText");
        edtTime.setFontSize(-13);
        edtTime.setFontName("Tahoma");
        edtTime.setFontStyle("[]");
        edtTime.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtTime", "OnEnter");
        });
        edtTime.setSaveLiteralCharacter(false);
        edtTime.applyProperties();
        FHBox6.addChildren(edtTime);
        addValidatable(edtTime);
    }

    public TFIconClass iconLimparTime = new TFIconClass();

    private void init_iconLimparTime() {
        iconLimparTime.setName("iconLimparTime");
        iconLimparTime.setLeft(110);
        iconLimparTime.setTop(0);
        iconLimparTime.setHint("Limpar Time");
        iconLimparTime.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconLimparTimeClick(event);
            processarFlow("FrmCadClienteContato", "iconLimparTime", "OnClick");
        });
        iconLimparTime.setIconClass("trash");
        iconLimparTime.setSize(25);
        iconLimparTime.setColor("clBlack");
        FHBox6.addChildren(iconLimparTime);
        iconLimparTime.applyProperties();
    }

    public TFVBox vBoxContatoHobby = new TFVBox();

    private void init_vBoxContatoHobby() {
        vBoxContatoHobby.setName("vBoxContatoHobby");
        vBoxContatoHobby.setLeft(181);
        vBoxContatoHobby.setTop(0);
        vBoxContatoHobby.setWidth(180);
        vBoxContatoHobby.setHeight(50);
        vBoxContatoHobby.setBorderStyle("stNone");
        vBoxContatoHobby.setPaddingTop(0);
        vBoxContatoHobby.setPaddingLeft(0);
        vBoxContatoHobby.setPaddingRight(0);
        vBoxContatoHobby.setPaddingBottom(0);
        vBoxContatoHobby.setMarginTop(0);
        vBoxContatoHobby.setMarginLeft(0);
        vBoxContatoHobby.setMarginRight(5);
        vBoxContatoHobby.setMarginBottom(0);
        vBoxContatoHobby.setSpacing(1);
        vBoxContatoHobby.setFlexVflex("ftMin");
        vBoxContatoHobby.setFlexHflex("ftTrue");
        vBoxContatoHobby.setScrollable(false);
        vBoxContatoHobby.setBoxShadowConfigHorizontalLength(10);
        vBoxContatoHobby.setBoxShadowConfigVerticalLength(10);
        vBoxContatoHobby.setBoxShadowConfigBlurRadius(5);
        vBoxContatoHobby.setBoxShadowConfigSpreadRadius(0);
        vBoxContatoHobby.setBoxShadowConfigShadowColor("clBlack");
        vBoxContatoHobby.setBoxShadowConfigOpacity(75);
        hBoxTbsCadastroLinha06.addChildren(vBoxContatoHobby);
        vBoxContatoHobby.applyProperties();
    }

    public TFLabel lblHobby = new TFLabel();

    private void init_lblHobby() {
        lblHobby.setName("lblHobby");
        lblHobby.setLeft(0);
        lblHobby.setTop(0);
        lblHobby.setWidth(39);
        lblHobby.setHeight(16);
        lblHobby.setCaption("Hobby ");
        lblHobby.setFontColor("clWindowText");
        lblHobby.setFontSize(-13);
        lblHobby.setFontName("Tahoma");
        lblHobby.setFontStyle("[]");
        lblHobby.setVerticalAlignment("taVerticalCenter");
        lblHobby.setWordBreak(false);
        vBoxContatoHobby.addChildren(lblHobby);
        lblHobby.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(17);
        FHBox7.setWidth(167);
        FHBox7.setHeight(26);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(3);
        FHBox7.setFlexVflex("ftMin");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vBoxContatoHobby.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFString edtHobby = new TFString();

    private void init_edtHobby() {
        edtHobby.setName("edtHobby");
        edtHobby.setLeft(0);
        edtHobby.setTop(0);
        edtHobby.setWidth(110);
        edtHobby.setHeight(24);
        edtHobby.setHint("Hobby");
        edtHobby.setTable(tbClienteContato);
        edtHobby.setFieldName("HOBBY");
        edtHobby.setHelpCaption("Hobby");
        edtHobby.setFlex(true);
        edtHobby.setRequired(false);
        edtHobby.setPrompt("Hobby");
        edtHobby.setConstraintCheckWhen("cwImmediate");
        edtHobby.setConstraintCheckType("ctExpression");
        edtHobby.setConstraintFocusOnError(false);
        edtHobby.setConstraintEnableUI(true);
        edtHobby.setConstraintEnabled(false);
        edtHobby.setConstraintFormCheck(true);
        edtHobby.setCharCase("ccUpper");
        edtHobby.setPwd(false);
        edtHobby.setMaxlength(30);
        edtHobby.setFontColor("clWindowText");
        edtHobby.setFontSize(-13);
        edtHobby.setFontName("Tahoma");
        edtHobby.setFontStyle("[]");
        edtHobby.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadClienteContato", "edtHobby", "OnEnter");
        });
        edtHobby.setSaveLiteralCharacter(false);
        edtHobby.applyProperties();
        FHBox7.addChildren(edtHobby);
        addValidatable(edtHobby);
    }

    public TFIconClass iconLimparHobby = new TFIconClass();

    private void init_iconLimparHobby() {
        iconLimparHobby.setName("iconLimparHobby");
        iconLimparHobby.setLeft(110);
        iconLimparHobby.setTop(0);
        iconLimparHobby.setHint("Limpar Hobby");
        iconLimparHobby.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconLimparHobbyClick(event);
            processarFlow("FrmCadClienteContato", "iconLimparHobby", "OnClick");
        });
        iconLimparHobby.setIconClass("trash");
        iconLimparHobby.setSize(25);
        iconLimparHobby.setColor("clBlack");
        FHBox7.addChildren(iconLimparHobby);
        iconLimparHobby.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void iconLimparPesquisaClick(final Event<Object> event);

    public void btnPesquisarContatoClick(final Event<Object> event) {
        if (btnPesquisarContato.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisarContato");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void icoAreaDeContatoClick(final Event<Object> event);

    public abstract void iconLimparFuncaoClick(final Event<Object> event);

    public abstract void edtCPFChange(final Event<Object> event);

    public abstract void icoWhatsappClick(final Event<Object> event);

    public abstract void icoLimparTelefoneWhatsappClick(final Event<Object> event);

    public abstract void icoLimparTelefoneResidencialClick(final Event<Object> event);

    public abstract void icoCelularClick(final Event<Object> event);

    public abstract void icoLimparTelefoneCelularClick(final Event<Object> event);

    public abstract void iconLimparTimeClick(final Event<Object> event);

    public abstract void iconLimparHobbyClick(final Event<Object> event);

}