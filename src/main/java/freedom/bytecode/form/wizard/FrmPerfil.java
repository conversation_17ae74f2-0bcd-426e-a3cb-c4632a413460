package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPerfil extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PerfilRNA rn = null;

    public FrmPerfil() {
        try {
            rn = (freedom.bytecode.rn.PerfilRNA) getRN(freedom.bytecode.rn.wizard.PerfilRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbPerfil();
        init_tbUsuarioFoto();
        init_tbSetorVenda();
        init_tbEmpresasUsuarios();
        init_tbLocalEstoque();
        init_tbProdutoSegmento();
        init_vBoxPerfil();
        init_FVBox1();
        init_lblNomeCompleto();
        init_FHBox1();
        init_FVBox2();
        init_FHBox5();
        init_FHBox6();
        init_FIconClass3();
        init_lblLogin();
        init_FHBox19();
        init_FHBox20();
        init_lblCapLogin();
        init_FHBox3();
        init_FHBox4();
        init_FIconClass2();
        init_lblFuncao();
        init_FHBox21();
        init_FHBox22();
        init_lblCapFuncao();
        init_FHBox2();
        init_FHBox23();
        init_FIconClass1();
        init_lblemail();
        init_FHBox24();
        init_FHBox40();
        init_FHBox25();
        init_lblCapEmail();
        init_hBoxEditarEmail();
        init_icoEditarEmail();
        init_FHBox44();
        init_FHBox51();
        init_iconRamalUsuario();
        init_FLabel8();
        init_FHBox52();
        init_FHBox53();
        init_lblRamalUsuario();
        init_lblImagePerfil();
        init_FVBox3();
        init_FHBox26();
        init_FVBox4();
        init_FHBox7();
        init_FHBox8();
        init_FIconClass4();
        init_lblEmpresa();
        init_FHBox27();
        init_FHBox28();
        init_lblCapEmpresa();
        init_FHBox9();
        init_FHBox10();
        init_FIconClass5();
        init_lblDepartamento();
        init_FHBox29();
        init_FHBox30();
        init_lblCapDepartamento();
        init_FHBox11();
        init_FHBox12();
        init_FIconClass6();
        init_lblDivisao();
        init_FHBox31();
        init_FHBox32();
        init_lblCapDivisao();
        init_FHBox36();
        init_FHBox37();
        init_FIconClass9();
        init_FLabel4();
        init_FHBox38();
        init_FHBox39();
        init_lblmarca();
        init_FVBox5();
        init_FHBox15();
        init_FHBox16();
        init_FIconClass8();
        init_FLabel2();
        init_FHBox17();
        init_FHBox18();
        init_iconAltTabPreco();
        init_FHBox33();
        init_FHBox34();
        init_FLabel3();
        init_FHBox13();
        init_FHBox14();
        init_FIconClass7();
        init_FLabel1();
        init_FHBox35();
        init_cmbSetorVenda();
        init_FHBox41();
        init_FHBox42();
        init_FIconClass11();
        init_FLabel5();
        init_FHBox43();
        init_cmbSegmento();
        init_FHBox48();
        init_FHBox49();
        init_FIconClass13();
        init_FLabel6();
        init_FHBox50();
        init_cmbLocalEstoque();
        init_vBoxAssinaturaUsadaRecepcao();
        init_FHBox46();
        init_FHBox47();
        init_FIconClass12();
        init_FLabel7();
        init_signature();
        init_vBoxImgSignatrueChk();
        init_imgSignatureChk();
        init_FHBox45();
        init_FVBox7();
        init_btnDesfazer();
        init_FVBox8();
        init_btnLimpar();
        init_FVBox9();
        init_btnSalvar();
        init_sc();
        init_FrmPerfil();
    }

    public PERFIL tbPerfil;

    private void init_tbPerfil() {
        tbPerfil = rn.tbPerfil;
        tbPerfil.setName("tbPerfil");
        tbPerfil.setMaxRowCount(200);
        tbPerfil.setWKey("7000145;70001");
        tbPerfil.setRatioBatchSize(20);
        getTables().put(tbPerfil, "tbPerfil");
        tbPerfil.applyProperties();
    }

    public USUARIO_FOTO tbUsuarioFoto;

    private void init_tbUsuarioFoto() {
        tbUsuarioFoto = rn.tbUsuarioFoto;
        tbUsuarioFoto.setName("tbUsuarioFoto");
        tbUsuarioFoto.setMaxRowCount(200);
        tbUsuarioFoto.setWKey("7000145;70002");
        tbUsuarioFoto.setRatioBatchSize(20);
        getTables().put(tbUsuarioFoto, "tbUsuarioFoto");
        tbUsuarioFoto.applyProperties();
    }

    public SETOR_VENDA tbSetorVenda;

    private void init_tbSetorVenda() {
        tbSetorVenda = rn.tbSetorVenda;
        tbSetorVenda.setName("tbSetorVenda");
        tbSetorVenda.setMaxRowCount(200);
        tbSetorVenda.setWKey("7000145;31001");
        tbSetorVenda.setRatioBatchSize(20);
        getTables().put(tbSetorVenda, "tbSetorVenda");
        tbSetorVenda.applyProperties();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("7000145;46001");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public LOCAL_ESTOQUE tbLocalEstoque;

    private void init_tbLocalEstoque() {
        tbLocalEstoque = rn.tbLocalEstoque;
        tbLocalEstoque.setName("tbLocalEstoque");
        tbLocalEstoque.setMaxRowCount(200);
        tbLocalEstoque.setWKey("7000145;53003");
        tbLocalEstoque.setRatioBatchSize(20);
        getTables().put(tbLocalEstoque, "tbLocalEstoque");
        tbLocalEstoque.applyProperties();
    }

    public PRODUTO_SEGMENTO tbProdutoSegmento;

    private void init_tbProdutoSegmento() {
        tbProdutoSegmento = rn.tbProdutoSegmento;
        tbProdutoSegmento.setName("tbProdutoSegmento");
        tbProdutoSegmento.setMaxRowCount(200);
        tbProdutoSegmento.setWKey("7000145;46005");
        tbProdutoSegmento.setRatioBatchSize(20);
        getTables().put(tbProdutoSegmento, "tbProdutoSegmento");
        tbProdutoSegmento.applyProperties();
    }

    protected TFForm FrmPerfil = this;
    private void init_FrmPerfil() {
        FrmPerfil.setName("FrmPerfil");
        FrmPerfil.setCaption("Perfil");
        FrmPerfil.setClientHeight(662);
        FrmPerfil.setClientWidth(829);
        FrmPerfil.setColor("clBtnFace");
        FrmPerfil.setWKey("7000145");
        FrmPerfil.setSpacing(0);
        FrmPerfil.applyProperties();
    }

    public TFVBox vBoxPerfil = new TFVBox();

    private void init_vBoxPerfil() {
        vBoxPerfil.setName("vBoxPerfil");
        vBoxPerfil.setLeft(0);
        vBoxPerfil.setTop(0);
        vBoxPerfil.setWidth(829);
        vBoxPerfil.setHeight(662);
        vBoxPerfil.setAlign("alClient");
        vBoxPerfil.setBorderStyle("stNone");
        vBoxPerfil.setPaddingTop(10);
        vBoxPerfil.setPaddingLeft(20);
        vBoxPerfil.setPaddingRight(0);
        vBoxPerfil.setPaddingBottom(0);
        vBoxPerfil.setMarginTop(0);
        vBoxPerfil.setMarginLeft(0);
        vBoxPerfil.setMarginRight(0);
        vBoxPerfil.setMarginBottom(0);
        vBoxPerfil.setSpacing(1);
        vBoxPerfil.setFlexVflex("ftTrue");
        vBoxPerfil.setFlexHflex("ftTrue");
        vBoxPerfil.setScrollable(true);
        vBoxPerfil.setBoxShadowConfigHorizontalLength(10);
        vBoxPerfil.setBoxShadowConfigVerticalLength(10);
        vBoxPerfil.setBoxShadowConfigBlurRadius(5);
        vBoxPerfil.setBoxShadowConfigSpreadRadius(0);
        vBoxPerfil.setBoxShadowConfigShadowColor("clBlack");
        vBoxPerfil.setBoxShadowConfigOpacity(75);
        FrmPerfil.addChildren(vBoxPerfil);
        vBoxPerfil.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(808);
        FVBox1.setHeight(225);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        vBoxPerfil.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel lblNomeCompleto = new TFLabel();

    private void init_lblNomeCompleto() {
        lblNomeCompleto.setName("lblNomeCompleto");
        lblNomeCompleto.setLeft(0);
        lblNomeCompleto.setTop(0);
        lblNomeCompleto.setWidth(145);
        lblNomeCompleto.setHeight(19);
        lblNomeCompleto.setCaption("lblNomeCompleto");
        lblNomeCompleto.setFontColor("clNavy");
        lblNomeCompleto.setFontSize(-16);
        lblNomeCompleto.setFontName("Tahoma");
        lblNomeCompleto.setFontStyle("[fsBold]");
        lblNomeCompleto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblNomeCompletoClick(event);
            processarFlow("FrmPerfil", "lblNomeCompleto", "OnClick");
        });
        lblNomeCompleto.setFieldName("NOME_COMPLETO");
        lblNomeCompleto.setTable(tbPerfil);
        lblNomeCompleto.setVerticalAlignment("taAlignBottom");
        lblNomeCompleto.setWordBreak(false);
        FVBox1.addChildren(lblNomeCompleto);
        lblNomeCompleto.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(20);
        FHBox1.setWidth(591);
        FHBox1.setHeight(201);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(373);
        FVBox2.setHeight(197);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(20);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(158);
        FHBox5.setHeight(18);
        FHBox5.setAlign("alLeft");
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox2.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(16);
        FHBox6.setHeight(21);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(2);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(0);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox5.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFIconClass FIconClass3 = new TFIconClass();

    private void init_FIconClass3() {
        FIconClass3.setName("FIconClass3");
        FIconClass3.setLeft(0);
        FIconClass3.setTop(0);
        FIconClass3.setIconClass("user");
        FIconClass3.setSize(16);
        FIconClass3.setColor("clBlack");
        FHBox6.addChildren(FIconClass3);
        FIconClass3.applyProperties();
    }

    public TFLabel lblLogin = new TFLabel();

    private void init_lblLogin() {
        lblLogin.setName("lblLogin");
        lblLogin.setLeft(16);
        lblLogin.setTop(0);
        lblLogin.setWidth(34);
        lblLogin.setHeight(16);
        lblLogin.setAlign("alLeft");
        lblLogin.setCaption("Login");
        lblLogin.setFontColor("clWindowText");
        lblLogin.setFontSize(-13);
        lblLogin.setFontName("Tahoma");
        lblLogin.setFontStyle("[fsBold]");
        lblLogin.setVerticalAlignment("taAlignBottom");
        lblLogin.setWordBreak(false);
        FHBox5.addChildren(lblLogin);
        lblLogin.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(19);
        FHBox19.setWidth(158);
        FHBox19.setHeight(20);
        FHBox19.setAlign("alLeft");
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftFalse");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FVBox2.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(0);
        FHBox20.setTop(0);
        FHBox20.setWidth(16);
        FHBox20.setHeight(13);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(0);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftFalse");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FHBox19.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFLabel lblCapLogin = new TFLabel();

    private void init_lblCapLogin() {
        lblCapLogin.setName("lblCapLogin");
        lblCapLogin.setLeft(16);
        lblCapLogin.setTop(0);
        lblCapLogin.setWidth(65);
        lblCapLogin.setHeight(16);
        lblCapLogin.setCaption("lblCapLogin");
        lblCapLogin.setFontColor("clWindowText");
        lblCapLogin.setFontSize(-13);
        lblCapLogin.setFontName("Tahoma");
        lblCapLogin.setFontStyle("[]");
        lblCapLogin.setFieldName("NOME");
        lblCapLogin.setTable(tbPerfil);
        lblCapLogin.setVerticalAlignment("taAlignTop");
        lblCapLogin.setWordBreak(false);
        FHBox19.addChildren(lblCapLogin);
        lblCapLogin.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(40);
        FHBox3.setWidth(158);
        FHBox3.setHeight(20);
        FHBox3.setAlign("alLeft");
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftTrue");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox2.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(16);
        FHBox4.setHeight(21);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(0);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox3.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFIconClass FIconClass2 = new TFIconClass();

    private void init_FIconClass2() {
        FIconClass2.setName("FIconClass2");
        FIconClass2.setLeft(0);
        FIconClass2.setTop(0);
        FIconClass2.setIconClass("credit-card");
        FIconClass2.setSize(12);
        FIconClass2.setColor("clBlack");
        FHBox4.addChildren(FIconClass2);
        FIconClass2.applyProperties();
    }

    public TFLabel lblFuncao = new TFLabel();

    private void init_lblFuncao() {
        lblFuncao.setName("lblFuncao");
        lblFuncao.setLeft(16);
        lblFuncao.setTop(0);
        lblFuncao.setWidth(45);
        lblFuncao.setHeight(16);
        lblFuncao.setAlign("alLeft");
        lblFuncao.setCaption("Fun\u00E7\u00E3o");
        lblFuncao.setFontColor("clWindowText");
        lblFuncao.setFontSize(-13);
        lblFuncao.setFontName("Tahoma");
        lblFuncao.setFontStyle("[fsBold]");
        lblFuncao.setFieldName("CPF");
        lblFuncao.setVerticalAlignment("taAlignBottom");
        lblFuncao.setWordBreak(false);
        FHBox3.addChildren(lblFuncao);
        lblFuncao.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(61);
        FHBox21.setWidth(158);
        FHBox21.setHeight(25);
        FHBox21.setAlign("alLeft");
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftFalse");
        FHBox21.setFlexHflex("ftFalse");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        FVBox2.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(0);
        FHBox22.setTop(0);
        FHBox22.setWidth(16);
        FHBox22.setHeight(13);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(0);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftFalse");
        FHBox22.setFlexHflex("ftFalse");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        FHBox21.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFLabel lblCapFuncao = new TFLabel();

    private void init_lblCapFuncao() {
        lblCapFuncao.setName("lblCapFuncao");
        lblCapFuncao.setLeft(16);
        lblCapFuncao.setTop(0);
        lblCapFuncao.setWidth(76);
        lblCapFuncao.setHeight(16);
        lblCapFuncao.setAlign("alLeft");
        lblCapFuncao.setCaption("lblCapFuncao");
        lblCapFuncao.setFontColor("clWindowText");
        lblCapFuncao.setFontSize(-13);
        lblCapFuncao.setFontName("Tahoma");
        lblCapFuncao.setFontStyle("[]");
        lblCapFuncao.setFieldName("FUNCAO");
        lblCapFuncao.setTable(tbPerfil);
        lblCapFuncao.setVerticalAlignment("taAlignTop");
        lblCapFuncao.setWordBreak(false);
        FHBox21.addChildren(lblCapFuncao);
        lblCapFuncao.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(87);
        FHBox2.setWidth(159);
        FHBox2.setHeight(24);
        FHBox2.setAlign("alLeft");
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox2.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(0);
        FHBox23.setTop(0);
        FHBox23.setWidth(17);
        FHBox23.setHeight(21);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(2);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(0);
        FHBox23.setFlexVflex("ftFalse");
        FHBox23.setFlexHflex("ftFalse");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        FHBox2.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFIconClass FIconClass1 = new TFIconClass();

    private void init_FIconClass1() {
        FIconClass1.setName("FIconClass1");
        FIconClass1.setLeft(0);
        FIconClass1.setTop(0);
        FIconClass1.setIconClass("envelope");
        FIconClass1.setSize(16);
        FIconClass1.setColor("clBlack");
        FHBox23.addChildren(FIconClass1);
        FIconClass1.applyProperties();
    }

    public TFLabel lblemail = new TFLabel();

    private void init_lblemail() {
        lblemail.setName("lblemail");
        lblemail.setLeft(17);
        lblemail.setTop(0);
        lblemail.setWidth(32);
        lblemail.setHeight(16);
        lblemail.setAlign("alLeft");
        lblemail.setCaption("Email");
        lblemail.setFontColor("clWindowText");
        lblemail.setFontSize(-13);
        lblemail.setFontName("Tahoma");
        lblemail.setFontStyle("[fsBold]");
        lblemail.setVerticalAlignment("taAlignBottom");
        lblemail.setWordBreak(false);
        FHBox2.addChildren(lblemail);
        lblemail.applyProperties();
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(0);
        FHBox24.setTop(112);
        FHBox24.setWidth(158);
        FHBox24.setHeight(25);
        FHBox24.setAlign("alLeft");
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(0);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(1);
        FHBox24.setFlexVflex("ftFalse");
        FHBox24.setFlexHflex("ftTrue");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvTop");
        FVBox2.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFHBox FHBox40 = new TFHBox();

    private void init_FHBox40() {
        FHBox40.setName("FHBox40");
        FHBox40.setLeft(0);
        FHBox40.setTop(0);
        FHBox40.setWidth(114);
        FHBox40.setHeight(23);
        FHBox40.setBorderStyle("stNone");
        FHBox40.setPaddingTop(3);
        FHBox40.setPaddingLeft(0);
        FHBox40.setPaddingRight(0);
        FHBox40.setPaddingBottom(0);
        FHBox40.setMarginTop(0);
        FHBox40.setMarginLeft(0);
        FHBox40.setMarginRight(0);
        FHBox40.setMarginBottom(0);
        FHBox40.setSpacing(1);
        FHBox40.setFlexVflex("ftTrue");
        FHBox40.setFlexHflex("ftMin");
        FHBox40.setScrollable(false);
        FHBox40.setBoxShadowConfigHorizontalLength(10);
        FHBox40.setBoxShadowConfigVerticalLength(10);
        FHBox40.setBoxShadowConfigBlurRadius(5);
        FHBox40.setBoxShadowConfigSpreadRadius(0);
        FHBox40.setBoxShadowConfigShadowColor("clBlack");
        FHBox40.setBoxShadowConfigOpacity(75);
        FHBox40.setVAlign("tvTop");
        FHBox24.addChildren(FHBox40);
        FHBox40.applyProperties();
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(0);
        FHBox25.setTop(0);
        FHBox25.setWidth(16);
        FHBox25.setHeight(13);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(0);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(0);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(1);
        FHBox25.setFlexVflex("ftFalse");
        FHBox25.setFlexHflex("ftFalse");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvTop");
        FHBox40.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFLabel lblCapEmail = new TFLabel();

    private void init_lblCapEmail() {
        lblCapEmail.setName("lblCapEmail");
        lblCapEmail.setLeft(16);
        lblCapEmail.setTop(0);
        lblCapEmail.setWidth(66);
        lblCapEmail.setHeight(16);
        lblCapEmail.setAlign("alLeft");
        lblCapEmail.setCaption("lblCapEmail");
        lblCapEmail.setFontColor("clWindowText");
        lblCapEmail.setFontSize(-13);
        lblCapEmail.setFontName("Tahoma");
        lblCapEmail.setFontStyle("[]");
        lblCapEmail.setFieldName("EMAIL");
        lblCapEmail.setTable(tbPerfil);
        lblCapEmail.setVerticalAlignment("taAlignTop");
        lblCapEmail.setWordBreak(false);
        FHBox40.addChildren(lblCapEmail);
        lblCapEmail.applyProperties();
    }

    public TFHBox hBoxEditarEmail = new TFHBox();

    private void init_hBoxEditarEmail() {
        hBoxEditarEmail.setName("hBoxEditarEmail");
        hBoxEditarEmail.setLeft(114);
        hBoxEditarEmail.setTop(0);
        hBoxEditarEmail.setWidth(44);
        hBoxEditarEmail.setHeight(40);
        hBoxEditarEmail.setBorderStyle("stNone");
        hBoxEditarEmail.setPaddingTop(0);
        hBoxEditarEmail.setPaddingLeft(5);
        hBoxEditarEmail.setPaddingRight(0);
        hBoxEditarEmail.setPaddingBottom(0);
        hBoxEditarEmail.setMarginTop(0);
        hBoxEditarEmail.setMarginLeft(0);
        hBoxEditarEmail.setMarginRight(0);
        hBoxEditarEmail.setMarginBottom(0);
        hBoxEditarEmail.setSpacing(1);
        hBoxEditarEmail.setFlexVflex("ftFalse");
        hBoxEditarEmail.setFlexHflex("ftFalse");
        hBoxEditarEmail.setScrollable(false);
        hBoxEditarEmail.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarEmail.setBoxShadowConfigVerticalLength(10);
        hBoxEditarEmail.setBoxShadowConfigBlurRadius(5);
        hBoxEditarEmail.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarEmail.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarEmail.setBoxShadowConfigOpacity(75);
        hBoxEditarEmail.setVAlign("tvTop");
        FHBox24.addChildren(hBoxEditarEmail);
        hBoxEditarEmail.applyProperties();
    }

    public TFIconClass icoEditarEmail = new TFIconClass();

    private void init_icoEditarEmail() {
        icoEditarEmail.setName("icoEditarEmail");
        icoEditarEmail.setLeft(0);
        icoEditarEmail.setTop(0);
        icoEditarEmail.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            icoEditarEmailClick(event);
            processarFlow("FrmPerfil", "icoEditarEmail", "OnClick");
        });
        icoEditarEmail.setIconClass("edit");
        icoEditarEmail.setSize(30);
        icoEditarEmail.setColor("clBlack");
        hBoxEditarEmail.addChildren(icoEditarEmail);
        icoEditarEmail.applyProperties();
    }

    public TFHBox FHBox44 = new TFHBox();

    private void init_FHBox44() {
        FHBox44.setName("FHBox44");
        FHBox44.setLeft(0);
        FHBox44.setTop(138);
        FHBox44.setWidth(160);
        FHBox44.setHeight(30);
        FHBox44.setAlign("alLeft");
        FHBox44.setBorderStyle("stNone");
        FHBox44.setPaddingTop(3);
        FHBox44.setPaddingLeft(0);
        FHBox44.setPaddingRight(0);
        FHBox44.setPaddingBottom(0);
        FHBox44.setMarginTop(4);
        FHBox44.setMarginLeft(0);
        FHBox44.setMarginRight(0);
        FHBox44.setMarginBottom(0);
        FHBox44.setSpacing(1);
        FHBox44.setFlexVflex("ftTrue");
        FHBox44.setFlexHflex("ftTrue");
        FHBox44.setScrollable(false);
        FHBox44.setBoxShadowConfigHorizontalLength(10);
        FHBox44.setBoxShadowConfigVerticalLength(10);
        FHBox44.setBoxShadowConfigBlurRadius(5);
        FHBox44.setBoxShadowConfigSpreadRadius(0);
        FHBox44.setBoxShadowConfigShadowColor("clBlack");
        FHBox44.setBoxShadowConfigOpacity(75);
        FHBox44.setVAlign("tvTop");
        FVBox2.addChildren(FHBox44);
        FHBox44.applyProperties();
    }

    public TFHBox FHBox51 = new TFHBox();

    private void init_FHBox51() {
        FHBox51.setName("FHBox51");
        FHBox51.setLeft(0);
        FHBox51.setTop(0);
        FHBox51.setWidth(16);
        FHBox51.setHeight(21);
        FHBox51.setBorderStyle("stNone");
        FHBox51.setPaddingTop(0);
        FHBox51.setPaddingLeft(0);
        FHBox51.setPaddingRight(0);
        FHBox51.setPaddingBottom(0);
        FHBox51.setMarginTop(0);
        FHBox51.setMarginLeft(0);
        FHBox51.setMarginRight(0);
        FHBox51.setMarginBottom(0);
        FHBox51.setSpacing(0);
        FHBox51.setFlexVflex("ftFalse");
        FHBox51.setFlexHflex("ftFalse");
        FHBox51.setScrollable(false);
        FHBox51.setBoxShadowConfigHorizontalLength(10);
        FHBox51.setBoxShadowConfigVerticalLength(10);
        FHBox51.setBoxShadowConfigBlurRadius(5);
        FHBox51.setBoxShadowConfigSpreadRadius(0);
        FHBox51.setBoxShadowConfigShadowColor("clBlack");
        FHBox51.setBoxShadowConfigOpacity(75);
        FHBox51.setVAlign("tvTop");
        FHBox44.addChildren(FHBox51);
        FHBox51.applyProperties();
    }

    public TFIconClass iconRamalUsuario = new TFIconClass();

    private void init_iconRamalUsuario() {
        iconRamalUsuario.setName("iconRamalUsuario");
        iconRamalUsuario.setLeft(0);
        iconRamalUsuario.setTop(0);
        iconRamalUsuario.setIconClass("phone");
        iconRamalUsuario.setSize(14);
        iconRamalUsuario.setColor("clBlack");
        FHBox51.addChildren(iconRamalUsuario);
        iconRamalUsuario.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(16);
        FLabel8.setTop(0);
        FLabel8.setWidth(39);
        FLabel8.setHeight(16);
        FLabel8.setAlign("alLeft");
        FLabel8.setCaption("Ramal");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-13);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[fsBold]");
        FLabel8.setVerticalAlignment("taAlignBottom");
        FLabel8.setWordBreak(false);
        FHBox44.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFHBox FHBox52 = new TFHBox();

    private void init_FHBox52() {
        FHBox52.setName("FHBox52");
        FHBox52.setLeft(0);
        FHBox52.setTop(169);
        FHBox52.setWidth(158);
        FHBox52.setHeight(25);
        FHBox52.setAlign("alLeft");
        FHBox52.setBorderStyle("stNone");
        FHBox52.setPaddingTop(0);
        FHBox52.setPaddingLeft(0);
        FHBox52.setPaddingRight(0);
        FHBox52.setPaddingBottom(0);
        FHBox52.setMarginTop(0);
        FHBox52.setMarginLeft(0);
        FHBox52.setMarginRight(0);
        FHBox52.setMarginBottom(0);
        FHBox52.setSpacing(1);
        FHBox52.setFlexVflex("ftFalse");
        FHBox52.setFlexHflex("ftFalse");
        FHBox52.setScrollable(false);
        FHBox52.setBoxShadowConfigHorizontalLength(10);
        FHBox52.setBoxShadowConfigVerticalLength(10);
        FHBox52.setBoxShadowConfigBlurRadius(5);
        FHBox52.setBoxShadowConfigSpreadRadius(0);
        FHBox52.setBoxShadowConfigShadowColor("clBlack");
        FHBox52.setBoxShadowConfigOpacity(75);
        FHBox52.setVAlign("tvTop");
        FVBox2.addChildren(FHBox52);
        FHBox52.applyProperties();
    }

    public TFHBox FHBox53 = new TFHBox();

    private void init_FHBox53() {
        FHBox53.setName("FHBox53");
        FHBox53.setLeft(0);
        FHBox53.setTop(0);
        FHBox53.setWidth(16);
        FHBox53.setHeight(13);
        FHBox53.setBorderStyle("stNone");
        FHBox53.setPaddingTop(0);
        FHBox53.setPaddingLeft(0);
        FHBox53.setPaddingRight(0);
        FHBox53.setPaddingBottom(0);
        FHBox53.setMarginTop(0);
        FHBox53.setMarginLeft(0);
        FHBox53.setMarginRight(0);
        FHBox53.setMarginBottom(0);
        FHBox53.setSpacing(1);
        FHBox53.setFlexVflex("ftFalse");
        FHBox53.setFlexHflex("ftFalse");
        FHBox53.setScrollable(false);
        FHBox53.setBoxShadowConfigHorizontalLength(10);
        FHBox53.setBoxShadowConfigVerticalLength(10);
        FHBox53.setBoxShadowConfigBlurRadius(5);
        FHBox53.setBoxShadowConfigSpreadRadius(0);
        FHBox53.setBoxShadowConfigShadowColor("clBlack");
        FHBox53.setBoxShadowConfigOpacity(75);
        FHBox53.setVAlign("tvTop");
        FHBox52.addChildren(FHBox53);
        FHBox53.applyProperties();
    }

    public TFLabel lblRamalUsuario = new TFLabel();

    private void init_lblRamalUsuario() {
        lblRamalUsuario.setName("lblRamalUsuario");
        lblRamalUsuario.setLeft(16);
        lblRamalUsuario.setTop(0);
        lblRamalUsuario.setWidth(92);
        lblRamalUsuario.setHeight(16);
        lblRamalUsuario.setAlign("alLeft");
        lblRamalUsuario.setCaption("lblRamalUsuario");
        lblRamalUsuario.setFontColor("clWindowText");
        lblRamalUsuario.setFontSize(-13);
        lblRamalUsuario.setFontName("Tahoma");
        lblRamalUsuario.setFontStyle("[]");
        lblRamalUsuario.setFieldName("RAMAL");
        lblRamalUsuario.setTable(tbPerfil);
        lblRamalUsuario.setVerticalAlignment("taAlignTop");
        lblRamalUsuario.setWordBreak(false);
        FHBox52.addChildren(lblRamalUsuario);
        lblRamalUsuario.applyProperties();
    }

    public TFImage lblImagePerfil = new TFImage();

    private void init_lblImagePerfil() {
        lblImagePerfil.setName("lblImagePerfil");
        lblImagePerfil.setLeft(373);
        lblImagePerfil.setTop(0);
        lblImagePerfil.setWidth(156);
        lblImagePerfil.setHeight(148);
        lblImagePerfil.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblImagePerfilClick(event);
            processarFlow("FrmPerfil", "lblImagePerfil", "OnClick");
        });
        lblImagePerfil.setTable(tbPerfil);
        lblImagePerfil.setFieldName("FOTO_ICONE");
        lblImagePerfil.setBoxSize(0);
        lblImagePerfil.setGrayScaleOnDisable(false);
        lblImagePerfil.setFlexVflex("ftFalse");
        lblImagePerfil.setFlexHflex("ftFalse");
        FHBox1.addChildren(lblImagePerfil);
        lblImagePerfil.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(226);
        FVBox3.setWidth(808);
        FVBox3.setHeight(485);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(true);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        vBoxPerfil.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(0);
        FHBox26.setTop(0);
        FHBox26.setWidth(591);
        FHBox26.setHeight(241);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(5);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(0);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(1);
        FHBox26.setFlexVflex("ftFalse");
        FHBox26.setFlexHflex("ftFalse");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvTop");
        FVBox3.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(350);
        FVBox4.setHeight(226);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox26.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(160);
        FHBox7.setHeight(15);
        FHBox7.setAlign("alLeft");
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftTrue");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox4.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(16);
        FHBox8.setHeight(21);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(0);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox7.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFIconClass FIconClass4 = new TFIconClass();

    private void init_FIconClass4() {
        FIconClass4.setName("FIconClass4");
        FIconClass4.setLeft(0);
        FIconClass4.setTop(0);
        FIconClass4.setIconClass("building-o");
        FIconClass4.setSize(12);
        FIconClass4.setColor("clBlack");
        FHBox8.addChildren(FIconClass4);
        FIconClass4.applyProperties();
    }

    public TFLabel lblEmpresa = new TFLabel();

    private void init_lblEmpresa() {
        lblEmpresa.setName("lblEmpresa");
        lblEmpresa.setLeft(16);
        lblEmpresa.setTop(0);
        lblEmpresa.setWidth(55);
        lblEmpresa.setHeight(16);
        lblEmpresa.setAlign("alLeft");
        lblEmpresa.setCaption("Empresa");
        lblEmpresa.setFontColor("clWindowText");
        lblEmpresa.setFontSize(-13);
        lblEmpresa.setFontName("Tahoma");
        lblEmpresa.setFontStyle("[fsBold]");
        lblEmpresa.setVerticalAlignment("taAlignBottom");
        lblEmpresa.setWordBreak(false);
        FHBox7.addChildren(lblEmpresa);
        lblEmpresa.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(0);
        FHBox27.setTop(16);
        FHBox27.setWidth(158);
        FHBox27.setHeight(25);
        FHBox27.setAlign("alLeft");
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(0);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(1);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftFalse");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        FVBox4.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(0);
        FHBox28.setTop(0);
        FHBox28.setWidth(16);
        FHBox28.setHeight(13);
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(0);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(0);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(1);
        FHBox28.setFlexVflex("ftFalse");
        FHBox28.setFlexHflex("ftFalse");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvTop");
        FHBox27.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFLabel lblCapEmpresa = new TFLabel();

    private void init_lblCapEmpresa() {
        lblCapEmpresa.setName("lblCapEmpresa");
        lblCapEmpresa.setLeft(16);
        lblCapEmpresa.setTop(0);
        lblCapEmpresa.setWidth(85);
        lblCapEmpresa.setHeight(16);
        lblCapEmpresa.setAlign("alLeft");
        lblCapEmpresa.setCaption("lblCapEmpresa");
        lblCapEmpresa.setFontColor("clWindowText");
        lblCapEmpresa.setFontSize(-13);
        lblCapEmpresa.setFontName("Tahoma");
        lblCapEmpresa.setFontStyle("[]");
        lblCapEmpresa.setFieldName("EMPRESA");
        lblCapEmpresa.setTable(tbPerfil);
        lblCapEmpresa.setVerticalAlignment("taAlignTop");
        lblCapEmpresa.setWordBreak(false);
        FHBox27.addChildren(lblCapEmpresa);
        lblCapEmpresa.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(42);
        FHBox9.setWidth(159);
        FHBox9.setHeight(15);
        FHBox9.setAlign("alLeft");
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftTrue");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBackgroundImage(" ");
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox4.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(16);
        FHBox10.setHeight(21);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(0);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox9.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFIconClass FIconClass5 = new TFIconClass();

    private void init_FIconClass5() {
        FIconClass5.setName("FIconClass5");
        FIconClass5.setLeft(0);
        FIconClass5.setTop(0);
        FIconClass5.setIconClass("archive");
        FIconClass5.setSize(12);
        FIconClass5.setColor("clBlack");
        FHBox10.addChildren(FIconClass5);
        FIconClass5.applyProperties();
    }

    public TFLabel lblDepartamento = new TFLabel();

    private void init_lblDepartamento() {
        lblDepartamento.setName("lblDepartamento");
        lblDepartamento.setLeft(16);
        lblDepartamento.setTop(0);
        lblDepartamento.setWidth(94);
        lblDepartamento.setHeight(16);
        lblDepartamento.setAlign("alLeft");
        lblDepartamento.setCaption("Departamento");
        lblDepartamento.setFontColor("clWindowText");
        lblDepartamento.setFontSize(-13);
        lblDepartamento.setFontName("Tahoma");
        lblDepartamento.setFontStyle("[fsBold]");
        lblDepartamento.setVerticalAlignment("taAlignBottom");
        lblDepartamento.setWordBreak(false);
        FHBox9.addChildren(lblDepartamento);
        lblDepartamento.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(58);
        FHBox29.setWidth(158);
        FHBox29.setHeight(25);
        FHBox29.setAlign("alLeft");
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(1);
        FHBox29.setFlexVflex("ftFalse");
        FHBox29.setFlexHflex("ftFalse");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        FVBox4.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFHBox FHBox30 = new TFHBox();

    private void init_FHBox30() {
        FHBox30.setName("FHBox30");
        FHBox30.setLeft(0);
        FHBox30.setTop(0);
        FHBox30.setWidth(16);
        FHBox30.setHeight(13);
        FHBox30.setBorderStyle("stNone");
        FHBox30.setPaddingTop(0);
        FHBox30.setPaddingLeft(0);
        FHBox30.setPaddingRight(0);
        FHBox30.setPaddingBottom(0);
        FHBox30.setMarginTop(0);
        FHBox30.setMarginLeft(0);
        FHBox30.setMarginRight(0);
        FHBox30.setMarginBottom(0);
        FHBox30.setSpacing(1);
        FHBox30.setFlexVflex("ftFalse");
        FHBox30.setFlexHflex("ftFalse");
        FHBox30.setScrollable(false);
        FHBox30.setBoxShadowConfigHorizontalLength(10);
        FHBox30.setBoxShadowConfigVerticalLength(10);
        FHBox30.setBoxShadowConfigBlurRadius(5);
        FHBox30.setBoxShadowConfigSpreadRadius(0);
        FHBox30.setBoxShadowConfigShadowColor("clBlack");
        FHBox30.setBoxShadowConfigOpacity(75);
        FHBox30.setVAlign("tvTop");
        FHBox29.addChildren(FHBox30);
        FHBox30.applyProperties();
    }

    public TFLabel lblCapDepartamento = new TFLabel();

    private void init_lblCapDepartamento() {
        lblCapDepartamento.setName("lblCapDepartamento");
        lblCapDepartamento.setLeft(16);
        lblCapDepartamento.setTop(0);
        lblCapDepartamento.setWidth(116);
        lblCapDepartamento.setHeight(16);
        lblCapDepartamento.setAlign("alLeft");
        lblCapDepartamento.setCaption("lblCapDepartamento");
        lblCapDepartamento.setFontColor("clWindowText");
        lblCapDepartamento.setFontSize(-13);
        lblCapDepartamento.setFontName("Tahoma");
        lblCapDepartamento.setFontStyle("[]");
        lblCapDepartamento.setFieldName("DEPARTAMENTO");
        lblCapDepartamento.setTable(tbPerfil);
        lblCapDepartamento.setVerticalAlignment("taAlignTop");
        lblCapDepartamento.setWordBreak(false);
        FHBox29.addChildren(lblCapDepartamento);
        lblCapDepartamento.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(84);
        FHBox11.setWidth(158);
        FHBox11.setHeight(15);
        FHBox11.setAlign("alLeft");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftTrue");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox4.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(0);
        FHBox12.setWidth(16);
        FHBox12.setHeight(21);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(0);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FHBox11.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFIconClass FIconClass6 = new TFIconClass();

    private void init_FIconClass6() {
        FIconClass6.setName("FIconClass6");
        FIconClass6.setLeft(0);
        FIconClass6.setTop(0);
        FIconClass6.setIconClass("trello");
        FIconClass6.setSize(12);
        FIconClass6.setColor("clBlack");
        FHBox12.addChildren(FIconClass6);
        FIconClass6.applyProperties();
    }

    public TFLabel lblDivisao = new TFLabel();

    private void init_lblDivisao() {
        lblDivisao.setName("lblDivisao");
        lblDivisao.setLeft(16);
        lblDivisao.setTop(0);
        lblDivisao.setWidth(46);
        lblDivisao.setHeight(16);
        lblDivisao.setAlign("alLeft");
        lblDivisao.setCaption("Divis\u00E3o");
        lblDivisao.setFontColor("clWindowText");
        lblDivisao.setFontSize(-13);
        lblDivisao.setFontName("Tahoma");
        lblDivisao.setFontStyle("[fsBold]");
        lblDivisao.setVerticalAlignment("taAlignBottom");
        lblDivisao.setWordBreak(false);
        FHBox11.addChildren(lblDivisao);
        lblDivisao.applyProperties();
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(0);
        FHBox31.setTop(100);
        FHBox31.setWidth(158);
        FHBox31.setHeight(25);
        FHBox31.setAlign("alLeft");
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(0);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(1);
        FHBox31.setFlexVflex("ftFalse");
        FHBox31.setFlexHflex("ftFalse");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        FVBox4.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFHBox FHBox32 = new TFHBox();

    private void init_FHBox32() {
        FHBox32.setName("FHBox32");
        FHBox32.setLeft(0);
        FHBox32.setTop(0);
        FHBox32.setWidth(16);
        FHBox32.setHeight(13);
        FHBox32.setBorderStyle("stNone");
        FHBox32.setPaddingTop(0);
        FHBox32.setPaddingLeft(0);
        FHBox32.setPaddingRight(0);
        FHBox32.setPaddingBottom(0);
        FHBox32.setMarginTop(0);
        FHBox32.setMarginLeft(0);
        FHBox32.setMarginRight(0);
        FHBox32.setMarginBottom(0);
        FHBox32.setSpacing(1);
        FHBox32.setFlexVflex("ftFalse");
        FHBox32.setFlexHflex("ftFalse");
        FHBox32.setScrollable(false);
        FHBox32.setBoxShadowConfigHorizontalLength(10);
        FHBox32.setBoxShadowConfigVerticalLength(10);
        FHBox32.setBoxShadowConfigBlurRadius(5);
        FHBox32.setBoxShadowConfigSpreadRadius(0);
        FHBox32.setBoxShadowConfigShadowColor("clBlack");
        FHBox32.setBoxShadowConfigOpacity(75);
        FHBox32.setVAlign("tvTop");
        FHBox31.addChildren(FHBox32);
        FHBox32.applyProperties();
    }

    public TFLabel lblCapDivisao = new TFLabel();

    private void init_lblCapDivisao() {
        lblCapDivisao.setName("lblCapDivisao");
        lblCapDivisao.setLeft(16);
        lblCapDivisao.setTop(0);
        lblCapDivisao.setWidth(75);
        lblCapDivisao.setHeight(16);
        lblCapDivisao.setAlign("alLeft");
        lblCapDivisao.setCaption("lblCapDivisao");
        lblCapDivisao.setFontColor("clWindowText");
        lblCapDivisao.setFontSize(-13);
        lblCapDivisao.setFontName("Tahoma");
        lblCapDivisao.setFontStyle("[]");
        lblCapDivisao.setFieldName("DIVISAO");
        lblCapDivisao.setTable(tbPerfil);
        lblCapDivisao.setVerticalAlignment("taAlignTop");
        lblCapDivisao.setWordBreak(false);
        FHBox31.addChildren(lblCapDivisao);
        lblCapDivisao.applyProperties();
    }

    public TFHBox FHBox36 = new TFHBox();

    private void init_FHBox36() {
        FHBox36.setName("FHBox36");
        FHBox36.setLeft(0);
        FHBox36.setTop(126);
        FHBox36.setWidth(158);
        FHBox36.setHeight(15);
        FHBox36.setAlign("alLeft");
        FHBox36.setBorderStyle("stNone");
        FHBox36.setPaddingTop(0);
        FHBox36.setPaddingLeft(0);
        FHBox36.setPaddingRight(0);
        FHBox36.setPaddingBottom(0);
        FHBox36.setMarginTop(0);
        FHBox36.setMarginLeft(0);
        FHBox36.setMarginRight(0);
        FHBox36.setMarginBottom(0);
        FHBox36.setSpacing(1);
        FHBox36.setFlexVflex("ftTrue");
        FHBox36.setFlexHflex("ftTrue");
        FHBox36.setScrollable(false);
        FHBox36.setBoxShadowConfigHorizontalLength(10);
        FHBox36.setBoxShadowConfigVerticalLength(10);
        FHBox36.setBoxShadowConfigBlurRadius(5);
        FHBox36.setBoxShadowConfigSpreadRadius(0);
        FHBox36.setBoxShadowConfigShadowColor("clBlack");
        FHBox36.setBoxShadowConfigOpacity(75);
        FHBox36.setVAlign("tvTop");
        FVBox4.addChildren(FHBox36);
        FHBox36.applyProperties();
    }

    public TFHBox FHBox37 = new TFHBox();

    private void init_FHBox37() {
        FHBox37.setName("FHBox37");
        FHBox37.setLeft(0);
        FHBox37.setTop(0);
        FHBox37.setWidth(16);
        FHBox37.setHeight(21);
        FHBox37.setBorderStyle("stNone");
        FHBox37.setPaddingTop(0);
        FHBox37.setPaddingLeft(0);
        FHBox37.setPaddingRight(0);
        FHBox37.setPaddingBottom(0);
        FHBox37.setMarginTop(0);
        FHBox37.setMarginLeft(0);
        FHBox37.setMarginRight(0);
        FHBox37.setMarginBottom(0);
        FHBox37.setSpacing(0);
        FHBox37.setFlexVflex("ftFalse");
        FHBox37.setFlexHflex("ftFalse");
        FHBox37.setScrollable(false);
        FHBox37.setBoxShadowConfigHorizontalLength(10);
        FHBox37.setBoxShadowConfigVerticalLength(10);
        FHBox37.setBoxShadowConfigBlurRadius(5);
        FHBox37.setBoxShadowConfigSpreadRadius(0);
        FHBox37.setBoxShadowConfigShadowColor("clBlack");
        FHBox37.setBoxShadowConfigOpacity(75);
        FHBox37.setVAlign("tvTop");
        FHBox36.addChildren(FHBox37);
        FHBox37.applyProperties();
    }

    public TFIconClass FIconClass9 = new TFIconClass();

    private void init_FIconClass9() {
        FIconClass9.setName("FIconClass9");
        FIconClass9.setLeft(0);
        FIconClass9.setTop(0);
        FIconClass9.setIconClass("bars");
        FIconClass9.setSize(12);
        FIconClass9.setColor("clBlack");
        FHBox37.addChildren(FIconClass9);
        FIconClass9.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(16);
        FLabel4.setTop(0);
        FLabel4.setWidth(40);
        FLabel4.setHeight(16);
        FLabel4.setAlign("alLeft");
        FLabel4.setCaption("Marca");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-13);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[fsBold]");
        FLabel4.setVerticalAlignment("taAlignBottom");
        FLabel4.setWordBreak(false);
        FHBox36.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFHBox FHBox38 = new TFHBox();

    private void init_FHBox38() {
        FHBox38.setName("FHBox38");
        FHBox38.setLeft(0);
        FHBox38.setTop(142);
        FHBox38.setWidth(158);
        FHBox38.setHeight(25);
        FHBox38.setAlign("alLeft");
        FHBox38.setBorderStyle("stNone");
        FHBox38.setPaddingTop(0);
        FHBox38.setPaddingLeft(0);
        FHBox38.setPaddingRight(0);
        FHBox38.setPaddingBottom(0);
        FHBox38.setMarginTop(0);
        FHBox38.setMarginLeft(0);
        FHBox38.setMarginRight(0);
        FHBox38.setMarginBottom(0);
        FHBox38.setSpacing(1);
        FHBox38.setFlexVflex("ftFalse");
        FHBox38.setFlexHflex("ftFalse");
        FHBox38.setScrollable(false);
        FHBox38.setBoxShadowConfigHorizontalLength(10);
        FHBox38.setBoxShadowConfigVerticalLength(10);
        FHBox38.setBoxShadowConfigBlurRadius(5);
        FHBox38.setBoxShadowConfigSpreadRadius(0);
        FHBox38.setBoxShadowConfigShadowColor("clBlack");
        FHBox38.setBoxShadowConfigOpacity(75);
        FHBox38.setVAlign("tvTop");
        FVBox4.addChildren(FHBox38);
        FHBox38.applyProperties();
    }

    public TFHBox FHBox39 = new TFHBox();

    private void init_FHBox39() {
        FHBox39.setName("FHBox39");
        FHBox39.setLeft(0);
        FHBox39.setTop(0);
        FHBox39.setWidth(16);
        FHBox39.setHeight(13);
        FHBox39.setBorderStyle("stNone");
        FHBox39.setPaddingTop(0);
        FHBox39.setPaddingLeft(0);
        FHBox39.setPaddingRight(0);
        FHBox39.setPaddingBottom(0);
        FHBox39.setMarginTop(0);
        FHBox39.setMarginLeft(0);
        FHBox39.setMarginRight(0);
        FHBox39.setMarginBottom(0);
        FHBox39.setSpacing(1);
        FHBox39.setFlexVflex("ftFalse");
        FHBox39.setFlexHflex("ftFalse");
        FHBox39.setScrollable(false);
        FHBox39.setBoxShadowConfigHorizontalLength(10);
        FHBox39.setBoxShadowConfigVerticalLength(10);
        FHBox39.setBoxShadowConfigBlurRadius(5);
        FHBox39.setBoxShadowConfigSpreadRadius(0);
        FHBox39.setBoxShadowConfigShadowColor("clBlack");
        FHBox39.setBoxShadowConfigOpacity(75);
        FHBox39.setVAlign("tvTop");
        FHBox38.addChildren(FHBox39);
        FHBox39.applyProperties();
    }

    public TFLabel lblmarca = new TFLabel();

    private void init_lblmarca() {
        lblmarca.setName("lblmarca");
        lblmarca.setLeft(16);
        lblmarca.setTop(0);
        lblmarca.setWidth(75);
        lblmarca.setHeight(16);
        lblmarca.setAlign("alLeft");
        lblmarca.setCaption("lblCapDivisao");
        lblmarca.setFontColor("clWindowText");
        lblmarca.setFontSize(-13);
        lblmarca.setFontName("Tahoma");
        lblmarca.setFontStyle("[]");
        lblmarca.setFieldName("MARCA");
        lblmarca.setTable(tbPerfil);
        lblmarca.setVerticalAlignment("taAlignTop");
        lblmarca.setWordBreak(false);
        FHBox38.addChildren(lblmarca);
        lblmarca.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(350);
        FVBox5.setTop(0);
        FVBox5.setWidth(225);
        FVBox5.setHeight(229);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox26.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(0);
        FHBox15.setWidth(217);
        FHBox15.setHeight(15);
        FHBox15.setAlign("alLeft");
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftTrue");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBackgroundImage(" ");
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FVBox5.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(0);
        FHBox16.setWidth(16);
        FHBox16.setHeight(21);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(0);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftFalse");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FHBox15.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFIconClass FIconClass8 = new TFIconClass();

    private void init_FIconClass8() {
        FIconClass8.setName("FIconClass8");
        FIconClass8.setLeft(0);
        FIconClass8.setTop(0);
        FIconClass8.setIconClass("money");
        FIconClass8.setSize(12);
        FIconClass8.setColor("clBlack");
        FHBox16.addChildren(FIconClass8);
        FIconClass8.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(16);
        FLabel2.setTop(0);
        FLabel2.setWidth(153);
        FLabel2.setHeight(16);
        FLabel2.setAlign("alLeft");
        FLabel2.setCaption("Tabela de pre\u00E7o padr\u00E3o");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-13);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taAlignBottom");
        FLabel2.setWordBreak(false);
        FHBox15.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(169);
        FHBox17.setTop(0);
        FHBox17.setWidth(6);
        FHBox17.setHeight(12);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftFalse");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FHBox15.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(175);
        FHBox18.setTop(0);
        FHBox18.setWidth(16);
        FHBox18.setHeight(21);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(0);
        FHBox18.setFlexVflex("ftFalse");
        FHBox18.setFlexHflex("ftFalse");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        FHBox15.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFIconClass iconAltTabPreco = new TFIconClass();

    private void init_iconAltTabPreco() {
        iconAltTabPreco.setName("iconAltTabPreco");
        iconAltTabPreco.setLeft(0);
        iconAltTabPreco.setTop(0);
        iconAltTabPreco.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconAltTabPrecoClick(event);
            processarFlow("FrmPerfil", "iconAltTabPreco", "OnClick");
        });
        iconAltTabPreco.setIconClass("edit");
        iconAltTabPreco.setSize(12);
        iconAltTabPreco.setColor("clBlack");
        FHBox18.addChildren(iconAltTabPreco);
        iconAltTabPreco.applyProperties();
    }

    public TFHBox FHBox33 = new TFHBox();

    private void init_FHBox33() {
        FHBox33.setName("FHBox33");
        FHBox33.setLeft(0);
        FHBox33.setTop(16);
        FHBox33.setWidth(214);
        FHBox33.setHeight(25);
        FHBox33.setAlign("alLeft");
        FHBox33.setBorderStyle("stNone");
        FHBox33.setPaddingTop(0);
        FHBox33.setPaddingLeft(0);
        FHBox33.setPaddingRight(0);
        FHBox33.setPaddingBottom(0);
        FHBox33.setMarginTop(0);
        FHBox33.setMarginLeft(0);
        FHBox33.setMarginRight(0);
        FHBox33.setMarginBottom(0);
        FHBox33.setSpacing(1);
        FHBox33.setFlexVflex("ftFalse");
        FHBox33.setFlexHflex("ftFalse");
        FHBox33.setScrollable(false);
        FHBox33.setBoxShadowConfigHorizontalLength(10);
        FHBox33.setBoxShadowConfigVerticalLength(10);
        FHBox33.setBoxShadowConfigBlurRadius(5);
        FHBox33.setBoxShadowConfigSpreadRadius(0);
        FHBox33.setBoxShadowConfigShadowColor("clBlack");
        FHBox33.setBoxShadowConfigOpacity(75);
        FHBox33.setVAlign("tvTop");
        FVBox5.addChildren(FHBox33);
        FHBox33.applyProperties();
    }

    public TFHBox FHBox34 = new TFHBox();

    private void init_FHBox34() {
        FHBox34.setName("FHBox34");
        FHBox34.setLeft(0);
        FHBox34.setTop(0);
        FHBox34.setWidth(16);
        FHBox34.setHeight(13);
        FHBox34.setBorderStyle("stNone");
        FHBox34.setPaddingTop(0);
        FHBox34.setPaddingLeft(0);
        FHBox34.setPaddingRight(0);
        FHBox34.setPaddingBottom(0);
        FHBox34.setMarginTop(0);
        FHBox34.setMarginLeft(0);
        FHBox34.setMarginRight(0);
        FHBox34.setMarginBottom(0);
        FHBox34.setSpacing(1);
        FHBox34.setFlexVflex("ftFalse");
        FHBox34.setFlexHflex("ftFalse");
        FHBox34.setScrollable(false);
        FHBox34.setBoxShadowConfigHorizontalLength(10);
        FHBox34.setBoxShadowConfigVerticalLength(10);
        FHBox34.setBoxShadowConfigBlurRadius(5);
        FHBox34.setBoxShadowConfigSpreadRadius(0);
        FHBox34.setBoxShadowConfigShadowColor("clBlack");
        FHBox34.setBoxShadowConfigOpacity(75);
        FHBox34.setVAlign("tvTop");
        FHBox33.addChildren(FHBox34);
        FHBox34.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(16);
        FLabel3.setTop(0);
        FLabel3.setWidth(124);
        FLabel3.setHeight(16);
        FLabel3.setAlign("alLeft");
        FLabel3.setCaption("lblTabelaPrecoPadrao");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-13);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setFieldName("TAB_PRECO");
        FLabel3.setTable(tbPerfil);
        FLabel3.setVerticalAlignment("taAlignTop");
        FLabel3.setWordBreak(false);
        FHBox33.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(42);
        FHBox13.setWidth(185);
        FHBox13.setHeight(19);
        FHBox13.setAlign("alLeft");
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FVBox5.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(0);
        FHBox14.setWidth(16);
        FHBox14.setHeight(21);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(0);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        FHBox13.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFIconClass FIconClass7 = new TFIconClass();

    private void init_FIconClass7() {
        FIconClass7.setName("FIconClass7");
        FIconClass7.setLeft(0);
        FIconClass7.setTop(0);
        FIconClass7.setIconClass("archive");
        FIconClass7.setSize(12);
        FIconClass7.setColor("clBlack");
        FHBox14.addChildren(FIconClass7);
        FIconClass7.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(16);
        FLabel1.setTop(0);
        FLabel1.setWidth(80);
        FLabel1.setHeight(16);
        FLabel1.setAlign("alLeft");
        FLabel1.setCaption("Setor venda");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-13);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taAlignBottom");
        FLabel1.setWordBreak(false);
        FHBox13.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFHBox FHBox35 = new TFHBox();

    private void init_FHBox35() {
        FHBox35.setName("FHBox35");
        FHBox35.setLeft(0);
        FHBox35.setTop(62);
        FHBox35.setWidth(214);
        FHBox35.setHeight(41);
        FHBox35.setAlign("alLeft");
        FHBox35.setBorderStyle("stNone");
        FHBox35.setPaddingTop(0);
        FHBox35.setPaddingLeft(0);
        FHBox35.setPaddingRight(0);
        FHBox35.setPaddingBottom(0);
        FHBox35.setMarginTop(0);
        FHBox35.setMarginLeft(0);
        FHBox35.setMarginRight(0);
        FHBox35.setMarginBottom(0);
        FHBox35.setSpacing(1);
        FHBox35.setFlexVflex("ftFalse");
        FHBox35.setFlexHflex("ftFalse");
        FHBox35.setScrollable(false);
        FHBox35.setBoxShadowConfigHorizontalLength(10);
        FHBox35.setBoxShadowConfigVerticalLength(10);
        FHBox35.setBoxShadowConfigBlurRadius(5);
        FHBox35.setBoxShadowConfigSpreadRadius(0);
        FHBox35.setBoxShadowConfigShadowColor("clBlack");
        FHBox35.setBoxShadowConfigOpacity(75);
        FHBox35.setVAlign("tvTop");
        FVBox5.addChildren(FHBox35);
        FHBox35.applyProperties();
    }

    public TFCombo cmbSetorVenda = new TFCombo();

    private void init_cmbSetorVenda() {
        cmbSetorVenda.setName("cmbSetorVenda");
        cmbSetorVenda.setLeft(0);
        cmbSetorVenda.setTop(0);
        cmbSetorVenda.setWidth(161);
        cmbSetorVenda.setHeight(21);
        cmbSetorVenda.setTable(tbPerfil);
        cmbSetorVenda.setLookupTable(tbSetorVenda);
        cmbSetorVenda.setFieldName("COD_SETOR");
        cmbSetorVenda.setLookupKey("COD_SETOR");
        cmbSetorVenda.setLookupDesc("DESCRICAO_SETOR");
        cmbSetorVenda.setFlex(false);
        cmbSetorVenda.setReadOnly(true);
        cmbSetorVenda.setRequired(false);
        cmbSetorVenda.setPrompt("Selecione");
        cmbSetorVenda.setConstraintCheckWhen("cwImmediate");
        cmbSetorVenda.setConstraintCheckType("ctExpression");
        cmbSetorVenda.setConstraintFocusOnError(false);
        cmbSetorVenda.setConstraintEnableUI(true);
        cmbSetorVenda.setConstraintEnabled(false);
        cmbSetorVenda.setConstraintFormCheck(true);
        cmbSetorVenda.setClearOnDelKey(true);
        cmbSetorVenda.setUseClearButton(false);
        cmbSetorVenda.setHideClearButtonOnNullValue(false);
        cmbSetorVenda.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbSetorVendaChange(event);
            processarFlow("FrmPerfil", "cmbSetorVenda", "OnChange");
        });
        FHBox35.addChildren(cmbSetorVenda);
        cmbSetorVenda.applyProperties();
        addValidatable(cmbSetorVenda);
    }

    public TFHBox FHBox41 = new TFHBox();

    private void init_FHBox41() {
        FHBox41.setName("FHBox41");
        FHBox41.setLeft(0);
        FHBox41.setTop(104);
        FHBox41.setWidth(158);
        FHBox41.setHeight(22);
        FHBox41.setAlign("alLeft");
        FHBox41.setBorderStyle("stNone");
        FHBox41.setPaddingTop(0);
        FHBox41.setPaddingLeft(0);
        FHBox41.setPaddingRight(0);
        FHBox41.setPaddingBottom(0);
        FHBox41.setMarginTop(0);
        FHBox41.setMarginLeft(0);
        FHBox41.setMarginRight(0);
        FHBox41.setMarginBottom(0);
        FHBox41.setSpacing(1);
        FHBox41.setFlexVflex("ftTrue");
        FHBox41.setFlexHflex("ftTrue");
        FHBox41.setScrollable(false);
        FHBox41.setBoxShadowConfigHorizontalLength(10);
        FHBox41.setBoxShadowConfigVerticalLength(10);
        FHBox41.setBoxShadowConfigBlurRadius(5);
        FHBox41.setBoxShadowConfigSpreadRadius(0);
        FHBox41.setBoxShadowConfigShadowColor("clBlack");
        FHBox41.setBoxShadowConfigOpacity(75);
        FHBox41.setVAlign("tvTop");
        FVBox5.addChildren(FHBox41);
        FHBox41.applyProperties();
    }

    public TFHBox FHBox42 = new TFHBox();

    private void init_FHBox42() {
        FHBox42.setName("FHBox42");
        FHBox42.setLeft(0);
        FHBox42.setTop(0);
        FHBox42.setWidth(16);
        FHBox42.setHeight(21);
        FHBox42.setBorderStyle("stNone");
        FHBox42.setPaddingTop(0);
        FHBox42.setPaddingLeft(0);
        FHBox42.setPaddingRight(0);
        FHBox42.setPaddingBottom(0);
        FHBox42.setMarginTop(0);
        FHBox42.setMarginLeft(0);
        FHBox42.setMarginRight(0);
        FHBox42.setMarginBottom(0);
        FHBox42.setSpacing(0);
        FHBox42.setFlexVflex("ftFalse");
        FHBox42.setFlexHflex("ftFalse");
        FHBox42.setScrollable(false);
        FHBox42.setBoxShadowConfigHorizontalLength(10);
        FHBox42.setBoxShadowConfigVerticalLength(10);
        FHBox42.setBoxShadowConfigBlurRadius(5);
        FHBox42.setBoxShadowConfigSpreadRadius(0);
        FHBox42.setBoxShadowConfigShadowColor("clBlack");
        FHBox42.setBoxShadowConfigOpacity(75);
        FHBox42.setVAlign("tvTop");
        FHBox41.addChildren(FHBox42);
        FHBox42.applyProperties();
    }

    public TFIconClass FIconClass11 = new TFIconClass();

    private void init_FIconClass11() {
        FIconClass11.setName("FIconClass11");
        FIconClass11.setLeft(0);
        FIconClass11.setTop(0);
        FIconClass11.setIconClass("car");
        FIconClass11.setSize(12);
        FIconClass11.setColor("clBlack");
        FHBox42.addChildren(FIconClass11);
        FIconClass11.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(16);
        FLabel5.setTop(0);
        FLabel5.setWidth(65);
        FLabel5.setHeight(16);
        FLabel5.setAlign("alLeft");
        FLabel5.setCaption("Segmento");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-13);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[fsBold]");
        FLabel5.setVerticalAlignment("taAlignBottom");
        FLabel5.setWordBreak(false);
        FHBox41.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFHBox FHBox43 = new TFHBox();

    private void init_FHBox43() {
        FHBox43.setName("FHBox43");
        FHBox43.setLeft(0);
        FHBox43.setTop(127);
        FHBox43.setWidth(214);
        FHBox43.setHeight(38);
        FHBox43.setAlign("alLeft");
        FHBox43.setBorderStyle("stNone");
        FHBox43.setPaddingTop(0);
        FHBox43.setPaddingLeft(0);
        FHBox43.setPaddingRight(0);
        FHBox43.setPaddingBottom(0);
        FHBox43.setMarginTop(0);
        FHBox43.setMarginLeft(0);
        FHBox43.setMarginRight(0);
        FHBox43.setMarginBottom(0);
        FHBox43.setSpacing(1);
        FHBox43.setFlexVflex("ftFalse");
        FHBox43.setFlexHflex("ftFalse");
        FHBox43.setScrollable(false);
        FHBox43.setBoxShadowConfigHorizontalLength(10);
        FHBox43.setBoxShadowConfigVerticalLength(10);
        FHBox43.setBoxShadowConfigBlurRadius(5);
        FHBox43.setBoxShadowConfigSpreadRadius(0);
        FHBox43.setBoxShadowConfigShadowColor("clBlack");
        FHBox43.setBoxShadowConfigOpacity(75);
        FHBox43.setVAlign("tvTop");
        FVBox5.addChildren(FHBox43);
        FHBox43.applyProperties();
    }

    public TFCombo cmbSegmento = new TFCombo();

    private void init_cmbSegmento() {
        cmbSegmento.setName("cmbSegmento");
        cmbSegmento.setLeft(0);
        cmbSegmento.setTop(0);
        cmbSegmento.setWidth(161);
        cmbSegmento.setHeight(21);
        cmbSegmento.setTable(tbPerfil);
        cmbSegmento.setLookupTable(tbProdutoSegmento);
        cmbSegmento.setFieldName("COD_SEGMENTO");
        cmbSegmento.setLookupKey("COD_SEGMENTO");
        cmbSegmento.setLookupDesc("DESCRICAO_SEGMENTO");
        cmbSegmento.setFlex(false);
        cmbSegmento.setReadOnly(true);
        cmbSegmento.setRequired(false);
        cmbSegmento.setPrompt("Selecione");
        cmbSegmento.setConstraintCheckWhen("cwImmediate");
        cmbSegmento.setConstraintCheckType("ctExpression");
        cmbSegmento.setConstraintFocusOnError(false);
        cmbSegmento.setConstraintEnableUI(true);
        cmbSegmento.setConstraintEnabled(false);
        cmbSegmento.setConstraintFormCheck(true);
        cmbSegmento.setClearOnDelKey(true);
        cmbSegmento.setUseClearButton(false);
        cmbSegmento.setHideClearButtonOnNullValue(false);
        cmbSegmento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbSegmentoChange(event);
            processarFlow("FrmPerfil", "cmbSegmento", "OnChange");
        });
        FHBox43.addChildren(cmbSegmento);
        cmbSegmento.applyProperties();
        addValidatable(cmbSegmento);
    }

    public TFHBox FHBox48 = new TFHBox();

    private void init_FHBox48() {
        FHBox48.setName("FHBox48");
        FHBox48.setLeft(0);
        FHBox48.setTop(166);
        FHBox48.setWidth(185);
        FHBox48.setHeight(21);
        FHBox48.setAlign("alLeft");
        FHBox48.setBorderStyle("stNone");
        FHBox48.setPaddingTop(0);
        FHBox48.setPaddingLeft(0);
        FHBox48.setPaddingRight(0);
        FHBox48.setPaddingBottom(0);
        FHBox48.setMarginTop(0);
        FHBox48.setMarginLeft(0);
        FHBox48.setMarginRight(0);
        FHBox48.setMarginBottom(0);
        FHBox48.setSpacing(1);
        FHBox48.setFlexVflex("ftFalse");
        FHBox48.setFlexHflex("ftFalse");
        FHBox48.setScrollable(false);
        FHBox48.setBoxShadowConfigHorizontalLength(10);
        FHBox48.setBoxShadowConfigVerticalLength(10);
        FHBox48.setBoxShadowConfigBlurRadius(5);
        FHBox48.setBoxShadowConfigSpreadRadius(0);
        FHBox48.setBoxShadowConfigShadowColor("clBlack");
        FHBox48.setBoxShadowConfigOpacity(75);
        FHBox48.setVAlign("tvTop");
        FVBox5.addChildren(FHBox48);
        FHBox48.applyProperties();
    }

    public TFHBox FHBox49 = new TFHBox();

    private void init_FHBox49() {
        FHBox49.setName("FHBox49");
        FHBox49.setLeft(0);
        FHBox49.setTop(0);
        FHBox49.setWidth(16);
        FHBox49.setHeight(21);
        FHBox49.setBorderStyle("stNone");
        FHBox49.setPaddingTop(0);
        FHBox49.setPaddingLeft(0);
        FHBox49.setPaddingRight(0);
        FHBox49.setPaddingBottom(0);
        FHBox49.setMarginTop(0);
        FHBox49.setMarginLeft(0);
        FHBox49.setMarginRight(0);
        FHBox49.setMarginBottom(0);
        FHBox49.setSpacing(0);
        FHBox49.setFlexVflex("ftFalse");
        FHBox49.setFlexHflex("ftFalse");
        FHBox49.setScrollable(false);
        FHBox49.setBoxShadowConfigHorizontalLength(10);
        FHBox49.setBoxShadowConfigVerticalLength(10);
        FHBox49.setBoxShadowConfigBlurRadius(5);
        FHBox49.setBoxShadowConfigSpreadRadius(0);
        FHBox49.setBoxShadowConfigShadowColor("clBlack");
        FHBox49.setBoxShadowConfigOpacity(75);
        FHBox49.setVAlign("tvTop");
        FHBox48.addChildren(FHBox49);
        FHBox49.applyProperties();
    }

    public TFIconClass FIconClass13 = new TFIconClass();

    private void init_FIconClass13() {
        FIconClass13.setName("FIconClass13");
        FIconClass13.setLeft(0);
        FIconClass13.setTop(0);
        FIconClass13.setIconClass("archive");
        FIconClass13.setSize(12);
        FIconClass13.setColor("clBlack");
        FHBox49.addChildren(FIconClass13);
        FIconClass13.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(16);
        FLabel6.setTop(0);
        FLabel6.setWidth(109);
        FLabel6.setHeight(16);
        FLabel6.setAlign("alLeft");
        FLabel6.setCaption("Local do Estoque");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-13);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[fsBold]");
        FLabel6.setVerticalAlignment("taAlignBottom");
        FLabel6.setWordBreak(false);
        FHBox48.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFHBox FHBox50 = new TFHBox();

    private void init_FHBox50() {
        FHBox50.setName("FHBox50");
        FHBox50.setLeft(0);
        FHBox50.setTop(188);
        FHBox50.setWidth(214);
        FHBox50.setHeight(34);
        FHBox50.setAlign("alLeft");
        FHBox50.setBorderStyle("stNone");
        FHBox50.setPaddingTop(0);
        FHBox50.setPaddingLeft(0);
        FHBox50.setPaddingRight(0);
        FHBox50.setPaddingBottom(0);
        FHBox50.setMarginTop(0);
        FHBox50.setMarginLeft(0);
        FHBox50.setMarginRight(0);
        FHBox50.setMarginBottom(0);
        FHBox50.setSpacing(1);
        FHBox50.setFlexVflex("ftFalse");
        FHBox50.setFlexHflex("ftFalse");
        FHBox50.setScrollable(false);
        FHBox50.setBoxShadowConfigHorizontalLength(10);
        FHBox50.setBoxShadowConfigVerticalLength(10);
        FHBox50.setBoxShadowConfigBlurRadius(5);
        FHBox50.setBoxShadowConfigSpreadRadius(0);
        FHBox50.setBoxShadowConfigShadowColor("clBlack");
        FHBox50.setBoxShadowConfigOpacity(75);
        FHBox50.setVAlign("tvTop");
        FVBox5.addChildren(FHBox50);
        FHBox50.applyProperties();
    }

    public TFCombo cmbLocalEstoque = new TFCombo();

    private void init_cmbLocalEstoque() {
        cmbLocalEstoque.setName("cmbLocalEstoque");
        cmbLocalEstoque.setLeft(0);
        cmbLocalEstoque.setTop(0);
        cmbLocalEstoque.setWidth(161);
        cmbLocalEstoque.setHeight(21);
        cmbLocalEstoque.setTable(tbEmpresasUsuarios);
        cmbLocalEstoque.setLookupTable(tbLocalEstoque);
        cmbLocalEstoque.setFieldName("COD_LOCAL_ESTOQUE");
        cmbLocalEstoque.setLookupKey("COD_LOCAL_ESTOQUE");
        cmbLocalEstoque.setLookupDesc("NOME_DO_LOCAL");
        cmbLocalEstoque.setFlex(false);
        cmbLocalEstoque.setReadOnly(true);
        cmbLocalEstoque.setRequired(false);
        cmbLocalEstoque.setPrompt("Selecione");
        cmbLocalEstoque.setConstraintCheckWhen("cwImmediate");
        cmbLocalEstoque.setConstraintCheckType("ctExpression");
        cmbLocalEstoque.setConstraintFocusOnError(false);
        cmbLocalEstoque.setConstraintEnableUI(true);
        cmbLocalEstoque.setConstraintEnabled(false);
        cmbLocalEstoque.setConstraintFormCheck(true);
        cmbLocalEstoque.setClearOnDelKey(true);
        cmbLocalEstoque.setUseClearButton(false);
        cmbLocalEstoque.setHideClearButtonOnNullValue(false);
        cmbLocalEstoque.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbLocalEstoqueChange(event);
            processarFlow("FrmPerfil", "cmbLocalEstoque", "OnChange");
        });
        FHBox50.addChildren(cmbLocalEstoque);
        cmbLocalEstoque.applyProperties();
        addValidatable(cmbLocalEstoque);
    }

    public TFVBox vBoxAssinaturaUsadaRecepcao = new TFVBox();

    private void init_vBoxAssinaturaUsadaRecepcao() {
        vBoxAssinaturaUsadaRecepcao.setName("vBoxAssinaturaUsadaRecepcao");
        vBoxAssinaturaUsadaRecepcao.setLeft(0);
        vBoxAssinaturaUsadaRecepcao.setTop(242);
        vBoxAssinaturaUsadaRecepcao.setWidth(721);
        vBoxAssinaturaUsadaRecepcao.setHeight(197);
        vBoxAssinaturaUsadaRecepcao.setBorderStyle("stNone");
        vBoxAssinaturaUsadaRecepcao.setPaddingTop(0);
        vBoxAssinaturaUsadaRecepcao.setPaddingLeft(0);
        vBoxAssinaturaUsadaRecepcao.setPaddingRight(0);
        vBoxAssinaturaUsadaRecepcao.setPaddingBottom(0);
        vBoxAssinaturaUsadaRecepcao.setMarginTop(0);
        vBoxAssinaturaUsadaRecepcao.setMarginLeft(0);
        vBoxAssinaturaUsadaRecepcao.setMarginRight(0);
        vBoxAssinaturaUsadaRecepcao.setMarginBottom(0);
        vBoxAssinaturaUsadaRecepcao.setSpacing(1);
        vBoxAssinaturaUsadaRecepcao.setFlexVflex("ftFalse");
        vBoxAssinaturaUsadaRecepcao.setFlexHflex("ftTrue");
        vBoxAssinaturaUsadaRecepcao.setScrollable(false);
        vBoxAssinaturaUsadaRecepcao.setBoxShadowConfigHorizontalLength(10);
        vBoxAssinaturaUsadaRecepcao.setBoxShadowConfigVerticalLength(10);
        vBoxAssinaturaUsadaRecepcao.setBoxShadowConfigBlurRadius(5);
        vBoxAssinaturaUsadaRecepcao.setBoxShadowConfigSpreadRadius(0);
        vBoxAssinaturaUsadaRecepcao.setBoxShadowConfigShadowColor("clBlack");
        vBoxAssinaturaUsadaRecepcao.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(vBoxAssinaturaUsadaRecepcao);
        vBoxAssinaturaUsadaRecepcao.applyProperties();
    }

    public TFHBox FHBox46 = new TFHBox();

    private void init_FHBox46() {
        FHBox46.setName("FHBox46");
        FHBox46.setLeft(0);
        FHBox46.setTop(0);
        FHBox46.setWidth(569);
        FHBox46.setHeight(15);
        FHBox46.setAlign("alLeft");
        FHBox46.setBorderStyle("stNone");
        FHBox46.setPaddingTop(0);
        FHBox46.setPaddingLeft(0);
        FHBox46.setPaddingRight(0);
        FHBox46.setPaddingBottom(0);
        FHBox46.setMarginTop(0);
        FHBox46.setMarginLeft(0);
        FHBox46.setMarginRight(0);
        FHBox46.setMarginBottom(0);
        FHBox46.setSpacing(1);
        FHBox46.setFlexVflex("ftTrue");
        FHBox46.setFlexHflex("ftTrue");
        FHBox46.setScrollable(false);
        FHBox46.setBoxShadowConfigHorizontalLength(10);
        FHBox46.setBoxShadowConfigVerticalLength(10);
        FHBox46.setBoxShadowConfigBlurRadius(5);
        FHBox46.setBoxShadowConfigSpreadRadius(0);
        FHBox46.setBoxShadowConfigShadowColor("clBlack");
        FHBox46.setBoxShadowConfigOpacity(75);
        FHBox46.setVAlign("tvTop");
        vBoxAssinaturaUsadaRecepcao.addChildren(FHBox46);
        FHBox46.applyProperties();
    }

    public TFHBox FHBox47 = new TFHBox();

    private void init_FHBox47() {
        FHBox47.setName("FHBox47");
        FHBox47.setLeft(0);
        FHBox47.setTop(0);
        FHBox47.setWidth(16);
        FHBox47.setHeight(21);
        FHBox47.setBorderStyle("stNone");
        FHBox47.setPaddingTop(0);
        FHBox47.setPaddingLeft(0);
        FHBox47.setPaddingRight(0);
        FHBox47.setPaddingBottom(0);
        FHBox47.setMarginTop(0);
        FHBox47.setMarginLeft(0);
        FHBox47.setMarginRight(0);
        FHBox47.setMarginBottom(0);
        FHBox47.setSpacing(0);
        FHBox47.setFlexVflex("ftFalse");
        FHBox47.setFlexHflex("ftFalse");
        FHBox47.setScrollable(false);
        FHBox47.setBoxShadowConfigHorizontalLength(10);
        FHBox47.setBoxShadowConfigVerticalLength(10);
        FHBox47.setBoxShadowConfigBlurRadius(5);
        FHBox47.setBoxShadowConfigSpreadRadius(0);
        FHBox47.setBoxShadowConfigShadowColor("clBlack");
        FHBox47.setBoxShadowConfigOpacity(75);
        FHBox47.setVAlign("tvTop");
        FHBox46.addChildren(FHBox47);
        FHBox47.applyProperties();
    }

    public TFIconClass FIconClass12 = new TFIconClass();

    private void init_FIconClass12() {
        FIconClass12.setName("FIconClass12");
        FIconClass12.setLeft(0);
        FIconClass12.setTop(0);
        FIconClass12.setIconClass("pencil");
        FIconClass12.setSize(12);
        FIconClass12.setColor("clBlack");
        FHBox47.addChildren(FIconClass12);
        FIconClass12.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(16);
        FLabel7.setTop(0);
        FLabel7.setWidth(239);
        FLabel7.setHeight(16);
        FLabel7.setAlign("alLeft");
        FLabel7.setCaption("Assinatura a Ser Usada na Recep\u00E7\u00E3o");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-13);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[fsBold]");
        FLabel7.setVerticalAlignment("taAlignBottom");
        FLabel7.setWordBreak(false);
        FHBox46.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFSignature signature = new TFSignature();

    private void init_signature() {
        signature.setName("signature");
        signature.setLeft(0);
        signature.setTop(16);
        signature.setWidth(714);
        signature.setHeight(153);
        signature.setBackgroundColor("clWhite");
        signature.setPenColor("clBlack");
        signature.setPenSize(4);
        signature.setSaveType("image/png");
        signature.setToolbarVisible(false);
        signature.addEventListener("onSave", (EventListener<UploadEvent>) (UploadEvent event) -> {
            signatureSave(event);
            processarFlow("FrmPerfil", "signature", "OnSave");
        });
        signature.addEventListener("onClear", (EventListener<Event<Object>>)(Event<Object> event) -> {
            signatureClear(event);
            processarFlow("FrmPerfil", "signature", "OnClear");
        });
        signature.setFlexVflex("ftFalse");
        signature.setFlexHflex("ftTrue");
        vBoxAssinaturaUsadaRecepcao.addChildren(signature);
        signature.applyProperties();
    }

    public TFVBox vBoxImgSignatrueChk = new TFVBox();

    private void init_vBoxImgSignatrueChk() {
        vBoxImgSignatrueChk.setName("vBoxImgSignatrueChk");
        vBoxImgSignatrueChk.setLeft(0);
        vBoxImgSignatrueChk.setTop(170);
        vBoxImgSignatrueChk.setWidth(757);
        vBoxImgSignatrueChk.setHeight(100);
        vBoxImgSignatrueChk.setBorderStyle("stNone");
        vBoxImgSignatrueChk.setPaddingTop(0);
        vBoxImgSignatrueChk.setPaddingLeft(0);
        vBoxImgSignatrueChk.setPaddingRight(0);
        vBoxImgSignatrueChk.setPaddingBottom(0);
        vBoxImgSignatrueChk.setVisible(false);
        vBoxImgSignatrueChk.setMarginTop(0);
        vBoxImgSignatrueChk.setMarginLeft(0);
        vBoxImgSignatrueChk.setMarginRight(0);
        vBoxImgSignatrueChk.setMarginBottom(0);
        vBoxImgSignatrueChk.setSpacing(1);
        vBoxImgSignatrueChk.setFlexVflex("ftFalse");
        vBoxImgSignatrueChk.setFlexHflex("ftTrue");
        vBoxImgSignatrueChk.setScrollable(false);
        vBoxImgSignatrueChk.setBoxShadowConfigHorizontalLength(10);
        vBoxImgSignatrueChk.setBoxShadowConfigVerticalLength(10);
        vBoxImgSignatrueChk.setBoxShadowConfigBlurRadius(5);
        vBoxImgSignatrueChk.setBoxShadowConfigSpreadRadius(0);
        vBoxImgSignatrueChk.setBoxShadowConfigShadowColor("clBlack");
        vBoxImgSignatrueChk.setBoxShadowConfigOpacity(75);
        vBoxAssinaturaUsadaRecepcao.addChildren(vBoxImgSignatrueChk);
        vBoxImgSignatrueChk.applyProperties();
    }

    public TFImage imgSignatureChk = new TFImage();

    private void init_imgSignatureChk() {
        imgSignatureChk.setName("imgSignatureChk");
        imgSignatureChk.setLeft(0);
        imgSignatureChk.setTop(0);
        imgSignatureChk.setWidth(753);
        imgSignatureChk.setHeight(200);
        imgSignatureChk.setTable(tbEmpresasUsuarios);
        imgSignatureChk.setFieldName("ASSINATURA");
        imgSignatureChk.setBoxSize(0);
        imgSignatureChk.setGrayScaleOnDisable(false);
        imgSignatureChk.setFlexVflex("ftTrue");
        imgSignatureChk.setFlexHflex("ftTrue");
        vBoxImgSignatrueChk.addChildren(imgSignatureChk);
        imgSignatureChk.applyProperties();
    }

    public TFHBox FHBox45 = new TFHBox();

    private void init_FHBox45() {
        FHBox45.setName("FHBox45");
        FHBox45.setLeft(0);
        FHBox45.setTop(440);
        FHBox45.setWidth(718);
        FHBox45.setHeight(44);
        FHBox45.setBorderStyle("stNone");
        FHBox45.setPaddingTop(3);
        FHBox45.setPaddingLeft(0);
        FHBox45.setPaddingRight(0);
        FHBox45.setPaddingBottom(0);
        FHBox45.setMarginTop(0);
        FHBox45.setMarginLeft(0);
        FHBox45.setMarginRight(0);
        FHBox45.setMarginBottom(0);
        FHBox45.setSpacing(1);
        FHBox45.setFlexVflex("ftFalse");
        FHBox45.setFlexHflex("ftTrue");
        FHBox45.setScrollable(false);
        FHBox45.setBoxShadowConfigHorizontalLength(10);
        FHBox45.setBoxShadowConfigVerticalLength(10);
        FHBox45.setBoxShadowConfigBlurRadius(5);
        FHBox45.setBoxShadowConfigSpreadRadius(0);
        FHBox45.setBoxShadowConfigShadowColor("clBlack");
        FHBox45.setBoxShadowConfigOpacity(75);
        FHBox45.setVAlign("tvTop");
        FVBox3.addChildren(FHBox45);
        FHBox45.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(0);
        FVBox7.setWidth(6);
        FVBox7.setHeight(28);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftFalse");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox45.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFButton btnDesfazer = new TFButton();

    private void init_btnDesfazer() {
        btnDesfazer.setName("btnDesfazer");
        btnDesfazer.setLeft(6);
        btnDesfazer.setTop(0);
        btnDesfazer.setWidth(88);
        btnDesfazer.setHeight(38);
        btnDesfazer.setCaption("Desfazer");
        btnDesfazer.setFontColor("clWindowText");
        btnDesfazer.setFontSize(-11);
        btnDesfazer.setFontName("Tahoma");
        btnDesfazer.setFontStyle("[]");
        btnDesfazer.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnDesfazerClick(event);
            processarFlow("FrmPerfil", "btnDesfazer", "OnClick");
        });
        btnDesfazer.setImageId(0);
        btnDesfazer.setColor("clBtnFace");
        btnDesfazer.setAccess(false);
        btnDesfazer.setIconClass("undo");
        btnDesfazer.setIconReverseDirection(false);
        FHBox45.addChildren(btnDesfazer);
        btnDesfazer.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(94);
        FVBox8.setTop(0);
        FVBox8.setWidth(6);
        FVBox8.setHeight(28);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftFalse");
        FVBox8.setFlexHflex("ftFalse");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FHBox45.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFButton btnLimpar = new TFButton();

    private void init_btnLimpar() {
        btnLimpar.setName("btnLimpar");
        btnLimpar.setLeft(100);
        btnLimpar.setTop(0);
        btnLimpar.setWidth(84);
        btnLimpar.setHeight(38);
        btnLimpar.setCaption("Limpar");
        btnLimpar.setFontColor("clWindowText");
        btnLimpar.setFontSize(-11);
        btnLimpar.setFontName("Tahoma");
        btnLimpar.setFontStyle("[]");
        btnLimpar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimparClick(event);
            processarFlow("FrmPerfil", "btnLimpar", "OnClick");
        });
        btnLimpar.setImageId(0);
        btnLimpar.setColor("clBtnFace");
        btnLimpar.setAccess(false);
        btnLimpar.setIconClass("close");
        btnLimpar.setIconReverseDirection(false);
        FHBox45.addChildren(btnLimpar);
        btnLimpar.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(184);
        FVBox9.setTop(0);
        FVBox9.setWidth(6);
        FVBox9.setHeight(28);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftFalse");
        FVBox9.setFlexHflex("ftFalse");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FHBox45.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(190);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(84);
        btnSalvar.setHeight(38);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmPerfil", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(0);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconClass("save");
        btnSalvar.setIconReverseDirection(false);
        FHBox45.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbUsuarioFoto);
        sc.getTables().add(item0);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void lblNomeCompletoClick(final Event<Object> event);

    public abstract void icoEditarEmailClick(final Event<Object> event);

    public abstract void lblImagePerfilClick(final Event<Object> event);

    public abstract void iconAltTabPrecoClick(final Event<Object> event);

    public abstract void cmbSetorVendaChange(final Event<Object> event);

    public abstract void cmbSegmentoChange(final Event<Object> event);

    public abstract void cmbLocalEstoqueChange(final Event<Object> event);

    public abstract void signatureSave(final UploadEvent event);

    public abstract void signatureClear(final Event<Object> event);

    public void btnDesfazerClick(final Event<Object> event) {
        if (btnDesfazer.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnDesfazer");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnLimparClick(final Event<Object> event) {
        if (btnLimpar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimpar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}