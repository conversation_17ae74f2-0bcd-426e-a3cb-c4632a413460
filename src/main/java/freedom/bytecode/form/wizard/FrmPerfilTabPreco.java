package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPerfilTabPreco extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PerfilTabPrecoRNA rn = null;

    public FrmPerfilTabPreco() {
        try {
            rn = (freedom.bytecode.rn.PerfilTabPrecoRNA) getRN(freedom.bytecode.rn.wizard.PerfilTabPrecoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbPerfilTabPreco();
        init_FVBox1();
        init_hboxEditarLead();
        init_btnSalvar();
        init_FVBox4();
        init_gridTabPreco();
        init_FrmPerfilTabPreco();
    }

    public PERFIL_TAB_PRECO tbPerfilTabPreco;

    private void init_tbPerfilTabPreco() {
        tbPerfilTabPreco = rn.tbPerfilTabPreco;
        tbPerfilTabPreco.setName("tbPerfilTabPreco");
        tbPerfilTabPreco.setMaxRowCount(200);
        tbPerfilTabPreco.setWKey("310039;31001");
        tbPerfilTabPreco.setRatioBatchSize(20);
        getTables().put(tbPerfilTabPreco, "tbPerfilTabPreco");
        tbPerfilTabPreco.applyProperties();
    }

    protected TFForm FrmPerfilTabPreco = this;
    private void init_FrmPerfilTabPreco() {
        FrmPerfilTabPreco.setName("FrmPerfilTabPreco");
        FrmPerfilTabPreco.setCaption("Alterar Divis\u00E3o para o Usu\u00E1rio Selecionado");
        FrmPerfilTabPreco.setClientHeight(295);
        FrmPerfilTabPreco.setClientWidth(515);
        FrmPerfilTabPreco.setColor("clBtnFace");
        FrmPerfilTabPreco.setWKey("310039");
        FrmPerfilTabPreco.setSpacing(0);
        FrmPerfilTabPreco.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(515);
        FVBox1.setHeight(295);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(3);
        FVBox1.setPaddingRight(3);
        FVBox1.setPaddingBottom(3);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmPerfilTabPreco.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox hboxEditarLead = new TFHBox();

    private void init_hboxEditarLead() {
        hboxEditarLead.setName("hboxEditarLead");
        hboxEditarLead.setLeft(0);
        hboxEditarLead.setTop(0);
        hboxEditarLead.setWidth(508);
        hboxEditarLead.setHeight(61);
        hboxEditarLead.setAlign("alTop");
        hboxEditarLead.setBorderStyle("stNone");
        hboxEditarLead.setPaddingTop(3);
        hboxEditarLead.setPaddingLeft(2);
        hboxEditarLead.setPaddingRight(0);
        hboxEditarLead.setPaddingBottom(0);
        hboxEditarLead.setMarginTop(0);
        hboxEditarLead.setMarginLeft(0);
        hboxEditarLead.setMarginRight(0);
        hboxEditarLead.setMarginBottom(0);
        hboxEditarLead.setSpacing(1);
        hboxEditarLead.setFlexVflex("ftFalse");
        hboxEditarLead.setFlexHflex("ftTrue");
        hboxEditarLead.setScrollable(false);
        hboxEditarLead.setBoxShadowConfigHorizontalLength(10);
        hboxEditarLead.setBoxShadowConfigVerticalLength(10);
        hboxEditarLead.setBoxShadowConfigBlurRadius(5);
        hboxEditarLead.setBoxShadowConfigSpreadRadius(0);
        hboxEditarLead.setBoxShadowConfigShadowColor("clBlack");
        hboxEditarLead.setBoxShadowConfigOpacity(75);
        hboxEditarLead.setVAlign("tvTop");
        FVBox1.addChildren(hboxEditarLead);
        hboxEditarLead.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(60);
        btnSalvar.setHeight(56);
        btnSalvar.setHint("Salvar Lead");
        btnSalvar.setAlign("alLeft");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmPerfilTabPreco", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(700080);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hboxEditarLead.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(60);
        FVBox4.setTop(0);
        FVBox4.setWidth(5);
        FVBox4.setHeight(55);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hboxEditarLead.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFGrid gridTabPreco = new TFGrid();

    private void init_gridTabPreco() {
        gridTabPreco.setName("gridTabPreco");
        gridTabPreco.setLeft(0);
        gridTabPreco.setTop(62);
        gridTabPreco.setWidth(507);
        gridTabPreco.setHeight(224);
        gridTabPreco.setAlign("alClient");
        gridTabPreco.setTable(tbPerfilTabPreco);
        gridTabPreco.setFlexVflex("ftTrue");
        gridTabPreco.setFlexHflex("ftTrue");
        gridTabPreco.setPagingEnabled(false);
        gridTabPreco.setFrozenColumns(0);
        gridTabPreco.setShowFooter(false);
        gridTabPreco.setShowHeader(true);
        gridTabPreco.setMultiSelection(false);
        gridTabPreco.setGroupingEnabled(false);
        gridTabPreco.setGroupingExpanded(false);
        gridTabPreco.setGroupingShowFooter(false);
        gridTabPreco.setCrosstabEnabled(false);
        gridTabPreco.setCrosstabGroupType("cgtConcat");
        gridTabPreco.setEditionEnabled(false);
        gridTabPreco.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Divis\u00E3o empresa");
        item0.setWidth(150);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridTabPreco.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TAB_PRECO");
        item1.setTitleCaption("Tabela Pre\u00E7o");
        item1.setWidth(200);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridTabPreco.getColumns().add(item1);
        FVBox1.addChildren(gridTabPreco);
        gridTabPreco.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}