package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClientesFlagsPgto extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClientesFlagsPgtoRNA rn = null;

    public FrmClientesFlagsPgto() {
        try {
            rn = (freedom.bytecode.rn.ClientesFlagsPgtoRNA) getRN(freedom.bytecode.rn.wizard.ClientesFlagsPgtoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteFormaPgtoDisp();
        init_tbEmpresas();
        init_tbEmpresasDepartamentos();
        init_tbFormaPgto();
        init_tbEmpresasFiliaisSel();
        init_tbListaEmpresasDepartamentos();
        init_tbListaTipoPgtoNbs();
        init_tbListaFormasPgto();
        init_tbClienteFormaPgtoDisp1();
        init_popGridPgtos();
        init_mmSelecionarTodosOsRegistrosDaGradeGridPgtos();
        init_mmSelecionarNenhumRegistroDaGradeGridPgtos();
        init_vBoxPrincipal();
        init_FHBox4();
        init_hBoxLinha01();
        init_FHBox1();
        init_btnVoltar();
        init_FHBox2();
        init_btnSalvar();
        init_FHBox3();
        init_FHBox5();
        init_hBoxLinha02();
        init_FHBox12();
        init_cboEmpresa();
        init_FHBox13();
        init_cboDepartamento();
        init_FHBox14();
        init_cboTipoDaFormaDePagamento();
        init_FHBox15();
        init_cboFormaDePagamentoExclusiva();
        init_FHBox16();
        init_FHBox6();
        init_hBoxLinha03();
        init_FHBox17();
        init_cboFormaDePagamento();
        init_FHBox18();
        init_cboCondicaoDePagamentoExclusiva();
        init_FHBox19();
        init_cboCondicaoDePagamento();
        init_FHBox20();
        init_btnPesquisar();
        init_FHBox21();
        init_FHBox7();
        init_FHBox8();
        init_FHBox10();
        init_gridPgtos();
        init_FHBox11();
        init_FHBox9();
        init_FrmClientesFlagsPgto();
    }

    public CLIENTE_FORMA_PGTO_DISP tbClienteFormaPgtoDisp;

    private void init_tbClienteFormaPgtoDisp() {
        tbClienteFormaPgtoDisp = rn.tbClienteFormaPgtoDisp;
        tbClienteFormaPgtoDisp.setName("tbClienteFormaPgtoDisp");
        tbClienteFormaPgtoDisp.setMaxRowCount(0);
        tbClienteFormaPgtoDisp.setWKey("310037;31001");
        tbClienteFormaPgtoDisp.setRatioBatchSize(20);
        getTables().put(tbClienteFormaPgtoDisp, "tbClienteFormaPgtoDisp");
        tbClienteFormaPgtoDisp.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("310037;31002");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public EMPRESAS_DEPARTAMENTOS tbEmpresasDepartamentos;

    private void init_tbEmpresasDepartamentos() {
        tbEmpresasDepartamentos = rn.tbEmpresasDepartamentos;
        tbEmpresasDepartamentos.setName("tbEmpresasDepartamentos");
        tbEmpresasDepartamentos.setMaxRowCount(0);
        tbEmpresasDepartamentos.setWKey("310037;31003");
        tbEmpresasDepartamentos.setRatioBatchSize(20);
        getTables().put(tbEmpresasDepartamentos, "tbEmpresasDepartamentos");
        tbEmpresasDepartamentos.applyProperties();
    }

    public FORMA_PGTO tbFormaPgto;

    private void init_tbFormaPgto() {
        tbFormaPgto = rn.tbFormaPgto;
        tbFormaPgto.setName("tbFormaPgto");
        tbFormaPgto.setMaxRowCount(0);
        tbFormaPgto.setWKey("310037;31004");
        tbFormaPgto.setRatioBatchSize(20);
        getTables().put(tbFormaPgto, "tbFormaPgto");
        tbFormaPgto.applyProperties();
    }

    public EMPRESAS_FILIAIS_SEL tbEmpresasFiliaisSel;

    private void init_tbEmpresasFiliaisSel() {
        tbEmpresasFiliaisSel = rn.tbEmpresasFiliaisSel;
        tbEmpresasFiliaisSel.setName("tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.setMaxRowCount(200);
        tbEmpresasFiliaisSel.setWKey("310037;53001");
        tbEmpresasFiliaisSel.setRatioBatchSize(20);
        getTables().put(tbEmpresasFiliaisSel, "tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.applyProperties();
    }

    public LISTA_EMPRESAS_DEPARTAMENTOS tbListaEmpresasDepartamentos;

    private void init_tbListaEmpresasDepartamentos() {
        tbListaEmpresasDepartamentos = rn.tbListaEmpresasDepartamentos;
        tbListaEmpresasDepartamentos.setName("tbListaEmpresasDepartamentos");
        tbListaEmpresasDepartamentos.setMaxRowCount(200);
        tbListaEmpresasDepartamentos.setWKey("310037;53002");
        tbListaEmpresasDepartamentos.setRatioBatchSize(20);
        getTables().put(tbListaEmpresasDepartamentos, "tbListaEmpresasDepartamentos");
        tbListaEmpresasDepartamentos.applyProperties();
    }

    public LISTA_TIPO_PGTO_NBS tbListaTipoPgtoNbs;

    private void init_tbListaTipoPgtoNbs() {
        tbListaTipoPgtoNbs = rn.tbListaTipoPgtoNbs;
        tbListaTipoPgtoNbs.setName("tbListaTipoPgtoNbs");
        tbListaTipoPgtoNbs.setMaxRowCount(200);
        tbListaTipoPgtoNbs.setWKey("310037;53003");
        tbListaTipoPgtoNbs.setRatioBatchSize(20);
        getTables().put(tbListaTipoPgtoNbs, "tbListaTipoPgtoNbs");
        tbListaTipoPgtoNbs.applyProperties();
    }

    public LISTA_FORMAS_PGTO tbListaFormasPgto;

    private void init_tbListaFormasPgto() {
        tbListaFormasPgto = rn.tbListaFormasPgto;
        tbListaFormasPgto.setName("tbListaFormasPgto");
        tbListaFormasPgto.setMaxRowCount(200);
        tbListaFormasPgto.setWKey("310037;53004");
        tbListaFormasPgto.setRatioBatchSize(20);
        getTables().put(tbListaFormasPgto, "tbListaFormasPgto");
        tbListaFormasPgto.applyProperties();
    }

    public CLIENTE_FORMA_PGTO_DISP tbClienteFormaPgtoDisp1;

    private void init_tbClienteFormaPgtoDisp1() {
        tbClienteFormaPgtoDisp1 = rn.tbClienteFormaPgtoDisp1;
        tbClienteFormaPgtoDisp1.setName("tbClienteFormaPgtoDisp1");
        tbClienteFormaPgtoDisp1.setMaxRowCount(200);
        tbClienteFormaPgtoDisp1.setWKey("310037;53005");
        tbClienteFormaPgtoDisp1.setRatioBatchSize(20);
        getTables().put(tbClienteFormaPgtoDisp1, "tbClienteFormaPgtoDisp1");
        tbClienteFormaPgtoDisp1.applyProperties();
    }

    public TFPopupMenu popGridPgtos = new TFPopupMenu();

    private void init_popGridPgtos() {
        popGridPgtos.setName("popGridPgtos");
        FrmClientesFlagsPgto.addChildren(popGridPgtos);
        popGridPgtos.applyProperties();
    }

    public TFMenuItem mmSelecionarTodosOsRegistrosDaGradeGridPgtos = new TFMenuItem();

    private void init_mmSelecionarTodosOsRegistrosDaGradeGridPgtos() {
        mmSelecionarTodosOsRegistrosDaGradeGridPgtos.setName("mmSelecionarTodosOsRegistrosDaGradeGridPgtos");
        mmSelecionarTodosOsRegistrosDaGradeGridPgtos.setCaption("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosDaGradeGridPgtos.setHint("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosDaGradeGridPgtos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarTodosOsRegistrosDaGradeGridPgtosClick(event);
            processarFlow("FrmClientesFlagsPgto", "mmSelecionarTodosOsRegistrosDaGradeGridPgtos", "OnClick");
        });
        mmSelecionarTodosOsRegistrosDaGradeGridPgtos.setAccess(false);
        mmSelecionarTodosOsRegistrosDaGradeGridPgtos.setCheckmark(false);
        popGridPgtos.addChildren(mmSelecionarTodosOsRegistrosDaGradeGridPgtos);
        mmSelecionarTodosOsRegistrosDaGradeGridPgtos.applyProperties();
    }

    public TFMenuItem mmSelecionarNenhumRegistroDaGradeGridPgtos = new TFMenuItem();

    private void init_mmSelecionarNenhumRegistroDaGradeGridPgtos() {
        mmSelecionarNenhumRegistroDaGradeGridPgtos.setName("mmSelecionarNenhumRegistroDaGradeGridPgtos");
        mmSelecionarNenhumRegistroDaGradeGridPgtos.setCaption("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroDaGradeGridPgtos.setHint("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroDaGradeGridPgtos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarNenhumRegistroDaGradeGridPgtosClick(event);
            processarFlow("FrmClientesFlagsPgto", "mmSelecionarNenhumRegistroDaGradeGridPgtos", "OnClick");
        });
        mmSelecionarNenhumRegistroDaGradeGridPgtos.setAccess(false);
        mmSelecionarNenhumRegistroDaGradeGridPgtos.setCheckmark(false);
        popGridPgtos.addChildren(mmSelecionarNenhumRegistroDaGradeGridPgtos);
        mmSelecionarNenhumRegistroDaGradeGridPgtos.applyProperties();
    }

    protected TFForm FrmClientesFlagsPgto = this;
    private void init_FrmClientesFlagsPgto() {
        FrmClientesFlagsPgto.setName("FrmClientesFlagsPgto");
        FrmClientesFlagsPgto.setCaption("Selecionar condi\u00E7\u00F5es de pagamento");
        FrmClientesFlagsPgto.setClientHeight(382);
        FrmClientesFlagsPgto.setClientWidth(1014);
        FrmClientesFlagsPgto.setColor("clBtnFace");
        FrmClientesFlagsPgto.setWKey("310037");
        FrmClientesFlagsPgto.setSpacing(0);
        FrmClientesFlagsPgto.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(1014);
        vBoxPrincipal.setHeight(382);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(8);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmClientesFlagsPgto.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(1000);
        FHBox4.setHeight(5);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox hBoxLinha01 = new TFHBox();

    private void init_hBoxLinha01() {
        hBoxLinha01.setName("hBoxLinha01");
        hBoxLinha01.setLeft(0);
        hBoxLinha01.setTop(6);
        hBoxLinha01.setWidth(1000);
        hBoxLinha01.setHeight(61);
        hBoxLinha01.setAlign("alTop");
        hBoxLinha01.setBorderStyle("stNone");
        hBoxLinha01.setPaddingTop(0);
        hBoxLinha01.setPaddingLeft(0);
        hBoxLinha01.setPaddingRight(0);
        hBoxLinha01.setPaddingBottom(0);
        hBoxLinha01.setMarginTop(2);
        hBoxLinha01.setMarginLeft(3);
        hBoxLinha01.setMarginRight(0);
        hBoxLinha01.setMarginBottom(0);
        hBoxLinha01.setSpacing(1);
        hBoxLinha01.setFlexVflex("ftMin");
        hBoxLinha01.setFlexHflex("ftTrue");
        hBoxLinha01.setScrollable(false);
        hBoxLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha01.setBoxShadowConfigOpacity(75);
        hBoxLinha01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha01);
        hBoxLinha01.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(5);
        FHBox1.setHeight(40);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(5);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(53);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmClientesFlagsPgto", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(70);
        FHBox2.setTop(0);
        FHBox2.setWidth(5);
        FHBox2.setHeight(40);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(75);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmClientesFlagsPgto", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934"
 + "31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2"
 + "D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE"
 + "6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5"
 + "6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013"
 + "090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF"
 + "349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E"
 + "8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871"
 + "175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39"
 + "FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622"
 + "F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673"
 + "BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E"
 + "01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146"
 + "0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219"
 + "506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6"
 + "0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991"
 + "CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313"
 + "9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0"
 + "39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D"
 + "AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7"
 + "C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743"
 + "FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE"
 + "E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660"
 + "474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF"
 + "D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61"
 + "33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E"
 + "0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE"
 + "F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF"
 + "921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5"
 + "A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E"
 + "9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163"
 + "8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09"
 + "C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C"
 + "D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E"
 + "23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA"
 + "FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E"
 + "9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1"
 + "5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A"
 + "25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2"
 + "FF02B065C443D9FE4B070000000049454E44AE426082");
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(140);
        FHBox3.setTop(0);
        FHBox3.setWidth(5);
        FHBox3.setHeight(40);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(68);
        FHBox5.setWidth(1000);
        FHBox5.setHeight(5);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox hBoxLinha02 = new TFHBox();

    private void init_hBoxLinha02() {
        hBoxLinha02.setName("hBoxLinha02");
        hBoxLinha02.setLeft(0);
        hBoxLinha02.setTop(74);
        hBoxLinha02.setWidth(1000);
        hBoxLinha02.setHeight(30);
        hBoxLinha02.setBorderStyle("stNone");
        hBoxLinha02.setPaddingTop(0);
        hBoxLinha02.setPaddingLeft(0);
        hBoxLinha02.setPaddingRight(0);
        hBoxLinha02.setPaddingBottom(0);
        hBoxLinha02.setMarginTop(0);
        hBoxLinha02.setMarginLeft(0);
        hBoxLinha02.setMarginRight(0);
        hBoxLinha02.setMarginBottom(0);
        hBoxLinha02.setSpacing(1);
        hBoxLinha02.setFlexVflex("ftMin");
        hBoxLinha02.setFlexHflex("ftTrue");
        hBoxLinha02.setScrollable(false);
        hBoxLinha02.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha02.setBoxShadowConfigVerticalLength(10);
        hBoxLinha02.setBoxShadowConfigBlurRadius(5);
        hBoxLinha02.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha02.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha02.setBoxShadowConfigOpacity(75);
        hBoxLinha02.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha02);
        hBoxLinha02.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(0);
        FHBox12.setWidth(5);
        FHBox12.setHeight(20);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFCombo cboEmpresa = new TFCombo();

    private void init_cboEmpresa() {
        cboEmpresa.setName("cboEmpresa");
        cboEmpresa.setLeft(5);
        cboEmpresa.setTop(0);
        cboEmpresa.setWidth(200);
        cboEmpresa.setHeight(21);
        cboEmpresa.setHint("Empresa");
        cboEmpresa.setLookupTable(tbEmpresasFiliaisSel);
        cboEmpresa.setLookupKey("CODIGODAEMPRESA");
        cboEmpresa.setLookupDesc("NOMEECODIGODAEMPRESA");
        cboEmpresa.setFlex(false);
        cboEmpresa.setHelpCaption("Empresa");
        cboEmpresa.setReadOnly(true);
        cboEmpresa.setRequired(false);
        cboEmpresa.setPrompt("Empresa");
        cboEmpresa.setConstraintCheckWhen("cwImmediate");
        cboEmpresa.setConstraintCheckType("ctExpression");
        cboEmpresa.setConstraintFocusOnError(false);
        cboEmpresa.setConstraintEnableUI(true);
        cboEmpresa.setConstraintEnabled(false);
        cboEmpresa.setConstraintFormCheck(true);
        cboEmpresa.setClearOnDelKey(true);
        cboEmpresa.setUseClearButton(true);
        cboEmpresa.setHideClearButtonOnNullValue(false);
        cboEmpresa.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaChange(event);
            processarFlow("FrmClientesFlagsPgto", "cboEmpresa", "OnChange");
        });
        cboEmpresa.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaClearClick(event);
            processarFlow("FrmClientesFlagsPgto", "cboEmpresa", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboEmpresa);
        cboEmpresa.applyProperties();
        addValidatable(cboEmpresa);
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(205);
        FHBox13.setTop(0);
        FHBox13.setWidth(5);
        FHBox13.setHeight(20);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFCombo cboDepartamento = new TFCombo();

    private void init_cboDepartamento() {
        cboDepartamento.setName("cboDepartamento");
        cboDepartamento.setLeft(210);
        cboDepartamento.setTop(0);
        cboDepartamento.setWidth(200);
        cboDepartamento.setHeight(21);
        cboDepartamento.setHint("Departamento");
        cboDepartamento.setLookupTable(tbListaEmpresasDepartamentos);
        cboDepartamento.setLookupKey("DPTO_CODIGO");
        cboDepartamento.setLookupDesc("DPTO_DESCRICAO_CODIGO");
        cboDepartamento.setFlex(false);
        cboDepartamento.setHelpCaption("Departamento");
        cboDepartamento.setReadOnly(true);
        cboDepartamento.setRequired(false);
        cboDepartamento.setPrompt("Departamento");
        cboDepartamento.setConstraintCheckWhen("cwImmediate");
        cboDepartamento.setConstraintCheckType("ctExpression");
        cboDepartamento.setConstraintFocusOnError(false);
        cboDepartamento.setConstraintEnableUI(true);
        cboDepartamento.setConstraintEnabled(false);
        cboDepartamento.setConstraintFormCheck(true);
        cboDepartamento.setClearOnDelKey(true);
        cboDepartamento.setUseClearButton(true);
        cboDepartamento.setHideClearButtonOnNullValue(false);
        cboDepartamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDepartamentoChange(event);
            processarFlow("FrmClientesFlagsPgto", "cboDepartamento", "OnChange");
        });
        cboDepartamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDepartamentoClearClick(event);
            processarFlow("FrmClientesFlagsPgto", "cboDepartamento", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboDepartamento);
        cboDepartamento.applyProperties();
        addValidatable(cboDepartamento);
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(410);
        FHBox14.setTop(0);
        FHBox14.setWidth(5);
        FHBox14.setHeight(20);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFCombo cboTipoDaFormaDePagamento = new TFCombo();

    private void init_cboTipoDaFormaDePagamento() {
        cboTipoDaFormaDePagamento.setName("cboTipoDaFormaDePagamento");
        cboTipoDaFormaDePagamento.setLeft(415);
        cboTipoDaFormaDePagamento.setTop(0);
        cboTipoDaFormaDePagamento.setWidth(200);
        cboTipoDaFormaDePagamento.setHeight(21);
        cboTipoDaFormaDePagamento.setHint("Tipo da forma de pagamento");
        cboTipoDaFormaDePagamento.setLookupTable(tbListaTipoPgtoNbs);
        cboTipoDaFormaDePagamento.setLookupKey("COD_TIPO_PGTO_NBS");
        cboTipoDaFormaDePagamento.setLookupDesc("DESC_COD_TIPO_PGTO_NBS");
        cboTipoDaFormaDePagamento.setFlex(false);
        cboTipoDaFormaDePagamento.setHelpCaption("Tipo da forma de pagamento");
        cboTipoDaFormaDePagamento.setReadOnly(true);
        cboTipoDaFormaDePagamento.setRequired(false);
        cboTipoDaFormaDePagamento.setPrompt("Tipo da forma de pagamento");
        cboTipoDaFormaDePagamento.setConstraintCheckWhen("cwImmediate");
        cboTipoDaFormaDePagamento.setConstraintCheckType("ctExpression");
        cboTipoDaFormaDePagamento.setConstraintFocusOnError(false);
        cboTipoDaFormaDePagamento.setConstraintEnableUI(true);
        cboTipoDaFormaDePagamento.setConstraintEnabled(false);
        cboTipoDaFormaDePagamento.setConstraintFormCheck(true);
        cboTipoDaFormaDePagamento.setClearOnDelKey(true);
        cboTipoDaFormaDePagamento.setUseClearButton(true);
        cboTipoDaFormaDePagamento.setHideClearButtonOnNullValue(false);
        cboTipoDaFormaDePagamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboTipoDaFormaDePagamentoChange(event);
            processarFlow("FrmClientesFlagsPgto", "cboTipoDaFormaDePagamento", "OnChange");
        });
        cboTipoDaFormaDePagamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboTipoDaFormaDePagamentoClearClick(event);
            processarFlow("FrmClientesFlagsPgto", "cboTipoDaFormaDePagamento", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboTipoDaFormaDePagamento);
        cboTipoDaFormaDePagamento.applyProperties();
        addValidatable(cboTipoDaFormaDePagamento);
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(615);
        FHBox15.setTop(0);
        FHBox15.setWidth(5);
        FHBox15.setHeight(20);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFCombo cboFormaDePagamentoExclusiva = new TFCombo();

    private void init_cboFormaDePagamentoExclusiva() {
        cboFormaDePagamentoExclusiva.setName("cboFormaDePagamentoExclusiva");
        cboFormaDePagamentoExclusiva.setLeft(620);
        cboFormaDePagamentoExclusiva.setTop(0);
        cboFormaDePagamentoExclusiva.setWidth(200);
        cboFormaDePagamentoExclusiva.setHeight(21);
        cboFormaDePagamentoExclusiva.setHint("Forma exclusiva");
        cboFormaDePagamentoExclusiva.setFlex(false);
        cboFormaDePagamentoExclusiva.setListOptions("Sim=S;N\u00E3o=N");
        cboFormaDePagamentoExclusiva.setHelpCaption("Forma exclusiva");
        cboFormaDePagamentoExclusiva.setReadOnly(true);
        cboFormaDePagamentoExclusiva.setRequired(false);
        cboFormaDePagamentoExclusiva.setPrompt("Forma exclusiva");
        cboFormaDePagamentoExclusiva.setConstraintCheckWhen("cwImmediate");
        cboFormaDePagamentoExclusiva.setConstraintCheckType("ctExpression");
        cboFormaDePagamentoExclusiva.setConstraintFocusOnError(false);
        cboFormaDePagamentoExclusiva.setConstraintEnableUI(true);
        cboFormaDePagamentoExclusiva.setConstraintEnabled(false);
        cboFormaDePagamentoExclusiva.setConstraintFormCheck(true);
        cboFormaDePagamentoExclusiva.setClearOnDelKey(true);
        cboFormaDePagamentoExclusiva.setUseClearButton(true);
        cboFormaDePagamentoExclusiva.setHideClearButtonOnNullValue(false);
        cboFormaDePagamentoExclusiva.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaDePagamentoExclusivaChange(event);
            processarFlow("FrmClientesFlagsPgto", "cboFormaDePagamentoExclusiva", "OnChange");
        });
        cboFormaDePagamentoExclusiva.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaDePagamentoExclusivaClearClick(event);
            processarFlow("FrmClientesFlagsPgto", "cboFormaDePagamentoExclusiva", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboFormaDePagamentoExclusiva);
        cboFormaDePagamentoExclusiva.applyProperties();
        addValidatable(cboFormaDePagamentoExclusiva);
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(820);
        FHBox16.setTop(0);
        FHBox16.setWidth(5);
        FHBox16.setHeight(20);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftFalse");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(105);
        FHBox6.setWidth(1000);
        FHBox6.setHeight(5);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox hBoxLinha03 = new TFHBox();

    private void init_hBoxLinha03() {
        hBoxLinha03.setName("hBoxLinha03");
        hBoxLinha03.setLeft(0);
        hBoxLinha03.setTop(111);
        hBoxLinha03.setWidth(1000);
        hBoxLinha03.setHeight(30);
        hBoxLinha03.setBorderStyle("stNone");
        hBoxLinha03.setPaddingTop(0);
        hBoxLinha03.setPaddingLeft(0);
        hBoxLinha03.setPaddingRight(0);
        hBoxLinha03.setPaddingBottom(0);
        hBoxLinha03.setMarginTop(0);
        hBoxLinha03.setMarginLeft(0);
        hBoxLinha03.setMarginRight(0);
        hBoxLinha03.setMarginBottom(0);
        hBoxLinha03.setSpacing(1);
        hBoxLinha03.setFlexVflex("ftMin");
        hBoxLinha03.setFlexHflex("ftTrue");
        hBoxLinha03.setScrollable(false);
        hBoxLinha03.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha03.setBoxShadowConfigVerticalLength(10);
        hBoxLinha03.setBoxShadowConfigBlurRadius(5);
        hBoxLinha03.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha03.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha03.setBoxShadowConfigOpacity(75);
        hBoxLinha03.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha03);
        hBoxLinha03.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(0);
        FHBox17.setWidth(5);
        FHBox17.setHeight(20);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftFalse");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        hBoxLinha03.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFCombo cboFormaDePagamento = new TFCombo();

    private void init_cboFormaDePagamento() {
        cboFormaDePagamento.setName("cboFormaDePagamento");
        cboFormaDePagamento.setLeft(5);
        cboFormaDePagamento.setTop(0);
        cboFormaDePagamento.setWidth(200);
        cboFormaDePagamento.setHeight(21);
        cboFormaDePagamento.setHint("Forma de pagamento");
        cboFormaDePagamento.setLookupTable(tbListaFormasPgto);
        cboFormaDePagamento.setLookupKey("FORMA_CODIGO");
        cboFormaDePagamento.setLookupDesc("FORMA_DESCRICAO_COD");
        cboFormaDePagamento.setFlex(false);
        cboFormaDePagamento.setHelpCaption("Forma de pagamento");
        cboFormaDePagamento.setReadOnly(true);
        cboFormaDePagamento.setRequired(false);
        cboFormaDePagamento.setPrompt("Forma de pagamento");
        cboFormaDePagamento.setConstraintCheckWhen("cwImmediate");
        cboFormaDePagamento.setConstraintCheckType("ctExpression");
        cboFormaDePagamento.setConstraintFocusOnError(false);
        cboFormaDePagamento.setConstraintEnableUI(true);
        cboFormaDePagamento.setConstraintEnabled(false);
        cboFormaDePagamento.setConstraintFormCheck(true);
        cboFormaDePagamento.setClearOnDelKey(true);
        cboFormaDePagamento.setUseClearButton(true);
        cboFormaDePagamento.setHideClearButtonOnNullValue(false);
        cboFormaDePagamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaDePagamentoChange(event);
            processarFlow("FrmClientesFlagsPgto", "cboFormaDePagamento", "OnChange");
        });
        cboFormaDePagamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaDePagamentoClearClick(event);
            processarFlow("FrmClientesFlagsPgto", "cboFormaDePagamento", "OnClearClick");
        });
        hBoxLinha03.addChildren(cboFormaDePagamento);
        cboFormaDePagamento.applyProperties();
        addValidatable(cboFormaDePagamento);
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(205);
        FHBox18.setTop(0);
        FHBox18.setWidth(5);
        FHBox18.setHeight(20);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftFalse");
        FHBox18.setFlexHflex("ftFalse");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        hBoxLinha03.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFCombo cboCondicaoDePagamentoExclusiva = new TFCombo();

    private void init_cboCondicaoDePagamentoExclusiva() {
        cboCondicaoDePagamentoExclusiva.setName("cboCondicaoDePagamentoExclusiva");
        cboCondicaoDePagamentoExclusiva.setLeft(210);
        cboCondicaoDePagamentoExclusiva.setTop(0);
        cboCondicaoDePagamentoExclusiva.setWidth(200);
        cboCondicaoDePagamentoExclusiva.setHeight(21);
        cboCondicaoDePagamentoExclusiva.setHint("Condi\u00E7\u00E3o exclusiva");
        cboCondicaoDePagamentoExclusiva.setFlex(false);
        cboCondicaoDePagamentoExclusiva.setListOptions("Sim=S;N\u00E3o=N");
        cboCondicaoDePagamentoExclusiva.setHelpCaption("Condi\u00E7\u00E3o exclusiva");
        cboCondicaoDePagamentoExclusiva.setReadOnly(true);
        cboCondicaoDePagamentoExclusiva.setRequired(false);
        cboCondicaoDePagamentoExclusiva.setPrompt("Condi\u00E7\u00E3o exclusiva");
        cboCondicaoDePagamentoExclusiva.setConstraintCheckWhen("cwImmediate");
        cboCondicaoDePagamentoExclusiva.setConstraintCheckType("ctExpression");
        cboCondicaoDePagamentoExclusiva.setConstraintFocusOnError(false);
        cboCondicaoDePagamentoExclusiva.setConstraintEnableUI(true);
        cboCondicaoDePagamentoExclusiva.setConstraintEnabled(false);
        cboCondicaoDePagamentoExclusiva.setConstraintFormCheck(true);
        cboCondicaoDePagamentoExclusiva.setClearOnDelKey(true);
        cboCondicaoDePagamentoExclusiva.setUseClearButton(true);
        cboCondicaoDePagamentoExclusiva.setHideClearButtonOnNullValue(false);
        cboCondicaoDePagamentoExclusiva.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboCondicaoDePagamentoExclusivaChange(event);
            processarFlow("FrmClientesFlagsPgto", "cboCondicaoDePagamentoExclusiva", "OnChange");
        });
        cboCondicaoDePagamentoExclusiva.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboCondicaoDePagamentoExclusivaClearClick(event);
            processarFlow("FrmClientesFlagsPgto", "cboCondicaoDePagamentoExclusiva", "OnClearClick");
        });
        hBoxLinha03.addChildren(cboCondicaoDePagamentoExclusiva);
        cboCondicaoDePagamentoExclusiva.applyProperties();
        addValidatable(cboCondicaoDePagamentoExclusiva);
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(410);
        FHBox19.setTop(0);
        FHBox19.setWidth(5);
        FHBox19.setHeight(20);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftFalse");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        hBoxLinha03.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFCombo cboCondicaoDePagamento = new TFCombo();

    private void init_cboCondicaoDePagamento() {
        cboCondicaoDePagamento.setName("cboCondicaoDePagamento");
        cboCondicaoDePagamento.setLeft(415);
        cboCondicaoDePagamento.setTop(0);
        cboCondicaoDePagamento.setWidth(200);
        cboCondicaoDePagamento.setHeight(21);
        cboCondicaoDePagamento.setHint("Condi\u00E7\u00E3o de pagamento");
        cboCondicaoDePagamento.setLookupTable(tbClienteFormaPgtoDisp1);
        cboCondicaoDePagamento.setLookupKey("COD_CONDICAO_PAGAMENTO");
        cboCondicaoDePagamento.setLookupDesc("NOME_COD_CONDICAO_PAGAMENTO");
        cboCondicaoDePagamento.setFlex(false);
        cboCondicaoDePagamento.setHelpCaption("Condi\u00E7\u00E3o de pagamento");
        cboCondicaoDePagamento.setReadOnly(true);
        cboCondicaoDePagamento.setRequired(false);
        cboCondicaoDePagamento.setPrompt("Condi\u00E7\u00E3o de pagamento");
        cboCondicaoDePagamento.setConstraintCheckWhen("cwImmediate");
        cboCondicaoDePagamento.setConstraintCheckType("ctExpression");
        cboCondicaoDePagamento.setConstraintFocusOnError(false);
        cboCondicaoDePagamento.setConstraintEnableUI(true);
        cboCondicaoDePagamento.setConstraintEnabled(false);
        cboCondicaoDePagamento.setConstraintFormCheck(true);
        cboCondicaoDePagamento.setClearOnDelKey(true);
        cboCondicaoDePagamento.setUseClearButton(true);
        cboCondicaoDePagamento.setHideClearButtonOnNullValue(false);
        cboCondicaoDePagamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboCondicaoDePagamentoChange(event);
            processarFlow("FrmClientesFlagsPgto", "cboCondicaoDePagamento", "OnChange");
        });
        cboCondicaoDePagamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboCondicaoDePagamentoClearClick(event);
            processarFlow("FrmClientesFlagsPgto", "cboCondicaoDePagamento", "OnClearClick");
        });
        hBoxLinha03.addChildren(cboCondicaoDePagamento);
        cboCondicaoDePagamento.applyProperties();
        addValidatable(cboCondicaoDePagamento);
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(615);
        FHBox20.setTop(0);
        FHBox20.setWidth(5);
        FHBox20.setHeight(20);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(0);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftFalse");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        hBoxLinha03.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(620);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(24);
        btnPesquisar.setHeight(25);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmClientesFlagsPgto", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51"
 + "782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9"
 + "B9B0DBF5D30000000049454E44AE426082");
        btnPesquisar.setImageId(0);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconClass("refresh");
        btnPesquisar.setIconReverseDirection(false);
        hBoxLinha03.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(644);
        FHBox21.setTop(0);
        FHBox21.setWidth(5);
        FHBox21.setHeight(20);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftFalse");
        FHBox21.setFlexHflex("ftFalse");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        hBoxLinha03.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(142);
        FHBox7.setWidth(1000);
        FHBox7.setHeight(5);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(148);
        FHBox8.setWidth(1000);
        FHBox8.setHeight(200);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftTrue");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(5);
        FHBox10.setHeight(40);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox8.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFGrid gridPgtos = new TFGrid();

    private void init_gridPgtos() {
        gridPgtos.setName("gridPgtos");
        gridPgtos.setLeft(5);
        gridPgtos.setTop(0);
        gridPgtos.setWidth(957);
        gridPgtos.setHeight(100);
        gridPgtos.setAlign("alClient");
        gridPgtos.setTable(tbClienteFormaPgtoDisp);
        gridPgtos.setFlexVflex("ftTrue");
        gridPgtos.setFlexHflex("ftTrue");
        gridPgtos.setPagingEnabled(false);
        gridPgtos.setFrozenColumns(0);
        gridPgtos.setShowFooter(false);
        gridPgtos.setShowHeader(true);
        gridPgtos.setMultiSelection(false);
        gridPgtos.setGroupingEnabled(false);
        gridPgtos.setGroupingExpanded(false);
        gridPgtos.setGroupingShowFooter(false);
        gridPgtos.setCrosstabEnabled(false);
        gridPgtos.setCrosstabGroupType("cgtConcat");
        gridPgtos.setEditionEnabled(false);
        gridPgtos.setContextMenu(popGridPgtos);
        gridPgtos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL = 'S'");
        item1.setHint("Registro selecionado");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPgtosdesmarcarRegistroNaGradeGridPgtos(event);
            processarFlow("FrmClientesFlagsPgto", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL = 'N'");
        item2.setHint("Registro n\u00E3o selecionado");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPgtosmarcarRegistroNaGradeGridPgtos(event);
            processarFlow("FrmClientesFlagsPgto", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("NOME_COD_EMPRESA");
        item3.setTitleCaption("Empresa");
        item3.setWidth(200);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setHiperLink(false);
        item3.setHint("Empresa");
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("NOME_COD_DEPARTAMENTO");
        item4.setTitleCaption("Departamento");
        item4.setWidth(160);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setHiperLink(false);
        item4.setHint("Departamento");
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("NOME_COD_FORMA_PGTO");
        item5.setTitleCaption("Forma de pagamento");
        item5.setWidth(200);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setHiperLink(false);
        item5.setHint("Forma de pagamento");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("NOME_COD_CONDICAO_PAGAMENTO");
        item6.setTitleCaption("Condi\u00E7\u00E3o de pagamento");
        item6.setWidth(200);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(true);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setHiperLink(false);
        item6.setHint("Condi\u00E7\u00E3o de pagamento");
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("CONDICAO_PAGAMENTO_EXCLUSIVA");
        item7.setTitleCaption("Condi\u00E7\u00E3o exclusiva");
        item7.setWidth(120);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taCenter");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(true);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        TFImageExpression item8 = new TFImageExpression();
        item8.setExpression("CONDICAO_PAGAMENTO_EXCLUSIVA = 'S'");
        item8.setHint("Condi\u00E7\u00E3o de pagamento exclusiva");
        item8.setEvalType("etExpression");
        item8.setImageId(4300107);
        item7.getImages().add(item8);
        TFImageExpression item9 = new TFImageExpression();
        item9.setExpression("CONDICAO_PAGAMENTO_EXCLUSIVA = 'N'");
        item9.setHint("Condi\u00E7\u00E3o de pagamento n\u00E3o exclusiva");
        item9.setEvalType("etExpression");
        item9.setImageId(4300106);
        item7.getImages().add(item9);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(false);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setHiperLink(false);
        item7.setHint("Condi\u00E7\u00E3o exclusiva");
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item7);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("NOME_COD_TIPO_FORMA_PGTO");
        item10.setTitleCaption("Tipo da forma de pagamento");
        item10.setWidth(120);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(true);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setHiperLink(false);
        item10.setHint("Tipo da forma de pagamento");
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item10);
        TFGridColumn item11 = new TFGridColumn();
        item11.setFieldName("FORMA_PGTO_EXCLUSIVA");
        item11.setTitleCaption("Forma exclusiva");
        item11.setWidth(110);
        item11.setVisible(true);
        item11.setPrecision(0);
        item11.setTextAlign("taCenter");
        item11.setFieldType("ftString");
        item11.setFlexRatio(0);
        item11.setSort(true);
        item11.setImageHeader(0);
        item11.setWrap(false);
        item11.setFlex(false);
        TFImageExpression item12 = new TFImageExpression();
        item12.setExpression("FORMA_PGTO_EXCLUSIVA = 'S'");
        item12.setHint("Forma de pagamento exclusiva");
        item12.setEvalType("etExpression");
        item12.setImageId(4300107);
        item11.getImages().add(item12);
        TFImageExpression item13 = new TFImageExpression();
        item13.setExpression("FORMA_PGTO_EXCLUSIVA = 'N'");
        item13.setHint("Forma de pagamento n\u00E3o exclusiva");
        item13.setEvalType("etExpression");
        item13.setImageId(4300106);
        item11.getImages().add(item13);
        item11.setCharCase("ccNormal");
        item11.setBlobConfigMimeType("bmtText");
        item11.setBlobConfigShowType("btImageViewer");
        item11.setShowLabel(false);
        item11.setEditorEditType("etTFString");
        item11.setEditorPrecision(0);
        item11.setEditorMaxLength(100);
        item11.setEditorLookupFilterKey(0);
        item11.setEditorLookupFilterDesc(0);
        item11.setEditorPopupHeight(400);
        item11.setEditorPopupWidth(400);
        item11.setEditorCharCase("ccNormal");
        item11.setEditorEnabled(false);
        item11.setEditorReadOnly(false);
        item11.setHiperLink(false);
        item11.setHint("Forma exclusiva");
        item11.setEditorConstraintCheckWhen("cwImmediate");
        item11.setEditorConstraintCheckType("ctExpression");
        item11.setEditorConstraintFocusOnError(false);
        item11.setEditorConstraintEnableUI(true);
        item11.setEditorConstraintEnabled(false);
        item11.setEmpty(false);
        item11.setMobileOptsShowMobile(false);
        item11.setMobileOptsOrder(0);
        item11.setBoxSize(0);
        item11.setImageSrcType("istSource");
        gridPgtos.getColumns().add(item11);
        FHBox8.addChildren(gridPgtos);
        gridPgtos.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(962);
        FHBox11.setTop(0);
        FHBox11.setWidth(5);
        FHBox11.setHeight(40);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FHBox8.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(349);
        FHBox9.setWidth(1000);
        FHBox9.setHeight(5);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cboEmpresaChange(final Event<Object> event);

    public abstract void cboEmpresaClearClick(final Event<Object> event);

    public abstract void cboDepartamentoChange(final Event<Object> event);

    public abstract void cboDepartamentoClearClick(final Event<Object> event);

    public abstract void cboTipoDaFormaDePagamentoChange(final Event<Object> event);

    public abstract void cboTipoDaFormaDePagamentoClearClick(final Event<Object> event);

    public abstract void cboFormaDePagamentoExclusivaChange(final Event<Object> event);

    public abstract void cboFormaDePagamentoExclusivaClearClick(final Event<Object> event);

    public abstract void cboFormaDePagamentoChange(final Event<Object> event);

    public abstract void cboFormaDePagamentoClearClick(final Event<Object> event);

    public abstract void cboCondicaoDePagamentoExclusivaChange(final Event<Object> event);

    public abstract void cboCondicaoDePagamentoExclusivaClearClick(final Event<Object> event);

    public abstract void cboCondicaoDePagamentoChange(final Event<Object> event);

    public abstract void cboCondicaoDePagamentoClearClick(final Event<Object> event);

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridPgtosdesmarcarRegistroNaGradeGridPgtos(final Event<Object> event);

    public abstract void gridPgtosmarcarRegistroNaGradeGridPgtos(final Event<Object> event);

    public abstract void mmSelecionarTodosOsRegistrosDaGradeGridPgtosClick(final Event<Object> event);

    public abstract void mmSelecionarNenhumRegistroDaGradeGridPgtosClick(final Event<Object> event);

}