package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAcessoEmpresas extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AcessoEmpresasRNA rn = null;

    public FrmAcessoEmpresas() {
        try {
            rn = (freedom.bytecode.rn.AcessoEmpresasRNA) getRN(freedom.bytecode.rn.wizard.AcessoEmpresasRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresasFuncoes();
        init_tbEmpresasFuncoesLkp();
        init_tbEmpresas();
        init_tbEmpresasLkp();
        init_tbCrmEmpresaFuncao();
        init_tbEmpresasSelecionada();
        init_FHBox6();
        init_FHBox1();
        init_btnConsultar();
        init_btnFiltroAvancado();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnSalvarContinuar();
        init_btnCancelar();
        init_FHBox2();
        init_btnAnterior();
        init_btnProximo();
        init_FHBox8();
        init_FHBox7();
        init_btnAceitar();
        init_btnMais();
        init_FHBox10();
        init_iconClassHelp();
        init_FVBox1();
        init_FHBox3();
        init_FHBox9();
        init_FLabel1();
        init_cboEmpresaSelecionada();
        init_FVBox2();
        init_FLabel4();
        init_FHBox4();
        init_FHBox5();
        init_gridFuncao();
        init_gridEmpresa();
        init_FVBox6();
        init_FVBox7();
        init_btnIncluirFuncaoEmpresa();
        init_btnExcluirFuncaoEmpresa();
        init_FVBox8();
        init_FVBox9();
        init_FGridPanel1();
        init_FLabel3();
        init_FLabel2();
        init_cmbEmpresa();
        init_cmbFuncao();
        init_gridEmpresaFuncao();
        init_FrmAcessoEmpresas();
    }

    public EMPRESAS_FUNCOES tbEmpresasFuncoes;

    private void init_tbEmpresasFuncoes() {
        tbEmpresasFuncoes = rn.tbEmpresasFuncoes;
        tbEmpresasFuncoes.setName("tbEmpresasFuncoes");
        tbEmpresasFuncoes.setMaxRowCount(200);
        tbEmpresasFuncoes.setWKey("340037;34002");
        tbEmpresasFuncoes.setRatioBatchSize(20);
        getTables().put(tbEmpresasFuncoes, "tbEmpresasFuncoes");
        tbEmpresasFuncoes.applyProperties();
    }

    public EMPRESAS_FUNCOES tbEmpresasFuncoesLkp;

    private void init_tbEmpresasFuncoesLkp() {
        tbEmpresasFuncoesLkp = rn.tbEmpresasFuncoesLkp;
        tbEmpresasFuncoesLkp.setName("tbEmpresasFuncoesLkp");
        tbEmpresasFuncoesLkp.setMaxRowCount(200);
        tbEmpresasFuncoesLkp.setWKey("340037;34006");
        tbEmpresasFuncoesLkp.setRatioBatchSize(20);
        getTables().put(tbEmpresasFuncoesLkp, "tbEmpresasFuncoesLkp");
        tbEmpresasFuncoesLkp.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.addEventListener("onBeforeOpen", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbEmpresasBeforeOpen(event);
            processarFlow("FrmAcessoEmpresas", "tbEmpresas", "OnBeforeOpen");
        });
        tbEmpresas.setWKey("340037;34003");
        tbEmpresas.setRatioBatchSize(0);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public EMPRESAS tbEmpresasLkp;

    private void init_tbEmpresasLkp() {
        tbEmpresasLkp = rn.tbEmpresasLkp;
        tbEmpresasLkp.setName("tbEmpresasLkp");
        tbEmpresasLkp.setMaxRowCount(200);
        tbEmpresasLkp.addEventListener("onBeforeOpen", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbEmpresasLkpBeforeOpen(event);
            processarFlow("FrmAcessoEmpresas", "tbEmpresasLkp", "OnBeforeOpen");
        });
        tbEmpresasLkp.setWKey("340037;34007");
        tbEmpresasLkp.setRatioBatchSize(20);
        getTables().put(tbEmpresasLkp, "tbEmpresasLkp");
        tbEmpresasLkp.applyProperties();
    }

    public CRM_EMPRESA_FUNCAO tbCrmEmpresaFuncao;

    private void init_tbCrmEmpresaFuncao() {
        tbCrmEmpresaFuncao = rn.tbCrmEmpresaFuncao;
        tbCrmEmpresaFuncao.setName("tbCrmEmpresaFuncao");
        tbCrmEmpresaFuncao.setMaxRowCount(200);
        tbCrmEmpresaFuncao.addEventListener("onBeforeOpen", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbCrmEmpresaFuncaoBeforeOpen(event);
            processarFlow("FrmAcessoEmpresas", "tbCrmEmpresaFuncao", "OnBeforeOpen");
        });
        tbCrmEmpresaFuncao.setWKey("340037;34004");
        tbCrmEmpresaFuncao.setRatioBatchSize(20);
        getTables().put(tbCrmEmpresaFuncao, "tbCrmEmpresaFuncao");
        tbCrmEmpresaFuncao.applyProperties();
    }

    public EMPRESAS tbEmpresasSelecionada;

    private void init_tbEmpresasSelecionada() {
        tbEmpresasSelecionada = rn.tbEmpresasSelecionada;
        tbEmpresasSelecionada.setName("tbEmpresasSelecionada");
        tbEmpresasSelecionada.setMaxRowCount(200);
        tbEmpresasSelecionada.setWKey("340037;34005");
        tbEmpresasSelecionada.setRatioBatchSize(20);
        getTables().put(tbEmpresasSelecionada, "tbEmpresasSelecionada");
        tbEmpresasSelecionada.applyProperties();
    }

    protected TFForm FrmAcessoEmpresas = this;
    private void init_FrmAcessoEmpresas() {
        FrmAcessoEmpresas.setName("FrmAcessoEmpresas");
        FrmAcessoEmpresas.setCaption("Acesso a Empresas");
        FrmAcessoEmpresas.setClientHeight(627);
        FrmAcessoEmpresas.setClientWidth(1085);
        FrmAcessoEmpresas.setColor("clBtnFace");
        FrmAcessoEmpresas.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAcessoEmpresas", "FrmAcessoEmpresas", "OnCreate");
        });
        FrmAcessoEmpresas.setWOrigem("EhMain");
        FrmAcessoEmpresas.setWKey("340037");
        FrmAcessoEmpresas.setSpacing(0);
        FrmAcessoEmpresas.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(1085);
        FHBox6.setHeight(68);
        FHBox6.setAlign("alTop");
        FHBox6.setBorderStyle("stNone");
        FHBox6.setColor("16514043");
        FHBox6.setPaddingTop(5);
        FHBox6.setPaddingLeft(2);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(5);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FrmAcessoEmpresas.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(829);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(2);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FHBox6.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnConsultar = new TFButton();

    private void init_btnConsultar() {
        btnConsultar.setName("btnConsultar");
        btnConsultar.setLeft(0);
        btnConsultar.setTop(0);
        btnConsultar.setWidth(65);
        btnConsultar.setHeight(53);
        btnConsultar.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultar.setCaption("Pesquisar");
        btnConsultar.setFontColor("clWindowText");
        btnConsultar.setFontSize(-11);
        btnConsultar.setFontName("Tahoma");
        btnConsultar.setFontStyle("[]");
        btnConsultar.setLayout("blGlyphTop");
        btnConsultar.setVisible(false);
        btnConsultar.setImageId(13);
        btnConsultar.setColor("clBtnFace");
        btnConsultar.setAccess(true);
        btnConsultar.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultar);
        btnConsultar.applyProperties();
    }

    public TFButton btnFiltroAvancado = new TFButton();

    private void init_btnFiltroAvancado() {
        btnFiltroAvancado.setName("btnFiltroAvancado");
        btnFiltroAvancado.setLeft(65);
        btnFiltroAvancado.setTop(0);
        btnFiltroAvancado.setWidth(65);
        btnFiltroAvancado.setHeight(53);
        btnFiltroAvancado.setHint("Filtro Avan\u00E7ado");
        btnFiltroAvancado.setCaption("Filtro");
        btnFiltroAvancado.setFontColor("clWindowText");
        btnFiltroAvancado.setFontSize(-11);
        btnFiltroAvancado.setFontName("Tahoma");
        btnFiltroAvancado.setFontStyle("[]");
        btnFiltroAvancado.setLayout("blGlyphTop");
        btnFiltroAvancado.setVisible(false);
        btnFiltroAvancado.setImageId(50002);
        btnFiltroAvancado.setColor("clBtnFace");
        btnFiltroAvancado.setAccess(true);
        btnFiltroAvancado.setIconReverseDirection(false);
        FHBox1.addChildren(btnFiltroAvancado);
        btnFiltroAvancado.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(130);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(53);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.setVisible(false);
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(195);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(53);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmAcessoEmpresas", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(260);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(53);
        btnExcluir.setHint("Exclui o Registro Selecionado  (CRTL+ 4)");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.setVisible(false);
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(true);
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(325);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAcessoEmpresas", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnSalvarContinuar = new TFButton();

    private void init_btnSalvarContinuar() {
        btnSalvarContinuar.setName("btnSalvarContinuar");
        btnSalvarContinuar.setLeft(390);
        btnSalvarContinuar.setTop(0);
        btnSalvarContinuar.setWidth(65);
        btnSalvarContinuar.setHeight(53);
        btnSalvarContinuar.setHint("Salvar e Continuar");
        btnSalvarContinuar.setCaption("Salvar Cont.");
        btnSalvarContinuar.setFontColor("clWindowText");
        btnSalvarContinuar.setFontSize(-11);
        btnSalvarContinuar.setFontName("Tahoma");
        btnSalvarContinuar.setFontStyle("[]");
        btnSalvarContinuar.setLayout("blGlyphTop");
        btnSalvarContinuar.setVisible(false);
        btnSalvarContinuar.setImageId(22001);
        btnSalvarContinuar.setColor("clBtnFace");
        btnSalvarContinuar.setAccess(false);
        btnSalvarContinuar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvarContinuar);
        btnSalvarContinuar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(455);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(53);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmAcessoEmpresas", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(520);
        FHBox2.setTop(0);
        FHBox2.setWidth(26);
        FHBox2.setHeight(28);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FHBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnAnterior = new TFButton();

    private void init_btnAnterior() {
        btnAnterior.setName("btnAnterior");
        btnAnterior.setLeft(546);
        btnAnterior.setTop(0);
        btnAnterior.setWidth(65);
        btnAnterior.setHeight(53);
        btnAnterior.setHint("Registro Anterior, Estando em Modo de Inclus\u00E3o/Altera\u00E7\u00E3o Salva Antes de Mover o Registro (CRTL+ 7)");
        btnAnterior.setFontColor("clWindowText");
        btnAnterior.setFontSize(-11);
        btnAnterior.setFontName("Tahoma");
        btnAnterior.setFontStyle("[]");
        btnAnterior.setLayout("blGlyphTop");
        btnAnterior.setVisible(false);
        btnAnterior.setImageId(14);
        btnAnterior.setColor("clBtnFace");
        btnAnterior.setAccess(false);
        btnAnterior.setIconReverseDirection(false);
        FHBox1.addChildren(btnAnterior);
        btnAnterior.applyProperties();
    }

    public TFButton btnProximo = new TFButton();

    private void init_btnProximo() {
        btnProximo.setName("btnProximo");
        btnProximo.setLeft(611);
        btnProximo.setTop(0);
        btnProximo.setWidth(65);
        btnProximo.setHeight(53);
        btnProximo.setHint("Pr\u00F3ximo Registro   (CRTL+ 8)");
        btnProximo.setFontColor("clWindowText");
        btnProximo.setFontSize(-11);
        btnProximo.setFontName("Tahoma");
        btnProximo.setFontStyle("[]");
        btnProximo.setLayout("blGlyphTop");
        btnProximo.setVisible(false);
        btnProximo.setImageId(15);
        btnProximo.setColor("clBtnFace");
        btnProximo.setAccess(false);
        btnProximo.setIconReverseDirection(false);
        FHBox1.addChildren(btnProximo);
        btnProximo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(676);
        FHBox8.setTop(0);
        FHBox8.setWidth(32);
        FHBox8.setHeight(32);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox1.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(829);
        FHBox7.setTop(0);
        FHBox7.setWidth(146);
        FHBox7.setHeight(60);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(2);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(3);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FHBox6.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(0);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(65);
        btnAceitar.setHeight(53);
        btnAceitar.setHint("Aceita o Registro Selecionado para Outro Formulario  (CRTL+ 9)");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.setVisible(false);
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox7.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnMais = new TFButton();

    private void init_btnMais() {
        btnMais.setName("btnMais");
        btnMais.setLeft(65);
        btnMais.setTop(0);
        btnMais.setWidth(65);
        btnMais.setHeight(53);
        btnMais.setHint("Mais Op\u00E7\u00F5es");
        btnMais.setFontColor("clWindowText");
        btnMais.setFontSize(-21);
        btnMais.setFontName("Tahoma");
        btnMais.setFontStyle("[]");
        btnMais.setLayout("blGlyphTop");
        btnMais.setVisible(false);
        btnMais.setImageId(22002);
        btnMais.setColor("clBtnFace");
        btnMais.setAccess(false);
        btnMais.setIconReverseDirection(false);
        FHBox7.addChildren(btnMais);
        btnMais.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(975);
        FHBox10.setTop(0);
        FHBox10.setWidth(85);
        FHBox10.setHeight(59);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(10);
        FHBox10.setPaddingLeft(10);
        FHBox10.setPaddingRight(10);
        FHBox10.setPaddingBottom(10);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox6.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFIconClass iconClassHelp = new TFIconClass();

    private void init_iconClassHelp() {
        iconClassHelp.setName("iconClassHelp");
        iconClassHelp.setLeft(0);
        iconClassHelp.setTop(0);
        iconClassHelp.setHint("Help");
        iconClassHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassHelpClick(event);
            processarFlow("FrmAcessoEmpresas", "iconClassHelp", "OnClick");
        });
        iconClassHelp.setIconClass("question-circle");
        iconClassHelp.setSize(26);
        iconClassHelp.setColor("clRed");
        FHBox10.addChildren(iconClassHelp);
        iconClassHelp.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(68);
        FVBox1.setWidth(1085);
        FVBox1.setHeight(559);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmAcessoEmpresas.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(981);
        FHBox3.setHeight(38);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setColor("clWhite");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(5);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(10);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(351);
        FHBox9.setHeight(21);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(5);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FHBox3.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(349);
        FLabel1.setHeight(14);
        FLabel1.setAlign("alClient");
        FLabel1.setCaption("Este cruzamento vai determinar, para cada fun\u00E7\u00E3o, na empresa ");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-12);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FHBox9.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cboEmpresaSelecionada = new TFCombo();

    private void init_cboEmpresaSelecionada() {
        cboEmpresaSelecionada.setName("cboEmpresaSelecionada");
        cboEmpresaSelecionada.setLeft(351);
        cboEmpresaSelecionada.setTop(0);
        cboEmpresaSelecionada.setWidth(255);
        cboEmpresaSelecionada.setHeight(21);
        cboEmpresaSelecionada.setLookupTable(tbEmpresasSelecionada);
        cboEmpresaSelecionada.setLookupKey("COD_EMPRESA");
        cboEmpresaSelecionada.setLookupDesc("NOME_INITCAP_COD_EMPRESA");
        cboEmpresaSelecionada.setFlex(false);
        cboEmpresaSelecionada.setReadOnly(true);
        cboEmpresaSelecionada.setRequired(false);
        cboEmpresaSelecionada.setPrompt("Selecione");
        cboEmpresaSelecionada.setConstraintCheckWhen("cwImmediate");
        cboEmpresaSelecionada.setConstraintCheckType("ctExpression");
        cboEmpresaSelecionada.setConstraintFocusOnError(false);
        cboEmpresaSelecionada.setConstraintEnableUI(true);
        cboEmpresaSelecionada.setConstraintEnabled(false);
        cboEmpresaSelecionada.setConstraintFormCheck(true);
        cboEmpresaSelecionada.setClearOnDelKey(true);
        cboEmpresaSelecionada.setUseClearButton(true);
        cboEmpresaSelecionada.setHideClearButtonOnNullValue(true);
        cboEmpresaSelecionada.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaSelecionadaChange(event);
            processarFlow("FrmAcessoEmpresas", "cboEmpresaSelecionada", "OnChange");
        });
        FHBox3.addChildren(cboEmpresaSelecionada);
        cboEmpresaSelecionada.applyProperties();
        addValidatable(cboEmpresaSelecionada);
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(606);
        FVBox2.setTop(0);
        FVBox2.setWidth(185);
        FVBox2.setHeight(22);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(5);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(143);
        FLabel4.setHeight(14);
        FLabel4.setAlign("alClient");
        FLabel4.setCaption("QUAIS empresas enxerga.");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-12);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FVBox2.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(39);
        FHBox4.setWidth(981);
        FHBox4.setHeight(393);
        FHBox4.setAlign("alClient");
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftTrue");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox1.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(960);
        FHBox5.setHeight(335);
        FHBox5.setAlign("alClient");
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftTrue");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox4.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFGrid gridFuncao = new TFGrid();

    private void init_gridFuncao() {
        gridFuncao.setName("gridFuncao");
        gridFuncao.setLeft(0);
        gridFuncao.setTop(0);
        gridFuncao.setWidth(161);
        gridFuncao.setHeight(297);
        gridFuncao.setAlign("alClient");
        gridFuncao.setTable(tbEmpresasFuncoes);
        gridFuncao.setFlexVflex("ftTrue");
        gridFuncao.setFlexHflex("ftTrue");
        gridFuncao.setPagingEnabled(false);
        gridFuncao.setFrozenColumns(0);
        gridFuncao.setShowFooter(false);
        gridFuncao.setShowHeader(true);
        gridFuncao.setMultiSelection(true);
        gridFuncao.setGroupingEnabled(false);
        gridFuncao.setGroupingExpanded(false);
        gridFuncao.setGroupingShowFooter(false);
        gridFuncao.setCrosstabEnabled(false);
        gridFuncao.setCrosstabGroupType("cgtConcat");
        gridFuncao.setEditionEnabled(false);
        gridFuncao.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO_CODFUNCAO");
        item0.setTitleCaption("Fun\u00E7\u00E3o");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridFuncao.getColumns().add(item0);
        FHBox5.addChildren(gridFuncao);
        gridFuncao.applyProperties();
    }

    public TFGrid gridEmpresa = new TFGrid();

    private void init_gridEmpresa() {
        gridEmpresa.setName("gridEmpresa");
        gridEmpresa.setLeft(161);
        gridEmpresa.setTop(0);
        gridEmpresa.setWidth(159);
        gridEmpresa.setHeight(297);
        gridEmpresa.setAlign("alClient");
        gridEmpresa.setTable(tbEmpresas);
        gridEmpresa.setFlexVflex("ftTrue");
        gridEmpresa.setFlexHflex("ftTrue");
        gridEmpresa.setPagingEnabled(false);
        gridEmpresa.setFrozenColumns(0);
        gridEmpresa.setShowFooter(false);
        gridEmpresa.setShowHeader(true);
        gridEmpresa.setMultiSelection(true);
        gridEmpresa.setGroupingEnabled(false);
        gridEmpresa.setGroupingExpanded(false);
        gridEmpresa.setGroupingShowFooter(false);
        gridEmpresa.setCrosstabEnabled(false);
        gridEmpresa.setCrosstabGroupType("cgtConcat");
        gridEmpresa.setEditionEnabled(false);
        gridEmpresa.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_INITCAP_COD_EMPRESA");
        item0.setTitleCaption("Empresa");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridEmpresa.getColumns().add(item0);
        FHBox5.addChildren(gridEmpresa);
        gridEmpresa.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(320);
        FVBox6.setTop(0);
        FVBox6.setWidth(27);
        FVBox6.setHeight(203);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(10);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftFalse");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox5.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(0);
        FVBox7.setWidth(23);
        FVBox7.setHeight(41);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FVBox6.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFButton btnIncluirFuncaoEmpresa = new TFButton();

    private void init_btnIncluirFuncaoEmpresa() {
        btnIncluirFuncaoEmpresa.setName("btnIncluirFuncaoEmpresa");
        btnIncluirFuncaoEmpresa.setLeft(0);
        btnIncluirFuncaoEmpresa.setTop(42);
        btnIncluirFuncaoEmpresa.setWidth(25);
        btnIncluirFuncaoEmpresa.setHeight(25);
        btnIncluirFuncaoEmpresa.setAlign("alBottom");
        btnIncluirFuncaoEmpresa.setFontColor("clWindowText");
        btnIncluirFuncaoEmpresa.setFontSize(-11);
        btnIncluirFuncaoEmpresa.setFontName("Tahoma");
        btnIncluirFuncaoEmpresa.setFontStyle("[]");
        btnIncluirFuncaoEmpresa.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirFuncaoEmpresaClick(event);
            processarFlow("FrmAcessoEmpresas", "btnIncluirFuncaoEmpresa", "OnClick");
        });
        btnIncluirFuncaoEmpresa.setImageId(34002);
        btnIncluirFuncaoEmpresa.setColor("clBtnFace");
        btnIncluirFuncaoEmpresa.setAccess(false);
        btnIncluirFuncaoEmpresa.setIconReverseDirection(false);
        FVBox6.addChildren(btnIncluirFuncaoEmpresa);
        btnIncluirFuncaoEmpresa.applyProperties();
    }

    public TFButton btnExcluirFuncaoEmpresa = new TFButton();

    private void init_btnExcluirFuncaoEmpresa() {
        btnExcluirFuncaoEmpresa.setName("btnExcluirFuncaoEmpresa");
        btnExcluirFuncaoEmpresa.setLeft(0);
        btnExcluirFuncaoEmpresa.setTop(68);
        btnExcluirFuncaoEmpresa.setWidth(25);
        btnExcluirFuncaoEmpresa.setHeight(25);
        btnExcluirFuncaoEmpresa.setAlign("alBottom");
        btnExcluirFuncaoEmpresa.setFontColor("clWindowText");
        btnExcluirFuncaoEmpresa.setFontSize(-11);
        btnExcluirFuncaoEmpresa.setFontName("Tahoma");
        btnExcluirFuncaoEmpresa.setFontStyle("[]");
        btnExcluirFuncaoEmpresa.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirFuncaoEmpresaClick(event);
            processarFlow("FrmAcessoEmpresas", "btnExcluirFuncaoEmpresa", "OnClick");
        });
        btnExcluirFuncaoEmpresa.setImageId(34003);
        btnExcluirFuncaoEmpresa.setColor("clBtnFace");
        btnExcluirFuncaoEmpresa.setAccess(false);
        btnExcluirFuncaoEmpresa.setIconReverseDirection(false);
        FVBox6.addChildren(btnExcluirFuncaoEmpresa);
        btnExcluirFuncaoEmpresa.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(94);
        FVBox8.setWidth(23);
        FVBox8.setHeight(41);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftTrue");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FVBox6.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(347);
        FVBox9.setTop(0);
        FVBox9.setWidth(550);
        FVBox9.setHeight(296);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftFalse");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FHBox5.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFGridPanel FGridPanel1 = new TFGridPanel();

    private void init_FGridPanel1() {
        FGridPanel1.setName("FGridPanel1");
        FGridPanel1.setLeft(0);
        FGridPanel1.setTop(0);
        FGridPanel1.setWidth(441);
        FGridPanel1.setHeight(57);
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(60.000000000000000000);
        FGridPanel1.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        FGridPanel1.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("FLabel3");
        item2.setRow(0);
        FGridPanel1.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(0);
        item3.setControl("FLabel2");
        item3.setRow(1);
        FGridPanel1.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(1);
        item4.setControl("cmbEmpresa");
        item4.setRow(0);
        FGridPanel1.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("cmbFuncao");
        item5.setRow(1);
        FGridPanel1.getControlCollection().add(item5);
        TFGridPanelRow item6 = new TFGridPanelRow();
        item6.setSizeStyle("ssAbsolute");
        item6.setValue(30.000000000000000000);
        FGridPanel1.getRowCollection().add(item6);
        TFGridPanelRow item7 = new TFGridPanelRow();
        item7.setSizeStyle("ssAbsolute");
        item7.setValue(30.000000000000000000);
        FGridPanel1.getRowCollection().add(item7);
        FGridPanel1.setFlexVflex("ftFalse");
        FGridPanel1.setFlexHflex("ftTrue");
        FGridPanel1.setAllRowFlex(false);
        FGridPanel1.setColumnTabOrder(false);
        FVBox9.addChildren(FGridPanel1);
        FGridPanel1.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(1);
        FLabel3.setTop(1);
        FLabel3.setWidth(60);
        FLabel3.setHeight(13);
        FLabel3.setAlign("alTop");
        FLabel3.setCaption("Empresas");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FGridPanel1.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(1);
        FLabel2.setTop(31);
        FLabel2.setWidth(60);
        FLabel2.setHeight(13);
        FLabel2.setAlign("alTop");
        FLabel2.setCaption("Fun\u00E7\u00E3o");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FGridPanel1.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cmbEmpresa = new TFCombo();

    private void init_cmbEmpresa() {
        cmbEmpresa.setName("cmbEmpresa");
        cmbEmpresa.setLeft(61);
        cmbEmpresa.setTop(1);
        cmbEmpresa.setWidth(379);
        cmbEmpresa.setHeight(21);
        cmbEmpresa.setLookupTable(tbEmpresasLkp);
        cmbEmpresa.setLookupKey("COD_EMPRESA");
        cmbEmpresa.setLookupDesc("NOME_INITCAP_COD_EMPRESA");
        cmbEmpresa.setFlex(true);
        cmbEmpresa.setReadOnly(true);
        cmbEmpresa.setRequired(false);
        cmbEmpresa.setPrompt("Selecione");
        cmbEmpresa.setConstraintCheckWhen("cwImmediate");
        cmbEmpresa.setConstraintCheckType("ctExpression");
        cmbEmpresa.setConstraintFocusOnError(false);
        cmbEmpresa.setConstraintEnableUI(true);
        cmbEmpresa.setConstraintEnabled(false);
        cmbEmpresa.setConstraintFormCheck(true);
        cmbEmpresa.setClearOnDelKey(true);
        cmbEmpresa.setUseClearButton(true);
        cmbEmpresa.setHideClearButtonOnNullValue(true);
        cmbEmpresa.setAlign("alTop");
        cmbEmpresa.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbEmpresaChange(event);
            processarFlow("FrmAcessoEmpresas", "cmbEmpresa", "OnChange");
        });
        FGridPanel1.addChildren(cmbEmpresa);
        cmbEmpresa.applyProperties();
        addValidatable(cmbEmpresa);
    }

    public TFCombo cmbFuncao = new TFCombo();

    private void init_cmbFuncao() {
        cmbFuncao.setName("cmbFuncao");
        cmbFuncao.setLeft(61);
        cmbFuncao.setTop(31);
        cmbFuncao.setWidth(379);
        cmbFuncao.setHeight(21);
        cmbFuncao.setLookupTable(tbEmpresasFuncoesLkp);
        cmbFuncao.setLookupKey("COD_FUNCAO");
        cmbFuncao.setLookupDesc("DESCRICAO_CODFUNCAO");
        cmbFuncao.setFlex(true);
        cmbFuncao.setReadOnly(true);
        cmbFuncao.setRequired(false);
        cmbFuncao.setPrompt("Selecione");
        cmbFuncao.setConstraintCheckWhen("cwImmediate");
        cmbFuncao.setConstraintCheckType("ctExpression");
        cmbFuncao.setConstraintFocusOnError(false);
        cmbFuncao.setConstraintEnableUI(true);
        cmbFuncao.setConstraintEnabled(false);
        cmbFuncao.setConstraintFormCheck(true);
        cmbFuncao.setClearOnDelKey(true);
        cmbFuncao.setUseClearButton(true);
        cmbFuncao.setHideClearButtonOnNullValue(true);
        cmbFuncao.setAlign("alTop");
        cmbFuncao.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cmbFuncaoChange(event);
            processarFlow("FrmAcessoEmpresas", "cmbFuncao", "OnChange");
        });
        FGridPanel1.addChildren(cmbFuncao);
        cmbFuncao.applyProperties();
        addValidatable(cmbFuncao);
    }

    public TFGrid gridEmpresaFuncao = new TFGrid();

    private void init_gridEmpresaFuncao() {
        gridEmpresaFuncao.setName("gridEmpresaFuncao");
        gridEmpresaFuncao.setLeft(0);
        gridEmpresaFuncao.setTop(58);
        gridEmpresaFuncao.setWidth(441);
        gridEmpresaFuncao.setHeight(149);
        gridEmpresaFuncao.setAlign("alTop");
        gridEmpresaFuncao.setTable(tbCrmEmpresaFuncao);
        gridEmpresaFuncao.setFlexVflex("ftTrue");
        gridEmpresaFuncao.setFlexHflex("ftTrue");
        gridEmpresaFuncao.setPagingEnabled(false);
        gridEmpresaFuncao.setFrozenColumns(0);
        gridEmpresaFuncao.setShowFooter(false);
        gridEmpresaFuncao.setShowHeader(true);
        gridEmpresaFuncao.setMultiSelection(true);
        gridEmpresaFuncao.setGroupingEnabled(false);
        gridEmpresaFuncao.setGroupingExpanded(false);
        gridEmpresaFuncao.setGroupingShowFooter(false);
        gridEmpresaFuncao.setCrosstabEnabled(false);
        gridEmpresaFuncao.setCrosstabGroupType("cgtConcat");
        gridEmpresaFuncao.setEditionEnabled(false);
        gridEmpresaFuncao.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_COD_FUNCAO");
        item0.setTitleCaption("Fun\u00E7\u00E3o");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridEmpresaFuncao.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NOME_INITCAP_COD_EMPRESA");
        item1.setTitleCaption("Empresa");
        item1.setWidth(40);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridEmpresaFuncao.getColumns().add(item1);
        FVBox9.addChildren(gridEmpresaFuncao);
        gridEmpresaFuncao.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void cboEmpresaSelecionadaChange(final Event<Object> event);

    public void btnIncluirFuncaoEmpresaClick(final Event<Object> event) {
        if (btnIncluirFuncaoEmpresa.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirFuncaoEmpresa");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirFuncaoEmpresaClick(final Event<Object> event) {
        if (btnExcluirFuncaoEmpresa.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirFuncaoEmpresa");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cmbEmpresaChange(final Event<Object> event);

    public abstract void cmbFuncaoChange(final Event<Object> event);

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void iconClassHelpClick(final Event<Object> event);

    public abstract void tbEmpresasBeforeOpen(final Event<Object> event);

    public abstract void tbEmpresasLkpBeforeOpen(final Event<Object> event);

    public abstract void tbCrmEmpresaFuncaoBeforeOpen(final Event<Object> event);

}