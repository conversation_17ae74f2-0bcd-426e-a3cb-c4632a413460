package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPgtoVendaInf extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PgtoVendaInfRNA rn = null;

    public FrmPgtoVendaInf() {
        try {
            rn = (freedom.bytecode.rn.PgtoVendaInfRNA) getRN(freedom.bytecode.rn.wizard.PgtoVendaInfRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbLeadsPgtoVendaInfCartao();
        init_tbLeadsCartoes();
        init_tbLeadsPgtoVendaInfBoleto();
        init_tbLeadsFormaPgtoCheque();
        init_tbLeadsPgtoVendaInfCheque();
        init_FHBox28();
        init_vBoxTopo();
        init_hBoxBotoesTopo();
        init_FHBox35();
        init_btnCancelar();
        init_btnConfirmar();
        init_FHBox2();
        init_pgcInfoPgto();
        init_tbsCartoes();
        init_vBoxInformeCartoes();
        init_FHBox27();
        init_hBoxValorCartoes();
        init_FHBox4();
        init_vBoxCartaoValorTotal();
        init_lblCartoesValorTotal();
        init_edtCartaoValorTotal();
        init_FHBox31();
        init_FVBox10();
        init_FLabel4();
        init_edtCartaoValorFaltaLanc();
        init_FHBox68();
        init_FVBox8();
        init_lblCartoesObservacao();
        init_edtCartoesObservacao();
        init_FHBox69();
        init_FHBox29();
        init_FHBox24();
        init_FVBox1();
        init_FHBox20();
        init_FHBox5();
        init_FHBox110();
        init_FVBox56();
        init_FHBox108();
        init_lblSetor();
        init_FHBox23();
        init_edtCartaoValor();
        init_FHBox6();
        init_FHBox8();
        init_FVBox2();
        init_FHBox9();
        init_lblCartaoQtdeParc();
        init_FHBox21();
        init_edtCartaoQtdParc();
        init_FHBox7();
        init_FHBox10();
        init_FVBox3();
        init_FHBox11();
        init_lblCartaoNrAut();
        init_FHBox22();
        init_edtCartaoNrAut();
        init_FHBox12();
        init_FHBox13();
        init_FVBox4();
        init_FHBox14();
        init_FLabel1();
        init_cbbCartao();
        init_FHBox15();
        init_FHBox16();
        init_FHBox17();
        init_btnIncluirCartoes();
        init_FHBox18();
        init_btnExcluirCartoes();
        init_FHBox3();
        init_FHBox25();
        init_vBoxGridCartoes();
        init_grdCartoesInformados();
        init_hBoxEditarCartoes();
        init_FVBox36();
        init_FVBox37();
        init_FLabel3();
        init_edtCartaoNomeCartao();
        init_FVBox7();
        init_lblCartaoParc();
        init_edtCartaoParcCartao();
        init_FVBox38();
        init_FVBox41();
        init_lblCartaoNrAutAlterar();
        init_edtCartaoAlterarNrAut();
        init_vBoxCartaoAlterar();
        init_hBoxCartoesTopAlterar();
        init_FHBox72();
        init_btnAlterarCartao();
        init_FHBox76();
        init_FVBox42();
        init_vBoxCartaoSalvarAlteracao();
        init_hBoxCartoesTopSalvarAlterar();
        init_FHBox78();
        init_btnCartaoSalvarAlteracao();
        init_FHBox79();
        init_btnCartaoCancelarAlteracao();
        init_FHBox80();
        init_FHBox39();
        init_FHBox34();
        init_tbsCheques();
        init_vBoxInformeCheques();
        init_FHBox48();
        init_FHBox50();
        init_FVBox15();
        init_FHBox51();
        init_lblChequesValorTotal();
        init_FVBox16();
        init_FHBox52();
        init_edtChequeValorTotal();
        init_FHBox54();
        init_FVBox17();
        init_FHBox55();
        init_lblChequesObservacao();
        init_FVBox18();
        init_FHBox56();
        init_edtChequesObservacao();
        init_FHBox70();
        init_hBoxInformeCheques();
        init_FHBox57();
        init_FVBox27();
        init_grdChequesCondicao();
        init_FHBox58();
        init_FHBox19();
        init_btnIncluirCheques();
        init_FHBox59();
        init_btnExcluirCheques();
        init_FHBox64();
        init_FHBox65();
        init_FVBox29();
        init_FHBox66();
        init_FLabel2();
        init_FVBox30();
        init_FHBox67();
        init_edtChequeValorFaltaLancar();
        init_FHBox60();
        init_vBoxGridCheques();
        init_grdChequesInformados();
        init_hBoxEditarCheques();
        init_FVBox5();
        init_FVBox28();
        init_lblChequeNr();
        init_edtChequeNumero();
        init_FVBox33();
        init_FVBox34();
        init_lblChequeVencimento();
        init_edtChequeDtVencimento();
        init_FVBox35();
        init_FVBox32();
        init_FLabel6();
        init_edtChequeValor();
        init_FVBox31();
        init_vBoxChequeAlterar();
        init_hBoxChequeBtnAlterarTop();
        init_FHBox73();
        init_btnChequeAlterar();
        init_FHBox74();
        init_btnChequeExcluir();
        init_vBoxChequesSalvarAlteracoes();
        init_hBoxChequeBtnSalvarTop();
        init_FHBox71();
        init_btnChequeConfirmar();
        init_FHBox75();
        init_btnChequeCancelar();
        init_tbsBoleto();
        init_vBoxBoletoBancario();
        init_FHBox40();
        init_FHBox41();
        init_FVBox12();
        init_FHBox42();
        init_FLabel5();
        init_FVBox13();
        init_FHBox43();
        init_edtBoletoValorTotal();
        init_FHBox44();
        init_FHBox45();
        init_FHBox46();
        init_FVBox14();
        init_grdBoletos();
        init_FHBox47();
        init_FHBox49();
        init_FHBox170004();
        init_FPanel170004();
        init_btnEditDetalheBoleto();
        init_FPanel1();
        init_btnConfirmarBoleto();
        init_FPanel2();
        init_btnCancelarBoleto();
        init_hBoxDetalheBoleto();
        init_FVBox19();
        init_FVBox20();
        init_lblBoletoParcela();
        init_edtBoletoParc();
        init_FVBox21();
        init_FVBox22();
        init_lblBoletoValor();
        init_edtBoletoValor();
        init_FVBox23();
        init_FVBox24();
        init_lblBoletoVencimento();
        init_edtBoletoVencimento();
        init_FVBox25();
        init_FVBox26();
        init_lblBoletoNossoNumero();
        init_edtBoletoNossoNumero();
        init_FVBox6();
        init_FHBox53();
        init_FrmPgtoVendaInf();
    }

    public LEADS_PGTO_VENDA_INF_CARTAO tbLeadsPgtoVendaInfCartao;

    private void init_tbLeadsPgtoVendaInfCartao() {
        tbLeadsPgtoVendaInfCartao = rn.tbLeadsPgtoVendaInfCartao;
        tbLeadsPgtoVendaInfCartao.setName("tbLeadsPgtoVendaInfCartao");
        tbLeadsPgtoVendaInfCartao.setMaxRowCount(200);
        tbLeadsPgtoVendaInfCartao.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsPgtoVendaInfCartaoAfterScroll(event);
            processarFlow("FrmPgtoVendaInf", "tbLeadsPgtoVendaInfCartao", "OnAfterScroll");
        });
        tbLeadsPgtoVendaInfCartao.setWKey("183028;18301");
        tbLeadsPgtoVendaInfCartao.setRatioBatchSize(20);
        getTables().put(tbLeadsPgtoVendaInfCartao, "tbLeadsPgtoVendaInfCartao");
        tbLeadsPgtoVendaInfCartao.applyProperties();
    }

    public LEADS_CARTOES tbLeadsCartoes;

    private void init_tbLeadsCartoes() {
        tbLeadsCartoes = rn.tbLeadsCartoes;
        tbLeadsCartoes.setName("tbLeadsCartoes");
        tbLeadsCartoes.setMaxRowCount(200);
        tbLeadsCartoes.setWKey("183028;18302");
        tbLeadsCartoes.setRatioBatchSize(20);
        getTables().put(tbLeadsCartoes, "tbLeadsCartoes");
        tbLeadsCartoes.applyProperties();
    }

    public LEADS_PGTO_VENDA_INF_BOLETO tbLeadsPgtoVendaInfBoleto;

    private void init_tbLeadsPgtoVendaInfBoleto() {
        tbLeadsPgtoVendaInfBoleto = rn.tbLeadsPgtoVendaInfBoleto;
        tbLeadsPgtoVendaInfBoleto.setName("tbLeadsPgtoVendaInfBoleto");
        tbLeadsPgtoVendaInfBoleto.setMaxRowCount(200);
        tbLeadsPgtoVendaInfBoleto.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsPgtoVendaInfBoletoAfterScroll(event);
            processarFlow("FrmPgtoVendaInf", "tbLeadsPgtoVendaInfBoleto", "OnAfterScroll");
        });
        tbLeadsPgtoVendaInfBoleto.setWKey("183028;18303");
        tbLeadsPgtoVendaInfBoleto.setRatioBatchSize(20);
        getTables().put(tbLeadsPgtoVendaInfBoleto, "tbLeadsPgtoVendaInfBoleto");
        tbLeadsPgtoVendaInfBoleto.applyProperties();
    }

    public LEADS_PGTO_VENDA_FORMA_CHEQUE tbLeadsFormaPgtoCheque;

    private void init_tbLeadsFormaPgtoCheque() {
        tbLeadsFormaPgtoCheque = rn.tbLeadsFormaPgtoCheque;
        tbLeadsFormaPgtoCheque.setName("tbLeadsFormaPgtoCheque");
        tbLeadsFormaPgtoCheque.setMaxRowCount(200);
        tbLeadsFormaPgtoCheque.setWKey("183028;18304");
        tbLeadsFormaPgtoCheque.setRatioBatchSize(20);
        getTables().put(tbLeadsFormaPgtoCheque, "tbLeadsFormaPgtoCheque");
        tbLeadsFormaPgtoCheque.applyProperties();
    }

    public LEADS_PGTO_VENDA_INF_CHEQUE tbLeadsPgtoVendaInfCheque;

    private void init_tbLeadsPgtoVendaInfCheque() {
        tbLeadsPgtoVendaInfCheque = rn.tbLeadsPgtoVendaInfCheque;
        tbLeadsPgtoVendaInfCheque.setName("tbLeadsPgtoVendaInfCheque");
        tbLeadsPgtoVendaInfCheque.setMaxRowCount(200);
        tbLeadsPgtoVendaInfCheque.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsPgtoVendaInfChequeAfterScroll(event);
            processarFlow("FrmPgtoVendaInf", "tbLeadsPgtoVendaInfCheque", "OnAfterScroll");
        });
        tbLeadsPgtoVendaInfCheque.setWKey("183028;18305");
        tbLeadsPgtoVendaInfCheque.setRatioBatchSize(20);
        getTables().put(tbLeadsPgtoVendaInfCheque, "tbLeadsPgtoVendaInfCheque");
        tbLeadsPgtoVendaInfCheque.applyProperties();
    }

    protected TFForm FrmPgtoVendaInf = this;
    private void init_FrmPgtoVendaInf() {
        FrmPgtoVendaInf.setName("FrmPgtoVendaInf");
        FrmPgtoVendaInf.setCaption("Informe o valor do Pagamento");
        FrmPgtoVendaInf.setClientHeight(443);
        FrmPgtoVendaInf.setClientWidth(854);
        FrmPgtoVendaInf.setColor("clBtnFace");
        FrmPgtoVendaInf.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            frmShow(event);
            processarFlow("FrmPgtoVendaInf", "FrmPgtoVendaInf", "OnCreate");
        });
        FrmPgtoVendaInf.setWOrigem("EhMain");
        FrmPgtoVendaInf.setWKey("183028");
        FrmPgtoVendaInf.setSpacing(0);
        FrmPgtoVendaInf.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(0);
        FHBox28.setTop(0);
        FHBox28.setWidth(68);
        FHBox28.setHeight(4);
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(0);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(0);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(1);
        FHBox28.setFlexVflex("ftFalse");
        FHBox28.setFlexHflex("ftTrue");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvTop");
        FrmPgtoVendaInf.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFVBox vBoxTopo = new TFVBox();

    private void init_vBoxTopo() {
        vBoxTopo.setName("vBoxTopo");
        vBoxTopo.setLeft(0);
        vBoxTopo.setTop(4);
        vBoxTopo.setWidth(768);
        vBoxTopo.setHeight(61);
        vBoxTopo.setBorderStyle("stNone");
        vBoxTopo.setPaddingTop(0);
        vBoxTopo.setPaddingLeft(0);
        vBoxTopo.setPaddingRight(0);
        vBoxTopo.setPaddingBottom(0);
        vBoxTopo.setMarginTop(0);
        vBoxTopo.setMarginLeft(0);
        vBoxTopo.setMarginRight(0);
        vBoxTopo.setMarginBottom(0);
        vBoxTopo.setSpacing(1);
        vBoxTopo.setFlexVflex("ftFalse");
        vBoxTopo.setFlexHflex("ftTrue");
        vBoxTopo.setScrollable(false);
        vBoxTopo.setBoxShadowConfigHorizontalLength(10);
        vBoxTopo.setBoxShadowConfigVerticalLength(10);
        vBoxTopo.setBoxShadowConfigBlurRadius(5);
        vBoxTopo.setBoxShadowConfigSpreadRadius(0);
        vBoxTopo.setBoxShadowConfigShadowColor("clBlack");
        vBoxTopo.setBoxShadowConfigOpacity(75);
        FrmPgtoVendaInf.addChildren(vBoxTopo);
        vBoxTopo.applyProperties();
    }

    public TFHBox hBoxBotoesTopo = new TFHBox();

    private void init_hBoxBotoesTopo() {
        hBoxBotoesTopo.setName("hBoxBotoesTopo");
        hBoxBotoesTopo.setLeft(0);
        hBoxBotoesTopo.setTop(0);
        hBoxBotoesTopo.setWidth(739);
        hBoxBotoesTopo.setHeight(57);
        hBoxBotoesTopo.setBorderStyle("stNone");
        hBoxBotoesTopo.setPaddingTop(1);
        hBoxBotoesTopo.setPaddingLeft(1);
        hBoxBotoesTopo.setPaddingRight(0);
        hBoxBotoesTopo.setPaddingBottom(0);
        hBoxBotoesTopo.setMarginTop(0);
        hBoxBotoesTopo.setMarginLeft(0);
        hBoxBotoesTopo.setMarginRight(0);
        hBoxBotoesTopo.setMarginBottom(0);
        hBoxBotoesTopo.setSpacing(3);
        hBoxBotoesTopo.setFlexVflex("ftFalse");
        hBoxBotoesTopo.setFlexHflex("ftTrue");
        hBoxBotoesTopo.setScrollable(false);
        hBoxBotoesTopo.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesTopo.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesTopo.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesTopo.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesTopo.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesTopo.setBoxShadowConfigOpacity(75);
        hBoxBotoesTopo.setVAlign("tvTop");
        vBoxTopo.addChildren(hBoxBotoesTopo);
        hBoxBotoesTopo.applyProperties();
    }

    public TFHBox FHBox35 = new TFHBox();

    private void init_FHBox35() {
        FHBox35.setName("FHBox35");
        FHBox35.setLeft(0);
        FHBox35.setTop(0);
        FHBox35.setWidth(4);
        FHBox35.setHeight(12);
        FHBox35.setBorderStyle("stNone");
        FHBox35.setPaddingTop(0);
        FHBox35.setPaddingLeft(0);
        FHBox35.setPaddingRight(0);
        FHBox35.setPaddingBottom(0);
        FHBox35.setMarginTop(0);
        FHBox35.setMarginLeft(0);
        FHBox35.setMarginRight(0);
        FHBox35.setMarginBottom(0);
        FHBox35.setSpacing(1);
        FHBox35.setFlexVflex("ftFalse");
        FHBox35.setFlexHflex("ftFalse");
        FHBox35.setScrollable(false);
        FHBox35.setBoxShadowConfigHorizontalLength(10);
        FHBox35.setBoxShadowConfigVerticalLength(10);
        FHBox35.setBoxShadowConfigBlurRadius(5);
        FHBox35.setBoxShadowConfigSpreadRadius(0);
        FHBox35.setBoxShadowConfigShadowColor("clBlack");
        FHBox35.setBoxShadowConfigOpacity(75);
        FHBox35.setVAlign("tvTop");
        hBoxBotoesTopo.addChildren(FHBox35);
        FHBox35.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(4);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(50);
        btnCancelar.setHint("Voltar...");
        btnCancelar.setCaption("Voltar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmPgtoVendaInf", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(0);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconClass("undo");
        btnCancelar.setIconReverseDirection(false);
        hBoxBotoesTopo.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFButton btnConfirmar = new TFButton();

    private void init_btnConfirmar() {
        btnConfirmar.setName("btnConfirmar");
        btnConfirmar.setLeft(69);
        btnConfirmar.setTop(0);
        btnConfirmar.setWidth(65);
        btnConfirmar.setHeight(50);
        btnConfirmar.setCaption("Confirmar");
        btnConfirmar.setFontColor("clWindowText");
        btnConfirmar.setFontSize(-11);
        btnConfirmar.setFontName("Tahoma");
        btnConfirmar.setFontStyle("[]");
        btnConfirmar.setLayout("blGlyphTop");
        btnConfirmar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarClick(event);
            processarFlow("FrmPgtoVendaInf", "btnConfirmar", "OnClick");
        });
        btnConfirmar.setImageId(0);
        btnConfirmar.setColor("clBtnFace");
        btnConfirmar.setAccess(false);
        btnConfirmar.setIconClass("check");
        btnConfirmar.setIconReverseDirection(false);
        hBoxBotoesTopo.addChildren(btnConfirmar);
        btnConfirmar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(70);
        FHBox2.setWidth(853);
        FHBox2.setHeight(373);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(5);
        FHBox2.setPaddingRight(5);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FrmPgtoVendaInf.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFPageControl pgcInfoPgto = new TFPageControl();

    private void init_pgcInfoPgto() {
        pgcInfoPgto.setName("pgcInfoPgto");
        pgcInfoPgto.setLeft(0);
        pgcInfoPgto.setTop(0);
        pgcInfoPgto.setWidth(847);
        pgcInfoPgto.setHeight(337);
        pgcInfoPgto.setTabPosition("tpTop");
        pgcInfoPgto.setFlexVflex("ftTrue");
        pgcInfoPgto.setFlexHflex("ftTrue");
        pgcInfoPgto.setRenderStyle("rsTabbed");
        pgcInfoPgto.applyProperties();
        FHBox2.addChildren(pgcInfoPgto);
    }

    public TFTabsheet tbsCartoes = new TFTabsheet();

    private void init_tbsCartoes() {
        tbsCartoes.setName("tbsCartoes");
        tbsCartoes.setCaption("Informe cart\u00F5es");
        tbsCartoes.setVisible(true);
        tbsCartoes.setClosable(false);
        pgcInfoPgto.addChildren(tbsCartoes);
        tbsCartoes.applyProperties();
    }

    public TFVBox vBoxInformeCartoes = new TFVBox();

    private void init_vBoxInformeCartoes() {
        vBoxInformeCartoes.setName("vBoxInformeCartoes");
        vBoxInformeCartoes.setLeft(0);
        vBoxInformeCartoes.setTop(0);
        vBoxInformeCartoes.setWidth(837);
        vBoxInformeCartoes.setHeight(308);
        vBoxInformeCartoes.setBorderStyle("stNone");
        vBoxInformeCartoes.setPaddingTop(0);
        vBoxInformeCartoes.setPaddingLeft(0);
        vBoxInformeCartoes.setPaddingRight(0);
        vBoxInformeCartoes.setPaddingBottom(0);
        vBoxInformeCartoes.setMarginTop(0);
        vBoxInformeCartoes.setMarginLeft(0);
        vBoxInformeCartoes.setMarginRight(0);
        vBoxInformeCartoes.setMarginBottom(0);
        vBoxInformeCartoes.setSpacing(5);
        vBoxInformeCartoes.setFlexVflex("ftTrue");
        vBoxInformeCartoes.setFlexHflex("ftTrue");
        vBoxInformeCartoes.setScrollable(false);
        vBoxInformeCartoes.setBoxShadowConfigHorizontalLength(10);
        vBoxInformeCartoes.setBoxShadowConfigVerticalLength(10);
        vBoxInformeCartoes.setBoxShadowConfigBlurRadius(5);
        vBoxInformeCartoes.setBoxShadowConfigSpreadRadius(0);
        vBoxInformeCartoes.setBoxShadowConfigShadowColor("clBlack");
        vBoxInformeCartoes.setBoxShadowConfigOpacity(75);
        tbsCartoes.addChildren(vBoxInformeCartoes);
        vBoxInformeCartoes.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(0);
        FHBox27.setTop(0);
        FHBox27.setWidth(33);
        FHBox27.setHeight(1);
        FHBox27.setBorderStyle("stSingleLine");
        FHBox27.setColor("clBackground");
        FHBox27.setPaddingTop(0);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(0);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(1);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftTrue");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvTop");
        vBoxInformeCartoes.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFHBox hBoxValorCartoes = new TFHBox();

    private void init_hBoxValorCartoes() {
        hBoxValorCartoes.setName("hBoxValorCartoes");
        hBoxValorCartoes.setLeft(0);
        hBoxValorCartoes.setTop(2);
        hBoxValorCartoes.setWidth(831);
        hBoxValorCartoes.setHeight(72);
        hBoxValorCartoes.setBorderStyle("stNone");
        hBoxValorCartoes.setPaddingTop(0);
        hBoxValorCartoes.setPaddingLeft(0);
        hBoxValorCartoes.setPaddingRight(0);
        hBoxValorCartoes.setPaddingBottom(0);
        hBoxValorCartoes.setMarginTop(0);
        hBoxValorCartoes.setMarginLeft(0);
        hBoxValorCartoes.setMarginRight(0);
        hBoxValorCartoes.setMarginBottom(0);
        hBoxValorCartoes.setSpacing(1);
        hBoxValorCartoes.setFlexVflex("ftMin");
        hBoxValorCartoes.setFlexHflex("ftFalse");
        hBoxValorCartoes.setScrollable(false);
        hBoxValorCartoes.setBoxShadowConfigHorizontalLength(10);
        hBoxValorCartoes.setBoxShadowConfigVerticalLength(10);
        hBoxValorCartoes.setBoxShadowConfigBlurRadius(5);
        hBoxValorCartoes.setBoxShadowConfigSpreadRadius(0);
        hBoxValorCartoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxValorCartoes.setBoxShadowConfigOpacity(75);
        hBoxValorCartoes.setVAlign("tvTop");
        vBoxInformeCartoes.addChildren(hBoxValorCartoes);
        hBoxValorCartoes.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(4);
        FHBox4.setHeight(12);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        hBoxValorCartoes.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox vBoxCartaoValorTotal = new TFVBox();

    private void init_vBoxCartaoValorTotal() {
        vBoxCartaoValorTotal.setName("vBoxCartaoValorTotal");
        vBoxCartaoValorTotal.setLeft(4);
        vBoxCartaoValorTotal.setTop(0);
        vBoxCartaoValorTotal.setWidth(115);
        vBoxCartaoValorTotal.setHeight(55);
        vBoxCartaoValorTotal.setBorderStyle("stNone");
        vBoxCartaoValorTotal.setPaddingTop(0);
        vBoxCartaoValorTotal.setPaddingLeft(0);
        vBoxCartaoValorTotal.setPaddingRight(0);
        vBoxCartaoValorTotal.setPaddingBottom(0);
        vBoxCartaoValorTotal.setMarginTop(0);
        vBoxCartaoValorTotal.setMarginLeft(0);
        vBoxCartaoValorTotal.setMarginRight(0);
        vBoxCartaoValorTotal.setMarginBottom(0);
        vBoxCartaoValorTotal.setSpacing(1);
        vBoxCartaoValorTotal.setFlexVflex("ftFalse");
        vBoxCartaoValorTotal.setFlexHflex("ftFalse");
        vBoxCartaoValorTotal.setScrollable(false);
        vBoxCartaoValorTotal.setBoxShadowConfigHorizontalLength(10);
        vBoxCartaoValorTotal.setBoxShadowConfigVerticalLength(10);
        vBoxCartaoValorTotal.setBoxShadowConfigBlurRadius(5);
        vBoxCartaoValorTotal.setBoxShadowConfigSpreadRadius(0);
        vBoxCartaoValorTotal.setBoxShadowConfigShadowColor("clBlack");
        vBoxCartaoValorTotal.setBoxShadowConfigOpacity(75);
        hBoxValorCartoes.addChildren(vBoxCartaoValorTotal);
        vBoxCartaoValorTotal.applyProperties();
    }

    public TFLabel lblCartoesValorTotal = new TFLabel();

    private void init_lblCartoesValorTotal() {
        lblCartoesValorTotal.setName("lblCartoesValorTotal");
        lblCartoesValorTotal.setLeft(0);
        lblCartoesValorTotal.setTop(0);
        lblCartoesValorTotal.setWidth(49);
        lblCartoesValorTotal.setHeight(13);
        lblCartoesValorTotal.setCaption("Valor total");
        lblCartoesValorTotal.setFontColor("clWindowText");
        lblCartoesValorTotal.setFontSize(-11);
        lblCartoesValorTotal.setFontName("Tahoma");
        lblCartoesValorTotal.setFontStyle("[]");
        lblCartoesValorTotal.setVerticalAlignment("taVerticalCenter");
        lblCartoesValorTotal.setWordBreak(false);
        vBoxCartaoValorTotal.addChildren(lblCartoesValorTotal);
        lblCartoesValorTotal.applyProperties();
    }

    public TFDecimal edtCartaoValorTotal = new TFDecimal();

    private void init_edtCartaoValorTotal() {
        edtCartaoValorTotal.setName("edtCartaoValorTotal");
        edtCartaoValorTotal.setLeft(0);
        edtCartaoValorTotal.setTop(14);
        edtCartaoValorTotal.setWidth(108);
        edtCartaoValorTotal.setHeight(24);
        edtCartaoValorTotal.setFlex(true);
        edtCartaoValorTotal.setRequired(false);
        edtCartaoValorTotal.setConstraintCheckWhen("cwImmediate");
        edtCartaoValorTotal.setConstraintCheckType("ctExpression");
        edtCartaoValorTotal.setConstraintFocusOnError(false);
        edtCartaoValorTotal.setConstraintEnableUI(true);
        edtCartaoValorTotal.setConstraintEnabled(false);
        edtCartaoValorTotal.setConstraintFormCheck(true);
        edtCartaoValorTotal.setMaxlength(0);
        edtCartaoValorTotal.setPrecision(0);
        edtCartaoValorTotal.setFontColor("clWindowText");
        edtCartaoValorTotal.setFontSize(-13);
        edtCartaoValorTotal.setFontName("Tahoma");
        edtCartaoValorTotal.setFontStyle("[]");
        edtCartaoValorTotal.setAlignment("taRightJustify");
        vBoxCartaoValorTotal.addChildren(edtCartaoValorTotal);
        edtCartaoValorTotal.applyProperties();
        addValidatable(edtCartaoValorTotal);
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(119);
        FHBox31.setTop(0);
        FHBox31.setWidth(6);
        FHBox31.setHeight(12);
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(0);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(1);
        FHBox31.setFlexVflex("ftFalse");
        FHBox31.setFlexHflex("ftFalse");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        hBoxValorCartoes.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(125);
        FVBox10.setTop(0);
        FVBox10.setWidth(115);
        FVBox10.setHeight(55);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(0);
        FVBox10.setPaddingLeft(0);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(1);
        FVBox10.setFlexVflex("ftFalse");
        FVBox10.setFlexHflex("ftFalse");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        hBoxValorCartoes.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(56);
        FLabel4.setHeight(13);
        FLabel4.setCaption("Falta lan\u00E7ar");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FVBox10.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFDecimal edtCartaoValorFaltaLanc = new TFDecimal();

    private void init_edtCartaoValorFaltaLanc() {
        edtCartaoValorFaltaLanc.setName("edtCartaoValorFaltaLanc");
        edtCartaoValorFaltaLanc.setLeft(0);
        edtCartaoValorFaltaLanc.setTop(14);
        edtCartaoValorFaltaLanc.setWidth(108);
        edtCartaoValorFaltaLanc.setHeight(24);
        edtCartaoValorFaltaLanc.setFlex(true);
        edtCartaoValorFaltaLanc.setRequired(false);
        edtCartaoValorFaltaLanc.setConstraintCheckWhen("cwImmediate");
        edtCartaoValorFaltaLanc.setConstraintCheckType("ctExpression");
        edtCartaoValorFaltaLanc.setConstraintFocusOnError(false);
        edtCartaoValorFaltaLanc.setConstraintEnableUI(true);
        edtCartaoValorFaltaLanc.setConstraintEnabled(false);
        edtCartaoValorFaltaLanc.setConstraintFormCheck(true);
        edtCartaoValorFaltaLanc.setMaxlength(0);
        edtCartaoValorFaltaLanc.setPrecision(0);
        edtCartaoValorFaltaLanc.setFontColor("clWindowText");
        edtCartaoValorFaltaLanc.setFontSize(-13);
        edtCartaoValorFaltaLanc.setFontName("Tahoma");
        edtCartaoValorFaltaLanc.setFontStyle("[]");
        edtCartaoValorFaltaLanc.setAlignment("taRightJustify");
        FVBox10.addChildren(edtCartaoValorFaltaLanc);
        edtCartaoValorFaltaLanc.applyProperties();
        addValidatable(edtCartaoValorFaltaLanc);
    }

    public TFHBox FHBox68 = new TFHBox();

    private void init_FHBox68() {
        FHBox68.setName("FHBox68");
        FHBox68.setLeft(240);
        FHBox68.setTop(0);
        FHBox68.setWidth(4);
        FHBox68.setHeight(12);
        FHBox68.setBorderStyle("stNone");
        FHBox68.setPaddingTop(0);
        FHBox68.setPaddingLeft(0);
        FHBox68.setPaddingRight(0);
        FHBox68.setPaddingBottom(0);
        FHBox68.setMarginTop(0);
        FHBox68.setMarginLeft(0);
        FHBox68.setMarginRight(0);
        FHBox68.setMarginBottom(0);
        FHBox68.setSpacing(1);
        FHBox68.setFlexVflex("ftFalse");
        FHBox68.setFlexHflex("ftFalse");
        FHBox68.setScrollable(false);
        FHBox68.setBoxShadowConfigHorizontalLength(10);
        FHBox68.setBoxShadowConfigVerticalLength(10);
        FHBox68.setBoxShadowConfigBlurRadius(5);
        FHBox68.setBoxShadowConfigSpreadRadius(0);
        FHBox68.setBoxShadowConfigShadowColor("clBlack");
        FHBox68.setBoxShadowConfigOpacity(75);
        FHBox68.setVAlign("tvTop");
        hBoxValorCartoes.addChildren(FHBox68);
        FHBox68.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(244);
        FVBox8.setTop(0);
        FVBox8.setWidth(340);
        FVBox8.setHeight(55);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftFalse");
        FVBox8.setFlexHflex("ftFalse");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        hBoxValorCartoes.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel lblCartoesObservacao = new TFLabel();

    private void init_lblCartoesObservacao() {
        lblCartoesObservacao.setName("lblCartoesObservacao");
        lblCartoesObservacao.setLeft(0);
        lblCartoesObservacao.setTop(0);
        lblCartoesObservacao.setWidth(58);
        lblCartoesObservacao.setHeight(13);
        lblCartoesObservacao.setCaption("Observa\u00E7\u00E3o");
        lblCartoesObservacao.setFontColor("clWindowText");
        lblCartoesObservacao.setFontSize(-11);
        lblCartoesObservacao.setFontName("Tahoma");
        lblCartoesObservacao.setFontStyle("[]");
        lblCartoesObservacao.setVerticalAlignment("taVerticalCenter");
        lblCartoesObservacao.setWordBreak(false);
        FVBox8.addChildren(lblCartoesObservacao);
        lblCartoesObservacao.applyProperties();
    }

    public TFString edtCartoesObservacao = new TFString();

    private void init_edtCartoesObservacao() {
        edtCartoesObservacao.setName("edtCartoesObservacao");
        edtCartoesObservacao.setLeft(0);
        edtCartoesObservacao.setTop(14);
        edtCartoesObservacao.setWidth(341);
        edtCartoesObservacao.setHeight(24);
        edtCartoesObservacao.setFlex(true);
        edtCartoesObservacao.setRequired(false);
        edtCartoesObservacao.setConstraintCheckWhen("cwImmediate");
        edtCartoesObservacao.setConstraintCheckType("ctExpression");
        edtCartoesObservacao.setConstraintFocusOnError(false);
        edtCartoesObservacao.setConstraintEnableUI(true);
        edtCartoesObservacao.setConstraintEnabled(false);
        edtCartoesObservacao.setConstraintFormCheck(true);
        edtCartoesObservacao.setCharCase("ccNormal");
        edtCartoesObservacao.setPwd(false);
        edtCartoesObservacao.setMaxlength(60);
        edtCartoesObservacao.setEnabled(false);
        edtCartoesObservacao.setFontColor("clWindowText");
        edtCartoesObservacao.setFontSize(-13);
        edtCartoesObservacao.setFontName("Tahoma");
        edtCartoesObservacao.setFontStyle("[]");
        edtCartoesObservacao.setSaveLiteralCharacter(false);
        edtCartoesObservacao.applyProperties();
        FVBox8.addChildren(edtCartoesObservacao);
        addValidatable(edtCartoesObservacao);
    }

    public TFHBox FHBox69 = new TFHBox();

    private void init_FHBox69() {
        FHBox69.setName("FHBox69");
        FHBox69.setLeft(584);
        FHBox69.setTop(0);
        FHBox69.setWidth(4);
        FHBox69.setHeight(12);
        FHBox69.setBorderStyle("stNone");
        FHBox69.setPaddingTop(0);
        FHBox69.setPaddingLeft(0);
        FHBox69.setPaddingRight(0);
        FHBox69.setPaddingBottom(0);
        FHBox69.setMarginTop(0);
        FHBox69.setMarginLeft(0);
        FHBox69.setMarginRight(0);
        FHBox69.setMarginBottom(0);
        FHBox69.setSpacing(1);
        FHBox69.setFlexVflex("ftFalse");
        FHBox69.setFlexHflex("ftFalse");
        FHBox69.setScrollable(false);
        FHBox69.setBoxShadowConfigHorizontalLength(10);
        FHBox69.setBoxShadowConfigVerticalLength(10);
        FHBox69.setBoxShadowConfigBlurRadius(5);
        FHBox69.setBoxShadowConfigSpreadRadius(0);
        FHBox69.setBoxShadowConfigShadowColor("clBlack");
        FHBox69.setBoxShadowConfigOpacity(75);
        FHBox69.setVAlign("tvTop");
        hBoxValorCartoes.addChildren(FHBox69);
        FHBox69.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(75);
        FHBox29.setWidth(33);
        FHBox29.setHeight(1);
        FHBox29.setBorderStyle("stSingleLine");
        FHBox29.setColor("clBackground");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(1);
        FHBox29.setFlexVflex("ftFalse");
        FHBox29.setFlexHflex("ftTrue");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        vBoxInformeCartoes.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(0);
        FHBox24.setTop(77);
        FHBox24.setWidth(724);
        FHBox24.setHeight(223);
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(0);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(1);
        FHBox24.setFlexVflex("ftTrue");
        FHBox24.setFlexHflex("ftTrue");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvTop");
        vBoxInformeCartoes.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(236);
        FVBox1.setHeight(215);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox24.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(0);
        FHBox20.setTop(0);
        FHBox20.setWidth(68);
        FHBox20.setHeight(4);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(0);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftTrue");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FVBox1.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(5);
        FHBox5.setWidth(232);
        FHBox5.setHeight(40);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox1.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox110 = new TFHBox();

    private void init_FHBox110() {
        FHBox110.setName("FHBox110");
        FHBox110.setLeft(0);
        FHBox110.setTop(0);
        FHBox110.setWidth(4);
        FHBox110.setHeight(12);
        FHBox110.setBorderStyle("stNone");
        FHBox110.setPaddingTop(0);
        FHBox110.setPaddingLeft(0);
        FHBox110.setPaddingRight(0);
        FHBox110.setPaddingBottom(0);
        FHBox110.setMarginTop(0);
        FHBox110.setMarginLeft(0);
        FHBox110.setMarginRight(0);
        FHBox110.setMarginBottom(0);
        FHBox110.setSpacing(1);
        FHBox110.setFlexVflex("ftFalse");
        FHBox110.setFlexHflex("ftFalse");
        FHBox110.setScrollable(false);
        FHBox110.setBoxShadowConfigHorizontalLength(10);
        FHBox110.setBoxShadowConfigVerticalLength(10);
        FHBox110.setBoxShadowConfigBlurRadius(5);
        FHBox110.setBoxShadowConfigSpreadRadius(0);
        FHBox110.setBoxShadowConfigShadowColor("clBlack");
        FHBox110.setBoxShadowConfigOpacity(75);
        FHBox110.setVAlign("tvTop");
        FHBox5.addChildren(FHBox110);
        FHBox110.applyProperties();
    }

    public TFVBox FVBox56 = new TFVBox();

    private void init_FVBox56() {
        FVBox56.setName("FVBox56");
        FVBox56.setLeft(4);
        FVBox56.setTop(0);
        FVBox56.setWidth(100);
        FVBox56.setHeight(25);
        FVBox56.setBorderStyle("stNone");
        FVBox56.setPaddingTop(0);
        FVBox56.setPaddingLeft(0);
        FVBox56.setPaddingRight(0);
        FVBox56.setPaddingBottom(0);
        FVBox56.setMarginTop(0);
        FVBox56.setMarginLeft(0);
        FVBox56.setMarginRight(0);
        FVBox56.setMarginBottom(0);
        FVBox56.setSpacing(1);
        FVBox56.setFlexVflex("ftFalse");
        FVBox56.setFlexHflex("ftFalse");
        FVBox56.setScrollable(false);
        FVBox56.setBoxShadowConfigHorizontalLength(10);
        FVBox56.setBoxShadowConfigVerticalLength(10);
        FVBox56.setBoxShadowConfigBlurRadius(5);
        FVBox56.setBoxShadowConfigSpreadRadius(0);
        FVBox56.setBoxShadowConfigShadowColor("clBlack");
        FVBox56.setBoxShadowConfigOpacity(75);
        FHBox5.addChildren(FVBox56);
        FVBox56.applyProperties();
    }

    public TFHBox FHBox108 = new TFHBox();

    private void init_FHBox108() {
        FHBox108.setName("FHBox108");
        FHBox108.setLeft(0);
        FHBox108.setTop(0);
        FHBox108.setWidth(68);
        FHBox108.setHeight(4);
        FHBox108.setBorderStyle("stNone");
        FHBox108.setPaddingTop(0);
        FHBox108.setPaddingLeft(0);
        FHBox108.setPaddingRight(0);
        FHBox108.setPaddingBottom(0);
        FHBox108.setMarginTop(0);
        FHBox108.setMarginLeft(0);
        FHBox108.setMarginRight(0);
        FHBox108.setMarginBottom(0);
        FHBox108.setSpacing(1);
        FHBox108.setFlexVflex("ftFalse");
        FHBox108.setFlexHflex("ftTrue");
        FHBox108.setScrollable(false);
        FHBox108.setBoxShadowConfigHorizontalLength(10);
        FHBox108.setBoxShadowConfigVerticalLength(10);
        FHBox108.setBoxShadowConfigBlurRadius(5);
        FHBox108.setBoxShadowConfigSpreadRadius(0);
        FHBox108.setBoxShadowConfigShadowColor("clBlack");
        FHBox108.setBoxShadowConfigOpacity(75);
        FHBox108.setVAlign("tvTop");
        FVBox56.addChildren(FHBox108);
        FHBox108.applyProperties();
    }

    public TFLabel lblSetor = new TFLabel();

    private void init_lblSetor() {
        lblSetor.setName("lblSetor");
        lblSetor.setLeft(0);
        lblSetor.setTop(5);
        lblSetor.setWidth(58);
        lblSetor.setHeight(13);
        lblSetor.setCaption("Valor cart\u00E3o");
        lblSetor.setFontColor("clWindowText");
        lblSetor.setFontSize(-11);
        lblSetor.setFontName("Tahoma");
        lblSetor.setFontStyle("[]");
        lblSetor.setVerticalAlignment("taVerticalCenter");
        lblSetor.setWordBreak(false);
        FVBox56.addChildren(lblSetor);
        lblSetor.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(104);
        FHBox23.setTop(0);
        FHBox23.setWidth(4);
        FHBox23.setHeight(12);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(0);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(1);
        FHBox23.setFlexVflex("ftFalse");
        FHBox23.setFlexHflex("ftTrue");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        FHBox5.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFDecimal edtCartaoValor = new TFDecimal();

    private void init_edtCartaoValor() {
        edtCartaoValor.setName("edtCartaoValor");
        edtCartaoValor.setLeft(108);
        edtCartaoValor.setTop(0);
        edtCartaoValor.setWidth(121);
        edtCartaoValor.setHeight(24);
        edtCartaoValor.setFlex(false);
        edtCartaoValor.setRequired(false);
        edtCartaoValor.setConstraintCheckWhen("cwImmediate");
        edtCartaoValor.setConstraintCheckType("ctExpression");
        edtCartaoValor.setConstraintFocusOnError(false);
        edtCartaoValor.setConstraintEnableUI(true);
        edtCartaoValor.setConstraintEnabled(false);
        edtCartaoValor.setConstraintFormCheck(true);
        edtCartaoValor.setMaxlength(0);
        edtCartaoValor.setPrecision(0);
        edtCartaoValor.setFontColor("clWindowText");
        edtCartaoValor.setFontSize(-13);
        edtCartaoValor.setFontName("Tahoma");
        edtCartaoValor.setFontStyle("[]");
        edtCartaoValor.setAlignment("taRightJustify");
        FHBox5.addChildren(edtCartaoValor);
        edtCartaoValor.applyProperties();
        addValidatable(edtCartaoValor);
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(46);
        FHBox6.setWidth(232);
        FHBox6.setHeight(40);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FVBox1.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(4);
        FHBox8.setHeight(12);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox6.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(4);
        FVBox2.setTop(0);
        FVBox2.setWidth(100);
        FVBox2.setHeight(25);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(68);
        FHBox9.setHeight(4);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox2.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFLabel lblCartaoQtdeParc = new TFLabel();

    private void init_lblCartaoQtdeParc() {
        lblCartaoQtdeParc.setName("lblCartaoQtdeParc");
        lblCartaoQtdeParc.setLeft(0);
        lblCartaoQtdeParc.setTop(5);
        lblCartaoQtdeParc.setWidth(67);
        lblCartaoQtdeParc.setHeight(13);
        lblCartaoQtdeParc.setCaption("Qtde parcelas");
        lblCartaoQtdeParc.setFontColor("clWindowText");
        lblCartaoQtdeParc.setFontSize(-11);
        lblCartaoQtdeParc.setFontName("Tahoma");
        lblCartaoQtdeParc.setFontStyle("[]");
        lblCartaoQtdeParc.setVerticalAlignment("taVerticalCenter");
        lblCartaoQtdeParc.setWordBreak(false);
        FVBox2.addChildren(lblCartaoQtdeParc);
        lblCartaoQtdeParc.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(104);
        FHBox21.setTop(0);
        FHBox21.setWidth(4);
        FHBox21.setHeight(12);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftFalse");
        FHBox21.setFlexHflex("ftTrue");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        FHBox6.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFInteger edtCartaoQtdParc = new TFInteger();

    private void init_edtCartaoQtdParc() {
        edtCartaoQtdParc.setName("edtCartaoQtdParc");
        edtCartaoQtdParc.setLeft(108);
        edtCartaoQtdParc.setTop(0);
        edtCartaoQtdParc.setWidth(47);
        edtCartaoQtdParc.setHeight(24);
        edtCartaoQtdParc.setFlex(false);
        edtCartaoQtdParc.setRequired(false);
        edtCartaoQtdParc.setConstraintCheckWhen("cwImmediate");
        edtCartaoQtdParc.setConstraintCheckType("ctExpression");
        edtCartaoQtdParc.setConstraintFocusOnError(false);
        edtCartaoQtdParc.setConstraintEnableUI(true);
        edtCartaoQtdParc.setConstraintEnabled(false);
        edtCartaoQtdParc.setConstraintFormCheck(true);
        edtCartaoQtdParc.setMaxlength(0);
        edtCartaoQtdParc.setFontColor("clWindowText");
        edtCartaoQtdParc.setFontSize(-13);
        edtCartaoQtdParc.setFontName("Tahoma");
        edtCartaoQtdParc.setFontStyle("[]");
        edtCartaoQtdParc.setAlignment("taRightJustify");
        edtCartaoQtdParc.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCartaoQtdParcExit(event);
            processarFlow("FrmPgtoVendaInf", "edtCartaoQtdParc", "OnExit");
        });
        FHBox6.addChildren(edtCartaoQtdParc);
        edtCartaoQtdParc.applyProperties();
        addValidatable(edtCartaoQtdParc);
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(87);
        FHBox7.setWidth(227);
        FHBox7.setHeight(40);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox1.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(4);
        FHBox10.setHeight(12);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox7.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(4);
        FVBox3.setTop(0);
        FVBox3.setWidth(71);
        FVBox3.setHeight(25);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(68);
        FHBox11.setHeight(4);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox3.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFLabel lblCartaoNrAut = new TFLabel();

    private void init_lblCartaoNrAut() {
        lblCartaoNrAut.setName("lblCartaoNrAut");
        lblCartaoNrAut.setLeft(0);
        lblCartaoNrAut.setTop(5);
        lblCartaoNrAut.setWidth(39);
        lblCartaoNrAut.setHeight(13);
        lblCartaoNrAut.setCaption("Nr. Aut.");
        lblCartaoNrAut.setFontColor("clWindowText");
        lblCartaoNrAut.setFontSize(-11);
        lblCartaoNrAut.setFontName("Tahoma");
        lblCartaoNrAut.setFontStyle("[]");
        lblCartaoNrAut.setVerticalAlignment("taVerticalCenter");
        lblCartaoNrAut.setWordBreak(false);
        FVBox3.addChildren(lblCartaoNrAut);
        lblCartaoNrAut.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(75);
        FHBox22.setTop(0);
        FHBox22.setWidth(4);
        FHBox22.setHeight(12);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(0);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftFalse");
        FHBox22.setFlexHflex("ftTrue");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        FHBox7.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFString edtCartaoNrAut = new TFString();

    private void init_edtCartaoNrAut() {
        edtCartaoNrAut.setName("edtCartaoNrAut");
        edtCartaoNrAut.setLeft(79);
        edtCartaoNrAut.setTop(0);
        edtCartaoNrAut.setWidth(129);
        edtCartaoNrAut.setHeight(24);
        edtCartaoNrAut.setFlex(false);
        edtCartaoNrAut.setRequired(false);
        edtCartaoNrAut.setConstraintCheckWhen("cwImmediate");
        edtCartaoNrAut.setConstraintCheckType("ctExpression");
        edtCartaoNrAut.setConstraintFocusOnError(false);
        edtCartaoNrAut.setConstraintEnableUI(true);
        edtCartaoNrAut.setConstraintEnabled(false);
        edtCartaoNrAut.setConstraintFormCheck(true);
        edtCartaoNrAut.setCharCase("ccNormal");
        edtCartaoNrAut.setPwd(false);
        edtCartaoNrAut.setMaxlength(20);
        edtCartaoNrAut.setFontColor("clWindowText");
        edtCartaoNrAut.setFontSize(-13);
        edtCartaoNrAut.setFontName("Tahoma");
        edtCartaoNrAut.setFontStyle("[]");
        edtCartaoNrAut.setSaveLiteralCharacter(false);
        edtCartaoNrAut.applyProperties();
        FHBox7.addChildren(edtCartaoNrAut);
        addValidatable(edtCartaoNrAut);
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(128);
        FHBox12.setWidth(230);
        FHBox12.setHeight(40);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FVBox1.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(0);
        FHBox13.setWidth(4);
        FHBox13.setHeight(12);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FHBox12.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(4);
        FVBox4.setTop(0);
        FVBox4.setWidth(44);
        FVBox4.setHeight(25);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox12.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(0);
        FHBox14.setWidth(30);
        FHBox14.setHeight(4);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftTrue");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        FVBox4.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(5);
        FLabel1.setWidth(33);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Cart\u00E3o");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox4.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cbbCartao = new TFCombo();

    private void init_cbbCartao() {
        cbbCartao.setName("cbbCartao");
        cbbCartao.setLeft(48);
        cbbCartao.setTop(0);
        cbbCartao.setWidth(145);
        cbbCartao.setHeight(21);
        cbbCartao.setLookupTable(tbLeadsCartoes);
        cbbCartao.setLookupKey("COD_CARTAO_CREDITO");
        cbbCartao.setLookupDesc("DESCRICAO_CARTAO");
        cbbCartao.setFlex(true);
        cbbCartao.setReadOnly(true);
        cbbCartao.setRequired(false);
        cbbCartao.setPrompt("Selecione");
        cbbCartao.setConstraintCheckWhen("cwImmediate");
        cbbCartao.setConstraintCheckType("ctExpression");
        cbbCartao.setConstraintFocusOnError(false);
        cbbCartao.setConstraintEnableUI(true);
        cbbCartao.setConstraintEnabled(false);
        cbbCartao.setConstraintFormCheck(true);
        cbbCartao.setClearOnDelKey(true);
        cbbCartao.setUseClearButton(false);
        cbbCartao.setHideClearButtonOnNullValue(false);
        FHBox12.addChildren(cbbCartao);
        cbbCartao.applyProperties();
        addValidatable(cbbCartao);
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(169);
        FHBox15.setWidth(185);
        FHBox15.setHeight(8);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        FVBox1.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(178);
        FHBox16.setWidth(243);
        FHBox16.setHeight(29);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftTrue");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FVBox1.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(0);
        FHBox17.setWidth(4);
        FHBox17.setHeight(12);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftFalse");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FHBox16.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFButton btnIncluirCartoes = new TFButton();

    private void init_btnIncluirCartoes() {
        btnIncluirCartoes.setName("btnIncluirCartoes");
        btnIncluirCartoes.setLeft(4);
        btnIncluirCartoes.setTop(0);
        btnIncluirCartoes.setWidth(111);
        btnIncluirCartoes.setHeight(25);
        btnIncluirCartoes.setCaption("Incluir Cart\u00F5es");
        btnIncluirCartoes.setFontColor("clWindowText");
        btnIncluirCartoes.setFontSize(-11);
        btnIncluirCartoes.setFontName("Tahoma");
        btnIncluirCartoes.setFontStyle("[]");
        btnIncluirCartoes.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirCartoesClick(event);
            processarFlow("FrmPgtoVendaInf", "btnIncluirCartoes", "OnClick");
        });
        btnIncluirCartoes.setImageId(0);
        btnIncluirCartoes.setColor("clBtnFace");
        btnIncluirCartoes.setAccess(false);
        btnIncluirCartoes.setIconClass("sign-in");
        btnIncluirCartoes.setIconReverseDirection(false);
        FHBox16.addChildren(btnIncluirCartoes);
        btnIncluirCartoes.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(115);
        FHBox18.setTop(0);
        FHBox18.setWidth(4);
        FHBox18.setHeight(12);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftFalse");
        FHBox18.setFlexHflex("ftFalse");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        FHBox16.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFButton btnExcluirCartoes = new TFButton();

    private void init_btnExcluirCartoes() {
        btnExcluirCartoes.setName("btnExcluirCartoes");
        btnExcluirCartoes.setLeft(119);
        btnExcluirCartoes.setTop(0);
        btnExcluirCartoes.setWidth(111);
        btnExcluirCartoes.setHeight(25);
        btnExcluirCartoes.setCaption("Excluir Cart\u00F5es");
        btnExcluirCartoes.setFontColor("clWindowText");
        btnExcluirCartoes.setFontSize(-11);
        btnExcluirCartoes.setFontName("Tahoma");
        btnExcluirCartoes.setFontStyle("[]");
        btnExcluirCartoes.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirCartoesClick(event);
            processarFlow("FrmPgtoVendaInf", "btnExcluirCartoes", "OnClick");
        });
        btnExcluirCartoes.setImageId(0);
        btnExcluirCartoes.setColor("clBtnFace");
        btnExcluirCartoes.setAccess(false);
        btnExcluirCartoes.setIconClass("trash-o");
        btnExcluirCartoes.setIconReverseDirection(false);
        FHBox16.addChildren(btnExcluirCartoes);
        btnExcluirCartoes.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(208);
        FHBox3.setWidth(33);
        FHBox3.setHeight(4);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(236);
        FHBox25.setTop(0);
        FHBox25.setWidth(4);
        FHBox25.setHeight(12);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(0);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(0);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(1);
        FHBox25.setFlexVflex("ftFalse");
        FHBox25.setFlexHflex("ftFalse");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvTop");
        FHBox24.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFVBox vBoxGridCartoes = new TFVBox();

    private void init_vBoxGridCartoes() {
        vBoxGridCartoes.setName("vBoxGridCartoes");
        vBoxGridCartoes.setLeft(240);
        vBoxGridCartoes.setTop(0);
        vBoxGridCartoes.setWidth(464);
        vBoxGridCartoes.setHeight(219);
        vBoxGridCartoes.setBorderStyle("stNone");
        vBoxGridCartoes.setPaddingTop(0);
        vBoxGridCartoes.setPaddingLeft(0);
        vBoxGridCartoes.setPaddingRight(0);
        vBoxGridCartoes.setPaddingBottom(0);
        vBoxGridCartoes.setMarginTop(0);
        vBoxGridCartoes.setMarginLeft(0);
        vBoxGridCartoes.setMarginRight(0);
        vBoxGridCartoes.setMarginBottom(0);
        vBoxGridCartoes.setSpacing(1);
        vBoxGridCartoes.setFlexVflex("ftTrue");
        vBoxGridCartoes.setFlexHflex("ftTrue");
        vBoxGridCartoes.setScrollable(false);
        vBoxGridCartoes.setBoxShadowConfigHorizontalLength(10);
        vBoxGridCartoes.setBoxShadowConfigVerticalLength(10);
        vBoxGridCartoes.setBoxShadowConfigBlurRadius(5);
        vBoxGridCartoes.setBoxShadowConfigSpreadRadius(0);
        vBoxGridCartoes.setBoxShadowConfigShadowColor("clBlack");
        vBoxGridCartoes.setBoxShadowConfigOpacity(75);
        FHBox24.addChildren(vBoxGridCartoes);
        vBoxGridCartoes.applyProperties();
    }

    public TFGrid grdCartoesInformados = new TFGrid();

    private void init_grdCartoesInformados() {
        grdCartoesInformados.setName("grdCartoesInformados");
        grdCartoesInformados.setLeft(0);
        grdCartoesInformados.setTop(0);
        grdCartoesInformados.setWidth(456);
        grdCartoesInformados.setHeight(120);
        grdCartoesInformados.setTable(tbLeadsPgtoVendaInfCartao);
        grdCartoesInformados.setFlexVflex("ftTrue");
        grdCartoesInformados.setFlexHflex("ftTrue");
        grdCartoesInformados.setPagingEnabled(false);
        grdCartoesInformados.setFrozenColumns(0);
        grdCartoesInformados.setShowFooter(false);
        grdCartoesInformados.setShowHeader(true);
        grdCartoesInformados.setMultiSelection(false);
        grdCartoesInformados.setGroupingEnabled(false);
        grdCartoesInformados.setGroupingExpanded(false);
        grdCartoesInformados.setGroupingShowFooter(false);
        grdCartoesInformados.setCrosstabEnabled(false);
        grdCartoesInformados.setCrosstabGroupType("cgtConcat");
        grdCartoesInformados.setEditionEnabled(false);
        grdCartoesInformados.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME");
        item0.setTitleCaption("Cart\u00E3o");
        item0.setWidth(148);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdCartoesInformados.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NR_AUTORIZACAO_VISA");
        item1.setTitleCaption("Nr. Autoriza\u00E7\u00E3o");
        item1.setWidth(160);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdCartoesInformados.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("PARCELA");
        item2.setTitleCaption("Parc");
        item2.setWidth(40);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdCartoesInformados.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("DATA_VENCIMENTO");
        item3.setTitleCaption("Dt.Vencimento");
        item3.setWidth(90);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taCenter");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("dd/MM/yyyy");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDateTime");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdCartoesInformados.getColumns().add(item3);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("VALOR");
        item5.setTitleCaption("Valor");
        item5.setWidth(105);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taRight");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFMaskExpression item6 = new TFMaskExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setMask(",##0.00");
        item6.setPadLength(0);
        item6.setPadDirection("pdNone");
        item6.setMaskType("mtDecimal");
        item5.getMasks().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdCartoesInformados.getColumns().add(item5);
        vBoxGridCartoes.addChildren(grdCartoesInformados);
        grdCartoesInformados.applyProperties();
    }

    public TFHBox hBoxEditarCartoes = new TFHBox();

    private void init_hBoxEditarCartoes() {
        hBoxEditarCartoes.setName("hBoxEditarCartoes");
        hBoxEditarCartoes.setLeft(0);
        hBoxEditarCartoes.setTop(121);
        hBoxEditarCartoes.setWidth(781);
        hBoxEditarCartoes.setHeight(50);
        hBoxEditarCartoes.setBorderStyle("stNone");
        hBoxEditarCartoes.setPaddingTop(0);
        hBoxEditarCartoes.setPaddingLeft(0);
        hBoxEditarCartoes.setPaddingRight(0);
        hBoxEditarCartoes.setPaddingBottom(0);
        hBoxEditarCartoes.setMarginTop(0);
        hBoxEditarCartoes.setMarginLeft(0);
        hBoxEditarCartoes.setMarginRight(0);
        hBoxEditarCartoes.setMarginBottom(0);
        hBoxEditarCartoes.setSpacing(1);
        hBoxEditarCartoes.setFlexVflex("ftFalse");
        hBoxEditarCartoes.setFlexHflex("ftFalse");
        hBoxEditarCartoes.setScrollable(false);
        hBoxEditarCartoes.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarCartoes.setBoxShadowConfigVerticalLength(10);
        hBoxEditarCartoes.setBoxShadowConfigBlurRadius(5);
        hBoxEditarCartoes.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarCartoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarCartoes.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.setVAlign("tvTop");
        vBoxGridCartoes.addChildren(hBoxEditarCartoes);
        hBoxEditarCartoes.applyProperties();
    }

    public TFVBox FVBox36 = new TFVBox();

    private void init_FVBox36() {
        FVBox36.setName("FVBox36");
        FVBox36.setLeft(0);
        FVBox36.setTop(0);
        FVBox36.setWidth(8);
        FVBox36.setHeight(41);
        FVBox36.setBorderStyle("stNone");
        FVBox36.setPaddingTop(0);
        FVBox36.setPaddingLeft(0);
        FVBox36.setPaddingRight(0);
        FVBox36.setPaddingBottom(0);
        FVBox36.setMarginTop(0);
        FVBox36.setMarginLeft(0);
        FVBox36.setMarginRight(0);
        FVBox36.setMarginBottom(0);
        FVBox36.setSpacing(1);
        FVBox36.setFlexVflex("ftFalse");
        FVBox36.setFlexHflex("ftFalse");
        FVBox36.setScrollable(false);
        FVBox36.setBoxShadowConfigHorizontalLength(10);
        FVBox36.setBoxShadowConfigVerticalLength(10);
        FVBox36.setBoxShadowConfigBlurRadius(5);
        FVBox36.setBoxShadowConfigSpreadRadius(0);
        FVBox36.setBoxShadowConfigShadowColor("clBlack");
        FVBox36.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(FVBox36);
        FVBox36.applyProperties();
    }

    public TFVBox FVBox37 = new TFVBox();

    private void init_FVBox37() {
        FVBox37.setName("FVBox37");
        FVBox37.setLeft(8);
        FVBox37.setTop(0);
        FVBox37.setWidth(140);
        FVBox37.setHeight(45);
        FVBox37.setBorderStyle("stNone");
        FVBox37.setPaddingTop(0);
        FVBox37.setPaddingLeft(0);
        FVBox37.setPaddingRight(0);
        FVBox37.setPaddingBottom(0);
        FVBox37.setMarginTop(0);
        FVBox37.setMarginLeft(0);
        FVBox37.setMarginRight(0);
        FVBox37.setMarginBottom(0);
        FVBox37.setSpacing(1);
        FVBox37.setFlexVflex("ftTrue");
        FVBox37.setFlexHflex("ftFalse");
        FVBox37.setScrollable(false);
        FVBox37.setBoxShadowConfigHorizontalLength(10);
        FVBox37.setBoxShadowConfigVerticalLength(10);
        FVBox37.setBoxShadowConfigBlurRadius(5);
        FVBox37.setBoxShadowConfigSpreadRadius(0);
        FVBox37.setBoxShadowConfigShadowColor("clBlack");
        FVBox37.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(FVBox37);
        FVBox37.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(33);
        FLabel3.setHeight(13);
        FLabel3.setAlign("alLeft");
        FLabel3.setCaption("Cart\u00E3o");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FVBox37.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFString edtCartaoNomeCartao = new TFString();

    private void init_edtCartaoNomeCartao() {
        edtCartaoNomeCartao.setName("edtCartaoNomeCartao");
        edtCartaoNomeCartao.setLeft(0);
        edtCartaoNomeCartao.setTop(14);
        edtCartaoNomeCartao.setWidth(133);
        edtCartaoNomeCartao.setHeight(24);
        edtCartaoNomeCartao.setFlex(true);
        edtCartaoNomeCartao.setRequired(false);
        edtCartaoNomeCartao.setConstraintCheckWhen("cwImmediate");
        edtCartaoNomeCartao.setConstraintCheckType("ctExpression");
        edtCartaoNomeCartao.setConstraintFocusOnError(false);
        edtCartaoNomeCartao.setConstraintEnableUI(true);
        edtCartaoNomeCartao.setConstraintEnabled(false);
        edtCartaoNomeCartao.setConstraintFormCheck(true);
        edtCartaoNomeCartao.setCharCase("ccNormal");
        edtCartaoNomeCartao.setPwd(false);
        edtCartaoNomeCartao.setMaxlength(0);
        edtCartaoNomeCartao.setEnabled(false);
        edtCartaoNomeCartao.setFontColor("clWindowText");
        edtCartaoNomeCartao.setFontSize(-13);
        edtCartaoNomeCartao.setFontName("Tahoma");
        edtCartaoNomeCartao.setFontStyle("[]");
        edtCartaoNomeCartao.setSaveLiteralCharacter(false);
        edtCartaoNomeCartao.applyProperties();
        FVBox37.addChildren(edtCartaoNomeCartao);
        addValidatable(edtCartaoNomeCartao);
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(148);
        FVBox7.setTop(0);
        FVBox7.setWidth(37);
        FVBox7.setHeight(45);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftFalse");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFLabel lblCartaoParc = new TFLabel();

    private void init_lblCartaoParc() {
        lblCartaoParc.setName("lblCartaoParc");
        lblCartaoParc.setLeft(0);
        lblCartaoParc.setTop(0);
        lblCartaoParc.setWidth(35);
        lblCartaoParc.setHeight(13);
        lblCartaoParc.setAlign("alLeft");
        lblCartaoParc.setCaption("Parcela");
        lblCartaoParc.setFontColor("clWindowText");
        lblCartaoParc.setFontSize(-11);
        lblCartaoParc.setFontName("Tahoma");
        lblCartaoParc.setFontStyle("[]");
        lblCartaoParc.setVerticalAlignment("taVerticalCenter");
        lblCartaoParc.setWordBreak(false);
        FVBox7.addChildren(lblCartaoParc);
        lblCartaoParc.applyProperties();
    }

    public TFString edtCartaoParcCartao = new TFString();

    private void init_edtCartaoParcCartao() {
        edtCartaoParcCartao.setName("edtCartaoParcCartao");
        edtCartaoParcCartao.setLeft(0);
        edtCartaoParcCartao.setTop(14);
        edtCartaoParcCartao.setWidth(31);
        edtCartaoParcCartao.setHeight(24);
        edtCartaoParcCartao.setFlex(true);
        edtCartaoParcCartao.setRequired(false);
        edtCartaoParcCartao.setConstraintCheckWhen("cwImmediate");
        edtCartaoParcCartao.setConstraintCheckType("ctExpression");
        edtCartaoParcCartao.setConstraintFocusOnError(false);
        edtCartaoParcCartao.setConstraintEnableUI(true);
        edtCartaoParcCartao.setConstraintEnabled(false);
        edtCartaoParcCartao.setConstraintFormCheck(true);
        edtCartaoParcCartao.setCharCase("ccNormal");
        edtCartaoParcCartao.setPwd(false);
        edtCartaoParcCartao.setMaxlength(20);
        edtCartaoParcCartao.setEnabled(false);
        edtCartaoParcCartao.setFontColor("clWindowText");
        edtCartaoParcCartao.setFontSize(-13);
        edtCartaoParcCartao.setFontName("Tahoma");
        edtCartaoParcCartao.setFontStyle("[]");
        edtCartaoParcCartao.setAlignment("taCenter");
        edtCartaoParcCartao.setSaveLiteralCharacter(false);
        edtCartaoParcCartao.applyProperties();
        FVBox7.addChildren(edtCartaoParcCartao);
        addValidatable(edtCartaoParcCartao);
    }

    public TFVBox FVBox38 = new TFVBox();

    private void init_FVBox38() {
        FVBox38.setName("FVBox38");
        FVBox38.setLeft(185);
        FVBox38.setTop(0);
        FVBox38.setWidth(4);
        FVBox38.setHeight(41);
        FVBox38.setBorderStyle("stNone");
        FVBox38.setPaddingTop(0);
        FVBox38.setPaddingLeft(0);
        FVBox38.setPaddingRight(0);
        FVBox38.setPaddingBottom(0);
        FVBox38.setMarginTop(0);
        FVBox38.setMarginLeft(0);
        FVBox38.setMarginRight(0);
        FVBox38.setMarginBottom(0);
        FVBox38.setSpacing(1);
        FVBox38.setFlexVflex("ftFalse");
        FVBox38.setFlexHflex("ftFalse");
        FVBox38.setScrollable(false);
        FVBox38.setBoxShadowConfigHorizontalLength(10);
        FVBox38.setBoxShadowConfigVerticalLength(10);
        FVBox38.setBoxShadowConfigBlurRadius(5);
        FVBox38.setBoxShadowConfigSpreadRadius(0);
        FVBox38.setBoxShadowConfigShadowColor("clBlack");
        FVBox38.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(FVBox38);
        FVBox38.applyProperties();
    }

    public TFVBox FVBox41 = new TFVBox();

    private void init_FVBox41() {
        FVBox41.setName("FVBox41");
        FVBox41.setLeft(189);
        FVBox41.setTop(0);
        FVBox41.setWidth(135);
        FVBox41.setHeight(45);
        FVBox41.setBorderStyle("stNone");
        FVBox41.setPaddingTop(0);
        FVBox41.setPaddingLeft(0);
        FVBox41.setPaddingRight(0);
        FVBox41.setPaddingBottom(0);
        FVBox41.setMarginTop(0);
        FVBox41.setMarginLeft(0);
        FVBox41.setMarginRight(0);
        FVBox41.setMarginBottom(0);
        FVBox41.setSpacing(1);
        FVBox41.setFlexVflex("ftTrue");
        FVBox41.setFlexHflex("ftFalse");
        FVBox41.setScrollable(false);
        FVBox41.setBoxShadowConfigHorizontalLength(10);
        FVBox41.setBoxShadowConfigVerticalLength(10);
        FVBox41.setBoxShadowConfigBlurRadius(5);
        FVBox41.setBoxShadowConfigSpreadRadius(0);
        FVBox41.setBoxShadowConfigShadowColor("clBlack");
        FVBox41.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(FVBox41);
        FVBox41.applyProperties();
    }

    public TFLabel lblCartaoNrAutAlterar = new TFLabel();

    private void init_lblCartaoNrAutAlterar() {
        lblCartaoNrAutAlterar.setName("lblCartaoNrAutAlterar");
        lblCartaoNrAutAlterar.setLeft(0);
        lblCartaoNrAutAlterar.setTop(0);
        lblCartaoNrAutAlterar.setWidth(75);
        lblCartaoNrAutAlterar.setHeight(13);
        lblCartaoNrAutAlterar.setAlign("alLeft");
        lblCartaoNrAutAlterar.setCaption("Nr. Autoriza\u00E7\u00E3o");
        lblCartaoNrAutAlterar.setFontColor("clWindowText");
        lblCartaoNrAutAlterar.setFontSize(-11);
        lblCartaoNrAutAlterar.setFontName("Tahoma");
        lblCartaoNrAutAlterar.setFontStyle("[]");
        lblCartaoNrAutAlterar.setVerticalAlignment("taVerticalCenter");
        lblCartaoNrAutAlterar.setWordBreak(false);
        FVBox41.addChildren(lblCartaoNrAutAlterar);
        lblCartaoNrAutAlterar.applyProperties();
    }

    public TFString edtCartaoAlterarNrAut = new TFString();

    private void init_edtCartaoAlterarNrAut() {
        edtCartaoAlterarNrAut.setName("edtCartaoAlterarNrAut");
        edtCartaoAlterarNrAut.setLeft(0);
        edtCartaoAlterarNrAut.setTop(14);
        edtCartaoAlterarNrAut.setWidth(129);
        edtCartaoAlterarNrAut.setHeight(24);
        edtCartaoAlterarNrAut.setFlex(true);
        edtCartaoAlterarNrAut.setRequired(false);
        edtCartaoAlterarNrAut.setConstraintCheckWhen("cwImmediate");
        edtCartaoAlterarNrAut.setConstraintCheckType("ctExpression");
        edtCartaoAlterarNrAut.setConstraintFocusOnError(false);
        edtCartaoAlterarNrAut.setConstraintEnableUI(true);
        edtCartaoAlterarNrAut.setConstraintEnabled(false);
        edtCartaoAlterarNrAut.setConstraintFormCheck(true);
        edtCartaoAlterarNrAut.setCharCase("ccNormal");
        edtCartaoAlterarNrAut.setPwd(false);
        edtCartaoAlterarNrAut.setMaxlength(20);
        edtCartaoAlterarNrAut.setFontColor("clWindowText");
        edtCartaoAlterarNrAut.setFontSize(-13);
        edtCartaoAlterarNrAut.setFontName("Tahoma");
        edtCartaoAlterarNrAut.setFontStyle("[]");
        edtCartaoAlterarNrAut.setSaveLiteralCharacter(false);
        edtCartaoAlterarNrAut.applyProperties();
        FVBox41.addChildren(edtCartaoAlterarNrAut);
        addValidatable(edtCartaoAlterarNrAut);
    }

    public TFVBox vBoxCartaoAlterar = new TFVBox();

    private void init_vBoxCartaoAlterar() {
        vBoxCartaoAlterar.setName("vBoxCartaoAlterar");
        vBoxCartaoAlterar.setLeft(324);
        vBoxCartaoAlterar.setTop(0);
        vBoxCartaoAlterar.setWidth(154);
        vBoxCartaoAlterar.setHeight(41);
        vBoxCartaoAlterar.setBorderStyle("stNone");
        vBoxCartaoAlterar.setPaddingTop(0);
        vBoxCartaoAlterar.setPaddingLeft(0);
        vBoxCartaoAlterar.setPaddingRight(0);
        vBoxCartaoAlterar.setPaddingBottom(0);
        vBoxCartaoAlterar.setMarginTop(0);
        vBoxCartaoAlterar.setMarginLeft(0);
        vBoxCartaoAlterar.setMarginRight(0);
        vBoxCartaoAlterar.setMarginBottom(0);
        vBoxCartaoAlterar.setSpacing(1);
        vBoxCartaoAlterar.setFlexVflex("ftTrue");
        vBoxCartaoAlterar.setFlexHflex("ftFalse");
        vBoxCartaoAlterar.setScrollable(false);
        vBoxCartaoAlterar.setBoxShadowConfigHorizontalLength(10);
        vBoxCartaoAlterar.setBoxShadowConfigVerticalLength(10);
        vBoxCartaoAlterar.setBoxShadowConfigBlurRadius(5);
        vBoxCartaoAlterar.setBoxShadowConfigSpreadRadius(0);
        vBoxCartaoAlterar.setBoxShadowConfigShadowColor("clBlack");
        vBoxCartaoAlterar.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(vBoxCartaoAlterar);
        vBoxCartaoAlterar.applyProperties();
    }

    public TFHBox hBoxCartoesTopAlterar = new TFHBox();

    private void init_hBoxCartoesTopAlterar() {
        hBoxCartoesTopAlterar.setName("hBoxCartoesTopAlterar");
        hBoxCartoesTopAlterar.setLeft(0);
        hBoxCartoesTopAlterar.setTop(0);
        hBoxCartoesTopAlterar.setWidth(33);
        hBoxCartoesTopAlterar.setHeight(13);
        hBoxCartoesTopAlterar.setBorderStyle("stNone");
        hBoxCartoesTopAlterar.setPaddingTop(0);
        hBoxCartoesTopAlterar.setPaddingLeft(0);
        hBoxCartoesTopAlterar.setPaddingRight(0);
        hBoxCartoesTopAlterar.setPaddingBottom(0);
        hBoxCartoesTopAlterar.setMarginTop(0);
        hBoxCartoesTopAlterar.setMarginLeft(0);
        hBoxCartoesTopAlterar.setMarginRight(0);
        hBoxCartoesTopAlterar.setMarginBottom(0);
        hBoxCartoesTopAlterar.setSpacing(1);
        hBoxCartoesTopAlterar.setFlexVflex("ftFalse");
        hBoxCartoesTopAlterar.setFlexHflex("ftTrue");
        hBoxCartoesTopAlterar.setScrollable(false);
        hBoxCartoesTopAlterar.setBoxShadowConfigHorizontalLength(10);
        hBoxCartoesTopAlterar.setBoxShadowConfigVerticalLength(10);
        hBoxCartoesTopAlterar.setBoxShadowConfigBlurRadius(5);
        hBoxCartoesTopAlterar.setBoxShadowConfigSpreadRadius(0);
        hBoxCartoesTopAlterar.setBoxShadowConfigShadowColor("clBlack");
        hBoxCartoesTopAlterar.setBoxShadowConfigOpacity(75);
        hBoxCartoesTopAlterar.setVAlign("tvTop");
        vBoxCartaoAlterar.addChildren(hBoxCartoesTopAlterar);
        hBoxCartoesTopAlterar.applyProperties();
    }

    public TFHBox FHBox72 = new TFHBox();

    private void init_FHBox72() {
        FHBox72.setName("FHBox72");
        FHBox72.setLeft(0);
        FHBox72.setTop(14);
        FHBox72.setWidth(152);
        FHBox72.setHeight(34);
        FHBox72.setBorderStyle("stNone");
        FHBox72.setPaddingTop(0);
        FHBox72.setPaddingLeft(0);
        FHBox72.setPaddingRight(0);
        FHBox72.setPaddingBottom(0);
        FHBox72.setMarginTop(0);
        FHBox72.setMarginLeft(0);
        FHBox72.setMarginRight(0);
        FHBox72.setMarginBottom(0);
        FHBox72.setSpacing(1);
        FHBox72.setFlexVflex("ftTrue");
        FHBox72.setFlexHflex("ftFalse");
        FHBox72.setScrollable(false);
        FHBox72.setBoxShadowConfigHorizontalLength(10);
        FHBox72.setBoxShadowConfigVerticalLength(10);
        FHBox72.setBoxShadowConfigBlurRadius(5);
        FHBox72.setBoxShadowConfigSpreadRadius(0);
        FHBox72.setBoxShadowConfigShadowColor("clBlack");
        FHBox72.setBoxShadowConfigOpacity(75);
        FHBox72.setVAlign("tvTop");
        vBoxCartaoAlterar.addChildren(FHBox72);
        FHBox72.applyProperties();
    }

    public TFButton btnAlterarCartao = new TFButton();

    private void init_btnAlterarCartao() {
        btnAlterarCartao.setName("btnAlterarCartao");
        btnAlterarCartao.setLeft(0);
        btnAlterarCartao.setTop(0);
        btnAlterarCartao.setWidth(70);
        btnAlterarCartao.setHeight(25);
        btnAlterarCartao.setHint("Alterar Cart\u00E3o");
        btnAlterarCartao.setCaption("Alterar");
        btnAlterarCartao.setEnabled(false);
        btnAlterarCartao.setFontColor("clWindowText");
        btnAlterarCartao.setFontSize(-11);
        btnAlterarCartao.setFontName("Tahoma");
        btnAlterarCartao.setFontStyle("[]");
        btnAlterarCartao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarCartaoClick(event);
            processarFlow("FrmPgtoVendaInf", "btnAlterarCartao", "OnClick");
        });
        btnAlterarCartao.setImageId(0);
        btnAlterarCartao.setColor("clBtnFace");
        btnAlterarCartao.setAccess(false);
        btnAlterarCartao.setIconClass("edit");
        btnAlterarCartao.setIconReverseDirection(false);
        FHBox72.addChildren(btnAlterarCartao);
        btnAlterarCartao.applyProperties();
    }

    public TFHBox FHBox76 = new TFHBox();

    private void init_FHBox76() {
        FHBox76.setName("FHBox76");
        FHBox76.setLeft(70);
        FHBox76.setTop(0);
        FHBox76.setWidth(4);
        FHBox76.setHeight(12);
        FHBox76.setBorderStyle("stNone");
        FHBox76.setPaddingTop(0);
        FHBox76.setPaddingLeft(0);
        FHBox76.setPaddingRight(0);
        FHBox76.setPaddingBottom(0);
        FHBox76.setMarginTop(0);
        FHBox76.setMarginLeft(0);
        FHBox76.setMarginRight(0);
        FHBox76.setMarginBottom(0);
        FHBox76.setSpacing(1);
        FHBox76.setFlexVflex("ftFalse");
        FHBox76.setFlexHflex("ftFalse");
        FHBox76.setScrollable(false);
        FHBox76.setBoxShadowConfigHorizontalLength(10);
        FHBox76.setBoxShadowConfigVerticalLength(10);
        FHBox76.setBoxShadowConfigBlurRadius(5);
        FHBox76.setBoxShadowConfigSpreadRadius(0);
        FHBox76.setBoxShadowConfigShadowColor("clBlack");
        FHBox76.setBoxShadowConfigOpacity(75);
        FHBox76.setVAlign("tvTop");
        FHBox72.addChildren(FHBox76);
        FHBox76.applyProperties();
    }

    public TFVBox FVBox42 = new TFVBox();

    private void init_FVBox42() {
        FVBox42.setName("FVBox42");
        FVBox42.setLeft(478);
        FVBox42.setTop(0);
        FVBox42.setWidth(5);
        FVBox42.setHeight(41);
        FVBox42.setBorderStyle("stNone");
        FVBox42.setPaddingTop(0);
        FVBox42.setPaddingLeft(0);
        FVBox42.setPaddingRight(0);
        FVBox42.setPaddingBottom(0);
        FVBox42.setMarginTop(0);
        FVBox42.setMarginLeft(0);
        FVBox42.setMarginRight(0);
        FVBox42.setMarginBottom(0);
        FVBox42.setSpacing(1);
        FVBox42.setFlexVflex("ftFalse");
        FVBox42.setFlexHflex("ftFalse");
        FVBox42.setScrollable(false);
        FVBox42.setBoxShadowConfigHorizontalLength(10);
        FVBox42.setBoxShadowConfigVerticalLength(10);
        FVBox42.setBoxShadowConfigBlurRadius(5);
        FVBox42.setBoxShadowConfigSpreadRadius(0);
        FVBox42.setBoxShadowConfigShadowColor("clBlack");
        FVBox42.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(FVBox42);
        FVBox42.applyProperties();
    }

    public TFVBox vBoxCartaoSalvarAlteracao = new TFVBox();

    private void init_vBoxCartaoSalvarAlteracao() {
        vBoxCartaoSalvarAlteracao.setName("vBoxCartaoSalvarAlteracao");
        vBoxCartaoSalvarAlteracao.setLeft(483);
        vBoxCartaoSalvarAlteracao.setTop(0);
        vBoxCartaoSalvarAlteracao.setWidth(170);
        vBoxCartaoSalvarAlteracao.setHeight(41);
        vBoxCartaoSalvarAlteracao.setBorderStyle("stNone");
        vBoxCartaoSalvarAlteracao.setPaddingTop(0);
        vBoxCartaoSalvarAlteracao.setPaddingLeft(0);
        vBoxCartaoSalvarAlteracao.setPaddingRight(0);
        vBoxCartaoSalvarAlteracao.setPaddingBottom(0);
        vBoxCartaoSalvarAlteracao.setMarginTop(0);
        vBoxCartaoSalvarAlteracao.setMarginLeft(0);
        vBoxCartaoSalvarAlteracao.setMarginRight(0);
        vBoxCartaoSalvarAlteracao.setMarginBottom(0);
        vBoxCartaoSalvarAlteracao.setSpacing(1);
        vBoxCartaoSalvarAlteracao.setFlexVflex("ftTrue");
        vBoxCartaoSalvarAlteracao.setFlexHflex("ftFalse");
        vBoxCartaoSalvarAlteracao.setScrollable(false);
        vBoxCartaoSalvarAlteracao.setBoxShadowConfigHorizontalLength(10);
        vBoxCartaoSalvarAlteracao.setBoxShadowConfigVerticalLength(10);
        vBoxCartaoSalvarAlteracao.setBoxShadowConfigBlurRadius(5);
        vBoxCartaoSalvarAlteracao.setBoxShadowConfigSpreadRadius(0);
        vBoxCartaoSalvarAlteracao.setBoxShadowConfigShadowColor("clBlack");
        vBoxCartaoSalvarAlteracao.setBoxShadowConfigOpacity(75);
        hBoxEditarCartoes.addChildren(vBoxCartaoSalvarAlteracao);
        vBoxCartaoSalvarAlteracao.applyProperties();
    }

    public TFHBox hBoxCartoesTopSalvarAlterar = new TFHBox();

    private void init_hBoxCartoesTopSalvarAlterar() {
        hBoxCartoesTopSalvarAlterar.setName("hBoxCartoesTopSalvarAlterar");
        hBoxCartoesTopSalvarAlterar.setLeft(0);
        hBoxCartoesTopSalvarAlterar.setTop(0);
        hBoxCartoesTopSalvarAlterar.setWidth(33);
        hBoxCartoesTopSalvarAlterar.setHeight(13);
        hBoxCartoesTopSalvarAlterar.setBorderStyle("stNone");
        hBoxCartoesTopSalvarAlterar.setPaddingTop(0);
        hBoxCartoesTopSalvarAlterar.setPaddingLeft(0);
        hBoxCartoesTopSalvarAlterar.setPaddingRight(0);
        hBoxCartoesTopSalvarAlterar.setPaddingBottom(0);
        hBoxCartoesTopSalvarAlterar.setMarginTop(0);
        hBoxCartoesTopSalvarAlterar.setMarginLeft(0);
        hBoxCartoesTopSalvarAlterar.setMarginRight(0);
        hBoxCartoesTopSalvarAlterar.setMarginBottom(0);
        hBoxCartoesTopSalvarAlterar.setSpacing(1);
        hBoxCartoesTopSalvarAlterar.setFlexVflex("ftFalse");
        hBoxCartoesTopSalvarAlterar.setFlexHflex("ftTrue");
        hBoxCartoesTopSalvarAlterar.setScrollable(false);
        hBoxCartoesTopSalvarAlterar.setBoxShadowConfigHorizontalLength(10);
        hBoxCartoesTopSalvarAlterar.setBoxShadowConfigVerticalLength(10);
        hBoxCartoesTopSalvarAlterar.setBoxShadowConfigBlurRadius(5);
        hBoxCartoesTopSalvarAlterar.setBoxShadowConfigSpreadRadius(0);
        hBoxCartoesTopSalvarAlterar.setBoxShadowConfigShadowColor("clBlack");
        hBoxCartoesTopSalvarAlterar.setBoxShadowConfigOpacity(75);
        hBoxCartoesTopSalvarAlterar.setVAlign("tvTop");
        vBoxCartaoSalvarAlteracao.addChildren(hBoxCartoesTopSalvarAlterar);
        hBoxCartoesTopSalvarAlterar.applyProperties();
    }

    public TFHBox FHBox78 = new TFHBox();

    private void init_FHBox78() {
        FHBox78.setName("FHBox78");
        FHBox78.setLeft(0);
        FHBox78.setTop(14);
        FHBox78.setWidth(185);
        FHBox78.setHeight(34);
        FHBox78.setBorderStyle("stNone");
        FHBox78.setPaddingTop(0);
        FHBox78.setPaddingLeft(0);
        FHBox78.setPaddingRight(0);
        FHBox78.setPaddingBottom(0);
        FHBox78.setMarginTop(0);
        FHBox78.setMarginLeft(0);
        FHBox78.setMarginRight(0);
        FHBox78.setMarginBottom(0);
        FHBox78.setSpacing(1);
        FHBox78.setFlexVflex("ftTrue");
        FHBox78.setFlexHflex("ftFalse");
        FHBox78.setScrollable(false);
        FHBox78.setBoxShadowConfigHorizontalLength(10);
        FHBox78.setBoxShadowConfigVerticalLength(10);
        FHBox78.setBoxShadowConfigBlurRadius(5);
        FHBox78.setBoxShadowConfigSpreadRadius(0);
        FHBox78.setBoxShadowConfigShadowColor("clBlack");
        FHBox78.setBoxShadowConfigOpacity(75);
        FHBox78.setVAlign("tvTop");
        vBoxCartaoSalvarAlteracao.addChildren(FHBox78);
        FHBox78.applyProperties();
    }

    public TFButton btnCartaoSalvarAlteracao = new TFButton();

    private void init_btnCartaoSalvarAlteracao() {
        btnCartaoSalvarAlteracao.setName("btnCartaoSalvarAlteracao");
        btnCartaoSalvarAlteracao.setLeft(0);
        btnCartaoSalvarAlteracao.setTop(0);
        btnCartaoSalvarAlteracao.setWidth(82);
        btnCartaoSalvarAlteracao.setHeight(28);
        btnCartaoSalvarAlteracao.setCaption("Confirmar");
        btnCartaoSalvarAlteracao.setEnabled(false);
        btnCartaoSalvarAlteracao.setFontColor("clWindowText");
        btnCartaoSalvarAlteracao.setFontSize(-11);
        btnCartaoSalvarAlteracao.setFontName("Tahoma");
        btnCartaoSalvarAlteracao.setFontStyle("[]");
        btnCartaoSalvarAlteracao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCartaoSalvarAlteracaoClick(event);
            processarFlow("FrmPgtoVendaInf", "btnCartaoSalvarAlteracao", "OnClick");
        });
        btnCartaoSalvarAlteracao.setImageId(220011);
        btnCartaoSalvarAlteracao.setColor("clBtnFace");
        btnCartaoSalvarAlteracao.setAccess(false);
        btnCartaoSalvarAlteracao.setIconReverseDirection(false);
        FHBox78.addChildren(btnCartaoSalvarAlteracao);
        btnCartaoSalvarAlteracao.applyProperties();
    }

    public TFHBox FHBox79 = new TFHBox();

    private void init_FHBox79() {
        FHBox79.setName("FHBox79");
        FHBox79.setLeft(82);
        FHBox79.setTop(0);
        FHBox79.setWidth(4);
        FHBox79.setHeight(12);
        FHBox79.setBorderStyle("stNone");
        FHBox79.setPaddingTop(0);
        FHBox79.setPaddingLeft(0);
        FHBox79.setPaddingRight(0);
        FHBox79.setPaddingBottom(0);
        FHBox79.setMarginTop(0);
        FHBox79.setMarginLeft(0);
        FHBox79.setMarginRight(0);
        FHBox79.setMarginBottom(0);
        FHBox79.setSpacing(1);
        FHBox79.setFlexVflex("ftFalse");
        FHBox79.setFlexHflex("ftFalse");
        FHBox79.setScrollable(false);
        FHBox79.setBoxShadowConfigHorizontalLength(10);
        FHBox79.setBoxShadowConfigVerticalLength(10);
        FHBox79.setBoxShadowConfigBlurRadius(5);
        FHBox79.setBoxShadowConfigSpreadRadius(0);
        FHBox79.setBoxShadowConfigShadowColor("clBlack");
        FHBox79.setBoxShadowConfigOpacity(75);
        FHBox79.setVAlign("tvTop");
        FHBox78.addChildren(FHBox79);
        FHBox79.applyProperties();
    }

    public TFButton btnCartaoCancelarAlteracao = new TFButton();

    private void init_btnCartaoCancelarAlteracao() {
        btnCartaoCancelarAlteracao.setName("btnCartaoCancelarAlteracao");
        btnCartaoCancelarAlteracao.setLeft(86);
        btnCartaoCancelarAlteracao.setTop(0);
        btnCartaoCancelarAlteracao.setWidth(82);
        btnCartaoCancelarAlteracao.setHeight(28);
        btnCartaoCancelarAlteracao.setCaption("Cancelar");
        btnCartaoCancelarAlteracao.setEnabled(false);
        btnCartaoCancelarAlteracao.setFontColor("clWindowText");
        btnCartaoCancelarAlteracao.setFontSize(-11);
        btnCartaoCancelarAlteracao.setFontName("Tahoma");
        btnCartaoCancelarAlteracao.setFontStyle("[]");
        btnCartaoCancelarAlteracao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCartaoCancelarAlteracaoClick(event);
            processarFlow("FrmPgtoVendaInf", "btnCartaoCancelarAlteracao", "OnClick");
        });
        btnCartaoCancelarAlteracao.setImageId(220020);
        btnCartaoCancelarAlteracao.setColor("clBtnFace");
        btnCartaoCancelarAlteracao.setAccess(false);
        btnCartaoCancelarAlteracao.setIconReverseDirection(false);
        FHBox78.addChildren(btnCartaoCancelarAlteracao);
        btnCartaoCancelarAlteracao.applyProperties();
    }

    public TFHBox FHBox80 = new TFHBox();

    private void init_FHBox80() {
        FHBox80.setName("FHBox80");
        FHBox80.setLeft(0);
        FHBox80.setTop(172);
        FHBox80.setWidth(33);
        FHBox80.setHeight(4);
        FHBox80.setBorderStyle("stNone");
        FHBox80.setPaddingTop(0);
        FHBox80.setPaddingLeft(0);
        FHBox80.setPaddingRight(0);
        FHBox80.setPaddingBottom(0);
        FHBox80.setMarginTop(0);
        FHBox80.setMarginLeft(0);
        FHBox80.setMarginRight(0);
        FHBox80.setMarginBottom(0);
        FHBox80.setSpacing(1);
        FHBox80.setFlexVflex("ftFalse");
        FHBox80.setFlexHflex("ftTrue");
        FHBox80.setScrollable(false);
        FHBox80.setBoxShadowConfigHorizontalLength(10);
        FHBox80.setBoxShadowConfigVerticalLength(10);
        FHBox80.setBoxShadowConfigBlurRadius(5);
        FHBox80.setBoxShadowConfigSpreadRadius(0);
        FHBox80.setBoxShadowConfigShadowColor("clBlack");
        FHBox80.setBoxShadowConfigOpacity(75);
        FHBox80.setVAlign("tvTop");
        vBoxGridCartoes.addChildren(FHBox80);
        FHBox80.applyProperties();
    }

    public TFHBox FHBox39 = new TFHBox();

    private void init_FHBox39() {
        FHBox39.setName("FHBox39");
        FHBox39.setLeft(704);
        FHBox39.setTop(0);
        FHBox39.setWidth(4);
        FHBox39.setHeight(12);
        FHBox39.setBorderStyle("stNone");
        FHBox39.setPaddingTop(0);
        FHBox39.setPaddingLeft(0);
        FHBox39.setPaddingRight(0);
        FHBox39.setPaddingBottom(0);
        FHBox39.setVisible(false);
        FHBox39.setMarginTop(0);
        FHBox39.setMarginLeft(0);
        FHBox39.setMarginRight(0);
        FHBox39.setMarginBottom(0);
        FHBox39.setSpacing(1);
        FHBox39.setFlexVflex("ftFalse");
        FHBox39.setFlexHflex("ftFalse");
        FHBox39.setScrollable(false);
        FHBox39.setBoxShadowConfigHorizontalLength(10);
        FHBox39.setBoxShadowConfigVerticalLength(10);
        FHBox39.setBoxShadowConfigBlurRadius(5);
        FHBox39.setBoxShadowConfigSpreadRadius(0);
        FHBox39.setBoxShadowConfigShadowColor("clBlack");
        FHBox39.setBoxShadowConfigOpacity(75);
        FHBox39.setVAlign("tvTop");
        FHBox24.addChildren(FHBox39);
        FHBox39.applyProperties();
    }

    public TFHBox FHBox34 = new TFHBox();

    private void init_FHBox34() {
        FHBox34.setName("FHBox34");
        FHBox34.setLeft(0);
        FHBox34.setTop(301);
        FHBox34.setWidth(33);
        FHBox34.setHeight(4);
        FHBox34.setBorderStyle("stNone");
        FHBox34.setPaddingTop(0);
        FHBox34.setPaddingLeft(0);
        FHBox34.setPaddingRight(0);
        FHBox34.setPaddingBottom(0);
        FHBox34.setMarginTop(0);
        FHBox34.setMarginLeft(0);
        FHBox34.setMarginRight(0);
        FHBox34.setMarginBottom(0);
        FHBox34.setSpacing(1);
        FHBox34.setFlexVflex("ftFalse");
        FHBox34.setFlexHflex("ftTrue");
        FHBox34.setScrollable(false);
        FHBox34.setBoxShadowConfigHorizontalLength(10);
        FHBox34.setBoxShadowConfigVerticalLength(10);
        FHBox34.setBoxShadowConfigBlurRadius(5);
        FHBox34.setBoxShadowConfigSpreadRadius(0);
        FHBox34.setBoxShadowConfigShadowColor("clBlack");
        FHBox34.setBoxShadowConfigOpacity(75);
        FHBox34.setVAlign("tvTop");
        vBoxInformeCartoes.addChildren(FHBox34);
        FHBox34.applyProperties();
    }

    public TFTabsheet tbsCheques = new TFTabsheet();

    private void init_tbsCheques() {
        tbsCheques.setName("tbsCheques");
        tbsCheques.setCaption("Informe os cheques");
        tbsCheques.setVisible(true);
        tbsCheques.setClosable(false);
        pgcInfoPgto.addChildren(tbsCheques);
        tbsCheques.applyProperties();
    }

    public TFVBox vBoxInformeCheques = new TFVBox();

    private void init_vBoxInformeCheques() {
        vBoxInformeCheques.setName("vBoxInformeCheques");
        vBoxInformeCheques.setLeft(0);
        vBoxInformeCheques.setTop(0);
        vBoxInformeCheques.setWidth(839);
        vBoxInformeCheques.setHeight(306);
        vBoxInformeCheques.setBorderStyle("stNone");
        vBoxInformeCheques.setPaddingTop(0);
        vBoxInformeCheques.setPaddingLeft(0);
        vBoxInformeCheques.setPaddingRight(0);
        vBoxInformeCheques.setPaddingBottom(0);
        vBoxInformeCheques.setMarginTop(0);
        vBoxInformeCheques.setMarginLeft(0);
        vBoxInformeCheques.setMarginRight(0);
        vBoxInformeCheques.setMarginBottom(0);
        vBoxInformeCheques.setSpacing(1);
        vBoxInformeCheques.setFlexVflex("ftTrue");
        vBoxInformeCheques.setFlexHflex("ftTrue");
        vBoxInformeCheques.setScrollable(false);
        vBoxInformeCheques.setBoxShadowConfigHorizontalLength(10);
        vBoxInformeCheques.setBoxShadowConfigVerticalLength(10);
        vBoxInformeCheques.setBoxShadowConfigBlurRadius(5);
        vBoxInformeCheques.setBoxShadowConfigSpreadRadius(0);
        vBoxInformeCheques.setBoxShadowConfigShadowColor("clBlack");
        vBoxInformeCheques.setBoxShadowConfigOpacity(75);
        tbsCheques.addChildren(vBoxInformeCheques);
        vBoxInformeCheques.applyProperties();
    }

    public TFHBox FHBox48 = new TFHBox();

    private void init_FHBox48() {
        FHBox48.setName("FHBox48");
        FHBox48.setLeft(0);
        FHBox48.setTop(0);
        FHBox48.setWidth(746);
        FHBox48.setHeight(37);
        FHBox48.setBorderStyle("stNone");
        FHBox48.setPaddingTop(0);
        FHBox48.setPaddingLeft(0);
        FHBox48.setPaddingRight(0);
        FHBox48.setPaddingBottom(0);
        FHBox48.setMarginTop(0);
        FHBox48.setMarginLeft(0);
        FHBox48.setMarginRight(0);
        FHBox48.setMarginBottom(0);
        FHBox48.setSpacing(1);
        FHBox48.setFlexVflex("ftMin");
        FHBox48.setFlexHflex("ftFalse");
        FHBox48.setScrollable(false);
        FHBox48.setBoxShadowConfigHorizontalLength(10);
        FHBox48.setBoxShadowConfigVerticalLength(10);
        FHBox48.setBoxShadowConfigBlurRadius(5);
        FHBox48.setBoxShadowConfigSpreadRadius(0);
        FHBox48.setBoxShadowConfigShadowColor("clBlack");
        FHBox48.setBoxShadowConfigOpacity(75);
        FHBox48.setVAlign("tvTop");
        vBoxInformeCheques.addChildren(FHBox48);
        FHBox48.applyProperties();
    }

    public TFHBox FHBox50 = new TFHBox();

    private void init_FHBox50() {
        FHBox50.setName("FHBox50");
        FHBox50.setLeft(0);
        FHBox50.setTop(0);
        FHBox50.setWidth(4);
        FHBox50.setHeight(12);
        FHBox50.setBorderStyle("stNone");
        FHBox50.setPaddingTop(0);
        FHBox50.setPaddingLeft(0);
        FHBox50.setPaddingRight(0);
        FHBox50.setPaddingBottom(0);
        FHBox50.setMarginTop(0);
        FHBox50.setMarginLeft(0);
        FHBox50.setMarginRight(0);
        FHBox50.setMarginBottom(0);
        FHBox50.setSpacing(1);
        FHBox50.setFlexVflex("ftFalse");
        FHBox50.setFlexHflex("ftFalse");
        FHBox50.setScrollable(false);
        FHBox50.setBoxShadowConfigHorizontalLength(10);
        FHBox50.setBoxShadowConfigVerticalLength(10);
        FHBox50.setBoxShadowConfigBlurRadius(5);
        FHBox50.setBoxShadowConfigSpreadRadius(0);
        FHBox50.setBoxShadowConfigShadowColor("clBlack");
        FHBox50.setBoxShadowConfigOpacity(75);
        FHBox50.setVAlign("tvTop");
        FHBox48.addChildren(FHBox50);
        FHBox50.applyProperties();
    }

    public TFVBox FVBox15 = new TFVBox();

    private void init_FVBox15() {
        FVBox15.setName("FVBox15");
        FVBox15.setLeft(4);
        FVBox15.setTop(0);
        FVBox15.setWidth(58);
        FVBox15.setHeight(30);
        FVBox15.setBorderStyle("stNone");
        FVBox15.setPaddingTop(0);
        FVBox15.setPaddingLeft(0);
        FVBox15.setPaddingRight(0);
        FVBox15.setPaddingBottom(0);
        FVBox15.setMarginTop(0);
        FVBox15.setMarginLeft(0);
        FVBox15.setMarginRight(0);
        FVBox15.setMarginBottom(0);
        FVBox15.setSpacing(1);
        FVBox15.setFlexVflex("ftFalse");
        FVBox15.setFlexHflex("ftFalse");
        FVBox15.setScrollable(false);
        FVBox15.setBoxShadowConfigHorizontalLength(10);
        FVBox15.setBoxShadowConfigVerticalLength(10);
        FVBox15.setBoxShadowConfigBlurRadius(5);
        FVBox15.setBoxShadowConfigSpreadRadius(0);
        FVBox15.setBoxShadowConfigShadowColor("clBlack");
        FVBox15.setBoxShadowConfigOpacity(75);
        FHBox48.addChildren(FVBox15);
        FVBox15.applyProperties();
    }

    public TFHBox FHBox51 = new TFHBox();

    private void init_FHBox51() {
        FHBox51.setName("FHBox51");
        FHBox51.setLeft(0);
        FHBox51.setTop(0);
        FHBox51.setWidth(33);
        FHBox51.setHeight(4);
        FHBox51.setBorderStyle("stNone");
        FHBox51.setPaddingTop(0);
        FHBox51.setPaddingLeft(0);
        FHBox51.setPaddingRight(0);
        FHBox51.setPaddingBottom(0);
        FHBox51.setMarginTop(0);
        FHBox51.setMarginLeft(0);
        FHBox51.setMarginRight(0);
        FHBox51.setMarginBottom(0);
        FHBox51.setSpacing(1);
        FHBox51.setFlexVflex("ftFalse");
        FHBox51.setFlexHflex("ftTrue");
        FHBox51.setScrollable(false);
        FHBox51.setBoxShadowConfigHorizontalLength(10);
        FHBox51.setBoxShadowConfigVerticalLength(10);
        FHBox51.setBoxShadowConfigBlurRadius(5);
        FHBox51.setBoxShadowConfigSpreadRadius(0);
        FHBox51.setBoxShadowConfigShadowColor("clBlack");
        FHBox51.setBoxShadowConfigOpacity(75);
        FHBox51.setVAlign("tvTop");
        FVBox15.addChildren(FHBox51);
        FHBox51.applyProperties();
    }

    public TFLabel lblChequesValorTotal = new TFLabel();

    private void init_lblChequesValorTotal() {
        lblChequesValorTotal.setName("lblChequesValorTotal");
        lblChequesValorTotal.setLeft(0);
        lblChequesValorTotal.setTop(5);
        lblChequesValorTotal.setWidth(53);
        lblChequesValorTotal.setHeight(13);
        lblChequesValorTotal.setCaption("Valor total:");
        lblChequesValorTotal.setFontColor("clWindowText");
        lblChequesValorTotal.setFontSize(-11);
        lblChequesValorTotal.setFontName("Tahoma");
        lblChequesValorTotal.setFontStyle("[]");
        lblChequesValorTotal.setVerticalAlignment("taVerticalCenter");
        lblChequesValorTotal.setWordBreak(false);
        FVBox15.addChildren(lblChequesValorTotal);
        lblChequesValorTotal.applyProperties();
    }

    public TFVBox FVBox16 = new TFVBox();

    private void init_FVBox16() {
        FVBox16.setName("FVBox16");
        FVBox16.setLeft(62);
        FVBox16.setTop(0);
        FVBox16.setWidth(117);
        FVBox16.setHeight(30);
        FVBox16.setBorderStyle("stNone");
        FVBox16.setPaddingTop(0);
        FVBox16.setPaddingLeft(0);
        FVBox16.setPaddingRight(0);
        FVBox16.setPaddingBottom(0);
        FVBox16.setMarginTop(0);
        FVBox16.setMarginLeft(0);
        FVBox16.setMarginRight(0);
        FVBox16.setMarginBottom(0);
        FVBox16.setSpacing(1);
        FVBox16.setFlexVflex("ftFalse");
        FVBox16.setFlexHflex("ftFalse");
        FVBox16.setScrollable(false);
        FVBox16.setBoxShadowConfigHorizontalLength(10);
        FVBox16.setBoxShadowConfigVerticalLength(10);
        FVBox16.setBoxShadowConfigBlurRadius(5);
        FVBox16.setBoxShadowConfigSpreadRadius(0);
        FVBox16.setBoxShadowConfigShadowColor("clBlack");
        FVBox16.setBoxShadowConfigOpacity(75);
        FHBox48.addChildren(FVBox16);
        FVBox16.applyProperties();
    }

    public TFHBox FHBox52 = new TFHBox();

    private void init_FHBox52() {
        FHBox52.setName("FHBox52");
        FHBox52.setLeft(0);
        FHBox52.setTop(0);
        FHBox52.setWidth(33);
        FHBox52.setHeight(1);
        FHBox52.setBorderStyle("stNone");
        FHBox52.setPaddingTop(0);
        FHBox52.setPaddingLeft(0);
        FHBox52.setPaddingRight(0);
        FHBox52.setPaddingBottom(0);
        FHBox52.setMarginTop(0);
        FHBox52.setMarginLeft(0);
        FHBox52.setMarginRight(0);
        FHBox52.setMarginBottom(0);
        FHBox52.setSpacing(1);
        FHBox52.setFlexVflex("ftFalse");
        FHBox52.setFlexHflex("ftTrue");
        FHBox52.setScrollable(false);
        FHBox52.setBoxShadowConfigHorizontalLength(10);
        FHBox52.setBoxShadowConfigVerticalLength(10);
        FHBox52.setBoxShadowConfigBlurRadius(5);
        FHBox52.setBoxShadowConfigSpreadRadius(0);
        FHBox52.setBoxShadowConfigShadowColor("clBlack");
        FHBox52.setBoxShadowConfigOpacity(75);
        FHBox52.setVAlign("tvTop");
        FVBox16.addChildren(FHBox52);
        FHBox52.applyProperties();
    }

    public TFDecimal edtChequeValorTotal = new TFDecimal();

    private void init_edtChequeValorTotal() {
        edtChequeValorTotal.setName("edtChequeValorTotal");
        edtChequeValorTotal.setLeft(0);
        edtChequeValorTotal.setTop(2);
        edtChequeValorTotal.setWidth(108);
        edtChequeValorTotal.setHeight(24);
        edtChequeValorTotal.setFlex(true);
        edtChequeValorTotal.setRequired(false);
        edtChequeValorTotal.setConstraintCheckWhen("cwImmediate");
        edtChequeValorTotal.setConstraintCheckType("ctExpression");
        edtChequeValorTotal.setConstraintFocusOnError(false);
        edtChequeValorTotal.setConstraintEnableUI(true);
        edtChequeValorTotal.setConstraintEnabled(false);
        edtChequeValorTotal.setConstraintFormCheck(true);
        edtChequeValorTotal.setMaxlength(0);
        edtChequeValorTotal.setPrecision(0);
        edtChequeValorTotal.setFontColor("clWindowText");
        edtChequeValorTotal.setFontSize(-13);
        edtChequeValorTotal.setFontName("Tahoma");
        edtChequeValorTotal.setFontStyle("[]");
        edtChequeValorTotal.setAlignment("taRightJustify");
        FVBox16.addChildren(edtChequeValorTotal);
        edtChequeValorTotal.applyProperties();
        addValidatable(edtChequeValorTotal);
    }

    public TFHBox FHBox54 = new TFHBox();

    private void init_FHBox54() {
        FHBox54.setName("FHBox54");
        FHBox54.setLeft(179);
        FHBox54.setTop(0);
        FHBox54.setWidth(4);
        FHBox54.setHeight(12);
        FHBox54.setBorderStyle("stNone");
        FHBox54.setPaddingTop(0);
        FHBox54.setPaddingLeft(0);
        FHBox54.setPaddingRight(0);
        FHBox54.setPaddingBottom(0);
        FHBox54.setMarginTop(0);
        FHBox54.setMarginLeft(0);
        FHBox54.setMarginRight(0);
        FHBox54.setMarginBottom(0);
        FHBox54.setSpacing(1);
        FHBox54.setFlexVflex("ftFalse");
        FHBox54.setFlexHflex("ftFalse");
        FHBox54.setScrollable(false);
        FHBox54.setBoxShadowConfigHorizontalLength(10);
        FHBox54.setBoxShadowConfigVerticalLength(10);
        FHBox54.setBoxShadowConfigBlurRadius(5);
        FHBox54.setBoxShadowConfigSpreadRadius(0);
        FHBox54.setBoxShadowConfigShadowColor("clBlack");
        FHBox54.setBoxShadowConfigOpacity(75);
        FHBox54.setVAlign("tvTop");
        FHBox48.addChildren(FHBox54);
        FHBox54.applyProperties();
    }

    public TFVBox FVBox17 = new TFVBox();

    private void init_FVBox17() {
        FVBox17.setName("FVBox17");
        FVBox17.setLeft(183);
        FVBox17.setTop(0);
        FVBox17.setWidth(67);
        FVBox17.setHeight(30);
        FVBox17.setBorderStyle("stNone");
        FVBox17.setPaddingTop(0);
        FVBox17.setPaddingLeft(0);
        FVBox17.setPaddingRight(0);
        FVBox17.setPaddingBottom(0);
        FVBox17.setMarginTop(0);
        FVBox17.setMarginLeft(0);
        FVBox17.setMarginRight(0);
        FVBox17.setMarginBottom(0);
        FVBox17.setSpacing(1);
        FVBox17.setFlexVflex("ftFalse");
        FVBox17.setFlexHflex("ftFalse");
        FVBox17.setScrollable(false);
        FVBox17.setBoxShadowConfigHorizontalLength(10);
        FVBox17.setBoxShadowConfigVerticalLength(10);
        FVBox17.setBoxShadowConfigBlurRadius(5);
        FVBox17.setBoxShadowConfigSpreadRadius(0);
        FVBox17.setBoxShadowConfigShadowColor("clBlack");
        FVBox17.setBoxShadowConfigOpacity(75);
        FHBox48.addChildren(FVBox17);
        FVBox17.applyProperties();
    }

    public TFHBox FHBox55 = new TFHBox();

    private void init_FHBox55() {
        FHBox55.setName("FHBox55");
        FHBox55.setLeft(0);
        FHBox55.setTop(0);
        FHBox55.setWidth(33);
        FHBox55.setHeight(4);
        FHBox55.setBorderStyle("stNone");
        FHBox55.setPaddingTop(0);
        FHBox55.setPaddingLeft(0);
        FHBox55.setPaddingRight(0);
        FHBox55.setPaddingBottom(0);
        FHBox55.setMarginTop(0);
        FHBox55.setMarginLeft(0);
        FHBox55.setMarginRight(0);
        FHBox55.setMarginBottom(0);
        FHBox55.setSpacing(1);
        FHBox55.setFlexVflex("ftFalse");
        FHBox55.setFlexHflex("ftTrue");
        FHBox55.setScrollable(false);
        FHBox55.setBoxShadowConfigHorizontalLength(10);
        FHBox55.setBoxShadowConfigVerticalLength(10);
        FHBox55.setBoxShadowConfigBlurRadius(5);
        FHBox55.setBoxShadowConfigSpreadRadius(0);
        FHBox55.setBoxShadowConfigShadowColor("clBlack");
        FHBox55.setBoxShadowConfigOpacity(75);
        FHBox55.setVAlign("tvTop");
        FVBox17.addChildren(FHBox55);
        FHBox55.applyProperties();
    }

    public TFLabel lblChequesObservacao = new TFLabel();

    private void init_lblChequesObservacao() {
        lblChequesObservacao.setName("lblChequesObservacao");
        lblChequesObservacao.setLeft(0);
        lblChequesObservacao.setTop(5);
        lblChequesObservacao.setWidth(62);
        lblChequesObservacao.setHeight(13);
        lblChequesObservacao.setCaption("Observa\u00E7\u00E3o:");
        lblChequesObservacao.setFontColor("clWindowText");
        lblChequesObservacao.setFontSize(-11);
        lblChequesObservacao.setFontName("Tahoma");
        lblChequesObservacao.setFontStyle("[]");
        lblChequesObservacao.setVerticalAlignment("taVerticalCenter");
        lblChequesObservacao.setWordBreak(false);
        FVBox17.addChildren(lblChequesObservacao);
        lblChequesObservacao.applyProperties();
    }

    public TFVBox FVBox18 = new TFVBox();

    private void init_FVBox18() {
        FVBox18.setName("FVBox18");
        FVBox18.setLeft(250);
        FVBox18.setTop(0);
        FVBox18.setWidth(361);
        FVBox18.setHeight(30);
        FVBox18.setBorderStyle("stNone");
        FVBox18.setPaddingTop(0);
        FVBox18.setPaddingLeft(0);
        FVBox18.setPaddingRight(0);
        FVBox18.setPaddingBottom(0);
        FVBox18.setMarginTop(0);
        FVBox18.setMarginLeft(0);
        FVBox18.setMarginRight(0);
        FVBox18.setMarginBottom(0);
        FVBox18.setSpacing(1);
        FVBox18.setFlexVflex("ftFalse");
        FVBox18.setFlexHflex("ftTrue");
        FVBox18.setScrollable(false);
        FVBox18.setBoxShadowConfigHorizontalLength(10);
        FVBox18.setBoxShadowConfigVerticalLength(10);
        FVBox18.setBoxShadowConfigBlurRadius(5);
        FVBox18.setBoxShadowConfigSpreadRadius(0);
        FVBox18.setBoxShadowConfigShadowColor("clBlack");
        FVBox18.setBoxShadowConfigOpacity(75);
        FHBox48.addChildren(FVBox18);
        FVBox18.applyProperties();
    }

    public TFHBox FHBox56 = new TFHBox();

    private void init_FHBox56() {
        FHBox56.setName("FHBox56");
        FHBox56.setLeft(0);
        FHBox56.setTop(0);
        FHBox56.setWidth(33);
        FHBox56.setHeight(1);
        FHBox56.setBorderStyle("stNone");
        FHBox56.setPaddingTop(0);
        FHBox56.setPaddingLeft(0);
        FHBox56.setPaddingRight(0);
        FHBox56.setPaddingBottom(0);
        FHBox56.setMarginTop(0);
        FHBox56.setMarginLeft(0);
        FHBox56.setMarginRight(0);
        FHBox56.setMarginBottom(0);
        FHBox56.setSpacing(1);
        FHBox56.setFlexVflex("ftFalse");
        FHBox56.setFlexHflex("ftTrue");
        FHBox56.setScrollable(false);
        FHBox56.setBoxShadowConfigHorizontalLength(10);
        FHBox56.setBoxShadowConfigVerticalLength(10);
        FHBox56.setBoxShadowConfigBlurRadius(5);
        FHBox56.setBoxShadowConfigSpreadRadius(0);
        FHBox56.setBoxShadowConfigShadowColor("clBlack");
        FHBox56.setBoxShadowConfigOpacity(75);
        FHBox56.setVAlign("tvTop");
        FVBox18.addChildren(FHBox56);
        FHBox56.applyProperties();
    }

    public TFString edtChequesObservacao = new TFString();

    private void init_edtChequesObservacao() {
        edtChequesObservacao.setName("edtChequesObservacao");
        edtChequesObservacao.setLeft(0);
        edtChequesObservacao.setTop(2);
        edtChequesObservacao.setWidth(341);
        edtChequesObservacao.setHeight(24);
        edtChequesObservacao.setFlex(true);
        edtChequesObservacao.setRequired(false);
        edtChequesObservacao.setConstraintCheckWhen("cwImmediate");
        edtChequesObservacao.setConstraintCheckType("ctExpression");
        edtChequesObservacao.setConstraintFocusOnError(false);
        edtChequesObservacao.setConstraintEnableUI(true);
        edtChequesObservacao.setConstraintEnabled(false);
        edtChequesObservacao.setConstraintFormCheck(true);
        edtChequesObservacao.setCharCase("ccNormal");
        edtChequesObservacao.setPwd(false);
        edtChequesObservacao.setMaxlength(60);
        edtChequesObservacao.setEnabled(false);
        edtChequesObservacao.setFontColor("clWindowText");
        edtChequesObservacao.setFontSize(-13);
        edtChequesObservacao.setFontName("Tahoma");
        edtChequesObservacao.setFontStyle("[]");
        edtChequesObservacao.setSaveLiteralCharacter(false);
        edtChequesObservacao.applyProperties();
        FVBox18.addChildren(edtChequesObservacao);
        addValidatable(edtChequesObservacao);
    }

    public TFHBox FHBox70 = new TFHBox();

    private void init_FHBox70() {
        FHBox70.setName("FHBox70");
        FHBox70.setLeft(611);
        FHBox70.setTop(0);
        FHBox70.setWidth(4);
        FHBox70.setHeight(12);
        FHBox70.setBorderStyle("stNone");
        FHBox70.setPaddingTop(0);
        FHBox70.setPaddingLeft(0);
        FHBox70.setPaddingRight(0);
        FHBox70.setPaddingBottom(0);
        FHBox70.setMarginTop(0);
        FHBox70.setMarginLeft(0);
        FHBox70.setMarginRight(0);
        FHBox70.setMarginBottom(0);
        FHBox70.setSpacing(1);
        FHBox70.setFlexVflex("ftFalse");
        FHBox70.setFlexHflex("ftFalse");
        FHBox70.setScrollable(false);
        FHBox70.setBoxShadowConfigHorizontalLength(10);
        FHBox70.setBoxShadowConfigVerticalLength(10);
        FHBox70.setBoxShadowConfigBlurRadius(5);
        FHBox70.setBoxShadowConfigSpreadRadius(0);
        FHBox70.setBoxShadowConfigShadowColor("clBlack");
        FHBox70.setBoxShadowConfigOpacity(75);
        FHBox70.setVAlign("tvTop");
        FHBox48.addChildren(FHBox70);
        FHBox70.applyProperties();
    }

    public TFHBox hBoxInformeCheques = new TFHBox();

    private void init_hBoxInformeCheques() {
        hBoxInformeCheques.setName("hBoxInformeCheques");
        hBoxInformeCheques.setLeft(0);
        hBoxInformeCheques.setTop(38);
        hBoxInformeCheques.setWidth(834);
        hBoxInformeCheques.setHeight(253);
        hBoxInformeCheques.setBorderStyle("stNone");
        hBoxInformeCheques.setPaddingTop(0);
        hBoxInformeCheques.setPaddingLeft(0);
        hBoxInformeCheques.setPaddingRight(0);
        hBoxInformeCheques.setPaddingBottom(0);
        hBoxInformeCheques.setMarginTop(0);
        hBoxInformeCheques.setMarginLeft(0);
        hBoxInformeCheques.setMarginRight(0);
        hBoxInformeCheques.setMarginBottom(0);
        hBoxInformeCheques.setSpacing(1);
        hBoxInformeCheques.setFlexVflex("ftTrue");
        hBoxInformeCheques.setFlexHflex("ftTrue");
        hBoxInformeCheques.setScrollable(false);
        hBoxInformeCheques.setBoxShadowConfigHorizontalLength(10);
        hBoxInformeCheques.setBoxShadowConfigVerticalLength(10);
        hBoxInformeCheques.setBoxShadowConfigBlurRadius(5);
        hBoxInformeCheques.setBoxShadowConfigSpreadRadius(0);
        hBoxInformeCheques.setBoxShadowConfigShadowColor("clBlack");
        hBoxInformeCheques.setBoxShadowConfigOpacity(75);
        hBoxInformeCheques.setVAlign("tvTop");
        vBoxInformeCheques.addChildren(hBoxInformeCheques);
        hBoxInformeCheques.applyProperties();
    }

    public TFHBox FHBox57 = new TFHBox();

    private void init_FHBox57() {
        FHBox57.setName("FHBox57");
        FHBox57.setLeft(0);
        FHBox57.setTop(0);
        FHBox57.setWidth(4);
        FHBox57.setHeight(12);
        FHBox57.setBorderStyle("stNone");
        FHBox57.setPaddingTop(0);
        FHBox57.setPaddingLeft(0);
        FHBox57.setPaddingRight(0);
        FHBox57.setPaddingBottom(0);
        FHBox57.setMarginTop(0);
        FHBox57.setMarginLeft(0);
        FHBox57.setMarginRight(0);
        FHBox57.setMarginBottom(0);
        FHBox57.setSpacing(1);
        FHBox57.setFlexVflex("ftFalse");
        FHBox57.setFlexHflex("ftFalse");
        FHBox57.setScrollable(false);
        FHBox57.setBoxShadowConfigHorizontalLength(10);
        FHBox57.setBoxShadowConfigVerticalLength(10);
        FHBox57.setBoxShadowConfigBlurRadius(5);
        FHBox57.setBoxShadowConfigSpreadRadius(0);
        FHBox57.setBoxShadowConfigShadowColor("clBlack");
        FHBox57.setBoxShadowConfigOpacity(75);
        FHBox57.setVAlign("tvTop");
        hBoxInformeCheques.addChildren(FHBox57);
        FHBox57.applyProperties();
    }

    public TFVBox FVBox27 = new TFVBox();

    private void init_FVBox27() {
        FVBox27.setName("FVBox27");
        FVBox27.setLeft(4);
        FVBox27.setTop(0);
        FVBox27.setWidth(289);
        FVBox27.setHeight(212);
        FVBox27.setBorderStyle("stSingleLine");
        FVBox27.setPaddingTop(0);
        FVBox27.setPaddingLeft(0);
        FVBox27.setPaddingRight(0);
        FVBox27.setPaddingBottom(0);
        FVBox27.setMarginTop(0);
        FVBox27.setMarginLeft(0);
        FVBox27.setMarginRight(0);
        FVBox27.setMarginBottom(0);
        FVBox27.setSpacing(1);
        FVBox27.setFlexVflex("ftTrue");
        FVBox27.setFlexHflex("ftFalse");
        FVBox27.setScrollable(false);
        FVBox27.setBoxShadowConfigHorizontalLength(10);
        FVBox27.setBoxShadowConfigVerticalLength(10);
        FVBox27.setBoxShadowConfigBlurRadius(5);
        FVBox27.setBoxShadowConfigSpreadRadius(0);
        FVBox27.setBoxShadowConfigShadowColor("clBlack");
        FVBox27.setBoxShadowConfigOpacity(75);
        hBoxInformeCheques.addChildren(FVBox27);
        FVBox27.applyProperties();
    }

    public TFGrid grdChequesCondicao = new TFGrid();

    private void init_grdChequesCondicao() {
        grdChequesCondicao.setName("grdChequesCondicao");
        grdChequesCondicao.setLeft(0);
        grdChequesCondicao.setTop(0);
        grdChequesCondicao.setWidth(287);
        grdChequesCondicao.setHeight(120);
        grdChequesCondicao.setTable(tbLeadsFormaPgtoCheque);
        grdChequesCondicao.setFlexVflex("ftTrue");
        grdChequesCondicao.setFlexHflex("ftTrue");
        grdChequesCondicao.setPagingEnabled(false);
        grdChequesCondicao.setFrozenColumns(0);
        grdChequesCondicao.setShowFooter(false);
        grdChequesCondicao.setShowHeader(true);
        grdChequesCondicao.setMultiSelection(false);
        grdChequesCondicao.setGroupingEnabled(false);
        grdChequesCondicao.setGroupingExpanded(false);
        grdChequesCondicao.setGroupingShowFooter(false);
        grdChequesCondicao.setCrosstabEnabled(false);
        grdChequesCondicao.setCrosstabGroupType("cgtConcat");
        grdChequesCondicao.setEditionEnabled(false);
        grdChequesCondicao.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Condi\u00E7\u00E3o");
        item0.setWidth(153);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdChequesCondicao.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("ENTRADA_DIAS");
        item1.setTitleCaption("Entr");
        item1.setWidth(35);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taCenter");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdChequesCondicao.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("INTERVALO");
        item2.setTitleCaption("Interv");
        item2.setWidth(40);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdChequesCondicao.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("TOTAL_PARCELAS");
        item3.setTitleCaption("Parc");
        item3.setWidth(35);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taCenter");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdChequesCondicao.getColumns().add(item3);
        FVBox27.addChildren(grdChequesCondicao);
        grdChequesCondicao.applyProperties();
    }

    public TFHBox FHBox58 = new TFHBox();

    private void init_FHBox58() {
        FHBox58.setName("FHBox58");
        FHBox58.setLeft(0);
        FHBox58.setTop(121);
        FHBox58.setWidth(284);
        FHBox58.setHeight(29);
        FHBox58.setBorderStyle("stNone");
        FHBox58.setPaddingTop(0);
        FHBox58.setPaddingLeft(0);
        FHBox58.setPaddingRight(0);
        FHBox58.setPaddingBottom(0);
        FHBox58.setMarginTop(0);
        FHBox58.setMarginLeft(0);
        FHBox58.setMarginRight(0);
        FHBox58.setMarginBottom(0);
        FHBox58.setSpacing(1);
        FHBox58.setFlexVflex("ftFalse");
        FHBox58.setFlexHflex("ftTrue");
        FHBox58.setScrollable(false);
        FHBox58.setBoxShadowConfigHorizontalLength(10);
        FHBox58.setBoxShadowConfigVerticalLength(10);
        FHBox58.setBoxShadowConfigBlurRadius(5);
        FHBox58.setBoxShadowConfigSpreadRadius(0);
        FHBox58.setBoxShadowConfigShadowColor("clBlack");
        FHBox58.setBoxShadowConfigOpacity(75);
        FHBox58.setVAlign("tvTop");
        FVBox27.addChildren(FHBox58);
        FHBox58.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(0);
        FHBox19.setWidth(4);
        FHBox19.setHeight(12);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftFalse");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FHBox58.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFButton btnIncluirCheques = new TFButton();

    private void init_btnIncluirCheques() {
        btnIncluirCheques.setName("btnIncluirCheques");
        btnIncluirCheques.setLeft(4);
        btnIncluirCheques.setTop(0);
        btnIncluirCheques.setWidth(111);
        btnIncluirCheques.setHeight(25);
        btnIncluirCheques.setCaption("Incluir Cheques");
        btnIncluirCheques.setFontColor("clWindowText");
        btnIncluirCheques.setFontSize(-11);
        btnIncluirCheques.setFontName("Tahoma");
        btnIncluirCheques.setFontStyle("[]");
        btnIncluirCheques.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirChequesClick(event);
            processarFlow("FrmPgtoVendaInf", "btnIncluirCheques", "OnClick");
        });
        btnIncluirCheques.setImageId(0);
        btnIncluirCheques.setColor("clBtnFace");
        btnIncluirCheques.setAccess(false);
        btnIncluirCheques.setIconClass("sign-in");
        btnIncluirCheques.setIconReverseDirection(false);
        FHBox58.addChildren(btnIncluirCheques);
        btnIncluirCheques.applyProperties();
    }

    public TFHBox FHBox59 = new TFHBox();

    private void init_FHBox59() {
        FHBox59.setName("FHBox59");
        FHBox59.setLeft(115);
        FHBox59.setTop(0);
        FHBox59.setWidth(4);
        FHBox59.setHeight(12);
        FHBox59.setBorderStyle("stNone");
        FHBox59.setPaddingTop(0);
        FHBox59.setPaddingLeft(0);
        FHBox59.setPaddingRight(0);
        FHBox59.setPaddingBottom(0);
        FHBox59.setMarginTop(0);
        FHBox59.setMarginLeft(0);
        FHBox59.setMarginRight(0);
        FHBox59.setMarginBottom(0);
        FHBox59.setSpacing(1);
        FHBox59.setFlexVflex("ftFalse");
        FHBox59.setFlexHflex("ftFalse");
        FHBox59.setScrollable(false);
        FHBox59.setBoxShadowConfigHorizontalLength(10);
        FHBox59.setBoxShadowConfigVerticalLength(10);
        FHBox59.setBoxShadowConfigBlurRadius(5);
        FHBox59.setBoxShadowConfigSpreadRadius(0);
        FHBox59.setBoxShadowConfigShadowColor("clBlack");
        FHBox59.setBoxShadowConfigOpacity(75);
        FHBox59.setVAlign("tvTop");
        FHBox58.addChildren(FHBox59);
        FHBox59.applyProperties();
    }

    public TFButton btnExcluirCheques = new TFButton();

    private void init_btnExcluirCheques() {
        btnExcluirCheques.setName("btnExcluirCheques");
        btnExcluirCheques.setLeft(119);
        btnExcluirCheques.setTop(0);
        btnExcluirCheques.setWidth(111);
        btnExcluirCheques.setHeight(25);
        btnExcluirCheques.setCaption("Excluir Cheques");
        btnExcluirCheques.setFontColor("clWindowText");
        btnExcluirCheques.setFontSize(-11);
        btnExcluirCheques.setFontName("Tahoma");
        btnExcluirCheques.setFontStyle("[]");
        btnExcluirCheques.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirChequesClick(event);
            processarFlow("FrmPgtoVendaInf", "btnExcluirCheques", "OnClick");
        });
        btnExcluirCheques.setImageId(0);
        btnExcluirCheques.setColor("clBtnFace");
        btnExcluirCheques.setAccess(false);
        btnExcluirCheques.setIconClass("trash-o");
        btnExcluirCheques.setIconReverseDirection(false);
        FHBox58.addChildren(btnExcluirCheques);
        btnExcluirCheques.applyProperties();
    }

    public TFHBox FHBox64 = new TFHBox();

    private void init_FHBox64() {
        FHBox64.setName("FHBox64");
        FHBox64.setLeft(0);
        FHBox64.setTop(151);
        FHBox64.setWidth(282);
        FHBox64.setHeight(34);
        FHBox64.setBorderStyle("stSingleLine");
        FHBox64.setPaddingTop(0);
        FHBox64.setPaddingLeft(0);
        FHBox64.setPaddingRight(0);
        FHBox64.setPaddingBottom(0);
        FHBox64.setMarginTop(0);
        FHBox64.setMarginLeft(0);
        FHBox64.setMarginRight(0);
        FHBox64.setMarginBottom(0);
        FHBox64.setSpacing(1);
        FHBox64.setFlexVflex("ftFalse");
        FHBox64.setFlexHflex("ftTrue");
        FHBox64.setScrollable(false);
        FHBox64.setBoxShadowConfigHorizontalLength(10);
        FHBox64.setBoxShadowConfigVerticalLength(10);
        FHBox64.setBoxShadowConfigBlurRadius(5);
        FHBox64.setBoxShadowConfigSpreadRadius(0);
        FHBox64.setBoxShadowConfigShadowColor("clBlack");
        FHBox64.setBoxShadowConfigOpacity(75);
        FHBox64.setVAlign("tvTop");
        FVBox27.addChildren(FHBox64);
        FHBox64.applyProperties();
    }

    public TFHBox FHBox65 = new TFHBox();

    private void init_FHBox65() {
        FHBox65.setName("FHBox65");
        FHBox65.setLeft(0);
        FHBox65.setTop(0);
        FHBox65.setWidth(4);
        FHBox65.setHeight(12);
        FHBox65.setBorderStyle("stNone");
        FHBox65.setPaddingTop(0);
        FHBox65.setPaddingLeft(0);
        FHBox65.setPaddingRight(0);
        FHBox65.setPaddingBottom(0);
        FHBox65.setMarginTop(0);
        FHBox65.setMarginLeft(0);
        FHBox65.setMarginRight(0);
        FHBox65.setMarginBottom(0);
        FHBox65.setSpacing(1);
        FHBox65.setFlexVflex("ftFalse");
        FHBox65.setFlexHflex("ftFalse");
        FHBox65.setScrollable(false);
        FHBox65.setBoxShadowConfigHorizontalLength(10);
        FHBox65.setBoxShadowConfigVerticalLength(10);
        FHBox65.setBoxShadowConfigBlurRadius(5);
        FHBox65.setBoxShadowConfigSpreadRadius(0);
        FHBox65.setBoxShadowConfigShadowColor("clBlack");
        FHBox65.setBoxShadowConfigOpacity(75);
        FHBox65.setVAlign("tvTop");
        FHBox64.addChildren(FHBox65);
        FHBox65.applyProperties();
    }

    public TFVBox FVBox29 = new TFVBox();

    private void init_FVBox29() {
        FVBox29.setName("FVBox29");
        FVBox29.setLeft(4);
        FVBox29.setTop(0);
        FVBox29.setWidth(64);
        FVBox29.setHeight(30);
        FVBox29.setBorderStyle("stNone");
        FVBox29.setPaddingTop(0);
        FVBox29.setPaddingLeft(0);
        FVBox29.setPaddingRight(0);
        FVBox29.setPaddingBottom(0);
        FVBox29.setMarginTop(0);
        FVBox29.setMarginLeft(0);
        FVBox29.setMarginRight(0);
        FVBox29.setMarginBottom(0);
        FVBox29.setSpacing(1);
        FVBox29.setFlexVflex("ftFalse");
        FVBox29.setFlexHflex("ftFalse");
        FVBox29.setScrollable(false);
        FVBox29.setBoxShadowConfigHorizontalLength(10);
        FVBox29.setBoxShadowConfigVerticalLength(10);
        FVBox29.setBoxShadowConfigBlurRadius(5);
        FVBox29.setBoxShadowConfigSpreadRadius(0);
        FVBox29.setBoxShadowConfigShadowColor("clBlack");
        FVBox29.setBoxShadowConfigOpacity(75);
        FHBox64.addChildren(FVBox29);
        FVBox29.applyProperties();
    }

    public TFHBox FHBox66 = new TFHBox();

    private void init_FHBox66() {
        FHBox66.setName("FHBox66");
        FHBox66.setLeft(0);
        FHBox66.setTop(0);
        FHBox66.setWidth(33);
        FHBox66.setHeight(4);
        FHBox66.setBorderStyle("stNone");
        FHBox66.setPaddingTop(0);
        FHBox66.setPaddingLeft(0);
        FHBox66.setPaddingRight(0);
        FHBox66.setPaddingBottom(0);
        FHBox66.setMarginTop(0);
        FHBox66.setMarginLeft(0);
        FHBox66.setMarginRight(0);
        FHBox66.setMarginBottom(0);
        FHBox66.setSpacing(1);
        FHBox66.setFlexVflex("ftFalse");
        FHBox66.setFlexHflex("ftTrue");
        FHBox66.setScrollable(false);
        FHBox66.setBoxShadowConfigHorizontalLength(10);
        FHBox66.setBoxShadowConfigVerticalLength(10);
        FHBox66.setBoxShadowConfigBlurRadius(5);
        FHBox66.setBoxShadowConfigSpreadRadius(0);
        FHBox66.setBoxShadowConfigShadowColor("clBlack");
        FHBox66.setBoxShadowConfigOpacity(75);
        FHBox66.setVAlign("tvTop");
        FVBox29.addChildren(FHBox66);
        FHBox66.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(5);
        FLabel2.setWidth(60);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Falta lan\u00E7ar:");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox29.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFVBox FVBox30 = new TFVBox();

    private void init_FVBox30() {
        FVBox30.setName("FVBox30");
        FVBox30.setLeft(68);
        FVBox30.setTop(0);
        FVBox30.setWidth(117);
        FVBox30.setHeight(30);
        FVBox30.setBorderStyle("stNone");
        FVBox30.setPaddingTop(0);
        FVBox30.setPaddingLeft(0);
        FVBox30.setPaddingRight(0);
        FVBox30.setPaddingBottom(0);
        FVBox30.setMarginTop(0);
        FVBox30.setMarginLeft(0);
        FVBox30.setMarginRight(0);
        FVBox30.setMarginBottom(0);
        FVBox30.setSpacing(1);
        FVBox30.setFlexVflex("ftFalse");
        FVBox30.setFlexHflex("ftFalse");
        FVBox30.setScrollable(false);
        FVBox30.setBoxShadowConfigHorizontalLength(10);
        FVBox30.setBoxShadowConfigVerticalLength(10);
        FVBox30.setBoxShadowConfigBlurRadius(5);
        FVBox30.setBoxShadowConfigSpreadRadius(0);
        FVBox30.setBoxShadowConfigShadowColor("clBlack");
        FVBox30.setBoxShadowConfigOpacity(75);
        FHBox64.addChildren(FVBox30);
        FVBox30.applyProperties();
    }

    public TFHBox FHBox67 = new TFHBox();

    private void init_FHBox67() {
        FHBox67.setName("FHBox67");
        FHBox67.setLeft(0);
        FHBox67.setTop(0);
        FHBox67.setWidth(33);
        FHBox67.setHeight(1);
        FHBox67.setBorderStyle("stNone");
        FHBox67.setPaddingTop(0);
        FHBox67.setPaddingLeft(0);
        FHBox67.setPaddingRight(0);
        FHBox67.setPaddingBottom(0);
        FHBox67.setMarginTop(0);
        FHBox67.setMarginLeft(0);
        FHBox67.setMarginRight(0);
        FHBox67.setMarginBottom(0);
        FHBox67.setSpacing(1);
        FHBox67.setFlexVflex("ftFalse");
        FHBox67.setFlexHflex("ftTrue");
        FHBox67.setScrollable(false);
        FHBox67.setBoxShadowConfigHorizontalLength(10);
        FHBox67.setBoxShadowConfigVerticalLength(10);
        FHBox67.setBoxShadowConfigBlurRadius(5);
        FHBox67.setBoxShadowConfigSpreadRadius(0);
        FHBox67.setBoxShadowConfigShadowColor("clBlack");
        FHBox67.setBoxShadowConfigOpacity(75);
        FHBox67.setVAlign("tvTop");
        FVBox30.addChildren(FHBox67);
        FHBox67.applyProperties();
    }

    public TFDecimal edtChequeValorFaltaLancar = new TFDecimal();

    private void init_edtChequeValorFaltaLancar() {
        edtChequeValorFaltaLancar.setName("edtChequeValorFaltaLancar");
        edtChequeValorFaltaLancar.setLeft(0);
        edtChequeValorFaltaLancar.setTop(2);
        edtChequeValorFaltaLancar.setWidth(108);
        edtChequeValorFaltaLancar.setHeight(24);
        edtChequeValorFaltaLancar.setFlex(true);
        edtChequeValorFaltaLancar.setRequired(false);
        edtChequeValorFaltaLancar.setConstraintCheckWhen("cwImmediate");
        edtChequeValorFaltaLancar.setConstraintCheckType("ctExpression");
        edtChequeValorFaltaLancar.setConstraintFocusOnError(false);
        edtChequeValorFaltaLancar.setConstraintEnableUI(true);
        edtChequeValorFaltaLancar.setConstraintEnabled(false);
        edtChequeValorFaltaLancar.setConstraintFormCheck(true);
        edtChequeValorFaltaLancar.setMaxlength(0);
        edtChequeValorFaltaLancar.setPrecision(0);
        edtChequeValorFaltaLancar.setFontColor("clWindowText");
        edtChequeValorFaltaLancar.setFontSize(-13);
        edtChequeValorFaltaLancar.setFontName("Tahoma");
        edtChequeValorFaltaLancar.setFontStyle("[]");
        edtChequeValorFaltaLancar.setAlignment("taRightJustify");
        FVBox30.addChildren(edtChequeValorFaltaLancar);
        edtChequeValorFaltaLancar.applyProperties();
        addValidatable(edtChequeValorFaltaLancar);
    }

    public TFHBox FHBox60 = new TFHBox();

    private void init_FHBox60() {
        FHBox60.setName("FHBox60");
        FHBox60.setLeft(293);
        FHBox60.setTop(0);
        FHBox60.setWidth(4);
        FHBox60.setHeight(12);
        FHBox60.setBorderStyle("stNone");
        FHBox60.setPaddingTop(0);
        FHBox60.setPaddingLeft(0);
        FHBox60.setPaddingRight(0);
        FHBox60.setPaddingBottom(0);
        FHBox60.setMarginTop(0);
        FHBox60.setMarginLeft(0);
        FHBox60.setMarginRight(0);
        FHBox60.setMarginBottom(0);
        FHBox60.setSpacing(1);
        FHBox60.setFlexVflex("ftFalse");
        FHBox60.setFlexHflex("ftFalse");
        FHBox60.setScrollable(false);
        FHBox60.setBoxShadowConfigHorizontalLength(10);
        FHBox60.setBoxShadowConfigVerticalLength(10);
        FHBox60.setBoxShadowConfigBlurRadius(5);
        FHBox60.setBoxShadowConfigSpreadRadius(0);
        FHBox60.setBoxShadowConfigShadowColor("clBlack");
        FHBox60.setBoxShadowConfigOpacity(75);
        FHBox60.setVAlign("tvTop");
        hBoxInformeCheques.addChildren(FHBox60);
        FHBox60.applyProperties();
    }

    public TFVBox vBoxGridCheques = new TFVBox();

    private void init_vBoxGridCheques() {
        vBoxGridCheques.setName("vBoxGridCheques");
        vBoxGridCheques.setLeft(297);
        vBoxGridCheques.setTop(0);
        vBoxGridCheques.setWidth(531);
        vBoxGridCheques.setHeight(226);
        vBoxGridCheques.setBorderStyle("stSingleLine");
        vBoxGridCheques.setPaddingTop(0);
        vBoxGridCheques.setPaddingLeft(0);
        vBoxGridCheques.setPaddingRight(0);
        vBoxGridCheques.setPaddingBottom(0);
        vBoxGridCheques.setMarginTop(0);
        vBoxGridCheques.setMarginLeft(0);
        vBoxGridCheques.setMarginRight(0);
        vBoxGridCheques.setMarginBottom(0);
        vBoxGridCheques.setSpacing(1);
        vBoxGridCheques.setFlexVflex("ftTrue");
        vBoxGridCheques.setFlexHflex("ftTrue");
        vBoxGridCheques.setScrollable(false);
        vBoxGridCheques.setBoxShadowConfigHorizontalLength(10);
        vBoxGridCheques.setBoxShadowConfigVerticalLength(10);
        vBoxGridCheques.setBoxShadowConfigBlurRadius(5);
        vBoxGridCheques.setBoxShadowConfigSpreadRadius(0);
        vBoxGridCheques.setBoxShadowConfigShadowColor("clBlack");
        vBoxGridCheques.setBoxShadowConfigOpacity(75);
        hBoxInformeCheques.addChildren(vBoxGridCheques);
        vBoxGridCheques.applyProperties();
    }

    public TFGrid grdChequesInformados = new TFGrid();

    private void init_grdChequesInformados() {
        grdChequesInformados.setName("grdChequesInformados");
        grdChequesInformados.setLeft(0);
        grdChequesInformados.setTop(0);
        grdChequesInformados.setWidth(456);
        grdChequesInformados.setHeight(120);
        grdChequesInformados.setTable(tbLeadsPgtoVendaInfCheque);
        grdChequesInformados.setFlexVflex("ftTrue");
        grdChequesInformados.setFlexHflex("ftTrue");
        grdChequesInformados.setPagingEnabled(false);
        grdChequesInformados.setFrozenColumns(0);
        grdChequesInformados.setShowFooter(false);
        grdChequesInformados.setShowHeader(true);
        grdChequesInformados.setMultiSelection(false);
        grdChequesInformados.setGroupingEnabled(false);
        grdChequesInformados.setGroupingExpanded(false);
        grdChequesInformados.setGroupingShowFooter(false);
        grdChequesInformados.setCrosstabEnabled(false);
        grdChequesInformados.setCrosstabGroupType("cgtConcat");
        grdChequesInformados.setEditionEnabled(false);
        grdChequesInformados.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NR_CHEQUE");
        item0.setTitleCaption("Nr. Cheque");
        item0.setWidth(80);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdChequesInformados.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DATA_VENCIMENTO");
        item1.setTitleCaption("Dt.Vencimento");
        item1.setWidth(90);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taCenter");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        TFMaskExpression item2 = new TFMaskExpression();
        item2.setExpression("*");
        item2.setEvalType("etExpression");
        item2.setMask("dd/MM/yyyy");
        item2.setPadLength(0);
        item2.setPadDirection("pdNone");
        item2.setMaskType("mtDateTime");
        item1.getMasks().add(item2);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdChequesInformados.getColumns().add(item1);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("VALOR");
        item3.setTitleCaption("Valor");
        item3.setWidth(105);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask(",##0.00");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDecimal");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdChequesInformados.getColumns().add(item3);
        TFGridColumn item5 = new TFGridColumn();
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(true);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdChequesInformados.getColumns().add(item5);
        vBoxGridCheques.addChildren(grdChequesInformados);
        grdChequesInformados.applyProperties();
    }

    public TFHBox hBoxEditarCheques = new TFHBox();

    private void init_hBoxEditarCheques() {
        hBoxEditarCheques.setName("hBoxEditarCheques");
        hBoxEditarCheques.setLeft(0);
        hBoxEditarCheques.setTop(121);
        hBoxEditarCheques.setWidth(781);
        hBoxEditarCheques.setHeight(48);
        hBoxEditarCheques.setBorderStyle("stNone");
        hBoxEditarCheques.setPaddingTop(0);
        hBoxEditarCheques.setPaddingLeft(0);
        hBoxEditarCheques.setPaddingRight(0);
        hBoxEditarCheques.setPaddingBottom(0);
        hBoxEditarCheques.setMarginTop(0);
        hBoxEditarCheques.setMarginLeft(0);
        hBoxEditarCheques.setMarginRight(0);
        hBoxEditarCheques.setMarginBottom(0);
        hBoxEditarCheques.setSpacing(1);
        hBoxEditarCheques.setFlexVflex("ftFalse");
        hBoxEditarCheques.setFlexHflex("ftFalse");
        hBoxEditarCheques.setScrollable(false);
        hBoxEditarCheques.setBoxShadowConfigHorizontalLength(10);
        hBoxEditarCheques.setBoxShadowConfigVerticalLength(10);
        hBoxEditarCheques.setBoxShadowConfigBlurRadius(5);
        hBoxEditarCheques.setBoxShadowConfigSpreadRadius(0);
        hBoxEditarCheques.setBoxShadowConfigShadowColor("clBlack");
        hBoxEditarCheques.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.setVAlign("tvTop");
        vBoxGridCheques.addChildren(hBoxEditarCheques);
        hBoxEditarCheques.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(8);
        FVBox5.setHeight(41);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFVBox FVBox28 = new TFVBox();

    private void init_FVBox28() {
        FVBox28.setName("FVBox28");
        FVBox28.setLeft(8);
        FVBox28.setTop(0);
        FVBox28.setWidth(86);
        FVBox28.setHeight(45);
        FVBox28.setBorderStyle("stNone");
        FVBox28.setPaddingTop(0);
        FVBox28.setPaddingLeft(0);
        FVBox28.setPaddingRight(0);
        FVBox28.setPaddingBottom(0);
        FVBox28.setMarginTop(0);
        FVBox28.setMarginLeft(0);
        FVBox28.setMarginRight(0);
        FVBox28.setMarginBottom(0);
        FVBox28.setSpacing(1);
        FVBox28.setFlexVflex("ftTrue");
        FVBox28.setFlexHflex("ftFalse");
        FVBox28.setScrollable(false);
        FVBox28.setBoxShadowConfigHorizontalLength(10);
        FVBox28.setBoxShadowConfigVerticalLength(10);
        FVBox28.setBoxShadowConfigBlurRadius(5);
        FVBox28.setBoxShadowConfigSpreadRadius(0);
        FVBox28.setBoxShadowConfigShadowColor("clBlack");
        FVBox28.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(FVBox28);
        FVBox28.applyProperties();
    }

    public TFLabel lblChequeNr = new TFLabel();

    private void init_lblChequeNr() {
        lblChequeNr.setName("lblChequeNr");
        lblChequeNr.setLeft(0);
        lblChequeNr.setTop(0);
        lblChequeNr.setWidth(55);
        lblChequeNr.setHeight(13);
        lblChequeNr.setAlign("alLeft");
        lblChequeNr.setCaption("Nr. Cheque");
        lblChequeNr.setFontColor("clWindowText");
        lblChequeNr.setFontSize(-11);
        lblChequeNr.setFontName("Tahoma");
        lblChequeNr.setFontStyle("[]");
        lblChequeNr.setVerticalAlignment("taVerticalCenter");
        lblChequeNr.setWordBreak(false);
        FVBox28.addChildren(lblChequeNr);
        lblChequeNr.applyProperties();
    }

    public TFString edtChequeNumero = new TFString();

    private void init_edtChequeNumero() {
        edtChequeNumero.setName("edtChequeNumero");
        edtChequeNumero.setLeft(0);
        edtChequeNumero.setTop(14);
        edtChequeNumero.setWidth(81);
        edtChequeNumero.setHeight(24);
        edtChequeNumero.setFlex(false);
        edtChequeNumero.setRequired(false);
        edtChequeNumero.setConstraintCheckWhen("cwImmediate");
        edtChequeNumero.setConstraintCheckType("ctExpression");
        edtChequeNumero.setConstraintFocusOnError(false);
        edtChequeNumero.setConstraintEnableUI(true);
        edtChequeNumero.setConstraintEnabled(false);
        edtChequeNumero.setConstraintFormCheck(true);
        edtChequeNumero.setCharCase("ccNormal");
        edtChequeNumero.setPwd(false);
        edtChequeNumero.setMaxlength(8);
        edtChequeNumero.setEnabled(false);
        edtChequeNumero.setFontColor("clWindowText");
        edtChequeNumero.setFontSize(-13);
        edtChequeNumero.setFontName("Tahoma");
        edtChequeNumero.setFontStyle("[]");
        edtChequeNumero.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtChequeNumeroExit(event);
            processarFlow("FrmPgtoVendaInf", "edtChequeNumero", "OnExit");
        });
        edtChequeNumero.setSaveLiteralCharacter(false);
        edtChequeNumero.applyProperties();
        FVBox28.addChildren(edtChequeNumero);
        addValidatable(edtChequeNumero);
    }

    public TFVBox FVBox33 = new TFVBox();

    private void init_FVBox33() {
        FVBox33.setName("FVBox33");
        FVBox33.setLeft(94);
        FVBox33.setTop(0);
        FVBox33.setWidth(4);
        FVBox33.setHeight(41);
        FVBox33.setBorderStyle("stNone");
        FVBox33.setPaddingTop(0);
        FVBox33.setPaddingLeft(0);
        FVBox33.setPaddingRight(0);
        FVBox33.setPaddingBottom(0);
        FVBox33.setMarginTop(0);
        FVBox33.setMarginLeft(0);
        FVBox33.setMarginRight(0);
        FVBox33.setMarginBottom(0);
        FVBox33.setSpacing(1);
        FVBox33.setFlexVflex("ftFalse");
        FVBox33.setFlexHflex("ftFalse");
        FVBox33.setScrollable(false);
        FVBox33.setBoxShadowConfigHorizontalLength(10);
        FVBox33.setBoxShadowConfigVerticalLength(10);
        FVBox33.setBoxShadowConfigBlurRadius(5);
        FVBox33.setBoxShadowConfigSpreadRadius(0);
        FVBox33.setBoxShadowConfigShadowColor("clBlack");
        FVBox33.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(FVBox33);
        FVBox33.applyProperties();
    }

    public TFVBox FVBox34 = new TFVBox();

    private void init_FVBox34() {
        FVBox34.setName("FVBox34");
        FVBox34.setLeft(98);
        FVBox34.setTop(0);
        FVBox34.setWidth(127);
        FVBox34.setHeight(45);
        FVBox34.setBorderStyle("stNone");
        FVBox34.setPaddingTop(0);
        FVBox34.setPaddingLeft(0);
        FVBox34.setPaddingRight(0);
        FVBox34.setPaddingBottom(0);
        FVBox34.setMarginTop(0);
        FVBox34.setMarginLeft(0);
        FVBox34.setMarginRight(0);
        FVBox34.setMarginBottom(0);
        FVBox34.setSpacing(1);
        FVBox34.setFlexVflex("ftTrue");
        FVBox34.setFlexHflex("ftFalse");
        FVBox34.setScrollable(false);
        FVBox34.setBoxShadowConfigHorizontalLength(10);
        FVBox34.setBoxShadowConfigVerticalLength(10);
        FVBox34.setBoxShadowConfigBlurRadius(5);
        FVBox34.setBoxShadowConfigSpreadRadius(0);
        FVBox34.setBoxShadowConfigShadowColor("clBlack");
        FVBox34.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(FVBox34);
        FVBox34.applyProperties();
    }

    public TFLabel lblChequeVencimento = new TFLabel();

    private void init_lblChequeVencimento() {
        lblChequeVencimento.setName("lblChequeVencimento");
        lblChequeVencimento.setLeft(0);
        lblChequeVencimento.setTop(0);
        lblChequeVencimento.setWidth(70);
        lblChequeVencimento.setHeight(13);
        lblChequeVencimento.setAlign("alLeft");
        lblChequeVencimento.setCaption("Dt.Vencimento");
        lblChequeVencimento.setFontColor("clWindowText");
        lblChequeVencimento.setFontSize(-11);
        lblChequeVencimento.setFontName("Tahoma");
        lblChequeVencimento.setFontStyle("[]");
        lblChequeVencimento.setVerticalAlignment("taVerticalCenter");
        lblChequeVencimento.setWordBreak(false);
        FVBox34.addChildren(lblChequeVencimento);
        lblChequeVencimento.applyProperties();
    }

    public TFDate edtChequeDtVencimento = new TFDate();

    private void init_edtChequeDtVencimento() {
        edtChequeDtVencimento.setName("edtChequeDtVencimento");
        edtChequeDtVencimento.setLeft(0);
        edtChequeDtVencimento.setTop(14);
        edtChequeDtVencimento.setWidth(121);
        edtChequeDtVencimento.setHeight(24);
        edtChequeDtVencimento.setFlex(false);
        edtChequeDtVencimento.setRequired(false);
        edtChequeDtVencimento.setConstraintCheckWhen("cwImmediate");
        edtChequeDtVencimento.setConstraintCheckType("ctExpression");
        edtChequeDtVencimento.setConstraintFocusOnError(false);
        edtChequeDtVencimento.setConstraintEnableUI(true);
        edtChequeDtVencimento.setConstraintEnabled(false);
        edtChequeDtVencimento.setConstraintFormCheck(true);
        edtChequeDtVencimento.setFormat("dd/MM/yyyy");
        edtChequeDtVencimento.setShowCheckBox(false);
        edtChequeDtVencimento.setEnabled(false);
        FVBox34.addChildren(edtChequeDtVencimento);
        edtChequeDtVencimento.applyProperties();
        addValidatable(edtChequeDtVencimento);
    }

    public TFVBox FVBox35 = new TFVBox();

    private void init_FVBox35() {
        FVBox35.setName("FVBox35");
        FVBox35.setLeft(225);
        FVBox35.setTop(0);
        FVBox35.setWidth(4);
        FVBox35.setHeight(41);
        FVBox35.setBorderStyle("stNone");
        FVBox35.setPaddingTop(0);
        FVBox35.setPaddingLeft(0);
        FVBox35.setPaddingRight(0);
        FVBox35.setPaddingBottom(0);
        FVBox35.setVisible(false);
        FVBox35.setMarginTop(0);
        FVBox35.setMarginLeft(0);
        FVBox35.setMarginRight(0);
        FVBox35.setMarginBottom(0);
        FVBox35.setSpacing(1);
        FVBox35.setFlexVflex("ftFalse");
        FVBox35.setFlexHflex("ftFalse");
        FVBox35.setScrollable(false);
        FVBox35.setBoxShadowConfigHorizontalLength(10);
        FVBox35.setBoxShadowConfigVerticalLength(10);
        FVBox35.setBoxShadowConfigBlurRadius(5);
        FVBox35.setBoxShadowConfigSpreadRadius(0);
        FVBox35.setBoxShadowConfigShadowColor("clBlack");
        FVBox35.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(FVBox35);
        FVBox35.applyProperties();
    }

    public TFVBox FVBox32 = new TFVBox();

    private void init_FVBox32() {
        FVBox32.setName("FVBox32");
        FVBox32.setLeft(229);
        FVBox32.setTop(0);
        FVBox32.setWidth(115);
        FVBox32.setHeight(45);
        FVBox32.setBorderStyle("stNone");
        FVBox32.setPaddingTop(0);
        FVBox32.setPaddingLeft(0);
        FVBox32.setPaddingRight(0);
        FVBox32.setPaddingBottom(0);
        FVBox32.setMarginTop(0);
        FVBox32.setMarginLeft(0);
        FVBox32.setMarginRight(0);
        FVBox32.setMarginBottom(0);
        FVBox32.setSpacing(1);
        FVBox32.setFlexVflex("ftTrue");
        FVBox32.setFlexHflex("ftFalse");
        FVBox32.setScrollable(false);
        FVBox32.setBoxShadowConfigHorizontalLength(10);
        FVBox32.setBoxShadowConfigVerticalLength(10);
        FVBox32.setBoxShadowConfigBlurRadius(5);
        FVBox32.setBoxShadowConfigSpreadRadius(0);
        FVBox32.setBoxShadowConfigShadowColor("clBlack");
        FVBox32.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(FVBox32);
        FVBox32.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(0);
        FLabel6.setWidth(24);
        FLabel6.setHeight(13);
        FLabel6.setAlign("alLeft");
        FLabel6.setCaption("Valor");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FVBox32.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFDecimal edtChequeValor = new TFDecimal();

    private void init_edtChequeValor() {
        edtChequeValor.setName("edtChequeValor");
        edtChequeValor.setLeft(0);
        edtChequeValor.setTop(14);
        edtChequeValor.setWidth(108);
        edtChequeValor.setHeight(24);
        edtChequeValor.setFlex(true);
        edtChequeValor.setRequired(false);
        edtChequeValor.setConstraintCheckWhen("cwImmediate");
        edtChequeValor.setConstraintCheckType("ctExpression");
        edtChequeValor.setConstraintFocusOnError(false);
        edtChequeValor.setConstraintEnableUI(true);
        edtChequeValor.setConstraintEnabled(false);
        edtChequeValor.setConstraintFormCheck(true);
        edtChequeValor.setMaxlength(0);
        edtChequeValor.setPrecision(0);
        edtChequeValor.setEnabled(false);
        edtChequeValor.setFontColor("clWindowText");
        edtChequeValor.setFontSize(-13);
        edtChequeValor.setFontName("Tahoma");
        edtChequeValor.setFontStyle("[]");
        edtChequeValor.setAlignment("taRightJustify");
        FVBox32.addChildren(edtChequeValor);
        edtChequeValor.applyProperties();
        addValidatable(edtChequeValor);
    }

    public TFVBox FVBox31 = new TFVBox();

    private void init_FVBox31() {
        FVBox31.setName("FVBox31");
        FVBox31.setLeft(344);
        FVBox31.setTop(0);
        FVBox31.setWidth(5);
        FVBox31.setHeight(41);
        FVBox31.setBorderStyle("stNone");
        FVBox31.setPaddingTop(0);
        FVBox31.setPaddingLeft(0);
        FVBox31.setPaddingRight(0);
        FVBox31.setPaddingBottom(0);
        FVBox31.setMarginTop(0);
        FVBox31.setMarginLeft(0);
        FVBox31.setMarginRight(0);
        FVBox31.setMarginBottom(0);
        FVBox31.setSpacing(1);
        FVBox31.setFlexVflex("ftFalse");
        FVBox31.setFlexHflex("ftFalse");
        FVBox31.setScrollable(false);
        FVBox31.setBoxShadowConfigHorizontalLength(10);
        FVBox31.setBoxShadowConfigVerticalLength(10);
        FVBox31.setBoxShadowConfigBlurRadius(5);
        FVBox31.setBoxShadowConfigSpreadRadius(0);
        FVBox31.setBoxShadowConfigShadowColor("clBlack");
        FVBox31.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(FVBox31);
        FVBox31.applyProperties();
    }

    public TFVBox vBoxChequeAlterar = new TFVBox();

    private void init_vBoxChequeAlterar() {
        vBoxChequeAlterar.setName("vBoxChequeAlterar");
        vBoxChequeAlterar.setLeft(349);
        vBoxChequeAlterar.setTop(0);
        vBoxChequeAlterar.setWidth(154);
        vBoxChequeAlterar.setHeight(41);
        vBoxChequeAlterar.setBorderStyle("stNone");
        vBoxChequeAlterar.setPaddingTop(0);
        vBoxChequeAlterar.setPaddingLeft(0);
        vBoxChequeAlterar.setPaddingRight(0);
        vBoxChequeAlterar.setPaddingBottom(0);
        vBoxChequeAlterar.setMarginTop(0);
        vBoxChequeAlterar.setMarginLeft(0);
        vBoxChequeAlterar.setMarginRight(0);
        vBoxChequeAlterar.setMarginBottom(0);
        vBoxChequeAlterar.setSpacing(1);
        vBoxChequeAlterar.setFlexVflex("ftTrue");
        vBoxChequeAlterar.setFlexHflex("ftFalse");
        vBoxChequeAlterar.setScrollable(false);
        vBoxChequeAlterar.setBoxShadowConfigHorizontalLength(10);
        vBoxChequeAlterar.setBoxShadowConfigVerticalLength(10);
        vBoxChequeAlterar.setBoxShadowConfigBlurRadius(5);
        vBoxChequeAlterar.setBoxShadowConfigSpreadRadius(0);
        vBoxChequeAlterar.setBoxShadowConfigShadowColor("clBlack");
        vBoxChequeAlterar.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(vBoxChequeAlterar);
        vBoxChequeAlterar.applyProperties();
    }

    public TFHBox hBoxChequeBtnAlterarTop = new TFHBox();

    private void init_hBoxChequeBtnAlterarTop() {
        hBoxChequeBtnAlterarTop.setName("hBoxChequeBtnAlterarTop");
        hBoxChequeBtnAlterarTop.setLeft(0);
        hBoxChequeBtnAlterarTop.setTop(0);
        hBoxChequeBtnAlterarTop.setWidth(33);
        hBoxChequeBtnAlterarTop.setHeight(13);
        hBoxChequeBtnAlterarTop.setBorderStyle("stNone");
        hBoxChequeBtnAlterarTop.setPaddingTop(0);
        hBoxChequeBtnAlterarTop.setPaddingLeft(0);
        hBoxChequeBtnAlterarTop.setPaddingRight(0);
        hBoxChequeBtnAlterarTop.setPaddingBottom(0);
        hBoxChequeBtnAlterarTop.setMarginTop(0);
        hBoxChequeBtnAlterarTop.setMarginLeft(0);
        hBoxChequeBtnAlterarTop.setMarginRight(0);
        hBoxChequeBtnAlterarTop.setMarginBottom(0);
        hBoxChequeBtnAlterarTop.setSpacing(1);
        hBoxChequeBtnAlterarTop.setFlexVflex("ftFalse");
        hBoxChequeBtnAlterarTop.setFlexHflex("ftTrue");
        hBoxChequeBtnAlterarTop.setScrollable(false);
        hBoxChequeBtnAlterarTop.setBoxShadowConfigHorizontalLength(10);
        hBoxChequeBtnAlterarTop.setBoxShadowConfigVerticalLength(10);
        hBoxChequeBtnAlterarTop.setBoxShadowConfigBlurRadius(5);
        hBoxChequeBtnAlterarTop.setBoxShadowConfigSpreadRadius(0);
        hBoxChequeBtnAlterarTop.setBoxShadowConfigShadowColor("clBlack");
        hBoxChequeBtnAlterarTop.setBoxShadowConfigOpacity(75);
        hBoxChequeBtnAlterarTop.setVAlign("tvTop");
        vBoxChequeAlterar.addChildren(hBoxChequeBtnAlterarTop);
        hBoxChequeBtnAlterarTop.applyProperties();
    }

    public TFHBox FHBox73 = new TFHBox();

    private void init_FHBox73() {
        FHBox73.setName("FHBox73");
        FHBox73.setLeft(0);
        FHBox73.setTop(14);
        FHBox73.setWidth(152);
        FHBox73.setHeight(34);
        FHBox73.setBorderStyle("stNone");
        FHBox73.setPaddingTop(0);
        FHBox73.setPaddingLeft(0);
        FHBox73.setPaddingRight(0);
        FHBox73.setPaddingBottom(0);
        FHBox73.setMarginTop(0);
        FHBox73.setMarginLeft(0);
        FHBox73.setMarginRight(0);
        FHBox73.setMarginBottom(0);
        FHBox73.setSpacing(1);
        FHBox73.setFlexVflex("ftTrue");
        FHBox73.setFlexHflex("ftFalse");
        FHBox73.setScrollable(false);
        FHBox73.setBoxShadowConfigHorizontalLength(10);
        FHBox73.setBoxShadowConfigVerticalLength(10);
        FHBox73.setBoxShadowConfigBlurRadius(5);
        FHBox73.setBoxShadowConfigSpreadRadius(0);
        FHBox73.setBoxShadowConfigShadowColor("clBlack");
        FHBox73.setBoxShadowConfigOpacity(75);
        FHBox73.setVAlign("tvTop");
        vBoxChequeAlterar.addChildren(FHBox73);
        FHBox73.applyProperties();
    }

    public TFButton btnChequeAlterar = new TFButton();

    private void init_btnChequeAlterar() {
        btnChequeAlterar.setName("btnChequeAlterar");
        btnChequeAlterar.setLeft(0);
        btnChequeAlterar.setTop(0);
        btnChequeAlterar.setWidth(70);
        btnChequeAlterar.setHeight(25);
        btnChequeAlterar.setHint("Alterar cheque");
        btnChequeAlterar.setCaption("Alterar");
        btnChequeAlterar.setEnabled(false);
        btnChequeAlterar.setFontColor("clWindowText");
        btnChequeAlterar.setFontSize(-11);
        btnChequeAlterar.setFontName("Tahoma");
        btnChequeAlterar.setFontStyle("[]");
        btnChequeAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnChequeAlterarClick(event);
            processarFlow("FrmPgtoVendaInf", "btnChequeAlterar", "OnClick");
        });
        btnChequeAlterar.setImageId(0);
        btnChequeAlterar.setColor("clBtnFace");
        btnChequeAlterar.setAccess(false);
        btnChequeAlterar.setIconClass("edit");
        btnChequeAlterar.setIconReverseDirection(false);
        FHBox73.addChildren(btnChequeAlterar);
        btnChequeAlterar.applyProperties();
    }

    public TFHBox FHBox74 = new TFHBox();

    private void init_FHBox74() {
        FHBox74.setName("FHBox74");
        FHBox74.setLeft(70);
        FHBox74.setTop(0);
        FHBox74.setWidth(4);
        FHBox74.setHeight(12);
        FHBox74.setBorderStyle("stNone");
        FHBox74.setPaddingTop(0);
        FHBox74.setPaddingLeft(0);
        FHBox74.setPaddingRight(0);
        FHBox74.setPaddingBottom(0);
        FHBox74.setMarginTop(0);
        FHBox74.setMarginLeft(0);
        FHBox74.setMarginRight(0);
        FHBox74.setMarginBottom(0);
        FHBox74.setSpacing(1);
        FHBox74.setFlexVflex("ftFalse");
        FHBox74.setFlexHflex("ftFalse");
        FHBox74.setScrollable(false);
        FHBox74.setBoxShadowConfigHorizontalLength(10);
        FHBox74.setBoxShadowConfigVerticalLength(10);
        FHBox74.setBoxShadowConfigBlurRadius(5);
        FHBox74.setBoxShadowConfigSpreadRadius(0);
        FHBox74.setBoxShadowConfigShadowColor("clBlack");
        FHBox74.setBoxShadowConfigOpacity(75);
        FHBox74.setVAlign("tvTop");
        FHBox73.addChildren(FHBox74);
        FHBox74.applyProperties();
    }

    public TFButton btnChequeExcluir = new TFButton();

    private void init_btnChequeExcluir() {
        btnChequeExcluir.setName("btnChequeExcluir");
        btnChequeExcluir.setLeft(74);
        btnChequeExcluir.setTop(0);
        btnChequeExcluir.setWidth(70);
        btnChequeExcluir.setHeight(25);
        btnChequeExcluir.setHint("Excluir cheque");
        btnChequeExcluir.setCaption("Excluir");
        btnChequeExcluir.setEnabled(false);
        btnChequeExcluir.setFontColor("clWindowText");
        btnChequeExcluir.setFontSize(-11);
        btnChequeExcluir.setFontName("Tahoma");
        btnChequeExcluir.setFontStyle("[]");
        btnChequeExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnChequeExcluirClick(event);
            processarFlow("FrmPgtoVendaInf", "btnChequeExcluir", "OnClick");
        });
        btnChequeExcluir.setImageId(0);
        btnChequeExcluir.setColor("clBtnFace");
        btnChequeExcluir.setAccess(false);
        btnChequeExcluir.setIconClass("trash");
        btnChequeExcluir.setIconReverseDirection(false);
        FHBox73.addChildren(btnChequeExcluir);
        btnChequeExcluir.applyProperties();
    }

    public TFVBox vBoxChequesSalvarAlteracoes = new TFVBox();

    private void init_vBoxChequesSalvarAlteracoes() {
        vBoxChequesSalvarAlteracoes.setName("vBoxChequesSalvarAlteracoes");
        vBoxChequesSalvarAlteracoes.setLeft(503);
        vBoxChequesSalvarAlteracoes.setTop(0);
        vBoxChequesSalvarAlteracoes.setWidth(170);
        vBoxChequesSalvarAlteracoes.setHeight(41);
        vBoxChequesSalvarAlteracoes.setBorderStyle("stNone");
        vBoxChequesSalvarAlteracoes.setPaddingTop(0);
        vBoxChequesSalvarAlteracoes.setPaddingLeft(0);
        vBoxChequesSalvarAlteracoes.setPaddingRight(0);
        vBoxChequesSalvarAlteracoes.setPaddingBottom(0);
        vBoxChequesSalvarAlteracoes.setMarginTop(0);
        vBoxChequesSalvarAlteracoes.setMarginLeft(0);
        vBoxChequesSalvarAlteracoes.setMarginRight(0);
        vBoxChequesSalvarAlteracoes.setMarginBottom(0);
        vBoxChequesSalvarAlteracoes.setSpacing(1);
        vBoxChequesSalvarAlteracoes.setFlexVflex("ftFalse");
        vBoxChequesSalvarAlteracoes.setFlexHflex("ftFalse");
        vBoxChequesSalvarAlteracoes.setScrollable(false);
        vBoxChequesSalvarAlteracoes.setBoxShadowConfigHorizontalLength(10);
        vBoxChequesSalvarAlteracoes.setBoxShadowConfigVerticalLength(10);
        vBoxChequesSalvarAlteracoes.setBoxShadowConfigBlurRadius(5);
        vBoxChequesSalvarAlteracoes.setBoxShadowConfigSpreadRadius(0);
        vBoxChequesSalvarAlteracoes.setBoxShadowConfigShadowColor("clBlack");
        vBoxChequesSalvarAlteracoes.setBoxShadowConfigOpacity(75);
        hBoxEditarCheques.addChildren(vBoxChequesSalvarAlteracoes);
        vBoxChequesSalvarAlteracoes.applyProperties();
    }

    public TFHBox hBoxChequeBtnSalvarTop = new TFHBox();

    private void init_hBoxChequeBtnSalvarTop() {
        hBoxChequeBtnSalvarTop.setName("hBoxChequeBtnSalvarTop");
        hBoxChequeBtnSalvarTop.setLeft(0);
        hBoxChequeBtnSalvarTop.setTop(0);
        hBoxChequeBtnSalvarTop.setWidth(33);
        hBoxChequeBtnSalvarTop.setHeight(13);
        hBoxChequeBtnSalvarTop.setBorderStyle("stNone");
        hBoxChequeBtnSalvarTop.setPaddingTop(0);
        hBoxChequeBtnSalvarTop.setPaddingLeft(0);
        hBoxChequeBtnSalvarTop.setPaddingRight(0);
        hBoxChequeBtnSalvarTop.setPaddingBottom(0);
        hBoxChequeBtnSalvarTop.setMarginTop(0);
        hBoxChequeBtnSalvarTop.setMarginLeft(0);
        hBoxChequeBtnSalvarTop.setMarginRight(0);
        hBoxChequeBtnSalvarTop.setMarginBottom(0);
        hBoxChequeBtnSalvarTop.setSpacing(1);
        hBoxChequeBtnSalvarTop.setFlexVflex("ftFalse");
        hBoxChequeBtnSalvarTop.setFlexHflex("ftTrue");
        hBoxChequeBtnSalvarTop.setScrollable(false);
        hBoxChequeBtnSalvarTop.setBoxShadowConfigHorizontalLength(10);
        hBoxChequeBtnSalvarTop.setBoxShadowConfigVerticalLength(10);
        hBoxChequeBtnSalvarTop.setBoxShadowConfigBlurRadius(5);
        hBoxChequeBtnSalvarTop.setBoxShadowConfigSpreadRadius(0);
        hBoxChequeBtnSalvarTop.setBoxShadowConfigShadowColor("clBlack");
        hBoxChequeBtnSalvarTop.setBoxShadowConfigOpacity(75);
        hBoxChequeBtnSalvarTop.setVAlign("tvTop");
        vBoxChequesSalvarAlteracoes.addChildren(hBoxChequeBtnSalvarTop);
        hBoxChequeBtnSalvarTop.applyProperties();
    }

    public TFHBox FHBox71 = new TFHBox();

    private void init_FHBox71() {
        FHBox71.setName("FHBox71");
        FHBox71.setLeft(0);
        FHBox71.setTop(14);
        FHBox71.setWidth(185);
        FHBox71.setHeight(34);
        FHBox71.setBorderStyle("stNone");
        FHBox71.setPaddingTop(0);
        FHBox71.setPaddingLeft(0);
        FHBox71.setPaddingRight(0);
        FHBox71.setPaddingBottom(0);
        FHBox71.setMarginTop(0);
        FHBox71.setMarginLeft(0);
        FHBox71.setMarginRight(0);
        FHBox71.setMarginBottom(0);
        FHBox71.setSpacing(1);
        FHBox71.setFlexVflex("ftFalse");
        FHBox71.setFlexHflex("ftFalse");
        FHBox71.setScrollable(false);
        FHBox71.setBoxShadowConfigHorizontalLength(10);
        FHBox71.setBoxShadowConfigVerticalLength(10);
        FHBox71.setBoxShadowConfigBlurRadius(5);
        FHBox71.setBoxShadowConfigSpreadRadius(0);
        FHBox71.setBoxShadowConfigShadowColor("clBlack");
        FHBox71.setBoxShadowConfigOpacity(75);
        FHBox71.setVAlign("tvTop");
        vBoxChequesSalvarAlteracoes.addChildren(FHBox71);
        FHBox71.applyProperties();
    }

    public TFButton btnChequeConfirmar = new TFButton();

    private void init_btnChequeConfirmar() {
        btnChequeConfirmar.setName("btnChequeConfirmar");
        btnChequeConfirmar.setLeft(0);
        btnChequeConfirmar.setTop(0);
        btnChequeConfirmar.setWidth(82);
        btnChequeConfirmar.setHeight(25);
        btnChequeConfirmar.setCaption("Confirmar");
        btnChequeConfirmar.setEnabled(false);
        btnChequeConfirmar.setFontColor("clWindowText");
        btnChequeConfirmar.setFontSize(-11);
        btnChequeConfirmar.setFontName("Tahoma");
        btnChequeConfirmar.setFontStyle("[]");
        btnChequeConfirmar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnChequeConfirmarClick(event);
            processarFlow("FrmPgtoVendaInf", "btnChequeConfirmar", "OnClick");
        });
        btnChequeConfirmar.setImageId(220011);
        btnChequeConfirmar.setColor("clBtnFace");
        btnChequeConfirmar.setAccess(false);
        btnChequeConfirmar.setIconReverseDirection(false);
        FHBox71.addChildren(btnChequeConfirmar);
        btnChequeConfirmar.applyProperties();
    }

    public TFHBox FHBox75 = new TFHBox();

    private void init_FHBox75() {
        FHBox75.setName("FHBox75");
        FHBox75.setLeft(82);
        FHBox75.setTop(0);
        FHBox75.setWidth(4);
        FHBox75.setHeight(12);
        FHBox75.setBorderStyle("stNone");
        FHBox75.setPaddingTop(0);
        FHBox75.setPaddingLeft(0);
        FHBox75.setPaddingRight(0);
        FHBox75.setPaddingBottom(0);
        FHBox75.setMarginTop(0);
        FHBox75.setMarginLeft(0);
        FHBox75.setMarginRight(0);
        FHBox75.setMarginBottom(0);
        FHBox75.setSpacing(1);
        FHBox75.setFlexVflex("ftFalse");
        FHBox75.setFlexHflex("ftFalse");
        FHBox75.setScrollable(false);
        FHBox75.setBoxShadowConfigHorizontalLength(10);
        FHBox75.setBoxShadowConfigVerticalLength(10);
        FHBox75.setBoxShadowConfigBlurRadius(5);
        FHBox75.setBoxShadowConfigSpreadRadius(0);
        FHBox75.setBoxShadowConfigShadowColor("clBlack");
        FHBox75.setBoxShadowConfigOpacity(75);
        FHBox75.setVAlign("tvTop");
        FHBox71.addChildren(FHBox75);
        FHBox75.applyProperties();
    }

    public TFButton btnChequeCancelar = new TFButton();

    private void init_btnChequeCancelar() {
        btnChequeCancelar.setName("btnChequeCancelar");
        btnChequeCancelar.setLeft(86);
        btnChequeCancelar.setTop(0);
        btnChequeCancelar.setWidth(82);
        btnChequeCancelar.setHeight(25);
        btnChequeCancelar.setCaption("Cancelar");
        btnChequeCancelar.setEnabled(false);
        btnChequeCancelar.setFontColor("clWindowText");
        btnChequeCancelar.setFontSize(-11);
        btnChequeCancelar.setFontName("Tahoma");
        btnChequeCancelar.setFontStyle("[]");
        btnChequeCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnChequeCancelarClick(event);
            processarFlow("FrmPgtoVendaInf", "btnChequeCancelar", "OnClick");
        });
        btnChequeCancelar.setImageId(220020);
        btnChequeCancelar.setColor("clBtnFace");
        btnChequeCancelar.setAccess(false);
        btnChequeCancelar.setIconReverseDirection(false);
        FHBox71.addChildren(btnChequeCancelar);
        btnChequeCancelar.applyProperties();
    }

    public TFTabsheet tbsBoleto = new TFTabsheet();

    private void init_tbsBoleto() {
        tbsBoleto.setName("tbsBoleto");
        tbsBoleto.setCaption("Boleto Banc\u00E1rio");
        tbsBoleto.setVisible(true);
        tbsBoleto.setClosable(false);
        pgcInfoPgto.addChildren(tbsBoleto);
        tbsBoleto.applyProperties();
    }

    public TFVBox vBoxBoletoBancario = new TFVBox();

    private void init_vBoxBoletoBancario() {
        vBoxBoletoBancario.setName("vBoxBoletoBancario");
        vBoxBoletoBancario.setLeft(0);
        vBoxBoletoBancario.setTop(0);
        vBoxBoletoBancario.setWidth(837);
        vBoxBoletoBancario.setHeight(314);
        vBoxBoletoBancario.setBorderStyle("stNone");
        vBoxBoletoBancario.setPaddingTop(0);
        vBoxBoletoBancario.setPaddingLeft(0);
        vBoxBoletoBancario.setPaddingRight(0);
        vBoxBoletoBancario.setPaddingBottom(0);
        vBoxBoletoBancario.setMarginTop(0);
        vBoxBoletoBancario.setMarginLeft(0);
        vBoxBoletoBancario.setMarginRight(0);
        vBoxBoletoBancario.setMarginBottom(0);
        vBoxBoletoBancario.setSpacing(1);
        vBoxBoletoBancario.setFlexVflex("ftTrue");
        vBoxBoletoBancario.setFlexHflex("ftTrue");
        vBoxBoletoBancario.setScrollable(false);
        vBoxBoletoBancario.setBoxShadowConfigHorizontalLength(10);
        vBoxBoletoBancario.setBoxShadowConfigVerticalLength(10);
        vBoxBoletoBancario.setBoxShadowConfigBlurRadius(5);
        vBoxBoletoBancario.setBoxShadowConfigSpreadRadius(0);
        vBoxBoletoBancario.setBoxShadowConfigShadowColor("clBlack");
        vBoxBoletoBancario.setBoxShadowConfigOpacity(75);
        tbsBoleto.addChildren(vBoxBoletoBancario);
        vBoxBoletoBancario.applyProperties();
    }

    public TFHBox FHBox40 = new TFHBox();

    private void init_FHBox40() {
        FHBox40.setName("FHBox40");
        FHBox40.setLeft(0);
        FHBox40.setTop(0);
        FHBox40.setWidth(746);
        FHBox40.setHeight(37);
        FHBox40.setBorderStyle("stNone");
        FHBox40.setPaddingTop(0);
        FHBox40.setPaddingLeft(0);
        FHBox40.setPaddingRight(0);
        FHBox40.setPaddingBottom(0);
        FHBox40.setVisible(false);
        FHBox40.setMarginTop(0);
        FHBox40.setMarginLeft(0);
        FHBox40.setMarginRight(0);
        FHBox40.setMarginBottom(0);
        FHBox40.setSpacing(1);
        FHBox40.setFlexVflex("ftMin");
        FHBox40.setFlexHflex("ftFalse");
        FHBox40.setScrollable(false);
        FHBox40.setBoxShadowConfigHorizontalLength(10);
        FHBox40.setBoxShadowConfigVerticalLength(10);
        FHBox40.setBoxShadowConfigBlurRadius(5);
        FHBox40.setBoxShadowConfigSpreadRadius(0);
        FHBox40.setBoxShadowConfigShadowColor("clBlack");
        FHBox40.setBoxShadowConfigOpacity(75);
        FHBox40.setVAlign("tvTop");
        vBoxBoletoBancario.addChildren(FHBox40);
        FHBox40.applyProperties();
    }

    public TFHBox FHBox41 = new TFHBox();

    private void init_FHBox41() {
        FHBox41.setName("FHBox41");
        FHBox41.setLeft(0);
        FHBox41.setTop(0);
        FHBox41.setWidth(4);
        FHBox41.setHeight(12);
        FHBox41.setBorderStyle("stNone");
        FHBox41.setPaddingTop(0);
        FHBox41.setPaddingLeft(0);
        FHBox41.setPaddingRight(0);
        FHBox41.setPaddingBottom(0);
        FHBox41.setMarginTop(0);
        FHBox41.setMarginLeft(0);
        FHBox41.setMarginRight(0);
        FHBox41.setMarginBottom(0);
        FHBox41.setSpacing(1);
        FHBox41.setFlexVflex("ftFalse");
        FHBox41.setFlexHflex("ftFalse");
        FHBox41.setScrollable(false);
        FHBox41.setBoxShadowConfigHorizontalLength(10);
        FHBox41.setBoxShadowConfigVerticalLength(10);
        FHBox41.setBoxShadowConfigBlurRadius(5);
        FHBox41.setBoxShadowConfigSpreadRadius(0);
        FHBox41.setBoxShadowConfigShadowColor("clBlack");
        FHBox41.setBoxShadowConfigOpacity(75);
        FHBox41.setVAlign("tvTop");
        FHBox40.addChildren(FHBox41);
        FHBox41.applyProperties();
    }

    public TFVBox FVBox12 = new TFVBox();

    private void init_FVBox12() {
        FVBox12.setName("FVBox12");
        FVBox12.setLeft(4);
        FVBox12.setTop(0);
        FVBox12.setWidth(58);
        FVBox12.setHeight(30);
        FVBox12.setBorderStyle("stNone");
        FVBox12.setPaddingTop(0);
        FVBox12.setPaddingLeft(0);
        FVBox12.setPaddingRight(0);
        FVBox12.setPaddingBottom(0);
        FVBox12.setMarginTop(0);
        FVBox12.setMarginLeft(0);
        FVBox12.setMarginRight(0);
        FVBox12.setMarginBottom(0);
        FVBox12.setSpacing(1);
        FVBox12.setFlexVflex("ftFalse");
        FVBox12.setFlexHflex("ftFalse");
        FVBox12.setScrollable(false);
        FVBox12.setBoxShadowConfigHorizontalLength(10);
        FVBox12.setBoxShadowConfigVerticalLength(10);
        FVBox12.setBoxShadowConfigBlurRadius(5);
        FVBox12.setBoxShadowConfigSpreadRadius(0);
        FVBox12.setBoxShadowConfigShadowColor("clBlack");
        FVBox12.setBoxShadowConfigOpacity(75);
        FHBox40.addChildren(FVBox12);
        FVBox12.applyProperties();
    }

    public TFHBox FHBox42 = new TFHBox();

    private void init_FHBox42() {
        FHBox42.setName("FHBox42");
        FHBox42.setLeft(0);
        FHBox42.setTop(0);
        FHBox42.setWidth(33);
        FHBox42.setHeight(4);
        FHBox42.setBorderStyle("stNone");
        FHBox42.setPaddingTop(0);
        FHBox42.setPaddingLeft(0);
        FHBox42.setPaddingRight(0);
        FHBox42.setPaddingBottom(0);
        FHBox42.setMarginTop(0);
        FHBox42.setMarginLeft(0);
        FHBox42.setMarginRight(0);
        FHBox42.setMarginBottom(0);
        FHBox42.setSpacing(1);
        FHBox42.setFlexVflex("ftFalse");
        FHBox42.setFlexHflex("ftTrue");
        FHBox42.setScrollable(false);
        FHBox42.setBoxShadowConfigHorizontalLength(10);
        FHBox42.setBoxShadowConfigVerticalLength(10);
        FHBox42.setBoxShadowConfigBlurRadius(5);
        FHBox42.setBoxShadowConfigSpreadRadius(0);
        FHBox42.setBoxShadowConfigShadowColor("clBlack");
        FHBox42.setBoxShadowConfigOpacity(75);
        FHBox42.setVAlign("tvTop");
        FVBox12.addChildren(FHBox42);
        FHBox42.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(5);
        FLabel5.setWidth(53);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Valor total:");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FVBox12.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFVBox FVBox13 = new TFVBox();

    private void init_FVBox13() {
        FVBox13.setName("FVBox13");
        FVBox13.setLeft(62);
        FVBox13.setTop(0);
        FVBox13.setWidth(117);
        FVBox13.setHeight(30);
        FVBox13.setBorderStyle("stNone");
        FVBox13.setPaddingTop(0);
        FVBox13.setPaddingLeft(0);
        FVBox13.setPaddingRight(0);
        FVBox13.setPaddingBottom(0);
        FVBox13.setMarginTop(0);
        FVBox13.setMarginLeft(0);
        FVBox13.setMarginRight(0);
        FVBox13.setMarginBottom(0);
        FVBox13.setSpacing(1);
        FVBox13.setFlexVflex("ftFalse");
        FVBox13.setFlexHflex("ftFalse");
        FVBox13.setScrollable(false);
        FVBox13.setBoxShadowConfigHorizontalLength(10);
        FVBox13.setBoxShadowConfigVerticalLength(10);
        FVBox13.setBoxShadowConfigBlurRadius(5);
        FVBox13.setBoxShadowConfigSpreadRadius(0);
        FVBox13.setBoxShadowConfigShadowColor("clBlack");
        FVBox13.setBoxShadowConfigOpacity(75);
        FHBox40.addChildren(FVBox13);
        FVBox13.applyProperties();
    }

    public TFHBox FHBox43 = new TFHBox();

    private void init_FHBox43() {
        FHBox43.setName("FHBox43");
        FHBox43.setLeft(0);
        FHBox43.setTop(0);
        FHBox43.setWidth(33);
        FHBox43.setHeight(1);
        FHBox43.setBorderStyle("stNone");
        FHBox43.setPaddingTop(0);
        FHBox43.setPaddingLeft(0);
        FHBox43.setPaddingRight(0);
        FHBox43.setPaddingBottom(0);
        FHBox43.setMarginTop(0);
        FHBox43.setMarginLeft(0);
        FHBox43.setMarginRight(0);
        FHBox43.setMarginBottom(0);
        FHBox43.setSpacing(1);
        FHBox43.setFlexVflex("ftFalse");
        FHBox43.setFlexHflex("ftTrue");
        FHBox43.setScrollable(false);
        FHBox43.setBoxShadowConfigHorizontalLength(10);
        FHBox43.setBoxShadowConfigVerticalLength(10);
        FHBox43.setBoxShadowConfigBlurRadius(5);
        FHBox43.setBoxShadowConfigSpreadRadius(0);
        FHBox43.setBoxShadowConfigShadowColor("clBlack");
        FHBox43.setBoxShadowConfigOpacity(75);
        FHBox43.setVAlign("tvTop");
        FVBox13.addChildren(FHBox43);
        FHBox43.applyProperties();
    }

    public TFDecimal edtBoletoValorTotal = new TFDecimal();

    private void init_edtBoletoValorTotal() {
        edtBoletoValorTotal.setName("edtBoletoValorTotal");
        edtBoletoValorTotal.setLeft(0);
        edtBoletoValorTotal.setTop(2);
        edtBoletoValorTotal.setWidth(108);
        edtBoletoValorTotal.setHeight(24);
        edtBoletoValorTotal.setFlex(true);
        edtBoletoValorTotal.setRequired(false);
        edtBoletoValorTotal.setConstraintCheckWhen("cwImmediate");
        edtBoletoValorTotal.setConstraintCheckType("ctExpression");
        edtBoletoValorTotal.setConstraintFocusOnError(false);
        edtBoletoValorTotal.setConstraintEnableUI(true);
        edtBoletoValorTotal.setConstraintEnabled(false);
        edtBoletoValorTotal.setConstraintFormCheck(true);
        edtBoletoValorTotal.setMaxlength(0);
        edtBoletoValorTotal.setPrecision(0);
        edtBoletoValorTotal.setFontColor("clWindowText");
        edtBoletoValorTotal.setFontSize(-13);
        edtBoletoValorTotal.setFontName("Tahoma");
        edtBoletoValorTotal.setFontStyle("[]");
        edtBoletoValorTotal.setAlignment("taRightJustify");
        FVBox13.addChildren(edtBoletoValorTotal);
        edtBoletoValorTotal.applyProperties();
        addValidatable(edtBoletoValorTotal);
    }

    public TFHBox FHBox44 = new TFHBox();

    private void init_FHBox44() {
        FHBox44.setName("FHBox44");
        FHBox44.setLeft(179);
        FHBox44.setTop(0);
        FHBox44.setWidth(4);
        FHBox44.setHeight(12);
        FHBox44.setBorderStyle("stNone");
        FHBox44.setPaddingTop(0);
        FHBox44.setPaddingLeft(0);
        FHBox44.setPaddingRight(0);
        FHBox44.setPaddingBottom(0);
        FHBox44.setMarginTop(0);
        FHBox44.setMarginLeft(0);
        FHBox44.setMarginRight(0);
        FHBox44.setMarginBottom(0);
        FHBox44.setSpacing(1);
        FHBox44.setFlexVflex("ftFalse");
        FHBox44.setFlexHflex("ftFalse");
        FHBox44.setScrollable(false);
        FHBox44.setBoxShadowConfigHorizontalLength(10);
        FHBox44.setBoxShadowConfigVerticalLength(10);
        FHBox44.setBoxShadowConfigBlurRadius(5);
        FHBox44.setBoxShadowConfigSpreadRadius(0);
        FHBox44.setBoxShadowConfigShadowColor("clBlack");
        FHBox44.setBoxShadowConfigOpacity(75);
        FHBox44.setVAlign("tvTop");
        FHBox40.addChildren(FHBox44);
        FHBox44.applyProperties();
    }

    public TFHBox FHBox45 = new TFHBox();

    private void init_FHBox45() {
        FHBox45.setName("FHBox45");
        FHBox45.setLeft(0);
        FHBox45.setTop(38);
        FHBox45.setWidth(733);
        FHBox45.setHeight(155);
        FHBox45.setBorderStyle("stNone");
        FHBox45.setPaddingTop(0);
        FHBox45.setPaddingLeft(0);
        FHBox45.setPaddingRight(0);
        FHBox45.setPaddingBottom(0);
        FHBox45.setMarginTop(0);
        FHBox45.setMarginLeft(0);
        FHBox45.setMarginRight(0);
        FHBox45.setMarginBottom(0);
        FHBox45.setSpacing(1);
        FHBox45.setFlexVflex("ftTrue");
        FHBox45.setFlexHflex("ftTrue");
        FHBox45.setScrollable(false);
        FHBox45.setBoxShadowConfigHorizontalLength(10);
        FHBox45.setBoxShadowConfigVerticalLength(10);
        FHBox45.setBoxShadowConfigBlurRadius(5);
        FHBox45.setBoxShadowConfigSpreadRadius(0);
        FHBox45.setBoxShadowConfigShadowColor("clBlack");
        FHBox45.setBoxShadowConfigOpacity(75);
        FHBox45.setVAlign("tvTop");
        vBoxBoletoBancario.addChildren(FHBox45);
        FHBox45.applyProperties();
    }

    public TFHBox FHBox46 = new TFHBox();

    private void init_FHBox46() {
        FHBox46.setName("FHBox46");
        FHBox46.setLeft(0);
        FHBox46.setTop(0);
        FHBox46.setWidth(4);
        FHBox46.setHeight(12);
        FHBox46.setBorderStyle("stNone");
        FHBox46.setPaddingTop(0);
        FHBox46.setPaddingLeft(0);
        FHBox46.setPaddingRight(0);
        FHBox46.setPaddingBottom(0);
        FHBox46.setVisible(false);
        FHBox46.setMarginTop(0);
        FHBox46.setMarginLeft(0);
        FHBox46.setMarginRight(0);
        FHBox46.setMarginBottom(0);
        FHBox46.setSpacing(1);
        FHBox46.setFlexVflex("ftFalse");
        FHBox46.setFlexHflex("ftFalse");
        FHBox46.setScrollable(false);
        FHBox46.setBoxShadowConfigHorizontalLength(10);
        FHBox46.setBoxShadowConfigVerticalLength(10);
        FHBox46.setBoxShadowConfigBlurRadius(5);
        FHBox46.setBoxShadowConfigSpreadRadius(0);
        FHBox46.setBoxShadowConfigShadowColor("clBlack");
        FHBox46.setBoxShadowConfigOpacity(75);
        FHBox46.setVAlign("tvTop");
        FHBox45.addChildren(FHBox46);
        FHBox46.applyProperties();
    }

    public TFVBox FVBox14 = new TFVBox();

    private void init_FVBox14() {
        FVBox14.setName("FVBox14");
        FVBox14.setLeft(4);
        FVBox14.setTop(0);
        FVBox14.setWidth(601);
        FVBox14.setHeight(150);
        FVBox14.setBorderStyle("stNone");
        FVBox14.setPaddingTop(0);
        FVBox14.setPaddingLeft(0);
        FVBox14.setPaddingRight(0);
        FVBox14.setPaddingBottom(0);
        FVBox14.setMarginTop(0);
        FVBox14.setMarginLeft(0);
        FVBox14.setMarginRight(0);
        FVBox14.setMarginBottom(0);
        FVBox14.setSpacing(1);
        FVBox14.setFlexVflex("ftTrue");
        FVBox14.setFlexHflex("ftTrue");
        FVBox14.setScrollable(false);
        FVBox14.setBoxShadowConfigHorizontalLength(10);
        FVBox14.setBoxShadowConfigVerticalLength(10);
        FVBox14.setBoxShadowConfigBlurRadius(5);
        FVBox14.setBoxShadowConfigSpreadRadius(0);
        FVBox14.setBoxShadowConfigShadowColor("clBlack");
        FVBox14.setBoxShadowConfigOpacity(75);
        FHBox45.addChildren(FVBox14);
        FVBox14.applyProperties();
    }

    public TFGrid grdBoletos = new TFGrid();

    private void init_grdBoletos() {
        grdBoletos.setName("grdBoletos");
        grdBoletos.setLeft(0);
        grdBoletos.setTop(0);
        grdBoletos.setWidth(595);
        grdBoletos.setHeight(118);
        grdBoletos.setTable(tbLeadsPgtoVendaInfBoleto);
        grdBoletos.setFlexVflex("ftTrue");
        grdBoletos.setFlexHflex("ftTrue");
        grdBoletos.setPagingEnabled(false);
        grdBoletos.setFrozenColumns(0);
        grdBoletos.setShowFooter(false);
        grdBoletos.setShowHeader(true);
        grdBoletos.setMultiSelection(false);
        grdBoletos.setGroupingEnabled(false);
        grdBoletos.setGroupingExpanded(false);
        grdBoletos.setGroupingShowFooter(false);
        grdBoletos.setCrosstabEnabled(false);
        grdBoletos.setCrosstabGroupType("cgtConcat");
        grdBoletos.setEditionEnabled(false);
        grdBoletos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("PARCELA");
        item0.setTitleCaption("Parcela");
        item0.setWidth(50);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdBoletos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("VALOR");
        item1.setTitleCaption("Valor");
        item1.setWidth(100);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taRight");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        TFMaskExpression item2 = new TFMaskExpression();
        item2.setExpression("*");
        item2.setEvalType("etExpression");
        item2.setMask(",##0.00");
        item2.setPadLength(0);
        item2.setPadDirection("pdNone");
        item2.setMaskType("mtDecimal");
        item1.getMasks().add(item2);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdBoletos.getColumns().add(item1);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("VENCIMENTO");
        item3.setTitleCaption("Vencimento");
        item3.setWidth(100);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taCenter");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("dd/MM/yyyy");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDateTime");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdBoletos.getColumns().add(item3);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("NOSSO_NUMERO");
        item5.setTitleCaption("Nosso N\u00FAmero");
        item5.setWidth(235);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(true);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdBoletos.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setWidth(5);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdBoletos.getColumns().add(item6);
        FVBox14.addChildren(grdBoletos);
        grdBoletos.applyProperties();
    }

    public TFHBox FHBox47 = new TFHBox();

    private void init_FHBox47() {
        FHBox47.setName("FHBox47");
        FHBox47.setLeft(605);
        FHBox47.setTop(0);
        FHBox47.setWidth(4);
        FHBox47.setHeight(12);
        FHBox47.setBorderStyle("stNone");
        FHBox47.setPaddingTop(0);
        FHBox47.setPaddingLeft(0);
        FHBox47.setPaddingRight(0);
        FHBox47.setPaddingBottom(0);
        FHBox47.setVisible(false);
        FHBox47.setMarginTop(0);
        FHBox47.setMarginLeft(0);
        FHBox47.setMarginRight(0);
        FHBox47.setMarginBottom(0);
        FHBox47.setSpacing(1);
        FHBox47.setFlexVflex("ftFalse");
        FHBox47.setFlexHflex("ftFalse");
        FHBox47.setScrollable(false);
        FHBox47.setBoxShadowConfigHorizontalLength(10);
        FHBox47.setBoxShadowConfigVerticalLength(10);
        FHBox47.setBoxShadowConfigBlurRadius(5);
        FHBox47.setBoxShadowConfigSpreadRadius(0);
        FHBox47.setBoxShadowConfigShadowColor("clBlack");
        FHBox47.setBoxShadowConfigOpacity(75);
        FHBox47.setVAlign("tvTop");
        FHBox45.addChildren(FHBox47);
        FHBox47.applyProperties();
    }

    public TFHBox FHBox49 = new TFHBox();

    private void init_FHBox49() {
        FHBox49.setName("FHBox49");
        FHBox49.setLeft(0);
        FHBox49.setTop(194);
        FHBox49.setWidth(33);
        FHBox49.setHeight(1);
        FHBox49.setBorderStyle("stNone");
        FHBox49.setColor("clSilver");
        FHBox49.setPaddingTop(0);
        FHBox49.setPaddingLeft(0);
        FHBox49.setPaddingRight(0);
        FHBox49.setPaddingBottom(0);
        FHBox49.setMarginTop(0);
        FHBox49.setMarginLeft(0);
        FHBox49.setMarginRight(0);
        FHBox49.setMarginBottom(0);
        FHBox49.setSpacing(1);
        FHBox49.setFlexVflex("ftFalse");
        FHBox49.setFlexHflex("ftTrue");
        FHBox49.setScrollable(false);
        FHBox49.setBoxShadowConfigHorizontalLength(10);
        FHBox49.setBoxShadowConfigVerticalLength(10);
        FHBox49.setBoxShadowConfigBlurRadius(5);
        FHBox49.setBoxShadowConfigSpreadRadius(0);
        FHBox49.setBoxShadowConfigShadowColor("clBlack");
        FHBox49.setBoxShadowConfigOpacity(75);
        FHBox49.setVAlign("tvTop");
        vBoxBoletoBancario.addChildren(FHBox49);
        FHBox49.applyProperties();
    }

    public TFHBox FHBox170004 = new TFHBox();

    private void init_FHBox170004() {
        FHBox170004.setName("FHBox170004");
        FHBox170004.setLeft(0);
        FHBox170004.setTop(196);
        FHBox170004.setWidth(774);
        FHBox170004.setHeight(34);
        FHBox170004.setBorderStyle("stSingleLine");
        FHBox170004.setPaddingTop(0);
        FHBox170004.setPaddingLeft(0);
        FHBox170004.setPaddingRight(0);
        FHBox170004.setPaddingBottom(0);
        FHBox170004.setMarginTop(0);
        FHBox170004.setMarginLeft(0);
        FHBox170004.setMarginRight(0);
        FHBox170004.setMarginBottom(0);
        FHBox170004.setSpacing(1);
        FHBox170004.setFlexVflex("ftFalse");
        FHBox170004.setFlexHflex("ftTrue");
        FHBox170004.setScrollable(false);
        FHBox170004.setBoxShadowConfigHorizontalLength(10);
        FHBox170004.setBoxShadowConfigVerticalLength(10);
        FHBox170004.setBoxShadowConfigBlurRadius(5);
        FHBox170004.setBoxShadowConfigSpreadRadius(0);
        FHBox170004.setBoxShadowConfigShadowColor("clBlack");
        FHBox170004.setBoxShadowConfigOpacity(75);
        FHBox170004.setVAlign("tvTop");
        vBoxBoletoBancario.addChildren(FHBox170004);
        FHBox170004.applyProperties();
    }

    public TFPanel FPanel170004 = new TFPanel();

    private void init_FPanel170004() {
        FPanel170004.setName("FPanel170004");
        FPanel170004.setLeft(0);
        FPanel170004.setTop(0);
        FPanel170004.setWidth(24);
        FPanel170004.setHeight(12);
        FPanel170004.setBorderStyle("stNone");
        FPanel170004.setPaddingTop(0);
        FPanel170004.setPaddingLeft(0);
        FPanel170004.setPaddingRight(0);
        FPanel170004.setPaddingBottom(0);
        FPanel170004.setFlexVflex("ftFalse");
        FPanel170004.setFlexHflex("ftFalse");
        FPanel170004.setMarginTop(0);
        FPanel170004.setMarginLeft(0);
        FPanel170004.setMarginRight(0);
        FPanel170004.setMarginBottom(0);
        FPanel170004.setBoxShadowConfigHorizontalLength(10);
        FPanel170004.setBoxShadowConfigVerticalLength(10);
        FPanel170004.setBoxShadowConfigBlurRadius(5);
        FPanel170004.setBoxShadowConfigSpreadRadius(0);
        FPanel170004.setBoxShadowConfigShadowColor("clBlack");
        FPanel170004.setBoxShadowConfigOpacity(75);
        FHBox170004.addChildren(FPanel170004);
        FPanel170004.applyProperties();
    }

    public TFButton btnEditDetalheBoleto = new TFButton();

    private void init_btnEditDetalheBoleto() {
        btnEditDetalheBoleto.setName("btnEditDetalheBoleto");
        btnEditDetalheBoleto.setLeft(24);
        btnEditDetalheBoleto.setTop(0);
        btnEditDetalheBoleto.setWidth(92);
        btnEditDetalheBoleto.setHeight(28);
        btnEditDetalheBoleto.setCaption("Alterar");
        btnEditDetalheBoleto.setFontColor("clWindowText");
        btnEditDetalheBoleto.setFontSize(-11);
        btnEditDetalheBoleto.setFontName("Tahoma");
        btnEditDetalheBoleto.setFontStyle("[]");
        btnEditDetalheBoleto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnEditDetalheBoletoClick(event);
            processarFlow("FrmPgtoVendaInf", "btnEditDetalheBoleto", "OnClick");
        });
        btnEditDetalheBoleto.setImageId(0);
        btnEditDetalheBoleto.setColor("clBtnFace");
        btnEditDetalheBoleto.setAccess(false);
        btnEditDetalheBoleto.setIconClass("edit");
        btnEditDetalheBoleto.setIconReverseDirection(false);
        FHBox170004.addChildren(btnEditDetalheBoleto);
        btnEditDetalheBoleto.applyProperties();
    }

    public TFPanel FPanel1 = new TFPanel();

    private void init_FPanel1() {
        FPanel1.setName("FPanel1");
        FPanel1.setLeft(116);
        FPanel1.setTop(0);
        FPanel1.setWidth(12);
        FPanel1.setHeight(15);
        FPanel1.setBorderStyle("stNone");
        FPanel1.setPaddingTop(0);
        FPanel1.setPaddingLeft(0);
        FPanel1.setPaddingRight(0);
        FPanel1.setPaddingBottom(0);
        FPanel1.setFlexVflex("ftFalse");
        FPanel1.setFlexHflex("ftFalse");
        FPanel1.setMarginTop(0);
        FPanel1.setMarginLeft(0);
        FPanel1.setMarginRight(0);
        FPanel1.setMarginBottom(0);
        FPanel1.setBoxShadowConfigHorizontalLength(10);
        FPanel1.setBoxShadowConfigVerticalLength(10);
        FPanel1.setBoxShadowConfigBlurRadius(5);
        FPanel1.setBoxShadowConfigSpreadRadius(0);
        FPanel1.setBoxShadowConfigShadowColor("clBlack");
        FPanel1.setBoxShadowConfigOpacity(75);
        FHBox170004.addChildren(FPanel1);
        FPanel1.applyProperties();
    }

    public TFButton btnConfirmarBoleto = new TFButton();

    private void init_btnConfirmarBoleto() {
        btnConfirmarBoleto.setName("btnConfirmarBoleto");
        btnConfirmarBoleto.setLeft(128);
        btnConfirmarBoleto.setTop(0);
        btnConfirmarBoleto.setWidth(92);
        btnConfirmarBoleto.setHeight(28);
        btnConfirmarBoleto.setCaption("Confirmar");
        btnConfirmarBoleto.setEnabled(false);
        btnConfirmarBoleto.setFontColor("clWindowText");
        btnConfirmarBoleto.setFontSize(-11);
        btnConfirmarBoleto.setFontName("Tahoma");
        btnConfirmarBoleto.setFontStyle("[]");
        btnConfirmarBoleto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarBoletoClick(event);
            processarFlow("FrmPgtoVendaInf", "btnConfirmarBoleto", "OnClick");
        });
        btnConfirmarBoleto.setImageId(220011);
        btnConfirmarBoleto.setColor("clBtnFace");
        btnConfirmarBoleto.setAccess(false);
        btnConfirmarBoleto.setIconReverseDirection(false);
        FHBox170004.addChildren(btnConfirmarBoleto);
        btnConfirmarBoleto.applyProperties();
    }

    public TFPanel FPanel2 = new TFPanel();

    private void init_FPanel2() {
        FPanel2.setName("FPanel2");
        FPanel2.setLeft(220);
        FPanel2.setTop(0);
        FPanel2.setWidth(12);
        FPanel2.setHeight(15);
        FPanel2.setBorderStyle("stNone");
        FPanel2.setPaddingTop(0);
        FPanel2.setPaddingLeft(0);
        FPanel2.setPaddingRight(0);
        FPanel2.setPaddingBottom(0);
        FPanel2.setFlexVflex("ftFalse");
        FPanel2.setFlexHflex("ftFalse");
        FPanel2.setMarginTop(0);
        FPanel2.setMarginLeft(0);
        FPanel2.setMarginRight(0);
        FPanel2.setMarginBottom(0);
        FPanel2.setBoxShadowConfigHorizontalLength(10);
        FPanel2.setBoxShadowConfigVerticalLength(10);
        FPanel2.setBoxShadowConfigBlurRadius(5);
        FPanel2.setBoxShadowConfigSpreadRadius(0);
        FPanel2.setBoxShadowConfigShadowColor("clBlack");
        FPanel2.setBoxShadowConfigOpacity(75);
        FHBox170004.addChildren(FPanel2);
        FPanel2.applyProperties();
    }

    public TFButton btnCancelarBoleto = new TFButton();

    private void init_btnCancelarBoleto() {
        btnCancelarBoleto.setName("btnCancelarBoleto");
        btnCancelarBoleto.setLeft(232);
        btnCancelarBoleto.setTop(0);
        btnCancelarBoleto.setWidth(92);
        btnCancelarBoleto.setHeight(28);
        btnCancelarBoleto.setCaption("Cancelar");
        btnCancelarBoleto.setEnabled(false);
        btnCancelarBoleto.setFontColor("clWindowText");
        btnCancelarBoleto.setFontSize(-11);
        btnCancelarBoleto.setFontName("Tahoma");
        btnCancelarBoleto.setFontStyle("[]");
        btnCancelarBoleto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarBoletoClick(event);
            processarFlow("FrmPgtoVendaInf", "btnCancelarBoleto", "OnClick");
        });
        btnCancelarBoleto.setImageId(220020);
        btnCancelarBoleto.setColor("clBtnFace");
        btnCancelarBoleto.setAccess(false);
        btnCancelarBoleto.setIconReverseDirection(false);
        FHBox170004.addChildren(btnCancelarBoleto);
        btnCancelarBoleto.applyProperties();
    }

    public TFHBox hBoxDetalheBoleto = new TFHBox();

    private void init_hBoxDetalheBoleto() {
        hBoxDetalheBoleto.setName("hBoxDetalheBoleto");
        hBoxDetalheBoleto.setLeft(0);
        hBoxDetalheBoleto.setTop(231);
        hBoxDetalheBoleto.setWidth(781);
        hBoxDetalheBoleto.setHeight(50);
        hBoxDetalheBoleto.setBorderStyle("stNone");
        hBoxDetalheBoleto.setPaddingTop(0);
        hBoxDetalheBoleto.setPaddingLeft(0);
        hBoxDetalheBoleto.setPaddingRight(0);
        hBoxDetalheBoleto.setPaddingBottom(0);
        hBoxDetalheBoleto.setMarginTop(0);
        hBoxDetalheBoleto.setMarginLeft(0);
        hBoxDetalheBoleto.setMarginRight(0);
        hBoxDetalheBoleto.setMarginBottom(0);
        hBoxDetalheBoleto.setSpacing(1);
        hBoxDetalheBoleto.setFlexVflex("ftFalse");
        hBoxDetalheBoleto.setFlexHflex("ftTrue");
        hBoxDetalheBoleto.setScrollable(false);
        hBoxDetalheBoleto.setBoxShadowConfigHorizontalLength(10);
        hBoxDetalheBoleto.setBoxShadowConfigVerticalLength(10);
        hBoxDetalheBoleto.setBoxShadowConfigBlurRadius(5);
        hBoxDetalheBoleto.setBoxShadowConfigSpreadRadius(0);
        hBoxDetalheBoleto.setBoxShadowConfigShadowColor("clBlack");
        hBoxDetalheBoleto.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.setVAlign("tvTop");
        vBoxBoletoBancario.addChildren(hBoxDetalheBoleto);
        hBoxDetalheBoleto.applyProperties();
    }

    public TFVBox FVBox19 = new TFVBox();

    private void init_FVBox19() {
        FVBox19.setName("FVBox19");
        FVBox19.setLeft(0);
        FVBox19.setTop(0);
        FVBox19.setWidth(24);
        FVBox19.setHeight(41);
        FVBox19.setBorderStyle("stNone");
        FVBox19.setPaddingTop(0);
        FVBox19.setPaddingLeft(0);
        FVBox19.setPaddingRight(0);
        FVBox19.setPaddingBottom(0);
        FVBox19.setMarginTop(0);
        FVBox19.setMarginLeft(0);
        FVBox19.setMarginRight(0);
        FVBox19.setMarginBottom(0);
        FVBox19.setSpacing(1);
        FVBox19.setFlexVflex("ftFalse");
        FVBox19.setFlexHflex("ftFalse");
        FVBox19.setScrollable(false);
        FVBox19.setBoxShadowConfigHorizontalLength(10);
        FVBox19.setBoxShadowConfigVerticalLength(10);
        FVBox19.setBoxShadowConfigBlurRadius(5);
        FVBox19.setBoxShadowConfigSpreadRadius(0);
        FVBox19.setBoxShadowConfigShadowColor("clBlack");
        FVBox19.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox19);
        FVBox19.applyProperties();
    }

    public TFVBox FVBox20 = new TFVBox();

    private void init_FVBox20() {
        FVBox20.setName("FVBox20");
        FVBox20.setLeft(24);
        FVBox20.setTop(0);
        FVBox20.setWidth(43);
        FVBox20.setHeight(45);
        FVBox20.setBorderStyle("stNone");
        FVBox20.setPaddingTop(0);
        FVBox20.setPaddingLeft(0);
        FVBox20.setPaddingRight(0);
        FVBox20.setPaddingBottom(0);
        FVBox20.setMarginTop(0);
        FVBox20.setMarginLeft(0);
        FVBox20.setMarginRight(0);
        FVBox20.setMarginBottom(0);
        FVBox20.setSpacing(1);
        FVBox20.setFlexVflex("ftFalse");
        FVBox20.setFlexHflex("ftFalse");
        FVBox20.setScrollable(false);
        FVBox20.setBoxShadowConfigHorizontalLength(10);
        FVBox20.setBoxShadowConfigVerticalLength(10);
        FVBox20.setBoxShadowConfigBlurRadius(5);
        FVBox20.setBoxShadowConfigSpreadRadius(0);
        FVBox20.setBoxShadowConfigShadowColor("clBlack");
        FVBox20.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox20);
        FVBox20.applyProperties();
    }

    public TFLabel lblBoletoParcela = new TFLabel();

    private void init_lblBoletoParcela() {
        lblBoletoParcela.setName("lblBoletoParcela");
        lblBoletoParcela.setLeft(0);
        lblBoletoParcela.setTop(0);
        lblBoletoParcela.setWidth(35);
        lblBoletoParcela.setHeight(13);
        lblBoletoParcela.setAlign("alLeft");
        lblBoletoParcela.setCaption("Parcela");
        lblBoletoParcela.setFontColor("clWindowText");
        lblBoletoParcela.setFontSize(-11);
        lblBoletoParcela.setFontName("Tahoma");
        lblBoletoParcela.setFontStyle("[]");
        lblBoletoParcela.setVerticalAlignment("taVerticalCenter");
        lblBoletoParcela.setWordBreak(false);
        FVBox20.addChildren(lblBoletoParcela);
        lblBoletoParcela.applyProperties();
    }

    public TFInteger edtBoletoParc = new TFInteger();

    private void init_edtBoletoParc() {
        edtBoletoParc.setName("edtBoletoParc");
        edtBoletoParc.setLeft(0);
        edtBoletoParc.setTop(14);
        edtBoletoParc.setWidth(22);
        edtBoletoParc.setHeight(24);
        edtBoletoParc.setFlex(true);
        edtBoletoParc.setRequired(false);
        edtBoletoParc.setConstraintCheckWhen("cwImmediate");
        edtBoletoParc.setConstraintCheckType("ctExpression");
        edtBoletoParc.setConstraintFocusOnError(false);
        edtBoletoParc.setConstraintEnableUI(true);
        edtBoletoParc.setConstraintEnabled(false);
        edtBoletoParc.setConstraintFormCheck(true);
        edtBoletoParc.setMaxlength(0);
        edtBoletoParc.setEnabled(false);
        edtBoletoParc.setFontColor("clWindowText");
        edtBoletoParc.setFontSize(-13);
        edtBoletoParc.setFontName("Tahoma");
        edtBoletoParc.setFontStyle("[]");
        edtBoletoParc.setAlignment("taRightJustify");
        FVBox20.addChildren(edtBoletoParc);
        edtBoletoParc.applyProperties();
        addValidatable(edtBoletoParc);
    }

    public TFVBox FVBox21 = new TFVBox();

    private void init_FVBox21() {
        FVBox21.setName("FVBox21");
        FVBox21.setLeft(67);
        FVBox21.setTop(0);
        FVBox21.setWidth(8);
        FVBox21.setHeight(41);
        FVBox21.setBorderStyle("stNone");
        FVBox21.setPaddingTop(0);
        FVBox21.setPaddingLeft(0);
        FVBox21.setPaddingRight(0);
        FVBox21.setPaddingBottom(0);
        FVBox21.setMarginTop(0);
        FVBox21.setMarginLeft(0);
        FVBox21.setMarginRight(0);
        FVBox21.setMarginBottom(0);
        FVBox21.setSpacing(1);
        FVBox21.setFlexVflex("ftFalse");
        FVBox21.setFlexHflex("ftFalse");
        FVBox21.setScrollable(false);
        FVBox21.setBoxShadowConfigHorizontalLength(10);
        FVBox21.setBoxShadowConfigVerticalLength(10);
        FVBox21.setBoxShadowConfigBlurRadius(5);
        FVBox21.setBoxShadowConfigSpreadRadius(0);
        FVBox21.setBoxShadowConfigShadowColor("clBlack");
        FVBox21.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox21);
        FVBox21.applyProperties();
    }

    public TFVBox FVBox22 = new TFVBox();

    private void init_FVBox22() {
        FVBox22.setName("FVBox22");
        FVBox22.setLeft(75);
        FVBox22.setTop(0);
        FVBox22.setWidth(115);
        FVBox22.setHeight(45);
        FVBox22.setBorderStyle("stNone");
        FVBox22.setPaddingTop(0);
        FVBox22.setPaddingLeft(0);
        FVBox22.setPaddingRight(0);
        FVBox22.setPaddingBottom(0);
        FVBox22.setMarginTop(0);
        FVBox22.setMarginLeft(0);
        FVBox22.setMarginRight(0);
        FVBox22.setMarginBottom(0);
        FVBox22.setSpacing(1);
        FVBox22.setFlexVflex("ftFalse");
        FVBox22.setFlexHflex("ftFalse");
        FVBox22.setScrollable(false);
        FVBox22.setBoxShadowConfigHorizontalLength(10);
        FVBox22.setBoxShadowConfigVerticalLength(10);
        FVBox22.setBoxShadowConfigBlurRadius(5);
        FVBox22.setBoxShadowConfigSpreadRadius(0);
        FVBox22.setBoxShadowConfigShadowColor("clBlack");
        FVBox22.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox22);
        FVBox22.applyProperties();
    }

    public TFLabel lblBoletoValor = new TFLabel();

    private void init_lblBoletoValor() {
        lblBoletoValor.setName("lblBoletoValor");
        lblBoletoValor.setLeft(0);
        lblBoletoValor.setTop(0);
        lblBoletoValor.setWidth(24);
        lblBoletoValor.setHeight(13);
        lblBoletoValor.setAlign("alLeft");
        lblBoletoValor.setCaption("Valor");
        lblBoletoValor.setFontColor("clWindowText");
        lblBoletoValor.setFontSize(-11);
        lblBoletoValor.setFontName("Tahoma");
        lblBoletoValor.setFontStyle("[]");
        lblBoletoValor.setVerticalAlignment("taVerticalCenter");
        lblBoletoValor.setWordBreak(false);
        FVBox22.addChildren(lblBoletoValor);
        lblBoletoValor.applyProperties();
    }

    public TFDecimal edtBoletoValor = new TFDecimal();

    private void init_edtBoletoValor() {
        edtBoletoValor.setName("edtBoletoValor");
        edtBoletoValor.setLeft(0);
        edtBoletoValor.setTop(14);
        edtBoletoValor.setWidth(108);
        edtBoletoValor.setHeight(24);
        edtBoletoValor.setFlex(true);
        edtBoletoValor.setRequired(false);
        edtBoletoValor.setConstraintCheckWhen("cwImmediate");
        edtBoletoValor.setConstraintCheckType("ctExpression");
        edtBoletoValor.setConstraintFocusOnError(false);
        edtBoletoValor.setConstraintEnableUI(true);
        edtBoletoValor.setConstraintEnabled(false);
        edtBoletoValor.setConstraintFormCheck(true);
        edtBoletoValor.setMaxlength(0);
        edtBoletoValor.setPrecision(0);
        edtBoletoValor.setEnabled(false);
        edtBoletoValor.setFontColor("clWindowText");
        edtBoletoValor.setFontSize(-13);
        edtBoletoValor.setFontName("Tahoma");
        edtBoletoValor.setFontStyle("[]");
        edtBoletoValor.setAlignment("taRightJustify");
        FVBox22.addChildren(edtBoletoValor);
        edtBoletoValor.applyProperties();
        addValidatable(edtBoletoValor);
    }

    public TFVBox FVBox23 = new TFVBox();

    private void init_FVBox23() {
        FVBox23.setName("FVBox23");
        FVBox23.setLeft(190);
        FVBox23.setTop(0);
        FVBox23.setWidth(8);
        FVBox23.setHeight(41);
        FVBox23.setBorderStyle("stNone");
        FVBox23.setPaddingTop(0);
        FVBox23.setPaddingLeft(0);
        FVBox23.setPaddingRight(0);
        FVBox23.setPaddingBottom(0);
        FVBox23.setMarginTop(0);
        FVBox23.setMarginLeft(0);
        FVBox23.setMarginRight(0);
        FVBox23.setMarginBottom(0);
        FVBox23.setSpacing(1);
        FVBox23.setFlexVflex("ftFalse");
        FVBox23.setFlexHflex("ftFalse");
        FVBox23.setScrollable(false);
        FVBox23.setBoxShadowConfigHorizontalLength(10);
        FVBox23.setBoxShadowConfigVerticalLength(10);
        FVBox23.setBoxShadowConfigBlurRadius(5);
        FVBox23.setBoxShadowConfigSpreadRadius(0);
        FVBox23.setBoxShadowConfigShadowColor("clBlack");
        FVBox23.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox23);
        FVBox23.applyProperties();
    }

    public TFVBox FVBox24 = new TFVBox();

    private void init_FVBox24() {
        FVBox24.setName("FVBox24");
        FVBox24.setLeft(198);
        FVBox24.setTop(0);
        FVBox24.setWidth(133);
        FVBox24.setHeight(45);
        FVBox24.setBorderStyle("stNone");
        FVBox24.setPaddingTop(0);
        FVBox24.setPaddingLeft(0);
        FVBox24.setPaddingRight(0);
        FVBox24.setPaddingBottom(0);
        FVBox24.setMarginTop(0);
        FVBox24.setMarginLeft(0);
        FVBox24.setMarginRight(0);
        FVBox24.setMarginBottom(0);
        FVBox24.setSpacing(1);
        FVBox24.setFlexVflex("ftFalse");
        FVBox24.setFlexHflex("ftFalse");
        FVBox24.setScrollable(false);
        FVBox24.setBoxShadowConfigHorizontalLength(10);
        FVBox24.setBoxShadowConfigVerticalLength(10);
        FVBox24.setBoxShadowConfigBlurRadius(5);
        FVBox24.setBoxShadowConfigSpreadRadius(0);
        FVBox24.setBoxShadowConfigShadowColor("clBlack");
        FVBox24.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox24);
        FVBox24.applyProperties();
    }

    public TFLabel lblBoletoVencimento = new TFLabel();

    private void init_lblBoletoVencimento() {
        lblBoletoVencimento.setName("lblBoletoVencimento");
        lblBoletoVencimento.setLeft(0);
        lblBoletoVencimento.setTop(0);
        lblBoletoVencimento.setWidth(55);
        lblBoletoVencimento.setHeight(13);
        lblBoletoVencimento.setAlign("alLeft");
        lblBoletoVencimento.setCaption("Vencimento");
        lblBoletoVencimento.setFontColor("clWindowText");
        lblBoletoVencimento.setFontSize(-11);
        lblBoletoVencimento.setFontName("Tahoma");
        lblBoletoVencimento.setFontStyle("[]");
        lblBoletoVencimento.setVerticalAlignment("taVerticalCenter");
        lblBoletoVencimento.setWordBreak(false);
        FVBox24.addChildren(lblBoletoVencimento);
        lblBoletoVencimento.applyProperties();
    }

    public TFDate edtBoletoVencimento = new TFDate();

    private void init_edtBoletoVencimento() {
        edtBoletoVencimento.setName("edtBoletoVencimento");
        edtBoletoVencimento.setLeft(0);
        edtBoletoVencimento.setTop(14);
        edtBoletoVencimento.setWidth(121);
        edtBoletoVencimento.setHeight(24);
        edtBoletoVencimento.setFlex(false);
        edtBoletoVencimento.setRequired(false);
        edtBoletoVencimento.setConstraintCheckWhen("cwImmediate");
        edtBoletoVencimento.setConstraintCheckType("ctExpression");
        edtBoletoVencimento.setConstraintFocusOnError(false);
        edtBoletoVencimento.setConstraintEnableUI(true);
        edtBoletoVencimento.setConstraintEnabled(false);
        edtBoletoVencimento.setConstraintFormCheck(true);
        edtBoletoVencimento.setFormat("dd/MM/yyyy");
        edtBoletoVencimento.setShowCheckBox(false);
        edtBoletoVencimento.setEnabled(false);
        FVBox24.addChildren(edtBoletoVencimento);
        edtBoletoVencimento.applyProperties();
        addValidatable(edtBoletoVencimento);
    }

    public TFVBox FVBox25 = new TFVBox();

    private void init_FVBox25() {
        FVBox25.setName("FVBox25");
        FVBox25.setLeft(331);
        FVBox25.setTop(0);
        FVBox25.setWidth(8);
        FVBox25.setHeight(41);
        FVBox25.setBorderStyle("stNone");
        FVBox25.setPaddingTop(0);
        FVBox25.setPaddingLeft(0);
        FVBox25.setPaddingRight(0);
        FVBox25.setPaddingBottom(0);
        FVBox25.setMarginTop(0);
        FVBox25.setMarginLeft(0);
        FVBox25.setMarginRight(0);
        FVBox25.setMarginBottom(0);
        FVBox25.setSpacing(1);
        FVBox25.setFlexVflex("ftFalse");
        FVBox25.setFlexHflex("ftFalse");
        FVBox25.setScrollable(false);
        FVBox25.setBoxShadowConfigHorizontalLength(10);
        FVBox25.setBoxShadowConfigVerticalLength(10);
        FVBox25.setBoxShadowConfigBlurRadius(5);
        FVBox25.setBoxShadowConfigSpreadRadius(0);
        FVBox25.setBoxShadowConfigShadowColor("clBlack");
        FVBox25.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox25);
        FVBox25.applyProperties();
    }

    public TFVBox FVBox26 = new TFVBox();

    private void init_FVBox26() {
        FVBox26.setName("FVBox26");
        FVBox26.setLeft(339);
        FVBox26.setTop(0);
        FVBox26.setWidth(270);
        FVBox26.setHeight(45);
        FVBox26.setBorderStyle("stNone");
        FVBox26.setPaddingTop(0);
        FVBox26.setPaddingLeft(0);
        FVBox26.setPaddingRight(0);
        FVBox26.setPaddingBottom(0);
        FVBox26.setMarginTop(0);
        FVBox26.setMarginLeft(0);
        FVBox26.setMarginRight(0);
        FVBox26.setMarginBottom(0);
        FVBox26.setSpacing(1);
        FVBox26.setFlexVflex("ftFalse");
        FVBox26.setFlexHflex("ftTrue");
        FVBox26.setScrollable(false);
        FVBox26.setBoxShadowConfigHorizontalLength(10);
        FVBox26.setBoxShadowConfigVerticalLength(10);
        FVBox26.setBoxShadowConfigBlurRadius(5);
        FVBox26.setBoxShadowConfigSpreadRadius(0);
        FVBox26.setBoxShadowConfigShadowColor("clBlack");
        FVBox26.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox26);
        FVBox26.applyProperties();
    }

    public TFLabel lblBoletoNossoNumero = new TFLabel();

    private void init_lblBoletoNossoNumero() {
        lblBoletoNossoNumero.setName("lblBoletoNossoNumero");
        lblBoletoNossoNumero.setLeft(0);
        lblBoletoNossoNumero.setTop(0);
        lblBoletoNossoNumero.setWidth(69);
        lblBoletoNossoNumero.setHeight(13);
        lblBoletoNossoNumero.setAlign("alLeft");
        lblBoletoNossoNumero.setCaption("Nosso N\u00FAmero");
        lblBoletoNossoNumero.setFontColor("clWindowText");
        lblBoletoNossoNumero.setFontSize(-11);
        lblBoletoNossoNumero.setFontName("Tahoma");
        lblBoletoNossoNumero.setFontStyle("[]");
        lblBoletoNossoNumero.setVerticalAlignment("taVerticalCenter");
        lblBoletoNossoNumero.setWordBreak(false);
        FVBox26.addChildren(lblBoletoNossoNumero);
        lblBoletoNossoNumero.applyProperties();
    }

    public TFString edtBoletoNossoNumero = new TFString();

    private void init_edtBoletoNossoNumero() {
        edtBoletoNossoNumero.setName("edtBoletoNossoNumero");
        edtBoletoNossoNumero.setLeft(0);
        edtBoletoNossoNumero.setTop(14);
        edtBoletoNossoNumero.setWidth(260);
        edtBoletoNossoNumero.setHeight(24);
        edtBoletoNossoNumero.setFlex(true);
        edtBoletoNossoNumero.setRequired(false);
        edtBoletoNossoNumero.setConstraintCheckWhen("cwImmediate");
        edtBoletoNossoNumero.setConstraintCheckType("ctExpression");
        edtBoletoNossoNumero.setConstraintFocusOnError(false);
        edtBoletoNossoNumero.setConstraintEnableUI(true);
        edtBoletoNossoNumero.setConstraintEnabled(false);
        edtBoletoNossoNumero.setConstraintFormCheck(true);
        edtBoletoNossoNumero.setCharCase("ccNormal");
        edtBoletoNossoNumero.setPwd(false);
        edtBoletoNossoNumero.setMaxlength(30);
        edtBoletoNossoNumero.setEnabled(false);
        edtBoletoNossoNumero.setFontColor("clWindowText");
        edtBoletoNossoNumero.setFontSize(-13);
        edtBoletoNossoNumero.setFontName("Tahoma");
        edtBoletoNossoNumero.setFontStyle("[]");
        edtBoletoNossoNumero.setSaveLiteralCharacter(false);
        edtBoletoNossoNumero.applyProperties();
        FVBox26.addChildren(edtBoletoNossoNumero);
        addValidatable(edtBoletoNossoNumero);
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(609);
        FVBox6.setTop(0);
        FVBox6.setWidth(8);
        FVBox6.setHeight(41);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftFalse");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        hBoxDetalheBoleto.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFHBox FHBox53 = new TFHBox();

    private void init_FHBox53() {
        FHBox53.setName("FHBox53");
        FHBox53.setLeft(0);
        FHBox53.setTop(412);
        FHBox53.setWidth(50);
        FHBox53.setHeight(3);
        FHBox53.setBorderStyle("stNone");
        FHBox53.setPaddingTop(0);
        FHBox53.setPaddingLeft(0);
        FHBox53.setPaddingRight(0);
        FHBox53.setPaddingBottom(0);
        FHBox53.setMarginTop(0);
        FHBox53.setMarginLeft(0);
        FHBox53.setMarginRight(0);
        FHBox53.setMarginBottom(0);
        FHBox53.setSpacing(1);
        FHBox53.setFlexVflex("ftFalse");
        FHBox53.setFlexHflex("ftFalse");
        FHBox53.setScrollable(false);
        FHBox53.setBoxShadowConfigHorizontalLength(10);
        FHBox53.setBoxShadowConfigVerticalLength(10);
        FHBox53.setBoxShadowConfigBlurRadius(5);
        FHBox53.setBoxShadowConfigSpreadRadius(0);
        FHBox53.setBoxShadowConfigShadowColor("clBlack");
        FHBox53.setBoxShadowConfigOpacity(75);
        FHBox53.setVAlign("tvTop");
        FrmPgtoVendaInf.addChildren(FHBox53);
        FHBox53.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void frmShow(final Event<Object> event);

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnConfirmarClick(final Event<Object> event) {
        if (btnConfirmar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConfirmar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtCartaoQtdParcExit(final Event<Object> event);

    public void btnIncluirCartoesClick(final Event<Object> event) {
        if (btnIncluirCartoes.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirCartoes");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirCartoesClick(final Event<Object> event) {
        if (btnExcluirCartoes.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirCartoes");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarCartaoClick(final Event<Object> event) {
        if (btnAlterarCartao.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarCartao");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCartaoSalvarAlteracaoClick(final Event<Object> event) {
        if (btnCartaoSalvarAlteracao.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCartaoSalvarAlteracao");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCartaoCancelarAlteracaoClick(final Event<Object> event) {
        if (btnCartaoCancelarAlteracao.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCartaoCancelarAlteracao");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnIncluirChequesClick(final Event<Object> event) {
        if (btnIncluirCheques.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirCheques");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirChequesClick(final Event<Object> event) {
        if (btnExcluirCheques.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirCheques");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtChequeNumeroExit(final Event<Object> event);

    public void btnChequeAlterarClick(final Event<Object> event) {
        if (btnChequeAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnChequeAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnChequeExcluirClick(final Event<Object> event) {
        if (btnChequeExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnChequeExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnChequeConfirmarClick(final Event<Object> event) {
        if (btnChequeConfirmar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnChequeConfirmar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnChequeCancelarClick(final Event<Object> event) {
        if (btnChequeCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnChequeCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnEditDetalheBoletoClick(final Event<Object> event) {
        if (btnEditDetalheBoleto.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnEditDetalheBoleto");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnConfirmarBoletoClick(final Event<Object> event) {
        if (btnConfirmarBoleto.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConfirmarBoleto");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarBoletoClick(final Event<Object> event) {
        if (btnCancelarBoleto.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarBoleto");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void tbLeadsPgtoVendaInfCartaoAfterScroll(final Event<Object> event);

    public abstract void tbLeadsPgtoVendaInfBoletoAfterScroll(final Event<Object> event);

    public abstract void tbLeadsPgtoVendaInfChequeAfterScroll(final Event<Object> event);

}