package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmTimeMembros extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.TimeMembrosRNA rn = null;

    public FrmTimeMembros() {
        try {
            rn = (freedom.bytecode.rn.TimeMembrosRNA) getRN(freedom.bytecode.rn.wizard.TimeMembrosRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbTime();
        init_tbTimeMembro();
        init_tbEmpresas();
        init_tbEmpresasFuncoes();
        init_tbTimeAgentesDisponiveis();
        init_tbTimeTemplate();
        init_tbTimeMembroTemplateDisponivel();
        init_tbTimeMembroTemplateCruzado();
        init_filtroAvancado();
        init_popMenuPrincipal();
        init_menuItemAbreTabelaAux();
        init_menuHabilitaNavegacao();
        init_menuSelecaoMultipla();
        init_FMenuItem1();
        init_menuItemConfgGrid();
        init_menuItemExportPdf();
        init_menuItemExportExcel();
        init_menuItemHelp();
        init_gridConfig();
        init_FHBox6();
        init_FHBox1();
        init_btnConsultar();
        init_btnFiltroAvancado();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnSalvarContinuar();
        init_btnCancelar();
        init_FHBox3();
        init_btnAnterior();
        init_btnProximo();
        init_FHBox8();
        init_FHBox5();
        init_btnAceitar();
        init_btnMais();
        init_FHBox4();
        init_lblMensagem();
        init_pgPrincipal();
        init_tabListagem();
        init_FVBox1();
        init_grpBoxFiltro();
        init_gpFiltroPrincipal();
        init_gridPrincipal();
        init_tabCadastro();
        init_FVBox2();
        init_grpBoxPrincipal();
        init_FGridPanel2();
        init_lbMaxDescServ70001();
        init_lbDescricao70001();
        init_lbIdTime70001();
        init_lbGrupo70001();
        init_lbAtivo70001();
        init_edIdTime70001();
        init_edDescricao70001();
        init_edGrupo70001();
        init_edAtivo70001();
        init_edMaxDescServ70001();
        init_tabSheetMembros();
        init_vBoxTabShhetMembros();
        init_gbDetalhe43001();
        init_gridPanelDetailEmpty43001();
        init_FHBox2();
        init_FVBox3();
        init_FLabel1();
        init_cbbEmpresa();
        init_FVBox4();
        init_FLabel2();
        init_cbbEmpresasFuncoes();
        init_DualListMembros();
        init_tabHorarios();
        init_FVBox5();
        init_grpTemplate();
        init_vboxModeloGrupoxModelo();
        init_FHBox7();
        init_cmbTemplate();
        init_FVBox6();
        init_btnAlterarTemplate();
        init_hboxModeloGridModeloECruzamento();
        init_vboxModelogridModelo();
        init_vboxModelolblSelecioneModelo();
        init_labelModeloSelecioneModelo();
        init_gridMembrosTemplateDisponivel();
        init_vBoxModeloAgrupaBotoes();
        init_hboxModeloCrtl1();
        init_btnHorariosAdicionarTemplate();
        init_hboxModeloCrtl3();
        init_btnHorariosRemoverTemplate();
        init_hboxModeloCrtl2();
        init_vboxModeloGridCruzamento();
        init_vboxModeloCruzamentos();
        init_FLabel14();
        init_gridMembrosTemplateCruzado();
        init_sc();
        init_FrmTimeMembros();
    }

    public CRM_TIME tbTime;

    private void init_tbTime() {
        tbTime = rn.tbTime;
        tbTime.setName("tbTime");
        tbTime.setMaxRowCount(0);
        tbTime.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbTimeAfterScroll(event);
            processarFlow("FrmTimeMembros", "tbTime", "OnAfterScroll");
        });
        tbTime.setWKey("7000194;70001");
        tbTime.setRatioBatchSize(20);
        getTables().put(tbTime, "tbTime");
        tbTime.applyProperties();
    }

    public CRM_TIME_MEMBRO tbTimeMembro;

    private void init_tbTimeMembro() {
        tbTimeMembro = rn.tbTimeMembro;
        tbTimeMembro.setName("tbTimeMembro");
        tbTimeMembro.setMasterFields("ID_TIME");
        tbTimeMembro.setDetailFilters("ID_TIME");
        tbTimeMembro.setMaxRowCount(0);
        tbTimeMembro.setMasterTable(tbTime);
        tbTimeMembro.addEventListener("onBeforePost", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbTimeMembroBeforePost(event);
            processarFlow("FrmTimeMembros", "tbTimeMembro", "OnBeforePost");
        });
        tbTimeMembro.setWKey("7000194;43001");
        tbTimeMembro.setRatioBatchSize(20);
        getTables().put(tbTimeMembro, "tbTimeMembro");
        tbTimeMembro.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(0);
        tbEmpresas.setWKey("7000194;43002");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public EMPRESAS_FUNCOES tbEmpresasFuncoes;

    private void init_tbEmpresasFuncoes() {
        tbEmpresasFuncoes = rn.tbEmpresasFuncoes;
        tbEmpresasFuncoes.setName("tbEmpresasFuncoes");
        tbEmpresasFuncoes.setMaxRowCount(0);
        tbEmpresasFuncoes.setWKey("7000194;43003");
        tbEmpresasFuncoes.setRatioBatchSize(20);
        getTables().put(tbEmpresasFuncoes, "tbEmpresasFuncoes");
        tbEmpresasFuncoes.applyProperties();
    }

    public TIME_AGENTES_DISPONIVEIS tbTimeAgentesDisponiveis;

    private void init_tbTimeAgentesDisponiveis() {
        tbTimeAgentesDisponiveis = rn.tbTimeAgentesDisponiveis;
        tbTimeAgentesDisponiveis.setName("tbTimeAgentesDisponiveis");
        tbTimeAgentesDisponiveis.setMaxRowCount(0);
        tbTimeAgentesDisponiveis.setWKey("7000194;43004");
        tbTimeAgentesDisponiveis.setRatioBatchSize(20);
        getTables().put(tbTimeAgentesDisponiveis, "tbTimeAgentesDisponiveis");
        tbTimeAgentesDisponiveis.applyProperties();
    }

    public TIME_TEMPLATE tbTimeTemplate;

    private void init_tbTimeTemplate() {
        tbTimeTemplate = rn.tbTimeTemplate;
        tbTimeTemplate.setName("tbTimeTemplate");
        tbTimeTemplate.setMaxRowCount(200);
        tbTimeTemplate.setWKey("7000194;26501");
        tbTimeTemplate.setRatioBatchSize(20);
        getTables().put(tbTimeTemplate, "tbTimeTemplate");
        tbTimeTemplate.applyProperties();
    }

    public CRM_TIME_MEMBRO tbTimeMembroTemplateDisponivel;

    private void init_tbTimeMembroTemplateDisponivel() {
        tbTimeMembroTemplateDisponivel = rn.tbTimeMembroTemplateDisponivel;
        tbTimeMembroTemplateDisponivel.setName("tbTimeMembroTemplateDisponivel");
        tbTimeMembroTemplateDisponivel.setMaxRowCount(200);
        tbTimeMembroTemplateDisponivel.setWKey("7000194;26502");
        tbTimeMembroTemplateDisponivel.setRatioBatchSize(20);
        getTables().put(tbTimeMembroTemplateDisponivel, "tbTimeMembroTemplateDisponivel");
        tbTimeMembroTemplateDisponivel.applyProperties();
    }

    public CRM_TIME_MEMBRO tbTimeMembroTemplateCruzado;

    private void init_tbTimeMembroTemplateCruzado() {
        tbTimeMembroTemplateCruzado = rn.tbTimeMembroTemplateCruzado;
        tbTimeMembroTemplateCruzado.setName("tbTimeMembroTemplateCruzado");
        tbTimeMembroTemplateCruzado.setMaxRowCount(200);
        tbTimeMembroTemplateCruzado.setWKey("7000194;26503");
        tbTimeMembroTemplateCruzado.setRatioBatchSize(20);
        getTables().put(tbTimeMembroTemplateCruzado, "tbTimeMembroTemplateCruzado");
        tbTimeMembroTemplateCruzado.applyProperties();
    }

    public TFFilterWindow filtroAvancado = new TFFilterWindow();

    private void init_filtroAvancado() {
        filtroAvancado.setName("filtroAvancado");
        filtroAvancado.setWidth(450);
        filtroAvancado.setHeight(400);
        filtroAvancado.setCaption("Filtro");
        filtroAvancado.setColumns(2);
        filtroAvancado.setFilterStyle("fsAddCondition");
        filtroAvancado.applyProperties();
    }

    public TFPopupMenu popMenuPrincipal = new TFPopupMenu();

    private void init_popMenuPrincipal() {
        popMenuPrincipal.setName("popMenuPrincipal");
        FrmTimeMembros.addChildren(popMenuPrincipal);
        popMenuPrincipal.applyProperties();
    }

    public TFMenuItem menuItemAbreTabelaAux = new TFMenuItem();

    private void init_menuItemAbreTabelaAux() {
        menuItemAbreTabelaAux.setName("menuItemAbreTabelaAux");
        menuItemAbreTabelaAux.setCaption("Abre Tabela Auxiliares");
        menuItemAbreTabelaAux.setHint("For\u00E7a a abertura das tabela auxiliares e lookup, visto que usuario pode abrir o cadastro em um aba independente e alterar o registro");
        menuItemAbreTabelaAux.setImageIndex(200022);
        menuItemAbreTabelaAux.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemAbreTabelaAuxClick(event);
            processarFlow("FrmTimeMembros", "menuItemAbreTabelaAux", "OnClick");
        });
        menuItemAbreTabelaAux.setAccess(false);
        menuItemAbreTabelaAux.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemAbreTabelaAux);
        menuItemAbreTabelaAux.applyProperties();
    }

    public TFMenuItem menuHabilitaNavegacao = new TFMenuItem();

    private void init_menuHabilitaNavegacao() {
        menuHabilitaNavegacao.setName("menuHabilitaNavegacao");
        menuHabilitaNavegacao.setCaption("Habilitar Navega\u00E7\u00E3o Durante Edi\u00E7\u00E3o");
        menuHabilitaNavegacao.setHint("Quando habilita a navega\u00E7\u00E3o durante a edi\u00E7\u00E3o (Inclus\u00E3o/Altera\u00E7\u00E3o) ao mover para frente ou para tr\u00E1s o registro atual \u00E9 automaticamente salvo");
        menuHabilitaNavegacao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuHabilitaNavegacaoClick(event);
            processarFlow("FrmTimeMembros", "menuHabilitaNavegacao", "OnClick");
        });
        menuHabilitaNavegacao.setAccess(true);
        menuHabilitaNavegacao.setCheckmark(true);
        popMenuPrincipal.addChildren(menuHabilitaNavegacao);
        menuHabilitaNavegacao.applyProperties();
    }

    public TFMenuItem menuSelecaoMultipla = new TFMenuItem();

    private void init_menuSelecaoMultipla() {
        menuSelecaoMultipla.setName("menuSelecaoMultipla");
        menuSelecaoMultipla.setCaption("Selecionar Multiplos Registros");
        menuSelecaoMultipla.setHint("Permite altera\u00E7\u00E3o/exclus\u00E3o de todos os registros selecionados");
        menuSelecaoMultipla.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuSelecaoMultiplaClick(event);
            processarFlow("FrmTimeMembros", "menuSelecaoMultipla", "OnClick");
        });
        menuSelecaoMultipla.setAccess(true);
        menuSelecaoMultipla.setCheckmark(true);
        popMenuPrincipal.addChildren(menuSelecaoMultipla);
        menuSelecaoMultipla.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("Grid");
        FMenuItem1.setImageIndex(22006);
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        popMenuPrincipal.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem menuItemConfgGrid = new TFMenuItem();

    private void init_menuItemConfgGrid() {
        menuItemConfgGrid.setName("menuItemConfgGrid");
        menuItemConfgGrid.setCaption("Configurar Colunas da Grid");
        menuItemConfgGrid.setImageIndex(200021);
        menuItemConfgGrid.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemConfgGridClick(event);
            processarFlow("FrmTimeMembros", "menuItemConfgGrid", "OnClick");
        });
        menuItemConfgGrid.setAccess(true);
        menuItemConfgGrid.setCheckmark(false);
        FMenuItem1.addChildren(menuItemConfgGrid);
        menuItemConfgGrid.applyProperties();
    }

    public TFMenuItem menuItemExportPdf = new TFMenuItem();

    private void init_menuItemExportPdf() {
        menuItemExportPdf.setName("menuItemExportPdf");
        menuItemExportPdf.setCaption("Exportar PDF");
        menuItemExportPdf.setImageIndex(22005);
        menuItemExportPdf.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportPdfClick(event);
            processarFlow("FrmTimeMembros", "menuItemExportPdf", "OnClick");
        });
        menuItemExportPdf.setAccess(true);
        menuItemExportPdf.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportPdf);
        menuItemExportPdf.applyProperties();
    }

    public TFMenuItem menuItemExportExcel = new TFMenuItem();

    private void init_menuItemExportExcel() {
        menuItemExportExcel.setName("menuItemExportExcel");
        menuItemExportExcel.setCaption("Exportar para Excel");
        menuItemExportExcel.setImageIndex(22004);
        menuItemExportExcel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportExcelClick(event);
            processarFlow("FrmTimeMembros", "menuItemExportExcel", "OnClick");
        });
        menuItemExportExcel.setAccess(true);
        menuItemExportExcel.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportExcel);
        menuItemExportExcel.applyProperties();
    }

    public TFMenuItem menuItemHelp = new TFMenuItem();

    private void init_menuItemHelp() {
        menuItemHelp.setName("menuItemHelp");
        menuItemHelp.setCaption("Help");
        menuItemHelp.setHint("Help da Tela");
        menuItemHelp.setImageIndex(11);
        menuItemHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemHelpClick(event);
            processarFlow("FrmTimeMembros", "menuItemHelp", "OnClick");
        });
        menuItemHelp.setAccess(false);
        menuItemHelp.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemHelp);
        menuItemHelp.applyProperties();
    }

    public TFGridConfigWindow gridConfig = new TFGridConfigWindow();

    private void init_gridConfig() {
        gridConfig.setName("gridConfig");
        gridConfig.setWidth(500);
        gridConfig.setHeight(500);
        gridConfig.setCaption("Configura\u00E7\u00E3o de Grid");
        gridConfig.setGrid(gridPrincipal);
        gridConfig.applyProperties();
    }

    protected TFForm FrmTimeMembros = this;
    private void init_FrmTimeMembros() {
        FrmTimeMembros.setName("FrmTimeMembros");
        FrmTimeMembros.setAlign("alTop");
        FrmTimeMembros.setCaption("Cadastro dos Times");
        FrmTimeMembros.setClientHeight(596);
        FrmTimeMembros.setClientWidth(981);
        FrmTimeMembros.setColor("clWhite");
        FrmTimeMembros.setWOrigem("EhMain");
        FrmTimeMembros.setWKey("7000194");
        TFShortcutKeyItem item0 = new TFShortcutKeyItem();
        item0.setModifier("smCtrl");
        item0.setKey("sk1");
        item0.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionPesquisar(event);
            processarFlow("FrmTimeMembros", "item0", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item0);
        TFShortcutKeyItem item1 = new TFShortcutKeyItem();
        item1.setModifier("smCtrl");
        item1.setKey("sk2");
        item1.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionIncluir(event);
            processarFlow("FrmTimeMembros", "item1", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item1);
        TFShortcutKeyItem item2 = new TFShortcutKeyItem();
        item2.setModifier("smCtrl");
        item2.setKey("sk3");
        item2.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionAlterar(event);
            processarFlow("FrmTimeMembros", "item2", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item2);
        TFShortcutKeyItem item3 = new TFShortcutKeyItem();
        item3.setModifier("smCtrl");
        item3.setKey("sk4");
        item3.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionExcluir(event);
            processarFlow("FrmTimeMembros", "item3", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item3);
        TFShortcutKeyItem item4 = new TFShortcutKeyItem();
        item4.setModifier("smCtrl");
        item4.setKey("sk5");
        item4.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionSalvar(event);
            processarFlow("FrmTimeMembros", "item4", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item4);
        TFShortcutKeyItem item5 = new TFShortcutKeyItem();
        item5.setModifier("smCtrl");
        item5.setKey("sk6");
        item5.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionCancelar(event);
            processarFlow("FrmTimeMembros", "item5", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item5);
        TFShortcutKeyItem item6 = new TFShortcutKeyItem();
        item6.setModifier("smCtrl");
        item6.setKey("sk7");
        item6.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionAnterior(event);
            processarFlow("FrmTimeMembros", "item6", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item6);
        TFShortcutKeyItem item7 = new TFShortcutKeyItem();
        item7.setModifier("smCtrl");
        item7.setKey("sk8");
        item7.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionProximo(event);
            processarFlow("FrmTimeMembros", "item7", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item7);
        TFShortcutKeyItem item8 = new TFShortcutKeyItem();
        item8.setModifier("smCtrl");
        item8.setKey("sk9");
        item8.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionAceitar(event);
            processarFlow("FrmTimeMembros", "item8", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item8);
        TFShortcutKeyItem item9 = new TFShortcutKeyItem();
        item9.setModifier("smCtrl");
        item9.setKey("sk0");
        item9.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmTimeMembroskeyActionSalvarContinuar(event);
            processarFlow("FrmTimeMembros", "item9", "OnKeyAction");
        });
        FrmTimeMembros.getShortcutKeys().add(item9);
        FrmTimeMembros.setSpacing(0);
        FrmTimeMembros.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridConfig.loadConfig();
        });
        FrmTimeMembros.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(981);
        FHBox6.setHeight(68);
        FHBox6.setAlign("alTop");
        FHBox6.setBorderStyle("stNone");
        FHBox6.setColor("16514043");
        FHBox6.setPaddingTop(5);
        FHBox6.setPaddingLeft(2);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(5);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FrmTimeMembros.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(829);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(2);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FHBox6.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnConsultar = new TFButton();

    private void init_btnConsultar() {
        btnConsultar.setName("btnConsultar");
        btnConsultar.setLeft(0);
        btnConsultar.setTop(0);
        btnConsultar.setWidth(65);
        btnConsultar.setHeight(53);
        btnConsultar.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultar.setCaption("Pesquisar");
        btnConsultar.setFontColor("clWindowText");
        btnConsultar.setFontSize(-11);
        btnConsultar.setFontName("Tahoma");
        btnConsultar.setFontStyle("[]");
        btnConsultar.setLayout("blGlyphTop");
        btnConsultar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConsultarClick(event);
            processarFlow("FrmTimeMembros", "btnConsultar", "OnClick");
        });
        btnConsultar.setImageId(13);
        btnConsultar.setColor("clBtnFace");
        btnConsultar.setAccess(true);
        btnConsultar.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultar);
        btnConsultar.applyProperties();
    }

    public TFButton btnFiltroAvancado = new TFButton();

    private void init_btnFiltroAvancado() {
        btnFiltroAvancado.setName("btnFiltroAvancado");
        btnFiltroAvancado.setLeft(65);
        btnFiltroAvancado.setTop(0);
        btnFiltroAvancado.setWidth(65);
        btnFiltroAvancado.setHeight(53);
        btnFiltroAvancado.setHint("Filtro Avan\u00E7ado");
        btnFiltroAvancado.setCaption("Filtro");
        btnFiltroAvancado.setFontColor("clWindowText");
        btnFiltroAvancado.setFontSize(-11);
        btnFiltroAvancado.setFontName("Tahoma");
        btnFiltroAvancado.setFontStyle("[]");
        btnFiltroAvancado.setLayout("blGlyphTop");
        btnFiltroAvancado.setVisible(false);
        btnFiltroAvancado.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFiltroAvancadoClick(event);
            processarFlow("FrmTimeMembros", "btnFiltroAvancado", "OnClick");
        });
        btnFiltroAvancado.setImageId(50002);
        btnFiltroAvancado.setColor("clBtnFace");
        btnFiltroAvancado.setAccess(true);
        btnFiltroAvancado.setIconReverseDirection(false);
        FHBox1.addChildren(btnFiltroAvancado);
        btnFiltroAvancado.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(130);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(53);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmTimeMembros", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(195);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(53);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmTimeMembros", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(260);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(53);
        btnExcluir.setHint("Exclui o Registro Selecionado  (CRTL+ 4)");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.setVisible(false);
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmTimeMembros", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(true);
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(325);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmTimeMembros", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnSalvarContinuar = new TFButton();

    private void init_btnSalvarContinuar() {
        btnSalvarContinuar.setName("btnSalvarContinuar");
        btnSalvarContinuar.setLeft(390);
        btnSalvarContinuar.setTop(0);
        btnSalvarContinuar.setWidth(65);
        btnSalvarContinuar.setHeight(53);
        btnSalvarContinuar.setHint("Salvar e Continuar");
        btnSalvarContinuar.setCaption("Salvar Cont.");
        btnSalvarContinuar.setFontColor("clWindowText");
        btnSalvarContinuar.setFontSize(-11);
        btnSalvarContinuar.setFontName("Tahoma");
        btnSalvarContinuar.setFontStyle("[]");
        btnSalvarContinuar.setLayout("blGlyphTop");
        btnSalvarContinuar.setVisible(false);
        btnSalvarContinuar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarContinuarClick(event);
            processarFlow("FrmTimeMembros", "btnSalvarContinuar", "OnClick");
        });
        btnSalvarContinuar.setImageId(22001);
        btnSalvarContinuar.setColor("clBtnFace");
        btnSalvarContinuar.setAccess(false);
        btnSalvarContinuar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvarContinuar);
        btnSalvarContinuar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(455);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(53);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmTimeMembros", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(520);
        FHBox3.setTop(0);
        FHBox3.setWidth(26);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnAnterior = new TFButton();

    private void init_btnAnterior() {
        btnAnterior.setName("btnAnterior");
        btnAnterior.setLeft(546);
        btnAnterior.setTop(0);
        btnAnterior.setWidth(65);
        btnAnterior.setHeight(53);
        btnAnterior.setHint("Registro Anterior, Estando em Modo de Inclus\u00E3o/Altera\u00E7\u00E3o Salva Antes de Mover o Registro (CRTL+ 7)");
        btnAnterior.setFontColor("clWindowText");
        btnAnterior.setFontSize(-11);
        btnAnterior.setFontName("Tahoma");
        btnAnterior.setFontStyle("[]");
        btnAnterior.setLayout("blGlyphTop");
        btnAnterior.setVisible(false);
        btnAnterior.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAnteriorClick(event);
            processarFlow("FrmTimeMembros", "btnAnterior", "OnClick");
        });
        btnAnterior.setImageId(14);
        btnAnterior.setColor("clBtnFace");
        btnAnterior.setAccess(false);
        btnAnterior.setIconReverseDirection(false);
        FHBox1.addChildren(btnAnterior);
        btnAnterior.applyProperties();
    }

    public TFButton btnProximo = new TFButton();

    private void init_btnProximo() {
        btnProximo.setName("btnProximo");
        btnProximo.setLeft(611);
        btnProximo.setTop(0);
        btnProximo.setWidth(65);
        btnProximo.setHeight(53);
        btnProximo.setHint("Pr\u00F3ximo Registro   (CRTL+ 8)");
        btnProximo.setFontColor("clWindowText");
        btnProximo.setFontSize(-11);
        btnProximo.setFontName("Tahoma");
        btnProximo.setFontStyle("[]");
        btnProximo.setLayout("blGlyphTop");
        btnProximo.setVisible(false);
        btnProximo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnProximoClick(event);
            processarFlow("FrmTimeMembros", "btnProximo", "OnClick");
        });
        btnProximo.setImageId(15);
        btnProximo.setColor("clBtnFace");
        btnProximo.setAccess(false);
        btnProximo.setIconReverseDirection(false);
        FHBox1.addChildren(btnProximo);
        btnProximo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(676);
        FHBox8.setTop(0);
        FHBox8.setWidth(32);
        FHBox8.setHeight(32);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox1.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(829);
        FHBox5.setTop(0);
        FHBox5.setWidth(146);
        FHBox5.setHeight(60);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(2);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(3);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox6.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(0);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(65);
        btnAceitar.setHeight(53);
        btnAceitar.setHint("Aceita o Registro Selecionado para Outro Formulario  (CRTL+ 9)");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.setVisible(false);
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmTimeMembros", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox5.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnMais = new TFButton();

    private void init_btnMais() {
        btnMais.setName("btnMais");
        btnMais.setLeft(65);
        btnMais.setTop(0);
        btnMais.setWidth(65);
        btnMais.setHeight(53);
        btnMais.setHint("Mais Op\u00E7\u00F5es");
        btnMais.setFontColor("clWindowText");
        btnMais.setFontSize(-21);
        btnMais.setFontName("Tahoma");
        btnMais.setFontStyle("[]");
        btnMais.setLayout("blGlyphTop");
        btnMais.setVisible(false);
        btnMais.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnMaisClick(event);
            processarFlow("FrmTimeMembros", "btnMais", "OnClick");
        });
        btnMais.setImageId(22002);
        btnMais.setColor("clBtnFace");
        btnMais.setAccess(false);
        btnMais.setIconReverseDirection(false);
        FHBox5.addChildren(btnMais);
        btnMais.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(3);
        FHBox4.setTop(69);
        FHBox4.setWidth(970);
        FHBox4.setHeight(23);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FrmTimeMembros.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblMensagem = new TFLabel();

    private void init_lblMensagem() {
        lblMensagem.setName("lblMensagem");
        lblMensagem.setLeft(0);
        lblMensagem.setTop(0);
        lblMensagem.setWidth(78);
        lblMensagem.setHeight(16);
        lblMensagem.setCaption("Mensagem....");
        lblMensagem.setFontColor("clNavy");
        lblMensagem.setFontSize(-13);
        lblMensagem.setFontName("Tahoma");
        lblMensagem.setFontStyle("[]");
        lblMensagem.setVerticalAlignment("taAlignTop");
        lblMensagem.setWordBreak(false);
        FHBox4.addChildren(lblMensagem);
        lblMensagem.applyProperties();
    }

    public TFPageControl pgPrincipal = new TFPageControl();

    private void init_pgPrincipal() {
        pgPrincipal.setName("pgPrincipal");
        pgPrincipal.setLeft(4);
        pgPrincipal.setTop(91);
        pgPrincipal.setWidth(974);
        pgPrincipal.setHeight(499);
        pgPrincipal.setTabPosition("tpTop");
        pgPrincipal.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgPrincipalChange(event);
            processarFlow("FrmTimeMembros", "pgPrincipal", "OnChange");
        });
        pgPrincipal.setFlexVflex("ftTrue");
        pgPrincipal.setFlexHflex("ftTrue");
        pgPrincipal.setRenderStyle("rsTabbed");
        pgPrincipal.applyProperties();
        FrmTimeMembros.addChildren(pgPrincipal);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setVisible(true);
        tabListagem.setClosable(false);
        pgPrincipal.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(966);
        FVBox1.setHeight(471);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setColor("clWhite");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        tabListagem.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGroupbox grpBoxFiltro = new TFGroupbox();

    private void init_grpBoxFiltro() {
        grpBoxFiltro.setName("grpBoxFiltro");
        grpBoxFiltro.setLeft(0);
        grpBoxFiltro.setTop(0);
        grpBoxFiltro.setWidth(960);
        grpBoxFiltro.setHeight(74);
        grpBoxFiltro.setCaption("Filtro R\u00E1pido");
        grpBoxFiltro.setFontColor("clWindowText");
        grpBoxFiltro.setFontSize(-11);
        grpBoxFiltro.setFontName("Tahoma");
        grpBoxFiltro.setFontStyle("[]");
        grpBoxFiltro.setVisible(false);
        grpBoxFiltro.setFlexVflex("ftMin");
        grpBoxFiltro.setFlexHflex("ftTrue");
        grpBoxFiltro.setScrollable(false);
        grpBoxFiltro.setClosable(true);
        grpBoxFiltro.setClosed(false);
        grpBoxFiltro.setOrient("coHorizontal");
        grpBoxFiltro.setStyle("grp3D");
        grpBoxFiltro.setHeaderImageId(0);
        FVBox1.addChildren(grpBoxFiltro);
        grpBoxFiltro.applyProperties();
    }

    public TFGridPanel gpFiltroPrincipal = new TFGridPanel();

    private void init_gpFiltroPrincipal() {
        gpFiltroPrincipal.setName("gpFiltroPrincipal");
        gpFiltroPrincipal.setLeft(2);
        gpFiltroPrincipal.setTop(15);
        gpFiltroPrincipal.setWidth(956);
        gpFiltroPrincipal.setHeight(47);
        gpFiltroPrincipal.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(33.333333333333340000);
        gpFiltroPrincipal.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(33.333333333333340000);
        gpFiltroPrincipal.getColumnCollection().add(item1);
        TFGridPanelColumn item2 = new TFGridPanelColumn();
        item2.setValue(33.333333333333340000);
        gpFiltroPrincipal.getColumnCollection().add(item2);
        TFGridPanelRow item3 = new TFGridPanelRow();
        item3.setSizeStyle("ssAbsolute");
        item3.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item3);
        TFGridPanelRow item4 = new TFGridPanelRow();
        item4.setSizeStyle("ssAbsolute");
        item4.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item4);
        gpFiltroPrincipal.setFlexVflex("ftTrue");
        gpFiltroPrincipal.setFlexHflex("ftTrue");
        gpFiltroPrincipal.setAllRowFlex(true);
        gpFiltroPrincipal.setColumnTabOrder(false);
        grpBoxFiltro.addChildren(gpFiltroPrincipal);
        gpFiltroPrincipal.applyProperties();
    }

    public TFGrid gridPrincipal = new TFGrid();

    private void init_gridPrincipal() {
        gridPrincipal.setName("gridPrincipal");
        gridPrincipal.setLeft(0);
        gridPrincipal.setTop(75);
        gridPrincipal.setWidth(956);
        gridPrincipal.setHeight(355);
        gridPrincipal.setTable(tbTime);
        gridPrincipal.setFlexVflex("ftTrue");
        gridPrincipal.setFlexHflex("ftTrue");
        gridPrincipal.setPagingEnabled(true);
        gridPrincipal.setFrozenColumns(0);
        gridPrincipal.setShowFooter(false);
        gridPrincipal.setShowHeader(true);
        gridPrincipal.setMultiSelection(false);
        gridPrincipal.setGroupingEnabled(false);
        gridPrincipal.setGroupingExpanded(false);
        gridPrincipal.setGroupingShowFooter(false);
        gridPrincipal.setCrosstabEnabled(false);
        gridPrincipal.setCrosstabGroupType("cgtConcat");
        gridPrincipal.setEditionEnabled(false);
        gridPrincipal.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption("Alt.");
        item0.setWidth(52);
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setEvalType("etExpression");
        item1.setImageId(7);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageAlterar(event);
            processarFlow("FrmTimeMembros", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setTitleCaption("Exc.");
        item2.setWidth(56);
        item2.setVisible(false);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("*");
        item3.setEvalType("etExpression");
        item3.setImageId(8);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageDelete(event);
            processarFlow("FrmTimeMembros", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DESCRICAO");
        item4.setTitleCaption("Descri\u00E7\u00E3o");
        item4.setWidth(319);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setHint("NOME DO TIME");
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("GRUPO_DESC");
        item5.setTitleCaption("Grupo");
        item5.setWidth(235);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(true);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("ATIVO");
        item6.setTitleCaption("Ativo");
        item6.setWidth(91);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftCheckBox");
        item6.setFlexRatio(0);
        item6.setSort(true);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setHint("TIME ATIVOS E NAO ATIVOS");
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item6);
        FVBox1.addChildren(gridPrincipal);
        gridPrincipal.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        pgPrincipal.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(966);
        FVBox2.setHeight(471);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(5);
        FVBox2.setPaddingLeft(5);
        FVBox2.setPaddingRight(5);
        FVBox2.setPaddingBottom(5);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFGroupbox grpBoxPrincipal = new TFGroupbox();

    private void init_grpBoxPrincipal() {
        grpBoxPrincipal.setName("grpBoxPrincipal");
        grpBoxPrincipal.setLeft(0);
        grpBoxPrincipal.setTop(0);
        grpBoxPrincipal.setWidth(729);
        grpBoxPrincipal.setHeight(126);
        grpBoxPrincipal.setCaption("Time");
        grpBoxPrincipal.setFontColor("clWindowText");
        grpBoxPrincipal.setFontSize(-11);
        grpBoxPrincipal.setFontName("Tahoma");
        grpBoxPrincipal.setFontStyle("[]");
        grpBoxPrincipal.setFlexVflex("ftMin");
        grpBoxPrincipal.setFlexHflex("ftTrue");
        grpBoxPrincipal.setScrollable(true);
        grpBoxPrincipal.setClosable(false);
        grpBoxPrincipal.setClosed(false);
        grpBoxPrincipal.setOrient("coVertical");
        grpBoxPrincipal.setStyle("grp3D");
        grpBoxPrincipal.setHeaderImageId(0);
        FVBox2.addChildren(grpBoxPrincipal);
        grpBoxPrincipal.applyProperties();
    }

    public TFGridPanel FGridPanel2 = new TFGridPanel();

    private void init_FGridPanel2() {
        FGridPanel2.setName("FGridPanel2");
        FGridPanel2.setLeft(2);
        FGridPanel2.setTop(15);
        FGridPanel2.setWidth(725);
        FGridPanel2.setHeight(104);
        FGridPanel2.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("lbIdTime70001");
        item2.setRow(0);
        FGridPanel2.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("edIdTime70001");
        item3.setRow(0);
        FGridPanel2.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("lbDescricao70001");
        item4.setRow(1);
        FGridPanel2.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("edDescricao70001");
        item5.setRow(1);
        FGridPanel2.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(0);
        item6.setControl("lbGrupo70001");
        item6.setRow(2);
        FGridPanel2.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(1);
        item7.setControl("edGrupo70001");
        item7.setRow(2);
        FGridPanel2.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(0);
        item8.setControl("lbAtivo70001");
        item8.setRow(3);
        FGridPanel2.getControlCollection().add(item8);
        TFControlItem item9 = new TFControlItem();
        item9.setColumn(1);
        item9.setControl("edAtivo70001");
        item9.setRow(3);
        FGridPanel2.getControlCollection().add(item9);
        TFControlItem item10 = new TFControlItem();
        item10.setColumn(0);
        item10.setControl("lbMaxDescServ70001");
        item10.setRow(4);
        FGridPanel2.getControlCollection().add(item10);
        TFControlItem item11 = new TFControlItem();
        item11.setColumn(1);
        item11.setControl("edMaxDescServ70001");
        item11.setRow(4);
        FGridPanel2.getControlCollection().add(item11);
        TFGridPanelRow item12 = new TFGridPanelRow();
        item12.setSizeStyle("ssAbsolute");
        item12.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item12);
        TFGridPanelRow item13 = new TFGridPanelRow();
        item13.setSizeStyle("ssAbsolute");
        item13.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item13);
        TFGridPanelRow item14 = new TFGridPanelRow();
        item14.setSizeStyle("ssAbsolute");
        item14.setValue(21.000000000000000000);
        FGridPanel2.getRowCollection().add(item14);
        TFGridPanelRow item15 = new TFGridPanelRow();
        item15.setSizeStyle("ssAbsolute");
        item15.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item15);
        TFGridPanelRow item16 = new TFGridPanelRow();
        item16.setSizeStyle("ssAbsolute");
        item16.setValue(21.000000000000000000);
        FGridPanel2.getRowCollection().add(item16);
        FGridPanel2.setFlexVflex("ftFalse");
        FGridPanel2.setFlexHflex("ftTrue");
        FGridPanel2.setAllRowFlex(true);
        FGridPanel2.setColumnTabOrder(false);
        grpBoxPrincipal.addChildren(FGridPanel2);
        FGridPanel2.applyProperties();
    }

    public TFLabel lbMaxDescServ70001 = new TFLabel();

    private void init_lbMaxDescServ70001() {
        lbMaxDescServ70001.setName("lbMaxDescServ70001");
        lbMaxDescServ70001.setLeft(15);
        lbMaxDescServ70001.setTop(79);
        lbMaxDescServ70001.setWidth(86);
        lbMaxDescServ70001.setHeight(13);
        lbMaxDescServ70001.setAlign("alRight");
        lbMaxDescServ70001.setCaption(" Desconto Servi\u00E7o");
        lbMaxDescServ70001.setFontColor("clWindowText");
        lbMaxDescServ70001.setFontSize(-11);
        lbMaxDescServ70001.setFontName("Tahoma");
        lbMaxDescServ70001.setFontStyle("[]");
        lbMaxDescServ70001.setVerticalAlignment("taVerticalCenter");
        lbMaxDescServ70001.setWordBreak(false);
        FGridPanel2.addChildren(lbMaxDescServ70001);
        lbMaxDescServ70001.applyProperties();
    }

    public TFLabel lbDescricao70001 = new TFLabel();

    private void init_lbDescricao70001() {
        lbDescricao70001.setName("lbDescricao70001");
        lbDescricao70001.setLeft(55);
        lbDescricao70001.setTop(20);
        lbDescricao70001.setWidth(46);
        lbDescricao70001.setHeight(13);
        lbDescricao70001.setAlign("alRight");
        lbDescricao70001.setCaption("Descri\u00E7\u00E3o");
        lbDescricao70001.setFontColor("clWindowText");
        lbDescricao70001.setFontSize(-11);
        lbDescricao70001.setFontName("Tahoma");
        lbDescricao70001.setFontStyle("[]");
        lbDescricao70001.setVerticalAlignment("taVerticalCenter");
        lbDescricao70001.setWordBreak(false);
        FGridPanel2.addChildren(lbDescricao70001);
        lbDescricao70001.applyProperties();
    }

    public TFLabel lbIdTime70001 = new TFLabel();

    private void init_lbIdTime70001() {
        lbIdTime70001.setName("lbIdTime70001");
        lbIdTime70001.setLeft(62);
        lbIdTime70001.setTop(1);
        lbIdTime70001.setWidth(39);
        lbIdTime70001.setHeight(13);
        lbIdTime70001.setAlign("alRight");
        lbIdTime70001.setCaption("Id. Time");
        lbIdTime70001.setFontColor("clWindowText");
        lbIdTime70001.setFontSize(-11);
        lbIdTime70001.setFontName("Tahoma");
        lbIdTime70001.setFontStyle("[]");
        lbIdTime70001.setVerticalAlignment("taVerticalCenter");
        lbIdTime70001.setWordBreak(false);
        FGridPanel2.addChildren(lbIdTime70001);
        lbIdTime70001.applyProperties();
    }

    public TFLabel lbGrupo70001 = new TFLabel();

    private void init_lbGrupo70001() {
        lbGrupo70001.setName("lbGrupo70001");
        lbGrupo70001.setLeft(72);
        lbGrupo70001.setTop(39);
        lbGrupo70001.setWidth(29);
        lbGrupo70001.setHeight(13);
        lbGrupo70001.setAlign("alRight");
        lbGrupo70001.setCaption("Grupo");
        lbGrupo70001.setFontColor("clWindowText");
        lbGrupo70001.setFontSize(-11);
        lbGrupo70001.setFontName("Tahoma");
        lbGrupo70001.setFontStyle("[]");
        lbGrupo70001.setVerticalAlignment("taVerticalCenter");
        lbGrupo70001.setWordBreak(false);
        FGridPanel2.addChildren(lbGrupo70001);
        lbGrupo70001.applyProperties();
    }

    public TFLabel lbAtivo70001 = new TFLabel();

    private void init_lbAtivo70001() {
        lbAtivo70001.setName("lbAtivo70001");
        lbAtivo70001.setLeft(76);
        lbAtivo70001.setTop(60);
        lbAtivo70001.setWidth(25);
        lbAtivo70001.setHeight(13);
        lbAtivo70001.setAlign("alRight");
        lbAtivo70001.setCaption("Ativo");
        lbAtivo70001.setFontColor("clWindowText");
        lbAtivo70001.setFontSize(-11);
        lbAtivo70001.setFontName("Tahoma");
        lbAtivo70001.setFontStyle("[]");
        lbAtivo70001.setVerticalAlignment("taVerticalCenter");
        lbAtivo70001.setWordBreak(false);
        FGridPanel2.addChildren(lbAtivo70001);
        lbAtivo70001.applyProperties();
    }

    public TFInteger edIdTime70001 = new TFInteger();

    private void init_edIdTime70001() {
        edIdTime70001.setName("edIdTime70001");
        edIdTime70001.setLeft(101);
        edIdTime70001.setTop(1);
        edIdTime70001.setWidth(80);
        edIdTime70001.setHeight(19);
        edIdTime70001.setHint("SEQUENCIA DO ID TIME");
        edIdTime70001.setTable(tbTime);
        edIdTime70001.setFieldName("ID_TIME");
        edIdTime70001.setHelpCaption("Id. Time");
        edIdTime70001.setFlex(false);
        edIdTime70001.setRequired(false);
        edIdTime70001.setConstraintCheckWhen("cwImmediate");
        edIdTime70001.setConstraintCheckType("ctExpression");
        edIdTime70001.setConstraintFocusOnError(false);
        edIdTime70001.setConstraintEnableUI(true);
        edIdTime70001.setConstraintEnabled(false);
        edIdTime70001.setConstraintFormCheck(true);
        edIdTime70001.setMaxlength(0);
        edIdTime70001.setAlign("alLeft");
        edIdTime70001.setFontColor("clWindowText");
        edIdTime70001.setFontSize(-13);
        edIdTime70001.setFontName("Tahoma");
        edIdTime70001.setFontStyle("[]");
        edIdTime70001.setAlignment("taRightJustify");
        edIdTime70001.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edIdTime70001Exit(event);
            processarFlow("FrmTimeMembros", "edIdTime70001", "OnExit");
        });
        FGridPanel2.addChildren(edIdTime70001);
        edIdTime70001.applyProperties();
        addValidatable(edIdTime70001);
    }

    public TFString edDescricao70001 = new TFString();

    private void init_edDescricao70001() {
        edDescricao70001.setName("edDescricao70001");
        edDescricao70001.setLeft(101);
        edDescricao70001.setTop(20);
        edDescricao70001.setWidth(580);
        edDescricao70001.setHeight(19);
        edDescricao70001.setHint("NOME DO TIME");
        edDescricao70001.setTable(tbTime);
        edDescricao70001.setFieldName("DESCRICAO");
        edDescricao70001.setHelpCaption("Descri\u00E7\u00E3o");
        edDescricao70001.setFlex(true);
        edDescricao70001.setRequired(true);
        edDescricao70001.setConstraintExpression("value is null or trim(value) = ''");
        edDescricao70001.setConstraintMessage("Campo Descri\u00E7\u00E3o, preenchimento \u00E9 obrigat\u00F3rio");
        edDescricao70001.setConstraintCheckWhen("cwImmediate");
        edDescricao70001.setConstraintCheckType("ctExpression");
        edDescricao70001.setConstraintFocusOnError(false);
        edDescricao70001.setConstraintGroupName("grpTbtime");
        edDescricao70001.setConstraintEnableUI(true);
        edDescricao70001.setConstraintEnabled(true);
        edDescricao70001.setConstraintFormCheck(true);
        edDescricao70001.setCharCase("ccNormal");
        edDescricao70001.setPwd(false);
        edDescricao70001.setMaxlength(100);
        edDescricao70001.setAlign("alLeft");
        edDescricao70001.setFontColor("clWindowText");
        edDescricao70001.setFontSize(-13);
        edDescricao70001.setFontName("Tahoma");
        edDescricao70001.setFontStyle("[]");
        edDescricao70001.setSaveLiteralCharacter(false);
        edDescricao70001.applyProperties();
        FGridPanel2.addChildren(edDescricao70001);
        addValidatable(edDescricao70001);
    }

    public TFCombo edGrupo70001 = new TFCombo();

    private void init_edGrupo70001() {
        edGrupo70001.setName("edGrupo70001");
        edGrupo70001.setLeft(101);
        edGrupo70001.setTop(39);
        edGrupo70001.setWidth(200);
        edGrupo70001.setHeight(21);
        edGrupo70001.setTable(tbTime);
        edGrupo70001.setFieldName("GRUPO");
        edGrupo70001.setFlex(false);
        edGrupo70001.setListOptions("Consultores=1; Agentes de PA=2;Reclama\u00E7\u00E3o (RAC)=3");
        edGrupo70001.setHelpCaption("Grupo");
        edGrupo70001.setReadOnly(false);
        edGrupo70001.setRequired(false);
        edGrupo70001.setPrompt("Selecione");
        edGrupo70001.setConstraintCheckWhen("cwImmediate");
        edGrupo70001.setConstraintCheckType("ctExpression");
        edGrupo70001.setConstraintFocusOnError(false);
        edGrupo70001.setConstraintEnableUI(true);
        edGrupo70001.setConstraintEnabled(false);
        edGrupo70001.setConstraintFormCheck(true);
        edGrupo70001.setClearOnDelKey(true);
        edGrupo70001.setUseClearButton(false);
        edGrupo70001.setHideClearButtonOnNullValue(false);
        edGrupo70001.setAlign("alLeft");
        FGridPanel2.addChildren(edGrupo70001);
        edGrupo70001.applyProperties();
        addValidatable(edGrupo70001);
    }

    public TFCheckBox edAtivo70001 = new TFCheckBox();

    private void init_edAtivo70001() {
        edAtivo70001.setName("edAtivo70001");
        edAtivo70001.setLeft(101);
        edAtivo70001.setTop(60);
        edAtivo70001.setWidth(16);
        edAtivo70001.setHeight(19);
        edAtivo70001.setHint("TIME ATIVOS E NAO ATIVOS");
        edAtivo70001.setAlign("alLeft");
        edAtivo70001.setFontColor("clWindowText");
        edAtivo70001.setFontSize(-11);
        edAtivo70001.setFontName("Tahoma");
        edAtivo70001.setFontStyle("[]");
        edAtivo70001.setTable(tbTime);
        edAtivo70001.setFieldName("ATIVO");
        edAtivo70001.setHelpCaption("Ativo");
        edAtivo70001.setVerticalAlignment("taAlignTop");
        FGridPanel2.addChildren(edAtivo70001);
        edAtivo70001.applyProperties();
    }

    public TFString edMaxDescServ70001 = new TFString();

    private void init_edMaxDescServ70001() {
        edMaxDescServ70001.setName("edMaxDescServ70001");
        edMaxDescServ70001.setLeft(101);
        edMaxDescServ70001.setTop(79);
        edMaxDescServ70001.setWidth(80);
        edMaxDescServ70001.setHeight(21);
        edMaxDescServ70001.setHint("Max desconto servi\u00E7o");
        edMaxDescServ70001.setTable(tbTime);
        edMaxDescServ70001.setFieldName("MAX_DESC_SERV");
        edMaxDescServ70001.setHelpCaption(" Desconto Servi\u00E7o");
        edMaxDescServ70001.setFlex(false);
        edMaxDescServ70001.setRequired(false);
        edMaxDescServ70001.setConstraintCheckWhen("cwImmediate");
        edMaxDescServ70001.setConstraintCheckType("ctExpression");
        edMaxDescServ70001.setConstraintFocusOnError(false);
        edMaxDescServ70001.setConstraintEnableUI(true);
        edMaxDescServ70001.setConstraintEnabled(false);
        edMaxDescServ70001.setConstraintFormCheck(true);
        edMaxDescServ70001.setCharCase("ccNormal");
        edMaxDescServ70001.setPwd(false);
        edMaxDescServ70001.setMaxlength(0);
        edMaxDescServ70001.setAlign("alLeft");
        edMaxDescServ70001.setFontColor("clWindowText");
        edMaxDescServ70001.setFontSize(-13);
        edMaxDescServ70001.setFontName("Tahoma");
        edMaxDescServ70001.setFontStyle("[]");
        edMaxDescServ70001.setSaveLiteralCharacter(false);
        edMaxDescServ70001.applyProperties();
        FGridPanel2.addChildren(edMaxDescServ70001);
        addValidatable(edMaxDescServ70001);
    }

    public TFTabsheet tabSheetMembros = new TFTabsheet();

    private void init_tabSheetMembros() {
        tabSheetMembros.setName("tabSheetMembros");
        tabSheetMembros.setCaption("Membros");
        tabSheetMembros.setVisible(true);
        tabSheetMembros.setClosable(false);
        pgPrincipal.addChildren(tabSheetMembros);
        tabSheetMembros.applyProperties();
    }

    public TFVBox vBoxTabShhetMembros = new TFVBox();

    private void init_vBoxTabShhetMembros() {
        vBoxTabShhetMembros.setName("vBoxTabShhetMembros");
        vBoxTabShhetMembros.setLeft(0);
        vBoxTabShhetMembros.setTop(0);
        vBoxTabShhetMembros.setWidth(966);
        vBoxTabShhetMembros.setHeight(471);
        vBoxTabShhetMembros.setAlign("alClient");
        vBoxTabShhetMembros.setBorderStyle("stNone");
        vBoxTabShhetMembros.setPaddingTop(5);
        vBoxTabShhetMembros.setPaddingLeft(5);
        vBoxTabShhetMembros.setPaddingRight(5);
        vBoxTabShhetMembros.setPaddingBottom(5);
        vBoxTabShhetMembros.setMarginTop(0);
        vBoxTabShhetMembros.setMarginLeft(0);
        vBoxTabShhetMembros.setMarginRight(0);
        vBoxTabShhetMembros.setMarginBottom(0);
        vBoxTabShhetMembros.setSpacing(1);
        vBoxTabShhetMembros.setFlexVflex("ftTrue");
        vBoxTabShhetMembros.setFlexHflex("ftTrue");
        vBoxTabShhetMembros.setScrollable(false);
        vBoxTabShhetMembros.setBoxShadowConfigHorizontalLength(10);
        vBoxTabShhetMembros.setBoxShadowConfigVerticalLength(10);
        vBoxTabShhetMembros.setBoxShadowConfigBlurRadius(5);
        vBoxTabShhetMembros.setBoxShadowConfigSpreadRadius(0);
        vBoxTabShhetMembros.setBoxShadowConfigShadowColor("clBlack");
        vBoxTabShhetMembros.setBoxShadowConfigOpacity(75);
        tabSheetMembros.addChildren(vBoxTabShhetMembros);
        vBoxTabShhetMembros.applyProperties();
    }

    public TFVBox gbDetalhe43001 = new TFVBox();

    private void init_gbDetalhe43001() {
        gbDetalhe43001.setName("gbDetalhe43001");
        gbDetalhe43001.setLeft(0);
        gbDetalhe43001.setTop(0);
        gbDetalhe43001.setWidth(947);
        gbDetalhe43001.setHeight(376);
        gbDetalhe43001.setAlign("alClient");
        gbDetalhe43001.setBorderStyle("stNone");
        gbDetalhe43001.setPaddingTop(0);
        gbDetalhe43001.setPaddingLeft(0);
        gbDetalhe43001.setPaddingRight(0);
        gbDetalhe43001.setPaddingBottom(0);
        gbDetalhe43001.setMarginTop(0);
        gbDetalhe43001.setMarginLeft(0);
        gbDetalhe43001.setMarginRight(0);
        gbDetalhe43001.setMarginBottom(0);
        gbDetalhe43001.setSpacing(1);
        gbDetalhe43001.setFlexVflex("ftTrue");
        gbDetalhe43001.setFlexHflex("ftTrue");
        gbDetalhe43001.setScrollable(false);
        gbDetalhe43001.setBoxShadowConfigHorizontalLength(10);
        gbDetalhe43001.setBoxShadowConfigVerticalLength(10);
        gbDetalhe43001.setBoxShadowConfigBlurRadius(5);
        gbDetalhe43001.setBoxShadowConfigSpreadRadius(0);
        gbDetalhe43001.setBoxShadowConfigShadowColor("clBlack");
        gbDetalhe43001.setBoxShadowConfigOpacity(75);
        vBoxTabShhetMembros.addChildren(gbDetalhe43001);
        gbDetalhe43001.applyProperties();
    }

    public TFGridPanel gridPanelDetailEmpty43001 = new TFGridPanel();

    private void init_gridPanelDetailEmpty43001() {
        gridPanelDetailEmpty43001.setName("gridPanelDetailEmpty43001");
        gridPanelDetailEmpty43001.setLeft(0);
        gridPanelDetailEmpty43001.setTop(0);
        gridPanelDetailEmpty43001.setWidth(0);
        gridPanelDetailEmpty43001.setHeight(26);
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(50.000000000000000000);
        gridPanelDetailEmpty43001.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(50.000000000000000000);
        gridPanelDetailEmpty43001.getColumnCollection().add(item1);
        TFGridPanelRow item2 = new TFGridPanelRow();
        item2.setSizeStyle("ssAbsolute");
        item2.setValue(21.000000000000000000);
        gridPanelDetailEmpty43001.getRowCollection().add(item2);
        gridPanelDetailEmpty43001.setVisible(false);
        gridPanelDetailEmpty43001.setFlexVflex("ftTrue");
        gridPanelDetailEmpty43001.setFlexHflex("ftTrue");
        gridPanelDetailEmpty43001.setAllRowFlex(false);
        gridPanelDetailEmpty43001.setColumnTabOrder(false);
        gbDetalhe43001.addChildren(gridPanelDetailEmpty43001);
        gridPanelDetailEmpty43001.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(27);
        FHBox2.setWidth(951);
        FHBox2.setHeight(67);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        gbDetalhe43001.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(440);
        FVBox3.setHeight(47);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(41);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Empresa");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox3.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cbbEmpresa = new TFCombo();

    private void init_cbbEmpresa() {
        cbbEmpresa.setName("cbbEmpresa");
        cbbEmpresa.setLeft(0);
        cbbEmpresa.setTop(14);
        cbbEmpresa.setWidth(420);
        cbbEmpresa.setHeight(21);
        cbbEmpresa.setLookupTable(tbEmpresas);
        cbbEmpresa.setLookupKey("COD_EMPRESA");
        cbbEmpresa.setLookupDesc("NOME");
        cbbEmpresa.setFlex(true);
        cbbEmpresa.setReadOnly(true);
        cbbEmpresa.setRequired(false);
        cbbEmpresa.setPrompt("Selecione");
        cbbEmpresa.setConstraintCheckWhen("cwImmediate");
        cbbEmpresa.setConstraintCheckType("ctExpression");
        cbbEmpresa.setConstraintFocusOnError(false);
        cbbEmpresa.setConstraintEnableUI(true);
        cbbEmpresa.setConstraintEnabled(false);
        cbbEmpresa.setConstraintFormCheck(true);
        cbbEmpresa.setClearOnDelKey(true);
        cbbEmpresa.setUseClearButton(true);
        cbbEmpresa.setHideClearButtonOnNullValue(true);
        cbbEmpresa.setEnabled(false);
        cbbEmpresa.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbEmpresaChange(event);
            processarFlow("FrmTimeMembros", "cbbEmpresa", "OnChange");
        });
        cbbEmpresa.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbEmpresaClearClick(event);
            processarFlow("FrmTimeMembros", "cbbEmpresa", "OnClearClick");
        });
        FVBox3.addChildren(cbbEmpresa);
        cbbEmpresa.applyProperties();
        addValidatable(cbbEmpresa);
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(440);
        FVBox4.setTop(0);
        FVBox4.setWidth(440);
        FVBox4.setHeight(47);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(35);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Fun\u00E7\u00E3o");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox4.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cbbEmpresasFuncoes = new TFCombo();

    private void init_cbbEmpresasFuncoes() {
        cbbEmpresasFuncoes.setName("cbbEmpresasFuncoes");
        cbbEmpresasFuncoes.setLeft(0);
        cbbEmpresasFuncoes.setTop(14);
        cbbEmpresasFuncoes.setWidth(420);
        cbbEmpresasFuncoes.setHeight(21);
        cbbEmpresasFuncoes.setLookupTable(tbEmpresasFuncoes);
        cbbEmpresasFuncoes.setLookupKey("COD_FUNCAO");
        cbbEmpresasFuncoes.setLookupDesc("DESCRICAO");
        cbbEmpresasFuncoes.setFlex(true);
        cbbEmpresasFuncoes.setReadOnly(true);
        cbbEmpresasFuncoes.setRequired(false);
        cbbEmpresasFuncoes.setPrompt("Selecione");
        cbbEmpresasFuncoes.setConstraintCheckWhen("cwImmediate");
        cbbEmpresasFuncoes.setConstraintCheckType("ctExpression");
        cbbEmpresasFuncoes.setConstraintFocusOnError(false);
        cbbEmpresasFuncoes.setConstraintEnableUI(true);
        cbbEmpresasFuncoes.setConstraintEnabled(false);
        cbbEmpresasFuncoes.setConstraintFormCheck(true);
        cbbEmpresasFuncoes.setClearOnDelKey(true);
        cbbEmpresasFuncoes.setUseClearButton(true);
        cbbEmpresasFuncoes.setHideClearButtonOnNullValue(true);
        cbbEmpresasFuncoes.setEnabled(false);
        cbbEmpresasFuncoes.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbEmpresasFuncoesChange(event);
            processarFlow("FrmTimeMembros", "cbbEmpresasFuncoes", "OnChange");
        });
        cbbEmpresasFuncoes.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbEmpresasFuncoesClearClick(event);
            processarFlow("FrmTimeMembros", "cbbEmpresasFuncoes", "OnClearClick");
        });
        FVBox4.addChildren(cbbEmpresasFuncoes);
        cbbEmpresasFuncoes.applyProperties();
        addValidatable(cbbEmpresasFuncoes);
    }

    public TFDualList DualListMembros = new TFDualList();

    private void init_DualListMembros() {
        DualListMembros.setName("DualListMembros");
        DualListMembros.setLeft(0);
        DualListMembros.setTop(95);
        DualListMembros.setWidth(951);
        DualListMembros.setHeight(272);
        DualListMembros.setCaption("Selecionados");
        DualListMembros.setLookupCaption("Dispon\u00EDveis");
        DualListMembros.setCaptionHiperLink(false);
        DualListMembros.setLookupCaptionHiperLink(false);
        DualListMembros.setFlexVflex("ftTrue");
        DualListMembros.setFlexHflex("ftTrue");
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_COMPLETO");
        item0.setTitleCaption("Nome");
        item0.setWidth(209);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        DualListMembros.getLookupColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("FUNCAO");
        item1.setTitleCaption("Fun\u00E7\u00E3o");
        item1.setWidth(200);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        DualListMembros.getLookupColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("NOME_COMPLETO");
        item2.setTitleCaption("Nome");
        item2.setWidth(221);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        DualListMembros.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("FUNCAO");
        item3.setTitleCaption("Fun\u00E7\u00E3o");
        item3.setWidth(213);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        DualListMembros.getColumns().add(item3);
        DualListMembros.setTable(tbTimeMembro);
        DualListMembros.setLookupTable(tbTimeAgentesDisponiveis);
        DualListMembros.setMultiSelection(true);
        DualListMembros.setPaging(false);
        DualListMembros.setAlign("alClient");
        DualListMembros.setEnabled(false);
        DualListMembros.setGroupingEnabled(false);
        DualListMembros.setGroupingExpanded(false);
        DualListMembros.setGroupingShowFooter(false);
        DualListMembros.setGroupingLookupEnabled(false);
        DualListMembros.setGroupingLookupExpanded(false);
        DualListMembros.setGroupingLookupShowFooter(false);
        gbDetalhe43001.addChildren(DualListMembros);
        DualListMembros.applyProperties();
    }

    public TFTabsheet tabHorarios = new TFTabsheet();

    private void init_tabHorarios() {
        tabHorarios.setName("tabHorarios");
        tabHorarios.setCaption("Horarios");
        tabHorarios.setVisible(true);
        tabHorarios.setClosable(false);
        pgPrincipal.addChildren(tabHorarios);
        tabHorarios.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(966);
        FVBox5.setHeight(471);
        FVBox5.setAlign("alClient");
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        tabHorarios.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFGroupbox grpTemplate = new TFGroupbox();

    private void init_grpTemplate() {
        grpTemplate.setName("grpTemplate");
        grpTemplate.setLeft(0);
        grpTemplate.setTop(0);
        grpTemplate.setWidth(657);
        grpTemplate.setHeight(317);
        grpTemplate.setAlign("alClient");
        grpTemplate.setCaption("Membro X Template");
        grpTemplate.setFontColor("clWindowText");
        grpTemplate.setFontSize(-11);
        grpTemplate.setFontName("Tahoma");
        grpTemplate.setFontStyle("[]");
        grpTemplate.setFlexVflex("ftTrue");
        grpTemplate.setFlexHflex("ftTrue");
        grpTemplate.setScrollable(false);
        grpTemplate.setClosable(false);
        grpTemplate.setClosed(false);
        grpTemplate.setOrient("coHorizontal");
        grpTemplate.setStyle("grp3D");
        grpTemplate.setHeaderImageId(0);
        FVBox5.addChildren(grpTemplate);
        grpTemplate.applyProperties();
    }

    public TFVBox vboxModeloGrupoxModelo = new TFVBox();

    private void init_vboxModeloGrupoxModelo() {
        vboxModeloGrupoxModelo.setName("vboxModeloGrupoxModelo");
        vboxModeloGrupoxModelo.setLeft(2);
        vboxModeloGrupoxModelo.setTop(15);
        vboxModeloGrupoxModelo.setWidth(653);
        vboxModeloGrupoxModelo.setHeight(300);
        vboxModeloGrupoxModelo.setAlign("alClient");
        vboxModeloGrupoxModelo.setBorderStyle("stNone");
        vboxModeloGrupoxModelo.setPaddingTop(5);
        vboxModeloGrupoxModelo.setPaddingLeft(5);
        vboxModeloGrupoxModelo.setPaddingRight(5);
        vboxModeloGrupoxModelo.setPaddingBottom(5);
        vboxModeloGrupoxModelo.setMarginTop(0);
        vboxModeloGrupoxModelo.setMarginLeft(0);
        vboxModeloGrupoxModelo.setMarginRight(0);
        vboxModeloGrupoxModelo.setMarginBottom(0);
        vboxModeloGrupoxModelo.setSpacing(5);
        vboxModeloGrupoxModelo.setFlexVflex("ftTrue");
        vboxModeloGrupoxModelo.setFlexHflex("ftTrue");
        vboxModeloGrupoxModelo.setScrollable(false);
        vboxModeloGrupoxModelo.setBoxShadowConfigHorizontalLength(10);
        vboxModeloGrupoxModelo.setBoxShadowConfigVerticalLength(10);
        vboxModeloGrupoxModelo.setBoxShadowConfigBlurRadius(5);
        vboxModeloGrupoxModelo.setBoxShadowConfigSpreadRadius(0);
        vboxModeloGrupoxModelo.setBoxShadowConfigShadowColor("clBlack");
        vboxModeloGrupoxModelo.setBoxShadowConfigOpacity(75);
        grpTemplate.addChildren(vboxModeloGrupoxModelo);
        vboxModeloGrupoxModelo.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(385);
        FHBox7.setHeight(37);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vboxModeloGrupoxModelo.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFCombo cmbTemplate = new TFCombo();

    private void init_cmbTemplate() {
        cmbTemplate.setName("cmbTemplate");
        cmbTemplate.setLeft(0);
        cmbTemplate.setTop(0);
        cmbTemplate.setWidth(200);
        cmbTemplate.setHeight(21);
        cmbTemplate.setHint("Templates Horarios");
        cmbTemplate.setLookupTable(tbTimeTemplate);
        cmbTemplate.setLookupKey("ID_TEMPLATE");
        cmbTemplate.setLookupDesc("DESCRICAO_INITCAP");
        cmbTemplate.setFlex(true);
        cmbTemplate.setHelpCaption("Tipo");
        cmbTemplate.setReadOnly(false);
        cmbTemplate.setRequired(true);
        cmbTemplate.setPrompt("Template Horario");
        cmbTemplate.setConstraintExpression("value is null");
        cmbTemplate.setConstraintCheckWhen("cwImmediate");
        cmbTemplate.setConstraintCheckType("ctExpression");
        cmbTemplate.setConstraintFocusOnError(false);
        cmbTemplate.setConstraintGroupName("grpTbpertencegrupo");
        cmbTemplate.setConstraintEnableUI(false);
        cmbTemplate.setConstraintEnabled(false);
        cmbTemplate.setConstraintFormCheck(false);
        cmbTemplate.setClearOnDelKey(false);
        cmbTemplate.setUseClearButton(false);
        cmbTemplate.setHideClearButtonOnNullValue(false);
        cmbTemplate.setAlign("alLeft");
        cmbTemplate.setEnabled(false);
        FHBox7.addChildren(cmbTemplate);
        cmbTemplate.applyProperties();
        addValidatable(cmbTemplate);
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(200);
        FVBox6.setTop(0);
        FVBox6.setWidth(41);
        FVBox6.setHeight(34);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(4);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftFalse");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFButton btnAlterarTemplate = new TFButton();

    private void init_btnAlterarTemplate() {
        btnAlterarTemplate.setName("btnAlterarTemplate");
        btnAlterarTemplate.setLeft(0);
        btnAlterarTemplate.setTop(0);
        btnAlterarTemplate.setWidth(34);
        btnAlterarTemplate.setHeight(27);
        btnAlterarTemplate.setHint("incluir e editar template");
        btnAlterarTemplate.setFontColor("clWindowText");
        btnAlterarTemplate.setFontSize(-11);
        btnAlterarTemplate.setFontName("Tahoma");
        btnAlterarTemplate.setFontStyle("[]");
        btnAlterarTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarTemplateClick(event);
            processarFlow("FrmTimeMembros", "btnAlterarTemplate", "OnClick");
        });
        btnAlterarTemplate.setImageId(430035);
        btnAlterarTemplate.setColor("clBtnFace");
        btnAlterarTemplate.setAccess(false);
        btnAlterarTemplate.setIconReverseDirection(false);
        FVBox6.addChildren(btnAlterarTemplate);
        btnAlterarTemplate.applyProperties();
    }

    public TFHBox hboxModeloGridModeloECruzamento = new TFHBox();

    private void init_hboxModeloGridModeloECruzamento() {
        hboxModeloGridModeloECruzamento.setName("hboxModeloGridModeloECruzamento");
        hboxModeloGridModeloECruzamento.setLeft(0);
        hboxModeloGridModeloECruzamento.setTop(38);
        hboxModeloGridModeloECruzamento.setWidth(567);
        hboxModeloGridModeloECruzamento.setHeight(229);
        hboxModeloGridModeloECruzamento.setBorderStyle("stNone");
        hboxModeloGridModeloECruzamento.setPaddingTop(0);
        hboxModeloGridModeloECruzamento.setPaddingLeft(0);
        hboxModeloGridModeloECruzamento.setPaddingRight(0);
        hboxModeloGridModeloECruzamento.setPaddingBottom(0);
        hboxModeloGridModeloECruzamento.setMarginTop(0);
        hboxModeloGridModeloECruzamento.setMarginLeft(0);
        hboxModeloGridModeloECruzamento.setMarginRight(0);
        hboxModeloGridModeloECruzamento.setMarginBottom(0);
        hboxModeloGridModeloECruzamento.setSpacing(1);
        hboxModeloGridModeloECruzamento.setFlexVflex("ftTrue");
        hboxModeloGridModeloECruzamento.setFlexHflex("ftTrue");
        hboxModeloGridModeloECruzamento.setScrollable(false);
        hboxModeloGridModeloECruzamento.setBoxShadowConfigHorizontalLength(10);
        hboxModeloGridModeloECruzamento.setBoxShadowConfigVerticalLength(10);
        hboxModeloGridModeloECruzamento.setBoxShadowConfigBlurRadius(5);
        hboxModeloGridModeloECruzamento.setBoxShadowConfigSpreadRadius(0);
        hboxModeloGridModeloECruzamento.setBoxShadowConfigShadowColor("clBlack");
        hboxModeloGridModeloECruzamento.setBoxShadowConfigOpacity(75);
        hboxModeloGridModeloECruzamento.setVAlign("tvTop");
        vboxModeloGrupoxModelo.addChildren(hboxModeloGridModeloECruzamento);
        hboxModeloGridModeloECruzamento.applyProperties();
    }

    public TFVBox vboxModelogridModelo = new TFVBox();

    private void init_vboxModelogridModelo() {
        vboxModelogridModelo.setName("vboxModelogridModelo");
        vboxModelogridModelo.setLeft(0);
        vboxModelogridModelo.setTop(0);
        vboxModelogridModelo.setWidth(249);
        vboxModelogridModelo.setHeight(223);
        vboxModelogridModelo.setAlign("alClient");
        vboxModelogridModelo.setBorderStyle("stNone");
        vboxModelogridModelo.setPaddingTop(0);
        vboxModelogridModelo.setPaddingLeft(0);
        vboxModelogridModelo.setPaddingRight(0);
        vboxModelogridModelo.setPaddingBottom(0);
        vboxModelogridModelo.setMarginTop(0);
        vboxModelogridModelo.setMarginLeft(0);
        vboxModelogridModelo.setMarginRight(0);
        vboxModelogridModelo.setMarginBottom(0);
        vboxModelogridModelo.setSpacing(1);
        vboxModelogridModelo.setFlexVflex("ftTrue");
        vboxModelogridModelo.setFlexHflex("ftTrue");
        vboxModelogridModelo.setScrollable(false);
        vboxModelogridModelo.setBoxShadowConfigHorizontalLength(10);
        vboxModelogridModelo.setBoxShadowConfigVerticalLength(10);
        vboxModelogridModelo.setBoxShadowConfigBlurRadius(5);
        vboxModelogridModelo.setBoxShadowConfigSpreadRadius(0);
        vboxModelogridModelo.setBoxShadowConfigShadowColor("clBlack");
        vboxModelogridModelo.setBoxShadowConfigOpacity(75);
        hboxModeloGridModeloECruzamento.addChildren(vboxModelogridModelo);
        vboxModelogridModelo.applyProperties();
    }

    public TFHBox vboxModelolblSelecioneModelo = new TFHBox();

    private void init_vboxModelolblSelecioneModelo() {
        vboxModelolblSelecioneModelo.setName("vboxModelolblSelecioneModelo");
        vboxModelolblSelecioneModelo.setLeft(0);
        vboxModelolblSelecioneModelo.setTop(0);
        vboxModelolblSelecioneModelo.setWidth(241);
        vboxModelolblSelecioneModelo.setHeight(34);
        vboxModelolblSelecioneModelo.setBorderStyle("stSingleLine");
        vboxModelolblSelecioneModelo.setPaddingTop(5);
        vboxModelolblSelecioneModelo.setPaddingLeft(5);
        vboxModelolblSelecioneModelo.setPaddingRight(0);
        vboxModelolblSelecioneModelo.setPaddingBottom(5);
        vboxModelolblSelecioneModelo.setMarginTop(0);
        vboxModelolblSelecioneModelo.setMarginLeft(0);
        vboxModelolblSelecioneModelo.setMarginRight(0);
        vboxModelolblSelecioneModelo.setMarginBottom(0);
        vboxModelolblSelecioneModelo.setSpacing(1);
        vboxModelolblSelecioneModelo.setFlexVflex("ftFalse");
        vboxModelolblSelecioneModelo.setFlexHflex("ftTrue");
        vboxModelolblSelecioneModelo.setScrollable(false);
        vboxModelolblSelecioneModelo.setBoxShadowConfigHorizontalLength(10);
        vboxModelolblSelecioneModelo.setBoxShadowConfigVerticalLength(10);
        vboxModelolblSelecioneModelo.setBoxShadowConfigBlurRadius(5);
        vboxModelolblSelecioneModelo.setBoxShadowConfigSpreadRadius(0);
        vboxModelolblSelecioneModelo.setBoxShadowConfigShadowColor("clBlack");
        vboxModelolblSelecioneModelo.setBoxShadowConfigOpacity(75);
        vboxModelolblSelecioneModelo.setVAlign("tvTop");
        vboxModelogridModelo.addChildren(vboxModelolblSelecioneModelo);
        vboxModelolblSelecioneModelo.applyProperties();
    }

    public TFLabel labelModeloSelecioneModelo = new TFLabel();

    private void init_labelModeloSelecioneModelo() {
        labelModeloSelecioneModelo.setName("labelModeloSelecioneModelo");
        labelModeloSelecioneModelo.setLeft(0);
        labelModeloSelecioneModelo.setTop(0);
        labelModeloSelecioneModelo.setWidth(188);
        labelModeloSelecioneModelo.setHeight(23);
        labelModeloSelecioneModelo.setCaption("Membro sem template");
        labelModeloSelecioneModelo.setFontColor("clGray");
        labelModeloSelecioneModelo.setFontSize(-19);
        labelModeloSelecioneModelo.setFontName("Tahoma");
        labelModeloSelecioneModelo.setFontStyle("[]");
        labelModeloSelecioneModelo.setVerticalAlignment("taVerticalCenter");
        labelModeloSelecioneModelo.setWordBreak(false);
        vboxModelolblSelecioneModelo.addChildren(labelModeloSelecioneModelo);
        labelModeloSelecioneModelo.applyProperties();
    }

    public TFGrid gridMembrosTemplateDisponivel = new TFGrid();

    private void init_gridMembrosTemplateDisponivel() {
        gridMembrosTemplateDisponivel.setName("gridMembrosTemplateDisponivel");
        gridMembrosTemplateDisponivel.setLeft(0);
        gridMembrosTemplateDisponivel.setTop(35);
        gridMembrosTemplateDisponivel.setWidth(244);
        gridMembrosTemplateDisponivel.setHeight(161);
        gridMembrosTemplateDisponivel.setEnabled(false);
        gridMembrosTemplateDisponivel.setTable(tbTimeMembroTemplateDisponivel);
        gridMembrosTemplateDisponivel.setFlexVflex("ftTrue");
        gridMembrosTemplateDisponivel.setFlexHflex("ftTrue");
        gridMembrosTemplateDisponivel.setPagingEnabled(true);
        gridMembrosTemplateDisponivel.setFrozenColumns(0);
        gridMembrosTemplateDisponivel.setShowFooter(false);
        gridMembrosTemplateDisponivel.setShowHeader(true);
        gridMembrosTemplateDisponivel.setMultiSelection(false);
        gridMembrosTemplateDisponivel.setGroupingEnabled(false);
        gridMembrosTemplateDisponivel.setGroupingExpanded(false);
        gridMembrosTemplateDisponivel.setGroupingShowFooter(false);
        gridMembrosTemplateDisponivel.setCrosstabEnabled(false);
        gridMembrosTemplateDisponivel.setCrosstabGroupType("cgtConcat");
        gridMembrosTemplateDisponivel.setEditionEnabled(false);
        gridMembrosTemplateDisponivel.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_COMPLETO");
        item0.setTitleCaption("Nome");
        item0.setWidth(90);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridMembrosTemplateDisponivelColumns0DoubleClick(event);
            processarFlow("FrmTimeMembros", "item0", "OnDoubleClick");
        });
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridMembrosTemplateDisponivel.getColumns().add(item0);
        vboxModelogridModelo.addChildren(gridMembrosTemplateDisponivel);
        gridMembrosTemplateDisponivel.applyProperties();
    }

    public TFVBox vBoxModeloAgrupaBotoes = new TFVBox();

    private void init_vBoxModeloAgrupaBotoes() {
        vBoxModeloAgrupaBotoes.setName("vBoxModeloAgrupaBotoes");
        vBoxModeloAgrupaBotoes.setLeft(249);
        vBoxModeloAgrupaBotoes.setTop(0);
        vBoxModeloAgrupaBotoes.setWidth(58);
        vBoxModeloAgrupaBotoes.setHeight(200);
        vBoxModeloAgrupaBotoes.setBorderStyle("stNone");
        vBoxModeloAgrupaBotoes.setPaddingTop(0);
        vBoxModeloAgrupaBotoes.setPaddingLeft(0);
        vBoxModeloAgrupaBotoes.setPaddingRight(0);
        vBoxModeloAgrupaBotoes.setPaddingBottom(0);
        vBoxModeloAgrupaBotoes.setMarginTop(0);
        vBoxModeloAgrupaBotoes.setMarginLeft(5);
        vBoxModeloAgrupaBotoes.setMarginRight(5);
        vBoxModeloAgrupaBotoes.setMarginBottom(0);
        vBoxModeloAgrupaBotoes.setSpacing(1);
        vBoxModeloAgrupaBotoes.setFlexVflex("ftTrue");
        vBoxModeloAgrupaBotoes.setFlexHflex("ftMin");
        vBoxModeloAgrupaBotoes.setScrollable(false);
        vBoxModeloAgrupaBotoes.setBoxShadowConfigHorizontalLength(10);
        vBoxModeloAgrupaBotoes.setBoxShadowConfigVerticalLength(10);
        vBoxModeloAgrupaBotoes.setBoxShadowConfigBlurRadius(5);
        vBoxModeloAgrupaBotoes.setBoxShadowConfigSpreadRadius(0);
        vBoxModeloAgrupaBotoes.setBoxShadowConfigShadowColor("clBlack");
        vBoxModeloAgrupaBotoes.setBoxShadowConfigOpacity(75);
        hboxModeloGridModeloECruzamento.addChildren(vBoxModeloAgrupaBotoes);
        vBoxModeloAgrupaBotoes.applyProperties();
    }

    public TFHBox hboxModeloCrtl1 = new TFHBox();

    private void init_hboxModeloCrtl1() {
        hboxModeloCrtl1.setName("hboxModeloCrtl1");
        hboxModeloCrtl1.setLeft(0);
        hboxModeloCrtl1.setTop(0);
        hboxModeloCrtl1.setWidth(44);
        hboxModeloCrtl1.setHeight(40);
        hboxModeloCrtl1.setBorderStyle("stNone");
        hboxModeloCrtl1.setPaddingTop(0);
        hboxModeloCrtl1.setPaddingLeft(0);
        hboxModeloCrtl1.setPaddingRight(0);
        hboxModeloCrtl1.setPaddingBottom(0);
        hboxModeloCrtl1.setMarginTop(0);
        hboxModeloCrtl1.setMarginLeft(0);
        hboxModeloCrtl1.setMarginRight(0);
        hboxModeloCrtl1.setMarginBottom(0);
        hboxModeloCrtl1.setSpacing(1);
        hboxModeloCrtl1.setFlexVflex("ftTrue");
        hboxModeloCrtl1.setFlexHflex("ftTrue");
        hboxModeloCrtl1.setScrollable(false);
        hboxModeloCrtl1.setBoxShadowConfigHorizontalLength(10);
        hboxModeloCrtl1.setBoxShadowConfigVerticalLength(10);
        hboxModeloCrtl1.setBoxShadowConfigBlurRadius(5);
        hboxModeloCrtl1.setBoxShadowConfigSpreadRadius(0);
        hboxModeloCrtl1.setBoxShadowConfigShadowColor("clBlack");
        hboxModeloCrtl1.setBoxShadowConfigOpacity(75);
        hboxModeloCrtl1.setVAlign("tvTop");
        vBoxModeloAgrupaBotoes.addChildren(hboxModeloCrtl1);
        hboxModeloCrtl1.applyProperties();
    }

    public TFButton btnHorariosAdicionarTemplate = new TFButton();

    private void init_btnHorariosAdicionarTemplate() {
        btnHorariosAdicionarTemplate.setName("btnHorariosAdicionarTemplate");
        btnHorariosAdicionarTemplate.setLeft(0);
        btnHorariosAdicionarTemplate.setTop(41);
        btnHorariosAdicionarTemplate.setWidth(48);
        btnHorariosAdicionarTemplate.setHeight(38);
        btnHorariosAdicionarTemplate.setEnabled(false);
        btnHorariosAdicionarTemplate.setFontColor("clWindowText");
        btnHorariosAdicionarTemplate.setFontSize(-11);
        btnHorariosAdicionarTemplate.setFontName("Tahoma");
        btnHorariosAdicionarTemplate.setFontStyle("[]");
        btnHorariosAdicionarTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnHorariosAdicionarTemplateClick(event);
            processarFlow("FrmTimeMembros", "btnHorariosAdicionarTemplate", "OnClick");
        });
        btnHorariosAdicionarTemplate.setImageId(430034);
        btnHorariosAdicionarTemplate.setColor("clBtnFace");
        btnHorariosAdicionarTemplate.setAccess(false);
        btnHorariosAdicionarTemplate.setIconReverseDirection(false);
        vBoxModeloAgrupaBotoes.addChildren(btnHorariosAdicionarTemplate);
        btnHorariosAdicionarTemplate.applyProperties();
    }

    public TFHBox hboxModeloCrtl3 = new TFHBox();

    private void init_hboxModeloCrtl3() {
        hboxModeloCrtl3.setName("hboxModeloCrtl3");
        hboxModeloCrtl3.setLeft(0);
        hboxModeloCrtl3.setTop(80);
        hboxModeloCrtl3.setWidth(24);
        hboxModeloCrtl3.setHeight(8);
        hboxModeloCrtl3.setBorderStyle("stNone");
        hboxModeloCrtl3.setPaddingTop(0);
        hboxModeloCrtl3.setPaddingLeft(0);
        hboxModeloCrtl3.setPaddingRight(0);
        hboxModeloCrtl3.setPaddingBottom(0);
        hboxModeloCrtl3.setMarginTop(0);
        hboxModeloCrtl3.setMarginLeft(0);
        hboxModeloCrtl3.setMarginRight(0);
        hboxModeloCrtl3.setMarginBottom(0);
        hboxModeloCrtl3.setSpacing(1);
        hboxModeloCrtl3.setFlexVflex("ftFalse");
        hboxModeloCrtl3.setFlexHflex("ftFalse");
        hboxModeloCrtl3.setScrollable(false);
        hboxModeloCrtl3.setBoxShadowConfigHorizontalLength(10);
        hboxModeloCrtl3.setBoxShadowConfigVerticalLength(10);
        hboxModeloCrtl3.setBoxShadowConfigBlurRadius(5);
        hboxModeloCrtl3.setBoxShadowConfigSpreadRadius(0);
        hboxModeloCrtl3.setBoxShadowConfigShadowColor("clBlack");
        hboxModeloCrtl3.setBoxShadowConfigOpacity(75);
        hboxModeloCrtl3.setVAlign("tvTop");
        vBoxModeloAgrupaBotoes.addChildren(hboxModeloCrtl3);
        hboxModeloCrtl3.applyProperties();
    }

    public TFButton btnHorariosRemoverTemplate = new TFButton();

    private void init_btnHorariosRemoverTemplate() {
        btnHorariosRemoverTemplate.setName("btnHorariosRemoverTemplate");
        btnHorariosRemoverTemplate.setLeft(0);
        btnHorariosRemoverTemplate.setTop(89);
        btnHorariosRemoverTemplate.setWidth(48);
        btnHorariosRemoverTemplate.setHeight(38);
        btnHorariosRemoverTemplate.setEnabled(false);
        btnHorariosRemoverTemplate.setFontColor("clWindowText");
        btnHorariosRemoverTemplate.setFontSize(-11);
        btnHorariosRemoverTemplate.setFontName("Tahoma");
        btnHorariosRemoverTemplate.setFontStyle("[]");
        btnHorariosRemoverTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnHorariosRemoverTemplateClick(event);
            processarFlow("FrmTimeMembros", "btnHorariosRemoverTemplate", "OnClick");
        });
        btnHorariosRemoverTemplate.setImageId(430033);
        btnHorariosRemoverTemplate.setColor("clBtnFace");
        btnHorariosRemoverTemplate.setAccess(false);
        btnHorariosRemoverTemplate.setIconReverseDirection(false);
        vBoxModeloAgrupaBotoes.addChildren(btnHorariosRemoverTemplate);
        btnHorariosRemoverTemplate.applyProperties();
    }

    public TFHBox hboxModeloCrtl2 = new TFHBox();

    private void init_hboxModeloCrtl2() {
        hboxModeloCrtl2.setName("hboxModeloCrtl2");
        hboxModeloCrtl2.setLeft(0);
        hboxModeloCrtl2.setTop(128);
        hboxModeloCrtl2.setWidth(48);
        hboxModeloCrtl2.setHeight(50);
        hboxModeloCrtl2.setBorderStyle("stNone");
        hboxModeloCrtl2.setPaddingTop(0);
        hboxModeloCrtl2.setPaddingLeft(0);
        hboxModeloCrtl2.setPaddingRight(0);
        hboxModeloCrtl2.setPaddingBottom(0);
        hboxModeloCrtl2.setMarginTop(0);
        hboxModeloCrtl2.setMarginLeft(0);
        hboxModeloCrtl2.setMarginRight(0);
        hboxModeloCrtl2.setMarginBottom(0);
        hboxModeloCrtl2.setSpacing(1);
        hboxModeloCrtl2.setFlexVflex("ftTrue");
        hboxModeloCrtl2.setFlexHflex("ftTrue");
        hboxModeloCrtl2.setScrollable(false);
        hboxModeloCrtl2.setBoxShadowConfigHorizontalLength(10);
        hboxModeloCrtl2.setBoxShadowConfigVerticalLength(10);
        hboxModeloCrtl2.setBoxShadowConfigBlurRadius(5);
        hboxModeloCrtl2.setBoxShadowConfigSpreadRadius(0);
        hboxModeloCrtl2.setBoxShadowConfigShadowColor("clBlack");
        hboxModeloCrtl2.setBoxShadowConfigOpacity(75);
        hboxModeloCrtl2.setVAlign("tvTop");
        vBoxModeloAgrupaBotoes.addChildren(hboxModeloCrtl2);
        hboxModeloCrtl2.applyProperties();
    }

    public TFVBox vboxModeloGridCruzamento = new TFVBox();

    private void init_vboxModeloGridCruzamento() {
        vboxModeloGridCruzamento.setName("vboxModeloGridCruzamento");
        vboxModeloGridCruzamento.setLeft(307);
        vboxModeloGridCruzamento.setTop(0);
        vboxModeloGridCruzamento.setWidth(249);
        vboxModeloGridCruzamento.setHeight(223);
        vboxModeloGridCruzamento.setAlign("alClient");
        vboxModeloGridCruzamento.setBorderStyle("stNone");
        vboxModeloGridCruzamento.setPaddingTop(0);
        vboxModeloGridCruzamento.setPaddingLeft(0);
        vboxModeloGridCruzamento.setPaddingRight(0);
        vboxModeloGridCruzamento.setPaddingBottom(0);
        vboxModeloGridCruzamento.setMarginTop(0);
        vboxModeloGridCruzamento.setMarginLeft(0);
        vboxModeloGridCruzamento.setMarginRight(0);
        vboxModeloGridCruzamento.setMarginBottom(0);
        vboxModeloGridCruzamento.setSpacing(1);
        vboxModeloGridCruzamento.setFlexVflex("ftTrue");
        vboxModeloGridCruzamento.setFlexHflex("ftTrue");
        vboxModeloGridCruzamento.setScrollable(false);
        vboxModeloGridCruzamento.setBoxShadowConfigHorizontalLength(10);
        vboxModeloGridCruzamento.setBoxShadowConfigVerticalLength(10);
        vboxModeloGridCruzamento.setBoxShadowConfigBlurRadius(5);
        vboxModeloGridCruzamento.setBoxShadowConfigSpreadRadius(0);
        vboxModeloGridCruzamento.setBoxShadowConfigShadowColor("clBlack");
        vboxModeloGridCruzamento.setBoxShadowConfigOpacity(75);
        hboxModeloGridModeloECruzamento.addChildren(vboxModeloGridCruzamento);
        vboxModeloGridCruzamento.applyProperties();
    }

    public TFHBox vboxModeloCruzamentos = new TFHBox();

    private void init_vboxModeloCruzamentos() {
        vboxModeloCruzamentos.setName("vboxModeloCruzamentos");
        vboxModeloCruzamentos.setLeft(0);
        vboxModeloCruzamentos.setTop(0);
        vboxModeloCruzamentos.setWidth(241);
        vboxModeloCruzamentos.setHeight(34);
        vboxModeloCruzamentos.setBorderStyle("stSingleLine");
        vboxModeloCruzamentos.setPaddingTop(5);
        vboxModeloCruzamentos.setPaddingLeft(5);
        vboxModeloCruzamentos.setPaddingRight(0);
        vboxModeloCruzamentos.setPaddingBottom(5);
        vboxModeloCruzamentos.setMarginTop(0);
        vboxModeloCruzamentos.setMarginLeft(0);
        vboxModeloCruzamentos.setMarginRight(0);
        vboxModeloCruzamentos.setMarginBottom(0);
        vboxModeloCruzamentos.setSpacing(1);
        vboxModeloCruzamentos.setFlexVflex("ftFalse");
        vboxModeloCruzamentos.setFlexHflex("ftTrue");
        vboxModeloCruzamentos.setScrollable(false);
        vboxModeloCruzamentos.setBoxShadowConfigHorizontalLength(10);
        vboxModeloCruzamentos.setBoxShadowConfigVerticalLength(10);
        vboxModeloCruzamentos.setBoxShadowConfigBlurRadius(5);
        vboxModeloCruzamentos.setBoxShadowConfigSpreadRadius(0);
        vboxModeloCruzamentos.setBoxShadowConfigShadowColor("clBlack");
        vboxModeloCruzamentos.setBoxShadowConfigOpacity(75);
        vboxModeloCruzamentos.setVAlign("tvTop");
        vboxModeloGridCruzamento.addChildren(vboxModeloCruzamentos);
        vboxModeloCruzamentos.applyProperties();
    }

    public TFLabel FLabel14 = new TFLabel();

    private void init_FLabel14() {
        FLabel14.setName("FLabel14");
        FLabel14.setLeft(0);
        FLabel14.setTop(0);
        FLabel14.setWidth(197);
        FLabel14.setHeight(23);
        FLabel14.setCaption("Membros com template");
        FLabel14.setFontColor("clGray");
        FLabel14.setFontSize(-19);
        FLabel14.setFontName("Tahoma");
        FLabel14.setFontStyle("[]");
        FLabel14.setVerticalAlignment("taVerticalCenter");
        FLabel14.setWordBreak(false);
        vboxModeloCruzamentos.addChildren(FLabel14);
        FLabel14.applyProperties();
    }

    public TFGrid gridMembrosTemplateCruzado = new TFGrid();

    private void init_gridMembrosTemplateCruzado() {
        gridMembrosTemplateCruzado.setName("gridMembrosTemplateCruzado");
        gridMembrosTemplateCruzado.setLeft(0);
        gridMembrosTemplateCruzado.setTop(35);
        gridMembrosTemplateCruzado.setWidth(244);
        gridMembrosTemplateCruzado.setHeight(161);
        gridMembrosTemplateCruzado.setTable(tbTimeMembroTemplateCruzado);
        gridMembrosTemplateCruzado.setFlexVflex("ftTrue");
        gridMembrosTemplateCruzado.setFlexHflex("ftTrue");
        gridMembrosTemplateCruzado.setPagingEnabled(true);
        gridMembrosTemplateCruzado.setFrozenColumns(0);
        gridMembrosTemplateCruzado.setShowFooter(false);
        gridMembrosTemplateCruzado.setShowHeader(true);
        gridMembrosTemplateCruzado.setMultiSelection(false);
        gridMembrosTemplateCruzado.setGroupingEnabled(false);
        gridMembrosTemplateCruzado.setGroupingExpanded(false);
        gridMembrosTemplateCruzado.setGroupingShowFooter(false);
        gridMembrosTemplateCruzado.setCrosstabEnabled(false);
        gridMembrosTemplateCruzado.setCrosstabGroupType("cgtConcat");
        gridMembrosTemplateCruzado.setEditionEnabled(false);
        gridMembrosTemplateCruzado.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_COMPLETO");
        item0.setTitleCaption("Nome");
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(80);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridMembrosTemplateCruzadoColumns0DoubleClick(event);
            processarFlow("FrmTimeMembros", "item0", "OnDoubleClick");
        });
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridMembrosTemplateCruzado.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TEMPLATE_DESCRICAO");
        TFFontExpression item2 = new TFFontExpression();
        item2.setExpression("*");
        item2.setEvalType("etExpression");
        item2.setFontColor("clBlue");
        item2.setFontSize(-11);
        item2.setFontName("Tahoma");
        item2.setFontStyle("[]");
        item1.getFont().add(item2);
        item1.setTitleCaption("Template");
        item1.setWidth(111);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftCombo");
        item1.setFlexRatio(20);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridMembrosTemplateCruzadoColumns1Click(event);
            processarFlow("FrmTimeMembros", "item1", "OnClick");
        });
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridMembrosTemplateCruzado.getColumns().add(item1);
        vboxModeloGridCruzamento.addChildren(gridMembrosTemplateCruzado);
        gridMembrosTemplateCruzado.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbTime);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbTimeMembro);
        sc.getTables().add(item1);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FrmTimeMembroskeyActionPesquisar(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionIncluir(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionAlterar(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionExcluir(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionSalvar(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionCancelar(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionAnterior(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionProximo(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionAceitar(final Event<Object> event);

    public abstract void FrmTimeMembroskeyActionSalvarContinuar(final Event<Object> event);

    public abstract void pgPrincipalChange(final Event<Object> event);

    public abstract void gridPrincipalClickImageAlterar(final Event<Object> event);

    public abstract void gridPrincipalClickImageDelete(final Event<Object> event);

    public abstract void edIdTime70001Exit(final Event<Object> event);

    public abstract void cbbEmpresaChange(final Event<Object> event);

    public abstract void cbbEmpresaClearClick(final Event<Object> event);

    public abstract void cbbEmpresasFuncoesChange(final Event<Object> event);

    public abstract void cbbEmpresasFuncoesClearClick(final Event<Object> event);

    public void btnAlterarTemplateClick(final Event<Object> event) {
        if (btnAlterarTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridMembrosTemplateDisponivelColumns0DoubleClick(final Event<Object> event);

    public void btnHorariosAdicionarTemplateClick(final Event<Object> event) {
        if (btnHorariosAdicionarTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnHorariosAdicionarTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnHorariosRemoverTemplateClick(final Event<Object> event) {
        if (btnHorariosRemoverTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnHorariosRemoverTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridMembrosTemplateCruzadoColumns0DoubleClick(final Event<Object> event);

    public abstract void gridMembrosTemplateCruzadoColumns1Click(final Event<Object> event);

    public void btnConsultarClick(final Event<Object> event) {
        if (btnConsultar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnFiltroAvancadoClick(final Event<Object> event) {
        if (btnFiltroAvancado.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnFiltroAvancado");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarContinuarClick(final Event<Object> event) {
        if (btnSalvarContinuar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarContinuar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAnteriorClick(final Event<Object> event) {
        if (btnAnterior.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAnterior");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnProximoClick(final Event<Object> event) {
        if (btnProximo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnProximo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnMaisClick(final Event<Object> event) {
        if (btnMais.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnMais");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void menuItemAbreTabelaAuxClick(final Event<Object> event);

    public abstract void menuHabilitaNavegacaoClick(final Event<Object> event);

    public abstract void menuSelecaoMultiplaClick(final Event<Object> event);

    public abstract void menuItemConfgGridClick(final Event<Object> event);

    public abstract void menuItemExportPdfClick(final Event<Object> event);

    public abstract void menuItemExportExcelClick(final Event<Object> event);

    public abstract void menuItemHelpClick(final Event<Object> event);

    public abstract void tbTimeAfterScroll(final Event<Object> event);

    public abstract void tbTimeMembroBeforePost(final Event<Object> event);

}