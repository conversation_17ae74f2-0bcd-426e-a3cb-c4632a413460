package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmMensagemGenerica extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.MensagemGenericaRNA rn = null;

    public FrmMensagemGenerica() {
        try {
            rn = (freedom.bytecode.rn.MensagemGenericaRNA) getRN(freedom.bytecode.rn.wizard.MensagemGenericaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vboxPrincipal();
        init_FrmMensagemGenerica();
    }

    protected TFForm FrmMensagemGenerica = this;
    private void init_FrmMensagemGenerica() {
        FrmMensagemGenerica.setName("FrmMensagemGenerica");
        FrmMensagemGenerica.setCaption("Mensagem gen\u00E9rica");
        FrmMensagemGenerica.setClientHeight(114);
        FrmMensagemGenerica.setClientWidth(357);
        FrmMensagemGenerica.setColor("clBtnFace");
        FrmMensagemGenerica.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmMensagemGenerica", "FrmMensagemGenerica", "OnCreate");
        });
        FrmMensagemGenerica.setWOrigem("EhMain");
        FrmMensagemGenerica.setWKey("548023");
        FrmMensagemGenerica.setSpacing(0);
        FrmMensagemGenerica.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(357);
        vboxPrincipal.setHeight(114);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(10);
        vboxPrincipal.setPaddingLeft(10);
        vboxPrincipal.setPaddingRight(10);
        vboxPrincipal.setPaddingBottom(10);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(0);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmMensagemGenerica.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

}