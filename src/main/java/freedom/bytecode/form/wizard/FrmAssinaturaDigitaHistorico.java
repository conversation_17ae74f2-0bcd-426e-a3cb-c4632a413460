package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAssinaturaDigitaHistorico extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AssinaturaDigitaHistoricoRNA rn = null;

    public FrmAssinaturaDigitaHistorico() {
        try {
            rn = (freedom.bytecode.rn.AssinaturaDigitaHistoricoRNA) getRN(freedom.bytecode.rn.wizard.AssinaturaDigitaHistoricoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbNbsapiEnvelopesLog();
        init_tbAssinaturaDigital();
        init_hboxTabDadosAssinaturaMain();
        init_hboxBotoes();
        init_btnVoltar();
        init_gridHistorico();
        init_vBoxGridSolicitacaoAssinaturaEspacoRodape();
        init_FrmAssinaturaDigitaHistorico();
    }

    public NBSAPI_ENVELOPES_LOG tbNbsapiEnvelopesLog;

    private void init_tbNbsapiEnvelopesLog() {
        tbNbsapiEnvelopesLog = rn.tbNbsapiEnvelopesLog;
        tbNbsapiEnvelopesLog.setName("tbNbsapiEnvelopesLog");
        tbNbsapiEnvelopesLog.setMaxRowCount(0);
        tbNbsapiEnvelopesLog.setWKey("469012;46901");
        tbNbsapiEnvelopesLog.setRatioBatchSize(20);
        getTables().put(tbNbsapiEnvelopesLog, "tbNbsapiEnvelopesLog");
        tbNbsapiEnvelopesLog.applyProperties();
    }

    public CRM_ASSINATURA_DIGITAL tbAssinaturaDigital;

    private void init_tbAssinaturaDigital() {
        tbAssinaturaDigital = rn.tbAssinaturaDigital;
        tbAssinaturaDigital.setName("tbAssinaturaDigital");
        tbAssinaturaDigital.setMaxRowCount(200);
        tbAssinaturaDigital.setWKey("469012;46902");
        tbAssinaturaDigital.setRatioBatchSize(20);
        getTables().put(tbAssinaturaDigital, "tbAssinaturaDigital");
        tbAssinaturaDigital.applyProperties();
    }

    protected TFForm FrmAssinaturaDigitaHistorico = this;
    private void init_FrmAssinaturaDigitaHistorico() {
        FrmAssinaturaDigitaHistorico.setName("FrmAssinaturaDigitaHistorico");
        FrmAssinaturaDigitaHistorico.setCaption("Assinatura Digital Hist\u00F3rico");
        FrmAssinaturaDigitaHistorico.setClientHeight(410);
        FrmAssinaturaDigitaHistorico.setClientWidth(583);
        FrmAssinaturaDigitaHistorico.setColor("clBtnFace");
        FrmAssinaturaDigitaHistorico.setWOrigem("EhMain");
        FrmAssinaturaDigitaHistorico.setWKey("469012");
        FrmAssinaturaDigitaHistorico.setSpacing(0);
        FrmAssinaturaDigitaHistorico.applyProperties();
    }

    public TFVBox hboxTabDadosAssinaturaMain = new TFVBox();

    private void init_hboxTabDadosAssinaturaMain() {
        hboxTabDadosAssinaturaMain.setName("hboxTabDadosAssinaturaMain");
        hboxTabDadosAssinaturaMain.setLeft(0);
        hboxTabDadosAssinaturaMain.setTop(0);
        hboxTabDadosAssinaturaMain.setWidth(583);
        hboxTabDadosAssinaturaMain.setHeight(410);
        hboxTabDadosAssinaturaMain.setAlign("alClient");
        hboxTabDadosAssinaturaMain.setBorderStyle("stNone");
        hboxTabDadosAssinaturaMain.setPaddingTop(5);
        hboxTabDadosAssinaturaMain.setPaddingLeft(5);
        hboxTabDadosAssinaturaMain.setPaddingRight(5);
        hboxTabDadosAssinaturaMain.setPaddingBottom(5);
        hboxTabDadosAssinaturaMain.setMarginTop(0);
        hboxTabDadosAssinaturaMain.setMarginLeft(0);
        hboxTabDadosAssinaturaMain.setMarginRight(0);
        hboxTabDadosAssinaturaMain.setMarginBottom(0);
        hboxTabDadosAssinaturaMain.setSpacing(5);
        hboxTabDadosAssinaturaMain.setFlexVflex("ftTrue");
        hboxTabDadosAssinaturaMain.setFlexHflex("ftTrue");
        hboxTabDadosAssinaturaMain.setScrollable(false);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigHorizontalLength(10);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigVerticalLength(10);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigBlurRadius(5);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigSpreadRadius(0);
        hboxTabDadosAssinaturaMain.setBoxShadowConfigShadowColor("clBlack");
        hboxTabDadosAssinaturaMain.setBoxShadowConfigOpacity(75);
        FrmAssinaturaDigitaHistorico.addChildren(hboxTabDadosAssinaturaMain);
        hboxTabDadosAssinaturaMain.applyProperties();
    }

    public TFHBox hboxBotoes = new TFHBox();

    private void init_hboxBotoes() {
        hboxBotoes.setName("hboxBotoes");
        hboxBotoes.setLeft(0);
        hboxBotoes.setTop(0);
        hboxBotoes.setWidth(679);
        hboxBotoes.setHeight(69);
        hboxBotoes.setAlign("alTop");
        hboxBotoes.setBorderStyle("stNone");
        hboxBotoes.setPaddingTop(0);
        hboxBotoes.setPaddingLeft(0);
        hboxBotoes.setPaddingRight(5);
        hboxBotoes.setPaddingBottom(0);
        hboxBotoes.setMarginTop(0);
        hboxBotoes.setMarginLeft(0);
        hboxBotoes.setMarginRight(0);
        hboxBotoes.setMarginBottom(0);
        hboxBotoes.setSpacing(5);
        hboxBotoes.setFlexVflex("ftFalse");
        hboxBotoes.setFlexHflex("ftTrue");
        hboxBotoes.setScrollable(false);
        hboxBotoes.setBoxShadowConfigHorizontalLength(10);
        hboxBotoes.setBoxShadowConfigVerticalLength(10);
        hboxBotoes.setBoxShadowConfigBlurRadius(5);
        hboxBotoes.setBoxShadowConfigSpreadRadius(0);
        hboxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hboxBotoes.setBoxShadowConfigOpacity(75);
        hboxBotoes.setVAlign("tvTop");
        hboxTabDadosAssinaturaMain.addChildren(hboxBotoes);
        hboxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAssinaturaDigitaHistorico", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hboxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFGrid gridHistorico = new TFGrid();

    private void init_gridHistorico() {
        gridHistorico.setName("gridHistorico");
        gridHistorico.setLeft(0);
        gridHistorico.setTop(70);
        gridHistorico.setWidth(567);
        gridHistorico.setHeight(138);
        gridHistorico.setTable(tbNbsapiEnvelopesLog);
        gridHistorico.setFlexVflex("ftTrue");
        gridHistorico.setFlexHflex("ftTrue");
        gridHistorico.setPagingEnabled(false);
        gridHistorico.setFrozenColumns(0);
        gridHistorico.setShowFooter(false);
        gridHistorico.setShowHeader(true);
        gridHistorico.setMultiSelection(false);
        gridHistorico.setGroupingEnabled(false);
        gridHistorico.setGroupingExpanded(false);
        gridHistorico.setGroupingShowFooter(false);
        gridHistorico.setCrosstabEnabled(false);
        gridHistorico.setCrosstabGroupType("cgtConcat");
        gridHistorico.setEditionEnabled(false);
        gridHistorico.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DATA_LOG");
        item0.setTitleCaption("Data");
        item0.setWidth(142);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFMaskExpression item1 = new TFMaskExpression();
        item1.setExpression("*");
        item1.setEvalType("etExpression");
        item1.setMask("dd/MM/yyyy HH:mm");
        item1.setPadLength(0);
        item1.setPadDirection("pdNone");
        item1.setMaskType("mtDateTime");
        item0.getMasks().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridHistorico.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("RESPONSAVEL");
        item2.setTitleCaption("Usu\u00E1rio");
        item2.setWidth(97);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridHistorico.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("OBSERVACAO");
        item3.setTitleCaption("Descri\u00E7\u00E3o");
        item3.setWidth(358);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridHistorico.getColumns().add(item3);
        hboxTabDadosAssinaturaMain.addChildren(gridHistorico);
        gridHistorico.applyProperties();
    }

    public TFHBox vBoxGridSolicitacaoAssinaturaEspacoRodape = new TFHBox();

    private void init_vBoxGridSolicitacaoAssinaturaEspacoRodape() {
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setName("vBoxGridSolicitacaoAssinaturaEspacoRodape");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setTop(209);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setWidth(185);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setHeight(13);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBorderStyle("stNone");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingTop(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingRight(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setPaddingBottom(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginTop(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginLeft(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginRight(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setMarginBottom(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setSpacing(1);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setFlexVflex("ftFalse");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setFlexHflex("ftFalse");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setScrollable(false);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigHorizontalLength(10);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigVerticalLength(10);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigBlurRadius(5);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigSpreadRadius(0);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigShadowColor("clBlack");
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setBoxShadowConfigOpacity(75);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.setVAlign("tvTop");
        hboxTabDadosAssinaturaMain.addChildren(vBoxGridSolicitacaoAssinaturaEspacoRodape);
        vBoxGridSolicitacaoAssinaturaEspacoRodape.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}