package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmInputBoxFixed extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.InputBoxFixedRNA rn = null;

    public FrmInputBoxFixed() {
        try {
            rn = (freedom.bytecode.rn.InputBoxFixedRNA) getRN(freedom.bytecode.rn.wizard.InputBoxFixedRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxImput();
        init_FHBox11();
        init_btnVoltar();
        init_btnAceitar();
        init_lblDescricao();
        init_edtDescricao();
        init_FrmInputBoxFixed();
    }

    protected TFForm FrmInputBoxFixed = this;
    private void init_FrmInputBoxFixed() {
        FrmInputBoxFixed.setName("FrmInputBoxFixed");
        FrmInputBoxFixed.setCaption("InputBox");
        FrmInputBoxFixed.setClientHeight(168);
        FrmInputBoxFixed.setClientWidth(409);
        FrmInputBoxFixed.setColor("clBtnFace");
        FrmInputBoxFixed.setWKey("4600415");
        FrmInputBoxFixed.setSpacing(0);
        FrmInputBoxFixed.applyProperties();
    }

    public TFVBox vBoxImput = new TFVBox();

    private void init_vBoxImput() {
        vBoxImput.setName("vBoxImput");
        vBoxImput.setLeft(0);
        vBoxImput.setTop(0);
        vBoxImput.setWidth(409);
        vBoxImput.setHeight(168);
        vBoxImput.setAlign("alClient");
        vBoxImput.setBorderStyle("stNone");
        vBoxImput.setPaddingTop(5);
        vBoxImput.setPaddingLeft(5);
        vBoxImput.setPaddingRight(5);
        vBoxImput.setPaddingBottom(5);
        vBoxImput.setMarginTop(0);
        vBoxImput.setMarginLeft(0);
        vBoxImput.setMarginRight(0);
        vBoxImput.setMarginBottom(0);
        vBoxImput.setSpacing(5);
        vBoxImput.setFlexVflex("ftTrue");
        vBoxImput.setFlexHflex("ftTrue");
        vBoxImput.setScrollable(false);
        vBoxImput.setBoxShadowConfigHorizontalLength(10);
        vBoxImput.setBoxShadowConfigVerticalLength(10);
        vBoxImput.setBoxShadowConfigBlurRadius(5);
        vBoxImput.setBoxShadowConfigSpreadRadius(0);
        vBoxImput.setBoxShadowConfigShadowColor("clBlack");
        vBoxImput.setBoxShadowConfigOpacity(75);
        FrmInputBoxFixed.addChildren(vBoxImput);
        vBoxImput.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(400);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxImput.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmInputBoxFixed", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(60);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-13);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmInputBoxFixed", "btnAceitar", "OnClick");
        });
        btnAceitar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515"
 + "0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3"
 + "1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7"
 + "95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC"
 + "101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D"
 + "1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537"
 + "A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC"
 + "C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB"
 + "482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB"
 + "1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D"
 + "C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F"
 + "8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082");
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox11.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFLabel lblDescricao = new TFLabel();

    private void init_lblDescricao() {
        lblDescricao.setName("lblDescricao");
        lblDescricao.setLeft(0);
        lblDescricao.setTop(62);
        lblDescricao.setWidth(46);
        lblDescricao.setHeight(13);
        lblDescricao.setCaption("Descri\u00E7\u00E3o");
        lblDescricao.setFontColor("clWindowText");
        lblDescricao.setFontSize(-11);
        lblDescricao.setFontName("Tahoma");
        lblDescricao.setFontStyle("[]");
        lblDescricao.setVerticalAlignment("taVerticalCenter");
        lblDescricao.setWordBreak(false);
        vBoxImput.addChildren(lblDescricao);
        lblDescricao.applyProperties();
    }

    public TFString edtDescricao = new TFString();

    private void init_edtDescricao() {
        edtDescricao.setName("edtDescricao");
        edtDescricao.setLeft(0);
        edtDescricao.setTop(76);
        edtDescricao.setWidth(399);
        edtDescricao.setHeight(24);
        edtDescricao.setFlex(true);
        edtDescricao.setRequired(false);
        edtDescricao.setConstraintCheckWhen("cwImmediate");
        edtDescricao.setConstraintCheckType("ctExpression");
        edtDescricao.setConstraintFocusOnError(false);
        edtDescricao.setConstraintEnableUI(true);
        edtDescricao.setConstraintEnabled(false);
        edtDescricao.setConstraintFormCheck(true);
        edtDescricao.setCharCase("ccNormal");
        edtDescricao.setPwd(false);
        edtDescricao.setMaxlength(0);
        edtDescricao.setFontColor("clWindowText");
        edtDescricao.setFontSize(-13);
        edtDescricao.setFontName("Tahoma");
        edtDescricao.setFontStyle("[]");
        edtDescricao.setSaveLiteralCharacter(false);
        edtDescricao.applyProperties();
        vBoxImput.addChildren(edtDescricao);
        addValidatable(edtDescricao);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}