package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadAreaDeContato extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadAreaDeContatoRNA rn = null;

    public FrmCadAreaDeContato() {
        try {
            rn = (freedom.bytecode.rn.CadAreaDeContatoRNA) getRN(freedom.bytecode.rn.wizard.CadAreaDeContatoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteContatoTipo();
        init_tbSimNao();
        init_tbSimNaoFt();
        init_filtroAvancado();
        init_popMenuPrincipal();
        init_menuItemAbreTabelaAux();
        init_menuHabilitaNavegacao();
        init_menuSelecaoMultipla();
        init_FMenuItem1();
        init_menuItemConfgGrid();
        init_menuItemExportPdf();
        init_menuItemExportExcel();
        init_menuItemHelp();
        init_gridConfig();
        init_FHBox6();
        init_FHBox1();
        init_FHBox2();
        init_btnConsultar();
        init_btnFiltroAvancado();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnSalvarContinuar();
        init_btnCancelar();
        init_FHBox3();
        init_btnAnterior();
        init_btnProximo();
        init_FHBox8();
        init_FHBox5();
        init_btnAceitar();
        init_btnMais();
        init_FHBox7();
        init_FHBox4();
        init_lblMensagem();
        init_pgPrincipal();
        init_tabListagem();
        init_hbListagem();
        init_FVBox1();
        init_grpBoxFiltro();
        init_gpFiltroPrincipal();
        init_lfAreaContato();
        init_efAreaContato();
        init_lfDescricao();
        init_efDescricao();
        init_lfAtivoEquals();
        init_efAtivoEquals();
        init_gridPrincipal();
        init_tabCadastro();
        init_FVBox2();
        init_grpBoxPrincipal();
        init_FGridPanel2();
        init_lbDescricao44801();
        init_lbAreaContato44801();
        init_lbAtivo44801();
        init_edAreaContato44801();
        init_edDescricao44801();
        init_edAtivo44801();
        init_scClienteContatoTipo();
        init_FrmCadAreaDeContato();
    }

    public CLIENTE_CONTATO_TIPO tbClienteContatoTipo;

    private void init_tbClienteContatoTipo() {
        tbClienteContatoTipo = rn.tbClienteContatoTipo;
        tbClienteContatoTipo.setName("tbClienteContatoTipo");
        tbClienteContatoTipo.setMaxRowCount(200);
        tbClienteContatoTipo.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbClienteContatoTipoAfterScroll(event);
            processarFlow("FrmCadAreaDeContato", "tbClienteContatoTipo", "OnAfterScroll");
        });
        tbClienteContatoTipo.setWKey("44801360;44801");
        tbClienteContatoTipo.setRatioBatchSize(20);
        getTables().put(tbClienteContatoTipo, "tbClienteContatoTipo");
        tbClienteContatoTipo.applyProperties();
    }

    public SIM_NAO tbSimNao;

    private void init_tbSimNao() {
        tbSimNao = rn.tbSimNao;
        tbSimNao.setName("tbSimNao");
        tbSimNao.setMaxRowCount(200);
        tbSimNao.setWKey("44801360;44802");
        tbSimNao.setRatioBatchSize(20);
        getTables().put(tbSimNao, "tbSimNao");
        tbSimNao.applyProperties();
    }

    public SIM_NAO tbSimNaoFt;

    private void init_tbSimNaoFt() {
        tbSimNaoFt = rn.tbSimNaoFt;
        tbSimNaoFt.setName("tbSimNaoFt");
        tbSimNaoFt.setMaxRowCount(200);
        tbSimNaoFt.setWKey("44801360;44801");
        tbSimNaoFt.setRatioBatchSize(20);
        getTables().put(tbSimNaoFt, "tbSimNaoFt");
        tbSimNaoFt.applyProperties();
    }

    public TFFilterWindow filtroAvancado = new TFFilterWindow();

    private void init_filtroAvancado() {
        filtroAvancado.setName("filtroAvancado");
        filtroAvancado.setWidth(450);
        filtroAvancado.setHeight(400);
        filtroAvancado.setCaption("Filtro");
        filtroAvancado.setColumns(2);
        filtroAvancado.setTable(tbClienteContatoTipo);
        filtroAvancado.setFilterStyle("fsAddCondition");
        filtroAvancado.applyProperties();
    }

    public TFPopupMenu popMenuPrincipal = new TFPopupMenu();

    private void init_popMenuPrincipal() {
        popMenuPrincipal.setName("popMenuPrincipal");
        FrmCadAreaDeContato.addChildren(popMenuPrincipal);
        popMenuPrincipal.applyProperties();
    }

    public TFMenuItem menuItemAbreTabelaAux = new TFMenuItem();

    private void init_menuItemAbreTabelaAux() {
        menuItemAbreTabelaAux.setName("menuItemAbreTabelaAux");
        menuItemAbreTabelaAux.setCaption("Abre Tabela Auxiliares");
        menuItemAbreTabelaAux.setHint("For\u00E7a a abertura das tabela auxiliares e lookup, visto que usuario pode abrir o cadastro em um aba independente e alterar o registro");
        menuItemAbreTabelaAux.setImageIndex(200022);
        menuItemAbreTabelaAux.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemAbreTabelaAuxClick(event);
            processarFlow("FrmCadAreaDeContato", "menuItemAbreTabelaAux", "OnClick");
        });
        menuItemAbreTabelaAux.setAccess(false);
        menuItemAbreTabelaAux.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemAbreTabelaAux);
        menuItemAbreTabelaAux.applyProperties();
    }

    public TFMenuItem menuHabilitaNavegacao = new TFMenuItem();

    private void init_menuHabilitaNavegacao() {
        menuHabilitaNavegacao.setName("menuHabilitaNavegacao");
        menuHabilitaNavegacao.setCaption("Habilitar Navega\u00E7\u00E3o Durante Edi\u00E7\u00E3o");
        menuHabilitaNavegacao.setHint("Quando habilita a navega\u00E7\u00E3o durante a edi\u00E7\u00E3o (Inclus\u00E3o/Altera\u00E7\u00E3o) ao mover para frente ou para tr\u00E1s o registro atual \u00E9 automaticamente salvo");
        menuHabilitaNavegacao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuHabilitaNavegacaoClick(event);
            processarFlow("FrmCadAreaDeContato", "menuHabilitaNavegacao", "OnClick");
        });
        menuHabilitaNavegacao.setAccess(true);
        menuHabilitaNavegacao.setCheckmark(true);
        popMenuPrincipal.addChildren(menuHabilitaNavegacao);
        menuHabilitaNavegacao.applyProperties();
    }

    public TFMenuItem menuSelecaoMultipla = new TFMenuItem();

    private void init_menuSelecaoMultipla() {
        menuSelecaoMultipla.setName("menuSelecaoMultipla");
        menuSelecaoMultipla.setCaption("Selecionar Multiplos Registros");
        menuSelecaoMultipla.setHint("Permite altera\u00E7\u00E3o/exclus\u00E3o de todos os registros selecionados");
        menuSelecaoMultipla.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuSelecaoMultiplaClick(event);
            processarFlow("FrmCadAreaDeContato", "menuSelecaoMultipla", "OnClick");
        });
        menuSelecaoMultipla.setAccess(true);
        menuSelecaoMultipla.setCheckmark(true);
        popMenuPrincipal.addChildren(menuSelecaoMultipla);
        menuSelecaoMultipla.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("Grid");
        FMenuItem1.setImageIndex(22006);
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        popMenuPrincipal.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem menuItemConfgGrid = new TFMenuItem();

    private void init_menuItemConfgGrid() {
        menuItemConfgGrid.setName("menuItemConfgGrid");
        menuItemConfgGrid.setCaption("Configurar Colunas da Grid");
        menuItemConfgGrid.setImageIndex(200021);
        menuItemConfgGrid.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemConfgGridClick(event);
            processarFlow("FrmCadAreaDeContato", "menuItemConfgGrid", "OnClick");
        });
        menuItemConfgGrid.setAccess(true);
        menuItemConfgGrid.setCheckmark(false);
        FMenuItem1.addChildren(menuItemConfgGrid);
        menuItemConfgGrid.applyProperties();
    }

    public TFMenuItem menuItemExportPdf = new TFMenuItem();

    private void init_menuItemExportPdf() {
        menuItemExportPdf.setName("menuItemExportPdf");
        menuItemExportPdf.setCaption("Exportar PDF");
        menuItemExportPdf.setImageIndex(22005);
        menuItemExportPdf.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportPdfClick(event);
            processarFlow("FrmCadAreaDeContato", "menuItemExportPdf", "OnClick");
        });
        menuItemExportPdf.setAccess(true);
        menuItemExportPdf.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportPdf);
        menuItemExportPdf.applyProperties();
    }

    public TFMenuItem menuItemExportExcel = new TFMenuItem();

    private void init_menuItemExportExcel() {
        menuItemExportExcel.setName("menuItemExportExcel");
        menuItemExportExcel.setCaption("Exportar para Excel");
        menuItemExportExcel.setImageIndex(22004);
        menuItemExportExcel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportExcelClick(event);
            processarFlow("FrmCadAreaDeContato", "menuItemExportExcel", "OnClick");
        });
        menuItemExportExcel.setAccess(true);
        menuItemExportExcel.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportExcel);
        menuItemExportExcel.applyProperties();
    }

    public TFMenuItem menuItemHelp = new TFMenuItem();

    private void init_menuItemHelp() {
        menuItemHelp.setName("menuItemHelp");
        menuItemHelp.setCaption("Help");
        menuItemHelp.setHint("Help da Tela");
        menuItemHelp.setImageIndex(11);
        menuItemHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemHelpClick(event);
            processarFlow("FrmCadAreaDeContato", "menuItemHelp", "OnClick");
        });
        menuItemHelp.setAccess(false);
        menuItemHelp.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemHelp);
        menuItemHelp.applyProperties();
    }

    public TFGridConfigWindow gridConfig = new TFGridConfigWindow();

    private void init_gridConfig() {
        gridConfig.setName("gridConfig");
        gridConfig.setWidth(500);
        gridConfig.setHeight(500);
        gridConfig.setCaption("Configura\u00E7\u00E3o de Grid");
        gridConfig.setGrid(gridPrincipal);
        gridConfig.applyProperties();
    }

    protected TFForm FrmCadAreaDeContato = this;
    private void init_FrmCadAreaDeContato() {
        FrmCadAreaDeContato.setName("FrmCadAreaDeContato");
        FrmCadAreaDeContato.setAlign("alTop");
        FrmCadAreaDeContato.setCaption("\u00C1rea de contato");
        FrmCadAreaDeContato.setClientHeight(511);
        FrmCadAreaDeContato.setClientWidth(514);
        FrmCadAreaDeContato.setColor("clWhite");
        FrmCadAreaDeContato.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmCadAreaDeContato", "FrmCadAreaDeContato", "OnCreate");
        });
        FrmCadAreaDeContato.setWOrigem("EhMain");
        FrmCadAreaDeContato.setWKey("44801360");
        TFShortcutKeyItem item0 = new TFShortcutKeyItem();
        item0.setModifier("smCtrl");
        item0.setKey("sk1");
        item0.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionPesquisar(event);
            processarFlow("FrmCadAreaDeContato", "item0", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item0);
        TFShortcutKeyItem item1 = new TFShortcutKeyItem();
        item1.setModifier("smCtrl");
        item1.setKey("sk2");
        item1.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionIncluir(event);
            processarFlow("FrmCadAreaDeContato", "item1", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item1);
        TFShortcutKeyItem item2 = new TFShortcutKeyItem();
        item2.setModifier("smCtrl");
        item2.setKey("sk3");
        item2.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionAlterar(event);
            processarFlow("FrmCadAreaDeContato", "item2", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item2);
        TFShortcutKeyItem item3 = new TFShortcutKeyItem();
        item3.setModifier("smCtrl");
        item3.setKey("sk4");
        item3.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionExcluir(event);
            processarFlow("FrmCadAreaDeContato", "item3", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item3);
        TFShortcutKeyItem item4 = new TFShortcutKeyItem();
        item4.setModifier("smCtrl");
        item4.setKey("sk5");
        item4.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionSalvar(event);
            processarFlow("FrmCadAreaDeContato", "item4", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item4);
        TFShortcutKeyItem item5 = new TFShortcutKeyItem();
        item5.setModifier("smCtrl");
        item5.setKey("sk6");
        item5.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionCancelar(event);
            processarFlow("FrmCadAreaDeContato", "item5", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item5);
        TFShortcutKeyItem item6 = new TFShortcutKeyItem();
        item6.setModifier("smCtrl");
        item6.setKey("sk7");
        item6.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionAnterior(event);
            processarFlow("FrmCadAreaDeContato", "item6", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item6);
        TFShortcutKeyItem item7 = new TFShortcutKeyItem();
        item7.setModifier("smCtrl");
        item7.setKey("sk8");
        item7.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionProximo(event);
            processarFlow("FrmCadAreaDeContato", "item7", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item7);
        TFShortcutKeyItem item8 = new TFShortcutKeyItem();
        item8.setModifier("smCtrl");
        item8.setKey("sk9");
        item8.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionAceitar(event);
            processarFlow("FrmCadAreaDeContato", "item8", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item8);
        TFShortcutKeyItem item9 = new TFShortcutKeyItem();
        item9.setModifier("smCtrl");
        item9.setKey("sk0");
        item9.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadAreaDeContatokeyActionSalvarContinuar(event);
            processarFlow("FrmCadAreaDeContato", "item9", "OnKeyAction");
        });
        FrmCadAreaDeContato.getShortcutKeys().add(item9);
        FrmCadAreaDeContato.setSpacing(0);
        FrmCadAreaDeContato.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridConfig.loadConfig();
        });
        FrmCadAreaDeContato.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(514);
        FHBox6.setHeight(43);
        FHBox6.setAlign("alTop");
        FHBox6.setBorderStyle("stRaised");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FrmCadAreaDeContato.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(1);
        FHBox1.setTop(1);
        FHBox1.setWidth(654);
        FHBox1.setHeight(38);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setColor("15592941");
        FHBox1.setPaddingTop(6);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FHBox6.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(13);
        FHBox2.setHeight(33);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FHBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnConsultar = new TFButton();

    private void init_btnConsultar() {
        btnConsultar.setName("btnConsultar");
        btnConsultar.setLeft(13);
        btnConsultar.setTop(0);
        btnConsultar.setWidth(33);
        btnConsultar.setHeight(30);
        btnConsultar.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultar.setFontColor("clHotLight");
        btnConsultar.setFontSize(-11);
        btnConsultar.setFontName("Tahoma");
        btnConsultar.setFontStyle("[]");
        btnConsultar.setLayout("blGlyphTop");
        btnConsultar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConsultarClick(event);
            processarFlow("FrmCadAreaDeContato", "btnConsultar", "OnClick");
        });
        btnConsultar.setImageId(13);
        btnConsultar.setColor("clBtnFace");
        btnConsultar.setAccess(true);
        btnConsultar.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultar);
        btnConsultar.applyProperties();
    }

    public TFButton btnFiltroAvancado = new TFButton();

    private void init_btnFiltroAvancado() {
        btnFiltroAvancado.setName("btnFiltroAvancado");
        btnFiltroAvancado.setLeft(46);
        btnFiltroAvancado.setTop(0);
        btnFiltroAvancado.setWidth(33);
        btnFiltroAvancado.setHeight(30);
        btnFiltroAvancado.setHint("Filtro Avan\u00E7ado");
        btnFiltroAvancado.setFontColor("clWindowText");
        btnFiltroAvancado.setFontSize(-11);
        btnFiltroAvancado.setFontName("Tahoma");
        btnFiltroAvancado.setFontStyle("[]");
        btnFiltroAvancado.setLayout("blGlyphTop");
        btnFiltroAvancado.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFiltroAvancadoClick(event);
            processarFlow("FrmCadAreaDeContato", "btnFiltroAvancado", "OnClick");
        });
        btnFiltroAvancado.setImageId(50002);
        btnFiltroAvancado.setColor("clBtnFace");
        btnFiltroAvancado.setAccess(true);
        btnFiltroAvancado.setIconReverseDirection(false);
        FHBox1.addChildren(btnFiltroAvancado);
        btnFiltroAvancado.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(79);
        btnNovo.setTop(0);
        btnNovo.setWidth(33);
        btnNovo.setHeight(30);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmCadAreaDeContato", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(112);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(33);
        btnAlterar.setHeight(30);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmCadAreaDeContato", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(145);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(33);
        btnExcluir.setHeight(30);
        btnExcluir.setHint("Exclui o Registro Selecionado  (CRTL+ 4)");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmCadAreaDeContato", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(true);
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(178);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(33);
        btnSalvar.setHeight(30);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadAreaDeContato", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnSalvarContinuar = new TFButton();

    private void init_btnSalvarContinuar() {
        btnSalvarContinuar.setName("btnSalvarContinuar");
        btnSalvarContinuar.setLeft(211);
        btnSalvarContinuar.setTop(0);
        btnSalvarContinuar.setWidth(33);
        btnSalvarContinuar.setHeight(30);
        btnSalvarContinuar.setHint("Salvar e Continuar");
        btnSalvarContinuar.setFontColor("clWindowText");
        btnSalvarContinuar.setFontSize(-11);
        btnSalvarContinuar.setFontName("Tahoma");
        btnSalvarContinuar.setFontStyle("[]");
        btnSalvarContinuar.setLayout("blGlyphTop");
        btnSalvarContinuar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarContinuarClick(event);
            processarFlow("FrmCadAreaDeContato", "btnSalvarContinuar", "OnClick");
        });
        btnSalvarContinuar.setImageId(22001);
        btnSalvarContinuar.setColor("clBtnFace");
        btnSalvarContinuar.setAccess(false);
        btnSalvarContinuar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvarContinuar);
        btnSalvarContinuar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(244);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(33);
        btnCancelar.setHeight(30);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmCadAreaDeContato", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(277);
        FHBox3.setTop(0);
        FHBox3.setWidth(26);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnAnterior = new TFButton();

    private void init_btnAnterior() {
        btnAnterior.setName("btnAnterior");
        btnAnterior.setLeft(303);
        btnAnterior.setTop(0);
        btnAnterior.setWidth(33);
        btnAnterior.setHeight(30);
        btnAnterior.setHint("Registro Anterior, Estando em Modo de Inclus\u00E3o/Altera\u00E7\u00E3o Salva Antes de Mover o Registro (CRTL+ 7)");
        btnAnterior.setFontColor("clWindowText");
        btnAnterior.setFontSize(-11);
        btnAnterior.setFontName("Tahoma");
        btnAnterior.setFontStyle("[]");
        btnAnterior.setLayout("blGlyphTop");
        btnAnterior.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAnteriorClick(event);
            processarFlow("FrmCadAreaDeContato", "btnAnterior", "OnClick");
        });
        btnAnterior.setImageId(14);
        btnAnterior.setColor("clBtnFace");
        btnAnterior.setAccess(false);
        btnAnterior.setIconReverseDirection(false);
        FHBox1.addChildren(btnAnterior);
        btnAnterior.applyProperties();
    }

    public TFButton btnProximo = new TFButton();

    private void init_btnProximo() {
        btnProximo.setName("btnProximo");
        btnProximo.setLeft(336);
        btnProximo.setTop(0);
        btnProximo.setWidth(33);
        btnProximo.setHeight(30);
        btnProximo.setHint("Pr\u00F3ximo Registro   (CRTL+ 8)");
        btnProximo.setFontColor("clWindowText");
        btnProximo.setFontSize(-11);
        btnProximo.setFontName("Tahoma");
        btnProximo.setFontStyle("[]");
        btnProximo.setLayout("blGlyphTop");
        btnProximo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnProximoClick(event);
            processarFlow("FrmCadAreaDeContato", "btnProximo", "OnClick");
        });
        btnProximo.setImageId(15);
        btnProximo.setColor("clBtnFace");
        btnProximo.setAccess(false);
        btnProximo.setIconReverseDirection(false);
        FHBox1.addChildren(btnProximo);
        btnProximo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(369);
        FHBox8.setTop(0);
        FHBox8.setWidth(32);
        FHBox8.setHeight(32);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox1.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(655);
        FHBox5.setTop(1);
        FHBox5.setWidth(84);
        FHBox5.setHeight(38);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setColor("15592941");
        FHBox5.setPaddingTop(6);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(3);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox6.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(0);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(33);
        btnAceitar.setHeight(30);
        btnAceitar.setHint("Aceita o Registro Selecionado para Outro Formulario  (CRTL+ 9)");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmCadAreaDeContato", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox5.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnMais = new TFButton();

    private void init_btnMais() {
        btnMais.setName("btnMais");
        btnMais.setLeft(33);
        btnMais.setTop(0);
        btnMais.setWidth(33);
        btnMais.setHeight(30);
        btnMais.setHint("Mais Op\u00E7\u00F5es");
        btnMais.setFontColor("clWindowText");
        btnMais.setFontSize(-21);
        btnMais.setFontName("Tahoma");
        btnMais.setFontStyle("[]");
        btnMais.setLayout("blGlyphTop");
        btnMais.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnMaisClick(event);
            processarFlow("FrmCadAreaDeContato", "btnMais", "OnClick");
        });
        btnMais.setImageId(22002);
        btnMais.setColor("clBtnFace");
        btnMais.setAccess(false);
        btnMais.setIconReverseDirection(false);
        FHBox5.addChildren(btnMais);
        btnMais.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(66);
        FHBox7.setTop(0);
        FHBox7.setWidth(28);
        FHBox7.setHeight(32);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FHBox5.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(2);
        FHBox4.setTop(48);
        FHBox4.setWidth(750);
        FHBox4.setHeight(18);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setColor("15592941");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(5);
        FHBox4.setPaddingRight(5);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FrmCadAreaDeContato.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblMensagem = new TFLabel();

    private void init_lblMensagem() {
        lblMensagem.setName("lblMensagem");
        lblMensagem.setLeft(0);
        lblMensagem.setTop(0);
        lblMensagem.setWidth(67);
        lblMensagem.setHeight(13);
        lblMensagem.setCaption("Mensagem....");
        lblMensagem.setFontColor("clNavy");
        lblMensagem.setFontSize(-11);
        lblMensagem.setFontName("Tahoma");
        lblMensagem.setFontStyle("[]");
        lblMensagem.setVerticalAlignment("taAlignTop");
        lblMensagem.setWordBreak(false);
        FHBox4.addChildren(lblMensagem);
        lblMensagem.applyProperties();
    }

    public TFPageControl pgPrincipal = new TFPageControl();

    private void init_pgPrincipal() {
        pgPrincipal.setName("pgPrincipal");
        pgPrincipal.setLeft(2);
        pgPrincipal.setTop(75);
        pgPrincipal.setWidth(747);
        pgPrincipal.setHeight(426);
        pgPrincipal.setTabPosition("tpTop");
        pgPrincipal.setFlexVflex("ftTrue");
        pgPrincipal.setFlexHflex("ftTrue");
        pgPrincipal.setRenderStyle("rsTabbed");
        pgPrincipal.applyProperties();
        FrmCadAreaDeContato.addChildren(pgPrincipal);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setVisible(true);
        tabListagem.setClosable(false);
        pgPrincipal.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFHBox hbListagem = new TFHBox();

    private void init_hbListagem() {
        hbListagem.setName("hbListagem");
        hbListagem.setLeft(0);
        hbListagem.setTop(0);
        hbListagem.setWidth(739);
        hbListagem.setHeight(398);
        hbListagem.setAlign("alClient");
        hbListagem.setBorderStyle("stNone");
        hbListagem.setPaddingTop(5);
        hbListagem.setPaddingLeft(5);
        hbListagem.setPaddingRight(5);
        hbListagem.setPaddingBottom(5);
        hbListagem.setMarginTop(0);
        hbListagem.setMarginLeft(0);
        hbListagem.setMarginRight(0);
        hbListagem.setMarginBottom(0);
        hbListagem.setSpacing(1);
        hbListagem.setFlexVflex("ftTrue");
        hbListagem.setFlexHflex("ftTrue");
        hbListagem.setScrollable(false);
        hbListagem.setBoxShadowConfigHorizontalLength(10);
        hbListagem.setBoxShadowConfigVerticalLength(10);
        hbListagem.setBoxShadowConfigBlurRadius(5);
        hbListagem.setBoxShadowConfigSpreadRadius(0);
        hbListagem.setBoxShadowConfigShadowColor("clBlack");
        hbListagem.setBoxShadowConfigOpacity(75);
        hbListagem.setVAlign("tvTop");
        tabListagem.addChildren(hbListagem);
        hbListagem.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(739);
        FVBox1.setHeight(398);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setColor("clWhite");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        hbListagem.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGroupbox grpBoxFiltro = new TFGroupbox();

    private void init_grpBoxFiltro() {
        grpBoxFiltro.setName("grpBoxFiltro");
        grpBoxFiltro.setLeft(0);
        grpBoxFiltro.setTop(0);
        grpBoxFiltro.setWidth(719);
        grpBoxFiltro.setHeight(74);
        grpBoxFiltro.setCaption("Filtro R\u00E1pido");
        grpBoxFiltro.setFontColor("clWindowText");
        grpBoxFiltro.setFontSize(-11);
        grpBoxFiltro.setFontName("Tahoma");
        grpBoxFiltro.setFontStyle("[]");
        grpBoxFiltro.setFlexVflex("ftMin");
        grpBoxFiltro.setFlexHflex("ftTrue");
        grpBoxFiltro.setScrollable(false);
        grpBoxFiltro.setClosable(true);
        grpBoxFiltro.setClosed(false);
        grpBoxFiltro.setOrient("coHorizontal");
        grpBoxFiltro.setStyle("grp3D");
        grpBoxFiltro.setHeaderImageId(0);
        FVBox1.addChildren(grpBoxFiltro);
        grpBoxFiltro.applyProperties();
    }

    public TFGridPanel gpFiltroPrincipal = new TFGridPanel();

    private void init_gpFiltroPrincipal() {
        gpFiltroPrincipal.setName("gpFiltroPrincipal");
        gpFiltroPrincipal.setLeft(2);
        gpFiltroPrincipal.setTop(15);
        gpFiltroPrincipal.setWidth(715);
        gpFiltroPrincipal.setHeight(47);
        gpFiltroPrincipal.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(58.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(80.924855491329480000);
        gpFiltroPrincipal.getColumnCollection().add(item1);
        TFGridPanelColumn item2 = new TFGridPanelColumn();
        item2.setValue(19.075144508670520000);
        gpFiltroPrincipal.getColumnCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(0);
        item3.setControl("lfAreaContato");
        item3.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("efAreaContato");
        item4.setRow(1);
        gpFiltroPrincipal.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("lfDescricao");
        item5.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(1);
        item6.setControl("efDescricao");
        item6.setRow(1);
        gpFiltroPrincipal.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(2);
        item7.setControl("lfAtivoEquals");
        item7.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(2);
        item8.setControl("efAtivoEquals");
        item8.setRow(1);
        gpFiltroPrincipal.getControlCollection().add(item8);
        TFGridPanelRow item9 = new TFGridPanelRow();
        item9.setSizeStyle("ssAbsolute");
        item9.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item9);
        TFGridPanelRow item10 = new TFGridPanelRow();
        item10.setSizeStyle("ssAbsolute");
        item10.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item10);
        gpFiltroPrincipal.setFlexVflex("ftTrue");
        gpFiltroPrincipal.setFlexHflex("ftTrue");
        gpFiltroPrincipal.setAllRowFlex(true);
        gpFiltroPrincipal.setColumnTabOrder(false);
        grpBoxFiltro.addChildren(gpFiltroPrincipal);
        gpFiltroPrincipal.applyProperties();
    }

    public TFLabel lfAreaContato = new TFLabel();

    private void init_lfAreaContato() {
        lfAreaContato.setName("lfAreaContato");
        lfAreaContato.setLeft(1);
        lfAreaContato.setTop(1);
        lfAreaContato.setWidth(33);
        lfAreaContato.setHeight(21);
        lfAreaContato.setAlign("alLeft");
        lfAreaContato.setCaption("C\u00F3digo");
        lfAreaContato.setFontColor("clWindowText");
        lfAreaContato.setFontSize(-11);
        lfAreaContato.setFontName("Tahoma");
        lfAreaContato.setFontStyle("[]");
        lfAreaContato.setVerticalAlignment("taAlignBottom");
        lfAreaContato.setWordBreak(false);
        gpFiltroPrincipal.addChildren(lfAreaContato);
        lfAreaContato.applyProperties();
    }

    public TFString efAreaContato = new TFString();

    private void init_efAreaContato() {
        efAreaContato.setName("efAreaContato");
        efAreaContato.setLeft(1);
        efAreaContato.setTop(22);
        efAreaContato.setWidth(38);
        efAreaContato.setHeight(21);
        efAreaContato.setHint("Filtro: Area contato");
        efAreaContato.setFlex(false);
        efAreaContato.setRequired(false);
        efAreaContato.setConstraintCheckWhen("cwImmediate");
        efAreaContato.setConstraintCheckType("ctExpression");
        efAreaContato.setConstraintFocusOnError(false);
        efAreaContato.setConstraintEnableUI(true);
        efAreaContato.setConstraintEnabled(false);
        efAreaContato.setConstraintFormCheck(true);
        efAreaContato.setCharCase("ccNormal");
        efAreaContato.setPwd(false);
        efAreaContato.setMaxlength(0);
        efAreaContato.setAlign("alLeft");
        efAreaContato.setFontColor("clWindowText");
        efAreaContato.setFontSize(-13);
        efAreaContato.setFontName("Tahoma");
        efAreaContato.setFontStyle("[]");
        efAreaContato.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            efAreaContatoEnter(event);
            processarFlow("FrmCadAreaDeContato", "efAreaContato", "OnEnter");
        });
        efAreaContato.setSaveLiteralCharacter(false);
        efAreaContato.applyProperties();
        gpFiltroPrincipal.addChildren(efAreaContato);
        addValidatable(efAreaContato);
    }

    public TFLabel lfDescricao = new TFLabel();

    private void init_lfDescricao() {
        lfDescricao.setName("lfDescricao");
        lfDescricao.setLeft(59);
        lfDescricao.setTop(1);
        lfDescricao.setWidth(46);
        lfDescricao.setHeight(21);
        lfDescricao.setAlign("alLeft");
        lfDescricao.setCaption("Descri\u00E7\u00E3o");
        lfDescricao.setFontColor("clWindowText");
        lfDescricao.setFontSize(-11);
        lfDescricao.setFontName("Tahoma");
        lfDescricao.setFontStyle("[]");
        lfDescricao.setVerticalAlignment("taAlignBottom");
        lfDescricao.setWordBreak(false);
        gpFiltroPrincipal.addChildren(lfDescricao);
        lfDescricao.applyProperties();
    }

    public TFString efDescricao = new TFString();

    private void init_efDescricao() {
        efDescricao.setName("efDescricao");
        efDescricao.setLeft(59);
        efDescricao.setTop(22);
        efDescricao.setWidth(400);
        efDescricao.setHeight(21);
        efDescricao.setHint("Filtro: Descri\u00E7\u00E3o");
        efDescricao.setFlex(true);
        efDescricao.setRequired(false);
        efDescricao.setConstraintCheckWhen("cwImmediate");
        efDescricao.setConstraintCheckType("ctExpression");
        efDescricao.setConstraintFocusOnError(false);
        efDescricao.setConstraintEnableUI(true);
        efDescricao.setConstraintEnabled(false);
        efDescricao.setConstraintFormCheck(true);
        efDescricao.setCharCase("ccNormal");
        efDescricao.setPwd(false);
        efDescricao.setMaxlength(0);
        efDescricao.setAlign("alLeft");
        efDescricao.setFontColor("clWindowText");
        efDescricao.setFontSize(-13);
        efDescricao.setFontName("Tahoma");
        efDescricao.setFontStyle("[]");
        efDescricao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            efDescricaoEnter(event);
            processarFlow("FrmCadAreaDeContato", "efDescricao", "OnEnter");
        });
        efDescricao.setSaveLiteralCharacter(false);
        efDescricao.applyProperties();
        gpFiltroPrincipal.addChildren(efDescricao);
        addValidatable(efDescricao);
    }

    public TFLabel lfAtivoEquals = new TFLabel();

    private void init_lfAtivoEquals() {
        lfAtivoEquals.setName("lfAtivoEquals");
        lfAtivoEquals.setLeft(589);
        lfAtivoEquals.setTop(1);
        lfAtivoEquals.setWidth(25);
        lfAtivoEquals.setHeight(21);
        lfAtivoEquals.setAlign("alLeft");
        lfAtivoEquals.setCaption("Ativo");
        lfAtivoEquals.setFontColor("clWindowText");
        lfAtivoEquals.setFontSize(-11);
        lfAtivoEquals.setFontName("Tahoma");
        lfAtivoEquals.setFontStyle("[]");
        lfAtivoEquals.setVerticalAlignment("taAlignBottom");
        lfAtivoEquals.setWordBreak(false);
        gpFiltroPrincipal.addChildren(lfAtivoEquals);
        lfAtivoEquals.applyProperties();
    }

    public TFCombo efAtivoEquals = new TFCombo();

    private void init_efAtivoEquals() {
        efAtivoEquals.setName("efAtivoEquals");
        efAtivoEquals.setLeft(589);
        efAtivoEquals.setTop(22);
        efAtivoEquals.setWidth(80);
        efAtivoEquals.setHeight(21);
        efAtivoEquals.setHint("Filtro: Ativo ");
        efAtivoEquals.setLookupTable(tbSimNao);
        efAtivoEquals.setLookupKey("FIELD_KEY");
        efAtivoEquals.setLookupDesc("FIELD_DESC");
        efAtivoEquals.setFlex(false);
        efAtivoEquals.setReadOnly(true);
        efAtivoEquals.setRequired(false);
        efAtivoEquals.setPrompt("Selecione");
        efAtivoEquals.setConstraintCheckWhen("cwImmediate");
        efAtivoEquals.setConstraintCheckType("ctExpression");
        efAtivoEquals.setConstraintFocusOnError(false);
        efAtivoEquals.setConstraintEnableUI(true);
        efAtivoEquals.setConstraintEnabled(false);
        efAtivoEquals.setConstraintFormCheck(true);
        efAtivoEquals.setClearOnDelKey(true);
        efAtivoEquals.setUseClearButton(false);
        efAtivoEquals.setHideClearButtonOnNullValue(false);
        efAtivoEquals.setAlign("alLeft");
        efAtivoEquals.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            efAtivoEqualsEnter(event);
            processarFlow("FrmCadAreaDeContato", "efAtivoEquals", "OnEnter");
        });
        gpFiltroPrincipal.addChildren(efAtivoEquals);
        efAtivoEquals.applyProperties();
        addValidatable(efAtivoEquals);
    }

    public TFGrid gridPrincipal = new TFGrid();

    private void init_gridPrincipal() {
        gridPrincipal.setName("gridPrincipal");
        gridPrincipal.setLeft(0);
        gridPrincipal.setTop(75);
        gridPrincipal.setWidth(729);
        gridPrincipal.setHeight(371);
        gridPrincipal.setTable(tbClienteContatoTipo);
        gridPrincipal.setFlexVflex("ftTrue");
        gridPrincipal.setFlexHflex("ftTrue");
        gridPrincipal.setPagingEnabled(true);
        gridPrincipal.setFrozenColumns(0);
        gridPrincipal.setShowFooter(false);
        gridPrincipal.setShowHeader(true);
        gridPrincipal.setMultiSelection(false);
        gridPrincipal.setGroupingEnabled(false);
        gridPrincipal.setGroupingExpanded(false);
        gridPrincipal.setGroupingShowFooter(false);
        gridPrincipal.setCrosstabEnabled(false);
        gridPrincipal.setCrosstabGroupType("cgtConcat");
        gridPrincipal.setEditionEnabled(false);
        gridPrincipal.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption("Alt.");
        item0.setWidth(30);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setEvalType("etExpression");
        item1.setImageId(7);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageAlterar(event);
            processarFlow("FrmCadAreaDeContato", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setTitleCaption("Exc.");
        item2.setWidth(30);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("*");
        item3.setEvalType("etExpression");
        item3.setImageId(8);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageDelete(event);
            processarFlow("FrmCadAreaDeContato", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("AREA_CONTATO");
        item4.setTitleCaption("C\u00F3digo");
        item4.setWidth(144);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setHint("Area contato");
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DESCRICAO");
        item5.setTitleCaption("Descri\u00E7\u00E3o");
        item5.setWidth(550);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(true);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setHint("Descri\u00E7\u00E3o");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("ATIVO");
        item6.setTitleCaption("Ativo");
        item6.setWidth(60);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftCombo");
        item6.setFlexRatio(0);
        item6.setSort(true);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setHint("Ativo ");
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item6);
        FVBox1.addChildren(gridPrincipal);
        gridPrincipal.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        pgPrincipal.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(739);
        FVBox2.setHeight(398);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setColor("clWhite");
        FVBox2.setPaddingTop(5);
        FVBox2.setPaddingLeft(5);
        FVBox2.setPaddingRight(5);
        FVBox2.setPaddingBottom(5);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFGroupbox grpBoxPrincipal = new TFGroupbox();

    private void init_grpBoxPrincipal() {
        grpBoxPrincipal.setName("grpBoxPrincipal");
        grpBoxPrincipal.setLeft(0);
        grpBoxPrincipal.setTop(0);
        grpBoxPrincipal.setWidth(729);
        grpBoxPrincipal.setHeight(67);
        grpBoxPrincipal.setCaption("Cliente Contato Tipo");
        grpBoxPrincipal.setFontColor("clWindowText");
        grpBoxPrincipal.setFontSize(-11);
        grpBoxPrincipal.setFontName("Tahoma");
        grpBoxPrincipal.setFontStyle("[]");
        grpBoxPrincipal.setFlexVflex("ftTrue");
        grpBoxPrincipal.setFlexHflex("ftTrue");
        grpBoxPrincipal.setScrollable(true);
        grpBoxPrincipal.setClosable(true);
        grpBoxPrincipal.setClosed(false);
        grpBoxPrincipal.setOrient("coVertical");
        grpBoxPrincipal.setStyle("grp3D");
        grpBoxPrincipal.setHeaderImageId(0);
        FVBox2.addChildren(grpBoxPrincipal);
        grpBoxPrincipal.applyProperties();
    }

    public TFGridPanel FGridPanel2 = new TFGridPanel();

    private void init_FGridPanel2() {
        FGridPanel2.setName("FGridPanel2");
        FGridPanel2.setLeft(2);
        FGridPanel2.setTop(15);
        FGridPanel2.setWidth(725);
        FGridPanel2.setHeight(62);
        FGridPanel2.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(145.000000000000000000);
        FGridPanel2.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("lbAreaContato44801");
        item2.setRow(0);
        FGridPanel2.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("edAreaContato44801");
        item3.setRow(0);
        FGridPanel2.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("lbDescricao44801");
        item4.setRow(1);
        FGridPanel2.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("edDescricao44801");
        item5.setRow(1);
        FGridPanel2.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(0);
        item6.setControl("lbAtivo44801");
        item6.setRow(2);
        FGridPanel2.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(1);
        item7.setControl("edAtivo44801");
        item7.setRow(2);
        FGridPanel2.getControlCollection().add(item7);
        TFGridPanelRow item8 = new TFGridPanelRow();
        item8.setSizeStyle("ssAbsolute");
        item8.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item8);
        TFGridPanelRow item9 = new TFGridPanelRow();
        item9.setSizeStyle("ssAbsolute");
        item9.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item9);
        TFGridPanelRow item10 = new TFGridPanelRow();
        item10.setSizeStyle("ssAbsolute");
        item10.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item10);
        FGridPanel2.setFlexVflex("ftFalse");
        FGridPanel2.setFlexHflex("ftTrue");
        FGridPanel2.setAllRowFlex(true);
        FGridPanel2.setColumnTabOrder(false);
        grpBoxPrincipal.addChildren(FGridPanel2);
        FGridPanel2.applyProperties();
    }

    public TFLabel lbDescricao44801 = new TFLabel();

    private void init_lbDescricao44801() {
        lbDescricao44801.setName("lbDescricao44801");
        lbDescricao44801.setLeft(100);
        lbDescricao44801.setTop(20);
        lbDescricao44801.setWidth(46);
        lbDescricao44801.setHeight(19);
        lbDescricao44801.setAlign("alRight");
        lbDescricao44801.setCaption("Descri\u00E7\u00E3o");
        lbDescricao44801.setFontColor("clWindowText");
        lbDescricao44801.setFontSize(-11);
        lbDescricao44801.setFontName("Tahoma");
        lbDescricao44801.setFontStyle("[]");
        lbDescricao44801.setVerticalAlignment("taVerticalCenter");
        lbDescricao44801.setWordBreak(false);
        FGridPanel2.addChildren(lbDescricao44801);
        lbDescricao44801.applyProperties();
    }

    public TFLabel lbAreaContato44801 = new TFLabel();

    private void init_lbAreaContato44801() {
        lbAreaContato44801.setName("lbAreaContato44801");
        lbAreaContato44801.setLeft(113);
        lbAreaContato44801.setTop(1);
        lbAreaContato44801.setWidth(33);
        lbAreaContato44801.setHeight(19);
        lbAreaContato44801.setAlign("alRight");
        lbAreaContato44801.setCaption("C\u00F3digo");
        lbAreaContato44801.setFontColor("clWindowText");
        lbAreaContato44801.setFontSize(-11);
        lbAreaContato44801.setFontName("Tahoma");
        lbAreaContato44801.setFontStyle("[]");
        lbAreaContato44801.setVerticalAlignment("taVerticalCenter");
        lbAreaContato44801.setWordBreak(false);
        FGridPanel2.addChildren(lbAreaContato44801);
        lbAreaContato44801.applyProperties();
    }

    public TFLabel lbAtivo44801 = new TFLabel();

    private void init_lbAtivo44801() {
        lbAtivo44801.setName("lbAtivo44801");
        lbAtivo44801.setLeft(121);
        lbAtivo44801.setTop(39);
        lbAtivo44801.setWidth(25);
        lbAtivo44801.setHeight(19);
        lbAtivo44801.setAlign("alRight");
        lbAtivo44801.setCaption("Ativo");
        lbAtivo44801.setFontColor("clWindowText");
        lbAtivo44801.setFontSize(-11);
        lbAtivo44801.setFontName("Tahoma");
        lbAtivo44801.setFontStyle("[]");
        lbAtivo44801.setVerticalAlignment("taVerticalCenter");
        lbAtivo44801.setWordBreak(false);
        FGridPanel2.addChildren(lbAtivo44801);
        lbAtivo44801.applyProperties();
    }

    public TFString edAreaContato44801 = new TFString();

    private void init_edAreaContato44801() {
        edAreaContato44801.setName("edAreaContato44801");
        edAreaContato44801.setLeft(146);
        edAreaContato44801.setTop(1);
        edAreaContato44801.setWidth(38);
        edAreaContato44801.setHeight(19);
        edAreaContato44801.setHint("Area contato");
        edAreaContato44801.setTable(tbClienteContatoTipo);
        edAreaContato44801.setFieldName("AREA_CONTATO");
        edAreaContato44801.setHelpCaption("C\u00F3digo");
        edAreaContato44801.setFlex(false);
        edAreaContato44801.setRequired(true);
        edAreaContato44801.setConstraintExpression("value is null or trim(value) = ''");
        edAreaContato44801.setConstraintMessage("Campo C\u00F3digo, preenchimento \u00E9 obrigat\u00F3rio");
        edAreaContato44801.setConstraintCheckWhen("cwImmediate");
        edAreaContato44801.setConstraintCheckType("ctExpression");
        edAreaContato44801.setConstraintFocusOnError(false);
        edAreaContato44801.setConstraintGroupName("grpTbclientecontatotipo");
        edAreaContato44801.setConstraintEnableUI(true);
        edAreaContato44801.setConstraintEnabled(true);
        edAreaContato44801.setConstraintFormCheck(true);
        edAreaContato44801.setCharCase("ccNormal");
        edAreaContato44801.setPwd(false);
        edAreaContato44801.setMaxlength(3);
        edAreaContato44801.setAlign("alLeft");
        edAreaContato44801.setFontColor("clWindowText");
        edAreaContato44801.setFontSize(-13);
        edAreaContato44801.setFontName("Tahoma");
        edAreaContato44801.setFontStyle("[]");
        edAreaContato44801.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edAreaContato44801Exit(event);
            processarFlow("FrmCadAreaDeContato", "edAreaContato44801", "OnExit");
        });
        edAreaContato44801.setSaveLiteralCharacter(false);
        edAreaContato44801.applyProperties();
        FGridPanel2.addChildren(edAreaContato44801);
        addValidatable(edAreaContato44801);
    }

    public TFString edDescricao44801 = new TFString();

    private void init_edDescricao44801() {
        edDescricao44801.setName("edDescricao44801");
        edDescricao44801.setLeft(146);
        edDescricao44801.setTop(20);
        edDescricao44801.setWidth(550);
        edDescricao44801.setHeight(19);
        edDescricao44801.setHint("Descri\u00E7\u00E3o");
        edDescricao44801.setTable(tbClienteContatoTipo);
        edDescricao44801.setFieldName("DESCRICAO");
        edDescricao44801.setHelpCaption("Descri\u00E7\u00E3o");
        edDescricao44801.setFlex(true);
        edDescricao44801.setRequired(true);
        edDescricao44801.setConstraintExpression("value is null or trim(value) = ''");
        edDescricao44801.setConstraintMessage("Campo Descri\u00E7\u00E3o, preenchimento \u00E9 obrigat\u00F3rio");
        edDescricao44801.setConstraintCheckWhen("cwImmediate");
        edDescricao44801.setConstraintCheckType("ctExpression");
        edDescricao44801.setConstraintFocusOnError(false);
        edDescricao44801.setConstraintGroupName("grpTbclientecontatotipo");
        edDescricao44801.setConstraintEnableUI(true);
        edDescricao44801.setConstraintEnabled(true);
        edDescricao44801.setConstraintFormCheck(true);
        edDescricao44801.setCharCase("ccNormal");
        edDescricao44801.setPwd(false);
        edDescricao44801.setMaxlength(100);
        edDescricao44801.setAlign("alLeft");
        edDescricao44801.setFontColor("clWindowText");
        edDescricao44801.setFontSize(-13);
        edDescricao44801.setFontName("Tahoma");
        edDescricao44801.setFontStyle("[]");
        edDescricao44801.setSaveLiteralCharacter(false);
        edDescricao44801.applyProperties();
        FGridPanel2.addChildren(edDescricao44801);
        addValidatable(edDescricao44801);
    }

    public TFCombo edAtivo44801 = new TFCombo();

    private void init_edAtivo44801() {
        edAtivo44801.setName("edAtivo44801");
        edAtivo44801.setLeft(146);
        edAtivo44801.setTop(39);
        edAtivo44801.setWidth(80);
        edAtivo44801.setHeight(21);
        edAtivo44801.setHint("Ativo ");
        edAtivo44801.setTable(tbClienteContatoTipo);
        edAtivo44801.setLookupTable(tbSimNao);
        edAtivo44801.setFieldName("ATIVO");
        edAtivo44801.setLookupKey("FIELD_KEY");
        edAtivo44801.setLookupDesc("FIELD_DESC");
        edAtivo44801.setFlex(false);
        edAtivo44801.setHelpCaption("Ativo");
        edAtivo44801.setReadOnly(false);
        edAtivo44801.setRequired(true);
        edAtivo44801.setPrompt("Selecione");
        edAtivo44801.setConstraintExpression("value is null");
        edAtivo44801.setConstraintMessage("Campo Ativo, preenchimento \u00E9 obrigat\u00F3rio");
        edAtivo44801.setConstraintCheckWhen("cwImmediate");
        edAtivo44801.setConstraintCheckType("ctExpression");
        edAtivo44801.setConstraintFocusOnError(false);
        edAtivo44801.setConstraintGroupName("grpTbclientecontatotipo");
        edAtivo44801.setConstraintEnableUI(true);
        edAtivo44801.setConstraintEnabled(true);
        edAtivo44801.setConstraintFormCheck(true);
        edAtivo44801.setClearOnDelKey(true);
        edAtivo44801.setUseClearButton(false);
        edAtivo44801.setHideClearButtonOnNullValue(false);
        edAtivo44801.setAlign("alLeft");
        FGridPanel2.addChildren(edAtivo44801);
        edAtivo44801.applyProperties();
        addValidatable(edAtivo44801);
    }

    public TFSchema scClienteContatoTipo;

    private void init_scClienteContatoTipo() {
        scClienteContatoTipo = rn.scClienteContatoTipo;
        scClienteContatoTipo.setName("scClienteContatoTipo");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteContatoTipo);
        scClienteContatoTipo.getTables().add(item0);
        scClienteContatoTipo.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionPesquisar(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionIncluir(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionAlterar(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionExcluir(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionSalvar(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionCancelar(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionAnterior(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionProximo(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionAceitar(final Event<Object> event);

    public abstract void FrmCadAreaDeContatokeyActionSalvarContinuar(final Event<Object> event);

    public abstract void efAreaContatoEnter(final Event<Object> event);

    public abstract void efDescricaoEnter(final Event<Object> event);

    public abstract void efAtivoEqualsEnter(final Event<Object> event);

    public abstract void gridPrincipalClickImageAlterar(final Event<Object> event);

    public abstract void gridPrincipalClickImageDelete(final Event<Object> event);

    public abstract void edAreaContato44801Exit(final Event<Object> event);

    public void btnConsultarClick(final Event<Object> event) {
        if (btnConsultar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnFiltroAvancadoClick(final Event<Object> event) {
        if (btnFiltroAvancado.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnFiltroAvancado");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarContinuarClick(final Event<Object> event) {
        if (btnSalvarContinuar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarContinuar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAnteriorClick(final Event<Object> event) {
        if (btnAnterior.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAnterior");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnProximoClick(final Event<Object> event) {
        if (btnProximo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnProximo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnMaisClick(final Event<Object> event) {
        if (btnMais.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnMais");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void menuItemAbreTabelaAuxClick(final Event<Object> event);

    public abstract void menuHabilitaNavegacaoClick(final Event<Object> event);

    public abstract void menuSelecaoMultiplaClick(final Event<Object> event);

    public abstract void menuItemConfgGridClick(final Event<Object> event);

    public abstract void menuItemExportPdfClick(final Event<Object> event);

    public abstract void menuItemExportExcelClick(final Event<Object> event);

    public abstract void menuItemHelpClick(final Event<Object> event);

    public abstract void tbClienteContatoTipoAfterScroll(final Event<Object> event);

}