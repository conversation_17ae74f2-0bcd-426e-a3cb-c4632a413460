package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmGerencialPainelFiltro extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.GerencialPainelFiltroRNA rn = null;

    public FrmGerencialPainelFiltro() {
        try {
            rn = (freedom.bytecode.rn.GerencialPainelFiltroRNA) getRN(freedom.bytecode.rn.wizard.GerencialPainelFiltroRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbAno();
        init_tbPainelGerencialUsuario();
        init_tbEmpresaCruzadasFuncao();
        init_vboxPrincipal();
        init_FHBox1();
        init_btnVoltar();
        init_btnAceitar();
        init_btnPreferencia();
        init_FVBox1();
        init_FLabel1();
        init_cbbPaineis();
        init_FVBox2();
        init_FLabel2();
        init_cbbEmpresas();
        init_FHBox2();
        init_FVBox3();
        init_FLabel3();
        init_cbbMes();
        init_FVBox4();
        init_FLabel4();
        init_cbbAno();
        init_FrmGerencialPainelFiltro();
    }

    public ANO tbAno;

    private void init_tbAno() {
        tbAno = rn.tbAno;
        tbAno.setName("tbAno");
        tbAno.setMaxRowCount(200);
        tbAno.setWKey("382035;38201");
        tbAno.setRatioBatchSize(20);
        getTables().put(tbAno, "tbAno");
        tbAno.applyProperties();
    }

    public PAINEL_GERENCIAL_USUARIO tbPainelGerencialUsuario;

    private void init_tbPainelGerencialUsuario() {
        tbPainelGerencialUsuario = rn.tbPainelGerencialUsuario;
        tbPainelGerencialUsuario.setName("tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.setMaxRowCount(200);
        tbPainelGerencialUsuario.setWKey("382035;38202");
        tbPainelGerencialUsuario.setRatioBatchSize(20);
        getTables().put(tbPainelGerencialUsuario, "tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.applyProperties();
    }

    public EMPRESA_CRUZADAS_FUNCAO tbEmpresaCruzadasFuncao;

    private void init_tbEmpresaCruzadasFuncao() {
        tbEmpresaCruzadasFuncao = rn.tbEmpresaCruzadasFuncao;
        tbEmpresaCruzadasFuncao.setName("tbEmpresaCruzadasFuncao");
        tbEmpresaCruzadasFuncao.setMaxRowCount(200);
        tbEmpresaCruzadasFuncao.setWKey("382035;38203");
        tbEmpresaCruzadasFuncao.setRatioBatchSize(20);
        getTables().put(tbEmpresaCruzadasFuncao, "tbEmpresaCruzadasFuncao");
        tbEmpresaCruzadasFuncao.applyProperties();
    }

    protected TFForm FrmGerencialPainelFiltro = this;
    private void init_FrmGerencialPainelFiltro() {
        FrmGerencialPainelFiltro.setName("FrmGerencialPainelFiltro");
        FrmGerencialPainelFiltro.setCaption("Filtro");
        FrmGerencialPainelFiltro.setClientHeight(302);
        FrmGerencialPainelFiltro.setClientWidth(492);
        FrmGerencialPainelFiltro.setColor("clBtnFace");
        FrmGerencialPainelFiltro.setWKey("382035");
        FrmGerencialPainelFiltro.setSpacing(0);
        FrmGerencialPainelFiltro.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(492);
        vboxPrincipal.setHeight(302);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(5);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(5);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(6);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmGerencialPainelFiltro.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(476);
        FHBox1.setHeight(70);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vboxPrincipal.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(75);
        btnVoltar.setHeight(60);
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmGerencialPainelFiltro", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox1.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(75);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(75);
        btnAceitar.setHeight(60);
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmGerencialPainelFiltro", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnPreferencia = new TFButton();

    private void init_btnPreferencia() {
        btnPreferencia.setName("btnPreferencia");
        btnPreferencia.setLeft(150);
        btnPreferencia.setTop(0);
        btnPreferencia.setWidth(75);
        btnPreferencia.setHeight(60);
        btnPreferencia.setCaption("Prefer\u00EAncia");
        btnPreferencia.setFontColor("clWindowText");
        btnPreferencia.setFontSize(-11);
        btnPreferencia.setFontName("Tahoma");
        btnPreferencia.setFontStyle("[]");
        btnPreferencia.setLayout("blGlyphTop");
        btnPreferencia.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPreferenciaClick(event);
            processarFlow("FrmGerencialPainelFiltro", "btnPreferencia", "OnClick");
        });
        btnPreferencia.setImageId(382020);
        btnPreferencia.setColor("clBtnFace");
        btnPreferencia.setAccess(false);
        btnPreferencia.setIconReverseDirection(false);
        FHBox1.addChildren(btnPreferencia);
        btnPreferencia.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(71);
        FVBox1.setWidth(475);
        FVBox1.setHeight(61);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(3);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(3);
        FVBox1.setFlexVflex("ftMin");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(179);
        FLabel1.setHeight(14);
        FLabel1.setCaption("Pa\u00EDneis que a fun\u00E7\u00E3o enxerga");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-12);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cbbPaineis = new TFCombo();

    private void init_cbbPaineis() {
        cbbPaineis.setName("cbbPaineis");
        cbbPaineis.setLeft(0);
        cbbPaineis.setTop(15);
        cbbPaineis.setWidth(269);
        cbbPaineis.setHeight(21);
        cbbPaineis.setLookupTable(tbPainelGerencialUsuario);
        cbbPaineis.setLookupKey("ID");
        cbbPaineis.setLookupDesc("DESCRICAO");
        cbbPaineis.setFlex(true);
        cbbPaineis.setReadOnly(true);
        cbbPaineis.setRequired(false);
        cbbPaineis.setPrompt("Selecione");
        cbbPaineis.setConstraintCheckWhen("cwImmediate");
        cbbPaineis.setConstraintCheckType("ctExpression");
        cbbPaineis.setConstraintFocusOnError(false);
        cbbPaineis.setConstraintEnableUI(true);
        cbbPaineis.setConstraintEnabled(false);
        cbbPaineis.setConstraintFormCheck(true);
        cbbPaineis.setClearOnDelKey(true);
        cbbPaineis.setUseClearButton(false);
        cbbPaineis.setHideClearButtonOnNullValue(false);
        FVBox1.addChildren(cbbPaineis);
        cbbPaineis.applyProperties();
        addValidatable(cbbPaineis);
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(133);
        FVBox2.setWidth(475);
        FVBox2.setHeight(61);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(3);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(3);
        FVBox2.setFlexVflex("ftMin");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        vboxPrincipal.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(205);
        FLabel2.setHeight(14);
        FLabel2.setCaption("Empresas Cruzadas para a Fun\u00E7\u00E3o");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-12);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox2.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cbbEmpresas = new TFCombo();

    private void init_cbbEmpresas() {
        cbbEmpresas.setName("cbbEmpresas");
        cbbEmpresas.setLeft(0);
        cbbEmpresas.setTop(15);
        cbbEmpresas.setWidth(269);
        cbbEmpresas.setHeight(21);
        cbbEmpresas.setLookupTable(tbEmpresaCruzadasFuncao);
        cbbEmpresas.setLookupKey("CE");
        cbbEmpresas.setLookupDesc("EMPRESA");
        cbbEmpresas.setFlex(true);
        cbbEmpresas.setReadOnly(true);
        cbbEmpresas.setRequired(false);
        cbbEmpresas.setPrompt("Selecione");
        cbbEmpresas.setConstraintCheckWhen("cwImmediate");
        cbbEmpresas.setConstraintCheckType("ctExpression");
        cbbEmpresas.setConstraintFocusOnError(false);
        cbbEmpresas.setConstraintEnableUI(true);
        cbbEmpresas.setConstraintEnabled(false);
        cbbEmpresas.setConstraintFormCheck(true);
        cbbEmpresas.setClearOnDelKey(true);
        cbbEmpresas.setUseClearButton(false);
        cbbEmpresas.setHideClearButtonOnNullValue(false);
        FVBox2.addChildren(cbbEmpresas);
        cbbEmpresas.applyProperties();
        addValidatable(cbbEmpresas);
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(195);
        FHBox2.setWidth(476);
        FHBox2.setHeight(73);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(3);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vboxPrincipal.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(170);
        FVBox3.setHeight(65);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftMin");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(24);
        FLabel3.setHeight(14);
        FLabel3.setCaption("M\u00EAs");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-12);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[fsBold]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FVBox3.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFCombo cbbMes = new TFCombo();

    private void init_cbbMes() {
        cbbMes.setName("cbbMes");
        cbbMes.setLeft(0);
        cbbMes.setTop(15);
        cbbMes.setWidth(161);
        cbbMes.setHeight(21);
        cbbMes.setFlex(true);
        cbbMes.setListOptions("Janeiro=1;Fevereiro=2;Mar\u00E7o=3;Abril=4;Maio=5;Junho=6;Julho=7;Agosto=8;Setembro=9;Outubro=10;Novembro=11;Dezembro=12");
        cbbMes.setReadOnly(true);
        cbbMes.setRequired(false);
        cbbMes.setPrompt("Selecione");
        cbbMes.setConstraintCheckWhen("cwImmediate");
        cbbMes.setConstraintCheckType("ctExpression");
        cbbMes.setConstraintFocusOnError(false);
        cbbMes.setConstraintEnableUI(true);
        cbbMes.setConstraintEnabled(false);
        cbbMes.setConstraintFormCheck(true);
        cbbMes.setClearOnDelKey(true);
        cbbMes.setUseClearButton(false);
        cbbMes.setHideClearButtonOnNullValue(false);
        FVBox3.addChildren(cbbMes);
        cbbMes.applyProperties();
        addValidatable(cbbMes);
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(170);
        FVBox4.setTop(0);
        FVBox4.setWidth(130);
        FVBox4.setHeight(65);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftMin");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(25);
        FLabel4.setHeight(14);
        FLabel4.setCaption("Ano");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-12);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[fsBold]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FVBox4.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFCombo cbbAno = new TFCombo();

    private void init_cbbAno() {
        cbbAno.setName("cbbAno");
        cbbAno.setLeft(0);
        cbbAno.setTop(15);
        cbbAno.setWidth(100);
        cbbAno.setHeight(21);
        cbbAno.setLookupTable(tbAno);
        cbbAno.setLookupKey("ANO");
        cbbAno.setLookupDesc("ANO");
        cbbAno.setFlex(false);
        cbbAno.setReadOnly(true);
        cbbAno.setRequired(false);
        cbbAno.setPrompt("Selecione");
        cbbAno.setConstraintCheckWhen("cwImmediate");
        cbbAno.setConstraintCheckType("ctExpression");
        cbbAno.setConstraintFocusOnError(false);
        cbbAno.setConstraintEnableUI(true);
        cbbAno.setConstraintEnabled(false);
        cbbAno.setConstraintFormCheck(true);
        cbbAno.setClearOnDelKey(true);
        cbbAno.setUseClearButton(false);
        cbbAno.setHideClearButtonOnNullValue(false);
        FVBox4.addChildren(cbbAno);
        cbbAno.applyProperties();
        addValidatable(cbbAno);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPreferenciaClick(final Event<Object> event) {
        if (btnPreferencia.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPreferencia");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}