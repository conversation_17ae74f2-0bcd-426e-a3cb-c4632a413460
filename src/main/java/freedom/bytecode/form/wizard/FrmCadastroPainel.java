package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadastroPainel extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadastroPainelRNA rn = null;

    public FrmCadastroPainel() {
        try {
            rn = (freedom.bytecode.rn.CadastroPainelRNA) getRN(freedom.bytecode.rn.wizard.CadastroPainelRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbPainel();
        init_tbComboGrupo();
        init_tbMaxIdPainel();
        init_FrmCadastroPainel();
    }

    public BSC_PAINEL tbPainel;

    private void init_tbPainel() {
        tbPainel = rn.tbPainel;
        tbPainel.setName("tbPainel");
        tbPainel.setMaxRowCount(200);
        tbPainel.setWKey("382029;38201");
        tbPainel.setRatioBatchSize(20);
        getTables().put(tbPainel, "tbPainel");
        tbPainel.applyProperties();
    }

    public BSC_COMBO_GRUPO tbComboGrupo;

    private void init_tbComboGrupo() {
        tbComboGrupo = rn.tbComboGrupo;
        tbComboGrupo.setName("tbComboGrupo");
        tbComboGrupo.setMaxRowCount(200);
        tbComboGrupo.setWKey("382029;38202");
        tbComboGrupo.setRatioBatchSize(20);
        getTables().put(tbComboGrupo, "tbComboGrupo");
        tbComboGrupo.applyProperties();
    }

    public GET_MAX_ID_PAINEL tbMaxIdPainel;

    private void init_tbMaxIdPainel() {
        tbMaxIdPainel = rn.tbMaxIdPainel;
        tbMaxIdPainel.setName("tbMaxIdPainel");
        tbMaxIdPainel.setMaxRowCount(200);
        tbMaxIdPainel.setWKey("382029;38203");
        tbMaxIdPainel.setRatioBatchSize(20);
        getTables().put(tbMaxIdPainel, "tbMaxIdPainel");
        tbMaxIdPainel.applyProperties();
    }

    protected TFForm FrmCadastroPainel = this;
    private void init_FrmCadastroPainel() {
        FrmCadastroPainel.setName("FrmCadastroPainel");
        FrmCadastroPainel.setCaption("Novo Painel");
        FrmCadastroPainel.setClientHeight(295);
        FrmCadastroPainel.setClientWidth(556);
        FrmCadastroPainel.setColor("clBtnFace");
        FrmCadastroPainel.setWKey("382029");
        FrmCadastroPainel.setSpacing(0);
        FrmCadastroPainel.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}