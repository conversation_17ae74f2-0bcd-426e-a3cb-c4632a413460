package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmTrocarImagemLogin extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.TrocarImagemLoginRNA rn = null;

    public FrmTrocarImagemLogin() {
        try {
            rn = (freedom.bytecode.rn.TrocarImagemLoginRNA) getRN(freedom.bytecode.rn.wizard.TrocarImagemLoginRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FHBox1();
        init_btnUploadImagem();
        init_btnLimparImagem();
        init_vBoxImage();
        init_FrmTrocarImagemLogin();
    }

    protected TFForm FrmTrocarImagemLogin = this;
    private void init_FrmTrocarImagemLogin() {
        FrmTrocarImagemLogin.setName("FrmTrocarImagemLogin");
        FrmTrocarImagemLogin.setCaption("Trocar Imagem Tela Inicial");
        FrmTrocarImagemLogin.setClientHeight(491);
        FrmTrocarImagemLogin.setClientWidth(811);
        FrmTrocarImagemLogin.setColor("clBtnFace");
        FrmTrocarImagemLogin.setWKey("7000182");
        FrmTrocarImagemLogin.setSpacing(0);
        FrmTrocarImagemLogin.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(811);
        FHBox1.setHeight(65);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(3);
        FHBox1.setPaddingLeft(5);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FrmTrocarImagemLogin.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnUploadImagem = new TFButton();

    private void init_btnUploadImagem() {
        btnUploadImagem.setName("btnUploadImagem");
        btnUploadImagem.setLeft(0);
        btnUploadImagem.setTop(0);
        btnUploadImagem.setWidth(60);
        btnUploadImagem.setHeight(56);
        btnUploadImagem.setHint("Carregar Imagem");
        btnUploadImagem.setAlign("alLeft");
        btnUploadImagem.setCaption("Upload");
        btnUploadImagem.setFontColor("clWindowText");
        btnUploadImagem.setFontSize(-11);
        btnUploadImagem.setFontName("Tahoma");
        btnUploadImagem.setFontStyle("[]");
        btnUploadImagem.setLayout("blGlyphTop");
        btnUploadImagem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnUploadImagemClick(event);
            processarFlow("FrmTrocarImagemLogin", "btnUploadImagem", "OnClick");
        });
        btnUploadImagem.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001634944415478DABDD5CD2B055118C7F173B31059785978E946DD64A3"
 + "8849B664753756140B0B65E3A52456C442491245AE85973F40142B2F59CB6581"
 + "94F296241B1B6565A3F89EE6DC1AB71977AEFBCC7DEAD33453737EE79C39734E"
 + "48055CA16C05E8EB10EA84DABDC22ABE1301C5B8C18850C0126AF0E10CB84495"
 + "50C02B6AA5021A711144402176F1806674E25E32E00903384404A7CACCBB4440"
 + "3B5AD5EF4531885C2C4A0454E2C03492A86D6C9A11894CD1042C4CA30F25E84E"
 + "F71BE46318B31E211D88E20C6B8EE7BE024A718D183E319FC6E85206E8D5B08F"
 + "6A73BF80BBA45EFE3BC0320D59492F6DE0185B8E6739A8C733DEFD04F4620A2D"
 + "1E3DDBC13A8ECCFD98E9C8177A5205DC22AEEC35FE57E9514CA209E518C70CDE"
 + "B0EC1550864714F89CE33D65FFB9732EC171B780304E54E6BBA9DE268AF0A204"
 + "7753673528FB2C88B805E8036734C3005D6DE8428533405FFB95BDE4242A0F2B"
 + "38CFDAA11F58FD006094671970A9BBE70000000049454E44AE426082");
        btnUploadImagem.setImageId(7000194);
        btnUploadImagem.setColor("clBtnFace");
        btnUploadImagem.setAccess(false);
        btnUploadImagem.setIconReverseDirection(false);
        FHBox1.addChildren(btnUploadImagem);
        btnUploadImagem.applyProperties();
    }

    public TFButton btnLimparImagem = new TFButton();

    private void init_btnLimparImagem() {
        btnLimparImagem.setName("btnLimparImagem");
        btnLimparImagem.setLeft(60);
        btnLimparImagem.setTop(0);
        btnLimparImagem.setWidth(60);
        btnLimparImagem.setHeight(56);
        btnLimparImagem.setHint("Limpar Imagem");
        btnLimparImagem.setAlign("alLeft");
        btnLimparImagem.setCaption("Limpar");
        btnLimparImagem.setFontColor("clWindowText");
        btnLimparImagem.setFontSize(-11);
        btnLimparImagem.setFontName("Tahoma");
        btnLimparImagem.setFontStyle("[]");
        btnLimparImagem.setLayout("blGlyphTop");
        btnLimparImagem.setVisible(false);
        btnLimparImagem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimparImagemClick(event);
            processarFlow("FrmTrocarImagemLogin", "btnLimparImagem", "OnClick");
        });
        btnLimparImagem.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F80000016D4944415478DAEDD6CD2B055118C7F173950D29B150364A62435E36"
 + "0A795F58B3C142FC0752B2F2BA932C6D2C9562C15E79B90965E3A56CA49494A2"
 + "48E9DAA8E1FB34CFD4699A193323BBFBD4A7B973CF39F7377316CFB919F3CF95"
 + "8939AF09D5A8D2FB07DCE3EAAF01FD58464BC8F80566B09F26A00307B8C50A4E"
 + "F1AC631568C7346AD1A7E38902F650A3DBF31132A744B7E90E0351013D984581"
 + "35D686577D83A8AA4339CEACEF1CCCE1C40B90ED5842191A718D7793AC4AADB5"
 + "6FFE00AFBA71A46F944D1810B8F6B700B98E6142C727F18535BD5FC7863C69DA"
 + "8005CC5BF3B2D63CA96F2CEABC7C403E201F1033403AE72586B06BDC7E228BBD"
 + "2678A8D75EBD3A3A2E7D6C103B6836D641E40F901F7A316EC392F65B892EE3B6"
 + "0329698A85FAA452E338C6A371DB7B8371CF0A272C406A049BC6ED2FB23DE7F8"
 + "34C1558456DD26091FC5963D21ECC019C6AABE419C7AC214B6FD0351275A313A"
 + "51AF9F832A871BDDA65CD084B8FF2A52D70F1A0B8E192C1E0DF5000000004945"
 + "4E44AE426082");
        btnLimparImagem.setImageId(4600235);
        btnLimparImagem.setColor("clBtnFace");
        btnLimparImagem.setAccess(false);
        btnLimparImagem.setIconReverseDirection(false);
        FHBox1.addChildren(btnLimparImagem);
        btnLimparImagem.applyProperties();
    }

    public TFVBox vBoxImage = new TFVBox();

    private void init_vBoxImage() {
        vBoxImage.setName("vBoxImage");
        vBoxImage.setLeft(0);
        vBoxImage.setTop(65);
        vBoxImage.setWidth(811);
        vBoxImage.setHeight(426);
        vBoxImage.setAlign("alClient");
        vBoxImage.setBorderStyle("stNone");
        vBoxImage.setPaddingTop(5);
        vBoxImage.setPaddingLeft(5);
        vBoxImage.setPaddingRight(5);
        vBoxImage.setPaddingBottom(5);
        vBoxImage.setMarginTop(0);
        vBoxImage.setMarginLeft(0);
        vBoxImage.setMarginRight(0);
        vBoxImage.setMarginBottom(0);
        vBoxImage.setSpacing(1);
        vBoxImage.setFlexVflex("ftTrue");
        vBoxImage.setFlexHflex("ftTrue");
        vBoxImage.setScrollable(false);
        vBoxImage.setBoxShadowConfigHorizontalLength(10);
        vBoxImage.setBoxShadowConfigVerticalLength(10);
        vBoxImage.setBoxShadowConfigBlurRadius(5);
        vBoxImage.setBoxShadowConfigSpreadRadius(0);
        vBoxImage.setBoxShadowConfigShadowColor("clBlack");
        vBoxImage.setBoxShadowConfigOpacity(75);
        FrmTrocarImagemLogin.addChildren(vBoxImage);
        vBoxImage.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnUploadImagemClick(final Event<Object> event) {
        if (btnUploadImagem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnUploadImagem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnLimparImagemClick(final Event<Object> event) {
        if (btnLimparImagem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimparImagem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}