package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmChatBotAtivo extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ChatBotAtivoRNA rn = null;

    public FrmChatBotAtivo() {
        try {
            rn = (freedom.bytecode.rn.ChatBotAtivoRNA) getRN(freedom.bytecode.rn.wizard.ChatBotAtivoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbDisparo2();
        init_tbDisparoChatbot();
        init_tbCadastroWhatsapp();
        init_tbEmailModelo();
        init_tbDescartes();
        init_tbDisparoTemplateEmpresa();
        init_tbEmpresas();
        init_tbWhatsappEmpresa();
        init_FVBox1();
        init_FHBox1();
        init_btnConsultarDisparo();
        init_btnAlterarDisparo();
        init_btnSalvarDisparo();
        init_btnCancelarDisparo();
        init_FHBox3();
        init_PageControlDisparo();
        init_tabListagem();
        init_FVBox2();
        init_FHBox2();
        init_FVBox5();
        init_lfDescricao();
        init_fTbxDisparoDescricao();
        init_FVBox4();
        init_fchkDisparoAtivo();
        init_gridDisparo();
        init_tabCadastro();
        init_FVBox11();
        init_FGroupbox2();
        init_FVBox9();
        init_FHBox16();
        init_FHBox17();
        init_FHBox4();
        init_FLabel4();
        init_etxbDisparoDescricao();
        init_FHBox19();
        init_FHBox20();
        init_FHBox21();
        init_FLabel6();
        init_ecmbDisparoTemplate();
        init_FHBox7();
        init_FHBox10();
        init_FHBox12();
        init_FLabel9();
        init_cbbAplServico();
        init_FHBox5();
        init_FHBox6();
        init_echkDisparoAtivo();
        init_tabChatBot();
        init_FVBox3();
        init_FVBox15();
        init_gridChatBot();
        init_FVBox6();
        init_FHBox11();
        init_btnAlterarChatBot();
        init_btnSalvarChatBot();
        init_btnCancelarChatBot();
        init_FHBox8();
        init_FVBox7();
        init_FLabel1();
        init_etxChatBotbDescricaoAlternativa();
        init_FVBox8();
        init_FLabel2();
        init_etxbChatBotMascara();
        init_FVBox10();
        init_echkChatBotAtivo();
        init_FVBox12();
        init_FLabel3();
        init_etxbChatBotLink();
        init_FVBox13();
        init_FLabel5();
        init_ecmbMotivoPerda();
        init_FVBox14();
        init_FLabel7();
        init_ememoChatBotMensagemRetorno();
        init_tabTemplates();
        init_FVBox16();
        init_FVBox17();
        init_gridTemplate();
        init_FVBox18();
        init_FHBox9();
        init_btnNovoTemplate();
        init_btnAlterarTemplate();
        init_btnExcluirTemplate();
        init_btnSalvarTemplate();
        init_btnCancelarTemplate();
        init_FVBox23();
        init_FLabel11();
        init_ecmbTemplateEmpresa();
        init_FVBox19();
        init_FLabel8();
        init_ecmbTemplateTemplate();
        init_FrmChatBotAtivo();
    }

    public CRM_DISPARO tbDisparo2;

    private void init_tbDisparo2() {
        tbDisparo2 = rn.tbDisparo2;
        tbDisparo2.setName("tbDisparo2");
        tbDisparo2.setMaxRowCount(200);
        tbDisparo2.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbDisparo2AfterScroll(event);
            processarFlow("FrmChatBotAtivo", "tbDisparo2", "OnAfterScroll");
        });
        tbDisparo2.setWKey("4600661;460012");
        tbDisparo2.setRatioBatchSize(20);
        getTables().put(tbDisparo2, "tbDisparo2");
        tbDisparo2.applyProperties();
    }

    public CRM_DISPARO_CHATBOT tbDisparoChatbot;

    private void init_tbDisparoChatbot() {
        tbDisparoChatbot = rn.tbDisparoChatbot;
        tbDisparoChatbot.setName("tbDisparoChatbot");
        tbDisparoChatbot.setMaxRowCount(200);
        tbDisparoChatbot.setWKey("4600661;46002");
        tbDisparoChatbot.setRatioBatchSize(20);
        getTables().put(tbDisparoChatbot, "tbDisparoChatbot");
        tbDisparoChatbot.applyProperties();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp;

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp = rn.tbCadastroWhatsapp;
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("4600661;46005");
        tbCadastroWhatsapp.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsapp, "tbCadastroWhatsapp");
        tbCadastroWhatsapp.applyProperties();
    }

    public CRM_EMAIL_MODELO tbEmailModelo;

    private void init_tbEmailModelo() {
        tbEmailModelo = rn.tbEmailModelo;
        tbEmailModelo.setName("tbEmailModelo");
        tbEmailModelo.setMaxRowCount(200);
        tbEmailModelo.setWKey("4600661;46004");
        tbEmailModelo.setRatioBatchSize(20);
        getTables().put(tbEmailModelo, "tbEmailModelo");
        tbEmailModelo.applyProperties();
    }

    public CRM_DESCARTES tbDescartes;

    private void init_tbDescartes() {
        tbDescartes = rn.tbDescartes;
        tbDescartes.setName("tbDescartes");
        tbDescartes.setMaxRowCount(200);
        tbDescartes.setWKey("4600661;46006");
        tbDescartes.setRatioBatchSize(20);
        getTables().put(tbDescartes, "tbDescartes");
        tbDescartes.applyProperties();
    }

    public CRM_DISPARO_TEMPLATE_EMPRESA tbDisparoTemplateEmpresa;

    private void init_tbDisparoTemplateEmpresa() {
        tbDisparoTemplateEmpresa = rn.tbDisparoTemplateEmpresa;
        tbDisparoTemplateEmpresa.setName("tbDisparoTemplateEmpresa");
        tbDisparoTemplateEmpresa.setMaxRowCount(200);
        tbDisparoTemplateEmpresa.setWKey("4600661;46007");
        tbDisparoTemplateEmpresa.setRatioBatchSize(20);
        getTables().put(tbDisparoTemplateEmpresa, "tbDisparoTemplateEmpresa");
        tbDisparoTemplateEmpresa.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("4600661;46008");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public CRM_WHATSAPP_EMPRESA tbWhatsappEmpresa;

    private void init_tbWhatsappEmpresa() {
        tbWhatsappEmpresa = rn.tbWhatsappEmpresa;
        tbWhatsappEmpresa.setName("tbWhatsappEmpresa");
        tbWhatsappEmpresa.setMaxRowCount(200);
        tbWhatsappEmpresa.setWKey("4600661;460011");
        tbWhatsappEmpresa.setRatioBatchSize(20);
        getTables().put(tbWhatsappEmpresa, "tbWhatsappEmpresa");
        tbWhatsappEmpresa.applyProperties();
    }

    protected TFForm FrmChatBotAtivo = this;
    private void init_FrmChatBotAtivo() {
        FrmChatBotAtivo.setName("FrmChatBotAtivo");
        FrmChatBotAtivo.setCaption("ChatBot Ativo");
        FrmChatBotAtivo.setClientHeight(541);
        FrmChatBotAtivo.setClientWidth(887);
        FrmChatBotAtivo.setColor("clBtnFace");
        FrmChatBotAtivo.setWOrigem("EhMain");
        FrmChatBotAtivo.setWKey("4600661");
        FrmChatBotAtivo.setSpacing(0);
        FrmChatBotAtivo.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(887);
        FVBox1.setHeight(541);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmChatBotAtivo.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(884);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnConsultarDisparo = new TFButton();

    private void init_btnConsultarDisparo() {
        btnConsultarDisparo.setName("btnConsultarDisparo");
        btnConsultarDisparo.setLeft(0);
        btnConsultarDisparo.setTop(0);
        btnConsultarDisparo.setWidth(65);
        btnConsultarDisparo.setHeight(53);
        btnConsultarDisparo.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultarDisparo.setCaption("Pesquisar");
        btnConsultarDisparo.setFontColor("clWindowText");
        btnConsultarDisparo.setFontSize(-11);
        btnConsultarDisparo.setFontName("Tahoma");
        btnConsultarDisparo.setFontStyle("[]");
        btnConsultarDisparo.setLayout("blGlyphTop");
        btnConsultarDisparo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            filtrarGrid(event);
            processarFlow("FrmChatBotAtivo", "btnConsultarDisparo", "OnClick");
        });
        btnConsultarDisparo.setImageId(13);
        btnConsultarDisparo.setColor("clBtnFace");
        btnConsultarDisparo.setAccess(true);
        btnConsultarDisparo.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultarDisparo);
        btnConsultarDisparo.applyProperties();
    }

    public TFButton btnAlterarDisparo = new TFButton();

    private void init_btnAlterarDisparo() {
        btnAlterarDisparo.setName("btnAlterarDisparo");
        btnAlterarDisparo.setLeft(65);
        btnAlterarDisparo.setTop(0);
        btnAlterarDisparo.setWidth(65);
        btnAlterarDisparo.setHeight(53);
        btnAlterarDisparo.setHint("Altera o Registro Selecionado");
        btnAlterarDisparo.setCaption("Alterar");
        btnAlterarDisparo.setFontColor("clWindowText");
        btnAlterarDisparo.setFontSize(-11);
        btnAlterarDisparo.setFontName("Tahoma");
        btnAlterarDisparo.setFontStyle("[]");
        btnAlterarDisparo.setLayout("blGlyphTop");
        btnAlterarDisparo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarDisparoClick(event);
            processarFlow("FrmChatBotAtivo", "btnAlterarDisparo", "OnClick");
        });
        btnAlterarDisparo.setImageId(7);
        btnAlterarDisparo.setColor("clBtnFace");
        btnAlterarDisparo.setAccess(true);
        btnAlterarDisparo.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterarDisparo);
        btnAlterarDisparo.applyProperties();
    }

    public TFButton btnSalvarDisparo = new TFButton();

    private void init_btnSalvarDisparo() {
        btnSalvarDisparo.setName("btnSalvarDisparo");
        btnSalvarDisparo.setLeft(130);
        btnSalvarDisparo.setTop(0);
        btnSalvarDisparo.setWidth(65);
        btnSalvarDisparo.setHeight(53);
        btnSalvarDisparo.setHint("Salvar");
        btnSalvarDisparo.setCaption("Salvar");
        btnSalvarDisparo.setFontColor("clWindowText");
        btnSalvarDisparo.setFontSize(-11);
        btnSalvarDisparo.setFontName("Tahoma");
        btnSalvarDisparo.setFontStyle("[]");
        btnSalvarDisparo.setLayout("blGlyphTop");
        btnSalvarDisparo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarDisparoClick(event);
            processarFlow("FrmChatBotAtivo", "btnSalvarDisparo", "OnClick");
        });
        btnSalvarDisparo.setImageId(4);
        btnSalvarDisparo.setColor("clBtnFace");
        btnSalvarDisparo.setAccess(false);
        btnSalvarDisparo.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvarDisparo);
        btnSalvarDisparo.applyProperties();
    }

    public TFButton btnCancelarDisparo = new TFButton();

    private void init_btnCancelarDisparo() {
        btnCancelarDisparo.setName("btnCancelarDisparo");
        btnCancelarDisparo.setLeft(195);
        btnCancelarDisparo.setTop(0);
        btnCancelarDisparo.setWidth(65);
        btnCancelarDisparo.setHeight(53);
        btnCancelarDisparo.setHint("Cancela as Altera\u00E7\u00F5es Correntes");
        btnCancelarDisparo.setCaption("Cancelar");
        btnCancelarDisparo.setFontColor("clWindowText");
        btnCancelarDisparo.setFontSize(-11);
        btnCancelarDisparo.setFontName("Tahoma");
        btnCancelarDisparo.setFontStyle("[]");
        btnCancelarDisparo.setLayout("blGlyphTop");
        btnCancelarDisparo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarDisparoClick(event);
            processarFlow("FrmChatBotAtivo", "btnCancelarDisparo", "OnClick");
        });
        btnCancelarDisparo.setImageId(9);
        btnCancelarDisparo.setColor("clBtnFace");
        btnCancelarDisparo.setAccess(false);
        btnCancelarDisparo.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelarDisparo);
        btnCancelarDisparo.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(260);
        FHBox3.setTop(0);
        FHBox3.setWidth(26);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setVisible(false);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFPageControl PageControlDisparo = new TFPageControl();

    private void init_PageControlDisparo() {
        PageControlDisparo.setName("PageControlDisparo");
        PageControlDisparo.setLeft(0);
        PageControlDisparo.setTop(61);
        PageControlDisparo.setWidth(882);
        PageControlDisparo.setHeight(531);
        PageControlDisparo.setTabPosition("tpTop");
        PageControlDisparo.setFlexVflex("ftTrue");
        PageControlDisparo.setFlexHflex("ftTrue");
        PageControlDisparo.setRenderStyle("rsTabbed");
        PageControlDisparo.applyProperties();
        FVBox1.addChildren(PageControlDisparo);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setVisible(true);
        tabListagem.setClosable(false);
        PageControlDisparo.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(874);
        FVBox2.setHeight(503);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(5);
        FVBox2.setPaddingLeft(5);
        FVBox2.setPaddingRight(5);
        FVBox2.setPaddingBottom(5);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabListagem.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(713);
        FHBox2.setHeight(56);
        FHBox2.setAlign("alClient");
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(10);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftMin");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FVBox2.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(505);
        FVBox5.setHeight(50);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel lfDescricao = new TFLabel();

    private void init_lfDescricao() {
        lfDescricao.setName("lfDescricao");
        lfDescricao.setLeft(0);
        lfDescricao.setTop(0);
        lfDescricao.setWidth(46);
        lfDescricao.setHeight(13);
        lfDescricao.setAlign("alLeft");
        lfDescricao.setCaption("Descri\u00E7\u00E3o");
        lfDescricao.setFontColor("clWindowText");
        lfDescricao.setFontSize(-11);
        lfDescricao.setFontName("Tahoma");
        lfDescricao.setFontStyle("[]");
        lfDescricao.setVerticalAlignment("taAlignBottom");
        lfDescricao.setWordBreak(false);
        FVBox5.addChildren(lfDescricao);
        lfDescricao.applyProperties();
    }

    public TFString fTbxDisparoDescricao = new TFString();

    private void init_fTbxDisparoDescricao() {
        fTbxDisparoDescricao.setName("fTbxDisparoDescricao");
        fTbxDisparoDescricao.setLeft(0);
        fTbxDisparoDescricao.setTop(14);
        fTbxDisparoDescricao.setWidth(500);
        fTbxDisparoDescricao.setHeight(24);
        fTbxDisparoDescricao.setHint("Filtra pelo Descri\u00E7\u00E3o do modelo de markup");
        fTbxDisparoDescricao.setFlex(false);
        fTbxDisparoDescricao.setRequired(false);
        fTbxDisparoDescricao.setConstraintCheckWhen("cwImmediate");
        fTbxDisparoDescricao.setConstraintCheckType("ctExpression");
        fTbxDisparoDescricao.setConstraintFocusOnError(false);
        fTbxDisparoDescricao.setConstraintEnableUI(true);
        fTbxDisparoDescricao.setConstraintEnabled(false);
        fTbxDisparoDescricao.setConstraintFormCheck(true);
        fTbxDisparoDescricao.setCharCase("ccNormal");
        fTbxDisparoDescricao.setPwd(false);
        fTbxDisparoDescricao.setMaxlength(0);
        fTbxDisparoDescricao.setAlign("alLeft");
        fTbxDisparoDescricao.setFontColor("clWindowText");
        fTbxDisparoDescricao.setFontSize(-13);
        fTbxDisparoDescricao.setFontName("Tahoma");
        fTbxDisparoDescricao.setFontStyle("[]");
        fTbxDisparoDescricao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            filtrarGrid(event);
            processarFlow("FrmChatBotAtivo", "fTbxDisparoDescricao", "OnEnter");
        });
        fTbxDisparoDescricao.setSaveLiteralCharacter(false);
        fTbxDisparoDescricao.applyProperties();
        FVBox5.addChildren(fTbxDisparoDescricao);
        addValidatable(fTbxDisparoDescricao);
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(505);
        FVBox4.setTop(0);
        FVBox4.setWidth(86);
        FVBox4.setHeight(50);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(25);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFCheckBox fchkDisparoAtivo = new TFCheckBox();

    private void init_fchkDisparoAtivo() {
        fchkDisparoAtivo.setName("fchkDisparoAtivo");
        fchkDisparoAtivo.setLeft(0);
        fchkDisparoAtivo.setTop(0);
        fchkDisparoAtivo.setWidth(80);
        fchkDisparoAtivo.setHeight(45);
        fchkDisparoAtivo.setAlign("alLeft");
        fchkDisparoAtivo.setCaption("Ativo");
        fchkDisparoAtivo.setChecked(true);
        fchkDisparoAtivo.setColor("clBtnFace");
        fchkDisparoAtivo.setFontColor("clWindowText");
        fchkDisparoAtivo.setFontSize(-11);
        fchkDisparoAtivo.setFontName("Tahoma");
        fchkDisparoAtivo.setFontStyle("[]");
        fchkDisparoAtivo.setVerticalAlignment("taAlignTop");
        FVBox4.addChildren(fchkDisparoAtivo);
        fchkDisparoAtivo.applyProperties();
    }

    public TFGrid gridDisparo = new TFGrid();

    private void init_gridDisparo() {
        gridDisparo.setName("gridDisparo");
        gridDisparo.setLeft(0);
        gridDisparo.setTop(57);
        gridDisparo.setWidth(716);
        gridDisparo.setHeight(179);
        gridDisparo.setTable(tbDisparo2);
        gridDisparo.setFlexVflex("ftTrue");
        gridDisparo.setFlexHflex("ftTrue");
        gridDisparo.setPagingEnabled(true);
        gridDisparo.setFrozenColumns(0);
        gridDisparo.setShowFooter(false);
        gridDisparo.setShowHeader(true);
        gridDisparo.setMultiSelection(false);
        gridDisparo.setGroupingEnabled(false);
        gridDisparo.setGroupingExpanded(false);
        gridDisparo.setGroupingShowFooter(false);
        gridDisparo.setCrosstabEnabled(false);
        gridDisparo.setCrosstabGroupType("cgtConcat");
        gridDisparo.setEditionEnabled(false);
        gridDisparo.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(246);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setHint("Descri\u00E7\u00E3o da ocorrencia: Abriu evento por exemplo");
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridDisparo.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("MODELO");
        item1.setTitleCaption("Template");
        item1.setWidth(402);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setHint("Modelo e-mail");
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridDisparo.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("ID_DISPARO");
        item2.setTitleCaption("Id.");
        item2.setWidth(40);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftInteger");
        item2.setFlexRatio(0);
        item2.setSort(true);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setHint("Id. whasapp modelo");
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridDisparo.getColumns().add(item2);
        FVBox2.addChildren(gridDisparo);
        gridDisparo.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        PageControlDisparo.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox FVBox11 = new TFVBox();

    private void init_FVBox11() {
        FVBox11.setName("FVBox11");
        FVBox11.setLeft(0);
        FVBox11.setTop(0);
        FVBox11.setWidth(874);
        FVBox11.setHeight(503);
        FVBox11.setAlign("alClient");
        FVBox11.setBorderStyle("stNone");
        FVBox11.setPaddingTop(5);
        FVBox11.setPaddingLeft(5);
        FVBox11.setPaddingRight(5);
        FVBox11.setPaddingBottom(5);
        FVBox11.setMarginTop(0);
        FVBox11.setMarginLeft(0);
        FVBox11.setMarginRight(0);
        FVBox11.setMarginBottom(0);
        FVBox11.setSpacing(1);
        FVBox11.setFlexVflex("ftTrue");
        FVBox11.setFlexHflex("ftTrue");
        FVBox11.setScrollable(false);
        FVBox11.setBoxShadowConfigHorizontalLength(10);
        FVBox11.setBoxShadowConfigVerticalLength(10);
        FVBox11.setBoxShadowConfigBlurRadius(5);
        FVBox11.setBoxShadowConfigSpreadRadius(0);
        FVBox11.setBoxShadowConfigShadowColor("clBlack");
        FVBox11.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(FVBox11);
        FVBox11.applyProperties();
    }

    public TFGroupbox FGroupbox2 = new TFGroupbox();

    private void init_FGroupbox2() {
        FGroupbox2.setName("FGroupbox2");
        FGroupbox2.setLeft(0);
        FGroupbox2.setTop(0);
        FGroupbox2.setWidth(772);
        FGroupbox2.setHeight(221);
        FGroupbox2.setCaption("Disparo");
        FGroupbox2.setFontColor("clWindowText");
        FGroupbox2.setFontSize(-11);
        FGroupbox2.setFontName("Tahoma");
        FGroupbox2.setFontStyle("[]");
        FGroupbox2.setFlexVflex("ftTrue");
        FGroupbox2.setFlexHflex("ftTrue");
        FGroupbox2.setScrollable(false);
        FGroupbox2.setClosable(false);
        FGroupbox2.setClosed(false);
        FGroupbox2.setOrient("coHorizontal");
        FGroupbox2.setStyle("grp3D");
        FGroupbox2.setHeaderImageId(0);
        FVBox11.addChildren(FGroupbox2);
        FGroupbox2.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(2);
        FVBox9.setTop(15);
        FVBox9.setWidth(768);
        FVBox9.setHeight(204);
        FVBox9.setAlign("alClient");
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftFalse");
        FVBox9.setFlexHflex("ftTrue");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FGroupbox2.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(0);
        FHBox16.setWidth(665);
        FHBox16.setHeight(41);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(5);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftTrue");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        FVBox9.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(0);
        FHBox17.setWidth(118);
        FHBox17.setHeight(37);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(5);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftFalse");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FHBox16.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(9);
        FHBox4.setHeight(31);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftTrue");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox17.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(9);
        FLabel4.setTop(0);
        FLabel4.setWidth(46);
        FLabel4.setHeight(13);
        FLabel4.setCaption("Descri\u00E7\u00E3o");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taAlignTop");
        FLabel4.setWordBreak(false);
        FHBox17.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFString etxbDisparoDescricao = new TFString();

    private void init_etxbDisparoDescricao() {
        etxbDisparoDescricao.setName("etxbDisparoDescricao");
        etxbDisparoDescricao.setLeft(118);
        etxbDisparoDescricao.setTop(0);
        etxbDisparoDescricao.setWidth(461);
        etxbDisparoDescricao.setHeight(24);
        etxbDisparoDescricao.setHint("Descri\u00E7\u00E3o do modelo de markup");
        etxbDisparoDescricao.setTable(tbDisparo2);
        etxbDisparoDescricao.setFieldName("DESCRICAO");
        etxbDisparoDescricao.setFlex(true);
        etxbDisparoDescricao.setRequired(true);
        etxbDisparoDescricao.setConstraintCheckWhen("cwImmediate");
        etxbDisparoDescricao.setConstraintCheckType("ctExpression");
        etxbDisparoDescricao.setConstraintFocusOnError(false);
        etxbDisparoDescricao.setConstraintEnableUI(true);
        etxbDisparoDescricao.setConstraintEnabled(false);
        etxbDisparoDescricao.setConstraintFormCheck(true);
        etxbDisparoDescricao.setCharCase("ccNormal");
        etxbDisparoDescricao.setPwd(false);
        etxbDisparoDescricao.setMaxlength(0);
        etxbDisparoDescricao.setFontColor("clWindowText");
        etxbDisparoDescricao.setFontSize(-13);
        etxbDisparoDescricao.setFontName("Tahoma");
        etxbDisparoDescricao.setFontStyle("[]");
        etxbDisparoDescricao.setSaveLiteralCharacter(false);
        etxbDisparoDescricao.applyProperties();
        FHBox16.addChildren(etxbDisparoDescricao);
        addValidatable(etxbDisparoDescricao);
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(42);
        FHBox19.setWidth(665);
        FHBox19.setHeight(41);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(5);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftTrue");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        FVBox9.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(0);
        FHBox20.setTop(0);
        FHBox20.setWidth(118);
        FHBox20.setHeight(37);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(5);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftFalse");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FHBox19.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(0);
        FHBox21.setWidth(9);
        FHBox21.setHeight(31);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftTrue");
        FHBox21.setFlexHflex("ftTrue");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        FHBox20.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(9);
        FLabel6.setTop(0);
        FLabel6.setWidth(44);
        FLabel6.setHeight(13);
        FLabel6.setCaption("Template");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox20.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFCombo ecmbDisparoTemplate = new TFCombo();

    private void init_ecmbDisparoTemplate() {
        ecmbDisparoTemplate.setName("ecmbDisparoTemplate");
        ecmbDisparoTemplate.setLeft(118);
        ecmbDisparoTemplate.setTop(0);
        ecmbDisparoTemplate.setWidth(399);
        ecmbDisparoTemplate.setHeight(21);
        ecmbDisparoTemplate.setHint("Template do Whatapp");
        ecmbDisparoTemplate.setTable(tbDisparo2);
        ecmbDisparoTemplate.setLookupTable(tbEmailModelo);
        ecmbDisparoTemplate.setFieldName("TEMPLATE");
        ecmbDisparoTemplate.setLookupKey("ID_EMAIL_MODELO");
        ecmbDisparoTemplate.setLookupDesc("MODELO");
        ecmbDisparoTemplate.setFlex(true);
        ecmbDisparoTemplate.setHelpCaption("Template");
        ecmbDisparoTemplate.setReadOnly(true);
        ecmbDisparoTemplate.setRequired(true);
        ecmbDisparoTemplate.setPrompt("Selecione");
        ecmbDisparoTemplate.setConstraintCheckWhen("cwImmediate");
        ecmbDisparoTemplate.setConstraintCheckType("ctExpression");
        ecmbDisparoTemplate.setConstraintFocusOnError(false);
        ecmbDisparoTemplate.setConstraintEnableUI(true);
        ecmbDisparoTemplate.setConstraintEnabled(false);
        ecmbDisparoTemplate.setConstraintFormCheck(true);
        ecmbDisparoTemplate.setClearOnDelKey(true);
        ecmbDisparoTemplate.setUseClearButton(false);
        ecmbDisparoTemplate.setHideClearButtonOnNullValue(false);
        FHBox19.addChildren(ecmbDisparoTemplate);
        ecmbDisparoTemplate.applyProperties();
        addValidatable(ecmbDisparoTemplate);
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(84);
        FHBox7.setWidth(665);
        FHBox7.setHeight(41);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(5);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox9.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(118);
        FHBox10.setHeight(37);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(5);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FHBox7.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(0);
        FHBox12.setWidth(9);
        FHBox12.setHeight(31);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftTrue");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        FHBox10.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFLabel FLabel9 = new TFLabel();

    private void init_FLabel9() {
        FLabel9.setName("FLabel9");
        FLabel9.setLeft(9);
        FLabel9.setTop(0);
        FLabel9.setWidth(46);
        FLabel9.setHeight(13);
        FLabel9.setCaption("Execu\u00E7\u00E3o");
        FLabel9.setFontColor("clWindowText");
        FLabel9.setFontSize(-11);
        FLabel9.setFontName("Tahoma");
        FLabel9.setFontStyle("[]");
        FLabel9.setVerticalAlignment("taVerticalCenter");
        FLabel9.setWordBreak(false);
        FHBox10.addChildren(FLabel9);
        FLabel9.applyProperties();
    }

    public TFCombo cbbAplServico = new TFCombo();

    private void init_cbbAplServico() {
        cbbAplServico.setName("cbbAplServico");
        cbbAplServico.setLeft(118);
        cbbAplServico.setTop(0);
        cbbAplServico.setWidth(399);
        cbbAplServico.setHeight(21);
        cbbAplServico.setHint("Template do Whatapp");
        cbbAplServico.setTable(tbDisparo2);
        cbbAplServico.setFieldName("SERVICO_EXECUCAO");
        cbbAplServico.setFlex(true);
        cbbAplServico.setListOptions("Leadzap=L; Service=S");
        cbbAplServico.setHelpCaption("Template");
        cbbAplServico.setReadOnly(true);
        cbbAplServico.setRequired(true);
        cbbAplServico.setPrompt("Selecione");
        cbbAplServico.setConstraintCheckWhen("cwImmediate");
        cbbAplServico.setConstraintCheckType("ctExpression");
        cbbAplServico.setConstraintFocusOnError(false);
        cbbAplServico.setConstraintEnableUI(true);
        cbbAplServico.setConstraintEnabled(false);
        cbbAplServico.setConstraintFormCheck(true);
        cbbAplServico.setClearOnDelKey(true);
        cbbAplServico.setUseClearButton(false);
        cbbAplServico.setHideClearButtonOnNullValue(false);
        FHBox7.addChildren(cbbAplServico);
        cbbAplServico.applyProperties();
        addValidatable(cbbAplServico);
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(126);
        FHBox5.setWidth(665);
        FHBox5.setHeight(41);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(5);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox9.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(118);
        FHBox6.setHeight(37);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(5);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox5.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFCheckBox echkDisparoAtivo = new TFCheckBox();

    private void init_echkDisparoAtivo() {
        echkDisparoAtivo.setName("echkDisparoAtivo");
        echkDisparoAtivo.setLeft(118);
        echkDisparoAtivo.setTop(0);
        echkDisparoAtivo.setWidth(82);
        echkDisparoAtivo.setHeight(35);
        echkDisparoAtivo.setAlign("alLeft");
        echkDisparoAtivo.setCaption("Ativo");
        echkDisparoAtivo.setColor("clBtnFace");
        echkDisparoAtivo.setFontColor("clWindowText");
        echkDisparoAtivo.setFontSize(-11);
        echkDisparoAtivo.setFontName("Tahoma");
        echkDisparoAtivo.setFontStyle("[]");
        echkDisparoAtivo.setTable(tbDisparo2);
        echkDisparoAtivo.setFieldName("ATIVO");
        echkDisparoAtivo.setCheckedValue("S");
        echkDisparoAtivo.setVerticalAlignment("taAlignTop");
        FHBox5.addChildren(echkDisparoAtivo);
        echkDisparoAtivo.applyProperties();
    }

    public TFTabsheet tabChatBot = new TFTabsheet();

    private void init_tabChatBot() {
        tabChatBot.setName("tabChatBot");
        tabChatBot.setCaption("ChatBot");
        tabChatBot.setVisible(true);
        tabChatBot.setClosable(false);
        PageControlDisparo.addChildren(tabChatBot);
        tabChatBot.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(874);
        FVBox3.setHeight(503);
        FVBox3.setAlign("alClient");
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        tabChatBot.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFVBox FVBox15 = new TFVBox();

    private void init_FVBox15() {
        FVBox15.setName("FVBox15");
        FVBox15.setLeft(0);
        FVBox15.setTop(0);
        FVBox15.setWidth(877);
        FVBox15.setHeight(144);
        FVBox15.setAlign("alClient");
        FVBox15.setBorderStyle("stNone");
        FVBox15.setPaddingTop(0);
        FVBox15.setPaddingLeft(0);
        FVBox15.setPaddingRight(0);
        FVBox15.setPaddingBottom(0);
        FVBox15.setMarginTop(0);
        FVBox15.setMarginLeft(0);
        FVBox15.setMarginRight(0);
        FVBox15.setMarginBottom(0);
        FVBox15.setSpacing(1);
        FVBox15.setFlexVflex("ftTrue");
        FVBox15.setFlexHflex("ftTrue");
        FVBox15.setScrollable(false);
        FVBox15.setBoxShadowConfigHorizontalLength(10);
        FVBox15.setBoxShadowConfigVerticalLength(10);
        FVBox15.setBoxShadowConfigBlurRadius(5);
        FVBox15.setBoxShadowConfigSpreadRadius(0);
        FVBox15.setBoxShadowConfigShadowColor("clBlack");
        FVBox15.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FVBox15);
        FVBox15.applyProperties();
    }

    public TFGrid gridChatBot = new TFGrid();

    private void init_gridChatBot() {
        gridChatBot.setName("gridChatBot");
        gridChatBot.setLeft(0);
        gridChatBot.setTop(0);
        gridChatBot.setWidth(635);
        gridChatBot.setHeight(134);
        gridChatBot.setTable(tbDisparoChatbot);
        gridChatBot.setFlexVflex("ftTrue");
        gridChatBot.setFlexHflex("ftTrue");
        gridChatBot.setPagingEnabled(true);
        gridChatBot.setFrozenColumns(0);
        gridChatBot.setShowFooter(false);
        gridChatBot.setShowHeader(true);
        gridChatBot.setMultiSelection(false);
        gridChatBot.setGroupingEnabled(false);
        gridChatBot.setGroupingExpanded(false);
        gridChatBot.setGroupingShowFooter(false);
        gridChatBot.setCrosstabEnabled(false);
        gridChatBot.setCrosstabGroupType("cgtConcat");
        gridChatBot.setEditionEnabled(false);
        gridChatBot.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_ITEM");
        item0.setTitleCaption("Seq.");
        item0.setWidth(62);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridChatBot.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO_FIXA");
        item1.setTitleCaption("Fixa");
        item1.setWidth(234);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridChatBot.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("DESCRICAO_OPCIONAL");
        item2.setTitleCaption("Descri\u00E7\u00E3o alternativa");
        item2.setWidth(216);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridChatBot.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("ATIVO");
        item3.setTitleCaption("Ativo");
        item3.setWidth(57);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taCenter");
        item3.setFieldType("ftCheckBox");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridChatBot.getColumns().add(item3);
        FVBox15.addChildren(gridChatBot);
        gridChatBot.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(145);
        FVBox6.setWidth(870);
        FVBox6.setHeight(304);
        FVBox6.setAlign("alClient");
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(5);
        FVBox6.setPaddingLeft(5);
        FVBox6.setPaddingRight(5);
        FVBox6.setPaddingBottom(5);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(true);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(438);
        FHBox11.setHeight(41);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox6.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnAlterarChatBot = new TFButton();

    private void init_btnAlterarChatBot() {
        btnAlterarChatBot.setName("btnAlterarChatBot");
        btnAlterarChatBot.setLeft(0);
        btnAlterarChatBot.setTop(0);
        btnAlterarChatBot.setWidth(48);
        btnAlterarChatBot.setHeight(35);
        btnAlterarChatBot.setHint("Alterar Item");
        btnAlterarChatBot.setFontColor("clWindowText");
        btnAlterarChatBot.setFontSize(-11);
        btnAlterarChatBot.setFontName("Tahoma");
        btnAlterarChatBot.setFontStyle("[]");
        btnAlterarChatBot.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarChatBotClick(event);
            processarFlow("FrmChatBotAtivo", "btnAlterarChatBot", "OnClick");
        });
        btnAlterarChatBot.setImageId(7);
        btnAlterarChatBot.setColor("clBtnFace");
        btnAlterarChatBot.setAccess(false);
        btnAlterarChatBot.setIconReverseDirection(false);
        FHBox11.addChildren(btnAlterarChatBot);
        btnAlterarChatBot.applyProperties();
    }

    public TFButton btnSalvarChatBot = new TFButton();

    private void init_btnSalvarChatBot() {
        btnSalvarChatBot.setName("btnSalvarChatBot");
        btnSalvarChatBot.setLeft(48);
        btnSalvarChatBot.setTop(0);
        btnSalvarChatBot.setWidth(48);
        btnSalvarChatBot.setHeight(35);
        btnSalvarChatBot.setHint("Salvar Item");
        btnSalvarChatBot.setFontColor("clWindowText");
        btnSalvarChatBot.setFontSize(-11);
        btnSalvarChatBot.setFontName("Tahoma");
        btnSalvarChatBot.setFontStyle("[]");
        btnSalvarChatBot.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarChatBotClick(event);
            processarFlow("FrmChatBotAtivo", "btnSalvarChatBot", "OnClick");
        });
        btnSalvarChatBot.setImageId(4);
        btnSalvarChatBot.setColor("clBtnFace");
        btnSalvarChatBot.setAccess(false);
        btnSalvarChatBot.setIconReverseDirection(false);
        FHBox11.addChildren(btnSalvarChatBot);
        btnSalvarChatBot.applyProperties();
    }

    public TFButton btnCancelarChatBot = new TFButton();

    private void init_btnCancelarChatBot() {
        btnCancelarChatBot.setName("btnCancelarChatBot");
        btnCancelarChatBot.setLeft(96);
        btnCancelarChatBot.setTop(0);
        btnCancelarChatBot.setWidth(48);
        btnCancelarChatBot.setHeight(35);
        btnCancelarChatBot.setHint("Cancelar Item");
        btnCancelarChatBot.setFontColor("clWindowText");
        btnCancelarChatBot.setFontSize(-11);
        btnCancelarChatBot.setFontName("Tahoma");
        btnCancelarChatBot.setFontStyle("[]");
        btnCancelarChatBot.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarChatBotClick(event);
            processarFlow("FrmChatBotAtivo", "btnCancelarChatBot", "OnClick");
        });
        btnCancelarChatBot.setImageId(9);
        btnCancelarChatBot.setColor("clBtnFace");
        btnCancelarChatBot.setAccess(false);
        btnCancelarChatBot.setIconReverseDirection(false);
        FHBox11.addChildren(btnCancelarChatBot);
        btnCancelarChatBot.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(42);
        FHBox8.setWidth(861);
        FHBox8.setHeight(56);
        FHBox8.setAlign("alClient");
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FVBox6.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(0);
        FVBox7.setWidth(645);
        FVBox7.setHeight(50);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(102);
        FLabel1.setHeight(13);
        FLabel1.setAlign("alLeft");
        FLabel1.setCaption("Descri\u00E7\u00E3o Alternativa");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taAlignBottom");
        FLabel1.setWordBreak(false);
        FVBox7.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFString etxChatBotbDescricaoAlternativa = new TFString();

    private void init_etxChatBotbDescricaoAlternativa() {
        etxChatBotbDescricaoAlternativa.setName("etxChatBotbDescricaoAlternativa");
        etxChatBotbDescricaoAlternativa.setLeft(0);
        etxChatBotbDescricaoAlternativa.setTop(14);
        etxChatBotbDescricaoAlternativa.setWidth(501);
        etxChatBotbDescricaoAlternativa.setHeight(24);
        etxChatBotbDescricaoAlternativa.setHint("Filtra pelo Descri\u00E7\u00E3o do modelo de markup");
        etxChatBotbDescricaoAlternativa.setTable(tbDisparoChatbot);
        etxChatBotbDescricaoAlternativa.setFieldName("DESCRICAO_OPCIONAL");
        etxChatBotbDescricaoAlternativa.setFlex(true);
        etxChatBotbDescricaoAlternativa.setRequired(false);
        etxChatBotbDescricaoAlternativa.setConstraintCheckWhen("cwImmediate");
        etxChatBotbDescricaoAlternativa.setConstraintCheckType("ctExpression");
        etxChatBotbDescricaoAlternativa.setConstraintFocusOnError(false);
        etxChatBotbDescricaoAlternativa.setConstraintEnableUI(true);
        etxChatBotbDescricaoAlternativa.setConstraintEnabled(false);
        etxChatBotbDescricaoAlternativa.setConstraintFormCheck(true);
        etxChatBotbDescricaoAlternativa.setCharCase("ccNormal");
        etxChatBotbDescricaoAlternativa.setPwd(false);
        etxChatBotbDescricaoAlternativa.setMaxlength(0);
        etxChatBotbDescricaoAlternativa.setAlign("alLeft");
        etxChatBotbDescricaoAlternativa.setFontColor("clWindowText");
        etxChatBotbDescricaoAlternativa.setFontSize(-13);
        etxChatBotbDescricaoAlternativa.setFontName("Tahoma");
        etxChatBotbDescricaoAlternativa.setFontStyle("[]");
        etxChatBotbDescricaoAlternativa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            filtrarGrid(event);
            processarFlow("FrmChatBotAtivo", "etxChatBotbDescricaoAlternativa", "OnEnter");
        });
        etxChatBotbDescricaoAlternativa.setSaveLiteralCharacter(false);
        etxChatBotbDescricaoAlternativa.applyProperties();
        FVBox7.addChildren(etxChatBotbDescricaoAlternativa);
        addValidatable(etxChatBotbDescricaoAlternativa);
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(645);
        FVBox8.setTop(0);
        FVBox8.setWidth(78);
        FVBox8.setHeight(50);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(10);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftFalse");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(40);
        FLabel2.setHeight(13);
        FLabel2.setAlign("alLeft");
        FLabel2.setCaption("Mascara");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taAlignBottom");
        FLabel2.setWordBreak(false);
        FVBox8.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFString etxbChatBotMascara = new TFString();

    private void init_etxbChatBotMascara() {
        etxbChatBotMascara.setName("etxbChatBotMascara");
        etxbChatBotMascara.setLeft(0);
        etxbChatBotMascara.setTop(14);
        etxbChatBotMascara.setWidth(74);
        etxbChatBotMascara.setHeight(24);
        etxbChatBotMascara.setHint("Filtra pelo Descri\u00E7\u00E3o do modelo de markup");
        etxbChatBotMascara.setTable(tbDisparoChatbot);
        etxbChatBotMascara.setFieldName("MASCARA");
        etxbChatBotMascara.setFlex(false);
        etxbChatBotMascara.setRequired(false);
        etxbChatBotMascara.setConstraintCheckWhen("cwImmediate");
        etxbChatBotMascara.setConstraintCheckType("ctExpression");
        etxbChatBotMascara.setConstraintFocusOnError(false);
        etxbChatBotMascara.setConstraintEnableUI(true);
        etxbChatBotMascara.setConstraintEnabled(false);
        etxbChatBotMascara.setConstraintFormCheck(true);
        etxbChatBotMascara.setCharCase("ccNormal");
        etxbChatBotMascara.setPwd(false);
        etxbChatBotMascara.setMaxlength(0);
        etxbChatBotMascara.setAlign("alLeft");
        etxbChatBotMascara.setFontColor("clWindowText");
        etxbChatBotMascara.setFontSize(-13);
        etxbChatBotMascara.setFontName("Tahoma");
        etxbChatBotMascara.setFontStyle("[]");
        etxbChatBotMascara.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            filtrarGrid(event);
            processarFlow("FrmChatBotAtivo", "etxbChatBotMascara", "OnEnter");
        });
        etxbChatBotMascara.setSaveLiteralCharacter(false);
        etxbChatBotMascara.applyProperties();
        FVBox8.addChildren(etxbChatBotMascara);
        addValidatable(etxbChatBotMascara);
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(723);
        FVBox10.setTop(0);
        FVBox10.setWidth(86);
        FVBox10.setHeight(50);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(25);
        FVBox10.setPaddingLeft(0);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(1);
        FVBox10.setFlexVflex("ftFalse");
        FVBox10.setFlexHflex("ftFalse");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFCheckBox echkChatBotAtivo = new TFCheckBox();

    private void init_echkChatBotAtivo() {
        echkChatBotAtivo.setName("echkChatBotAtivo");
        echkChatBotAtivo.setLeft(0);
        echkChatBotAtivo.setTop(0);
        echkChatBotAtivo.setWidth(82);
        echkChatBotAtivo.setHeight(46);
        echkChatBotAtivo.setAlign("alLeft");
        echkChatBotAtivo.setCaption("Ativo");
        echkChatBotAtivo.setColor("clBtnFace");
        echkChatBotAtivo.setFontColor("clWindowText");
        echkChatBotAtivo.setFontSize(-11);
        echkChatBotAtivo.setFontName("Tahoma");
        echkChatBotAtivo.setFontStyle("[]");
        echkChatBotAtivo.setTable(tbDisparoChatbot);
        echkChatBotAtivo.setFieldName("ATIVO");
        echkChatBotAtivo.setCheckedValue("S");
        echkChatBotAtivo.setVerticalAlignment("taAlignTop");
        FVBox10.addChildren(echkChatBotAtivo);
        echkChatBotAtivo.applyProperties();
    }

    public TFVBox FVBox12 = new TFVBox();

    private void init_FVBox12() {
        FVBox12.setName("FVBox12");
        FVBox12.setLeft(0);
        FVBox12.setTop(99);
        FVBox12.setWidth(860);
        FVBox12.setHeight(58);
        FVBox12.setBorderStyle("stNone");
        FVBox12.setPaddingTop(0);
        FVBox12.setPaddingLeft(0);
        FVBox12.setPaddingRight(0);
        FVBox12.setPaddingBottom(0);
        FVBox12.setMarginTop(0);
        FVBox12.setMarginLeft(0);
        FVBox12.setMarginRight(0);
        FVBox12.setMarginBottom(0);
        FVBox12.setSpacing(1);
        FVBox12.setFlexVflex("ftFalse");
        FVBox12.setFlexHflex("ftTrue");
        FVBox12.setScrollable(false);
        FVBox12.setBoxShadowConfigHorizontalLength(10);
        FVBox12.setBoxShadowConfigVerticalLength(10);
        FVBox12.setBoxShadowConfigBlurRadius(5);
        FVBox12.setBoxShadowConfigSpreadRadius(0);
        FVBox12.setBoxShadowConfigShadowColor("clBlack");
        FVBox12.setBoxShadowConfigOpacity(75);
        FVBox6.addChildren(FVBox12);
        FVBox12.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(193);
        FLabel3.setHeight(13);
        FLabel3.setAlign("alLeft");
        FLabel3.setCaption("Link a ser adicionado para agendamento");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taAlignBottom");
        FLabel3.setWordBreak(false);
        FVBox12.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFString etxbChatBotLink = new TFString();

    private void init_etxbChatBotLink() {
        etxbChatBotLink.setName("etxbChatBotLink");
        etxbChatBotLink.setLeft(0);
        etxbChatBotLink.setTop(14);
        etxbChatBotLink.setWidth(501);
        etxbChatBotLink.setHeight(24);
        etxbChatBotLink.setHint("Filtra pelo Descri\u00E7\u00E3o do modelo de markup");
        etxbChatBotLink.setTable(tbDisparoChatbot);
        etxbChatBotLink.setFieldName("LINK");
        etxbChatBotLink.setFlex(true);
        etxbChatBotLink.setRequired(false);
        etxbChatBotLink.setConstraintCheckWhen("cwImmediate");
        etxbChatBotLink.setConstraintCheckType("ctExpression");
        etxbChatBotLink.setConstraintFocusOnError(false);
        etxbChatBotLink.setConstraintEnableUI(true);
        etxbChatBotLink.setConstraintEnabled(false);
        etxbChatBotLink.setConstraintFormCheck(true);
        etxbChatBotLink.setCharCase("ccNormal");
        etxbChatBotLink.setPwd(false);
        etxbChatBotLink.setMaxlength(0);
        etxbChatBotLink.setAlign("alLeft");
        etxbChatBotLink.setFontColor("clWindowText");
        etxbChatBotLink.setFontSize(-13);
        etxbChatBotLink.setFontName("Tahoma");
        etxbChatBotLink.setFontStyle("[]");
        etxbChatBotLink.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            filtrarGrid(event);
            processarFlow("FrmChatBotAtivo", "etxbChatBotLink", "OnEnter");
        });
        etxbChatBotLink.setSaveLiteralCharacter(false);
        etxbChatBotLink.applyProperties();
        FVBox12.addChildren(etxbChatBotLink);
        addValidatable(etxbChatBotLink);
    }

    public TFVBox FVBox13 = new TFVBox();

    private void init_FVBox13() {
        FVBox13.setName("FVBox13");
        FVBox13.setLeft(0);
        FVBox13.setTop(158);
        FVBox13.setWidth(860);
        FVBox13.setHeight(58);
        FVBox13.setBorderStyle("stNone");
        FVBox13.setPaddingTop(0);
        FVBox13.setPaddingLeft(0);
        FVBox13.setPaddingRight(0);
        FVBox13.setPaddingBottom(0);
        FVBox13.setMarginTop(0);
        FVBox13.setMarginLeft(0);
        FVBox13.setMarginRight(0);
        FVBox13.setMarginBottom(0);
        FVBox13.setSpacing(1);
        FVBox13.setFlexVflex("ftFalse");
        FVBox13.setFlexHflex("ftTrue");
        FVBox13.setScrollable(false);
        FVBox13.setBoxShadowConfigHorizontalLength(10);
        FVBox13.setBoxShadowConfigVerticalLength(10);
        FVBox13.setBoxShadowConfigBlurRadius(5);
        FVBox13.setBoxShadowConfigSpreadRadius(0);
        FVBox13.setBoxShadowConfigShadowColor("clBlack");
        FVBox13.setBoxShadowConfigOpacity(75);
        FVBox6.addChildren(FVBox13);
        FVBox13.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(78);
        FLabel5.setHeight(13);
        FLabel5.setAlign("alLeft");
        FLabel5.setCaption("Motivo de perda");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taAlignBottom");
        FLabel5.setWordBreak(false);
        FVBox13.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFCombo ecmbMotivoPerda = new TFCombo();

    private void init_ecmbMotivoPerda() {
        ecmbMotivoPerda.setName("ecmbMotivoPerda");
        ecmbMotivoPerda.setLeft(0);
        ecmbMotivoPerda.setTop(14);
        ecmbMotivoPerda.setWidth(503);
        ecmbMotivoPerda.setHeight(21);
        ecmbMotivoPerda.setHint("Tipo custo.  Op\u00E7\u00F5es: Contabil=C; Forncedor=F");
        ecmbMotivoPerda.setTable(tbDisparoChatbot);
        ecmbMotivoPerda.setLookupTable(tbDescartes);
        ecmbMotivoPerda.setFieldName("COD_DESCARTE");
        ecmbMotivoPerda.setLookupKey("COD_DESCARTE");
        ecmbMotivoPerda.setLookupDesc("DESCRICAO_DESCARTE");
        ecmbMotivoPerda.setFlex(true);
        ecmbMotivoPerda.setReadOnly(true);
        ecmbMotivoPerda.setRequired(false);
        ecmbMotivoPerda.setPrompt("Selecione");
        ecmbMotivoPerda.setConstraintCheckWhen("cwImmediate");
        ecmbMotivoPerda.setConstraintCheckType("ctExpression");
        ecmbMotivoPerda.setConstraintFocusOnError(false);
        ecmbMotivoPerda.setConstraintEnableUI(true);
        ecmbMotivoPerda.setConstraintEnabled(false);
        ecmbMotivoPerda.setConstraintFormCheck(true);
        ecmbMotivoPerda.setClearOnDelKey(false);
        ecmbMotivoPerda.setUseClearButton(false);
        ecmbMotivoPerda.setHideClearButtonOnNullValue(false);
        FVBox13.addChildren(ecmbMotivoPerda);
        ecmbMotivoPerda.applyProperties();
        addValidatable(ecmbMotivoPerda);
    }

    public TFVBox FVBox14 = new TFVBox();

    private void init_FVBox14() {
        FVBox14.setName("FVBox14");
        FVBox14.setLeft(0);
        FVBox14.setTop(217);
        FVBox14.setWidth(860);
        FVBox14.setHeight(103);
        FVBox14.setBorderStyle("stNone");
        FVBox14.setPaddingTop(0);
        FVBox14.setPaddingLeft(0);
        FVBox14.setPaddingRight(0);
        FVBox14.setPaddingBottom(0);
        FVBox14.setMarginTop(0);
        FVBox14.setMarginLeft(0);
        FVBox14.setMarginRight(0);
        FVBox14.setMarginBottom(0);
        FVBox14.setSpacing(1);
        FVBox14.setFlexVflex("ftFalse");
        FVBox14.setFlexHflex("ftTrue");
        FVBox14.setScrollable(false);
        FVBox14.setBoxShadowConfigHorizontalLength(10);
        FVBox14.setBoxShadowConfigVerticalLength(10);
        FVBox14.setBoxShadowConfigBlurRadius(5);
        FVBox14.setBoxShadowConfigSpreadRadius(0);
        FVBox14.setBoxShadowConfigShadowColor("clBlack");
        FVBox14.setBoxShadowConfigOpacity(75);
        FVBox6.addChildren(FVBox14);
        FVBox14.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(0);
        FLabel7.setTop(0);
        FLabel7.setWidth(249);
        FLabel7.setHeight(13);
        FLabel7.setAlign("alLeft");
        FLabel7.setCaption("Mensagem de retorno quando cliente escolhe op\u00E7\u00E3o");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[]");
        FLabel7.setVerticalAlignment("taAlignBottom");
        FLabel7.setWordBreak(false);
        FVBox14.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFMemo ememoChatBotMensagemRetorno = new TFMemo();

    private void init_ememoChatBotMensagemRetorno() {
        ememoChatBotMensagemRetorno.setName("ememoChatBotMensagemRetorno");
        ememoChatBotMensagemRetorno.setLeft(0);
        ememoChatBotMensagemRetorno.setTop(14);
        ememoChatBotMensagemRetorno.setWidth(854);
        ememoChatBotMensagemRetorno.setHeight(62);
        ememoChatBotMensagemRetorno.setCharCase("ccNormal");
        ememoChatBotMensagemRetorno.setFontColor("clWindowText");
        ememoChatBotMensagemRetorno.setFontSize(-11);
        ememoChatBotMensagemRetorno.setFontName("Tahoma");
        ememoChatBotMensagemRetorno.setFontStyle("[]");
        ememoChatBotMensagemRetorno.setMaxlength(0);
        ememoChatBotMensagemRetorno.setFieldName("TEXTO_RESPOSTA");
        ememoChatBotMensagemRetorno.setTable(tbDisparoChatbot);
        ememoChatBotMensagemRetorno.setFlexVflex("ftFalse");
        ememoChatBotMensagemRetorno.setFlexHflex("ftTrue");
        ememoChatBotMensagemRetorno.setConstraintCheckWhen("cwImmediate");
        ememoChatBotMensagemRetorno.setConstraintCheckType("ctExpression");
        ememoChatBotMensagemRetorno.setConstraintFocusOnError(false);
        ememoChatBotMensagemRetorno.setConstraintEnableUI(true);
        ememoChatBotMensagemRetorno.setConstraintEnabled(false);
        ememoChatBotMensagemRetorno.setConstraintFormCheck(true);
        ememoChatBotMensagemRetorno.setRequired(false);
        FVBox14.addChildren(ememoChatBotMensagemRetorno);
        ememoChatBotMensagemRetorno.applyProperties();
        addValidatable(ememoChatBotMensagemRetorno);
    }

    public TFTabsheet tabTemplates = new TFTabsheet();

    private void init_tabTemplates() {
        tabTemplates.setName("tabTemplates");
        tabTemplates.setCaption("Templates");
        tabTemplates.setVisible(true);
        tabTemplates.setClosable(false);
        PageControlDisparo.addChildren(tabTemplates);
        tabTemplates.applyProperties();
    }

    public TFVBox FVBox16 = new TFVBox();

    private void init_FVBox16() {
        FVBox16.setName("FVBox16");
        FVBox16.setLeft(0);
        FVBox16.setTop(0);
        FVBox16.setWidth(874);
        FVBox16.setHeight(503);
        FVBox16.setAlign("alClient");
        FVBox16.setBorderStyle("stNone");
        FVBox16.setPaddingTop(0);
        FVBox16.setPaddingLeft(0);
        FVBox16.setPaddingRight(0);
        FVBox16.setPaddingBottom(0);
        FVBox16.setMarginTop(0);
        FVBox16.setMarginLeft(0);
        FVBox16.setMarginRight(0);
        FVBox16.setMarginBottom(0);
        FVBox16.setSpacing(1);
        FVBox16.setFlexVflex("ftTrue");
        FVBox16.setFlexHflex("ftTrue");
        FVBox16.setScrollable(false);
        FVBox16.setBoxShadowConfigHorizontalLength(10);
        FVBox16.setBoxShadowConfigVerticalLength(10);
        FVBox16.setBoxShadowConfigBlurRadius(5);
        FVBox16.setBoxShadowConfigSpreadRadius(0);
        FVBox16.setBoxShadowConfigShadowColor("clBlack");
        FVBox16.setBoxShadowConfigOpacity(75);
        tabTemplates.addChildren(FVBox16);
        FVBox16.applyProperties();
    }

    public TFVBox FVBox17 = new TFVBox();

    private void init_FVBox17() {
        FVBox17.setName("FVBox17");
        FVBox17.setLeft(0);
        FVBox17.setTop(0);
        FVBox17.setWidth(877);
        FVBox17.setHeight(144);
        FVBox17.setAlign("alClient");
        FVBox17.setBorderStyle("stNone");
        FVBox17.setPaddingTop(0);
        FVBox17.setPaddingLeft(0);
        FVBox17.setPaddingRight(0);
        FVBox17.setPaddingBottom(0);
        FVBox17.setMarginTop(0);
        FVBox17.setMarginLeft(0);
        FVBox17.setMarginRight(0);
        FVBox17.setMarginBottom(0);
        FVBox17.setSpacing(1);
        FVBox17.setFlexVflex("ftTrue");
        FVBox17.setFlexHflex("ftTrue");
        FVBox17.setScrollable(false);
        FVBox17.setBoxShadowConfigHorizontalLength(10);
        FVBox17.setBoxShadowConfigVerticalLength(10);
        FVBox17.setBoxShadowConfigBlurRadius(5);
        FVBox17.setBoxShadowConfigSpreadRadius(0);
        FVBox17.setBoxShadowConfigShadowColor("clBlack");
        FVBox17.setBoxShadowConfigOpacity(75);
        FVBox16.addChildren(FVBox17);
        FVBox17.applyProperties();
    }

    public TFGrid gridTemplate = new TFGrid();

    private void init_gridTemplate() {
        gridTemplate.setName("gridTemplate");
        gridTemplate.setLeft(0);
        gridTemplate.setTop(0);
        gridTemplate.setWidth(635);
        gridTemplate.setHeight(134);
        gridTemplate.setTable(tbDisparoTemplateEmpresa);
        gridTemplate.setFlexVflex("ftTrue");
        gridTemplate.setFlexHflex("ftTrue");
        gridTemplate.setPagingEnabled(true);
        gridTemplate.setFrozenColumns(0);
        gridTemplate.setShowFooter(false);
        gridTemplate.setShowHeader(true);
        gridTemplate.setMultiSelection(false);
        gridTemplate.setGroupingEnabled(false);
        gridTemplate.setGroupingExpanded(false);
        gridTemplate.setGroupingShowFooter(false);
        gridTemplate.setCrosstabEnabled(false);
        gridTemplate.setCrosstabGroupType("cgtConcat");
        gridTemplate.setEditionEnabled(false);
        gridTemplate.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_EMPRESA_NOME_EMPRESA");
        item0.setTitleCaption("Empresa");
        item0.setWidth(221);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridTemplate.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TEMPLATE");
        item1.setTitleCaption("Template");
        item1.setWidth(234);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftCombo");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorLookupDesc("MODELO");
        item1.setEditorLookupKey("ID_EMAIL_MODELO");
        item1.setEditorLookupTable(tbEmailModelo);
        item1.setEditorEditType("etTFCombo");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridTemplate.getColumns().add(item1);
        FVBox17.addChildren(gridTemplate);
        gridTemplate.applyProperties();
    }

    public TFVBox FVBox18 = new TFVBox();

    private void init_FVBox18() {
        FVBox18.setName("FVBox18");
        FVBox18.setLeft(0);
        FVBox18.setTop(145);
        FVBox18.setWidth(870);
        FVBox18.setHeight(304);
        FVBox18.setAlign("alClient");
        FVBox18.setBorderStyle("stNone");
        FVBox18.setPaddingTop(5);
        FVBox18.setPaddingLeft(5);
        FVBox18.setPaddingRight(5);
        FVBox18.setPaddingBottom(5);
        FVBox18.setMarginTop(0);
        FVBox18.setMarginLeft(0);
        FVBox18.setMarginRight(0);
        FVBox18.setMarginBottom(0);
        FVBox18.setSpacing(1);
        FVBox18.setFlexVflex("ftTrue");
        FVBox18.setFlexHflex("ftTrue");
        FVBox18.setScrollable(true);
        FVBox18.setBoxShadowConfigHorizontalLength(10);
        FVBox18.setBoxShadowConfigVerticalLength(10);
        FVBox18.setBoxShadowConfigBlurRadius(5);
        FVBox18.setBoxShadowConfigSpreadRadius(0);
        FVBox18.setBoxShadowConfigShadowColor("clBlack");
        FVBox18.setBoxShadowConfigOpacity(75);
        FVBox16.addChildren(FVBox18);
        FVBox18.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(0);
        FHBox9.setWidth(438);
        FHBox9.setHeight(41);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(5);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox18.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFButton btnNovoTemplate = new TFButton();

    private void init_btnNovoTemplate() {
        btnNovoTemplate.setName("btnNovoTemplate");
        btnNovoTemplate.setLeft(0);
        btnNovoTemplate.setTop(0);
        btnNovoTemplate.setWidth(48);
        btnNovoTemplate.setHeight(35);
        btnNovoTemplate.setHint("Novo Item");
        btnNovoTemplate.setFontColor("clWindowText");
        btnNovoTemplate.setFontSize(-11);
        btnNovoTemplate.setFontName("Tahoma");
        btnNovoTemplate.setFontStyle("[]");
        btnNovoTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoTemplateClick(event);
            processarFlow("FrmChatBotAtivo", "btnNovoTemplate", "OnClick");
        });
        btnNovoTemplate.setImageId(6);
        btnNovoTemplate.setColor("clBtnFace");
        btnNovoTemplate.setAccess(false);
        btnNovoTemplate.setIconReverseDirection(false);
        FHBox9.addChildren(btnNovoTemplate);
        btnNovoTemplate.applyProperties();
    }

    public TFButton btnAlterarTemplate = new TFButton();

    private void init_btnAlterarTemplate() {
        btnAlterarTemplate.setName("btnAlterarTemplate");
        btnAlterarTemplate.setLeft(48);
        btnAlterarTemplate.setTop(0);
        btnAlterarTemplate.setWidth(48);
        btnAlterarTemplate.setHeight(35);
        btnAlterarTemplate.setHint("Alterar Item");
        btnAlterarTemplate.setFontColor("clWindowText");
        btnAlterarTemplate.setFontSize(-11);
        btnAlterarTemplate.setFontName("Tahoma");
        btnAlterarTemplate.setFontStyle("[]");
        btnAlterarTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarTemplateClick(event);
            processarFlow("FrmChatBotAtivo", "btnAlterarTemplate", "OnClick");
        });
        btnAlterarTemplate.setImageId(7);
        btnAlterarTemplate.setColor("clBtnFace");
        btnAlterarTemplate.setAccess(false);
        btnAlterarTemplate.setIconReverseDirection(false);
        FHBox9.addChildren(btnAlterarTemplate);
        btnAlterarTemplate.applyProperties();
    }

    public TFButton btnExcluirTemplate = new TFButton();

    private void init_btnExcluirTemplate() {
        btnExcluirTemplate.setName("btnExcluirTemplate");
        btnExcluirTemplate.setLeft(96);
        btnExcluirTemplate.setTop(0);
        btnExcluirTemplate.setWidth(48);
        btnExcluirTemplate.setHeight(35);
        btnExcluirTemplate.setHint("Excluir Item");
        btnExcluirTemplate.setFontColor("clWindowText");
        btnExcluirTemplate.setFontSize(-11);
        btnExcluirTemplate.setFontName("Tahoma");
        btnExcluirTemplate.setFontStyle("[]");
        btnExcluirTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirTemplateClick(event);
            processarFlow("FrmChatBotAtivo", "btnExcluirTemplate", "OnClick");
        });
        btnExcluirTemplate.setImageId(8);
        btnExcluirTemplate.setColor("clBtnFace");
        btnExcluirTemplate.setAccess(false);
        btnExcluirTemplate.setIconReverseDirection(false);
        FHBox9.addChildren(btnExcluirTemplate);
        btnExcluirTemplate.applyProperties();
    }

    public TFButton btnSalvarTemplate = new TFButton();

    private void init_btnSalvarTemplate() {
        btnSalvarTemplate.setName("btnSalvarTemplate");
        btnSalvarTemplate.setLeft(144);
        btnSalvarTemplate.setTop(0);
        btnSalvarTemplate.setWidth(48);
        btnSalvarTemplate.setHeight(35);
        btnSalvarTemplate.setHint("Salvar Item");
        btnSalvarTemplate.setFontColor("clWindowText");
        btnSalvarTemplate.setFontSize(-11);
        btnSalvarTemplate.setFontName("Tahoma");
        btnSalvarTemplate.setFontStyle("[]");
        btnSalvarTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarTemplateClick(event);
            processarFlow("FrmChatBotAtivo", "btnSalvarTemplate", "OnClick");
        });
        btnSalvarTemplate.setImageId(4);
        btnSalvarTemplate.setColor("clBtnFace");
        btnSalvarTemplate.setAccess(false);
        btnSalvarTemplate.setIconReverseDirection(false);
        FHBox9.addChildren(btnSalvarTemplate);
        btnSalvarTemplate.applyProperties();
    }

    public TFButton btnCancelarTemplate = new TFButton();

    private void init_btnCancelarTemplate() {
        btnCancelarTemplate.setName("btnCancelarTemplate");
        btnCancelarTemplate.setLeft(192);
        btnCancelarTemplate.setTop(0);
        btnCancelarTemplate.setWidth(48);
        btnCancelarTemplate.setHeight(35);
        btnCancelarTemplate.setHint("Cancelar Item");
        btnCancelarTemplate.setFontColor("clWindowText");
        btnCancelarTemplate.setFontSize(-11);
        btnCancelarTemplate.setFontName("Tahoma");
        btnCancelarTemplate.setFontStyle("[]");
        btnCancelarTemplate.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarTemplateClick(event);
            processarFlow("FrmChatBotAtivo", "btnCancelarTemplate", "OnClick");
        });
        btnCancelarTemplate.setImageId(9);
        btnCancelarTemplate.setColor("clBtnFace");
        btnCancelarTemplate.setAccess(false);
        btnCancelarTemplate.setIconReverseDirection(false);
        FHBox9.addChildren(btnCancelarTemplate);
        btnCancelarTemplate.applyProperties();
    }

    public TFVBox FVBox23 = new TFVBox();

    private void init_FVBox23() {
        FVBox23.setName("FVBox23");
        FVBox23.setLeft(0);
        FVBox23.setTop(42);
        FVBox23.setWidth(860);
        FVBox23.setHeight(58);
        FVBox23.setBorderStyle("stNone");
        FVBox23.setPaddingTop(0);
        FVBox23.setPaddingLeft(0);
        FVBox23.setPaddingRight(0);
        FVBox23.setPaddingBottom(0);
        FVBox23.setMarginTop(0);
        FVBox23.setMarginLeft(0);
        FVBox23.setMarginRight(0);
        FVBox23.setMarginBottom(0);
        FVBox23.setSpacing(1);
        FVBox23.setFlexVflex("ftFalse");
        FVBox23.setFlexHflex("ftTrue");
        FVBox23.setScrollable(false);
        FVBox23.setBoxShadowConfigHorizontalLength(10);
        FVBox23.setBoxShadowConfigVerticalLength(10);
        FVBox23.setBoxShadowConfigBlurRadius(5);
        FVBox23.setBoxShadowConfigSpreadRadius(0);
        FVBox23.setBoxShadowConfigShadowColor("clBlack");
        FVBox23.setBoxShadowConfigOpacity(75);
        FVBox18.addChildren(FVBox23);
        FVBox23.applyProperties();
    }

    public TFLabel FLabel11 = new TFLabel();

    private void init_FLabel11() {
        FLabel11.setName("FLabel11");
        FLabel11.setLeft(0);
        FLabel11.setTop(0);
        FLabel11.setWidth(41);
        FLabel11.setHeight(13);
        FLabel11.setAlign("alLeft");
        FLabel11.setCaption("Empresa");
        FLabel11.setFontColor("clWindowText");
        FLabel11.setFontSize(-11);
        FLabel11.setFontName("Tahoma");
        FLabel11.setFontStyle("[]");
        FLabel11.setVerticalAlignment("taAlignBottom");
        FLabel11.setWordBreak(false);
        FVBox23.addChildren(FLabel11);
        FLabel11.applyProperties();
    }

    public TFCombo ecmbTemplateEmpresa = new TFCombo();

    private void init_ecmbTemplateEmpresa() {
        ecmbTemplateEmpresa.setName("ecmbTemplateEmpresa");
        ecmbTemplateEmpresa.setLeft(0);
        ecmbTemplateEmpresa.setTop(14);
        ecmbTemplateEmpresa.setWidth(503);
        ecmbTemplateEmpresa.setHeight(21);
        ecmbTemplateEmpresa.setHint("Tipo custo.  Op\u00E7\u00F5es: Contabil=C; Forncedor=F");
        ecmbTemplateEmpresa.setTable(tbDisparoTemplateEmpresa);
        ecmbTemplateEmpresa.setLookupTable(tbEmpresas);
        ecmbTemplateEmpresa.setFieldName("COD_EMPRESA");
        ecmbTemplateEmpresa.setLookupKey("COD_EMPRESA");
        ecmbTemplateEmpresa.setLookupDesc("NOME");
        ecmbTemplateEmpresa.setFlex(true);
        ecmbTemplateEmpresa.setReadOnly(true);
        ecmbTemplateEmpresa.setRequired(true);
        ecmbTemplateEmpresa.setPrompt("Selecione");
        ecmbTemplateEmpresa.setConstraintCheckWhen("cwImmediate");
        ecmbTemplateEmpresa.setConstraintCheckType("ctExpression");
        ecmbTemplateEmpresa.setConstraintFocusOnError(false);
        ecmbTemplateEmpresa.setConstraintEnableUI(true);
        ecmbTemplateEmpresa.setConstraintEnabled(false);
        ecmbTemplateEmpresa.setConstraintFormCheck(true);
        ecmbTemplateEmpresa.setClearOnDelKey(false);
        ecmbTemplateEmpresa.setUseClearButton(false);
        ecmbTemplateEmpresa.setHideClearButtonOnNullValue(false);
        FVBox23.addChildren(ecmbTemplateEmpresa);
        ecmbTemplateEmpresa.applyProperties();
        addValidatable(ecmbTemplateEmpresa);
    }

    public TFVBox FVBox19 = new TFVBox();

    private void init_FVBox19() {
        FVBox19.setName("FVBox19");
        FVBox19.setLeft(0);
        FVBox19.setTop(101);
        FVBox19.setWidth(860);
        FVBox19.setHeight(58);
        FVBox19.setBorderStyle("stNone");
        FVBox19.setPaddingTop(0);
        FVBox19.setPaddingLeft(0);
        FVBox19.setPaddingRight(0);
        FVBox19.setPaddingBottom(0);
        FVBox19.setMarginTop(0);
        FVBox19.setMarginLeft(0);
        FVBox19.setMarginRight(0);
        FVBox19.setMarginBottom(0);
        FVBox19.setSpacing(1);
        FVBox19.setFlexVflex("ftFalse");
        FVBox19.setFlexHflex("ftTrue");
        FVBox19.setScrollable(false);
        FVBox19.setBoxShadowConfigHorizontalLength(10);
        FVBox19.setBoxShadowConfigVerticalLength(10);
        FVBox19.setBoxShadowConfigBlurRadius(5);
        FVBox19.setBoxShadowConfigSpreadRadius(0);
        FVBox19.setBoxShadowConfigShadowColor("clBlack");
        FVBox19.setBoxShadowConfigOpacity(75);
        FVBox18.addChildren(FVBox19);
        FVBox19.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(0);
        FLabel8.setTop(0);
        FLabel8.setWidth(44);
        FLabel8.setHeight(13);
        FLabel8.setAlign("alLeft");
        FLabel8.setCaption("Template");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taAlignBottom");
        FLabel8.setWordBreak(false);
        FVBox19.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFCombo ecmbTemplateTemplate = new TFCombo();

    private void init_ecmbTemplateTemplate() {
        ecmbTemplateTemplate.setName("ecmbTemplateTemplate");
        ecmbTemplateTemplate.setLeft(0);
        ecmbTemplateTemplate.setTop(14);
        ecmbTemplateTemplate.setWidth(503);
        ecmbTemplateTemplate.setHeight(21);
        ecmbTemplateTemplate.setHint("Tipo custo.  Op\u00E7\u00F5es: Contabil=C; Forncedor=F");
        ecmbTemplateTemplate.setTable(tbDisparoTemplateEmpresa);
        ecmbTemplateTemplate.setLookupTable(tbEmailModelo);
        ecmbTemplateTemplate.setFieldName("TEMPLATE");
        ecmbTemplateTemplate.setLookupKey("ID_EMAIL_MODELO");
        ecmbTemplateTemplate.setLookupDesc("MODELO");
        ecmbTemplateTemplate.setFlex(true);
        ecmbTemplateTemplate.setReadOnly(true);
        ecmbTemplateTemplate.setRequired(true);
        ecmbTemplateTemplate.setPrompt("Selecione");
        ecmbTemplateTemplate.setConstraintCheckWhen("cwImmediate");
        ecmbTemplateTemplate.setConstraintCheckType("ctExpression");
        ecmbTemplateTemplate.setConstraintFocusOnError(false);
        ecmbTemplateTemplate.setConstraintEnableUI(true);
        ecmbTemplateTemplate.setConstraintEnabled(false);
        ecmbTemplateTemplate.setConstraintFormCheck(true);
        ecmbTemplateTemplate.setClearOnDelKey(false);
        ecmbTemplateTemplate.setUseClearButton(false);
        ecmbTemplateTemplate.setHideClearButtonOnNullValue(false);
        FVBox19.addChildren(ecmbTemplateTemplate);
        ecmbTemplateTemplate.applyProperties();
        addValidatable(ecmbTemplateTemplate);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void filtrarGrid(final Event<Object> event) {
        if (btnConsultarDisparo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultarDisparo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarDisparoClick(final Event<Object> event) {
        if (btnAlterarDisparo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarDisparo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarDisparoClick(final Event<Object> event) {
        if (btnSalvarDisparo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarDisparo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarDisparoClick(final Event<Object> event) {
        if (btnCancelarDisparo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarDisparo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarChatBotClick(final Event<Object> event) {
        if (btnAlterarChatBot.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarChatBot");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarChatBotClick(final Event<Object> event) {
        if (btnSalvarChatBot.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarChatBot");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarChatBotClick(final Event<Object> event) {
        if (btnCancelarChatBot.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarChatBot");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoTemplateClick(final Event<Object> event) {
        if (btnNovoTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovoTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarTemplateClick(final Event<Object> event) {
        if (btnAlterarTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirTemplateClick(final Event<Object> event) {
        if (btnExcluirTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarTemplateClick(final Event<Object> event) {
        if (btnSalvarTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarTemplateClick(final Event<Object> event) {
        if (btnCancelarTemplate.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarTemplate");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void tbDisparo2AfterScroll(final Event<Object> event);

}