package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadastroLeadzap extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadastroLeadzapRNA rn = null;

    public FrmCadastroLeadzap() {
        try {
            rn = (freedom.bytecode.rn.CadastroLeadzapRNA) getRN(freedom.bytecode.rn.wizard.CadastroLeadzapRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCadastroWhatsapp();
        init_tbWhatsappEmpresa();
        init_tbEmpresas();
        init_tbWhatsappParametrosEmpresa();
        init_tbCadastroWhatsappValidar();
        init_tbEmpresasCruzaLeadZap();
        init_tbWhatsappLog();
        init_tbCadWhatsappAtivo();
        init_tbUf();
        init_tbCidades();
        init_tbCadastroWhatsappCidades();
        init_tbLeadzapMenu();
        init_filtroAvancado();
        init_popMenuPrincipal();
        init_menuItemAbreTabelaAux();
        init_menuHabilitaNavegacao();
        init_menuSelecaoMultipla();
        init_FMenuItem1();
        init_menuItemConfgGrid();
        init_menuItemExportPdf();
        init_menuItemExportExcel();
        init_menuItemHelp();
        init_gridConfig();
        init_spValidadeLeadZap();
        init_FHBox6();
        init_FHBox1();
        init_btnConsultar();
        init_btnFiltroAvancado();
        init_btnNovo();
        init_btnAlterar();
        init_btnExcluir();
        init_btnSalvar();
        init_btnSalvarContinuar();
        init_btnCancelar();
        init_FHBox3();
        init_btnAnterior();
        init_btnProximo();
        init_FHBox8();
        init_FHBox5();
        init_btnAceitar();
        init_btnMais();
        init_FHBox4();
        init_lblMensagem();
        init_pgPrincipal();
        init_tabListagem();
        init_FVBox1();
        init_grpBoxFiltro();
        init_gpFiltroPrincipal();
        init_lfDescricao();
        init_efDescricao();
        init_gridPrincipal();
        init_tabCadastro();
        init_FVBox2();
        init_grpBoxPrincipal();
        init_FGridPanel2();
        init_lbIdMenu44001();
        init_edIdMenu44001();
        init_FGroupbox1();
        init_FVBox3();
        init_hBoxXApiToken();
        init_FHBox25();
        init_FVBox7();
        init_FLabel15();
        init_strXApiToken();
        init_hBoxUrlApi();
        init_FHBox28();
        init_FVBox12();
        init_FLabel17();
        init_strUrlApi();
        init_FHBox29();
        init_FHBox30();
        init_FVBox13();
        init_FLabel16();
        init_strTokenApi();
        init_hBoxDDD();
        init_FHBox14();
        init_FVBox6();
        init_FLabel5();
        init_edCelular();
        init_FPanel13();
        init_FLabel13();
        init_FHBox19();
        init_chkAtivo();
        init_FHBox9();
        init_FHBox15();
        init_FVBox8();
        init_FLabel12();
        init_edDescricao();
        init_FPanel15();
        init_FLabel14();
        init_FHBox7();
        init_FHBox16();
        init_FVBox9();
        init_FLabel6();
        init_edCodEmpresa();
        init_FPanel11();
        init_FLabel4();
        init_FHBox31();
        init_FHBox32();
        init_FVBox14();
        init_FLabel18();
        init_edtUsuario();
        init_FHBox33();
        init_FVBox15();
        init_FLabel19();
        init_edtSenha();
        init_btnTestar();
        init_FPanel1();
        init_FLabel20();
        init_FHBox34();
        init_FHBox35();
        init_FVBox16();
        init_FLabel21();
        init_edtEmailNotificacaoApi();
        init_FPanel2();
        init_FLabel22();
        init_FHBox38();
        init_FHBox39();
        init_FVBox24();
        init_FLabel25();
        init_cbbLeadzapReceptivo();
        init_hboxTipoApi();
        init_hboxlblTipoApi();
        init_seplblTipoApi();
        init_lblTipoApi();
        init_cbbTipoApi();
        init_hboxDadosZapi();
        init_hboxInstancia();
        init_hbxlblInstancia();
        init_seplblInstancia();
        init_lblInstancia();
        init_edtInstancia();
        init_hoxTokenInstancia();
        init_hboxlblTokenInstancia();
        init_seplblTokenInstancia();
        init_lblTokenInstancia();
        init_edtTokenInstancia();
        init_hboxClienteToken();
        init_hboxlblClienteToken();
        init_seplblClienteToken();
        init_lblClienteToken();
        init_edtClienteToken();
        init_hboxbtnTestarConexaoZAPI();
        init_sepbtnTestarConexaoZAPI();
        init_btnTestarConexaoZAPI();
        init_FHBox13();
        init_FHBox20();
        init_FLabel9();
        init_FHBox27();
        init_FHBox11();
        init_btnCadastrar();
        init_btnSincronizar();
        init_FVBox5();
        init_FLabel10();
        init_lblStatusSinc();
        init_btnRefreshSinc();
        init_FHBox10();
        init_FHBox18();
        init_FVBox11();
        init_FLabel1();
        init_FPanel8();
        init_FPanel9();
        init_imgPerfil();
        init_FVBox4();
        init_FHBox21();
        init_btnUploadImage();
        init_btnLimparImagem();
        init_FLabel8();
        init_FHBox22();
        init_FHBox23();
        init_FLabel11();
        init_FHBox26();
        init_FHBox24();
        init_btnAtualizarLog();
        init_gridLog();
        init_tabCruzaEmpresa();
        init_vBoxCruzaEmpresa();
        init_gbDetalhe46001();
        init_gridPanelDetailEmpty46001();
        init_FHBox12();
        init_FLabel7();
        init_dualListEmpresasVinc();
        init_tabCidades();
        init_FVBox17();
        init_FHBox36();
        init_FVBox18();
        init_FLabel23();
        init_cbbUf();
        init_FVBox19();
        init_FLabel24();
        init_edtCidades();
        init_FVBox20();
        init_btnPesquisarCid();
        init_FHBox37();
        init_FGroupbox2();
        init_gridCidDisp();
        init_FVBox21();
        init_FVBox22();
        init_btnAddCidades();
        init_btnDelCidades();
        init_FVBox23();
        init_FGroupbox3();
        init_gridCidSel();
        init_scCrmCadastroWhatsapp();
        init_FrmCadastroLeadzap();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp;

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp = rn.tbCadastroWhatsapp;
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbCadastroWhatsappAfterScroll(event);
            processarFlow("FrmCadastroLeadzap", "tbCadastroWhatsapp", "OnAfterScroll");
        });
        tbCadastroWhatsapp.setWKey("440093;44001");
        tbCadastroWhatsapp.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsapp, "tbCadastroWhatsapp");
        tbCadastroWhatsapp.applyProperties();
    }

    public CRM_WHATSAPP_EMPRESA tbWhatsappEmpresa;

    private void init_tbWhatsappEmpresa() {
        tbWhatsappEmpresa = rn.tbWhatsappEmpresa;
        tbWhatsappEmpresa.setName("tbWhatsappEmpresa");
        tbWhatsappEmpresa.setMasterFields("ID_CELULAR");
        tbWhatsappEmpresa.setDetailFilters("ID_CELULAR");
        tbWhatsappEmpresa.setMaxRowCount(200);
        tbWhatsappEmpresa.setMasterTable(tbCadastroWhatsapp);
        tbWhatsappEmpresa.setWKey("440093;46001");
        tbWhatsappEmpresa.setRatioBatchSize(20);
        getTables().put(tbWhatsappEmpresa, "tbWhatsappEmpresa");
        tbWhatsappEmpresa.applyProperties();
    }

    public EMPRESAS tbEmpresas;

    private void init_tbEmpresas() {
        tbEmpresas = rn.tbEmpresas;
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.addEventListener("onBeforeOpen", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbEmpresasBeforeOpen(event);
            processarFlow("FrmCadastroLeadzap", "tbEmpresas", "OnBeforeOpen");
        });
        tbEmpresas.setWKey("440093;44002");
        tbEmpresas.setRatioBatchSize(20);
        getTables().put(tbEmpresas, "tbEmpresas");
        tbEmpresas.applyProperties();
    }

    public WHATSAPP_PARAMETROS_EMPRESA tbWhatsappParametrosEmpresa;

    private void init_tbWhatsappParametrosEmpresa() {
        tbWhatsappParametrosEmpresa = rn.tbWhatsappParametrosEmpresa;
        tbWhatsappParametrosEmpresa.setName("tbWhatsappParametrosEmpresa");
        tbWhatsappParametrosEmpresa.setMaxRowCount(200);
        tbWhatsappParametrosEmpresa.setWKey("440093;44003");
        tbWhatsappParametrosEmpresa.setRatioBatchSize(20);
        getTables().put(tbWhatsappParametrosEmpresa, "tbWhatsappParametrosEmpresa");
        tbWhatsappParametrosEmpresa.applyProperties();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsappValidar;

    private void init_tbCadastroWhatsappValidar() {
        tbCadastroWhatsappValidar = rn.tbCadastroWhatsappValidar;
        tbCadastroWhatsappValidar.setName("tbCadastroWhatsappValidar");
        tbCadastroWhatsappValidar.setMaxRowCount(200);
        tbCadastroWhatsappValidar.setWKey("440093;44004");
        tbCadastroWhatsappValidar.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsappValidar, "tbCadastroWhatsappValidar");
        tbCadastroWhatsappValidar.applyProperties();
    }

    public EMPRESAS tbEmpresasCruzaLeadZap;

    private void init_tbEmpresasCruzaLeadZap() {
        tbEmpresasCruzaLeadZap = rn.tbEmpresasCruzaLeadZap;
        tbEmpresasCruzaLeadZap.setName("tbEmpresasCruzaLeadZap");
        tbEmpresasCruzaLeadZap.setMaxRowCount(200);
        tbEmpresasCruzaLeadZap.setWKey("440093;46002");
        tbEmpresasCruzaLeadZap.setRatioBatchSize(20);
        getTables().put(tbEmpresasCruzaLeadZap, "tbEmpresasCruzaLeadZap");
        tbEmpresasCruzaLeadZap.applyProperties();
    }

    public CRM_WHATSAPP_LOG tbWhatsappLog;

    private void init_tbWhatsappLog() {
        tbWhatsappLog = rn.tbWhatsappLog;
        tbWhatsappLog.setName("tbWhatsappLog");
        tbWhatsappLog.setMaxRowCount(0);
        tbWhatsappLog.setWKey("440093;46003");
        tbWhatsappLog.setRatioBatchSize(20);
        getTables().put(tbWhatsappLog, "tbWhatsappLog");
        tbWhatsappLog.applyProperties();
    }

    public CRM_CAD_WHATSAPP_ATIVO tbCadWhatsappAtivo;

    private void init_tbCadWhatsappAtivo() {
        tbCadWhatsappAtivo = rn.tbCadWhatsappAtivo;
        tbCadWhatsappAtivo.setName("tbCadWhatsappAtivo");
        tbCadWhatsappAtivo.setMaxRowCount(200);
        tbCadWhatsappAtivo.setWKey("440093;46004");
        tbCadWhatsappAtivo.setRatioBatchSize(20);
        getTables().put(tbCadWhatsappAtivo, "tbCadWhatsappAtivo");
        tbCadWhatsappAtivo.applyProperties();
    }

    public UF tbUf;

    private void init_tbUf() {
        tbUf = rn.tbUf;
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(200);
        tbUf.setWKey("440093;46005");
        tbUf.setRatioBatchSize(20);
        getTables().put(tbUf, "tbUf");
        tbUf.applyProperties();
    }

    public CIDADES tbCidades;

    private void init_tbCidades() {
        tbCidades = rn.tbCidades;
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.addEventListener("onMaxRow", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbCidadesMaxRow(event);
            processarFlow("FrmCadastroLeadzap", "tbCidades", "OnMaxRow");
        });
        tbCidades.setWKey("440093;46006");
        tbCidades.setRatioBatchSize(20);
        getTables().put(tbCidades, "tbCidades");
        tbCidades.applyProperties();
    }

    public CRM_CADASTRO_WHATSAPP_CIDADES tbCadastroWhatsappCidades;

    private void init_tbCadastroWhatsappCidades() {
        tbCadastroWhatsappCidades = rn.tbCadastroWhatsappCidades;
        tbCadastroWhatsappCidades.setName("tbCadastroWhatsappCidades");
        tbCadastroWhatsappCidades.setMaxRowCount(200);
        tbCadastroWhatsappCidades.addEventListener("onMaxRow", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbCadastroWhatsappCidadesMaxRow(event);
            processarFlow("FrmCadastroLeadzap", "tbCadastroWhatsappCidades", "OnMaxRow");
        });
        tbCadastroWhatsappCidades.setWKey("440093;46007");
        tbCadastroWhatsappCidades.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsappCidades, "tbCadastroWhatsappCidades");
        tbCadastroWhatsappCidades.applyProperties();
    }

    public CRM_LEADZAP_MENU tbLeadzapMenu;

    private void init_tbLeadzapMenu() {
        tbLeadzapMenu = rn.tbLeadzapMenu;
        tbLeadzapMenu.setName("tbLeadzapMenu");
        tbLeadzapMenu.setMaxRowCount(200);
        tbLeadzapMenu.setWKey("440093;46008");
        tbLeadzapMenu.setRatioBatchSize(20);
        getTables().put(tbLeadzapMenu, "tbLeadzapMenu");
        tbLeadzapMenu.applyProperties();
    }

    public TFFilterWindow filtroAvancado = new TFFilterWindow();

    private void init_filtroAvancado() {
        filtroAvancado.setName("filtroAvancado");
        filtroAvancado.setWidth(450);
        filtroAvancado.setHeight(400);
        filtroAvancado.setCaption("Filtro");
        filtroAvancado.setColumns(2);
        filtroAvancado.setFilterStyle("fsAddCondition");
        filtroAvancado.applyProperties();
    }

    public TFPopupMenu popMenuPrincipal = new TFPopupMenu();

    private void init_popMenuPrincipal() {
        popMenuPrincipal.setName("popMenuPrincipal");
        FrmCadastroLeadzap.addChildren(popMenuPrincipal);
        popMenuPrincipal.applyProperties();
    }

    public TFMenuItem menuItemAbreTabelaAux = new TFMenuItem();

    private void init_menuItemAbreTabelaAux() {
        menuItemAbreTabelaAux.setName("menuItemAbreTabelaAux");
        menuItemAbreTabelaAux.setCaption("Abre Tabela Auxiliares");
        menuItemAbreTabelaAux.setHint("For\u00E7a a abertura das tabela auxiliares e lookup, visto que usuario pode abrir o cadastro em um aba independente e alterar o registro");
        menuItemAbreTabelaAux.setImageIndex(200022);
        menuItemAbreTabelaAux.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemAbreTabelaAuxClick(event);
            processarFlow("FrmCadastroLeadzap", "menuItemAbreTabelaAux", "OnClick");
        });
        menuItemAbreTabelaAux.setAccess(false);
        menuItemAbreTabelaAux.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemAbreTabelaAux);
        menuItemAbreTabelaAux.applyProperties();
    }

    public TFMenuItem menuHabilitaNavegacao = new TFMenuItem();

    private void init_menuHabilitaNavegacao() {
        menuHabilitaNavegacao.setName("menuHabilitaNavegacao");
        menuHabilitaNavegacao.setCaption("Habilitar Navega\u00E7\u00E3o Durante Edi\u00E7\u00E3o");
        menuHabilitaNavegacao.setHint("Quando habilita a navega\u00E7\u00E3o durante a edi\u00E7\u00E3o (Inclus\u00E3o/Altera\u00E7\u00E3o) ao mover para frente ou para tr\u00E1s o registro atual \u00E9 automaticamente salvo");
        menuHabilitaNavegacao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuHabilitaNavegacaoClick(event);
            processarFlow("FrmCadastroLeadzap", "menuHabilitaNavegacao", "OnClick");
        });
        menuHabilitaNavegacao.setAccess(true);
        menuHabilitaNavegacao.setCheckmark(true);
        popMenuPrincipal.addChildren(menuHabilitaNavegacao);
        menuHabilitaNavegacao.applyProperties();
    }

    public TFMenuItem menuSelecaoMultipla = new TFMenuItem();

    private void init_menuSelecaoMultipla() {
        menuSelecaoMultipla.setName("menuSelecaoMultipla");
        menuSelecaoMultipla.setCaption("Selecionar Multiplos Registros");
        menuSelecaoMultipla.setHint("Permite altera\u00E7\u00E3o/exclus\u00E3o de todos os registros selecionados");
        menuSelecaoMultipla.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuSelecaoMultiplaClick(event);
            processarFlow("FrmCadastroLeadzap", "menuSelecaoMultipla", "OnClick");
        });
        menuSelecaoMultipla.setAccess(true);
        menuSelecaoMultipla.setCheckmark(true);
        popMenuPrincipal.addChildren(menuSelecaoMultipla);
        menuSelecaoMultipla.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("Grid");
        FMenuItem1.setImageIndex(22006);
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        popMenuPrincipal.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem menuItemConfgGrid = new TFMenuItem();

    private void init_menuItemConfgGrid() {
        menuItemConfgGrid.setName("menuItemConfgGrid");
        menuItemConfgGrid.setCaption("Configurar Colunas da Grid");
        menuItemConfgGrid.setImageIndex(200021);
        menuItemConfgGrid.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemConfgGridClick(event);
            processarFlow("FrmCadastroLeadzap", "menuItemConfgGrid", "OnClick");
        });
        menuItemConfgGrid.setAccess(true);
        menuItemConfgGrid.setCheckmark(false);
        FMenuItem1.addChildren(menuItemConfgGrid);
        menuItemConfgGrid.applyProperties();
    }

    public TFMenuItem menuItemExportPdf = new TFMenuItem();

    private void init_menuItemExportPdf() {
        menuItemExportPdf.setName("menuItemExportPdf");
        menuItemExportPdf.setCaption("Exportar PDF");
        menuItemExportPdf.setImageIndex(22005);
        menuItemExportPdf.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportPdfClick(event);
            processarFlow("FrmCadastroLeadzap", "menuItemExportPdf", "OnClick");
        });
        menuItemExportPdf.setAccess(true);
        menuItemExportPdf.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportPdf);
        menuItemExportPdf.applyProperties();
    }

    public TFMenuItem menuItemExportExcel = new TFMenuItem();

    private void init_menuItemExportExcel() {
        menuItemExportExcel.setName("menuItemExportExcel");
        menuItemExportExcel.setCaption("Exportar para Excel");
        menuItemExportExcel.setImageIndex(22004);
        menuItemExportExcel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemExportExcelClick(event);
            processarFlow("FrmCadastroLeadzap", "menuItemExportExcel", "OnClick");
        });
        menuItemExportExcel.setAccess(true);
        menuItemExportExcel.setCheckmark(false);
        FMenuItem1.addChildren(menuItemExportExcel);
        menuItemExportExcel.applyProperties();
    }

    public TFMenuItem menuItemHelp = new TFMenuItem();

    private void init_menuItemHelp() {
        menuItemHelp.setName("menuItemHelp");
        menuItemHelp.setCaption("Help");
        menuItemHelp.setHint("Help da Tela");
        menuItemHelp.setImageIndex(11);
        menuItemHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            menuItemHelpClick(event);
            processarFlow("FrmCadastroLeadzap", "menuItemHelp", "OnClick");
        });
        menuItemHelp.setAccess(false);
        menuItemHelp.setCheckmark(false);
        popMenuPrincipal.addChildren(menuItemHelp);
        menuItemHelp.applyProperties();
    }

    public TFGridConfigWindow gridConfig = new TFGridConfigWindow();

    private void init_gridConfig() {
        gridConfig.setName("gridConfig");
        gridConfig.setWidth(500);
        gridConfig.setHeight(500);
        gridConfig.setCaption("Configura\u00E7\u00E3o de Grid");
        gridConfig.setGrid(gridPrincipal);
        gridConfig.applyProperties();
    }

    public TFStoredProcedure spValidadeLeadZap = new TFStoredProcedure();

    private void init_spValidadeLeadZap() {
        spValidadeLeadZap.setName("spValidadeLeadZap");
        TFStoredProcedureParam item0 = new TFStoredProcedureParam();
        item0.setDataType("dtNUMBER");
        item0.setDirection("pdIN");
        item0.setName("I_COD_EMPRESA");
        spValidadeLeadZap.getParams().add(item0);
        TFStoredProcedureParam item1 = new TFStoredProcedureParam();
        item1.setDataType("dtNUMBER");
        item1.setDirection("pdOUT");
        item1.setName("O_ID_CELULAR");
        spValidadeLeadZap.getParams().add(item1);
        TFStoredProcedureParam item2 = new TFStoredProcedureParam();
        item2.setDataType("dtVARCHAR2");
        item2.setDirection("pdOUT");
        item2.setName("O_RESULT");
        spValidadeLeadZap.getParams().add(item2);
        spValidadeLeadZap.setSql("call GETVALIDADELEADZAP(:I_COD_EMPRESA,:O_ID_CELULAR,:O_RESULT)");
        spValidadeLeadZap.applyProperties();
    }

    protected TFForm FrmCadastroLeadzap = this;
    private void init_FrmCadastroLeadzap() {
        FrmCadastroLeadzap.setName("FrmCadastroLeadzap");
        FrmCadastroLeadzap.setAlign("alTop");
        FrmCadastroLeadzap.setCaption("Cadastro LeadZap");
        FrmCadastroLeadzap.setClientHeight(749);
        FrmCadastroLeadzap.setClientWidth(992);
        FrmCadastroLeadzap.setColor("clWhite");
        FrmCadastroLeadzap.setWOrigem("EhMain");
        FrmCadastroLeadzap.setWKey("440093");
        TFShortcutKeyItem item0 = new TFShortcutKeyItem();
        item0.setModifier("smCtrl");
        item0.setKey("sk1");
        item0.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionPesquisar(event);
            processarFlow("FrmCadastroLeadzap", "item0", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item0);
        TFShortcutKeyItem item1 = new TFShortcutKeyItem();
        item1.setModifier("smCtrl");
        item1.setKey("sk2");
        item1.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionIncluir(event);
            processarFlow("FrmCadastroLeadzap", "item1", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item1);
        TFShortcutKeyItem item2 = new TFShortcutKeyItem();
        item2.setModifier("smCtrl");
        item2.setKey("sk3");
        item2.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionAlterar(event);
            processarFlow("FrmCadastroLeadzap", "item2", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item2);
        TFShortcutKeyItem item3 = new TFShortcutKeyItem();
        item3.setModifier("smCtrl");
        item3.setKey("sk4");
        item3.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionExcluir(event);
            processarFlow("FrmCadastroLeadzap", "item3", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item3);
        TFShortcutKeyItem item4 = new TFShortcutKeyItem();
        item4.setModifier("smCtrl");
        item4.setKey("sk5");
        item4.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionSalvar(event);
            processarFlow("FrmCadastroLeadzap", "item4", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item4);
        TFShortcutKeyItem item5 = new TFShortcutKeyItem();
        item5.setModifier("smCtrl");
        item5.setKey("sk6");
        item5.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionCancelar(event);
            processarFlow("FrmCadastroLeadzap", "item5", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item5);
        TFShortcutKeyItem item6 = new TFShortcutKeyItem();
        item6.setModifier("smCtrl");
        item6.setKey("sk7");
        item6.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionAnterior(event);
            processarFlow("FrmCadastroLeadzap", "item6", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item6);
        TFShortcutKeyItem item7 = new TFShortcutKeyItem();
        item7.setModifier("smCtrl");
        item7.setKey("sk8");
        item7.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionProximo(event);
            processarFlow("FrmCadastroLeadzap", "item7", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item7);
        TFShortcutKeyItem item8 = new TFShortcutKeyItem();
        item8.setModifier("smCtrl");
        item8.setKey("sk9");
        item8.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionAceitar(event);
            processarFlow("FrmCadastroLeadzap", "item8", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item8);
        TFShortcutKeyItem item9 = new TFShortcutKeyItem();
        item9.setModifier("smCtrl");
        item9.setKey("sk0");
        item9.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmCadastroLeadzapkeyActionSalvarContinuar(event);
            processarFlow("FrmCadastroLeadzap", "item9", "OnKeyAction");
        });
        FrmCadastroLeadzap.getShortcutKeys().add(item9);
        FrmCadastroLeadzap.setSpacing(0);
        FrmCadastroLeadzap.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridConfig.loadConfig();
        });
        FrmCadastroLeadzap.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(992);
        FHBox6.setHeight(68);
        FHBox6.setAlign("alTop");
        FHBox6.setBorderStyle("stNone");
        FHBox6.setColor("16514043");
        FHBox6.setPaddingTop(5);
        FHBox6.setPaddingLeft(2);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(5);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftTrue");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FrmCadastroLeadzap.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(829);
        FHBox1.setHeight(60);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(2);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FHBox6.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnConsultar = new TFButton();

    private void init_btnConsultar() {
        btnConsultar.setName("btnConsultar");
        btnConsultar.setLeft(0);
        btnConsultar.setTop(0);
        btnConsultar.setWidth(65);
        btnConsultar.setHeight(53);
        btnConsultar.setHint("Executa Pesquisa (CRTL+ 1)");
        btnConsultar.setCaption("Pesquisar");
        btnConsultar.setFontColor("clWindowText");
        btnConsultar.setFontSize(-11);
        btnConsultar.setFontName("Tahoma");
        btnConsultar.setFontStyle("[]");
        btnConsultar.setLayout("blGlyphTop");
        btnConsultar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConsultarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnConsultar", "OnClick");
        });
        btnConsultar.setImageId(13);
        btnConsultar.setColor("clBtnFace");
        btnConsultar.setAccess(true);
        btnConsultar.setIconReverseDirection(false);
        FHBox1.addChildren(btnConsultar);
        btnConsultar.applyProperties();
    }

    public TFButton btnFiltroAvancado = new TFButton();

    private void init_btnFiltroAvancado() {
        btnFiltroAvancado.setName("btnFiltroAvancado");
        btnFiltroAvancado.setLeft(65);
        btnFiltroAvancado.setTop(0);
        btnFiltroAvancado.setWidth(65);
        btnFiltroAvancado.setHeight(53);
        btnFiltroAvancado.setHint("Filtro Avan\u00E7ado");
        btnFiltroAvancado.setCaption("Filtro");
        btnFiltroAvancado.setFontColor("clWindowText");
        btnFiltroAvancado.setFontSize(-11);
        btnFiltroAvancado.setFontName("Tahoma");
        btnFiltroAvancado.setFontStyle("[]");
        btnFiltroAvancado.setLayout("blGlyphTop");
        btnFiltroAvancado.setVisible(false);
        btnFiltroAvancado.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFiltroAvancadoClick(event);
            processarFlow("FrmCadastroLeadzap", "btnFiltroAvancado", "OnClick");
        });
        btnFiltroAvancado.setImageId(50002);
        btnFiltroAvancado.setColor("clBtnFace");
        btnFiltroAvancado.setAccess(true);
        btnFiltroAvancado.setIconReverseDirection(false);
        FHBox1.addChildren(btnFiltroAvancado);
        btnFiltroAvancado.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(130);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(53);
        btnNovo.setHint("Inclui um Novo Registro  (CRTL+ 2)");
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmCadastroLeadzap", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(true);
        btnNovo.setIconReverseDirection(false);
        FHBox1.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(195);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(53);
        btnAlterar.setHint("Altera o Registro Selecionado  (CRTL+ 3)");
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(true);
        btnAlterar.setIconReverseDirection(false);
        FHBox1.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(260);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(53);
        btnExcluir.setHint("Exclui o Registro Selecionado  (CRTL+ 4)");
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmCadastroLeadzap", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(true);
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(325);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar  (CRTL+ 5)");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnSalvarContinuar = new TFButton();

    private void init_btnSalvarContinuar() {
        btnSalvarContinuar.setName("btnSalvarContinuar");
        btnSalvarContinuar.setLeft(390);
        btnSalvarContinuar.setTop(0);
        btnSalvarContinuar.setWidth(65);
        btnSalvarContinuar.setHeight(53);
        btnSalvarContinuar.setHint("Salvar e Continuar");
        btnSalvarContinuar.setCaption("Salvar Cont.");
        btnSalvarContinuar.setFontColor("clWindowText");
        btnSalvarContinuar.setFontSize(-11);
        btnSalvarContinuar.setFontName("Tahoma");
        btnSalvarContinuar.setFontStyle("[]");
        btnSalvarContinuar.setLayout("blGlyphTop");
        btnSalvarContinuar.setVisible(false);
        btnSalvarContinuar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarContinuarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnSalvarContinuar", "OnClick");
        });
        btnSalvarContinuar.setImageId(22001);
        btnSalvarContinuar.setColor("clBtnFace");
        btnSalvarContinuar.setAccess(false);
        btnSalvarContinuar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvarContinuar);
        btnSalvarContinuar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(455);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(53);
        btnCancelar.setHint("Cancela as Altera\u00E7\u00F5es Correntes  (CRTL+ 6)");
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(520);
        FHBox3.setTop(0);
        FHBox3.setWidth(26);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setVisible(false);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnAnterior = new TFButton();

    private void init_btnAnterior() {
        btnAnterior.setName("btnAnterior");
        btnAnterior.setLeft(546);
        btnAnterior.setTop(0);
        btnAnterior.setWidth(65);
        btnAnterior.setHeight(53);
        btnAnterior.setHint("Registro Anterior, Estando em Modo de Inclus\u00E3o/Altera\u00E7\u00E3o Salva Antes de Mover o Registro (CRTL+ 7)");
        btnAnterior.setFontColor("clWindowText");
        btnAnterior.setFontSize(-11);
        btnAnterior.setFontName("Tahoma");
        btnAnterior.setFontStyle("[]");
        btnAnterior.setLayout("blGlyphTop");
        btnAnterior.setVisible(false);
        btnAnterior.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAnteriorClick(event);
            processarFlow("FrmCadastroLeadzap", "btnAnterior", "OnClick");
        });
        btnAnterior.setImageId(14);
        btnAnterior.setColor("clBtnFace");
        btnAnterior.setAccess(false);
        btnAnterior.setIconReverseDirection(false);
        FHBox1.addChildren(btnAnterior);
        btnAnterior.applyProperties();
    }

    public TFButton btnProximo = new TFButton();

    private void init_btnProximo() {
        btnProximo.setName("btnProximo");
        btnProximo.setLeft(611);
        btnProximo.setTop(0);
        btnProximo.setWidth(65);
        btnProximo.setHeight(53);
        btnProximo.setHint("Pr\u00F3ximo Registro   (CRTL+ 8)");
        btnProximo.setFontColor("clWindowText");
        btnProximo.setFontSize(-11);
        btnProximo.setFontName("Tahoma");
        btnProximo.setFontStyle("[]");
        btnProximo.setLayout("blGlyphTop");
        btnProximo.setVisible(false);
        btnProximo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnProximoClick(event);
            processarFlow("FrmCadastroLeadzap", "btnProximo", "OnClick");
        });
        btnProximo.setImageId(15);
        btnProximo.setColor("clBtnFace");
        btnProximo.setAccess(false);
        btnProximo.setIconReverseDirection(false);
        FHBox1.addChildren(btnProximo);
        btnProximo.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(676);
        FHBox8.setTop(0);
        FHBox8.setWidth(32);
        FHBox8.setHeight(32);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FHBox1.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(829);
        FHBox5.setTop(0);
        FHBox5.setWidth(146);
        FHBox5.setHeight(60);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(2);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setVisible(false);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(3);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FHBox6.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(0);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(65);
        btnAceitar.setHeight(53);
        btnAceitar.setHint("Aceita o Registro Selecionado para Outro Formulario  (CRTL+ 9)");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox5.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnMais = new TFButton();

    private void init_btnMais() {
        btnMais.setName("btnMais");
        btnMais.setLeft(65);
        btnMais.setTop(0);
        btnMais.setWidth(65);
        btnMais.setHeight(53);
        btnMais.setHint("Mais Op\u00E7\u00F5es");
        btnMais.setFontColor("clWindowText");
        btnMais.setFontSize(-21);
        btnMais.setFontName("Tahoma");
        btnMais.setFontStyle("[]");
        btnMais.setLayout("blGlyphTop");
        btnMais.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnMaisClick(event);
            processarFlow("FrmCadastroLeadzap", "btnMais", "OnClick");
        });
        btnMais.setImageId(22002);
        btnMais.setColor("clBtnFace");
        btnMais.setAccess(false);
        btnMais.setIconReverseDirection(false);
        FHBox5.addChildren(btnMais);
        btnMais.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(3);
        FHBox4.setTop(69);
        FHBox4.setWidth(970);
        FHBox4.setHeight(23);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FrmCadastroLeadzap.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblMensagem = new TFLabel();

    private void init_lblMensagem() {
        lblMensagem.setName("lblMensagem");
        lblMensagem.setLeft(0);
        lblMensagem.setTop(0);
        lblMensagem.setWidth(78);
        lblMensagem.setHeight(16);
        lblMensagem.setCaption("Mensagem....");
        lblMensagem.setFontColor("clNavy");
        lblMensagem.setFontSize(-13);
        lblMensagem.setFontName("Tahoma");
        lblMensagem.setFontStyle("[]");
        lblMensagem.setVerticalAlignment("taAlignTop");
        lblMensagem.setWordBreak(false);
        FHBox4.addChildren(lblMensagem);
        lblMensagem.applyProperties();
    }

    public TFPageControl pgPrincipal = new TFPageControl();

    private void init_pgPrincipal() {
        pgPrincipal.setName("pgPrincipal");
        pgPrincipal.setLeft(5);
        pgPrincipal.setTop(91);
        pgPrincipal.setWidth(974);
        pgPrincipal.setHeight(982);
        pgPrincipal.setTabPosition("tpTop");
        pgPrincipal.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgPrincipalChange(event);
            processarFlow("FrmCadastroLeadzap", "pgPrincipal", "OnChange");
        });
        pgPrincipal.setFlexVflex("ftTrue");
        pgPrincipal.setFlexHflex("ftTrue");
        pgPrincipal.setRenderStyle("rsTabbed");
        pgPrincipal.applyProperties();
        FrmCadastroLeadzap.addChildren(pgPrincipal);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setVisible(true);
        tabListagem.setClosable(false);
        pgPrincipal.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(966);
        FVBox1.setHeight(954);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setColor("clWhite");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        tabListagem.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGroupbox grpBoxFiltro = new TFGroupbox();

    private void init_grpBoxFiltro() {
        grpBoxFiltro.setName("grpBoxFiltro");
        grpBoxFiltro.setLeft(0);
        grpBoxFiltro.setTop(0);
        grpBoxFiltro.setWidth(960);
        grpBoxFiltro.setHeight(74);
        grpBoxFiltro.setCaption("Filtro R\u00E1pido");
        grpBoxFiltro.setFontColor("clWindowText");
        grpBoxFiltro.setFontSize(-11);
        grpBoxFiltro.setFontName("Tahoma");
        grpBoxFiltro.setFontStyle("[]");
        grpBoxFiltro.setVisible(false);
        grpBoxFiltro.setFlexVflex("ftMin");
        grpBoxFiltro.setFlexHflex("ftTrue");
        grpBoxFiltro.setScrollable(false);
        grpBoxFiltro.setClosable(true);
        grpBoxFiltro.setClosed(false);
        grpBoxFiltro.setOrient("coHorizontal");
        grpBoxFiltro.setStyle("grp3D");
        grpBoxFiltro.setHeaderImageId(0);
        FVBox1.addChildren(grpBoxFiltro);
        grpBoxFiltro.applyProperties();
    }

    public TFGridPanel gpFiltroPrincipal = new TFGridPanel();

    private void init_gpFiltroPrincipal() {
        gpFiltroPrincipal.setName("gpFiltroPrincipal");
        gpFiltroPrincipal.setLeft(2);
        gpFiltroPrincipal.setTop(15);
        gpFiltroPrincipal.setWidth(956);
        gpFiltroPrincipal.setHeight(47);
        gpFiltroPrincipal.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(600.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(50.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item1);
        TFGridPanelColumn item2 = new TFGridPanelColumn();
        item2.setValue(50.000000000000000000);
        gpFiltroPrincipal.getColumnCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(0);
        item3.setControl("lfDescricao");
        item3.setRow(0);
        gpFiltroPrincipal.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(0);
        item4.setControl("efDescricao");
        item4.setRow(1);
        gpFiltroPrincipal.getControlCollection().add(item4);
        TFGridPanelRow item5 = new TFGridPanelRow();
        item5.setSizeStyle("ssAbsolute");
        item5.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item5);
        TFGridPanelRow item6 = new TFGridPanelRow();
        item6.setSizeStyle("ssAbsolute");
        item6.setValue(21.000000000000000000);
        gpFiltroPrincipal.getRowCollection().add(item6);
        gpFiltroPrincipal.setFlexVflex("ftTrue");
        gpFiltroPrincipal.setFlexHflex("ftTrue");
        gpFiltroPrincipal.setAllRowFlex(true);
        gpFiltroPrincipal.setColumnTabOrder(false);
        grpBoxFiltro.addChildren(gpFiltroPrincipal);
        gpFiltroPrincipal.applyProperties();
    }

    public TFLabel lfDescricao = new TFLabel();

    private void init_lfDescricao() {
        lfDescricao.setName("lfDescricao");
        lfDescricao.setLeft(1);
        lfDescricao.setTop(1);
        lfDescricao.setWidth(46);
        lfDescricao.setHeight(21);
        lfDescricao.setAlign("alLeft");
        lfDescricao.setCaption("Descri\u00E7\u00E3o");
        lfDescricao.setFontColor("clWindowText");
        lfDescricao.setFontSize(-11);
        lfDescricao.setFontName("Tahoma");
        lfDescricao.setFontStyle("[]");
        lfDescricao.setVerticalAlignment("taAlignBottom");
        lfDescricao.setWordBreak(false);
        gpFiltroPrincipal.addChildren(lfDescricao);
        lfDescricao.applyProperties();
    }

    public TFString efDescricao = new TFString();

    private void init_efDescricao() {
        efDescricao.setName("efDescricao");
        efDescricao.setLeft(1);
        efDescricao.setTop(22);
        efDescricao.setWidth(580);
        efDescricao.setHeight(21);
        efDescricao.setHint("Filtra pelo Descri\u00E7\u00E3o");
        efDescricao.setFlex(false);
        efDescricao.setRequired(false);
        efDescricao.setConstraintCheckWhen("cwImmediate");
        efDescricao.setConstraintCheckType("ctExpression");
        efDescricao.setConstraintFocusOnError(false);
        efDescricao.setConstraintEnableUI(true);
        efDescricao.setConstraintEnabled(false);
        efDescricao.setConstraintFormCheck(true);
        efDescricao.setCharCase("ccNormal");
        efDescricao.setPwd(false);
        efDescricao.setMaxlength(0);
        efDescricao.setAlign("alLeft");
        efDescricao.setFontColor("clWindowText");
        efDescricao.setFontSize(-13);
        efDescricao.setFontName("Tahoma");
        efDescricao.setFontStyle("[]");
        efDescricao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            efDescricaoEnter(event);
            processarFlow("FrmCadastroLeadzap", "efDescricao", "OnEnter");
        });
        efDescricao.setSaveLiteralCharacter(false);
        efDescricao.applyProperties();
        gpFiltroPrincipal.addChildren(efDescricao);
        addValidatable(efDescricao);
    }

    public TFGrid gridPrincipal = new TFGrid();

    private void init_gridPrincipal() {
        gridPrincipal.setName("gridPrincipal");
        gridPrincipal.setLeft(0);
        gridPrincipal.setTop(75);
        gridPrincipal.setWidth(956);
        gridPrincipal.setHeight(355);
        gridPrincipal.setTable(tbCadastroWhatsapp);
        gridPrincipal.setFlexVflex("ftTrue");
        gridPrincipal.setFlexHflex("ftTrue");
        gridPrincipal.setPagingEnabled(true);
        gridPrincipal.setFrozenColumns(0);
        gridPrincipal.setShowFooter(false);
        gridPrincipal.setShowHeader(true);
        gridPrincipal.setMultiSelection(false);
        gridPrincipal.setGroupingEnabled(false);
        gridPrincipal.setGroupingExpanded(false);
        gridPrincipal.setGroupingShowFooter(false);
        gridPrincipal.setCrosstabEnabled(false);
        gridPrincipal.setCrosstabGroupType("cgtConcat");
        gridPrincipal.setEditionEnabled(false);
        gridPrincipal.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption("Alt.");
        item0.setWidth(47);
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setEvalType("etExpression");
        item1.setImageId(7);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageAlterar(event);
            processarFlow("FrmCadastroLeadzap", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setTitleCaption("Exc.");
        item2.setWidth(45);
        item2.setVisible(false);
        item2.setPrecision(0);
        item2.setTextAlign("taCenter");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("*");
        item3.setEvalType("etExpression");
        item3.setImageId(8);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridPrincipalClickImageDelete(event);
            processarFlow("FrmCadastroLeadzap", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("CELULAR");
        item4.setTitleCaption("Celular");
        item4.setWidth(120);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setHint("Celular");
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DESCRICAO");
        item5.setTitleCaption("Descri\u00E7\u00E3o");
        item5.setWidth(698);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(true);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setHint("Descri\u00E7\u00E3o");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridPrincipal.getColumns().add(item5);
        FVBox1.addChildren(gridPrincipal);
        gridPrincipal.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        pgPrincipal.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(966);
        FVBox2.setHeight(954);
        FVBox2.setAlign("alClient");
        FVBox2.setBorderStyle("stNone");
        FVBox2.setColor("clWhite");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftMin");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFGroupbox grpBoxPrincipal = new TFGroupbox();

    private void init_grpBoxPrincipal() {
        grpBoxPrincipal.setName("grpBoxPrincipal");
        grpBoxPrincipal.setLeft(0);
        grpBoxPrincipal.setTop(0);
        grpBoxPrincipal.setWidth(729);
        grpBoxPrincipal.setHeight(58);
        grpBoxPrincipal.setCaption("Cadastro Whatsapp");
        grpBoxPrincipal.setFontColor("clWindowText");
        grpBoxPrincipal.setFontSize(-11);
        grpBoxPrincipal.setFontName("Tahoma");
        grpBoxPrincipal.setFontStyle("[]");
        grpBoxPrincipal.setVisible(false);
        grpBoxPrincipal.setFlexVflex("ftMin");
        grpBoxPrincipal.setFlexHflex("ftTrue");
        grpBoxPrincipal.setScrollable(true);
        grpBoxPrincipal.setClosable(true);
        grpBoxPrincipal.setClosed(false);
        grpBoxPrincipal.setOrient("coVertical");
        grpBoxPrincipal.setStyle("grp3D");
        grpBoxPrincipal.setHeaderImageId(0);
        FVBox2.addChildren(grpBoxPrincipal);
        grpBoxPrincipal.applyProperties();
    }

    public TFGridPanel FGridPanel2 = new TFGridPanel();

    private void init_FGridPanel2() {
        FGridPanel2.setName("FGridPanel2");
        FGridPanel2.setLeft(2);
        FGridPanel2.setTop(15);
        FGridPanel2.setWidth(725);
        FGridPanel2.setHeight(24);
        FGridPanel2.setAlign("alTop");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(145.000000000000000000);
        FGridPanel2.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(100.000000000000000000);
        FGridPanel2.getColumnCollection().add(item1);
        TFControlItem item2 = new TFControlItem();
        item2.setColumn(0);
        item2.setControl("lbIdMenu44001");
        item2.setRow(0);
        FGridPanel2.getControlCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("edIdMenu44001");
        item3.setRow(0);
        FGridPanel2.getControlCollection().add(item3);
        TFGridPanelRow item4 = new TFGridPanelRow();
        item4.setSizeStyle("ssAbsolute");
        item4.setValue(19.000000000000000000);
        FGridPanel2.getRowCollection().add(item4);
        FGridPanel2.setFlexVflex("ftFalse");
        FGridPanel2.setFlexHflex("ftTrue");
        FGridPanel2.setAllRowFlex(true);
        FGridPanel2.setColumnTabOrder(false);
        grpBoxPrincipal.addChildren(FGridPanel2);
        FGridPanel2.applyProperties();
    }

    public TFLabel lbIdMenu44001 = new TFLabel();

    private void init_lbIdMenu44001() {
        lbIdMenu44001.setName("lbIdMenu44001");
        lbIdMenu44001.setLeft(103);
        lbIdMenu44001.setTop(1);
        lbIdMenu44001.setWidth(43);
        lbIdMenu44001.setHeight(19);
        lbIdMenu44001.setAlign("alRight");
        lbIdMenu44001.setCaption("Id. Menu");
        lbIdMenu44001.setFontColor("clWindowText");
        lbIdMenu44001.setFontSize(-11);
        lbIdMenu44001.setFontName("Tahoma");
        lbIdMenu44001.setFontStyle("[]");
        lbIdMenu44001.setVerticalAlignment("taVerticalCenter");
        lbIdMenu44001.setWordBreak(false);
        FGridPanel2.addChildren(lbIdMenu44001);
        lbIdMenu44001.applyProperties();
    }

    public TFInteger edIdMenu44001 = new TFInteger();

    private void init_edIdMenu44001() {
        edIdMenu44001.setName("edIdMenu44001");
        edIdMenu44001.setLeft(146);
        edIdMenu44001.setTop(1);
        edIdMenu44001.setWidth(80);
        edIdMenu44001.setHeight(19);
        edIdMenu44001.setHint("yuiyuiy");
        edIdMenu44001.setTable(tbCadastroWhatsapp);
        edIdMenu44001.setFieldName("ID_MENU");
        edIdMenu44001.setHelpCaption("Id. Menu");
        edIdMenu44001.setFlex(false);
        edIdMenu44001.setRequired(false);
        edIdMenu44001.setConstraintExpression("value is null");
        edIdMenu44001.setConstraintMessage("Campo Id. Menu, preenchimento \u00E9 obrigat\u00F3rio");
        edIdMenu44001.setConstraintCheckWhen("cwImmediate");
        edIdMenu44001.setConstraintCheckType("ctExpression");
        edIdMenu44001.setConstraintFocusOnError(false);
        edIdMenu44001.setConstraintGroupName("grpTbcadastrowhatsapp");
        edIdMenu44001.setConstraintEnableUI(true);
        edIdMenu44001.setConstraintEnabled(true);
        edIdMenu44001.setConstraintFormCheck(true);
        edIdMenu44001.setMaxlength(0);
        edIdMenu44001.setAlign("alLeft");
        edIdMenu44001.setFontColor("clWindowText");
        edIdMenu44001.setFontSize(-13);
        edIdMenu44001.setFontName("Tahoma");
        edIdMenu44001.setFontStyle("[]");
        edIdMenu44001.setAlignment("taRightJustify");
        FGridPanel2.addChildren(edIdMenu44001);
        edIdMenu44001.applyProperties();
        addValidatable(edIdMenu44001);
    }

    public TFGroupbox FGroupbox1 = new TFGroupbox();

    private void init_FGroupbox1() {
        FGroupbox1.setName("FGroupbox1");
        FGroupbox1.setLeft(0);
        FGroupbox1.setTop(59);
        FGroupbox1.setWidth(737);
        FGroupbox1.setHeight(889);
        FGroupbox1.setCaption("Whatsapp");
        FGroupbox1.setFontColor("clWindowText");
        FGroupbox1.setFontSize(-11);
        FGroupbox1.setFontName("Tahoma");
        FGroupbox1.setFontStyle("[]");
        FGroupbox1.setFlexVflex("ftTrue");
        FGroupbox1.setFlexHflex("ftTrue");
        FGroupbox1.setScrollable(false);
        FGroupbox1.setClosable(false);
        FGroupbox1.setClosed(false);
        FGroupbox1.setOrient("coHorizontal");
        FGroupbox1.setStyle("grp3D");
        FGroupbox1.setHeaderImageId(0);
        FVBox2.addChildren(FGroupbox1);
        FGroupbox1.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(2);
        FVBox3.setTop(15);
        FVBox3.setWidth(733);
        FVBox3.setHeight(872);
        FVBox3.setAlign("alClient");
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(5);
        FVBox3.setPaddingLeft(5);
        FVBox3.setPaddingRight(5);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(3);
        FVBox3.setFlexVflex("ftMin");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FGroupbox1.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox hBoxXApiToken = new TFHBox();

    private void init_hBoxXApiToken() {
        hBoxXApiToken.setName("hBoxXApiToken");
        hBoxXApiToken.setLeft(0);
        hBoxXApiToken.setTop(0);
        hBoxXApiToken.setWidth(724);
        hBoxXApiToken.setHeight(38);
        hBoxXApiToken.setBorderStyle("stNone");
        hBoxXApiToken.setPaddingTop(0);
        hBoxXApiToken.setPaddingLeft(0);
        hBoxXApiToken.setPaddingRight(0);
        hBoxXApiToken.setPaddingBottom(0);
        hBoxXApiToken.setMarginTop(0);
        hBoxXApiToken.setMarginLeft(0);
        hBoxXApiToken.setMarginRight(0);
        hBoxXApiToken.setMarginBottom(0);
        hBoxXApiToken.setSpacing(5);
        hBoxXApiToken.setFlexVflex("ftFalse");
        hBoxXApiToken.setFlexHflex("ftTrue");
        hBoxXApiToken.setScrollable(false);
        hBoxXApiToken.setBoxShadowConfigHorizontalLength(10);
        hBoxXApiToken.setBoxShadowConfigVerticalLength(10);
        hBoxXApiToken.setBoxShadowConfigBlurRadius(5);
        hBoxXApiToken.setBoxShadowConfigSpreadRadius(0);
        hBoxXApiToken.setBoxShadowConfigShadowColor("clBlack");
        hBoxXApiToken.setBoxShadowConfigOpacity(75);
        hBoxXApiToken.setVAlign("tvTop");
        FVBox3.addChildren(hBoxXApiToken);
        hBoxXApiToken.applyProperties();
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(0);
        FHBox25.setTop(0);
        FHBox25.setWidth(120);
        FHBox25.setHeight(30);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(10);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(7);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(1);
        FHBox25.setFlexVflex("ftFalse");
        FHBox25.setFlexHflex("ftFalse");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvMiddle");
        hBoxXApiToken.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(0);
        FVBox7.setWidth(48);
        FVBox7.setHeight(28);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftTrue");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox25.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFLabel FLabel15 = new TFLabel();

    private void init_FLabel15() {
        FLabel15.setName("FLabel15");
        FLabel15.setLeft(48);
        FLabel15.setTop(0);
        FLabel15.setWidth(64);
        FLabel15.setHeight(13);
        FLabel15.setAlign("alRight");
        FLabel15.setCaption("X-API-TOKEN");
        FLabel15.setFontColor("clWindowText");
        FLabel15.setFontSize(-11);
        FLabel15.setFontName("Tahoma");
        FLabel15.setFontStyle("[]");
        FLabel15.setVerticalAlignment("taVerticalCenter");
        FLabel15.setWordBreak(false);
        FHBox25.addChildren(FLabel15);
        FLabel15.applyProperties();
    }

    public TFString strXApiToken = new TFString();

    private void init_strXApiToken() {
        strXApiToken.setName("strXApiToken");
        strXApiToken.setLeft(120);
        strXApiToken.setTop(0);
        strXApiToken.setWidth(318);
        strXApiToken.setHeight(24);
        strXApiToken.setTable(tbCadastroWhatsapp);
        strXApiToken.setFieldName("X_API_TOKEN");
        strXApiToken.setFlex(true);
        strXApiToken.setRequired(true);
        strXApiToken.setConstraintCheckWhen("cwImmediate");
        strXApiToken.setConstraintCheckType("ctExpression");
        strXApiToken.setConstraintFocusOnError(false);
        strXApiToken.setConstraintEnableUI(true);
        strXApiToken.setConstraintEnabled(false);
        strXApiToken.setConstraintFormCheck(true);
        strXApiToken.setCharCase("ccNormal");
        strXApiToken.setPwd(false);
        strXApiToken.setMaxlength(0);
        strXApiToken.setFontColor("clWindowText");
        strXApiToken.setFontSize(-13);
        strXApiToken.setFontName("Tahoma");
        strXApiToken.setFontStyle("[]");
        strXApiToken.setSaveLiteralCharacter(false);
        strXApiToken.applyProperties();
        hBoxXApiToken.addChildren(strXApiToken);
        addValidatable(strXApiToken);
    }

    public TFHBox hBoxUrlApi = new TFHBox();

    private void init_hBoxUrlApi() {
        hBoxUrlApi.setName("hBoxUrlApi");
        hBoxUrlApi.setLeft(0);
        hBoxUrlApi.setTop(39);
        hBoxUrlApi.setWidth(724);
        hBoxUrlApi.setHeight(38);
        hBoxUrlApi.setBorderStyle("stNone");
        hBoxUrlApi.setPaddingTop(0);
        hBoxUrlApi.setPaddingLeft(0);
        hBoxUrlApi.setPaddingRight(0);
        hBoxUrlApi.setPaddingBottom(0);
        hBoxUrlApi.setMarginTop(0);
        hBoxUrlApi.setMarginLeft(0);
        hBoxUrlApi.setMarginRight(0);
        hBoxUrlApi.setMarginBottom(0);
        hBoxUrlApi.setSpacing(5);
        hBoxUrlApi.setFlexVflex("ftFalse");
        hBoxUrlApi.setFlexHflex("ftTrue");
        hBoxUrlApi.setScrollable(false);
        hBoxUrlApi.setBoxShadowConfigHorizontalLength(10);
        hBoxUrlApi.setBoxShadowConfigVerticalLength(10);
        hBoxUrlApi.setBoxShadowConfigBlurRadius(5);
        hBoxUrlApi.setBoxShadowConfigSpreadRadius(0);
        hBoxUrlApi.setBoxShadowConfigShadowColor("clBlack");
        hBoxUrlApi.setBoxShadowConfigOpacity(75);
        hBoxUrlApi.setVAlign("tvTop");
        FVBox3.addChildren(hBoxUrlApi);
        hBoxUrlApi.applyProperties();
    }

    public TFHBox FHBox28 = new TFHBox();

    private void init_FHBox28() {
        FHBox28.setName("FHBox28");
        FHBox28.setLeft(0);
        FHBox28.setTop(0);
        FHBox28.setWidth(120);
        FHBox28.setHeight(30);
        FHBox28.setBorderStyle("stNone");
        FHBox28.setPaddingTop(10);
        FHBox28.setPaddingLeft(0);
        FHBox28.setPaddingRight(7);
        FHBox28.setPaddingBottom(0);
        FHBox28.setMarginTop(0);
        FHBox28.setMarginLeft(0);
        FHBox28.setMarginRight(0);
        FHBox28.setMarginBottom(0);
        FHBox28.setSpacing(1);
        FHBox28.setFlexVflex("ftFalse");
        FHBox28.setFlexHflex("ftFalse");
        FHBox28.setScrollable(false);
        FHBox28.setBoxShadowConfigHorizontalLength(10);
        FHBox28.setBoxShadowConfigVerticalLength(10);
        FHBox28.setBoxShadowConfigBlurRadius(5);
        FHBox28.setBoxShadowConfigSpreadRadius(0);
        FHBox28.setBoxShadowConfigShadowColor("clBlack");
        FHBox28.setBoxShadowConfigOpacity(75);
        FHBox28.setVAlign("tvMiddle");
        hBoxUrlApi.addChildren(FHBox28);
        FHBox28.applyProperties();
    }

    public TFVBox FVBox12 = new TFVBox();

    private void init_FVBox12() {
        FVBox12.setName("FVBox12");
        FVBox12.setLeft(0);
        FVBox12.setTop(0);
        FVBox12.setWidth(74);
        FVBox12.setHeight(28);
        FVBox12.setBorderStyle("stNone");
        FVBox12.setPaddingTop(0);
        FVBox12.setPaddingLeft(0);
        FVBox12.setPaddingRight(0);
        FVBox12.setPaddingBottom(0);
        FVBox12.setMarginTop(0);
        FVBox12.setMarginLeft(0);
        FVBox12.setMarginRight(0);
        FVBox12.setMarginBottom(0);
        FVBox12.setSpacing(1);
        FVBox12.setFlexVflex("ftTrue");
        FVBox12.setFlexHflex("ftTrue");
        FVBox12.setScrollable(false);
        FVBox12.setBoxShadowConfigHorizontalLength(10);
        FVBox12.setBoxShadowConfigVerticalLength(10);
        FVBox12.setBoxShadowConfigBlurRadius(5);
        FVBox12.setBoxShadowConfigSpreadRadius(0);
        FVBox12.setBoxShadowConfigShadowColor("clBlack");
        FVBox12.setBoxShadowConfigOpacity(75);
        FHBox28.addChildren(FVBox12);
        FVBox12.applyProperties();
    }

    public TFLabel FLabel17 = new TFLabel();

    private void init_FLabel17() {
        FLabel17.setName("FLabel17");
        FLabel17.setLeft(74);
        FLabel17.setTop(0);
        FLabel17.setWidth(37);
        FLabel17.setHeight(13);
        FLabel17.setAlign("alRight");
        FLabel17.setCaption("URL Api");
        FLabel17.setFontColor("clWindowText");
        FLabel17.setFontSize(-11);
        FLabel17.setFontName("Tahoma");
        FLabel17.setFontStyle("[]");
        FLabel17.setVerticalAlignment("taVerticalCenter");
        FLabel17.setWordBreak(false);
        FHBox28.addChildren(FLabel17);
        FLabel17.applyProperties();
    }

    public TFString strUrlApi = new TFString();

    private void init_strUrlApi() {
        strUrlApi.setName("strUrlApi");
        strUrlApi.setLeft(120);
        strUrlApi.setTop(0);
        strUrlApi.setWidth(318);
        strUrlApi.setHeight(24);
        strUrlApi.setTable(tbCadastroWhatsapp);
        strUrlApi.setFieldName("URL_API");
        strUrlApi.setFlex(true);
        strUrlApi.setRequired(true);
        strUrlApi.setConstraintCheckWhen("cwImmediate");
        strUrlApi.setConstraintCheckType("ctExpression");
        strUrlApi.setConstraintFocusOnError(false);
        strUrlApi.setConstraintEnableUI(true);
        strUrlApi.setConstraintEnabled(false);
        strUrlApi.setConstraintFormCheck(true);
        strUrlApi.setCharCase("ccNormal");
        strUrlApi.setPwd(false);
        strUrlApi.setMaxlength(0);
        strUrlApi.setFontColor("clWindowText");
        strUrlApi.setFontSize(-13);
        strUrlApi.setFontName("Tahoma");
        strUrlApi.setFontStyle("[]");
        strUrlApi.setSaveLiteralCharacter(false);
        strUrlApi.applyProperties();
        hBoxUrlApi.addChildren(strUrlApi);
        addValidatable(strUrlApi);
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(78);
        FHBox29.setWidth(724);
        FHBox29.setHeight(38);
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(5);
        FHBox29.setFlexVflex("ftFalse");
        FHBox29.setFlexHflex("ftTrue");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        FVBox3.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFHBox FHBox30 = new TFHBox();

    private void init_FHBox30() {
        FHBox30.setName("FHBox30");
        FHBox30.setLeft(0);
        FHBox30.setTop(0);
        FHBox30.setWidth(120);
        FHBox30.setHeight(30);
        FHBox30.setBorderStyle("stNone");
        FHBox30.setPaddingTop(10);
        FHBox30.setPaddingLeft(0);
        FHBox30.setPaddingRight(7);
        FHBox30.setPaddingBottom(0);
        FHBox30.setMarginTop(0);
        FHBox30.setMarginLeft(0);
        FHBox30.setMarginRight(0);
        FHBox30.setMarginBottom(0);
        FHBox30.setSpacing(1);
        FHBox30.setFlexVflex("ftFalse");
        FHBox30.setFlexHflex("ftFalse");
        FHBox30.setScrollable(false);
        FHBox30.setBoxShadowConfigHorizontalLength(10);
        FHBox30.setBoxShadowConfigVerticalLength(10);
        FHBox30.setBoxShadowConfigBlurRadius(5);
        FHBox30.setBoxShadowConfigSpreadRadius(0);
        FHBox30.setBoxShadowConfigShadowColor("clBlack");
        FHBox30.setBoxShadowConfigOpacity(75);
        FHBox30.setVAlign("tvMiddle");
        FHBox29.addChildren(FHBox30);
        FHBox30.applyProperties();
    }

    public TFVBox FVBox13 = new TFVBox();

    private void init_FVBox13() {
        FVBox13.setName("FVBox13");
        FVBox13.setLeft(0);
        FVBox13.setTop(0);
        FVBox13.setWidth(37);
        FVBox13.setHeight(28);
        FVBox13.setBorderStyle("stNone");
        FVBox13.setPaddingTop(0);
        FVBox13.setPaddingLeft(0);
        FVBox13.setPaddingRight(0);
        FVBox13.setPaddingBottom(0);
        FVBox13.setMarginTop(0);
        FVBox13.setMarginLeft(0);
        FVBox13.setMarginRight(0);
        FVBox13.setMarginBottom(0);
        FVBox13.setSpacing(1);
        FVBox13.setFlexVflex("ftTrue");
        FVBox13.setFlexHflex("ftTrue");
        FVBox13.setScrollable(false);
        FVBox13.setBoxShadowConfigHorizontalLength(10);
        FVBox13.setBoxShadowConfigVerticalLength(10);
        FVBox13.setBoxShadowConfigBlurRadius(5);
        FVBox13.setBoxShadowConfigSpreadRadius(0);
        FVBox13.setBoxShadowConfigShadowColor("clBlack");
        FVBox13.setBoxShadowConfigOpacity(75);
        FHBox30.addChildren(FVBox13);
        FVBox13.applyProperties();
    }

    public TFLabel FLabel16 = new TFLabel();

    private void init_FLabel16() {
        FLabel16.setName("FLabel16");
        FLabel16.setLeft(37);
        FLabel16.setTop(0);
        FLabel16.setWidth(75);
        FLabel16.setHeight(13);
        FLabel16.setAlign("alRight");
        FLabel16.setCaption("URL Painel Web");
        FLabel16.setFontColor("clWindowText");
        FLabel16.setFontSize(-11);
        FLabel16.setFontName("Tahoma");
        FLabel16.setFontStyle("[]");
        FLabel16.setVerticalAlignment("taVerticalCenter");
        FLabel16.setWordBreak(false);
        FHBox30.addChildren(FLabel16);
        FLabel16.applyProperties();
    }

    public TFString strTokenApi = new TFString();

    private void init_strTokenApi() {
        strTokenApi.setName("strTokenApi");
        strTokenApi.setLeft(120);
        strTokenApi.setTop(0);
        strTokenApi.setWidth(318);
        strTokenApi.setHeight(24);
        strTokenApi.setTable(tbCadastroWhatsapp);
        strTokenApi.setFieldName("URL_PAINEL_WEB");
        strTokenApi.setFlex(true);
        strTokenApi.setRequired(true);
        strTokenApi.setConstraintCheckWhen("cwImmediate");
        strTokenApi.setConstraintCheckType("ctExpression");
        strTokenApi.setConstraintFocusOnError(false);
        strTokenApi.setConstraintEnableUI(true);
        strTokenApi.setConstraintEnabled(false);
        strTokenApi.setConstraintFormCheck(true);
        strTokenApi.setCharCase("ccNormal");
        strTokenApi.setPwd(false);
        strTokenApi.setMaxlength(0);
        strTokenApi.setFontColor("clWindowText");
        strTokenApi.setFontSize(-13);
        strTokenApi.setFontName("Tahoma");
        strTokenApi.setFontStyle("[]");
        strTokenApi.setSaveLiteralCharacter(false);
        strTokenApi.applyProperties();
        FHBox29.addChildren(strTokenApi);
        addValidatable(strTokenApi);
    }

    public TFHBox hBoxDDD = new TFHBox();

    private void init_hBoxDDD() {
        hBoxDDD.setName("hBoxDDD");
        hBoxDDD.setLeft(0);
        hBoxDDD.setTop(117);
        hBoxDDD.setWidth(724);
        hBoxDDD.setHeight(38);
        hBoxDDD.setBorderStyle("stNone");
        hBoxDDD.setPaddingTop(0);
        hBoxDDD.setPaddingLeft(0);
        hBoxDDD.setPaddingRight(0);
        hBoxDDD.setPaddingBottom(0);
        hBoxDDD.setMarginTop(0);
        hBoxDDD.setMarginLeft(0);
        hBoxDDD.setMarginRight(0);
        hBoxDDD.setMarginBottom(0);
        hBoxDDD.setSpacing(5);
        hBoxDDD.setFlexVflex("ftFalse");
        hBoxDDD.setFlexHflex("ftTrue");
        hBoxDDD.setScrollable(false);
        hBoxDDD.setBoxShadowConfigHorizontalLength(10);
        hBoxDDD.setBoxShadowConfigVerticalLength(10);
        hBoxDDD.setBoxShadowConfigBlurRadius(5);
        hBoxDDD.setBoxShadowConfigSpreadRadius(0);
        hBoxDDD.setBoxShadowConfigShadowColor("clBlack");
        hBoxDDD.setBoxShadowConfigOpacity(75);
        hBoxDDD.setVAlign("tvTop");
        FVBox3.addChildren(hBoxDDD);
        hBoxDDD.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(0);
        FHBox14.setWidth(120);
        FHBox14.setHeight(30);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(10);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(7);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvMiddle");
        hBoxDDD.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(74);
        FVBox6.setHeight(28);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox14.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(74);
        FLabel5.setTop(0);
        FLabel5.setWidth(33);
        FLabel5.setHeight(13);
        FLabel5.setAlign("alRight");
        FLabel5.setCaption("Celular");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FHBox14.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFInteger edCelular = new TFInteger();

    private void init_edCelular() {
        edCelular.setName("edCelular");
        edCelular.setLeft(120);
        edCelular.setTop(0);
        edCelular.setWidth(130);
        edCelular.setHeight(24);
        edCelular.setTable(tbCadastroWhatsapp);
        edCelular.setFieldName("CELULAR");
        edCelular.setFlex(false);
        edCelular.setRequired(false);
        edCelular.setConstraintCheckWhen("cwImmediate");
        edCelular.setConstraintCheckType("ctExpression");
        edCelular.setConstraintFocusOnError(false);
        edCelular.setConstraintEnableUI(true);
        edCelular.setConstraintEnabled(false);
        edCelular.setConstraintFormCheck(true);
        edCelular.setMaxlength(0);
        edCelular.setFontColor("clWindowText");
        edCelular.setFontSize(-13);
        edCelular.setFontName("Tahoma");
        edCelular.setFontStyle("[]");
        edCelular.setAlignment("taRightJustify");
        hBoxDDD.addChildren(edCelular);
        edCelular.applyProperties();
        addValidatable(edCelular);
    }

    public TFPanel FPanel13 = new TFPanel();

    private void init_FPanel13() {
        FPanel13.setName("FPanel13");
        FPanel13.setLeft(250);
        FPanel13.setTop(0);
        FPanel13.setWidth(275);
        FPanel13.setHeight(30);
        FPanel13.setBorderStyle("stNone");
        FPanel13.setPaddingTop(0);
        FPanel13.setPaddingLeft(0);
        FPanel13.setPaddingRight(0);
        FPanel13.setPaddingBottom(0);
        FPanel13.setFlexVflex("ftFalse");
        FPanel13.setFlexHflex("ftFalse");
        FPanel13.setMarginTop(0);
        FPanel13.setMarginLeft(0);
        FPanel13.setMarginRight(0);
        FPanel13.setMarginBottom(0);
        FPanel13.setBoxShadowConfigHorizontalLength(10);
        FPanel13.setBoxShadowConfigVerticalLength(10);
        FPanel13.setBoxShadowConfigBlurRadius(5);
        FPanel13.setBoxShadowConfigSpreadRadius(0);
        FPanel13.setBoxShadowConfigShadowColor("clBlack");
        FPanel13.setBoxShadowConfigOpacity(75);
        hBoxDDD.addChildren(FPanel13);
        FPanel13.applyProperties();
    }

    public TFLabel FLabel13 = new TFLabel();

    private void init_FLabel13() {
        FLabel13.setName("FLabel13");
        FLabel13.setLeft(5);
        FLabel13.setTop(6);
        FLabel13.setWidth(262);
        FLabel13.setHeight(13);
        FLabel13.setCaption("DDI (55 Opcional) + DDD com 2 d\u00EDgitos e sem m\u00E1scara ");
        FLabel13.setFontColor("clBlack");
        FLabel13.setFontSize(-11);
        FLabel13.setFontName("Tahoma");
        FLabel13.setFontStyle("[]");
        FLabel13.setVerticalAlignment("taVerticalCenter");
        FLabel13.setWordBreak(false);
        FPanel13.addChildren(FLabel13);
        FLabel13.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(525);
        FHBox19.setTop(0);
        FHBox19.setWidth(120);
        FHBox19.setHeight(30);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(6);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(7);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftFalse");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvMiddle");
        hBoxDDD.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFCheckBox chkAtivo = new TFCheckBox();

    private void init_chkAtivo() {
        chkAtivo.setName("chkAtivo");
        chkAtivo.setLeft(0);
        chkAtivo.setTop(0);
        chkAtivo.setWidth(97);
        chkAtivo.setHeight(17);
        chkAtivo.setCaption("Ativo");
        chkAtivo.setFontColor("clWindowText");
        chkAtivo.setFontSize(-11);
        chkAtivo.setFontName("Tahoma");
        chkAtivo.setFontStyle("[]");
        chkAtivo.setTable(tbCadastroWhatsapp);
        chkAtivo.setFieldName("STATUS");
        chkAtivo.setCheckedValue("S");
        chkAtivo.setUncheckedValue("N");
        chkAtivo.setVerticalAlignment("taAlignTop");
        FHBox19.addChildren(chkAtivo);
        chkAtivo.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(156);
        FHBox9.setWidth(720);
        FHBox9.setHeight(38);
        FHBox9.setAlign("alClient");
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(5);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftTrue");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        FVBox3.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(0);
        FHBox15.setWidth(120);
        FHBox15.setHeight(30);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(10);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(7);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvMiddle");
        FHBox9.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(0);
        FVBox8.setWidth(60);
        FVBox8.setHeight(28);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftTrue");
        FVBox8.setFlexHflex("ftTrue");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FHBox15.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel FLabel12 = new TFLabel();

    private void init_FLabel12() {
        FLabel12.setName("FLabel12");
        FLabel12.setLeft(60);
        FLabel12.setTop(0);
        FLabel12.setWidth(46);
        FLabel12.setHeight(13);
        FLabel12.setAlign("alRight");
        FLabel12.setCaption("Descri\u00E7\u00E3o");
        FLabel12.setFontColor("clWindowText");
        FLabel12.setFontSize(-11);
        FLabel12.setFontName("Tahoma");
        FLabel12.setFontStyle("[]");
        FLabel12.setVerticalAlignment("taVerticalCenter");
        FLabel12.setWordBreak(false);
        FHBox15.addChildren(FLabel12);
        FLabel12.applyProperties();
    }

    public TFString edDescricao = new TFString();

    private void init_edDescricao() {
        edDescricao.setName("edDescricao");
        edDescricao.setLeft(120);
        edDescricao.setTop(0);
        edDescricao.setWidth(318);
        edDescricao.setHeight(24);
        edDescricao.setTable(tbCadastroWhatsapp);
        edDescricao.setFieldName("DESCRICAO");
        edDescricao.setFlex(true);
        edDescricao.setRequired(true);
        edDescricao.setConstraintCheckWhen("cwImmediate");
        edDescricao.setConstraintCheckType("ctExpression");
        edDescricao.setConstraintFocusOnError(false);
        edDescricao.setConstraintEnableUI(true);
        edDescricao.setConstraintEnabled(false);
        edDescricao.setConstraintFormCheck(true);
        edDescricao.setCharCase("ccNormal");
        edDescricao.setPwd(false);
        edDescricao.setMaxlength(0);
        edDescricao.setFontColor("clWindowText");
        edDescricao.setFontSize(-13);
        edDescricao.setFontName("Tahoma");
        edDescricao.setFontStyle("[]");
        edDescricao.setSaveLiteralCharacter(false);
        edDescricao.applyProperties();
        FHBox9.addChildren(edDescricao);
        addValidatable(edDescricao);
    }

    public TFPanel FPanel15 = new TFPanel();

    private void init_FPanel15() {
        FPanel15.setName("FPanel15");
        FPanel15.setLeft(438);
        FPanel15.setTop(0);
        FPanel15.setWidth(205);
        FPanel15.setHeight(30);
        FPanel15.setBorderStyle("stNone");
        FPanel15.setPaddingTop(0);
        FPanel15.setPaddingLeft(0);
        FPanel15.setPaddingRight(0);
        FPanel15.setPaddingBottom(0);
        FPanel15.setVisible(false);
        FPanel15.setFlexVflex("ftFalse");
        FPanel15.setFlexHflex("ftFalse");
        FPanel15.setMarginTop(0);
        FPanel15.setMarginLeft(0);
        FPanel15.setMarginRight(0);
        FPanel15.setMarginBottom(0);
        FPanel15.setBoxShadowConfigHorizontalLength(10);
        FPanel15.setBoxShadowConfigVerticalLength(10);
        FPanel15.setBoxShadowConfigBlurRadius(5);
        FPanel15.setBoxShadowConfigSpreadRadius(0);
        FPanel15.setBoxShadowConfigShadowColor("clBlack");
        FPanel15.setBoxShadowConfigOpacity(75);
        FHBox9.addChildren(FPanel15);
        FPanel15.applyProperties();
    }

    public TFLabel FLabel14 = new TFLabel();

    private void init_FLabel14() {
        FLabel14.setName("FLabel14");
        FLabel14.setLeft(1);
        FLabel14.setTop(6);
        FLabel14.setWidth(179);
        FLabel14.setHeight(13);
        FLabel14.setCaption("Esse nome vai aparecer no whatsapp");
        FLabel14.setColor("clBtnFace");
        FLabel14.setFontColor("clRed");
        FLabel14.setFontSize(-11);
        FLabel14.setFontName("Tahoma");
        FLabel14.setFontStyle("[]");
        FLabel14.setVerticalAlignment("taVerticalCenter");
        FLabel14.setWordBreak(false);
        FPanel15.addChildren(FLabel14);
        FLabel14.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(195);
        FHBox7.setWidth(722);
        FHBox7.setHeight(38);
        FHBox7.setAlign("alClient");
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(5);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FVBox3.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(0);
        FHBox16.setWidth(120);
        FHBox16.setHeight(30);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(10);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(7);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftFalse");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvMiddle");
        FHBox7.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(0);
        FVBox9.setTop(0);
        FVBox9.setWidth(63);
        FVBox9.setHeight(28);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftTrue");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        FHBox16.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(63);
        FLabel6.setTop(0);
        FLabel6.setWidth(41);
        FLabel6.setHeight(13);
        FLabel6.setAlign("alRight");
        FLabel6.setCaption("Empresa");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        FHBox16.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFCombo edCodEmpresa = new TFCombo();

    private void init_edCodEmpresa() {
        edCodEmpresa.setName("edCodEmpresa");
        edCodEmpresa.setLeft(120);
        edCodEmpresa.setTop(0);
        edCodEmpresa.setWidth(321);
        edCodEmpresa.setHeight(21);
        edCodEmpresa.setTable(tbCadastroWhatsapp);
        edCodEmpresa.setLookupTable(tbEmpresas);
        edCodEmpresa.setFieldName("COD_EMPRESA");
        edCodEmpresa.setLookupKey("COD_EMPRESA");
        edCodEmpresa.setLookupDesc("NOME_EMPRESA");
        edCodEmpresa.setFlex(true);
        edCodEmpresa.setReadOnly(true);
        edCodEmpresa.setRequired(true);
        edCodEmpresa.setPrompt("Selecione");
        edCodEmpresa.setConstraintCheckWhen("cwImmediate");
        edCodEmpresa.setConstraintCheckType("ctExpression");
        edCodEmpresa.setConstraintFocusOnError(false);
        edCodEmpresa.setConstraintEnableUI(true);
        edCodEmpresa.setConstraintEnabled(false);
        edCodEmpresa.setConstraintFormCheck(true);
        edCodEmpresa.setClearOnDelKey(true);
        edCodEmpresa.setUseClearButton(false);
        edCodEmpresa.setHideClearButtonOnNullValue(false);
        FHBox7.addChildren(edCodEmpresa);
        edCodEmpresa.applyProperties();
        addValidatable(edCodEmpresa);
    }

    public TFPanel FPanel11 = new TFPanel();

    private void init_FPanel11() {
        FPanel11.setName("FPanel11");
        FPanel11.setLeft(441);
        FPanel11.setTop(0);
        FPanel11.setWidth(205);
        FPanel11.setHeight(30);
        FPanel11.setBorderStyle("stNone");
        FPanel11.setPaddingTop(0);
        FPanel11.setPaddingLeft(5);
        FPanel11.setPaddingRight(0);
        FPanel11.setPaddingBottom(0);
        FPanel11.setFlexVflex("ftFalse");
        FPanel11.setFlexHflex("ftFalse");
        FPanel11.setMarginTop(0);
        FPanel11.setMarginLeft(0);
        FPanel11.setMarginRight(0);
        FPanel11.setMarginBottom(0);
        FPanel11.setBoxShadowConfigHorizontalLength(10);
        FPanel11.setBoxShadowConfigVerticalLength(10);
        FPanel11.setBoxShadowConfigBlurRadius(5);
        FPanel11.setBoxShadowConfigSpreadRadius(0);
        FPanel11.setBoxShadowConfigShadowColor("clBlack");
        FPanel11.setBoxShadowConfigOpacity(75);
        FHBox7.addChildren(FPanel11);
        FPanel11.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(1);
        FLabel4.setTop(5);
        FLabel4.setWidth(192);
        FLabel4.setHeight(13);
        FLabel4.setCaption("Coloque aqui a empresa que foi liberada");
        FLabel4.setColor("clBtnFace");
        FLabel4.setFontColor("clRed");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FPanel11.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(0);
        FHBox31.setTop(234);
        FHBox31.setWidth(728);
        FHBox31.setHeight(38);
        FHBox31.setAlign("alClient");
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(0);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(5);
        FHBox31.setFlexVflex("ftFalse");
        FHBox31.setFlexHflex("ftTrue");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        FVBox3.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFHBox FHBox32 = new TFHBox();

    private void init_FHBox32() {
        FHBox32.setName("FHBox32");
        FHBox32.setLeft(0);
        FHBox32.setTop(0);
        FHBox32.setWidth(120);
        FHBox32.setHeight(30);
        FHBox32.setBorderStyle("stNone");
        FHBox32.setPaddingTop(10);
        FHBox32.setPaddingLeft(0);
        FHBox32.setPaddingRight(7);
        FHBox32.setPaddingBottom(0);
        FHBox32.setMarginTop(0);
        FHBox32.setMarginLeft(0);
        FHBox32.setMarginRight(0);
        FHBox32.setMarginBottom(0);
        FHBox32.setSpacing(1);
        FHBox32.setFlexVflex("ftFalse");
        FHBox32.setFlexHflex("ftFalse");
        FHBox32.setScrollable(false);
        FHBox32.setBoxShadowConfigHorizontalLength(10);
        FHBox32.setBoxShadowConfigVerticalLength(10);
        FHBox32.setBoxShadowConfigBlurRadius(5);
        FHBox32.setBoxShadowConfigSpreadRadius(0);
        FHBox32.setBoxShadowConfigShadowColor("clBlack");
        FHBox32.setBoxShadowConfigOpacity(75);
        FHBox32.setVAlign("tvMiddle");
        FHBox31.addChildren(FHBox32);
        FHBox32.applyProperties();
    }

    public TFVBox FVBox14 = new TFVBox();

    private void init_FVBox14() {
        FVBox14.setName("FVBox14");
        FVBox14.setLeft(0);
        FVBox14.setTop(0);
        FVBox14.setWidth(75);
        FVBox14.setHeight(28);
        FVBox14.setBorderStyle("stNone");
        FVBox14.setPaddingTop(0);
        FVBox14.setPaddingLeft(0);
        FVBox14.setPaddingRight(0);
        FVBox14.setPaddingBottom(0);
        FVBox14.setMarginTop(0);
        FVBox14.setMarginLeft(0);
        FVBox14.setMarginRight(0);
        FVBox14.setMarginBottom(0);
        FVBox14.setSpacing(1);
        FVBox14.setFlexVflex("ftTrue");
        FVBox14.setFlexHflex("ftTrue");
        FVBox14.setScrollable(false);
        FVBox14.setBoxShadowConfigHorizontalLength(10);
        FVBox14.setBoxShadowConfigVerticalLength(10);
        FVBox14.setBoxShadowConfigBlurRadius(5);
        FVBox14.setBoxShadowConfigSpreadRadius(0);
        FVBox14.setBoxShadowConfigShadowColor("clBlack");
        FVBox14.setBoxShadowConfigOpacity(75);
        FHBox32.addChildren(FVBox14);
        FVBox14.applyProperties();
    }

    public TFLabel FLabel18 = new TFLabel();

    private void init_FLabel18() {
        FLabel18.setName("FLabel18");
        FLabel18.setLeft(75);
        FLabel18.setTop(0);
        FLabel18.setWidth(36);
        FLabel18.setHeight(13);
        FLabel18.setAlign("alRight");
        FLabel18.setCaption("Usu\u00E1rio");
        FLabel18.setFontColor("clWindowText");
        FLabel18.setFontSize(-11);
        FLabel18.setFontName("Tahoma");
        FLabel18.setFontStyle("[]");
        FLabel18.setVerticalAlignment("taVerticalCenter");
        FLabel18.setWordBreak(false);
        FHBox32.addChildren(FLabel18);
        FLabel18.applyProperties();
    }

    public TFString edtUsuario = new TFString();

    private void init_edtUsuario() {
        edtUsuario.setName("edtUsuario");
        edtUsuario.setLeft(120);
        edtUsuario.setTop(0);
        edtUsuario.setWidth(121);
        edtUsuario.setHeight(24);
        edtUsuario.setTable(tbCadastroWhatsapp);
        edtUsuario.setFieldName("API_USUARIO");
        edtUsuario.setFlex(false);
        edtUsuario.setRequired(true);
        edtUsuario.setConstraintCheckWhen("cwImmediate");
        edtUsuario.setConstraintCheckType("ctExpression");
        edtUsuario.setConstraintFocusOnError(false);
        edtUsuario.setConstraintEnableUI(true);
        edtUsuario.setConstraintEnabled(false);
        edtUsuario.setConstraintFormCheck(true);
        edtUsuario.setCharCase("ccNormal");
        edtUsuario.setPwd(false);
        edtUsuario.setMaxlength(0);
        edtUsuario.setFontColor("clWindowText");
        edtUsuario.setFontSize(-13);
        edtUsuario.setFontName("Tahoma");
        edtUsuario.setFontStyle("[]");
        edtUsuario.setSaveLiteralCharacter(false);
        edtUsuario.applyProperties();
        FHBox31.addChildren(edtUsuario);
        addValidatable(edtUsuario);
    }

    public TFHBox FHBox33 = new TFHBox();

    private void init_FHBox33() {
        FHBox33.setName("FHBox33");
        FHBox33.setLeft(241);
        FHBox33.setTop(0);
        FHBox33.setWidth(52);
        FHBox33.setHeight(30);
        FHBox33.setBorderStyle("stNone");
        FHBox33.setPaddingTop(10);
        FHBox33.setPaddingLeft(0);
        FHBox33.setPaddingRight(7);
        FHBox33.setPaddingBottom(0);
        FHBox33.setMarginTop(0);
        FHBox33.setMarginLeft(0);
        FHBox33.setMarginRight(0);
        FHBox33.setMarginBottom(0);
        FHBox33.setSpacing(1);
        FHBox33.setFlexVflex("ftFalse");
        FHBox33.setFlexHflex("ftFalse");
        FHBox33.setScrollable(false);
        FHBox33.setBoxShadowConfigHorizontalLength(10);
        FHBox33.setBoxShadowConfigVerticalLength(10);
        FHBox33.setBoxShadowConfigBlurRadius(5);
        FHBox33.setBoxShadowConfigSpreadRadius(0);
        FHBox33.setBoxShadowConfigShadowColor("clBlack");
        FHBox33.setBoxShadowConfigOpacity(75);
        FHBox33.setVAlign("tvMiddle");
        FHBox31.addChildren(FHBox33);
        FHBox33.applyProperties();
    }

    public TFVBox FVBox15 = new TFVBox();

    private void init_FVBox15() {
        FVBox15.setName("FVBox15");
        FVBox15.setLeft(0);
        FVBox15.setTop(0);
        FVBox15.setWidth(11);
        FVBox15.setHeight(28);
        FVBox15.setBorderStyle("stNone");
        FVBox15.setPaddingTop(0);
        FVBox15.setPaddingLeft(0);
        FVBox15.setPaddingRight(0);
        FVBox15.setPaddingBottom(0);
        FVBox15.setMarginTop(0);
        FVBox15.setMarginLeft(0);
        FVBox15.setMarginRight(0);
        FVBox15.setMarginBottom(0);
        FVBox15.setSpacing(1);
        FVBox15.setFlexVflex("ftTrue");
        FVBox15.setFlexHflex("ftTrue");
        FVBox15.setScrollable(false);
        FVBox15.setBoxShadowConfigHorizontalLength(10);
        FVBox15.setBoxShadowConfigVerticalLength(10);
        FVBox15.setBoxShadowConfigBlurRadius(5);
        FVBox15.setBoxShadowConfigSpreadRadius(0);
        FVBox15.setBoxShadowConfigShadowColor("clBlack");
        FVBox15.setBoxShadowConfigOpacity(75);
        FHBox33.addChildren(FVBox15);
        FVBox15.applyProperties();
    }

    public TFLabel FLabel19 = new TFLabel();

    private void init_FLabel19() {
        FLabel19.setName("FLabel19");
        FLabel19.setLeft(11);
        FLabel19.setTop(0);
        FLabel19.setWidth(30);
        FLabel19.setHeight(13);
        FLabel19.setAlign("alRight");
        FLabel19.setCaption("Senha");
        FLabel19.setFontColor("clWindowText");
        FLabel19.setFontSize(-11);
        FLabel19.setFontName("Tahoma");
        FLabel19.setFontStyle("[]");
        FLabel19.setVerticalAlignment("taVerticalCenter");
        FLabel19.setWordBreak(false);
        FHBox33.addChildren(FLabel19);
        FLabel19.applyProperties();
    }

    public TFString edtSenha = new TFString();

    private void init_edtSenha() {
        edtSenha.setName("edtSenha");
        edtSenha.setLeft(293);
        edtSenha.setTop(0);
        edtSenha.setWidth(121);
        edtSenha.setHeight(24);
        edtSenha.setTable(tbCadastroWhatsapp);
        edtSenha.setFieldName("API_SENHA");
        edtSenha.setFlex(false);
        edtSenha.setRequired(true);
        edtSenha.setConstraintCheckWhen("cwImmediate");
        edtSenha.setConstraintCheckType("ctExpression");
        edtSenha.setConstraintFocusOnError(false);
        edtSenha.setConstraintEnableUI(true);
        edtSenha.setConstraintEnabled(false);
        edtSenha.setConstraintFormCheck(true);
        edtSenha.setCharCase("ccNormal");
        edtSenha.setPwd(true);
        edtSenha.setMaxlength(0);
        edtSenha.setFontColor("clWindowText");
        edtSenha.setFontSize(-13);
        edtSenha.setFontName("Tahoma");
        edtSenha.setFontStyle("[]");
        edtSenha.setSaveLiteralCharacter(false);
        edtSenha.applyProperties();
        FHBox31.addChildren(edtSenha);
        addValidatable(edtSenha);
    }

    public TFButton btnTestar = new TFButton();

    private void init_btnTestar() {
        btnTestar.setName("btnTestar");
        btnTestar.setLeft(414);
        btnTestar.setTop(0);
        btnTestar.setWidth(75);
        btnTestar.setHeight(33);
        btnTestar.setCaption("Testar");
        btnTestar.setFontColor("clWindowText");
        btnTestar.setFontSize(-11);
        btnTestar.setFontName("Tahoma");
        btnTestar.setFontStyle("[]");
        btnTestar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnTestarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnTestar", "OnClick");
        });
        btnTestar.setImageId(0);
        btnTestar.setColor("clBtnFace");
        btnTestar.setAccess(false);
        btnTestar.setIconReverseDirection(false);
        FHBox31.addChildren(btnTestar);
        btnTestar.applyProperties();
    }

    public TFPanel FPanel1 = new TFPanel();

    private void init_FPanel1() {
        FPanel1.setName("FPanel1");
        FPanel1.setLeft(489);
        FPanel1.setTop(0);
        FPanel1.setWidth(309);
        FPanel1.setHeight(30);
        FPanel1.setBorderStyle("stNone");
        FPanel1.setPaddingTop(0);
        FPanel1.setPaddingLeft(0);
        FPanel1.setPaddingRight(0);
        FPanel1.setPaddingBottom(0);
        FPanel1.setFlexVflex("ftFalse");
        FPanel1.setFlexHflex("ftTrue");
        FPanel1.setMarginTop(0);
        FPanel1.setMarginLeft(0);
        FPanel1.setMarginRight(0);
        FPanel1.setMarginBottom(0);
        FPanel1.setBoxShadowConfigHorizontalLength(10);
        FPanel1.setBoxShadowConfigVerticalLength(10);
        FPanel1.setBoxShadowConfigBlurRadius(5);
        FPanel1.setBoxShadowConfigSpreadRadius(0);
        FPanel1.setBoxShadowConfigShadowColor("clBlack");
        FPanel1.setBoxShadowConfigOpacity(75);
        FHBox31.addChildren(FPanel1);
        FPanel1.applyProperties();
    }

    public TFLabel FLabel20 = new TFLabel();

    private void init_FLabel20() {
        FLabel20.setName("FLabel20");
        FLabel20.setLeft(2);
        FLabel20.setTop(6);
        FLabel20.setWidth(301);
        FLabel20.setHeight(13);
        FLabel20.setCaption("Informe o usu\u00E1rio e senha para autenticar API(Usu\u00E1rio Oracle)");
        FLabel20.setColor("clBtnFace");
        FLabel20.setFontColor("clRed");
        FLabel20.setFontSize(-11);
        FLabel20.setFontName("Tahoma");
        FLabel20.setFontStyle("[]");
        FLabel20.setVerticalAlignment("taVerticalCenter");
        FLabel20.setWordBreak(false);
        FPanel1.addChildren(FLabel20);
        FLabel20.applyProperties();
    }

    public TFHBox FHBox34 = new TFHBox();

    private void init_FHBox34() {
        FHBox34.setName("FHBox34");
        FHBox34.setLeft(0);
        FHBox34.setTop(273);
        FHBox34.setWidth(728);
        FHBox34.setHeight(38);
        FHBox34.setAlign("alClient");
        FHBox34.setBorderStyle("stNone");
        FHBox34.setPaddingTop(0);
        FHBox34.setPaddingLeft(0);
        FHBox34.setPaddingRight(0);
        FHBox34.setPaddingBottom(0);
        FHBox34.setMarginTop(0);
        FHBox34.setMarginLeft(0);
        FHBox34.setMarginRight(0);
        FHBox34.setMarginBottom(0);
        FHBox34.setSpacing(5);
        FHBox34.setFlexVflex("ftFalse");
        FHBox34.setFlexHflex("ftTrue");
        FHBox34.setScrollable(false);
        FHBox34.setBoxShadowConfigHorizontalLength(10);
        FHBox34.setBoxShadowConfigVerticalLength(10);
        FHBox34.setBoxShadowConfigBlurRadius(5);
        FHBox34.setBoxShadowConfigSpreadRadius(0);
        FHBox34.setBoxShadowConfigShadowColor("clBlack");
        FHBox34.setBoxShadowConfigOpacity(75);
        FHBox34.setVAlign("tvTop");
        FVBox3.addChildren(FHBox34);
        FHBox34.applyProperties();
    }

    public TFHBox FHBox35 = new TFHBox();

    private void init_FHBox35() {
        FHBox35.setName("FHBox35");
        FHBox35.setLeft(0);
        FHBox35.setTop(0);
        FHBox35.setWidth(120);
        FHBox35.setHeight(30);
        FHBox35.setBorderStyle("stNone");
        FHBox35.setPaddingTop(10);
        FHBox35.setPaddingLeft(0);
        FHBox35.setPaddingRight(7);
        FHBox35.setPaddingBottom(0);
        FHBox35.setMarginTop(0);
        FHBox35.setMarginLeft(0);
        FHBox35.setMarginRight(0);
        FHBox35.setMarginBottom(0);
        FHBox35.setSpacing(1);
        FHBox35.setFlexVflex("ftFalse");
        FHBox35.setFlexHflex("ftFalse");
        FHBox35.setScrollable(false);
        FHBox35.setBoxShadowConfigHorizontalLength(10);
        FHBox35.setBoxShadowConfigVerticalLength(10);
        FHBox35.setBoxShadowConfigBlurRadius(5);
        FHBox35.setBoxShadowConfigSpreadRadius(0);
        FHBox35.setBoxShadowConfigShadowColor("clBlack");
        FHBox35.setBoxShadowConfigOpacity(75);
        FHBox35.setVAlign("tvMiddle");
        FHBox34.addChildren(FHBox35);
        FHBox35.applyProperties();
    }

    public TFVBox FVBox16 = new TFVBox();

    private void init_FVBox16() {
        FVBox16.setName("FVBox16");
        FVBox16.setLeft(0);
        FVBox16.setTop(0);
        FVBox16.setWidth(20);
        FVBox16.setHeight(28);
        FVBox16.setBorderStyle("stNone");
        FVBox16.setPaddingTop(0);
        FVBox16.setPaddingLeft(0);
        FVBox16.setPaddingRight(0);
        FVBox16.setPaddingBottom(0);
        FVBox16.setMarginTop(0);
        FVBox16.setMarginLeft(0);
        FVBox16.setMarginRight(0);
        FVBox16.setMarginBottom(0);
        FVBox16.setSpacing(1);
        FVBox16.setFlexVflex("ftTrue");
        FVBox16.setFlexHflex("ftTrue");
        FVBox16.setScrollable(false);
        FVBox16.setBoxShadowConfigHorizontalLength(10);
        FVBox16.setBoxShadowConfigVerticalLength(10);
        FVBox16.setBoxShadowConfigBlurRadius(5);
        FVBox16.setBoxShadowConfigSpreadRadius(0);
        FVBox16.setBoxShadowConfigShadowColor("clBlack");
        FVBox16.setBoxShadowConfigOpacity(75);
        FHBox35.addChildren(FVBox16);
        FVBox16.applyProperties();
    }

    public TFLabel FLabel21 = new TFLabel();

    private void init_FLabel21() {
        FLabel21.setName("FLabel21");
        FLabel21.setLeft(20);
        FLabel21.setTop(0);
        FLabel21.setWidth(84);
        FLabel21.setHeight(13);
        FLabel21.setAlign("alRight");
        FLabel21.setCaption("e-mail Notifica\u00E7\u00E3o");
        FLabel21.setFontColor("clWindowText");
        FLabel21.setFontSize(-11);
        FLabel21.setFontName("Tahoma");
        FLabel21.setFontStyle("[]");
        FLabel21.setVerticalAlignment("taVerticalCenter");
        FLabel21.setWordBreak(false);
        FHBox35.addChildren(FLabel21);
        FLabel21.applyProperties();
    }

    public TFString edtEmailNotificacaoApi = new TFString();

    private void init_edtEmailNotificacaoApi() {
        edtEmailNotificacaoApi.setName("edtEmailNotificacaoApi");
        edtEmailNotificacaoApi.setLeft(120);
        edtEmailNotificacaoApi.setTop(0);
        edtEmailNotificacaoApi.setWidth(318);
        edtEmailNotificacaoApi.setHeight(24);
        edtEmailNotificacaoApi.setTable(tbCadastroWhatsapp);
        edtEmailNotificacaoApi.setFieldName("API_EMAIL_NOTIFICACAO");
        edtEmailNotificacaoApi.setFlex(true);
        edtEmailNotificacaoApi.setRequired(false);
        edtEmailNotificacaoApi.setConstraintCheckWhen("cwImmediate");
        edtEmailNotificacaoApi.setConstraintCheckType("ctExpression");
        edtEmailNotificacaoApi.setConstraintFocusOnError(false);
        edtEmailNotificacaoApi.setConstraintEnableUI(true);
        edtEmailNotificacaoApi.setConstraintEnabled(false);
        edtEmailNotificacaoApi.setConstraintFormCheck(true);
        edtEmailNotificacaoApi.setCharCase("ccNormal");
        edtEmailNotificacaoApi.setPwd(false);
        edtEmailNotificacaoApi.setMaxlength(0);
        edtEmailNotificacaoApi.setFontColor("clWindowText");
        edtEmailNotificacaoApi.setFontSize(-13);
        edtEmailNotificacaoApi.setFontName("Tahoma");
        edtEmailNotificacaoApi.setFontStyle("[]");
        edtEmailNotificacaoApi.setSaveLiteralCharacter(false);
        edtEmailNotificacaoApi.applyProperties();
        FHBox34.addChildren(edtEmailNotificacaoApi);
        addValidatable(edtEmailNotificacaoApi);
    }

    public TFPanel FPanel2 = new TFPanel();

    private void init_FPanel2() {
        FPanel2.setName("FPanel2");
        FPanel2.setLeft(438);
        FPanel2.setTop(0);
        FPanel2.setWidth(337);
        FPanel2.setHeight(30);
        FPanel2.setBorderStyle("stNone");
        FPanel2.setPaddingTop(0);
        FPanel2.setPaddingLeft(0);
        FPanel2.setPaddingRight(0);
        FPanel2.setPaddingBottom(0);
        FPanel2.setFlexVflex("ftFalse");
        FPanel2.setFlexHflex("ftFalse");
        FPanel2.setMarginTop(0);
        FPanel2.setMarginLeft(0);
        FPanel2.setMarginRight(0);
        FPanel2.setMarginBottom(0);
        FPanel2.setBoxShadowConfigHorizontalLength(10);
        FPanel2.setBoxShadowConfigVerticalLength(10);
        FPanel2.setBoxShadowConfigBlurRadius(5);
        FPanel2.setBoxShadowConfigSpreadRadius(0);
        FPanel2.setBoxShadowConfigShadowColor("clBlack");
        FPanel2.setBoxShadowConfigOpacity(75);
        FHBox34.addChildren(FPanel2);
        FPanel2.applyProperties();
    }

    public TFLabel FLabel22 = new TFLabel();

    private void init_FLabel22() {
        FLabel22.setName("FLabel22");
        FLabel22.setLeft(2);
        FLabel22.setTop(6);
        FLabel22.setWidth(280);
        FLabel22.setHeight(13);
        FLabel22.setCaption("e-mail para receber notifica\u00E7\u00E3o sobre o status do template");
        FLabel22.setFontColor("clRed");
        FLabel22.setFontSize(-11);
        FLabel22.setFontName("Tahoma");
        FLabel22.setFontStyle("[]");
        FLabel22.setVerticalAlignment("taVerticalCenter");
        FLabel22.setWordBreak(false);
        FPanel2.addChildren(FLabel22);
        FLabel22.applyProperties();
    }

    public TFHBox FHBox38 = new TFHBox();

    private void init_FHBox38() {
        FHBox38.setName("FHBox38");
        FHBox38.setLeft(0);
        FHBox38.setTop(312);
        FHBox38.setWidth(728);
        FHBox38.setHeight(38);
        FHBox38.setAlign("alClient");
        FHBox38.setBorderStyle("stNone");
        FHBox38.setPaddingTop(0);
        FHBox38.setPaddingLeft(0);
        FHBox38.setPaddingRight(0);
        FHBox38.setPaddingBottom(0);
        FHBox38.setMarginTop(0);
        FHBox38.setMarginLeft(0);
        FHBox38.setMarginRight(0);
        FHBox38.setMarginBottom(0);
        FHBox38.setSpacing(5);
        FHBox38.setFlexVflex("ftFalse");
        FHBox38.setFlexHflex("ftTrue");
        FHBox38.setScrollable(false);
        FHBox38.setBoxShadowConfigHorizontalLength(10);
        FHBox38.setBoxShadowConfigVerticalLength(10);
        FHBox38.setBoxShadowConfigBlurRadius(5);
        FHBox38.setBoxShadowConfigSpreadRadius(0);
        FHBox38.setBoxShadowConfigShadowColor("clBlack");
        FHBox38.setBoxShadowConfigOpacity(75);
        FHBox38.setVAlign("tvTop");
        FVBox3.addChildren(FHBox38);
        FHBox38.applyProperties();
    }

    public TFHBox FHBox39 = new TFHBox();

    private void init_FHBox39() {
        FHBox39.setName("FHBox39");
        FHBox39.setLeft(0);
        FHBox39.setTop(0);
        FHBox39.setWidth(120);
        FHBox39.setHeight(30);
        FHBox39.setBorderStyle("stNone");
        FHBox39.setPaddingTop(10);
        FHBox39.setPaddingLeft(0);
        FHBox39.setPaddingRight(7);
        FHBox39.setPaddingBottom(0);
        FHBox39.setMarginTop(0);
        FHBox39.setMarginLeft(0);
        FHBox39.setMarginRight(0);
        FHBox39.setMarginBottom(0);
        FHBox39.setSpacing(1);
        FHBox39.setFlexVflex("ftFalse");
        FHBox39.setFlexHflex("ftFalse");
        FHBox39.setScrollable(false);
        FHBox39.setBoxShadowConfigHorizontalLength(10);
        FHBox39.setBoxShadowConfigVerticalLength(10);
        FHBox39.setBoxShadowConfigBlurRadius(5);
        FHBox39.setBoxShadowConfigSpreadRadius(0);
        FHBox39.setBoxShadowConfigShadowColor("clBlack");
        FHBox39.setBoxShadowConfigOpacity(75);
        FHBox39.setVAlign("tvMiddle");
        FHBox38.addChildren(FHBox39);
        FHBox39.applyProperties();
    }

    public TFVBox FVBox24 = new TFVBox();

    private void init_FVBox24() {
        FVBox24.setName("FVBox24");
        FVBox24.setLeft(0);
        FVBox24.setTop(0);
        FVBox24.setWidth(20);
        FVBox24.setHeight(28);
        FVBox24.setBorderStyle("stNone");
        FVBox24.setPaddingTop(0);
        FVBox24.setPaddingLeft(0);
        FVBox24.setPaddingRight(0);
        FVBox24.setPaddingBottom(0);
        FVBox24.setMarginTop(0);
        FVBox24.setMarginLeft(0);
        FVBox24.setMarginRight(0);
        FVBox24.setMarginBottom(0);
        FVBox24.setSpacing(1);
        FVBox24.setFlexVflex("ftTrue");
        FVBox24.setFlexHflex("ftTrue");
        FVBox24.setScrollable(false);
        FVBox24.setBoxShadowConfigHorizontalLength(10);
        FVBox24.setBoxShadowConfigVerticalLength(10);
        FVBox24.setBoxShadowConfigBlurRadius(5);
        FVBox24.setBoxShadowConfigSpreadRadius(0);
        FVBox24.setBoxShadowConfigShadowColor("clBlack");
        FVBox24.setBoxShadowConfigOpacity(75);
        FHBox39.addChildren(FVBox24);
        FVBox24.applyProperties();
    }

    public TFLabel FLabel25 = new TFLabel();

    private void init_FLabel25() {
        FLabel25.setName("FLabel25");
        FLabel25.setLeft(20);
        FLabel25.setTop(0);
        FLabel25.setWidth(91);
        FLabel25.setHeight(13);
        FLabel25.setAlign("alRight");
        FLabel25.setCaption("Leadzap Receptivo");
        FLabel25.setFontColor("clWindowText");
        FLabel25.setFontSize(-11);
        FLabel25.setFontName("Tahoma");
        FLabel25.setFontStyle("[]");
        FLabel25.setVerticalAlignment("taVerticalCenter");
        FLabel25.setWordBreak(false);
        FHBox39.addChildren(FLabel25);
        FLabel25.applyProperties();
    }

    public TFCombo cbbLeadzapReceptivo = new TFCombo();

    private void init_cbbLeadzapReceptivo() {
        cbbLeadzapReceptivo.setName("cbbLeadzapReceptivo");
        cbbLeadzapReceptivo.setLeft(120);
        cbbLeadzapReceptivo.setTop(0);
        cbbLeadzapReceptivo.setWidth(145);
        cbbLeadzapReceptivo.setHeight(21);
        cbbLeadzapReceptivo.setTable(tbCadastroWhatsapp);
        cbbLeadzapReceptivo.setLookupTable(tbLeadzapMenu);
        cbbLeadzapReceptivo.setFieldName("ID_MENU");
        cbbLeadzapReceptivo.setLookupKey("ID_MENU");
        cbbLeadzapReceptivo.setLookupDesc("MENU_DESCRICAO");
        cbbLeadzapReceptivo.setFlex(true);
        cbbLeadzapReceptivo.setReadOnly(true);
        cbbLeadzapReceptivo.setRequired(false);
        cbbLeadzapReceptivo.setPrompt("Selecione");
        cbbLeadzapReceptivo.setConstraintCheckWhen("cwImmediate");
        cbbLeadzapReceptivo.setConstraintCheckType("ctExpression");
        cbbLeadzapReceptivo.setConstraintFocusOnError(false);
        cbbLeadzapReceptivo.setConstraintEnableUI(true);
        cbbLeadzapReceptivo.setConstraintEnabled(false);
        cbbLeadzapReceptivo.setConstraintFormCheck(true);
        cbbLeadzapReceptivo.setClearOnDelKey(true);
        cbbLeadzapReceptivo.setUseClearButton(true);
        cbbLeadzapReceptivo.setHideClearButtonOnNullValue(false);
        cbbLeadzapReceptivo.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbLeadzapReceptivoClearClick(event);
            processarFlow("FrmCadastroLeadzap", "cbbLeadzapReceptivo", "OnClearClick");
        });
        FHBox38.addChildren(cbbLeadzapReceptivo);
        cbbLeadzapReceptivo.applyProperties();
        addValidatable(cbbLeadzapReceptivo);
    }

    public TFHBox hboxTipoApi = new TFHBox();

    private void init_hboxTipoApi() {
        hboxTipoApi.setName("hboxTipoApi");
        hboxTipoApi.setLeft(0);
        hboxTipoApi.setTop(351);
        hboxTipoApi.setWidth(719);
        hboxTipoApi.setHeight(38);
        hboxTipoApi.setAlign("alClient");
        hboxTipoApi.setBorderStyle("stNone");
        hboxTipoApi.setPaddingTop(0);
        hboxTipoApi.setPaddingLeft(0);
        hboxTipoApi.setPaddingRight(0);
        hboxTipoApi.setPaddingBottom(0);
        hboxTipoApi.setMarginTop(0);
        hboxTipoApi.setMarginLeft(0);
        hboxTipoApi.setMarginRight(0);
        hboxTipoApi.setMarginBottom(0);
        hboxTipoApi.setSpacing(5);
        hboxTipoApi.setFlexVflex("ftFalse");
        hboxTipoApi.setFlexHflex("ftTrue");
        hboxTipoApi.setScrollable(false);
        hboxTipoApi.setBoxShadowConfigHorizontalLength(10);
        hboxTipoApi.setBoxShadowConfigVerticalLength(10);
        hboxTipoApi.setBoxShadowConfigBlurRadius(5);
        hboxTipoApi.setBoxShadowConfigSpreadRadius(0);
        hboxTipoApi.setBoxShadowConfigShadowColor("clBlack");
        hboxTipoApi.setBoxShadowConfigOpacity(75);
        hboxTipoApi.setVAlign("tvTop");
        FVBox3.addChildren(hboxTipoApi);
        hboxTipoApi.applyProperties();
    }

    public TFHBox hboxlblTipoApi = new TFHBox();

    private void init_hboxlblTipoApi() {
        hboxlblTipoApi.setName("hboxlblTipoApi");
        hboxlblTipoApi.setLeft(0);
        hboxlblTipoApi.setTop(0);
        hboxlblTipoApi.setWidth(120);
        hboxlblTipoApi.setHeight(30);
        hboxlblTipoApi.setBorderStyle("stNone");
        hboxlblTipoApi.setPaddingTop(10);
        hboxlblTipoApi.setPaddingLeft(0);
        hboxlblTipoApi.setPaddingRight(7);
        hboxlblTipoApi.setPaddingBottom(0);
        hboxlblTipoApi.setMarginTop(0);
        hboxlblTipoApi.setMarginLeft(0);
        hboxlblTipoApi.setMarginRight(0);
        hboxlblTipoApi.setMarginBottom(0);
        hboxlblTipoApi.setSpacing(1);
        hboxlblTipoApi.setFlexVflex("ftFalse");
        hboxlblTipoApi.setFlexHflex("ftFalse");
        hboxlblTipoApi.setScrollable(false);
        hboxlblTipoApi.setBoxShadowConfigHorizontalLength(10);
        hboxlblTipoApi.setBoxShadowConfigVerticalLength(10);
        hboxlblTipoApi.setBoxShadowConfigBlurRadius(5);
        hboxlblTipoApi.setBoxShadowConfigSpreadRadius(0);
        hboxlblTipoApi.setBoxShadowConfigShadowColor("clBlack");
        hboxlblTipoApi.setBoxShadowConfigOpacity(75);
        hboxlblTipoApi.setVAlign("tvMiddle");
        hboxTipoApi.addChildren(hboxlblTipoApi);
        hboxlblTipoApi.applyProperties();
    }

    public TFVBox seplblTipoApi = new TFVBox();

    private void init_seplblTipoApi() {
        seplblTipoApi.setName("seplblTipoApi");
        seplblTipoApi.setLeft(0);
        seplblTipoApi.setTop(0);
        seplblTipoApi.setWidth(20);
        seplblTipoApi.setHeight(28);
        seplblTipoApi.setBorderStyle("stNone");
        seplblTipoApi.setPaddingTop(0);
        seplblTipoApi.setPaddingLeft(0);
        seplblTipoApi.setPaddingRight(0);
        seplblTipoApi.setPaddingBottom(0);
        seplblTipoApi.setMarginTop(0);
        seplblTipoApi.setMarginLeft(0);
        seplblTipoApi.setMarginRight(0);
        seplblTipoApi.setMarginBottom(0);
        seplblTipoApi.setSpacing(1);
        seplblTipoApi.setFlexVflex("ftTrue");
        seplblTipoApi.setFlexHflex("ftTrue");
        seplblTipoApi.setScrollable(false);
        seplblTipoApi.setBoxShadowConfigHorizontalLength(10);
        seplblTipoApi.setBoxShadowConfigVerticalLength(10);
        seplblTipoApi.setBoxShadowConfigBlurRadius(5);
        seplblTipoApi.setBoxShadowConfigSpreadRadius(0);
        seplblTipoApi.setBoxShadowConfigShadowColor("clBlack");
        seplblTipoApi.setBoxShadowConfigOpacity(75);
        hboxlblTipoApi.addChildren(seplblTipoApi);
        seplblTipoApi.applyProperties();
    }

    public TFLabel lblTipoApi = new TFLabel();

    private void init_lblTipoApi() {
        lblTipoApi.setName("lblTipoApi");
        lblTipoApi.setLeft(20);
        lblTipoApi.setTop(0);
        lblTipoApi.setWidth(38);
        lblTipoApi.setHeight(13);
        lblTipoApi.setAlign("alRight");
        lblTipoApi.setCaption("Tipo Api");
        lblTipoApi.setFontColor("clWindowText");
        lblTipoApi.setFontSize(-11);
        lblTipoApi.setFontName("Tahoma");
        lblTipoApi.setFontStyle("[]");
        lblTipoApi.setVerticalAlignment("taVerticalCenter");
        lblTipoApi.setWordBreak(false);
        hboxlblTipoApi.addChildren(lblTipoApi);
        lblTipoApi.applyProperties();
    }

    public TFCombo cbbTipoApi = new TFCombo();

    private void init_cbbTipoApi() {
        cbbTipoApi.setName("cbbTipoApi");
        cbbTipoApi.setLeft(120);
        cbbTipoApi.setTop(0);
        cbbTipoApi.setWidth(145);
        cbbTipoApi.setHeight(21);
        cbbTipoApi.setTable(tbCadastroWhatsapp);
        cbbTipoApi.setFieldName("API_TIPO");
        cbbTipoApi.setFlex(true);
        cbbTipoApi.setListOptions("Z-API=Z-API;Zenvia=ZENVIA");
        cbbTipoApi.setReadOnly(true);
        cbbTipoApi.setRequired(false);
        cbbTipoApi.setPrompt("Selecione");
        cbbTipoApi.setConstraintCheckWhen("cwImmediate");
        cbbTipoApi.setConstraintCheckType("ctExpression");
        cbbTipoApi.setConstraintFocusOnError(false);
        cbbTipoApi.setConstraintEnableUI(true);
        cbbTipoApi.setConstraintEnabled(false);
        cbbTipoApi.setConstraintFormCheck(true);
        cbbTipoApi.setClearOnDelKey(false);
        cbbTipoApi.setUseClearButton(false);
        cbbTipoApi.setHideClearButtonOnNullValue(true);
        cbbTipoApi.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbTipoApiChange(event);
            processarFlow("FrmCadastroLeadzap", "cbbTipoApi", "OnChange");
        });
        hboxTipoApi.addChildren(cbbTipoApi);
        cbbTipoApi.applyProperties();
        addValidatable(cbbTipoApi);
    }

    public TFVBox hboxDadosZapi = new TFVBox();

    private void init_hboxDadosZapi() {
        hboxDadosZapi.setName("hboxDadosZapi");
        hboxDadosZapi.setLeft(0);
        hboxDadosZapi.setTop(390);
        hboxDadosZapi.setWidth(727);
        hboxDadosZapi.setHeight(166);
        hboxDadosZapi.setBorderStyle("stNone");
        hboxDadosZapi.setPaddingTop(0);
        hboxDadosZapi.setPaddingLeft(0);
        hboxDadosZapi.setPaddingRight(0);
        hboxDadosZapi.setPaddingBottom(0);
        hboxDadosZapi.setMarginTop(0);
        hboxDadosZapi.setMarginLeft(0);
        hboxDadosZapi.setMarginRight(0);
        hboxDadosZapi.setMarginBottom(0);
        hboxDadosZapi.setSpacing(1);
        hboxDadosZapi.setFlexVflex("ftMin");
        hboxDadosZapi.setFlexHflex("ftTrue");
        hboxDadosZapi.setScrollable(false);
        hboxDadosZapi.setBoxShadowConfigHorizontalLength(10);
        hboxDadosZapi.setBoxShadowConfigVerticalLength(10);
        hboxDadosZapi.setBoxShadowConfigBlurRadius(5);
        hboxDadosZapi.setBoxShadowConfigSpreadRadius(0);
        hboxDadosZapi.setBoxShadowConfigShadowColor("clBlack");
        hboxDadosZapi.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(hboxDadosZapi);
        hboxDadosZapi.applyProperties();
    }

    public TFHBox hboxInstancia = new TFHBox();

    private void init_hboxInstancia() {
        hboxInstancia.setName("hboxInstancia");
        hboxInstancia.setLeft(0);
        hboxInstancia.setTop(0);
        hboxInstancia.setWidth(690);
        hboxInstancia.setHeight(38);
        hboxInstancia.setBorderStyle("stNone");
        hboxInstancia.setPaddingTop(0);
        hboxInstancia.setPaddingLeft(0);
        hboxInstancia.setPaddingRight(0);
        hboxInstancia.setPaddingBottom(0);
        hboxInstancia.setMarginTop(0);
        hboxInstancia.setMarginLeft(0);
        hboxInstancia.setMarginRight(0);
        hboxInstancia.setMarginBottom(0);
        hboxInstancia.setSpacing(5);
        hboxInstancia.setFlexVflex("ftFalse");
        hboxInstancia.setFlexHflex("ftTrue");
        hboxInstancia.setScrollable(false);
        hboxInstancia.setBoxShadowConfigHorizontalLength(10);
        hboxInstancia.setBoxShadowConfigVerticalLength(10);
        hboxInstancia.setBoxShadowConfigBlurRadius(5);
        hboxInstancia.setBoxShadowConfigSpreadRadius(0);
        hboxInstancia.setBoxShadowConfigShadowColor("clBlack");
        hboxInstancia.setBoxShadowConfigOpacity(75);
        hboxInstancia.setVAlign("tvTop");
        hboxDadosZapi.addChildren(hboxInstancia);
        hboxInstancia.applyProperties();
    }

    public TFHBox hbxlblInstancia = new TFHBox();

    private void init_hbxlblInstancia() {
        hbxlblInstancia.setName("hbxlblInstancia");
        hbxlblInstancia.setLeft(0);
        hbxlblInstancia.setTop(0);
        hbxlblInstancia.setWidth(120);
        hbxlblInstancia.setHeight(30);
        hbxlblInstancia.setBorderStyle("stNone");
        hbxlblInstancia.setPaddingTop(10);
        hbxlblInstancia.setPaddingLeft(0);
        hbxlblInstancia.setPaddingRight(7);
        hbxlblInstancia.setPaddingBottom(0);
        hbxlblInstancia.setMarginTop(0);
        hbxlblInstancia.setMarginLeft(0);
        hbxlblInstancia.setMarginRight(0);
        hbxlblInstancia.setMarginBottom(0);
        hbxlblInstancia.setSpacing(1);
        hbxlblInstancia.setFlexVflex("ftFalse");
        hbxlblInstancia.setFlexHflex("ftFalse");
        hbxlblInstancia.setScrollable(false);
        hbxlblInstancia.setBoxShadowConfigHorizontalLength(10);
        hbxlblInstancia.setBoxShadowConfigVerticalLength(10);
        hbxlblInstancia.setBoxShadowConfigBlurRadius(5);
        hbxlblInstancia.setBoxShadowConfigSpreadRadius(0);
        hbxlblInstancia.setBoxShadowConfigShadowColor("clBlack");
        hbxlblInstancia.setBoxShadowConfigOpacity(75);
        hbxlblInstancia.setVAlign("tvMiddle");
        hboxInstancia.addChildren(hbxlblInstancia);
        hbxlblInstancia.applyProperties();
    }

    public TFVBox seplblInstancia = new TFVBox();

    private void init_seplblInstancia() {
        seplblInstancia.setName("seplblInstancia");
        seplblInstancia.setLeft(0);
        seplblInstancia.setTop(0);
        seplblInstancia.setWidth(39);
        seplblInstancia.setHeight(28);
        seplblInstancia.setBorderStyle("stNone");
        seplblInstancia.setPaddingTop(0);
        seplblInstancia.setPaddingLeft(0);
        seplblInstancia.setPaddingRight(0);
        seplblInstancia.setPaddingBottom(0);
        seplblInstancia.setMarginTop(0);
        seplblInstancia.setMarginLeft(0);
        seplblInstancia.setMarginRight(0);
        seplblInstancia.setMarginBottom(0);
        seplblInstancia.setSpacing(1);
        seplblInstancia.setFlexVflex("ftTrue");
        seplblInstancia.setFlexHflex("ftTrue");
        seplblInstancia.setScrollable(false);
        seplblInstancia.setBoxShadowConfigHorizontalLength(10);
        seplblInstancia.setBoxShadowConfigVerticalLength(10);
        seplblInstancia.setBoxShadowConfigBlurRadius(5);
        seplblInstancia.setBoxShadowConfigSpreadRadius(0);
        seplblInstancia.setBoxShadowConfigShadowColor("clBlack");
        seplblInstancia.setBoxShadowConfigOpacity(75);
        hbxlblInstancia.addChildren(seplblInstancia);
        seplblInstancia.applyProperties();
    }

    public TFLabel lblInstancia = new TFLabel();

    private void init_lblInstancia() {
        lblInstancia.setName("lblInstancia");
        lblInstancia.setLeft(39);
        lblInstancia.setTop(0);
        lblInstancia.setWidth(44);
        lblInstancia.setHeight(13);
        lblInstancia.setAlign("alRight");
        lblInstancia.setCaption("Inst\u00E2ncia");
        lblInstancia.setFontColor("clWindowText");
        lblInstancia.setFontSize(-11);
        lblInstancia.setFontName("Tahoma");
        lblInstancia.setFontStyle("[]");
        lblInstancia.setVerticalAlignment("taVerticalCenter");
        lblInstancia.setWordBreak(false);
        hbxlblInstancia.addChildren(lblInstancia);
        lblInstancia.applyProperties();
    }

    public TFString edtInstancia = new TFString();

    private void init_edtInstancia() {
        edtInstancia.setName("edtInstancia");
        edtInstancia.setLeft(120);
        edtInstancia.setTop(0);
        edtInstancia.setWidth(318);
        edtInstancia.setHeight(24);
        edtInstancia.setTable(tbCadastroWhatsapp);
        edtInstancia.setFieldName("ZAPI_INSTANCE");
        edtInstancia.setFlex(true);
        edtInstancia.setRequired(true);
        edtInstancia.setConstraintCheckWhen("cwImmediate");
        edtInstancia.setConstraintCheckType("ctExpression");
        edtInstancia.setConstraintFocusOnError(false);
        edtInstancia.setConstraintEnableUI(true);
        edtInstancia.setConstraintEnabled(false);
        edtInstancia.setConstraintFormCheck(true);
        edtInstancia.setCharCase("ccNormal");
        edtInstancia.setPwd(false);
        edtInstancia.setMaxlength(0);
        edtInstancia.setFontColor("clWindowText");
        edtInstancia.setFontSize(-13);
        edtInstancia.setFontName("Tahoma");
        edtInstancia.setFontStyle("[]");
        edtInstancia.setSaveLiteralCharacter(false);
        edtInstancia.applyProperties();
        hboxInstancia.addChildren(edtInstancia);
        addValidatable(edtInstancia);
    }

    public TFHBox hoxTokenInstancia = new TFHBox();

    private void init_hoxTokenInstancia() {
        hoxTokenInstancia.setName("hoxTokenInstancia");
        hoxTokenInstancia.setLeft(0);
        hoxTokenInstancia.setTop(39);
        hoxTokenInstancia.setWidth(690);
        hoxTokenInstancia.setHeight(38);
        hoxTokenInstancia.setBorderStyle("stNone");
        hoxTokenInstancia.setPaddingTop(0);
        hoxTokenInstancia.setPaddingLeft(0);
        hoxTokenInstancia.setPaddingRight(0);
        hoxTokenInstancia.setPaddingBottom(0);
        hoxTokenInstancia.setMarginTop(0);
        hoxTokenInstancia.setMarginLeft(0);
        hoxTokenInstancia.setMarginRight(0);
        hoxTokenInstancia.setMarginBottom(0);
        hoxTokenInstancia.setSpacing(5);
        hoxTokenInstancia.setFlexVflex("ftFalse");
        hoxTokenInstancia.setFlexHflex("ftTrue");
        hoxTokenInstancia.setScrollable(false);
        hoxTokenInstancia.setBoxShadowConfigHorizontalLength(10);
        hoxTokenInstancia.setBoxShadowConfigVerticalLength(10);
        hoxTokenInstancia.setBoxShadowConfigBlurRadius(5);
        hoxTokenInstancia.setBoxShadowConfigSpreadRadius(0);
        hoxTokenInstancia.setBoxShadowConfigShadowColor("clBlack");
        hoxTokenInstancia.setBoxShadowConfigOpacity(75);
        hoxTokenInstancia.setVAlign("tvTop");
        hboxDadosZapi.addChildren(hoxTokenInstancia);
        hoxTokenInstancia.applyProperties();
    }

    public TFHBox hboxlblTokenInstancia = new TFHBox();

    private void init_hboxlblTokenInstancia() {
        hboxlblTokenInstancia.setName("hboxlblTokenInstancia");
        hboxlblTokenInstancia.setLeft(0);
        hboxlblTokenInstancia.setTop(0);
        hboxlblTokenInstancia.setWidth(120);
        hboxlblTokenInstancia.setHeight(30);
        hboxlblTokenInstancia.setBorderStyle("stNone");
        hboxlblTokenInstancia.setPaddingTop(10);
        hboxlblTokenInstancia.setPaddingLeft(0);
        hboxlblTokenInstancia.setPaddingRight(7);
        hboxlblTokenInstancia.setPaddingBottom(0);
        hboxlblTokenInstancia.setMarginTop(0);
        hboxlblTokenInstancia.setMarginLeft(0);
        hboxlblTokenInstancia.setMarginRight(0);
        hboxlblTokenInstancia.setMarginBottom(0);
        hboxlblTokenInstancia.setSpacing(1);
        hboxlblTokenInstancia.setFlexVflex("ftFalse");
        hboxlblTokenInstancia.setFlexHflex("ftFalse");
        hboxlblTokenInstancia.setScrollable(false);
        hboxlblTokenInstancia.setBoxShadowConfigHorizontalLength(10);
        hboxlblTokenInstancia.setBoxShadowConfigVerticalLength(10);
        hboxlblTokenInstancia.setBoxShadowConfigBlurRadius(5);
        hboxlblTokenInstancia.setBoxShadowConfigSpreadRadius(0);
        hboxlblTokenInstancia.setBoxShadowConfigShadowColor("clBlack");
        hboxlblTokenInstancia.setBoxShadowConfigOpacity(75);
        hboxlblTokenInstancia.setVAlign("tvMiddle");
        hoxTokenInstancia.addChildren(hboxlblTokenInstancia);
        hboxlblTokenInstancia.applyProperties();
    }

    public TFVBox seplblTokenInstancia = new TFVBox();

    private void init_seplblTokenInstancia() {
        seplblTokenInstancia.setName("seplblTokenInstancia");
        seplblTokenInstancia.setLeft(0);
        seplblTokenInstancia.setTop(0);
        seplblTokenInstancia.setWidth(40);
        seplblTokenInstancia.setHeight(28);
        seplblTokenInstancia.setBorderStyle("stNone");
        seplblTokenInstancia.setPaddingTop(0);
        seplblTokenInstancia.setPaddingLeft(0);
        seplblTokenInstancia.setPaddingRight(0);
        seplblTokenInstancia.setPaddingBottom(0);
        seplblTokenInstancia.setMarginTop(0);
        seplblTokenInstancia.setMarginLeft(0);
        seplblTokenInstancia.setMarginRight(0);
        seplblTokenInstancia.setMarginBottom(0);
        seplblTokenInstancia.setSpacing(1);
        seplblTokenInstancia.setFlexVflex("ftTrue");
        seplblTokenInstancia.setFlexHflex("ftTrue");
        seplblTokenInstancia.setScrollable(false);
        seplblTokenInstancia.setBoxShadowConfigHorizontalLength(10);
        seplblTokenInstancia.setBoxShadowConfigVerticalLength(10);
        seplblTokenInstancia.setBoxShadowConfigBlurRadius(5);
        seplblTokenInstancia.setBoxShadowConfigSpreadRadius(0);
        seplblTokenInstancia.setBoxShadowConfigShadowColor("clBlack");
        seplblTokenInstancia.setBoxShadowConfigOpacity(75);
        hboxlblTokenInstancia.addChildren(seplblTokenInstancia);
        seplblTokenInstancia.applyProperties();
    }

    public TFLabel lblTokenInstancia = new TFLabel();

    private void init_lblTokenInstancia() {
        lblTokenInstancia.setName("lblTokenInstancia");
        lblTokenInstancia.setLeft(40);
        lblTokenInstancia.setTop(0);
        lblTokenInstancia.setWidth(76);
        lblTokenInstancia.setHeight(13);
        lblTokenInstancia.setAlign("alRight");
        lblTokenInstancia.setCaption("Token Inst\u00E2ncia");
        lblTokenInstancia.setFontColor("clWindowText");
        lblTokenInstancia.setFontSize(-11);
        lblTokenInstancia.setFontName("Tahoma");
        lblTokenInstancia.setFontStyle("[]");
        lblTokenInstancia.setVerticalAlignment("taVerticalCenter");
        lblTokenInstancia.setWordBreak(false);
        hboxlblTokenInstancia.addChildren(lblTokenInstancia);
        lblTokenInstancia.applyProperties();
    }

    public TFString edtTokenInstancia = new TFString();

    private void init_edtTokenInstancia() {
        edtTokenInstancia.setName("edtTokenInstancia");
        edtTokenInstancia.setLeft(120);
        edtTokenInstancia.setTop(0);
        edtTokenInstancia.setWidth(318);
        edtTokenInstancia.setHeight(24);
        edtTokenInstancia.setTable(tbCadastroWhatsapp);
        edtTokenInstancia.setFieldName("ZAPI_INSTANCE_TOKEN");
        edtTokenInstancia.setFlex(true);
        edtTokenInstancia.setRequired(true);
        edtTokenInstancia.setConstraintCheckWhen("cwImmediate");
        edtTokenInstancia.setConstraintCheckType("ctExpression");
        edtTokenInstancia.setConstraintFocusOnError(false);
        edtTokenInstancia.setConstraintEnableUI(true);
        edtTokenInstancia.setConstraintEnabled(false);
        edtTokenInstancia.setConstraintFormCheck(true);
        edtTokenInstancia.setCharCase("ccNormal");
        edtTokenInstancia.setPwd(false);
        edtTokenInstancia.setMaxlength(0);
        edtTokenInstancia.setFontColor("clWindowText");
        edtTokenInstancia.setFontSize(-13);
        edtTokenInstancia.setFontName("Tahoma");
        edtTokenInstancia.setFontStyle("[]");
        edtTokenInstancia.setSaveLiteralCharacter(false);
        edtTokenInstancia.applyProperties();
        hoxTokenInstancia.addChildren(edtTokenInstancia);
        addValidatable(edtTokenInstancia);
    }

    public TFHBox hboxClienteToken = new TFHBox();

    private void init_hboxClienteToken() {
        hboxClienteToken.setName("hboxClienteToken");
        hboxClienteToken.setLeft(0);
        hboxClienteToken.setTop(78);
        hboxClienteToken.setWidth(690);
        hboxClienteToken.setHeight(38);
        hboxClienteToken.setBorderStyle("stNone");
        hboxClienteToken.setPaddingTop(0);
        hboxClienteToken.setPaddingLeft(0);
        hboxClienteToken.setPaddingRight(0);
        hboxClienteToken.setPaddingBottom(0);
        hboxClienteToken.setMarginTop(0);
        hboxClienteToken.setMarginLeft(0);
        hboxClienteToken.setMarginRight(0);
        hboxClienteToken.setMarginBottom(0);
        hboxClienteToken.setSpacing(5);
        hboxClienteToken.setFlexVflex("ftFalse");
        hboxClienteToken.setFlexHflex("ftTrue");
        hboxClienteToken.setScrollable(false);
        hboxClienteToken.setBoxShadowConfigHorizontalLength(10);
        hboxClienteToken.setBoxShadowConfigVerticalLength(10);
        hboxClienteToken.setBoxShadowConfigBlurRadius(5);
        hboxClienteToken.setBoxShadowConfigSpreadRadius(0);
        hboxClienteToken.setBoxShadowConfigShadowColor("clBlack");
        hboxClienteToken.setBoxShadowConfigOpacity(75);
        hboxClienteToken.setVAlign("tvTop");
        hboxDadosZapi.addChildren(hboxClienteToken);
        hboxClienteToken.applyProperties();
    }

    public TFHBox hboxlblClienteToken = new TFHBox();

    private void init_hboxlblClienteToken() {
        hboxlblClienteToken.setName("hboxlblClienteToken");
        hboxlblClienteToken.setLeft(0);
        hboxlblClienteToken.setTop(0);
        hboxlblClienteToken.setWidth(120);
        hboxlblClienteToken.setHeight(30);
        hboxlblClienteToken.setBorderStyle("stNone");
        hboxlblClienteToken.setPaddingTop(10);
        hboxlblClienteToken.setPaddingLeft(0);
        hboxlblClienteToken.setPaddingRight(7);
        hboxlblClienteToken.setPaddingBottom(0);
        hboxlblClienteToken.setMarginTop(0);
        hboxlblClienteToken.setMarginLeft(0);
        hboxlblClienteToken.setMarginRight(0);
        hboxlblClienteToken.setMarginBottom(0);
        hboxlblClienteToken.setSpacing(1);
        hboxlblClienteToken.setFlexVflex("ftFalse");
        hboxlblClienteToken.setFlexHflex("ftFalse");
        hboxlblClienteToken.setScrollable(false);
        hboxlblClienteToken.setBoxShadowConfigHorizontalLength(10);
        hboxlblClienteToken.setBoxShadowConfigVerticalLength(10);
        hboxlblClienteToken.setBoxShadowConfigBlurRadius(5);
        hboxlblClienteToken.setBoxShadowConfigSpreadRadius(0);
        hboxlblClienteToken.setBoxShadowConfigShadowColor("clBlack");
        hboxlblClienteToken.setBoxShadowConfigOpacity(75);
        hboxlblClienteToken.setVAlign("tvMiddle");
        hboxClienteToken.addChildren(hboxlblClienteToken);
        hboxlblClienteToken.applyProperties();
    }

    public TFVBox seplblClienteToken = new TFVBox();

    private void init_seplblClienteToken() {
        seplblClienteToken.setName("seplblClienteToken");
        seplblClienteToken.setLeft(0);
        seplblClienteToken.setTop(0);
        seplblClienteToken.setWidth(39);
        seplblClienteToken.setHeight(28);
        seplblClienteToken.setBorderStyle("stNone");
        seplblClienteToken.setPaddingTop(0);
        seplblClienteToken.setPaddingLeft(0);
        seplblClienteToken.setPaddingRight(0);
        seplblClienteToken.setPaddingBottom(0);
        seplblClienteToken.setMarginTop(0);
        seplblClienteToken.setMarginLeft(0);
        seplblClienteToken.setMarginRight(0);
        seplblClienteToken.setMarginBottom(0);
        seplblClienteToken.setSpacing(1);
        seplblClienteToken.setFlexVflex("ftTrue");
        seplblClienteToken.setFlexHflex("ftTrue");
        seplblClienteToken.setScrollable(false);
        seplblClienteToken.setBoxShadowConfigHorizontalLength(10);
        seplblClienteToken.setBoxShadowConfigVerticalLength(10);
        seplblClienteToken.setBoxShadowConfigBlurRadius(5);
        seplblClienteToken.setBoxShadowConfigSpreadRadius(0);
        seplblClienteToken.setBoxShadowConfigShadowColor("clBlack");
        seplblClienteToken.setBoxShadowConfigOpacity(75);
        hboxlblClienteToken.addChildren(seplblClienteToken);
        seplblClienteToken.applyProperties();
    }

    public TFLabel lblClienteToken = new TFLabel();

    private void init_lblClienteToken() {
        lblClienteToken.setName("lblClienteToken");
        lblClienteToken.setLeft(39);
        lblClienteToken.setTop(0);
        lblClienteToken.setWidth(65);
        lblClienteToken.setHeight(13);
        lblClienteToken.setAlign("alRight");
        lblClienteToken.setCaption("Cliente Token");
        lblClienteToken.setFontColor("clWindowText");
        lblClienteToken.setFontSize(-11);
        lblClienteToken.setFontName("Tahoma");
        lblClienteToken.setFontStyle("[]");
        lblClienteToken.setVerticalAlignment("taVerticalCenter");
        lblClienteToken.setWordBreak(false);
        hboxlblClienteToken.addChildren(lblClienteToken);
        lblClienteToken.applyProperties();
    }

    public TFString edtClienteToken = new TFString();

    private void init_edtClienteToken() {
        edtClienteToken.setName("edtClienteToken");
        edtClienteToken.setLeft(120);
        edtClienteToken.setTop(0);
        edtClienteToken.setWidth(318);
        edtClienteToken.setHeight(24);
        edtClienteToken.setTable(tbCadastroWhatsapp);
        edtClienteToken.setFieldName("ZAPI_CLIENT_TOKEN");
        edtClienteToken.setFlex(true);
        edtClienteToken.setRequired(true);
        edtClienteToken.setConstraintCheckWhen("cwImmediate");
        edtClienteToken.setConstraintCheckType("ctExpression");
        edtClienteToken.setConstraintFocusOnError(false);
        edtClienteToken.setConstraintEnableUI(true);
        edtClienteToken.setConstraintEnabled(false);
        edtClienteToken.setConstraintFormCheck(true);
        edtClienteToken.setCharCase("ccNormal");
        edtClienteToken.setPwd(false);
        edtClienteToken.setMaxlength(0);
        edtClienteToken.setFontColor("clWindowText");
        edtClienteToken.setFontSize(-13);
        edtClienteToken.setFontName("Tahoma");
        edtClienteToken.setFontStyle("[]");
        edtClienteToken.setSaveLiteralCharacter(false);
        edtClienteToken.applyProperties();
        hboxClienteToken.addChildren(edtClienteToken);
        addValidatable(edtClienteToken);
    }

    public TFHBox hboxbtnTestarConexaoZAPI = new TFHBox();

    private void init_hboxbtnTestarConexaoZAPI() {
        hboxbtnTestarConexaoZAPI.setName("hboxbtnTestarConexaoZAPI");
        hboxbtnTestarConexaoZAPI.setLeft(0);
        hboxbtnTestarConexaoZAPI.setTop(117);
        hboxbtnTestarConexaoZAPI.setWidth(690);
        hboxbtnTestarConexaoZAPI.setHeight(38);
        hboxbtnTestarConexaoZAPI.setBorderStyle("stNone");
        hboxbtnTestarConexaoZAPI.setPaddingTop(0);
        hboxbtnTestarConexaoZAPI.setPaddingLeft(0);
        hboxbtnTestarConexaoZAPI.setPaddingRight(0);
        hboxbtnTestarConexaoZAPI.setPaddingBottom(0);
        hboxbtnTestarConexaoZAPI.setMarginTop(0);
        hboxbtnTestarConexaoZAPI.setMarginLeft(0);
        hboxbtnTestarConexaoZAPI.setMarginRight(0);
        hboxbtnTestarConexaoZAPI.setMarginBottom(0);
        hboxbtnTestarConexaoZAPI.setSpacing(5);
        hboxbtnTestarConexaoZAPI.setFlexVflex("ftFalse");
        hboxbtnTestarConexaoZAPI.setFlexHflex("ftTrue");
        hboxbtnTestarConexaoZAPI.setScrollable(false);
        hboxbtnTestarConexaoZAPI.setBoxShadowConfigHorizontalLength(10);
        hboxbtnTestarConexaoZAPI.setBoxShadowConfigVerticalLength(10);
        hboxbtnTestarConexaoZAPI.setBoxShadowConfigBlurRadius(5);
        hboxbtnTestarConexaoZAPI.setBoxShadowConfigSpreadRadius(0);
        hboxbtnTestarConexaoZAPI.setBoxShadowConfigShadowColor("clBlack");
        hboxbtnTestarConexaoZAPI.setBoxShadowConfigOpacity(75);
        hboxbtnTestarConexaoZAPI.setVAlign("tvTop");
        hboxDadosZapi.addChildren(hboxbtnTestarConexaoZAPI);
        hboxbtnTestarConexaoZAPI.applyProperties();
    }

    public TFHBox sepbtnTestarConexaoZAPI = new TFHBox();

    private void init_sepbtnTestarConexaoZAPI() {
        sepbtnTestarConexaoZAPI.setName("sepbtnTestarConexaoZAPI");
        sepbtnTestarConexaoZAPI.setLeft(0);
        sepbtnTestarConexaoZAPI.setTop(0);
        sepbtnTestarConexaoZAPI.setWidth(120);
        sepbtnTestarConexaoZAPI.setHeight(30);
        sepbtnTestarConexaoZAPI.setBorderStyle("stNone");
        sepbtnTestarConexaoZAPI.setPaddingTop(10);
        sepbtnTestarConexaoZAPI.setPaddingLeft(0);
        sepbtnTestarConexaoZAPI.setPaddingRight(7);
        sepbtnTestarConexaoZAPI.setPaddingBottom(0);
        sepbtnTestarConexaoZAPI.setMarginTop(0);
        sepbtnTestarConexaoZAPI.setMarginLeft(0);
        sepbtnTestarConexaoZAPI.setMarginRight(0);
        sepbtnTestarConexaoZAPI.setMarginBottom(0);
        sepbtnTestarConexaoZAPI.setSpacing(1);
        sepbtnTestarConexaoZAPI.setFlexVflex("ftFalse");
        sepbtnTestarConexaoZAPI.setFlexHflex("ftFalse");
        sepbtnTestarConexaoZAPI.setScrollable(false);
        sepbtnTestarConexaoZAPI.setBoxShadowConfigHorizontalLength(10);
        sepbtnTestarConexaoZAPI.setBoxShadowConfigVerticalLength(10);
        sepbtnTestarConexaoZAPI.setBoxShadowConfigBlurRadius(5);
        sepbtnTestarConexaoZAPI.setBoxShadowConfigSpreadRadius(0);
        sepbtnTestarConexaoZAPI.setBoxShadowConfigShadowColor("clBlack");
        sepbtnTestarConexaoZAPI.setBoxShadowConfigOpacity(75);
        sepbtnTestarConexaoZAPI.setVAlign("tvMiddle");
        hboxbtnTestarConexaoZAPI.addChildren(sepbtnTestarConexaoZAPI);
        sepbtnTestarConexaoZAPI.applyProperties();
    }

    public TFButton btnTestarConexaoZAPI = new TFButton();

    private void init_btnTestarConexaoZAPI() {
        btnTestarConexaoZAPI.setName("btnTestarConexaoZAPI");
        btnTestarConexaoZAPI.setLeft(120);
        btnTestarConexaoZAPI.setTop(0);
        btnTestarConexaoZAPI.setWidth(75);
        btnTestarConexaoZAPI.setHeight(33);
        btnTestarConexaoZAPI.setCaption("Conectar");
        btnTestarConexaoZAPI.setFontColor("clWindowText");
        btnTestarConexaoZAPI.setFontSize(-11);
        btnTestarConexaoZAPI.setFontName("Tahoma");
        btnTestarConexaoZAPI.setFontStyle("[]");
        btnTestarConexaoZAPI.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnTestarConexaoZAPIClick(event);
            processarFlow("FrmCadastroLeadzap", "btnTestarConexaoZAPI", "OnClick");
        });
        btnTestarConexaoZAPI.setImageId(0);
        btnTestarConexaoZAPI.setColor("clBtnFace");
        btnTestarConexaoZAPI.setAccess(false);
        btnTestarConexaoZAPI.setIconReverseDirection(false);
        hboxbtnTestarConexaoZAPI.addChildren(btnTestarConexaoZAPI);
        btnTestarConexaoZAPI.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(0);
        FHBox13.setTop(557);
        FHBox13.setWidth(518);
        FHBox13.setHeight(32);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setVisible(false);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftTrue");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        FVBox3.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(0);
        FHBox20.setTop(0);
        FHBox20.setWidth(120);
        FHBox20.setHeight(30);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(10);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(7);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftTrue");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvMiddle");
        FHBox13.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFLabel FLabel9 = new TFLabel();

    private void init_FLabel9() {
        FLabel9.setName("FLabel9");
        FLabel9.setLeft(120);
        FLabel9.setTop(0);
        FLabel9.setWidth(201);
        FLabel9.setHeight(19);
        FLabel9.setCaption("Integra\u00E7\u00E3o com LeadZap");
        FLabel9.setFontColor("clWindowText");
        FLabel9.setFontSize(-16);
        FLabel9.setFontName("Tahoma");
        FLabel9.setFontStyle("[fsBold]");
        FLabel9.setVerticalAlignment("taVerticalCenter");
        FLabel9.setWordBreak(false);
        FHBox13.addChildren(FLabel9);
        FLabel9.applyProperties();
    }

    public TFHBox FHBox27 = new TFHBox();

    private void init_FHBox27() {
        FHBox27.setName("FHBox27");
        FHBox27.setLeft(321);
        FHBox27.setTop(0);
        FHBox27.setWidth(120);
        FHBox27.setHeight(30);
        FHBox27.setBorderStyle("stNone");
        FHBox27.setPaddingTop(10);
        FHBox27.setPaddingLeft(0);
        FHBox27.setPaddingRight(7);
        FHBox27.setPaddingBottom(0);
        FHBox27.setMarginTop(0);
        FHBox27.setMarginLeft(0);
        FHBox27.setMarginRight(0);
        FHBox27.setMarginBottom(0);
        FHBox27.setSpacing(1);
        FHBox27.setFlexVflex("ftFalse");
        FHBox27.setFlexHflex("ftTrue");
        FHBox27.setScrollable(false);
        FHBox27.setBoxShadowConfigHorizontalLength(10);
        FHBox27.setBoxShadowConfigVerticalLength(10);
        FHBox27.setBoxShadowConfigBlurRadius(5);
        FHBox27.setBoxShadowConfigSpreadRadius(0);
        FHBox27.setBoxShadowConfigShadowColor("clBlack");
        FHBox27.setBoxShadowConfigOpacity(75);
        FHBox27.setVAlign("tvMiddle");
        FHBox13.addChildren(FHBox27);
        FHBox27.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(590);
        FHBox11.setWidth(753);
        FHBox11.setHeight(77);
        FHBox11.setAlign("alClient");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(5);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setVisible(false);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox3.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnCadastrar = new TFButton();

    private void init_btnCadastrar() {
        btnCadastrar.setName("btnCadastrar");
        btnCadastrar.setLeft(0);
        btnCadastrar.setTop(0);
        btnCadastrar.setWidth(66);
        btnCadastrar.setHeight(66);
        btnCadastrar.setAlign("alClient");
        btnCadastrar.setCaption("Cadastrar");
        btnCadastrar.setFontColor("clWindowText");
        btnCadastrar.setFontSize(-11);
        btnCadastrar.setFontName("Tahoma");
        btnCadastrar.setFontStyle("[]");
        btnCadastrar.setLayout("blGlyphTop");
        btnCadastrar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCadastrarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnCadastrar", "OnClick");
        });
        btnCadastrar.setImageId(220013);
        btnCadastrar.setColor("clBtnFace");
        btnCadastrar.setAccess(false);
        btnCadastrar.setIconReverseDirection(false);
        FHBox11.addChildren(btnCadastrar);
        btnCadastrar.applyProperties();
    }

    public TFButton btnSincronizar = new TFButton();

    private void init_btnSincronizar() {
        btnSincronizar.setName("btnSincronizar");
        btnSincronizar.setLeft(66);
        btnSincronizar.setTop(0);
        btnSincronizar.setWidth(66);
        btnSincronizar.setHeight(66);
        btnSincronizar.setAlign("alClient");
        btnSincronizar.setCaption("Sincronizar");
        btnSincronizar.setFontColor("clWindowText");
        btnSincronizar.setFontSize(-11);
        btnSincronizar.setFontName("Tahoma");
        btnSincronizar.setFontStyle("[]");
        btnSincronizar.setLayout("blGlyphTop");
        btnSincronizar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSincronizarClick(event);
            processarFlow("FrmCadastroLeadzap", "btnSincronizar", "OnClick");
        });
        btnSincronizar.setImageId(700091);
        btnSincronizar.setColor("clBtnFace");
        btnSincronizar.setAccess(false);
        btnSincronizar.setIconReverseDirection(false);
        FHBox11.addChildren(btnSincronizar);
        btnSincronizar.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(132);
        FVBox5.setTop(0);
        FVBox5.setWidth(156);
        FVBox5.setHeight(68);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(13);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox11.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel FLabel10 = new TFLabel();

    private void init_FLabel10() {
        FLabel10.setName("FLabel10");
        FLabel10.setLeft(0);
        FLabel10.setTop(0);
        FLabel10.setWidth(52);
        FLabel10.setHeight(16);
        FLabel10.setCaption("Status: ");
        FLabel10.setFontColor("clWindowText");
        FLabel10.setFontSize(-13);
        FLabel10.setFontName("Tahoma");
        FLabel10.setFontStyle("[fsBold]");
        FLabel10.setVerticalAlignment("taVerticalCenter");
        FLabel10.setWordBreak(false);
        FVBox5.addChildren(FLabel10);
        FLabel10.applyProperties();
    }

    public TFLabel lblStatusSinc = new TFLabel();

    private void init_lblStatusSinc() {
        lblStatusSinc.setName("lblStatusSinc");
        lblStatusSinc.setLeft(0);
        lblStatusSinc.setTop(17);
        lblStatusSinc.setWidth(99);
        lblStatusSinc.setHeight(16);
        lblStatusSinc.setCaption("N\u00E3o Sincronizado");
        lblStatusSinc.setFontColor("clWindowText");
        lblStatusSinc.setFontSize(-13);
        lblStatusSinc.setFontName("Tahoma");
        lblStatusSinc.setFontStyle("[]");
        lblStatusSinc.setVerticalAlignment("taVerticalCenter");
        lblStatusSinc.setWordBreak(false);
        FVBox5.addChildren(lblStatusSinc);
        lblStatusSinc.applyProperties();
    }

    public TFButton btnRefreshSinc = new TFButton();

    private void init_btnRefreshSinc() {
        btnRefreshSinc.setName("btnRefreshSinc");
        btnRefreshSinc.setLeft(288);
        btnRefreshSinc.setTop(0);
        btnRefreshSinc.setWidth(66);
        btnRefreshSinc.setHeight(66);
        btnRefreshSinc.setCaption("LeadZap");
        btnRefreshSinc.setFontColor("clWindowText");
        btnRefreshSinc.setFontSize(-11);
        btnRefreshSinc.setFontName("Tahoma");
        btnRefreshSinc.setFontStyle("[]");
        btnRefreshSinc.setLayout("blGlyphTop");
        btnRefreshSinc.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnRefreshSincClick(event);
            processarFlow("FrmCadastroLeadzap", "btnRefreshSinc", "OnClick");
        });
        btnRefreshSinc.setImageId(310035);
        btnRefreshSinc.setColor("clBtnFace");
        btnRefreshSinc.setAccess(false);
        btnRefreshSinc.setIconReverseDirection(false);
        FHBox11.addChildren(btnRefreshSinc);
        btnRefreshSinc.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(668);
        FHBox10.setWidth(690);
        FHBox10.setHeight(110);
        FHBox10.setAlign("alClient");
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setVisible(false);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(10);
        FHBox10.setSpacing(5);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FVBox3.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(0);
        FHBox18.setWidth(120);
        FHBox18.setHeight(30);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(10);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(7);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftFalse");
        FHBox18.setFlexHflex("ftFalse");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvMiddle");
        FHBox10.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFVBox FVBox11 = new TFVBox();

    private void init_FVBox11() {
        FVBox11.setName("FVBox11");
        FVBox11.setLeft(0);
        FVBox11.setTop(0);
        FVBox11.setWidth(38);
        FVBox11.setHeight(28);
        FVBox11.setBorderStyle("stNone");
        FVBox11.setPaddingTop(0);
        FVBox11.setPaddingLeft(0);
        FVBox11.setPaddingRight(0);
        FVBox11.setPaddingBottom(0);
        FVBox11.setMarginTop(0);
        FVBox11.setMarginLeft(0);
        FVBox11.setMarginRight(0);
        FVBox11.setMarginBottom(0);
        FVBox11.setSpacing(1);
        FVBox11.setFlexVflex("ftTrue");
        FVBox11.setFlexHflex("ftTrue");
        FVBox11.setScrollable(false);
        FVBox11.setBoxShadowConfigHorizontalLength(10);
        FVBox11.setBoxShadowConfigVerticalLength(10);
        FVBox11.setBoxShadowConfigBlurRadius(5);
        FVBox11.setBoxShadowConfigSpreadRadius(0);
        FVBox11.setBoxShadowConfigShadowColor("clBlack");
        FVBox11.setBoxShadowConfigOpacity(75);
        FHBox18.addChildren(FVBox11);
        FVBox11.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(38);
        FLabel1.setTop(0);
        FLabel1.setWidth(38);
        FLabel1.setHeight(13);
        FLabel1.setAlign("alRight");
        FLabel1.setCaption("Imagem");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FHBox18.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFPanel FPanel8 = new TFPanel();

    private void init_FPanel8() {
        FPanel8.setName("FPanel8");
        FPanel8.setLeft(120);
        FPanel8.setTop(0);
        FPanel8.setWidth(440);
        FPanel8.setHeight(107);
        FPanel8.setAlign("alLeft");
        FPanel8.setBorderStyle("stNone");
        FPanel8.setPaddingTop(0);
        FPanel8.setPaddingLeft(0);
        FPanel8.setPaddingRight(0);
        FPanel8.setPaddingBottom(0);
        FPanel8.setFlexVflex("ftFalse");
        FPanel8.setFlexHflex("ftTrue");
        FPanel8.setMarginTop(0);
        FPanel8.setMarginLeft(0);
        FPanel8.setMarginRight(0);
        FPanel8.setMarginBottom(0);
        FPanel8.setBoxShadowConfigHorizontalLength(10);
        FPanel8.setBoxShadowConfigVerticalLength(10);
        FPanel8.setBoxShadowConfigBlurRadius(5);
        FPanel8.setBoxShadowConfigSpreadRadius(0);
        FPanel8.setBoxShadowConfigShadowColor("clBlack");
        FPanel8.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(FPanel8);
        FPanel8.applyProperties();
    }

    public TFPanel FPanel9 = new TFPanel();

    private void init_FPanel9() {
        FPanel9.setName("FPanel9");
        FPanel9.setLeft(0);
        FPanel9.setTop(0);
        FPanel9.setWidth(100);
        FPanel9.setHeight(100);
        FPanel9.setBorderStyle("stGroupBox");
        FPanel9.setPaddingTop(0);
        FPanel9.setPaddingLeft(0);
        FPanel9.setPaddingRight(0);
        FPanel9.setPaddingBottom(0);
        FPanel9.setFlexVflex("ftFalse");
        FPanel9.setFlexHflex("ftFalse");
        FPanel9.setMarginTop(0);
        FPanel9.setMarginLeft(0);
        FPanel9.setMarginRight(0);
        FPanel9.setMarginBottom(0);
        FPanel9.setBoxShadowConfigHorizontalLength(10);
        FPanel9.setBoxShadowConfigVerticalLength(10);
        FPanel9.setBoxShadowConfigBlurRadius(2);
        FPanel9.setBoxShadowConfigSpreadRadius(0);
        FPanel9.setBoxShadowConfigShadowColor("clBlack");
        FPanel9.setBoxShadowConfigOpacity(75);
        FPanel8.addChildren(FPanel9);
        FPanel9.applyProperties();
    }

    public TFImage imgPerfil = new TFImage();

    private void init_imgPerfil() {
        imgPerfil.setName("imgPerfil");
        imgPerfil.setLeft(0);
        imgPerfil.setTop(0);
        imgPerfil.setWidth(100);
        imgPerfil.setHeight(100);
        imgPerfil.setTable(tbCadastroWhatsapp);
        imgPerfil.setFieldName("IMAGEM");
        imgPerfil.setBoxSize(0);
        imgPerfil.setGrayScaleOnDisable(false);
        imgPerfil.setFlexVflex("ftFalse");
        imgPerfil.setFlexHflex("ftFalse");
        imgPerfil.setImageId(0);
        FPanel9.addChildren(imgPerfil);
        imgPerfil.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(118);
        FVBox4.setTop(1);
        FVBox4.setWidth(185);
        FVBox4.setHeight(103);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FPanel8.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(0);
        FHBox21.setWidth(179);
        FHBox21.setHeight(84);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(5);
        FHBox21.setFlexVflex("ftFalse");
        FHBox21.setFlexHflex("ftFalse");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        FVBox4.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFButton btnUploadImage = new TFButton();

    private void init_btnUploadImage() {
        btnUploadImage.setName("btnUploadImage");
        btnUploadImage.setLeft(0);
        btnUploadImage.setTop(0);
        btnUploadImage.setWidth(66);
        btnUploadImage.setHeight(66);
        btnUploadImage.setCaption("Upload");
        btnUploadImage.setFontColor("clWindowText");
        btnUploadImage.setFontSize(-11);
        btnUploadImage.setFontName("Tahoma");
        btnUploadImage.setFontStyle("[]");
        btnUploadImage.setLayout("blGlyphTop");
        btnUploadImage.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnUploadImageClick(event);
            processarFlow("FrmCadastroLeadzap", "btnUploadImage", "OnClick");
        });
        btnUploadImage.setImageId(7000194);
        btnUploadImage.setColor("clBtnFace");
        btnUploadImage.setAccess(false);
        btnUploadImage.setIconReverseDirection(false);
        FHBox21.addChildren(btnUploadImage);
        btnUploadImage.applyProperties();
    }

    public TFButton btnLimparImagem = new TFButton();

    private void init_btnLimparImagem() {
        btnLimparImagem.setName("btnLimparImagem");
        btnLimparImagem.setLeft(66);
        btnLimparImagem.setTop(0);
        btnLimparImagem.setWidth(66);
        btnLimparImagem.setHeight(66);
        btnLimparImagem.setCaption("Limpar");
        btnLimparImagem.setFontColor("clWindowText");
        btnLimparImagem.setFontSize(-11);
        btnLimparImagem.setFontName("Tahoma");
        btnLimparImagem.setFontStyle("[]");
        btnLimparImagem.setLayout("blGlyphTop");
        btnLimparImagem.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimparImagemClick(event);
            processarFlow("FrmCadastroLeadzap", "btnLimparImagem", "OnClick");
        });
        btnLimparImagem.setImageId(310030);
        btnLimparImagem.setColor("clBtnFace");
        btnLimparImagem.setAccess(false);
        btnLimparImagem.setIconReverseDirection(false);
        FHBox21.addChildren(btnLimparImagem);
        btnLimparImagem.applyProperties();
    }

    public TFLabel FLabel8 = new TFLabel();

    private void init_FLabel8() {
        FLabel8.setName("FLabel8");
        FLabel8.setLeft(0);
        FLabel8.setTop(85);
        FLabel8.setWidth(143);
        FLabel8.setHeight(13);
        FLabel8.setCaption("Recomendado 400x400px jpg");
        FLabel8.setFontColor("clWindowText");
        FLabel8.setFontSize(-11);
        FLabel8.setFontName("Tahoma");
        FLabel8.setFontStyle("[]");
        FLabel8.setVerticalAlignment("taVerticalCenter");
        FLabel8.setWordBreak(false);
        FVBox4.addChildren(FLabel8);
        FLabel8.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(0);
        FHBox22.setTop(779);
        FHBox22.setWidth(628);
        FHBox22.setHeight(32);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(0);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftFalse");
        FHBox22.setFlexHflex("ftTrue");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        FVBox3.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(0);
        FHBox23.setTop(0);
        FHBox23.setWidth(120);
        FHBox23.setHeight(30);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(10);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(7);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(1);
        FHBox23.setFlexVflex("ftFalse");
        FHBox23.setFlexHflex("ftTrue");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvMiddle");
        FHBox22.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFLabel FLabel11 = new TFLabel();

    private void init_FLabel11() {
        FLabel11.setName("FLabel11");
        FLabel11.setLeft(120);
        FLabel11.setTop(0);
        FLabel11.setWidth(146);
        FLabel11.setHeight(19);
        FLabel11.setCaption("Logs de Intera\u00E7\u00E3o");
        FLabel11.setFontColor("clWindowText");
        FLabel11.setFontSize(-16);
        FLabel11.setFontName("Tahoma");
        FLabel11.setFontStyle("[fsBold]");
        FLabel11.setVerticalAlignment("taVerticalCenter");
        FLabel11.setWordBreak(false);
        FHBox22.addChildren(FLabel11);
        FLabel11.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(266);
        FHBox26.setTop(0);
        FHBox26.setWidth(120);
        FHBox26.setHeight(30);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(10);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(7);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(1);
        FHBox26.setFlexVflex("ftFalse");
        FHBox26.setFlexHflex("ftTrue");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvMiddle");
        FHBox22.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(0);
        FHBox24.setTop(812);
        FHBox24.setWidth(712);
        FHBox24.setHeight(175);
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(5);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(5);
        FHBox24.setFlexVflex("ftFalse");
        FHBox24.setFlexHflex("ftTrue");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvTop");
        FVBox3.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFButton btnAtualizarLog = new TFButton();

    private void init_btnAtualizarLog() {
        btnAtualizarLog.setName("btnAtualizarLog");
        btnAtualizarLog.setLeft(0);
        btnAtualizarLog.setTop(0);
        btnAtualizarLog.setWidth(66);
        btnAtualizarLog.setHeight(66);
        btnAtualizarLog.setCaption("Atualizar");
        btnAtualizarLog.setFontColor("clWindowText");
        btnAtualizarLog.setFontSize(-11);
        btnAtualizarLog.setFontName("Tahoma");
        btnAtualizarLog.setFontStyle("[]");
        btnAtualizarLog.setLayout("blGlyphTop");
        btnAtualizarLog.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAtualizarLogClick(event);
            processarFlow("FrmCadastroLeadzap", "btnAtualizarLog", "OnClick");
        });
        btnAtualizarLog.setImageId(310035);
        btnAtualizarLog.setColor("clBtnFace");
        btnAtualizarLog.setAccess(false);
        btnAtualizarLog.setIconReverseDirection(false);
        FHBox24.addChildren(btnAtualizarLog);
        btnAtualizarLog.applyProperties();
    }

    public TFGrid gridLog = new TFGrid();

    private void init_gridLog() {
        gridLog.setName("gridLog");
        gridLog.setLeft(66);
        gridLog.setTop(0);
        gridLog.setWidth(518);
        gridLog.setHeight(175);
        gridLog.setTable(tbWhatsappLog);
        gridLog.setFlexVflex("ftTrue");
        gridLog.setFlexHflex("ftTrue");
        gridLog.setPagingEnabled(false);
        gridLog.setFrozenColumns(0);
        gridLog.setShowFooter(false);
        gridLog.setShowHeader(false);
        gridLog.setMultiSelection(false);
        gridLog.setGroupingEnabled(false);
        gridLog.setGroupingExpanded(false);
        gridLog.setGroupingShowFooter(false);
        gridLog.setCrosstabEnabled(false);
        gridLog.setCrosstabGroupType("cgtConcat");
        gridLog.setEditionEnabled(false);
        gridLog.setNoBorder(true);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DATA_OCORRENCIA");
        item0.setTitleCaption("Data Ocorr\u00EAncia");
        item0.setWidth(174);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftDateTime");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFMaskExpression item1 = new TFMaskExpression();
        item1.setExpression("*");
        item1.setEvalType("etExpression");
        item1.setMask("dd/MM/yyyy HH:mm:ss");
        item1.setPadLength(0);
        item1.setPadDirection("pdNone");
        item1.setMaskType("mtDateTime");
        item0.getMasks().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridLog.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("OCORRENCIA");
        item2.setTitleCaption("Ocorr\u00EAncia");
        item2.setWidth(298);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridLog.getColumns().add(item2);
        FHBox24.addChildren(gridLog);
        gridLog.applyProperties();
    }

    public TFTabsheet tabCruzaEmpresa = new TFTabsheet();

    private void init_tabCruzaEmpresa() {
        tabCruzaEmpresa.setName("tabCruzaEmpresa");
        tabCruzaEmpresa.setCaption("Empresas");
        tabCruzaEmpresa.setVisible(true);
        tabCruzaEmpresa.setClosable(false);
        pgPrincipal.addChildren(tabCruzaEmpresa);
        tabCruzaEmpresa.applyProperties();
    }

    public TFVBox vBoxCruzaEmpresa = new TFVBox();

    private void init_vBoxCruzaEmpresa() {
        vBoxCruzaEmpresa.setName("vBoxCruzaEmpresa");
        vBoxCruzaEmpresa.setLeft(0);
        vBoxCruzaEmpresa.setTop(0);
        vBoxCruzaEmpresa.setWidth(966);
        vBoxCruzaEmpresa.setHeight(954);
        vBoxCruzaEmpresa.setAlign("alClient");
        vBoxCruzaEmpresa.setBorderStyle("stNone");
        vBoxCruzaEmpresa.setPaddingTop(0);
        vBoxCruzaEmpresa.setPaddingLeft(0);
        vBoxCruzaEmpresa.setPaddingRight(0);
        vBoxCruzaEmpresa.setPaddingBottom(0);
        vBoxCruzaEmpresa.setMarginTop(0);
        vBoxCruzaEmpresa.setMarginLeft(0);
        vBoxCruzaEmpresa.setMarginRight(0);
        vBoxCruzaEmpresa.setMarginBottom(0);
        vBoxCruzaEmpresa.setSpacing(1);
        vBoxCruzaEmpresa.setFlexVflex("ftTrue");
        vBoxCruzaEmpresa.setFlexHflex("ftTrue");
        vBoxCruzaEmpresa.setScrollable(false);
        vBoxCruzaEmpresa.setBoxShadowConfigHorizontalLength(10);
        vBoxCruzaEmpresa.setBoxShadowConfigVerticalLength(10);
        vBoxCruzaEmpresa.setBoxShadowConfigBlurRadius(5);
        vBoxCruzaEmpresa.setBoxShadowConfigSpreadRadius(0);
        vBoxCruzaEmpresa.setBoxShadowConfigShadowColor("clBlack");
        vBoxCruzaEmpresa.setBoxShadowConfigOpacity(75);
        tabCruzaEmpresa.addChildren(vBoxCruzaEmpresa);
        vBoxCruzaEmpresa.applyProperties();
    }

    public TFVBox gbDetalhe46001 = new TFVBox();

    private void init_gbDetalhe46001() {
        gbDetalhe46001.setName("gbDetalhe46001");
        gbDetalhe46001.setLeft(0);
        gbDetalhe46001.setTop(0);
        gbDetalhe46001.setWidth(947);
        gbDetalhe46001.setHeight(660);
        gbDetalhe46001.setAlign("alClient");
        gbDetalhe46001.setBorderStyle("stNone");
        gbDetalhe46001.setPaddingTop(5);
        gbDetalhe46001.setPaddingLeft(5);
        gbDetalhe46001.setPaddingRight(5);
        gbDetalhe46001.setPaddingBottom(5);
        gbDetalhe46001.setMarginTop(0);
        gbDetalhe46001.setMarginLeft(0);
        gbDetalhe46001.setMarginRight(0);
        gbDetalhe46001.setMarginBottom(0);
        gbDetalhe46001.setSpacing(1);
        gbDetalhe46001.setFlexVflex("ftTrue");
        gbDetalhe46001.setFlexHflex("ftTrue");
        gbDetalhe46001.setScrollable(false);
        gbDetalhe46001.setBoxShadowConfigHorizontalLength(10);
        gbDetalhe46001.setBoxShadowConfigVerticalLength(10);
        gbDetalhe46001.setBoxShadowConfigBlurRadius(5);
        gbDetalhe46001.setBoxShadowConfigSpreadRadius(0);
        gbDetalhe46001.setBoxShadowConfigShadowColor("clBlack");
        gbDetalhe46001.setBoxShadowConfigOpacity(75);
        vBoxCruzaEmpresa.addChildren(gbDetalhe46001);
        gbDetalhe46001.applyProperties();
    }

    public TFGridPanel gridPanelDetailEmpty46001 = new TFGridPanel();

    private void init_gridPanelDetailEmpty46001() {
        gridPanelDetailEmpty46001.setName("gridPanelDetailEmpty46001");
        gridPanelDetailEmpty46001.setLeft(0);
        gridPanelDetailEmpty46001.setTop(0);
        gridPanelDetailEmpty46001.setWidth(0);
        gridPanelDetailEmpty46001.setHeight(26);
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(50.000000000000000000);
        gridPanelDetailEmpty46001.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(50.000000000000000000);
        gridPanelDetailEmpty46001.getColumnCollection().add(item1);
        TFGridPanelRow item2 = new TFGridPanelRow();
        item2.setSizeStyle("ssAbsolute");
        item2.setValue(21.000000000000000000);
        gridPanelDetailEmpty46001.getRowCollection().add(item2);
        gridPanelDetailEmpty46001.setVisible(false);
        gridPanelDetailEmpty46001.setFlexVflex("ftTrue");
        gridPanelDetailEmpty46001.setFlexHflex("ftTrue");
        gridPanelDetailEmpty46001.setAllRowFlex(false);
        gridPanelDetailEmpty46001.setColumnTabOrder(false);
        gbDetalhe46001.addChildren(gridPanelDetailEmpty46001);
        gridPanelDetailEmpty46001.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(27);
        FHBox12.setWidth(889);
        FHBox12.setHeight(29);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setColor("clSilver");
        FHBox12.setPaddingTop(3);
        FHBox12.setPaddingLeft(5);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        gbDetalhe46001.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(0);
        FLabel7.setTop(0);
        FLabel7.setWidth(470);
        FLabel7.setHeight(13);
        FLabel7.setCaption("Quando houver contato com o cliente, esse cruzamento define quais empresas usam esse n\u00FAmero");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        FHBox12.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFDualList dualListEmpresasVinc = new TFDualList();

    private void init_dualListEmpresasVinc() {
        dualListEmpresasVinc.setName("dualListEmpresasVinc");
        dualListEmpresasVinc.setLeft(0);
        dualListEmpresasVinc.setTop(57);
        dualListEmpresasVinc.setWidth(880);
        dualListEmpresasVinc.setHeight(445);
        dualListEmpresasVinc.setCaption("Selecionados");
        dualListEmpresasVinc.setLookupCaption("Dispon\u00EDveis");
        dualListEmpresasVinc.setCaptionHiperLink(false);
        dualListEmpresasVinc.setLookupCaptionHiperLink(false);
        dualListEmpresasVinc.setFlexVflex("ftTrue");
        dualListEmpresasVinc.setFlexHflex("ftTrue");
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_EMPRESA");
        item0.setTitleCaption("Nome Empresa");
        item0.setWidth(359);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        dualListEmpresasVinc.getLookupColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NOME_EMPRESA");
        item1.setTitleCaption("Nome Empresa");
        item1.setWidth(362);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        dualListEmpresasVinc.getColumns().add(item1);
        dualListEmpresasVinc.setTable(tbWhatsappEmpresa);
        dualListEmpresasVinc.setLookupTable(tbEmpresasCruzaLeadZap);
        dualListEmpresasVinc.setMultiSelection(false);
        dualListEmpresasVinc.setPaging(true);
        dualListEmpresasVinc.setAlign("alClient");
        dualListEmpresasVinc.setGroupingEnabled(false);
        dualListEmpresasVinc.setGroupingExpanded(false);
        dualListEmpresasVinc.setGroupingShowFooter(false);
        dualListEmpresasVinc.setGroupingLookupEnabled(false);
        dualListEmpresasVinc.setGroupingLookupExpanded(false);
        dualListEmpresasVinc.setGroupingLookupShowFooter(false);
        gbDetalhe46001.addChildren(dualListEmpresasVinc);
        dualListEmpresasVinc.applyProperties();
    }

    public TFTabsheet tabCidades = new TFTabsheet();

    private void init_tabCidades() {
        tabCidades.setName("tabCidades");
        tabCidades.setCaption("Cidades");
        tabCidades.setVisible(true);
        tabCidades.setClosable(false);
        pgPrincipal.addChildren(tabCidades);
        tabCidades.applyProperties();
    }

    public TFVBox FVBox17 = new TFVBox();

    private void init_FVBox17() {
        FVBox17.setName("FVBox17");
        FVBox17.setLeft(0);
        FVBox17.setTop(0);
        FVBox17.setWidth(966);
        FVBox17.setHeight(954);
        FVBox17.setAlign("alClient");
        FVBox17.setBorderStyle("stNone");
        FVBox17.setPaddingTop(0);
        FVBox17.setPaddingLeft(5);
        FVBox17.setPaddingRight(5);
        FVBox17.setPaddingBottom(0);
        FVBox17.setMarginTop(0);
        FVBox17.setMarginLeft(0);
        FVBox17.setMarginRight(0);
        FVBox17.setMarginBottom(0);
        FVBox17.setSpacing(1);
        FVBox17.setFlexVflex("ftTrue");
        FVBox17.setFlexHflex("ftTrue");
        FVBox17.setScrollable(false);
        FVBox17.setBoxShadowConfigHorizontalLength(10);
        FVBox17.setBoxShadowConfigVerticalLength(10);
        FVBox17.setBoxShadowConfigBlurRadius(5);
        FVBox17.setBoxShadowConfigSpreadRadius(0);
        FVBox17.setBoxShadowConfigShadowColor("clBlack");
        FVBox17.setBoxShadowConfigOpacity(75);
        tabCidades.addChildren(FVBox17);
        FVBox17.applyProperties();
    }

    public TFHBox FHBox36 = new TFHBox();

    private void init_FHBox36() {
        FHBox36.setName("FHBox36");
        FHBox36.setLeft(0);
        FHBox36.setTop(0);
        FHBox36.setWidth(553);
        FHBox36.setHeight(72);
        FHBox36.setBorderStyle("stNone");
        FHBox36.setPaddingTop(5);
        FHBox36.setPaddingLeft(0);
        FHBox36.setPaddingRight(0);
        FHBox36.setPaddingBottom(0);
        FHBox36.setMarginTop(0);
        FHBox36.setMarginLeft(0);
        FHBox36.setMarginRight(0);
        FHBox36.setMarginBottom(0);
        FHBox36.setSpacing(1);
        FHBox36.setFlexVflex("ftFalse");
        FHBox36.setFlexHflex("ftTrue");
        FHBox36.setScrollable(false);
        FHBox36.setBoxShadowConfigHorizontalLength(10);
        FHBox36.setBoxShadowConfigVerticalLength(10);
        FHBox36.setBoxShadowConfigBlurRadius(5);
        FHBox36.setBoxShadowConfigSpreadRadius(0);
        FHBox36.setBoxShadowConfigShadowColor("clBlack");
        FHBox36.setBoxShadowConfigOpacity(75);
        FHBox36.setVAlign("tvTop");
        FVBox17.addChildren(FHBox36);
        FHBox36.applyProperties();
    }

    public TFVBox FVBox18 = new TFVBox();

    private void init_FVBox18() {
        FVBox18.setName("FVBox18");
        FVBox18.setLeft(0);
        FVBox18.setTop(0);
        FVBox18.setWidth(153);
        FVBox18.setHeight(60);
        FVBox18.setBorderStyle("stNone");
        FVBox18.setPaddingTop(0);
        FVBox18.setPaddingLeft(0);
        FVBox18.setPaddingRight(0);
        FVBox18.setPaddingBottom(0);
        FVBox18.setMarginTop(0);
        FVBox18.setMarginLeft(0);
        FVBox18.setMarginRight(0);
        FVBox18.setMarginBottom(0);
        FVBox18.setSpacing(5);
        FVBox18.setFlexVflex("ftFalse");
        FVBox18.setFlexHflex("ftFalse");
        FVBox18.setScrollable(false);
        FVBox18.setBoxShadowConfigHorizontalLength(10);
        FVBox18.setBoxShadowConfigVerticalLength(10);
        FVBox18.setBoxShadowConfigBlurRadius(5);
        FVBox18.setBoxShadowConfigSpreadRadius(0);
        FVBox18.setBoxShadowConfigShadowColor("clBlack");
        FVBox18.setBoxShadowConfigOpacity(75);
        FHBox36.addChildren(FVBox18);
        FVBox18.applyProperties();
    }

    public TFLabel FLabel23 = new TFLabel();

    private void init_FLabel23() {
        FLabel23.setName("FLabel23");
        FLabel23.setLeft(0);
        FLabel23.setTop(0);
        FLabel23.setWidth(13);
        FLabel23.setHeight(13);
        FLabel23.setCaption("UF");
        FLabel23.setFontColor("clWindowText");
        FLabel23.setFontSize(-11);
        FLabel23.setFontName("Tahoma");
        FLabel23.setFontStyle("[]");
        FLabel23.setVerticalAlignment("taVerticalCenter");
        FLabel23.setWordBreak(false);
        FVBox18.addChildren(FLabel23);
        FLabel23.applyProperties();
    }

    public TFCombo cbbUf = new TFCombo();

    private void init_cbbUf() {
        cbbUf.setName("cbbUf");
        cbbUf.setLeft(0);
        cbbUf.setTop(14);
        cbbUf.setWidth(145);
        cbbUf.setHeight(21);
        cbbUf.setLookupTable(tbUf);
        cbbUf.setLookupKey("UF");
        cbbUf.setLookupDesc("UF");
        cbbUf.setFlex(false);
        cbbUf.setReadOnly(true);
        cbbUf.setRequired(true);
        cbbUf.setPrompt("Selecione");
        cbbUf.setConstraintCheckWhen("cwImmediate");
        cbbUf.setConstraintCheckType("ctExpression");
        cbbUf.setConstraintFocusOnError(false);
        cbbUf.setConstraintEnableUI(true);
        cbbUf.setConstraintEnabled(false);
        cbbUf.setConstraintFormCheck(true);
        cbbUf.setClearOnDelKey(true);
        cbbUf.setUseClearButton(false);
        cbbUf.setHideClearButtonOnNullValue(false);
        cbbUf.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbUfChange(event);
            processarFlow("FrmCadastroLeadzap", "cbbUf", "OnChange");
        });
        FVBox18.addChildren(cbbUf);
        cbbUf.applyProperties();
        addValidatable(cbbUf);
    }

    public TFVBox FVBox19 = new TFVBox();

    private void init_FVBox19() {
        FVBox19.setName("FVBox19");
        FVBox19.setLeft(153);
        FVBox19.setTop(0);
        FVBox19.setWidth(185);
        FVBox19.setHeight(60);
        FVBox19.setBorderStyle("stNone");
        FVBox19.setPaddingTop(0);
        FVBox19.setPaddingLeft(0);
        FVBox19.setPaddingRight(5);
        FVBox19.setPaddingBottom(0);
        FVBox19.setMarginTop(0);
        FVBox19.setMarginLeft(0);
        FVBox19.setMarginRight(0);
        FVBox19.setMarginBottom(0);
        FVBox19.setSpacing(5);
        FVBox19.setFlexVflex("ftFalse");
        FVBox19.setFlexHflex("ftTrue");
        FVBox19.setScrollable(false);
        FVBox19.setBoxShadowConfigHorizontalLength(10);
        FVBox19.setBoxShadowConfigVerticalLength(10);
        FVBox19.setBoxShadowConfigBlurRadius(5);
        FVBox19.setBoxShadowConfigSpreadRadius(0);
        FVBox19.setBoxShadowConfigShadowColor("clBlack");
        FVBox19.setBoxShadowConfigOpacity(75);
        FHBox36.addChildren(FVBox19);
        FVBox19.applyProperties();
    }

    public TFLabel FLabel24 = new TFLabel();

    private void init_FLabel24() {
        FLabel24.setName("FLabel24");
        FLabel24.setLeft(0);
        FLabel24.setTop(0);
        FLabel24.setWidth(38);
        FLabel24.setHeight(13);
        FLabel24.setCaption("Cidades");
        FLabel24.setFontColor("clWindowText");
        FLabel24.setFontSize(-11);
        FLabel24.setFontName("Tahoma");
        FLabel24.setFontStyle("[]");
        FLabel24.setVerticalAlignment("taVerticalCenter");
        FLabel24.setWordBreak(false);
        FVBox19.addChildren(FLabel24);
        FLabel24.applyProperties();
    }

    public TFString edtCidades = new TFString();

    private void init_edtCidades() {
        edtCidades.setName("edtCidades");
        edtCidades.setLeft(0);
        edtCidades.setTop(14);
        edtCidades.setWidth(121);
        edtCidades.setHeight(24);
        edtCidades.setFlex(true);
        edtCidades.setRequired(false);
        edtCidades.setConstraintCheckWhen("cwImmediate");
        edtCidades.setConstraintCheckType("ctExpression");
        edtCidades.setConstraintFocusOnError(false);
        edtCidades.setConstraintEnableUI(true);
        edtCidades.setConstraintEnabled(false);
        edtCidades.setConstraintFormCheck(true);
        edtCidades.setCharCase("ccNormal");
        edtCidades.setPwd(false);
        edtCidades.setMaxlength(0);
        edtCidades.setFontColor("clWindowText");
        edtCidades.setFontSize(-13);
        edtCidades.setFontName("Tahoma");
        edtCidades.setFontStyle("[]");
        edtCidades.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCidadesEnter(event);
            processarFlow("FrmCadastroLeadzap", "edtCidades", "OnEnter");
        });
        edtCidades.setSaveLiteralCharacter(false);
        edtCidades.applyProperties();
        FVBox19.addChildren(edtCidades);
        addValidatable(edtCidades);
    }

    public TFVBox FVBox20 = new TFVBox();

    private void init_FVBox20() {
        FVBox20.setName("FVBox20");
        FVBox20.setLeft(338);
        FVBox20.setTop(0);
        FVBox20.setWidth(61);
        FVBox20.setHeight(65);
        FVBox20.setBorderStyle("stNone");
        FVBox20.setPaddingTop(0);
        FVBox20.setPaddingLeft(2);
        FVBox20.setPaddingRight(0);
        FVBox20.setPaddingBottom(0);
        FVBox20.setMarginTop(0);
        FVBox20.setMarginLeft(0);
        FVBox20.setMarginRight(0);
        FVBox20.setMarginBottom(0);
        FVBox20.setSpacing(1);
        FVBox20.setFlexVflex("ftFalse");
        FVBox20.setFlexHflex("ftFalse");
        FVBox20.setScrollable(false);
        FVBox20.setBoxShadowConfigHorizontalLength(10);
        FVBox20.setBoxShadowConfigVerticalLength(10);
        FVBox20.setBoxShadowConfigBlurRadius(5);
        FVBox20.setBoxShadowConfigSpreadRadius(0);
        FVBox20.setBoxShadowConfigShadowColor("clBlack");
        FVBox20.setBoxShadowConfigOpacity(75);
        FHBox36.addChildren(FVBox20);
        FVBox20.applyProperties();
    }

    public TFButton btnPesquisarCid = new TFButton();

    private void init_btnPesquisarCid() {
        btnPesquisarCid.setName("btnPesquisarCid");
        btnPesquisarCid.setLeft(0);
        btnPesquisarCid.setTop(0);
        btnPesquisarCid.setWidth(55);
        btnPesquisarCid.setHeight(58);
        btnPesquisarCid.setFontColor("clWindowText");
        btnPesquisarCid.setFontSize(-11);
        btnPesquisarCid.setFontName("Tahoma");
        btnPesquisarCid.setFontStyle("[]");
        btnPesquisarCid.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarCidClick(event);
            processarFlow("FrmCadastroLeadzap", "btnPesquisarCid", "OnClick");
        });
        btnPesquisarCid.setImageId(4600397);
        btnPesquisarCid.setColor("clBtnFace");
        btnPesquisarCid.setAccess(false);
        btnPesquisarCid.setIconReverseDirection(false);
        FVBox20.addChildren(btnPesquisarCid);
        btnPesquisarCid.applyProperties();
    }

    public TFHBox FHBox37 = new TFHBox();

    private void init_FHBox37() {
        FHBox37.setName("FHBox37");
        FHBox37.setLeft(0);
        FHBox37.setTop(73);
        FHBox37.setWidth(749);
        FHBox37.setHeight(289);
        FHBox37.setBorderStyle("stNone");
        FHBox37.setPaddingTop(10);
        FHBox37.setPaddingLeft(0);
        FHBox37.setPaddingRight(0);
        FHBox37.setPaddingBottom(0);
        FHBox37.setMarginTop(0);
        FHBox37.setMarginLeft(0);
        FHBox37.setMarginRight(0);
        FHBox37.setMarginBottom(0);
        FHBox37.setSpacing(1);
        FHBox37.setFlexVflex("ftTrue");
        FHBox37.setFlexHflex("ftTrue");
        FHBox37.setScrollable(false);
        FHBox37.setBoxShadowConfigHorizontalLength(10);
        FHBox37.setBoxShadowConfigVerticalLength(10);
        FHBox37.setBoxShadowConfigBlurRadius(5);
        FHBox37.setBoxShadowConfigSpreadRadius(0);
        FHBox37.setBoxShadowConfigShadowColor("clBlack");
        FHBox37.setBoxShadowConfigOpacity(75);
        FHBox37.setVAlign("tvTop");
        FVBox17.addChildren(FHBox37);
        FHBox37.applyProperties();
    }

    public TFGroupbox FGroupbox2 = new TFGroupbox();

    private void init_FGroupbox2() {
        FGroupbox2.setName("FGroupbox2");
        FGroupbox2.setLeft(0);
        FGroupbox2.setTop(0);
        FGroupbox2.setWidth(257);
        FGroupbox2.setHeight(283);
        FGroupbox2.setCaption("Dispon\u00EDveis");
        FGroupbox2.setFontColor("clWindowText");
        FGroupbox2.setFontSize(-11);
        FGroupbox2.setFontName("Tahoma");
        FGroupbox2.setFontStyle("[]");
        FGroupbox2.setFlexVflex("ftTrue");
        FGroupbox2.setFlexHflex("ftTrue");
        FGroupbox2.setScrollable(false);
        FGroupbox2.setClosable(false);
        FGroupbox2.setClosed(false);
        FGroupbox2.setOrient("coHorizontal");
        FGroupbox2.setStyle("grpLine");
        FGroupbox2.setHeaderImageId(0);
        FHBox37.addChildren(FGroupbox2);
        FGroupbox2.applyProperties();
    }

    public TFGrid gridCidDisp = new TFGrid();

    private void init_gridCidDisp() {
        gridCidDisp.setName("gridCidDisp");
        gridCidDisp.setLeft(12);
        gridCidDisp.setTop(19);
        gridCidDisp.setWidth(234);
        gridCidDisp.setHeight(256);
        gridCidDisp.setTable(tbCidades);
        gridCidDisp.setFlexVflex("ftTrue");
        gridCidDisp.setFlexHflex("ftTrue");
        gridCidDisp.setPagingEnabled(false);
        gridCidDisp.setFrozenColumns(0);
        gridCidDisp.setShowFooter(false);
        gridCidDisp.setShowHeader(true);
        gridCidDisp.setMultiSelection(false);
        gridCidDisp.setGroupingEnabled(false);
        gridCidDisp.setGroupingExpanded(false);
        gridCidDisp.setGroupingShowFooter(false);
        gridCidDisp.setCrosstabEnabled(false);
        gridCidDisp.setCrosstabGroupType("cgtConcat");
        gridCidDisp.setEditionEnabled(false);
        gridCidDisp.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("UF");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridCidDisp.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(40);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridCidDisp.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("COD_CIDADES");
        item2.setTitleCaption("C\u00F3d. Cidades");
        item2.setWidth(40);
        item2.setVisible(false);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridCidDisp.getColumns().add(item2);
        FGroupbox2.addChildren(gridCidDisp);
        gridCidDisp.applyProperties();
    }

    public TFVBox FVBox21 = new TFVBox();

    private void init_FVBox21() {
        FVBox21.setName("FVBox21");
        FVBox21.setLeft(257);
        FVBox21.setTop(0);
        FVBox21.setWidth(59);
        FVBox21.setHeight(283);
        FVBox21.setBorderStyle("stNone");
        FVBox21.setPaddingTop(0);
        FVBox21.setPaddingLeft(5);
        FVBox21.setPaddingRight(0);
        FVBox21.setPaddingBottom(0);
        FVBox21.setMarginTop(0);
        FVBox21.setMarginLeft(0);
        FVBox21.setMarginRight(0);
        FVBox21.setMarginBottom(0);
        FVBox21.setSpacing(5);
        FVBox21.setFlexVflex("ftTrue");
        FVBox21.setFlexHflex("ftFalse");
        FVBox21.setScrollable(false);
        FVBox21.setBoxShadowConfigHorizontalLength(10);
        FVBox21.setBoxShadowConfigVerticalLength(10);
        FVBox21.setBoxShadowConfigBlurRadius(5);
        FVBox21.setBoxShadowConfigSpreadRadius(0);
        FVBox21.setBoxShadowConfigShadowColor("clBlack");
        FVBox21.setBoxShadowConfigOpacity(75);
        FHBox37.addChildren(FVBox21);
        FVBox21.applyProperties();
    }

    public TFVBox FVBox22 = new TFVBox();

    private void init_FVBox22() {
        FVBox22.setName("FVBox22");
        FVBox22.setLeft(0);
        FVBox22.setTop(0);
        FVBox22.setWidth(37);
        FVBox22.setHeight(41);
        FVBox22.setBorderStyle("stNone");
        FVBox22.setPaddingTop(0);
        FVBox22.setPaddingLeft(0);
        FVBox22.setPaddingRight(0);
        FVBox22.setPaddingBottom(0);
        FVBox22.setMarginTop(0);
        FVBox22.setMarginLeft(0);
        FVBox22.setMarginRight(0);
        FVBox22.setMarginBottom(0);
        FVBox22.setSpacing(1);
        FVBox22.setFlexVflex("ftTrue");
        FVBox22.setFlexHflex("ftFalse");
        FVBox22.setScrollable(false);
        FVBox22.setBoxShadowConfigHorizontalLength(10);
        FVBox22.setBoxShadowConfigVerticalLength(10);
        FVBox22.setBoxShadowConfigBlurRadius(5);
        FVBox22.setBoxShadowConfigSpreadRadius(0);
        FVBox22.setBoxShadowConfigShadowColor("clBlack");
        FVBox22.setBoxShadowConfigOpacity(75);
        FVBox21.addChildren(FVBox22);
        FVBox22.applyProperties();
    }

    public TFButton btnAddCidades = new TFButton();

    private void init_btnAddCidades() {
        btnAddCidades.setName("btnAddCidades");
        btnAddCidades.setLeft(0);
        btnAddCidades.setTop(42);
        btnAddCidades.setWidth(47);
        btnAddCidades.setHeight(41);
        btnAddCidades.setFontColor("clWindowText");
        btnAddCidades.setFontSize(-11);
        btnAddCidades.setFontName("Tahoma");
        btnAddCidades.setFontStyle("[]");
        btnAddCidades.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAddCidadesClick(event);
            processarFlow("FrmCadastroLeadzap", "btnAddCidades", "OnClick");
        });
        btnAddCidades.setImageId(430034);
        btnAddCidades.setColor("clBtnFace");
        btnAddCidades.setAccess(false);
        btnAddCidades.setIconReverseDirection(false);
        FVBox21.addChildren(btnAddCidades);
        btnAddCidades.applyProperties();
    }

    public TFButton btnDelCidades = new TFButton();

    private void init_btnDelCidades() {
        btnDelCidades.setName("btnDelCidades");
        btnDelCidades.setLeft(0);
        btnDelCidades.setTop(84);
        btnDelCidades.setWidth(47);
        btnDelCidades.setHeight(41);
        btnDelCidades.setFontColor("clWindowText");
        btnDelCidades.setFontSize(-11);
        btnDelCidades.setFontName("Tahoma");
        btnDelCidades.setFontStyle("[]");
        btnDelCidades.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnDelCidadesClick(event);
            processarFlow("FrmCadastroLeadzap", "btnDelCidades", "OnClick");
        });
        btnDelCidades.setImageId(430033);
        btnDelCidades.setColor("clBtnFace");
        btnDelCidades.setAccess(false);
        btnDelCidades.setIconReverseDirection(false);
        FVBox21.addChildren(btnDelCidades);
        btnDelCidades.applyProperties();
    }

    public TFVBox FVBox23 = new TFVBox();

    private void init_FVBox23() {
        FVBox23.setName("FVBox23");
        FVBox23.setLeft(0);
        FVBox23.setTop(126);
        FVBox23.setWidth(37);
        FVBox23.setHeight(41);
        FVBox23.setBorderStyle("stNone");
        FVBox23.setPaddingTop(0);
        FVBox23.setPaddingLeft(0);
        FVBox23.setPaddingRight(0);
        FVBox23.setPaddingBottom(0);
        FVBox23.setMarginTop(0);
        FVBox23.setMarginLeft(0);
        FVBox23.setMarginRight(0);
        FVBox23.setMarginBottom(0);
        FVBox23.setSpacing(1);
        FVBox23.setFlexVflex("ftTrue");
        FVBox23.setFlexHflex("ftFalse");
        FVBox23.setScrollable(false);
        FVBox23.setBoxShadowConfigHorizontalLength(10);
        FVBox23.setBoxShadowConfigVerticalLength(10);
        FVBox23.setBoxShadowConfigBlurRadius(5);
        FVBox23.setBoxShadowConfigSpreadRadius(0);
        FVBox23.setBoxShadowConfigShadowColor("clBlack");
        FVBox23.setBoxShadowConfigOpacity(75);
        FVBox21.addChildren(FVBox23);
        FVBox23.applyProperties();
    }

    public TFGroupbox FGroupbox3 = new TFGroupbox();

    private void init_FGroupbox3() {
        FGroupbox3.setName("FGroupbox3");
        FGroupbox3.setLeft(316);
        FGroupbox3.setTop(0);
        FGroupbox3.setWidth(257);
        FGroupbox3.setHeight(283);
        FGroupbox3.setCaption("Selecionados");
        FGroupbox3.setFontColor("clWindowText");
        FGroupbox3.setFontSize(-11);
        FGroupbox3.setFontName("Tahoma");
        FGroupbox3.setFontStyle("[]");
        FGroupbox3.setFlexVflex("ftTrue");
        FGroupbox3.setFlexHflex("ftTrue");
        FGroupbox3.setScrollable(false);
        FGroupbox3.setClosable(false);
        FGroupbox3.setClosed(false);
        FGroupbox3.setOrient("coHorizontal");
        FGroupbox3.setStyle("grpLine");
        FGroupbox3.setHeaderImageId(0);
        FHBox37.addChildren(FGroupbox3);
        FGroupbox3.applyProperties();
    }

    public TFGrid gridCidSel = new TFGrid();

    private void init_gridCidSel() {
        gridCidSel.setName("gridCidSel");
        gridCidSel.setLeft(12);
        gridCidSel.setTop(19);
        gridCidSel.setWidth(234);
        gridCidSel.setHeight(256);
        gridCidSel.setTable(tbCadastroWhatsappCidades);
        gridCidSel.setFlexVflex("ftTrue");
        gridCidSel.setFlexHflex("ftTrue");
        gridCidSel.setPagingEnabled(false);
        gridCidSel.setFrozenColumns(0);
        gridCidSel.setShowFooter(false);
        gridCidSel.setShowHeader(true);
        gridCidSel.setMultiSelection(false);
        gridCidSel.setGroupingEnabled(false);
        gridCidSel.setGroupingExpanded(false);
        gridCidSel.setGroupingShowFooter(false);
        gridCidSel.setCrosstabEnabled(false);
        gridCidSel.setCrosstabGroupType("cgtConcat");
        gridCidSel.setEditionEnabled(false);
        gridCidSel.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("UF");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridCidSel.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(40);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridCidSel.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("ID_WHATS_CIDADE");
        item2.setTitleCaption("Id. Whats Cidade");
        item2.setWidth(40);
        item2.setVisible(false);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridCidSel.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("ID_CELULAR");
        item3.setTitleCaption("Id. Celular");
        item3.setWidth(40);
        item3.setVisible(false);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridCidSel.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("COD_CIDADES");
        item4.setTitleCaption("C\u00F3d. Cidades");
        item4.setWidth(40);
        item4.setVisible(false);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridCidSel.getColumns().add(item4);
        FGroupbox3.addChildren(gridCidSel);
        gridCidSel.applyProperties();
    }

    public TFSchema scCrmCadastroWhatsapp;

    private void init_scCrmCadastroWhatsapp() {
        scCrmCadastroWhatsapp = rn.scCrmCadastroWhatsapp;
        scCrmCadastroWhatsapp.setName("scCrmCadastroWhatsapp");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbCadastroWhatsapp);
        scCrmCadastroWhatsapp.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbWhatsappEmpresa);
        scCrmCadastroWhatsapp.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbCadastroWhatsappCidades);
        scCrmCadastroWhatsapp.getTables().add(item2);
        scCrmCadastroWhatsapp.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FrmCadastroLeadzapkeyActionPesquisar(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionIncluir(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionAlterar(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionExcluir(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionSalvar(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionCancelar(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionAnterior(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionProximo(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionAceitar(final Event<Object> event);

    public abstract void FrmCadastroLeadzapkeyActionSalvarContinuar(final Event<Object> event);

    public abstract void pgPrincipalChange(final Event<Object> event);

    public abstract void efDescricaoEnter(final Event<Object> event);

    public abstract void gridPrincipalClickImageAlterar(final Event<Object> event);

    public abstract void gridPrincipalClickImageDelete(final Event<Object> event);

    public void btnTestarClick(final Event<Object> event) {
        if (btnTestar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnTestar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbbLeadzapReceptivoClearClick(final Event<Object> event);

    public abstract void cbbTipoApiChange(final Event<Object> event);

    public void btnTestarConexaoZAPIClick(final Event<Object> event) {
        if (btnTestarConexaoZAPI.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnTestarConexaoZAPI");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCadastrarClick(final Event<Object> event) {
        if (btnCadastrar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCadastrar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSincronizarClick(final Event<Object> event) {
        if (btnSincronizar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSincronizar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnRefreshSincClick(final Event<Object> event) {
        if (btnRefreshSinc.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnRefreshSinc");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnUploadImageClick(final Event<Object> event) {
        if (btnUploadImage.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnUploadImage");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnLimparImagemClick(final Event<Object> event) {
        if (btnLimparImagem.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimparImagem");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAtualizarLogClick(final Event<Object> event) {
        if (btnAtualizarLog.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAtualizarLog");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cbbUfChange(final Event<Object> event);

    public abstract void edtCidadesEnter(final Event<Object> event);

    public void btnPesquisarCidClick(final Event<Object> event) {
        if (btnPesquisarCid.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisarCid");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAddCidadesClick(final Event<Object> event) {
        if (btnAddCidades.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAddCidades");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnDelCidadesClick(final Event<Object> event) {
        if (btnDelCidades.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnDelCidades");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnConsultarClick(final Event<Object> event) {
        if (btnConsultar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConsultar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnFiltroAvancadoClick(final Event<Object> event) {
        if (btnFiltroAvancado.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnFiltroAvancado");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarContinuarClick(final Event<Object> event) {
        if (btnSalvarContinuar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarContinuar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAnteriorClick(final Event<Object> event) {
        if (btnAnterior.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAnterior");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnProximoClick(final Event<Object> event) {
        if (btnProximo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnProximo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnMaisClick(final Event<Object> event) {
        if (btnMais.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnMais");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void menuItemAbreTabelaAuxClick(final Event<Object> event);

    public abstract void menuHabilitaNavegacaoClick(final Event<Object> event);

    public abstract void menuSelecaoMultiplaClick(final Event<Object> event);

    public abstract void menuItemConfgGridClick(final Event<Object> event);

    public abstract void menuItemExportPdfClick(final Event<Object> event);

    public abstract void menuItemExportExcelClick(final Event<Object> event);

    public abstract void menuItemHelpClick(final Event<Object> event);

    public abstract void tbCadastroWhatsappAfterScroll(final Event<Object> event);

    public abstract void tbEmpresasBeforeOpen(final Event<Object> event);

    public abstract void tbCidadesMaxRow(final Event<Object> event);

    public abstract void tbCadastroWhatsappCidadesMaxRow(final Event<Object> event);

}