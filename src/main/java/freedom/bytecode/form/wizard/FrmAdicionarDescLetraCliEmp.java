package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAdicionarDescLetraCliEmp extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AdicionarDescLetraCliEmpRNA rn = null;

    public FrmAdicionarDescLetraCliEmp() {
        try {
            rn = (freedom.bytecode.rn.AdicionarDescLetraCliEmpRNA) getRN(freedom.bytecode.rn.wizard.AdicionarDescLetraCliEmpRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbItensPerDescFlag();
        init_tbEmpresasFiliaisSel();
        init_vBoxPrincipal();
        init_FHBox1();
        init_hBoxLinha01();
        init_FHBox2();
        init_btnVoltar();
        init_FHBox3();
        init_btnSalvar();
        init_FHBox4();
        init_FHBox5();
        init_hBoxLinha02();
        init_FHBox7();
        init_FVBox1();
        init_lblLetraDeDesconto();
        init_cboLetraDeDesconto();
        init_FHBox8();
        init_FVBox2();
        init_lblDecimalLetraEmpresa();
        init_edtDescontoClienteEmpresa();
        init_FHBox9();
        init_vBoxEmpresaLetraDesconto();
        init_lblEmpresaLetraDesconto();
        init_cboEmpresaLetraDesconto();
        init_FHBox10();
        init_FHBox6();
        init_FrmAdicionarDescLetraCliEmp();
    }

    public ITENS_PER_DESC_FLAG tbItensPerDescFlag;

    private void init_tbItensPerDescFlag() {
        tbItensPerDescFlag = rn.tbItensPerDescFlag;
        tbItensPerDescFlag.setName("tbItensPerDescFlag");
        tbItensPerDescFlag.setMaxRowCount(200);
        tbItensPerDescFlag.setWKey("5300640;53001");
        tbItensPerDescFlag.setRatioBatchSize(20);
        getTables().put(tbItensPerDescFlag, "tbItensPerDescFlag");
        tbItensPerDescFlag.applyProperties();
    }

    public EMPRESAS_FILIAIS_SEL tbEmpresasFiliaisSel;

    private void init_tbEmpresasFiliaisSel() {
        tbEmpresasFiliaisSel = rn.tbEmpresasFiliaisSel;
        tbEmpresasFiliaisSel.setName("tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.setMaxRowCount(200);
        tbEmpresasFiliaisSel.setWKey("5300640;53002");
        tbEmpresasFiliaisSel.setRatioBatchSize(20);
        getTables().put(tbEmpresasFiliaisSel, "tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.applyProperties();
    }

    protected TFForm FrmAdicionarDescLetraCliEmp = this;
    private void init_FrmAdicionarDescLetraCliEmp() {
        FrmAdicionarDescLetraCliEmp.setName("FrmAdicionarDescLetraCliEmp");
        FrmAdicionarDescLetraCliEmp.setCaption("Adicionar desconto por letra para o cliente empres");
        FrmAdicionarDescLetraCliEmp.setClientHeight(152);
        FrmAdicionarDescLetraCliEmp.setClientWidth(504);
        FrmAdicionarDescLetraCliEmp.setColor("clBtnFace");
        FrmAdicionarDescLetraCliEmp.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAdicionarDescLetraCliEmp", "FrmAdicionarDescLetraCliEmp", "OnCreate");
        });
        FrmAdicionarDescLetraCliEmp.setWOrigem("EhMain");
        FrmAdicionarDescLetraCliEmp.setWKey("5300640");
        FrmAdicionarDescLetraCliEmp.setSpacing(0);
        FrmAdicionarDescLetraCliEmp.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(504);
        vBoxPrincipal.setHeight(152);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftFalse");
        vBoxPrincipal.setFlexHflex("ftFalse");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmAdicionarDescLetraCliEmp.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(490);
        FHBox1.setHeight(5);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox hBoxLinha01 = new TFHBox();

    private void init_hBoxLinha01() {
        hBoxLinha01.setName("hBoxLinha01");
        hBoxLinha01.setLeft(0);
        hBoxLinha01.setTop(6);
        hBoxLinha01.setWidth(490);
        hBoxLinha01.setHeight(63);
        hBoxLinha01.setBorderStyle("stNone");
        hBoxLinha01.setPaddingTop(0);
        hBoxLinha01.setPaddingLeft(0);
        hBoxLinha01.setPaddingRight(0);
        hBoxLinha01.setPaddingBottom(0);
        hBoxLinha01.setMarginTop(0);
        hBoxLinha01.setMarginLeft(0);
        hBoxLinha01.setMarginRight(0);
        hBoxLinha01.setMarginBottom(0);
        hBoxLinha01.setSpacing(1);
        hBoxLinha01.setFlexVflex("ftMin");
        hBoxLinha01.setFlexHflex("ftTrue");
        hBoxLinha01.setScrollable(false);
        hBoxLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha01.setBoxShadowConfigOpacity(75);
        hBoxLinha01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha01);
        hBoxLinha01.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(5);
        FHBox2.setHeight(20);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(5);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(57);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAdicionarDescLetraCliEmp", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(70);
        FHBox3.setTop(0);
        FHBox3.setWidth(5);
        FHBox3.setHeight(20);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(75);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(57);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmAdicionarDescLetraCliEmp", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(140);
        FHBox4.setTop(0);
        FHBox4.setWidth(5);
        FHBox4.setHeight(20);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(70);
        FHBox5.setWidth(490);
        FHBox5.setHeight(5);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox hBoxLinha02 = new TFHBox();

    private void init_hBoxLinha02() {
        hBoxLinha02.setName("hBoxLinha02");
        hBoxLinha02.setLeft(0);
        hBoxLinha02.setTop(76);
        hBoxLinha02.setWidth(490);
        hBoxLinha02.setHeight(60);
        hBoxLinha02.setBorderStyle("stNone");
        hBoxLinha02.setPaddingTop(0);
        hBoxLinha02.setPaddingLeft(0);
        hBoxLinha02.setPaddingRight(0);
        hBoxLinha02.setPaddingBottom(0);
        hBoxLinha02.setMarginTop(0);
        hBoxLinha02.setMarginLeft(0);
        hBoxLinha02.setMarginRight(0);
        hBoxLinha02.setMarginBottom(0);
        hBoxLinha02.setSpacing(1);
        hBoxLinha02.setFlexVflex("ftMin");
        hBoxLinha02.setFlexHflex("ftTrue");
        hBoxLinha02.setScrollable(false);
        hBoxLinha02.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha02.setBoxShadowConfigVerticalLength(10);
        hBoxLinha02.setBoxShadowConfigBlurRadius(5);
        hBoxLinha02.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha02.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha02.setBoxShadowConfigOpacity(75);
        hBoxLinha02.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha02);
        hBoxLinha02.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(5);
        FHBox7.setHeight(20);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(5);
        FVBox1.setTop(0);
        FVBox1.setWidth(120);
        FVBox1.setHeight(50);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftMin");
        FVBox1.setFlexHflex("ftMin");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        hBoxLinha02.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel lblLetraDeDesconto = new TFLabel();

    private void init_lblLetraDeDesconto() {
        lblLetraDeDesconto.setName("lblLetraDeDesconto");
        lblLetraDeDesconto.setLeft(0);
        lblLetraDeDesconto.setTop(0);
        lblLetraDeDesconto.setWidth(102);
        lblLetraDeDesconto.setHeight(13);
        lblLetraDeDesconto.setAlign("alTop");
        lblLetraDeDesconto.setCaption("Letra de desconto");
        lblLetraDeDesconto.setFontColor("clWindowText");
        lblLetraDeDesconto.setFontSize(-11);
        lblLetraDeDesconto.setFontName("Tahoma");
        lblLetraDeDesconto.setFontStyle("[fsBold]");
        lblLetraDeDesconto.setVerticalAlignment("taVerticalCenter");
        lblLetraDeDesconto.setWordBreak(false);
        FVBox1.addChildren(lblLetraDeDesconto);
        lblLetraDeDesconto.applyProperties();
    }

    public TFCombo cboLetraDeDesconto = new TFCombo();

    private void init_cboLetraDeDesconto() {
        cboLetraDeDesconto.setName("cboLetraDeDesconto");
        cboLetraDeDesconto.setLeft(0);
        cboLetraDeDesconto.setTop(14);
        cboLetraDeDesconto.setWidth(100);
        cboLetraDeDesconto.setHeight(21);
        cboLetraDeDesconto.setHint("Letra de desconto");
        cboLetraDeDesconto.setLookupTable(tbItensPerDescFlag);
        cboLetraDeDesconto.setLookupKey("COD_MAX_DESC");
        cboLetraDeDesconto.setLookupDesc("COD_LETRA_DESCONTO");
        cboLetraDeDesconto.setFlex(false);
        cboLetraDeDesconto.setHelpCaption("Letra de desconto");
        cboLetraDeDesconto.setReadOnly(true);
        cboLetraDeDesconto.setRequired(true);
        cboLetraDeDesconto.setPrompt("Letra de desconto");
        cboLetraDeDesconto.setConstraintCheckWhen("cwImmediate");
        cboLetraDeDesconto.setConstraintCheckType("ctExpression");
        cboLetraDeDesconto.setConstraintFocusOnError(false);
        cboLetraDeDesconto.setConstraintEnableUI(true);
        cboLetraDeDesconto.setConstraintEnabled(false);
        cboLetraDeDesconto.setConstraintFormCheck(true);
        cboLetraDeDesconto.setClearOnDelKey(true);
        cboLetraDeDesconto.setUseClearButton(false);
        cboLetraDeDesconto.setHideClearButtonOnNullValue(false);
        cboLetraDeDesconto.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboLetraDeDescontoChange(event);
            processarFlow("FrmAdicionarDescLetraCliEmp", "cboLetraDeDesconto", "OnChange");
        });
        cboLetraDeDesconto.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboLetraDeDescontoEnter(event);
            processarFlow("FrmAdicionarDescLetraCliEmp", "cboLetraDeDesconto", "OnEnter");
        });
        FVBox1.addChildren(cboLetraDeDesconto);
        cboLetraDeDesconto.applyProperties();
        addValidatable(cboLetraDeDesconto);
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(125);
        FHBox8.setTop(0);
        FHBox8.setWidth(5);
        FHBox8.setHeight(20);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(130);
        FVBox2.setTop(0);
        FVBox2.setWidth(140);
        FVBox2.setHeight(50);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftMin");
        FVBox2.setFlexHflex("ftMin");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hBoxLinha02.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel lblDecimalLetraEmpresa = new TFLabel();

    private void init_lblDecimalLetraEmpresa() {
        lblDecimalLetraEmpresa.setName("lblDecimalLetraEmpresa");
        lblDecimalLetraEmpresa.setLeft(0);
        lblDecimalLetraEmpresa.setTop(0);
        lblDecimalLetraEmpresa.setWidth(123);
        lblDecimalLetraEmpresa.setHeight(13);
        lblDecimalLetraEmpresa.setCaption("Desconto para cliente");
        lblDecimalLetraEmpresa.setFontColor("clWindowText");
        lblDecimalLetraEmpresa.setFontSize(-11);
        lblDecimalLetraEmpresa.setFontName("Tahoma");
        lblDecimalLetraEmpresa.setFontStyle("[fsBold]");
        lblDecimalLetraEmpresa.setVerticalAlignment("taVerticalCenter");
        lblDecimalLetraEmpresa.setWordBreak(false);
        FVBox2.addChildren(lblDecimalLetraEmpresa);
        lblDecimalLetraEmpresa.applyProperties();
    }

    public TFDecimal edtDescontoClienteEmpresa = new TFDecimal();

    private void init_edtDescontoClienteEmpresa() {
        edtDescontoClienteEmpresa.setName("edtDescontoClienteEmpresa");
        edtDescontoClienteEmpresa.setLeft(0);
        edtDescontoClienteEmpresa.setTop(14);
        edtDescontoClienteEmpresa.setWidth(100);
        edtDescontoClienteEmpresa.setHeight(24);
        edtDescontoClienteEmpresa.setHint("Desconto para cliente");
        edtDescontoClienteEmpresa.setHelpCaption("Desconto para cliente");
        edtDescontoClienteEmpresa.setFlex(false);
        edtDescontoClienteEmpresa.setRequired(true);
        edtDescontoClienteEmpresa.setPrompt("Desconto para cliente");
        edtDescontoClienteEmpresa.setConstraintCheckWhen("cwImmediate");
        edtDescontoClienteEmpresa.setConstraintCheckType("ctExpression");
        edtDescontoClienteEmpresa.setConstraintFocusOnError(false);
        edtDescontoClienteEmpresa.setConstraintEnableUI(true);
        edtDescontoClienteEmpresa.setConstraintEnabled(false);
        edtDescontoClienteEmpresa.setConstraintFormCheck(true);
        edtDescontoClienteEmpresa.setMaxlength(0);
        edtDescontoClienteEmpresa.setPrecision(0);
        edtDescontoClienteEmpresa.setFontColor("clWindowText");
        edtDescontoClienteEmpresa.setFontSize(-13);
        edtDescontoClienteEmpresa.setFontName("Tahoma");
        edtDescontoClienteEmpresa.setFontStyle("[]");
        edtDescontoClienteEmpresa.setAlignment("taRightJustify");
        edtDescontoClienteEmpresa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtDescontoClienteEmpresaEnter(event);
            processarFlow("FrmAdicionarDescLetraCliEmp", "edtDescontoClienteEmpresa", "OnEnter");
        });
        FVBox2.addChildren(edtDescontoClienteEmpresa);
        edtDescontoClienteEmpresa.applyProperties();
        addValidatable(edtDescontoClienteEmpresa);
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(270);
        FHBox9.setTop(0);
        FHBox9.setWidth(5);
        FHBox9.setHeight(20);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFVBox vBoxEmpresaLetraDesconto = new TFVBox();

    private void init_vBoxEmpresaLetraDesconto() {
        vBoxEmpresaLetraDesconto.setName("vBoxEmpresaLetraDesconto");
        vBoxEmpresaLetraDesconto.setLeft(275);
        vBoxEmpresaLetraDesconto.setTop(0);
        vBoxEmpresaLetraDesconto.setWidth(200);
        vBoxEmpresaLetraDesconto.setHeight(50);
        vBoxEmpresaLetraDesconto.setBorderStyle("stNone");
        vBoxEmpresaLetraDesconto.setPaddingTop(0);
        vBoxEmpresaLetraDesconto.setPaddingLeft(0);
        vBoxEmpresaLetraDesconto.setPaddingRight(0);
        vBoxEmpresaLetraDesconto.setPaddingBottom(0);
        vBoxEmpresaLetraDesconto.setMarginTop(0);
        vBoxEmpresaLetraDesconto.setMarginLeft(0);
        vBoxEmpresaLetraDesconto.setMarginRight(0);
        vBoxEmpresaLetraDesconto.setMarginBottom(0);
        vBoxEmpresaLetraDesconto.setSpacing(1);
        vBoxEmpresaLetraDesconto.setFlexVflex("ftMin");
        vBoxEmpresaLetraDesconto.setFlexHflex("ftFalse");
        vBoxEmpresaLetraDesconto.setScrollable(false);
        vBoxEmpresaLetraDesconto.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresaLetraDesconto.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresaLetraDesconto.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresaLetraDesconto.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresaLetraDesconto.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresaLetraDesconto.setBoxShadowConfigOpacity(75);
        hBoxLinha02.addChildren(vBoxEmpresaLetraDesconto);
        vBoxEmpresaLetraDesconto.applyProperties();
    }

    public TFLabel lblEmpresaLetraDesconto = new TFLabel();

    private void init_lblEmpresaLetraDesconto() {
        lblEmpresaLetraDesconto.setName("lblEmpresaLetraDesconto");
        lblEmpresaLetraDesconto.setLeft(0);
        lblEmpresaLetraDesconto.setTop(0);
        lblEmpresaLetraDesconto.setWidth(49);
        lblEmpresaLetraDesconto.setHeight(13);
        lblEmpresaLetraDesconto.setCaption("Empresa");
        lblEmpresaLetraDesconto.setFontColor("clWindowText");
        lblEmpresaLetraDesconto.setFontSize(-11);
        lblEmpresaLetraDesconto.setFontName("Tahoma");
        lblEmpresaLetraDesconto.setFontStyle("[fsBold]");
        lblEmpresaLetraDesconto.setVerticalAlignment("taVerticalCenter");
        lblEmpresaLetraDesconto.setWordBreak(false);
        vBoxEmpresaLetraDesconto.addChildren(lblEmpresaLetraDesconto);
        lblEmpresaLetraDesconto.applyProperties();
    }

    public TFCombo cboEmpresaLetraDesconto = new TFCombo();

    private void init_cboEmpresaLetraDesconto() {
        cboEmpresaLetraDesconto.setName("cboEmpresaLetraDesconto");
        cboEmpresaLetraDesconto.setLeft(0);
        cboEmpresaLetraDesconto.setTop(14);
        cboEmpresaLetraDesconto.setWidth(145);
        cboEmpresaLetraDesconto.setHeight(21);
        cboEmpresaLetraDesconto.setHint("Empresa");
        cboEmpresaLetraDesconto.setLookupTable(tbEmpresasFiliaisSel);
        cboEmpresaLetraDesconto.setLookupKey("CODIGODAEMPRESA");
        cboEmpresaLetraDesconto.setLookupDesc("NOMEECODIGODAEMPRESA");
        cboEmpresaLetraDesconto.setFlex(true);
        cboEmpresaLetraDesconto.setHelpCaption("Empresa");
        cboEmpresaLetraDesconto.setReadOnly(true);
        cboEmpresaLetraDesconto.setRequired(true);
        cboEmpresaLetraDesconto.setPrompt("Empresa");
        cboEmpresaLetraDesconto.setConstraintCheckWhen("cwImmediate");
        cboEmpresaLetraDesconto.setConstraintCheckType("ctExpression");
        cboEmpresaLetraDesconto.setConstraintFocusOnError(false);
        cboEmpresaLetraDesconto.setConstraintEnableUI(true);
        cboEmpresaLetraDesconto.setConstraintEnabled(false);
        cboEmpresaLetraDesconto.setConstraintFormCheck(true);
        cboEmpresaLetraDesconto.setClearOnDelKey(true);
        cboEmpresaLetraDesconto.setUseClearButton(false);
        cboEmpresaLetraDesconto.setHideClearButtonOnNullValue(false);
        cboEmpresaLetraDesconto.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaLetraDescontoEnter(event);
            processarFlow("FrmAdicionarDescLetraCliEmp", "cboEmpresaLetraDesconto", "OnEnter");
        });
        vBoxEmpresaLetraDesconto.addChildren(cboEmpresaLetraDesconto);
        cboEmpresaLetraDesconto.applyProperties();
        addValidatable(cboEmpresaLetraDesconto);
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(475);
        FHBox10.setTop(0);
        FHBox10.setWidth(5);
        FHBox10.setHeight(20);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(137);
        FHBox6.setWidth(490);
        FHBox6.setHeight(5);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cboLetraDeDescontoChange(final Event<Object> event);

    public abstract void cboLetraDeDescontoEnter(final Event<Object> event);

    public abstract void edtDescontoClienteEmpresaEnter(final Event<Object> event);

    public abstract void cboEmpresaLetraDescontoEnter(final Event<Object> event);

}