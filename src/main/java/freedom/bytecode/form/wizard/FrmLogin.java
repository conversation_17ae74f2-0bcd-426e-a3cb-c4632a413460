package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmLogin extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.LoginRNA rn = null;

    public FrmLogin() {
        try {
            rn = (freedom.bytecode.rn.LoginRNA) getRN(freedom.bytecode.rn.wizard.LoginRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbSchema();
        init_tbSchemaUser();
        init_tbUsuarioLogado();
        init_tbTables();
        init_tbServidor();
        init_tbSysServidor();
        init_tbServidorUser();
        init_tbSysServidorUser();
        init_imageList();
        init_logo();
        init_FHBox1();
        init_FHBox2();
        init_logoNBS();
        init_FGridPanelLabel();
        init_lblSchema();
        init_lblUsuario();
        init_lblSenha();
        init_lblTema();
        init_cboTema();
        init_cboSchema();
        init_edtStringUsuario();
        init_edtStringSenha();
        init_chkLembrarSenha();
        init_btnLogin();
        init_FHBox6();
        init_FHBox3();
        init_FHBox4();
        init_lblVersaoSistema();
        init_FVBox1();
        init_FHBox5();
        init_FLabel5();
        init_FVBox2();
        init_sc();
        init_FrmLogin();
    }

    public NBS_SCHEMA tbSchema;

    private void init_tbSchema() {
        tbSchema = rn.tbSchema;
        tbSchema.setName("tbSchema");
        tbSchema.setMaxRowCount(200);
        tbSchema.setWKey("29001;70001");
        tbSchema.setRatioBatchSize(20);
        getTables().put(tbSchema, "tbSchema");
        tbSchema.applyProperties();
    }

    public NBS_SCHEMA_USER tbSchemaUser;

    private void init_tbSchemaUser() {
        tbSchemaUser = rn.tbSchemaUser;
        tbSchemaUser.setName("tbSchemaUser");
        tbSchemaUser.setMaxRowCount(200);
        tbSchemaUser.setWKey("29001;70002");
        tbSchemaUser.setRatioBatchSize(20);
        getTables().put(tbSchemaUser, "tbSchemaUser");
        tbSchemaUser.applyProperties();
    }

    public USUARIO_LOGADO tbUsuarioLogado;

    private void init_tbUsuarioLogado() {
        tbUsuarioLogado = rn.tbUsuarioLogado;
        tbUsuarioLogado.setName("tbUsuarioLogado");
        tbUsuarioLogado.setMaxRowCount(200);
        tbUsuarioLogado.setWKey("29001;70003");
        tbUsuarioLogado.setRatioBatchSize(20);
        getTables().put(tbUsuarioLogado, "tbUsuarioLogado");
        tbUsuarioLogado.applyProperties();
    }

    public ALL_TABLES tbTables;

    private void init_tbTables() {
        tbTables = rn.tbTables;
        tbTables.setName("tbTables");
        tbTables.setMaxRowCount(200);
        tbTables.setWKey("29001;70004");
        tbTables.setRatioBatchSize(20);
        getTables().put(tbTables, "tbTables");
        tbTables.applyProperties();
    }

    public NBS_SERVIDOR tbServidor;

    private void init_tbServidor() {
        tbServidor = rn.tbServidor;
        tbServidor.setName("tbServidor");
        tbServidor.setMaxRowCount(200);
        tbServidor.setWKey("29001;70005");
        tbServidor.setRatioBatchSize(20);
        getTables().put(tbServidor, "tbServidor");
        tbServidor.applyProperties();
    }

    public SYS_NBS_SERVIDOR tbSysServidor;

    private void init_tbSysServidor() {
        tbSysServidor = rn.tbSysServidor;
        tbSysServidor.setName("tbSysServidor");
        tbSysServidor.setMaxRowCount(200);
        tbSysServidor.setWKey("29001;70006");
        tbSysServidor.setRatioBatchSize(20);
        getTables().put(tbSysServidor, "tbSysServidor");
        tbSysServidor.applyProperties();
    }

    public NBS_SERVIDOR_USER tbServidorUser;

    private void init_tbServidorUser() {
        tbServidorUser = rn.tbServidorUser;
        tbServidorUser.setName("tbServidorUser");
        tbServidorUser.setMaxRowCount(200);
        tbServidorUser.setWKey("29001;70007");
        tbServidorUser.setRatioBatchSize(20);
        getTables().put(tbServidorUser, "tbServidorUser");
        tbServidorUser.applyProperties();
    }

    public SYS_NBS_SERVIDOR_USER tbSysServidorUser;

    private void init_tbSysServidorUser() {
        tbSysServidorUser = rn.tbSysServidorUser;
        tbSysServidorUser.setName("tbSysServidorUser");
        tbSysServidorUser.setMaxRowCount(200);
        tbSysServidorUser.setWKey("29001;70008");
        tbSysServidorUser.setRatioBatchSize(20);
        getTables().put(tbSysServidorUser, "tbSysServidorUser");
        tbSysServidorUser.applyProperties();
    }

    public TFPopupMenu imageList = new TFPopupMenu();

    private void init_imageList() {
        imageList.setName("imageList");
        FrmLogin.addChildren(imageList);
        imageList.applyProperties();
    }

    public TFMenuItem logo = new TFMenuItem();

    private void init_logo() {
        logo.setName("logo");
        logo.setCaption("logo");
        logo.setImageIndex(7000195);
        logo.setAccess(false);
        logo.setCheckmark(false);
        imageList.addChildren(logo);
        logo.applyProperties();
    }

    protected TFForm FrmLogin = this;
    private void init_FrmLogin() {
        FrmLogin.setName("FrmLogin");
        FrmLogin.setCaption("Form Login");
        FrmLogin.setClientHeight(430);
        FrmLogin.setClientWidth(974);
        FrmLogin.setColor("clBtnFace");
        FrmLogin.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmLogin", "FrmLogin", "OnCreate");
        });
        FrmLogin.setWOrigem("EhMain");
        FrmLogin.setWKey("29001");
        FrmLogin.setSpacing(0);
        FrmLogin.setBackgroundImage("images/CrmParts.jpg");
        FrmLogin.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(974);
        FHBox1.setHeight(33);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FrmLogin.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(32);
        FHBox2.setWidth(966);
        FHBox2.setHeight(367);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(10);
        FHBox2.setPaddingRight(15);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FrmLogin.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFImage logoNBS = new TFImage();

    private void init_logoNBS() {
        logoNBS.setName("logoNBS");
        logoNBS.setLeft(0);
        logoNBS.setTop(0);
        logoNBS.setWidth(120);
        logoNBS.setHeight(60);
        logoNBS.setBoxSize(0);
        logoNBS.setGrayScaleOnDisable(false);
        logoNBS.setFlexVflex("ftFalse");
        logoNBS.setFlexHflex("ftFalse");
        logoNBS.setImageId(0);
        FHBox2.addChildren(logoNBS);
        logoNBS.applyProperties();
    }

    public TFGridPanel FGridPanelLabel = new TFGridPanel();

    private void init_FGridPanelLabel() {
        FGridPanelLabel.setName("FGridPanelLabel");
        FGridPanelLabel.setLeft(120);
        FGridPanelLabel.setTop(0);
        FGridPanelLabel.setWidth(693);
        FGridPanelLabel.setHeight(197);
        FGridPanelLabel.setAlign("alClient");
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setValue(100.000000000000000000);
        FGridPanelLabel.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setSizeStyle("ssAbsolute");
        item1.setValue(205.000000000000000000);
        FGridPanelLabel.getColumnCollection().add(item1);
        TFGridPanelColumn item2 = new TFGridPanelColumn();
        item2.setSizeStyle("ssAbsolute");
        item2.setValue(30.000000000000000000);
        FGridPanelLabel.getColumnCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(1);
        item3.setControl("cboTema");
        item3.setRow(0);
        FGridPanelLabel.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(1);
        item4.setControl("cboSchema");
        item4.setRow(1);
        FGridPanelLabel.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(1);
        item5.setControl("edtStringUsuario");
        item5.setRow(2);
        FGridPanelLabel.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(1);
        item6.setControl("edtStringSenha");
        item6.setRow(3);
        FGridPanelLabel.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(1);
        item7.setControl("btnLogin");
        item7.setRow(5);
        FGridPanelLabel.getControlCollection().add(item7);
        TFControlItem item8 = new TFControlItem();
        item8.setColumn(0);
        item8.setControl("lblTema");
        item8.setRow(0);
        FGridPanelLabel.getControlCollection().add(item8);
        TFControlItem item9 = new TFControlItem();
        item9.setColumn(0);
        item9.setControl("lblSchema");
        item9.setRow(1);
        FGridPanelLabel.getControlCollection().add(item9);
        TFControlItem item10 = new TFControlItem();
        item10.setColumn(0);
        item10.setControl("lblUsuario");
        item10.setRow(2);
        FGridPanelLabel.getControlCollection().add(item10);
        TFControlItem item11 = new TFControlItem();
        item11.setColumn(0);
        item11.setControl("lblSenha");
        item11.setRow(3);
        FGridPanelLabel.getControlCollection().add(item11);
        TFControlItem item12 = new TFControlItem();
        item12.setColumn(1);
        item12.setControl("chkLembrarSenha");
        item12.setRow(4);
        FGridPanelLabel.getControlCollection().add(item12);
        TFGridPanelRow item13 = new TFGridPanelRow();
        item13.setSizeStyle("ssAuto");
        FGridPanelLabel.getRowCollection().add(item13);
        TFGridPanelRow item14 = new TFGridPanelRow();
        item14.setSizeStyle("ssAuto");
        FGridPanelLabel.getRowCollection().add(item14);
        TFGridPanelRow item15 = new TFGridPanelRow();
        item15.setSizeStyle("ssAuto");
        item15.setValue(100.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item15);
        TFGridPanelRow item16 = new TFGridPanelRow();
        item16.setSizeStyle("ssAuto");
        item16.setValue(50.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item16);
        TFGridPanelRow item17 = new TFGridPanelRow();
        item17.setSizeStyle("ssAuto");
        item17.setValue(100.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item17);
        TFGridPanelRow item18 = new TFGridPanelRow();
        item18.setSizeStyle("ssAuto");
        item18.setValue(100.000000000000000000);
        FGridPanelLabel.getRowCollection().add(item18);
        FGridPanelLabel.setFlexVflex("ftFalse");
        FGridPanelLabel.setFlexHflex("ftTrue");
        FGridPanelLabel.setAllRowFlex(true);
        FGridPanelLabel.setColumnTabOrder(false);
        FHBox2.addChildren(FGridPanelLabel);
        FGridPanelLabel.applyProperties();
    }

    public TFLabel lblSchema = new TFLabel();

    private void init_lblSchema() {
        lblSchema.setName("lblSchema");
        lblSchema.setLeft(414);
        lblSchema.setTop(22);
        lblSchema.setWidth(43);
        lblSchema.setHeight(21);
        lblSchema.setAlign("alRight");
        lblSchema.setCaption("Schema");
        lblSchema.setFontColor("16645629");
        lblSchema.setFontSize(-12);
        lblSchema.setFontName("Tahoma");
        lblSchema.setFontStyle("[]");
        lblSchema.setVerticalAlignment("taVerticalCenter");
        lblSchema.setWordBreak(false);
        FGridPanelLabel.addChildren(lblSchema);
        lblSchema.applyProperties();
    }

    public TFLabel lblUsuario = new TFLabel();

    private void init_lblUsuario() {
        lblUsuario.setName("lblUsuario");
        lblUsuario.setLeft(418);
        lblUsuario.setTop(43);
        lblUsuario.setWidth(39);
        lblUsuario.setHeight(21);
        lblUsuario.setAlign("alRight");
        lblUsuario.setCaption("Usu\u00E1rio");
        lblUsuario.setFontColor("16645629");
        lblUsuario.setFontSize(-12);
        lblUsuario.setFontName("Tahoma");
        lblUsuario.setFontStyle("[]");
        lblUsuario.setVerticalAlignment("taVerticalCenter");
        lblUsuario.setWordBreak(false);
        FGridPanelLabel.addChildren(lblUsuario);
        lblUsuario.applyProperties();
    }

    public TFLabel lblSenha = new TFLabel();

    private void init_lblSenha() {
        lblSenha.setName("lblSenha");
        lblSenha.setLeft(423);
        lblSenha.setTop(64);
        lblSenha.setWidth(34);
        lblSenha.setHeight(21);
        lblSenha.setAlign("alRight");
        lblSenha.setCaption("Senha");
        lblSenha.setFontColor("16645629");
        lblSenha.setFontSize(-12);
        lblSenha.setFontName("Tahoma");
        lblSenha.setFontStyle("[]");
        lblSenha.setVerticalAlignment("taVerticalCenter");
        lblSenha.setWordBreak(false);
        FGridPanelLabel.addChildren(lblSenha);
        lblSenha.applyProperties();
    }

    public TFLabel lblTema = new TFLabel();

    private void init_lblTema() {
        lblTema.setName("lblTema");
        lblTema.setLeft(426);
        lblTema.setTop(1);
        lblTema.setWidth(31);
        lblTema.setHeight(21);
        lblTema.setAlign("alRight");
        lblTema.setCaption("Tema");
        lblTema.setColor("clBtnFace");
        lblTema.setFontColor("16645629");
        lblTema.setFontSize(-12);
        lblTema.setFontName("Tahoma");
        lblTema.setFontStyle("[]");
        lblTema.setVerticalAlignment("taVerticalCenter");
        lblTema.setWordBreak(false);
        FGridPanelLabel.addChildren(lblTema);
        lblTema.applyProperties();
    }

    public TFCombo cboTema = new TFCombo();

    private void init_cboTema() {
        cboTema.setName("cboTema");
        cboTema.setLeft(457);
        cboTema.setTop(1);
        cboTema.setWidth(145);
        cboTema.setHeight(21);
        cboTema.setHint("Tema");
        cboTema.setFlex(true);
        cboTema.setHelpCaption("Tema");
        cboTema.setReadOnly(true);
        cboTema.setRequired(false);
        cboTema.setPrompt("Tema");
        cboTema.setConstraintCheckWhen("cwImmediate");
        cboTema.setConstraintCheckType("ctExpression");
        cboTema.setConstraintFocusOnError(false);
        cboTema.setConstraintEnableUI(true);
        cboTema.setConstraintEnabled(false);
        cboTema.setConstraintFormCheck(true);
        cboTema.setClearOnDelKey(true);
        cboTema.setUseClearButton(false);
        cboTema.setHideClearButtonOnNullValue(false);
        cboTema.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboTemaChange(event);
            processarFlow("FrmLogin", "cboTema", "OnChange");
        });
        cboTema.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboTemaEnter(event);
            processarFlow("FrmLogin", "cboTema", "OnEnter");
        });
        FGridPanelLabel.addChildren(cboTema);
        cboTema.applyProperties();
        addValidatable(cboTema);
    }

    public TFCombo cboSchema = new TFCombo();

    private void init_cboSchema() {
        cboSchema.setName("cboSchema");
        cboSchema.setLeft(457);
        cboSchema.setTop(22);
        cboSchema.setWidth(145);
        cboSchema.setHeight(21);
        cboSchema.setHint("Schema");
        cboSchema.setFlex(true);
        cboSchema.setHelpCaption("Schema");
        cboSchema.setReadOnly(true);
        cboSchema.setRequired(true);
        cboSchema.setPrompt("Schema");
        cboSchema.setConstraintCheckWhen("cwImmediate");
        cboSchema.setConstraintCheckType("ctExpression");
        cboSchema.setConstraintFocusOnError(false);
        cboSchema.setConstraintEnableUI(true);
        cboSchema.setConstraintEnabled(false);
        cboSchema.setConstraintFormCheck(true);
        cboSchema.setClearOnDelKey(true);
        cboSchema.setUseClearButton(false);
        cboSchema.setHideClearButtonOnNullValue(false);
        cboSchema.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboSchemaChange(event);
            processarFlow("FrmLogin", "cboSchema", "OnChange");
        });
        cboSchema.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboSchemaEnter(event);
            processarFlow("FrmLogin", "cboSchema", "OnEnter");
        });
        FGridPanelLabel.addChildren(cboSchema);
        cboSchema.applyProperties();
        addValidatable(cboSchema);
    }

    public TFString edtStringUsuario = new TFString();

    private void init_edtStringUsuario() {
        edtStringUsuario.setName("edtStringUsuario");
        edtStringUsuario.setLeft(457);
        edtStringUsuario.setTop(43);
        edtStringUsuario.setWidth(145);
        edtStringUsuario.setHeight(21);
        edtStringUsuario.setHint("Usu\u00E1rio");
        edtStringUsuario.setHelpCaption("Usu\u00E1rio");
        edtStringUsuario.setFlex(true);
        edtStringUsuario.setRequired(true);
        edtStringUsuario.setPrompt("Usu\u00E1rio");
        edtStringUsuario.setConstraintCheckWhen("cwImmediate");
        edtStringUsuario.setConstraintCheckType("ctExpression");
        edtStringUsuario.setConstraintFocusOnError(false);
        edtStringUsuario.setConstraintEnableUI(true);
        edtStringUsuario.setConstraintEnabled(false);
        edtStringUsuario.setConstraintFormCheck(true);
        edtStringUsuario.setCharCase("ccUpper");
        edtStringUsuario.setPwd(false);
        edtStringUsuario.setMaxlength(0);
        edtStringUsuario.setFontColor("clWindowText");
        edtStringUsuario.setFontSize(-11);
        edtStringUsuario.setFontName("Tahoma");
        edtStringUsuario.setFontStyle("[]");
        edtStringUsuario.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtStringUsuarioEnter(event);
            processarFlow("FrmLogin", "edtStringUsuario", "OnEnter");
        });
        edtStringUsuario.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtStringUsuarioExit(event);
            processarFlow("FrmLogin", "edtStringUsuario", "OnExit");
        });
        edtStringUsuario.setSaveLiteralCharacter(true);
        edtStringUsuario.applyProperties();
        FGridPanelLabel.addChildren(edtStringUsuario);
        addValidatable(edtStringUsuario);
    }

    public TFString edtStringSenha = new TFString();

    private void init_edtStringSenha() {
        edtStringSenha.setName("edtStringSenha");
        edtStringSenha.setLeft(457);
        edtStringSenha.setTop(64);
        edtStringSenha.setWidth(145);
        edtStringSenha.setHeight(21);
        edtStringSenha.setHint("Senha");
        edtStringSenha.setHelpCaption("Senha");
        edtStringSenha.setFlex(true);
        edtStringSenha.setRequired(true);
        edtStringSenha.setPrompt("Senha");
        edtStringSenha.setConstraintCheckWhen("cwImmediate");
        edtStringSenha.setConstraintCheckType("ctExpression");
        edtStringSenha.setConstraintFocusOnError(false);
        edtStringSenha.setConstraintEnableUI(true);
        edtStringSenha.setConstraintEnabled(false);
        edtStringSenha.setConstraintFormCheck(true);
        edtStringSenha.setCharCase("ccNormal");
        edtStringSenha.setPwd(true);
        edtStringSenha.setMaxlength(0);
        edtStringSenha.setFontColor("clWindowText");
        edtStringSenha.setFontSize(-11);
        edtStringSenha.setFontName("Tahoma");
        edtStringSenha.setFontStyle("[]");
        edtStringSenha.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtStringSenhaEnter(event);
            processarFlow("FrmLogin", "edtStringSenha", "OnEnter");
        });
        edtStringSenha.setSaveLiteralCharacter(true);
        edtStringSenha.applyProperties();
        FGridPanelLabel.addChildren(edtStringSenha);
        addValidatable(edtStringSenha);
    }

    public TFCheckBox chkLembrarSenha = new TFCheckBox();

    private void init_chkLembrarSenha() {
        chkLembrarSenha.setName("chkLembrarSenha");
        chkLembrarSenha.setLeft(511);
        chkLembrarSenha.setTop(85);
        chkLembrarSenha.setWidth(97);
        chkLembrarSenha.setHeight(17);
        chkLembrarSenha.setHint("Lembrar Senha");
        chkLembrarSenha.setCaption("Lembrar Senha?");
        chkLembrarSenha.setFontColor("clBlack");
        chkLembrarSenha.setFontSize(-11);
        chkLembrarSenha.setFontName("Tahoma");
        chkLembrarSenha.setFontStyle("[]");
        chkLembrarSenha.setCheckedValue("S");
        chkLembrarSenha.setUncheckedValue("N");
        chkLembrarSenha.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkLembrarSenhaCheck(event);
            processarFlow("FrmLogin", "chkLembrarSenha", "OnCheck");
        });
        chkLembrarSenha.setVerticalAlignment("taAlignTop");
        FGridPanelLabel.addChildren(chkLembrarSenha);
        chkLembrarSenha.applyProperties();
    }

    public TFButton btnLogin = new TFButton();

    private void init_btnLogin() {
        btnLogin.setName("btnLogin");
        btnLogin.setLeft(555);
        btnLogin.setTop(102);
        btnLogin.setWidth(107);
        btnLogin.setHeight(53);
        btnLogin.setAlign("alRight");
        btnLogin.setCaption("Login");
        btnLogin.setFontColor("clWindowText");
        btnLogin.setFontSize(-11);
        btnLogin.setFontName("Tahoma");
        btnLogin.setFontStyle("[]");
        btnLogin.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLoginClick(event);
            processarFlow("FrmLogin", "btnLogin", "OnClick");
        });
        btnLogin.setImageId(0);
        btnLogin.setColor("clBtnFace");
        btnLogin.setAccess(false);
        btnLogin.setIconReverseDirection(false);
        FGridPanelLabel.addChildren(btnLogin);
        btnLogin.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(813);
        FHBox6.setTop(0);
        FHBox6.setWidth(54);
        FHBox6.setHeight(41);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        FHBox2.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(402);
        FHBox3.setWidth(842);
        FHBox3.setHeight(28);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setColor("6776679");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(0);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FrmLogin.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(409);
        FHBox4.setHeight(23);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(5);
        FHBox4.setPaddingLeft(5);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox3.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblVersaoSistema = new TFLabel();

    private void init_lblVersaoSistema() {
        lblVersaoSistema.setName("lblVersaoSistema");
        lblVersaoSistema.setLeft(0);
        lblVersaoSistema.setTop(0);
        lblVersaoSistema.setWidth(37);
        lblVersaoSistema.setHeight(13);
        lblVersaoSistema.setCaption("Versao:");
        lblVersaoSistema.setFontColor("16645629");
        lblVersaoSistema.setFontSize(-11);
        lblVersaoSistema.setFontName("Tahoma");
        lblVersaoSistema.setFontStyle("[]");
        lblVersaoSistema.setVerticalAlignment("taVerticalCenter");
        lblVersaoSistema.setWordBreak(false);
        FHBox4.addChildren(lblVersaoSistema);
        lblVersaoSistema.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(409);
        FVBox1.setTop(0);
        FVBox1.setWidth(392);
        FVBox1.setHeight(23);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(0);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftMin");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(0);
        FHBox5.setWidth(185);
        FHBox5.setHeight(9);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(0);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        FVBox1.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(10);
        FLabel5.setWidth(274);
        FLabel5.setHeight(13);
        FLabel5.setAlign("alRight");
        FLabel5.setCaption("Copyright NBS Tecnologia - Todos os direitos reservados ");
        FLabel5.setFontColor("16645629");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FVBox1.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(801);
        FVBox2.setTop(0);
        FVBox2.setWidth(10);
        FVBox2.setHeight(7);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbUsuarioLogado);
        sc.getTables().add(item0);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void cboTemaChange(final Event<Object> event);

    public abstract void cboTemaEnter(final Event<Object> event);

    public abstract void cboSchemaChange(final Event<Object> event);

    public abstract void cboSchemaEnter(final Event<Object> event);

    public abstract void edtStringUsuarioEnter(final Event<Object> event);

    public abstract void edtStringUsuarioExit(final Event<Object> event);

    public abstract void edtStringSenhaEnter(final Event<Object> event);

    public void btnLoginClick(final Event<Object> event) {
        if (btnLogin.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLogin");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void chkLembrarSenhaCheck(final Event<Object> event);

}