package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmHistoricoFichaCliente extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.HistoricoFichaClienteRNA rn = null;

    public FrmHistoricoFichaCliente() {
        try {
            rn = (freedom.bytecode.rn.HistoricoFichaClienteRNA) getRN(freedom.bytecode.rn.wizard.HistoricoFichaClienteRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbFichaClienteEventos();
        init_tbFichaClienteVeiculos();
        init_tbFichaClienteOsVeiculosCombo();
        init_tbFichaClienteOsOrc();
        init_tbFichaClienteOsVeiculosCombo2();
        init_tbFichaClienteOsOrcCombo();
        init_tbFichaClienteServicos();
        init_tbFichaClientePecas();
        init_FPopupMenu1();
        init_imgLegendaPreto();
        init_imgLegendaVermelho();
        init_imgLegendaAzul();
        init_imgLegendaAmarelo();
        init_vboxpaginaPrincipal();
        init_FHBox198();
        init_FHBox203();
        init_btnVoltar();
        init_vboxCabecalho();
        init_labelNomeCliente();
        init_vboxgridview();
        init_pgCtrlPrincipal();
        init_tabEventos();
        init_vBoxtabEventosPrincipal();
        init_gridEventos();
        init_FHBox1();
        init_imgEventosSucesso();
        init_FVBox1();
        init_labelEventosSucesso();
        init_FVBox3();
        init_imgEventosPendentes();
        init_FVBox2();
        init_labelEventosPendente();
        init_FVBox4();
        init_imgEventosPerdido();
        init_FVBox5();
        init_labelEventosPedido();
        init_FVBox6();
        init_imgEventosDescartado();
        init_FVBox7();
        init_labelEventosDescartado();
        init_tabVeiculos();
        init_vBoxTabVeiculos();
        init_gridVeiculos();
        init_tabOsEOrc();
        init_vBoxtabOsOrc();
        init_hBoxFiltroOsEOrc();
        init_comboTabOsOrcVeiculos();
        init_gridOsOrc();
        init_tabServicosEPecas();
        init_vBoxTavServicosEPecas();
        init_hboxfiltrosServicosEPecas();
        init_comboTabServicosPecasVeiculo();
        init_comboTabServicosPecasOS();
        init_gridSevicos();
        init_gridPecas();
        init_FrmHistoricoFichaCliente();
    }

    public FICHA_CLIENTE_EVENTOS tbFichaClienteEventos;

    private void init_tbFichaClienteEventos() {
        tbFichaClienteEventos = rn.tbFichaClienteEventos;
        tbFichaClienteEventos.setName("tbFichaClienteEventos");
        tbFichaClienteEventos.setMaxRowCount(0);
        tbFichaClienteEventos.setWKey("16507;16502");
        tbFichaClienteEventos.setRatioBatchSize(20);
        getTables().put(tbFichaClienteEventos, "tbFichaClienteEventos");
        tbFichaClienteEventos.applyProperties();
    }

    public FICHA_CLIENTE_VEICULOS tbFichaClienteVeiculos;

    private void init_tbFichaClienteVeiculos() {
        tbFichaClienteVeiculos = rn.tbFichaClienteVeiculos;
        tbFichaClienteVeiculos.setName("tbFichaClienteVeiculos");
        tbFichaClienteVeiculos.setMaxRowCount(0);
        tbFichaClienteVeiculos.setWKey("16507;16503");
        tbFichaClienteVeiculos.setRatioBatchSize(20);
        getTables().put(tbFichaClienteVeiculos, "tbFichaClienteVeiculos");
        tbFichaClienteVeiculos.applyProperties();
    }

    public FICHA_CLIENTE_OS_VEICULOS tbFichaClienteOsVeiculosCombo;

    private void init_tbFichaClienteOsVeiculosCombo() {
        tbFichaClienteOsVeiculosCombo = rn.tbFichaClienteOsVeiculosCombo;
        tbFichaClienteOsVeiculosCombo.setName("tbFichaClienteOsVeiculosCombo");
        tbFichaClienteOsVeiculosCombo.setMaxRowCount(0);
        tbFichaClienteOsVeiculosCombo.setWKey("16507;16504");
        tbFichaClienteOsVeiculosCombo.setRatioBatchSize(20);
        getTables().put(tbFichaClienteOsVeiculosCombo, "tbFichaClienteOsVeiculosCombo");
        tbFichaClienteOsVeiculosCombo.applyProperties();
    }

    public FICHA_CLIENTE_OS_ORC tbFichaClienteOsOrc;

    private void init_tbFichaClienteOsOrc() {
        tbFichaClienteOsOrc = rn.tbFichaClienteOsOrc;
        tbFichaClienteOsOrc.setName("tbFichaClienteOsOrc");
        tbFichaClienteOsOrc.setMaxRowCount(0);
        tbFichaClienteOsOrc.setWKey("16507;16505");
        tbFichaClienteOsOrc.setRatioBatchSize(20);
        getTables().put(tbFichaClienteOsOrc, "tbFichaClienteOsOrc");
        tbFichaClienteOsOrc.applyProperties();
    }

    public FICHA_CLIENTE_OS_VEICULOS tbFichaClienteOsVeiculosCombo2;

    private void init_tbFichaClienteOsVeiculosCombo2() {
        tbFichaClienteOsVeiculosCombo2 = rn.tbFichaClienteOsVeiculosCombo2;
        tbFichaClienteOsVeiculosCombo2.setName("tbFichaClienteOsVeiculosCombo2");
        tbFichaClienteOsVeiculosCombo2.setMaxRowCount(0);
        tbFichaClienteOsVeiculosCombo2.setWKey("16507;16506");
        tbFichaClienteOsVeiculosCombo2.setRatioBatchSize(20);
        getTables().put(tbFichaClienteOsVeiculosCombo2, "tbFichaClienteOsVeiculosCombo2");
        tbFichaClienteOsVeiculosCombo2.applyProperties();
    }

    public FICHA_CLIENTE_OS_ORC tbFichaClienteOsOrcCombo;

    private void init_tbFichaClienteOsOrcCombo() {
        tbFichaClienteOsOrcCombo = rn.tbFichaClienteOsOrcCombo;
        tbFichaClienteOsOrcCombo.setName("tbFichaClienteOsOrcCombo");
        tbFichaClienteOsOrcCombo.setMaxRowCount(0);
        tbFichaClienteOsOrcCombo.setWKey("16507;16507");
        tbFichaClienteOsOrcCombo.setRatioBatchSize(20);
        getTables().put(tbFichaClienteOsOrcCombo, "tbFichaClienteOsOrcCombo");
        tbFichaClienteOsOrcCombo.applyProperties();
    }

    public FICHA_CLIENTE_SERVICOS tbFichaClienteServicos;

    private void init_tbFichaClienteServicos() {
        tbFichaClienteServicos = rn.tbFichaClienteServicos;
        tbFichaClienteServicos.setName("tbFichaClienteServicos");
        tbFichaClienteServicos.setMaxRowCount(0);
        tbFichaClienteServicos.setWKey("16507;16508");
        tbFichaClienteServicos.setRatioBatchSize(20);
        getTables().put(tbFichaClienteServicos, "tbFichaClienteServicos");
        tbFichaClienteServicos.applyProperties();
    }

    public FICHA_CLIENTE_PECAS tbFichaClientePecas;

    private void init_tbFichaClientePecas() {
        tbFichaClientePecas = rn.tbFichaClientePecas;
        tbFichaClientePecas.setName("tbFichaClientePecas");
        tbFichaClientePecas.setMaxRowCount(0);
        tbFichaClientePecas.setWKey("16507;16509");
        tbFichaClientePecas.setRatioBatchSize(20);
        getTables().put(tbFichaClientePecas, "tbFichaClientePecas");
        tbFichaClientePecas.applyProperties();
    }

    public TFPopupMenu FPopupMenu1 = new TFPopupMenu();

    private void init_FPopupMenu1() {
        FPopupMenu1.setName("FPopupMenu1");
        FrmHistoricoFichaCliente.addChildren(FPopupMenu1);
        FPopupMenu1.applyProperties();
    }

    public TFMenuItem imgLegendaPreto = new TFMenuItem();

    private void init_imgLegendaPreto() {
        imgLegendaPreto.setName("imgLegendaPreto");
        imgLegendaPreto.setCaption("imgLegendaPreto");
        imgLegendaPreto.setImageIndex(7000175);
        imgLegendaPreto.setAccess(false);
        imgLegendaPreto.setCheckmark(false);
        FPopupMenu1.addChildren(imgLegendaPreto);
        imgLegendaPreto.applyProperties();
    }

    public TFMenuItem imgLegendaVermelho = new TFMenuItem();

    private void init_imgLegendaVermelho() {
        imgLegendaVermelho.setName("imgLegendaVermelho");
        imgLegendaVermelho.setCaption("imgLegendaVermelho");
        imgLegendaVermelho.setImageIndex(310013);
        imgLegendaVermelho.setAccess(false);
        imgLegendaVermelho.setCheckmark(false);
        FPopupMenu1.addChildren(imgLegendaVermelho);
        imgLegendaVermelho.applyProperties();
    }

    public TFMenuItem imgLegendaAzul = new TFMenuItem();

    private void init_imgLegendaAzul() {
        imgLegendaAzul.setName("imgLegendaAzul");
        imgLegendaAzul.setCaption("imgLegendaAzul");
        imgLegendaAzul.setImageIndex(4600264);
        imgLegendaAzul.setAccess(false);
        imgLegendaAzul.setCheckmark(false);
        FPopupMenu1.addChildren(imgLegendaAzul);
        imgLegendaAzul.applyProperties();
    }

    public TFMenuItem imgLegendaAmarelo = new TFMenuItem();

    private void init_imgLegendaAmarelo() {
        imgLegendaAmarelo.setName("imgLegendaAmarelo");
        imgLegendaAmarelo.setCaption("imgLegendaAmarelo");
        imgLegendaAmarelo.setImageIndex(31009);
        imgLegendaAmarelo.setAccess(false);
        imgLegendaAmarelo.setCheckmark(false);
        FPopupMenu1.addChildren(imgLegendaAmarelo);
        imgLegendaAmarelo.applyProperties();
    }

    protected TFForm FrmHistoricoFichaCliente = this;
    private void init_FrmHistoricoFichaCliente() {
        FrmHistoricoFichaCliente.setName("FrmHistoricoFichaCliente");
        FrmHistoricoFichaCliente.setCaption("Historico ficha do cliente");
        FrmHistoricoFichaCliente.setClientHeight(462);
        FrmHistoricoFichaCliente.setClientWidth(662);
        FrmHistoricoFichaCliente.setColor("clBtnFace");
        FrmHistoricoFichaCliente.setWKey("16507");
        FrmHistoricoFichaCliente.setSpacing(0);
        FrmHistoricoFichaCliente.applyProperties();
    }

    public TFVBox vboxpaginaPrincipal = new TFVBox();

    private void init_vboxpaginaPrincipal() {
        vboxpaginaPrincipal.setName("vboxpaginaPrincipal");
        vboxpaginaPrincipal.setLeft(0);
        vboxpaginaPrincipal.setTop(0);
        vboxpaginaPrincipal.setWidth(662);
        vboxpaginaPrincipal.setHeight(462);
        vboxpaginaPrincipal.setAlign("alClient");
        vboxpaginaPrincipal.setBorderStyle("stNone");
        vboxpaginaPrincipal.setPaddingTop(0);
        vboxpaginaPrincipal.setPaddingLeft(0);
        vboxpaginaPrincipal.setPaddingRight(0);
        vboxpaginaPrincipal.setPaddingBottom(0);
        vboxpaginaPrincipal.setMarginTop(0);
        vboxpaginaPrincipal.setMarginLeft(0);
        vboxpaginaPrincipal.setMarginRight(0);
        vboxpaginaPrincipal.setMarginBottom(0);
        vboxpaginaPrincipal.setSpacing(2);
        vboxpaginaPrincipal.setFlexVflex("ftTrue");
        vboxpaginaPrincipal.setFlexHflex("ftTrue");
        vboxpaginaPrincipal.setScrollable(false);
        vboxpaginaPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxpaginaPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxpaginaPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxpaginaPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxpaginaPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxpaginaPrincipal.setBoxShadowConfigOpacity(75);
        FrmHistoricoFichaCliente.addChildren(vboxpaginaPrincipal);
        vboxpaginaPrincipal.applyProperties();
    }

    public TFHBox FHBox198 = new TFHBox();

    private void init_FHBox198() {
        FHBox198.setName("FHBox198");
        FHBox198.setLeft(0);
        FHBox198.setTop(0);
        FHBox198.setWidth(497);
        FHBox198.setHeight(68);
        FHBox198.setBorderStyle("stNone");
        FHBox198.setPaddingTop(5);
        FHBox198.setPaddingLeft(5);
        FHBox198.setPaddingRight(5);
        FHBox198.setPaddingBottom(5);
        FHBox198.setMarginTop(0);
        FHBox198.setMarginLeft(0);
        FHBox198.setMarginRight(0);
        FHBox198.setMarginBottom(0);
        FHBox198.setSpacing(1);
        FHBox198.setFlexVflex("ftFalse");
        FHBox198.setFlexHflex("ftTrue");
        FHBox198.setScrollable(false);
        FHBox198.setBoxShadowConfigHorizontalLength(10);
        FHBox198.setBoxShadowConfigVerticalLength(10);
        FHBox198.setBoxShadowConfigBlurRadius(5);
        FHBox198.setBoxShadowConfigSpreadRadius(0);
        FHBox198.setBoxShadowConfigShadowColor("clBlack");
        FHBox198.setBoxShadowConfigOpacity(75);
        FHBox198.setVAlign("tvTop");
        vboxpaginaPrincipal.addChildren(FHBox198);
        FHBox198.applyProperties();
    }

    public TFHBox FHBox203 = new TFHBox();

    private void init_FHBox203() {
        FHBox203.setName("FHBox203");
        FHBox203.setLeft(0);
        FHBox203.setTop(0);
        FHBox203.setWidth(53);
        FHBox203.setHeight(60);
        FHBox203.setBorderStyle("stNone");
        FHBox203.setPaddingTop(9);
        FHBox203.setPaddingLeft(0);
        FHBox203.setPaddingRight(0);
        FHBox203.setPaddingBottom(0);
        FHBox203.setMarginTop(0);
        FHBox203.setMarginLeft(0);
        FHBox203.setMarginRight(0);
        FHBox203.setMarginBottom(0);
        FHBox203.setSpacing(4);
        FHBox203.setFlexVflex("ftTrue");
        FHBox203.setFlexHflex("ftTrue");
        FHBox203.setScrollable(false);
        FHBox203.setBoxShadowConfigHorizontalLength(10);
        FHBox203.setBoxShadowConfigVerticalLength(10);
        FHBox203.setBoxShadowConfigBlurRadius(5);
        FHBox203.setBoxShadowConfigSpreadRadius(0);
        FHBox203.setBoxShadowConfigShadowColor("clBlack");
        FHBox203.setBoxShadowConfigOpacity(75);
        FHBox203.setVAlign("tvTop");
        FHBox198.addChildren(FHBox203);
        FHBox203.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(47);
        btnVoltar.setHeight(46);
        btnVoltar.setHint("Inclui um Novo Registro");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmHistoricoFichaCliente", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(true);
        btnVoltar.setIconReverseDirection(false);
        FHBox203.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFVBox vboxCabecalho = new TFVBox();

    private void init_vboxCabecalho() {
        vboxCabecalho.setName("vboxCabecalho");
        vboxCabecalho.setLeft(0);
        vboxCabecalho.setTop(69);
        vboxCabecalho.setWidth(641);
        vboxCabecalho.setHeight(33);
        vboxCabecalho.setBorderStyle("stNone");
        vboxCabecalho.setPaddingTop(5);
        vboxCabecalho.setPaddingLeft(5);
        vboxCabecalho.setPaddingRight(5);
        vboxCabecalho.setPaddingBottom(5);
        vboxCabecalho.setMarginTop(0);
        vboxCabecalho.setMarginLeft(0);
        vboxCabecalho.setMarginRight(0);
        vboxCabecalho.setMarginBottom(0);
        vboxCabecalho.setSpacing(1);
        vboxCabecalho.setFlexVflex("ftFalse");
        vboxCabecalho.setFlexHflex("ftTrue");
        vboxCabecalho.setScrollable(false);
        vboxCabecalho.setBoxShadowConfigHorizontalLength(10);
        vboxCabecalho.setBoxShadowConfigVerticalLength(10);
        vboxCabecalho.setBoxShadowConfigBlurRadius(5);
        vboxCabecalho.setBoxShadowConfigSpreadRadius(0);
        vboxCabecalho.setBoxShadowConfigShadowColor("clBlack");
        vboxCabecalho.setBoxShadowConfigOpacity(75);
        vboxpaginaPrincipal.addChildren(vboxCabecalho);
        vboxCabecalho.applyProperties();
    }

    public TFLabel labelNomeCliente = new TFLabel();

    private void init_labelNomeCliente() {
        labelNomeCliente.setName("labelNomeCliente");
        labelNomeCliente.setLeft(0);
        labelNomeCliente.setTop(0);
        labelNomeCliente.setWidth(182);
        labelNomeCliente.setHeight(29);
        labelNomeCliente.setCaption("labelNomeCliente");
        labelNomeCliente.setFontColor("clHighlight");
        labelNomeCliente.setFontSize(-24);
        labelNomeCliente.setFontName("Tahoma");
        labelNomeCliente.setFontStyle("[]");
        labelNomeCliente.setVerticalAlignment("taVerticalCenter");
        labelNomeCliente.setWordBreak(false);
        vboxCabecalho.addChildren(labelNomeCliente);
        labelNomeCliente.applyProperties();
    }

    public TFVBox vboxgridview = new TFVBox();

    private void init_vboxgridview() {
        vboxgridview.setName("vboxgridview");
        vboxgridview.setLeft(0);
        vboxgridview.setTop(103);
        vboxgridview.setWidth(642);
        vboxgridview.setHeight(354);
        vboxgridview.setBorderStyle("stNone");
        vboxgridview.setPaddingTop(5);
        vboxgridview.setPaddingLeft(5);
        vboxgridview.setPaddingRight(5);
        vboxgridview.setPaddingBottom(5);
        vboxgridview.setMarginTop(0);
        vboxgridview.setMarginLeft(0);
        vboxgridview.setMarginRight(0);
        vboxgridview.setMarginBottom(0);
        vboxgridview.setSpacing(1);
        vboxgridview.setFlexVflex("ftTrue");
        vboxgridview.setFlexHflex("ftTrue");
        vboxgridview.setScrollable(false);
        vboxgridview.setBoxShadowConfigHorizontalLength(10);
        vboxgridview.setBoxShadowConfigVerticalLength(10);
        vboxgridview.setBoxShadowConfigBlurRadius(5);
        vboxgridview.setBoxShadowConfigSpreadRadius(0);
        vboxgridview.setBoxShadowConfigShadowColor("clBlack");
        vboxgridview.setBoxShadowConfigOpacity(75);
        vboxpaginaPrincipal.addChildren(vboxgridview);
        vboxgridview.applyProperties();
    }

    public TFPageControl pgCtrlPrincipal = new TFPageControl();

    private void init_pgCtrlPrincipal() {
        pgCtrlPrincipal.setName("pgCtrlPrincipal");
        pgCtrlPrincipal.setLeft(0);
        pgCtrlPrincipal.setTop(0);
        pgCtrlPrincipal.setWidth(636);
        pgCtrlPrincipal.setHeight(348);
        pgCtrlPrincipal.setTabPosition("tpTop");
        pgCtrlPrincipal.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgCtrlPrincipalChange(event);
            processarFlow("FrmHistoricoFichaCliente", "pgCtrlPrincipal", "OnChange");
        });
        pgCtrlPrincipal.setFlexVflex("ftTrue");
        pgCtrlPrincipal.setFlexHflex("ftTrue");
        pgCtrlPrincipal.setRenderStyle("rsTabbed");
        pgCtrlPrincipal.applyProperties();
        vboxgridview.addChildren(pgCtrlPrincipal);
    }

    public TFTabsheet tabEventos = new TFTabsheet();

    private void init_tabEventos() {
        tabEventos.setName("tabEventos");
        tabEventos.setCaption("Eventos");
        tabEventos.setClosable(false);
        pgCtrlPrincipal.addChildren(tabEventos);
        tabEventos.applyProperties();
    }

    public TFVBox vBoxtabEventosPrincipal = new TFVBox();

    private void init_vBoxtabEventosPrincipal() {
        vBoxtabEventosPrincipal.setName("vBoxtabEventosPrincipal");
        vBoxtabEventosPrincipal.setLeft(0);
        vBoxtabEventosPrincipal.setTop(0);
        vBoxtabEventosPrincipal.setWidth(628);
        vBoxtabEventosPrincipal.setHeight(320);
        vBoxtabEventosPrincipal.setAlign("alClient");
        vBoxtabEventosPrincipal.setBorderStyle("stNone");
        vBoxtabEventosPrincipal.setPaddingTop(0);
        vBoxtabEventosPrincipal.setPaddingLeft(0);
        vBoxtabEventosPrincipal.setPaddingRight(0);
        vBoxtabEventosPrincipal.setPaddingBottom(0);
        vBoxtabEventosPrincipal.setMarginTop(0);
        vBoxtabEventosPrincipal.setMarginLeft(0);
        vBoxtabEventosPrincipal.setMarginRight(0);
        vBoxtabEventosPrincipal.setMarginBottom(0);
        vBoxtabEventosPrincipal.setSpacing(1);
        vBoxtabEventosPrincipal.setFlexVflex("ftTrue");
        vBoxtabEventosPrincipal.setFlexHflex("ftTrue");
        vBoxtabEventosPrincipal.setScrollable(false);
        vBoxtabEventosPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxtabEventosPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxtabEventosPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxtabEventosPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxtabEventosPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxtabEventosPrincipal.setBoxShadowConfigOpacity(75);
        tabEventos.addChildren(vBoxtabEventosPrincipal);
        vBoxtabEventosPrincipal.applyProperties();
    }

    public TFGrid gridEventos = new TFGrid();

    private void init_gridEventos() {
        gridEventos.setName("gridEventos");
        gridEventos.setLeft(0);
        gridEventos.setTop(0);
        gridEventos.setWidth(548);
        gridEventos.setHeight(120);
        gridEventos.setTable(tbFichaClienteEventos);
        gridEventos.setFlexVflex("ftTrue");
        gridEventos.setFlexHflex("ftTrue");
        gridEventos.setPagingEnabled(true);
        gridEventos.setFrozenColumns(0);
        gridEventos.setShowFooter(false);
        gridEventos.setShowHeader(true);
        gridEventos.setMultiSelection(false);
        gridEventos.setGroupingEnabled(false);
        gridEventos.setGroupingExpanded(false);
        gridEventos.setGroupingShowFooter(false);
        gridEventos.setCrosstabEnabled(false);
        gridEventos.setCrosstabGroupType("cgtConcat");
        gridEventos.setEditionEnabled(false);
        gridEventos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("STATUS = 'AMARELO'");
        item1.setEvalType("etExpression");
        item1.setImageId(31009);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("STATUS = 'AZUL'");
        item2.setEvalType("etExpression");
        item2.setImageId(4600264);
        item0.getImages().add(item2);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("STATUS = 'VERDE'");
        item3.setEvalType("etExpression");
        item3.setImageId(310012);
        item0.getImages().add(item3);
        TFImageExpression item4 = new TFImageExpression();
        item4.setExpression("STATUS = 'VERMELHO'");
        item4.setEvalType("etExpression");
        item4.setImageId(310013);
        item0.getImages().add(item4);
        TFImageExpression item5 = new TFImageExpression();
        item5.setExpression("STATUS = 'PRETO'");
        item5.setEvalType("etExpression");
        item5.setImageId(7000175);
        item0.getImages().add(item5);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridEventos.getColumns().add(item0);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("EVENTO");
        item6.setTitleCaption("Evento");
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(50);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(true);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridEventos.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("GRUPO");
        item7.setTitleCaption("Grupo");
        item7.setWidth(155);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(20);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(true);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridEventos.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("DATA_EVENTO");
        item8.setTitleCaption("Data Evento");
        item8.setWidth(144);
        item8.setVisible(true);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(30);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(true);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridEventos.getColumns().add(item8);
        vBoxtabEventosPrincipal.addChildren(gridEventos);
        gridEventos.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(121);
        FHBox1.setWidth(577);
        FHBox1.setHeight(29);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(3);
        FHBox1.setPaddingLeft(3);
        FHBox1.setPaddingRight(3);
        FHBox1.setPaddingBottom(3);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxtabEventosPrincipal.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFImage imgEventosSucesso = new TFImage();

    private void init_imgEventosSucesso() {
        imgEventosSucesso.setName("imgEventosSucesso");
        imgEventosSucesso.setLeft(0);
        imgEventosSucesso.setTop(0);
        imgEventosSucesso.setWidth(18);
        imgEventosSucesso.setHeight(18);
        imgEventosSucesso.setImageSrc("/images/crmservice4600264.png");
        imgEventosSucesso.setBoxSize(0);
        imgEventosSucesso.setGrayScaleOnDisable(false);
        imgEventosSucesso.setFlexVflex("ftFalse");
        imgEventosSucesso.setFlexHflex("ftFalse");
        FHBox1.addChildren(imgEventosSucesso);
        imgEventosSucesso.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(18);
        FVBox1.setTop(0);
        FVBox1.setWidth(77);
        FVBox1.setHeight(19);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel labelEventosSucesso = new TFLabel();

    private void init_labelEventosSucesso() {
        labelEventosSucesso.setName("labelEventosSucesso");
        labelEventosSucesso.setLeft(0);
        labelEventosSucesso.setTop(0);
        labelEventosSucesso.setWidth(39);
        labelEventosSucesso.setHeight(13);
        labelEventosSucesso.setCaption("Sucesso");
        labelEventosSucesso.setFontColor("clWindowText");
        labelEventosSucesso.setFontSize(-11);
        labelEventosSucesso.setFontName("Tahoma");
        labelEventosSucesso.setFontStyle("[]");
        labelEventosSucesso.setVerticalAlignment("taVerticalCenter");
        labelEventosSucesso.setWordBreak(false);
        FVBox1.addChildren(labelEventosSucesso);
        labelEventosSucesso.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(95);
        FVBox3.setTop(0);
        FVBox3.setWidth(29);
        FVBox3.setHeight(18);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFImage imgEventosPendentes = new TFImage();

    private void init_imgEventosPendentes() {
        imgEventosPendentes.setName("imgEventosPendentes");
        imgEventosPendentes.setLeft(124);
        imgEventosPendentes.setTop(0);
        imgEventosPendentes.setWidth(18);
        imgEventosPendentes.setHeight(18);
        imgEventosPendentes.setImageSrc("/images/crmservice31009.png");
        imgEventosPendentes.setBoxSize(0);
        imgEventosPendentes.setGrayScaleOnDisable(false);
        imgEventosPendentes.setFlexVflex("ftFalse");
        imgEventosPendentes.setFlexHflex("ftFalse");
        FHBox1.addChildren(imgEventosPendentes);
        imgEventosPendentes.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(142);
        FVBox2.setTop(0);
        FVBox2.setWidth(77);
        FVBox2.setHeight(18);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel labelEventosPendente = new TFLabel();

    private void init_labelEventosPendente() {
        labelEventosPendente.setName("labelEventosPendente");
        labelEventosPendente.setLeft(0);
        labelEventosPendente.setTop(0);
        labelEventosPendente.setWidth(46);
        labelEventosPendente.setHeight(13);
        labelEventosPendente.setCaption("Pendente");
        labelEventosPendente.setFontColor("clWindowText");
        labelEventosPendente.setFontSize(-11);
        labelEventosPendente.setFontName("Tahoma");
        labelEventosPendente.setFontStyle("[]");
        labelEventosPendente.setVerticalAlignment("taVerticalCenter");
        labelEventosPendente.setWordBreak(false);
        FVBox2.addChildren(labelEventosPendente);
        labelEventosPendente.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(219);
        FVBox4.setTop(0);
        FVBox4.setWidth(29);
        FVBox4.setHeight(18);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFImage imgEventosPerdido = new TFImage();

    private void init_imgEventosPerdido() {
        imgEventosPerdido.setName("imgEventosPerdido");
        imgEventosPerdido.setLeft(248);
        imgEventosPerdido.setTop(0);
        imgEventosPerdido.setWidth(18);
        imgEventosPerdido.setHeight(18);
        imgEventosPerdido.setImageSrc("/images/crmservice310013.png");
        imgEventosPerdido.setBoxSize(0);
        imgEventosPerdido.setGrayScaleOnDisable(false);
        imgEventosPerdido.setFlexVflex("ftFalse");
        imgEventosPerdido.setFlexHflex("ftFalse");
        FHBox1.addChildren(imgEventosPerdido);
        imgEventosPerdido.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(266);
        FVBox5.setTop(0);
        FVBox5.setWidth(77);
        FVBox5.setHeight(18);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel labelEventosPedido = new TFLabel();

    private void init_labelEventosPedido() {
        labelEventosPedido.setName("labelEventosPedido");
        labelEventosPedido.setLeft(0);
        labelEventosPedido.setTop(0);
        labelEventosPedido.setWidth(36);
        labelEventosPedido.setHeight(13);
        labelEventosPedido.setCaption("Perdido");
        labelEventosPedido.setFontColor("clWindowText");
        labelEventosPedido.setFontSize(-11);
        labelEventosPedido.setFontName("Tahoma");
        labelEventosPedido.setFontStyle("[]");
        labelEventosPedido.setVerticalAlignment("taVerticalCenter");
        labelEventosPedido.setWordBreak(false);
        FVBox5.addChildren(labelEventosPedido);
        labelEventosPedido.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(343);
        FVBox6.setTop(0);
        FVBox6.setWidth(29);
        FVBox6.setHeight(18);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftTrue");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFImage imgEventosDescartado = new TFImage();

    private void init_imgEventosDescartado() {
        imgEventosDescartado.setName("imgEventosDescartado");
        imgEventosDescartado.setLeft(372);
        imgEventosDescartado.setTop(0);
        imgEventosDescartado.setWidth(18);
        imgEventosDescartado.setHeight(18);
        imgEventosDescartado.setImageSrc("/images/crmservice7000175.png");
        imgEventosDescartado.setBoxSize(0);
        imgEventosDescartado.setGrayScaleOnDisable(false);
        imgEventosDescartado.setFlexVflex("ftFalse");
        imgEventosDescartado.setFlexHflex("ftFalse");
        FHBox1.addChildren(imgEventosDescartado);
        imgEventosDescartado.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(390);
        FVBox7.setTop(0);
        FVBox7.setWidth(77);
        FVBox7.setHeight(18);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftFalse");
        FVBox7.setFlexHflex("ftFalse");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFLabel labelEventosDescartado = new TFLabel();

    private void init_labelEventosDescartado() {
        labelEventosDescartado.setName("labelEventosDescartado");
        labelEventosDescartado.setLeft(0);
        labelEventosDescartado.setTop(0);
        labelEventosDescartado.setWidth(55);
        labelEventosDescartado.setHeight(13);
        labelEventosDescartado.setCaption("Descartado");
        labelEventosDescartado.setFontColor("clWindowText");
        labelEventosDescartado.setFontSize(-11);
        labelEventosDescartado.setFontName("Tahoma");
        labelEventosDescartado.setFontStyle("[]");
        labelEventosDescartado.setVerticalAlignment("taVerticalCenter");
        labelEventosDescartado.setWordBreak(false);
        FVBox7.addChildren(labelEventosDescartado);
        labelEventosDescartado.applyProperties();
    }

    public TFTabsheet tabVeiculos = new TFTabsheet();

    private void init_tabVeiculos() {
        tabVeiculos.setName("tabVeiculos");
        tabVeiculos.setCaption("Ve\u00EDculos");
        tabVeiculos.setClosable(false);
        pgCtrlPrincipal.addChildren(tabVeiculos);
        tabVeiculos.applyProperties();
    }

    public TFVBox vBoxTabVeiculos = new TFVBox();

    private void init_vBoxTabVeiculos() {
        vBoxTabVeiculos.setName("vBoxTabVeiculos");
        vBoxTabVeiculos.setLeft(0);
        vBoxTabVeiculos.setTop(0);
        vBoxTabVeiculos.setWidth(628);
        vBoxTabVeiculos.setHeight(320);
        vBoxTabVeiculos.setAlign("alClient");
        vBoxTabVeiculos.setBorderStyle("stNone");
        vBoxTabVeiculos.setPaddingTop(0);
        vBoxTabVeiculos.setPaddingLeft(0);
        vBoxTabVeiculos.setPaddingRight(0);
        vBoxTabVeiculos.setPaddingBottom(0);
        vBoxTabVeiculos.setMarginTop(0);
        vBoxTabVeiculos.setMarginLeft(0);
        vBoxTabVeiculos.setMarginRight(0);
        vBoxTabVeiculos.setMarginBottom(0);
        vBoxTabVeiculos.setSpacing(1);
        vBoxTabVeiculos.setFlexVflex("ftTrue");
        vBoxTabVeiculos.setFlexHflex("ftTrue");
        vBoxTabVeiculos.setScrollable(false);
        vBoxTabVeiculos.setBoxShadowConfigHorizontalLength(10);
        vBoxTabVeiculos.setBoxShadowConfigVerticalLength(10);
        vBoxTabVeiculos.setBoxShadowConfigBlurRadius(5);
        vBoxTabVeiculos.setBoxShadowConfigSpreadRadius(0);
        vBoxTabVeiculos.setBoxShadowConfigShadowColor("clBlack");
        vBoxTabVeiculos.setBoxShadowConfigOpacity(75);
        tabVeiculos.addChildren(vBoxTabVeiculos);
        vBoxTabVeiculos.applyProperties();
    }

    public TFGrid gridVeiculos = new TFGrid();

    private void init_gridVeiculos() {
        gridVeiculos.setName("gridVeiculos");
        gridVeiculos.setLeft(0);
        gridVeiculos.setTop(0);
        gridVeiculos.setWidth(548);
        gridVeiculos.setHeight(120);
        gridVeiculos.setTable(tbFichaClienteVeiculos);
        gridVeiculos.setFlexVflex("ftTrue");
        gridVeiculos.setFlexHflex("ftTrue");
        gridVeiculos.setPagingEnabled(true);
        gridVeiculos.setFrozenColumns(0);
        gridVeiculos.setShowFooter(false);
        gridVeiculos.setShowHeader(true);
        gridVeiculos.setMultiSelection(false);
        gridVeiculos.setGroupingEnabled(false);
        gridVeiculos.setGroupingExpanded(false);
        gridVeiculos.setGroupingShowFooter(false);
        gridVeiculos.setCrosstabEnabled(false);
        gridVeiculos.setCrosstabGroupType("cgtConcat");
        gridVeiculos.setEditionEnabled(false);
        gridVeiculos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("MODELO");
        item0.setTitleCaption("Modelo");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(60);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridVeiculos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DATA_VENDA");
        item1.setTitleCaption("Data Venda");
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(15);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridVeiculos.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("TIPO");
        item2.setTitleCaption("Tipo");
        item2.setWidth(155);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(10);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridVeiculos.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("PLACA_CHASSI");
        item3.setTitleCaption("Placa/Chassi");
        item3.setWidth(144);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(15);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridVeiculos.getColumns().add(item3);
        vBoxTabVeiculos.addChildren(gridVeiculos);
        gridVeiculos.applyProperties();
    }

    public TFTabsheet tabOsEOrc = new TFTabsheet();

    private void init_tabOsEOrc() {
        tabOsEOrc.setName("tabOsEOrc");
        tabOsEOrc.setCaption("OS & ORC");
        tabOsEOrc.setClosable(false);
        pgCtrlPrincipal.addChildren(tabOsEOrc);
        tabOsEOrc.applyProperties();
    }

    public TFVBox vBoxtabOsOrc = new TFVBox();

    private void init_vBoxtabOsOrc() {
        vBoxtabOsOrc.setName("vBoxtabOsOrc");
        vBoxtabOsOrc.setLeft(0);
        vBoxtabOsOrc.setTop(0);
        vBoxtabOsOrc.setWidth(628);
        vBoxtabOsOrc.setHeight(320);
        vBoxtabOsOrc.setAlign("alClient");
        vBoxtabOsOrc.setBorderStyle("stNone");
        vBoxtabOsOrc.setPaddingTop(10);
        vBoxtabOsOrc.setPaddingLeft(0);
        vBoxtabOsOrc.setPaddingRight(0);
        vBoxtabOsOrc.setPaddingBottom(0);
        vBoxtabOsOrc.setMarginTop(0);
        vBoxtabOsOrc.setMarginLeft(0);
        vBoxtabOsOrc.setMarginRight(0);
        vBoxtabOsOrc.setMarginBottom(0);
        vBoxtabOsOrc.setSpacing(5);
        vBoxtabOsOrc.setFlexVflex("ftTrue");
        vBoxtabOsOrc.setFlexHflex("ftTrue");
        vBoxtabOsOrc.setScrollable(false);
        vBoxtabOsOrc.setBoxShadowConfigHorizontalLength(10);
        vBoxtabOsOrc.setBoxShadowConfigVerticalLength(10);
        vBoxtabOsOrc.setBoxShadowConfigBlurRadius(5);
        vBoxtabOsOrc.setBoxShadowConfigSpreadRadius(0);
        vBoxtabOsOrc.setBoxShadowConfigShadowColor("clBlack");
        vBoxtabOsOrc.setBoxShadowConfigOpacity(75);
        tabOsEOrc.addChildren(vBoxtabOsOrc);
        vBoxtabOsOrc.applyProperties();
    }

    public TFHBox hBoxFiltroOsEOrc = new TFHBox();

    private void init_hBoxFiltroOsEOrc() {
        hBoxFiltroOsEOrc.setName("hBoxFiltroOsEOrc");
        hBoxFiltroOsEOrc.setLeft(0);
        hBoxFiltroOsEOrc.setTop(0);
        hBoxFiltroOsEOrc.setWidth(593);
        hBoxFiltroOsEOrc.setHeight(41);
        hBoxFiltroOsEOrc.setBorderStyle("stNone");
        hBoxFiltroOsEOrc.setPaddingTop(0);
        hBoxFiltroOsEOrc.setPaddingLeft(0);
        hBoxFiltroOsEOrc.setPaddingRight(0);
        hBoxFiltroOsEOrc.setPaddingBottom(0);
        hBoxFiltroOsEOrc.setMarginTop(0);
        hBoxFiltroOsEOrc.setMarginLeft(0);
        hBoxFiltroOsEOrc.setMarginRight(0);
        hBoxFiltroOsEOrc.setMarginBottom(0);
        hBoxFiltroOsEOrc.setSpacing(1);
        hBoxFiltroOsEOrc.setFlexVflex("ftFalse");
        hBoxFiltroOsEOrc.setFlexHflex("ftTrue");
        hBoxFiltroOsEOrc.setScrollable(false);
        hBoxFiltroOsEOrc.setBoxShadowConfigHorizontalLength(10);
        hBoxFiltroOsEOrc.setBoxShadowConfigVerticalLength(10);
        hBoxFiltroOsEOrc.setBoxShadowConfigBlurRadius(5);
        hBoxFiltroOsEOrc.setBoxShadowConfigSpreadRadius(0);
        hBoxFiltroOsEOrc.setBoxShadowConfigShadowColor("clBlack");
        hBoxFiltroOsEOrc.setBoxShadowConfigOpacity(75);
        hBoxFiltroOsEOrc.setVAlign("tvTop");
        vBoxtabOsOrc.addChildren(hBoxFiltroOsEOrc);
        hBoxFiltroOsEOrc.applyProperties();
    }

    public TFCombo comboTabOsOrcVeiculos = new TFCombo();

    private void init_comboTabOsOrcVeiculos() {
        comboTabOsOrcVeiculos.setName("comboTabOsOrcVeiculos");
        comboTabOsOrcVeiculos.setLeft(0);
        comboTabOsOrcVeiculos.setTop(0);
        comboTabOsOrcVeiculos.setWidth(341);
        comboTabOsOrcVeiculos.setHeight(21);
        comboTabOsOrcVeiculos.setHint("Ve\u00EDculo");
        comboTabOsOrcVeiculos.setLookupTable(tbFichaClienteOsVeiculosCombo);
        comboTabOsOrcVeiculos.setLookupKey("CHASSI");
        comboTabOsOrcVeiculos.setLookupDesc("MODELO");
        comboTabOsOrcVeiculos.setFlex(true);
        comboTabOsOrcVeiculos.setReadOnly(true);
        comboTabOsOrcVeiculos.setRequired(false);
        comboTabOsOrcVeiculos.setPrompt("Ve\u00EDculo");
        comboTabOsOrcVeiculos.setConstraintCheckWhen("cwImmediate");
        comboTabOsOrcVeiculos.setConstraintCheckType("ctExpression");
        comboTabOsOrcVeiculos.setConstraintFocusOnError(false);
        comboTabOsOrcVeiculos.setConstraintEnableUI(true);
        comboTabOsOrcVeiculos.setConstraintEnabled(false);
        comboTabOsOrcVeiculos.setConstraintFormCheck(true);
        comboTabOsOrcVeiculos.setClearOnDelKey(false);
        comboTabOsOrcVeiculos.setUseClearButton(true);
        comboTabOsOrcVeiculos.setHideClearButtonOnNullValue(false);
        comboTabOsOrcVeiculos.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabOsOrcVeiculosChange(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabOsOrcVeiculos", "OnChange");
        });
        comboTabOsOrcVeiculos.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabOsOrcVeiculosEnter(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabOsOrcVeiculos", "OnEnter");
        });
        comboTabOsOrcVeiculos.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabOsOrcVeiculosClearClick(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabOsOrcVeiculos", "OnClearClick");
        });
        hBoxFiltroOsEOrc.addChildren(comboTabOsOrcVeiculos);
        comboTabOsOrcVeiculos.applyProperties();
        addValidatable(comboTabOsOrcVeiculos);
    }

    public TFGrid gridOsOrc = new TFGrid();

    private void init_gridOsOrc() {
        gridOsOrc.setName("gridOsOrc");
        gridOsOrc.setLeft(0);
        gridOsOrc.setTop(42);
        gridOsOrc.setWidth(552);
        gridOsOrc.setHeight(120);
        gridOsOrc.setTable(tbFichaClienteOsOrc);
        gridOsOrc.setFlexVflex("ftTrue");
        gridOsOrc.setFlexHflex("ftTrue");
        gridOsOrc.setPagingEnabled(true);
        gridOsOrc.setFrozenColumns(0);
        gridOsOrc.setShowFooter(false);
        gridOsOrc.setShowHeader(true);
        gridOsOrc.setMultiSelection(false);
        gridOsOrc.setGroupingEnabled(false);
        gridOsOrc.setGroupingExpanded(false);
        gridOsOrc.setGroupingShowFooter(false);
        gridOsOrc.setCrosstabEnabled(false);
        gridOsOrc.setCrosstabGroupType("cgtConcat");
        gridOsOrc.setEditionEnabled(false);
        gridOsOrc.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("OS");
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(20);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridOsOrc.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TIPO");
        item1.setTitleCaption("Tipo");
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(40);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridOsOrc.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("STATUS");
        item2.setTitleCaption("Status");
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(20);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridOsOrc.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("TOTAL_VALOR");
        item3.setTitleCaption("Total Valor");
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftDecimal");
        item3.setFlexRatio(20);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("R$ ,##0.00");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDecimal");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridOsOrc.getColumns().add(item3);
        vBoxtabOsOrc.addChildren(gridOsOrc);
        gridOsOrc.applyProperties();
    }

    public TFTabsheet tabServicosEPecas = new TFTabsheet();

    private void init_tabServicosEPecas() {
        tabServicosEPecas.setName("tabServicosEPecas");
        tabServicosEPecas.setCaption("Servi\u00E7os & Pe\u00E7as");
        tabServicosEPecas.setClosable(false);
        pgCtrlPrincipal.addChildren(tabServicosEPecas);
        tabServicosEPecas.applyProperties();
    }

    public TFVBox vBoxTavServicosEPecas = new TFVBox();

    private void init_vBoxTavServicosEPecas() {
        vBoxTavServicosEPecas.setName("vBoxTavServicosEPecas");
        vBoxTavServicosEPecas.setLeft(0);
        vBoxTavServicosEPecas.setTop(0);
        vBoxTavServicosEPecas.setWidth(628);
        vBoxTavServicosEPecas.setHeight(320);
        vBoxTavServicosEPecas.setAlign("alClient");
        vBoxTavServicosEPecas.setBorderStyle("stNone");
        vBoxTavServicosEPecas.setPaddingTop(10);
        vBoxTavServicosEPecas.setPaddingLeft(0);
        vBoxTavServicosEPecas.setPaddingRight(0);
        vBoxTavServicosEPecas.setPaddingBottom(0);
        vBoxTavServicosEPecas.setMarginTop(0);
        vBoxTavServicosEPecas.setMarginLeft(0);
        vBoxTavServicosEPecas.setMarginRight(0);
        vBoxTavServicosEPecas.setMarginBottom(0);
        vBoxTavServicosEPecas.setSpacing(5);
        vBoxTavServicosEPecas.setFlexVflex("ftTrue");
        vBoxTavServicosEPecas.setFlexHflex("ftTrue");
        vBoxTavServicosEPecas.setScrollable(false);
        vBoxTavServicosEPecas.setBoxShadowConfigHorizontalLength(10);
        vBoxTavServicosEPecas.setBoxShadowConfigVerticalLength(10);
        vBoxTavServicosEPecas.setBoxShadowConfigBlurRadius(5);
        vBoxTavServicosEPecas.setBoxShadowConfigSpreadRadius(0);
        vBoxTavServicosEPecas.setBoxShadowConfigShadowColor("clBlack");
        vBoxTavServicosEPecas.setBoxShadowConfigOpacity(75);
        tabServicosEPecas.addChildren(vBoxTavServicosEPecas);
        vBoxTavServicosEPecas.applyProperties();
    }

    public TFHBox hboxfiltrosServicosEPecas = new TFHBox();

    private void init_hboxfiltrosServicosEPecas() {
        hboxfiltrosServicosEPecas.setName("hboxfiltrosServicosEPecas");
        hboxfiltrosServicosEPecas.setLeft(0);
        hboxfiltrosServicosEPecas.setTop(0);
        hboxfiltrosServicosEPecas.setWidth(593);
        hboxfiltrosServicosEPecas.setHeight(41);
        hboxfiltrosServicosEPecas.setBorderStyle("stNone");
        hboxfiltrosServicosEPecas.setPaddingTop(0);
        hboxfiltrosServicosEPecas.setPaddingLeft(0);
        hboxfiltrosServicosEPecas.setPaddingRight(0);
        hboxfiltrosServicosEPecas.setPaddingBottom(0);
        hboxfiltrosServicosEPecas.setMarginTop(0);
        hboxfiltrosServicosEPecas.setMarginLeft(0);
        hboxfiltrosServicosEPecas.setMarginRight(0);
        hboxfiltrosServicosEPecas.setMarginBottom(0);
        hboxfiltrosServicosEPecas.setSpacing(1);
        hboxfiltrosServicosEPecas.setFlexVflex("ftFalse");
        hboxfiltrosServicosEPecas.setFlexHflex("ftTrue");
        hboxfiltrosServicosEPecas.setScrollable(false);
        hboxfiltrosServicosEPecas.setBoxShadowConfigHorizontalLength(10);
        hboxfiltrosServicosEPecas.setBoxShadowConfigVerticalLength(10);
        hboxfiltrosServicosEPecas.setBoxShadowConfigBlurRadius(5);
        hboxfiltrosServicosEPecas.setBoxShadowConfigSpreadRadius(0);
        hboxfiltrosServicosEPecas.setBoxShadowConfigShadowColor("clBlack");
        hboxfiltrosServicosEPecas.setBoxShadowConfigOpacity(75);
        hboxfiltrosServicosEPecas.setVAlign("tvTop");
        vBoxTavServicosEPecas.addChildren(hboxfiltrosServicosEPecas);
        hboxfiltrosServicosEPecas.applyProperties();
    }

    public TFCombo comboTabServicosPecasVeiculo = new TFCombo();

    private void init_comboTabServicosPecasVeiculo() {
        comboTabServicosPecasVeiculo.setName("comboTabServicosPecasVeiculo");
        comboTabServicosPecasVeiculo.setLeft(0);
        comboTabServicosPecasVeiculo.setTop(0);
        comboTabServicosPecasVeiculo.setWidth(273);
        comboTabServicosPecasVeiculo.setHeight(21);
        comboTabServicosPecasVeiculo.setHint("Ve\u00EDculo");
        comboTabServicosPecasVeiculo.setLookupTable(tbFichaClienteOsVeiculosCombo2);
        comboTabServicosPecasVeiculo.setLookupKey("CHASSI");
        comboTabServicosPecasVeiculo.setLookupDesc("MODELO");
        comboTabServicosPecasVeiculo.setFlex(true);
        comboTabServicosPecasVeiculo.setReadOnly(true);
        comboTabServicosPecasVeiculo.setRequired(false);
        comboTabServicosPecasVeiculo.setPrompt("Ve\u00EDculo");
        comboTabServicosPecasVeiculo.setConstraintCheckWhen("cwImmediate");
        comboTabServicosPecasVeiculo.setConstraintCheckType("ctExpression");
        comboTabServicosPecasVeiculo.setConstraintFocusOnError(false);
        comboTabServicosPecasVeiculo.setConstraintEnableUI(true);
        comboTabServicosPecasVeiculo.setConstraintEnabled(false);
        comboTabServicosPecasVeiculo.setConstraintFormCheck(true);
        comboTabServicosPecasVeiculo.setClearOnDelKey(false);
        comboTabServicosPecasVeiculo.setUseClearButton(true);
        comboTabServicosPecasVeiculo.setHideClearButtonOnNullValue(false);
        comboTabServicosPecasVeiculo.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabServicosPecasVeiculoChange(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabServicosPecasVeiculo", "OnChange");
        });
        comboTabServicosPecasVeiculo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabServicosPecasVeiculoEnter(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabServicosPecasVeiculo", "OnEnter");
        });
        comboTabServicosPecasVeiculo.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabServicosPecasVeiculoClearClick(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabServicosPecasVeiculo", "OnClearClick");
        });
        hboxfiltrosServicosEPecas.addChildren(comboTabServicosPecasVeiculo);
        comboTabServicosPecasVeiculo.applyProperties();
        addValidatable(comboTabServicosPecasVeiculo);
    }

    public TFCombo comboTabServicosPecasOS = new TFCombo();

    private void init_comboTabServicosPecasOS() {
        comboTabServicosPecasOS.setName("comboTabServicosPecasOS");
        comboTabServicosPecasOS.setLeft(273);
        comboTabServicosPecasOS.setTop(0);
        comboTabServicosPecasOS.setWidth(297);
        comboTabServicosPecasOS.setHeight(21);
        comboTabServicosPecasOS.setHint("OS");
        comboTabServicosPecasOS.setLookupTable(tbFichaClienteOsOrcCombo);
        comboTabServicosPecasOS.setLookupKey("NUMERO_OS");
        comboTabServicosPecasOS.setLookupDesc("OS");
        comboTabServicosPecasOS.setFlex(true);
        comboTabServicosPecasOS.setReadOnly(true);
        comboTabServicosPecasOS.setRequired(false);
        comboTabServicosPecasOS.setPrompt("OS");
        comboTabServicosPecasOS.setConstraintCheckWhen("cwImmediate");
        comboTabServicosPecasOS.setConstraintCheckType("ctExpression");
        comboTabServicosPecasOS.setConstraintFocusOnError(false);
        comboTabServicosPecasOS.setConstraintEnableUI(true);
        comboTabServicosPecasOS.setConstraintEnabled(false);
        comboTabServicosPecasOS.setConstraintFormCheck(true);
        comboTabServicosPecasOS.setClearOnDelKey(false);
        comboTabServicosPecasOS.setUseClearButton(true);
        comboTabServicosPecasOS.setHideClearButtonOnNullValue(false);
        comboTabServicosPecasOS.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabServicosPecasOSChange(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabServicosPecasOS", "OnChange");
        });
        comboTabServicosPecasOS.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabServicosPecasOSEnter(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabServicosPecasOS", "OnEnter");
        });
        comboTabServicosPecasOS.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            comboTabServicosPecasOSClearClick(event);
            processarFlow("FrmHistoricoFichaCliente", "comboTabServicosPecasOS", "OnClearClick");
        });
        hboxfiltrosServicosEPecas.addChildren(comboTabServicosPecasOS);
        comboTabServicosPecasOS.applyProperties();
        addValidatable(comboTabServicosPecasOS);
    }

    public TFGrid gridSevicos = new TFGrid();

    private void init_gridSevicos() {
        gridSevicos.setName("gridSevicos");
        gridSevicos.setLeft(0);
        gridSevicos.setTop(42);
        gridSevicos.setWidth(552);
        gridSevicos.setHeight(120);
        gridSevicos.setTable(tbFichaClienteServicos);
        gridSevicos.setFlexVflex("ftTrue");
        gridSevicos.setFlexHflex("ftTrue");
        gridSevicos.setPagingEnabled(true);
        gridSevicos.setFrozenColumns(0);
        gridSevicos.setShowFooter(false);
        gridSevicos.setShowHeader(true);
        gridSevicos.setMultiSelection(false);
        gridSevicos.setGroupingEnabled(false);
        gridSevicos.setGroupingExpanded(false);
        gridSevicos.setGroupingShowFooter(false);
        gridSevicos.setCrosstabEnabled(false);
        gridSevicos.setCrosstabGroupType("cgtConcat");
        gridSevicos.setEditionEnabled(false);
        gridSevicos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_SERVICO");
        item0.setTitleCaption("Cod. Servi\u00E7o");
        item0.setWidth(99);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(30);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridSevicos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("SERVICO");
        item1.setTitleCaption("Servi\u00E7o");
        item1.setWidth(146);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(50);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridSevicos.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("NUMERO_OS");
        item2.setTitleCaption("N\u00FAmero Os");
        item2.setWidth(110);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftString");
        item2.setFlexRatio(20);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridSevicos.getColumns().add(item2);
        vBoxTavServicosEPecas.addChildren(gridSevicos);
        gridSevicos.applyProperties();
    }

    public TFGrid gridPecas = new TFGrid();

    private void init_gridPecas() {
        gridPecas.setName("gridPecas");
        gridPecas.setLeft(0);
        gridPecas.setTop(163);
        gridPecas.setWidth(552);
        gridPecas.setHeight(120);
        gridPecas.setTable(tbFichaClientePecas);
        gridPecas.setFlexVflex("ftTrue");
        gridPecas.setFlexHflex("ftTrue");
        gridPecas.setPagingEnabled(true);
        gridPecas.setFrozenColumns(0);
        gridPecas.setShowFooter(false);
        gridPecas.setShowHeader(true);
        gridPecas.setMultiSelection(false);
        gridPecas.setGroupingEnabled(false);
        gridPecas.setGroupingExpanded(false);
        gridPecas.setGroupingShowFooter(false);
        gridPecas.setCrosstabEnabled(false);
        gridPecas.setCrosstabGroupType("cgtConcat");
        gridPecas.setEditionEnabled(false);
        gridPecas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_ITEM");
        item0.setTitleCaption("C\u00F3d. Item");
        item0.setWidth(68);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(20);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridPecas.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(150);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(40);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridPecas.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("FORNC");
        item2.setTitleCaption("Fornecedor");
        item2.setWidth(75);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(20);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(true);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridPecas.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("QTDE");
        item3.setTitleCaption("Qtde");
        item3.setWidth(70);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(10);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridPecas.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("PRECO_TOTAL");
        item4.setTitleCaption("Pre\u00E7o Total");
        item4.setWidth(94);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taRight");
        item4.setFieldType("ftDecimal");
        item4.setFlexRatio(10);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        TFMaskExpression item5 = new TFMaskExpression();
        item5.setExpression("*");
        item5.setEvalType("etExpression");
        item5.setMask("R$ #,##0.00");
        item5.setPadLength(0);
        item5.setPadDirection("pdNone");
        item5.setMaskType("mtDecimal");
        item4.getMasks().add(item5);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridPecas.getColumns().add(item4);
        vBoxTavServicosEPecas.addChildren(gridPecas);
        gridPecas.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void pgCtrlPrincipalChange(final Event<Object> event);

    public abstract void comboTabOsOrcVeiculosChange(final Event<Object> event);

    public abstract void comboTabOsOrcVeiculosEnter(final Event<Object> event);

    public abstract void comboTabOsOrcVeiculosClearClick(final Event<Object> event);

    public abstract void comboTabServicosPecasVeiculoChange(final Event<Object> event);

    public abstract void comboTabServicosPecasVeiculoEnter(final Event<Object> event);

    public abstract void comboTabServicosPecasVeiculoClearClick(final Event<Object> event);

    public abstract void comboTabServicosPecasOSChange(final Event<Object> event);

    public abstract void comboTabServicosPecasOSEnter(final Event<Object> event);

    public abstract void comboTabServicosPecasOSClearClick(final Event<Object> event);

}