package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmHome extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.HomeRNA rn = null;

    public FrmHome() {
        try {
            rn = (freedom.bytecode.rn.HomeRNA) getRN(freedom.bytecode.rn.wizard.HomeRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbUserInformation();
        init_tbMenu();
        init_tbSistemaAcesso();
        init_tbProjeto();
        init_tbProjetoConnDic();
        init_tbModulo();
        init_tbModuloConnDic();
        init_tbForm();
        init_tbFormConnDic();
        init_tbFrMenu();
        init_tbFrMenuConnDic();
        init_tbMenuAcesso();
        init_mnuPrincipal();
        init_tmrRelogio();
        init_popMenuLogin();
        init_mmPerfil();
        init_mmAlterarSenha();
        init_mmHelp();
        init_FMenuItem1();
        init_mmSair();
        init_imageList();
        init_FMenuItem2();
        init_FMenuItem3();
        init_FMenuItem4();
        init_FMenuItem5();
        init_FMenuItem6();
        init_FMenuItem7();
        init_FMenuItem8();
        init_FMenuItem9();
        init_Pesquisar();
        init_FMenuItem10();
        init_FMenuItemCarAdd();
        init_LogoParts();
        init_tmrMensagem();
        init_borderPanel();
        init_pnlTop();
        init_FHBox2();
        init_FVBox2();
        init_FHBox10();
        init_imgLogo();
        init_FHBox4();
        init_FVBox3();
        init_hBoxPesquisas();
        init_lblUsuarioLogadoTopoEsquerdo();
        init_hBoxFiltros();
        init_hBoxFiltrosEsquerdos();
        init_FHBox6();
        init_imgBuscar();
        init_FHBox9();
        init_edtBuscar();
        init_FHBox5();
        init_btnSearch();
        init_btnPesquisaAvancadaCliente();
        init_FHBox14();
        init_hBoxFiltrosDireitos();
        init_FHBox8();
        init_imgBuscarItem();
        init_FHBox11();
        init_edtBuscarCodigo();
        init_FHBox12();
        init_btnSearchCodigo();
        init_FHBox13();
        init_FVBox1();
        init_hBoxVersaoSistemaTopoDireito();
        init_FHBox7();
        init_lblVersaoSistemaTopoDireito();
        init_FHBox1();
        init_vBoxPesqOrcItem();
        init_iconItensOrc();
        init_vBoxNovoImage();
        init_iconClassNovo();
        init_imageUsuario();
        init_pnlWest();
        init_accordion();
        init_lblVersaoSistema();
        init_FHBox15();
        init_pnlCenter();
        init_pgctrlPrincipal();
        init_tabHome();
        init_FHBox3();
        init_sc();
        init_FrmHome();
    }

    public USER_INFORMATION tbUserInformation;

    private void init_tbUserInformation() {
        tbUserInformation = rn.tbUserInformation;
        tbUserInformation.setName("tbUserInformation");
        tbUserInformation.setMaxRowCount(200);
        tbUserInformation.setWKey("29002;70002");
        tbUserInformation.setRatioBatchSize(20);
        getTables().put(tbUserInformation, "tbUserInformation");
        tbUserInformation.applyProperties();
    }

    public DBMENU tbMenu;

    private void init_tbMenu() {
        tbMenu = rn.tbMenu;
        tbMenu.setName("tbMenu");
        tbMenu.setMaxRowCount(200);
        tbMenu.setWKey("29002;70003");
        tbMenu.setRatioBatchSize(20);
        getTables().put(tbMenu, "tbMenu");
        tbMenu.applyProperties();
    }

    public SISTEMA_ACESSO tbSistemaAcesso;

    private void init_tbSistemaAcesso() {
        tbSistemaAcesso = rn.tbSistemaAcesso;
        tbSistemaAcesso.setName("tbSistemaAcesso");
        tbSistemaAcesso.setMaxRowCount(200);
        tbSistemaAcesso.setWKey("29002;70004");
        tbSistemaAcesso.setRatioBatchSize(20);
        getTables().put(tbSistemaAcesso, "tbSistemaAcesso");
        tbSistemaAcesso.applyProperties();
    }

    public FR_PROJETO tbProjeto;

    private void init_tbProjeto() {
        tbProjeto = rn.tbProjeto;
        tbProjeto.setName("tbProjeto");
        tbProjeto.setMaxRowCount(200);
        tbProjeto.setWKey("29002;46001");
        tbProjeto.setRatioBatchSize(20);
        getTables().put(tbProjeto, "tbProjeto");
        tbProjeto.applyProperties();
    }

    public FR_PROJETO tbProjetoConnDic;

    private void init_tbProjetoConnDic() {
        tbProjetoConnDic = rn.tbProjetoConnDic;
        tbProjetoConnDic.setName("tbProjetoConnDic");
        tbProjetoConnDic.setMaxRowCount(200);
        tbProjetoConnDic.setWKey("29002;46002");
        tbProjetoConnDic.setRatioBatchSize(20);
        getTables().put(tbProjetoConnDic, "tbProjetoConnDic");
        tbProjetoConnDic.applyProperties();
    }

    public FR_MODULO tbModulo;

    private void init_tbModulo() {
        tbModulo = rn.tbModulo;
        tbModulo.setName("tbModulo");
        tbModulo.setMaxRowCount(200);
        tbModulo.setWKey("29002;46003");
        tbModulo.setRatioBatchSize(20);
        getTables().put(tbModulo, "tbModulo");
        tbModulo.applyProperties();
    }

    public FR_MODULO tbModuloConnDic;

    private void init_tbModuloConnDic() {
        tbModuloConnDic = rn.tbModuloConnDic;
        tbModuloConnDic.setName("tbModuloConnDic");
        tbModuloConnDic.setMaxRowCount(200);
        tbModuloConnDic.setWKey("29002;46004");
        tbModuloConnDic.setRatioBatchSize(20);
        getTables().put(tbModuloConnDic, "tbModuloConnDic");
        tbModuloConnDic.applyProperties();
    }

    public FR_FORM tbForm;

    private void init_tbForm() {
        tbForm = rn.tbForm;
        tbForm.setName("tbForm");
        tbForm.setMaxRowCount(200);
        tbForm.setWKey("29002;46005");
        tbForm.setRatioBatchSize(20);
        getTables().put(tbForm, "tbForm");
        tbForm.applyProperties();
    }

    public FR_FORM tbFormConnDic;

    private void init_tbFormConnDic() {
        tbFormConnDic = rn.tbFormConnDic;
        tbFormConnDic.setName("tbFormConnDic");
        tbFormConnDic.setMaxRowCount(200);
        tbFormConnDic.setWKey("29002;46006");
        tbFormConnDic.setRatioBatchSize(20);
        getTables().put(tbFormConnDic, "tbFormConnDic");
        tbFormConnDic.applyProperties();
    }

    public FR_MENU tbFrMenu;

    private void init_tbFrMenu() {
        tbFrMenu = rn.tbFrMenu;
        tbFrMenu.setName("tbFrMenu");
        tbFrMenu.setMaxRowCount(200);
        tbFrMenu.setWKey("29002;46007");
        tbFrMenu.setRatioBatchSize(20);
        getTables().put(tbFrMenu, "tbFrMenu");
        tbFrMenu.applyProperties();
    }

    public FR_MENU tbFrMenuConnDic;

    private void init_tbFrMenuConnDic() {
        tbFrMenuConnDic = rn.tbFrMenuConnDic;
        tbFrMenuConnDic.setName("tbFrMenuConnDic");
        tbFrMenuConnDic.setMaxRowCount(200);
        tbFrMenuConnDic.setWKey("29002;46008");
        tbFrMenuConnDic.setRatioBatchSize(20);
        getTables().put(tbFrMenuConnDic, "tbFrMenuConnDic");
        tbFrMenuConnDic.applyProperties();
    }

    public DBMENU_ACESSO tbMenuAcesso;

    private void init_tbMenuAcesso() {
        tbMenuAcesso = rn.tbMenuAcesso;
        tbMenuAcesso.setName("tbMenuAcesso");
        tbMenuAcesso.setMaxRowCount(200);
        tbMenuAcesso.setWKey("29002;37901");
        tbMenuAcesso.setRatioBatchSize(20);
        getTables().put(tbMenuAcesso, "tbMenuAcesso");
        tbMenuAcesso.applyProperties();
    }

    public TFMenu mnuPrincipal = new TFMenu();

    private void init_mnuPrincipal() {
        mnuPrincipal.setName("mnuPrincipal");
        FrmHome.addChildren(mnuPrincipal);
        mnuPrincipal.applyProperties();
    }

    public TFTimer tmrRelogio = new TFTimer();

    private void init_tmrRelogio() {
        tmrRelogio.setName("tmrRelogio");
        tmrRelogio.setEnabled(false);
        tmrRelogio.setInterval(60000);
        tmrRelogio.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            tmrRelogioTimer(event);
            processarFlow("FrmHome", "tmrRelogio", "OnTimer");
        });
        tmrRelogio.setRepeats(false);
        FrmHome.addChildren(tmrRelogio);
        tmrRelogio.applyProperties();
    }

    public TFPopupMenu popMenuLogin = new TFPopupMenu();

    private void init_popMenuLogin() {
        popMenuLogin.setName("popMenuLogin");
        FrmHome.addChildren(popMenuLogin);
        popMenuLogin.applyProperties();
    }

    public TFMenuItem mmPerfil = new TFMenuItem();

    private void init_mmPerfil() {
        mmPerfil.setName("mmPerfil");
        mmPerfil.setCaption("Perfil");
        mmPerfil.setImageIndex(310061);
        mmPerfil.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmPerfilClick(event);
            processarFlow("FrmHome", "mmPerfil", "OnClick");
        });
        mmPerfil.setAccess(false);
        mmPerfil.setCheckmark(false);
        popMenuLogin.addChildren(mmPerfil);
        mmPerfil.applyProperties();
    }

    public TFMenuItem mmAlterarSenha = new TFMenuItem();

    private void init_mmAlterarSenha() {
        mmAlterarSenha.setName("mmAlterarSenha");
        mmAlterarSenha.setCaption("Alterar Senha");
        mmAlterarSenha.setImageIndex(310067);
        mmAlterarSenha.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmAlterarSenhaClick(event);
            processarFlow("FrmHome", "mmAlterarSenha", "OnClick");
        });
        mmAlterarSenha.setAccess(false);
        mmAlterarSenha.setCheckmark(false);
        popMenuLogin.addChildren(mmAlterarSenha);
        mmAlterarSenha.applyProperties();
    }

    public TFMenuItem mmHelp = new TFMenuItem();

    private void init_mmHelp() {
        mmHelp.setName("mmHelp");
        mmHelp.setCaption("Help");
        mmHelp.setImageIndex(7000109);
        mmHelp.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmHelpClick(event);
            processarFlow("FrmHome", "mmHelp", "OnClick");
        });
        mmHelp.setAccess(false);
        mmHelp.setCheckmark(false);
        popMenuLogin.addChildren(mmHelp);
        mmHelp.applyProperties();
    }

    public TFMenuItem FMenuItem1 = new TFMenuItem();

    private void init_FMenuItem1() {
        FMenuItem1.setName("FMenuItem1");
        FMenuItem1.setCaption("-");
        FMenuItem1.setAccess(false);
        FMenuItem1.setCheckmark(false);
        popMenuLogin.addChildren(FMenuItem1);
        FMenuItem1.applyProperties();
    }

    public TFMenuItem mmSair = new TFMenuItem();

    private void init_mmSair() {
        mmSair.setName("mmSair");
        mmSair.setCaption("Sair");
        mmSair.setImageIndex(310060);
        mmSair.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSairClick(event);
            processarFlow("FrmHome", "mmSair", "OnClick");
        });
        mmSair.setAccess(false);
        mmSair.setCheckmark(false);
        popMenuLogin.addChildren(mmSair);
        mmSair.applyProperties();
    }

    public TFPopupMenu imageList = new TFPopupMenu();

    private void init_imageList() {
        imageList.setName("imageList");
        FrmHome.addChildren(imageList);
        imageList.applyProperties();
    }

    public TFMenuItem FMenuItem2 = new TFMenuItem();

    private void init_FMenuItem2() {
        FMenuItem2.setName("FMenuItem2");
        FMenuItem2.setCaption("ImagemUsuario");
        FMenuItem2.setImageIndex(700090);
        FMenuItem2.setAccess(false);
        FMenuItem2.setCheckmark(false);
        imageList.addChildren(FMenuItem2);
        FMenuItem2.applyProperties();
    }

    public TFMenuItem FMenuItem3 = new TFMenuItem();

    private void init_FMenuItem3() {
        FMenuItem3.setName("FMenuItem3");
        FMenuItem3.setCaption("ImagemSair");
        FMenuItem3.setImageIndex(310065);
        FMenuItem3.setAccess(false);
        FMenuItem3.setCheckmark(false);
        imageList.addChildren(FMenuItem3);
        FMenuItem3.applyProperties();
    }

    public TFMenuItem FMenuItem4 = new TFMenuItem();

    private void init_FMenuItem4() {
        FMenuItem4.setName("FMenuItem4");
        FMenuItem4.setCaption("Calendar");
        FMenuItem4.setImageIndex(310062);
        FMenuItem4.setAccess(false);
        FMenuItem4.setCheckmark(false);
        imageList.addChildren(FMenuItem4);
        FMenuItem4.applyProperties();
    }

    public TFMenuItem FMenuItem5 = new TFMenuItem();

    private void init_FMenuItem5() {
        FMenuItem5.setName("FMenuItem5");
        FMenuItem5.setCaption("Globo");
        FMenuItem5.setImageIndex(310063);
        FMenuItem5.setAccess(false);
        FMenuItem5.setCheckmark(false);
        imageList.addChildren(FMenuItem5);
        FMenuItem5.applyProperties();
    }

    public TFMenuItem FMenuItem6 = new TFMenuItem();

    private void init_FMenuItem6() {
        FMenuItem6.setName("FMenuItem6");
        FMenuItem6.setCaption("User Cicle");
        FMenuItem6.setImageIndex(310064);
        FMenuItem6.setAccess(false);
        FMenuItem6.setCheckmark(false);
        imageList.addChildren(FMenuItem6);
        FMenuItem6.applyProperties();
    }

    public TFMenuItem FMenuItem7 = new TFMenuItem();

    private void init_FMenuItem7() {
        FMenuItem7.setName("FMenuItem7");
        FMenuItem7.setCaption("Account Logout Black");
        FMenuItem7.setImageIndex(310065);
        FMenuItem7.setAccess(false);
        FMenuItem7.setCheckmark(false);
        imageList.addChildren(FMenuItem7);
        FMenuItem7.applyProperties();
    }

    public TFMenuItem FMenuItem8 = new TFMenuItem();

    private void init_FMenuItem8() {
        FMenuItem8.setName("FMenuItem8");
        FMenuItem8.setCaption("Version");
        FMenuItem8.setImageIndex(310066);
        FMenuItem8.setAccess(false);
        FMenuItem8.setCheckmark(false);
        imageList.addChildren(FMenuItem8);
        FMenuItem8.applyProperties();
    }

    public TFMenuItem FMenuItem9 = new TFMenuItem();

    private void init_FMenuItem9() {
        FMenuItem9.setName("FMenuItem9");
        FMenuItem9.setCaption("Alterar Senha");
        FMenuItem9.setImageIndex(310067);
        FMenuItem9.setAccess(false);
        FMenuItem9.setCheckmark(false);
        imageList.addChildren(FMenuItem9);
        FMenuItem9.applyProperties();
    }

    public TFMenuItem Pesquisar = new TFMenuItem();

    private void init_Pesquisar() {
        Pesquisar.setName("Pesquisar");
        Pesquisar.setCaption("Pesquiisar");
        Pesquisar.setImageIndex(700089);
        Pesquisar.setAccess(false);
        Pesquisar.setCheckmark(false);
        imageList.addChildren(Pesquisar);
        Pesquisar.applyProperties();
    }

    public TFMenuItem FMenuItem10 = new TFMenuItem();

    private void init_FMenuItem10() {
        FMenuItem10.setName("FMenuItem10");
        FMenuItem10.setCaption("LogoNbs");
        FMenuItem10.setImageIndex(310038);
        FMenuItem10.setAccess(false);
        FMenuItem10.setCheckmark(false);
        imageList.addChildren(FMenuItem10);
        FMenuItem10.applyProperties();
    }

    public TFMenuItem FMenuItemCarAdd = new TFMenuItem();

    private void init_FMenuItemCarAdd() {
        FMenuItemCarAdd.setName("FMenuItemCarAdd");
        FMenuItemCarAdd.setCaption("FMenuItemCarAdd");
        FMenuItemCarAdd.setImageIndex(34008);
        FMenuItemCarAdd.setAccess(false);
        FMenuItemCarAdd.setCheckmark(false);
        imageList.addChildren(FMenuItemCarAdd);
        FMenuItemCarAdd.applyProperties();
    }

    public TFMenuItem LogoParts = new TFMenuItem();

    private void init_LogoParts() {
        LogoParts.setName("LogoParts");
        LogoParts.setCaption("LogoParts");
        LogoParts.setImageIndex(7000256);
        LogoParts.setAccess(false);
        LogoParts.setCheckmark(false);
        imageList.addChildren(LogoParts);
        LogoParts.applyProperties();
    }

    public TFTimer tmrMensagem = new TFTimer();

    private void init_tmrMensagem() {
        tmrMensagem.setName("tmrMensagem");
        tmrMensagem.setEnabled(false);
        tmrMensagem.setInterval(30000);
        tmrMensagem.addEventListener("onTimer", (EventListener<Event<Object>>)(Event<Object> event) -> {
            tmrMensagemTimer(event);
            processarFlow("FrmHome", "tmrMensagem", "OnTimer");
        });
        tmrMensagem.setRepeats(false);
        FrmHome.addChildren(tmrMensagem);
        tmrMensagem.applyProperties();
    }

    protected TFForm FrmHome = this;
    private void init_FrmHome() {
        FrmHome.setName("FrmHome");
        FrmHome.setCaption("Form Home");
        FrmHome.setClientHeight(494);
        FrmHome.setClientWidth(1248);
        FrmHome.setColor("clBtnFace");
        FrmHome.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmHome", "FrmHome", "OnCreate");
        });
        FrmHome.setWOrigem("EhMain");
        FrmHome.setWKey("29002");
        TFShortcutKeyItem item0 = new TFShortcutKeyItem();
        item0.setModifier("smCtrl");
        item0.setKey("sk0");
        item0.addEventListener("onKeyAction", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FrmHomepesquisarItensEmBranco(event);
            processarFlow("FrmHome", "item0", "OnKeyAction");
        });
        FrmHome.getShortcutKeys().add(item0);
        FrmHome.setSpacing(2);
        FrmHome.applyProperties();
    }

    public TFBorderPanel borderPanel = new TFBorderPanel();

    private void init_borderPanel() {
        borderPanel.setName("borderPanel");
        borderPanel.setLeft(0);
        borderPanel.setTop(0);
        borderPanel.setWidth(1248);
        borderPanel.setHeight(494);
        borderPanel.setNorth("pnlTop");
        borderPanel.setCenter("pnlCenter");
        borderPanel.setWest("pnlWest");
        borderPanel.setFlexVflex("ftTrue");
        borderPanel.setFlexHflex("ftTrue");
        borderPanel.setNorthCollapsible(false);
        borderPanel.setNorthSplittable(false);
        borderPanel.setNorthOpen(true);
        borderPanel.setSouthOpen(true);
        borderPanel.setEastOpen(true);
        borderPanel.setWestOpen(true);
        borderPanel.setSouthCollapsible(false);
        borderPanel.setSouthSplittable(false);
        borderPanel.setEastCollapsible(true);
        borderPanel.setEastSplittable(true);
        borderPanel.setWestCollapsible(true);
        borderPanel.setWestSplittable(true);
        borderPanel.setNorthSizePercent(0);
        borderPanel.setSouthSizePercent(0);
        borderPanel.setEastSizePercent(0);
        borderPanel.setWestSizePercent(0);
        FrmHome.addChildren(borderPanel);
        borderPanel.applyProperties();
    }

    public TFVBox pnlTop = new TFVBox();

    private void init_pnlTop() {
        pnlTop.setName("pnlTop");
        pnlTop.setLeft(0);
        pnlTop.setTop(0);
        pnlTop.setWidth(1244);
        pnlTop.setHeight(73);
        pnlTop.setBorderStyle("stBoxShadow");
        pnlTop.setPaddingTop(0);
        pnlTop.setPaddingLeft(0);
        pnlTop.setPaddingRight(0);
        pnlTop.setPaddingBottom(0);
        pnlTop.setMarginTop(0);
        pnlTop.setMarginLeft(1);
        pnlTop.setMarginRight(1);
        pnlTop.setMarginBottom(1);
        pnlTop.setSpacing(0);
        pnlTop.setFlexVflex("ftFalse");
        pnlTop.setFlexHflex("ftTrue");
        pnlTop.setScrollable(false);
        pnlTop.setBoxShadowConfigHorizontalLength(5);
        pnlTop.setBoxShadowConfigVerticalLength(0);
        pnlTop.setBoxShadowConfigBlurRadius(30);
        pnlTop.setBoxShadowConfigSpreadRadius(0);
        pnlTop.setBoxShadowConfigShadowColor("clBlack");
        pnlTop.setBoxShadowConfigOpacity(8);
        borderPanel.addChildren(pnlTop);
        pnlTop.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(1241);
        FHBox2.setHeight(67);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftTrue");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        pnlTop.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(258);
        FVBox2.setHeight(62);
        FVBox2.setBorderStyle("stBoxShadow");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(5);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(5);
        FVBox2.setBoxShadowConfigVerticalLength(5);
        FVBox2.setBoxShadowConfigBlurRadius(64);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(8);
        FHBox2.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(253);
        FHBox10.setHeight(57);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(10);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftTrue");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FVBox2.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFImage imgLogo = new TFImage();

    private void init_imgLogo() {
        imgLogo.setName("imgLogo");
        imgLogo.setLeft(0);
        imgLogo.setTop(0);
        imgLogo.setWidth(250);
        imgLogo.setHeight(50);
        imgLogo.setAlign("alRight");
        imgLogo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            imgLogoClick(event);
            processarFlow("FrmHome", "imgLogo", "OnClick");
        });
        imgLogo.setImageSrc("/images/crmparts7000256.jpg");
        imgLogo.setBoxSize(0);
        imgLogo.setGrayScaleOnDisable(false);
        imgLogo.setFlexVflex("ftFalse");
        imgLogo.setFlexHflex("ftFalse");
        imgLogo.setImageId(0);
        FHBox10.addChildren(imgLogo);
        imgLogo.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(258);
        FHBox4.setTop(0);
        FHBox4.setWidth(987);
        FHBox4.setHeight(62);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftTrue");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(10);
        FHBox4.setVAlign("tvTop");
        FHBox2.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(0);
        FVBox3.setTop(0);
        FVBox3.setWidth(864);
        FVBox3.setHeight(58);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFHBox hBoxPesquisas = new TFHBox();

    private void init_hBoxPesquisas() {
        hBoxPesquisas.setName("hBoxPesquisas");
        hBoxPesquisas.setLeft(0);
        hBoxPesquisas.setTop(0);
        hBoxPesquisas.setWidth(857);
        hBoxPesquisas.setHeight(20);
        hBoxPesquisas.setBorderStyle("stNone");
        hBoxPesquisas.setPaddingTop(0);
        hBoxPesquisas.setPaddingLeft(0);
        hBoxPesquisas.setPaddingRight(0);
        hBoxPesquisas.setPaddingBottom(0);
        hBoxPesquisas.setMarginTop(0);
        hBoxPesquisas.setMarginLeft(5);
        hBoxPesquisas.setMarginRight(0);
        hBoxPesquisas.setMarginBottom(0);
        hBoxPesquisas.setSpacing(1);
        hBoxPesquisas.setFlexVflex("ftFalse");
        hBoxPesquisas.setFlexHflex("ftFalse");
        hBoxPesquisas.setScrollable(false);
        hBoxPesquisas.setBoxShadowConfigHorizontalLength(10);
        hBoxPesquisas.setBoxShadowConfigVerticalLength(10);
        hBoxPesquisas.setBoxShadowConfigBlurRadius(5);
        hBoxPesquisas.setBoxShadowConfigSpreadRadius(0);
        hBoxPesquisas.setBoxShadowConfigShadowColor("clBlack");
        hBoxPesquisas.setBoxShadowConfigOpacity(75);
        hBoxPesquisas.setVAlign("tvTop");
        FVBox3.addChildren(hBoxPesquisas);
        hBoxPesquisas.applyProperties();
    }

    public TFLabel lblUsuarioLogadoTopoEsquerdo = new TFLabel();

    private void init_lblUsuarioLogadoTopoEsquerdo() {
        lblUsuarioLogadoTopoEsquerdo.setName("lblUsuarioLogadoTopoEsquerdo");
        lblUsuarioLogadoTopoEsquerdo.setLeft(0);
        lblUsuarioLogadoTopoEsquerdo.setTop(0);
        lblUsuarioLogadoTopoEsquerdo.setWidth(150);
        lblUsuarioLogadoTopoEsquerdo.setHeight(13);
        lblUsuarioLogadoTopoEsquerdo.setCaption("lblUsuarioLogadoTopoEsquerdo");
        lblUsuarioLogadoTopoEsquerdo.setFontColor("clSilver");
        lblUsuarioLogadoTopoEsquerdo.setFontSize(-11);
        lblUsuarioLogadoTopoEsquerdo.setFontName("Tahoma");
        lblUsuarioLogadoTopoEsquerdo.setFontStyle("[]");
        lblUsuarioLogadoTopoEsquerdo.setVerticalAlignment("taVerticalCenter");
        lblUsuarioLogadoTopoEsquerdo.setWordBreak(false);
        hBoxPesquisas.addChildren(lblUsuarioLogadoTopoEsquerdo);
        lblUsuarioLogadoTopoEsquerdo.applyProperties();
    }

    public TFHBox hBoxFiltros = new TFHBox();

    private void init_hBoxFiltros() {
        hBoxFiltros.setName("hBoxFiltros");
        hBoxFiltros.setLeft(0);
        hBoxFiltros.setTop(21);
        hBoxFiltros.setWidth(857);
        hBoxFiltros.setHeight(35);
        hBoxFiltros.setBorderStyle("stNone");
        hBoxFiltros.setPaddingTop(0);
        hBoxFiltros.setPaddingLeft(0);
        hBoxFiltros.setPaddingRight(0);
        hBoxFiltros.setPaddingBottom(0);
        hBoxFiltros.setMarginTop(0);
        hBoxFiltros.setMarginLeft(0);
        hBoxFiltros.setMarginRight(0);
        hBoxFiltros.setMarginBottom(0);
        hBoxFiltros.setSpacing(1);
        hBoxFiltros.setFlexVflex("ftTrue");
        hBoxFiltros.setFlexHflex("ftTrue");
        hBoxFiltros.setScrollable(false);
        hBoxFiltros.setBoxShadowConfigHorizontalLength(10);
        hBoxFiltros.setBoxShadowConfigVerticalLength(10);
        hBoxFiltros.setBoxShadowConfigBlurRadius(5);
        hBoxFiltros.setBoxShadowConfigSpreadRadius(0);
        hBoxFiltros.setBoxShadowConfigShadowColor("clBlack");
        hBoxFiltros.setBoxShadowConfigOpacity(75);
        hBoxFiltros.setVAlign("tvTop");
        FVBox3.addChildren(hBoxFiltros);
        hBoxFiltros.applyProperties();
    }

    public TFHBox hBoxFiltrosEsquerdos = new TFHBox();

    private void init_hBoxFiltrosEsquerdos() {
        hBoxFiltrosEsquerdos.setName("hBoxFiltrosEsquerdos");
        hBoxFiltrosEsquerdos.setLeft(0);
        hBoxFiltrosEsquerdos.setTop(0);
        hBoxFiltrosEsquerdos.setWidth(428);
        hBoxFiltrosEsquerdos.setHeight(30);
        hBoxFiltrosEsquerdos.setBorderStyle("stNone");
        hBoxFiltrosEsquerdos.setPaddingTop(0);
        hBoxFiltrosEsquerdos.setPaddingLeft(0);
        hBoxFiltrosEsquerdos.setPaddingRight(0);
        hBoxFiltrosEsquerdos.setPaddingBottom(0);
        hBoxFiltrosEsquerdos.setMarginTop(0);
        hBoxFiltrosEsquerdos.setMarginLeft(0);
        hBoxFiltrosEsquerdos.setMarginRight(0);
        hBoxFiltrosEsquerdos.setMarginBottom(0);
        hBoxFiltrosEsquerdos.setSpacing(1);
        hBoxFiltrosEsquerdos.setFlexVflex("ftTrue");
        hBoxFiltrosEsquerdos.setFlexHflex("ftTrue");
        hBoxFiltrosEsquerdos.setScrollable(false);
        hBoxFiltrosEsquerdos.setBoxShadowConfigHorizontalLength(10);
        hBoxFiltrosEsquerdos.setBoxShadowConfigVerticalLength(10);
        hBoxFiltrosEsquerdos.setBoxShadowConfigBlurRadius(5);
        hBoxFiltrosEsquerdos.setBoxShadowConfigSpreadRadius(0);
        hBoxFiltrosEsquerdos.setBoxShadowConfigShadowColor("clBlack");
        hBoxFiltrosEsquerdos.setBoxShadowConfigOpacity(75);
        hBoxFiltrosEsquerdos.setVAlign("tvTop");
        hBoxFiltros.addChildren(hBoxFiltrosEsquerdos);
        hBoxFiltrosEsquerdos.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(0);
        FHBox6.setWidth(10);
        FHBox6.setHeight(20);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        hBoxFiltrosEsquerdos.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFImage imgBuscar = new TFImage();

    private void init_imgBuscar() {
        imgBuscar.setName("imgBuscar");
        imgBuscar.setLeft(10);
        imgBuscar.setTop(0);
        imgBuscar.setWidth(24);
        imgBuscar.setHeight(24);
        imgBuscar.setImageSrc("/images/700089.png");
        imgBuscar.setBoxSize(0);
        imgBuscar.setGrayScaleOnDisable(false);
        imgBuscar.setFlexVflex("ftFalse");
        imgBuscar.setFlexHflex("ftFalse");
        imgBuscar.setImageId(0);
        hBoxFiltrosEsquerdos.addChildren(imgBuscar);
        imgBuscar.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(34);
        FHBox9.setTop(0);
        FHBox9.setWidth(10);
        FHBox9.setHeight(20);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        hBoxFiltrosEsquerdos.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFString edtBuscar = new TFString();

    private void init_edtBuscar() {
        edtBuscar.setName("edtBuscar");
        edtBuscar.setLeft(44);
        edtBuscar.setTop(0);
        edtBuscar.setWidth(250);
        edtBuscar.setHeight(24);
        edtBuscar.setFlex(true);
        edtBuscar.setRequired(false);
        edtBuscar.setPrompt("Buscar Por...");
        edtBuscar.setConstraintCheckWhen("cwImmediate");
        edtBuscar.setConstraintCheckType("ctExpression");
        edtBuscar.setConstraintFocusOnError(false);
        edtBuscar.setConstraintEnableUI(true);
        edtBuscar.setConstraintEnabled(false);
        edtBuscar.setConstraintFormCheck(true);
        edtBuscar.setCharCase("ccNormal");
        edtBuscar.setPwd(false);
        edtBuscar.setMaxlength(0);
        edtBuscar.setAlign("alLeft");
        edtBuscar.setFontColor("clWindowText");
        edtBuscar.setFontSize(-13);
        edtBuscar.setFontName("Tahoma");
        edtBuscar.setFontStyle("[]");
        edtBuscar.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtBuscarEnter(event);
            processarFlow("FrmHome", "edtBuscar", "OnEnter");
        });
        edtBuscar.addEventListener("onChanging", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtBuscarChanging(event);
            processarFlow("FrmHome", "edtBuscar", "OnChanging");
        });
        edtBuscar.setSaveLiteralCharacter(false);
        edtBuscar.applyProperties();
        hBoxFiltrosEsquerdos.addChildren(edtBuscar);
        addValidatable(edtBuscar);
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(294);
        FHBox5.setTop(0);
        FHBox5.setWidth(10);
        FHBox5.setHeight(20);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        hBoxFiltrosEsquerdos.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFButton btnSearch = new TFButton();

    private void init_btnSearch() {
        btnSearch.setName("btnSearch");
        btnSearch.setLeft(304);
        btnSearch.setTop(0);
        btnSearch.setWidth(50);
        btnSearch.setHeight(26);
        btnSearch.setAlign("alLeft");
        btnSearch.setCaption("Buscar");
        btnSearch.setFontColor("clWindowText");
        btnSearch.setFontSize(-11);
        btnSearch.setFontName("Tahoma");
        btnSearch.setFontStyle("[]");
        btnSearch.setVisible(false);
        btnSearch.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSearchClick(event);
            processarFlow("FrmHome", "btnSearch", "OnClick");
        });
        btnSearch.setImageId(0);
        btnSearch.setColor("clBtnFace");
        btnSearch.setAccess(false);
        btnSearch.setIconReverseDirection(false);
        hBoxFiltrosEsquerdos.addChildren(btnSearch);
        btnSearch.applyProperties();
    }

    public TFButton btnPesquisaAvancadaCliente = new TFButton();

    private void init_btnPesquisaAvancadaCliente() {
        btnPesquisaAvancadaCliente.setName("btnPesquisaAvancadaCliente");
        btnPesquisaAvancadaCliente.setLeft(354);
        btnPesquisaAvancadaCliente.setTop(0);
        btnPesquisaAvancadaCliente.setWidth(20);
        btnPesquisaAvancadaCliente.setHeight(20);
        btnPesquisaAvancadaCliente.setHint("Pesquisa avan\u00E7ada");
        btnPesquisaAvancadaCliente.setFontColor("clWindowText");
        btnPesquisaAvancadaCliente.setFontSize(-11);
        btnPesquisaAvancadaCliente.setFontName("Tahoma");
        btnPesquisaAvancadaCliente.setFontStyle("[]");
        btnPesquisaAvancadaCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisaAvancadaClienteClick(event);
            processarFlow("FrmHome", "btnPesquisaAvancadaCliente", "OnClick");
        });
        btnPesquisaAvancadaCliente.setImageId(5300469);
        btnPesquisaAvancadaCliente.setColor("clWindow");
        btnPesquisaAvancadaCliente.setAccess(false);
        btnPesquisaAvancadaCliente.setIconReverseDirection(false);
        hBoxFiltrosEsquerdos.addChildren(btnPesquisaAvancadaCliente);
        btnPesquisaAvancadaCliente.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(374);
        FHBox14.setTop(0);
        FHBox14.setWidth(10);
        FHBox14.setHeight(20);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        hBoxFiltrosEsquerdos.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFHBox hBoxFiltrosDireitos = new TFHBox();

    private void init_hBoxFiltrosDireitos() {
        hBoxFiltrosDireitos.setName("hBoxFiltrosDireitos");
        hBoxFiltrosDireitos.setLeft(428);
        hBoxFiltrosDireitos.setTop(0);
        hBoxFiltrosDireitos.setWidth(428);
        hBoxFiltrosDireitos.setHeight(30);
        hBoxFiltrosDireitos.setBorderStyle("stNone");
        hBoxFiltrosDireitos.setPaddingTop(0);
        hBoxFiltrosDireitos.setPaddingLeft(0);
        hBoxFiltrosDireitos.setPaddingRight(0);
        hBoxFiltrosDireitos.setPaddingBottom(0);
        hBoxFiltrosDireitos.setMarginTop(0);
        hBoxFiltrosDireitos.setMarginLeft(0);
        hBoxFiltrosDireitos.setMarginRight(0);
        hBoxFiltrosDireitos.setMarginBottom(0);
        hBoxFiltrosDireitos.setSpacing(1);
        hBoxFiltrosDireitos.setFlexVflex("ftTrue");
        hBoxFiltrosDireitos.setFlexHflex("ftFalse");
        hBoxFiltrosDireitos.setScrollable(false);
        hBoxFiltrosDireitos.setBoxShadowConfigHorizontalLength(10);
        hBoxFiltrosDireitos.setBoxShadowConfigVerticalLength(10);
        hBoxFiltrosDireitos.setBoxShadowConfigBlurRadius(5);
        hBoxFiltrosDireitos.setBoxShadowConfigSpreadRadius(0);
        hBoxFiltrosDireitos.setBoxShadowConfigShadowColor("clBlack");
        hBoxFiltrosDireitos.setBoxShadowConfigOpacity(75);
        hBoxFiltrosDireitos.setVAlign("tvTop");
        hBoxFiltros.addChildren(hBoxFiltrosDireitos);
        hBoxFiltrosDireitos.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(10);
        FHBox8.setHeight(20);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        hBoxFiltrosDireitos.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFImage imgBuscarItem = new TFImage();

    private void init_imgBuscarItem() {
        imgBuscarItem.setName("imgBuscarItem");
        imgBuscarItem.setLeft(10);
        imgBuscarItem.setTop(0);
        imgBuscarItem.setWidth(24);
        imgBuscarItem.setHeight(24);
        imgBuscarItem.setVisible(false);
        imgBuscarItem.setImageSrc("/images/700089.png");
        imgBuscarItem.setBoxSize(0);
        imgBuscarItem.setGrayScaleOnDisable(false);
        imgBuscarItem.setFlexVflex("ftFalse");
        imgBuscarItem.setFlexHflex("ftFalse");
        imgBuscarItem.setImageId(0);
        hBoxFiltrosDireitos.addChildren(imgBuscarItem);
        imgBuscarItem.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(34);
        FHBox11.setTop(0);
        FHBox11.setWidth(10);
        FHBox11.setHeight(20);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        hBoxFiltrosDireitos.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFString edtBuscarCodigo = new TFString();

    private void init_edtBuscarCodigo() {
        edtBuscarCodigo.setName("edtBuscarCodigo");
        edtBuscarCodigo.setLeft(44);
        edtBuscarCodigo.setTop(0);
        edtBuscarCodigo.setWidth(250);
        edtBuscarCodigo.setHeight(24);
        edtBuscarCodigo.setFlex(true);
        edtBuscarCodigo.setRequired(false);
        edtBuscarCodigo.setPrompt("Buscar por C\u00F3digo/Descri\u00E7\u00E3o Item");
        edtBuscarCodigo.setConstraintCheckWhen("cwImmediate");
        edtBuscarCodigo.setConstraintCheckType("ctExpression");
        edtBuscarCodigo.setConstraintFocusOnError(false);
        edtBuscarCodigo.setConstraintEnableUI(true);
        edtBuscarCodigo.setConstraintEnabled(false);
        edtBuscarCodigo.setConstraintFormCheck(true);
        edtBuscarCodigo.setCharCase("ccNormal");
        edtBuscarCodigo.setPwd(false);
        edtBuscarCodigo.setMaxlength(0);
        edtBuscarCodigo.setAlign("alLeft");
        edtBuscarCodigo.setVisible(false);
        edtBuscarCodigo.setFontColor("clWindowText");
        edtBuscarCodigo.setFontSize(-13);
        edtBuscarCodigo.setFontName("Tahoma");
        edtBuscarCodigo.setFontStyle("[]");
        edtBuscarCodigo.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtBuscarCodigoEnter(event);
            processarFlow("FrmHome", "edtBuscarCodigo", "OnEnter");
        });
        edtBuscarCodigo.addEventListener("onChanging", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtBuscarCodigoChanging(event);
            processarFlow("FrmHome", "edtBuscarCodigo", "OnChanging");
        });
        edtBuscarCodigo.setSaveLiteralCharacter(false);
        edtBuscarCodigo.applyProperties();
        hBoxFiltrosDireitos.addChildren(edtBuscarCodigo);
        addValidatable(edtBuscarCodigo);
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(294);
        FHBox12.setTop(0);
        FHBox12.setWidth(10);
        FHBox12.setHeight(20);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        hBoxFiltrosDireitos.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFButton btnSearchCodigo = new TFButton();

    private void init_btnSearchCodigo() {
        btnSearchCodigo.setName("btnSearchCodigo");
        btnSearchCodigo.setLeft(304);
        btnSearchCodigo.setTop(0);
        btnSearchCodigo.setWidth(50);
        btnSearchCodigo.setHeight(26);
        btnSearchCodigo.setAlign("alLeft");
        btnSearchCodigo.setCaption("Buscar");
        btnSearchCodigo.setFontColor("clWindowText");
        btnSearchCodigo.setFontSize(-11);
        btnSearchCodigo.setFontName("Tahoma");
        btnSearchCodigo.setFontStyle("[]");
        btnSearchCodigo.setVisible(false);
        btnSearchCodigo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSearchCodigoClick(event);
            processarFlow("FrmHome", "btnSearchCodigo", "OnClick");
        });
        btnSearchCodigo.setImageId(0);
        btnSearchCodigo.setColor("clBtnFace");
        btnSearchCodigo.setAccess(false);
        btnSearchCodigo.setIconReverseDirection(false);
        hBoxFiltrosDireitos.addChildren(btnSearchCodigo);
        btnSearchCodigo.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(354);
        FHBox13.setTop(0);
        FHBox13.setWidth(10);
        FHBox13.setHeight(20);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        hBoxFiltrosDireitos.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(864);
        FVBox1.setTop(0);
        FVBox1.setWidth(117);
        FVBox1.setHeight(61);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox4.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox hBoxVersaoSistemaTopoDireito = new TFHBox();

    private void init_hBoxVersaoSistemaTopoDireito() {
        hBoxVersaoSistemaTopoDireito.setName("hBoxVersaoSistemaTopoDireito");
        hBoxVersaoSistemaTopoDireito.setLeft(0);
        hBoxVersaoSistemaTopoDireito.setTop(0);
        hBoxVersaoSistemaTopoDireito.setWidth(108);
        hBoxVersaoSistemaTopoDireito.setHeight(20);
        hBoxVersaoSistemaTopoDireito.setBorderStyle("stNone");
        hBoxVersaoSistemaTopoDireito.setPaddingTop(0);
        hBoxVersaoSistemaTopoDireito.setPaddingLeft(0);
        hBoxVersaoSistemaTopoDireito.setPaddingRight(0);
        hBoxVersaoSistemaTopoDireito.setPaddingBottom(0);
        hBoxVersaoSistemaTopoDireito.setMarginTop(0);
        hBoxVersaoSistemaTopoDireito.setMarginLeft(0);
        hBoxVersaoSistemaTopoDireito.setMarginRight(5);
        hBoxVersaoSistemaTopoDireito.setMarginBottom(0);
        hBoxVersaoSistemaTopoDireito.setSpacing(1);
        hBoxVersaoSistemaTopoDireito.setFlexVflex("ftFalse");
        hBoxVersaoSistemaTopoDireito.setFlexHflex("ftTrue");
        hBoxVersaoSistemaTopoDireito.setScrollable(false);
        hBoxVersaoSistemaTopoDireito.setBoxShadowConfigHorizontalLength(10);
        hBoxVersaoSistemaTopoDireito.setBoxShadowConfigVerticalLength(10);
        hBoxVersaoSistemaTopoDireito.setBoxShadowConfigBlurRadius(5);
        hBoxVersaoSistemaTopoDireito.setBoxShadowConfigSpreadRadius(0);
        hBoxVersaoSistemaTopoDireito.setBoxShadowConfigShadowColor("clBlack");
        hBoxVersaoSistemaTopoDireito.setBoxShadowConfigOpacity(75);
        hBoxVersaoSistemaTopoDireito.setVAlign("tvTop");
        FVBox1.addChildren(hBoxVersaoSistemaTopoDireito);
        hBoxVersaoSistemaTopoDireito.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(5);
        FHBox7.setHeight(10);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftTrue");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        hBoxVersaoSistemaTopoDireito.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFLabel lblVersaoSistemaTopoDireito = new TFLabel();

    private void init_lblVersaoSistemaTopoDireito() {
        lblVersaoSistemaTopoDireito.setName("lblVersaoSistemaTopoDireito");
        lblVersaoSistemaTopoDireito.setLeft(5);
        lblVersaoSistemaTopoDireito.setTop(0);
        lblVersaoSistemaTopoDireito.setWidth(135);
        lblVersaoSistemaTopoDireito.setHeight(13);
        lblVersaoSistemaTopoDireito.setCaption("lblVersaoSistemaTopoDireito");
        lblVersaoSistemaTopoDireito.setFontColor("clSilver");
        lblVersaoSistemaTopoDireito.setFontSize(-11);
        lblVersaoSistemaTopoDireito.setFontName("Tahoma");
        lblVersaoSistemaTopoDireito.setFontStyle("[]");
        lblVersaoSistemaTopoDireito.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblVersaoSistemaTopoDireitoClick(event);
            processarFlow("FrmHome", "lblVersaoSistemaTopoDireito", "OnClick");
        });
        lblVersaoSistemaTopoDireito.setVerticalAlignment("taVerticalCenter");
        lblVersaoSistemaTopoDireito.setWordBreak(false);
        hBoxVersaoSistemaTopoDireito.addChildren(lblVersaoSistemaTopoDireito);
        lblVersaoSistemaTopoDireito.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(21);
        FHBox1.setWidth(110);
        FHBox1.setHeight(34);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox vBoxPesqOrcItem = new TFVBox();

    private void init_vBoxPesqOrcItem() {
        vBoxPesqOrcItem.setName("vBoxPesqOrcItem");
        vBoxPesqOrcItem.setLeft(0);
        vBoxPesqOrcItem.setTop(0);
        vBoxPesqOrcItem.setWidth(37);
        vBoxPesqOrcItem.setHeight(29);
        vBoxPesqOrcItem.setAlign("alLeft");
        vBoxPesqOrcItem.setBorderStyle("stNone");
        vBoxPesqOrcItem.setPaddingTop(6);
        vBoxPesqOrcItem.setPaddingLeft(0);
        vBoxPesqOrcItem.setPaddingRight(0);
        vBoxPesqOrcItem.setPaddingBottom(0);
        vBoxPesqOrcItem.setVisible(false);
        vBoxPesqOrcItem.setMarginTop(0);
        vBoxPesqOrcItem.setMarginLeft(0);
        vBoxPesqOrcItem.setMarginRight(0);
        vBoxPesqOrcItem.setMarginBottom(0);
        vBoxPesqOrcItem.setSpacing(1);
        vBoxPesqOrcItem.setFlexVflex("ftTrue");
        vBoxPesqOrcItem.setFlexHflex("ftTrue");
        vBoxPesqOrcItem.setScrollable(false);
        vBoxPesqOrcItem.setBoxShadowConfigHorizontalLength(10);
        vBoxPesqOrcItem.setBoxShadowConfigVerticalLength(10);
        vBoxPesqOrcItem.setBoxShadowConfigBlurRadius(5);
        vBoxPesqOrcItem.setBoxShadowConfigSpreadRadius(0);
        vBoxPesqOrcItem.setBoxShadowConfigShadowColor("clBlack");
        vBoxPesqOrcItem.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(vBoxPesqOrcItem);
        vBoxPesqOrcItem.applyProperties();
    }

    public TFIconClass iconItensOrc = new TFIconClass();

    private void init_iconItensOrc() {
        iconItensOrc.setName("iconItensOrc");
        iconItensOrc.setLeft(0);
        iconItensOrc.setTop(0);
        iconItensOrc.setHint("Pesquisar todos Or\u00E7amentos que cont\u00E9m um determinado item.");
        iconItensOrc.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconItensOrcClick(event);
            processarFlow("FrmHome", "iconItensOrc", "OnClick");
        });
        iconItensOrc.setIconClass("shopping-cart");
        iconItensOrc.setSize(26);
        iconItensOrc.setColor("6776679");
        vBoxPesqOrcItem.addChildren(iconItensOrc);
        iconItensOrc.applyProperties();
    }

    public TFVBox vBoxNovoImage = new TFVBox();

    private void init_vBoxNovoImage() {
        vBoxNovoImage.setName("vBoxNovoImage");
        vBoxNovoImage.setLeft(37);
        vBoxNovoImage.setTop(0);
        vBoxNovoImage.setWidth(37);
        vBoxNovoImage.setHeight(29);
        vBoxNovoImage.setAlign("alLeft");
        vBoxNovoImage.setBorderStyle("stNone");
        vBoxNovoImage.setPaddingTop(6);
        vBoxNovoImage.setPaddingLeft(0);
        vBoxNovoImage.setPaddingRight(0);
        vBoxNovoImage.setPaddingBottom(0);
        vBoxNovoImage.setVisible(false);
        vBoxNovoImage.setMarginTop(0);
        vBoxNovoImage.setMarginLeft(0);
        vBoxNovoImage.setMarginRight(0);
        vBoxNovoImage.setMarginBottom(0);
        vBoxNovoImage.setSpacing(1);
        vBoxNovoImage.setFlexVflex("ftTrue");
        vBoxNovoImage.setFlexHflex("ftTrue");
        vBoxNovoImage.setScrollable(false);
        vBoxNovoImage.setBoxShadowConfigHorizontalLength(10);
        vBoxNovoImage.setBoxShadowConfigVerticalLength(10);
        vBoxNovoImage.setBoxShadowConfigBlurRadius(5);
        vBoxNovoImage.setBoxShadowConfigSpreadRadius(0);
        vBoxNovoImage.setBoxShadowConfigShadowColor("clBlack");
        vBoxNovoImage.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(vBoxNovoImage);
        vBoxNovoImage.applyProperties();
    }

    public TFIconClass iconClassNovo = new TFIconClass();

    private void init_iconClassNovo() {
        iconClassNovo.setName("iconClassNovo");
        iconClassNovo.setLeft(0);
        iconClassNovo.setTop(0);
        iconClassNovo.setHint("Novo");
        iconClassNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassNovoClick(event);
            processarFlow("FrmHome", "iconClassNovo", "OnClick");
        });
        iconClassNovo.setIconClass("user-plus");
        iconClassNovo.setSize(26);
        iconClassNovo.setColor("6776679");
        vBoxNovoImage.addChildren(iconClassNovo);
        iconClassNovo.applyProperties();
    }

    public TFImage imageUsuario = new TFImage();

    private void init_imageUsuario() {
        imageUsuario.setName("imageUsuario");
        imageUsuario.setLeft(74);
        imageUsuario.setTop(0);
        imageUsuario.setWidth(30);
        imageUsuario.setHeight(30);
        imageUsuario.setHint("Usu\u00E1rio");
        imageUsuario.setVisible(false);
        imageUsuario.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            imageUsuarioClick(event);
            processarFlow("FrmHome", "imageUsuario", "OnClick");
        });
        imageUsuario.setImageSrc("/images/700090.png");
        imageUsuario.setBoxSize(0);
        imageUsuario.setGrayScaleOnDisable(false);
        imageUsuario.setFlexVflex("ftFalse");
        imageUsuario.setFlexHflex("ftFalse");
        imageUsuario.setImageId(0);
        FHBox1.addChildren(imageUsuario);
        imageUsuario.applyProperties();
    }

    public TFVBox pnlWest = new TFVBox();

    private void init_pnlWest() {
        pnlWest.setName("pnlWest");
        pnlWest.setLeft(2);
        pnlWest.setTop(76);
        pnlWest.setWidth(260);
        pnlWest.setHeight(390);
        pnlWest.setBorderStyle("stNone");
        pnlWest.setPaddingTop(0);
        pnlWest.setPaddingLeft(0);
        pnlWest.setPaddingRight(0);
        pnlWest.setPaddingBottom(0);
        pnlWest.setMarginTop(0);
        pnlWest.setMarginLeft(0);
        pnlWest.setMarginRight(0);
        pnlWest.setMarginBottom(0);
        pnlWest.setSpacing(1);
        pnlWest.setFlexVflex("ftTrue");
        pnlWest.setFlexHflex("ftFalse");
        pnlWest.setScrollable(false);
        pnlWest.setBoxShadowConfigHorizontalLength(10);
        pnlWest.setBoxShadowConfigVerticalLength(10);
        pnlWest.setBoxShadowConfigBlurRadius(5);
        pnlWest.setBoxShadowConfigSpreadRadius(0);
        pnlWest.setBoxShadowConfigShadowColor("clBlack");
        pnlWest.setBoxShadowConfigOpacity(75);
        borderPanel.addChildren(pnlWest);
        pnlWest.applyProperties();
    }

    public TFAccordionMenu accordion = new TFAccordionMenu();

    private void init_accordion() {
        accordion.setName("accordion");
        accordion.setLeft(0);
        accordion.setTop(0);
        accordion.setWidth(244);
        accordion.setHeight(366);
        accordion.setFlexVflex("ftTrue");
        accordion.setFlexHflex("ftTrue");
        accordion.setMenu(mnuPrincipal);
        accordion.setExpandAll(false);
        accordion.setShowSearchBar(false);
        accordion.setOpenOnSelect(true);
        pnlWest.addChildren(accordion);
        accordion.applyProperties();
    }

    public TFLabel lblVersaoSistema = new TFLabel();

    private void init_lblVersaoSistema() {
        lblVersaoSistema.setName("lblVersaoSistema");
        lblVersaoSistema.setLeft(0);
        lblVersaoSistema.setTop(367);
        lblVersaoSistema.setWidth(76);
        lblVersaoSistema.setHeight(13);
        lblVersaoSistema.setCaption("Vers\u00E3o: *******");
        lblVersaoSistema.setFontColor("clWindow");
        lblVersaoSistema.setFontSize(-11);
        lblVersaoSistema.setFontName("Tahoma");
        lblVersaoSistema.setFontStyle("[fsUnderline]");
        lblVersaoSistema.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblVersaoSistemaClick(event);
            processarFlow("FrmHome", "lblVersaoSistema", "OnClick");
        });
        lblVersaoSistema.setVerticalAlignment("taVerticalCenter");
        lblVersaoSistema.setWordBreak(false);
        pnlWest.addChildren(lblVersaoSistema);
        lblVersaoSistema.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(0);
        FHBox15.setTop(381);
        FHBox15.setWidth(185);
        FHBox15.setHeight(5);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        pnlWest.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFVBox pnlCenter = new TFVBox();

    private void init_pnlCenter() {
        pnlCenter.setName("pnlCenter");
        pnlCenter.setLeft(262);
        pnlCenter.setTop(76);
        pnlCenter.setWidth(572);
        pnlCenter.setHeight(389);
        pnlCenter.setBorderStyle("stNone");
        pnlCenter.setPaddingTop(0);
        pnlCenter.setPaddingLeft(1);
        pnlCenter.setPaddingRight(0);
        pnlCenter.setPaddingBottom(0);
        pnlCenter.setMarginTop(0);
        pnlCenter.setMarginLeft(0);
        pnlCenter.setMarginRight(0);
        pnlCenter.setMarginBottom(0);
        pnlCenter.setSpacing(1);
        pnlCenter.setFlexVflex("ftTrue");
        pnlCenter.setFlexHflex("ftTrue");
        pnlCenter.setScrollable(false);
        pnlCenter.setBoxShadowConfigHorizontalLength(10);
        pnlCenter.setBoxShadowConfigVerticalLength(10);
        pnlCenter.setBoxShadowConfigBlurRadius(5);
        pnlCenter.setBoxShadowConfigSpreadRadius(0);
        pnlCenter.setBoxShadowConfigShadowColor("clBlack");
        pnlCenter.setBoxShadowConfigOpacity(75);
        borderPanel.addChildren(pnlCenter);
        pnlCenter.applyProperties();
    }

    public TFPageControl pgctrlPrincipal = new TFPageControl();

    private void init_pgctrlPrincipal() {
        pgctrlPrincipal.setName("pgctrlPrincipal");
        pgctrlPrincipal.setLeft(0);
        pgctrlPrincipal.setTop(0);
        pgctrlPrincipal.setWidth(554);
        pgctrlPrincipal.setHeight(351);
        pgctrlPrincipal.setTabPosition("tpTop");
        pgctrlPrincipal.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgctrlPrincipalChange(event);
            processarFlow("FrmHome", "pgctrlPrincipal", "OnChange");
        });
        pgctrlPrincipal.setFlexVflex("ftTrue");
        pgctrlPrincipal.setFlexHflex("ftTrue");
        pgctrlPrincipal.setRenderStyle("rsTabbed");
        pgctrlPrincipal.applyProperties();
        pnlCenter.addChildren(pgctrlPrincipal);
    }

    public TFTabsheet tabHome = new TFTabsheet();

    private void init_tabHome() {
        tabHome.setName("tabHome");
        tabHome.setCaption("Painel");
        tabHome.setVisible(true);
        tabHome.setClosable(false);
        pgctrlPrincipal.addChildren(tabHome);
        tabHome.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(10);
        FHBox3.setTop(0);
        FHBox3.setWidth(10);
        FHBox3.setHeight(20);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FrmHome.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbProjeto);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbModulo);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbForm);
        sc.getTables().add(item2);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void FrmHomepesquisarItensEmBranco(final Event<Object> event);

    public abstract void imgLogoClick(final Event<Object> event);

    public abstract void edtBuscarEnter(final Event<Object> event);

    public abstract void edtBuscarChanging(final Event<Object> event);

    public void btnSearchClick(final Event<Object> event) {
        if (btnSearch.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSearch");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisaAvancadaClienteClick(final Event<Object> event) {
        if (btnPesquisaAvancadaCliente.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisaAvancadaCliente");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtBuscarCodigoEnter(final Event<Object> event);

    public abstract void edtBuscarCodigoChanging(final Event<Object> event);

    public void btnSearchCodigoClick(final Event<Object> event) {
        if (btnSearchCodigo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSearchCodigo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void lblVersaoSistemaTopoDireitoClick(final Event<Object> event);

    public abstract void iconItensOrcClick(final Event<Object> event);

    public abstract void iconClassNovoClick(final Event<Object> event);

    public abstract void imageUsuarioClick(final Event<Object> event);

    public abstract void pgctrlPrincipalChange(final Event<Object> event);

    public abstract void lblVersaoSistemaClick(final Event<Object> event);

    public abstract void tmrRelogioTimer(final Event<Object> event);

    public abstract void mmPerfilClick(final Event<Object> event);

    public abstract void mmAlterarSenhaClick(final Event<Object> event);

    public abstract void mmHelpClick(final Event<Object> event);

    public abstract void mmSairClick(final Event<Object> event);

    public abstract void tmrMensagemTimer(final Event<Object> event);

}