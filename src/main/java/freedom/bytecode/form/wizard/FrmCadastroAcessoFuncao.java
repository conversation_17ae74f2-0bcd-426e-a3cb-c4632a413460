package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCadastroAcessoFuncao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CadastroAcessoFuncaoRNA rn = null;

    public FrmCadastroAcessoFuncao() {
        try {
            rn = (freedom.bytecode.rn.CadastroAcessoFuncaoRNA) getRN(freedom.bytecode.rn.wizard.CadastroAcessoFuncaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbGridCadAcessoFunc();
        init_tbPainelAcessoFuncao();
        init_vboxPrincipal();
        init_FHBox1();
        init_btnSalvar();
        init_btnCancelar();
        init_gridAcessoFuncao();
        init_FrmCadastroAcessoFuncao();
    }

    public BSC_GRID_CAD_ACESSO_FUNC tbGridCadAcessoFunc;

    private void init_tbGridCadAcessoFunc() {
        tbGridCadAcessoFunc = rn.tbGridCadAcessoFunc;
        tbGridCadAcessoFunc.setName("tbGridCadAcessoFunc");
        tbGridCadAcessoFunc.setMaxRowCount(200);
        tbGridCadAcessoFunc.setWKey("382034;38201");
        tbGridCadAcessoFunc.setRatioBatchSize(20);
        getTables().put(tbGridCadAcessoFunc, "tbGridCadAcessoFunc");
        tbGridCadAcessoFunc.applyProperties();
    }

    public BSC_PAINEL_ACESSO_FUNCAO tbPainelAcessoFuncao;

    private void init_tbPainelAcessoFuncao() {
        tbPainelAcessoFuncao = rn.tbPainelAcessoFuncao;
        tbPainelAcessoFuncao.setName("tbPainelAcessoFuncao");
        tbPainelAcessoFuncao.setMaxRowCount(200);
        tbPainelAcessoFuncao.setWKey("382034;38202");
        tbPainelAcessoFuncao.setRatioBatchSize(20);
        getTables().put(tbPainelAcessoFuncao, "tbPainelAcessoFuncao");
        tbPainelAcessoFuncao.applyProperties();
    }

    protected TFForm FrmCadastroAcessoFuncao = this;
    private void init_FrmCadastroAcessoFuncao() {
        FrmCadastroAcessoFuncao.setName("FrmCadastroAcessoFuncao");
        FrmCadastroAcessoFuncao.setCaption("Nova Fun\u00E7\u00E3o");
        FrmCadastroAcessoFuncao.setClientHeight(372);
        FrmCadastroAcessoFuncao.setClientWidth(495);
        FrmCadastroAcessoFuncao.setColor("clBtnFace");
        FrmCadastroAcessoFuncao.setWKey("382034");
        FrmCadastroAcessoFuncao.setSpacing(0);
        FrmCadastroAcessoFuncao.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(495);
        vboxPrincipal.setHeight(372);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(5);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(5);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(5);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmCadastroAcessoFuncao.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(476);
        FHBox1.setHeight(70);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(5);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vboxPrincipal.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(0);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(75);
        btnSalvar.setHeight(60);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCadastroAcessoFuncao", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox1.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(75);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(75);
        btnCancelar.setHeight(60);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmCadastroAcessoFuncao", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        FHBox1.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFGrid gridAcessoFuncao = new TFGrid();

    private void init_gridAcessoFuncao() {
        gridAcessoFuncao.setName("gridAcessoFuncao");
        gridAcessoFuncao.setLeft(0);
        gridAcessoFuncao.setTop(71);
        gridAcessoFuncao.setWidth(474);
        gridAcessoFuncao.setHeight(232);
        gridAcessoFuncao.setTable(tbGridCadAcessoFunc);
        gridAcessoFuncao.setFlexVflex("ftTrue");
        gridAcessoFuncao.setFlexHflex("ftTrue");
        gridAcessoFuncao.setPagingEnabled(false);
        gridAcessoFuncao.setFrozenColumns(0);
        gridAcessoFuncao.setShowFooter(false);
        gridAcessoFuncao.setShowHeader(true);
        gridAcessoFuncao.setMultiSelection(false);
        gridAcessoFuncao.setGroupingEnabled(false);
        gridAcessoFuncao.setGroupingExpanded(false);
        gridAcessoFuncao.setGroupingShowFooter(false);
        gridAcessoFuncao.setCrosstabEnabled(false);
        gridAcessoFuncao.setCrosstabGroupType("cgtConcat");
        gridAcessoFuncao.setEditionEnabled(false);
        gridAcessoFuncao.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEL");
        item0.setTitleCaption("Sel");
        item0.setWidth(60);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL = 'N'");
        item1.setEvalType("etExpression");
        item1.setImageId(34006);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridAcessoFuncaogridFuncaoUnChecked(event);
            processarFlow("FrmCadastroAcessoFuncao", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL = 'S'");
        item2.setEvalType("etExpression");
        item2.setImageId(34007);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridAcessoFuncaogridFuncaoChecked(event);
            processarFlow("FrmCadastroAcessoFuncao", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridAcessoFuncao.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("ID");
        item3.setTitleCaption("Id");
        item3.setWidth(60);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridAcessoFuncao.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("FUNCAO");
        item4.setTitleCaption("Fun\u00E7\u00E3o");
        item4.setWidth(120);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridAcessoFuncao.getColumns().add(item4);
        vboxPrincipal.addChildren(gridAcessoFuncao);
        gridAcessoFuncao.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridAcessoFuncaogridFuncaoUnChecked(final Event<Object> event);

    public abstract void gridAcessoFuncaogridFuncaoChecked(final Event<Object> event);

}