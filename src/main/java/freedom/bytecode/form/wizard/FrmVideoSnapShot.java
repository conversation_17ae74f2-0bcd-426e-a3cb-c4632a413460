package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmVideoSnapShot extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.VideoSnapShotRNA rn = null;

    public FrmVideoSnapShot() {
        try {
            rn = (freedom.bytecode.rn.VideoSnapShotRNA) getRN(freedom.bytecode.rn.wizard.VideoSnapShotRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FVBox1();
        init_video();
        init_imagePreview();
        init_videoPreview();
        init_hBoxOpcoes();
        init_FVBox2();
        init_iconOk();
        init_vBoxCapturarVideo();
        init_buttonCapturarVideo();
        init_vBoxCancel();
        init_iconCancel();
        init_vboxStop();
        init_iconStop();
        init_hBoxClose();
        init_btnClose();
        init_FVBox4();
        init_FrmVideoSnapShot();
    }

    protected TFForm FrmVideoSnapShot = this;
    private void init_FrmVideoSnapShot() {
        FrmVideoSnapShot.setName("FrmVideoSnapShot");
        FrmVideoSnapShot.setCaption("Video");
        FrmVideoSnapShot.setClientHeight(874);
        FrmVideoSnapShot.setClientWidth(584);
        FrmVideoSnapShot.setColor("clBtnFace");
        FrmVideoSnapShot.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmVideoSnapShot", "FrmVideoSnapShot", "OnCreate");
        });
        FrmVideoSnapShot.setWKey("16704");
        FrmVideoSnapShot.setSpacing(0);
        FrmVideoSnapShot.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(584);
        FVBox1.setHeight(874);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmVideoSnapShot.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFCamera video = new TFCamera();

    private void init_video() {
        video.setName("video");
        video.setLeft(0);
        video.setTop(0);
        video.setWidth(644);
        video.setHeight(787);
        video.setMaxSize(-1);
        video.setAudio(false);
        video.setPreviewRecord(true);
        video.setLengthLimit(120);
        video.setSnapshotFormat("image/jpeg");
        video.setRecordFormat("video/mp4");
        video.addEventListener("onVideoUpload", (EventListener<UploadEvent>) (UploadEvent event) -> {
            videoSnapshotUpload(event);
            processarFlow("FrmVideoSnapShot", "video", "OnVideoUpload");
        });
        video.setFlexVflex("ftTrue");
        video.setFlexHflex("ftTrue");
        FVBox1.addChildren(video);
        video.applyProperties();
    }

    public TFImage imagePreview = new TFImage();

    private void init_imagePreview() {
        imagePreview.setName("imagePreview");
        imagePreview.setLeft(0);
        imagePreview.setTop(788);
        imagePreview.setWidth(24);
        imagePreview.setHeight(20);
        imagePreview.setVisible(false);
        imagePreview.setBoxSize(0);
        imagePreview.setGrayScaleOnDisable(false);
        imagePreview.setFlexVflex("ftTrue");
        imagePreview.setFlexHflex("ftTrue");
        FVBox1.addChildren(imagePreview);
        imagePreview.applyProperties();
    }

    public TFVBox videoPreview = new TFVBox();

    private void init_videoPreview() {
        videoPreview.setName("videoPreview");
        videoPreview.setLeft(0);
        videoPreview.setTop(809);
        videoPreview.setWidth(477);
        videoPreview.setHeight(400);
        videoPreview.setBorderStyle("stNone");
        videoPreview.setPaddingTop(0);
        videoPreview.setPaddingLeft(0);
        videoPreview.setPaddingRight(0);
        videoPreview.setPaddingBottom(0);
        videoPreview.setVisible(false);
        videoPreview.setMarginTop(0);
        videoPreview.setMarginLeft(0);
        videoPreview.setMarginRight(0);
        videoPreview.setMarginBottom(0);
        videoPreview.setSpacing(1);
        videoPreview.setFlexVflex("ftFalse");
        videoPreview.setFlexHflex("ftTrue");
        videoPreview.setScrollable(false);
        videoPreview.setBoxShadowConfigHorizontalLength(10);
        videoPreview.setBoxShadowConfigVerticalLength(10);
        videoPreview.setBoxShadowConfigBlurRadius(5);
        videoPreview.setBoxShadowConfigSpreadRadius(0);
        videoPreview.setBoxShadowConfigShadowColor("clBlack");
        videoPreview.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(videoPreview);
        videoPreview.applyProperties();
    }

    public TFHBox hBoxOpcoes = new TFHBox();

    private void init_hBoxOpcoes() {
        hBoxOpcoes.setName("hBoxOpcoes");
        hBoxOpcoes.setLeft(0);
        hBoxOpcoes.setTop(1210);
        hBoxOpcoes.setWidth(646);
        hBoxOpcoes.setHeight(64);
        hBoxOpcoes.setBorderStyle("stNone");
        hBoxOpcoes.setPaddingTop(5);
        hBoxOpcoes.setPaddingLeft(5);
        hBoxOpcoes.setPaddingRight(5);
        hBoxOpcoes.setPaddingBottom(0);
        hBoxOpcoes.setMarginTop(0);
        hBoxOpcoes.setMarginLeft(0);
        hBoxOpcoes.setMarginRight(0);
        hBoxOpcoes.setMarginBottom(0);
        hBoxOpcoes.setSpacing(30);
        hBoxOpcoes.setFlexVflex("ftFalse");
        hBoxOpcoes.setFlexHflex("ftTrue");
        hBoxOpcoes.setScrollable(false);
        hBoxOpcoes.setBoxShadowConfigHorizontalLength(10);
        hBoxOpcoes.setBoxShadowConfigVerticalLength(10);
        hBoxOpcoes.setBoxShadowConfigBlurRadius(5);
        hBoxOpcoes.setBoxShadowConfigSpreadRadius(0);
        hBoxOpcoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxOpcoes.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.setVAlign("tvTop");
        FVBox1.addChildren(hBoxOpcoes);
        hBoxOpcoes.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(97);
        FVBox2.setHeight(32);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFIconClass iconOk = new TFIconClass();

    private void init_iconOk() {
        iconOk.setName("iconOk");
        iconOk.setLeft(97);
        iconOk.setTop(0);
        iconOk.setVisible(false);
        iconOk.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconOkClick(event);
            processarFlow("FrmVideoSnapShot", "iconOk", "OnClick");
        });
        iconOk.setIconClass("check");
        iconOk.setSize(44);
        iconOk.setColor("clGreen");
        hBoxOpcoes.addChildren(iconOk);
        iconOk.applyProperties();
    }

    public TFVBox vBoxCapturarVideo = new TFVBox();

    private void init_vBoxCapturarVideo() {
        vBoxCapturarVideo.setName("vBoxCapturarVideo");
        vBoxCapturarVideo.setLeft(141);
        vBoxCapturarVideo.setTop(0);
        vBoxCapturarVideo.setWidth(55);
        vBoxCapturarVideo.setHeight(55);
        vBoxCapturarVideo.setBorderStyle("stNone");
        vBoxCapturarVideo.setPaddingTop(0);
        vBoxCapturarVideo.setPaddingLeft(0);
        vBoxCapturarVideo.setPaddingRight(0);
        vBoxCapturarVideo.setPaddingBottom(0);
        vBoxCapturarVideo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxCapturarVideoClick(event);
            processarFlow("FrmVideoSnapShot", "vBoxCapturarVideo", "OnClick");
        });
        vBoxCapturarVideo.setMarginTop(0);
        vBoxCapturarVideo.setMarginLeft(0);
        vBoxCapturarVideo.setMarginRight(0);
        vBoxCapturarVideo.setMarginBottom(0);
        vBoxCapturarVideo.setSpacing(1);
        vBoxCapturarVideo.setFlexVflex("ftFalse");
        vBoxCapturarVideo.setFlexHflex("ftFalse");
        vBoxCapturarVideo.setScrollable(false);
        vBoxCapturarVideo.setBoxShadowConfigHorizontalLength(10);
        vBoxCapturarVideo.setBoxShadowConfigVerticalLength(10);
        vBoxCapturarVideo.setBoxShadowConfigBlurRadius(5);
        vBoxCapturarVideo.setBoxShadowConfigSpreadRadius(0);
        vBoxCapturarVideo.setBoxShadowConfigShadowColor("clBlack");
        vBoxCapturarVideo.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(vBoxCapturarVideo);
        vBoxCapturarVideo.applyProperties();
    }

    public TFIconClass buttonCapturarVideo = new TFIconClass();

    private void init_buttonCapturarVideo() {
        buttonCapturarVideo.setName("buttonCapturarVideo");
        buttonCapturarVideo.setLeft(0);
        buttonCapturarVideo.setTop(0);
        buttonCapturarVideo.setIconClass("video-camera");
        buttonCapturarVideo.setSize(44);
        buttonCapturarVideo.setColor("clBlack");
        vBoxCapturarVideo.addChildren(buttonCapturarVideo);
        buttonCapturarVideo.applyProperties();
    }

    public TFVBox vBoxCancel = new TFVBox();

    private void init_vBoxCancel() {
        vBoxCancel.setName("vBoxCancel");
        vBoxCancel.setLeft(196);
        vBoxCancel.setTop(0);
        vBoxCancel.setWidth(55);
        vBoxCancel.setHeight(55);
        vBoxCancel.setBorderStyle("stNone");
        vBoxCancel.setPaddingTop(0);
        vBoxCancel.setPaddingLeft(0);
        vBoxCancel.setPaddingRight(0);
        vBoxCancel.setPaddingBottom(0);
        vBoxCancel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxCancelClick(event);
            processarFlow("FrmVideoSnapShot", "vBoxCancel", "OnClick");
        });
        vBoxCancel.setMarginTop(0);
        vBoxCancel.setMarginLeft(0);
        vBoxCancel.setMarginRight(0);
        vBoxCancel.setMarginBottom(0);
        vBoxCancel.setSpacing(1);
        vBoxCancel.setFlexVflex("ftFalse");
        vBoxCancel.setFlexHflex("ftFalse");
        vBoxCancel.setScrollable(false);
        vBoxCancel.setBoxShadowConfigHorizontalLength(10);
        vBoxCancel.setBoxShadowConfigVerticalLength(10);
        vBoxCancel.setBoxShadowConfigBlurRadius(5);
        vBoxCancel.setBoxShadowConfigSpreadRadius(0);
        vBoxCancel.setBoxShadowConfigShadowColor("clBlack");
        vBoxCancel.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(vBoxCancel);
        vBoxCancel.applyProperties();
    }

    public TFIconClass iconCancel = new TFIconClass();

    private void init_iconCancel() {
        iconCancel.setName("iconCancel");
        iconCancel.setLeft(0);
        iconCancel.setTop(0);
        iconCancel.setVisible(false);
        iconCancel.setIconClass("ban");
        iconCancel.setSize(44);
        iconCancel.setColor("clRed");
        vBoxCancel.addChildren(iconCancel);
        iconCancel.applyProperties();
    }

    public TFVBox vboxStop = new TFVBox();

    private void init_vboxStop() {
        vboxStop.setName("vboxStop");
        vboxStop.setLeft(251);
        vboxStop.setTop(0);
        vboxStop.setWidth(55);
        vboxStop.setHeight(55);
        vboxStop.setBorderStyle("stNone");
        vboxStop.setPaddingTop(0);
        vboxStop.setPaddingLeft(0);
        vboxStop.setPaddingRight(0);
        vboxStop.setPaddingBottom(0);
        vboxStop.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxStopClick(event);
            processarFlow("FrmVideoSnapShot", "vboxStop", "OnClick");
        });
        vboxStop.setMarginTop(0);
        vboxStop.setMarginLeft(0);
        vboxStop.setMarginRight(0);
        vboxStop.setMarginBottom(0);
        vboxStop.setSpacing(1);
        vboxStop.setFlexVflex("ftFalse");
        vboxStop.setFlexHflex("ftFalse");
        vboxStop.setScrollable(false);
        vboxStop.setBoxShadowConfigHorizontalLength(10);
        vboxStop.setBoxShadowConfigVerticalLength(10);
        vboxStop.setBoxShadowConfigBlurRadius(5);
        vboxStop.setBoxShadowConfigSpreadRadius(0);
        vboxStop.setBoxShadowConfigShadowColor("clBlack");
        vboxStop.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(vboxStop);
        vboxStop.applyProperties();
    }

    public TFIconClass iconStop = new TFIconClass();

    private void init_iconStop() {
        iconStop.setName("iconStop");
        iconStop.setLeft(0);
        iconStop.setTop(0);
        iconStop.setVisible(false);
        iconStop.setIconClass("stop");
        iconStop.setSize(44);
        iconStop.setColor("clRed");
        vboxStop.addChildren(iconStop);
        iconStop.applyProperties();
    }

    public TFHBox hBoxClose = new TFHBox();

    private void init_hBoxClose() {
        hBoxClose.setName("hBoxClose");
        hBoxClose.setLeft(306);
        hBoxClose.setTop(0);
        hBoxClose.setWidth(55);
        hBoxClose.setHeight(55);
        hBoxClose.setBorderStyle("stNone");
        hBoxClose.setPaddingTop(0);
        hBoxClose.setPaddingLeft(0);
        hBoxClose.setPaddingRight(0);
        hBoxClose.setPaddingBottom(0);
        hBoxClose.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxCloseClick(event);
            processarFlow("FrmVideoSnapShot", "hBoxClose", "OnClick");
        });
        hBoxClose.setMarginTop(0);
        hBoxClose.setMarginLeft(0);
        hBoxClose.setMarginRight(0);
        hBoxClose.setMarginBottom(0);
        hBoxClose.setSpacing(1);
        hBoxClose.setFlexVflex("ftFalse");
        hBoxClose.setFlexHflex("ftFalse");
        hBoxClose.setScrollable(false);
        hBoxClose.setBoxShadowConfigHorizontalLength(10);
        hBoxClose.setBoxShadowConfigVerticalLength(10);
        hBoxClose.setBoxShadowConfigBlurRadius(5);
        hBoxClose.setBoxShadowConfigSpreadRadius(0);
        hBoxClose.setBoxShadowConfigShadowColor("clBlack");
        hBoxClose.setBoxShadowConfigOpacity(75);
        hBoxClose.setVAlign("tvTop");
        hBoxOpcoes.addChildren(hBoxClose);
        hBoxClose.applyProperties();
    }

    public TFIconClass btnClose = new TFIconClass();

    private void init_btnClose() {
        btnClose.setName("btnClose");
        btnClose.setLeft(0);
        btnClose.setTop(0);
        btnClose.setIconClass("window-close");
        btnClose.setSize(44);
        btnClose.setColor("clBlack");
        hBoxClose.addChildren(btnClose);
        btnClose.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(361);
        FVBox4.setTop(0);
        FVBox4.setWidth(137);
        FVBox4.setHeight(32);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void videoSnapshotUpload(final UploadEvent event);

    public abstract void iconOkClick(final Event<Object> event);

    public abstract void vBoxCapturarVideoClick(final Event<Object> event);

    public abstract void vBoxCancelClick(final Event<Object> event);

    public abstract void vBoxStopClick(final Event<Object> event);

    public abstract void hBoxCloseClick(final Event<Object> event);

}