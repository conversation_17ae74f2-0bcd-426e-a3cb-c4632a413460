package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmFrameChatQR extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.FrameChatQRRNA rn = null;

    public FrmFrameChatQR() {
        try {
            rn = (freedom.bytecode.rn.FrameChatQRRNA) getRN(freedom.bytecode.rn.wizard.FrameChatQRRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbCadastroWhatsapp();
        init_frameChat();
        init_FrmFrameChatQR();
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp;

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp = rn.tbCadastroWhatsapp;
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("513017;51301");
        tbCadastroWhatsapp.setRatioBatchSize(20);
        getTables().put(tbCadastroWhatsapp, "tbCadastroWhatsapp");
        tbCadastroWhatsapp.applyProperties();
    }

    protected TFForm FrmFrameChatQR = this;
    private void init_FrmFrameChatQR() {
        FrmFrameChatQR.setName("FrmFrameChatQR");
        FrmFrameChatQR.setCaption("Chat QRCode");
        FrmFrameChatQR.setClientHeight(369);
        FrmFrameChatQR.setClientWidth(395);
        FrmFrameChatQR.setColor("clBtnFace");
        FrmFrameChatQR.setWOrigem("EhMain");
        FrmFrameChatQR.setWKey("513017");
        FrmFrameChatQR.setSpacing(0);
        FrmFrameChatQR.applyProperties();
    }

    public TFFrame frameChat = new TFFrame();

    private void init_frameChat() {
        frameChat.setName("frameChat");
        frameChat.setLeft(0);
        frameChat.setTop(0);
        frameChat.setWidth(395);
        frameChat.setHeight(369);
        frameChat.setAlign("alClient");
        frameChat.setFlexVflex("ftTrue");
        frameChat.setFlexHflex("ftTrue");
        FrmFrameChatQR.addChildren(frameChat);
        frameChat.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}