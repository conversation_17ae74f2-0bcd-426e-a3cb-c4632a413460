package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAssinatura extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AssinaturaRNA rn = null;

    public FrmAssinatura() {
        try {
            rn = (freedom.bytecode.rn.AssinaturaRNA) getRN(freedom.bytecode.rn.wizard.AssinaturaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxAssinar();
        init_FHBox11();
        init_btnVoltar();
        init_btnAceitar();
        init_flabelNomeAssinatura();
        init_signature();
        init_FHBox1();
        init_FHBox3();
        init_btnDesfazer();
        init_btnLimpar();
        init_FHBox43();
        init_vboxDadosData();
        init_FLabel5();
        init_FHBox6();
        init_FVBox5();
        init_FLabel27();
        init_dataAssinatura();
        init_vboxDadosKMCombustivel();
        init_FLabel2();
        init_FHBox2();
        init_FVBox1();
        init_FHBox4();
        init_lblKm();
        init_lblKmEntrada();
        init_edtKm();
        init_FVBox4();
        init_FLabel4();
        init_hboxMarcadorCombustivel();
        init_hboxMarca0();
        init_FHBox150();
        init_FLabel57();
        init_FHBox151();
        init_hboxMarca1();
        init_FHBox153();
        init_FLabel60();
        init_FHBox154();
        init_hboxMarca2();
        init_FHBox156();
        init_FLabel61();
        init_FHBox157();
        init_hboxMarca3();
        init_FHBox159();
        init_FLabel62();
        init_FHBox160();
        init_hboxMarca4();
        init_FHBox162();
        init_FLabel63();
        init_FHBox163();
        init_FrmAssinatura();
    }

    protected TFForm FrmAssinatura = this;
    private void init_FrmAssinatura() {
        FrmAssinatura.setName("FrmAssinatura");
        FrmAssinatura.setCaption("Assinar");
        FrmAssinatura.setClientHeight(530);
        FrmAssinatura.setClientWidth(534);
        FrmAssinatura.setColor("clBtnFace");
        FrmAssinatura.setWKey("4600413");
        FrmAssinatura.setSpacing(0);
        FrmAssinatura.applyProperties();
    }

    public TFVBox vBoxAssinar = new TFVBox();

    private void init_vBoxAssinar() {
        vBoxAssinar.setName("vBoxAssinar");
        vBoxAssinar.setLeft(0);
        vBoxAssinar.setTop(0);
        vBoxAssinar.setWidth(534);
        vBoxAssinar.setHeight(530);
        vBoxAssinar.setAlign("alClient");
        vBoxAssinar.setBorderStyle("stNone");
        vBoxAssinar.setPaddingTop(5);
        vBoxAssinar.setPaddingLeft(5);
        vBoxAssinar.setPaddingRight(5);
        vBoxAssinar.setPaddingBottom(5);
        vBoxAssinar.setMarginTop(0);
        vBoxAssinar.setMarginLeft(0);
        vBoxAssinar.setMarginRight(0);
        vBoxAssinar.setMarginBottom(0);
        vBoxAssinar.setSpacing(5);
        vBoxAssinar.setFlexVflex("ftFalse");
        vBoxAssinar.setFlexHflex("ftTrue");
        vBoxAssinar.setScrollable(false);
        vBoxAssinar.setBoxShadowConfigHorizontalLength(10);
        vBoxAssinar.setBoxShadowConfigVerticalLength(10);
        vBoxAssinar.setBoxShadowConfigBlurRadius(5);
        vBoxAssinar.setBoxShadowConfigSpreadRadius(0);
        vBoxAssinar.setBoxShadowConfigShadowColor("clBlack");
        vBoxAssinar.setBoxShadowConfigOpacity(75);
        FrmAssinatura.addChildren(vBoxAssinar);
        vBoxAssinar.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(528);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxAssinar.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAssinatura", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(60);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-13);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmAssinatura", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox11.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFLabel flabelNomeAssinatura = new TFLabel();

    private void init_flabelNomeAssinatura() {
        flabelNomeAssinatura.setName("flabelNomeAssinatura");
        flabelNomeAssinatura.setLeft(0);
        flabelNomeAssinatura.setTop(62);
        flabelNomeAssinatura.setWidth(524);
        flabelNomeAssinatura.setHeight(13);
        flabelNomeAssinatura.setCaption("Assinatura");
        flabelNomeAssinatura.setFontColor("clWindowText");
        flabelNomeAssinatura.setFontSize(-11);
        flabelNomeAssinatura.setFontName("Tahoma");
        flabelNomeAssinatura.setFontStyle("[]");
        flabelNomeAssinatura.setVerticalAlignment("taVerticalCenter");
        flabelNomeAssinatura.setWordBreak(true);
        vBoxAssinar.addChildren(flabelNomeAssinatura);
        flabelNomeAssinatura.applyProperties();
    }

    public TFSignature signature = new TFSignature();

    private void init_signature() {
        signature.setName("signature");
        signature.setLeft(0);
        signature.setTop(76);
        signature.setWidth(527);
        signature.setHeight(237);
        signature.setBackgroundColor("clWhite");
        signature.setPenColor("clBlack");
        signature.setPenSize(4);
        signature.setSaveType("image/png");
        signature.setToolbarVisible(false);
        signature.addEventListener("onSave", (EventListener<UploadEvent>) (UploadEvent event) -> {
            signatureSave(event);
            processarFlow("FrmAssinatura", "signature", "OnSave");
        });
        signature.setFlexVflex("ftFalse");
        signature.setFlexHflex("ftTrue");
        vBoxAssinar.addChildren(signature);
        signature.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(314);
        FHBox1.setWidth(529);
        FHBox1.setHeight(53);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxAssinar.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(522);
        FHBox3.setHeight(46);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(5);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FHBox1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnDesfazer = new TFButton();

    private void init_btnDesfazer() {
        btnDesfazer.setName("btnDesfazer");
        btnDesfazer.setLeft(0);
        btnDesfazer.setTop(0);
        btnDesfazer.setWidth(88);
        btnDesfazer.setHeight(38);
        btnDesfazer.setCaption("Desfazer");
        btnDesfazer.setFontColor("clWindowText");
        btnDesfazer.setFontSize(-11);
        btnDesfazer.setFontName("Tahoma");
        btnDesfazer.setFontStyle("[]");
        btnDesfazer.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnDesfazerClick(event);
            processarFlow("FrmAssinatura", "btnDesfazer", "OnClick");
        });
        btnDesfazer.setImageId(0);
        btnDesfazer.setColor("clBtnFace");
        btnDesfazer.setAccess(false);
        btnDesfazer.setIconClass("undo");
        btnDesfazer.setIconReverseDirection(false);
        FHBox3.addChildren(btnDesfazer);
        btnDesfazer.applyProperties();
    }

    public TFButton btnLimpar = new TFButton();

    private void init_btnLimpar() {
        btnLimpar.setName("btnLimpar");
        btnLimpar.setLeft(88);
        btnLimpar.setTop(0);
        btnLimpar.setWidth(84);
        btnLimpar.setHeight(38);
        btnLimpar.setCaption("Limpar");
        btnLimpar.setFontColor("clWindowText");
        btnLimpar.setFontSize(-11);
        btnLimpar.setFontName("Tahoma");
        btnLimpar.setFontStyle("[]");
        btnLimpar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimparClick(event);
            processarFlow("FrmAssinatura", "btnLimpar", "OnClick");
        });
        btnLimpar.setImageId(0);
        btnLimpar.setColor("clBtnFace");
        btnLimpar.setAccess(false);
        btnLimpar.setIconClass("close");
        btnLimpar.setIconReverseDirection(false);
        FHBox3.addChildren(btnLimpar);
        btnLimpar.applyProperties();
    }

    public TFHBox FHBox43 = new TFHBox();

    private void init_FHBox43() {
        FHBox43.setName("FHBox43");
        FHBox43.setLeft(0);
        FHBox43.setTop(368);
        FHBox43.setWidth(526);
        FHBox43.setHeight(107);
        FHBox43.setBorderStyle("stNone");
        FHBox43.setPaddingTop(0);
        FHBox43.setPaddingLeft(0);
        FHBox43.setPaddingRight(0);
        FHBox43.setPaddingBottom(0);
        FHBox43.setMarginTop(0);
        FHBox43.setMarginLeft(0);
        FHBox43.setMarginRight(0);
        FHBox43.setMarginBottom(0);
        FHBox43.setSpacing(1);
        FHBox43.setFlexVflex("ftFalse");
        FHBox43.setFlexHflex("ftTrue");
        FHBox43.setScrollable(false);
        FHBox43.setBoxShadowConfigHorizontalLength(10);
        FHBox43.setBoxShadowConfigVerticalLength(10);
        FHBox43.setBoxShadowConfigBlurRadius(5);
        FHBox43.setBoxShadowConfigSpreadRadius(0);
        FHBox43.setBoxShadowConfigShadowColor("clBlack");
        FHBox43.setBoxShadowConfigOpacity(75);
        FHBox43.setVAlign("tvTop");
        vBoxAssinar.addChildren(FHBox43);
        FHBox43.applyProperties();
    }

    public TFVBox vboxDadosData = new TFVBox();

    private void init_vboxDadosData() {
        vboxDadosData.setName("vboxDadosData");
        vboxDadosData.setLeft(0);
        vboxDadosData.setTop(0);
        vboxDadosData.setWidth(186);
        vboxDadosData.setHeight(100);
        vboxDadosData.setBorderStyle("stSingleLine");
        vboxDadosData.setPaddingTop(5);
        vboxDadosData.setPaddingLeft(5);
        vboxDadosData.setPaddingRight(0);
        vboxDadosData.setPaddingBottom(0);
        vboxDadosData.setMarginTop(0);
        vboxDadosData.setMarginLeft(0);
        vboxDadosData.setMarginRight(0);
        vboxDadosData.setMarginBottom(0);
        vboxDadosData.setSpacing(1);
        vboxDadosData.setFlexVflex("ftFalse");
        vboxDadosData.setFlexHflex("ftTrue");
        vboxDadosData.setScrollable(false);
        vboxDadosData.setBoxShadowConfigHorizontalLength(10);
        vboxDadosData.setBoxShadowConfigVerticalLength(10);
        vboxDadosData.setBoxShadowConfigBlurRadius(5);
        vboxDadosData.setBoxShadowConfigSpreadRadius(0);
        vboxDadosData.setBoxShadowConfigShadowColor("clBlack");
        vboxDadosData.setBoxShadowConfigOpacity(75);
        FHBox43.addChildren(vboxDadosData);
        vboxDadosData.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(31);
        FLabel5.setHeight(16);
        FLabel5.setCaption("Data");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-13);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[fsBold]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        vboxDadosData.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(17);
        FHBox6.setWidth(181);
        FHBox6.setHeight(72);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(5);
        FHBox6.setPaddingLeft(5);
        FHBox6.setPaddingRight(5);
        FHBox6.setPaddingBottom(5);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(5);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vboxDadosData.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(0);
        FVBox5.setTop(0);
        FVBox5.setWidth(168);
        FVBox5.setHeight(58);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel FLabel27 = new TFLabel();

    private void init_FLabel27() {
        FLabel27.setName("FLabel27");
        FLabel27.setLeft(0);
        FLabel27.setTop(0);
        FLabel27.setWidth(106);
        FLabel27.setHeight(16);
        FLabel27.setCaption("Data Assinatura");
        FLabel27.setFontColor("clWindowText");
        FLabel27.setFontSize(-13);
        FLabel27.setFontName("Tahoma");
        FLabel27.setFontStyle("[fsBold]");
        FLabel27.setVerticalAlignment("taVerticalCenter");
        FLabel27.setWordBreak(false);
        FVBox5.addChildren(FLabel27);
        FLabel27.applyProperties();
    }

    public TFDate dataAssinatura = new TFDate();

    private void init_dataAssinatura() {
        dataAssinatura.setName("dataAssinatura");
        dataAssinatura.setLeft(0);
        dataAssinatura.setTop(17);
        dataAssinatura.setWidth(150);
        dataAssinatura.setHeight(24);
        dataAssinatura.setFlex(false);
        dataAssinatura.setRequired(false);
        dataAssinatura.setConstraintCheckWhen("cwImmediate");
        dataAssinatura.setConstraintCheckType("ctExpression");
        dataAssinatura.setConstraintFocusOnError(false);
        dataAssinatura.setConstraintEnableUI(true);
        dataAssinatura.setConstraintEnabled(false);
        dataAssinatura.setConstraintFormCheck(true);
        dataAssinatura.setFormat("dd/MM/yyyy");
        dataAssinatura.setShowCheckBox(false);
        FVBox5.addChildren(dataAssinatura);
        dataAssinatura.applyProperties();
        addValidatable(dataAssinatura);
    }

    public TFVBox vboxDadosKMCombustivel = new TFVBox();

    private void init_vboxDadosKMCombustivel() {
        vboxDadosKMCombustivel.setName("vboxDadosKMCombustivel");
        vboxDadosKMCombustivel.setLeft(186);
        vboxDadosKMCombustivel.setTop(0);
        vboxDadosKMCombustivel.setWidth(337);
        vboxDadosKMCombustivel.setHeight(99);
        vboxDadosKMCombustivel.setBorderStyle("stSingleLine");
        vboxDadosKMCombustivel.setPaddingTop(5);
        vboxDadosKMCombustivel.setPaddingLeft(5);
        vboxDadosKMCombustivel.setPaddingRight(0);
        vboxDadosKMCombustivel.setPaddingBottom(0);
        vboxDadosKMCombustivel.setVisible(false);
        vboxDadosKMCombustivel.setMarginTop(0);
        vboxDadosKMCombustivel.setMarginLeft(0);
        vboxDadosKMCombustivel.setMarginRight(0);
        vboxDadosKMCombustivel.setMarginBottom(0);
        vboxDadosKMCombustivel.setSpacing(1);
        vboxDadosKMCombustivel.setFlexVflex("ftFalse");
        vboxDadosKMCombustivel.setFlexHflex("ftFalse");
        vboxDadosKMCombustivel.setScrollable(false);
        vboxDadosKMCombustivel.setBoxShadowConfigHorizontalLength(10);
        vboxDadosKMCombustivel.setBoxShadowConfigVerticalLength(10);
        vboxDadosKMCombustivel.setBoxShadowConfigBlurRadius(5);
        vboxDadosKMCombustivel.setBoxShadowConfigSpreadRadius(0);
        vboxDadosKMCombustivel.setBoxShadowConfigShadowColor("clBlack");
        vboxDadosKMCombustivel.setBoxShadowConfigOpacity(75);
        FHBox43.addChildren(vboxDadosKMCombustivel);
        vboxDadosKMCombustivel.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(35);
        FLabel2.setHeight(16);
        FLabel2.setCaption("Sa\u00EDda");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-13);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        vboxDadosKMCombustivel.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(17);
        FHBox2.setWidth(319);
        FHBox2.setHeight(72);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(5);
        FHBox2.setPaddingLeft(5);
        FHBox2.setPaddingRight(5);
        FHBox2.setPaddingBottom(5);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(5);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vboxDadosKMCombustivel.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(117);
        FVBox1.setHeight(62);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(85);
        FHBox4.setHeight(17);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(5);
        FHBox4.setFlexVflex("ftMin");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FVBox1.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFLabel lblKm = new TFLabel();

    private void init_lblKm() {
        lblKm.setName("lblKm");
        lblKm.setLeft(0);
        lblKm.setTop(0);
        lblKm.setWidth(19);
        lblKm.setHeight(16);
        lblKm.setCaption("KM");
        lblKm.setFontColor("clWindowText");
        lblKm.setFontSize(-13);
        lblKm.setFontName("Tahoma");
        lblKm.setFontStyle("[fsBold]");
        lblKm.setVerticalAlignment("taVerticalCenter");
        lblKm.setWordBreak(false);
        FHBox4.addChildren(lblKm);
        lblKm.applyProperties();
    }

    public TFLabel lblKmEntrada = new TFLabel();

    private void init_lblKmEntrada() {
        lblKmEntrada.setName("lblKmEntrada");
        lblKmEntrada.setLeft(19);
        lblKmEntrada.setTop(0);
        lblKmEntrada.setWidth(70);
        lblKmEntrada.setHeight(13);
        lblKmEntrada.setCaption("Km de entrada");
        lblKmEntrada.setFontColor("clRed");
        lblKmEntrada.setFontSize(-11);
        lblKmEntrada.setFontName("Tahoma");
        lblKmEntrada.setFontStyle("[]");
        lblKmEntrada.setVerticalAlignment("taVerticalCenter");
        lblKmEntrada.setWordBreak(false);
        FHBox4.addChildren(lblKmEntrada);
        lblKmEntrada.applyProperties();
    }

    public TFInteger edtKm = new TFInteger();

    private void init_edtKm() {
        edtKm.setName("edtKm");
        edtKm.setLeft(0);
        edtKm.setTop(18);
        edtKm.setWidth(102);
        edtKm.setHeight(27);
        edtKm.setFlex(true);
        edtKm.setRequired(false);
        edtKm.setConstraintCheckWhen("cwImmediate");
        edtKm.setConstraintCheckType("ctExpression");
        edtKm.setConstraintFocusOnError(false);
        edtKm.setConstraintEnableUI(true);
        edtKm.setConstraintEnabled(false);
        edtKm.setConstraintFormCheck(true);
        edtKm.setMaxlength(7);
        edtKm.setFontColor("clWindowText");
        edtKm.setFontSize(-16);
        edtKm.setFontName("Tahoma");
        edtKm.setFontStyle("[]");
        edtKm.setAlignment("taRightJustify");
        edtKm.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtKmChange(event);
            processarFlow("FrmAssinatura", "edtKm", "OnChange");
        });
        FVBox1.addChildren(edtKm);
        edtKm.applyProperties();
        addValidatable(edtKm);
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(117);
        FVBox4.setTop(0);
        FVBox4.setWidth(169);
        FVBox4.setHeight(61);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(78);
        FLabel4.setHeight(16);
        FLabel4.setCaption("Combust\u00EDvel");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-13);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[fsBold]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FVBox4.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFHBox hboxMarcadorCombustivel = new TFHBox();

    private void init_hboxMarcadorCombustivel() {
        hboxMarcadorCombustivel.setName("hboxMarcadorCombustivel");
        hboxMarcadorCombustivel.setLeft(0);
        hboxMarcadorCombustivel.setTop(17);
        hboxMarcadorCombustivel.setWidth(160);
        hboxMarcadorCombustivel.setHeight(37);
        hboxMarcadorCombustivel.setBorderStyle("stNone");
        hboxMarcadorCombustivel.setPaddingTop(0);
        hboxMarcadorCombustivel.setPaddingLeft(0);
        hboxMarcadorCombustivel.setPaddingRight(0);
        hboxMarcadorCombustivel.setPaddingBottom(0);
        hboxMarcadorCombustivel.setMarginTop(0);
        hboxMarcadorCombustivel.setMarginLeft(0);
        hboxMarcadorCombustivel.setMarginRight(0);
        hboxMarcadorCombustivel.setMarginBottom(0);
        hboxMarcadorCombustivel.setSpacing(0);
        hboxMarcadorCombustivel.setFlexVflex("ftFalse");
        hboxMarcadorCombustivel.setFlexHflex("ftFalse");
        hboxMarcadorCombustivel.setScrollable(false);
        hboxMarcadorCombustivel.setBoxShadowConfigHorizontalLength(10);
        hboxMarcadorCombustivel.setBoxShadowConfigVerticalLength(10);
        hboxMarcadorCombustivel.setBoxShadowConfigBlurRadius(5);
        hboxMarcadorCombustivel.setBoxShadowConfigSpreadRadius(0);
        hboxMarcadorCombustivel.setBoxShadowConfigShadowColor("clBlack");
        hboxMarcadorCombustivel.setBoxShadowConfigOpacity(75);
        hboxMarcadorCombustivel.setVAlign("tvTop");
        FVBox4.addChildren(hboxMarcadorCombustivel);
        hboxMarcadorCombustivel.applyProperties();
    }

    public TFHBox hboxMarca0 = new TFHBox();

    private void init_hboxMarca0() {
        hboxMarca0.setName("hboxMarca0");
        hboxMarca0.setLeft(0);
        hboxMarca0.setTop(0);
        hboxMarca0.setWidth(31);
        hboxMarca0.setHeight(31);
        hboxMarca0.setBorderStyle("stNone");
        hboxMarca0.setPaddingTop(4);
        hboxMarca0.setPaddingLeft(0);
        hboxMarca0.setPaddingRight(0);
        hboxMarca0.setPaddingBottom(0);
        hboxMarca0.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxMarca0Click(event);
            processarFlow("FrmAssinatura", "hboxMarca0", "OnClick");
        });
        hboxMarca0.setMarginTop(0);
        hboxMarca0.setMarginLeft(0);
        hboxMarca0.setMarginRight(0);
        hboxMarca0.setMarginBottom(0);
        hboxMarca0.setSpacing(1);
        hboxMarca0.setFlexVflex("ftFalse");
        hboxMarca0.setFlexHflex("ftFalse");
        hboxMarca0.setScrollable(false);
        hboxMarca0.setBoxShadowConfigHorizontalLength(10);
        hboxMarca0.setBoxShadowConfigVerticalLength(10);
        hboxMarca0.setBoxShadowConfigBlurRadius(5);
        hboxMarca0.setBoxShadowConfigSpreadRadius(0);
        hboxMarca0.setBoxShadowConfigShadowColor("clBlack");
        hboxMarca0.setBoxShadowConfigOpacity(75);
        hboxMarca0.setVAlign("tvTop");
        hboxMarcadorCombustivel.addChildren(hboxMarca0);
        hboxMarca0.applyProperties();
    }

    public TFHBox FHBox150 = new TFHBox();

    private void init_FHBox150() {
        FHBox150.setName("FHBox150");
        FHBox150.setLeft(0);
        FHBox150.setTop(0);
        FHBox150.setWidth(7);
        FHBox150.setHeight(22);
        FHBox150.setBorderStyle("stNone");
        FHBox150.setPaddingTop(0);
        FHBox150.setPaddingLeft(0);
        FHBox150.setPaddingRight(0);
        FHBox150.setPaddingBottom(0);
        FHBox150.setMarginTop(0);
        FHBox150.setMarginLeft(0);
        FHBox150.setMarginRight(0);
        FHBox150.setMarginBottom(0);
        FHBox150.setSpacing(1);
        FHBox150.setFlexVflex("ftTrue");
        FHBox150.setFlexHflex("ftTrue");
        FHBox150.setScrollable(false);
        FHBox150.setBoxShadowConfigHorizontalLength(10);
        FHBox150.setBoxShadowConfigVerticalLength(10);
        FHBox150.setBoxShadowConfigBlurRadius(5);
        FHBox150.setBoxShadowConfigSpreadRadius(0);
        FHBox150.setBoxShadowConfigShadowColor("clBlack");
        FHBox150.setBoxShadowConfigOpacity(75);
        FHBox150.setVAlign("tvTop");
        hboxMarca0.addChildren(FHBox150);
        FHBox150.applyProperties();
    }

    public TFLabel FLabel57 = new TFLabel();

    private void init_FLabel57() {
        FLabel57.setName("FLabel57");
        FLabel57.setLeft(7);
        FLabel57.setTop(0);
        FLabel57.setWidth(8);
        FLabel57.setHeight(14);
        FLabel57.setCaption("0");
        FLabel57.setFontColor("clWindowText");
        FLabel57.setFontSize(-12);
        FLabel57.setFontName("Tahoma");
        FLabel57.setFontStyle("[fsBold]");
        FLabel57.setVerticalAlignment("taVerticalCenter");
        FLabel57.setWordBreak(false);
        hboxMarca0.addChildren(FLabel57);
        FLabel57.applyProperties();
    }

    public TFHBox FHBox151 = new TFHBox();

    private void init_FHBox151() {
        FHBox151.setName("FHBox151");
        FHBox151.setLeft(15);
        FHBox151.setTop(0);
        FHBox151.setWidth(7);
        FHBox151.setHeight(22);
        FHBox151.setBorderStyle("stNone");
        FHBox151.setPaddingTop(0);
        FHBox151.setPaddingLeft(0);
        FHBox151.setPaddingRight(0);
        FHBox151.setPaddingBottom(0);
        FHBox151.setMarginTop(0);
        FHBox151.setMarginLeft(0);
        FHBox151.setMarginRight(0);
        FHBox151.setMarginBottom(0);
        FHBox151.setSpacing(1);
        FHBox151.setFlexVflex("ftTrue");
        FHBox151.setFlexHflex("ftTrue");
        FHBox151.setScrollable(false);
        FHBox151.setBoxShadowConfigHorizontalLength(10);
        FHBox151.setBoxShadowConfigVerticalLength(10);
        FHBox151.setBoxShadowConfigBlurRadius(5);
        FHBox151.setBoxShadowConfigSpreadRadius(0);
        FHBox151.setBoxShadowConfigShadowColor("clBlack");
        FHBox151.setBoxShadowConfigOpacity(75);
        FHBox151.setVAlign("tvTop");
        hboxMarca0.addChildren(FHBox151);
        FHBox151.applyProperties();
    }

    public TFHBox hboxMarca1 = new TFHBox();

    private void init_hboxMarca1() {
        hboxMarca1.setName("hboxMarca1");
        hboxMarca1.setLeft(31);
        hboxMarca1.setTop(0);
        hboxMarca1.setWidth(31);
        hboxMarca1.setHeight(31);
        hboxMarca1.setBorderStyle("stNone");
        hboxMarca1.setPaddingTop(4);
        hboxMarca1.setPaddingLeft(0);
        hboxMarca1.setPaddingRight(0);
        hboxMarca1.setPaddingBottom(0);
        hboxMarca1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxMarca1Click(event);
            processarFlow("FrmAssinatura", "hboxMarca1", "OnClick");
        });
        hboxMarca1.setMarginTop(0);
        hboxMarca1.setMarginLeft(0);
        hboxMarca1.setMarginRight(0);
        hboxMarca1.setMarginBottom(0);
        hboxMarca1.setSpacing(1);
        hboxMarca1.setFlexVflex("ftFalse");
        hboxMarca1.setFlexHflex("ftFalse");
        hboxMarca1.setScrollable(false);
        hboxMarca1.setBoxShadowConfigHorizontalLength(10);
        hboxMarca1.setBoxShadowConfigVerticalLength(10);
        hboxMarca1.setBoxShadowConfigBlurRadius(5);
        hboxMarca1.setBoxShadowConfigSpreadRadius(0);
        hboxMarca1.setBoxShadowConfigShadowColor("clBlack");
        hboxMarca1.setBoxShadowConfigOpacity(75);
        hboxMarca1.setVAlign("tvTop");
        hboxMarcadorCombustivel.addChildren(hboxMarca1);
        hboxMarca1.applyProperties();
    }

    public TFHBox FHBox153 = new TFHBox();

    private void init_FHBox153() {
        FHBox153.setName("FHBox153");
        FHBox153.setLeft(0);
        FHBox153.setTop(0);
        FHBox153.setWidth(7);
        FHBox153.setHeight(22);
        FHBox153.setBorderStyle("stNone");
        FHBox153.setPaddingTop(0);
        FHBox153.setPaddingLeft(0);
        FHBox153.setPaddingRight(0);
        FHBox153.setPaddingBottom(0);
        FHBox153.setMarginTop(0);
        FHBox153.setMarginLeft(0);
        FHBox153.setMarginRight(0);
        FHBox153.setMarginBottom(0);
        FHBox153.setSpacing(1);
        FHBox153.setFlexVflex("ftTrue");
        FHBox153.setFlexHflex("ftTrue");
        FHBox153.setScrollable(false);
        FHBox153.setBoxShadowConfigHorizontalLength(10);
        FHBox153.setBoxShadowConfigVerticalLength(10);
        FHBox153.setBoxShadowConfigBlurRadius(5);
        FHBox153.setBoxShadowConfigSpreadRadius(0);
        FHBox153.setBoxShadowConfigShadowColor("clBlack");
        FHBox153.setBoxShadowConfigOpacity(75);
        FHBox153.setVAlign("tvTop");
        hboxMarca1.addChildren(FHBox153);
        FHBox153.applyProperties();
    }

    public TFLabel FLabel60 = new TFLabel();

    private void init_FLabel60() {
        FLabel60.setName("FLabel60");
        FLabel60.setLeft(7);
        FLabel60.setTop(0);
        FLabel60.setWidth(23);
        FLabel60.setHeight(14);
        FLabel60.setCaption("1/4");
        FLabel60.setFontColor("clWindowText");
        FLabel60.setFontSize(-12);
        FLabel60.setFontName("Tahoma");
        FLabel60.setFontStyle("[fsBold]");
        FLabel60.setVerticalAlignment("taVerticalCenter");
        FLabel60.setWordBreak(false);
        hboxMarca1.addChildren(FLabel60);
        FLabel60.applyProperties();
    }

    public TFHBox FHBox154 = new TFHBox();

    private void init_FHBox154() {
        FHBox154.setName("FHBox154");
        FHBox154.setLeft(30);
        FHBox154.setTop(0);
        FHBox154.setWidth(7);
        FHBox154.setHeight(22);
        FHBox154.setBorderStyle("stNone");
        FHBox154.setPaddingTop(0);
        FHBox154.setPaddingLeft(0);
        FHBox154.setPaddingRight(0);
        FHBox154.setPaddingBottom(0);
        FHBox154.setMarginTop(0);
        FHBox154.setMarginLeft(0);
        FHBox154.setMarginRight(0);
        FHBox154.setMarginBottom(0);
        FHBox154.setSpacing(1);
        FHBox154.setFlexVflex("ftTrue");
        FHBox154.setFlexHflex("ftTrue");
        FHBox154.setScrollable(false);
        FHBox154.setBoxShadowConfigHorizontalLength(10);
        FHBox154.setBoxShadowConfigVerticalLength(10);
        FHBox154.setBoxShadowConfigBlurRadius(5);
        FHBox154.setBoxShadowConfigSpreadRadius(0);
        FHBox154.setBoxShadowConfigShadowColor("clBlack");
        FHBox154.setBoxShadowConfigOpacity(75);
        FHBox154.setVAlign("tvTop");
        hboxMarca1.addChildren(FHBox154);
        FHBox154.applyProperties();
    }

    public TFHBox hboxMarca2 = new TFHBox();

    private void init_hboxMarca2() {
        hboxMarca2.setName("hboxMarca2");
        hboxMarca2.setLeft(62);
        hboxMarca2.setTop(0);
        hboxMarca2.setWidth(31);
        hboxMarca2.setHeight(31);
        hboxMarca2.setBorderStyle("stNone");
        hboxMarca2.setPaddingTop(4);
        hboxMarca2.setPaddingLeft(0);
        hboxMarca2.setPaddingRight(0);
        hboxMarca2.setPaddingBottom(0);
        hboxMarca2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxMarca2Click(event);
            processarFlow("FrmAssinatura", "hboxMarca2", "OnClick");
        });
        hboxMarca2.setMarginTop(0);
        hboxMarca2.setMarginLeft(0);
        hboxMarca2.setMarginRight(0);
        hboxMarca2.setMarginBottom(0);
        hboxMarca2.setSpacing(1);
        hboxMarca2.setFlexVflex("ftFalse");
        hboxMarca2.setFlexHflex("ftFalse");
        hboxMarca2.setScrollable(false);
        hboxMarca2.setBoxShadowConfigHorizontalLength(10);
        hboxMarca2.setBoxShadowConfigVerticalLength(10);
        hboxMarca2.setBoxShadowConfigBlurRadius(5);
        hboxMarca2.setBoxShadowConfigSpreadRadius(0);
        hboxMarca2.setBoxShadowConfigShadowColor("clBlack");
        hboxMarca2.setBoxShadowConfigOpacity(75);
        hboxMarca2.setVAlign("tvTop");
        hboxMarcadorCombustivel.addChildren(hboxMarca2);
        hboxMarca2.applyProperties();
    }

    public TFHBox FHBox156 = new TFHBox();

    private void init_FHBox156() {
        FHBox156.setName("FHBox156");
        FHBox156.setLeft(0);
        FHBox156.setTop(0);
        FHBox156.setWidth(7);
        FHBox156.setHeight(22);
        FHBox156.setBorderStyle("stNone");
        FHBox156.setPaddingTop(0);
        FHBox156.setPaddingLeft(0);
        FHBox156.setPaddingRight(0);
        FHBox156.setPaddingBottom(0);
        FHBox156.setMarginTop(0);
        FHBox156.setMarginLeft(0);
        FHBox156.setMarginRight(0);
        FHBox156.setMarginBottom(0);
        FHBox156.setSpacing(1);
        FHBox156.setFlexVflex("ftTrue");
        FHBox156.setFlexHflex("ftTrue");
        FHBox156.setScrollable(false);
        FHBox156.setBoxShadowConfigHorizontalLength(10);
        FHBox156.setBoxShadowConfigVerticalLength(10);
        FHBox156.setBoxShadowConfigBlurRadius(5);
        FHBox156.setBoxShadowConfigSpreadRadius(0);
        FHBox156.setBoxShadowConfigShadowColor("clBlack");
        FHBox156.setBoxShadowConfigOpacity(75);
        FHBox156.setVAlign("tvTop");
        hboxMarca2.addChildren(FHBox156);
        FHBox156.applyProperties();
    }

    public TFLabel FLabel61 = new TFLabel();

    private void init_FLabel61() {
        FLabel61.setName("FLabel61");
        FLabel61.setLeft(7);
        FLabel61.setTop(0);
        FLabel61.setWidth(23);
        FLabel61.setHeight(14);
        FLabel61.setCaption("1/2");
        FLabel61.setFontColor("clWindowText");
        FLabel61.setFontSize(-12);
        FLabel61.setFontName("Tahoma");
        FLabel61.setFontStyle("[fsBold]");
        FLabel61.setVerticalAlignment("taVerticalCenter");
        FLabel61.setWordBreak(false);
        hboxMarca2.addChildren(FLabel61);
        FLabel61.applyProperties();
    }

    public TFHBox FHBox157 = new TFHBox();

    private void init_FHBox157() {
        FHBox157.setName("FHBox157");
        FHBox157.setLeft(30);
        FHBox157.setTop(0);
        FHBox157.setWidth(7);
        FHBox157.setHeight(22);
        FHBox157.setBorderStyle("stNone");
        FHBox157.setPaddingTop(0);
        FHBox157.setPaddingLeft(0);
        FHBox157.setPaddingRight(0);
        FHBox157.setPaddingBottom(0);
        FHBox157.setMarginTop(0);
        FHBox157.setMarginLeft(0);
        FHBox157.setMarginRight(0);
        FHBox157.setMarginBottom(0);
        FHBox157.setSpacing(1);
        FHBox157.setFlexVflex("ftTrue");
        FHBox157.setFlexHflex("ftTrue");
        FHBox157.setScrollable(false);
        FHBox157.setBoxShadowConfigHorizontalLength(10);
        FHBox157.setBoxShadowConfigVerticalLength(10);
        FHBox157.setBoxShadowConfigBlurRadius(5);
        FHBox157.setBoxShadowConfigSpreadRadius(0);
        FHBox157.setBoxShadowConfigShadowColor("clBlack");
        FHBox157.setBoxShadowConfigOpacity(75);
        FHBox157.setVAlign("tvTop");
        hboxMarca2.addChildren(FHBox157);
        FHBox157.applyProperties();
    }

    public TFHBox hboxMarca3 = new TFHBox();

    private void init_hboxMarca3() {
        hboxMarca3.setName("hboxMarca3");
        hboxMarca3.setLeft(93);
        hboxMarca3.setTop(0);
        hboxMarca3.setWidth(31);
        hboxMarca3.setHeight(31);
        hboxMarca3.setBorderStyle("stNone");
        hboxMarca3.setPaddingTop(4);
        hboxMarca3.setPaddingLeft(0);
        hboxMarca3.setPaddingRight(0);
        hboxMarca3.setPaddingBottom(0);
        hboxMarca3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxMarca3Click(event);
            processarFlow("FrmAssinatura", "hboxMarca3", "OnClick");
        });
        hboxMarca3.setMarginTop(0);
        hboxMarca3.setMarginLeft(0);
        hboxMarca3.setMarginRight(0);
        hboxMarca3.setMarginBottom(0);
        hboxMarca3.setSpacing(1);
        hboxMarca3.setFlexVflex("ftFalse");
        hboxMarca3.setFlexHflex("ftFalse");
        hboxMarca3.setScrollable(false);
        hboxMarca3.setBoxShadowConfigHorizontalLength(10);
        hboxMarca3.setBoxShadowConfigVerticalLength(10);
        hboxMarca3.setBoxShadowConfigBlurRadius(5);
        hboxMarca3.setBoxShadowConfigSpreadRadius(0);
        hboxMarca3.setBoxShadowConfigShadowColor("clBlack");
        hboxMarca3.setBoxShadowConfigOpacity(75);
        hboxMarca3.setVAlign("tvTop");
        hboxMarcadorCombustivel.addChildren(hboxMarca3);
        hboxMarca3.applyProperties();
    }

    public TFHBox FHBox159 = new TFHBox();

    private void init_FHBox159() {
        FHBox159.setName("FHBox159");
        FHBox159.setLeft(0);
        FHBox159.setTop(0);
        FHBox159.setWidth(7);
        FHBox159.setHeight(22);
        FHBox159.setBorderStyle("stNone");
        FHBox159.setPaddingTop(0);
        FHBox159.setPaddingLeft(0);
        FHBox159.setPaddingRight(0);
        FHBox159.setPaddingBottom(0);
        FHBox159.setMarginTop(0);
        FHBox159.setMarginLeft(0);
        FHBox159.setMarginRight(0);
        FHBox159.setMarginBottom(0);
        FHBox159.setSpacing(1);
        FHBox159.setFlexVflex("ftTrue");
        FHBox159.setFlexHflex("ftTrue");
        FHBox159.setScrollable(false);
        FHBox159.setBoxShadowConfigHorizontalLength(10);
        FHBox159.setBoxShadowConfigVerticalLength(10);
        FHBox159.setBoxShadowConfigBlurRadius(5);
        FHBox159.setBoxShadowConfigSpreadRadius(0);
        FHBox159.setBoxShadowConfigShadowColor("clBlack");
        FHBox159.setBoxShadowConfigOpacity(75);
        FHBox159.setVAlign("tvTop");
        hboxMarca3.addChildren(FHBox159);
        FHBox159.applyProperties();
    }

    public TFLabel FLabel62 = new TFLabel();

    private void init_FLabel62() {
        FLabel62.setName("FLabel62");
        FLabel62.setLeft(7);
        FLabel62.setTop(0);
        FLabel62.setWidth(23);
        FLabel62.setHeight(14);
        FLabel62.setCaption("3/4");
        FLabel62.setFontColor("clWindowText");
        FLabel62.setFontSize(-12);
        FLabel62.setFontName("Tahoma");
        FLabel62.setFontStyle("[fsBold]");
        FLabel62.setVerticalAlignment("taVerticalCenter");
        FLabel62.setWordBreak(false);
        hboxMarca3.addChildren(FLabel62);
        FLabel62.applyProperties();
    }

    public TFHBox FHBox160 = new TFHBox();

    private void init_FHBox160() {
        FHBox160.setName("FHBox160");
        FHBox160.setLeft(30);
        FHBox160.setTop(0);
        FHBox160.setWidth(7);
        FHBox160.setHeight(22);
        FHBox160.setBorderStyle("stNone");
        FHBox160.setPaddingTop(0);
        FHBox160.setPaddingLeft(0);
        FHBox160.setPaddingRight(0);
        FHBox160.setPaddingBottom(0);
        FHBox160.setMarginTop(0);
        FHBox160.setMarginLeft(0);
        FHBox160.setMarginRight(0);
        FHBox160.setMarginBottom(0);
        FHBox160.setSpacing(1);
        FHBox160.setFlexVflex("ftTrue");
        FHBox160.setFlexHflex("ftTrue");
        FHBox160.setScrollable(false);
        FHBox160.setBoxShadowConfigHorizontalLength(10);
        FHBox160.setBoxShadowConfigVerticalLength(10);
        FHBox160.setBoxShadowConfigBlurRadius(5);
        FHBox160.setBoxShadowConfigSpreadRadius(0);
        FHBox160.setBoxShadowConfigShadowColor("clBlack");
        FHBox160.setBoxShadowConfigOpacity(75);
        FHBox160.setVAlign("tvTop");
        hboxMarca3.addChildren(FHBox160);
        FHBox160.applyProperties();
    }

    public TFHBox hboxMarca4 = new TFHBox();

    private void init_hboxMarca4() {
        hboxMarca4.setName("hboxMarca4");
        hboxMarca4.setLeft(124);
        hboxMarca4.setTop(0);
        hboxMarca4.setWidth(31);
        hboxMarca4.setHeight(31);
        hboxMarca4.setBorderStyle("stNone");
        hboxMarca4.setPaddingTop(4);
        hboxMarca4.setPaddingLeft(0);
        hboxMarca4.setPaddingRight(0);
        hboxMarca4.setPaddingBottom(0);
        hboxMarca4.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hboxMarca4Click(event);
            processarFlow("FrmAssinatura", "hboxMarca4", "OnClick");
        });
        hboxMarca4.setMarginTop(0);
        hboxMarca4.setMarginLeft(0);
        hboxMarca4.setMarginRight(0);
        hboxMarca4.setMarginBottom(0);
        hboxMarca4.setSpacing(1);
        hboxMarca4.setFlexVflex("ftFalse");
        hboxMarca4.setFlexHflex("ftFalse");
        hboxMarca4.setScrollable(false);
        hboxMarca4.setBoxShadowConfigHorizontalLength(10);
        hboxMarca4.setBoxShadowConfigVerticalLength(10);
        hboxMarca4.setBoxShadowConfigBlurRadius(5);
        hboxMarca4.setBoxShadowConfigSpreadRadius(0);
        hboxMarca4.setBoxShadowConfigShadowColor("clBlack");
        hboxMarca4.setBoxShadowConfigOpacity(75);
        hboxMarca4.setVAlign("tvTop");
        hboxMarcadorCombustivel.addChildren(hboxMarca4);
        hboxMarca4.applyProperties();
    }

    public TFHBox FHBox162 = new TFHBox();

    private void init_FHBox162() {
        FHBox162.setName("FHBox162");
        FHBox162.setLeft(0);
        FHBox162.setTop(0);
        FHBox162.setWidth(7);
        FHBox162.setHeight(22);
        FHBox162.setBorderStyle("stNone");
        FHBox162.setPaddingTop(0);
        FHBox162.setPaddingLeft(0);
        FHBox162.setPaddingRight(0);
        FHBox162.setPaddingBottom(0);
        FHBox162.setMarginTop(0);
        FHBox162.setMarginLeft(0);
        FHBox162.setMarginRight(0);
        FHBox162.setMarginBottom(0);
        FHBox162.setSpacing(1);
        FHBox162.setFlexVflex("ftTrue");
        FHBox162.setFlexHflex("ftTrue");
        FHBox162.setScrollable(false);
        FHBox162.setBoxShadowConfigHorizontalLength(10);
        FHBox162.setBoxShadowConfigVerticalLength(10);
        FHBox162.setBoxShadowConfigBlurRadius(5);
        FHBox162.setBoxShadowConfigSpreadRadius(0);
        FHBox162.setBoxShadowConfigShadowColor("clBlack");
        FHBox162.setBoxShadowConfigOpacity(75);
        FHBox162.setVAlign("tvTop");
        hboxMarca4.addChildren(FHBox162);
        FHBox162.applyProperties();
    }

    public TFLabel FLabel63 = new TFLabel();

    private void init_FLabel63() {
        FLabel63.setName("FLabel63");
        FLabel63.setLeft(7);
        FLabel63.setTop(0);
        FLabel63.setWidth(8);
        FLabel63.setHeight(14);
        FLabel63.setCaption("1");
        FLabel63.setFontColor("clWindowText");
        FLabel63.setFontSize(-12);
        FLabel63.setFontName("Tahoma");
        FLabel63.setFontStyle("[fsBold]");
        FLabel63.setVerticalAlignment("taVerticalCenter");
        FLabel63.setWordBreak(false);
        hboxMarca4.addChildren(FLabel63);
        FLabel63.applyProperties();
    }

    public TFHBox FHBox163 = new TFHBox();

    private void init_FHBox163() {
        FHBox163.setName("FHBox163");
        FHBox163.setLeft(15);
        FHBox163.setTop(0);
        FHBox163.setWidth(7);
        FHBox163.setHeight(22);
        FHBox163.setBorderStyle("stNone");
        FHBox163.setPaddingTop(0);
        FHBox163.setPaddingLeft(0);
        FHBox163.setPaddingRight(0);
        FHBox163.setPaddingBottom(0);
        FHBox163.setMarginTop(0);
        FHBox163.setMarginLeft(0);
        FHBox163.setMarginRight(0);
        FHBox163.setMarginBottom(0);
        FHBox163.setSpacing(1);
        FHBox163.setFlexVflex("ftTrue");
        FHBox163.setFlexHflex("ftTrue");
        FHBox163.setScrollable(false);
        FHBox163.setBoxShadowConfigHorizontalLength(10);
        FHBox163.setBoxShadowConfigVerticalLength(10);
        FHBox163.setBoxShadowConfigBlurRadius(5);
        FHBox163.setBoxShadowConfigSpreadRadius(0);
        FHBox163.setBoxShadowConfigShadowColor("clBlack");
        FHBox163.setBoxShadowConfigOpacity(75);
        FHBox163.setVAlign("tvTop");
        hboxMarca4.addChildren(FHBox163);
        FHBox163.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void signatureSave(final UploadEvent event);

    public void btnDesfazerClick(final Event<Object> event) {
        if (btnDesfazer.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnDesfazer");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnLimparClick(final Event<Object> event) {
        if (btnLimpar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimpar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtKmChange(final Event<Object> event);

    public abstract void hboxMarca0Click(final Event<Object> event);

    public abstract void hboxMarca1Click(final Event<Object> event);

    public abstract void hboxMarca2Click(final Event<Object> event);

    public abstract void hboxMarca3Click(final Event<Object> event);

    public abstract void hboxMarca4Click(final Event<Object> event);

}