package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPesquisaFormaPgto extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PesquisaFormaPgtoRNA rn = null;

    public FrmPesquisaFormaPgto() {
        try {
            rn = (freedom.bytecode.rn.PesquisaFormaPgtoRNA) getRN(freedom.bytecode.rn.wizard.PesquisaFormaPgtoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbListaFormasPgto();
        init_tbEmpresasFiliaisSel();
        init_tbListaEmpresasDepartamentos();
        init_tbListaFormasPgto1();
        init_tbListaTipoPgtoNbs();
        init_popGrdFormasPgto();
        init_mmSelecionarTodos();
        init_mmSelecionarNenhum();
        init_mmExportarExcelGrdFormasPgto();
        init_vBoxPrincipal();
        init_FHBox4();
        init_hBoxLinha01();
        init_FHBox2();
        init_btnVoltar();
        init_FHBox1();
        init_btnSalvar();
        init_FHBox3();
        init_FHBox5();
        init_hBoxLinha02();
        init_FHBox10();
        init_cboEmpresa();
        init_FHBox11();
        init_cboDepartamento();
        init_FHBox12();
        init_cboTipoFormaPagamento();
        init_FHBox13();
        init_cboExclusiva();
        init_FHBox14();
        init_cboFormaPagamento();
        init_FHBox15();
        init_btnPesquisar();
        init_FHBox16();
        init_FHBox6();
        init_hBoxLinha03();
        init_FHBox8();
        init_grdFormasPgto();
        init_FHBox9();
        init_FHBox7();
        init_FrmPesquisaFormaPgto();
    }

    public LISTA_FORMAS_PGTO tbListaFormasPgto;

    private void init_tbListaFormasPgto() {
        tbListaFormasPgto = rn.tbListaFormasPgto;
        tbListaFormasPgto.setName("tbListaFormasPgto");
        tbListaFormasPgto.setMaxRowCount(0);
        tbListaFormasPgto.setWKey("5300550;53001");
        tbListaFormasPgto.setRatioBatchSize(20);
        getTables().put(tbListaFormasPgto, "tbListaFormasPgto");
        tbListaFormasPgto.applyProperties();
    }

    public EMPRESAS_FILIAIS_SEL tbEmpresasFiliaisSel;

    private void init_tbEmpresasFiliaisSel() {
        tbEmpresasFiliaisSel = rn.tbEmpresasFiliaisSel;
        tbEmpresasFiliaisSel.setName("tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.setMaxRowCount(0);
        tbEmpresasFiliaisSel.setWKey("5300550;53002");
        tbEmpresasFiliaisSel.setRatioBatchSize(20);
        getTables().put(tbEmpresasFiliaisSel, "tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.applyProperties();
    }

    public LISTA_EMPRESAS_DEPARTAMENTOS tbListaEmpresasDepartamentos;

    private void init_tbListaEmpresasDepartamentos() {
        tbListaEmpresasDepartamentos = rn.tbListaEmpresasDepartamentos;
        tbListaEmpresasDepartamentos.setName("tbListaEmpresasDepartamentos");
        tbListaEmpresasDepartamentos.setMaxRowCount(0);
        tbListaEmpresasDepartamentos.setWKey("5300550;53003");
        tbListaEmpresasDepartamentos.setRatioBatchSize(20);
        getTables().put(tbListaEmpresasDepartamentos, "tbListaEmpresasDepartamentos");
        tbListaEmpresasDepartamentos.applyProperties();
    }

    public LISTA_FORMAS_PGTO tbListaFormasPgto1;

    private void init_tbListaFormasPgto1() {
        tbListaFormasPgto1 = rn.tbListaFormasPgto1;
        tbListaFormasPgto1.setName("tbListaFormasPgto1");
        tbListaFormasPgto1.setMaxRowCount(0);
        tbListaFormasPgto1.setWKey("5300550;53004");
        tbListaFormasPgto1.setRatioBatchSize(20);
        getTables().put(tbListaFormasPgto1, "tbListaFormasPgto1");
        tbListaFormasPgto1.applyProperties();
    }

    public LISTA_TIPO_PGTO_NBS tbListaTipoPgtoNbs;

    private void init_tbListaTipoPgtoNbs() {
        tbListaTipoPgtoNbs = rn.tbListaTipoPgtoNbs;
        tbListaTipoPgtoNbs.setName("tbListaTipoPgtoNbs");
        tbListaTipoPgtoNbs.setMaxRowCount(0);
        tbListaTipoPgtoNbs.setWKey("5300550;53005");
        tbListaTipoPgtoNbs.setRatioBatchSize(20);
        getTables().put(tbListaTipoPgtoNbs, "tbListaTipoPgtoNbs");
        tbListaTipoPgtoNbs.applyProperties();
    }

    public TFPopupMenu popGrdFormasPgto = new TFPopupMenu();

    private void init_popGrdFormasPgto() {
        popGrdFormasPgto.setName("popGrdFormasPgto");
        FrmPesquisaFormaPgto.addChildren(popGrdFormasPgto);
        popGrdFormasPgto.applyProperties();
    }

    public TFMenuItem mmSelecionarTodos = new TFMenuItem();

    private void init_mmSelecionarTodos() {
        mmSelecionarTodos.setName("mmSelecionarTodos");
        mmSelecionarTodos.setCaption("Selecionar Todos");
        mmSelecionarTodos.setHint("Selecionar Todos");
        mmSelecionarTodos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarTodosClick(event);
            processarFlow("FrmPesquisaFormaPgto", "mmSelecionarTodos", "OnClick");
        });
        mmSelecionarTodos.setAccess(false);
        mmSelecionarTodos.setCheckmark(false);
        popGrdFormasPgto.addChildren(mmSelecionarTodos);
        mmSelecionarTodos.applyProperties();
    }

    public TFMenuItem mmSelecionarNenhum = new TFMenuItem();

    private void init_mmSelecionarNenhum() {
        mmSelecionarNenhum.setName("mmSelecionarNenhum");
        mmSelecionarNenhum.setCaption("Selecionar Nenhum");
        mmSelecionarNenhum.setHint("Selecionar Nenhum");
        mmSelecionarNenhum.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarNenhumClick(event);
            processarFlow("FrmPesquisaFormaPgto", "mmSelecionarNenhum", "OnClick");
        });
        mmSelecionarNenhum.setAccess(false);
        mmSelecionarNenhum.setCheckmark(false);
        popGrdFormasPgto.addChildren(mmSelecionarNenhum);
        mmSelecionarNenhum.applyProperties();
    }

    public TFMenuItem mmExportarExcelGrdFormasPgto = new TFMenuItem();

    private void init_mmExportarExcelGrdFormasPgto() {
        mmExportarExcelGrdFormasPgto.setName("mmExportarExcelGrdFormasPgto");
        mmExportarExcelGrdFormasPgto.setCaption("Exportar Excel");
        mmExportarExcelGrdFormasPgto.setHint("Exportar Excel");
        mmExportarExcelGrdFormasPgto.setImageIndex(22004);
        mmExportarExcelGrdFormasPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmExportarExcelGrdFormasPgtoClick(event);
            processarFlow("FrmPesquisaFormaPgto", "mmExportarExcelGrdFormasPgto", "OnClick");
        });
        mmExportarExcelGrdFormasPgto.setAccess(false);
        mmExportarExcelGrdFormasPgto.setCheckmark(false);
        popGrdFormasPgto.addChildren(mmExportarExcelGrdFormasPgto);
        mmExportarExcelGrdFormasPgto.applyProperties();
    }

    protected TFForm FrmPesquisaFormaPgto = this;
    private void init_FrmPesquisaFormaPgto() {
        FrmPesquisaFormaPgto.setName("FrmPesquisaFormaPgto");
        FrmPesquisaFormaPgto.setCaption("Selecionar formas de pagamento");
        FrmPesquisaFormaPgto.setClientHeight(392);
        FrmPesquisaFormaPgto.setClientWidth(1014);
        FrmPesquisaFormaPgto.setColor("clBtnFace");
        FrmPesquisaFormaPgto.setWKey("5300550");
        FrmPesquisaFormaPgto.setSpacing(0);
        FrmPesquisaFormaPgto.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(1014);
        vBoxPrincipal.setHeight(392);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmPesquisaFormaPgto.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(0);
        FHBox4.setWidth(1000);
        FHBox4.setHeight(5);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox hBoxLinha01 = new TFHBox();

    private void init_hBoxLinha01() {
        hBoxLinha01.setName("hBoxLinha01");
        hBoxLinha01.setLeft(0);
        hBoxLinha01.setTop(6);
        hBoxLinha01.setWidth(1000);
        hBoxLinha01.setHeight(61);
        hBoxLinha01.setAlign("alTop");
        hBoxLinha01.setBorderStyle("stNone");
        hBoxLinha01.setPaddingTop(0);
        hBoxLinha01.setPaddingLeft(0);
        hBoxLinha01.setPaddingRight(0);
        hBoxLinha01.setPaddingBottom(0);
        hBoxLinha01.setMarginTop(0);
        hBoxLinha01.setMarginLeft(0);
        hBoxLinha01.setMarginRight(0);
        hBoxLinha01.setMarginBottom(0);
        hBoxLinha01.setSpacing(1);
        hBoxLinha01.setFlexVflex("ftMin");
        hBoxLinha01.setFlexHflex("ftTrue");
        hBoxLinha01.setScrollable(false);
        hBoxLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha01.setBoxShadowConfigOpacity(75);
        hBoxLinha01.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha01);
        hBoxLinha01.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(5);
        FHBox2.setHeight(40);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(5);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(65);
        btnVoltar.setHeight(53);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmPesquisaFormaPgto", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(70);
        FHBox1.setTop(0);
        FHBox1.setWidth(5);
        FHBox1.setHeight(40);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(75);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(53);
        btnSalvar.setHint("Salvar");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmPesquisaFormaPgto", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3"
 + "0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220"
 + "AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950"
 + "4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5"
 + "5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37"
 + "35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067"
 + "603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7"
 + "29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B"
 + "DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA"
 + "B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B"
 + "A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194"
 + "B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002"
 + "EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715"
 + "87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B"
 + "AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9"
 + "32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66"
 + "5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E"
 + "4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05"
 + "05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF"
 + "0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76"
 + "093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0"
 + "C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65"
 + "C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70"
 + "9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D"
 + "80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12"
 + "B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8"
 + "6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087"
 + "94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF"
 + "0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34"
 + "2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2"
 + "134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000"
 + "0049454E44AE426082");
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxLinha01.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(140);
        FHBox3.setTop(0);
        FHBox3.setWidth(5);
        FHBox3.setHeight(40);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        hBoxLinha01.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(68);
        FHBox5.setWidth(1000);
        FHBox5.setHeight(5);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox hBoxLinha02 = new TFHBox();

    private void init_hBoxLinha02() {
        hBoxLinha02.setName("hBoxLinha02");
        hBoxLinha02.setLeft(0);
        hBoxLinha02.setTop(74);
        hBoxLinha02.setWidth(1000);
        hBoxLinha02.setHeight(35);
        hBoxLinha02.setAlign("alTop");
        hBoxLinha02.setBorderStyle("stNone");
        hBoxLinha02.setPaddingTop(0);
        hBoxLinha02.setPaddingLeft(0);
        hBoxLinha02.setPaddingRight(0);
        hBoxLinha02.setPaddingBottom(0);
        hBoxLinha02.setMarginTop(0);
        hBoxLinha02.setMarginLeft(0);
        hBoxLinha02.setMarginRight(0);
        hBoxLinha02.setMarginBottom(0);
        hBoxLinha02.setSpacing(1);
        hBoxLinha02.setFlexVflex("ftMin");
        hBoxLinha02.setFlexHflex("ftTrue");
        hBoxLinha02.setScrollable(false);
        hBoxLinha02.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha02.setBoxShadowConfigVerticalLength(10);
        hBoxLinha02.setBoxShadowConfigBlurRadius(5);
        hBoxLinha02.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha02.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha02.setBoxShadowConfigOpacity(75);
        hBoxLinha02.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha02);
        hBoxLinha02.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(5);
        FHBox10.setHeight(20);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftFalse");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFCombo cboEmpresa = new TFCombo();

    private void init_cboEmpresa() {
        cboEmpresa.setName("cboEmpresa");
        cboEmpresa.setLeft(5);
        cboEmpresa.setTop(0);
        cboEmpresa.setWidth(190);
        cboEmpresa.setHeight(21);
        cboEmpresa.setHint("Empresa");
        cboEmpresa.setLookupTable(tbEmpresasFiliaisSel);
        cboEmpresa.setLookupKey("CODIGODAEMPRESA");
        cboEmpresa.setLookupDesc("NOMEECODIGODAEMPRESA");
        cboEmpresa.setFlex(false);
        cboEmpresa.setHelpCaption("Empresa");
        cboEmpresa.setReadOnly(true);
        cboEmpresa.setRequired(false);
        cboEmpresa.setPrompt("Empresa");
        cboEmpresa.setConstraintCheckWhen("cwImmediate");
        cboEmpresa.setConstraintCheckType("ctExpression");
        cboEmpresa.setConstraintFocusOnError(false);
        cboEmpresa.setConstraintEnableUI(true);
        cboEmpresa.setConstraintEnabled(false);
        cboEmpresa.setConstraintFormCheck(true);
        cboEmpresa.setClearOnDelKey(true);
        cboEmpresa.setUseClearButton(true);
        cboEmpresa.setHideClearButtonOnNullValue(false);
        cboEmpresa.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaChange(event);
            processarFlow("FrmPesquisaFormaPgto", "cboEmpresa", "OnChange");
        });
        cboEmpresa.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboEmpresaClearClick(event);
            processarFlow("FrmPesquisaFormaPgto", "cboEmpresa", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboEmpresa);
        cboEmpresa.applyProperties();
        addValidatable(cboEmpresa);
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(195);
        FHBox11.setTop(0);
        FHBox11.setWidth(5);
        FHBox11.setHeight(20);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFCombo cboDepartamento = new TFCombo();

    private void init_cboDepartamento() {
        cboDepartamento.setName("cboDepartamento");
        cboDepartamento.setLeft(200);
        cboDepartamento.setTop(0);
        cboDepartamento.setWidth(190);
        cboDepartamento.setHeight(21);
        cboDepartamento.setHint("Departamento");
        cboDepartamento.setLookupTable(tbListaEmpresasDepartamentos);
        cboDepartamento.setLookupKey("DPTO_CODIGO");
        cboDepartamento.setLookupDesc("DPTO_DESCRICAO_CODIGO");
        cboDepartamento.setFlex(false);
        cboDepartamento.setHelpCaption("Departamento");
        cboDepartamento.setReadOnly(true);
        cboDepartamento.setRequired(false);
        cboDepartamento.setPrompt("Departamento");
        cboDepartamento.setConstraintCheckWhen("cwImmediate");
        cboDepartamento.setConstraintCheckType("ctExpression");
        cboDepartamento.setConstraintFocusOnError(false);
        cboDepartamento.setConstraintEnableUI(true);
        cboDepartamento.setConstraintEnabled(false);
        cboDepartamento.setConstraintFormCheck(true);
        cboDepartamento.setClearOnDelKey(true);
        cboDepartamento.setUseClearButton(true);
        cboDepartamento.setHideClearButtonOnNullValue(false);
        cboDepartamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDepartamentoChange(event);
            processarFlow("FrmPesquisaFormaPgto", "cboDepartamento", "OnChange");
        });
        cboDepartamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboDepartamentoClearClick(event);
            processarFlow("FrmPesquisaFormaPgto", "cboDepartamento", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboDepartamento);
        cboDepartamento.applyProperties();
        addValidatable(cboDepartamento);
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(390);
        FHBox12.setTop(0);
        FHBox12.setWidth(5);
        FHBox12.setHeight(20);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFCombo cboTipoFormaPagamento = new TFCombo();

    private void init_cboTipoFormaPagamento() {
        cboTipoFormaPagamento.setName("cboTipoFormaPagamento");
        cboTipoFormaPagamento.setLeft(395);
        cboTipoFormaPagamento.setTop(0);
        cboTipoFormaPagamento.setWidth(190);
        cboTipoFormaPagamento.setHeight(21);
        cboTipoFormaPagamento.setHint("Tipo da Forma de Pagamento");
        cboTipoFormaPagamento.setLookupTable(tbListaTipoPgtoNbs);
        cboTipoFormaPagamento.setLookupKey("COD_TIPO_PGTO_NBS");
        cboTipoFormaPagamento.setLookupDesc("DESC_COD_TIPO_PGTO_NBS");
        cboTipoFormaPagamento.setFlex(false);
        cboTipoFormaPagamento.setHelpCaption("Tipo da Forma de Pagamento");
        cboTipoFormaPagamento.setReadOnly(true);
        cboTipoFormaPagamento.setRequired(false);
        cboTipoFormaPagamento.setPrompt("Tipo da Forma de Pagamento");
        cboTipoFormaPagamento.setConstraintCheckWhen("cwImmediate");
        cboTipoFormaPagamento.setConstraintCheckType("ctExpression");
        cboTipoFormaPagamento.setConstraintFocusOnError(false);
        cboTipoFormaPagamento.setConstraintEnableUI(true);
        cboTipoFormaPagamento.setConstraintEnabled(false);
        cboTipoFormaPagamento.setConstraintFormCheck(true);
        cboTipoFormaPagamento.setClearOnDelKey(true);
        cboTipoFormaPagamento.setUseClearButton(true);
        cboTipoFormaPagamento.setHideClearButtonOnNullValue(false);
        cboTipoFormaPagamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboTipoFormaPagamentoChange(event);
            processarFlow("FrmPesquisaFormaPgto", "cboTipoFormaPagamento", "OnChange");
        });
        cboTipoFormaPagamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboTipoFormaPagamentoClearClick(event);
            processarFlow("FrmPesquisaFormaPgto", "cboTipoFormaPagamento", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboTipoFormaPagamento);
        cboTipoFormaPagamento.applyProperties();
        addValidatable(cboTipoFormaPagamento);
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(585);
        FHBox13.setTop(0);
        FHBox13.setWidth(5);
        FHBox13.setHeight(20);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFCombo cboExclusiva = new TFCombo();

    private void init_cboExclusiva() {
        cboExclusiva.setName("cboExclusiva");
        cboExclusiva.setLeft(590);
        cboExclusiva.setTop(0);
        cboExclusiva.setWidth(150);
        cboExclusiva.setHeight(21);
        cboExclusiva.setHint("Exclusiva");
        cboExclusiva.setFlex(false);
        cboExclusiva.setListOptions("Sim=S;N\u00E3o=N");
        cboExclusiva.setHelpCaption("Exclusiva");
        cboExclusiva.setReadOnly(true);
        cboExclusiva.setRequired(false);
        cboExclusiva.setPrompt("Exclusiva");
        cboExclusiva.setConstraintCheckWhen("cwImmediate");
        cboExclusiva.setConstraintCheckType("ctExpression");
        cboExclusiva.setConstraintFocusOnError(false);
        cboExclusiva.setConstraintEnableUI(true);
        cboExclusiva.setConstraintEnabled(false);
        cboExclusiva.setConstraintFormCheck(true);
        cboExclusiva.setClearOnDelKey(true);
        cboExclusiva.setUseClearButton(true);
        cboExclusiva.setHideClearButtonOnNullValue(false);
        cboExclusiva.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboExclusivaChange(event);
            processarFlow("FrmPesquisaFormaPgto", "cboExclusiva", "OnChange");
        });
        cboExclusiva.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboExclusivaClearClick(event);
            processarFlow("FrmPesquisaFormaPgto", "cboExclusiva", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboExclusiva);
        cboExclusiva.applyProperties();
        addValidatable(cboExclusiva);
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(740);
        FHBox14.setTop(0);
        FHBox14.setWidth(5);
        FHBox14.setHeight(20);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFCombo cboFormaPagamento = new TFCombo();

    private void init_cboFormaPagamento() {
        cboFormaPagamento.setName("cboFormaPagamento");
        cboFormaPagamento.setLeft(745);
        cboFormaPagamento.setTop(0);
        cboFormaPagamento.setWidth(190);
        cboFormaPagamento.setHeight(21);
        cboFormaPagamento.setHint("Forma de Pagamento");
        cboFormaPagamento.setLookupTable(tbListaFormasPgto1);
        cboFormaPagamento.setLookupKey("FORMA_CODIGO");
        cboFormaPagamento.setLookupDesc("FORMA_DESCRICAO_COD");
        cboFormaPagamento.setFlex(false);
        cboFormaPagamento.setHelpCaption("Forma de Pagamento");
        cboFormaPagamento.setReadOnly(true);
        cboFormaPagamento.setRequired(false);
        cboFormaPagamento.setPrompt("Forma de Pagamento");
        cboFormaPagamento.setConstraintCheckWhen("cwImmediate");
        cboFormaPagamento.setConstraintCheckType("ctExpression");
        cboFormaPagamento.setConstraintFocusOnError(false);
        cboFormaPagamento.setConstraintEnableUI(true);
        cboFormaPagamento.setConstraintEnabled(false);
        cboFormaPagamento.setConstraintFormCheck(true);
        cboFormaPagamento.setClearOnDelKey(true);
        cboFormaPagamento.setUseClearButton(true);
        cboFormaPagamento.setHideClearButtonOnNullValue(false);
        cboFormaPagamento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaPagamentoChange(event);
            processarFlow("FrmPesquisaFormaPgto", "cboFormaPagamento", "OnChange");
        });
        cboFormaPagamento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaPagamentoClearClick(event);
            processarFlow("FrmPesquisaFormaPgto", "cboFormaPagamento", "OnClearClick");
        });
        hBoxLinha02.addChildren(cboFormaPagamento);
        cboFormaPagamento.applyProperties();
        addValidatable(cboFormaPagamento);
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(935);
        FHBox15.setTop(0);
        FHBox15.setWidth(5);
        FHBox15.setHeight(20);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(940);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(24);
        btnPesquisar.setHeight(24);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmPesquisaFormaPgto", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51"
 + "782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9"
 + "B9B0DBF5D30000000049454E44AE426082");
        btnPesquisar.setImageId(0);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconClass("refresh");
        btnPesquisar.setIconReverseDirection(false);
        hBoxLinha02.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(964);
        FHBox16.setTop(0);
        FHBox16.setWidth(5);
        FHBox16.setHeight(20);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftFalse");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        hBoxLinha02.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(0);
        FHBox6.setTop(110);
        FHBox6.setWidth(1000);
        FHBox6.setHeight(5);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFHBox hBoxLinha03 = new TFHBox();

    private void init_hBoxLinha03() {
        hBoxLinha03.setName("hBoxLinha03");
        hBoxLinha03.setLeft(0);
        hBoxLinha03.setTop(116);
        hBoxLinha03.setWidth(1000);
        hBoxLinha03.setHeight(245);
        hBoxLinha03.setBorderStyle("stNone");
        hBoxLinha03.setPaddingTop(0);
        hBoxLinha03.setPaddingLeft(0);
        hBoxLinha03.setPaddingRight(0);
        hBoxLinha03.setPaddingBottom(0);
        hBoxLinha03.setMarginTop(0);
        hBoxLinha03.setMarginLeft(0);
        hBoxLinha03.setMarginRight(0);
        hBoxLinha03.setMarginBottom(0);
        hBoxLinha03.setSpacing(1);
        hBoxLinha03.setFlexVflex("ftTrue");
        hBoxLinha03.setFlexHflex("ftTrue");
        hBoxLinha03.setScrollable(false);
        hBoxLinha03.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha03.setBoxShadowConfigVerticalLength(10);
        hBoxLinha03.setBoxShadowConfigBlurRadius(5);
        hBoxLinha03.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha03.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha03.setBoxShadowConfigOpacity(75);
        hBoxLinha03.setVAlign("tvTop");
        vBoxPrincipal.addChildren(hBoxLinha03);
        hBoxLinha03.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(5);
        FHBox8.setHeight(40);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftFalse");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        hBoxLinha03.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFGrid grdFormasPgto = new TFGrid();

    private void init_grdFormasPgto() {
        grdFormasPgto.setName("grdFormasPgto");
        grdFormasPgto.setLeft(5);
        grdFormasPgto.setTop(0);
        grdFormasPgto.setWidth(946);
        grdFormasPgto.setHeight(200);
        grdFormasPgto.setAlign("alClient");
        grdFormasPgto.setTable(tbListaFormasPgto);
        grdFormasPgto.setFlexVflex("ftTrue");
        grdFormasPgto.setFlexHflex("ftTrue");
        grdFormasPgto.setPagingEnabled(true);
        grdFormasPgto.setFrozenColumns(0);
        grdFormasPgto.setShowFooter(false);
        grdFormasPgto.setShowHeader(true);
        grdFormasPgto.setMultiSelection(false);
        grdFormasPgto.setGroupingEnabled(false);
        grdFormasPgto.setGroupingExpanded(false);
        grdFormasPgto.setGroupingShowFooter(false);
        grdFormasPgto.setCrosstabEnabled(false);
        grdFormasPgto.setCrosstabGroupType("cgtConcat");
        grdFormasPgto.setEditionEnabled(false);
        grdFormasPgto.setContextMenu(popGrdFormasPgto);
        grdFormasPgto.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEL");
        item0.setTitleCaption(" ");
        item0.setWidth(50);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL = 'S'");
        item1.setHint("Registro selecionado");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdFormasPgtodesmarcarRegistroDaGrade(event);
            processarFlow("FrmPesquisaFormaPgto", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL = 'N'");
        item2.setHint("Registro n\u00E3o selecionado");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdFormasPgtomarcarRegistroDaGrade(event);
            processarFlow("FrmPesquisaFormaPgto", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setHint("Selecionar");
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdFormasPgto.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("EMP_CODIGO");
        item3.setTitleCaption("CE");
        item3.setWidth(50);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdFormasPgto.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("EMP_NOME");
        item4.setTitleCaption("Empresa");
        item4.setWidth(200);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdFormasPgto.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DPTO_DESCRICAO");
        item5.setTitleCaption("Departamento");
        item5.setWidth(200);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdFormasPgto.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("FORMA_DESCRICAO");
        item6.setTitleCaption("Forma de Pagamento");
        item6.setWidth(200);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(true);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setHiperLink(false);
        item6.setHint("Forma de Pagamento");
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdFormasPgto.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("FORMA_TIPO_DESCRICAO_CODIGO");
        item7.setTitleCaption("Tipo da Forma de Pagamento");
        item7.setWidth(200);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(true);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setHiperLink(false);
        item7.setHint("Tipo da Forma de Pagamento");
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        grdFormasPgto.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("EH_EXCLUSIVA_CLIENTE");
        item8.setTitleCaption("Exclusiva");
        item8.setWidth(60);
        item8.setVisible(true);
        item8.setPrecision(0);
        item8.setTextAlign("taCenter");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(true);
        item8.setImageHeader(0);
        item8.setWrap(true);
        item8.setFlex(false);
        TFImageExpression item9 = new TFImageExpression();
        item9.setExpression("EH_EXCLUSIVA_CLIENTE = 'S'");
        item9.setHint("Sim");
        item9.setEvalType("etExpression");
        item9.setImageId(4300107);
        item8.getImages().add(item9);
        TFImageExpression item10 = new TFImageExpression();
        item10.setExpression("EH_EXCLUSIVA_CLIENTE = 'N'");
        item10.setHint("N\u00E3o");
        item10.setEvalType("etExpression");
        item10.setImageId(4300106);
        item8.getImages().add(item10);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(false);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setHiperLink(false);
        item8.setHint("Exclusiva Cliente Especial");
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        grdFormasPgto.getColumns().add(item8);
        hBoxLinha03.addChildren(grdFormasPgto);
        grdFormasPgto.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(951);
        FHBox9.setTop(0);
        FHBox9.setWidth(5);
        FHBox9.setHeight(40);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        hBoxLinha03.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(362);
        FHBox7.setWidth(1000);
        FHBox7.setHeight(5);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vBoxPrincipal.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void cboEmpresaChange(final Event<Object> event);

    public abstract void cboEmpresaClearClick(final Event<Object> event);

    public abstract void cboDepartamentoChange(final Event<Object> event);

    public abstract void cboDepartamentoClearClick(final Event<Object> event);

    public abstract void cboTipoFormaPagamentoChange(final Event<Object> event);

    public abstract void cboTipoFormaPagamentoClearClick(final Event<Object> event);

    public abstract void cboExclusivaChange(final Event<Object> event);

    public abstract void cboExclusivaClearClick(final Event<Object> event);

    public abstract void cboFormaPagamentoChange(final Event<Object> event);

    public abstract void cboFormaPagamentoClearClick(final Event<Object> event);

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdFormasPgtodesmarcarRegistroDaGrade(final Event<Object> event);

    public abstract void grdFormasPgtomarcarRegistroDaGrade(final Event<Object> event);

    public abstract void mmSelecionarTodosClick(final Event<Object> event);

    public abstract void mmSelecionarNenhumClick(final Event<Object> event);

    public abstract void mmExportarExcelGrdFormasPgtoClick(final Event<Object> event);

}