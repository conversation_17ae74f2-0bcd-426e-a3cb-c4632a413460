package freedom.bytecode.form.wizard;

import freedom.bytecode.rn.MotivoPerdasRNA;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.grid.TFGridExporter;
import freedom.client.controls.impl.treegrid.TFTreeGridExporter;
import freedom.client.controls.IBaseComponent;
import freedom.client.event.Event;
import freedom.client.controls.IFocusable;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.IDialog;
import freedom.data.RowState;
import freedom.data.TableState;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.impl.Row;
import freedom.data.Value;
import freedom.util.CastUtil;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import static freedom.client.util.Dialog.showMessage;
import java.util.Date;


public abstract class FrmMotivoPerdasW extends FrmMotivoPerdas {

    private static final long serialVersionUID = 20130827081850L;

    public FrmMotivoPerdasW() {
        lblMensagem.setCaption("");
        habilitaComp(false);
        efAtivo.setValue("T");        
        onAbreTabelaAux();            
    }

    protected void habilitaComp(Boolean enabled) {
        gridPrincipal.setEnabled(!enabled);
        btnConsultar.setEnabled(!enabled);
        btnFiltroAvancado.setEnabled(!enabled);
        btnNovo.setEnabled(!enabled && !menuSelecaoMultipla.isChecked());
        btnAlterar.setEnabled(!enabled && !tbDescartes.isEmpty());
        btnExcluir.setEnabled(!enabled && !tbDescartes.isEmpty());
        if (! menuHabilitaNavegacao.isChecked()) {                                       // menu popup habilitar navegação
            btnProximo.setEnabled(!enabled && !tbDescartes.isEmpty());
            btnAnterior.setEnabled(!enabled && !tbDescartes.isEmpty());
        }
        btnAceitar.setEnabled(!enabled && !tbDescartes.isEmpty());
        btnCancelar.setEnabled(enabled);
        btnSalvar.setEnabled(enabled);
        btnSalvarContinuar.setEnabled(enabled && !menuSelecaoMultipla.isChecked());
        menuSelecaoMultipla.setVisible(!enabled);
        
        edDescricaoDescarte34001.setEnabled(enabled && ( ! tbDescartes.isEmpty()  || tbDescartes.getState() == TableState.INSERTING));
        edDepartamento34001.setEnabled(enabled && ( ! tbDescartes.isEmpty()  || tbDescartes.getState() == TableState.INSERTING));
        edEliminarCicloFuturo34001.setEnabled(enabled && ( ! tbDescartes.isEmpty()  || tbDescartes.getState() == TableState.INSERTING));
        edAtivo34001.setEnabled(enabled && ( ! tbDescartes.isEmpty()  || tbDescartes.getState() == TableState.INSERTING));
        edExclusivoDescarteAuto34001.setEnabled(enabled && ( ! tbDescartes.isEmpty()  || tbDescartes.getState() == TableState.INSERTING));

        
    }

    @Override
    public void btnConsultarClick(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }
    
    @Override
    public void btnFiltroAvancadoClick(Event<Object> event) {
        filtroAvancado.doModal();
    }    

    @Override
    public void btnNovoClick(final Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAnteriorClick(final Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnProximoClick(final Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnAlterarClick(final Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnExcluirClick(final Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnCancelarClick(final Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarClick(final Event<Object> event) {
        try {
            if (menuSelecaoMultipla.isChecked()) {
                onSalvarMultiplo();
            } else {
                onSalvar();
            }
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void btnSalvarContinuarClick(final Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    public void executaFiltroPrincipal() throws DataException {
        tbDescartes.clearFilters();
        if (! efDepartamento.getValue().equals(null)) {
           tbDescartes.setFilterDEPARTAMENTO(efDepartamento.getValue());
        }
        if (! efAtivo.getValue().equals(null)) {
           tbDescartes.setFilterATIVO(efAtivo.getValue());
        }
        if (! efExclusivoDescarteAuto.getValue().equals(null)) {
           tbDescartes.setFilterEXCLUSIVO_DESCARTE_AUTO(efExclusivoDescarteAuto.getValue());
        }

    }

    @Override
    public void btnAceitarClick(final Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuHabilitaNavegacaoClick(final Event<Object> event) {
        if (menuHabilitaNavegacao.isChecked()) {
            btnProximo.setEnabled(true);
            btnAnterior.setEnabled(true);
        } else {
            btnProximo.setEnabled(btnNovo.isEnabled());
            btnAnterior.setEnabled(btnNovo.isEnabled());
        }
    }

    @Override
    public void menuSelecaoMultiplaClick(final Event<Object> event) {

        boolean checkedMenu = menuSelecaoMultipla.isChecked();
        gridPrincipal.setMultiSelection(checkedMenu);

        // tratamento das abas visto que pode mexer somente na tabela master
        // Não necessariamente as outras abas podem ser detalhe, pode ser um consulta etc
        // for (int i = 2; i <= pgPrincipal.getPageCount()-1; i++) {
        //      pgPrincipal.selectTab(i);
        //      pgPrincipal.getSelectedTab().setVisible(!checkedMenu);
        // }

        // opções da barra de ferramenta
        btnNovo.setEnabled(! checkedMenu && btnAlterar.isEnabled());
        btnSalvarContinuar.setEnabled(! checkedMenu && btnAlterar.isEnabled());


        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()+30);
            // desregistra o marter table dos componentes
                        edDescricaoDescarte34001.setTable(null);
            edDescricaoDescarte34001.setValue(null);
            edDepartamento34001.setTable(null);
            edDepartamento34001.setValue(null);
            edEliminarCicloFuturo34001.setTable(null);
            edEliminarCicloFuturo34001.setValue(null);
            edAtivo34001.setTable(null);
            edAtivo34001.setValue(null);
            edExclusivoDescarteAuto34001.setTable(null);
            edExclusivoDescarteAuto34001.setValue(null);

            gridPrincipal.clearSelection();
        } else {
            gridPrincipal.getColumns().get(0).setWidth(gridPrincipal.getColumns().get(0).getWidth()-30);
            // registra o master table para os componentes
                        edDescricaoDescarte34001.setTable(tbDescartes);
            edDepartamento34001.setTable(tbDescartes);
            edEliminarCicloFuturo34001.setTable(tbDescartes);
            edAtivo34001.setTable(tbDescartes);
            edExclusivoDescarteAuto34001.setTable(tbDescartes);

        }
        pgPrincipal.selectTab(0);
    }

    @Override
    public void FrmMotivoPerdaskeyActionPesquisar(Event<Object> event) {
        try {
            onConsultar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao consultar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionIncluir(Event<Object> event) {
        try {
            onIncluir();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao incluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionExcluir(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionSalvar(Event<Object> event) {
        try {
            onSalvar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionSalvarContinuar(Event<Object> event) {
        try {
            onSalvarContinuar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionCancelar(Event<Object> event) {
        try {
            onCancelar();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao cancelar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionAnterior(Event<Object> event) {
        try {
            onAnterior();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao voltar para registro anterior")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionProximo(Event<Object> event) {
        try {
            onProximo();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao avançar para o próximo registro")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void FrmMotivoPerdaskeyActionAceitar(Event<Object> event) {
        try {
            onAceitar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao aceitar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }    
    
    @Override
    public void btnMaisClick(Event<Object> event) {
        popMenuPrincipal.open(this);
    }

    @Override
    public void menuItemAbreTabelaAuxClick(Event<Object> event) {
        onAbreTabelaAux();
    }

    @Override
    public void menuItemConfgGridClick(Event<Object> event) {
        gridConfig.doModal();
    }

    @Override
    public void menuItemHelpClick(Event<Object> event) {
        FormUtil.redirect("help/FrmMotivoPerdas.zul", true);
    }

    protected void onConsultar() throws Exception {
        
        tbDescartes.close();
        executaFiltroPrincipal();
        
        tbDescartes.open();
        habilitaComp(false);

        if (menuSelecaoMultipla.isChecked()) {
            gridPrincipal.clearSelection();
        }

        if (tbDescartes.isEmpty()) {
            Dialog.create()
                      .title("Aviso")
                      .message("Registro Não Encontrado...")
                      .showInformation();
        }
    }

    protected void onAnterior() throws Exception {

        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbDescartes.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnAnterior);
        }

        if (tbDescartes.bof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o Primeiro Registro")
                    .showInformation();
        } else {
            tbDescartes.prior();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onProximo() throws Exception {
        // se estiver inserindo ou alteradno salva o registro antes de mover
        TableState st = tbDescartes.getState();
        if (st != TableState.QUERYING) {
            onSalvar();
            Dialog.create().showNotificationInfo("Registro Salvo...", "end_after", 3000, btnProximo);
        }

        if (tbDescartes.eof()) {
            Dialog.create()
                    .title("Erro ao processar")
                    .message("Já é o último registgro")
                    .showInformation();
        } else {
            tbDescartes.next();
        }

        if (st != TableState.QUERYING) {
            onAlterar();
        }
    }

    protected void onIncluir() throws Exception {
        
        rn.incluir(); 
        
        
        pgPrincipal.selectTab(1);
        edDescricaoDescarte34001.setFocus();
        habilitaComp(true);
        lblMensagem.setCaption("Incluindo...");
    }

    protected void onAlterar() throws Exception {
        if (menuSelecaoMultipla.isChecked()) {
            lblMensagem.setCaption("ATENÇÃO: Alterando multiplos registros. Será alterado todos os registros selecionados...");
            pgPrincipal.selectTab(1);
            habilitaComp(true);
        } else {
            if (!tbDescartes.isEmpty()) {
                rn.alterar();
                if (pgPrincipal.getSelectedIndex() == 0)  {
                   pgPrincipal.selectTab(1);
                }                
                habilitaComp(true);
                edDescricaoDescarte34001.setFocus();
                lblMensagem.setCaption("Alterando "+tbDescartes.getDESCRICAO_DESCARTE().asString()+"...");
            } else {
                Dialog.create()
                      .title("Erro ao editar")
                      .message("Selecione um registro antes de editar")
                      .showError();
            }
        }
    }

    protected void onExcluir() throws DataException {
        if (!tbDescartes.isEmpty()) {
           String titulo;
           String mensagem;
           if (menuSelecaoMultipla.isChecked()) {
               titulo = "Exclusão Multipla";
               mensagem = "ATENÇÃO: Serão excluido(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?";
           } else {
               titulo = "Exclusão de Registro";
               mensagem = "Confirma a exclusão do registro selecionado?";
           }

            Dialog.create()
                    .title(titulo)
                    .message(mensagem)
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {


                            try {

                                rn.disableTables();  // desabilita controls e master table das tabelas da transação

                                if (menuSelecaoMultipla.isChecked()) {
                                    for (int bm : gridPrincipal.getSelectedIndices(false)) {
                                         tbDescartes.gotoBookmark(bm);
                                         rn.excluiTableMaster();
                                     }
                                 } else {
                                      rn.excluiTableMaster();
                                 }

                                 rn.excluir();                                        
                                 
                                 habilitaComp(false);
                                 onBeforeExcluir();

                            } catch (DataException ex) {
                                Dialog.create()
                                    .title("Erro ao excluir")
                                    .message(ex.getMessage())
                                    .showException(ex);

                                    try {
                                        tbDescartes.cancelUpdates();
                                    } catch (DataException e) {
                                        showMessage(e.getMessage());
                                    }

                            } finally {
                                  try {
                                      rn.enableTables();  // habilita controls e master table das tabelas da transação
                                  } catch (DataException e) {
                                      showMessage(e.getMessage());
                                  }
                            }
                        }
                    });
        } else {
            Dialog.create()
                    .title("Erro ao excluir")
                    .message("Selecione um registro antes de excluir")
                    .showError();
        }
    }

    protected void onBeforeExcluir() {

    }


    protected void onSalvarMultiplo() throws DataException {

        Dialog.create()
            .title("Alteração Multipla")
            .message("ATENÇÃO: Serão alterado(s) "+gridPrincipal.getSelectionCount()+" registro(s). Confirma?")
            .confirmSimNao((String dialogResult) -> {
                if (CastUtil.asInteger(dialogResult) == IDialog.YES) {

                     try {
                           tbDescartes.disableControls();
                           int lastBookmark = tbDescartes.getBookmark();
                           try {
                               for (int bm : gridPrincipal.getSelectedIndices(true)) {
                                   tbDescartes.gotoBookmark(bm);
                                   tbDescartes.edit();
                                   
                                   if ( ! edDescricaoDescarte34001.getValue().isNull()) {
                                       tbDescartes.setDESCRICAO_DESCARTE(edDescricaoDescarte34001.getValue());
                                   }
                                   if ( ! edDepartamento34001.getValue().isNull()) {
                                       tbDescartes.setDEPARTAMENTO(edDepartamento34001.getValue());
                                   }
                                   if ( ! edEliminarCicloFuturo34001.getValue().isNull()) {
                                       tbDescartes.setELIMINAR_CICLO_FUTURO(edEliminarCicloFuturo34001.getValue());
                                   }
                                   if ( ! edAtivo34001.getValue().isNull()) {
                                       tbDescartes.setATIVO(edAtivo34001.getValue());
                                   }
                                   if ( ! edExclusivoDescarteAuto34001.getValue().isNull()) {
                                       tbDescartes.setEXCLUSIVO_DESCARTE_AUTO(edExclusivoDescarteAuto34001.getValue());
                                   }

                                   tbDescartes.post();
                               }

                               onSalvar();

                           } finally {
                               tbDescartes.close();
                               tbDescartes.open();
                               // tbDescartes.gotoBookmark(lastBookmark);
                               tbDescartes.enableControls();
                               gridPrincipal.clearSelection();
                           }

                     } catch (DataException e) {
                         Dialog.create()
                               .title("Erro ao salvar")
                               .message(e.getMessage())
                               .showException(e);
                    }
                }
        });
    }

    protected void onSalvar() throws DataException {
        // executa a validação das constraint dos objetos edition
//        check();
//        if (!getErrorMap().isEmpty()) {
//            StringBuilder strBuilder = new StringBuilder();
//
//            getErrorMap().values().stream().forEach((s) -> {
//                strBuilder.append(s).append("\n");
//            });        
//
//            // manda o focu para o primeiro objeto que deu erro de constraint
//            ((IFocusable)getErrorMap().keySet().iterator().next()).setFocus();
//
//            Dialog.create()
//                  .title("Erro ao validar")
//                  .message("Existe validação(s) pendente...\n" + strBuilder.toString())
//                  .showError();
//
//            return;
//        }

        setCalcUpdate();
        rn.salvar();

        // atualiza o registro
        tbDescartes.refreshRecord();
        
        habilitaComp(false);
        lblMensagem.setCaption("");
    }

    protected void onSalvarContinuar() throws DataException {
        try {
            TableState st = tbDescartes.getState();
            onSalvar();
            if (st == TableState.INSERTING) {
                onIncluir();
            } else if (st == TableState.MODIFYING) {
                onAlterar();
            }
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao salvar e continuar a edição")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    protected void onCancelar() throws DataException {
        habilitaComp(false);
        rn.cancelar();
        
        lblMensagem.setCaption("Registro Selecionado: "+tbDescartes.getDESCRICAO_DESCARTE().asString());
    }
    
    protected void onAceitar() throws Exception {
        if (FormUtil.isExternalCall()) {
            // passa os parametros para a resposta ao VB
            FormUtil.externalCall(tbDescartes.getField("ID_PESSOA").asString());
        } else {
            close();
        }
    }
    
    protected void onAbreTabelaAux() {
        try {
            rn.abreTabelaAux();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao Abrir Tabelas Auxiliares")
                  .message(e.getMessage())
                  .showException(e);
        }
    }
    
    protected void setCalcUpdate() throws DataException {
        
        postTable();
    }

    private void postTable() throws DataException {
        tbDescartes.post();
    }

    public void loadFormPk(Integer codDescarte ) throws DataException {
        tbDescartes.close(); 
        tbDescartes.clearFilters();
        
        if (codDescarte > 0) {
            tbDescartes.addFilter("COD_DESCARTE");
            tbDescartes.addParam("COD_DESCARTE", codDescarte);
        } else return;
        
        tbDescartes.open();
        habilitaComp(false);          // se tem registro habilita botões da barra de ferramenta  
    }         

    // retorna true se o master esta sendo editado, pode ser usado para verificar se o form esta 
    // habilitado edição
    public boolean masterIsEditing() {
       return rn.getOperRN() != TableState.QUERYING;
    }
	
	private void isEditing() throws DataException {
        if (rn.getOperRN() == TableState.QUERYING) {
           throw new DataException("O Cadastro Não Esta em Modo de Edição...");
        }	
	}
    
    
    @Override
    public void tbDescartesAfterScroll(final Event<Object> event) {
        lblMensagem.setCaption("Selecionado: "+tbDescartes.getDESCRICAO_DESCARTE().asString());

    }

        
    
    @Override
    public void efDepartamentoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
    @Override
    public void efAtivoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
    @Override
    public void efExclusivoDescarteAutoEnter(final Event<Object> event) {
        try {
            onConsultar();
        } catch(Exception e) {
            Dialog.create()
                     .title("Erro ao processar")
                     .message(e.getMessage())
                     .showException(e);
        }
    }
        
            
    @Override
    public void gridPrincipalClickImageDelete(Event<Object> event) {
        try {
            onExcluir();
        } catch (DataException e) {
            Dialog.create()
                  .title("Erro ao excluir")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void gridPrincipalClickImageAlterar(Event<Object> event) {
        try {
            onAlterar();
        } catch (Exception e) {
            Dialog.create()
                  .title("Erro ao alterar")
                  .message(e.getMessage())
                  .showException(e);
        }
    }

    @Override
    public void menuItemExportPdfClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportPdf(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }

    @Override
    public void menuItemExportExcelClick(Event<Object> event) {
        TFGridExporter ge = new TFGridExporter();
        try {
            ge.exportExcel(gridPrincipal);
        } catch (Exception e) {
            Dialog.create()
                    .title("Erro ao aceitar")
                    .message(e.getMessage())
                    .showException(e);
        }
    }




}

