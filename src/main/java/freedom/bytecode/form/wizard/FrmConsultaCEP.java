package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmConsultaCEP extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ConsultaCEPRNA rn = null;

    public FrmConsultaCEP() {
        try {
            rn = (freedom.bytecode.rn.ConsultaCEPRNA) getRN(freedom.bytecode.rn.wizard.ConsultaCEPRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbConsultaCep();
        init_tbUf();
        init_tbCidades();
        init_vBoxTela();
        init_FHBox11();
        init_btnVoltar();
        init_btnPesquisar();
        init_btnAceitar();
        init_FVBox1();
        init_FGridPanel1();
        init_comboUf();
        init_edtPesquisa();
        init_comboCidade();
        init_FHBox3();
        init_ckLogrQualquerPosicao();
        init_FLabel1();
        init_gpLogradouro();
        init_hBoxTipoCopia();
        init_ckCopiarResidencial();
        init_ckCopiarComercial();
        init_ckCopiarCobranca();
        init_FrmConsultaCEP();
    }

    public CONSULTA_CEP tbConsultaCep;

    private void init_tbConsultaCep() {
        tbConsultaCep = rn.tbConsultaCep;
        tbConsultaCep.setName("tbConsultaCep");
        tbConsultaCep.setMaxRowCount(0);
        tbConsultaCep.setWKey("310073;31001");
        tbConsultaCep.setRatioBatchSize(20);
        getTables().put(tbConsultaCep, "tbConsultaCep");
        tbConsultaCep.applyProperties();
    }

    public UF tbUf;

    private void init_tbUf() {
        tbUf = rn.tbUf;
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(0);
        tbUf.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbUfAfterScroll(event);
            processarFlow("FrmConsultaCEP", "tbUf", "OnAfterScroll");
        });
        tbUf.setWKey("310073;31002");
        tbUf.setRatioBatchSize(20);
        getTables().put(tbUf, "tbUf");
        tbUf.applyProperties();
    }

    public CIDADES tbCidades;

    private void init_tbCidades() {
        tbCidades = rn.tbCidades;
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(0);
        tbCidades.setWKey("310073;31003");
        tbCidades.setRatioBatchSize(20);
        getTables().put(tbCidades, "tbCidades");
        tbCidades.applyProperties();
    }

    protected TFForm FrmConsultaCEP = this;
    private void init_FrmConsultaCEP() {
        FrmConsultaCEP.setName("FrmConsultaCEP");
        FrmConsultaCEP.setCaption("Consulta CEP");
        FrmConsultaCEP.setClientHeight(454);
        FrmConsultaCEP.setClientWidth(554);
        FrmConsultaCEP.setColor("clBtnFace");
        FrmConsultaCEP.setWKey("310073");
        FrmConsultaCEP.setSpacing(0);
        FrmConsultaCEP.applyProperties();
    }

    public TFVBox vBoxTela = new TFVBox();

    private void init_vBoxTela() {
        vBoxTela.setName("vBoxTela");
        vBoxTela.setLeft(0);
        vBoxTela.setTop(0);
        vBoxTela.setWidth(554);
        vBoxTela.setHeight(454);
        vBoxTela.setAlign("alClient");
        vBoxTela.setBorderStyle("stNone");
        vBoxTela.setPaddingTop(3);
        vBoxTela.setPaddingLeft(5);
        vBoxTela.setPaddingRight(5);
        vBoxTela.setPaddingBottom(5);
        vBoxTela.setMarginTop(0);
        vBoxTela.setMarginLeft(0);
        vBoxTela.setMarginRight(0);
        vBoxTela.setMarginBottom(0);
        vBoxTela.setSpacing(1);
        vBoxTela.setFlexVflex("ftTrue");
        vBoxTela.setFlexHflex("ftTrue");
        vBoxTela.setScrollable(false);
        vBoxTela.setBoxShadowConfigHorizontalLength(10);
        vBoxTela.setBoxShadowConfigVerticalLength(10);
        vBoxTela.setBoxShadowConfigBlurRadius(5);
        vBoxTela.setBoxShadowConfigSpreadRadius(0);
        vBoxTela.setBoxShadowConfigShadowColor("clBlack");
        vBoxTela.setBoxShadowConfigOpacity(75);
        FrmConsultaCEP.addChildren(vBoxTela);
        vBoxTela.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(653);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxTela.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmConsultaCEP", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(60);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(60);
        btnPesquisar.setHeight(56);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setAlign("alLeft");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-13);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmConsultaCEP", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000002224944415478DAAD954D481561148667840C347F1229DD0822680B4B"
 + "282368D12A90144522027321B8B44D8128B6085CA508091A486D021191D4853F"
 + "F8B30B42022D085D184482D4E6128982284A747B4E9DF0F03933772EDC0F5E5E"
 + "BE33EF39EF9CEF67C6F72286EFFBE7A13250020E40026C2593C95F5ECCE10714"
 + "95D86D30022A42F2C640274689B40CA85D08BD079762BE600FE8C72899D280E2"
 + "B20C5F414EDCF6758C53BF35D280E267A16D70D13C5B07ED608302C7BA74D2E1"
 + "033004B28CB60BCD4094813CEC34F17E693FAC75F4C5FA02A5267C01F98F00AD"
 + "5F00EFC66DD93149984E5E93D71E64700F9E34B13C84FB71169FDC87D00B133A"
 + "E31E61319886EFEA7C0E41539CE29A2CF764C7842AC9FFE21A6CC1E53AEF4030"
 + "928681ECE16F13BA45FE3B57B307E7EBBC0DC1685C032D600F4203F90BAEC10A"
 + "7C53E7CF103C49A3783674644237C85F750D9EC2BD3A97F52C8EBA994E722DB4"
 + "664292FBD33590CFC2A689D5235A8CB9FE9FC0150D7D23AF2C4827C2CFA05263"
 + "72CCCA117F4F61D007759B50333933A7742ABEECFDBB999E3191E3BAE42E17DA"
 + "73D02BD062C272518B8296D67EEC1E43CF9DE7B2272FB5C35C7007348634D542"
 + "FD8950033579040DA65AFF8871CA24E88723CB35E59DEC49D81806F36039CAC4"
 + "0FCAD48DAF02F7411DA80687E00378036629B2ABDAABD0C730934083740726D7"
 + "D4DC8EBFA72A23066AE25E3A195919335093EBD0FF4FC53A1DD464D4404DE4B7"
 + "5B03DECAAFF60FF9CFC91D70A0B6C30000000049454E44AE426082");
        btnPesquisar.setImageId(27001);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        FHBox11.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(120);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-13);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmConsultaCEP", "btnAceitar", "OnClick");
        });
        btnAceitar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515"
 + "0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3"
 + "1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7"
 + "95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC"
 + "101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D"
 + "1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537"
 + "A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC"
 + "C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB"
 + "482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB"
 + "1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D"
 + "C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F"
 + "8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082");
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox11.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(62);
        FVBox1.setWidth(650);
        FVBox1.setHeight(85);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        vBoxTela.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGridPanel FGridPanel1 = new TFGridPanel();

    private void init_FGridPanel1() {
        FGridPanel1.setName("FGridPanel1");
        FGridPanel1.setLeft(0);
        FGridPanel1.setTop(0);
        FGridPanel1.setWidth(641);
        FGridPanel1.setHeight(79);
        TFGridPanelColumn item0 = new TFGridPanelColumn();
        item0.setSizeStyle("ssAbsolute");
        item0.setValue(100.000000000000000000);
        FGridPanel1.getColumnCollection().add(item0);
        TFGridPanelColumn item1 = new TFGridPanelColumn();
        item1.setValue(50.001017385273530000);
        FGridPanel1.getColumnCollection().add(item1);
        TFGridPanelColumn item2 = new TFGridPanelColumn();
        item2.setValue(49.998982614726470000);
        FGridPanel1.getColumnCollection().add(item2);
        TFControlItem item3 = new TFControlItem();
        item3.setColumn(0);
        item3.setControl("comboUf");
        item3.setRow(0);
        FGridPanel1.getControlCollection().add(item3);
        TFControlItem item4 = new TFControlItem();
        item4.setColumn(1);
        item4.setControl("comboCidade");
        item4.setRow(0);
        FGridPanel1.getControlCollection().add(item4);
        TFControlItem item5 = new TFControlItem();
        item5.setColumn(0);
        item5.setColumnSpan(2);
        item5.setControl("edtPesquisa");
        item5.setRow(1);
        FGridPanel1.getControlCollection().add(item5);
        TFControlItem item6 = new TFControlItem();
        item6.setColumn(2);
        item6.setControl("FHBox3");
        item6.setRow(1);
        FGridPanel1.getControlCollection().add(item6);
        TFControlItem item7 = new TFControlItem();
        item7.setColumn(2);
        item7.setControl("FLabel1");
        item7.setRow(0);
        FGridPanel1.getControlCollection().add(item7);
        TFGridPanelRow item8 = new TFGridPanelRow();
        item8.setSizeStyle("ssAbsolute");
        item8.setValue(38.000000000000000000);
        FGridPanel1.getRowCollection().add(item8);
        TFGridPanelRow item9 = new TFGridPanelRow();
        item9.setSizeStyle("ssAbsolute");
        item9.setValue(38.000000000000000000);
        FGridPanel1.getRowCollection().add(item9);
        FGridPanel1.setFlexVflex("ftFalse");
        FGridPanel1.setFlexHflex("ftTrue");
        FGridPanel1.setAllRowFlex(false);
        FGridPanel1.setColumnTabOrder(false);
        FVBox1.addChildren(FGridPanel1);
        FGridPanel1.applyProperties();
    }

    public TFCombo comboUf = new TFCombo();

    private void init_comboUf() {
        comboUf.setName("comboUf");
        comboUf.setLeft(1);
        comboUf.setTop(1);
        comboUf.setWidth(100);
        comboUf.setHeight(21);
        comboUf.setLookupTable(tbUf);
        comboUf.setLookupKey("UF");
        comboUf.setLookupDesc("UF");
        comboUf.setFlex(false);
        comboUf.setReadOnly(true);
        comboUf.setRequired(true);
        comboUf.setPrompt("UF");
        comboUf.setConstraintCheckWhen("cwImmediate");
        comboUf.setConstraintCheckType("ctExpression");
        comboUf.setConstraintFocusOnError(false);
        comboUf.setConstraintEnableUI(true);
        comboUf.setConstraintEnabled(false);
        comboUf.setConstraintFormCheck(true);
        comboUf.setClearOnDelKey(true);
        comboUf.setUseClearButton(false);
        comboUf.setHideClearButtonOnNullValue(false);
        FGridPanel1.addChildren(comboUf);
        comboUf.applyProperties();
        addValidatable(comboUf);
    }

    public TFString edtPesquisa = new TFString();

    private void init_edtPesquisa() {
        edtPesquisa.setName("edtPesquisa");
        edtPesquisa.setLeft(1);
        edtPesquisa.setTop(39);
        edtPesquisa.setWidth(369);
        edtPesquisa.setHeight(24);
        edtPesquisa.setFlex(true);
        edtPesquisa.setRequired(false);
        edtPesquisa.setPrompt("Digite CEP ou logradouro");
        edtPesquisa.setConstraintCheckWhen("cwImmediate");
        edtPesquisa.setConstraintCheckType("ctExpression");
        edtPesquisa.setConstraintFocusOnError(false);
        edtPesquisa.setConstraintEnableUI(true);
        edtPesquisa.setConstraintEnabled(false);
        edtPesquisa.setConstraintFormCheck(true);
        edtPesquisa.setCharCase("ccNormal");
        edtPesquisa.setPwd(false);
        edtPesquisa.setMaxlength(0);
        edtPesquisa.setFontColor("clWindowText");
        edtPesquisa.setFontSize(-13);
        edtPesquisa.setFontName("Tahoma");
        edtPesquisa.setFontStyle("[]");
        edtPesquisa.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtPesquisaEnter(event);
            processarFlow("FrmConsultaCEP", "edtPesquisa", "OnEnter");
        });
        edtPesquisa.setSaveLiteralCharacter(false);
        edtPesquisa.applyProperties();
        FGridPanel1.addChildren(edtPesquisa);
        addValidatable(edtPesquisa);
    }

    public TFCombo comboCidade = new TFCombo();

    private void init_comboCidade() {
        comboCidade.setName("comboCidade");
        comboCidade.setLeft(101);
        comboCidade.setTop(1);
        comboCidade.setWidth(329);
        comboCidade.setHeight(21);
        comboCidade.setLookupTable(tbCidades);
        comboCidade.setLookupKey("COD_CIDADES");
        comboCidade.setLookupDesc("DESCRICAO");
        comboCidade.setFlex(true);
        comboCidade.setReadOnly(true);
        comboCidade.setRequired(false);
        comboCidade.setPrompt("Cidade");
        comboCidade.setConstraintCheckWhen("cwImmediate");
        comboCidade.setConstraintCheckType("ctExpression");
        comboCidade.setConstraintFocusOnError(false);
        comboCidade.setConstraintEnableUI(true);
        comboCidade.setConstraintEnabled(false);
        comboCidade.setConstraintFormCheck(true);
        comboCidade.setClearOnDelKey(true);
        comboCidade.setUseClearButton(true);
        comboCidade.setHideClearButtonOnNullValue(true);
        comboCidade.setAlign("alLeft");
        FGridPanel1.addChildren(comboCidade);
        comboCidade.applyProperties();
        addValidatable(comboCidade);
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(402);
        FHBox3.setTop(39);
        FHBox3.setWidth(205);
        FHBox3.setHeight(38);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(5);
        FHBox3.setPaddingLeft(5);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FGridPanel1.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFCheckBox ckLogrQualquerPosicao = new TFCheckBox();

    private void init_ckLogrQualquerPosicao() {
        ckLogrQualquerPosicao.setName("ckLogrQualquerPosicao");
        ckLogrQualquerPosicao.setLeft(0);
        ckLogrQualquerPosicao.setTop(0);
        ckLogrQualquerPosicao.setWidth(189);
        ckLogrQualquerPosicao.setHeight(17);
        ckLogrQualquerPosicao.setCaption("CEP escreve qualquer posi\u00E7\u00E3o");
        ckLogrQualquerPosicao.setFontColor("clWindowText");
        ckLogrQualquerPosicao.setFontSize(-13);
        ckLogrQualquerPosicao.setFontName("Tahoma");
        ckLogrQualquerPosicao.setFontStyle("[]");
        ckLogrQualquerPosicao.setVerticalAlignment("taAlignTop");
        FHBox3.addChildren(ckLogrQualquerPosicao);
        ckLogrQualquerPosicao.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(502);
        FLabel1.setTop(13);
        FLabel1.setWidth(5);
        FLabel1.setHeight(13);
        FLabel1.setCaption("?");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVisible(false);
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FGridPanel1.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFGrid gpLogradouro = new TFGrid();

    private void init_gpLogradouro() {
        gpLogradouro.setName("gpLogradouro");
        gpLogradouro.setLeft(0);
        gpLogradouro.setTop(148);
        gpLogradouro.setWidth(644);
        gpLogradouro.setHeight(240);
        gpLogradouro.setAlign("alClient");
        gpLogradouro.setTable(tbConsultaCep);
        gpLogradouro.setFlexVflex("ftTrue");
        gpLogradouro.setFlexHflex("ftTrue");
        gpLogradouro.setPagingEnabled(false);
        gpLogradouro.setFrozenColumns(0);
        gpLogradouro.setShowFooter(false);
        gpLogradouro.setShowHeader(true);
        gpLogradouro.setMultiSelection(false);
        gpLogradouro.setGroupingEnabled(false);
        gpLogradouro.setGroupingExpanded(false);
        gpLogradouro.setGroupingShowFooter(false);
        gpLogradouro.setCrosstabEnabled(false);
        gpLogradouro.setCrosstabGroupType("cgtConcat");
        gpLogradouro.setEditionEnabled(false);
        gpLogradouro.setNoBorder(false);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("CEP");
        item10.setTitleCaption("Cep");
        item10.setWidth(110);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        gpLogradouro.getColumns().add(item10);
        TFGridColumn item11 = new TFGridColumn();
        item11.setFieldName("RUA");
        item11.setTitleCaption("Rua");
        item11.setWidth(221);
        item11.setVisible(true);
        item11.setPrecision(0);
        item11.setTextAlign("taLeft");
        item11.setFieldType("ftString");
        item11.setFlexRatio(0);
        item11.setSort(false);
        item11.setImageHeader(0);
        item11.setWrap(false);
        item11.setFlex(true);
        item11.setCharCase("ccNormal");
        item11.setBlobConfigMimeType("bmtText");
        item11.setBlobConfigShowType("btImageViewer");
        item11.setShowLabel(true);
        item11.setEditorEditType("etTFString");
        item11.setEditorPrecision(0);
        item11.setEditorMaxLength(100);
        item11.setEditorLookupFilterKey(0);
        item11.setEditorLookupFilterDesc(0);
        item11.setEditorPopupHeight(400);
        item11.setEditorPopupWidth(400);
        item11.setEditorCharCase("ccNormal");
        item11.setEditorEnabled(false);
        item11.setEditorReadOnly(false);
        item11.setHiperLink(false);
        item11.setEditorConstraintCheckWhen("cwImmediate");
        item11.setEditorConstraintCheckType("ctExpression");
        item11.setEditorConstraintFocusOnError(false);
        item11.setEditorConstraintEnableUI(true);
        item11.setEditorConstraintEnabled(false);
        item11.setEmpty(false);
        item11.setMobileOptsShowMobile(false);
        item11.setMobileOptsOrder(0);
        item11.setBoxSize(0);
        item11.setImageSrcType("istSource");
        gpLogradouro.getColumns().add(item11);
        TFGridColumn item12 = new TFGridColumn();
        item12.setFieldName("BAIRRO");
        item12.setTitleCaption("Bairro");
        item12.setWidth(240);
        item12.setVisible(true);
        item12.setPrecision(0);
        item12.setTextAlign("taLeft");
        item12.setFieldType("ftString");
        item12.setFlexRatio(0);
        item12.setSort(false);
        item12.setImageHeader(0);
        item12.setWrap(false);
        item12.setFlex(false);
        item12.setCharCase("ccNormal");
        item12.setBlobConfigMimeType("bmtText");
        item12.setBlobConfigShowType("btImageViewer");
        item12.setShowLabel(true);
        item12.setEditorEditType("etTFString");
        item12.setEditorPrecision(0);
        item12.setEditorMaxLength(100);
        item12.setEditorLookupFilterKey(0);
        item12.setEditorLookupFilterDesc(0);
        item12.setEditorPopupHeight(400);
        item12.setEditorPopupWidth(400);
        item12.setEditorCharCase("ccNormal");
        item12.setEditorEnabled(false);
        item12.setEditorReadOnly(false);
        item12.setHiperLink(false);
        item12.setEditorConstraintCheckWhen("cwImmediate");
        item12.setEditorConstraintCheckType("ctExpression");
        item12.setEditorConstraintFocusOnError(false);
        item12.setEditorConstraintEnableUI(true);
        item12.setEditorConstraintEnabled(false);
        item12.setEmpty(false);
        item12.setMobileOptsShowMobile(false);
        item12.setMobileOptsOrder(0);
        item12.setBoxSize(0);
        item12.setImageSrcType("istSource");
        gpLogradouro.getColumns().add(item12);
        vBoxTela.addChildren(gpLogradouro);
        gpLogradouro.applyProperties();
    }

    public TFHBox hBoxTipoCopia = new TFHBox();

    private void init_hBoxTipoCopia() {
        hBoxTipoCopia.setName("hBoxTipoCopia");
        hBoxTipoCopia.setLeft(0);
        hBoxTipoCopia.setTop(389);
        hBoxTipoCopia.setWidth(648);
        hBoxTipoCopia.setHeight(41);
        hBoxTipoCopia.setBorderStyle("stNone");
        hBoxTipoCopia.setPaddingTop(5);
        hBoxTipoCopia.setPaddingLeft(5);
        hBoxTipoCopia.setPaddingRight(0);
        hBoxTipoCopia.setPaddingBottom(0);
        hBoxTipoCopia.setVisible(false);
        hBoxTipoCopia.setMarginTop(0);
        hBoxTipoCopia.setMarginLeft(0);
        hBoxTipoCopia.setMarginRight(0);
        hBoxTipoCopia.setMarginBottom(0);
        hBoxTipoCopia.setSpacing(20);
        hBoxTipoCopia.setFlexVflex("ftFalse");
        hBoxTipoCopia.setFlexHflex("ftFalse");
        hBoxTipoCopia.setScrollable(false);
        hBoxTipoCopia.setBoxShadowConfigHorizontalLength(10);
        hBoxTipoCopia.setBoxShadowConfigVerticalLength(10);
        hBoxTipoCopia.setBoxShadowConfigBlurRadius(5);
        hBoxTipoCopia.setBoxShadowConfigSpreadRadius(0);
        hBoxTipoCopia.setBoxShadowConfigShadowColor("clBlack");
        hBoxTipoCopia.setBoxShadowConfigOpacity(75);
        hBoxTipoCopia.setVAlign("tvTop");
        vBoxTela.addChildren(hBoxTipoCopia);
        hBoxTipoCopia.applyProperties();
    }

    public TFCheckBox ckCopiarResidencial = new TFCheckBox();

    private void init_ckCopiarResidencial() {
        ckCopiarResidencial.setName("ckCopiarResidencial");
        ckCopiarResidencial.setLeft(0);
        ckCopiarResidencial.setTop(0);
        ckCopiarResidencial.setWidth(205);
        ckCopiarResidencial.setHeight(17);
        ckCopiarResidencial.setCaption("Copiar Para End. Residencial");
        ckCopiarResidencial.setFontColor("clWindowText");
        ckCopiarResidencial.setFontSize(-13);
        ckCopiarResidencial.setFontName("Tahoma");
        ckCopiarResidencial.setFontStyle("[]");
        ckCopiarResidencial.setVerticalAlignment("taAlignTop");
        hBoxTipoCopia.addChildren(ckCopiarResidencial);
        ckCopiarResidencial.applyProperties();
    }

    public TFCheckBox ckCopiarComercial = new TFCheckBox();

    private void init_ckCopiarComercial() {
        ckCopiarComercial.setName("ckCopiarComercial");
        ckCopiarComercial.setLeft(205);
        ckCopiarComercial.setTop(0);
        ckCopiarComercial.setWidth(185);
        ckCopiarComercial.setHeight(17);
        ckCopiarComercial.setCaption("Copiar Para End. Comercial");
        ckCopiarComercial.setFontColor("clWindowText");
        ckCopiarComercial.setFontSize(-13);
        ckCopiarComercial.setFontName("Tahoma");
        ckCopiarComercial.setFontStyle("[]");
        ckCopiarComercial.setVerticalAlignment("taAlignTop");
        hBoxTipoCopia.addChildren(ckCopiarComercial);
        ckCopiarComercial.applyProperties();
    }

    public TFCheckBox ckCopiarCobranca = new TFCheckBox();

    private void init_ckCopiarCobranca() {
        ckCopiarCobranca.setName("ckCopiarCobranca");
        ckCopiarCobranca.setLeft(390);
        ckCopiarCobranca.setTop(0);
        ckCopiarCobranca.setWidth(193);
        ckCopiarCobranca.setHeight(17);
        ckCopiarCobranca.setCaption("Copiar Para End. Cobran\u00E7a");
        ckCopiarCobranca.setFontColor("clWindowText");
        ckCopiarCobranca.setFontSize(-13);
        ckCopiarCobranca.setFontName("Tahoma");
        ckCopiarCobranca.setFontStyle("[]");
        ckCopiarCobranca.setVerticalAlignment("taAlignTop");
        hBoxTipoCopia.addChildren(ckCopiarCobranca);
        ckCopiarCobranca.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtPesquisaEnter(final Event<Object> event);

    public abstract void tbUfAfterScroll(final Event<Object> event);

}