package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmSnapShotCamera extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.SnapShotCameraRNA rn = null;

    public FrmSnapShotCamera() {
        try {
            rn = (freedom.bytecode.rn.SnapShotCameraRNA) getRN(freedom.bytecode.rn.wizard.SnapShotCameraRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FVBox1();
        init_camera();
        init_imagePreview();
        init_hBoxOpcoes();
        init_FVBox2();
        init_iconOk();
        init_vBoxCapturarFoto();
        init_buttonCapturarFoto();
        init_FHBox1();
        init_vBoxCancel();
        init_iconCancel();
        init_FVBox4();
        init_hBoxClose();
        init_btnClose();
        init_hboxEsp1();
        init_vboxFlash();
        init_iconClassFlash();
        init_FHBox2();
        init_vboxZoom();
        init_iconClassZoom();
        init_FVBox3();
        init_FrmSnapShotCamera();
    }

    protected TFForm FrmSnapShotCamera = this;
    private void init_FrmSnapShotCamera() {
        FrmSnapShotCamera.setName("FrmSnapShotCamera");
        FrmSnapShotCamera.setCaption("Foto");
        FrmSnapShotCamera.setClientHeight(874);
        FrmSnapShotCamera.setClientWidth(584);
        FrmSnapShotCamera.setColor("clBtnFace");
        FrmSnapShotCamera.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmSnapShotCamera", "FrmSnapShotCamera", "OnCreate");
        });
        FrmSnapShotCamera.setWOrigem("EhMain");
        FrmSnapShotCamera.setWKey("430053");
        FrmSnapShotCamera.setSpacing(0);
        FrmSnapShotCamera.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(584);
        FVBox1.setHeight(874);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmSnapShotCamera.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFCamera camera = new TFCamera();

    private void init_camera() {
        camera.setName("camera");
        camera.setLeft(0);
        camera.setTop(0);
        camera.setWidth(644);
        camera.setHeight(787);
        camera.setMaxSize(-1);
        camera.setAudio(false);
        camera.setPreviewRecord(true);
        camera.setLengthLimit(120);
        camera.setSnapshotFormat("image/jpeg");
        camera.setRecordFormat("video/mp4");
        camera.addEventListener("onSnapshotUpload", (EventListener<UploadEvent>) (UploadEvent event) -> {
            cameraSnapshotUpload(event);
            processarFlow("FrmSnapShotCamera", "camera", "OnSnapshotUpload");
        });
        camera.setFlexVflex("ftTrue");
        camera.setFlexHflex("ftTrue");
        FVBox1.addChildren(camera);
        camera.applyProperties();
    }

    public TFImage imagePreview = new TFImage();

    private void init_imagePreview() {
        imagePreview.setName("imagePreview");
        imagePreview.setLeft(0);
        imagePreview.setTop(788);
        imagePreview.setWidth(24);
        imagePreview.setHeight(20);
        imagePreview.setVisible(false);
        imagePreview.setBoxSize(0);
        imagePreview.setGrayScaleOnDisable(false);
        imagePreview.setFlexVflex("ftTrue");
        imagePreview.setFlexHflex("ftTrue");
        imagePreview.setImageId(0);
        FVBox1.addChildren(imagePreview);
        imagePreview.applyProperties();
    }

    public TFHBox hBoxOpcoes = new TFHBox();

    private void init_hBoxOpcoes() {
        hBoxOpcoes.setName("hBoxOpcoes");
        hBoxOpcoes.setLeft(0);
        hBoxOpcoes.setTop(809);
        hBoxOpcoes.setWidth(646);
        hBoxOpcoes.setHeight(64);
        hBoxOpcoes.setBorderStyle("stNone");
        hBoxOpcoes.setPaddingTop(5);
        hBoxOpcoes.setPaddingLeft(5);
        hBoxOpcoes.setPaddingRight(5);
        hBoxOpcoes.setPaddingBottom(0);
        hBoxOpcoes.setMarginTop(0);
        hBoxOpcoes.setMarginLeft(0);
        hBoxOpcoes.setMarginRight(0);
        hBoxOpcoes.setMarginBottom(0);
        hBoxOpcoes.setSpacing(3);
        hBoxOpcoes.setFlexVflex("ftFalse");
        hBoxOpcoes.setFlexHflex("ftTrue");
        hBoxOpcoes.setScrollable(false);
        hBoxOpcoes.setBoxShadowConfigHorizontalLength(10);
        hBoxOpcoes.setBoxShadowConfigVerticalLength(10);
        hBoxOpcoes.setBoxShadowConfigBlurRadius(5);
        hBoxOpcoes.setBoxShadowConfigSpreadRadius(0);
        hBoxOpcoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxOpcoes.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.setVAlign("tvTop");
        FVBox1.addChildren(hBoxOpcoes);
        hBoxOpcoes.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(85);
        FVBox2.setHeight(32);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftTrue");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFIconClass iconOk = new TFIconClass();

    private void init_iconOk() {
        iconOk.setName("iconOk");
        iconOk.setLeft(85);
        iconOk.setTop(0);
        iconOk.setVisible(false);
        iconOk.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconOkClick(event);
            processarFlow("FrmSnapShotCamera", "iconOk", "OnClick");
        });
        iconOk.setIconClass("check");
        iconOk.setSize(44);
        iconOk.setColor("clGreen");
        hBoxOpcoes.addChildren(iconOk);
        iconOk.applyProperties();
    }

    public TFVBox vBoxCapturarFoto = new TFVBox();

    private void init_vBoxCapturarFoto() {
        vBoxCapturarFoto.setName("vBoxCapturarFoto");
        vBoxCapturarFoto.setLeft(101);
        vBoxCapturarFoto.setTop(0);
        vBoxCapturarFoto.setWidth(55);
        vBoxCapturarFoto.setHeight(55);
        vBoxCapturarFoto.setBorderStyle("stNone");
        vBoxCapturarFoto.setPaddingTop(0);
        vBoxCapturarFoto.setPaddingLeft(0);
        vBoxCapturarFoto.setPaddingRight(0);
        vBoxCapturarFoto.setPaddingBottom(0);
        vBoxCapturarFoto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxCapturarFotoClick(event);
            processarFlow("FrmSnapShotCamera", "vBoxCapturarFoto", "OnClick");
        });
        vBoxCapturarFoto.setMarginTop(0);
        vBoxCapturarFoto.setMarginLeft(0);
        vBoxCapturarFoto.setMarginRight(0);
        vBoxCapturarFoto.setMarginBottom(0);
        vBoxCapturarFoto.setSpacing(1);
        vBoxCapturarFoto.setFlexVflex("ftFalse");
        vBoxCapturarFoto.setFlexHflex("ftFalse");
        vBoxCapturarFoto.setScrollable(false);
        vBoxCapturarFoto.setBoxShadowConfigHorizontalLength(10);
        vBoxCapturarFoto.setBoxShadowConfigVerticalLength(10);
        vBoxCapturarFoto.setBoxShadowConfigBlurRadius(5);
        vBoxCapturarFoto.setBoxShadowConfigSpreadRadius(0);
        vBoxCapturarFoto.setBoxShadowConfigShadowColor("clBlack");
        vBoxCapturarFoto.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(vBoxCapturarFoto);
        vBoxCapturarFoto.applyProperties();
    }

    public TFIconClass buttonCapturarFoto = new TFIconClass();

    private void init_buttonCapturarFoto() {
        buttonCapturarFoto.setName("buttonCapturarFoto");
        buttonCapturarFoto.setLeft(0);
        buttonCapturarFoto.setTop(0);
        buttonCapturarFoto.setIconClass("camera");
        buttonCapturarFoto.setSize(44);
        buttonCapturarFoto.setColor("clBlack");
        vBoxCapturarFoto.addChildren(buttonCapturarFoto);
        buttonCapturarFoto.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(156);
        FHBox1.setTop(0);
        FHBox1.setWidth(20);
        FHBox1.setHeight(54);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        hBoxOpcoes.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox vBoxCancel = new TFVBox();

    private void init_vBoxCancel() {
        vBoxCancel.setName("vBoxCancel");
        vBoxCancel.setLeft(176);
        vBoxCancel.setTop(0);
        vBoxCancel.setWidth(55);
        vBoxCancel.setHeight(55);
        vBoxCancel.setBorderStyle("stNone");
        vBoxCancel.setPaddingTop(0);
        vBoxCancel.setPaddingLeft(0);
        vBoxCancel.setPaddingRight(0);
        vBoxCancel.setPaddingBottom(0);
        vBoxCancel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vBoxCancelClick(event);
            processarFlow("FrmSnapShotCamera", "vBoxCancel", "OnClick");
        });
        vBoxCancel.setMarginTop(0);
        vBoxCancel.setMarginLeft(0);
        vBoxCancel.setMarginRight(0);
        vBoxCancel.setMarginBottom(0);
        vBoxCancel.setSpacing(1);
        vBoxCancel.setFlexVflex("ftFalse");
        vBoxCancel.setFlexHflex("ftFalse");
        vBoxCancel.setScrollable(false);
        vBoxCancel.setBoxShadowConfigHorizontalLength(10);
        vBoxCancel.setBoxShadowConfigVerticalLength(10);
        vBoxCancel.setBoxShadowConfigBlurRadius(5);
        vBoxCancel.setBoxShadowConfigSpreadRadius(0);
        vBoxCancel.setBoxShadowConfigShadowColor("clBlack");
        vBoxCancel.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(vBoxCancel);
        vBoxCancel.applyProperties();
    }

    public TFIconClass iconCancel = new TFIconClass();

    private void init_iconCancel() {
        iconCancel.setName("iconCancel");
        iconCancel.setLeft(0);
        iconCancel.setTop(0);
        iconCancel.setVisible(false);
        iconCancel.setIconClass("ban");
        iconCancel.setSize(44);
        iconCancel.setColor("clRed");
        vBoxCancel.addChildren(iconCancel);
        iconCancel.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(231);
        FVBox4.setTop(0);
        FVBox4.setWidth(37);
        FVBox4.setHeight(32);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFHBox hBoxClose = new TFHBox();

    private void init_hBoxClose() {
        hBoxClose.setName("hBoxClose");
        hBoxClose.setLeft(268);
        hBoxClose.setTop(0);
        hBoxClose.setWidth(55);
        hBoxClose.setHeight(55);
        hBoxClose.setBorderStyle("stNone");
        hBoxClose.setPaddingTop(0);
        hBoxClose.setPaddingLeft(0);
        hBoxClose.setPaddingRight(0);
        hBoxClose.setPaddingBottom(0);
        hBoxClose.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxCloseClick(event);
            processarFlow("FrmSnapShotCamera", "hBoxClose", "OnClick");
        });
        hBoxClose.setMarginTop(0);
        hBoxClose.setMarginLeft(0);
        hBoxClose.setMarginRight(0);
        hBoxClose.setMarginBottom(0);
        hBoxClose.setSpacing(1);
        hBoxClose.setFlexVflex("ftFalse");
        hBoxClose.setFlexHflex("ftFalse");
        hBoxClose.setScrollable(false);
        hBoxClose.setBoxShadowConfigHorizontalLength(10);
        hBoxClose.setBoxShadowConfigVerticalLength(10);
        hBoxClose.setBoxShadowConfigBlurRadius(5);
        hBoxClose.setBoxShadowConfigSpreadRadius(0);
        hBoxClose.setBoxShadowConfigShadowColor("clBlack");
        hBoxClose.setBoxShadowConfigOpacity(75);
        hBoxClose.setVAlign("tvTop");
        hBoxOpcoes.addChildren(hBoxClose);
        hBoxClose.applyProperties();
    }

    public TFIconClass btnClose = new TFIconClass();

    private void init_btnClose() {
        btnClose.setName("btnClose");
        btnClose.setLeft(0);
        btnClose.setTop(0);
        btnClose.setIconClass("window-close");
        btnClose.setSize(44);
        btnClose.setColor("clBlack");
        hBoxClose.addChildren(btnClose);
        btnClose.applyProperties();
    }

    public TFHBox hboxEsp1 = new TFHBox();

    private void init_hboxEsp1() {
        hboxEsp1.setName("hboxEsp1");
        hboxEsp1.setLeft(323);
        hboxEsp1.setTop(0);
        hboxEsp1.setWidth(16);
        hboxEsp1.setHeight(46);
        hboxEsp1.setBorderStyle("stNone");
        hboxEsp1.setPaddingTop(0);
        hboxEsp1.setPaddingLeft(0);
        hboxEsp1.setPaddingRight(0);
        hboxEsp1.setPaddingBottom(0);
        hboxEsp1.setMarginTop(0);
        hboxEsp1.setMarginLeft(0);
        hboxEsp1.setMarginRight(0);
        hboxEsp1.setMarginBottom(0);
        hboxEsp1.setSpacing(1);
        hboxEsp1.setFlexVflex("ftFalse");
        hboxEsp1.setFlexHflex("ftFalse");
        hboxEsp1.setScrollable(false);
        hboxEsp1.setBoxShadowConfigHorizontalLength(10);
        hboxEsp1.setBoxShadowConfigVerticalLength(10);
        hboxEsp1.setBoxShadowConfigBlurRadius(5);
        hboxEsp1.setBoxShadowConfigSpreadRadius(0);
        hboxEsp1.setBoxShadowConfigShadowColor("clBlack");
        hboxEsp1.setBoxShadowConfigOpacity(75);
        hboxEsp1.setVAlign("tvTop");
        hBoxOpcoes.addChildren(hboxEsp1);
        hboxEsp1.applyProperties();
    }

    public TFVBox vboxFlash = new TFVBox();

    private void init_vboxFlash() {
        vboxFlash.setName("vboxFlash");
        vboxFlash.setLeft(339);
        vboxFlash.setTop(0);
        vboxFlash.setWidth(55);
        vboxFlash.setHeight(55);
        vboxFlash.setBorderStyle("stNone");
        vboxFlash.setPaddingTop(0);
        vboxFlash.setPaddingLeft(0);
        vboxFlash.setPaddingRight(0);
        vboxFlash.setPaddingBottom(0);
        vboxFlash.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vboxFlashClick(event);
            processarFlow("FrmSnapShotCamera", "vboxFlash", "OnClick");
        });
        vboxFlash.setMarginTop(0);
        vboxFlash.setMarginLeft(20);
        vboxFlash.setMarginRight(0);
        vboxFlash.setMarginBottom(0);
        vboxFlash.setSpacing(1);
        vboxFlash.setFlexVflex("ftFalse");
        vboxFlash.setFlexHflex("ftFalse");
        vboxFlash.setScrollable(false);
        vboxFlash.setBoxShadowConfigHorizontalLength(10);
        vboxFlash.setBoxShadowConfigVerticalLength(10);
        vboxFlash.setBoxShadowConfigBlurRadius(5);
        vboxFlash.setBoxShadowConfigSpreadRadius(0);
        vboxFlash.setBoxShadowConfigShadowColor("clBlack");
        vboxFlash.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(vboxFlash);
        vboxFlash.applyProperties();
    }

    public TFIconClass iconClassFlash = new TFIconClass();

    private void init_iconClassFlash() {
        iconClassFlash.setName("iconClassFlash");
        iconClassFlash.setLeft(0);
        iconClassFlash.setTop(0);
        iconClassFlash.setIconClass("bolt");
        iconClassFlash.setSize(40);
        iconClassFlash.setColor("clBlack");
        vboxFlash.addChildren(iconClassFlash);
        iconClassFlash.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(394);
        FHBox2.setTop(0);
        FHBox2.setWidth(16);
        FHBox2.setHeight(46);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxOpcoes.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox vboxZoom = new TFVBox();

    private void init_vboxZoom() {
        vboxZoom.setName("vboxZoom");
        vboxZoom.setLeft(410);
        vboxZoom.setTop(0);
        vboxZoom.setWidth(55);
        vboxZoom.setHeight(55);
        vboxZoom.setBorderStyle("stNone");
        vboxZoom.setPaddingTop(0);
        vboxZoom.setPaddingLeft(0);
        vboxZoom.setPaddingRight(0);
        vboxZoom.setPaddingBottom(0);
        vboxZoom.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            vboxZoomClick(event);
            processarFlow("FrmSnapShotCamera", "vboxZoom", "OnClick");
        });
        vboxZoom.setMarginTop(0);
        vboxZoom.setMarginLeft(0);
        vboxZoom.setMarginRight(0);
        vboxZoom.setMarginBottom(0);
        vboxZoom.setSpacing(1);
        vboxZoom.setFlexVflex("ftFalse");
        vboxZoom.setFlexHflex("ftFalse");
        vboxZoom.setScrollable(false);
        vboxZoom.setBoxShadowConfigHorizontalLength(10);
        vboxZoom.setBoxShadowConfigVerticalLength(10);
        vboxZoom.setBoxShadowConfigBlurRadius(5);
        vboxZoom.setBoxShadowConfigSpreadRadius(0);
        vboxZoom.setBoxShadowConfigShadowColor("clBlack");
        vboxZoom.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(vboxZoom);
        vboxZoom.applyProperties();
    }

    public TFIconClass iconClassZoom = new TFIconClass();

    private void init_iconClassZoom() {
        iconClassZoom.setName("iconClassZoom");
        iconClassZoom.setLeft(0);
        iconClassZoom.setTop(0);
        iconClassZoom.setIconClass("search-plus");
        iconClassZoom.setSize(40);
        iconClassZoom.setColor("clBlack");
        vboxZoom.addChildren(iconClassZoom);
        iconClassZoom.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(465);
        FVBox3.setTop(0);
        FVBox3.setWidth(20);
        FVBox3.setHeight(32);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftTrue");
        FVBox3.setFlexHflex("ftTrue");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        hBoxOpcoes.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void cameraSnapshotUpload(final UploadEvent event);

    public abstract void iconOkClick(final Event<Object> event);

    public abstract void vBoxCapturarFotoClick(final Event<Object> event);

    public abstract void vBoxCancelClick(final Event<Object> event);

    public abstract void hBoxCloseClick(final Event<Object> event);

    public abstract void vboxFlashClick(final Event<Object> event);

    public abstract void vboxZoomClick(final Event<Object> event);

}