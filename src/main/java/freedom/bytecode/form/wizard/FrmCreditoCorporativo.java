package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCreditoCorporativo extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CreditoCorporativoRNA rn = null;

    public FrmCreditoCorporativo() {
        try {
            rn = (freedom.bytecode.rn.CreditoCorporativoRNA) getRN(freedom.bytecode.rn.wizard.CreditoCorporativoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbRapClienteLogsCc();
        init_vBoxCreditoCorporativoContainer();
        init_hBoxCreditoCorporativoTop1();
        init_btnVoltar();
        init_vBoxCreditoCorporativo1();
        init_hBoxCreditoCorporativoTop2();
        init_vBoxTipo();
        init_FLabel4();
        init_hBoxCBTipo();
        init_cbTipo();
        init_lblValidarTipoPessoa();
        init_vBoxNomeCliente();
        init_lblNomeCliente();
        init_hBoxNomeClienteCadRapido();
        init_edtNomeCliente();
        init_vBoxCreditoCorporativoGridContainer();
        init_gridCreditoCorporativo();
        init_vBoxCreditoCorporativo2();
        init_hBoxCreditoCorporativoTop3();
        init_FLabel1();
        init_memoCreditoCorporativo();
        init_FrmCreditoCorporativo();
    }

    public CAD_RAP_CLIENTE_LOGS_CC tbRapClienteLogsCc;

    private void init_tbRapClienteLogsCc() {
        tbRapClienteLogsCc = rn.tbRapClienteLogsCc;
        tbRapClienteLogsCc.setName("tbRapClienteLogsCc");
        tbRapClienteLogsCc.setMaxRowCount(200);
        tbRapClienteLogsCc.setWKey("17702;17701");
        tbRapClienteLogsCc.setRatioBatchSize(20);
        getTables().put(tbRapClienteLogsCc, "tbRapClienteLogsCc");
        tbRapClienteLogsCc.applyProperties();
    }

    protected TFForm FrmCreditoCorporativo = this;
    private void init_FrmCreditoCorporativo() {
        FrmCreditoCorporativo.setName("FrmCreditoCorporativo");
        FrmCreditoCorporativo.setCaption("Cr\u00E9dito Corporativo");
        FrmCreditoCorporativo.setClientHeight(541);
        FrmCreditoCorporativo.setClientWidth(954);
        FrmCreditoCorporativo.setColor("clBtnFace");
        FrmCreditoCorporativo.setWKey("17702");
        FrmCreditoCorporativo.setSpacing(0);
        FrmCreditoCorporativo.applyProperties();
    }

    public TFVBox vBoxCreditoCorporativoContainer = new TFVBox();

    private void init_vBoxCreditoCorporativoContainer() {
        vBoxCreditoCorporativoContainer.setName("vBoxCreditoCorporativoContainer");
        vBoxCreditoCorporativoContainer.setLeft(0);
        vBoxCreditoCorporativoContainer.setTop(0);
        vBoxCreditoCorporativoContainer.setWidth(954);
        vBoxCreditoCorporativoContainer.setHeight(541);
        vBoxCreditoCorporativoContainer.setAlign("alClient");
        vBoxCreditoCorporativoContainer.setBorderStyle("stNone");
        vBoxCreditoCorporativoContainer.setPaddingTop(5);
        vBoxCreditoCorporativoContainer.setPaddingLeft(0);
        vBoxCreditoCorporativoContainer.setPaddingRight(0);
        vBoxCreditoCorporativoContainer.setPaddingBottom(5);
        vBoxCreditoCorporativoContainer.setMarginTop(0);
        vBoxCreditoCorporativoContainer.setMarginLeft(0);
        vBoxCreditoCorporativoContainer.setMarginRight(0);
        vBoxCreditoCorporativoContainer.setMarginBottom(0);
        vBoxCreditoCorporativoContainer.setSpacing(1);
        vBoxCreditoCorporativoContainer.setFlexVflex("ftFalse");
        vBoxCreditoCorporativoContainer.setFlexHflex("ftFalse");
        vBoxCreditoCorporativoContainer.setScrollable(false);
        vBoxCreditoCorporativoContainer.setBoxShadowConfigHorizontalLength(10);
        vBoxCreditoCorporativoContainer.setBoxShadowConfigVerticalLength(10);
        vBoxCreditoCorporativoContainer.setBoxShadowConfigBlurRadius(5);
        vBoxCreditoCorporativoContainer.setBoxShadowConfigSpreadRadius(0);
        vBoxCreditoCorporativoContainer.setBoxShadowConfigShadowColor("clBlack");
        vBoxCreditoCorporativoContainer.setBoxShadowConfigOpacity(75);
        FrmCreditoCorporativo.addChildren(vBoxCreditoCorporativoContainer);
        vBoxCreditoCorporativoContainer.applyProperties();
    }

    public TFHBox hBoxCreditoCorporativoTop1 = new TFHBox();

    private void init_hBoxCreditoCorporativoTop1() {
        hBoxCreditoCorporativoTop1.setName("hBoxCreditoCorporativoTop1");
        hBoxCreditoCorporativoTop1.setLeft(0);
        hBoxCreditoCorporativoTop1.setTop(0);
        hBoxCreditoCorporativoTop1.setWidth(945);
        hBoxCreditoCorporativoTop1.setHeight(61);
        hBoxCreditoCorporativoTop1.setAlign("alTop");
        hBoxCreditoCorporativoTop1.setBorderStyle("stNone");
        hBoxCreditoCorporativoTop1.setPaddingTop(0);
        hBoxCreditoCorporativoTop1.setPaddingLeft(0);
        hBoxCreditoCorporativoTop1.setPaddingRight(0);
        hBoxCreditoCorporativoTop1.setPaddingBottom(0);
        hBoxCreditoCorporativoTop1.setMarginTop(5);
        hBoxCreditoCorporativoTop1.setMarginLeft(10);
        hBoxCreditoCorporativoTop1.setMarginRight(0);
        hBoxCreditoCorporativoTop1.setMarginBottom(0);
        hBoxCreditoCorporativoTop1.setSpacing(1);
        hBoxCreditoCorporativoTop1.setFlexVflex("ftFalse");
        hBoxCreditoCorporativoTop1.setFlexHflex("ftFalse");
        hBoxCreditoCorporativoTop1.setScrollable(false);
        hBoxCreditoCorporativoTop1.setBoxShadowConfigHorizontalLength(10);
        hBoxCreditoCorporativoTop1.setBoxShadowConfigVerticalLength(10);
        hBoxCreditoCorporativoTop1.setBoxShadowConfigBlurRadius(5);
        hBoxCreditoCorporativoTop1.setBoxShadowConfigSpreadRadius(0);
        hBoxCreditoCorporativoTop1.setBoxShadowConfigShadowColor("clBlack");
        hBoxCreditoCorporativoTop1.setBoxShadowConfigOpacity(75);
        hBoxCreditoCorporativoTop1.setVAlign("tvTop");
        vBoxCreditoCorporativoContainer.addChildren(hBoxCreditoCorporativoTop1);
        hBoxCreditoCorporativoTop1.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(61);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmCreditoCorporativo", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxCreditoCorporativoTop1.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFVBox vBoxCreditoCorporativo1 = new TFVBox();

    private void init_vBoxCreditoCorporativo1() {
        vBoxCreditoCorporativo1.setName("vBoxCreditoCorporativo1");
        vBoxCreditoCorporativo1.setLeft(0);
        vBoxCreditoCorporativo1.setTop(62);
        vBoxCreditoCorporativo1.setWidth(945);
        vBoxCreditoCorporativo1.setHeight(296);
        vBoxCreditoCorporativo1.setAlign("alTop");
        vBoxCreditoCorporativo1.setBorderStyle("stNone");
        vBoxCreditoCorporativo1.setPaddingTop(0);
        vBoxCreditoCorporativo1.setPaddingLeft(5);
        vBoxCreditoCorporativo1.setPaddingRight(0);
        vBoxCreditoCorporativo1.setPaddingBottom(0);
        vBoxCreditoCorporativo1.setMarginTop(0);
        vBoxCreditoCorporativo1.setMarginLeft(0);
        vBoxCreditoCorporativo1.setMarginRight(0);
        vBoxCreditoCorporativo1.setMarginBottom(0);
        vBoxCreditoCorporativo1.setSpacing(1);
        vBoxCreditoCorporativo1.setFlexVflex("ftFalse");
        vBoxCreditoCorporativo1.setFlexHflex("ftFalse");
        vBoxCreditoCorporativo1.setScrollable(false);
        vBoxCreditoCorporativo1.setBoxShadowConfigHorizontalLength(10);
        vBoxCreditoCorporativo1.setBoxShadowConfigVerticalLength(10);
        vBoxCreditoCorporativo1.setBoxShadowConfigBlurRadius(5);
        vBoxCreditoCorporativo1.setBoxShadowConfigSpreadRadius(0);
        vBoxCreditoCorporativo1.setBoxShadowConfigShadowColor("clBlack");
        vBoxCreditoCorporativo1.setBoxShadowConfigOpacity(75);
        vBoxCreditoCorporativoContainer.addChildren(vBoxCreditoCorporativo1);
        vBoxCreditoCorporativo1.applyProperties();
    }

    public TFHBox hBoxCreditoCorporativoTop2 = new TFHBox();

    private void init_hBoxCreditoCorporativoTop2() {
        hBoxCreditoCorporativoTop2.setName("hBoxCreditoCorporativoTop2");
        hBoxCreditoCorporativoTop2.setLeft(0);
        hBoxCreditoCorporativoTop2.setTop(0);
        hBoxCreditoCorporativoTop2.setWidth(717);
        hBoxCreditoCorporativoTop2.setHeight(64);
        hBoxCreditoCorporativoTop2.setAlign("alTop");
        hBoxCreditoCorporativoTop2.setBorderStyle("stNone");
        hBoxCreditoCorporativoTop2.setPaddingTop(5);
        hBoxCreditoCorporativoTop2.setPaddingLeft(0);
        hBoxCreditoCorporativoTop2.setPaddingRight(0);
        hBoxCreditoCorporativoTop2.setPaddingBottom(0);
        hBoxCreditoCorporativoTop2.setMarginTop(0);
        hBoxCreditoCorporativoTop2.setMarginLeft(0);
        hBoxCreditoCorporativoTop2.setMarginRight(0);
        hBoxCreditoCorporativoTop2.setMarginBottom(0);
        hBoxCreditoCorporativoTop2.setSpacing(1);
        hBoxCreditoCorporativoTop2.setFlexVflex("ftFalse");
        hBoxCreditoCorporativoTop2.setFlexHflex("ftFalse");
        hBoxCreditoCorporativoTop2.setScrollable(false);
        hBoxCreditoCorporativoTop2.setBoxShadowConfigHorizontalLength(10);
        hBoxCreditoCorporativoTop2.setBoxShadowConfigVerticalLength(10);
        hBoxCreditoCorporativoTop2.setBoxShadowConfigBlurRadius(5);
        hBoxCreditoCorporativoTop2.setBoxShadowConfigSpreadRadius(0);
        hBoxCreditoCorporativoTop2.setBoxShadowConfigShadowColor("clBlack");
        hBoxCreditoCorporativoTop2.setBoxShadowConfigOpacity(75);
        hBoxCreditoCorporativoTop2.setVAlign("tvTop");
        vBoxCreditoCorporativo1.addChildren(hBoxCreditoCorporativoTop2);
        hBoxCreditoCorporativoTop2.applyProperties();
    }

    public TFVBox vBoxTipo = new TFVBox();

    private void init_vBoxTipo() {
        vBoxTipo.setName("vBoxTipo");
        vBoxTipo.setLeft(0);
        vBoxTipo.setTop(0);
        vBoxTipo.setWidth(141);
        vBoxTipo.setHeight(50);
        vBoxTipo.setBorderStyle("stNone");
        vBoxTipo.setPaddingTop(0);
        vBoxTipo.setPaddingLeft(0);
        vBoxTipo.setPaddingRight(0);
        vBoxTipo.setPaddingBottom(0);
        vBoxTipo.setMarginTop(0);
        vBoxTipo.setMarginLeft(0);
        vBoxTipo.setMarginRight(0);
        vBoxTipo.setMarginBottom(0);
        vBoxTipo.setSpacing(0);
        vBoxTipo.setFlexVflex("ftMin");
        vBoxTipo.setFlexHflex("ftFalse");
        vBoxTipo.setScrollable(false);
        vBoxTipo.setBoxShadowConfigHorizontalLength(10);
        vBoxTipo.setBoxShadowConfigVerticalLength(10);
        vBoxTipo.setBoxShadowConfigBlurRadius(5);
        vBoxTipo.setBoxShadowConfigSpreadRadius(0);
        vBoxTipo.setBoxShadowConfigShadowColor("clBlack");
        vBoxTipo.setBoxShadowConfigOpacity(75);
        hBoxCreditoCorporativoTop2.addChildren(vBoxTipo);
        vBoxTipo.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(0);
        FLabel4.setWidth(28);
        FLabel4.setHeight(18);
        FLabel4.setCaption("Tipo");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-15);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        vBoxTipo.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFHBox hBoxCBTipo = new TFHBox();

    private void init_hBoxCBTipo() {
        hBoxCBTipo.setName("hBoxCBTipo");
        hBoxCBTipo.setLeft(0);
        hBoxCBTipo.setTop(19);
        hBoxCBTipo.setWidth(136);
        hBoxCBTipo.setHeight(27);
        hBoxCBTipo.setBorderStyle("stNone");
        hBoxCBTipo.setPaddingTop(0);
        hBoxCBTipo.setPaddingLeft(0);
        hBoxCBTipo.setPaddingRight(0);
        hBoxCBTipo.setPaddingBottom(0);
        hBoxCBTipo.setMarginTop(0);
        hBoxCBTipo.setMarginLeft(0);
        hBoxCBTipo.setMarginRight(0);
        hBoxCBTipo.setMarginBottom(0);
        hBoxCBTipo.setSpacing(0);
        hBoxCBTipo.setFlexVflex("ftMin");
        hBoxCBTipo.setFlexHflex("ftTrue");
        hBoxCBTipo.setScrollable(false);
        hBoxCBTipo.setBoxShadowConfigHorizontalLength(10);
        hBoxCBTipo.setBoxShadowConfigVerticalLength(10);
        hBoxCBTipo.setBoxShadowConfigBlurRadius(5);
        hBoxCBTipo.setBoxShadowConfigSpreadRadius(0);
        hBoxCBTipo.setBoxShadowConfigShadowColor("clBlack");
        hBoxCBTipo.setBoxShadowConfigOpacity(75);
        hBoxCBTipo.setVAlign("tvTop");
        vBoxTipo.addChildren(hBoxCBTipo);
        hBoxCBTipo.applyProperties();
    }

    public TFCombo cbTipo = new TFCombo();

    private void init_cbTipo() {
        cbTipo.setName("cbTipo");
        cbTipo.setLeft(0);
        cbTipo.setTop(0);
        cbTipo.setWidth(130);
        cbTipo.setHeight(21);
        cbTipo.setFieldName("COD_CLASSE");
        cbTipo.setFlex(true);
        cbTipo.setListOptions("F\u00EDsica=F;Jur\u00EDdica=J");
        cbTipo.setReadOnly(true);
        cbTipo.setRequired(false);
        cbTipo.setPrompt("Selecione");
        cbTipo.setConstraintCheckWhen("cwImmediate");
        cbTipo.setConstraintCheckType("ctExpression");
        cbTipo.setConstraintFocusOnError(false);
        cbTipo.setConstraintEnableUI(true);
        cbTipo.setConstraintEnabled(false);
        cbTipo.setConstraintFormCheck(true);
        cbTipo.setClearOnDelKey(false);
        cbTipo.setUseClearButton(false);
        cbTipo.setHideClearButtonOnNullValue(false);
        hBoxCBTipo.addChildren(cbTipo);
        cbTipo.applyProperties();
        addValidatable(cbTipo);
    }

    public TFLabel lblValidarTipoPessoa = new TFLabel();

    private void init_lblValidarTipoPessoa() {
        lblValidarTipoPessoa.setName("lblValidarTipoPessoa");
        lblValidarTipoPessoa.setLeft(0);
        lblValidarTipoPessoa.setTop(47);
        lblValidarTipoPessoa.setWidth(120);
        lblValidarTipoPessoa.setHeight(13);
        lblValidarTipoPessoa.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblValidarTipoPessoa.setFontColor("clRed");
        lblValidarTipoPessoa.setFontSize(-11);
        lblValidarTipoPessoa.setFontName("Tahoma");
        lblValidarTipoPessoa.setFontStyle("[]");
        lblValidarTipoPessoa.setVisible(false);
        lblValidarTipoPessoa.setVerticalAlignment("taVerticalCenter");
        lblValidarTipoPessoa.setWordBreak(false);
        vBoxTipo.addChildren(lblValidarTipoPessoa);
        lblValidarTipoPessoa.applyProperties();
    }

    public TFVBox vBoxNomeCliente = new TFVBox();

    private void init_vBoxNomeCliente() {
        vBoxNomeCliente.setName("vBoxNomeCliente");
        vBoxNomeCliente.setLeft(141);
        vBoxNomeCliente.setTop(0);
        vBoxNomeCliente.setWidth(361);
        vBoxNomeCliente.setHeight(62);
        vBoxNomeCliente.setBorderStyle("stNone");
        vBoxNomeCliente.setPaddingTop(0);
        vBoxNomeCliente.setPaddingLeft(0);
        vBoxNomeCliente.setPaddingRight(0);
        vBoxNomeCliente.setPaddingBottom(0);
        vBoxNomeCliente.setMarginTop(0);
        vBoxNomeCliente.setMarginLeft(0);
        vBoxNomeCliente.setMarginRight(5);
        vBoxNomeCliente.setMarginBottom(0);
        vBoxNomeCliente.setSpacing(0);
        vBoxNomeCliente.setFlexVflex("ftMin");
        vBoxNomeCliente.setFlexHflex("ftTrue");
        vBoxNomeCliente.setScrollable(false);
        vBoxNomeCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxNomeCliente.setBoxShadowConfigVerticalLength(10);
        vBoxNomeCliente.setBoxShadowConfigBlurRadius(5);
        vBoxNomeCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxNomeCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxNomeCliente.setBoxShadowConfigOpacity(75);
        hBoxCreditoCorporativoTop2.addChildren(vBoxNomeCliente);
        vBoxNomeCliente.applyProperties();
    }

    public TFLabel lblNomeCliente = new TFLabel();

    private void init_lblNomeCliente() {
        lblNomeCliente.setName("lblNomeCliente");
        lblNomeCliente.setLeft(0);
        lblNomeCliente.setTop(0);
        lblNomeCliente.setWidth(39);
        lblNomeCliente.setHeight(18);
        lblNomeCliente.setCaption("Nome");
        lblNomeCliente.setFontColor("clWindowText");
        lblNomeCliente.setFontSize(-15);
        lblNomeCliente.setFontName("Tahoma");
        lblNomeCliente.setFontStyle("[]");
        lblNomeCliente.setVerticalAlignment("taVerticalCenter");
        lblNomeCliente.setWordBreak(false);
        vBoxNomeCliente.addChildren(lblNomeCliente);
        lblNomeCliente.applyProperties();
    }

    public TFHBox hBoxNomeClienteCadRapido = new TFHBox();

    private void init_hBoxNomeClienteCadRapido() {
        hBoxNomeClienteCadRapido.setName("hBoxNomeClienteCadRapido");
        hBoxNomeClienteCadRapido.setLeft(0);
        hBoxNomeClienteCadRapido.setTop(19);
        hBoxNomeClienteCadRapido.setWidth(344);
        hBoxNomeClienteCadRapido.setHeight(36);
        hBoxNomeClienteCadRapido.setBorderStyle("stNone");
        hBoxNomeClienteCadRapido.setPaddingTop(0);
        hBoxNomeClienteCadRapido.setPaddingLeft(0);
        hBoxNomeClienteCadRapido.setPaddingRight(0);
        hBoxNomeClienteCadRapido.setPaddingBottom(0);
        hBoxNomeClienteCadRapido.setMarginTop(0);
        hBoxNomeClienteCadRapido.setMarginLeft(0);
        hBoxNomeClienteCadRapido.setMarginRight(0);
        hBoxNomeClienteCadRapido.setMarginBottom(0);
        hBoxNomeClienteCadRapido.setSpacing(3);
        hBoxNomeClienteCadRapido.setFlexVflex("ftMin");
        hBoxNomeClienteCadRapido.setFlexHflex("ftTrue");
        hBoxNomeClienteCadRapido.setScrollable(false);
        hBoxNomeClienteCadRapido.setBoxShadowConfigHorizontalLength(10);
        hBoxNomeClienteCadRapido.setBoxShadowConfigVerticalLength(10);
        hBoxNomeClienteCadRapido.setBoxShadowConfigBlurRadius(5);
        hBoxNomeClienteCadRapido.setBoxShadowConfigSpreadRadius(0);
        hBoxNomeClienteCadRapido.setBoxShadowConfigShadowColor("clBlack");
        hBoxNomeClienteCadRapido.setBoxShadowConfigOpacity(75);
        hBoxNomeClienteCadRapido.setVAlign("tvTop");
        vBoxNomeCliente.addChildren(hBoxNomeClienteCadRapido);
        hBoxNomeClienteCadRapido.applyProperties();
    }

    public TFString edtNomeCliente = new TFString();

    private void init_edtNomeCliente() {
        edtNomeCliente.setName("edtNomeCliente");
        edtNomeCliente.setLeft(0);
        edtNomeCliente.setTop(0);
        edtNomeCliente.setWidth(293);
        edtNomeCliente.setHeight(29);
        edtNomeCliente.setFieldName("NOME");
        edtNomeCliente.setFlex(true);
        edtNomeCliente.setRequired(false);
        edtNomeCliente.setConstraintCheckWhen("cwImmediate");
        edtNomeCliente.setConstraintCheckType("ctExpression");
        edtNomeCliente.setConstraintFocusOnError(false);
        edtNomeCliente.setConstraintEnableUI(true);
        edtNomeCliente.setConstraintEnabled(false);
        edtNomeCliente.setConstraintFormCheck(true);
        edtNomeCliente.setCharCase("ccUpper");
        edtNomeCliente.setPwd(false);
        edtNomeCliente.setMaxlength(150);
        edtNomeCliente.setFontColor("clWindowText");
        edtNomeCliente.setFontSize(-17);
        edtNomeCliente.setFontName("Tahoma");
        edtNomeCliente.setFontStyle("[]");
        edtNomeCliente.setSaveLiteralCharacter(false);
        edtNomeCliente.applyProperties();
        hBoxNomeClienteCadRapido.addChildren(edtNomeCliente);
        addValidatable(edtNomeCliente);
    }

    public TFVBox vBoxCreditoCorporativoGridContainer = new TFVBox();

    private void init_vBoxCreditoCorporativoGridContainer() {
        vBoxCreditoCorporativoGridContainer.setName("vBoxCreditoCorporativoGridContainer");
        vBoxCreditoCorporativoGridContainer.setLeft(0);
        vBoxCreditoCorporativoGridContainer.setTop(65);
        vBoxCreditoCorporativoGridContainer.setWidth(937);
        vBoxCreditoCorporativoGridContainer.setHeight(224);
        vBoxCreditoCorporativoGridContainer.setAlign("alTop");
        vBoxCreditoCorporativoGridContainer.setBorderStyle("stNone");
        vBoxCreditoCorporativoGridContainer.setPaddingTop(0);
        vBoxCreditoCorporativoGridContainer.setPaddingLeft(0);
        vBoxCreditoCorporativoGridContainer.setPaddingRight(0);
        vBoxCreditoCorporativoGridContainer.setPaddingBottom(0);
        vBoxCreditoCorporativoGridContainer.setMarginTop(0);
        vBoxCreditoCorporativoGridContainer.setMarginLeft(0);
        vBoxCreditoCorporativoGridContainer.setMarginRight(0);
        vBoxCreditoCorporativoGridContainer.setMarginBottom(0);
        vBoxCreditoCorporativoGridContainer.setSpacing(1);
        vBoxCreditoCorporativoGridContainer.setFlexVflex("ftFalse");
        vBoxCreditoCorporativoGridContainer.setFlexHflex("ftFalse");
        vBoxCreditoCorporativoGridContainer.setScrollable(false);
        vBoxCreditoCorporativoGridContainer.setBoxShadowConfigHorizontalLength(10);
        vBoxCreditoCorporativoGridContainer.setBoxShadowConfigVerticalLength(10);
        vBoxCreditoCorporativoGridContainer.setBoxShadowConfigBlurRadius(5);
        vBoxCreditoCorporativoGridContainer.setBoxShadowConfigSpreadRadius(0);
        vBoxCreditoCorporativoGridContainer.setBoxShadowConfigShadowColor("clBlack");
        vBoxCreditoCorporativoGridContainer.setBoxShadowConfigOpacity(75);
        vBoxCreditoCorporativo1.addChildren(vBoxCreditoCorporativoGridContainer);
        vBoxCreditoCorporativoGridContainer.applyProperties();
    }

    public TFGrid gridCreditoCorporativo = new TFGrid();

    private void init_gridCreditoCorporativo() {
        gridCreditoCorporativo.setName("gridCreditoCorporativo");
        gridCreditoCorporativo.setLeft(0);
        gridCreditoCorporativo.setTop(0);
        gridCreditoCorporativo.setWidth(930);
        gridCreditoCorporativo.setHeight(214);
        gridCreditoCorporativo.setAlign("alClient");
        gridCreditoCorporativo.setTable(tbRapClienteLogsCc);
        gridCreditoCorporativo.setFlexVflex("ftTrue");
        gridCreditoCorporativo.setFlexHflex("ftTrue");
        gridCreditoCorporativo.setPagingEnabled(false);
        gridCreditoCorporativo.setFrozenColumns(0);
        gridCreditoCorporativo.setShowFooter(false);
        gridCreditoCorporativo.setShowHeader(true);
        gridCreditoCorporativo.setMultiSelection(false);
        gridCreditoCorporativo.setGroupingEnabled(false);
        gridCreditoCorporativo.setGroupingExpanded(false);
        gridCreditoCorporativo.setGroupingShowFooter(false);
        gridCreditoCorporativo.setCrosstabEnabled(false);
        gridCreditoCorporativo.setCrosstabGroupType("cgtConcat");
        gridCreditoCorporativo.setEditionEnabled(false);
        gridCreditoCorporativo.setNoBorder(false);
        TFGridColumn gridCreditoCorporativoCOD_EMPRESA = new TFGridColumn();
        gridCreditoCorporativoCOD_EMPRESA.setFieldName("COD_EMPRESA");
        gridCreditoCorporativoCOD_EMPRESA.setTitleCaption("CE");
        gridCreditoCorporativoCOD_EMPRESA.setWidth(30);
        gridCreditoCorporativoCOD_EMPRESA.setVisible(true);
        gridCreditoCorporativoCOD_EMPRESA.setPrecision(0);
        gridCreditoCorporativoCOD_EMPRESA.setTextAlign("taLeft");
        gridCreditoCorporativoCOD_EMPRESA.setFieldType("ftString");
        gridCreditoCorporativoCOD_EMPRESA.setFlexRatio(0);
        gridCreditoCorporativoCOD_EMPRESA.setSort(false);
        gridCreditoCorporativoCOD_EMPRESA.setImageHeader(0);
        gridCreditoCorporativoCOD_EMPRESA.setWrap(false);
        gridCreditoCorporativoCOD_EMPRESA.setFlex(false);
        gridCreditoCorporativoCOD_EMPRESA.setCharCase("ccNormal");
        gridCreditoCorporativoCOD_EMPRESA.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoCOD_EMPRESA.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoCOD_EMPRESA.setShowLabel(true);
        gridCreditoCorporativoCOD_EMPRESA.setEditorEditType("etTFString");
        gridCreditoCorporativoCOD_EMPRESA.setEditorPrecision(0);
        gridCreditoCorporativoCOD_EMPRESA.setEditorMaxLength(100);
        gridCreditoCorporativoCOD_EMPRESA.setEditorLookupFilterKey(0);
        gridCreditoCorporativoCOD_EMPRESA.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoCOD_EMPRESA.setEditorPopupHeight(400);
        gridCreditoCorporativoCOD_EMPRESA.setEditorPopupWidth(400);
        gridCreditoCorporativoCOD_EMPRESA.setEditorCharCase("ccNormal");
        gridCreditoCorporativoCOD_EMPRESA.setEditorEnabled(false);
        gridCreditoCorporativoCOD_EMPRESA.setEditorReadOnly(false);
        gridCreditoCorporativoCOD_EMPRESA.setCheckedValue("S");
        gridCreditoCorporativoCOD_EMPRESA.setUncheckedValue("N");
        gridCreditoCorporativoCOD_EMPRESA.setHiperLink(false);
        gridCreditoCorporativoCOD_EMPRESA.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoCOD_EMPRESA.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoCOD_EMPRESA.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoCOD_EMPRESA.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoCOD_EMPRESA.setEditorConstraintEnabled(false);
        gridCreditoCorporativoCOD_EMPRESA.setEmpty(false);
        gridCreditoCorporativoCOD_EMPRESA.setMobileOptsShowMobile(false);
        gridCreditoCorporativoCOD_EMPRESA.setMobileOptsOrder(0);
        gridCreditoCorporativoCOD_EMPRESA.setBoxSize(0);
        gridCreditoCorporativoCOD_EMPRESA.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoCOD_EMPRESA);
        TFGridColumn gridCreditoCorporativoDATA = new TFGridColumn();
        gridCreditoCorporativoDATA.setFieldName("DATA");
        gridCreditoCorporativoDATA.setTitleCaption("Data");
        gridCreditoCorporativoDATA.setWidth(130);
        gridCreditoCorporativoDATA.setVisible(true);
        gridCreditoCorporativoDATA.setPrecision(0);
        gridCreditoCorporativoDATA.setTextAlign("taLeft");
        gridCreditoCorporativoDATA.setFieldType("ftDateTime");
        gridCreditoCorporativoDATA.setFlexRatio(0);
        gridCreditoCorporativoDATA.setSort(false);
        gridCreditoCorporativoDATA.setImageHeader(0);
        gridCreditoCorporativoDATA.setWrap(false);
        gridCreditoCorporativoDATA.setFlex(false);
        gridCreditoCorporativoDATA.setCharCase("ccNormal");
        gridCreditoCorporativoDATA.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoDATA.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoDATA.setShowLabel(true);
        gridCreditoCorporativoDATA.setEditorEditType("etTFString");
        gridCreditoCorporativoDATA.setEditorPrecision(0);
        gridCreditoCorporativoDATA.setEditorMaxLength(100);
        gridCreditoCorporativoDATA.setEditorLookupFilterKey(0);
        gridCreditoCorporativoDATA.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoDATA.setEditorPopupHeight(400);
        gridCreditoCorporativoDATA.setEditorPopupWidth(400);
        gridCreditoCorporativoDATA.setEditorCharCase("ccNormal");
        gridCreditoCorporativoDATA.setEditorEnabled(false);
        gridCreditoCorporativoDATA.setEditorReadOnly(false);
        gridCreditoCorporativoDATA.setCheckedValue("S");
        gridCreditoCorporativoDATA.setUncheckedValue("N");
        gridCreditoCorporativoDATA.setHiperLink(false);
        gridCreditoCorporativoDATA.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoDATA.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoDATA.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoDATA.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoDATA.setEditorConstraintEnabled(false);
        gridCreditoCorporativoDATA.setEmpty(false);
        gridCreditoCorporativoDATA.setMobileOptsShowMobile(false);
        gridCreditoCorporativoDATA.setMobileOptsOrder(0);
        gridCreditoCorporativoDATA.setBoxSize(0);
        gridCreditoCorporativoDATA.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoDATA);
        TFGridColumn gridCreditoCorporativoUSUARIO = new TFGridColumn();
        gridCreditoCorporativoUSUARIO.setFieldName("USUARIO");
        gridCreditoCorporativoUSUARIO.setTitleCaption("Usu\u00E1rio");
        gridCreditoCorporativoUSUARIO.setWidth(100);
        gridCreditoCorporativoUSUARIO.setVisible(true);
        gridCreditoCorporativoUSUARIO.setPrecision(0);
        gridCreditoCorporativoUSUARIO.setTextAlign("taLeft");
        gridCreditoCorporativoUSUARIO.setFieldType("ftString");
        gridCreditoCorporativoUSUARIO.setFlexRatio(0);
        gridCreditoCorporativoUSUARIO.setSort(false);
        gridCreditoCorporativoUSUARIO.setImageHeader(0);
        gridCreditoCorporativoUSUARIO.setWrap(false);
        gridCreditoCorporativoUSUARIO.setFlex(false);
        gridCreditoCorporativoUSUARIO.setCharCase("ccNormal");
        gridCreditoCorporativoUSUARIO.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoUSUARIO.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoUSUARIO.setShowLabel(true);
        gridCreditoCorporativoUSUARIO.setEditorEditType("etTFString");
        gridCreditoCorporativoUSUARIO.setEditorPrecision(0);
        gridCreditoCorporativoUSUARIO.setEditorMaxLength(100);
        gridCreditoCorporativoUSUARIO.setEditorLookupFilterKey(0);
        gridCreditoCorporativoUSUARIO.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoUSUARIO.setEditorPopupHeight(400);
        gridCreditoCorporativoUSUARIO.setEditorPopupWidth(400);
        gridCreditoCorporativoUSUARIO.setEditorCharCase("ccNormal");
        gridCreditoCorporativoUSUARIO.setEditorEnabled(false);
        gridCreditoCorporativoUSUARIO.setEditorReadOnly(false);
        gridCreditoCorporativoUSUARIO.setCheckedValue("S");
        gridCreditoCorporativoUSUARIO.setUncheckedValue("N");
        gridCreditoCorporativoUSUARIO.setHiperLink(false);
        gridCreditoCorporativoUSUARIO.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoUSUARIO.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoUSUARIO.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoUSUARIO.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoUSUARIO.setEditorConstraintEnabled(false);
        gridCreditoCorporativoUSUARIO.setEmpty(false);
        gridCreditoCorporativoUSUARIO.setMobileOptsShowMobile(false);
        gridCreditoCorporativoUSUARIO.setMobileOptsOrder(0);
        gridCreditoCorporativoUSUARIO.setBoxSize(0);
        gridCreditoCorporativoUSUARIO.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoUSUARIO);
        TFGridColumn gridCreditoCorporativoMETODO = new TFGridColumn();
        gridCreditoCorporativoMETODO.setFieldName("METODO");
        gridCreditoCorporativoMETODO.setTitleCaption("M\u00E9todo");
        gridCreditoCorporativoMETODO.setWidth(100);
        gridCreditoCorporativoMETODO.setVisible(true);
        gridCreditoCorporativoMETODO.setPrecision(0);
        gridCreditoCorporativoMETODO.setTextAlign("taLeft");
        gridCreditoCorporativoMETODO.setFieldType("ftString");
        gridCreditoCorporativoMETODO.setFlexRatio(0);
        gridCreditoCorporativoMETODO.setSort(false);
        gridCreditoCorporativoMETODO.setImageHeader(0);
        gridCreditoCorporativoMETODO.setWrap(false);
        gridCreditoCorporativoMETODO.setFlex(false);
        gridCreditoCorporativoMETODO.setCharCase("ccNormal");
        gridCreditoCorporativoMETODO.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoMETODO.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoMETODO.setShowLabel(true);
        gridCreditoCorporativoMETODO.setEditorEditType("etTFString");
        gridCreditoCorporativoMETODO.setEditorPrecision(0);
        gridCreditoCorporativoMETODO.setEditorMaxLength(100);
        gridCreditoCorporativoMETODO.setEditorLookupFilterKey(0);
        gridCreditoCorporativoMETODO.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoMETODO.setEditorPopupHeight(400);
        gridCreditoCorporativoMETODO.setEditorPopupWidth(400);
        gridCreditoCorporativoMETODO.setEditorCharCase("ccNormal");
        gridCreditoCorporativoMETODO.setEditorEnabled(false);
        gridCreditoCorporativoMETODO.setEditorReadOnly(false);
        gridCreditoCorporativoMETODO.setCheckedValue("S");
        gridCreditoCorporativoMETODO.setUncheckedValue("N");
        gridCreditoCorporativoMETODO.setHiperLink(false);
        gridCreditoCorporativoMETODO.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoMETODO.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoMETODO.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoMETODO.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoMETODO.setEditorConstraintEnabled(false);
        gridCreditoCorporativoMETODO.setEmpty(false);
        gridCreditoCorporativoMETODO.setMobileOptsShowMobile(false);
        gridCreditoCorporativoMETODO.setMobileOptsOrder(0);
        gridCreditoCorporativoMETODO.setBoxSize(0);
        gridCreditoCorporativoMETODO.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoMETODO);
        TFGridColumn gridCreditoCorporativoLIMITE = new TFGridColumn();
        gridCreditoCorporativoLIMITE.setFieldName("LIMITE");
        gridCreditoCorporativoLIMITE.setTitleCaption("Limite");
        gridCreditoCorporativoLIMITE.setWidth(130);
        gridCreditoCorporativoLIMITE.setVisible(true);
        gridCreditoCorporativoLIMITE.setPrecision(0);
        gridCreditoCorporativoLIMITE.setTextAlign("taRight");
        gridCreditoCorporativoLIMITE.setFieldType("ftDecimal");
        gridCreditoCorporativoLIMITE.setFlexRatio(0);
        gridCreditoCorporativoLIMITE.setSort(false);
        gridCreditoCorporativoLIMITE.setImageHeader(0);
        gridCreditoCorporativoLIMITE.setWrap(false);
        gridCreditoCorporativoLIMITE.setFlex(false);
        gridCreditoCorporativoLIMITE.setCharCase("ccNormal");
        gridCreditoCorporativoLIMITE.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoLIMITE.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoLIMITE.setShowLabel(true);
        gridCreditoCorporativoLIMITE.setEditorEditType("etTFString");
        gridCreditoCorporativoLIMITE.setEditorPrecision(0);
        gridCreditoCorporativoLIMITE.setEditorMaxLength(100);
        gridCreditoCorporativoLIMITE.setEditorLookupFilterKey(0);
        gridCreditoCorporativoLIMITE.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoLIMITE.setEditorPopupHeight(400);
        gridCreditoCorporativoLIMITE.setEditorPopupWidth(400);
        gridCreditoCorporativoLIMITE.setEditorCharCase("ccNormal");
        gridCreditoCorporativoLIMITE.setEditorEnabled(false);
        gridCreditoCorporativoLIMITE.setEditorReadOnly(false);
        gridCreditoCorporativoLIMITE.setCheckedValue("S");
        gridCreditoCorporativoLIMITE.setUncheckedValue("N");
        gridCreditoCorporativoLIMITE.setHiperLink(false);
        gridCreditoCorporativoLIMITE.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoLIMITE.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoLIMITE.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoLIMITE.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoLIMITE.setEditorConstraintEnabled(false);
        gridCreditoCorporativoLIMITE.setEmpty(false);
        gridCreditoCorporativoLIMITE.setMobileOptsShowMobile(false);
        gridCreditoCorporativoLIMITE.setMobileOptsOrder(0);
        gridCreditoCorporativoLIMITE.setBoxSize(0);
        gridCreditoCorporativoLIMITE.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoLIMITE);
        TFGridColumn gridCreditoCorporativoVALOR_DISPONIVEL = new TFGridColumn();
        gridCreditoCorporativoVALOR_DISPONIVEL.setFieldName("VALOR_DISPONIVEL");
        gridCreditoCorporativoVALOR_DISPONIVEL.setTitleCaption("Valor Dispon\u00EDvel");
        gridCreditoCorporativoVALOR_DISPONIVEL.setWidth(130);
        gridCreditoCorporativoVALOR_DISPONIVEL.setVisible(true);
        gridCreditoCorporativoVALOR_DISPONIVEL.setPrecision(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setTextAlign("taRight");
        gridCreditoCorporativoVALOR_DISPONIVEL.setFieldType("ftDecimal");
        gridCreditoCorporativoVALOR_DISPONIVEL.setFlexRatio(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setSort(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setImageHeader(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setWrap(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setFlex(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setCharCase("ccNormal");
        gridCreditoCorporativoVALOR_DISPONIVEL.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoVALOR_DISPONIVEL.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoVALOR_DISPONIVEL.setShowLabel(true);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorEditType("etTFString");
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorPrecision(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorMaxLength(100);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorLookupFilterKey(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorPopupHeight(400);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorPopupWidth(400);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorCharCase("ccNormal");
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorEnabled(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorReadOnly(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setCheckedValue("S");
        gridCreditoCorporativoVALOR_DISPONIVEL.setUncheckedValue("N");
        gridCreditoCorporativoVALOR_DISPONIVEL.setHiperLink(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEditorConstraintEnabled(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setEmpty(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setMobileOptsShowMobile(false);
        gridCreditoCorporativoVALOR_DISPONIVEL.setMobileOptsOrder(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setBoxSize(0);
        gridCreditoCorporativoVALOR_DISPONIVEL.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoVALOR_DISPONIVEL);
        TFGridColumn gridCreditoCorporativoVALOR_UTILIZADO = new TFGridColumn();
        gridCreditoCorporativoVALOR_UTILIZADO.setFieldName("VALOR_UTILIZADO");
        gridCreditoCorporativoVALOR_UTILIZADO.setTitleCaption("Valor Utilizado");
        gridCreditoCorporativoVALOR_UTILIZADO.setWidth(130);
        gridCreditoCorporativoVALOR_UTILIZADO.setVisible(true);
        gridCreditoCorporativoVALOR_UTILIZADO.setPrecision(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setTextAlign("taRight");
        gridCreditoCorporativoVALOR_UTILIZADO.setFieldType("ftDecimal");
        gridCreditoCorporativoVALOR_UTILIZADO.setFlexRatio(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setSort(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setImageHeader(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setWrap(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setFlex(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setCharCase("ccNormal");
        gridCreditoCorporativoVALOR_UTILIZADO.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoVALOR_UTILIZADO.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoVALOR_UTILIZADO.setShowLabel(true);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorEditType("etTFString");
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorPrecision(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorMaxLength(100);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorLookupFilterKey(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorPopupHeight(400);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorPopupWidth(400);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorCharCase("ccNormal");
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorEnabled(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorReadOnly(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setCheckedValue("S");
        gridCreditoCorporativoVALOR_UTILIZADO.setUncheckedValue("N");
        gridCreditoCorporativoVALOR_UTILIZADO.setHiperLink(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoVALOR_UTILIZADO.setEditorConstraintEnabled(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setEmpty(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setMobileOptsShowMobile(false);
        gridCreditoCorporativoVALOR_UTILIZADO.setMobileOptsOrder(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setBoxSize(0);
        gridCreditoCorporativoVALOR_UTILIZADO.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoVALOR_UTILIZADO);
        TFGridColumn gridCreditoCorporativoDATA_VIGENCIA = new TFGridColumn();
        gridCreditoCorporativoDATA_VIGENCIA.setFieldName("DATA_VIGENCIA");
        gridCreditoCorporativoDATA_VIGENCIA.setTitleCaption("Data Vig\u00EAncia");
        gridCreditoCorporativoDATA_VIGENCIA.setWidth(130);
        gridCreditoCorporativoDATA_VIGENCIA.setVisible(true);
        gridCreditoCorporativoDATA_VIGENCIA.setPrecision(0);
        gridCreditoCorporativoDATA_VIGENCIA.setTextAlign("taLeft");
        gridCreditoCorporativoDATA_VIGENCIA.setFieldType("ftDate");
        gridCreditoCorporativoDATA_VIGENCIA.setFlexRatio(0);
        gridCreditoCorporativoDATA_VIGENCIA.setSort(false);
        gridCreditoCorporativoDATA_VIGENCIA.setImageHeader(0);
        gridCreditoCorporativoDATA_VIGENCIA.setWrap(false);
        gridCreditoCorporativoDATA_VIGENCIA.setFlex(false);
        gridCreditoCorporativoDATA_VIGENCIA.setCharCase("ccNormal");
        gridCreditoCorporativoDATA_VIGENCIA.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoDATA_VIGENCIA.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoDATA_VIGENCIA.setShowLabel(true);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorEditType("etTFString");
        gridCreditoCorporativoDATA_VIGENCIA.setEditorPrecision(0);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorMaxLength(100);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorLookupFilterKey(0);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorPopupHeight(400);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorPopupWidth(400);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorCharCase("ccNormal");
        gridCreditoCorporativoDATA_VIGENCIA.setEditorEnabled(false);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorReadOnly(false);
        gridCreditoCorporativoDATA_VIGENCIA.setCheckedValue("S");
        gridCreditoCorporativoDATA_VIGENCIA.setUncheckedValue("N");
        gridCreditoCorporativoDATA_VIGENCIA.setHiperLink(false);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoDATA_VIGENCIA.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoDATA_VIGENCIA.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoDATA_VIGENCIA.setEditorConstraintEnabled(false);
        gridCreditoCorporativoDATA_VIGENCIA.setEmpty(false);
        gridCreditoCorporativoDATA_VIGENCIA.setMobileOptsShowMobile(false);
        gridCreditoCorporativoDATA_VIGENCIA.setMobileOptsOrder(0);
        gridCreditoCorporativoDATA_VIGENCIA.setBoxSize(0);
        gridCreditoCorporativoDATA_VIGENCIA.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoDATA_VIGENCIA);
        TFGridColumn gridCreditoCorporativoMODALIDADE = new TFGridColumn();
        gridCreditoCorporativoMODALIDADE.setFieldName("MODALIDADE");
        gridCreditoCorporativoMODALIDADE.setTitleCaption("Modalidade");
        gridCreditoCorporativoMODALIDADE.setWidth(100);
        gridCreditoCorporativoMODALIDADE.setVisible(true);
        gridCreditoCorporativoMODALIDADE.setPrecision(0);
        gridCreditoCorporativoMODALIDADE.setTextAlign("taLeft");
        gridCreditoCorporativoMODALIDADE.setFieldType("ftString");
        gridCreditoCorporativoMODALIDADE.setFlexRatio(0);
        gridCreditoCorporativoMODALIDADE.setSort(false);
        gridCreditoCorporativoMODALIDADE.setImageHeader(0);
        gridCreditoCorporativoMODALIDADE.setWrap(false);
        gridCreditoCorporativoMODALIDADE.setFlex(false);
        gridCreditoCorporativoMODALIDADE.setCharCase("ccNormal");
        gridCreditoCorporativoMODALIDADE.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoMODALIDADE.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoMODALIDADE.setShowLabel(true);
        gridCreditoCorporativoMODALIDADE.setEditorEditType("etTFString");
        gridCreditoCorporativoMODALIDADE.setEditorPrecision(0);
        gridCreditoCorporativoMODALIDADE.setEditorMaxLength(100);
        gridCreditoCorporativoMODALIDADE.setEditorLookupFilterKey(0);
        gridCreditoCorporativoMODALIDADE.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoMODALIDADE.setEditorPopupHeight(400);
        gridCreditoCorporativoMODALIDADE.setEditorPopupWidth(400);
        gridCreditoCorporativoMODALIDADE.setEditorCharCase("ccNormal");
        gridCreditoCorporativoMODALIDADE.setEditorEnabled(false);
        gridCreditoCorporativoMODALIDADE.setEditorReadOnly(false);
        gridCreditoCorporativoMODALIDADE.setCheckedValue("S");
        gridCreditoCorporativoMODALIDADE.setUncheckedValue("N");
        gridCreditoCorporativoMODALIDADE.setHiperLink(false);
        gridCreditoCorporativoMODALIDADE.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoMODALIDADE.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoMODALIDADE.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoMODALIDADE.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoMODALIDADE.setEditorConstraintEnabled(false);
        gridCreditoCorporativoMODALIDADE.setEmpty(false);
        gridCreditoCorporativoMODALIDADE.setMobileOptsShowMobile(false);
        gridCreditoCorporativoMODALIDADE.setMobileOptsOrder(0);
        gridCreditoCorporativoMODALIDADE.setBoxSize(0);
        gridCreditoCorporativoMODALIDADE.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoMODALIDADE);
        TFGridColumn gridCreditoCorporativoLIMITE_NOVO = new TFGridColumn();
        gridCreditoCorporativoLIMITE_NOVO.setFieldName("LIMITE_NOVO");
        gridCreditoCorporativoLIMITE_NOVO.setTitleCaption("Limite Novo");
        gridCreditoCorporativoLIMITE_NOVO.setWidth(130);
        gridCreditoCorporativoLIMITE_NOVO.setVisible(true);
        gridCreditoCorporativoLIMITE_NOVO.setPrecision(0);
        gridCreditoCorporativoLIMITE_NOVO.setTextAlign("taRight");
        gridCreditoCorporativoLIMITE_NOVO.setFieldType("ftDecimal");
        gridCreditoCorporativoLIMITE_NOVO.setFlexRatio(0);
        gridCreditoCorporativoLIMITE_NOVO.setSort(false);
        gridCreditoCorporativoLIMITE_NOVO.setImageHeader(0);
        gridCreditoCorporativoLIMITE_NOVO.setWrap(false);
        gridCreditoCorporativoLIMITE_NOVO.setFlex(false);
        gridCreditoCorporativoLIMITE_NOVO.setCharCase("ccNormal");
        gridCreditoCorporativoLIMITE_NOVO.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoLIMITE_NOVO.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoLIMITE_NOVO.setShowLabel(true);
        gridCreditoCorporativoLIMITE_NOVO.setEditorEditType("etTFString");
        gridCreditoCorporativoLIMITE_NOVO.setEditorPrecision(0);
        gridCreditoCorporativoLIMITE_NOVO.setEditorMaxLength(100);
        gridCreditoCorporativoLIMITE_NOVO.setEditorLookupFilterKey(0);
        gridCreditoCorporativoLIMITE_NOVO.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoLIMITE_NOVO.setEditorPopupHeight(400);
        gridCreditoCorporativoLIMITE_NOVO.setEditorPopupWidth(400);
        gridCreditoCorporativoLIMITE_NOVO.setEditorCharCase("ccNormal");
        gridCreditoCorporativoLIMITE_NOVO.setEditorEnabled(false);
        gridCreditoCorporativoLIMITE_NOVO.setEditorReadOnly(false);
        gridCreditoCorporativoLIMITE_NOVO.setCheckedValue("S");
        gridCreditoCorporativoLIMITE_NOVO.setUncheckedValue("N");
        gridCreditoCorporativoLIMITE_NOVO.setHiperLink(false);
        gridCreditoCorporativoLIMITE_NOVO.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoLIMITE_NOVO.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoLIMITE_NOVO.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoLIMITE_NOVO.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoLIMITE_NOVO.setEditorConstraintEnabled(false);
        gridCreditoCorporativoLIMITE_NOVO.setEmpty(false);
        gridCreditoCorporativoLIMITE_NOVO.setMobileOptsShowMobile(false);
        gridCreditoCorporativoLIMITE_NOVO.setMobileOptsOrder(0);
        gridCreditoCorporativoLIMITE_NOVO.setBoxSize(0);
        gridCreditoCorporativoLIMITE_NOVO.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoLIMITE_NOVO);
        TFGridColumn gridCreditoCorporativoVALOR_CONSULTA = new TFGridColumn();
        gridCreditoCorporativoVALOR_CONSULTA.setFieldName("VALOR_CONSULTA");
        gridCreditoCorporativoVALOR_CONSULTA.setTitleCaption("Valor Consulta");
        gridCreditoCorporativoVALOR_CONSULTA.setWidth(130);
        gridCreditoCorporativoVALOR_CONSULTA.setVisible(true);
        gridCreditoCorporativoVALOR_CONSULTA.setPrecision(0);
        gridCreditoCorporativoVALOR_CONSULTA.setTextAlign("taRight");
        gridCreditoCorporativoVALOR_CONSULTA.setFieldType("ftDecimal");
        gridCreditoCorporativoVALOR_CONSULTA.setFlexRatio(0);
        gridCreditoCorporativoVALOR_CONSULTA.setSort(false);
        gridCreditoCorporativoVALOR_CONSULTA.setImageHeader(0);
        gridCreditoCorporativoVALOR_CONSULTA.setWrap(false);
        gridCreditoCorporativoVALOR_CONSULTA.setFlex(false);
        gridCreditoCorporativoVALOR_CONSULTA.setCharCase("ccNormal");
        gridCreditoCorporativoVALOR_CONSULTA.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoVALOR_CONSULTA.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoVALOR_CONSULTA.setShowLabel(true);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorEditType("etTFString");
        gridCreditoCorporativoVALOR_CONSULTA.setEditorPrecision(0);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorMaxLength(100);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorLookupFilterKey(0);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorPopupHeight(400);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorPopupWidth(400);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorCharCase("ccNormal");
        gridCreditoCorporativoVALOR_CONSULTA.setEditorEnabled(false);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorReadOnly(false);
        gridCreditoCorporativoVALOR_CONSULTA.setCheckedValue("S");
        gridCreditoCorporativoVALOR_CONSULTA.setUncheckedValue("N");
        gridCreditoCorporativoVALOR_CONSULTA.setHiperLink(false);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoVALOR_CONSULTA.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoVALOR_CONSULTA.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoVALOR_CONSULTA.setEditorConstraintEnabled(false);
        gridCreditoCorporativoVALOR_CONSULTA.setEmpty(false);
        gridCreditoCorporativoVALOR_CONSULTA.setMobileOptsShowMobile(false);
        gridCreditoCorporativoVALOR_CONSULTA.setMobileOptsOrder(0);
        gridCreditoCorporativoVALOR_CONSULTA.setBoxSize(0);
        gridCreditoCorporativoVALOR_CONSULTA.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoVALOR_CONSULTA);
        TFGridColumn gridCreditoCorporativoNUMERO_NOTA = new TFGridColumn();
        gridCreditoCorporativoNUMERO_NOTA.setFieldName("NUMERO_NOTA");
        gridCreditoCorporativoNUMERO_NOTA.setTitleCaption("N\u00FAmero Nota");
        gridCreditoCorporativoNUMERO_NOTA.setWidth(100);
        gridCreditoCorporativoNUMERO_NOTA.setVisible(true);
        gridCreditoCorporativoNUMERO_NOTA.setPrecision(0);
        gridCreditoCorporativoNUMERO_NOTA.setTextAlign("taLeft");
        gridCreditoCorporativoNUMERO_NOTA.setFieldType("ftString");
        gridCreditoCorporativoNUMERO_NOTA.setFlexRatio(0);
        gridCreditoCorporativoNUMERO_NOTA.setSort(false);
        gridCreditoCorporativoNUMERO_NOTA.setImageHeader(0);
        gridCreditoCorporativoNUMERO_NOTA.setWrap(false);
        gridCreditoCorporativoNUMERO_NOTA.setFlex(false);
        gridCreditoCorporativoNUMERO_NOTA.setCharCase("ccNormal");
        gridCreditoCorporativoNUMERO_NOTA.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoNUMERO_NOTA.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoNUMERO_NOTA.setShowLabel(true);
        gridCreditoCorporativoNUMERO_NOTA.setEditorEditType("etTFString");
        gridCreditoCorporativoNUMERO_NOTA.setEditorPrecision(0);
        gridCreditoCorporativoNUMERO_NOTA.setEditorMaxLength(100);
        gridCreditoCorporativoNUMERO_NOTA.setEditorLookupFilterKey(0);
        gridCreditoCorporativoNUMERO_NOTA.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoNUMERO_NOTA.setEditorPopupHeight(400);
        gridCreditoCorporativoNUMERO_NOTA.setEditorPopupWidth(400);
        gridCreditoCorporativoNUMERO_NOTA.setEditorCharCase("ccNormal");
        gridCreditoCorporativoNUMERO_NOTA.setEditorEnabled(false);
        gridCreditoCorporativoNUMERO_NOTA.setEditorReadOnly(false);
        gridCreditoCorporativoNUMERO_NOTA.setCheckedValue("S");
        gridCreditoCorporativoNUMERO_NOTA.setUncheckedValue("N");
        gridCreditoCorporativoNUMERO_NOTA.setHiperLink(false);
        gridCreditoCorporativoNUMERO_NOTA.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoNUMERO_NOTA.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoNUMERO_NOTA.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoNUMERO_NOTA.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoNUMERO_NOTA.setEditorConstraintEnabled(false);
        gridCreditoCorporativoNUMERO_NOTA.setEmpty(false);
        gridCreditoCorporativoNUMERO_NOTA.setMobileOptsShowMobile(false);
        gridCreditoCorporativoNUMERO_NOTA.setMobileOptsOrder(0);
        gridCreditoCorporativoNUMERO_NOTA.setBoxSize(0);
        gridCreditoCorporativoNUMERO_NOTA.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoNUMERO_NOTA);
        TFGridColumn gridCreditoCorporativoSERIE = new TFGridColumn();
        gridCreditoCorporativoSERIE.setFieldName("SERIE");
        gridCreditoCorporativoSERIE.setTitleCaption("S\u00E9rie");
        gridCreditoCorporativoSERIE.setWidth(100);
        gridCreditoCorporativoSERIE.setVisible(true);
        gridCreditoCorporativoSERIE.setPrecision(0);
        gridCreditoCorporativoSERIE.setTextAlign("taLeft");
        gridCreditoCorporativoSERIE.setFieldType("ftString");
        gridCreditoCorporativoSERIE.setFlexRatio(0);
        gridCreditoCorporativoSERIE.setSort(false);
        gridCreditoCorporativoSERIE.setImageHeader(0);
        gridCreditoCorporativoSERIE.setWrap(false);
        gridCreditoCorporativoSERIE.setFlex(false);
        gridCreditoCorporativoSERIE.setCharCase("ccNormal");
        gridCreditoCorporativoSERIE.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoSERIE.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoSERIE.setShowLabel(true);
        gridCreditoCorporativoSERIE.setEditorEditType("etTFString");
        gridCreditoCorporativoSERIE.setEditorPrecision(0);
        gridCreditoCorporativoSERIE.setEditorMaxLength(100);
        gridCreditoCorporativoSERIE.setEditorLookupFilterKey(0);
        gridCreditoCorporativoSERIE.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoSERIE.setEditorPopupHeight(400);
        gridCreditoCorporativoSERIE.setEditorPopupWidth(400);
        gridCreditoCorporativoSERIE.setEditorCharCase("ccNormal");
        gridCreditoCorporativoSERIE.setEditorEnabled(false);
        gridCreditoCorporativoSERIE.setEditorReadOnly(false);
        gridCreditoCorporativoSERIE.setCheckedValue("S");
        gridCreditoCorporativoSERIE.setUncheckedValue("N");
        gridCreditoCorporativoSERIE.setHiperLink(false);
        gridCreditoCorporativoSERIE.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoSERIE.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoSERIE.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoSERIE.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoSERIE.setEditorConstraintEnabled(false);
        gridCreditoCorporativoSERIE.setEmpty(false);
        gridCreditoCorporativoSERIE.setMobileOptsShowMobile(false);
        gridCreditoCorporativoSERIE.setMobileOptsOrder(0);
        gridCreditoCorporativoSERIE.setBoxSize(0);
        gridCreditoCorporativoSERIE.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoSERIE);
        TFGridColumn gridCreditoCorporativoCOD_CLIENTE = new TFGridColumn();
        gridCreditoCorporativoCOD_CLIENTE.setFieldName("COD_CLIENTE");
        gridCreditoCorporativoCOD_CLIENTE.setTitleCaption("C\u00F3d. Cliente");
        gridCreditoCorporativoCOD_CLIENTE.setWidth(40);
        gridCreditoCorporativoCOD_CLIENTE.setVisible(false);
        gridCreditoCorporativoCOD_CLIENTE.setPrecision(0);
        gridCreditoCorporativoCOD_CLIENTE.setTextAlign("taLeft");
        gridCreditoCorporativoCOD_CLIENTE.setFieldType("ftString");
        gridCreditoCorporativoCOD_CLIENTE.setFlexRatio(0);
        gridCreditoCorporativoCOD_CLIENTE.setSort(false);
        gridCreditoCorporativoCOD_CLIENTE.setImageHeader(0);
        gridCreditoCorporativoCOD_CLIENTE.setWrap(false);
        gridCreditoCorporativoCOD_CLIENTE.setFlex(false);
        gridCreditoCorporativoCOD_CLIENTE.setCharCase("ccNormal");
        gridCreditoCorporativoCOD_CLIENTE.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoCOD_CLIENTE.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoCOD_CLIENTE.setShowLabel(true);
        gridCreditoCorporativoCOD_CLIENTE.setEditorEditType("etTFString");
        gridCreditoCorporativoCOD_CLIENTE.setEditorPrecision(0);
        gridCreditoCorporativoCOD_CLIENTE.setEditorMaxLength(100);
        gridCreditoCorporativoCOD_CLIENTE.setEditorLookupFilterKey(0);
        gridCreditoCorporativoCOD_CLIENTE.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoCOD_CLIENTE.setEditorPopupHeight(400);
        gridCreditoCorporativoCOD_CLIENTE.setEditorPopupWidth(400);
        gridCreditoCorporativoCOD_CLIENTE.setEditorCharCase("ccNormal");
        gridCreditoCorporativoCOD_CLIENTE.setEditorEnabled(false);
        gridCreditoCorporativoCOD_CLIENTE.setEditorReadOnly(false);
        gridCreditoCorporativoCOD_CLIENTE.setCheckedValue("S");
        gridCreditoCorporativoCOD_CLIENTE.setUncheckedValue("N");
        gridCreditoCorporativoCOD_CLIENTE.setHiperLink(false);
        gridCreditoCorporativoCOD_CLIENTE.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoCOD_CLIENTE.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoCOD_CLIENTE.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoCOD_CLIENTE.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoCOD_CLIENTE.setEditorConstraintEnabled(false);
        gridCreditoCorporativoCOD_CLIENTE.setEmpty(false);
        gridCreditoCorporativoCOD_CLIENTE.setMobileOptsShowMobile(false);
        gridCreditoCorporativoCOD_CLIENTE.setMobileOptsOrder(0);
        gridCreditoCorporativoCOD_CLIENTE.setBoxSize(0);
        gridCreditoCorporativoCOD_CLIENTE.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoCOD_CLIENTE);
        TFGridColumn gridCreditoCorporativoCREDITO_CORPORATIVO = new TFGridColumn();
        gridCreditoCorporativoCREDITO_CORPORATIVO.setFieldName("CREDITO_CORPORATIVO");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setTitleCaption("Cr\u00E9dito Corporativo");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setWidth(40);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setVisible(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setPrecision(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setTextAlign("taLeft");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setFieldType("ftString");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setFlexRatio(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setSort(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setImageHeader(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setWrap(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setFlex(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setCharCase("ccNormal");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setShowLabel(true);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorEditType("etTFString");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorPrecision(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorMaxLength(100);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorLookupFilterKey(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorPopupHeight(400);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorPopupWidth(400);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorCharCase("ccNormal");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorEnabled(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorReadOnly(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setCheckedValue("S");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setUncheckedValue("N");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setHiperLink(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEditorConstraintEnabled(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setEmpty(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setMobileOptsShowMobile(false);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setMobileOptsOrder(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setBoxSize(0);
        gridCreditoCorporativoCREDITO_CORPORATIVO.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoCREDITO_CORPORATIVO);
        TFGridColumn gridCreditoCorporativoVALOR_LIBERADO_CC = new TFGridColumn();
        gridCreditoCorporativoVALOR_LIBERADO_CC.setFieldName("VALOR_LIBERADO_CC");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setTitleCaption("Valor Liberado Cc");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setWidth(40);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setVisible(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setPrecision(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setTextAlign("taLeft");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setFieldType("ftString");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setFlexRatio(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setSort(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setImageHeader(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setWrap(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setFlex(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setCharCase("ccNormal");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setShowLabel(true);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorEditType("etTFString");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorPrecision(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorMaxLength(100);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorLookupFilterKey(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorPopupHeight(400);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorPopupWidth(400);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorCharCase("ccNormal");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorEnabled(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorReadOnly(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setCheckedValue("S");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setUncheckedValue("N");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setHiperLink(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEditorConstraintEnabled(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setEmpty(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setMobileOptsShowMobile(false);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setMobileOptsOrder(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setBoxSize(0);
        gridCreditoCorporativoVALOR_LIBERADO_CC.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoVALOR_LIBERADO_CC);
        TFGridColumn gridCreditoCorporativoDATA_LIBERACAO_CC = new TFGridColumn();
        gridCreditoCorporativoDATA_LIBERACAO_CC.setFieldName("DATA_LIBERACAO_CC");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setTitleCaption("Data Libera\u00E7\u00E3o Cc");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setWidth(40);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setVisible(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setPrecision(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setTextAlign("taLeft");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setFieldType("ftString");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setFlexRatio(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setSort(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setImageHeader(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setWrap(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setFlex(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setCharCase("ccNormal");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setShowLabel(true);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorEditType("etTFString");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorPrecision(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorMaxLength(100);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorLookupFilterKey(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorPopupHeight(400);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorPopupWidth(400);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorCharCase("ccNormal");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorEnabled(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorReadOnly(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setCheckedValue("S");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setUncheckedValue("N");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setHiperLink(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEditorConstraintEnabled(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setEmpty(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setMobileOptsShowMobile(false);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setMobileOptsOrder(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setBoxSize(0);
        gridCreditoCorporativoDATA_LIBERACAO_CC.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoDATA_LIBERACAO_CC);
        TFGridColumn gridCreditoCorporativoDATA_UTILIZACAO_CC = new TFGridColumn();
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setFieldName("DATA_UTILIZACAO_CC");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setTitleCaption("Data Utiliza\u00E7\u00E3o Cc");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setWidth(40);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setVisible(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setPrecision(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setTextAlign("taLeft");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setFieldType("ftString");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setFlexRatio(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setSort(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setImageHeader(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setWrap(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setFlex(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setCharCase("ccNormal");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setShowLabel(true);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorEditType("etTFString");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorPrecision(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorMaxLength(100);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorLookupFilterKey(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorPopupHeight(400);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorPopupWidth(400);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorCharCase("ccNormal");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorEnabled(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorReadOnly(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setCheckedValue("S");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setUncheckedValue("N");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setHiperLink(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEditorConstraintEnabled(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setEmpty(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setMobileOptsShowMobile(false);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setMobileOptsOrder(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setBoxSize(0);
        gridCreditoCorporativoDATA_UTILIZACAO_CC.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoDATA_UTILIZACAO_CC);
        TFGridColumn gridCreditoCorporativoBLOQUEADO_CC = new TFGridColumn();
        gridCreditoCorporativoBLOQUEADO_CC.setFieldName("BLOQUEADO_CC");
        gridCreditoCorporativoBLOQUEADO_CC.setTitleCaption("Bloqueado Cc");
        gridCreditoCorporativoBLOQUEADO_CC.setWidth(40);
        gridCreditoCorporativoBLOQUEADO_CC.setVisible(false);
        gridCreditoCorporativoBLOQUEADO_CC.setPrecision(0);
        gridCreditoCorporativoBLOQUEADO_CC.setTextAlign("taLeft");
        gridCreditoCorporativoBLOQUEADO_CC.setFieldType("ftString");
        gridCreditoCorporativoBLOQUEADO_CC.setFlexRatio(0);
        gridCreditoCorporativoBLOQUEADO_CC.setSort(false);
        gridCreditoCorporativoBLOQUEADO_CC.setImageHeader(0);
        gridCreditoCorporativoBLOQUEADO_CC.setWrap(false);
        gridCreditoCorporativoBLOQUEADO_CC.setFlex(false);
        gridCreditoCorporativoBLOQUEADO_CC.setCharCase("ccNormal");
        gridCreditoCorporativoBLOQUEADO_CC.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoBLOQUEADO_CC.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoBLOQUEADO_CC.setShowLabel(true);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorEditType("etTFString");
        gridCreditoCorporativoBLOQUEADO_CC.setEditorPrecision(0);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorMaxLength(100);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorLookupFilterKey(0);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorPopupHeight(400);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorPopupWidth(400);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorCharCase("ccNormal");
        gridCreditoCorporativoBLOQUEADO_CC.setEditorEnabled(false);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorReadOnly(false);
        gridCreditoCorporativoBLOQUEADO_CC.setCheckedValue("S");
        gridCreditoCorporativoBLOQUEADO_CC.setUncheckedValue("N");
        gridCreditoCorporativoBLOQUEADO_CC.setHiperLink(false);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoBLOQUEADO_CC.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoBLOQUEADO_CC.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoBLOQUEADO_CC.setEditorConstraintEnabled(false);
        gridCreditoCorporativoBLOQUEADO_CC.setEmpty(false);
        gridCreditoCorporativoBLOQUEADO_CC.setMobileOptsShowMobile(false);
        gridCreditoCorporativoBLOQUEADO_CC.setMobileOptsOrder(0);
        gridCreditoCorporativoBLOQUEADO_CC.setBoxSize(0);
        gridCreditoCorporativoBLOQUEADO_CC.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoBLOQUEADO_CC);
        TFGridColumn gridCreditoCorporativoMOTIVO_CC = new TFGridColumn();
        gridCreditoCorporativoMOTIVO_CC.setFieldName("MOTIVO_CC");
        gridCreditoCorporativoMOTIVO_CC.setTitleCaption("Motivo Cc");
        gridCreditoCorporativoMOTIVO_CC.setWidth(40);
        gridCreditoCorporativoMOTIVO_CC.setVisible(false);
        gridCreditoCorporativoMOTIVO_CC.setPrecision(0);
        gridCreditoCorporativoMOTIVO_CC.setTextAlign("taLeft");
        gridCreditoCorporativoMOTIVO_CC.setFieldType("ftString");
        gridCreditoCorporativoMOTIVO_CC.setFlexRatio(0);
        gridCreditoCorporativoMOTIVO_CC.setSort(false);
        gridCreditoCorporativoMOTIVO_CC.setImageHeader(0);
        gridCreditoCorporativoMOTIVO_CC.setWrap(false);
        gridCreditoCorporativoMOTIVO_CC.setFlex(false);
        gridCreditoCorporativoMOTIVO_CC.setCharCase("ccNormal");
        gridCreditoCorporativoMOTIVO_CC.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoMOTIVO_CC.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoMOTIVO_CC.setShowLabel(true);
        gridCreditoCorporativoMOTIVO_CC.setEditorEditType("etTFString");
        gridCreditoCorporativoMOTIVO_CC.setEditorPrecision(0);
        gridCreditoCorporativoMOTIVO_CC.setEditorMaxLength(100);
        gridCreditoCorporativoMOTIVO_CC.setEditorLookupFilterKey(0);
        gridCreditoCorporativoMOTIVO_CC.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoMOTIVO_CC.setEditorPopupHeight(400);
        gridCreditoCorporativoMOTIVO_CC.setEditorPopupWidth(400);
        gridCreditoCorporativoMOTIVO_CC.setEditorCharCase("ccNormal");
        gridCreditoCorporativoMOTIVO_CC.setEditorEnabled(false);
        gridCreditoCorporativoMOTIVO_CC.setEditorReadOnly(false);
        gridCreditoCorporativoMOTIVO_CC.setCheckedValue("S");
        gridCreditoCorporativoMOTIVO_CC.setUncheckedValue("N");
        gridCreditoCorporativoMOTIVO_CC.setHiperLink(false);
        gridCreditoCorporativoMOTIVO_CC.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoMOTIVO_CC.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoMOTIVO_CC.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoMOTIVO_CC.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoMOTIVO_CC.setEditorConstraintEnabled(false);
        gridCreditoCorporativoMOTIVO_CC.setEmpty(false);
        gridCreditoCorporativoMOTIVO_CC.setMobileOptsShowMobile(false);
        gridCreditoCorporativoMOTIVO_CC.setMobileOptsOrder(0);
        gridCreditoCorporativoMOTIVO_CC.setBoxSize(0);
        gridCreditoCorporativoMOTIVO_CC.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoMOTIVO_CC);
        TFGridColumn gridCreditoCorporativoCOD_PEDIDO_WEB = new TFGridColumn();
        gridCreditoCorporativoCOD_PEDIDO_WEB.setFieldName("COD_PEDIDO_WEB");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setTitleCaption("C\u00F3d. Pedido Web");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setWidth(40);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setVisible(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setPrecision(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setTextAlign("taLeft");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setFieldType("ftString");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setFlexRatio(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setSort(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setImageHeader(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setWrap(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setFlex(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setCharCase("ccNormal");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setBlobConfigMimeType("bmtText");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setBlobConfigShowType("btImageViewer");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setShowLabel(true);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorEditType("etTFString");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorPrecision(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorMaxLength(100);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorLookupFilterKey(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorLookupFilterDesc(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorPopupHeight(400);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorPopupWidth(400);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorCharCase("ccNormal");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorEnabled(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorReadOnly(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setCheckedValue("S");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setUncheckedValue("N");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setHiperLink(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorConstraintCheckWhen("cwImmediate");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorConstraintCheckType("ctExpression");
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorConstraintFocusOnError(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorConstraintEnableUI(true);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEditorConstraintEnabled(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setEmpty(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setMobileOptsShowMobile(false);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setMobileOptsOrder(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setBoxSize(0);
        gridCreditoCorporativoCOD_PEDIDO_WEB.setImageSrcType("istSource");
        gridCreditoCorporativo.getColumns().add(gridCreditoCorporativoCOD_PEDIDO_WEB);
        vBoxCreditoCorporativoGridContainer.addChildren(gridCreditoCorporativo);
        gridCreditoCorporativo.applyProperties();
    }

    public TFVBox vBoxCreditoCorporativo2 = new TFVBox();

    private void init_vBoxCreditoCorporativo2() {
        vBoxCreditoCorporativo2.setName("vBoxCreditoCorporativo2");
        vBoxCreditoCorporativo2.setLeft(0);
        vBoxCreditoCorporativo2.setTop(359);
        vBoxCreditoCorporativo2.setWidth(949);
        vBoxCreditoCorporativo2.setHeight(159);
        vBoxCreditoCorporativo2.setAlign("alTop");
        vBoxCreditoCorporativo2.setBorderStyle("stNone");
        vBoxCreditoCorporativo2.setPaddingTop(0);
        vBoxCreditoCorporativo2.setPaddingLeft(5);
        vBoxCreditoCorporativo2.setPaddingRight(0);
        vBoxCreditoCorporativo2.setPaddingBottom(5);
        vBoxCreditoCorporativo2.setMarginTop(0);
        vBoxCreditoCorporativo2.setMarginLeft(0);
        vBoxCreditoCorporativo2.setMarginRight(0);
        vBoxCreditoCorporativo2.setMarginBottom(0);
        vBoxCreditoCorporativo2.setSpacing(1);
        vBoxCreditoCorporativo2.setFlexVflex("ftFalse");
        vBoxCreditoCorporativo2.setFlexHflex("ftFalse");
        vBoxCreditoCorporativo2.setScrollable(false);
        vBoxCreditoCorporativo2.setBoxShadowConfigHorizontalLength(10);
        vBoxCreditoCorporativo2.setBoxShadowConfigVerticalLength(10);
        vBoxCreditoCorporativo2.setBoxShadowConfigBlurRadius(5);
        vBoxCreditoCorporativo2.setBoxShadowConfigSpreadRadius(0);
        vBoxCreditoCorporativo2.setBoxShadowConfigShadowColor("clBlack");
        vBoxCreditoCorporativo2.setBoxShadowConfigOpacity(75);
        vBoxCreditoCorporativoContainer.addChildren(vBoxCreditoCorporativo2);
        vBoxCreditoCorporativo2.applyProperties();
    }

    public TFHBox hBoxCreditoCorporativoTop3 = new TFHBox();

    private void init_hBoxCreditoCorporativoTop3() {
        hBoxCreditoCorporativoTop3.setName("hBoxCreditoCorporativoTop3");
        hBoxCreditoCorporativoTop3.setLeft(0);
        hBoxCreditoCorporativoTop3.setTop(0);
        hBoxCreditoCorporativoTop3.setWidth(513);
        hBoxCreditoCorporativoTop3.setHeight(20);
        hBoxCreditoCorporativoTop3.setAlign("alTop");
        hBoxCreditoCorporativoTop3.setBorderStyle("stNone");
        hBoxCreditoCorporativoTop3.setPaddingTop(0);
        hBoxCreditoCorporativoTop3.setPaddingLeft(0);
        hBoxCreditoCorporativoTop3.setPaddingRight(0);
        hBoxCreditoCorporativoTop3.setPaddingBottom(0);
        hBoxCreditoCorporativoTop3.setMarginTop(0);
        hBoxCreditoCorporativoTop3.setMarginLeft(0);
        hBoxCreditoCorporativoTop3.setMarginRight(0);
        hBoxCreditoCorporativoTop3.setMarginBottom(0);
        hBoxCreditoCorporativoTop3.setSpacing(1);
        hBoxCreditoCorporativoTop3.setFlexVflex("ftFalse");
        hBoxCreditoCorporativoTop3.setFlexHflex("ftFalse");
        hBoxCreditoCorporativoTop3.setScrollable(false);
        hBoxCreditoCorporativoTop3.setBoxShadowConfigHorizontalLength(10);
        hBoxCreditoCorporativoTop3.setBoxShadowConfigVerticalLength(10);
        hBoxCreditoCorporativoTop3.setBoxShadowConfigBlurRadius(5);
        hBoxCreditoCorporativoTop3.setBoxShadowConfigSpreadRadius(0);
        hBoxCreditoCorporativoTop3.setBoxShadowConfigShadowColor("clBlack");
        hBoxCreditoCorporativoTop3.setBoxShadowConfigOpacity(75);
        hBoxCreditoCorporativoTop3.setVAlign("tvTop");
        vBoxCreditoCorporativo2.addChildren(hBoxCreditoCorporativoTop3);
        hBoxCreditoCorporativoTop3.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(300);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Retorno Cr\u00E9dito Corporativo ----------------------------------------");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        hBoxCreditoCorporativoTop3.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFMemo memoCreditoCorporativo = new TFMemo();

    private void init_memoCreditoCorporativo() {
        memoCreditoCorporativo.setName("memoCreditoCorporativo");
        memoCreditoCorporativo.setLeft(0);
        memoCreditoCorporativo.setTop(21);
        memoCreditoCorporativo.setWidth(940);
        memoCreditoCorporativo.setHeight(116);
        memoCreditoCorporativo.setAlign("alTop");
        memoCreditoCorporativo.setCharCase("ccNormal");
        memoCreditoCorporativo.setFontColor("clWindowText");
        memoCreditoCorporativo.setFontSize(-11);
        memoCreditoCorporativo.setFontName("Tahoma");
        memoCreditoCorporativo.setFontStyle("[]");
        memoCreditoCorporativo.setMaxlength(0);
        memoCreditoCorporativo.setReadOnly(true);
        memoCreditoCorporativo.setFieldName("RETORNO_CONSULTA");
        memoCreditoCorporativo.setTable(tbRapClienteLogsCc);
        memoCreditoCorporativo.setFlexVflex("ftFalse");
        memoCreditoCorporativo.setFlexHflex("ftFalse");
        memoCreditoCorporativo.setConstraintCheckWhen("cwImmediate");
        memoCreditoCorporativo.setConstraintCheckType("ctExpression");
        memoCreditoCorporativo.setConstraintFocusOnError(false);
        memoCreditoCorporativo.setConstraintEnableUI(true);
        memoCreditoCorporativo.setConstraintEnabled(false);
        memoCreditoCorporativo.setConstraintFormCheck(true);
        memoCreditoCorporativo.setRequired(false);
        vBoxCreditoCorporativo2.addChildren(memoCreditoCorporativo);
        memoCreditoCorporativo.applyProperties();
        addValidatable(memoCreditoCorporativo);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}