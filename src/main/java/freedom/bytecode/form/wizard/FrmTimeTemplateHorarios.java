package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmTimeTemplateHorarios extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.TimeTemplateHorariosRNA rn = null;

    public FrmTimeTemplateHorarios() {
        try {
            rn = (freedom.bytecode.rn.TimeTemplateHorariosRNA) getRN(freedom.bytecode.rn.wizard.TimeTemplateHorariosRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbTimeTemplateHorario();
        init_tbTimeTemplate();
        init_vboxPrincipal();
        init_hBoxBotoes();
        init_btnVoltar();
        init_btnNovo();
        init_btnExcluir();
        init_btnAlterar();
        init_btnSalvar();
        init_btnCancelar();
        init_pgControlTemplates();
        init_tabListagem();
        init_vboxPrincipal2();
        init_gridTemplates();
        init_gridTemplatesHorariosAmostra();
        init_tabCadastro();
        init_boxCadastroPrincipal();
        init_groupboxDeltalheTemplate();
        init_vboxDetalheTemplate();
        init_vboxDescricao();
        init_lblDescricao();
        init_edDescricao();
        init_gridTemplatesHorarios();
        init_vboxEditarHoarario();
        init_blPeriodoAtendimento();
        init_FHBox11();
        init_btnAlterarHorario();
        init_btnSalvarHorario();
        init_btnCancelarHorario();
        init_btnLimparHorario();
        init_hboxEditTimeTemplateHorario();
        init_vboxInicio();
        init_lblInicio();
        init_edInicial();
        init_vboxFim();
        init_lblFim();
        init_edFinal();
        init_FrmTimeTemplateHorarios();
    }

    public TIME_TEMPLATE_HORARIO_SQL tbTimeTemplateHorario;

    private void init_tbTimeTemplateHorario() {
        tbTimeTemplateHorario = rn.tbTimeTemplateHorario;
        tbTimeTemplateHorario.setName("tbTimeTemplateHorario");
        tbTimeTemplateHorario.setMaxRowCount(200);
        tbTimeTemplateHorario.setWKey("24509;24503");
        tbTimeTemplateHorario.setRatioBatchSize(20);
        getTables().put(tbTimeTemplateHorario, "tbTimeTemplateHorario");
        tbTimeTemplateHorario.applyProperties();
    }

    public TIME_TEMPLATE_SQL tbTimeTemplate;

    private void init_tbTimeTemplate() {
        tbTimeTemplate = rn.tbTimeTemplate;
        tbTimeTemplate.setName("tbTimeTemplate");
        tbTimeTemplate.setMaxRowCount(200);
        tbTimeTemplate.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbTimeTemplateAfterScroll(event);
            processarFlow("FrmTimeTemplateHorarios", "tbTimeTemplate", "OnAfterScroll");
        });
        tbTimeTemplate.setWKey("24509;24505");
        tbTimeTemplate.setRatioBatchSize(20);
        getTables().put(tbTimeTemplate, "tbTimeTemplate");
        tbTimeTemplate.applyProperties();
    }

    protected TFForm FrmTimeTemplateHorarios = this;
    private void init_FrmTimeTemplateHorarios() {
        FrmTimeTemplateHorarios.setName("FrmTimeTemplateHorarios");
        FrmTimeTemplateHorarios.setCaption("Time templates horarios");
        FrmTimeTemplateHorarios.setClientHeight(536);
        FrmTimeTemplateHorarios.setClientWidth(764);
        FrmTimeTemplateHorarios.setColor("clBtnFace");
        FrmTimeTemplateHorarios.setWOrigem("EhMain");
        FrmTimeTemplateHorarios.setWKey("24509");
        FrmTimeTemplateHorarios.setSpacing(0);
        FrmTimeTemplateHorarios.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(764);
        vboxPrincipal.setHeight(536);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(5);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(5);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(1);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmTimeTemplateHorarios.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(0);
        hBoxBotoes.setWidth(750);
        hBoxBotoes.setHeight(65);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(5);
        hBoxBotoes.setPaddingLeft(5);
        hBoxBotoes.setPaddingRight(5);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(3);
        hBoxBotoes.setFlexVflex("ftFalse");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vboxPrincipal.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnNovo = new TFButton();

    private void init_btnNovo() {
        btnNovo.setName("btnNovo");
        btnNovo.setLeft(60);
        btnNovo.setTop(0);
        btnNovo.setWidth(65);
        btnNovo.setHeight(56);
        btnNovo.setCaption("Novo");
        btnNovo.setFontColor("clWindowText");
        btnNovo.setFontSize(-11);
        btnNovo.setFontName("Tahoma");
        btnNovo.setFontStyle("[]");
        btnNovo.setLayout("blGlyphTop");
        btnNovo.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnNovo", "OnClick");
        });
        btnNovo.setImageId(6);
        btnNovo.setColor("clBtnFace");
        btnNovo.setAccess(false);
        btnNovo.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnNovo);
        btnNovo.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(125);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(56);
        btnExcluir.setCaption("Excluir");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.setVisible(false);
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(8);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFButton btnAlterar = new TFButton();

    private void init_btnAlterar() {
        btnAlterar.setName("btnAlterar");
        btnAlterar.setLeft(190);
        btnAlterar.setTop(0);
        btnAlterar.setWidth(65);
        btnAlterar.setHeight(56);
        btnAlterar.setCaption("Alterar");
        btnAlterar.setFontColor("clWindowText");
        btnAlterar.setFontSize(-11);
        btnAlterar.setFontName("Tahoma");
        btnAlterar.setFontStyle("[]");
        btnAlterar.setLayout("blGlyphTop");
        btnAlterar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnAlterar", "OnClick");
        });
        btnAlterar.setImageId(7);
        btnAlterar.setColor("clBtnFace");
        btnAlterar.setAccess(false);
        btnAlterar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAlterar);
        btnAlterar.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(255);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(65);
        btnSalvar.setHeight(56);
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-11);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnSalvar", "OnClick");
        });
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFButton btnCancelar = new TFButton();

    private void init_btnCancelar() {
        btnCancelar.setName("btnCancelar");
        btnCancelar.setLeft(320);
        btnCancelar.setTop(0);
        btnCancelar.setWidth(65);
        btnCancelar.setHeight(56);
        btnCancelar.setCaption("Cancelar");
        btnCancelar.setFontColor("clWindowText");
        btnCancelar.setFontSize(-11);
        btnCancelar.setFontName("Tahoma");
        btnCancelar.setFontStyle("[]");
        btnCancelar.setLayout("blGlyphTop");
        btnCancelar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnCancelar", "OnClick");
        });
        btnCancelar.setImageId(9);
        btnCancelar.setColor("clBtnFace");
        btnCancelar.setAccess(false);
        btnCancelar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnCancelar);
        btnCancelar.applyProperties();
    }

    public TFPageControl pgControlTemplates = new TFPageControl();

    private void init_pgControlTemplates() {
        pgControlTemplates.setName("pgControlTemplates");
        pgControlTemplates.setLeft(0);
        pgControlTemplates.setTop(66);
        pgControlTemplates.setWidth(748);
        pgControlTemplates.setHeight(461);
        pgControlTemplates.setTabPosition("tpTop");
        pgControlTemplates.setFlexVflex("ftTrue");
        pgControlTemplates.setFlexHflex("ftTrue");
        pgControlTemplates.setRenderStyle("rsTabbed");
        pgControlTemplates.applyProperties();
        vboxPrincipal.addChildren(pgControlTemplates);
    }

    public TFTabsheet tabListagem = new TFTabsheet();

    private void init_tabListagem() {
        tabListagem.setName("tabListagem");
        tabListagem.setCaption("Listagem");
        tabListagem.setVisible(true);
        tabListagem.setClosable(false);
        pgControlTemplates.addChildren(tabListagem);
        tabListagem.applyProperties();
    }

    public TFVBox vboxPrincipal2 = new TFVBox();

    private void init_vboxPrincipal2() {
        vboxPrincipal2.setName("vboxPrincipal2");
        vboxPrincipal2.setLeft(0);
        vboxPrincipal2.setTop(0);
        vboxPrincipal2.setWidth(740);
        vboxPrincipal2.setHeight(433);
        vboxPrincipal2.setAlign("alClient");
        vboxPrincipal2.setBorderStyle("stNone");
        vboxPrincipal2.setPaddingTop(5);
        vboxPrincipal2.setPaddingLeft(5);
        vboxPrincipal2.setPaddingRight(5);
        vboxPrincipal2.setPaddingBottom(5);
        vboxPrincipal2.setMarginTop(0);
        vboxPrincipal2.setMarginLeft(0);
        vboxPrincipal2.setMarginRight(0);
        vboxPrincipal2.setMarginBottom(0);
        vboxPrincipal2.setSpacing(5);
        vboxPrincipal2.setFlexVflex("ftTrue");
        vboxPrincipal2.setFlexHflex("ftTrue");
        vboxPrincipal2.setScrollable(false);
        vboxPrincipal2.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal2.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal2.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal2.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal2.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal2.setBoxShadowConfigOpacity(75);
        tabListagem.addChildren(vboxPrincipal2);
        vboxPrincipal2.applyProperties();
    }

    public TFGrid gridTemplates = new TFGrid();

    private void init_gridTemplates() {
        gridTemplates.setName("gridTemplates");
        gridTemplates.setLeft(0);
        gridTemplates.setTop(0);
        gridTemplates.setWidth(452);
        gridTemplates.setHeight(135);
        gridTemplates.setTable(tbTimeTemplate);
        gridTemplates.setFlexVflex("ftTrue");
        gridTemplates.setFlexHflex("ftTrue");
        gridTemplates.setPagingEnabled(false);
        gridTemplates.setFrozenColumns(0);
        gridTemplates.setShowFooter(false);
        gridTemplates.setShowHeader(true);
        gridTemplates.setMultiSelection(false);
        gridTemplates.setGroupingEnabled(false);
        gridTemplates.setGroupingExpanded(false);
        gridTemplates.setGroupingShowFooter(false);
        gridTemplates.setCrosstabEnabled(false);
        gridTemplates.setCrosstabGroupType("cgtConcat");
        gridTemplates.setEditionEnabled(false);
        gridTemplates.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_TEMPLATE");
        item0.setTitleCaption("Id");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(10);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridTemplates.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("DESCRICAO_INITCAP");
        item1.setTitleCaption("Descri\u00E7\u00E3o");
        item1.setWidth(270);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(90);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridTemplates.getColumns().add(item1);
        vboxPrincipal2.addChildren(gridTemplates);
        gridTemplates.applyProperties();
    }

    public TFGrid gridTemplatesHorariosAmostra = new TFGrid();

    private void init_gridTemplatesHorariosAmostra() {
        gridTemplatesHorariosAmostra.setName("gridTemplatesHorariosAmostra");
        gridTemplatesHorariosAmostra.setLeft(0);
        gridTemplatesHorariosAmostra.setTop(136);
        gridTemplatesHorariosAmostra.setWidth(454);
        gridTemplatesHorariosAmostra.setHeight(151);
        gridTemplatesHorariosAmostra.setTable(tbTimeTemplateHorario);
        gridTemplatesHorariosAmostra.setFlexVflex("ftTrue");
        gridTemplatesHorariosAmostra.setFlexHflex("ftTrue");
        gridTemplatesHorariosAmostra.setPagingEnabled(false);
        gridTemplatesHorariosAmostra.setFrozenColumns(0);
        gridTemplatesHorariosAmostra.setShowFooter(false);
        gridTemplatesHorariosAmostra.setShowHeader(true);
        gridTemplatesHorariosAmostra.setMultiSelection(false);
        gridTemplatesHorariosAmostra.setGroupingEnabled(false);
        gridTemplatesHorariosAmostra.setGroupingExpanded(false);
        gridTemplatesHorariosAmostra.setGroupingShowFooter(false);
        gridTemplatesHorariosAmostra.setCrosstabEnabled(false);
        gridTemplatesHorariosAmostra.setCrosstabGroupType("cgtConcat");
        gridTemplatesHorariosAmostra.setEditionEnabled(false);
        gridTemplatesHorariosAmostra.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_DIA_SEMANA");
        item0.setTitleCaption("Dia Semana");
        item0.setWidth(165);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(50);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridTemplatesHorariosAmostra.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("INICIO");
        item1.setTitleCaption("In\u00EDcio");
        item1.setWidth(66);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftDateTime");
        item1.setFlexRatio(25);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        TFMaskExpression item2 = new TFMaskExpression();
        item2.setExpression("*");
        item2.setEvalType("etExpression");
        item2.setMask("HH:mm");
        item2.setPadLength(0);
        item2.setPadDirection("pdNone");
        item2.setMaskType("mtDateTime");
        item1.getMasks().add(item2);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridTemplatesHorariosAmostra.getColumns().add(item1);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("FIM");
        item3.setTitleCaption("Fim");
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftDateTime");
        item3.setFlexRatio(25);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("HH:mm");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDateTime");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridTemplatesHorariosAmostra.getColumns().add(item3);
        vboxPrincipal2.addChildren(gridTemplatesHorariosAmostra);
        gridTemplatesHorariosAmostra.applyProperties();
    }

    public TFTabsheet tabCadastro = new TFTabsheet();

    private void init_tabCadastro() {
        tabCadastro.setName("tabCadastro");
        tabCadastro.setCaption("Cadastro");
        tabCadastro.setVisible(true);
        tabCadastro.setClosable(false);
        pgControlTemplates.addChildren(tabCadastro);
        tabCadastro.applyProperties();
    }

    public TFVBox boxCadastroPrincipal = new TFVBox();

    private void init_boxCadastroPrincipal() {
        boxCadastroPrincipal.setName("boxCadastroPrincipal");
        boxCadastroPrincipal.setLeft(0);
        boxCadastroPrincipal.setTop(0);
        boxCadastroPrincipal.setWidth(740);
        boxCadastroPrincipal.setHeight(433);
        boxCadastroPrincipal.setAlign("alClient");
        boxCadastroPrincipal.setBorderStyle("stNone");
        boxCadastroPrincipal.setPaddingTop(0);
        boxCadastroPrincipal.setPaddingLeft(0);
        boxCadastroPrincipal.setPaddingRight(0);
        boxCadastroPrincipal.setPaddingBottom(0);
        boxCadastroPrincipal.setMarginTop(0);
        boxCadastroPrincipal.setMarginLeft(0);
        boxCadastroPrincipal.setMarginRight(0);
        boxCadastroPrincipal.setMarginBottom(0);
        boxCadastroPrincipal.setSpacing(1);
        boxCadastroPrincipal.setFlexVflex("ftTrue");
        boxCadastroPrincipal.setFlexHflex("ftTrue");
        boxCadastroPrincipal.setScrollable(false);
        boxCadastroPrincipal.setBoxShadowConfigHorizontalLength(10);
        boxCadastroPrincipal.setBoxShadowConfigVerticalLength(10);
        boxCadastroPrincipal.setBoxShadowConfigBlurRadius(5);
        boxCadastroPrincipal.setBoxShadowConfigSpreadRadius(0);
        boxCadastroPrincipal.setBoxShadowConfigShadowColor("clBlack");
        boxCadastroPrincipal.setBoxShadowConfigOpacity(75);
        tabCadastro.addChildren(boxCadastroPrincipal);
        boxCadastroPrincipal.applyProperties();
    }

    public TFGroupbox groupboxDeltalheTemplate = new TFGroupbox();

    private void init_groupboxDeltalheTemplate() {
        groupboxDeltalheTemplate.setName("groupboxDeltalheTemplate");
        groupboxDeltalheTemplate.setLeft(0);
        groupboxDeltalheTemplate.setTop(0);
        groupboxDeltalheTemplate.setWidth(726);
        groupboxDeltalheTemplate.setHeight(427);
        groupboxDeltalheTemplate.setCaption("Detalhes Template");
        groupboxDeltalheTemplate.setFontColor("clWindowText");
        groupboxDeltalheTemplate.setFontSize(-11);
        groupboxDeltalheTemplate.setFontName("Tahoma");
        groupboxDeltalheTemplate.setFontStyle("[]");
        groupboxDeltalheTemplate.setFlexVflex("ftTrue");
        groupboxDeltalheTemplate.setFlexHflex("ftTrue");
        groupboxDeltalheTemplate.setScrollable(false);
        groupboxDeltalheTemplate.setClosable(false);
        groupboxDeltalheTemplate.setClosed(false);
        groupboxDeltalheTemplate.setOrient("coHorizontal");
        groupboxDeltalheTemplate.setStyle("grp3D");
        groupboxDeltalheTemplate.setHeaderImageId(0);
        boxCadastroPrincipal.addChildren(groupboxDeltalheTemplate);
        groupboxDeltalheTemplate.applyProperties();
    }

    public TFVBox vboxDetalheTemplate = new TFVBox();

    private void init_vboxDetalheTemplate() {
        vboxDetalheTemplate.setName("vboxDetalheTemplate");
        vboxDetalheTemplate.setLeft(2);
        vboxDetalheTemplate.setTop(15);
        vboxDetalheTemplate.setWidth(722);
        vboxDetalheTemplate.setHeight(410);
        vboxDetalheTemplate.setAlign("alClient");
        vboxDetalheTemplate.setBorderStyle("stNone");
        vboxDetalheTemplate.setPaddingTop(5);
        vboxDetalheTemplate.setPaddingLeft(5);
        vboxDetalheTemplate.setPaddingRight(5);
        vboxDetalheTemplate.setPaddingBottom(5);
        vboxDetalheTemplate.setMarginTop(0);
        vboxDetalheTemplate.setMarginLeft(0);
        vboxDetalheTemplate.setMarginRight(0);
        vboxDetalheTemplate.setMarginBottom(0);
        vboxDetalheTemplate.setSpacing(8);
        vboxDetalheTemplate.setFlexVflex("ftTrue");
        vboxDetalheTemplate.setFlexHflex("ftTrue");
        vboxDetalheTemplate.setScrollable(false);
        vboxDetalheTemplate.setBoxShadowConfigHorizontalLength(10);
        vboxDetalheTemplate.setBoxShadowConfigVerticalLength(10);
        vboxDetalheTemplate.setBoxShadowConfigBlurRadius(5);
        vboxDetalheTemplate.setBoxShadowConfigSpreadRadius(0);
        vboxDetalheTemplate.setBoxShadowConfigShadowColor("clBlack");
        vboxDetalheTemplate.setBoxShadowConfigOpacity(75);
        groupboxDeltalheTemplate.addChildren(vboxDetalheTemplate);
        vboxDetalheTemplate.applyProperties();
    }

    public TFVBox vboxDescricao = new TFVBox();

    private void init_vboxDescricao() {
        vboxDescricao.setName("vboxDescricao");
        vboxDescricao.setLeft(0);
        vboxDescricao.setTop(0);
        vboxDescricao.setWidth(654);
        vboxDescricao.setHeight(61);
        vboxDescricao.setBorderStyle("stNone");
        vboxDescricao.setPaddingTop(0);
        vboxDescricao.setPaddingLeft(0);
        vboxDescricao.setPaddingRight(0);
        vboxDescricao.setPaddingBottom(0);
        vboxDescricao.setMarginTop(0);
        vboxDescricao.setMarginLeft(0);
        vboxDescricao.setMarginRight(0);
        vboxDescricao.setMarginBottom(0);
        vboxDescricao.setSpacing(1);
        vboxDescricao.setFlexVflex("ftFalse");
        vboxDescricao.setFlexHflex("ftTrue");
        vboxDescricao.setScrollable(false);
        vboxDescricao.setBoxShadowConfigHorizontalLength(10);
        vboxDescricao.setBoxShadowConfigVerticalLength(10);
        vboxDescricao.setBoxShadowConfigBlurRadius(5);
        vboxDescricao.setBoxShadowConfigSpreadRadius(0);
        vboxDescricao.setBoxShadowConfigShadowColor("clBlack");
        vboxDescricao.setBoxShadowConfigOpacity(75);
        vboxDetalheTemplate.addChildren(vboxDescricao);
        vboxDescricao.applyProperties();
    }

    public TFLabel lblDescricao = new TFLabel();

    private void init_lblDescricao() {
        lblDescricao.setName("lblDescricao");
        lblDescricao.setLeft(0);
        lblDescricao.setTop(0);
        lblDescricao.setWidth(46);
        lblDescricao.setHeight(13);
        lblDescricao.setCaption("Descri\u00E7\u00E3o");
        lblDescricao.setFontColor("clWindowText");
        lblDescricao.setFontSize(-11);
        lblDescricao.setFontName("Tahoma");
        lblDescricao.setFontStyle("[]");
        lblDescricao.setVerticalAlignment("taVerticalCenter");
        lblDescricao.setWordBreak(false);
        vboxDescricao.addChildren(lblDescricao);
        lblDescricao.applyProperties();
    }

    public TFString edDescricao = new TFString();

    private void init_edDescricao() {
        edDescricao.setName("edDescricao");
        edDescricao.setLeft(0);
        edDescricao.setTop(14);
        edDescricao.setWidth(613);
        edDescricao.setHeight(24);
        edDescricao.setTable(tbTimeTemplate);
        edDescricao.setFieldName("DESCRICAO");
        edDescricao.setFlex(true);
        edDescricao.setRequired(true);
        edDescricao.setPrompt("Descri\u00E7\u00E3o");
        edDescricao.setConstraintCheckWhen("cwImmediate");
        edDescricao.setConstraintCheckType("ctExpression");
        edDescricao.setConstraintFocusOnError(false);
        edDescricao.setConstraintEnableUI(true);
        edDescricao.setConstraintEnabled(true);
        edDescricao.setConstraintFormCheck(true);
        edDescricao.setCharCase("ccNormal");
        edDescricao.setPwd(false);
        edDescricao.setMaxlength(100);
        edDescricao.setFontColor("clWindowText");
        edDescricao.setFontSize(-13);
        edDescricao.setFontName("Tahoma");
        edDescricao.setFontStyle("[]");
        edDescricao.setSaveLiteralCharacter(false);
        edDescricao.applyProperties();
        vboxDescricao.addChildren(edDescricao);
        addValidatable(edDescricao);
    }

    public TFGrid gridTemplatesHorarios = new TFGrid();

    private void init_gridTemplatesHorarios() {
        gridTemplatesHorarios.setName("gridTemplatesHorarios");
        gridTemplatesHorarios.setLeft(0);
        gridTemplatesHorarios.setTop(62);
        gridTemplatesHorarios.setWidth(617);
        gridTemplatesHorarios.setHeight(208);
        gridTemplatesHorarios.setTable(tbTimeTemplateHorario);
        gridTemplatesHorarios.setFlexVflex("ftTrue");
        gridTemplatesHorarios.setFlexHflex("ftTrue");
        gridTemplatesHorarios.setPagingEnabled(false);
        gridTemplatesHorarios.setFrozenColumns(0);
        gridTemplatesHorarios.setShowFooter(false);
        gridTemplatesHorarios.setShowHeader(true);
        gridTemplatesHorarios.setMultiSelection(false);
        gridTemplatesHorarios.setGroupingEnabled(false);
        gridTemplatesHorarios.setGroupingExpanded(false);
        gridTemplatesHorarios.setGroupingShowFooter(false);
        gridTemplatesHorarios.setCrosstabEnabled(false);
        gridTemplatesHorarios.setCrosstabGroupType("cgtConcat");
        gridTemplatesHorarios.setEditionEnabled(false);
        gridTemplatesHorarios.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("NOME_DIA_SEMANA");
        item0.setTitleCaption("Dia Semana");
        item0.setWidth(165);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(50);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridTemplatesHorarios.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("INICIO");
        item1.setTitleCaption("In\u00EDcio");
        item1.setWidth(66);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftDateTime");
        item1.setFlexRatio(25);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        TFMaskExpression item2 = new TFMaskExpression();
        item2.setExpression("*");
        item2.setEvalType("etExpression");
        item2.setMask("HH:mm");
        item2.setPadLength(0);
        item2.setPadDirection("pdNone");
        item2.setMaskType("mtDateTime");
        item1.getMasks().add(item2);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridTemplatesHorarios.getColumns().add(item1);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("FIM");
        item3.setTitleCaption("Fim");
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(25);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("HH:mm");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDateTime");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridTemplatesHorarios.getColumns().add(item3);
        vboxDetalheTemplate.addChildren(gridTemplatesHorarios);
        gridTemplatesHorarios.applyProperties();
    }

    public TFVBox vboxEditarHoarario = new TFVBox();

    private void init_vboxEditarHoarario() {
        vboxEditarHoarario.setName("vboxEditarHoarario");
        vboxEditarHoarario.setLeft(0);
        vboxEditarHoarario.setTop(271);
        vboxEditarHoarario.setWidth(686);
        vboxEditarHoarario.setHeight(129);
        vboxEditarHoarario.setBorderStyle("stNone");
        vboxEditarHoarario.setPaddingTop(0);
        vboxEditarHoarario.setPaddingLeft(0);
        vboxEditarHoarario.setPaddingRight(0);
        vboxEditarHoarario.setPaddingBottom(0);
        vboxEditarHoarario.setVisible(false);
        vboxEditarHoarario.setMarginTop(0);
        vboxEditarHoarario.setMarginLeft(0);
        vboxEditarHoarario.setMarginRight(0);
        vboxEditarHoarario.setMarginBottom(0);
        vboxEditarHoarario.setSpacing(1);
        vboxEditarHoarario.setFlexVflex("ftFalse");
        vboxEditarHoarario.setFlexHflex("ftTrue");
        vboxEditarHoarario.setScrollable(false);
        vboxEditarHoarario.setBoxShadowConfigHorizontalLength(10);
        vboxEditarHoarario.setBoxShadowConfigVerticalLength(10);
        vboxEditarHoarario.setBoxShadowConfigBlurRadius(5);
        vboxEditarHoarario.setBoxShadowConfigSpreadRadius(0);
        vboxEditarHoarario.setBoxShadowConfigShadowColor("clBlack");
        vboxEditarHoarario.setBoxShadowConfigOpacity(75);
        vboxDetalheTemplate.addChildren(vboxEditarHoarario);
        vboxEditarHoarario.applyProperties();
    }

    public TFLabel blPeriodoAtendimento = new TFLabel();

    private void init_blPeriodoAtendimento() {
        blPeriodoAtendimento.setName("blPeriodoAtendimento");
        blPeriodoAtendimento.setLeft(0);
        blPeriodoAtendimento.setTop(0);
        blPeriodoAtendimento.setWidth(178);
        blPeriodoAtendimento.setHeight(21);
        blPeriodoAtendimento.setCaption("Per\u00EDodo de Atendimento");
        blPeriodoAtendimento.setFontColor("clHighlight");
        blPeriodoAtendimento.setFontSize(-17);
        blPeriodoAtendimento.setFontName("Tahoma");
        blPeriodoAtendimento.setFontStyle("[]");
        blPeriodoAtendimento.setVerticalAlignment("taVerticalCenter");
        blPeriodoAtendimento.setWordBreak(false);
        vboxEditarHoarario.addChildren(blPeriodoAtendimento);
        blPeriodoAtendimento.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(22);
        FHBox11.setWidth(438);
        FHBox11.setHeight(41);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vboxEditarHoarario.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnAlterarHorario = new TFButton();

    private void init_btnAlterarHorario() {
        btnAlterarHorario.setName("btnAlterarHorario");
        btnAlterarHorario.setLeft(0);
        btnAlterarHorario.setTop(0);
        btnAlterarHorario.setWidth(48);
        btnAlterarHorario.setHeight(35);
        btnAlterarHorario.setHint("Alterar periodo atendimento");
        btnAlterarHorario.setFontColor("clWindowText");
        btnAlterarHorario.setFontSize(-11);
        btnAlterarHorario.setFontName("Tahoma");
        btnAlterarHorario.setFontStyle("[]");
        btnAlterarHorario.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarHorarioClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnAlterarHorario", "OnClick");
        });
        btnAlterarHorario.setImageId(7);
        btnAlterarHorario.setColor("clBtnFace");
        btnAlterarHorario.setAccess(false);
        btnAlterarHorario.setIconReverseDirection(false);
        FHBox11.addChildren(btnAlterarHorario);
        btnAlterarHorario.applyProperties();
    }

    public TFButton btnSalvarHorario = new TFButton();

    private void init_btnSalvarHorario() {
        btnSalvarHorario.setName("btnSalvarHorario");
        btnSalvarHorario.setLeft(48);
        btnSalvarHorario.setTop(0);
        btnSalvarHorario.setWidth(48);
        btnSalvarHorario.setHeight(35);
        btnSalvarHorario.setHint("Salvar periodo atendimento");
        btnSalvarHorario.setFontColor("clWindowText");
        btnSalvarHorario.setFontSize(-11);
        btnSalvarHorario.setFontName("Tahoma");
        btnSalvarHorario.setFontStyle("[]");
        btnSalvarHorario.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarHorarioClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnSalvarHorario", "OnClick");
        });
        btnSalvarHorario.setImageId(4);
        btnSalvarHorario.setColor("clBtnFace");
        btnSalvarHorario.setAccess(false);
        btnSalvarHorario.setIconReverseDirection(false);
        FHBox11.addChildren(btnSalvarHorario);
        btnSalvarHorario.applyProperties();
    }

    public TFButton btnCancelarHorario = new TFButton();

    private void init_btnCancelarHorario() {
        btnCancelarHorario.setName("btnCancelarHorario");
        btnCancelarHorario.setLeft(96);
        btnCancelarHorario.setTop(0);
        btnCancelarHorario.setWidth(48);
        btnCancelarHorario.setHeight(35);
        btnCancelarHorario.setHint("Cancelar pedido antendimento");
        btnCancelarHorario.setFontColor("clWindowText");
        btnCancelarHorario.setFontSize(-11);
        btnCancelarHorario.setFontName("Tahoma");
        btnCancelarHorario.setFontStyle("[]");
        btnCancelarHorario.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelarHorarioClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnCancelarHorario", "OnClick");
        });
        btnCancelarHorario.setImageId(9);
        btnCancelarHorario.setColor("clBtnFace");
        btnCancelarHorario.setAccess(false);
        btnCancelarHorario.setIconReverseDirection(false);
        FHBox11.addChildren(btnCancelarHorario);
        btnCancelarHorario.applyProperties();
    }

    public TFButton btnLimparHorario = new TFButton();

    private void init_btnLimparHorario() {
        btnLimparHorario.setName("btnLimparHorario");
        btnLimparHorario.setLeft(144);
        btnLimparHorario.setTop(0);
        btnLimparHorario.setWidth(48);
        btnLimparHorario.setHeight(35);
        btnLimparHorario.setHint("Limpar horarios");
        btnLimparHorario.setFontColor("clWindowText");
        btnLimparHorario.setFontSize(-11);
        btnLimparHorario.setFontName("Tahoma");
        btnLimparHorario.setFontStyle("[]");
        btnLimparHorario.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimparHorarioClick(event);
            processarFlow("FrmTimeTemplateHorarios", "btnLimparHorario", "OnClick");
        });
        btnLimparHorario.setImageId(4600235);
        btnLimparHorario.setColor("clBtnFace");
        btnLimparHorario.setAccess(false);
        btnLimparHorario.setIconReverseDirection(false);
        FHBox11.addChildren(btnLimparHorario);
        btnLimparHorario.applyProperties();
    }

    public TFHBox hboxEditTimeTemplateHorario = new TFHBox();

    private void init_hboxEditTimeTemplateHorario() {
        hboxEditTimeTemplateHorario.setName("hboxEditTimeTemplateHorario");
        hboxEditTimeTemplateHorario.setLeft(0);
        hboxEditTimeTemplateHorario.setTop(64);
        hboxEditTimeTemplateHorario.setWidth(654);
        hboxEditTimeTemplateHorario.setHeight(61);
        hboxEditTimeTemplateHorario.setBorderStyle("stSingleLine");
        hboxEditTimeTemplateHorario.setPaddingTop(0);
        hboxEditTimeTemplateHorario.setPaddingLeft(5);
        hboxEditTimeTemplateHorario.setPaddingRight(5);
        hboxEditTimeTemplateHorario.setPaddingBottom(0);
        hboxEditTimeTemplateHorario.setMarginTop(0);
        hboxEditTimeTemplateHorario.setMarginLeft(0);
        hboxEditTimeTemplateHorario.setMarginRight(0);
        hboxEditTimeTemplateHorario.setMarginBottom(0);
        hboxEditTimeTemplateHorario.setSpacing(1);
        hboxEditTimeTemplateHorario.setFlexVflex("ftFalse");
        hboxEditTimeTemplateHorario.setFlexHflex("ftTrue");
        hboxEditTimeTemplateHorario.setScrollable(false);
        hboxEditTimeTemplateHorario.setBoxShadowConfigHorizontalLength(10);
        hboxEditTimeTemplateHorario.setBoxShadowConfigVerticalLength(10);
        hboxEditTimeTemplateHorario.setBoxShadowConfigBlurRadius(5);
        hboxEditTimeTemplateHorario.setBoxShadowConfigSpreadRadius(0);
        hboxEditTimeTemplateHorario.setBoxShadowConfigShadowColor("clBlack");
        hboxEditTimeTemplateHorario.setBoxShadowConfigOpacity(75);
        hboxEditTimeTemplateHorario.setVAlign("tvTop");
        vboxEditarHoarario.addChildren(hboxEditTimeTemplateHorario);
        hboxEditTimeTemplateHorario.applyProperties();
    }

    public TFVBox vboxInicio = new TFVBox();

    private void init_vboxInicio() {
        vboxInicio.setName("vboxInicio");
        vboxInicio.setLeft(0);
        vboxInicio.setTop(0);
        vboxInicio.setWidth(118);
        vboxInicio.setHeight(56);
        vboxInicio.setBorderStyle("stNone");
        vboxInicio.setPaddingTop(0);
        vboxInicio.setPaddingLeft(0);
        vboxInicio.setPaddingRight(0);
        vboxInicio.setPaddingBottom(0);
        vboxInicio.setMarginTop(0);
        vboxInicio.setMarginLeft(0);
        vboxInicio.setMarginRight(0);
        vboxInicio.setMarginBottom(0);
        vboxInicio.setSpacing(1);
        vboxInicio.setFlexVflex("ftFalse");
        vboxInicio.setFlexHflex("ftFalse");
        vboxInicio.setScrollable(false);
        vboxInicio.setBoxShadowConfigHorizontalLength(10);
        vboxInicio.setBoxShadowConfigVerticalLength(10);
        vboxInicio.setBoxShadowConfigBlurRadius(5);
        vboxInicio.setBoxShadowConfigSpreadRadius(0);
        vboxInicio.setBoxShadowConfigShadowColor("clBlack");
        vboxInicio.setBoxShadowConfigOpacity(75);
        hboxEditTimeTemplateHorario.addChildren(vboxInicio);
        vboxInicio.applyProperties();
    }

    public TFLabel lblInicio = new TFLabel();

    private void init_lblInicio() {
        lblInicio.setName("lblInicio");
        lblInicio.setLeft(0);
        lblInicio.setTop(0);
        lblInicio.setWidth(27);
        lblInicio.setHeight(13);
        lblInicio.setCaption("Inicial");
        lblInicio.setFontColor("clWindowText");
        lblInicio.setFontSize(-11);
        lblInicio.setFontName("Tahoma");
        lblInicio.setFontStyle("[]");
        lblInicio.setVerticalAlignment("taVerticalCenter");
        lblInicio.setWordBreak(false);
        vboxInicio.addChildren(lblInicio);
        lblInicio.applyProperties();
    }

    public TFTime edInicial = new TFTime();

    private void init_edInicial() {
        edInicial.setName("edInicial");
        edInicial.setLeft(0);
        edInicial.setTop(14);
        edInicial.setWidth(108);
        edInicial.setHeight(24);
        edInicial.setTable(tbTimeTemplateHorario);
        edInicial.setFieldName("INICIO");
        edInicial.setFlex(false);
        edInicial.setRequired(false);
        edInicial.setPrompt("Inicial");
        edInicial.setConstraintCheckWhen("cwImmediate");
        edInicial.setConstraintCheckType("ctExpression");
        edInicial.setConstraintFocusOnError(false);
        edInicial.setConstraintEnableUI(true);
        edInicial.setConstraintEnabled(false);
        edInicial.setConstraintFormCheck(true);
        edInicial.setFormat("HH:mm");
        edInicial.setFontColor("clWindowText");
        edInicial.setFontSize(-13);
        edInicial.setFontName("Tahoma");
        edInicial.setFontStyle("[]");
        vboxInicio.addChildren(edInicial);
        edInicial.applyProperties();
        addValidatable(edInicial);
    }

    public TFVBox vboxFim = new TFVBox();

    private void init_vboxFim() {
        vboxFim.setName("vboxFim");
        vboxFim.setLeft(118);
        vboxFim.setTop(0);
        vboxFim.setWidth(118);
        vboxFim.setHeight(56);
        vboxFim.setBorderStyle("stNone");
        vboxFim.setPaddingTop(0);
        vboxFim.setPaddingLeft(0);
        vboxFim.setPaddingRight(0);
        vboxFim.setPaddingBottom(0);
        vboxFim.setMarginTop(0);
        vboxFim.setMarginLeft(0);
        vboxFim.setMarginRight(0);
        vboxFim.setMarginBottom(0);
        vboxFim.setSpacing(1);
        vboxFim.setFlexVflex("ftFalse");
        vboxFim.setFlexHflex("ftFalse");
        vboxFim.setScrollable(false);
        vboxFim.setBoxShadowConfigHorizontalLength(10);
        vboxFim.setBoxShadowConfigVerticalLength(10);
        vboxFim.setBoxShadowConfigBlurRadius(5);
        vboxFim.setBoxShadowConfigSpreadRadius(0);
        vboxFim.setBoxShadowConfigShadowColor("clBlack");
        vboxFim.setBoxShadowConfigOpacity(75);
        hboxEditTimeTemplateHorario.addChildren(vboxFim);
        vboxFim.applyProperties();
    }

    public TFLabel lblFim = new TFLabel();

    private void init_lblFim() {
        lblFim.setName("lblFim");
        lblFim.setLeft(0);
        lblFim.setTop(0);
        lblFim.setWidth(22);
        lblFim.setHeight(13);
        lblFim.setCaption("Final");
        lblFim.setFontColor("clWindowText");
        lblFim.setFontSize(-11);
        lblFim.setFontName("Tahoma");
        lblFim.setFontStyle("[]");
        lblFim.setVerticalAlignment("taVerticalCenter");
        lblFim.setWordBreak(false);
        vboxFim.addChildren(lblFim);
        lblFim.applyProperties();
    }

    public TFTime edFinal = new TFTime();

    private void init_edFinal() {
        edFinal.setName("edFinal");
        edFinal.setLeft(0);
        edFinal.setTop(14);
        edFinal.setWidth(108);
        edFinal.setHeight(24);
        edFinal.setTable(tbTimeTemplateHorario);
        edFinal.setFieldName("FIM");
        edFinal.setFlex(false);
        edFinal.setRequired(false);
        edFinal.setPrompt("Final");
        edFinal.setConstraintCheckWhen("cwImmediate");
        edFinal.setConstraintCheckType("ctExpression");
        edFinal.setConstraintFocusOnError(false);
        edFinal.setConstraintEnableUI(true);
        edFinal.setConstraintEnabled(false);
        edFinal.setConstraintFormCheck(true);
        edFinal.setFormat("HH:mm");
        edFinal.setFontColor("clWindowText");
        edFinal.setFontSize(-13);
        edFinal.setFontName("Tahoma");
        edFinal.setFontStyle("[]");
        vboxFim.addChildren(edFinal);
        edFinal.applyProperties();
        addValidatable(edFinal);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClick(final Event<Object> event) {
        if (btnNovo.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovo");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClick(final Event<Object> event) {
        if (btnAlterar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarClick(final Event<Object> event) {
        if (btnCancelar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarHorarioClick(final Event<Object> event) {
        if (btnAlterarHorario.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarHorario");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarHorarioClick(final Event<Object> event) {
        if (btnSalvarHorario.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvarHorario");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnCancelarHorarioClick(final Event<Object> event) {
        if (btnCancelarHorario.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancelarHorario");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnLimparHorarioClick(final Event<Object> event) {
        if (btnLimparHorario.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimparHorario");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void tbTimeTemplateAfterScroll(final Event<Object> event);

}