package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmPesquisaCliente extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.PesquisaClienteRNA rn = null;

    public FrmPesquisaCliente() {
        try {
            rn = (freedom.bytecode.rn.PesquisaClienteRNA) getRN(freedom.bytecode.rn.wizard.PesquisaClienteRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbLeadsConsultaClientes();
        init_tbLeadsEnderecoCliente();
        init_tbCrmpartsExisteEvento();
        init_tbListFoneCliente();
        init_tbClientesDescontos();
        init_tbCrmpartsGridClienteEspecial();
        init_FRadioGroup();
        init_hboxMenuBotoes();
        init_btnVoltar();
        init_btnPesquisar();
        init_btnAceitar();
        init_btnNovoCliente();
        init_btnAlterarCliente();
        init_btnExcluirCliente();
        init_btnAlterarFoneEmail();
        init_btnLimite();
        init_btnFlagsEspeciais();
        init_HboxSeparadorBotoesPesquisa();
        init_hBoxPesquisaCliente();
        init_edtPesquisarCliente();
        init_vBoxGrid();
        init_hbCliente();
        init_hbGridClientes();
        init_HboxExpacoGrid();
        init_lblFaturamento();
        init_grdClientes();
        init_vboxTelefone();
        init_hBoxQualMelhorFone();
        init_lblQualMelhorFone();
        init_vBoxSeparadorSelecioneFone();
        init_FIconClassEditarCliente();
        init_grdFoneCliente();
        init_vBoxGridEnderecoFaturamento();
        init_hBoxLabelEnderecoFaturamento();
        init_lblEnderecoFaturamento();
        init_vBoxFinalEnderecoFaturamento();
        init_iconClassEditarEndereco();
        init_grdEndereco();
        init_FrmPesquisaCliente();
    }

    public LEADS_CONSULTA_CLIENTES tbLeadsConsultaClientes;

    private void init_tbLeadsConsultaClientes() {
        tbLeadsConsultaClientes = rn.tbLeadsConsultaClientes;
        tbLeadsConsultaClientes.setName("tbLeadsConsultaClientes");
        tbLeadsConsultaClientes.setMaxRowCount(1000);
        tbLeadsConsultaClientes.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsConsultaClientesAfterScroll(event);
            processarFlow("FrmPesquisaCliente", "tbLeadsConsultaClientes", "OnAfterScroll");
        });
        tbLeadsConsultaClientes.setWKey("7000122;70001");
        tbLeadsConsultaClientes.setRatioBatchSize(20);
        getTables().put(tbLeadsConsultaClientes, "tbLeadsConsultaClientes");
        tbLeadsConsultaClientes.applyProperties();
    }

    public LEADS_ENDERECO_CLIENTE tbLeadsEnderecoCliente;

    private void init_tbLeadsEnderecoCliente() {
        tbLeadsEnderecoCliente = rn.tbLeadsEnderecoCliente;
        tbLeadsEnderecoCliente.setName("tbLeadsEnderecoCliente");
        TFTableField item6 = new TFTableField();
        item6.setName("CHECKED");
        item6.setCalculated(true);
        item6.setUpdatable(false);
        item6.setPrimaryKey(false);
        item6.setFieldType("ftString");
        item6.setJSONConfigNullOnEmpty(false);
        item6.setCaption("CHECKED");
        tbLeadsEnderecoCliente.getFieldDefs().add(item6);
        tbLeadsEnderecoCliente.setMaxRowCount(200);
        tbLeadsEnderecoCliente.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsEnderecoClienteAfterScroll(event);
            processarFlow("FrmPesquisaCliente", "tbLeadsEnderecoCliente", "OnAfterScroll");
        });
        tbLeadsEnderecoCliente.addEventListener("onBeforeScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsEnderecoClienteBeforeScroll(event);
            processarFlow("FrmPesquisaCliente", "tbLeadsEnderecoCliente", "OnBeforeScroll");
        });
        tbLeadsEnderecoCliente.setWKey("7000122;70002");
        tbLeadsEnderecoCliente.setRatioBatchSize(20);
        getTables().put(tbLeadsEnderecoCliente, "tbLeadsEnderecoCliente");
        tbLeadsEnderecoCliente.applyProperties();
    }

    public CRMPARTS_EXISTE_EVENTO tbCrmpartsExisteEvento;

    private void init_tbCrmpartsExisteEvento() {
        tbCrmpartsExisteEvento = rn.tbCrmpartsExisteEvento;
        tbCrmpartsExisteEvento.setName("tbCrmpartsExisteEvento");
        tbCrmpartsExisteEvento.setMaxRowCount(200);
        tbCrmpartsExisteEvento.setWKey("7000122;70004");
        tbCrmpartsExisteEvento.setRatioBatchSize(20);
        getTables().put(tbCrmpartsExisteEvento, "tbCrmpartsExisteEvento");
        tbCrmpartsExisteEvento.applyProperties();
    }

    public DUAL tbListFoneCliente;

    private void init_tbListFoneCliente() {
        tbListFoneCliente = rn.tbListFoneCliente;
        tbListFoneCliente.setName("tbListFoneCliente");
        TFTableField item0 = new TFTableField();
        item0.setName("TIPO_FONE");
        item0.setCalculated(true);
        item0.setUpdatable(false);
        item0.setPrimaryKey(false);
        item0.setFieldType("ftString");
        item0.setJSONConfigNullOnEmpty(false);
        item0.setCaption("TIPO_FONE");
        tbListFoneCliente.getFieldDefs().add(item0);
        TFTableField item1 = new TFTableField();
        item1.setName("FONE");
        item1.setCalculated(true);
        item1.setUpdatable(false);
        item1.setPrimaryKey(false);
        item1.setFieldType("ftString");
        item1.setJSONConfigNullOnEmpty(false);
        item1.setCaption("FONE");
        tbListFoneCliente.getFieldDefs().add(item1);
        tbListFoneCliente.setMaxRowCount(200);
        tbListFoneCliente.setWKey("7000122;70006");
        tbListFoneCliente.setRatioBatchSize(20);
        getTables().put(tbListFoneCliente, "tbListFoneCliente");
        tbListFoneCliente.applyProperties();
    }

    public CLIENTES_DESCONTOS tbClientesDescontos;

    private void init_tbClientesDescontos() {
        tbClientesDescontos = rn.tbClientesDescontos;
        tbClientesDescontos.setName("tbClientesDescontos");
        tbClientesDescontos.setMaxRowCount(200);
        tbClientesDescontos.setWKey("7000122;31001");
        tbClientesDescontos.setRatioBatchSize(20);
        getTables().put(tbClientesDescontos, "tbClientesDescontos");
        tbClientesDescontos.applyProperties();
    }

    public CRMPARTS_GRID_CLIENTE_ESPECIAL tbCrmpartsGridClienteEspecial;

    private void init_tbCrmpartsGridClienteEspecial() {
        tbCrmpartsGridClienteEspecial = rn.tbCrmpartsGridClienteEspecial;
        tbCrmpartsGridClienteEspecial.setName("tbCrmpartsGridClienteEspecial");
        tbCrmpartsGridClienteEspecial.setMaxRowCount(200);
        tbCrmpartsGridClienteEspecial.setWKey("7000122;53002");
        tbCrmpartsGridClienteEspecial.setRatioBatchSize(20);
        getTables().put(tbCrmpartsGridClienteEspecial, "tbCrmpartsGridClienteEspecial");
        tbCrmpartsGridClienteEspecial.applyProperties();
    }

    public TFRadioGroup FRadioGroup = new TFRadioGroup();

    private void init_FRadioGroup() {
        FRadioGroup.setName("FRadioGroup");
        FrmPesquisaCliente.addChildren(FRadioGroup);
        FRadioGroup.applyProperties();
    }

    protected TFForm FrmPesquisaCliente = this;
    private void init_FrmPesquisaCliente() {
        FrmPesquisaCliente.setName("FrmPesquisaCliente");
        FrmPesquisaCliente.setCaption("Pesquisa Cliente");
        FrmPesquisaCliente.setClientHeight(604);
        FrmPesquisaCliente.setClientWidth(698);
        FrmPesquisaCliente.setColor("clBtnFace");
        FrmPesquisaCliente.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmPesquisaCliente", "FrmPesquisaCliente", "OnCreate");
        });
        FrmPesquisaCliente.setWOrigem("EhMain");
        FrmPesquisaCliente.setWKey("7000122");
        FrmPesquisaCliente.setSpacing(0);
        FrmPesquisaCliente.applyProperties();
    }

    public TFHBox hboxMenuBotoes = new TFHBox();

    private void init_hboxMenuBotoes() {
        hboxMenuBotoes.setName("hboxMenuBotoes");
        hboxMenuBotoes.setLeft(0);
        hboxMenuBotoes.setTop(0);
        hboxMenuBotoes.setWidth(698);
        hboxMenuBotoes.setHeight(61);
        hboxMenuBotoes.setAlign("alTop");
        hboxMenuBotoes.setBorderStyle("stNone");
        hboxMenuBotoes.setPaddingTop(3);
        hboxMenuBotoes.setPaddingLeft(3);
        hboxMenuBotoes.setPaddingRight(3);
        hboxMenuBotoes.setPaddingBottom(0);
        hboxMenuBotoes.setMarginTop(0);
        hboxMenuBotoes.setMarginLeft(0);
        hboxMenuBotoes.setMarginRight(0);
        hboxMenuBotoes.setMarginBottom(0);
        hboxMenuBotoes.setSpacing(5);
        hboxMenuBotoes.setFlexVflex("ftFalse");
        hboxMenuBotoes.setFlexHflex("ftTrue");
        hboxMenuBotoes.setScrollable(false);
        hboxMenuBotoes.setBoxShadowConfigHorizontalLength(10);
        hboxMenuBotoes.setBoxShadowConfigVerticalLength(10);
        hboxMenuBotoes.setBoxShadowConfigBlurRadius(5);
        hboxMenuBotoes.setBoxShadowConfigSpreadRadius(0);
        hboxMenuBotoes.setBoxShadowConfigShadowColor("clBlack");
        hboxMenuBotoes.setBoxShadowConfigOpacity(75);
        hboxMenuBotoes.setVAlign("tvTop");
        FrmPesquisaCliente.addChildren(hboxMenuBotoes);
        hboxMenuBotoes.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setUploadMime("image/*");
        btnVoltar.setLeft(0);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-11);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmPesquisaCliente", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setUploadMime("image/*");
        btnPesquisar.setLeft(60);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(60);
        btnPesquisar.setHeight(56);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setAlign("alLeft");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmPesquisaCliente", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(27001);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setUploadMime("image/*");
        btnAceitar.setLeft(120);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmPesquisaCliente", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFButton btnNovoCliente = new TFButton();

    private void init_btnNovoCliente() {
        btnNovoCliente.setName("btnNovoCliente");
        btnNovoCliente.setUploadMime("image/*");
        btnNovoCliente.setLeft(180);
        btnNovoCliente.setTop(0);
        btnNovoCliente.setWidth(60);
        btnNovoCliente.setHeight(56);
        btnNovoCliente.setHint("Cadastrar Novo Cliente");
        btnNovoCliente.setAlign("alLeft");
        btnNovoCliente.setCaption("Novo");
        btnNovoCliente.setFontColor("clWindowText");
        btnNovoCliente.setFontSize(-11);
        btnNovoCliente.setFontName("Tahoma");
        btnNovoCliente.setFontStyle("[]");
        btnNovoCliente.setLayout("blGlyphTop");
        btnNovoCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnNovoClienteClick(event);
            processarFlow("FrmPesquisaCliente", "btnNovoCliente", "OnClick");
        });
        btnNovoCliente.setImageId(0);
        btnNovoCliente.setColor("clBtnFace");
        btnNovoCliente.setAccess(false);
        btnNovoCliente.setIconClass("user-plus");
        btnNovoCliente.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnNovoCliente);
        btnNovoCliente.applyProperties();
    }

    public TFButton btnAlterarCliente = new TFButton();

    private void init_btnAlterarCliente() {
        btnAlterarCliente.setName("btnAlterarCliente");
        btnAlterarCliente.setUploadMime("image/*");
        btnAlterarCliente.setLeft(240);
        btnAlterarCliente.setTop(0);
        btnAlterarCliente.setWidth(60);
        btnAlterarCliente.setHeight(56);
        btnAlterarCliente.setHint("Alterar Cadastro Cliente");
        btnAlterarCliente.setAlign("alLeft");
        btnAlterarCliente.setCaption("Alterar");
        btnAlterarCliente.setFontColor("clWindowText");
        btnAlterarCliente.setFontSize(-11);
        btnAlterarCliente.setFontName("Tahoma");
        btnAlterarCliente.setFontStyle("[]");
        btnAlterarCliente.setLayout("blGlyphTop");
        btnAlterarCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarClienteClick(event);
            processarFlow("FrmPesquisaCliente", "btnAlterarCliente", "OnClick");
        });
        btnAlterarCliente.setImageId(0);
        btnAlterarCliente.setColor("clBtnFace");
        btnAlterarCliente.setAccess(false);
        btnAlterarCliente.setIconClass("address-card");
        btnAlterarCliente.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnAlterarCliente);
        btnAlterarCliente.applyProperties();
    }

    public TFButton btnExcluirCliente = new TFButton();

    private void init_btnExcluirCliente() {
        btnExcluirCliente.setName("btnExcluirCliente");
        btnExcluirCliente.setUploadMime("image/*");
        btnExcluirCliente.setLeft(300);
        btnExcluirCliente.setTop(0);
        btnExcluirCliente.setWidth(60);
        btnExcluirCliente.setHeight(56);
        btnExcluirCliente.setHint("Excluir cliente");
        btnExcluirCliente.setAlign("alLeft");
        btnExcluirCliente.setCaption("Excluir");
        btnExcluirCliente.setFontColor("clWindowText");
        btnExcluirCliente.setFontSize(-11);
        btnExcluirCliente.setFontName("Tahoma");
        btnExcluirCliente.setFontStyle("[]");
        btnExcluirCliente.setLayout("blGlyphTop");
        btnExcluirCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClienteClick(event);
            processarFlow("FrmPesquisaCliente", "btnExcluirCliente", "OnClick");
        });
        btnExcluirCliente.setImageId(4600385);
        btnExcluirCliente.setColor("clBtnFace");
        btnExcluirCliente.setAccess(false);
        btnExcluirCliente.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnExcluirCliente);
        btnExcluirCliente.applyProperties();
    }

    public TFButton btnAlterarFoneEmail = new TFButton();

    private void init_btnAlterarFoneEmail() {
        btnAlterarFoneEmail.setName("btnAlterarFoneEmail");
        btnAlterarFoneEmail.setUploadMime("image/*");
        btnAlterarFoneEmail.setLeft(360);
        btnAlterarFoneEmail.setTop(0);
        btnAlterarFoneEmail.setWidth(68);
        btnAlterarFoneEmail.setHeight(56);
        btnAlterarFoneEmail.setHint("Alterar Fone/e-mail cliente");
        btnAlterarFoneEmail.setAlign("alLeft");
        btnAlterarFoneEmail.setCaption("Alterar Fone");
        btnAlterarFoneEmail.setFontColor("clWindowText");
        btnAlterarFoneEmail.setFontSize(-11);
        btnAlterarFoneEmail.setFontName("Tahoma");
        btnAlterarFoneEmail.setFontStyle("[]");
        btnAlterarFoneEmail.setLayout("blGlyphTop");
        btnAlterarFoneEmail.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAlterarFoneEmailClick(event);
            processarFlow("FrmPesquisaCliente", "btnAlterarFoneEmail", "OnClick");
        });
        btnAlterarFoneEmail.setImageId(0);
        btnAlterarFoneEmail.setColor("clBtnFace");
        btnAlterarFoneEmail.setAccess(false);
        btnAlterarFoneEmail.setIconClass("pencil-square-o");
        btnAlterarFoneEmail.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnAlterarFoneEmail);
        btnAlterarFoneEmail.applyProperties();
    }

    public TFButton btnLimite = new TFButton();

    private void init_btnLimite() {
        btnLimite.setName("btnLimite");
        btnLimite.setUploadMime("image/*");
        btnLimite.setLeft(428);
        btnLimite.setTop(0);
        btnLimite.setWidth(68);
        btnLimite.setHeight(56);
        btnLimite.setHint("Consultar Limite Credito");
        btnLimite.setAlign("alLeft");
        btnLimite.setCaption("Limite");
        btnLimite.setFontColor("clWindowText");
        btnLimite.setFontSize(-11);
        btnLimite.setFontName("Tahoma");
        btnLimite.setFontStyle("[]");
        btnLimite.setLayout("blGlyphTop");
        btnLimite.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnLimiteClick(event);
            processarFlow("FrmPesquisaCliente", "btnLimite", "OnClick");
        });
        btnLimite.setImageId(4600436);
        btnLimite.setColor("clBtnFace");
        btnLimite.setAccess(false);
        btnLimite.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnLimite);
        btnLimite.applyProperties();
    }

    public TFButton btnFlagsEspeciais = new TFButton();

    private void init_btnFlagsEspeciais() {
        btnFlagsEspeciais.setName("btnFlagsEspeciais");
        btnFlagsEspeciais.setUploadMime("image/*");
        btnFlagsEspeciais.setLeft(496);
        btnFlagsEspeciais.setTop(0);
        btnFlagsEspeciais.setWidth(78);
        btnFlagsEspeciais.setHeight(56);
        btnFlagsEspeciais.setHint("Ver flags de Venda do Cliente (Condi\u00E7\u00F5es Especiais)\r\n\r\nAcesso: K0235\r\n\r\nNo formul\u00E1rio \"Clientes\" [Gerenciamento > Clientes], na guia \"Especiais\", o cliente dever\u00E1 atender, pelo \rmenos, uma das seguintes condi\u00E7\u00F5es para ser considerado com Condi\u00E7\u00F5es Especiais:\r\n01 - Exibi\u00E7\u00E3o de um c\u00EDrculo verde nas seguintes colunas:\r\n01.01 - Desconto\r\n01.02 - Condi\u00E7\u00E3o\r\n01.03 - Garantia\r\n01.04 - F\u00E1brica\r\n01.05 - Reserva\r\n01.06 - Pr\u00E9-Pedido\r\n02 - Na coluna \"Tempo Reserva\" exibir valor maior que \"0 Horas\"\r\n03 - Na coluna \"Segmento\" deve haver alguma informa\u00E7\u00E3o");
        btnFlagsEspeciais.setAlign("alLeft");
        btnFlagsEspeciais.setCaption("Flags Especiais");
        btnFlagsEspeciais.setFontColor("clWindowText");
        btnFlagsEspeciais.setFontSize(-11);
        btnFlagsEspeciais.setFontName("Tahoma");
        btnFlagsEspeciais.setFontStyle("[]");
        btnFlagsEspeciais.setLayout("blGlyphTop");
        btnFlagsEspeciais.setVisible(false);
        btnFlagsEspeciais.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFlagsEspeciaisClick(event);
            processarFlow("FrmPesquisaCliente", "btnFlagsEspeciais", "OnClick");
        });
        btnFlagsEspeciais.setImageId(0);
        btnFlagsEspeciais.setColor("clWindowText");
        btnFlagsEspeciais.setAccess(false);
        btnFlagsEspeciais.setIconClass("handshake-o");
        btnFlagsEspeciais.setIconReverseDirection(false);        hboxMenuBotoes.addChildren(btnFlagsEspeciais);
        btnFlagsEspeciais.applyProperties();
    }

    public TFHBox HboxSeparadorBotoesPesquisa = new TFHBox();

    private void init_HboxSeparadorBotoesPesquisa() {
        HboxSeparadorBotoesPesquisa.setName("HboxSeparadorBotoesPesquisa");
        HboxSeparadorBotoesPesquisa.setLeft(0);
        HboxSeparadorBotoesPesquisa.setTop(61);
        HboxSeparadorBotoesPesquisa.setWidth(698);
        HboxSeparadorBotoesPesquisa.setHeight(9);
        HboxSeparadorBotoesPesquisa.setAlign("alTop");
        HboxSeparadorBotoesPesquisa.setBorderStyle("stNone");
        HboxSeparadorBotoesPesquisa.setPaddingTop(0);
        HboxSeparadorBotoesPesquisa.setPaddingLeft(0);
        HboxSeparadorBotoesPesquisa.setPaddingRight(0);
        HboxSeparadorBotoesPesquisa.setPaddingBottom(0);
        HboxSeparadorBotoesPesquisa.setMarginTop(0);
        HboxSeparadorBotoesPesquisa.setMarginLeft(0);
        HboxSeparadorBotoesPesquisa.setMarginRight(0);
        HboxSeparadorBotoesPesquisa.setMarginBottom(0);
        HboxSeparadorBotoesPesquisa.setSpacing(1);
        HboxSeparadorBotoesPesquisa.setFlexVflex("ftFalse");
        HboxSeparadorBotoesPesquisa.setFlexHflex("ftTrue");
        HboxSeparadorBotoesPesquisa.setScrollable(false);
        HboxSeparadorBotoesPesquisa.setBoxShadowConfigHorizontalLength(10);
        HboxSeparadorBotoesPesquisa.setBoxShadowConfigVerticalLength(10);
        HboxSeparadorBotoesPesquisa.setBoxShadowConfigBlurRadius(5);
        HboxSeparadorBotoesPesquisa.setBoxShadowConfigSpreadRadius(0);
        HboxSeparadorBotoesPesquisa.setBoxShadowConfigShadowColor("clBlack");
        HboxSeparadorBotoesPesquisa.setBoxShadowConfigOpacity(75);
        HboxSeparadorBotoesPesquisa.setVAlign("tvTop");
        FrmPesquisaCliente.addChildren(HboxSeparadorBotoesPesquisa);
        HboxSeparadorBotoesPesquisa.applyProperties();
    }

    public TFHBox hBoxPesquisaCliente = new TFHBox();

    private void init_hBoxPesquisaCliente() {
        hBoxPesquisaCliente.setName("hBoxPesquisaCliente");
        hBoxPesquisaCliente.setLeft(0);
        hBoxPesquisaCliente.setTop(70);
        hBoxPesquisaCliente.setWidth(698);
        hBoxPesquisaCliente.setHeight(31);
        hBoxPesquisaCliente.setAlign("alTop");
        hBoxPesquisaCliente.setBorderStyle("stNone");
        hBoxPesquisaCliente.setPaddingTop(0);
        hBoxPesquisaCliente.setPaddingLeft(3);
        hBoxPesquisaCliente.setPaddingRight(3);
        hBoxPesquisaCliente.setPaddingBottom(0);
        hBoxPesquisaCliente.setMarginTop(0);
        hBoxPesquisaCliente.setMarginLeft(0);
        hBoxPesquisaCliente.setMarginRight(0);
        hBoxPesquisaCliente.setMarginBottom(0);
        hBoxPesquisaCliente.setSpacing(1);
        hBoxPesquisaCliente.setFlexVflex("ftFalse");
        hBoxPesquisaCliente.setFlexHflex("ftTrue");
        hBoxPesquisaCliente.setScrollable(false);
        hBoxPesquisaCliente.setBoxShadowConfigHorizontalLength(10);
        hBoxPesquisaCliente.setBoxShadowConfigVerticalLength(10);
        hBoxPesquisaCliente.setBoxShadowConfigBlurRadius(5);
        hBoxPesquisaCliente.setBoxShadowConfigSpreadRadius(0);
        hBoxPesquisaCliente.setBoxShadowConfigShadowColor("clBlack");
        hBoxPesquisaCliente.setBoxShadowConfigOpacity(75);
        hBoxPesquisaCliente.setVAlign("tvTop");
        FrmPesquisaCliente.addChildren(hBoxPesquisaCliente);
        hBoxPesquisaCliente.applyProperties();
    }

    public TFString edtPesquisarCliente = new TFString();

    private void init_edtPesquisarCliente() {
        edtPesquisarCliente.setName("edtPesquisarCliente");
        edtPesquisarCliente.setLeft(0);
        edtPesquisarCliente.setTop(0);
        edtPesquisarCliente.setWidth(631);
        edtPesquisarCliente.setHeight(24);
        edtPesquisarCliente.setHint("Nome, telefone ou e-mail");
        edtPesquisarCliente.setHelpCaption("Nome, telefone ou e-mail");
        edtPesquisarCliente.setFlex(true);
        edtPesquisarCliente.setRequired(false);
        edtPesquisarCliente.setPrompt("Nome, telefone ou e-mail");
        edtPesquisarCliente.setConstraintCheckWhen("cwImmediate");
        edtPesquisarCliente.setConstraintCheckType("ctExpression");
        edtPesquisarCliente.setConstraintFocusOnError(false);
        edtPesquisarCliente.setConstraintEnableUI(true);
        edtPesquisarCliente.setConstraintEnabled(false);
        edtPesquisarCliente.setConstraintFormCheck(true);
        edtPesquisarCliente.setCharCase("ccNormal");
        edtPesquisarCliente.setPwd(false);
        edtPesquisarCliente.setMaxlength(0);
        edtPesquisarCliente.setFontColor("clWindowText");
        edtPesquisarCliente.setFontSize(-13);
        edtPesquisarCliente.setFontName("Tahoma");
        edtPesquisarCliente.setFontStyle("[]");
        edtPesquisarCliente.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtPesquisarClienteEnter(event);
            processarFlow("FrmPesquisaCliente", "edtPesquisarCliente", "OnEnter");
        });
        edtPesquisarCliente.setSaveLiteralCharacter(false);
        edtPesquisarCliente.applyProperties();
        hBoxPesquisaCliente.addChildren(edtPesquisarCliente);
        addValidatable(edtPesquisarCliente);
    }

    public TFVBox vBoxGrid = new TFVBox();

    private void init_vBoxGrid() {
        vBoxGrid.setName("vBoxGrid");
        vBoxGrid.setLeft(0);
        vBoxGrid.setTop(101);
        vBoxGrid.setWidth(698);
        vBoxGrid.setHeight(503);
        vBoxGrid.setAlign("alClient");
        vBoxGrid.setBorderStyle("stNone");
        vBoxGrid.setPaddingTop(3);
        vBoxGrid.setPaddingLeft(3);
        vBoxGrid.setPaddingRight(3);
        vBoxGrid.setPaddingBottom(3);
        vBoxGrid.setMarginTop(0);
        vBoxGrid.setMarginLeft(0);
        vBoxGrid.setMarginRight(0);
        vBoxGrid.setMarginBottom(0);
        vBoxGrid.setSpacing(1);
        vBoxGrid.setFlexVflex("ftTrue");
        vBoxGrid.setFlexHflex("ftTrue");
        vBoxGrid.setScrollable(false);
        vBoxGrid.setBoxShadowConfigHorizontalLength(10);
        vBoxGrid.setBoxShadowConfigVerticalLength(10);
        vBoxGrid.setBoxShadowConfigBlurRadius(5);
        vBoxGrid.setBoxShadowConfigSpreadRadius(0);
        vBoxGrid.setBoxShadowConfigShadowColor("clBlack");
        vBoxGrid.setBoxShadowConfigOpacity(75);
        FrmPesquisaCliente.addChildren(vBoxGrid);
        vBoxGrid.applyProperties();
    }

    public TFHBox hbCliente = new TFHBox();

    private void init_hbCliente() {
        hbCliente.setName("hbCliente");
        hbCliente.setLeft(0);
        hbCliente.setTop(0);
        hbCliente.setWidth(699);
        hbCliente.setHeight(196);
        hbCliente.setBorderStyle("stNone");
        hbCliente.setPaddingTop(0);
        hbCliente.setPaddingLeft(0);
        hbCliente.setPaddingRight(0);
        hbCliente.setPaddingBottom(0);
        hbCliente.setMarginTop(0);
        hbCliente.setMarginLeft(0);
        hbCliente.setMarginRight(0);
        hbCliente.setMarginBottom(0);
        hbCliente.setSpacing(1);
        hbCliente.setFlexVflex("ftTrue");
        hbCliente.setFlexHflex("ftTrue");
        hbCliente.setScrollable(false);
        hbCliente.setBoxShadowConfigHorizontalLength(10);
        hbCliente.setBoxShadowConfigVerticalLength(10);
        hbCliente.setBoxShadowConfigBlurRadius(5);
        hbCliente.setBoxShadowConfigSpreadRadius(0);
        hbCliente.setBoxShadowConfigShadowColor("clBlack");
        hbCliente.setBoxShadowConfigOpacity(75);
        hbCliente.setVAlign("tvTop");
        vBoxGrid.addChildren(hbCliente);
        hbCliente.applyProperties();
    }

    public TFVBox hbGridClientes = new TFVBox();

    private void init_hbGridClientes() {
        hbGridClientes.setName("hbGridClientes");
        hbGridClientes.setLeft(0);
        hbGridClientes.setTop(0);
        hbGridClientes.setWidth(429);
        hbGridClientes.setHeight(173);
        hbGridClientes.setBorderStyle("stNone");
        hbGridClientes.setPaddingTop(0);
        hbGridClientes.setPaddingLeft(0);
        hbGridClientes.setPaddingRight(0);
        hbGridClientes.setPaddingBottom(0);
        hbGridClientes.setMarginTop(0);
        hbGridClientes.setMarginLeft(0);
        hbGridClientes.setMarginRight(0);
        hbGridClientes.setMarginBottom(0);
        hbGridClientes.setSpacing(1);
        hbGridClientes.setFlexVflex("ftTrue");
        hbGridClientes.setFlexHflex("ftTrue");
        hbGridClientes.setScrollable(false);
        hbGridClientes.setBoxShadowConfigHorizontalLength(10);
        hbGridClientes.setBoxShadowConfigVerticalLength(10);
        hbGridClientes.setBoxShadowConfigBlurRadius(5);
        hbGridClientes.setBoxShadowConfigSpreadRadius(0);
        hbGridClientes.setBoxShadowConfigShadowColor("clBlack");
        hbGridClientes.setBoxShadowConfigOpacity(75);
        hbCliente.addChildren(hbGridClientes);
        hbGridClientes.applyProperties();
    }

    public TFHBox HboxExpacoGrid = new TFHBox();

    private void init_HboxExpacoGrid() {
        HboxExpacoGrid.setName("HboxExpacoGrid");
        HboxExpacoGrid.setLeft(0);
        HboxExpacoGrid.setTop(0);
        HboxExpacoGrid.setWidth(327);
        HboxExpacoGrid.setHeight(21);
        HboxExpacoGrid.setBorderStyle("stNone");
        HboxExpacoGrid.setPaddingTop(0);
        HboxExpacoGrid.setPaddingLeft(0);
        HboxExpacoGrid.setPaddingRight(0);
        HboxExpacoGrid.setPaddingBottom(0);
        HboxExpacoGrid.setMarginTop(0);
        HboxExpacoGrid.setMarginLeft(0);
        HboxExpacoGrid.setMarginRight(0);
        HboxExpacoGrid.setMarginBottom(0);
        HboxExpacoGrid.setSpacing(1);
        HboxExpacoGrid.setFlexVflex("ftMin");
        HboxExpacoGrid.setFlexHflex("ftTrue");
        HboxExpacoGrid.setScrollable(false);
        HboxExpacoGrid.setBoxShadowConfigHorizontalLength(10);
        HboxExpacoGrid.setBoxShadowConfigVerticalLength(10);
        HboxExpacoGrid.setBoxShadowConfigBlurRadius(5);
        HboxExpacoGrid.setBoxShadowConfigSpreadRadius(0);
        HboxExpacoGrid.setBoxShadowConfigShadowColor("clBlack");
        HboxExpacoGrid.setBoxShadowConfigOpacity(75);
        HboxExpacoGrid.setVAlign("tvTop");
        hbGridClientes.addChildren(HboxExpacoGrid);
        HboxExpacoGrid.applyProperties();
    }

    public TFLabel lblFaturamento = new TFLabel();

    private void init_lblFaturamento() {
        lblFaturamento.setName("lblFaturamento");
        lblFaturamento.setLeft(0);
        lblFaturamento.setTop(0);
        lblFaturamento.setWidth(39);
        lblFaturamento.setHeight(13);
        lblFaturamento.setCaption("Cliente");
        lblFaturamento.setFontColor("clWindowText");
        lblFaturamento.setFontSize(-11);
        lblFaturamento.setFontName("Tahoma");
        lblFaturamento.setFontStyle("[fsBold]");
        lblFaturamento.setVerticalAlignment("taVerticalCenter");
        lblFaturamento.setWordBreak(false);
        HboxExpacoGrid.addChildren(lblFaturamento);
        lblFaturamento.applyProperties();
    }

    public TFGrid grdClientes = new TFGrid();

    private void init_grdClientes() {
        grdClientes.setName("grdClientes");
        grdClientes.setLeft(0);
        grdClientes.setTop(22);
        grdClientes.setWidth(404);
        grdClientes.setHeight(137);
        grdClientes.setAlign("alClient");
        grdClientes.setTable(tbLeadsConsultaClientes);
        grdClientes.setFlexVflex("ftTrue");
        grdClientes.setFlexHflex("ftTrue");
        grdClientes.setPagingEnabled(true);
        grdClientes.setFrozenColumns(0);
        grdClientes.setShowFooter(false);
        grdClientes.setShowHeader(true);
        grdClientes.setMultiSelection(false);
        grdClientes.setGroupingEnabled(false);
        grdClientes.setGroupingExpanded(false);
        grdClientes.setGroupingShowFooter(false);
        grdClientes.setCrosstabEnabled(false);
        grdClientes.setCrosstabGroupType("cgtConcat");
        grdClientes.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmPesquisaCliente", "grdClientes", "OnDoubleClick");
        });
        grdClientes.setEditionEnabled(false);
        grdClientes.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_CLIENTE_EXIBIR");
        item0.setTitleCaption("C\u00F3d. Cliente");
        item0.setWidth(120);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdClientes.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("CLIENTE_EXIBIR");
        item1.setTitleCaption("Nome");
        item1.setWidth(200);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdClientes.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("EMAIL_EXIBIR");
        item2.setTitleCaption("E-mail");
        item2.setWidth(200);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdClientes.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("EMAIL_NFE_EXIBIR");
        item3.setTitleCaption("E-mail NF-e");
        item3.setWidth(200);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdClientes.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("EH_ATACADISTA");
        item4.setTitleCaption("Atacadista");
        item4.setWidth(80);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taCenter");
        item4.setFieldType("ftCheckBox");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdClientes.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("MIDIA_DESCRICAO_CODIGO");
        item5.setTitleCaption("M\u00EDdia");
        item5.setWidth(100);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdClientes.getColumns().add(item5);
        hbGridClientes.addChildren(grdClientes);
        grdClientes.applyProperties();
    }

    public TFVBox vboxTelefone = new TFVBox();

    private void init_vboxTelefone() {
        vboxTelefone.setName("vboxTelefone");
        vboxTelefone.setLeft(429);
        vboxTelefone.setTop(0);
        vboxTelefone.setWidth(255);
        vboxTelefone.setHeight(173);
        vboxTelefone.setBorderStyle("stNone");
        vboxTelefone.setPaddingTop(0);
        vboxTelefone.setPaddingLeft(3);
        vboxTelefone.setPaddingRight(0);
        vboxTelefone.setPaddingBottom(0);
        vboxTelefone.setMarginTop(0);
        vboxTelefone.setMarginLeft(0);
        vboxTelefone.setMarginRight(0);
        vboxTelefone.setMarginBottom(0);
        vboxTelefone.setSpacing(1);
        vboxTelefone.setFlexVflex("ftTrue");
        vboxTelefone.setFlexHflex("ftFalse");
        vboxTelefone.setScrollable(false);
        vboxTelefone.setBoxShadowConfigHorizontalLength(10);
        vboxTelefone.setBoxShadowConfigVerticalLength(10);
        vboxTelefone.setBoxShadowConfigBlurRadius(5);
        vboxTelefone.setBoxShadowConfigSpreadRadius(0);
        vboxTelefone.setBoxShadowConfigShadowColor("clBlack");
        vboxTelefone.setBoxShadowConfigOpacity(75);
        hbCliente.addChildren(vboxTelefone);
        vboxTelefone.applyProperties();
    }

    public TFHBox hBoxQualMelhorFone = new TFHBox();

    private void init_hBoxQualMelhorFone() {
        hBoxQualMelhorFone.setName("hBoxQualMelhorFone");
        hBoxQualMelhorFone.setLeft(0);
        hBoxQualMelhorFone.setTop(0);
        hBoxQualMelhorFone.setWidth(243);
        hBoxQualMelhorFone.setHeight(20);
        hBoxQualMelhorFone.setBorderStyle("stNone");
        hBoxQualMelhorFone.setPaddingTop(0);
        hBoxQualMelhorFone.setPaddingLeft(0);
        hBoxQualMelhorFone.setPaddingRight(0);
        hBoxQualMelhorFone.setPaddingBottom(0);
        hBoxQualMelhorFone.setMarginTop(0);
        hBoxQualMelhorFone.setMarginLeft(0);
        hBoxQualMelhorFone.setMarginRight(0);
        hBoxQualMelhorFone.setMarginBottom(0);
        hBoxQualMelhorFone.setSpacing(1);
        hBoxQualMelhorFone.setFlexVflex("ftMin");
        hBoxQualMelhorFone.setFlexHflex("ftTrue");
        hBoxQualMelhorFone.setScrollable(false);
        hBoxQualMelhorFone.setBoxShadowConfigHorizontalLength(10);
        hBoxQualMelhorFone.setBoxShadowConfigVerticalLength(10);
        hBoxQualMelhorFone.setBoxShadowConfigBlurRadius(5);
        hBoxQualMelhorFone.setBoxShadowConfigSpreadRadius(0);
        hBoxQualMelhorFone.setBoxShadowConfigShadowColor("clBlack");
        hBoxQualMelhorFone.setBoxShadowConfigOpacity(75);
        hBoxQualMelhorFone.setVAlign("tvTop");
        vboxTelefone.addChildren(hBoxQualMelhorFone);
        hBoxQualMelhorFone.applyProperties();
    }

    public TFLabel lblQualMelhorFone = new TFLabel();

    private void init_lblQualMelhorFone() {
        lblQualMelhorFone.setName("lblQualMelhorFone");
        lblQualMelhorFone.setLeft(0);
        lblQualMelhorFone.setTop(0);
        lblQualMelhorFone.setWidth(233);
        lblQualMelhorFone.setHeight(13);
        lblQualMelhorFone.setCaption("Selecione o melhor telefone para contato");
        lblQualMelhorFone.setFontColor("clWindowText");
        lblQualMelhorFone.setFontSize(-11);
        lblQualMelhorFone.setFontName("Tahoma");
        lblQualMelhorFone.setFontStyle("[fsBold]");
        lblQualMelhorFone.setVerticalAlignment("taVerticalCenter");
        lblQualMelhorFone.setWordBreak(false);
        hBoxQualMelhorFone.addChildren(lblQualMelhorFone);
        lblQualMelhorFone.applyProperties();
    }

    public TFVBox vBoxSeparadorSelecioneFone = new TFVBox();

    private void init_vBoxSeparadorSelecioneFone() {
        vBoxSeparadorSelecioneFone.setName("vBoxSeparadorSelecioneFone");
        vBoxSeparadorSelecioneFone.setLeft(233);
        vBoxSeparadorSelecioneFone.setTop(0);
        vBoxSeparadorSelecioneFone.setWidth(6);
        vBoxSeparadorSelecioneFone.setHeight(17);
        vBoxSeparadorSelecioneFone.setBorderStyle("stNone");
        vBoxSeparadorSelecioneFone.setPaddingTop(0);
        vBoxSeparadorSelecioneFone.setPaddingLeft(0);
        vBoxSeparadorSelecioneFone.setPaddingRight(0);
        vBoxSeparadorSelecioneFone.setPaddingBottom(0);
        vBoxSeparadorSelecioneFone.setMarginTop(0);
        vBoxSeparadorSelecioneFone.setMarginLeft(0);
        vBoxSeparadorSelecioneFone.setMarginRight(0);
        vBoxSeparadorSelecioneFone.setMarginBottom(0);
        vBoxSeparadorSelecioneFone.setSpacing(1);
        vBoxSeparadorSelecioneFone.setFlexVflex("ftFalse");
        vBoxSeparadorSelecioneFone.setFlexHflex("ftFalse");
        vBoxSeparadorSelecioneFone.setScrollable(false);
        vBoxSeparadorSelecioneFone.setBoxShadowConfigHorizontalLength(10);
        vBoxSeparadorSelecioneFone.setBoxShadowConfigVerticalLength(10);
        vBoxSeparadorSelecioneFone.setBoxShadowConfigBlurRadius(5);
        vBoxSeparadorSelecioneFone.setBoxShadowConfigSpreadRadius(0);
        vBoxSeparadorSelecioneFone.setBoxShadowConfigShadowColor("clBlack");
        vBoxSeparadorSelecioneFone.setBoxShadowConfigOpacity(75);
        hBoxQualMelhorFone.addChildren(vBoxSeparadorSelecioneFone);
        vBoxSeparadorSelecioneFone.applyProperties();
    }

    public TFIconClass FIconClassEditarCliente = new TFIconClass();

    private void init_FIconClassEditarCliente() {
        FIconClassEditarCliente.setName("FIconClassEditarCliente");
        FIconClassEditarCliente.setLeft(239);
        FIconClassEditarCliente.setTop(0);
        FIconClassEditarCliente.setHint("Alterar Fone/e-mail cliente");
        FIconClassEditarCliente.setVisible(false);
        FIconClassEditarCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FIconClassEditarClienteClick(event);
            processarFlow("FrmPesquisaCliente", "FIconClassEditarCliente", "OnClick");
        });
        FIconClassEditarCliente.setIconClass("pencil-square-o");
        FIconClassEditarCliente.setSize(18);
        FIconClassEditarCliente.setColor("clBlack");
        hBoxQualMelhorFone.addChildren(FIconClassEditarCliente);
        FIconClassEditarCliente.applyProperties();
    }

    public TFGrid grdFoneCliente = new TFGrid();

    private void init_grdFoneCliente() {
        grdFoneCliente.setName("grdFoneCliente");
        grdFoneCliente.setLeft(0);
        grdFoneCliente.setTop(21);
        grdFoneCliente.setWidth(236);
        grdFoneCliente.setHeight(145);
        grdFoneCliente.setAlign("alClient");
        grdFoneCliente.setTable(tbListFoneCliente);
        grdFoneCliente.setFlexVflex("ftTrue");
        grdFoneCliente.setFlexHflex("ftTrue");
        grdFoneCliente.setPagingEnabled(false);
        grdFoneCliente.setFrozenColumns(0);
        grdFoneCliente.setShowFooter(false);
        grdFoneCliente.setShowHeader(true);
        grdFoneCliente.setMultiSelection(false);
        grdFoneCliente.setGroupingEnabled(false);
        grdFoneCliente.setGroupingExpanded(false);
        grdFoneCliente.setGroupingShowFooter(false);
        grdFoneCliente.setCrosstabEnabled(false);
        grdFoneCliente.setCrosstabGroupType("cgtConcat");
        grdFoneCliente.setEditionEnabled(false);
        grdFoneCliente.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("TIPO_FONE");
        item0.setTitleCaption("Tipo");
        item0.setWidth(81);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdFoneCliente.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("FONE");
        item1.setTitleCaption("Telefone");
        item1.setWidth(100);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdFoneCliente.getColumns().add(item1);
        vboxTelefone.addChildren(grdFoneCliente);
        grdFoneCliente.applyProperties();
    }

    public TFVBox vBoxGridEnderecoFaturamento = new TFVBox();

    private void init_vBoxGridEnderecoFaturamento() {
        vBoxGridEnderecoFaturamento.setName("vBoxGridEnderecoFaturamento");
        vBoxGridEnderecoFaturamento.setLeft(0);
        vBoxGridEnderecoFaturamento.setTop(197);
        vBoxGridEnderecoFaturamento.setWidth(672);
        vBoxGridEnderecoFaturamento.setHeight(273);
        vBoxGridEnderecoFaturamento.setBorderStyle("stNone");
        vBoxGridEnderecoFaturamento.setPaddingTop(0);
        vBoxGridEnderecoFaturamento.setPaddingLeft(0);
        vBoxGridEnderecoFaturamento.setPaddingRight(0);
        vBoxGridEnderecoFaturamento.setPaddingBottom(0);
        vBoxGridEnderecoFaturamento.setMarginTop(0);
        vBoxGridEnderecoFaturamento.setMarginLeft(0);
        vBoxGridEnderecoFaturamento.setMarginRight(0);
        vBoxGridEnderecoFaturamento.setMarginBottom(0);
        vBoxGridEnderecoFaturamento.setSpacing(1);
        vBoxGridEnderecoFaturamento.setFlexVflex("ftTrue");
        vBoxGridEnderecoFaturamento.setFlexHflex("ftTrue");
        vBoxGridEnderecoFaturamento.setScrollable(false);
        vBoxGridEnderecoFaturamento.setBoxShadowConfigHorizontalLength(10);
        vBoxGridEnderecoFaturamento.setBoxShadowConfigVerticalLength(10);
        vBoxGridEnderecoFaturamento.setBoxShadowConfigBlurRadius(5);
        vBoxGridEnderecoFaturamento.setBoxShadowConfigSpreadRadius(0);
        vBoxGridEnderecoFaturamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxGridEnderecoFaturamento.setBoxShadowConfigOpacity(75);
        vBoxGrid.addChildren(vBoxGridEnderecoFaturamento);
        vBoxGridEnderecoFaturamento.applyProperties();
    }

    public TFHBox hBoxLabelEnderecoFaturamento = new TFHBox();

    private void init_hBoxLabelEnderecoFaturamento() {
        hBoxLabelEnderecoFaturamento.setName("hBoxLabelEnderecoFaturamento");
        hBoxLabelEnderecoFaturamento.setLeft(0);
        hBoxLabelEnderecoFaturamento.setTop(0);
        hBoxLabelEnderecoFaturamento.setWidth(207);
        hBoxLabelEnderecoFaturamento.setHeight(20);
        hBoxLabelEnderecoFaturamento.setBorderStyle("stNone");
        hBoxLabelEnderecoFaturamento.setPaddingTop(0);
        hBoxLabelEnderecoFaturamento.setPaddingLeft(0);
        hBoxLabelEnderecoFaturamento.setPaddingRight(0);
        hBoxLabelEnderecoFaturamento.setPaddingBottom(0);
        hBoxLabelEnderecoFaturamento.setMarginTop(0);
        hBoxLabelEnderecoFaturamento.setMarginLeft(0);
        hBoxLabelEnderecoFaturamento.setMarginRight(0);
        hBoxLabelEnderecoFaturamento.setMarginBottom(0);
        hBoxLabelEnderecoFaturamento.setSpacing(1);
        hBoxLabelEnderecoFaturamento.setFlexVflex("ftMin");
        hBoxLabelEnderecoFaturamento.setFlexHflex("ftTrue");
        hBoxLabelEnderecoFaturamento.setScrollable(false);
        hBoxLabelEnderecoFaturamento.setBoxShadowConfigHorizontalLength(10);
        hBoxLabelEnderecoFaturamento.setBoxShadowConfigVerticalLength(10);
        hBoxLabelEnderecoFaturamento.setBoxShadowConfigBlurRadius(5);
        hBoxLabelEnderecoFaturamento.setBoxShadowConfigSpreadRadius(0);
        hBoxLabelEnderecoFaturamento.setBoxShadowConfigShadowColor("clBlack");
        hBoxLabelEnderecoFaturamento.setBoxShadowConfigOpacity(75);
        hBoxLabelEnderecoFaturamento.setVAlign("tvTop");
        vBoxGridEnderecoFaturamento.addChildren(hBoxLabelEnderecoFaturamento);
        hBoxLabelEnderecoFaturamento.applyProperties();
    }

    public TFLabel lblEnderecoFaturamento = new TFLabel();

    private void init_lblEnderecoFaturamento() {
        lblEnderecoFaturamento.setName("lblEnderecoFaturamento");
        lblEnderecoFaturamento.setLeft(0);
        lblEnderecoFaturamento.setTop(0);
        lblEnderecoFaturamento.setWidth(146);
        lblEnderecoFaturamento.setHeight(13);
        lblEnderecoFaturamento.setCaption("Endere\u00E7o de Faturamento");
        lblEnderecoFaturamento.setFontColor("clWindowText");
        lblEnderecoFaturamento.setFontSize(-11);
        lblEnderecoFaturamento.setFontName("Tahoma");
        lblEnderecoFaturamento.setFontStyle("[fsBold]");
        lblEnderecoFaturamento.setVerticalAlignment("taVerticalCenter");
        lblEnderecoFaturamento.setWordBreak(false);
        hBoxLabelEnderecoFaturamento.addChildren(lblEnderecoFaturamento);
        lblEnderecoFaturamento.applyProperties();
    }

    public TFVBox vBoxFinalEnderecoFaturamento = new TFVBox();

    private void init_vBoxFinalEnderecoFaturamento() {
        vBoxFinalEnderecoFaturamento.setName("vBoxFinalEnderecoFaturamento");
        vBoxFinalEnderecoFaturamento.setLeft(146);
        vBoxFinalEnderecoFaturamento.setTop(0);
        vBoxFinalEnderecoFaturamento.setWidth(6);
        vBoxFinalEnderecoFaturamento.setHeight(17);
        vBoxFinalEnderecoFaturamento.setBorderStyle("stNone");
        vBoxFinalEnderecoFaturamento.setPaddingTop(0);
        vBoxFinalEnderecoFaturamento.setPaddingLeft(0);
        vBoxFinalEnderecoFaturamento.setPaddingRight(0);
        vBoxFinalEnderecoFaturamento.setPaddingBottom(0);
        vBoxFinalEnderecoFaturamento.setMarginTop(0);
        vBoxFinalEnderecoFaturamento.setMarginLeft(0);
        vBoxFinalEnderecoFaturamento.setMarginRight(0);
        vBoxFinalEnderecoFaturamento.setMarginBottom(0);
        vBoxFinalEnderecoFaturamento.setSpacing(1);
        vBoxFinalEnderecoFaturamento.setFlexVflex("ftFalse");
        vBoxFinalEnderecoFaturamento.setFlexHflex("ftFalse");
        vBoxFinalEnderecoFaturamento.setScrollable(false);
        vBoxFinalEnderecoFaturamento.setBoxShadowConfigHorizontalLength(10);
        vBoxFinalEnderecoFaturamento.setBoxShadowConfigVerticalLength(10);
        vBoxFinalEnderecoFaturamento.setBoxShadowConfigBlurRadius(5);
        vBoxFinalEnderecoFaturamento.setBoxShadowConfigSpreadRadius(0);
        vBoxFinalEnderecoFaturamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxFinalEnderecoFaturamento.setBoxShadowConfigOpacity(75);
        hBoxLabelEnderecoFaturamento.addChildren(vBoxFinalEnderecoFaturamento);
        vBoxFinalEnderecoFaturamento.applyProperties();
    }

    public TFIconClass iconClassEditarEndereco = new TFIconClass();

    private void init_iconClassEditarEndereco() {
        iconClassEditarEndereco.setName("iconClassEditarEndereco");
        iconClassEditarEndereco.setLeft(152);
        iconClassEditarEndereco.setTop(0);
        iconClassEditarEndereco.setHint("Alterar Endere\u00E7o Selecionado");
        iconClassEditarEndereco.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconClassEditarEnderecoClick(event);
            processarFlow("FrmPesquisaCliente", "iconClassEditarEndereco", "OnClick");
        });
        iconClassEditarEndereco.setIconClass("pencil-square-o");
        iconClassEditarEndereco.setSize(18);
        iconClassEditarEndereco.setColor("clBlack");
        hBoxLabelEnderecoFaturamento.addChildren(iconClassEditarEndereco);
        iconClassEditarEndereco.applyProperties();
    }

    public TFGrid grdEndereco = new TFGrid();

    private void init_grdEndereco() {
        grdEndereco.setName("grdEndereco");
        grdEndereco.setLeft(0);
        grdEndereco.setTop(21);
        grdEndereco.setWidth(657);
        grdEndereco.setHeight(194);
        grdEndereco.setTable(tbLeadsEnderecoCliente);
        grdEndereco.setFlexVflex("ftTrue");
        grdEndereco.setFlexHflex("ftTrue");
        grdEndereco.setPagingEnabled(false);
        grdEndereco.setFrozenColumns(0);
        grdEndereco.setShowFooter(false);
        grdEndereco.setShowHeader(true);
        grdEndereco.setMultiSelection(false);
        grdEndereco.setGroupingEnabled(false);
        grdEndereco.setGroupingExpanded(false);
        grdEndereco.setGroupingShowFooter(false);
        grdEndereco.setCrosstabEnabled(false);
        grdEndereco.setCrosstabGroupType("cgtConcat");
        grdEndereco.setEditionEnabled(false);
        grdEndereco.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption("#");
        item0.setWidth(35);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("CHECKED = 'S'");
        item1.setEvalType("etExpression");
        item1.setImageId(7000110);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("CHECKED = 'N'");
        item2.setEvalType("etExpression");
        item2.setImageId(7000111);
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("TIPO_ENDERECO");
        item3.setTitleCaption("Tipo");
        item3.setWidth(80);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("UF");
        item4.setWidth(40);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("CIDADE_EXIBIR");
        item5.setTitleCaption("Cidade");
        item5.setWidth(100);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("BAIRRO_EXIBIR");
        item6.setTitleCaption("Bairro");
        item6.setWidth(100);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("RUA_EXIBIR");
        item7.setTitleCaption("Rua");
        item7.setWidth(100);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("NUMERO_EXIBIR");
        item8.setTitleCaption("N\u00FAmero");
        item8.setWidth(60);
        item8.setVisible(true);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("INSCRICAO_ESTADUAL_EXIBIR");
        item9.setTitleCaption("Inscri\u00E7\u00E3o Estadual");
        item9.setWidth(120);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item9);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("CEP_EXIBIR");
        item10.setTitleCaption("CEP");
        item10.setWidth(80);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setCheckedValue("S");
        item10.setUncheckedValue("N");
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        grdEndereco.getColumns().add(item10);
        vBoxGridEnderecoFaturamento.addChildren(grdEndereco);
        grdEndereco.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnNovoClienteClick(final Event<Object> event) {
        if (btnNovoCliente.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnNovoCliente");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarClienteClick(final Event<Object> event) {
        if (btnAlterarCliente.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarCliente");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnExcluirClienteClick(final Event<Object> event) {
        if (btnExcluirCliente.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirCliente");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAlterarFoneEmailClick(final Event<Object> event) {
        if (btnAlterarFoneEmail.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAlterarFoneEmail");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnLimiteClick(final Event<Object> event) {
        if (btnLimite.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnLimite");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnFlagsEspeciaisClick(final Event<Object> event) {
        if (btnFlagsEspeciais.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnFlagsEspeciais");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtPesquisarClienteEnter(final Event<Object> event);

    public abstract void FIconClassEditarClienteClick(final Event<Object> event);

    public abstract void iconClassEditarEnderecoClick(final Event<Object> event);

    public abstract void tbLeadsConsultaClientesAfterScroll(final Event<Object> event);

    public abstract void tbLeadsEnderecoClienteAfterScroll(final Event<Object> event);

    public abstract void tbLeadsEnderecoClienteBeforeScroll(final Event<Object> event);

}