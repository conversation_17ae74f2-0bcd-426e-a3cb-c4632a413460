package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClientesFlags extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClientesFlagsRNA rn = null;

    public FrmClientesFlags() {
        try {
            rn = (freedom.bytecode.rn.ClientesFlagsRNA) getRN(freedom.bytecode.rn.wizard.ClientesFlagsRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClienteDiversoFlag();
        init_tbClientesParamEmpFlag();
        init_tbFormaCobrancaFlag();
        init_tbClienteTransportadoraFlag();
        init_tbTransportadorasFlag();
        init_tbClienteFormaPgtoFlag();
        init_tbClientesDescontosFlag();
        init_tbItensPerDescFlag();
        init_tbVendedorRespFlag();
        init_tbDadosJuridicosFlag();
        init_tbBuscaRepresentanteCliente();
        init_tbListaClienteFormasPagamento();
        init_tbClienteFormasPgto();
        init_tbDescEmp();
        init_tbEmpresasFiliaisSel();
        init_tbClientesEspeciaisMargem();
        init_tbCliEspMargEmp();
        init_tbListaClienteResponsavel();
        init_tbClienteSegmento();
        init_hBoxLinha01();
        init_vBoxNome();
        init_lblNomeCliente2();
        init_lblNomeCliente();
        init_hBoxSeparadorHorizontal01();
        init_vBoxCodigo();
        init_lblCodigo();
        init_lblCodCliente();
        init_hBoxSeparadorHorizontal02();
        init_vBoxCurva();
        init_lblCurva();
        init_FLabel11();
        init_pgFlags();
        init_tabDesconto();
        init_vBoxTabDesconto();
        init_FHBox1();
        init_hBoxTabDescontoLinha01();
        init_FHBox2();
        init_vBoxSegmento();
        init_FLabel10();
        init_cboSegmento();
        init_FHBox5();
        init_FHBox3();
        init_hBoxTabDescontoLinha02();
        init_FHBox11();
        init_vBoxVendaDescontoPorLetra();
        init_hBoxDecontoPorLetraLinha01();
        init_lblDescontoPorLetra();
        init_vBoxSeparadorDescontoPorLetra();
        init_btnIncluirDescontoPorLetra();
        init_FHBox17();
        init_grdDescontoLetra();
        init_FHBox13();
        init_vBoxVendaMargemMinimaMarkup();
        init_hBoxMargemMinimaMarkupLinha01();
        init_lblMargemMinimaMarkup();
        init_vBoxSeparadorMargemMinimaMarkup1();
        init_btnIncluirMargemMinimaMarkup();
        init_FHBox18();
        init_grdMargemMinimaMarkup();
        init_FHBox15();
        init_FHBox9();
        init_FHBox8();
        init_FHBox20();
        init_********************************();
        init_hBoxDecontoPorLetraEmpresaLinha01();
        init_lblDescontoLetraEmpresa();
        init_vBoxSeparadorDescontoPorLetraEmpresa();
        init_btnIncluirDescontoPorLetraEmpresa();
        init_FHBox24();
        init_grdDescontoLetraEmpresa();
        init_FHBox22();
        init_vBoxVendaMargemMinimaMarkupEmpresa();
        init_hBoxMargemMinimaMarkupEmpresaLinha01();
        init_lblMargemMinimaMarkupEmpresa();
        init_FVBox2();
        init_btnIncluirMargemMinimaMarkupEmpresa();
        init_FHBox25();
        init_grdMargemMinimaMarkupEmpresa();
        init_FHBox39();
        init_FHBox19();
        init_tabVenda();
        init_vBoxTabVenda();
        init_FHBox26();
        init_pgFlagsDiverso();
        init_tabNegociacao();
        init_vBoxTabNegociacao();
        init_FHBox35();
        init_vBoxPrecosPraticadosNaVenda();
        init_FLabel1();
        init_chkUsaPrecoFabrica();
        init_chkUsaPrecoGarantia();
        init_vBoxPrePedido();
        init_FLabel2();
        init_chkAprovaAuto();
        init_vBoxReservaDaPeca();
        init_FLabel3();
        init_hBoxReservaDaPeca();
        init_chkReservaAuto();
        init_lblTempoAdicional();
        init_intTempoReserva();
        init_lblHoras();
        init_FVBox8();
        init_lblClienteAtacado();
        init_chkAtacadista();
        init_tabVendedor();
        init_vBoxVendedor();
        init_FHBox37();
        init_FHBox38();
        init_FVBox5();
        init_FHBox44();
        init_vBoxRepresentanteAssociadoAoCliente();
        init_FLabel16();
        init_cboRepresentante();
        init_FHBox46();
        init_vBoxVendedorAssociadoAoCliente();
        init_FLabel15();
        init_cboVendedor();
        init_FHBox40();
        init_FHBox42();
        init_FHBox48();
        init_FVBox6();
        init_FHBox50();
        init_lblVendedorTitulo();
        init_FVBox7();
        init_btnVincularVendedor();
        init_grdVendedoresAssociados();
        init_FHBox32();
        init_FHBox51();
        init_tabTransportadora();
        init_vBoxTransportadora();
        init_hBoxVendaLinha02();
        init_FHBox45();
        init_vBoxVendaTransportadora();
        init_hBoxTransportadoraLinha01();
        init_FLabel7();
        init_FVBox4();
        init_btnIncluirTransportadora();
        init_grdClienteTransp();
        init_FHBox49();
        init_FHBox47();
        init_tabFinanceiro();
        init_vBoxFinanceiro();
        init_pgFinanceiro();
        init_tabFormaCondicaoPagamento();
        init_vBoxFormaCondicaoPagamento();
        init_FHBox29();
        init_hBoxFaturamentoLivreLinha01();
        init_FHBox52();
        init_vBoxFinanceiroLinha01Coluna02();
        init_vBoxFormaCobranca();
        init_FLabel6();
        init_cboFormaCob();
        init_FHBox4();
        init_vBoxFormaPgto();
        init_hBoxCaptionFormaPgto();
        init_lblFormasDePagamento();
        init_FVBox1();
        init_btnAddFormaPgto();
        init_FHBox12();
        init_grdFormasDePagamento();
        init_FHBox7();
        init_vBoxCondicaoPagamento();
        init_hBoxCaptionCondPgto();
        init_lblCondicoesPgto();
        init_FVBox3();
        init_btnAddCondicaoPgto();
        init_FHBox14();
        init_grdCondPgto();
        init_FHBox33();
        init_FHBox31();
        init_tabFaturamentoLivre();
        init_vBoxFaturamentoLivre();
        init_FHBox16();
        init_hBoxFaturamentoLivreLinha001();
        init_FHBox21();
        init_vBoxFaturamentoLivre02();
        init_chkFatLivre();
        init_grdClienteFatLivre();
        init_FHBox23();
        init_sc();
        init_FrmClientesFlags();
    }

    public CLIENTE_DIVERSO_FLAG tbClienteDiversoFlag;

    private void init_tbClienteDiversoFlag() {
        tbClienteDiversoFlag = rn.tbClienteDiversoFlag;
        tbClienteDiversoFlag.setName("tbClienteDiversoFlag");
        tbClienteDiversoFlag.setMaxRowCount(0);
        tbClienteDiversoFlag.setWKey("310036;31006");
        tbClienteDiversoFlag.setRatioBatchSize(20);
        getTables().put(tbClienteDiversoFlag, "tbClienteDiversoFlag");
        tbClienteDiversoFlag.applyProperties();
    }

    public CLIENTES_PARAM_EMP_FLAG tbClientesParamEmpFlag;

    private void init_tbClientesParamEmpFlag() {
        tbClientesParamEmpFlag = rn.tbClientesParamEmpFlag;
        tbClientesParamEmpFlag.setName("tbClientesParamEmpFlag");
        tbClientesParamEmpFlag.setMaxRowCount(0);
        tbClientesParamEmpFlag.setWKey("310036;31007");
        tbClientesParamEmpFlag.setRatioBatchSize(20);
        getTables().put(tbClientesParamEmpFlag, "tbClientesParamEmpFlag");
        tbClientesParamEmpFlag.applyProperties();
    }

    public FORMA_COBRANCA_FLAG tbFormaCobrancaFlag;

    private void init_tbFormaCobrancaFlag() {
        tbFormaCobrancaFlag = rn.tbFormaCobrancaFlag;
        tbFormaCobrancaFlag.setName("tbFormaCobrancaFlag");
        tbFormaCobrancaFlag.setMaxRowCount(0);
        tbFormaCobrancaFlag.setWKey("310036;31008");
        tbFormaCobrancaFlag.setRatioBatchSize(20);
        getTables().put(tbFormaCobrancaFlag, "tbFormaCobrancaFlag");
        tbFormaCobrancaFlag.applyProperties();
    }

    public CLIENTE_TRANSPORTADORA_FLAG tbClienteTransportadoraFlag;

    private void init_tbClienteTransportadoraFlag() {
        tbClienteTransportadoraFlag = rn.tbClienteTransportadoraFlag;
        tbClienteTransportadoraFlag.setName("tbClienteTransportadoraFlag");
        tbClienteTransportadoraFlag.setMaxRowCount(0);
        tbClienteTransportadoraFlag.setWKey("310036;31009");
        tbClienteTransportadoraFlag.setRatioBatchSize(20);
        getTables().put(tbClienteTransportadoraFlag, "tbClienteTransportadoraFlag");
        tbClienteTransportadoraFlag.applyProperties();
    }

    public TRANSPORTADORAS_FLAG tbTransportadorasFlag;

    private void init_tbTransportadorasFlag() {
        tbTransportadorasFlag = rn.tbTransportadorasFlag;
        tbTransportadorasFlag.setName("tbTransportadorasFlag");
        tbTransportadorasFlag.setMaxRowCount(0);
        tbTransportadorasFlag.setWKey("310036;310010");
        tbTransportadorasFlag.setRatioBatchSize(20);
        getTables().put(tbTransportadorasFlag, "tbTransportadorasFlag");
        tbTransportadorasFlag.applyProperties();
    }

    public CLIENTE_FORMA_PGTO_FLAG tbClienteFormaPgtoFlag;

    private void init_tbClienteFormaPgtoFlag() {
        tbClienteFormaPgtoFlag = rn.tbClienteFormaPgtoFlag;
        tbClienteFormaPgtoFlag.setName("tbClienteFormaPgtoFlag");
        tbClienteFormaPgtoFlag.setMaxRowCount(0);
        tbClienteFormaPgtoFlag.setWKey("310036;310011");
        tbClienteFormaPgtoFlag.setRatioBatchSize(20);
        getTables().put(tbClienteFormaPgtoFlag, "tbClienteFormaPgtoFlag");
        tbClienteFormaPgtoFlag.applyProperties();
    }

    public CLIENTES_DESCONTOS_FLAG tbClientesDescontosFlag;

    private void init_tbClientesDescontosFlag() {
        tbClientesDescontosFlag = rn.tbClientesDescontosFlag;
        tbClientesDescontosFlag.setName("tbClientesDescontosFlag");
        tbClientesDescontosFlag.setMaxRowCount(200);
        tbClientesDescontosFlag.setWKey("310036;310012");
        tbClientesDescontosFlag.setRatioBatchSize(20);
        getTables().put(tbClientesDescontosFlag, "tbClientesDescontosFlag");
        tbClientesDescontosFlag.applyProperties();
    }

    public ITENS_PER_DESC_FLAG tbItensPerDescFlag;

    private void init_tbItensPerDescFlag() {
        tbItensPerDescFlag = rn.tbItensPerDescFlag;
        tbItensPerDescFlag.setName("tbItensPerDescFlag");
        tbItensPerDescFlag.setMaxRowCount(0);
        tbItensPerDescFlag.setWKey("310036;310016");
        tbItensPerDescFlag.setRatioBatchSize(20);
        getTables().put(tbItensPerDescFlag, "tbItensPerDescFlag");
        tbItensPerDescFlag.applyProperties();
    }

    public VENDEDOR_RESP_FLAG tbVendedorRespFlag;

    private void init_tbVendedorRespFlag() {
        tbVendedorRespFlag = rn.tbVendedorRespFlag;
        tbVendedorRespFlag.setName("tbVendedorRespFlag");
        tbVendedorRespFlag.setMaxRowCount(0);
        tbVendedorRespFlag.setWKey("310036;310017");
        tbVendedorRespFlag.setRatioBatchSize(20);
        getTables().put(tbVendedorRespFlag, "tbVendedorRespFlag");
        tbVendedorRespFlag.applyProperties();
    }

    public DADOS_JURIDICOS_FLAG tbDadosJuridicosFlag;

    private void init_tbDadosJuridicosFlag() {
        tbDadosJuridicosFlag = rn.tbDadosJuridicosFlag;
        tbDadosJuridicosFlag.setName("tbDadosJuridicosFlag");
        tbDadosJuridicosFlag.setMaxRowCount(0);
        tbDadosJuridicosFlag.setWKey("310036;310018");
        tbDadosJuridicosFlag.setRatioBatchSize(20);
        getTables().put(tbDadosJuridicosFlag, "tbDadosJuridicosFlag");
        tbDadosJuridicosFlag.applyProperties();
    }

    public BUSCA_REPRESENTANTE_CLIENTE tbBuscaRepresentanteCliente;

    private void init_tbBuscaRepresentanteCliente() {
        tbBuscaRepresentanteCliente = rn.tbBuscaRepresentanteCliente;
        tbBuscaRepresentanteCliente.setName("tbBuscaRepresentanteCliente");
        tbBuscaRepresentanteCliente.setMaxRowCount(0);
        tbBuscaRepresentanteCliente.setWKey("310036;53003");
        tbBuscaRepresentanteCliente.setRatioBatchSize(20);
        getTables().put(tbBuscaRepresentanteCliente, "tbBuscaRepresentanteCliente");
        tbBuscaRepresentanteCliente.applyProperties();
    }

    public LISTA_CLIENTE_FORMAS_PAGAMENTO tbListaClienteFormasPagamento;

    private void init_tbListaClienteFormasPagamento() {
        tbListaClienteFormasPagamento = rn.tbListaClienteFormasPagamento;
        tbListaClienteFormasPagamento.setName("tbListaClienteFormasPagamento");
        tbListaClienteFormasPagamento.setMaxRowCount(200);
        tbListaClienteFormasPagamento.setWKey("310036;53005");
        tbListaClienteFormasPagamento.setRatioBatchSize(20);
        getTables().put(tbListaClienteFormasPagamento, "tbListaClienteFormasPagamento");
        tbListaClienteFormasPagamento.applyProperties();
    }

    public CLIENTE_FORMAS_PGTO tbClienteFormasPgto;

    private void init_tbClienteFormasPgto() {
        tbClienteFormasPgto = rn.tbClienteFormasPgto;
        tbClienteFormasPgto.setName("tbClienteFormasPgto");
        tbClienteFormasPgto.setMaxRowCount(200);
        tbClienteFormasPgto.setWKey("310036;53006");
        tbClienteFormasPgto.setRatioBatchSize(20);
        getTables().put(tbClienteFormasPgto, "tbClienteFormasPgto");
        tbClienteFormasPgto.applyProperties();
    }

    public CLI_DESC_EMP tbDescEmp;

    private void init_tbDescEmp() {
        tbDescEmp = rn.tbDescEmp;
        tbDescEmp.setName("tbDescEmp");
        tbDescEmp.setMaxRowCount(200);
        tbDescEmp.setWKey("310036;53008");
        tbDescEmp.setRatioBatchSize(20);
        getTables().put(tbDescEmp, "tbDescEmp");
        tbDescEmp.applyProperties();
    }

    public EMPRESAS_FILIAIS_SEL tbEmpresasFiliaisSel;

    private void init_tbEmpresasFiliaisSel() {
        tbEmpresasFiliaisSel = rn.tbEmpresasFiliaisSel;
        tbEmpresasFiliaisSel.setName("tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.setMaxRowCount(200);
        tbEmpresasFiliaisSel.setWKey("310036;530012");
        tbEmpresasFiliaisSel.setRatioBatchSize(20);
        getTables().put(tbEmpresasFiliaisSel, "tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.applyProperties();
    }

    public CLIENTES_ESPECIAIS_MARGEM tbClientesEspeciaisMargem;

    private void init_tbClientesEspeciaisMargem() {
        tbClientesEspeciaisMargem = rn.tbClientesEspeciaisMargem;
        tbClientesEspeciaisMargem.setName("tbClientesEspeciaisMargem");
        tbClientesEspeciaisMargem.setMaxRowCount(200);
        tbClientesEspeciaisMargem.setWKey("310036;530013");
        tbClientesEspeciaisMargem.setRatioBatchSize(20);
        getTables().put(tbClientesEspeciaisMargem, "tbClientesEspeciaisMargem");
        tbClientesEspeciaisMargem.applyProperties();
    }

    public BUSCA_CLI_ESP_MARG_EMP tbCliEspMargEmp;

    private void init_tbCliEspMargEmp() {
        tbCliEspMargEmp = rn.tbCliEspMargEmp;
        tbCliEspMargEmp.setName("tbCliEspMargEmp");
        tbCliEspMargEmp.setMaxRowCount(200);
        tbCliEspMargEmp.setWKey("310036;161014");
        tbCliEspMargEmp.setRatioBatchSize(20);
        getTables().put(tbCliEspMargEmp, "tbCliEspMargEmp");
        tbCliEspMargEmp.applyProperties();
    }

    public LISTA_CLIENTE_RESPONSAVEL tbListaClienteResponsavel;

    private void init_tbListaClienteResponsavel() {
        tbListaClienteResponsavel = rn.tbListaClienteResponsavel;
        tbListaClienteResponsavel.setName("tbListaClienteResponsavel");
        tbListaClienteResponsavel.setMaxRowCount(200);
        tbListaClienteResponsavel.setWKey("310036;28401");
        tbListaClienteResponsavel.setRatioBatchSize(20);
        getTables().put(tbListaClienteResponsavel, "tbListaClienteResponsavel");
        tbListaClienteResponsavel.applyProperties();
    }

    public BUSCA_CLIENTE_SEGMENTO tbClienteSegmento;

    private void init_tbClienteSegmento() {
        tbClienteSegmento = rn.tbClienteSegmento;
        tbClienteSegmento.setName("tbClienteSegmento");
        tbClienteSegmento.setMaxRowCount(200);
        tbClienteSegmento.setWKey("310036;507015");
        tbClienteSegmento.setRatioBatchSize(20);
        getTables().put(tbClienteSegmento, "tbClienteSegmento");
        tbClienteSegmento.applyProperties();
    }

    protected TFForm FrmClientesFlags = this;
    private void init_FrmClientesFlags() {
        FrmClientesFlags.setName("FrmClientesFlags");
        FrmClientesFlags.setCaption("Flags - Condi\u00E7\u00F5es especiais");
        FrmClientesFlags.setClientHeight(622);
        FrmClientesFlags.setClientWidth(1020);
        FrmClientesFlags.setColor("clBtnFace");
        FrmClientesFlags.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmClientesFlags", "FrmClientesFlags", "OnCreate");
        });
        FrmClientesFlags.setWOrigem("EhMain");
        FrmClientesFlags.setWKey("310036");
        FrmClientesFlags.setSpacing(0);
        FrmClientesFlags.applyProperties();
    }

    public TFHBox hBoxLinha01 = new TFHBox();

    private void init_hBoxLinha01() {
        hBoxLinha01.setName("hBoxLinha01");
        hBoxLinha01.setLeft(0);
        hBoxLinha01.setTop(0);
        hBoxLinha01.setWidth(1020);
        hBoxLinha01.setHeight(57);
        hBoxLinha01.setAlign("alTop");
        hBoxLinha01.setBorderStyle("stNone");
        hBoxLinha01.setPaddingTop(0);
        hBoxLinha01.setPaddingLeft(5);
        hBoxLinha01.setPaddingRight(0);
        hBoxLinha01.setPaddingBottom(0);
        hBoxLinha01.setMarginTop(0);
        hBoxLinha01.setMarginLeft(0);
        hBoxLinha01.setMarginRight(0);
        hBoxLinha01.setMarginBottom(0);
        hBoxLinha01.setSpacing(1);
        hBoxLinha01.setFlexVflex("ftFalse");
        hBoxLinha01.setFlexHflex("ftTrue");
        hBoxLinha01.setScrollable(false);
        hBoxLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxLinha01.setBoxShadowConfigOpacity(75);
        hBoxLinha01.setVAlign("tvTop");
        FrmClientesFlags.addChildren(hBoxLinha01);
        hBoxLinha01.applyProperties();
    }

    public TFVBox vBoxNome = new TFVBox();

    private void init_vBoxNome() {
        vBoxNome.setName("vBoxNome");
        vBoxNome.setLeft(0);
        vBoxNome.setTop(0);
        vBoxNome.setWidth(630);
        vBoxNome.setHeight(47);
        vBoxNome.setBorderStyle("stNone");
        vBoxNome.setPaddingTop(0);
        vBoxNome.setPaddingLeft(0);
        vBoxNome.setPaddingRight(0);
        vBoxNome.setPaddingBottom(0);
        vBoxNome.setMarginTop(0);
        vBoxNome.setMarginLeft(0);
        vBoxNome.setMarginRight(0);
        vBoxNome.setMarginBottom(0);
        vBoxNome.setSpacing(1);
        vBoxNome.setFlexVflex("ftFalse");
        vBoxNome.setFlexHflex("ftTrue");
        vBoxNome.setScrollable(false);
        vBoxNome.setBoxShadowConfigHorizontalLength(10);
        vBoxNome.setBoxShadowConfigVerticalLength(10);
        vBoxNome.setBoxShadowConfigBlurRadius(5);
        vBoxNome.setBoxShadowConfigSpreadRadius(0);
        vBoxNome.setBoxShadowConfigShadowColor("clBlack");
        vBoxNome.setBoxShadowConfigOpacity(75);
        hBoxLinha01.addChildren(vBoxNome);
        vBoxNome.applyProperties();
    }

    public TFLabel lblNomeCliente2 = new TFLabel();

    private void init_lblNomeCliente2() {
        lblNomeCliente2.setName("lblNomeCliente2");
        lblNomeCliente2.setLeft(0);
        lblNomeCliente2.setTop(0);
        lblNomeCliente2.setWidth(32);
        lblNomeCliente2.setHeight(13);
        lblNomeCliente2.setCaption("Nome");
        lblNomeCliente2.setFontColor("clWindowText");
        lblNomeCliente2.setFontSize(-11);
        lblNomeCliente2.setFontName("Tahoma");
        lblNomeCliente2.setFontStyle("[fsBold]");
        lblNomeCliente2.setVerticalAlignment("taVerticalCenter");
        lblNomeCliente2.setWordBreak(false);
        vBoxNome.addChildren(lblNomeCliente2);
        lblNomeCliente2.applyProperties();
    }

    public TFLabel lblNomeCliente = new TFLabel();

    private void init_lblNomeCliente() {
        lblNomeCliente.setName("lblNomeCliente");
        lblNomeCliente.setLeft(0);
        lblNomeCliente.setTop(14);
        lblNomeCliente.setWidth(197);
        lblNomeCliente.setHeight(24);
        lblNomeCliente.setCaption("Antonio Carlos Orione");
        lblNomeCliente.setFontColor("clNavy");
        lblNomeCliente.setFontSize(-19);
        lblNomeCliente.setFontName("Trebuchet MS");
        lblNomeCliente.setFontStyle("[fsBold]");
        lblNomeCliente.setFieldName("NOME");
        lblNomeCliente.setTable(tbClienteDiversoFlag);
        lblNomeCliente.setVerticalAlignment("taVerticalCenter");
        lblNomeCliente.setWordBreak(false);
        vBoxNome.addChildren(lblNomeCliente);
        lblNomeCliente.applyProperties();
    }

    public TFHBox hBoxSeparadorHorizontal01 = new TFHBox();

    private void init_hBoxSeparadorHorizontal01() {
        hBoxSeparadorHorizontal01.setName("hBoxSeparadorHorizontal01");
        hBoxSeparadorHorizontal01.setLeft(630);
        hBoxSeparadorHorizontal01.setTop(0);
        hBoxSeparadorHorizontal01.setWidth(13);
        hBoxSeparadorHorizontal01.setHeight(47);
        hBoxSeparadorHorizontal01.setBorderStyle("stNone");
        hBoxSeparadorHorizontal01.setPaddingTop(0);
        hBoxSeparadorHorizontal01.setPaddingLeft(0);
        hBoxSeparadorHorizontal01.setPaddingRight(0);
        hBoxSeparadorHorizontal01.setPaddingBottom(0);
        hBoxSeparadorHorizontal01.setMarginTop(0);
        hBoxSeparadorHorizontal01.setMarginLeft(0);
        hBoxSeparadorHorizontal01.setMarginRight(0);
        hBoxSeparadorHorizontal01.setMarginBottom(0);
        hBoxSeparadorHorizontal01.setSpacing(1);
        hBoxSeparadorHorizontal01.setFlexVflex("ftFalse");
        hBoxSeparadorHorizontal01.setFlexHflex("ftFalse");
        hBoxSeparadorHorizontal01.setScrollable(false);
        hBoxSeparadorHorizontal01.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorHorizontal01.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorHorizontal01.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorHorizontal01.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorHorizontal01.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorHorizontal01.setBoxShadowConfigOpacity(75);
        hBoxSeparadorHorizontal01.setVAlign("tvTop");
        hBoxLinha01.addChildren(hBoxSeparadorHorizontal01);
        hBoxSeparadorHorizontal01.applyProperties();
    }

    public TFVBox vBoxCodigo = new TFVBox();

    private void init_vBoxCodigo() {
        vBoxCodigo.setName("vBoxCodigo");
        vBoxCodigo.setLeft(643);
        vBoxCodigo.setTop(0);
        vBoxCodigo.setWidth(185);
        vBoxCodigo.setHeight(47);
        vBoxCodigo.setBorderStyle("stNone");
        vBoxCodigo.setPaddingTop(0);
        vBoxCodigo.setPaddingLeft(0);
        vBoxCodigo.setPaddingRight(0);
        vBoxCodigo.setPaddingBottom(0);
        vBoxCodigo.setMarginTop(0);
        vBoxCodigo.setMarginLeft(0);
        vBoxCodigo.setMarginRight(0);
        vBoxCodigo.setMarginBottom(0);
        vBoxCodigo.setSpacing(1);
        vBoxCodigo.setFlexVflex("ftFalse");
        vBoxCodigo.setFlexHflex("ftFalse");
        vBoxCodigo.setScrollable(false);
        vBoxCodigo.setBoxShadowConfigHorizontalLength(10);
        vBoxCodigo.setBoxShadowConfigVerticalLength(10);
        vBoxCodigo.setBoxShadowConfigBlurRadius(5);
        vBoxCodigo.setBoxShadowConfigSpreadRadius(0);
        vBoxCodigo.setBoxShadowConfigShadowColor("clBlack");
        vBoxCodigo.setBoxShadowConfigOpacity(75);
        hBoxLinha01.addChildren(vBoxCodigo);
        vBoxCodigo.applyProperties();
    }

    public TFLabel lblCodigo = new TFLabel();

    private void init_lblCodigo() {
        lblCodigo.setName("lblCodigo");
        lblCodigo.setLeft(0);
        lblCodigo.setTop(0);
        lblCodigo.setWidth(38);
        lblCodigo.setHeight(13);
        lblCodigo.setCaption("C\u00F3digo");
        lblCodigo.setFontColor("clWindowText");
        lblCodigo.setFontSize(-11);
        lblCodigo.setFontName("Tahoma");
        lblCodigo.setFontStyle("[fsBold]");
        lblCodigo.setVerticalAlignment("taVerticalCenter");
        lblCodigo.setWordBreak(false);
        vBoxCodigo.addChildren(lblCodigo);
        lblCodigo.applyProperties();
    }

    public TFLabel lblCodCliente = new TFLabel();

    private void init_lblCodCliente() {
        lblCodCliente.setName("lblCodCliente");
        lblCodCliente.setLeft(0);
        lblCodCliente.setTop(14);
        lblCodCliente.setWidth(142);
        lblCodCliente.setHeight(24);
        lblCodCliente.setCaption("993.313.301-20");
        lblCodCliente.setFontColor("clNavy");
        lblCodCliente.setFontSize(-19);
        lblCodCliente.setFontName("Trebuchet MS");
        lblCodCliente.setFontStyle("[fsBold]");
        lblCodCliente.setFieldName("CPF_CGC");
        lblCodCliente.setTable(tbClienteDiversoFlag);
        lblCodCliente.setVerticalAlignment("taVerticalCenter");
        lblCodCliente.setWordBreak(false);
        vBoxCodigo.addChildren(lblCodCliente);
        lblCodCliente.applyProperties();
    }

    public TFHBox hBoxSeparadorHorizontal02 = new TFHBox();

    private void init_hBoxSeparadorHorizontal02() {
        hBoxSeparadorHorizontal02.setName("hBoxSeparadorHorizontal02");
        hBoxSeparadorHorizontal02.setLeft(828);
        hBoxSeparadorHorizontal02.setTop(0);
        hBoxSeparadorHorizontal02.setWidth(12);
        hBoxSeparadorHorizontal02.setHeight(47);
        hBoxSeparadorHorizontal02.setBorderStyle("stNone");
        hBoxSeparadorHorizontal02.setPaddingTop(0);
        hBoxSeparadorHorizontal02.setPaddingLeft(0);
        hBoxSeparadorHorizontal02.setPaddingRight(0);
        hBoxSeparadorHorizontal02.setPaddingBottom(0);
        hBoxSeparadorHorizontal02.setMarginTop(0);
        hBoxSeparadorHorizontal02.setMarginLeft(0);
        hBoxSeparadorHorizontal02.setMarginRight(0);
        hBoxSeparadorHorizontal02.setMarginBottom(0);
        hBoxSeparadorHorizontal02.setSpacing(1);
        hBoxSeparadorHorizontal02.setFlexVflex("ftFalse");
        hBoxSeparadorHorizontal02.setFlexHflex("ftFalse");
        hBoxSeparadorHorizontal02.setScrollable(false);
        hBoxSeparadorHorizontal02.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparadorHorizontal02.setBoxShadowConfigVerticalLength(10);
        hBoxSeparadorHorizontal02.setBoxShadowConfigBlurRadius(5);
        hBoxSeparadorHorizontal02.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparadorHorizontal02.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparadorHorizontal02.setBoxShadowConfigOpacity(75);
        hBoxSeparadorHorizontal02.setVAlign("tvTop");
        hBoxLinha01.addChildren(hBoxSeparadorHorizontal02);
        hBoxSeparadorHorizontal02.applyProperties();
    }

    public TFVBox vBoxCurva = new TFVBox();

    private void init_vBoxCurva() {
        vBoxCurva.setName("vBoxCurva");
        vBoxCurva.setLeft(840);
        vBoxCurva.setTop(0);
        vBoxCurva.setWidth(173);
        vBoxCurva.setHeight(47);
        vBoxCurva.setBorderStyle("stNone");
        vBoxCurva.setPaddingTop(0);
        vBoxCurva.setPaddingLeft(0);
        vBoxCurva.setPaddingRight(0);
        vBoxCurva.setPaddingBottom(0);
        vBoxCurva.setMarginTop(0);
        vBoxCurva.setMarginLeft(0);
        vBoxCurva.setMarginRight(0);
        vBoxCurva.setMarginBottom(0);
        vBoxCurva.setSpacing(1);
        vBoxCurva.setFlexVflex("ftFalse");
        vBoxCurva.setFlexHflex("ftFalse");
        vBoxCurva.setScrollable(false);
        vBoxCurva.setBoxShadowConfigHorizontalLength(10);
        vBoxCurva.setBoxShadowConfigVerticalLength(10);
        vBoxCurva.setBoxShadowConfigBlurRadius(5);
        vBoxCurva.setBoxShadowConfigSpreadRadius(0);
        vBoxCurva.setBoxShadowConfigShadowColor("clBlack");
        vBoxCurva.setBoxShadowConfigOpacity(75);
        hBoxLinha01.addChildren(vBoxCurva);
        vBoxCurva.applyProperties();
    }

    public TFLabel lblCurva = new TFLabel();

    private void init_lblCurva() {
        lblCurva.setName("lblCurva");
        lblCurva.setLeft(0);
        lblCurva.setTop(0);
        lblCurva.setWidth(33);
        lblCurva.setHeight(13);
        lblCurva.setCaption("Curva");
        lblCurva.setFontColor("clWindowText");
        lblCurva.setFontSize(-11);
        lblCurva.setFontName("Tahoma");
        lblCurva.setFontStyle("[fsBold]");
        lblCurva.setVerticalAlignment("taVerticalCenter");
        lblCurva.setWordBreak(false);
        vBoxCurva.addChildren(lblCurva);
        lblCurva.applyProperties();
    }

    public TFLabel FLabel11 = new TFLabel();

    private void init_FLabel11() {
        FLabel11.setName("FLabel11");
        FLabel11.setLeft(0);
        FLabel11.setTop(14);
        FLabel11.setWidth(52);
        FLabel11.setHeight(24);
        FLabel11.setCaption("Curva");
        FLabel11.setFontColor("clNavy");
        FLabel11.setFontSize(-19);
        FLabel11.setFontName("Trebuchet MS");
        FLabel11.setFontStyle("[fsBold]");
        FLabel11.setFieldName("CURVA");
        FLabel11.setTable(tbClienteDiversoFlag);
        FLabel11.setVerticalAlignment("taVerticalCenter");
        FLabel11.setWordBreak(false);
        vBoxCurva.addChildren(FLabel11);
        FLabel11.applyProperties();
    }

    public TFPageControl pgFlags = new TFPageControl();

    private void init_pgFlags() {
        pgFlags.setName("pgFlags");
        pgFlags.setLeft(0);
        pgFlags.setTop(57);
        pgFlags.setWidth(1020);
        pgFlags.setHeight(565);
        pgFlags.setAlign("alClient");
        pgFlags.setTabPosition("tpTop");
        pgFlags.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            pgFlagsChange(event);
            processarFlow("FrmClientesFlags", "pgFlags", "OnChange");
        });
        pgFlags.setFlexVflex("ftTrue");
        pgFlags.setFlexHflex("ftTrue");
        pgFlags.setRenderStyle("rsTabbed");
        pgFlags.applyProperties();
        FrmClientesFlags.addChildren(pgFlags);
    }

    public TFTabsheet tabDesconto = new TFTabsheet();

    private void init_tabDesconto() {
        tabDesconto.setName("tabDesconto");
        tabDesconto.setCaption("Desconto");
        tabDesconto.setVisible(true);
        tabDesconto.setClosable(false);
        pgFlags.addChildren(tabDesconto);
        tabDesconto.applyProperties();
    }

    public TFVBox vBoxTabDesconto = new TFVBox();

    private void init_vBoxTabDesconto() {
        vBoxTabDesconto.setName("vBoxTabDesconto");
        vBoxTabDesconto.setLeft(0);
        vBoxTabDesconto.setTop(0);
        vBoxTabDesconto.setWidth(1012);
        vBoxTabDesconto.setHeight(537);
        vBoxTabDesconto.setAlign("alClient");
        vBoxTabDesconto.setBorderStyle("stNone");
        vBoxTabDesconto.setPaddingTop(0);
        vBoxTabDesconto.setPaddingLeft(0);
        vBoxTabDesconto.setPaddingRight(0);
        vBoxTabDesconto.setPaddingBottom(0);
        vBoxTabDesconto.setMarginTop(0);
        vBoxTabDesconto.setMarginLeft(0);
        vBoxTabDesconto.setMarginRight(0);
        vBoxTabDesconto.setMarginBottom(0);
        vBoxTabDesconto.setSpacing(1);
        vBoxTabDesconto.setFlexVflex("ftTrue");
        vBoxTabDesconto.setFlexHflex("ftTrue");
        vBoxTabDesconto.setScrollable(false);
        vBoxTabDesconto.setBoxShadowConfigHorizontalLength(10);
        vBoxTabDesconto.setBoxShadowConfigVerticalLength(10);
        vBoxTabDesconto.setBoxShadowConfigBlurRadius(5);
        vBoxTabDesconto.setBoxShadowConfigSpreadRadius(0);
        vBoxTabDesconto.setBoxShadowConfigShadowColor("clBlack");
        vBoxTabDesconto.setBoxShadowConfigOpacity(75);
        tabDesconto.addChildren(vBoxTabDesconto);
        vBoxTabDesconto.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(1000);
        FHBox1.setHeight(5);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        vBoxTabDesconto.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox hBoxTabDescontoLinha01 = new TFHBox();

    private void init_hBoxTabDescontoLinha01() {
        hBoxTabDescontoLinha01.setName("hBoxTabDescontoLinha01");
        hBoxTabDescontoLinha01.setLeft(0);
        hBoxTabDescontoLinha01.setTop(6);
        hBoxTabDescontoLinha01.setWidth(1000);
        hBoxTabDescontoLinha01.setHeight(60);
        hBoxTabDescontoLinha01.setBorderStyle("stNone");
        hBoxTabDescontoLinha01.setPaddingTop(0);
        hBoxTabDescontoLinha01.setPaddingLeft(0);
        hBoxTabDescontoLinha01.setPaddingRight(0);
        hBoxTabDescontoLinha01.setPaddingBottom(0);
        hBoxTabDescontoLinha01.setMarginTop(0);
        hBoxTabDescontoLinha01.setMarginLeft(0);
        hBoxTabDescontoLinha01.setMarginRight(0);
        hBoxTabDescontoLinha01.setMarginBottom(0);
        hBoxTabDescontoLinha01.setSpacing(1);
        hBoxTabDescontoLinha01.setFlexVflex("ftMin");
        hBoxTabDescontoLinha01.setFlexHflex("ftTrue");
        hBoxTabDescontoLinha01.setScrollable(false);
        hBoxTabDescontoLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxTabDescontoLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxTabDescontoLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxTabDescontoLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxTabDescontoLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxTabDescontoLinha01.setBoxShadowConfigOpacity(75);
        hBoxTabDescontoLinha01.setVAlign("tvTop");
        vBoxTabDesconto.addChildren(hBoxTabDescontoLinha01);
        hBoxTabDescontoLinha01.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(0);
        FHBox2.setWidth(5);
        FHBox2.setHeight(20);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        hBoxTabDescontoLinha01.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox vBoxSegmento = new TFVBox();

    private void init_vBoxSegmento() {
        vBoxSegmento.setName("vBoxSegmento");
        vBoxSegmento.setLeft(5);
        vBoxSegmento.setTop(0);
        vBoxSegmento.setWidth(250);
        vBoxSegmento.setHeight(50);
        vBoxSegmento.setBorderStyle("stNone");
        vBoxSegmento.setPaddingTop(0);
        vBoxSegmento.setPaddingLeft(0);
        vBoxSegmento.setPaddingRight(0);
        vBoxSegmento.setPaddingBottom(0);
        vBoxSegmento.setMarginTop(0);
        vBoxSegmento.setMarginLeft(0);
        vBoxSegmento.setMarginRight(0);
        vBoxSegmento.setMarginBottom(0);
        vBoxSegmento.setSpacing(1);
        vBoxSegmento.setFlexVflex("ftMin");
        vBoxSegmento.setFlexHflex("ftTrue");
        vBoxSegmento.setScrollable(false);
        vBoxSegmento.setBoxShadowConfigHorizontalLength(10);
        vBoxSegmento.setBoxShadowConfigVerticalLength(10);
        vBoxSegmento.setBoxShadowConfigBlurRadius(5);
        vBoxSegmento.setBoxShadowConfigSpreadRadius(0);
        vBoxSegmento.setBoxShadowConfigShadowColor("clBlack");
        vBoxSegmento.setBoxShadowConfigOpacity(75);
        hBoxTabDescontoLinha01.addChildren(vBoxSegmento);
        vBoxSegmento.applyProperties();
    }

    public TFLabel FLabel10 = new TFLabel();

    private void init_FLabel10() {
        FLabel10.setName("FLabel10");
        FLabel10.setLeft(0);
        FLabel10.setTop(0);
        FLabel10.setWidth(190);
        FLabel10.setHeight(13);
        FLabel10.setHint("Segmento (afeta Desconto Letra)");
        FLabel10.setCaption("Segmento (afeta Desconto Letra)");
        FLabel10.setFontColor("clWindowText");
        FLabel10.setFontSize(-11);
        FLabel10.setFontName("Tahoma");
        FLabel10.setFontStyle("[fsBold]");
        FLabel10.setVerticalAlignment("taVerticalCenter");
        FLabel10.setWordBreak(false);
        vBoxSegmento.addChildren(FLabel10);
        FLabel10.applyProperties();
    }

    public TFCombo cboSegmento = new TFCombo();

    private void init_cboSegmento() {
        cboSegmento.setName("cboSegmento");
        cboSegmento.setLeft(0);
        cboSegmento.setTop(14);
        cboSegmento.setWidth(240);
        cboSegmento.setHeight(21);
        cboSegmento.setHint("Segmento (afeta Desconto Letra)\r\n\r\nExibido para pessoas jur\u00EDdicas");
        cboSegmento.setTable(tbDadosJuridicosFlag);
        cboSegmento.setLookupTable(tbClienteSegmento);
        cboSegmento.setFieldName("ID_SEGMENTO");
        cboSegmento.setLookupKey("ID_SEGMENTO");
        cboSegmento.setLookupDesc("SEGMENTO_DESCRICAO_CODIGO");
        cboSegmento.setFlex(true);
        cboSegmento.setHelpCaption("Segmento (afeta Desconto Letra)");
        cboSegmento.setReadOnly(true);
        cboSegmento.setRequired(false);
        cboSegmento.setPrompt("Segmento (afeta Desconto Letra)");
        cboSegmento.setConstraintCheckWhen("cwImmediate");
        cboSegmento.setConstraintCheckType("ctExpression");
        cboSegmento.setConstraintFocusOnError(false);
        cboSegmento.setConstraintEnableUI(true);
        cboSegmento.setConstraintEnabled(false);
        cboSegmento.setConstraintFormCheck(true);
        cboSegmento.setClearOnDelKey(false);
        cboSegmento.setUseClearButton(true);
        cboSegmento.setHideClearButtonOnNullValue(true);
        cboSegmento.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboSegmentoChange(event);
            processarFlow("FrmClientesFlags", "cboSegmento", "OnChange");
        });
        cboSegmento.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboSegmentoClearClick(event);
            processarFlow("FrmClientesFlags", "cboSegmento", "OnClearClick");
        });
        vBoxSegmento.addChildren(cboSegmento);
        cboSegmento.applyProperties();
        addValidatable(cboSegmento);
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(255);
        FHBox5.setTop(0);
        FHBox5.setWidth(5);
        FHBox5.setHeight(20);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftFalse");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        hBoxTabDescontoLinha01.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(67);
        FHBox3.setWidth(1000);
        FHBox3.setHeight(5);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftFalse");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        vBoxTabDesconto.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFHBox hBoxTabDescontoLinha02 = new TFHBox();

    private void init_hBoxTabDescontoLinha02() {
        hBoxTabDescontoLinha02.setName("hBoxTabDescontoLinha02");
        hBoxTabDescontoLinha02.setLeft(0);
        hBoxTabDescontoLinha02.setTop(73);
        hBoxTabDescontoLinha02.setWidth(1000);
        hBoxTabDescontoLinha02.setHeight(200);
        hBoxTabDescontoLinha02.setBorderStyle("stNone");
        hBoxTabDescontoLinha02.setPaddingTop(0);
        hBoxTabDescontoLinha02.setPaddingLeft(0);
        hBoxTabDescontoLinha02.setPaddingRight(0);
        hBoxTabDescontoLinha02.setPaddingBottom(0);
        hBoxTabDescontoLinha02.setMarginTop(0);
        hBoxTabDescontoLinha02.setMarginLeft(0);
        hBoxTabDescontoLinha02.setMarginRight(0);
        hBoxTabDescontoLinha02.setMarginBottom(0);
        hBoxTabDescontoLinha02.setSpacing(1);
        hBoxTabDescontoLinha02.setFlexVflex("ftMin");
        hBoxTabDescontoLinha02.setFlexHflex("ftTrue");
        hBoxTabDescontoLinha02.setScrollable(false);
        hBoxTabDescontoLinha02.setBoxShadowConfigHorizontalLength(10);
        hBoxTabDescontoLinha02.setBoxShadowConfigVerticalLength(10);
        hBoxTabDescontoLinha02.setBoxShadowConfigBlurRadius(5);
        hBoxTabDescontoLinha02.setBoxShadowConfigSpreadRadius(0);
        hBoxTabDescontoLinha02.setBoxShadowConfigShadowColor("clBlack");
        hBoxTabDescontoLinha02.setBoxShadowConfigOpacity(75);
        hBoxTabDescontoLinha02.setVAlign("tvTop");
        vBoxTabDesconto.addChildren(hBoxTabDescontoLinha02);
        hBoxTabDescontoLinha02.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(5);
        FHBox11.setHeight(20);
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftFalse");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        hBoxTabDescontoLinha02.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFVBox vBoxVendaDescontoPorLetra = new TFVBox();

    private void init_vBoxVendaDescontoPorLetra() {
        vBoxVendaDescontoPorLetra.setName("vBoxVendaDescontoPorLetra");
        vBoxVendaDescontoPorLetra.setLeft(5);
        vBoxVendaDescontoPorLetra.setTop(0);
        vBoxVendaDescontoPorLetra.setWidth(500);
        vBoxVendaDescontoPorLetra.setHeight(190);
        vBoxVendaDescontoPorLetra.setBorderStyle("stNone");
        vBoxVendaDescontoPorLetra.setPaddingTop(0);
        vBoxVendaDescontoPorLetra.setPaddingLeft(0);
        vBoxVendaDescontoPorLetra.setPaddingRight(0);
        vBoxVendaDescontoPorLetra.setPaddingBottom(0);
        vBoxVendaDescontoPorLetra.setMarginTop(0);
        vBoxVendaDescontoPorLetra.setMarginLeft(0);
        vBoxVendaDescontoPorLetra.setMarginRight(0);
        vBoxVendaDescontoPorLetra.setMarginBottom(0);
        vBoxVendaDescontoPorLetra.setSpacing(1);
        vBoxVendaDescontoPorLetra.setFlexVflex("ftFalse");
        vBoxVendaDescontoPorLetra.setFlexHflex("ftMin");
        vBoxVendaDescontoPorLetra.setScrollable(false);
        vBoxVendaDescontoPorLetra.setBoxShadowConfigHorizontalLength(10);
        vBoxVendaDescontoPorLetra.setBoxShadowConfigVerticalLength(10);
        vBoxVendaDescontoPorLetra.setBoxShadowConfigBlurRadius(5);
        vBoxVendaDescontoPorLetra.setBoxShadowConfigSpreadRadius(0);
        vBoxVendaDescontoPorLetra.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendaDescontoPorLetra.setBoxShadowConfigOpacity(75);
        hBoxTabDescontoLinha02.addChildren(vBoxVendaDescontoPorLetra);
        vBoxVendaDescontoPorLetra.applyProperties();
    }

    public TFHBox hBoxDecontoPorLetraLinha01 = new TFHBox();

    private void init_hBoxDecontoPorLetraLinha01() {
        hBoxDecontoPorLetraLinha01.setName("hBoxDecontoPorLetraLinha01");
        hBoxDecontoPorLetraLinha01.setLeft(0);
        hBoxDecontoPorLetraLinha01.setTop(0);
        hBoxDecontoPorLetraLinha01.setWidth(480);
        hBoxDecontoPorLetraLinha01.setHeight(30);
        hBoxDecontoPorLetraLinha01.setBorderStyle("stNone");
        hBoxDecontoPorLetraLinha01.setPaddingTop(0);
        hBoxDecontoPorLetraLinha01.setPaddingLeft(0);
        hBoxDecontoPorLetraLinha01.setPaddingRight(0);
        hBoxDecontoPorLetraLinha01.setPaddingBottom(0);
        hBoxDecontoPorLetraLinha01.setMarginTop(0);
        hBoxDecontoPorLetraLinha01.setMarginLeft(0);
        hBoxDecontoPorLetraLinha01.setMarginRight(0);
        hBoxDecontoPorLetraLinha01.setMarginBottom(0);
        hBoxDecontoPorLetraLinha01.setSpacing(1);
        hBoxDecontoPorLetraLinha01.setFlexVflex("ftMin");
        hBoxDecontoPorLetraLinha01.setFlexHflex("ftTrue");
        hBoxDecontoPorLetraLinha01.setScrollable(false);
        hBoxDecontoPorLetraLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxDecontoPorLetraLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxDecontoPorLetraLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxDecontoPorLetraLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxDecontoPorLetraLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxDecontoPorLetraLinha01.setBoxShadowConfigOpacity(75);
        hBoxDecontoPorLetraLinha01.setVAlign("tvTop");
        vBoxVendaDescontoPorLetra.addChildren(hBoxDecontoPorLetraLinha01);
        hBoxDecontoPorLetraLinha01.applyProperties();
    }

    public TFLabel lblDescontoPorLetra = new TFLabel();

    private void init_lblDescontoPorLetra() {
        lblDescontoPorLetra.setName("lblDescontoPorLetra");
        lblDescontoPorLetra.setLeft(0);
        lblDescontoPorLetra.setTop(0);
        lblDescontoPorLetra.setWidth(105);
        lblDescontoPorLetra.setHeight(13);
        lblDescontoPorLetra.setCaption("Desconto por letra");
        lblDescontoPorLetra.setFontColor("clWindowText");
        lblDescontoPorLetra.setFontSize(-11);
        lblDescontoPorLetra.setFontName("Tahoma");
        lblDescontoPorLetra.setFontStyle("[fsBold]");
        lblDescontoPorLetra.setVerticalAlignment("taVerticalCenter");
        lblDescontoPorLetra.setWordBreak(false);
        hBoxDecontoPorLetraLinha01.addChildren(lblDescontoPorLetra);
        lblDescontoPorLetra.applyProperties();
    }

    public TFVBox vBoxSeparadorDescontoPorLetra = new TFVBox();

    private void init_vBoxSeparadorDescontoPorLetra() {
        vBoxSeparadorDescontoPorLetra.setName("vBoxSeparadorDescontoPorLetra");
        vBoxSeparadorDescontoPorLetra.setLeft(105);
        vBoxSeparadorDescontoPorLetra.setTop(0);
        vBoxSeparadorDescontoPorLetra.setWidth(5);
        vBoxSeparadorDescontoPorLetra.setHeight(20);
        vBoxSeparadorDescontoPorLetra.setBorderStyle("stNone");
        vBoxSeparadorDescontoPorLetra.setPaddingTop(0);
        vBoxSeparadorDescontoPorLetra.setPaddingLeft(0);
        vBoxSeparadorDescontoPorLetra.setPaddingRight(0);
        vBoxSeparadorDescontoPorLetra.setPaddingBottom(0);
        vBoxSeparadorDescontoPorLetra.setMarginTop(0);
        vBoxSeparadorDescontoPorLetra.setMarginLeft(0);
        vBoxSeparadorDescontoPorLetra.setMarginRight(0);
        vBoxSeparadorDescontoPorLetra.setMarginBottom(0);
        vBoxSeparadorDescontoPorLetra.setSpacing(1);
        vBoxSeparadorDescontoPorLetra.setFlexVflex("ftFalse");
        vBoxSeparadorDescontoPorLetra.setFlexHflex("ftFalse");
        vBoxSeparadorDescontoPorLetra.setScrollable(false);
        vBoxSeparadorDescontoPorLetra.setBoxShadowConfigHorizontalLength(10);
        vBoxSeparadorDescontoPorLetra.setBoxShadowConfigVerticalLength(10);
        vBoxSeparadorDescontoPorLetra.setBoxShadowConfigBlurRadius(5);
        vBoxSeparadorDescontoPorLetra.setBoxShadowConfigSpreadRadius(0);
        vBoxSeparadorDescontoPorLetra.setBoxShadowConfigShadowColor("clBlack");
        vBoxSeparadorDescontoPorLetra.setBoxShadowConfigOpacity(75);
        hBoxDecontoPorLetraLinha01.addChildren(vBoxSeparadorDescontoPorLetra);
        vBoxSeparadorDescontoPorLetra.applyProperties();
    }

    public TFButton btnIncluirDescontoPorLetra = new TFButton();

    private void init_btnIncluirDescontoPorLetra() {
        btnIncluirDescontoPorLetra.setName("btnIncluirDescontoPorLetra");
        btnIncluirDescontoPorLetra.setLeft(110);
        btnIncluirDescontoPorLetra.setTop(0);
        btnIncluirDescontoPorLetra.setWidth(25);
        btnIncluirDescontoPorLetra.setHeight(25);
        btnIncluirDescontoPorLetra.setHint("Incluir desconto por letra");
        btnIncluirDescontoPorLetra.setFontColor("clWindowText");
        btnIncluirDescontoPorLetra.setFontSize(-11);
        btnIncluirDescontoPorLetra.setFontName("Tahoma");
        btnIncluirDescontoPorLetra.setFontStyle("[]");
        btnIncluirDescontoPorLetra.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirDescontoPorLetraClick(event);
            processarFlow("FrmClientesFlags", "btnIncluirDescontoPorLetra", "OnClick");
        });
        btnIncluirDescontoPorLetra.setImageId(7000128);
        btnIncluirDescontoPorLetra.setColor("clBtnFace");
        btnIncluirDescontoPorLetra.setAccess(false);
        btnIncluirDescontoPorLetra.setIconReverseDirection(false);
        hBoxDecontoPorLetraLinha01.addChildren(btnIncluirDescontoPorLetra);
        btnIncluirDescontoPorLetra.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(31);
        FHBox17.setWidth(480);
        FHBox17.setHeight(5);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftFalse");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        vBoxVendaDescontoPorLetra.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFGrid grdDescontoLetra = new TFGrid();

    private void init_grdDescontoLetra() {
        grdDescontoLetra.setName("grdDescontoLetra");
        grdDescontoLetra.setLeft(0);
        grdDescontoLetra.setTop(37);
        grdDescontoLetra.setWidth(490);
        grdDescontoLetra.setHeight(140);
        grdDescontoLetra.setHint("Desconto por letra");
        grdDescontoLetra.setTable(tbClientesDescontosFlag);
        grdDescontoLetra.setFlexVflex("ftTrue");
        grdDescontoLetra.setFlexHflex("ftTrue");
        grdDescontoLetra.setPagingEnabled(false);
        grdDescontoLetra.setFrozenColumns(0);
        grdDescontoLetra.setShowFooter(false);
        grdDescontoLetra.setShowHeader(true);
        grdDescontoLetra.setMultiSelection(false);
        grdDescontoLetra.setGroupingEnabled(false);
        grdDescontoLetra.setGroupingExpanded(false);
        grdDescontoLetra.setGroupingShowFooter(false);
        grdDescontoLetra.setCrosstabEnabled(false);
        grdDescontoLetra.setCrosstabGroupType("cgtConcat");
        grdDescontoLetra.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdDescontoLetraDoubleClick(event);
            processarFlow("FrmClientesFlags", "grdDescontoLetra", "OnDoubleClick");
        });
        grdDescontoLetra.setEditionEnabled(false);
        grdDescontoLetra.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setHint("Alterar desconto por letra");
        item1.setEvalType("etExpression");
        item1.setImageId(7);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdDescontoLetraalterarDescontoPorLetra(event);
            processarFlow("FrmClientesFlags", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdDescontoLetra.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setWidth(33);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("*");
        item3.setHint("Excluir desconto por letra");
        item3.setEvalType("etExpression");
        item3.setImageId(700095);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdDescontoLetraexcluirDescontoPorLetra(event);
            processarFlow("FrmClientesFlags", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdDescontoLetra.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("LETRA");
        item4.setTitleCaption("Letra");
        item4.setWidth(50);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdDescontoLetra.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DESCONTO");
        item5.setTitleCaption("Desconto padr\u00E3o");
        item5.setWidth(150);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taRight");
        item5.setFieldType("ftDecimal");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFMaskExpression item6 = new TFMaskExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setMask(",##0.00");
        item6.setPadLength(0);
        item6.setPadDirection("pdNone");
        item6.setMaskType("mtDecimal");
        item5.getMasks().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setHint("Desconto padr\u00E3o");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdDescontoLetra.getColumns().add(item5);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("PER_DESC");
        item7.setTitleCaption("Desconto para o cliente");
        item7.setWidth(150);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taRight");
        item7.setFieldType("ftDecimal");
        item7.setFlexRatio(0);
        item7.setSort(true);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(true);
        TFMaskExpression item8 = new TFMaskExpression();
        item8.setExpression("*");
        item8.setEvalType("etExpression");
        item8.setMask(",##0.00");
        item8.setPadLength(0);
        item8.setPadDirection("pdNone");
        item8.setMaskType("mtDecimal");
        item7.getMasks().add(item8);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFDecimal");
        item7.setEditorPrecision(0);
        item7.setEditorMask(",##0.00");
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorPrompt("Preencha o desconto para o cliente");
        item7.setEditorEnabled(true);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setHint("Desconto para o cliente");
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctEvent");
        item7.setEditorConstraintFocusOnError(true);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(true);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        grdDescontoLetra.getColumns().add(item7);
        vBoxVendaDescontoPorLetra.addChildren(grdDescontoLetra);
        grdDescontoLetra.applyProperties();
    }

    public TFHBox FHBox13 = new TFHBox();

    private void init_FHBox13() {
        FHBox13.setName("FHBox13");
        FHBox13.setLeft(505);
        FHBox13.setTop(0);
        FHBox13.setWidth(5);
        FHBox13.setHeight(20);
        FHBox13.setBorderStyle("stNone");
        FHBox13.setPaddingTop(0);
        FHBox13.setPaddingLeft(0);
        FHBox13.setPaddingRight(0);
        FHBox13.setPaddingBottom(0);
        FHBox13.setMarginTop(0);
        FHBox13.setMarginLeft(0);
        FHBox13.setMarginRight(0);
        FHBox13.setMarginBottom(0);
        FHBox13.setSpacing(1);
        FHBox13.setFlexVflex("ftFalse");
        FHBox13.setFlexHflex("ftFalse");
        FHBox13.setScrollable(false);
        FHBox13.setBoxShadowConfigHorizontalLength(10);
        FHBox13.setBoxShadowConfigVerticalLength(10);
        FHBox13.setBoxShadowConfigBlurRadius(5);
        FHBox13.setBoxShadowConfigSpreadRadius(0);
        FHBox13.setBoxShadowConfigShadowColor("clBlack");
        FHBox13.setBoxShadowConfigOpacity(75);
        FHBox13.setVAlign("tvTop");
        hBoxTabDescontoLinha02.addChildren(FHBox13);
        FHBox13.applyProperties();
    }

    public TFVBox vBoxVendaMargemMinimaMarkup = new TFVBox();

    private void init_vBoxVendaMargemMinimaMarkup() {
        vBoxVendaMargemMinimaMarkup.setName("vBoxVendaMargemMinimaMarkup");
        vBoxVendaMargemMinimaMarkup.setLeft(510);
        vBoxVendaMargemMinimaMarkup.setTop(0);
        vBoxVendaMargemMinimaMarkup.setWidth(490);
        vBoxVendaMargemMinimaMarkup.setHeight(190);
        vBoxVendaMargemMinimaMarkup.setBorderStyle("stNone");
        vBoxVendaMargemMinimaMarkup.setPaddingTop(0);
        vBoxVendaMargemMinimaMarkup.setPaddingLeft(0);
        vBoxVendaMargemMinimaMarkup.setPaddingRight(0);
        vBoxVendaMargemMinimaMarkup.setPaddingBottom(0);
        vBoxVendaMargemMinimaMarkup.setMarginTop(0);
        vBoxVendaMargemMinimaMarkup.setMarginLeft(0);
        vBoxVendaMargemMinimaMarkup.setMarginRight(0);
        vBoxVendaMargemMinimaMarkup.setMarginBottom(0);
        vBoxVendaMargemMinimaMarkup.setSpacing(1);
        vBoxVendaMargemMinimaMarkup.setFlexVflex("ftFalse");
        vBoxVendaMargemMinimaMarkup.setFlexHflex("ftTrue");
        vBoxVendaMargemMinimaMarkup.setScrollable(false);
        vBoxVendaMargemMinimaMarkup.setBoxShadowConfigHorizontalLength(10);
        vBoxVendaMargemMinimaMarkup.setBoxShadowConfigVerticalLength(10);
        vBoxVendaMargemMinimaMarkup.setBoxShadowConfigBlurRadius(5);
        vBoxVendaMargemMinimaMarkup.setBoxShadowConfigSpreadRadius(0);
        vBoxVendaMargemMinimaMarkup.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendaMargemMinimaMarkup.setBoxShadowConfigOpacity(75);
        hBoxTabDescontoLinha02.addChildren(vBoxVendaMargemMinimaMarkup);
        vBoxVendaMargemMinimaMarkup.applyProperties();
    }

    public TFHBox hBoxMargemMinimaMarkupLinha01 = new TFHBox();

    private void init_hBoxMargemMinimaMarkupLinha01() {
        hBoxMargemMinimaMarkupLinha01.setName("hBoxMargemMinimaMarkupLinha01");
        hBoxMargemMinimaMarkupLinha01.setLeft(0);
        hBoxMargemMinimaMarkupLinha01.setTop(0);
        hBoxMargemMinimaMarkupLinha01.setWidth(480);
        hBoxMargemMinimaMarkupLinha01.setHeight(30);
        hBoxMargemMinimaMarkupLinha01.setBorderStyle("stNone");
        hBoxMargemMinimaMarkupLinha01.setPaddingTop(0);
        hBoxMargemMinimaMarkupLinha01.setPaddingLeft(0);
        hBoxMargemMinimaMarkupLinha01.setPaddingRight(0);
        hBoxMargemMinimaMarkupLinha01.setPaddingBottom(0);
        hBoxMargemMinimaMarkupLinha01.setMarginTop(0);
        hBoxMargemMinimaMarkupLinha01.setMarginLeft(0);
        hBoxMargemMinimaMarkupLinha01.setMarginRight(0);
        hBoxMargemMinimaMarkupLinha01.setMarginBottom(0);
        hBoxMargemMinimaMarkupLinha01.setSpacing(1);
        hBoxMargemMinimaMarkupLinha01.setFlexVflex("ftMin");
        hBoxMargemMinimaMarkupLinha01.setFlexHflex("ftTrue");
        hBoxMargemMinimaMarkupLinha01.setScrollable(false);
        hBoxMargemMinimaMarkupLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxMargemMinimaMarkupLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxMargemMinimaMarkupLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxMargemMinimaMarkupLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxMargemMinimaMarkupLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxMargemMinimaMarkupLinha01.setBoxShadowConfigOpacity(75);
        hBoxMargemMinimaMarkupLinha01.setVAlign("tvTop");
        vBoxVendaMargemMinimaMarkup.addChildren(hBoxMargemMinimaMarkupLinha01);
        hBoxMargemMinimaMarkupLinha01.applyProperties();
    }

    public TFLabel lblMargemMinimaMarkup = new TFLabel();

    private void init_lblMargemMinimaMarkup() {
        lblMargemMinimaMarkup.setName("lblMargemMinimaMarkup");
        lblMargemMinimaMarkup.setLeft(0);
        lblMargemMinimaMarkup.setTop(0);
        lblMargemMinimaMarkup.setWidth(139);
        lblMargemMinimaMarkup.setHeight(13);
        lblMargemMinimaMarkup.setCaption("Margem m\u00EDnima markup");
        lblMargemMinimaMarkup.setFontColor("clWindowText");
        lblMargemMinimaMarkup.setFontSize(-11);
        lblMargemMinimaMarkup.setFontName("Tahoma");
        lblMargemMinimaMarkup.setFontStyle("[fsBold]");
        lblMargemMinimaMarkup.setVerticalAlignment("taVerticalCenter");
        lblMargemMinimaMarkup.setWordBreak(false);
        hBoxMargemMinimaMarkupLinha01.addChildren(lblMargemMinimaMarkup);
        lblMargemMinimaMarkup.applyProperties();
    }

    public TFVBox vBoxSeparadorMargemMinimaMarkup1 = new TFVBox();

    private void init_vBoxSeparadorMargemMinimaMarkup1() {
        vBoxSeparadorMargemMinimaMarkup1.setName("vBoxSeparadorMargemMinimaMarkup1");
        vBoxSeparadorMargemMinimaMarkup1.setLeft(139);
        vBoxSeparadorMargemMinimaMarkup1.setTop(0);
        vBoxSeparadorMargemMinimaMarkup1.setWidth(5);
        vBoxSeparadorMargemMinimaMarkup1.setHeight(20);
        vBoxSeparadorMargemMinimaMarkup1.setBorderStyle("stNone");
        vBoxSeparadorMargemMinimaMarkup1.setPaddingTop(0);
        vBoxSeparadorMargemMinimaMarkup1.setPaddingLeft(0);
        vBoxSeparadorMargemMinimaMarkup1.setPaddingRight(0);
        vBoxSeparadorMargemMinimaMarkup1.setPaddingBottom(0);
        vBoxSeparadorMargemMinimaMarkup1.setMarginTop(0);
        vBoxSeparadorMargemMinimaMarkup1.setMarginLeft(0);
        vBoxSeparadorMargemMinimaMarkup1.setMarginRight(0);
        vBoxSeparadorMargemMinimaMarkup1.setMarginBottom(0);
        vBoxSeparadorMargemMinimaMarkup1.setSpacing(1);
        vBoxSeparadorMargemMinimaMarkup1.setFlexVflex("ftFalse");
        vBoxSeparadorMargemMinimaMarkup1.setFlexHflex("ftFalse");
        vBoxSeparadorMargemMinimaMarkup1.setScrollable(false);
        vBoxSeparadorMargemMinimaMarkup1.setBoxShadowConfigHorizontalLength(10);
        vBoxSeparadorMargemMinimaMarkup1.setBoxShadowConfigVerticalLength(10);
        vBoxSeparadorMargemMinimaMarkup1.setBoxShadowConfigBlurRadius(5);
        vBoxSeparadorMargemMinimaMarkup1.setBoxShadowConfigSpreadRadius(0);
        vBoxSeparadorMargemMinimaMarkup1.setBoxShadowConfigShadowColor("clBlack");
        vBoxSeparadorMargemMinimaMarkup1.setBoxShadowConfigOpacity(75);
        hBoxMargemMinimaMarkupLinha01.addChildren(vBoxSeparadorMargemMinimaMarkup1);
        vBoxSeparadorMargemMinimaMarkup1.applyProperties();
    }

    public TFButton btnIncluirMargemMinimaMarkup = new TFButton();

    private void init_btnIncluirMargemMinimaMarkup() {
        btnIncluirMargemMinimaMarkup.setName("btnIncluirMargemMinimaMarkup");
        btnIncluirMargemMinimaMarkup.setLeft(144);
        btnIncluirMargemMinimaMarkup.setTop(0);
        btnIncluirMargemMinimaMarkup.setWidth(25);
        btnIncluirMargemMinimaMarkup.setHeight(25);
        btnIncluirMargemMinimaMarkup.setHint("Incluir margem m\u00EDnima markup (s\u00F3 \u00E9 permitido 1 registro por cliente porque vale para todas as empresas)");
        btnIncluirMargemMinimaMarkup.setFontColor("clWindowText");
        btnIncluirMargemMinimaMarkup.setFontSize(-11);
        btnIncluirMargemMinimaMarkup.setFontName("Tahoma");
        btnIncluirMargemMinimaMarkup.setFontStyle("[]");
        btnIncluirMargemMinimaMarkup.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirMargemMinimaMarkupClick(event);
            processarFlow("FrmClientesFlags", "btnIncluirMargemMinimaMarkup", "OnClick");
        });
        btnIncluirMargemMinimaMarkup.setImageId(7000128);
        btnIncluirMargemMinimaMarkup.setColor("clBtnFace");
        btnIncluirMargemMinimaMarkup.setAccess(false);
        btnIncluirMargemMinimaMarkup.setIconReverseDirection(false);
        hBoxMargemMinimaMarkupLinha01.addChildren(btnIncluirMargemMinimaMarkup);
        btnIncluirMargemMinimaMarkup.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(31);
        FHBox18.setWidth(480);
        FHBox18.setHeight(5);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(1);
        FHBox18.setFlexVflex("ftFalse");
        FHBox18.setFlexHflex("ftFalse");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        vBoxVendaMargemMinimaMarkup.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFGrid grdMargemMinimaMarkup = new TFGrid();

    private void init_grdMargemMinimaMarkup() {
        grdMargemMinimaMarkup.setName("grdMargemMinimaMarkup");
        grdMargemMinimaMarkup.setLeft(0);
        grdMargemMinimaMarkup.setTop(37);
        grdMargemMinimaMarkup.setWidth(480);
        grdMargemMinimaMarkup.setHeight(140);
        grdMargemMinimaMarkup.setHint("Margem m\u00EDnima markup");
        grdMargemMinimaMarkup.setTable(tbClientesEspeciaisMargem);
        grdMargemMinimaMarkup.setFlexVflex("ftTrue");
        grdMargemMinimaMarkup.setFlexHflex("ftTrue");
        grdMargemMinimaMarkup.setPagingEnabled(false);
        grdMargemMinimaMarkup.setFrozenColumns(0);
        grdMargemMinimaMarkup.setShowFooter(false);
        grdMargemMinimaMarkup.setShowHeader(true);
        grdMargemMinimaMarkup.setMultiSelection(false);
        grdMargemMinimaMarkup.setGroupingEnabled(false);
        grdMargemMinimaMarkup.setGroupingExpanded(false);
        grdMargemMinimaMarkup.setGroupingShowFooter(false);
        grdMargemMinimaMarkup.setCrosstabEnabled(false);
        grdMargemMinimaMarkup.setCrosstabGroupType("cgtConcat");
        grdMargemMinimaMarkup.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdMargemMinimaMarkupDoubleClick(event);
            processarFlow("FrmClientesFlags", "grdMargemMinimaMarkup", "OnDoubleClick");
        });
        grdMargemMinimaMarkup.setEditionEnabled(true);
        grdMargemMinimaMarkup.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setHint("Alterar margem m\u00EDnima markup");
        item1.setEvalType("etExpression");
        item1.setImageId(7);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdMargemMinimaMarkupalterarMargemMinimaMarkup(event);
            processarFlow("FrmClientesFlags", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdMargemMinimaMarkup.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setWidth(33);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("*");
        item3.setHint("Excluir margem m\u00EDnima markup");
        item3.setEvalType("etExpression");
        item3.setImageId(700095);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdMargemMinimaMarkupexcluirMargemMinimaMarkup(event);
            processarFlow("FrmClientesFlags", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdMargemMinimaMarkup.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("MARGEM_MINIMA");
        item4.setTitleCaption("Margem M\u00EDnima");
        item4.setWidth(100);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        TFMaskExpression item5 = new TFMaskExpression();
        item5.setExpression("*");
        item5.setEvalType("etExpression");
        item5.setMask(",##0.00");
        item5.setPadLength(0);
        item5.setPadDirection("pdNone");
        item5.setMaskType("mtDecimal");
        item4.getMasks().add(item5);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdMargemMinimaMarkup.getColumns().add(item4);
        vBoxVendaMargemMinimaMarkup.addChildren(grdMargemMinimaMarkup);
        grdMargemMinimaMarkup.applyProperties();
    }

    public TFHBox FHBox15 = new TFHBox();

    private void init_FHBox15() {
        FHBox15.setName("FHBox15");
        FHBox15.setLeft(1000);
        FHBox15.setTop(0);
        FHBox15.setWidth(5);
        FHBox15.setHeight(20);
        FHBox15.setBorderStyle("stNone");
        FHBox15.setPaddingTop(0);
        FHBox15.setPaddingLeft(0);
        FHBox15.setPaddingRight(0);
        FHBox15.setPaddingBottom(0);
        FHBox15.setMarginTop(0);
        FHBox15.setMarginLeft(0);
        FHBox15.setMarginRight(0);
        FHBox15.setMarginBottom(0);
        FHBox15.setSpacing(1);
        FHBox15.setFlexVflex("ftFalse");
        FHBox15.setFlexHflex("ftFalse");
        FHBox15.setScrollable(false);
        FHBox15.setBoxShadowConfigHorizontalLength(10);
        FHBox15.setBoxShadowConfigVerticalLength(10);
        FHBox15.setBoxShadowConfigBlurRadius(5);
        FHBox15.setBoxShadowConfigSpreadRadius(0);
        FHBox15.setBoxShadowConfigShadowColor("clBlack");
        FHBox15.setBoxShadowConfigOpacity(75);
        FHBox15.setVAlign("tvTop");
        hBoxTabDescontoLinha02.addChildren(FHBox15);
        FHBox15.applyProperties();
    }

    public TFHBox FHBox9 = new TFHBox();

    private void init_FHBox9() {
        FHBox9.setName("FHBox9");
        FHBox9.setLeft(0);
        FHBox9.setTop(274);
        FHBox9.setWidth(1000);
        FHBox9.setHeight(5);
        FHBox9.setBorderStyle("stNone");
        FHBox9.setPaddingTop(0);
        FHBox9.setPaddingLeft(0);
        FHBox9.setPaddingRight(0);
        FHBox9.setPaddingBottom(0);
        FHBox9.setMarginTop(0);
        FHBox9.setMarginLeft(0);
        FHBox9.setMarginRight(0);
        FHBox9.setMarginBottom(0);
        FHBox9.setSpacing(1);
        FHBox9.setFlexVflex("ftFalse");
        FHBox9.setFlexHflex("ftFalse");
        FHBox9.setScrollable(false);
        FHBox9.setBoxShadowConfigHorizontalLength(10);
        FHBox9.setBoxShadowConfigVerticalLength(10);
        FHBox9.setBoxShadowConfigBlurRadius(5);
        FHBox9.setBoxShadowConfigSpreadRadius(0);
        FHBox9.setBoxShadowConfigShadowColor("clBlack");
        FHBox9.setBoxShadowConfigOpacity(75);
        FHBox9.setVAlign("tvTop");
        vBoxTabDesconto.addChildren(FHBox9);
        FHBox9.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(280);
        FHBox8.setWidth(1000);
        FHBox8.setHeight(200);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftTrue");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        vBoxTabDesconto.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFHBox FHBox20 = new TFHBox();

    private void init_FHBox20() {
        FHBox20.setName("FHBox20");
        FHBox20.setLeft(0);
        FHBox20.setTop(0);
        FHBox20.setWidth(5);
        FHBox20.setHeight(20);
        FHBox20.setBorderStyle("stNone");
        FHBox20.setPaddingTop(0);
        FHBox20.setPaddingLeft(0);
        FHBox20.setPaddingRight(0);
        FHBox20.setPaddingBottom(0);
        FHBox20.setMarginTop(0);
        FHBox20.setMarginLeft(0);
        FHBox20.setMarginRight(0);
        FHBox20.setMarginBottom(0);
        FHBox20.setSpacing(1);
        FHBox20.setFlexVflex("ftFalse");
        FHBox20.setFlexHflex("ftFalse");
        FHBox20.setScrollable(false);
        FHBox20.setBoxShadowConfigHorizontalLength(10);
        FHBox20.setBoxShadowConfigVerticalLength(10);
        FHBox20.setBoxShadowConfigBlurRadius(5);
        FHBox20.setBoxShadowConfigSpreadRadius(0);
        FHBox20.setBoxShadowConfigShadowColor("clBlack");
        FHBox20.setBoxShadowConfigOpacity(75);
        FHBox20.setVAlign("tvTop");
        FHBox8.addChildren(FHBox20);
        FHBox20.applyProperties();
    }

    public TFVBox ******************************** = new TFVBox();

    private void init_********************************() {
        ********************************.setName("********************************");
        ********************************.setLeft(5);
        ********************************.setTop(0);
        ********************************.setWidth(500);
        ********************************.setHeight(190);
        ********************************.setBorderStyle("stNone");
        ********************************.setPaddingTop(0);
        ********************************.setPaddingLeft(0);
        ********************************.setPaddingRight(0);
        ********************************.setPaddingBottom(0);
        ********************************.setMarginTop(0);
        ********************************.setMarginLeft(0);
        ********************************.setMarginRight(0);
        ********************************.setMarginBottom(0);
        ********************************.setSpacing(1);
        ********************************.setFlexVflex("ftTrue");
        ********************************.setFlexHflex("ftMin");
        ********************************.setScrollable(false);
        ********************************.setBoxShadowConfigHorizontalLength(10);
        ********************************.setBoxShadowConfigVerticalLength(10);
        ********************************.setBoxShadowConfigBlurRadius(5);
        ********************************.setBoxShadowConfigSpreadRadius(0);
        ********************************.setBoxShadowConfigShadowColor("clBlack");
        ********************************.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(********************************);
        ********************************.applyProperties();
    }

    public TFHBox hBoxDecontoPorLetraEmpresaLinha01 = new TFHBox();

    private void init_hBoxDecontoPorLetraEmpresaLinha01() {
        hBoxDecontoPorLetraEmpresaLinha01.setName("hBoxDecontoPorLetraEmpresaLinha01");
        hBoxDecontoPorLetraEmpresaLinha01.setLeft(0);
        hBoxDecontoPorLetraEmpresaLinha01.setTop(0);
        hBoxDecontoPorLetraEmpresaLinha01.setWidth(480);
        hBoxDecontoPorLetraEmpresaLinha01.setHeight(31);
        hBoxDecontoPorLetraEmpresaLinha01.setBorderStyle("stNone");
        hBoxDecontoPorLetraEmpresaLinha01.setPaddingTop(0);
        hBoxDecontoPorLetraEmpresaLinha01.setPaddingLeft(0);
        hBoxDecontoPorLetraEmpresaLinha01.setPaddingRight(0);
        hBoxDecontoPorLetraEmpresaLinha01.setPaddingBottom(0);
        hBoxDecontoPorLetraEmpresaLinha01.setMarginTop(0);
        hBoxDecontoPorLetraEmpresaLinha01.setMarginLeft(0);
        hBoxDecontoPorLetraEmpresaLinha01.setMarginRight(0);
        hBoxDecontoPorLetraEmpresaLinha01.setMarginBottom(0);
        hBoxDecontoPorLetraEmpresaLinha01.setSpacing(1);
        hBoxDecontoPorLetraEmpresaLinha01.setFlexVflex("ftMin");
        hBoxDecontoPorLetraEmpresaLinha01.setFlexHflex("ftTrue");
        hBoxDecontoPorLetraEmpresaLinha01.setScrollable(false);
        hBoxDecontoPorLetraEmpresaLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxDecontoPorLetraEmpresaLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxDecontoPorLetraEmpresaLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxDecontoPorLetraEmpresaLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxDecontoPorLetraEmpresaLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxDecontoPorLetraEmpresaLinha01.setBoxShadowConfigOpacity(75);
        hBoxDecontoPorLetraEmpresaLinha01.setVAlign("tvTop");
        ********************************.addChildren(hBoxDecontoPorLetraEmpresaLinha01);
        hBoxDecontoPorLetraEmpresaLinha01.applyProperties();
    }

    public TFLabel lblDescontoLetraEmpresa = new TFLabel();

    private void init_lblDescontoLetraEmpresa() {
        lblDescontoLetraEmpresa.setName("lblDescontoLetraEmpresa");
        lblDescontoLetraEmpresa.setLeft(0);
        lblDescontoLetraEmpresa.setTop(0);
        lblDescontoLetraEmpresa.setWidth(161);
        lblDescontoLetraEmpresa.setHeight(13);
        lblDescontoLetraEmpresa.setCaption("Desconto por letra/empresa");
        lblDescontoLetraEmpresa.setFontColor("clWindowText");
        lblDescontoLetraEmpresa.setFontSize(-11);
        lblDescontoLetraEmpresa.setFontName("Tahoma");
        lblDescontoLetraEmpresa.setFontStyle("[fsBold]");
        lblDescontoLetraEmpresa.setVerticalAlignment("taVerticalCenter");
        lblDescontoLetraEmpresa.setWordBreak(false);
        hBoxDecontoPorLetraEmpresaLinha01.addChildren(lblDescontoLetraEmpresa);
        lblDescontoLetraEmpresa.applyProperties();
    }

    public TFVBox vBoxSeparadorDescontoPorLetraEmpresa = new TFVBox();

    private void init_vBoxSeparadorDescontoPorLetraEmpresa() {
        vBoxSeparadorDescontoPorLetraEmpresa.setName("vBoxSeparadorDescontoPorLetraEmpresa");
        vBoxSeparadorDescontoPorLetraEmpresa.setLeft(161);
        vBoxSeparadorDescontoPorLetraEmpresa.setTop(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setWidth(5);
        vBoxSeparadorDescontoPorLetraEmpresa.setHeight(20);
        vBoxSeparadorDescontoPorLetraEmpresa.setBorderStyle("stNone");
        vBoxSeparadorDescontoPorLetraEmpresa.setPaddingTop(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setPaddingLeft(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setPaddingRight(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setPaddingBottom(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setMarginTop(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setMarginLeft(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setMarginRight(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setMarginBottom(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setSpacing(1);
        vBoxSeparadorDescontoPorLetraEmpresa.setFlexVflex("ftFalse");
        vBoxSeparadorDescontoPorLetraEmpresa.setFlexHflex("ftFalse");
        vBoxSeparadorDescontoPorLetraEmpresa.setScrollable(false);
        vBoxSeparadorDescontoPorLetraEmpresa.setBoxShadowConfigHorizontalLength(10);
        vBoxSeparadorDescontoPorLetraEmpresa.setBoxShadowConfigVerticalLength(10);
        vBoxSeparadorDescontoPorLetraEmpresa.setBoxShadowConfigBlurRadius(5);
        vBoxSeparadorDescontoPorLetraEmpresa.setBoxShadowConfigSpreadRadius(0);
        vBoxSeparadorDescontoPorLetraEmpresa.setBoxShadowConfigShadowColor("clBlack");
        vBoxSeparadorDescontoPorLetraEmpresa.setBoxShadowConfigOpacity(75);
        hBoxDecontoPorLetraEmpresaLinha01.addChildren(vBoxSeparadorDescontoPorLetraEmpresa);
        vBoxSeparadorDescontoPorLetraEmpresa.applyProperties();
    }

    public TFButton btnIncluirDescontoPorLetraEmpresa = new TFButton();

    private void init_btnIncluirDescontoPorLetraEmpresa() {
        btnIncluirDescontoPorLetraEmpresa.setName("btnIncluirDescontoPorLetraEmpresa");
        btnIncluirDescontoPorLetraEmpresa.setLeft(166);
        btnIncluirDescontoPorLetraEmpresa.setTop(0);
        btnIncluirDescontoPorLetraEmpresa.setWidth(25);
        btnIncluirDescontoPorLetraEmpresa.setHeight(25);
        btnIncluirDescontoPorLetraEmpresa.setHint("Incluir desconto por letra/empresa");
        btnIncluirDescontoPorLetraEmpresa.setFontColor("clWindowText");
        btnIncluirDescontoPorLetraEmpresa.setFontSize(-11);
        btnIncluirDescontoPorLetraEmpresa.setFontName("Tahoma");
        btnIncluirDescontoPorLetraEmpresa.setFontStyle("[]");
        btnIncluirDescontoPorLetraEmpresa.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirDescontoPorLetraEmpresaClick(event);
            processarFlow("FrmClientesFlags", "btnIncluirDescontoPorLetraEmpresa", "OnClick");
        });
        btnIncluirDescontoPorLetraEmpresa.setImageId(7000128);
        btnIncluirDescontoPorLetraEmpresa.setColor("clBtnFace");
        btnIncluirDescontoPorLetraEmpresa.setAccess(false);
        btnIncluirDescontoPorLetraEmpresa.setIconReverseDirection(false);
        hBoxDecontoPorLetraEmpresaLinha01.addChildren(btnIncluirDescontoPorLetraEmpresa);
        btnIncluirDescontoPorLetraEmpresa.applyProperties();
    }

    public TFHBox FHBox24 = new TFHBox();

    private void init_FHBox24() {
        FHBox24.setName("FHBox24");
        FHBox24.setLeft(0);
        FHBox24.setTop(32);
        FHBox24.setWidth(480);
        FHBox24.setHeight(5);
        FHBox24.setBorderStyle("stNone");
        FHBox24.setPaddingTop(0);
        FHBox24.setPaddingLeft(0);
        FHBox24.setPaddingRight(0);
        FHBox24.setPaddingBottom(0);
        FHBox24.setMarginTop(0);
        FHBox24.setMarginLeft(0);
        FHBox24.setMarginRight(0);
        FHBox24.setMarginBottom(0);
        FHBox24.setSpacing(1);
        FHBox24.setFlexVflex("ftFalse");
        FHBox24.setFlexHflex("ftFalse");
        FHBox24.setScrollable(false);
        FHBox24.setBoxShadowConfigHorizontalLength(10);
        FHBox24.setBoxShadowConfigVerticalLength(10);
        FHBox24.setBoxShadowConfigBlurRadius(5);
        FHBox24.setBoxShadowConfigSpreadRadius(0);
        FHBox24.setBoxShadowConfigShadowColor("clBlack");
        FHBox24.setBoxShadowConfigOpacity(75);
        FHBox24.setVAlign("tvTop");
        ********************************.addChildren(FHBox24);
        FHBox24.applyProperties();
    }

    public TFGrid grdDescontoLetraEmpresa = new TFGrid();

    private void init_grdDescontoLetraEmpresa() {
        grdDescontoLetraEmpresa.setName("grdDescontoLetraEmpresa");
        grdDescontoLetraEmpresa.setLeft(0);
        grdDescontoLetraEmpresa.setTop(38);
        grdDescontoLetraEmpresa.setWidth(490);
        grdDescontoLetraEmpresa.setHeight(140);
        grdDescontoLetraEmpresa.setHint("Desconto por letra/empresa");
        grdDescontoLetraEmpresa.setTable(tbDescEmp);
        grdDescontoLetraEmpresa.setFlexVflex("ftTrue");
        grdDescontoLetraEmpresa.setFlexHflex("ftTrue");
        grdDescontoLetraEmpresa.setPagingEnabled(false);
        grdDescontoLetraEmpresa.setFrozenColumns(0);
        grdDescontoLetraEmpresa.setShowFooter(false);
        grdDescontoLetraEmpresa.setShowHeader(true);
        grdDescontoLetraEmpresa.setMultiSelection(false);
        grdDescontoLetraEmpresa.setGroupingEnabled(false);
        grdDescontoLetraEmpresa.setGroupingExpanded(false);
        grdDescontoLetraEmpresa.setGroupingShowFooter(false);
        grdDescontoLetraEmpresa.setCrosstabEnabled(false);
        grdDescontoLetraEmpresa.setCrosstabGroupType("cgtConcat");
        grdDescontoLetraEmpresa.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdDescontoLetraEmpresaDoubleClick(event);
            processarFlow("FrmClientesFlags", "grdDescontoLetraEmpresa", "OnDoubleClick");
        });
        grdDescontoLetraEmpresa.setEditionEnabled(false);
        grdDescontoLetraEmpresa.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setHint("Alterar desconto por letra/empresa");
        item1.setEvalType("etExpression");
        item1.setImageId(7);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdDescontoLetraEmpresaalterarDescontoPorLetraEmpresa(event);
            processarFlow("FrmClientesFlags", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdDescontoLetraEmpresa.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setWidth(33);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("*");
        item3.setHint("Excluir desconto por letra/empresa");
        item3.setEvalType("etExpression");
        item3.setImageId(700095);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdDescontoLetraEmpresaexcluirDescontoPorLetraEmpresa(event);
            processarFlow("FrmClientesFlags", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdDescontoLetraEmpresa.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("LETRA");
        item4.setTitleCaption("Letra");
        item4.setWidth(50);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdDescontoLetraEmpresa.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DESCONTO_LETRA");
        item5.setTitleCaption("Desconto padr\u00E3o");
        item5.setWidth(110);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taRight");
        item5.setFieldType("ftDecimal");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFMaskExpression item6 = new TFMaskExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setMask(",##0.00");
        item6.setPadLength(0);
        item6.setPadDirection("pdNone");
        item6.setMaskType("mtDecimal");
        item5.getMasks().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFDecimal");
        item5.setEditorPrecision(0);
        item5.setEditorMask(",##0.00");
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setHint("Desconto padr\u00E3o");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdDescontoLetraEmpresa.getColumns().add(item5);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("PER_DESC");
        item7.setTitleCaption("Desconto para o cliente");
        item7.setWidth(140);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taRight");
        item7.setFieldType("ftDecimal");
        item7.setFlexRatio(0);
        item7.setSort(true);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        TFMaskExpression item8 = new TFMaskExpression();
        item8.setExpression("*");
        item8.setEvalType("etExpression");
        item8.setMask(",##0.00");
        item8.setPadLength(0);
        item8.setPadDirection("pdNone");
        item8.setMaskType("mtDecimal");
        item7.getMasks().add(item8);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFDecimal");
        item7.setEditorPrecision(0);
        item7.setEditorMask(",##0.00");
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorPrompt("Preencha o desconto para o cliente");
        item7.setEditorEnabled(true);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setHint("Desconto para o cliente");
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctEvent");
        item7.setEditorConstraintFocusOnError(true);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(true);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        grdDescontoLetraEmpresa.getColumns().add(item7);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("COD_EMPRESA");
        item9.setTitleCaption("Empresa");
        item9.setWidth(120);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(true);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorLookupDesc("NOMEECODIGODAEMPRESA");
        item9.setEditorLookupKey("CODIGODAEMPRESA");
        item9.setEditorLookupTable(tbEmpresasFiliaisSel);
        item9.setEditorEditType("etTFCombo");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorPrompt("Selecione a empresa");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setHint("Empresa");
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        grdDescontoLetraEmpresa.getColumns().add(item9);
        ********************************.addChildren(grdDescontoLetraEmpresa);
        grdDescontoLetraEmpresa.applyProperties();
    }

    public TFHBox FHBox22 = new TFHBox();

    private void init_FHBox22() {
        FHBox22.setName("FHBox22");
        FHBox22.setLeft(505);
        FHBox22.setTop(0);
        FHBox22.setWidth(5);
        FHBox22.setHeight(20);
        FHBox22.setBorderStyle("stNone");
        FHBox22.setPaddingTop(0);
        FHBox22.setPaddingLeft(0);
        FHBox22.setPaddingRight(0);
        FHBox22.setPaddingBottom(0);
        FHBox22.setMarginTop(0);
        FHBox22.setMarginLeft(0);
        FHBox22.setMarginRight(0);
        FHBox22.setMarginBottom(0);
        FHBox22.setSpacing(1);
        FHBox22.setFlexVflex("ftFalse");
        FHBox22.setFlexHflex("ftFalse");
        FHBox22.setScrollable(false);
        FHBox22.setBoxShadowConfigHorizontalLength(10);
        FHBox22.setBoxShadowConfigVerticalLength(10);
        FHBox22.setBoxShadowConfigBlurRadius(5);
        FHBox22.setBoxShadowConfigSpreadRadius(0);
        FHBox22.setBoxShadowConfigShadowColor("clBlack");
        FHBox22.setBoxShadowConfigOpacity(75);
        FHBox22.setVAlign("tvTop");
        FHBox8.addChildren(FHBox22);
        FHBox22.applyProperties();
    }

    public TFVBox vBoxVendaMargemMinimaMarkupEmpresa = new TFVBox();

    private void init_vBoxVendaMargemMinimaMarkupEmpresa() {
        vBoxVendaMargemMinimaMarkupEmpresa.setName("vBoxVendaMargemMinimaMarkupEmpresa");
        vBoxVendaMargemMinimaMarkupEmpresa.setLeft(510);
        vBoxVendaMargemMinimaMarkupEmpresa.setTop(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setWidth(490);
        vBoxVendaMargemMinimaMarkupEmpresa.setHeight(190);
        vBoxVendaMargemMinimaMarkupEmpresa.setBorderStyle("stNone");
        vBoxVendaMargemMinimaMarkupEmpresa.setPaddingTop(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setPaddingLeft(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setPaddingRight(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setPaddingBottom(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setMarginTop(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setMarginLeft(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setMarginRight(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setMarginBottom(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setSpacing(1);
        vBoxVendaMargemMinimaMarkupEmpresa.setFlexVflex("ftTrue");
        vBoxVendaMargemMinimaMarkupEmpresa.setFlexHflex("ftTrue");
        vBoxVendaMargemMinimaMarkupEmpresa.setScrollable(false);
        vBoxVendaMargemMinimaMarkupEmpresa.setBoxShadowConfigHorizontalLength(10);
        vBoxVendaMargemMinimaMarkupEmpresa.setBoxShadowConfigVerticalLength(10);
        vBoxVendaMargemMinimaMarkupEmpresa.setBoxShadowConfigBlurRadius(5);
        vBoxVendaMargemMinimaMarkupEmpresa.setBoxShadowConfigSpreadRadius(0);
        vBoxVendaMargemMinimaMarkupEmpresa.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendaMargemMinimaMarkupEmpresa.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(vBoxVendaMargemMinimaMarkupEmpresa);
        vBoxVendaMargemMinimaMarkupEmpresa.applyProperties();
    }

    public TFHBox hBoxMargemMinimaMarkupEmpresaLinha01 = new TFHBox();

    private void init_hBoxMargemMinimaMarkupEmpresaLinha01() {
        hBoxMargemMinimaMarkupEmpresaLinha01.setName("hBoxMargemMinimaMarkupEmpresaLinha01");
        hBoxMargemMinimaMarkupEmpresaLinha01.setLeft(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setTop(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setWidth(480);
        hBoxMargemMinimaMarkupEmpresaLinha01.setHeight(30);
        hBoxMargemMinimaMarkupEmpresaLinha01.setBorderStyle("stNone");
        hBoxMargemMinimaMarkupEmpresaLinha01.setPaddingTop(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setPaddingLeft(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setPaddingRight(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setPaddingBottom(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setMarginTop(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setMarginLeft(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setMarginRight(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setMarginBottom(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setSpacing(1);
        hBoxMargemMinimaMarkupEmpresaLinha01.setFlexVflex("ftMin");
        hBoxMargemMinimaMarkupEmpresaLinha01.setFlexHflex("ftTrue");
        hBoxMargemMinimaMarkupEmpresaLinha01.setScrollable(false);
        hBoxMargemMinimaMarkupEmpresaLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxMargemMinimaMarkupEmpresaLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxMargemMinimaMarkupEmpresaLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxMargemMinimaMarkupEmpresaLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxMargemMinimaMarkupEmpresaLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxMargemMinimaMarkupEmpresaLinha01.setBoxShadowConfigOpacity(75);
        hBoxMargemMinimaMarkupEmpresaLinha01.setVAlign("tvTop");
        vBoxVendaMargemMinimaMarkupEmpresa.addChildren(hBoxMargemMinimaMarkupEmpresaLinha01);
        hBoxMargemMinimaMarkupEmpresaLinha01.applyProperties();
    }

    public TFLabel lblMargemMinimaMarkupEmpresa = new TFLabel();

    private void init_lblMargemMinimaMarkupEmpresa() {
        lblMargemMinimaMarkupEmpresa.setName("lblMargemMinimaMarkupEmpresa");
        lblMargemMinimaMarkupEmpresa.setLeft(0);
        lblMargemMinimaMarkupEmpresa.setTop(0);
        lblMargemMinimaMarkupEmpresa.setWidth(195);
        lblMargemMinimaMarkupEmpresa.setHeight(13);
        lblMargemMinimaMarkupEmpresa.setCaption("Margem m\u00EDnima markup/empresa");
        lblMargemMinimaMarkupEmpresa.setFontColor("clWindowText");
        lblMargemMinimaMarkupEmpresa.setFontSize(-11);
        lblMargemMinimaMarkupEmpresa.setFontName("Tahoma");
        lblMargemMinimaMarkupEmpresa.setFontStyle("[fsBold]");
        lblMargemMinimaMarkupEmpresa.setVerticalAlignment("taVerticalCenter");
        lblMargemMinimaMarkupEmpresa.setWordBreak(false);
        hBoxMargemMinimaMarkupEmpresaLinha01.addChildren(lblMargemMinimaMarkupEmpresa);
        lblMargemMinimaMarkupEmpresa.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(195);
        FVBox2.setTop(0);
        FVBox2.setWidth(5);
        FVBox2.setHeight(20);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hBoxMargemMinimaMarkupEmpresaLinha01.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFButton btnIncluirMargemMinimaMarkupEmpresa = new TFButton();

    private void init_btnIncluirMargemMinimaMarkupEmpresa() {
        btnIncluirMargemMinimaMarkupEmpresa.setName("btnIncluirMargemMinimaMarkupEmpresa");
        btnIncluirMargemMinimaMarkupEmpresa.setLeft(200);
        btnIncluirMargemMinimaMarkupEmpresa.setTop(0);
        btnIncluirMargemMinimaMarkupEmpresa.setWidth(21);
        btnIncluirMargemMinimaMarkupEmpresa.setHeight(25);
        btnIncluirMargemMinimaMarkupEmpresa.setHint("Incluir margem m\u00EDnima markup/empresa");
        btnIncluirMargemMinimaMarkupEmpresa.setFontColor("clWindowText");
        btnIncluirMargemMinimaMarkupEmpresa.setFontSize(-11);
        btnIncluirMargemMinimaMarkupEmpresa.setFontName("Tahoma");
        btnIncluirMargemMinimaMarkupEmpresa.setFontStyle("[]");
        btnIncluirMargemMinimaMarkupEmpresa.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirMargemMinimaMarkupEmpresaClick(event);
            processarFlow("FrmClientesFlags", "btnIncluirMargemMinimaMarkupEmpresa", "OnClick");
        });
        btnIncluirMargemMinimaMarkupEmpresa.setImageId(7000128);
        btnIncluirMargemMinimaMarkupEmpresa.setColor("clBtnFace");
        btnIncluirMargemMinimaMarkupEmpresa.setAccess(false);
        btnIncluirMargemMinimaMarkupEmpresa.setIconReverseDirection(false);
        hBoxMargemMinimaMarkupEmpresaLinha01.addChildren(btnIncluirMargemMinimaMarkupEmpresa);
        btnIncluirMargemMinimaMarkupEmpresa.applyProperties();
    }

    public TFHBox FHBox25 = new TFHBox();

    private void init_FHBox25() {
        FHBox25.setName("FHBox25");
        FHBox25.setLeft(0);
        FHBox25.setTop(31);
        FHBox25.setWidth(480);
        FHBox25.setHeight(5);
        FHBox25.setBorderStyle("stNone");
        FHBox25.setPaddingTop(0);
        FHBox25.setPaddingLeft(0);
        FHBox25.setPaddingRight(0);
        FHBox25.setPaddingBottom(0);
        FHBox25.setMarginTop(0);
        FHBox25.setMarginLeft(0);
        FHBox25.setMarginRight(0);
        FHBox25.setMarginBottom(0);
        FHBox25.setSpacing(1);
        FHBox25.setFlexVflex("ftFalse");
        FHBox25.setFlexHflex("ftFalse");
        FHBox25.setScrollable(false);
        FHBox25.setBoxShadowConfigHorizontalLength(10);
        FHBox25.setBoxShadowConfigVerticalLength(10);
        FHBox25.setBoxShadowConfigBlurRadius(5);
        FHBox25.setBoxShadowConfigSpreadRadius(0);
        FHBox25.setBoxShadowConfigShadowColor("clBlack");
        FHBox25.setBoxShadowConfigOpacity(75);
        FHBox25.setVAlign("tvTop");
        vBoxVendaMargemMinimaMarkupEmpresa.addChildren(FHBox25);
        FHBox25.applyProperties();
    }

    public TFGrid grdMargemMinimaMarkupEmpresa = new TFGrid();

    private void init_grdMargemMinimaMarkupEmpresa() {
        grdMargemMinimaMarkupEmpresa.setName("grdMargemMinimaMarkupEmpresa");
        grdMargemMinimaMarkupEmpresa.setLeft(0);
        grdMargemMinimaMarkupEmpresa.setTop(37);
        grdMargemMinimaMarkupEmpresa.setWidth(480);
        grdMargemMinimaMarkupEmpresa.setHeight(140);
        grdMargemMinimaMarkupEmpresa.setHint("Margem m\u00EDnima markup/empresa");
        grdMargemMinimaMarkupEmpresa.setTable(tbCliEspMargEmp);
        grdMargemMinimaMarkupEmpresa.setFlexVflex("ftTrue");
        grdMargemMinimaMarkupEmpresa.setFlexHflex("ftTrue");
        grdMargemMinimaMarkupEmpresa.setPagingEnabled(false);
        grdMargemMinimaMarkupEmpresa.setFrozenColumns(0);
        grdMargemMinimaMarkupEmpresa.setShowFooter(false);
        grdMargemMinimaMarkupEmpresa.setShowHeader(true);
        grdMargemMinimaMarkupEmpresa.setMultiSelection(false);
        grdMargemMinimaMarkupEmpresa.setGroupingEnabled(false);
        grdMargemMinimaMarkupEmpresa.setGroupingExpanded(false);
        grdMargemMinimaMarkupEmpresa.setGroupingShowFooter(false);
        grdMargemMinimaMarkupEmpresa.setCrosstabEnabled(false);
        grdMargemMinimaMarkupEmpresa.setCrosstabGroupType("cgtConcat");
        grdMargemMinimaMarkupEmpresa.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdMargemMinimaMarkupEmpresaDoubleClick(event);
            processarFlow("FrmClientesFlags", "grdMargemMinimaMarkupEmpresa", "OnDoubleClick");
        });
        grdMargemMinimaMarkupEmpresa.setEditionEnabled(true);
        grdMargemMinimaMarkupEmpresa.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setHint("Alterar margem m\u00EDnima markup/empresa");
        item1.setEvalType("etExpression");
        item1.setImageId(7);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdMargemMinimaMarkupEmpresaalterarMargemMinimaMarkupEmpresa(event);
            processarFlow("FrmClientesFlags", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdMargemMinimaMarkupEmpresa.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setWidth(33);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        TFImageExpression item3 = new TFImageExpression();
        item3.setExpression("*");
        item3.setHint("Excluir margem m\u00EDnima markup/empresa");
        item3.setEvalType("etExpression");
        item3.setImageId(700095);
        item3.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdMargemMinimaMarkupEmpresaexcluirMargemMinimaMarkupEmpresa(event);
            processarFlow("FrmClientesFlags", "item3", "OnClick");
        });
        item2.getImages().add(item3);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdMargemMinimaMarkupEmpresa.getColumns().add(item2);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("EMPRESA_NOME_CODIGO");
        item4.setTitleCaption("Empresa");
        item4.setWidth(120);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdMargemMinimaMarkupEmpresa.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("MARGEM_MINIMA");
        item5.setTitleCaption("Margem M\u00EDnima");
        item5.setWidth(100);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFMaskExpression item6 = new TFMaskExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setMask(",##0.00");
        item6.setPadLength(0);
        item6.setPadDirection("pdNone");
        item6.setMaskType("mtDecimal");
        item5.getMasks().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdMargemMinimaMarkupEmpresa.getColumns().add(item5);
        vBoxVendaMargemMinimaMarkupEmpresa.addChildren(grdMargemMinimaMarkupEmpresa);
        grdMargemMinimaMarkupEmpresa.applyProperties();
    }

    public TFHBox FHBox39 = new TFHBox();

    private void init_FHBox39() {
        FHBox39.setName("FHBox39");
        FHBox39.setLeft(1000);
        FHBox39.setTop(0);
        FHBox39.setWidth(5);
        FHBox39.setHeight(20);
        FHBox39.setBorderStyle("stNone");
        FHBox39.setPaddingTop(0);
        FHBox39.setPaddingLeft(0);
        FHBox39.setPaddingRight(0);
        FHBox39.setPaddingBottom(0);
        FHBox39.setMarginTop(0);
        FHBox39.setMarginLeft(0);
        FHBox39.setMarginRight(0);
        FHBox39.setMarginBottom(0);
        FHBox39.setSpacing(1);
        FHBox39.setFlexVflex("ftFalse");
        FHBox39.setFlexHflex("ftFalse");
        FHBox39.setScrollable(false);
        FHBox39.setBoxShadowConfigHorizontalLength(10);
        FHBox39.setBoxShadowConfigVerticalLength(10);
        FHBox39.setBoxShadowConfigBlurRadius(5);
        FHBox39.setBoxShadowConfigSpreadRadius(0);
        FHBox39.setBoxShadowConfigShadowColor("clBlack");
        FHBox39.setBoxShadowConfigOpacity(75);
        FHBox39.setVAlign("tvTop");
        FHBox8.addChildren(FHBox39);
        FHBox39.applyProperties();
    }

    public TFHBox FHBox19 = new TFHBox();

    private void init_FHBox19() {
        FHBox19.setName("FHBox19");
        FHBox19.setLeft(0);
        FHBox19.setTop(481);
        FHBox19.setWidth(1000);
        FHBox19.setHeight(5);
        FHBox19.setBorderStyle("stNone");
        FHBox19.setPaddingTop(0);
        FHBox19.setPaddingLeft(0);
        FHBox19.setPaddingRight(0);
        FHBox19.setPaddingBottom(0);
        FHBox19.setMarginTop(0);
        FHBox19.setMarginLeft(0);
        FHBox19.setMarginRight(0);
        FHBox19.setMarginBottom(0);
        FHBox19.setSpacing(1);
        FHBox19.setFlexVflex("ftFalse");
        FHBox19.setFlexHflex("ftFalse");
        FHBox19.setScrollable(false);
        FHBox19.setBoxShadowConfigHorizontalLength(10);
        FHBox19.setBoxShadowConfigVerticalLength(10);
        FHBox19.setBoxShadowConfigBlurRadius(5);
        FHBox19.setBoxShadowConfigSpreadRadius(0);
        FHBox19.setBoxShadowConfigShadowColor("clBlack");
        FHBox19.setBoxShadowConfigOpacity(75);
        FHBox19.setVAlign("tvTop");
        vBoxTabDesconto.addChildren(FHBox19);
        FHBox19.applyProperties();
    }

    public TFTabsheet tabVenda = new TFTabsheet();

    private void init_tabVenda() {
        tabVenda.setName("tabVenda");
        tabVenda.setCaption("Venda");
        tabVenda.setVisible(true);
        tabVenda.setClosable(false);
        pgFlags.addChildren(tabVenda);
        tabVenda.applyProperties();
    }

    public TFVBox vBoxTabVenda = new TFVBox();

    private void init_vBoxTabVenda() {
        vBoxTabVenda.setName("vBoxTabVenda");
        vBoxTabVenda.setLeft(0);
        vBoxTabVenda.setTop(0);
        vBoxTabVenda.setWidth(1012);
        vBoxTabVenda.setHeight(537);
        vBoxTabVenda.setAlign("alClient");
        vBoxTabVenda.setBorderStyle("stNone");
        vBoxTabVenda.setPaddingTop(0);
        vBoxTabVenda.setPaddingLeft(0);
        vBoxTabVenda.setPaddingRight(0);
        vBoxTabVenda.setPaddingBottom(0);
        vBoxTabVenda.setMarginTop(0);
        vBoxTabVenda.setMarginLeft(0);
        vBoxTabVenda.setMarginRight(0);
        vBoxTabVenda.setMarginBottom(0);
        vBoxTabVenda.setSpacing(1);
        vBoxTabVenda.setFlexVflex("ftTrue");
        vBoxTabVenda.setFlexHflex("ftTrue");
        vBoxTabVenda.setScrollable(false);
        vBoxTabVenda.setBoxShadowConfigHorizontalLength(10);
        vBoxTabVenda.setBoxShadowConfigVerticalLength(10);
        vBoxTabVenda.setBoxShadowConfigBlurRadius(5);
        vBoxTabVenda.setBoxShadowConfigSpreadRadius(0);
        vBoxTabVenda.setBoxShadowConfigShadowColor("clBlack");
        vBoxTabVenda.setBoxShadowConfigOpacity(75);
        tabVenda.addChildren(vBoxTabVenda);
        vBoxTabVenda.applyProperties();
    }

    public TFHBox FHBox26 = new TFHBox();

    private void init_FHBox26() {
        FHBox26.setName("FHBox26");
        FHBox26.setLeft(0);
        FHBox26.setTop(0);
        FHBox26.setWidth(1000);
        FHBox26.setHeight(5);
        FHBox26.setBorderStyle("stNone");
        FHBox26.setPaddingTop(0);
        FHBox26.setPaddingLeft(0);
        FHBox26.setPaddingRight(0);
        FHBox26.setPaddingBottom(0);
        FHBox26.setMarginTop(0);
        FHBox26.setMarginLeft(0);
        FHBox26.setMarginRight(0);
        FHBox26.setMarginBottom(0);
        FHBox26.setSpacing(1);
        FHBox26.setFlexVflex("ftFalse");
        FHBox26.setFlexHflex("ftFalse");
        FHBox26.setScrollable(false);
        FHBox26.setBoxShadowConfigHorizontalLength(10);
        FHBox26.setBoxShadowConfigVerticalLength(10);
        FHBox26.setBoxShadowConfigBlurRadius(5);
        FHBox26.setBoxShadowConfigSpreadRadius(0);
        FHBox26.setBoxShadowConfigShadowColor("clBlack");
        FHBox26.setBoxShadowConfigOpacity(75);
        FHBox26.setVAlign("tvTop");
        vBoxTabVenda.addChildren(FHBox26);
        FHBox26.applyProperties();
    }

    public TFPageControl pgFlagsDiverso = new TFPageControl();

    private void init_pgFlagsDiverso() {
        pgFlagsDiverso.setName("pgFlagsDiverso");
        pgFlagsDiverso.setLeft(0);
        pgFlagsDiverso.setTop(6);
        pgFlagsDiverso.setWidth(1006);
        pgFlagsDiverso.setHeight(429);
        pgFlagsDiverso.setTabPosition("tpTop");
        pgFlagsDiverso.setFlexVflex("ftTrue");
        pgFlagsDiverso.setFlexHflex("ftTrue");
        pgFlagsDiverso.setRenderStyle("rsTabbed");
        pgFlagsDiverso.applyProperties();
        vBoxTabVenda.addChildren(pgFlagsDiverso);
    }

    public TFTabsheet tabNegociacao = new TFTabsheet();

    private void init_tabNegociacao() {
        tabNegociacao.setName("tabNegociacao");
        tabNegociacao.setCaption("Negocia\u00E7\u00E3o");
        tabNegociacao.setFontColor("clWindowText");
        tabNegociacao.setFontSize(-12);
        tabNegociacao.setFontName("Tahoma");
        tabNegociacao.setFontStyle("[]");
        tabNegociacao.setVisible(true);
        tabNegociacao.setClosable(false);
        pgFlagsDiverso.addChildren(tabNegociacao);
        tabNegociacao.applyProperties();
    }

    public TFVBox vBoxTabNegociacao = new TFVBox();

    private void init_vBoxTabNegociacao() {
        vBoxTabNegociacao.setName("vBoxTabNegociacao");
        vBoxTabNegociacao.setLeft(0);
        vBoxTabNegociacao.setTop(0);
        vBoxTabNegociacao.setWidth(998);
        vBoxTabNegociacao.setHeight(401);
        vBoxTabNegociacao.setAlign("alClient");
        vBoxTabNegociacao.setBorderStyle("stNone");
        vBoxTabNegociacao.setPaddingTop(0);
        vBoxTabNegociacao.setPaddingLeft(0);
        vBoxTabNegociacao.setPaddingRight(0);
        vBoxTabNegociacao.setPaddingBottom(0);
        vBoxTabNegociacao.setMarginTop(5);
        vBoxTabNegociacao.setMarginLeft(5);
        vBoxTabNegociacao.setMarginRight(5);
        vBoxTabNegociacao.setMarginBottom(5);
        vBoxTabNegociacao.setSpacing(5);
        vBoxTabNegociacao.setFlexVflex("ftTrue");
        vBoxTabNegociacao.setFlexHflex("ftTrue");
        vBoxTabNegociacao.setScrollable(false);
        vBoxTabNegociacao.setBoxShadowConfigHorizontalLength(10);
        vBoxTabNegociacao.setBoxShadowConfigVerticalLength(10);
        vBoxTabNegociacao.setBoxShadowConfigBlurRadius(5);
        vBoxTabNegociacao.setBoxShadowConfigSpreadRadius(0);
        vBoxTabNegociacao.setBoxShadowConfigShadowColor("clBlack");
        vBoxTabNegociacao.setBoxShadowConfigOpacity(75);
        tabNegociacao.addChildren(vBoxTabNegociacao);
        vBoxTabNegociacao.applyProperties();
    }

    public TFHBox FHBox35 = new TFHBox();

    private void init_FHBox35() {
        FHBox35.setName("FHBox35");
        FHBox35.setLeft(0);
        FHBox35.setTop(0);
        FHBox35.setWidth(1000);
        FHBox35.setHeight(5);
        FHBox35.setBorderStyle("stNone");
        FHBox35.setPaddingTop(0);
        FHBox35.setPaddingLeft(0);
        FHBox35.setPaddingRight(0);
        FHBox35.setPaddingBottom(0);
        FHBox35.setMarginTop(0);
        FHBox35.setMarginLeft(0);
        FHBox35.setMarginRight(0);
        FHBox35.setMarginBottom(0);
        FHBox35.setSpacing(1);
        FHBox35.setFlexVflex("ftFalse");
        FHBox35.setFlexHflex("ftFalse");
        FHBox35.setScrollable(false);
        FHBox35.setBoxShadowConfigHorizontalLength(10);
        FHBox35.setBoxShadowConfigVerticalLength(10);
        FHBox35.setBoxShadowConfigBlurRadius(5);
        FHBox35.setBoxShadowConfigSpreadRadius(0);
        FHBox35.setBoxShadowConfigShadowColor("clBlack");
        FHBox35.setBoxShadowConfigOpacity(75);
        FHBox35.setVAlign("tvTop");
        vBoxTabNegociacao.addChildren(FHBox35);
        FHBox35.applyProperties();
    }

    public TFVBox vBoxPrecosPraticadosNaVenda = new TFVBox();

    private void init_vBoxPrecosPraticadosNaVenda() {
        vBoxPrecosPraticadosNaVenda.setName("vBoxPrecosPraticadosNaVenda");
        vBoxPrecosPraticadosNaVenda.setLeft(0);
        vBoxPrecosPraticadosNaVenda.setTop(6);
        vBoxPrecosPraticadosNaVenda.setWidth(260);
        vBoxPrecosPraticadosNaVenda.setHeight(60);
        vBoxPrecosPraticadosNaVenda.setBorderStyle("stNone");
        vBoxPrecosPraticadosNaVenda.setPaddingTop(0);
        vBoxPrecosPraticadosNaVenda.setPaddingLeft(0);
        vBoxPrecosPraticadosNaVenda.setPaddingRight(0);
        vBoxPrecosPraticadosNaVenda.setPaddingBottom(0);
        vBoxPrecosPraticadosNaVenda.setMarginTop(0);
        vBoxPrecosPraticadosNaVenda.setMarginLeft(0);
        vBoxPrecosPraticadosNaVenda.setMarginRight(0);
        vBoxPrecosPraticadosNaVenda.setMarginBottom(0);
        vBoxPrecosPraticadosNaVenda.setSpacing(1);
        vBoxPrecosPraticadosNaVenda.setFlexVflex("ftMin");
        vBoxPrecosPraticadosNaVenda.setFlexHflex("ftTrue");
        vBoxPrecosPraticadosNaVenda.setScrollable(false);
        vBoxPrecosPraticadosNaVenda.setBoxShadowConfigHorizontalLength(10);
        vBoxPrecosPraticadosNaVenda.setBoxShadowConfigVerticalLength(10);
        vBoxPrecosPraticadosNaVenda.setBoxShadowConfigBlurRadius(5);
        vBoxPrecosPraticadosNaVenda.setBoxShadowConfigSpreadRadius(0);
        vBoxPrecosPraticadosNaVenda.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrecosPraticadosNaVenda.setBoxShadowConfigOpacity(75);
        vBoxTabNegociacao.addChildren(vBoxPrecosPraticadosNaVenda);
        vBoxPrecosPraticadosNaVenda.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(156);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Pre\u00E7os praticados na venda");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[fsBold]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        vBoxPrecosPraticadosNaVenda.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCheckBox chkUsaPrecoFabrica = new TFCheckBox();

    private void init_chkUsaPrecoFabrica() {
        chkUsaPrecoFabrica.setName("chkUsaPrecoFabrica");
        chkUsaPrecoFabrica.setLeft(0);
        chkUsaPrecoFabrica.setTop(14);
        chkUsaPrecoFabrica.setWidth(110);
        chkUsaPrecoFabrica.setHeight(21);
        chkUsaPrecoFabrica.setHint("Usa pre\u00E7o f\u00E1brica");
        chkUsaPrecoFabrica.setCaption("Usa pre\u00E7o f\u00E1brica");
        chkUsaPrecoFabrica.setFontColor("clWindowText");
        chkUsaPrecoFabrica.setFontSize(-11);
        chkUsaPrecoFabrica.setFontName("Tahoma");
        chkUsaPrecoFabrica.setFontStyle("[]");
        chkUsaPrecoFabrica.setTable(tbClienteDiversoFlag);
        chkUsaPrecoFabrica.setFieldName("CLIENTE_PRECO_FABRICA");
        chkUsaPrecoFabrica.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkUsaPrecoFabricaCheck(event);
            processarFlow("FrmClientesFlags", "chkUsaPrecoFabrica", "OnCheck");
        });
        chkUsaPrecoFabrica.setHelpCaption("Usa pre\u00E7o f\u00E1brica");
        chkUsaPrecoFabrica.setVerticalAlignment("taAlignTop");
        vBoxPrecosPraticadosNaVenda.addChildren(chkUsaPrecoFabrica);
        chkUsaPrecoFabrica.applyProperties();
    }

    public TFCheckBox chkUsaPrecoGarantia = new TFCheckBox();

    private void init_chkUsaPrecoGarantia() {
        chkUsaPrecoGarantia.setName("chkUsaPrecoGarantia");
        chkUsaPrecoGarantia.setLeft(0);
        chkUsaPrecoGarantia.setTop(36);
        chkUsaPrecoGarantia.setWidth(109);
        chkUsaPrecoGarantia.setHeight(17);
        chkUsaPrecoGarantia.setHint("Usa pre\u00E7o garantia");
        chkUsaPrecoGarantia.setCaption("Usa pre\u00E7o garantia");
        chkUsaPrecoGarantia.setFontColor("clWindowText");
        chkUsaPrecoGarantia.setFontSize(-11);
        chkUsaPrecoGarantia.setFontName("Tahoma");
        chkUsaPrecoGarantia.setFontStyle("[]");
        chkUsaPrecoGarantia.setTable(tbClienteDiversoFlag);
        chkUsaPrecoGarantia.setFieldName("CLIENTE_GARANTIA");
        chkUsaPrecoGarantia.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkUsaPrecoGarantiaCheck(event);
            processarFlow("FrmClientesFlags", "chkUsaPrecoGarantia", "OnCheck");
        });
        chkUsaPrecoGarantia.setHelpCaption("Usa pre\u00E7o garantia");
        chkUsaPrecoGarantia.setVerticalAlignment("taAlignTop");
        vBoxPrecosPraticadosNaVenda.addChildren(chkUsaPrecoGarantia);
        chkUsaPrecoGarantia.applyProperties();
    }

    public TFVBox vBoxPrePedido = new TFVBox();

    private void init_vBoxPrePedido() {
        vBoxPrePedido.setName("vBoxPrePedido");
        vBoxPrePedido.setLeft(0);
        vBoxPrePedido.setTop(67);
        vBoxPrePedido.setWidth(260);
        vBoxPrePedido.setHeight(40);
        vBoxPrePedido.setBorderStyle("stNone");
        vBoxPrePedido.setPaddingTop(0);
        vBoxPrePedido.setPaddingLeft(5);
        vBoxPrePedido.setPaddingRight(0);
        vBoxPrePedido.setPaddingBottom(0);
        vBoxPrePedido.setMarginTop(0);
        vBoxPrePedido.setMarginLeft(0);
        vBoxPrePedido.setMarginRight(0);
        vBoxPrePedido.setMarginBottom(0);
        vBoxPrePedido.setSpacing(1);
        vBoxPrePedido.setFlexVflex("ftMin");
        vBoxPrePedido.setFlexHflex("ftTrue");
        vBoxPrePedido.setScrollable(false);
        vBoxPrePedido.setBoxShadowConfigHorizontalLength(10);
        vBoxPrePedido.setBoxShadowConfigVerticalLength(10);
        vBoxPrePedido.setBoxShadowConfigBlurRadius(5);
        vBoxPrePedido.setBoxShadowConfigSpreadRadius(0);
        vBoxPrePedido.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrePedido.setBoxShadowConfigOpacity(75);
        vBoxTabNegociacao.addChildren(vBoxPrePedido);
        vBoxPrePedido.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(62);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Pr\u00E9-Pedido");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[fsBold]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        vBoxPrePedido.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCheckBox chkAprovaAuto = new TFCheckBox();

    private void init_chkAprovaAuto() {
        chkAprovaAuto.setName("chkAprovaAuto");
        chkAprovaAuto.setLeft(0);
        chkAprovaAuto.setTop(14);
        chkAprovaAuto.setWidth(120);
        chkAprovaAuto.setHeight(21);
        chkAprovaAuto.setHint("Aprova autom\u00E1tico");
        chkAprovaAuto.setCaption("Aprova autom\u00E1tico");
        chkAprovaAuto.setFontColor("clWindowText");
        chkAprovaAuto.setFontSize(-11);
        chkAprovaAuto.setFontName("Tahoma");
        chkAprovaAuto.setFontStyle("[]");
        chkAprovaAuto.setTable(tbClienteDiversoFlag);
        chkAprovaAuto.setFieldName("VP_AUTOMATICA");
        chkAprovaAuto.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkAprovaAutoCheck(event);
            processarFlow("FrmClientesFlags", "chkAprovaAuto", "OnCheck");
        });
        chkAprovaAuto.setHelpCaption("Aprova autom\u00E1tico");
        chkAprovaAuto.setVerticalAlignment("taAlignTop");
        vBoxPrePedido.addChildren(chkAprovaAuto);
        chkAprovaAuto.applyProperties();
    }

    public TFVBox vBoxReservaDaPeca = new TFVBox();

    private void init_vBoxReservaDaPeca() {
        vBoxReservaDaPeca.setName("vBoxReservaDaPeca");
        vBoxReservaDaPeca.setLeft(0);
        vBoxReservaDaPeca.setTop(108);
        vBoxReservaDaPeca.setWidth(260);
        vBoxReservaDaPeca.setHeight(50);
        vBoxReservaDaPeca.setBorderStyle("stNone");
        vBoxReservaDaPeca.setPaddingTop(0);
        vBoxReservaDaPeca.setPaddingLeft(5);
        vBoxReservaDaPeca.setPaddingRight(0);
        vBoxReservaDaPeca.setPaddingBottom(0);
        vBoxReservaDaPeca.setMarginTop(0);
        vBoxReservaDaPeca.setMarginLeft(0);
        vBoxReservaDaPeca.setMarginRight(0);
        vBoxReservaDaPeca.setMarginBottom(0);
        vBoxReservaDaPeca.setSpacing(1);
        vBoxReservaDaPeca.setFlexVflex("ftMin");
        vBoxReservaDaPeca.setFlexHflex("ftTrue");
        vBoxReservaDaPeca.setScrollable(false);
        vBoxReservaDaPeca.setBoxShadowConfigHorizontalLength(10);
        vBoxReservaDaPeca.setBoxShadowConfigVerticalLength(10);
        vBoxReservaDaPeca.setBoxShadowConfigBlurRadius(5);
        vBoxReservaDaPeca.setBoxShadowConfigSpreadRadius(0);
        vBoxReservaDaPeca.setBoxShadowConfigShadowColor("clBlack");
        vBoxReservaDaPeca.setBoxShadowConfigOpacity(75);
        vBoxTabNegociacao.addChildren(vBoxReservaDaPeca);
        vBoxReservaDaPeca.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(94);
        FLabel3.setHeight(13);
        FLabel3.setCaption("Reserva da Pe\u00E7a");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[fsBold]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        vBoxReservaDaPeca.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFHBox hBoxReservaDaPeca = new TFHBox();

    private void init_hBoxReservaDaPeca() {
        hBoxReservaDaPeca.setName("hBoxReservaDaPeca");
        hBoxReservaDaPeca.setLeft(0);
        hBoxReservaDaPeca.setTop(14);
        hBoxReservaDaPeca.setWidth(250);
        hBoxReservaDaPeca.setHeight(30);
        hBoxReservaDaPeca.setBorderStyle("stNone");
        hBoxReservaDaPeca.setPaddingTop(0);
        hBoxReservaDaPeca.setPaddingLeft(0);
        hBoxReservaDaPeca.setPaddingRight(0);
        hBoxReservaDaPeca.setPaddingBottom(0);
        hBoxReservaDaPeca.setMarginTop(0);
        hBoxReservaDaPeca.setMarginLeft(0);
        hBoxReservaDaPeca.setMarginRight(0);
        hBoxReservaDaPeca.setMarginBottom(0);
        hBoxReservaDaPeca.setSpacing(5);
        hBoxReservaDaPeca.setFlexVflex("ftMin");
        hBoxReservaDaPeca.setFlexHflex("ftTrue");
        hBoxReservaDaPeca.setScrollable(false);
        hBoxReservaDaPeca.setBoxShadowConfigHorizontalLength(10);
        hBoxReservaDaPeca.setBoxShadowConfigVerticalLength(10);
        hBoxReservaDaPeca.setBoxShadowConfigBlurRadius(5);
        hBoxReservaDaPeca.setBoxShadowConfigSpreadRadius(0);
        hBoxReservaDaPeca.setBoxShadowConfigShadowColor("clBlack");
        hBoxReservaDaPeca.setBoxShadowConfigOpacity(75);
        hBoxReservaDaPeca.setVAlign("tvTop");
        vBoxReservaDaPeca.addChildren(hBoxReservaDaPeca);
        hBoxReservaDaPeca.applyProperties();
    }

    public TFCheckBox chkReservaAuto = new TFCheckBox();

    private void init_chkReservaAuto() {
        chkReservaAuto.setName("chkReservaAuto");
        chkReservaAuto.setLeft(0);
        chkReservaAuto.setTop(0);
        chkReservaAuto.setWidth(80);
        chkReservaAuto.setHeight(21);
        chkReservaAuto.setHint("Autom\u00E1tica");
        chkReservaAuto.setCaption("Autom\u00E1tica");
        chkReservaAuto.setFontColor("clWindowText");
        chkReservaAuto.setFontSize(-11);
        chkReservaAuto.setFontName("Tahoma");
        chkReservaAuto.setFontStyle("[]");
        chkReservaAuto.setTable(tbClienteDiversoFlag);
        chkReservaAuto.setFieldName("RESERVA_AUTOMATICA");
        chkReservaAuto.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkReservaAutoCheck(event);
            processarFlow("FrmClientesFlags", "chkReservaAuto", "OnCheck");
        });
        chkReservaAuto.setHelpCaption("Autom\u00E1tica");
        chkReservaAuto.setVerticalAlignment("taAlignTop");
        hBoxReservaDaPeca.addChildren(chkReservaAuto);
        chkReservaAuto.applyProperties();
    }

    public TFLabel lblTempoAdicional = new TFLabel();

    private void init_lblTempoAdicional() {
        lblTempoAdicional.setName("lblTempoAdicional");
        lblTempoAdicional.setLeft(80);
        lblTempoAdicional.setTop(0);
        lblTempoAdicional.setWidth(77);
        lblTempoAdicional.setHeight(13);
        lblTempoAdicional.setCaption("Tempo Adicional");
        lblTempoAdicional.setFontColor("clWindowText");
        lblTempoAdicional.setFontSize(-11);
        lblTempoAdicional.setFontName("Tahoma");
        lblTempoAdicional.setFontStyle("[]");
        lblTempoAdicional.setVerticalAlignment("taVerticalCenter");
        lblTempoAdicional.setWordBreak(false);
        hBoxReservaDaPeca.addChildren(lblTempoAdicional);
        lblTempoAdicional.applyProperties();
    }

    public TFInteger intTempoReserva = new TFInteger();

    private void init_intTempoReserva() {
        intTempoReserva.setName("intTempoReserva");
        intTempoReserva.setLeft(157);
        intTempoReserva.setTop(0);
        intTempoReserva.setWidth(39);
        intTempoReserva.setHeight(24);
        intTempoReserva.setHint("Tempo Adicional (Horas)");
        intTempoReserva.setTable(tbClienteDiversoFlag);
        intTempoReserva.setFieldName("TEMPO_RESERVA_ADICIONAL");
        intTempoReserva.setHelpCaption("Tempo Adicional (Horas)");
        intTempoReserva.setFlex(false);
        intTempoReserva.setRequired(false);
        intTempoReserva.setPrompt("Tempo Adicional (Horas)");
        intTempoReserva.setConstraintCheckWhen("cwImmediate");
        intTempoReserva.setConstraintCheckType("ctExpression");
        intTempoReserva.setConstraintFocusOnError(false);
        intTempoReserva.setConstraintEnableUI(true);
        intTempoReserva.setConstraintEnabled(false);
        intTempoReserva.setConstraintFormCheck(true);
        intTempoReserva.setMaxlength(0);
        intTempoReserva.setFontColor("clWindowText");
        intTempoReserva.setFontSize(-13);
        intTempoReserva.setFontName("Tahoma");
        intTempoReserva.setFontStyle("[]");
        intTempoReserva.setAlignment("taRightJustify");
        intTempoReserva.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            intTempoReservaExit(event);
            processarFlow("FrmClientesFlags", "intTempoReserva", "OnExit");
        });
        hBoxReservaDaPeca.addChildren(intTempoReserva);
        intTempoReserva.applyProperties();
        addValidatable(intTempoReserva);
    }

    public TFLabel lblHoras = new TFLabel();

    private void init_lblHoras() {
        lblHoras.setName("lblHoras");
        lblHoras.setLeft(196);
        lblHoras.setTop(0);
        lblHoras.setWidth(28);
        lblHoras.setHeight(13);
        lblHoras.setCaption("Horas");
        lblHoras.setFontColor("clWindowText");
        lblHoras.setFontSize(-11);
        lblHoras.setFontName("Tahoma");
        lblHoras.setFontStyle("[]");
        lblHoras.setVerticalAlignment("taVerticalCenter");
        lblHoras.setWordBreak(false);
        hBoxReservaDaPeca.addChildren(lblHoras);
        lblHoras.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(159);
        FVBox8.setWidth(260);
        FVBox8.setHeight(40);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftMin");
        FVBox8.setFlexHflex("ftTrue");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        vBoxTabNegociacao.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel lblClienteAtacado = new TFLabel();

    private void init_lblClienteAtacado() {
        lblClienteAtacado.setName("lblClienteAtacado");
        lblClienteAtacado.setLeft(0);
        lblClienteAtacado.setTop(0);
        lblClienteAtacado.setWidth(89);
        lblClienteAtacado.setHeight(13);
        lblClienteAtacado.setCaption("Cliente Atacado");
        lblClienteAtacado.setFontColor("clWindowText");
        lblClienteAtacado.setFontSize(-11);
        lblClienteAtacado.setFontName("Tahoma");
        lblClienteAtacado.setFontStyle("[fsBold]");
        lblClienteAtacado.setVerticalAlignment("taVerticalCenter");
        lblClienteAtacado.setWordBreak(false);
        FVBox8.addChildren(lblClienteAtacado);
        lblClienteAtacado.applyProperties();
    }

    public TFCheckBox chkAtacadista = new TFCheckBox();

    private void init_chkAtacadista() {
        chkAtacadista.setName("chkAtacadista");
        chkAtacadista.setLeft(0);
        chkAtacadista.setTop(14);
        chkAtacadista.setWidth(90);
        chkAtacadista.setHeight(17);
        chkAtacadista.setHint("Atacadista");
        chkAtacadista.setCaption("Atacadista");
        chkAtacadista.setFontColor("clWindowText");
        chkAtacadista.setFontSize(-11);
        chkAtacadista.setFontName("Tahoma");
        chkAtacadista.setFontStyle("[]");
        chkAtacadista.setTable(tbClienteDiversoFlag);
        chkAtacadista.setFieldName("EH_ATACADISTA");
        chkAtacadista.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkAtacadistaCheck(event);
            processarFlow("FrmClientesFlags", "chkAtacadista", "OnCheck");
        });
        chkAtacadista.setHelpCaption("Atacadista");
        chkAtacadista.setVerticalAlignment("taAlignTop");
        FVBox8.addChildren(chkAtacadista);
        chkAtacadista.applyProperties();
    }

    public TFTabsheet tabVendedor = new TFTabsheet();

    private void init_tabVendedor() {
        tabVendedor.setName("tabVendedor");
        tabVendedor.setCaption("Vendedor");
        tabVendedor.setFontColor("clWindowText");
        tabVendedor.setFontSize(-12);
        tabVendedor.setFontName("Tahoma");
        tabVendedor.setFontStyle("[]");
        tabVendedor.setVisible(true);
        tabVendedor.setClosable(false);
        pgFlagsDiverso.addChildren(tabVendedor);
        tabVendedor.applyProperties();
    }

    public TFVBox vBoxVendedor = new TFVBox();

    private void init_vBoxVendedor() {
        vBoxVendedor.setName("vBoxVendedor");
        vBoxVendedor.setLeft(0);
        vBoxVendedor.setTop(0);
        vBoxVendedor.setWidth(998);
        vBoxVendedor.setHeight(401);
        vBoxVendedor.setAlign("alClient");
        vBoxVendedor.setBorderStyle("stNone");
        vBoxVendedor.setPaddingTop(0);
        vBoxVendedor.setPaddingLeft(0);
        vBoxVendedor.setPaddingRight(0);
        vBoxVendedor.setPaddingBottom(0);
        vBoxVendedor.setMarginTop(0);
        vBoxVendedor.setMarginLeft(0);
        vBoxVendedor.setMarginRight(0);
        vBoxVendedor.setMarginBottom(0);
        vBoxVendedor.setSpacing(1);
        vBoxVendedor.setFlexVflex("ftTrue");
        vBoxVendedor.setFlexHflex("ftTrue");
        vBoxVendedor.setScrollable(false);
        vBoxVendedor.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedor.setBoxShadowConfigVerticalLength(10);
        vBoxVendedor.setBoxShadowConfigBlurRadius(5);
        vBoxVendedor.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedor.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedor.setBoxShadowConfigOpacity(75);
        tabVendedor.addChildren(vBoxVendedor);
        vBoxVendedor.applyProperties();
    }

    public TFHBox FHBox37 = new TFHBox();

    private void init_FHBox37() {
        FHBox37.setName("FHBox37");
        FHBox37.setLeft(0);
        FHBox37.setTop(0);
        FHBox37.setWidth(1000);
        FHBox37.setHeight(140);
        FHBox37.setBorderStyle("stNone");
        FHBox37.setPaddingTop(0);
        FHBox37.setPaddingLeft(0);
        FHBox37.setPaddingRight(0);
        FHBox37.setPaddingBottom(0);
        FHBox37.setMarginTop(0);
        FHBox37.setMarginLeft(0);
        FHBox37.setMarginRight(0);
        FHBox37.setMarginBottom(0);
        FHBox37.setSpacing(1);
        FHBox37.setFlexVflex("ftMin");
        FHBox37.setFlexHflex("ftTrue");
        FHBox37.setScrollable(false);
        FHBox37.setBoxShadowConfigHorizontalLength(10);
        FHBox37.setBoxShadowConfigVerticalLength(10);
        FHBox37.setBoxShadowConfigBlurRadius(5);
        FHBox37.setBoxShadowConfigSpreadRadius(0);
        FHBox37.setBoxShadowConfigShadowColor("clBlack");
        FHBox37.setBoxShadowConfigOpacity(75);
        FHBox37.setVAlign("tvTop");
        vBoxVendedor.addChildren(FHBox37);
        FHBox37.applyProperties();
    }

    public TFHBox FHBox38 = new TFHBox();

    private void init_FHBox38() {
        FHBox38.setName("FHBox38");
        FHBox38.setLeft(0);
        FHBox38.setTop(0);
        FHBox38.setWidth(5);
        FHBox38.setHeight(20);
        FHBox38.setBorderStyle("stNone");
        FHBox38.setPaddingTop(0);
        FHBox38.setPaddingLeft(0);
        FHBox38.setPaddingRight(0);
        FHBox38.setPaddingBottom(0);
        FHBox38.setMarginTop(0);
        FHBox38.setMarginLeft(0);
        FHBox38.setMarginRight(0);
        FHBox38.setMarginBottom(0);
        FHBox38.setSpacing(1);
        FHBox38.setFlexVflex("ftFalse");
        FHBox38.setFlexHflex("ftFalse");
        FHBox38.setScrollable(false);
        FHBox38.setBoxShadowConfigHorizontalLength(10);
        FHBox38.setBoxShadowConfigVerticalLength(10);
        FHBox38.setBoxShadowConfigBlurRadius(5);
        FHBox38.setBoxShadowConfigSpreadRadius(0);
        FHBox38.setBoxShadowConfigShadowColor("clBlack");
        FHBox38.setBoxShadowConfigOpacity(75);
        FHBox38.setVAlign("tvTop");
        FHBox37.addChildren(FHBox38);
        FHBox38.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(5);
        FVBox5.setTop(0);
        FVBox5.setWidth(465);
        FVBox5.setHeight(130);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox37.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFHBox FHBox44 = new TFHBox();

    private void init_FHBox44() {
        FHBox44.setName("FHBox44");
        FHBox44.setLeft(0);
        FHBox44.setTop(0);
        FHBox44.setWidth(61);
        FHBox44.setHeight(5);
        FHBox44.setBorderStyle("stNone");
        FHBox44.setPaddingTop(0);
        FHBox44.setPaddingLeft(0);
        FHBox44.setPaddingRight(0);
        FHBox44.setPaddingBottom(0);
        FHBox44.setMarginTop(0);
        FHBox44.setMarginLeft(0);
        FHBox44.setMarginRight(0);
        FHBox44.setMarginBottom(0);
        FHBox44.setSpacing(1);
        FHBox44.setFlexVflex("ftFalse");
        FHBox44.setFlexHflex("ftFalse");
        FHBox44.setScrollable(false);
        FHBox44.setBoxShadowConfigHorizontalLength(10);
        FHBox44.setBoxShadowConfigVerticalLength(10);
        FHBox44.setBoxShadowConfigBlurRadius(5);
        FHBox44.setBoxShadowConfigSpreadRadius(0);
        FHBox44.setBoxShadowConfigShadowColor("clBlack");
        FHBox44.setBoxShadowConfigOpacity(75);
        FHBox44.setVAlign("tvTop");
        FVBox5.addChildren(FHBox44);
        FHBox44.applyProperties();
    }

    public TFVBox vBoxRepresentanteAssociadoAoCliente = new TFVBox();

    private void init_vBoxRepresentanteAssociadoAoCliente() {
        vBoxRepresentanteAssociadoAoCliente.setName("vBoxRepresentanteAssociadoAoCliente");
        vBoxRepresentanteAssociadoAoCliente.setLeft(0);
        vBoxRepresentanteAssociadoAoCliente.setTop(6);
        vBoxRepresentanteAssociadoAoCliente.setWidth(250);
        vBoxRepresentanteAssociadoAoCliente.setHeight(50);
        vBoxRepresentanteAssociadoAoCliente.setBorderStyle("stNone");
        vBoxRepresentanteAssociadoAoCliente.setPaddingTop(0);
        vBoxRepresentanteAssociadoAoCliente.setPaddingLeft(0);
        vBoxRepresentanteAssociadoAoCliente.setPaddingRight(0);
        vBoxRepresentanteAssociadoAoCliente.setPaddingBottom(0);
        vBoxRepresentanteAssociadoAoCliente.setMarginTop(0);
        vBoxRepresentanteAssociadoAoCliente.setMarginLeft(0);
        vBoxRepresentanteAssociadoAoCliente.setMarginRight(0);
        vBoxRepresentanteAssociadoAoCliente.setMarginBottom(0);
        vBoxRepresentanteAssociadoAoCliente.setSpacing(1);
        vBoxRepresentanteAssociadoAoCliente.setFlexVflex("ftFalse");
        vBoxRepresentanteAssociadoAoCliente.setFlexHflex("ftTrue");
        vBoxRepresentanteAssociadoAoCliente.setScrollable(false);
        vBoxRepresentanteAssociadoAoCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxRepresentanteAssociadoAoCliente.setBoxShadowConfigVerticalLength(10);
        vBoxRepresentanteAssociadoAoCliente.setBoxShadowConfigBlurRadius(5);
        vBoxRepresentanteAssociadoAoCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxRepresentanteAssociadoAoCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxRepresentanteAssociadoAoCliente.setBoxShadowConfigOpacity(75);
        FVBox5.addChildren(vBoxRepresentanteAssociadoAoCliente);
        vBoxRepresentanteAssociadoAoCliente.applyProperties();
    }

    public TFLabel FLabel16 = new TFLabel();

    private void init_FLabel16() {
        FLabel16.setName("FLabel16");
        FLabel16.setLeft(0);
        FLabel16.setTop(0);
        FLabel16.setWidth(202);
        FLabel16.setHeight(13);
        FLabel16.setHint("Representante associado ao cliente");
        FLabel16.setCaption("Representante associado ao cliente");
        FLabel16.setFontColor("clWindowText");
        FLabel16.setFontSize(-11);
        FLabel16.setFontName("Tahoma");
        FLabel16.setFontStyle("[fsBold]");
        FLabel16.setVerticalAlignment("taVerticalCenter");
        FLabel16.setWordBreak(false);
        vBoxRepresentanteAssociadoAoCliente.addChildren(FLabel16);
        FLabel16.applyProperties();
    }

    public TFCombo cboRepresentante = new TFCombo();

    private void init_cboRepresentante() {
        cboRepresentante.setName("cboRepresentante");
        cboRepresentante.setLeft(0);
        cboRepresentante.setTop(14);
        cboRepresentante.setWidth(240);
        cboRepresentante.setHeight(22);
        cboRepresentante.setHint("Representante associado ao cliente");
        cboRepresentante.setTable(tbClienteDiversoFlag);
        cboRepresentante.setLookupTable(tbBuscaRepresentanteCliente);
        cboRepresentante.setFieldName("REPRESENTANTE");
        cboRepresentante.setLookupKey("NOME");
        cboRepresentante.setLookupDesc("NOME_LOGIN");
        cboRepresentante.setFlex(true);
        cboRepresentante.setHelpCaption("Representante associado ao cliente");
        cboRepresentante.setReadOnly(false);
        cboRepresentante.setRequired(false);
        cboRepresentante.setPrompt("Representante associado ao cliente");
        cboRepresentante.setConstraintCheckWhen("cwImmediate");
        cboRepresentante.setConstraintCheckType("ctExpression");
        cboRepresentante.setConstraintFocusOnError(false);
        cboRepresentante.setConstraintEnableUI(true);
        cboRepresentante.setConstraintEnabled(false);
        cboRepresentante.setConstraintFormCheck(true);
        cboRepresentante.setClearOnDelKey(true);
        cboRepresentante.setUseClearButton(true);
        cboRepresentante.setHideClearButtonOnNullValue(false);
        cboRepresentante.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboRepresentanteChange(event);
            processarFlow("FrmClientesFlags", "cboRepresentante", "OnChange");
        });
        cboRepresentante.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboRepresentanteClearClick(event);
            processarFlow("FrmClientesFlags", "cboRepresentante", "OnClearClick");
        });
        vBoxRepresentanteAssociadoAoCliente.addChildren(cboRepresentante);
        cboRepresentante.applyProperties();
        addValidatable(cboRepresentante);
    }

    public TFHBox FHBox46 = new TFHBox();

    private void init_FHBox46() {
        FHBox46.setName("FHBox46");
        FHBox46.setLeft(0);
        FHBox46.setTop(57);
        FHBox46.setWidth(61);
        FHBox46.setHeight(5);
        FHBox46.setBorderStyle("stNone");
        FHBox46.setPaddingTop(0);
        FHBox46.setPaddingLeft(0);
        FHBox46.setPaddingRight(0);
        FHBox46.setPaddingBottom(0);
        FHBox46.setMarginTop(0);
        FHBox46.setMarginLeft(0);
        FHBox46.setMarginRight(0);
        FHBox46.setMarginBottom(0);
        FHBox46.setSpacing(1);
        FHBox46.setFlexVflex("ftFalse");
        FHBox46.setFlexHflex("ftFalse");
        FHBox46.setScrollable(false);
        FHBox46.setBoxShadowConfigHorizontalLength(10);
        FHBox46.setBoxShadowConfigVerticalLength(10);
        FHBox46.setBoxShadowConfigBlurRadius(5);
        FHBox46.setBoxShadowConfigSpreadRadius(0);
        FHBox46.setBoxShadowConfigShadowColor("clBlack");
        FHBox46.setBoxShadowConfigOpacity(75);
        FHBox46.setVAlign("tvTop");
        FVBox5.addChildren(FHBox46);
        FHBox46.applyProperties();
    }

    public TFVBox vBoxVendedorAssociadoAoCliente = new TFVBox();

    private void init_vBoxVendedorAssociadoAoCliente() {
        vBoxVendedorAssociadoAoCliente.setName("vBoxVendedorAssociadoAoCliente");
        vBoxVendedorAssociadoAoCliente.setLeft(0);
        vBoxVendedorAssociadoAoCliente.setTop(63);
        vBoxVendedorAssociadoAoCliente.setWidth(250);
        vBoxVendedorAssociadoAoCliente.setHeight(50);
        vBoxVendedorAssociadoAoCliente.setBorderStyle("stNone");
        vBoxVendedorAssociadoAoCliente.setPaddingTop(0);
        vBoxVendedorAssociadoAoCliente.setPaddingLeft(0);
        vBoxVendedorAssociadoAoCliente.setPaddingRight(0);
        vBoxVendedorAssociadoAoCliente.setPaddingBottom(0);
        vBoxVendedorAssociadoAoCliente.setMarginTop(0);
        vBoxVendedorAssociadoAoCliente.setMarginLeft(0);
        vBoxVendedorAssociadoAoCliente.setMarginRight(0);
        vBoxVendedorAssociadoAoCliente.setMarginBottom(0);
        vBoxVendedorAssociadoAoCliente.setSpacing(1);
        vBoxVendedorAssociadoAoCliente.setFlexVflex("ftFalse");
        vBoxVendedorAssociadoAoCliente.setFlexHflex("ftTrue");
        vBoxVendedorAssociadoAoCliente.setScrollable(false);
        vBoxVendedorAssociadoAoCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxVendedorAssociadoAoCliente.setBoxShadowConfigVerticalLength(10);
        vBoxVendedorAssociadoAoCliente.setBoxShadowConfigBlurRadius(5);
        vBoxVendedorAssociadoAoCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxVendedorAssociadoAoCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendedorAssociadoAoCliente.setBoxShadowConfigOpacity(75);
        FVBox5.addChildren(vBoxVendedorAssociadoAoCliente);
        vBoxVendedorAssociadoAoCliente.applyProperties();
    }

    public TFLabel FLabel15 = new TFLabel();

    private void init_FLabel15() {
        FLabel15.setName("FLabel15");
        FLabel15.setLeft(0);
        FLabel15.setTop(0);
        FLabel15.setWidth(171);
        FLabel15.setHeight(13);
        FLabel15.setHint("Vendedor associado ao cliente");
        FLabel15.setCaption("Vendedor associado ao cliente");
        FLabel15.setFontColor("clWindowText");
        FLabel15.setFontSize(-11);
        FLabel15.setFontName("Tahoma");
        FLabel15.setFontStyle("[fsBold]");
        FLabel15.setVerticalAlignment("taVerticalCenter");
        FLabel15.setWordBreak(false);
        vBoxVendedorAssociadoAoCliente.addChildren(FLabel15);
        FLabel15.applyProperties();
    }

    public TFCombo cboVendedor = new TFCombo();

    private void init_cboVendedor() {
        cboVendedor.setName("cboVendedor");
        cboVendedor.setLeft(0);
        cboVendedor.setTop(14);
        cboVendedor.setWidth(240);
        cboVendedor.setHeight(22);
        cboVendedor.setHint("Vendedor associado ao cliente");
        cboVendedor.setTable(tbClienteDiversoFlag);
        cboVendedor.setLookupTable(tbVendedorRespFlag);
        cboVendedor.setFieldName("VENDEDOR_RESPONSAVEL");
        cboVendedor.setLookupKey("NOME");
        cboVendedor.setLookupDesc("NOME_COMPLETO");
        cboVendedor.setFlex(true);
        cboVendedor.setHelpCaption("Vendedor associado ao cliente");
        cboVendedor.setReadOnly(false);
        cboVendedor.setRequired(false);
        cboVendedor.setPrompt("Vendedor associado ao cliente");
        cboVendedor.setConstraintCheckWhen("cwImmediate");
        cboVendedor.setConstraintCheckType("ctExpression");
        cboVendedor.setConstraintFocusOnError(false);
        cboVendedor.setConstraintEnableUI(true);
        cboVendedor.setConstraintEnabled(false);
        cboVendedor.setConstraintFormCheck(true);
        cboVendedor.setClearOnDelKey(false);
        cboVendedor.setUseClearButton(true);
        cboVendedor.setHideClearButtonOnNullValue(false);
        cboVendedor.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboVendedorChange(event);
            processarFlow("FrmClientesFlags", "cboVendedor", "OnChange");
        });
        cboVendedor.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboVendedorExit(event);
            processarFlow("FrmClientesFlags", "cboVendedor", "OnExit");
        });
        cboVendedor.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboVendedorClearClick(event);
            processarFlow("FrmClientesFlags", "cboVendedor", "OnClearClick");
        });
        vBoxVendedorAssociadoAoCliente.addChildren(cboVendedor);
        cboVendedor.applyProperties();
        addValidatable(cboVendedor);
    }

    public TFHBox FHBox40 = new TFHBox();

    private void init_FHBox40() {
        FHBox40.setName("FHBox40");
        FHBox40.setLeft(0);
        FHBox40.setTop(114);
        FHBox40.setWidth(61);
        FHBox40.setHeight(5);
        FHBox40.setBorderStyle("stNone");
        FHBox40.setPaddingTop(0);
        FHBox40.setPaddingLeft(0);
        FHBox40.setPaddingRight(0);
        FHBox40.setPaddingBottom(0);
        FHBox40.setMarginTop(0);
        FHBox40.setMarginLeft(0);
        FHBox40.setMarginRight(0);
        FHBox40.setMarginBottom(0);
        FHBox40.setSpacing(1);
        FHBox40.setFlexVflex("ftFalse");
        FHBox40.setFlexHflex("ftFalse");
        FHBox40.setScrollable(false);
        FHBox40.setBoxShadowConfigHorizontalLength(10);
        FHBox40.setBoxShadowConfigVerticalLength(10);
        FHBox40.setBoxShadowConfigBlurRadius(5);
        FHBox40.setBoxShadowConfigSpreadRadius(0);
        FHBox40.setBoxShadowConfigShadowColor("clBlack");
        FHBox40.setBoxShadowConfigOpacity(75);
        FHBox40.setVAlign("tvTop");
        FVBox5.addChildren(FHBox40);
        FHBox40.applyProperties();
    }

    public TFHBox FHBox42 = new TFHBox();

    private void init_FHBox42() {
        FHBox42.setName("FHBox42");
        FHBox42.setLeft(0);
        FHBox42.setTop(141);
        FHBox42.setWidth(989);
        FHBox42.setHeight(252);
        FHBox42.setBorderStyle("stNone");
        FHBox42.setPaddingTop(0);
        FHBox42.setPaddingLeft(0);
        FHBox42.setPaddingRight(0);
        FHBox42.setPaddingBottom(0);
        FHBox42.setMarginTop(0);
        FHBox42.setMarginLeft(0);
        FHBox42.setMarginRight(0);
        FHBox42.setMarginBottom(0);
        FHBox42.setSpacing(1);
        FHBox42.setFlexVflex("ftTrue");
        FHBox42.setFlexHflex("ftFalse");
        FHBox42.setScrollable(false);
        FHBox42.setBoxShadowConfigHorizontalLength(10);
        FHBox42.setBoxShadowConfigVerticalLength(10);
        FHBox42.setBoxShadowConfigBlurRadius(5);
        FHBox42.setBoxShadowConfigSpreadRadius(0);
        FHBox42.setBoxShadowConfigShadowColor("clBlack");
        FHBox42.setBoxShadowConfigOpacity(75);
        FHBox42.setVAlign("tvTop");
        vBoxVendedor.addChildren(FHBox42);
        FHBox42.applyProperties();
    }

    public TFHBox FHBox48 = new TFHBox();

    private void init_FHBox48() {
        FHBox48.setName("FHBox48");
        FHBox48.setLeft(0);
        FHBox48.setTop(0);
        FHBox48.setWidth(5);
        FHBox48.setHeight(20);
        FHBox48.setBorderStyle("stNone");
        FHBox48.setPaddingTop(0);
        FHBox48.setPaddingLeft(0);
        FHBox48.setPaddingRight(0);
        FHBox48.setPaddingBottom(0);
        FHBox48.setMarginTop(0);
        FHBox48.setMarginLeft(0);
        FHBox48.setMarginRight(0);
        FHBox48.setMarginBottom(0);
        FHBox48.setSpacing(1);
        FHBox48.setFlexVflex("ftFalse");
        FHBox48.setFlexHflex("ftFalse");
        FHBox48.setScrollable(false);
        FHBox48.setBoxShadowConfigHorizontalLength(10);
        FHBox48.setBoxShadowConfigVerticalLength(10);
        FHBox48.setBoxShadowConfigBlurRadius(5);
        FHBox48.setBoxShadowConfigSpreadRadius(0);
        FHBox48.setBoxShadowConfigShadowColor("clBlack");
        FHBox48.setBoxShadowConfigOpacity(75);
        FHBox48.setVAlign("tvTop");
        FHBox42.addChildren(FHBox48);
        FHBox48.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(5);
        FVBox6.setTop(0);
        FVBox6.setWidth(946);
        FVBox6.setHeight(247);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftFalse");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox42.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFHBox FHBox50 = new TFHBox();

    private void init_FHBox50() {
        FHBox50.setName("FHBox50");
        FHBox50.setLeft(0);
        FHBox50.setTop(0);
        FHBox50.setWidth(655);
        FHBox50.setHeight(31);
        FHBox50.setBorderStyle("stNone");
        FHBox50.setPaddingTop(0);
        FHBox50.setPaddingLeft(0);
        FHBox50.setPaddingRight(0);
        FHBox50.setPaddingBottom(0);
        FHBox50.setMarginTop(0);
        FHBox50.setMarginLeft(0);
        FHBox50.setMarginRight(0);
        FHBox50.setMarginBottom(0);
        FHBox50.setSpacing(1);
        FHBox50.setFlexVflex("ftFalse");
        FHBox50.setFlexHflex("ftFalse");
        FHBox50.setScrollable(false);
        FHBox50.setBoxShadowConfigHorizontalLength(10);
        FHBox50.setBoxShadowConfigVerticalLength(10);
        FHBox50.setBoxShadowConfigBlurRadius(5);
        FHBox50.setBoxShadowConfigSpreadRadius(0);
        FHBox50.setBoxShadowConfigShadowColor("clBlack");
        FHBox50.setBoxShadowConfigOpacity(75);
        FHBox50.setVAlign("tvTop");
        FVBox6.addChildren(FHBox50);
        FHBox50.applyProperties();
    }

    public TFLabel lblVendedorTitulo = new TFLabel();

    private void init_lblVendedorTitulo() {
        lblVendedorTitulo.setName("lblVendedorTitulo");
        lblVendedorTitulo.setLeft(0);
        lblVendedorTitulo.setTop(0);
        lblVendedorTitulo.setWidth(249);
        lblVendedorTitulo.setHeight(13);
        lblVendedorTitulo.setCaption("Vendedor associado ao cliente por empresa ");
        lblVendedorTitulo.setFontColor("clWindowText");
        lblVendedorTitulo.setFontSize(-11);
        lblVendedorTitulo.setFontName("Tahoma");
        lblVendedorTitulo.setFontStyle("[fsBold]");
        lblVendedorTitulo.setVerticalAlignment("taVerticalCenter");
        lblVendedorTitulo.setWordBreak(false);
        FHBox50.addChildren(lblVendedorTitulo);
        lblVendedorTitulo.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(249);
        FVBox7.setTop(0);
        FVBox7.setWidth(5);
        FVBox7.setHeight(20);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftFalse");
        FVBox7.setFlexHflex("ftFalse");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FHBox50.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFButton btnVincularVendedor = new TFButton();

    private void init_btnVincularVendedor() {
        btnVincularVendedor.setName("btnVincularVendedor");
        btnVincularVendedor.setLeft(254);
        btnVincularVendedor.setTop(0);
        btnVincularVendedor.setWidth(25);
        btnVincularVendedor.setHeight(25);
        btnVincularVendedor.setHint("Vincular Vendedor\r\n\r\nAcesso \"K1030\"");
        btnVincularVendedor.setFontColor("clWindowText");
        btnVincularVendedor.setFontSize(-11);
        btnVincularVendedor.setFontName("Tahoma");
        btnVincularVendedor.setFontStyle("[]");
        btnVincularVendedor.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVincularVendedorClick(event);
            processarFlow("FrmClientesFlags", "btnVincularVendedor", "OnClick");
        });
        btnVincularVendedor.setImageId(0);
        btnVincularVendedor.setColor("clBtnFace");
        btnVincularVendedor.setAccess(false);
        btnVincularVendedor.setIconClass("external-link");
        btnVincularVendedor.setIconReverseDirection(false);
        FHBox50.addChildren(btnVincularVendedor);
        btnVincularVendedor.applyProperties();
    }

    public TFGrid grdVendedoresAssociados = new TFGrid();

    private void init_grdVendedoresAssociados() {
        grdVendedoresAssociados.setName("grdVendedoresAssociados");
        grdVendedoresAssociados.setLeft(0);
        grdVendedoresAssociados.setTop(32);
        grdVendedoresAssociados.setWidth(829);
        grdVendedoresAssociados.setHeight(194);
        grdVendedoresAssociados.setAlign("alClient");
        grdVendedoresAssociados.setTable(tbListaClienteResponsavel);
        grdVendedoresAssociados.setFlexVflex("ftTrue");
        grdVendedoresAssociados.setFlexHflex("ftTrue");
        grdVendedoresAssociados.setPagingEnabled(false);
        grdVendedoresAssociados.setFrozenColumns(0);
        grdVendedoresAssociados.setShowFooter(false);
        grdVendedoresAssociados.setShowHeader(true);
        grdVendedoresAssociados.setMultiSelection(false);
        grdVendedoresAssociados.setGroupingEnabled(false);
        grdVendedoresAssociados.setGroupingExpanded(false);
        grdVendedoresAssociados.setGroupingShowFooter(false);
        grdVendedoresAssociados.setCrosstabEnabled(false);
        grdVendedoresAssociados.setCrosstabGroupType("cgtConcat");
        grdVendedoresAssociados.setEditionEnabled(false);
        grdVendedoresAssociados.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("EMPRESAS_NOME_INITCAP_COD");
        item0.setTitleCaption("Empresa");
        item0.setWidth(200);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("COD_CLIENTE");
        item1.setTitleCaption("C\u00F3d. Cliente");
        item1.setWidth(136);
        item1.setVisible(false);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("SISTEMA");
        item2.setTitleCaption("Sistema");
        item2.setWidth(91);
        item2.setVisible(false);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("PROCESSO");
        item3.setTitleCaption("Processo");
        item3.setWidth(92);
        item3.setVisible(false);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("TEMPERATURA");
        item4.setTitleCaption("Temperatura");
        item4.setWidth(108);
        item4.setVisible(false);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("SISTEMA_STR");
        item5.setTitleCaption("Sistema");
        item5.setWidth(85);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("PROCESSO_STR");
        item6.setTitleCaption("Processo");
        item6.setWidth(132);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("TEMPERATURA_STR");
        item7.setTitleCaption("Temperatura");
        item7.setWidth(118);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("RESPONSAVEL");
        item8.setTitleCaption("Responsavel");
        item8.setWidth(86);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("VENDEDOR");
        item9.setTitleCaption("Vendedor");
        item9.setWidth(230);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(true);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        grdVendedoresAssociados.getColumns().add(item9);
        FVBox6.addChildren(grdVendedoresAssociados);
        grdVendedoresAssociados.applyProperties();
    }

    public TFHBox FHBox32 = new TFHBox();

    private void init_FHBox32() {
        FHBox32.setName("FHBox32");
        FHBox32.setLeft(0);
        FHBox32.setTop(227);
        FHBox32.setWidth(185);
        FHBox32.setHeight(17);
        FHBox32.setBorderStyle("stNone");
        FHBox32.setPaddingTop(0);
        FHBox32.setPaddingLeft(0);
        FHBox32.setPaddingRight(0);
        FHBox32.setPaddingBottom(0);
        FHBox32.setMarginTop(0);
        FHBox32.setMarginLeft(0);
        FHBox32.setMarginRight(0);
        FHBox32.setMarginBottom(0);
        FHBox32.setSpacing(1);
        FHBox32.setFlexVflex("ftFalse");
        FHBox32.setFlexHflex("ftFalse");
        FHBox32.setScrollable(false);
        FHBox32.setBoxShadowConfigHorizontalLength(10);
        FHBox32.setBoxShadowConfigVerticalLength(10);
        FHBox32.setBoxShadowConfigBlurRadius(5);
        FHBox32.setBoxShadowConfigSpreadRadius(0);
        FHBox32.setBoxShadowConfigShadowColor("clBlack");
        FHBox32.setBoxShadowConfigOpacity(75);
        FHBox32.setVAlign("tvTop");
        FVBox6.addChildren(FHBox32);
        FHBox32.applyProperties();
    }

    public TFHBox FHBox51 = new TFHBox();

    private void init_FHBox51() {
        FHBox51.setName("FHBox51");
        FHBox51.setLeft(951);
        FHBox51.setTop(0);
        FHBox51.setWidth(5);
        FHBox51.setHeight(20);
        FHBox51.setBorderStyle("stNone");
        FHBox51.setPaddingTop(0);
        FHBox51.setPaddingLeft(0);
        FHBox51.setPaddingRight(0);
        FHBox51.setPaddingBottom(0);
        FHBox51.setMarginTop(0);
        FHBox51.setMarginLeft(0);
        FHBox51.setMarginRight(0);
        FHBox51.setMarginBottom(0);
        FHBox51.setSpacing(1);
        FHBox51.setFlexVflex("ftFalse");
        FHBox51.setFlexHflex("ftFalse");
        FHBox51.setScrollable(false);
        FHBox51.setBoxShadowConfigHorizontalLength(10);
        FHBox51.setBoxShadowConfigVerticalLength(10);
        FHBox51.setBoxShadowConfigBlurRadius(5);
        FHBox51.setBoxShadowConfigSpreadRadius(0);
        FHBox51.setBoxShadowConfigShadowColor("clBlack");
        FHBox51.setBoxShadowConfigOpacity(75);
        FHBox51.setVAlign("tvTop");
        FHBox42.addChildren(FHBox51);
        FHBox51.applyProperties();
    }

    public TFTabsheet tabTransportadora = new TFTabsheet();

    private void init_tabTransportadora() {
        tabTransportadora.setName("tabTransportadora");
        tabTransportadora.setCaption("Transportadora");
        tabTransportadora.setFontColor("clWindowText");
        tabTransportadora.setFontSize(-12);
        tabTransportadora.setFontName("Tahoma");
        tabTransportadora.setFontStyle("[]");
        tabTransportadora.setVisible(true);
        tabTransportadora.setClosable(false);
        pgFlagsDiverso.addChildren(tabTransportadora);
        tabTransportadora.applyProperties();
    }

    public TFVBox vBoxTransportadora = new TFVBox();

    private void init_vBoxTransportadora() {
        vBoxTransportadora.setName("vBoxTransportadora");
        vBoxTransportadora.setLeft(0);
        vBoxTransportadora.setTop(0);
        vBoxTransportadora.setWidth(998);
        vBoxTransportadora.setHeight(401);
        vBoxTransportadora.setAlign("alClient");
        vBoxTransportadora.setBorderStyle("stNone");
        vBoxTransportadora.setPaddingTop(0);
        vBoxTransportadora.setPaddingLeft(0);
        vBoxTransportadora.setPaddingRight(0);
        vBoxTransportadora.setPaddingBottom(0);
        vBoxTransportadora.setMarginTop(0);
        vBoxTransportadora.setMarginLeft(0);
        vBoxTransportadora.setMarginRight(0);
        vBoxTransportadora.setMarginBottom(0);
        vBoxTransportadora.setSpacing(1);
        vBoxTransportadora.setFlexVflex("ftTrue");
        vBoxTransportadora.setFlexHflex("ftTrue");
        vBoxTransportadora.setScrollable(false);
        vBoxTransportadora.setBoxShadowConfigHorizontalLength(10);
        vBoxTransportadora.setBoxShadowConfigVerticalLength(10);
        vBoxTransportadora.setBoxShadowConfigBlurRadius(5);
        vBoxTransportadora.setBoxShadowConfigSpreadRadius(0);
        vBoxTransportadora.setBoxShadowConfigShadowColor("clBlack");
        vBoxTransportadora.setBoxShadowConfigOpacity(75);
        tabTransportadora.addChildren(vBoxTransportadora);
        vBoxTransportadora.applyProperties();
    }

    public TFHBox hBoxVendaLinha02 = new TFHBox();

    private void init_hBoxVendaLinha02() {
        hBoxVendaLinha02.setName("hBoxVendaLinha02");
        hBoxVendaLinha02.setLeft(0);
        hBoxVendaLinha02.setTop(0);
        hBoxVendaLinha02.setWidth(989);
        hBoxVendaLinha02.setHeight(378);
        hBoxVendaLinha02.setBorderStyle("stNone");
        hBoxVendaLinha02.setPaddingTop(0);
        hBoxVendaLinha02.setPaddingLeft(0);
        hBoxVendaLinha02.setPaddingRight(0);
        hBoxVendaLinha02.setPaddingBottom(0);
        hBoxVendaLinha02.setMarginTop(0);
        hBoxVendaLinha02.setMarginLeft(0);
        hBoxVendaLinha02.setMarginRight(0);
        hBoxVendaLinha02.setMarginBottom(0);
        hBoxVendaLinha02.setSpacing(1);
        hBoxVendaLinha02.setFlexVflex("ftTrue");
        hBoxVendaLinha02.setFlexHflex("ftFalse");
        hBoxVendaLinha02.setScrollable(false);
        hBoxVendaLinha02.setBoxShadowConfigHorizontalLength(10);
        hBoxVendaLinha02.setBoxShadowConfigVerticalLength(10);
        hBoxVendaLinha02.setBoxShadowConfigBlurRadius(5);
        hBoxVendaLinha02.setBoxShadowConfigSpreadRadius(0);
        hBoxVendaLinha02.setBoxShadowConfigShadowColor("clBlack");
        hBoxVendaLinha02.setBoxShadowConfigOpacity(75);
        hBoxVendaLinha02.setVAlign("tvTop");
        vBoxTransportadora.addChildren(hBoxVendaLinha02);
        hBoxVendaLinha02.applyProperties();
    }

    public TFHBox FHBox45 = new TFHBox();

    private void init_FHBox45() {
        FHBox45.setName("FHBox45");
        FHBox45.setLeft(0);
        FHBox45.setTop(0);
        FHBox45.setWidth(5);
        FHBox45.setHeight(20);
        FHBox45.setBorderStyle("stNone");
        FHBox45.setPaddingTop(0);
        FHBox45.setPaddingLeft(0);
        FHBox45.setPaddingRight(0);
        FHBox45.setPaddingBottom(0);
        FHBox45.setMarginTop(0);
        FHBox45.setMarginLeft(0);
        FHBox45.setMarginRight(0);
        FHBox45.setMarginBottom(0);
        FHBox45.setSpacing(1);
        FHBox45.setFlexVflex("ftFalse");
        FHBox45.setFlexHflex("ftFalse");
        FHBox45.setScrollable(false);
        FHBox45.setBoxShadowConfigHorizontalLength(10);
        FHBox45.setBoxShadowConfigVerticalLength(10);
        FHBox45.setBoxShadowConfigBlurRadius(5);
        FHBox45.setBoxShadowConfigSpreadRadius(0);
        FHBox45.setBoxShadowConfigShadowColor("clBlack");
        FHBox45.setBoxShadowConfigOpacity(75);
        FHBox45.setVAlign("tvTop");
        hBoxVendaLinha02.addChildren(FHBox45);
        FHBox45.applyProperties();
    }

    public TFVBox vBoxVendaTransportadora = new TFVBox();

    private void init_vBoxVendaTransportadora() {
        vBoxVendaTransportadora.setName("vBoxVendaTransportadora");
        vBoxVendaTransportadora.setLeft(5);
        vBoxVendaTransportadora.setTop(0);
        vBoxVendaTransportadora.setWidth(946);
        vBoxVendaTransportadora.setHeight(370);
        vBoxVendaTransportadora.setBorderStyle("stNone");
        vBoxVendaTransportadora.setPaddingTop(0);
        vBoxVendaTransportadora.setPaddingLeft(0);
        vBoxVendaTransportadora.setPaddingRight(0);
        vBoxVendaTransportadora.setPaddingBottom(0);
        vBoxVendaTransportadora.setMarginTop(0);
        vBoxVendaTransportadora.setMarginLeft(0);
        vBoxVendaTransportadora.setMarginRight(0);
        vBoxVendaTransportadora.setMarginBottom(0);
        vBoxVendaTransportadora.setSpacing(1);
        vBoxVendaTransportadora.setFlexVflex("ftTrue");
        vBoxVendaTransportadora.setFlexHflex("ftFalse");
        vBoxVendaTransportadora.setScrollable(false);
        vBoxVendaTransportadora.setBoxShadowConfigHorizontalLength(10);
        vBoxVendaTransportadora.setBoxShadowConfigVerticalLength(10);
        vBoxVendaTransportadora.setBoxShadowConfigBlurRadius(5);
        vBoxVendaTransportadora.setBoxShadowConfigSpreadRadius(0);
        vBoxVendaTransportadora.setBoxShadowConfigShadowColor("clBlack");
        vBoxVendaTransportadora.setBoxShadowConfigOpacity(75);
        hBoxVendaLinha02.addChildren(vBoxVendaTransportadora);
        vBoxVendaTransportadora.applyProperties();
    }

    public TFHBox hBoxTransportadoraLinha01 = new TFHBox();

    private void init_hBoxTransportadoraLinha01() {
        hBoxTransportadoraLinha01.setName("hBoxTransportadoraLinha01");
        hBoxTransportadoraLinha01.setLeft(0);
        hBoxTransportadoraLinha01.setTop(0);
        hBoxTransportadoraLinha01.setWidth(655);
        hBoxTransportadoraLinha01.setHeight(31);
        hBoxTransportadoraLinha01.setBorderStyle("stNone");
        hBoxTransportadoraLinha01.setPaddingTop(0);
        hBoxTransportadoraLinha01.setPaddingLeft(0);
        hBoxTransportadoraLinha01.setPaddingRight(0);
        hBoxTransportadoraLinha01.setPaddingBottom(0);
        hBoxTransportadoraLinha01.setMarginTop(0);
        hBoxTransportadoraLinha01.setMarginLeft(0);
        hBoxTransportadoraLinha01.setMarginRight(0);
        hBoxTransportadoraLinha01.setMarginBottom(0);
        hBoxTransportadoraLinha01.setSpacing(1);
        hBoxTransportadoraLinha01.setFlexVflex("ftFalse");
        hBoxTransportadoraLinha01.setFlexHflex("ftFalse");
        hBoxTransportadoraLinha01.setScrollable(false);
        hBoxTransportadoraLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxTransportadoraLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxTransportadoraLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxTransportadoraLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxTransportadoraLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxTransportadoraLinha01.setBoxShadowConfigOpacity(75);
        hBoxTransportadoraLinha01.setVAlign("tvTop");
        vBoxVendaTransportadora.addChildren(hBoxTransportadoraLinha01);
        hBoxTransportadoraLinha01.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(0);
        FLabel7.setTop(0);
        FLabel7.setWidth(89);
        FLabel7.setHeight(13);
        FLabel7.setCaption("Transportadora");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[fsBold]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        hBoxTransportadoraLinha01.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(89);
        FVBox4.setTop(0);
        FVBox4.setWidth(5);
        FVBox4.setHeight(20);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hBoxTransportadoraLinha01.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFButton btnIncluirTransportadora = new TFButton();

    private void init_btnIncluirTransportadora() {
        btnIncluirTransportadora.setName("btnIncluirTransportadora");
        btnIncluirTransportadora.setLeft(94);
        btnIncluirTransportadora.setTop(0);
        btnIncluirTransportadora.setWidth(25);
        btnIncluirTransportadora.setHeight(25);
        btnIncluirTransportadora.setHint("Incluir transportadora");
        btnIncluirTransportadora.setFontColor("clWindowText");
        btnIncluirTransportadora.setFontSize(-11);
        btnIncluirTransportadora.setFontName("Tahoma");
        btnIncluirTransportadora.setFontStyle("[]");
        btnIncluirTransportadora.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnIncluirTransportadoraClick(event);
            processarFlow("FrmClientesFlags", "btnIncluirTransportadora", "OnClick");
        });
        btnIncluirTransportadora.setImageId(7000128);
        btnIncluirTransportadora.setColor("clBtnFace");
        btnIncluirTransportadora.setAccess(false);
        btnIncluirTransportadora.setIconReverseDirection(false);
        hBoxTransportadoraLinha01.addChildren(btnIncluirTransportadora);
        btnIncluirTransportadora.applyProperties();
    }

    public TFGrid grdClienteTransp = new TFGrid();

    private void init_grdClienteTransp() {
        grdClienteTransp.setName("grdClienteTransp");
        grdClienteTransp.setLeft(0);
        grdClienteTransp.setTop(32);
        grdClienteTransp.setWidth(760);
        grdClienteTransp.setHeight(288);
        grdClienteTransp.setHint("Transportadora");
        grdClienteTransp.setTable(tbClienteTransportadoraFlag);
        grdClienteTransp.setFlexVflex("ftTrue");
        grdClienteTransp.setFlexHflex("ftFalse");
        grdClienteTransp.setPagingEnabled(false);
        grdClienteTransp.setFrozenColumns(0);
        grdClienteTransp.setShowFooter(false);
        grdClienteTransp.setShowHeader(true);
        grdClienteTransp.setMultiSelection(false);
        grdClienteTransp.setGroupingEnabled(false);
        grdClienteTransp.setGroupingExpanded(false);
        grdClienteTransp.setGroupingShowFooter(false);
        grdClienteTransp.setCrosstabEnabled(false);
        grdClienteTransp.setCrosstabGroupType("cgtConcat");
        grdClienteTransp.setEditionEnabled(true);
        grdClienteTransp.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption(" ");
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("*");
        item1.setHint("Excluir transportadora do cliente");
        item1.setEvalType("etExpression");
        item1.setImageId(700095);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdClienteTranspexcluirTransportadora(event);
            processarFlow("FrmClientesFlags", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdClienteTransp.getColumns().add(item0);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("DESCRICAO_COD_TRANSPORTADORA");
        item2.setTitleCaption("Nome transportadora");
        item2.setWidth(406);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdClienteTransp.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("CPF_CGC");
        item3.setTitleCaption("CNPJ/CPF");
        item3.setWidth(193);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdClienteTransp.getColumns().add(item3);
        vBoxVendaTransportadora.addChildren(grdClienteTransp);
        grdClienteTransp.applyProperties();
    }

    public TFHBox FHBox49 = new TFHBox();

    private void init_FHBox49() {
        FHBox49.setName("FHBox49");
        FHBox49.setLeft(0);
        FHBox49.setTop(321);
        FHBox49.setWidth(185);
        FHBox49.setHeight(17);
        FHBox49.setBorderStyle("stNone");
        FHBox49.setPaddingTop(0);
        FHBox49.setPaddingLeft(0);
        FHBox49.setPaddingRight(0);
        FHBox49.setPaddingBottom(0);
        FHBox49.setMarginTop(0);
        FHBox49.setMarginLeft(0);
        FHBox49.setMarginRight(0);
        FHBox49.setMarginBottom(0);
        FHBox49.setSpacing(1);
        FHBox49.setFlexVflex("ftFalse");
        FHBox49.setFlexHflex("ftFalse");
        FHBox49.setScrollable(false);
        FHBox49.setBoxShadowConfigHorizontalLength(10);
        FHBox49.setBoxShadowConfigVerticalLength(10);
        FHBox49.setBoxShadowConfigBlurRadius(5);
        FHBox49.setBoxShadowConfigSpreadRadius(0);
        FHBox49.setBoxShadowConfigShadowColor("clBlack");
        FHBox49.setBoxShadowConfigOpacity(75);
        FHBox49.setVAlign("tvTop");
        vBoxVendaTransportadora.addChildren(FHBox49);
        FHBox49.applyProperties();
    }

    public TFHBox FHBox47 = new TFHBox();

    private void init_FHBox47() {
        FHBox47.setName("FHBox47");
        FHBox47.setLeft(951);
        FHBox47.setTop(0);
        FHBox47.setWidth(5);
        FHBox47.setHeight(20);
        FHBox47.setBorderStyle("stNone");
        FHBox47.setPaddingTop(0);
        FHBox47.setPaddingLeft(0);
        FHBox47.setPaddingRight(0);
        FHBox47.setPaddingBottom(0);
        FHBox47.setMarginTop(0);
        FHBox47.setMarginLeft(0);
        FHBox47.setMarginRight(0);
        FHBox47.setMarginBottom(0);
        FHBox47.setSpacing(1);
        FHBox47.setFlexVflex("ftFalse");
        FHBox47.setFlexHflex("ftFalse");
        FHBox47.setScrollable(false);
        FHBox47.setBoxShadowConfigHorizontalLength(10);
        FHBox47.setBoxShadowConfigVerticalLength(10);
        FHBox47.setBoxShadowConfigBlurRadius(5);
        FHBox47.setBoxShadowConfigSpreadRadius(0);
        FHBox47.setBoxShadowConfigShadowColor("clBlack");
        FHBox47.setBoxShadowConfigOpacity(75);
        FHBox47.setVAlign("tvTop");
        hBoxVendaLinha02.addChildren(FHBox47);
        FHBox47.applyProperties();
    }

    public TFTabsheet tabFinanceiro = new TFTabsheet();

    private void init_tabFinanceiro() {
        tabFinanceiro.setName("tabFinanceiro");
        tabFinanceiro.setCaption("Financeiro");
        tabFinanceiro.setVisible(true);
        tabFinanceiro.setClosable(false);
        pgFlags.addChildren(tabFinanceiro);
        tabFinanceiro.applyProperties();
    }

    public TFVBox vBoxFinanceiro = new TFVBox();

    private void init_vBoxFinanceiro() {
        vBoxFinanceiro.setName("vBoxFinanceiro");
        vBoxFinanceiro.setLeft(0);
        vBoxFinanceiro.setTop(0);
        vBoxFinanceiro.setWidth(1012);
        vBoxFinanceiro.setHeight(537);
        vBoxFinanceiro.setAlign("alClient");
        vBoxFinanceiro.setBorderStyle("stNone");
        vBoxFinanceiro.setPaddingTop(0);
        vBoxFinanceiro.setPaddingLeft(3);
        vBoxFinanceiro.setPaddingRight(3);
        vBoxFinanceiro.setPaddingBottom(10);
        vBoxFinanceiro.setMarginTop(0);
        vBoxFinanceiro.setMarginLeft(0);
        vBoxFinanceiro.setMarginRight(0);
        vBoxFinanceiro.setMarginBottom(0);
        vBoxFinanceiro.setSpacing(1);
        vBoxFinanceiro.setFlexVflex("ftTrue");
        vBoxFinanceiro.setFlexHflex("ftTrue");
        vBoxFinanceiro.setScrollable(false);
        vBoxFinanceiro.setBoxShadowConfigHorizontalLength(10);
        vBoxFinanceiro.setBoxShadowConfigVerticalLength(10);
        vBoxFinanceiro.setBoxShadowConfigBlurRadius(5);
        vBoxFinanceiro.setBoxShadowConfigSpreadRadius(0);
        vBoxFinanceiro.setBoxShadowConfigShadowColor("clBlack");
        vBoxFinanceiro.setBoxShadowConfigOpacity(75);
        tabFinanceiro.addChildren(vBoxFinanceiro);
        vBoxFinanceiro.applyProperties();
    }

    public TFPageControl pgFinanceiro = new TFPageControl();

    private void init_pgFinanceiro() {
        pgFinanceiro.setName("pgFinanceiro");
        pgFinanceiro.setLeft(0);
        pgFinanceiro.setTop(0);
        pgFinanceiro.setWidth(1000);
        pgFinanceiro.setHeight(520);
        pgFinanceiro.setAlign("alClient");
        pgFinanceiro.setTabPosition("tpTop");
        pgFinanceiro.setFlexVflex("ftTrue");
        pgFinanceiro.setFlexHflex("ftTrue");
        pgFinanceiro.setRenderStyle("rsTabbed");
        pgFinanceiro.applyProperties();
        vBoxFinanceiro.addChildren(pgFinanceiro);
    }

    public TFTabsheet tabFormaCondicaoPagamento = new TFTabsheet();

    private void init_tabFormaCondicaoPagamento() {
        tabFormaCondicaoPagamento.setName("tabFormaCondicaoPagamento");
        tabFormaCondicaoPagamento.setCaption("Forma/Condi\u00E7\u00E3o de pagamento");
        tabFormaCondicaoPagamento.setVisible(true);
        tabFormaCondicaoPagamento.setClosable(false);
        pgFinanceiro.addChildren(tabFormaCondicaoPagamento);
        tabFormaCondicaoPagamento.applyProperties();
    }

    public TFVBox vBoxFormaCondicaoPagamento = new TFVBox();

    private void init_vBoxFormaCondicaoPagamento() {
        vBoxFormaCondicaoPagamento.setName("vBoxFormaCondicaoPagamento");
        vBoxFormaCondicaoPagamento.setLeft(0);
        vBoxFormaCondicaoPagamento.setTop(0);
        vBoxFormaCondicaoPagamento.setWidth(992);
        vBoxFormaCondicaoPagamento.setHeight(492);
        vBoxFormaCondicaoPagamento.setAlign("alClient");
        vBoxFormaCondicaoPagamento.setBorderStyle("stNone");
        vBoxFormaCondicaoPagamento.setPaddingTop(0);
        vBoxFormaCondicaoPagamento.setPaddingLeft(0);
        vBoxFormaCondicaoPagamento.setPaddingRight(0);
        vBoxFormaCondicaoPagamento.setPaddingBottom(0);
        vBoxFormaCondicaoPagamento.setMarginTop(0);
        vBoxFormaCondicaoPagamento.setMarginLeft(0);
        vBoxFormaCondicaoPagamento.setMarginRight(0);
        vBoxFormaCondicaoPagamento.setMarginBottom(0);
        vBoxFormaCondicaoPagamento.setSpacing(1);
        vBoxFormaCondicaoPagamento.setFlexVflex("ftTrue");
        vBoxFormaCondicaoPagamento.setFlexHflex("ftTrue");
        vBoxFormaCondicaoPagamento.setScrollable(false);
        vBoxFormaCondicaoPagamento.setBoxShadowConfigHorizontalLength(10);
        vBoxFormaCondicaoPagamento.setBoxShadowConfigVerticalLength(10);
        vBoxFormaCondicaoPagamento.setBoxShadowConfigBlurRadius(5);
        vBoxFormaCondicaoPagamento.setBoxShadowConfigSpreadRadius(0);
        vBoxFormaCondicaoPagamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxFormaCondicaoPagamento.setBoxShadowConfigOpacity(75);
        tabFormaCondicaoPagamento.addChildren(vBoxFormaCondicaoPagamento);
        vBoxFormaCondicaoPagamento.applyProperties();
    }

    public TFHBox FHBox29 = new TFHBox();

    private void init_FHBox29() {
        FHBox29.setName("FHBox29");
        FHBox29.setLeft(0);
        FHBox29.setTop(0);
        FHBox29.setWidth(980);
        FHBox29.setHeight(5);
        FHBox29.setBorderStyle("stNone");
        FHBox29.setPaddingTop(0);
        FHBox29.setPaddingLeft(0);
        FHBox29.setPaddingRight(0);
        FHBox29.setPaddingBottom(0);
        FHBox29.setMarginTop(0);
        FHBox29.setMarginLeft(0);
        FHBox29.setMarginRight(0);
        FHBox29.setMarginBottom(0);
        FHBox29.setSpacing(1);
        FHBox29.setFlexVflex("ftFalse");
        FHBox29.setFlexHflex("ftFalse");
        FHBox29.setScrollable(false);
        FHBox29.setBoxShadowConfigHorizontalLength(10);
        FHBox29.setBoxShadowConfigVerticalLength(10);
        FHBox29.setBoxShadowConfigBlurRadius(5);
        FHBox29.setBoxShadowConfigSpreadRadius(0);
        FHBox29.setBoxShadowConfigShadowColor("clBlack");
        FHBox29.setBoxShadowConfigOpacity(75);
        FHBox29.setVAlign("tvTop");
        vBoxFormaCondicaoPagamento.addChildren(FHBox29);
        FHBox29.applyProperties();
    }

    public TFHBox hBoxFaturamentoLivreLinha01 = new TFHBox();

    private void init_hBoxFaturamentoLivreLinha01() {
        hBoxFaturamentoLivreLinha01.setName("hBoxFaturamentoLivreLinha01");
        hBoxFaturamentoLivreLinha01.setLeft(0);
        hBoxFaturamentoLivreLinha01.setTop(6);
        hBoxFaturamentoLivreLinha01.setWidth(980);
        hBoxFaturamentoLivreLinha01.setHeight(470);
        hBoxFaturamentoLivreLinha01.setBorderStyle("stNone");
        hBoxFaturamentoLivreLinha01.setPaddingTop(0);
        hBoxFaturamentoLivreLinha01.setPaddingLeft(0);
        hBoxFaturamentoLivreLinha01.setPaddingRight(0);
        hBoxFaturamentoLivreLinha01.setPaddingBottom(0);
        hBoxFaturamentoLivreLinha01.setMarginTop(0);
        hBoxFaturamentoLivreLinha01.setMarginLeft(0);
        hBoxFaturamentoLivreLinha01.setMarginRight(0);
        hBoxFaturamentoLivreLinha01.setMarginBottom(0);
        hBoxFaturamentoLivreLinha01.setSpacing(1);
        hBoxFaturamentoLivreLinha01.setFlexVflex("ftTrue");
        hBoxFaturamentoLivreLinha01.setFlexHflex("ftTrue");
        hBoxFaturamentoLivreLinha01.setScrollable(false);
        hBoxFaturamentoLivreLinha01.setBoxShadowConfigHorizontalLength(10);
        hBoxFaturamentoLivreLinha01.setBoxShadowConfigVerticalLength(10);
        hBoxFaturamentoLivreLinha01.setBoxShadowConfigBlurRadius(5);
        hBoxFaturamentoLivreLinha01.setBoxShadowConfigSpreadRadius(0);
        hBoxFaturamentoLivreLinha01.setBoxShadowConfigShadowColor("clBlack");
        hBoxFaturamentoLivreLinha01.setBoxShadowConfigOpacity(75);
        hBoxFaturamentoLivreLinha01.setVAlign("tvTop");
        vBoxFormaCondicaoPagamento.addChildren(hBoxFaturamentoLivreLinha01);
        hBoxFaturamentoLivreLinha01.applyProperties();
    }

    public TFHBox FHBox52 = new TFHBox();

    private void init_FHBox52() {
        FHBox52.setName("FHBox52");
        FHBox52.setLeft(0);
        FHBox52.setTop(0);
        FHBox52.setWidth(5);
        FHBox52.setHeight(20);
        FHBox52.setBorderStyle("stNone");
        FHBox52.setPaddingTop(0);
        FHBox52.setPaddingLeft(0);
        FHBox52.setPaddingRight(0);
        FHBox52.setPaddingBottom(0);
        FHBox52.setMarginTop(0);
        FHBox52.setMarginLeft(0);
        FHBox52.setMarginRight(0);
        FHBox52.setMarginBottom(0);
        FHBox52.setSpacing(1);
        FHBox52.setFlexVflex("ftFalse");
        FHBox52.setFlexHflex("ftFalse");
        FHBox52.setScrollable(false);
        FHBox52.setBoxShadowConfigHorizontalLength(10);
        FHBox52.setBoxShadowConfigVerticalLength(10);
        FHBox52.setBoxShadowConfigBlurRadius(5);
        FHBox52.setBoxShadowConfigSpreadRadius(0);
        FHBox52.setBoxShadowConfigShadowColor("clBlack");
        FHBox52.setBoxShadowConfigOpacity(75);
        FHBox52.setVAlign("tvTop");
        hBoxFaturamentoLivreLinha01.addChildren(FHBox52);
        FHBox52.applyProperties();
    }

    public TFVBox vBoxFinanceiroLinha01Coluna02 = new TFVBox();

    private void init_vBoxFinanceiroLinha01Coluna02() {
        vBoxFinanceiroLinha01Coluna02.setName("vBoxFinanceiroLinha01Coluna02");
        vBoxFinanceiroLinha01Coluna02.setLeft(5);
        vBoxFinanceiroLinha01Coluna02.setTop(0);
        vBoxFinanceiroLinha01Coluna02.setWidth(960);
        vBoxFinanceiroLinha01Coluna02.setHeight(460);
        vBoxFinanceiroLinha01Coluna02.setBorderStyle("stNone");
        vBoxFinanceiroLinha01Coluna02.setPaddingTop(0);
        vBoxFinanceiroLinha01Coluna02.setPaddingLeft(4);
        vBoxFinanceiroLinha01Coluna02.setPaddingRight(4);
        vBoxFinanceiroLinha01Coluna02.setPaddingBottom(0);
        vBoxFinanceiroLinha01Coluna02.setMarginTop(0);
        vBoxFinanceiroLinha01Coluna02.setMarginLeft(0);
        vBoxFinanceiroLinha01Coluna02.setMarginRight(0);
        vBoxFinanceiroLinha01Coluna02.setMarginBottom(0);
        vBoxFinanceiroLinha01Coluna02.setSpacing(1);
        vBoxFinanceiroLinha01Coluna02.setFlexVflex("ftTrue");
        vBoxFinanceiroLinha01Coluna02.setFlexHflex("ftTrue");
        vBoxFinanceiroLinha01Coluna02.setScrollable(false);
        vBoxFinanceiroLinha01Coluna02.setBoxShadowConfigHorizontalLength(10);
        vBoxFinanceiroLinha01Coluna02.setBoxShadowConfigVerticalLength(10);
        vBoxFinanceiroLinha01Coluna02.setBoxShadowConfigBlurRadius(5);
        vBoxFinanceiroLinha01Coluna02.setBoxShadowConfigSpreadRadius(0);
        vBoxFinanceiroLinha01Coluna02.setBoxShadowConfigShadowColor("clBlack");
        vBoxFinanceiroLinha01Coluna02.setBoxShadowConfigOpacity(75);
        hBoxFaturamentoLivreLinha01.addChildren(vBoxFinanceiroLinha01Coluna02);
        vBoxFinanceiroLinha01Coluna02.applyProperties();
    }

    public TFVBox vBoxFormaCobranca = new TFVBox();

    private void init_vBoxFormaCobranca() {
        vBoxFormaCobranca.setName("vBoxFormaCobranca");
        vBoxFormaCobranca.setLeft(0);
        vBoxFormaCobranca.setTop(0);
        vBoxFormaCobranca.setWidth(950);
        vBoxFormaCobranca.setHeight(50);
        vBoxFormaCobranca.setBorderStyle("stNone");
        vBoxFormaCobranca.setPaddingTop(0);
        vBoxFormaCobranca.setPaddingLeft(0);
        vBoxFormaCobranca.setPaddingRight(0);
        vBoxFormaCobranca.setPaddingBottom(0);
        vBoxFormaCobranca.setMarginTop(0);
        vBoxFormaCobranca.setMarginLeft(0);
        vBoxFormaCobranca.setMarginRight(0);
        vBoxFormaCobranca.setMarginBottom(0);
        vBoxFormaCobranca.setSpacing(1);
        vBoxFormaCobranca.setFlexVflex("ftMin");
        vBoxFormaCobranca.setFlexHflex("ftTrue");
        vBoxFormaCobranca.setScrollable(false);
        vBoxFormaCobranca.setBoxShadowConfigHorizontalLength(10);
        vBoxFormaCobranca.setBoxShadowConfigVerticalLength(10);
        vBoxFormaCobranca.setBoxShadowConfigBlurRadius(5);
        vBoxFormaCobranca.setBoxShadowConfigSpreadRadius(0);
        vBoxFormaCobranca.setBoxShadowConfigShadowColor("clBlack");
        vBoxFormaCobranca.setBoxShadowConfigOpacity(75);
        vBoxFinanceiroLinha01Coluna02.addChildren(vBoxFormaCobranca);
        vBoxFormaCobranca.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(0);
        FLabel6.setWidth(114);
        FLabel6.setHeight(13);
        FLabel6.setCaption("  Forma de cobran\u00E7a");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[fsBold]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        vBoxFormaCobranca.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFCombo cboFormaCob = new TFCombo();

    private void init_cboFormaCob() {
        cboFormaCob.setName("cboFormaCob");
        cboFormaCob.setLeft(0);
        cboFormaCob.setTop(14);
        cboFormaCob.setWidth(940);
        cboFormaCob.setHeight(21);
        cboFormaCob.setHint("Forma de cobran\u00E7a");
        cboFormaCob.setTable(tbClienteDiversoFlag);
        cboFormaCob.setLookupTable(tbFormaCobrancaFlag);
        cboFormaCob.setFieldName("CHAVE_COB");
        cboFormaCob.setLookupKey("CHAVE_COB");
        cboFormaCob.setLookupDesc("DESCRICAO");
        cboFormaCob.setFlex(true);
        cboFormaCob.setHelpCaption("Forma de cobran\u00E7a");
        cboFormaCob.setReadOnly(false);
        cboFormaCob.setRequired(false);
        cboFormaCob.setPrompt("Forma de cobran\u00E7a");
        cboFormaCob.setConstraintCheckWhen("cwImmediate");
        cboFormaCob.setConstraintCheckType("ctExpression");
        cboFormaCob.setConstraintFocusOnError(false);
        cboFormaCob.setConstraintEnableUI(true);
        cboFormaCob.setConstraintEnabled(false);
        cboFormaCob.setConstraintFormCheck(true);
        cboFormaCob.setClearOnDelKey(false);
        cboFormaCob.setUseClearButton(true);
        cboFormaCob.setHideClearButtonOnNullValue(false);
        cboFormaCob.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaCobChange(event);
            processarFlow("FrmClientesFlags", "cboFormaCob", "OnChange");
        });
        cboFormaCob.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaCobExit(event);
            processarFlow("FrmClientesFlags", "cboFormaCob", "OnExit");
        });
        cboFormaCob.addEventListener("onClearClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cboFormaCobClearClick(event);
            processarFlow("FrmClientesFlags", "cboFormaCob", "OnClearClick");
        });
        vBoxFormaCobranca.addChildren(cboFormaCob);
        cboFormaCob.applyProperties();
        addValidatable(cboFormaCob);
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(51);
        FHBox4.setWidth(950);
        FHBox4.setHeight(5);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        vBoxFinanceiroLinha01Coluna02.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFVBox vBoxFormaPgto = new TFVBox();

    private void init_vBoxFormaPgto() {
        vBoxFormaPgto.setName("vBoxFormaPgto");
        vBoxFormaPgto.setLeft(0);
        vBoxFormaPgto.setTop(57);
        vBoxFormaPgto.setWidth(950);
        vBoxFormaPgto.setHeight(160);
        vBoxFormaPgto.setBorderStyle("stNone");
        vBoxFormaPgto.setPaddingTop(0);
        vBoxFormaPgto.setPaddingLeft(0);
        vBoxFormaPgto.setPaddingRight(0);
        vBoxFormaPgto.setPaddingBottom(0);
        vBoxFormaPgto.setMarginTop(0);
        vBoxFormaPgto.setMarginLeft(0);
        vBoxFormaPgto.setMarginRight(0);
        vBoxFormaPgto.setMarginBottom(0);
        vBoxFormaPgto.setSpacing(1);
        vBoxFormaPgto.setFlexVflex("ftFalse");
        vBoxFormaPgto.setFlexHflex("ftTrue");
        vBoxFormaPgto.setScrollable(false);
        vBoxFormaPgto.setBoxShadowConfigHorizontalLength(10);
        vBoxFormaPgto.setBoxShadowConfigVerticalLength(10);
        vBoxFormaPgto.setBoxShadowConfigBlurRadius(5);
        vBoxFormaPgto.setBoxShadowConfigSpreadRadius(0);
        vBoxFormaPgto.setBoxShadowConfigShadowColor("clBlack");
        vBoxFormaPgto.setBoxShadowConfigOpacity(75);
        vBoxFinanceiroLinha01Coluna02.addChildren(vBoxFormaPgto);
        vBoxFormaPgto.applyProperties();
    }

    public TFHBox hBoxCaptionFormaPgto = new TFHBox();

    private void init_hBoxCaptionFormaPgto() {
        hBoxCaptionFormaPgto.setName("hBoxCaptionFormaPgto");
        hBoxCaptionFormaPgto.setLeft(0);
        hBoxCaptionFormaPgto.setTop(0);
        hBoxCaptionFormaPgto.setWidth(940);
        hBoxCaptionFormaPgto.setHeight(25);
        hBoxCaptionFormaPgto.setBorderStyle("stNone");
        hBoxCaptionFormaPgto.setPaddingTop(0);
        hBoxCaptionFormaPgto.setPaddingLeft(0);
        hBoxCaptionFormaPgto.setPaddingRight(0);
        hBoxCaptionFormaPgto.setPaddingBottom(0);
        hBoxCaptionFormaPgto.setMarginTop(0);
        hBoxCaptionFormaPgto.setMarginLeft(0);
        hBoxCaptionFormaPgto.setMarginRight(0);
        hBoxCaptionFormaPgto.setMarginBottom(0);
        hBoxCaptionFormaPgto.setSpacing(1);
        hBoxCaptionFormaPgto.setFlexVflex("ftFalse");
        hBoxCaptionFormaPgto.setFlexHflex("ftFalse");
        hBoxCaptionFormaPgto.setScrollable(false);
        hBoxCaptionFormaPgto.setBoxShadowConfigHorizontalLength(10);
        hBoxCaptionFormaPgto.setBoxShadowConfigVerticalLength(10);
        hBoxCaptionFormaPgto.setBoxShadowConfigBlurRadius(5);
        hBoxCaptionFormaPgto.setBoxShadowConfigSpreadRadius(0);
        hBoxCaptionFormaPgto.setBoxShadowConfigShadowColor("clBlack");
        hBoxCaptionFormaPgto.setBoxShadowConfigOpacity(75);
        hBoxCaptionFormaPgto.setVAlign("tvTop");
        vBoxFormaPgto.addChildren(hBoxCaptionFormaPgto);
        hBoxCaptionFormaPgto.applyProperties();
    }

    public TFLabel lblFormasDePagamento = new TFLabel();

    private void init_lblFormasDePagamento() {
        lblFormasDePagamento.setName("lblFormasDePagamento");
        lblFormasDePagamento.setLeft(0);
        lblFormasDePagamento.setTop(0);
        lblFormasDePagamento.setWidth(133);
        lblFormasDePagamento.setHeight(13);
        lblFormasDePagamento.setAlign("alLeft");
        lblFormasDePagamento.setCaption("  Formas de pagamento");
        lblFormasDePagamento.setFontColor("clWindowText");
        lblFormasDePagamento.setFontSize(-11);
        lblFormasDePagamento.setFontName("Tahoma");
        lblFormasDePagamento.setFontStyle("[fsBold]");
        lblFormasDePagamento.setVerticalAlignment("taAlignBottom");
        lblFormasDePagamento.setWordBreak(false);
        hBoxCaptionFormaPgto.addChildren(lblFormasDePagamento);
        lblFormasDePagamento.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(133);
        FVBox1.setTop(0);
        FVBox1.setWidth(5);
        FVBox1.setHeight(17);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        hBoxCaptionFormaPgto.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFButton btnAddFormaPgto = new TFButton();

    private void init_btnAddFormaPgto() {
        btnAddFormaPgto.setName("btnAddFormaPgto");
        btnAddFormaPgto.setLeft(138);
        btnAddFormaPgto.setTop(0);
        btnAddFormaPgto.setWidth(22);
        btnAddFormaPgto.setHeight(22);
        btnAddFormaPgto.setHint("Incluir forma de pagameto");
        btnAddFormaPgto.setFontColor("clWindowText");
        btnAddFormaPgto.setFontSize(-11);
        btnAddFormaPgto.setFontName("Tahoma");
        btnAddFormaPgto.setFontStyle("[]");
        btnAddFormaPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAddFormaPgtoClick(event);
            processarFlow("FrmClientesFlags", "btnAddFormaPgto", "OnClick");
        });
        btnAddFormaPgto.setImageId(7000128);
        btnAddFormaPgto.setColor("clBtnFace");
        btnAddFormaPgto.setAccess(false);
        btnAddFormaPgto.setIconReverseDirection(false);
        hBoxCaptionFormaPgto.addChildren(btnAddFormaPgto);
        btnAddFormaPgto.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(26);
        FHBox12.setWidth(940);
        FHBox12.setHeight(5);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftFalse");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        vBoxFormaPgto.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFGrid grdFormasDePagamento = new TFGrid();

    private void init_grdFormasDePagamento() {
        grdFormasDePagamento.setName("grdFormasDePagamento");
        grdFormasDePagamento.setLeft(0);
        grdFormasDePagamento.setTop(32);
        grdFormasDePagamento.setWidth(940);
        grdFormasDePagamento.setHeight(120);
        grdFormasDePagamento.setHint("Formas de pagamento");
        grdFormasDePagamento.setTable(tbListaClienteFormasPagamento);
        grdFormasDePagamento.setFlexVflex("ftTrue");
        grdFormasDePagamento.setFlexHflex("ftTrue");
        grdFormasDePagamento.setPagingEnabled(false);
        grdFormasDePagamento.setFrozenColumns(0);
        grdFormasDePagamento.setShowFooter(false);
        grdFormasDePagamento.setShowHeader(true);
        grdFormasDePagamento.setMultiSelection(false);
        grdFormasDePagamento.setGroupingEnabled(false);
        grdFormasDePagamento.setGroupingExpanded(false);
        grdFormasDePagamento.setGroupingShowFooter(false);
        grdFormasDePagamento.setCrosstabEnabled(false);
        grdFormasDePagamento.setCrosstabGroupType("cgtConcat");
        grdFormasDePagamento.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdFormasDePagamentoDoubleClick(event);
            processarFlow("FrmClientesFlags", "grdFormasDePagamento", "OnDoubleClick");
        });
        grdFormasDePagamento.setEditionEnabled(false);
        grdFormasDePagamento.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(30);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("FORMA_PGTO_CODIGO IS NULL");
        item1.setHint("Incluir forma de pagamento");
        item1.setEvalType("etExpression");
        item1.setImageId(7000128);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("FORMA_PGTO_CODIGO > 0");
        item2.setHint("Excluir forma de pagamento");
        item2.setEvalType("etExpression");
        item2.setImageId(700095);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdFormasDePagamentoexcluirFormaDePagamento(event);
            processarFlow("FrmClientesFlags", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setHint("Selecionar");
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdFormasDePagamento.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("EMPRESA_NOME_CODIGO");
        item3.setTitleCaption("Empresa");
        item3.setWidth(200);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setHint("Empresa");
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdFormasDePagamento.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DEPARTAMENTO_DESCRICAO_CODIGO");
        item4.setTitleCaption("Departamento");
        item4.setWidth(200);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setHint("Departamento");
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdFormasDePagamento.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("FORMA_PGTO_DESCRICAO_CODIGO");
        item5.setTitleCaption("Forma de pagamento");
        item5.setWidth(200);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setHint("Forma de pagamento");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdFormasDePagamento.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("FORMA_PGTO_EXCLUSIVA");
        item6.setTitleCaption("Forma exclusiva");
        item6.setWidth(130);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(true);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        TFImageExpression item7 = new TFImageExpression();
        item7.setExpression("FORMA_PGTO_EXCLUSIVA = 'S'");
        item7.setHint("Sim");
        item7.setEvalType("etExpression");
        item7.setImageId(4300107);
        item6.getImages().add(item7);
        TFImageExpression item8 = new TFImageExpression();
        item8.setExpression("FORMA_PGTO_EXCLUSIVA = 'N'");
        item8.setHint("N\u00E3o");
        item8.setEvalType("etExpression");
        item8.setImageId(4300106);
        item6.getImages().add(item8);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(false);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setHint("Forma exclusiva");
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdFormasDePagamento.getColumns().add(item6);
        vBoxFormaPgto.addChildren(grdFormasDePagamento);
        grdFormasDePagamento.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(218);
        FHBox7.setWidth(950);
        FHBox7.setHeight(5);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        vBoxFinanceiroLinha01Coluna02.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFVBox vBoxCondicaoPagamento = new TFVBox();

    private void init_vBoxCondicaoPagamento() {
        vBoxCondicaoPagamento.setName("vBoxCondicaoPagamento");
        vBoxCondicaoPagamento.setLeft(0);
        vBoxCondicaoPagamento.setTop(224);
        vBoxCondicaoPagamento.setWidth(950);
        vBoxCondicaoPagamento.setHeight(210);
        vBoxCondicaoPagamento.setBorderStyle("stNone");
        vBoxCondicaoPagamento.setPaddingTop(0);
        vBoxCondicaoPagamento.setPaddingLeft(0);
        vBoxCondicaoPagamento.setPaddingRight(0);
        vBoxCondicaoPagamento.setPaddingBottom(0);
        vBoxCondicaoPagamento.setMarginTop(0);
        vBoxCondicaoPagamento.setMarginLeft(0);
        vBoxCondicaoPagamento.setMarginRight(0);
        vBoxCondicaoPagamento.setMarginBottom(0);
        vBoxCondicaoPagamento.setSpacing(1);
        vBoxCondicaoPagamento.setFlexVflex("ftTrue");
        vBoxCondicaoPagamento.setFlexHflex("ftTrue");
        vBoxCondicaoPagamento.setScrollable(false);
        vBoxCondicaoPagamento.setBoxShadowConfigHorizontalLength(10);
        vBoxCondicaoPagamento.setBoxShadowConfigVerticalLength(10);
        vBoxCondicaoPagamento.setBoxShadowConfigBlurRadius(5);
        vBoxCondicaoPagamento.setBoxShadowConfigSpreadRadius(0);
        vBoxCondicaoPagamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxCondicaoPagamento.setBoxShadowConfigOpacity(75);
        vBoxFinanceiroLinha01Coluna02.addChildren(vBoxCondicaoPagamento);
        vBoxCondicaoPagamento.applyProperties();
    }

    public TFHBox hBoxCaptionCondPgto = new TFHBox();

    private void init_hBoxCaptionCondPgto() {
        hBoxCaptionCondPgto.setName("hBoxCaptionCondPgto");
        hBoxCaptionCondPgto.setLeft(0);
        hBoxCaptionCondPgto.setTop(0);
        hBoxCaptionCondPgto.setWidth(940);
        hBoxCaptionCondPgto.setHeight(27);
        hBoxCaptionCondPgto.setBorderStyle("stNone");
        hBoxCaptionCondPgto.setPaddingTop(0);
        hBoxCaptionCondPgto.setPaddingLeft(0);
        hBoxCaptionCondPgto.setPaddingRight(0);
        hBoxCaptionCondPgto.setPaddingBottom(0);
        hBoxCaptionCondPgto.setMarginTop(0);
        hBoxCaptionCondPgto.setMarginLeft(0);
        hBoxCaptionCondPgto.setMarginRight(0);
        hBoxCaptionCondPgto.setMarginBottom(0);
        hBoxCaptionCondPgto.setSpacing(1);
        hBoxCaptionCondPgto.setFlexVflex("ftFalse");
        hBoxCaptionCondPgto.setFlexHflex("ftFalse");
        hBoxCaptionCondPgto.setScrollable(false);
        hBoxCaptionCondPgto.setBoxShadowConfigHorizontalLength(10);
        hBoxCaptionCondPgto.setBoxShadowConfigVerticalLength(10);
        hBoxCaptionCondPgto.setBoxShadowConfigBlurRadius(5);
        hBoxCaptionCondPgto.setBoxShadowConfigSpreadRadius(0);
        hBoxCaptionCondPgto.setBoxShadowConfigShadowColor("clBlack");
        hBoxCaptionCondPgto.setBoxShadowConfigOpacity(75);
        hBoxCaptionCondPgto.setVAlign("tvTop");
        vBoxCondicaoPagamento.addChildren(hBoxCaptionCondPgto);
        hBoxCaptionCondPgto.applyProperties();
    }

    public TFLabel lblCondicoesPgto = new TFLabel();

    private void init_lblCondicoesPgto() {
        lblCondicoesPgto.setName("lblCondicoesPgto");
        lblCondicoesPgto.setLeft(0);
        lblCondicoesPgto.setTop(0);
        lblCondicoesPgto.setWidth(148);
        lblCondicoesPgto.setHeight(13);
        lblCondicoesPgto.setCaption("  Condi\u00E7\u00F5es de pagamento");
        lblCondicoesPgto.setFontColor("clWindowText");
        lblCondicoesPgto.setFontSize(-11);
        lblCondicoesPgto.setFontName("Tahoma");
        lblCondicoesPgto.setFontStyle("[fsBold]");
        lblCondicoesPgto.setVerticalAlignment("taAlignBottom");
        lblCondicoesPgto.setWordBreak(false);
        hBoxCaptionCondPgto.addChildren(lblCondicoesPgto);
        lblCondicoesPgto.applyProperties();
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(148);
        FVBox3.setTop(0);
        FVBox3.setWidth(5);
        FVBox3.setHeight(23);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        hBoxCaptionCondPgto.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFButton btnAddCondicaoPgto = new TFButton();

    private void init_btnAddCondicaoPgto() {
        btnAddCondicaoPgto.setName("btnAddCondicaoPgto");
        btnAddCondicaoPgto.setLeft(153);
        btnAddCondicaoPgto.setTop(0);
        btnAddCondicaoPgto.setWidth(22);
        btnAddCondicaoPgto.setHeight(22);
        btnAddCondicaoPgto.setHint("Incluir condi\u00E7\u00E3o de pagameto");
        btnAddCondicaoPgto.setFontColor("clWindowText");
        btnAddCondicaoPgto.setFontSize(-11);
        btnAddCondicaoPgto.setFontName("Tahoma");
        btnAddCondicaoPgto.setFontStyle("[]");
        btnAddCondicaoPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAddCondicaoPgtoClick(event);
            processarFlow("FrmClientesFlags", "btnAddCondicaoPgto", "OnClick");
        });
        btnAddCondicaoPgto.setImageId(7000128);
        btnAddCondicaoPgto.setColor("clBtnFace");
        btnAddCondicaoPgto.setAccess(false);
        btnAddCondicaoPgto.setIconReverseDirection(false);
        hBoxCaptionCondPgto.addChildren(btnAddCondicaoPgto);
        btnAddCondicaoPgto.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(28);
        FHBox14.setWidth(940);
        FHBox14.setHeight(5);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(1);
        FHBox14.setFlexVflex("ftFalse");
        FHBox14.setFlexHflex("ftFalse");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        vBoxCondicaoPagamento.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFGrid grdCondPgto = new TFGrid();

    private void init_grdCondPgto() {
        grdCondPgto.setName("grdCondPgto");
        grdCondPgto.setLeft(0);
        grdCondPgto.setTop(34);
        grdCondPgto.setWidth(940);
        grdCondPgto.setHeight(160);
        grdCondPgto.setHint("Condi\u00E7\u00F5es de pagamento");
        grdCondPgto.setTable(tbClienteFormaPgtoFlag);
        grdCondPgto.setFlexVflex("ftTrue");
        grdCondPgto.setFlexHflex("ftTrue");
        grdCondPgto.setPagingEnabled(false);
        grdCondPgto.setFrozenColumns(0);
        grdCondPgto.setShowFooter(false);
        grdCondPgto.setShowHeader(true);
        grdCondPgto.setMultiSelection(false);
        grdCondPgto.setGroupingEnabled(false);
        grdCondPgto.setGroupingExpanded(false);
        grdCondPgto.setGroupingShowFooter(false);
        grdCondPgto.setCrosstabEnabled(false);
        grdCondPgto.setCrosstabGroupType("cgtConcat");
        grdCondPgto.addEventListener("onDoubleClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdCondPgtoDoubleClick(event);
            processarFlow("FrmClientesFlags", "grdCondPgto", "OnDoubleClick");
        });
        grdCondPgto.setEditionEnabled(false);
        grdCondPgto.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setWidth(30);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("COD_FORMA_PGTO IS NULL");
        item1.setHint("Incluir condi\u00E7\u00E3o de pagamento");
        item1.setEvalType("etExpression");
        item1.setImageId(7000128);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("COD_FORMA_PGTO > 0");
        item2.setHint("Excluir condi\u00E7\u00E3o de pagamento");
        item2.setEvalType("etExpression");
        item2.setImageId(700095);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdCondPgtoexcluirCondicaoDePagamento(event);
            processarFlow("FrmClientesFlags", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setHint("Selecionar");
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdCondPgto.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("NOME_CODIGO_EMPRESA");
        item3.setTitleCaption("Empresa");
        item3.setWidth(200);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(true);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setHint("Empresa");
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdCondPgto.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("NOME_CODIGO_DEPARTAMENTO");
        item4.setTitleCaption("Departamento");
        item4.setWidth(200);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setHint("Departamento");
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdCondPgto.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("DESCRICAO_CODIGO_FORMA_PGTO");
        item5.setTitleCaption("Forma de pagamento");
        item5.setWidth(200);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setHint("Forma de pagamento");
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        grdCondPgto.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("DESCRICAO_CODIGO_CONDICAO_PGTO");
        item6.setTitleCaption("Condi\u00E7\u00E3o de pagamento");
        item6.setWidth(200);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(true);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setHint("Condi\u00E7\u00E3o de pagamento");
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        grdCondPgto.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("CONDICAO_PGTO_EXCLUSIVA");
        item7.setTitleCaption("Condi\u00E7\u00E3o exclusiva");
        item7.setWidth(140);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(true);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        TFImageExpression item8 = new TFImageExpression();
        item8.setExpression("CONDICAO_PGTO_EXCLUSIVA = 'S'");
        item8.setHint("Sim");
        item8.setEvalType("etExpression");
        item8.setImageId(4300107);
        item7.getImages().add(item8);
        TFImageExpression item9 = new TFImageExpression();
        item9.setExpression("CONDICAO_PGTO_EXCLUSIVA = 'N'");
        item9.setHint("N\u00E3o");
        item9.setEvalType("etExpression");
        item9.setImageId(4300106);
        item7.getImages().add(item9);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(false);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setHint("Condi\u00E7\u00E3o exclusiva");
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        grdCondPgto.getColumns().add(item7);
        vBoxCondicaoPagamento.addChildren(grdCondPgto);
        grdCondPgto.applyProperties();
    }

    public TFHBox FHBox33 = new TFHBox();

    private void init_FHBox33() {
        FHBox33.setName("FHBox33");
        FHBox33.setLeft(0);
        FHBox33.setTop(435);
        FHBox33.setWidth(950);
        FHBox33.setHeight(5);
        FHBox33.setBorderStyle("stNone");
        FHBox33.setPaddingTop(0);
        FHBox33.setPaddingLeft(0);
        FHBox33.setPaddingRight(0);
        FHBox33.setPaddingBottom(0);
        FHBox33.setMarginTop(0);
        FHBox33.setMarginLeft(0);
        FHBox33.setMarginRight(0);
        FHBox33.setMarginBottom(0);
        FHBox33.setSpacing(1);
        FHBox33.setFlexVflex("ftFalse");
        FHBox33.setFlexHflex("ftFalse");
        FHBox33.setScrollable(false);
        FHBox33.setBoxShadowConfigHorizontalLength(10);
        FHBox33.setBoxShadowConfigVerticalLength(10);
        FHBox33.setBoxShadowConfigBlurRadius(5);
        FHBox33.setBoxShadowConfigSpreadRadius(0);
        FHBox33.setBoxShadowConfigShadowColor("clBlack");
        FHBox33.setBoxShadowConfigOpacity(75);
        FHBox33.setVAlign("tvTop");
        vBoxFinanceiroLinha01Coluna02.addChildren(FHBox33);
        FHBox33.applyProperties();
    }

    public TFHBox FHBox31 = new TFHBox();

    private void init_FHBox31() {
        FHBox31.setName("FHBox31");
        FHBox31.setLeft(965);
        FHBox31.setTop(0);
        FHBox31.setWidth(5);
        FHBox31.setHeight(20);
        FHBox31.setBorderStyle("stNone");
        FHBox31.setPaddingTop(0);
        FHBox31.setPaddingLeft(0);
        FHBox31.setPaddingRight(0);
        FHBox31.setPaddingBottom(0);
        FHBox31.setMarginTop(0);
        FHBox31.setMarginLeft(0);
        FHBox31.setMarginRight(0);
        FHBox31.setMarginBottom(0);
        FHBox31.setSpacing(1);
        FHBox31.setFlexVflex("ftFalse");
        FHBox31.setFlexHflex("ftFalse");
        FHBox31.setScrollable(false);
        FHBox31.setBoxShadowConfigHorizontalLength(10);
        FHBox31.setBoxShadowConfigVerticalLength(10);
        FHBox31.setBoxShadowConfigBlurRadius(5);
        FHBox31.setBoxShadowConfigSpreadRadius(0);
        FHBox31.setBoxShadowConfigShadowColor("clBlack");
        FHBox31.setBoxShadowConfigOpacity(75);
        FHBox31.setVAlign("tvTop");
        hBoxFaturamentoLivreLinha01.addChildren(FHBox31);
        FHBox31.applyProperties();
    }

    public TFTabsheet tabFaturamentoLivre = new TFTabsheet();

    private void init_tabFaturamentoLivre() {
        tabFaturamentoLivre.setName("tabFaturamentoLivre");
        tabFaturamentoLivre.setCaption("Faturamento livre");
        tabFaturamentoLivre.setVisible(true);
        tabFaturamentoLivre.setClosable(false);
        pgFinanceiro.addChildren(tabFaturamentoLivre);
        tabFaturamentoLivre.applyProperties();
    }

    public TFVBox vBoxFaturamentoLivre = new TFVBox();

    private void init_vBoxFaturamentoLivre() {
        vBoxFaturamentoLivre.setName("vBoxFaturamentoLivre");
        vBoxFaturamentoLivre.setLeft(0);
        vBoxFaturamentoLivre.setTop(0);
        vBoxFaturamentoLivre.setWidth(992);
        vBoxFaturamentoLivre.setHeight(492);
        vBoxFaturamentoLivre.setAlign("alClient");
        vBoxFaturamentoLivre.setBorderStyle("stNone");
        vBoxFaturamentoLivre.setPaddingTop(0);
        vBoxFaturamentoLivre.setPaddingLeft(0);
        vBoxFaturamentoLivre.setPaddingRight(0);
        vBoxFaturamentoLivre.setPaddingBottom(0);
        vBoxFaturamentoLivre.setMarginTop(0);
        vBoxFaturamentoLivre.setMarginLeft(0);
        vBoxFaturamentoLivre.setMarginRight(0);
        vBoxFaturamentoLivre.setMarginBottom(0);
        vBoxFaturamentoLivre.setSpacing(1);
        vBoxFaturamentoLivre.setFlexVflex("ftTrue");
        vBoxFaturamentoLivre.setFlexHflex("ftTrue");
        vBoxFaturamentoLivre.setScrollable(false);
        vBoxFaturamentoLivre.setBoxShadowConfigHorizontalLength(10);
        vBoxFaturamentoLivre.setBoxShadowConfigVerticalLength(10);
        vBoxFaturamentoLivre.setBoxShadowConfigBlurRadius(5);
        vBoxFaturamentoLivre.setBoxShadowConfigSpreadRadius(0);
        vBoxFaturamentoLivre.setBoxShadowConfigShadowColor("clBlack");
        vBoxFaturamentoLivre.setBoxShadowConfigOpacity(75);
        tabFaturamentoLivre.addChildren(vBoxFaturamentoLivre);
        vBoxFaturamentoLivre.applyProperties();
    }

    public TFHBox FHBox16 = new TFHBox();

    private void init_FHBox16() {
        FHBox16.setName("FHBox16");
        FHBox16.setLeft(0);
        FHBox16.setTop(0);
        FHBox16.setWidth(980);
        FHBox16.setHeight(5);
        FHBox16.setBorderStyle("stNone");
        FHBox16.setPaddingTop(0);
        FHBox16.setPaddingLeft(0);
        FHBox16.setPaddingRight(0);
        FHBox16.setPaddingBottom(0);
        FHBox16.setMarginTop(0);
        FHBox16.setMarginLeft(0);
        FHBox16.setMarginRight(0);
        FHBox16.setMarginBottom(0);
        FHBox16.setSpacing(1);
        FHBox16.setFlexVflex("ftFalse");
        FHBox16.setFlexHflex("ftFalse");
        FHBox16.setScrollable(false);
        FHBox16.setBoxShadowConfigHorizontalLength(10);
        FHBox16.setBoxShadowConfigVerticalLength(10);
        FHBox16.setBoxShadowConfigBlurRadius(5);
        FHBox16.setBoxShadowConfigSpreadRadius(0);
        FHBox16.setBoxShadowConfigShadowColor("clBlack");
        FHBox16.setBoxShadowConfigOpacity(75);
        FHBox16.setVAlign("tvTop");
        vBoxFaturamentoLivre.addChildren(FHBox16);
        FHBox16.applyProperties();
    }

    public TFHBox hBoxFaturamentoLivreLinha001 = new TFHBox();

    private void init_hBoxFaturamentoLivreLinha001() {
        hBoxFaturamentoLivreLinha001.setName("hBoxFaturamentoLivreLinha001");
        hBoxFaturamentoLivreLinha001.setLeft(0);
        hBoxFaturamentoLivreLinha001.setTop(6);
        hBoxFaturamentoLivreLinha001.setWidth(980);
        hBoxFaturamentoLivreLinha001.setHeight(470);
        hBoxFaturamentoLivreLinha001.setBorderStyle("stNone");
        hBoxFaturamentoLivreLinha001.setPaddingTop(0);
        hBoxFaturamentoLivreLinha001.setPaddingLeft(0);
        hBoxFaturamentoLivreLinha001.setPaddingRight(0);
        hBoxFaturamentoLivreLinha001.setPaddingBottom(0);
        hBoxFaturamentoLivreLinha001.setMarginTop(0);
        hBoxFaturamentoLivreLinha001.setMarginLeft(0);
        hBoxFaturamentoLivreLinha001.setMarginRight(0);
        hBoxFaturamentoLivreLinha001.setMarginBottom(0);
        hBoxFaturamentoLivreLinha001.setSpacing(1);
        hBoxFaturamentoLivreLinha001.setFlexVflex("ftTrue");
        hBoxFaturamentoLivreLinha001.setFlexHflex("ftTrue");
        hBoxFaturamentoLivreLinha001.setScrollable(false);
        hBoxFaturamentoLivreLinha001.setBoxShadowConfigHorizontalLength(10);
        hBoxFaturamentoLivreLinha001.setBoxShadowConfigVerticalLength(10);
        hBoxFaturamentoLivreLinha001.setBoxShadowConfigBlurRadius(5);
        hBoxFaturamentoLivreLinha001.setBoxShadowConfigSpreadRadius(0);
        hBoxFaturamentoLivreLinha001.setBoxShadowConfigShadowColor("clBlack");
        hBoxFaturamentoLivreLinha001.setBoxShadowConfigOpacity(75);
        hBoxFaturamentoLivreLinha001.setVAlign("tvTop");
        vBoxFaturamentoLivre.addChildren(hBoxFaturamentoLivreLinha001);
        hBoxFaturamentoLivreLinha001.applyProperties();
    }

    public TFHBox FHBox21 = new TFHBox();

    private void init_FHBox21() {
        FHBox21.setName("FHBox21");
        FHBox21.setLeft(0);
        FHBox21.setTop(0);
        FHBox21.setWidth(5);
        FHBox21.setHeight(20);
        FHBox21.setBorderStyle("stNone");
        FHBox21.setPaddingTop(0);
        FHBox21.setPaddingLeft(0);
        FHBox21.setPaddingRight(0);
        FHBox21.setPaddingBottom(0);
        FHBox21.setMarginTop(0);
        FHBox21.setMarginLeft(0);
        FHBox21.setMarginRight(0);
        FHBox21.setMarginBottom(0);
        FHBox21.setSpacing(1);
        FHBox21.setFlexVflex("ftFalse");
        FHBox21.setFlexHflex("ftFalse");
        FHBox21.setScrollable(false);
        FHBox21.setBoxShadowConfigHorizontalLength(10);
        FHBox21.setBoxShadowConfigVerticalLength(10);
        FHBox21.setBoxShadowConfigBlurRadius(5);
        FHBox21.setBoxShadowConfigSpreadRadius(0);
        FHBox21.setBoxShadowConfigShadowColor("clBlack");
        FHBox21.setBoxShadowConfigOpacity(75);
        FHBox21.setVAlign("tvTop");
        hBoxFaturamentoLivreLinha001.addChildren(FHBox21);
        FHBox21.applyProperties();
    }

    public TFVBox vBoxFaturamentoLivre02 = new TFVBox();

    private void init_vBoxFaturamentoLivre02() {
        vBoxFaturamentoLivre02.setName("vBoxFaturamentoLivre02");
        vBoxFaturamentoLivre02.setLeft(5);
        vBoxFaturamentoLivre02.setTop(0);
        vBoxFaturamentoLivre02.setWidth(960);
        vBoxFaturamentoLivre02.setHeight(460);
        vBoxFaturamentoLivre02.setBorderStyle("stNone");
        vBoxFaturamentoLivre02.setPaddingTop(0);
        vBoxFaturamentoLivre02.setPaddingLeft(0);
        vBoxFaturamentoLivre02.setPaddingRight(0);
        vBoxFaturamentoLivre02.setPaddingBottom(0);
        vBoxFaturamentoLivre02.setMarginTop(0);
        vBoxFaturamentoLivre02.setMarginLeft(0);
        vBoxFaturamentoLivre02.setMarginRight(0);
        vBoxFaturamentoLivre02.setMarginBottom(0);
        vBoxFaturamentoLivre02.setSpacing(1);
        vBoxFaturamentoLivre02.setFlexVflex("ftTrue");
        vBoxFaturamentoLivre02.setFlexHflex("ftTrue");
        vBoxFaturamentoLivre02.setScrollable(false);
        vBoxFaturamentoLivre02.setBoxShadowConfigHorizontalLength(10);
        vBoxFaturamentoLivre02.setBoxShadowConfigVerticalLength(10);
        vBoxFaturamentoLivre02.setBoxShadowConfigBlurRadius(5);
        vBoxFaturamentoLivre02.setBoxShadowConfigSpreadRadius(0);
        vBoxFaturamentoLivre02.setBoxShadowConfigShadowColor("clBlack");
        vBoxFaturamentoLivre02.setBoxShadowConfigOpacity(75);
        hBoxFaturamentoLivreLinha001.addChildren(vBoxFaturamentoLivre02);
        vBoxFaturamentoLivre02.applyProperties();
    }

    public TFCheckBox chkFatLivre = new TFCheckBox();

    private void init_chkFatLivre() {
        chkFatLivre.setName("chkFatLivre");
        chkFatLivre.setLeft(0);
        chkFatLivre.setTop(0);
        chkFatLivre.setWidth(257);
        chkFatLivre.setHeight(17);
        chkFatLivre.setHint("Faturamento livre (todas empresas)");
        chkFatLivre.setCaption("Faturamento livre (todas empresas)");
        chkFatLivre.setFontColor("clWindowText");
        chkFatLivre.setFontSize(-11);
        chkFatLivre.setFontName("Tahoma");
        chkFatLivre.setFontStyle("[fsBold]");
        chkFatLivre.setTable(tbClienteDiversoFlag);
        chkFatLivre.setFieldName("FATURAMENTO_LIVRE");
        chkFatLivre.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkFatLivreCheck(event);
            processarFlow("FrmClientesFlags", "chkFatLivre", "OnCheck");
        });
        chkFatLivre.setHelpCaption("Faturamento livre (todas empresas)");
        chkFatLivre.setVerticalAlignment("taAlignTop");
        vBoxFaturamentoLivre02.addChildren(chkFatLivre);
        chkFatLivre.applyProperties();
    }

    public TFGrid grdClienteFatLivre = new TFGrid();

    private void init_grdClienteFatLivre() {
        grdClienteFatLivre.setName("grdClienteFatLivre");
        grdClienteFatLivre.setLeft(0);
        grdClienteFatLivre.setTop(18);
        grdClienteFatLivre.setWidth(950);
        grdClienteFatLivre.setHeight(430);
        grdClienteFatLivre.setHint("Faturamento livre (todas empresas)");
        grdClienteFatLivre.setTable(tbClientesParamEmpFlag);
        grdClienteFatLivre.setFlexVflex("ftTrue");
        grdClienteFatLivre.setFlexHflex("ftTrue");
        grdClienteFatLivre.setPagingEnabled(false);
        grdClienteFatLivre.setFrozenColumns(0);
        grdClienteFatLivre.setShowFooter(false);
        grdClienteFatLivre.setShowHeader(true);
        grdClienteFatLivre.setMultiSelection(false);
        grdClienteFatLivre.setGroupingEnabled(false);
        grdClienteFatLivre.setGroupingExpanded(false);
        grdClienteFatLivre.setGroupingShowFooter(false);
        grdClienteFatLivre.setCrosstabEnabled(false);
        grdClienteFatLivre.setCrosstabGroupType("cgtConcat");
        grdClienteFatLivre.setEditionEnabled(false);
        grdClienteFatLivre.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("COD_EMPRESA");
        item0.setTitleCaption("CE");
        item0.setWidth(30);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taRight");
        item0.setFieldType("ftInteger");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdClienteFatLivre.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("NOME");
        item1.setTitleCaption("Empresa");
        item1.setWidth(120);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(true);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdClienteFatLivre.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("CGC");
        item2.setTitleCaption("CNPJ");
        item2.setWidth(140);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(true);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdClienteFatLivre.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setTitleCaption("Fat. Livre");
        item3.setWidth(60);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFImageExpression item4 = new TFImageExpression();
        item4.setExpression("FATURAMENTO_LIVRE ='N'");
        item4.setEvalType("etExpression");
        item4.setImageId(310011);
        item4.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdClienteFatLivresetFaturamentoLivre(event);
            processarFlow("FrmClientesFlags", "item4", "OnClick");
        });
        item3.getImages().add(item4);
        TFImageExpression item5 = new TFImageExpression();
        item5.setExpression("FATURAMENTO_LIVRE='S'");
        item5.setEvalType("etExpression");
        item5.setImageId(310010);
        item5.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdClienteFatLivresetFaturamentoLivre(event);
            processarFlow("FrmClientesFlags", "item5", "OnClick");
        });
        item3.getImages().add(item5);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdClienteFatLivre.getColumns().add(item3);
        vBoxFaturamentoLivre02.addChildren(grdClienteFatLivre);
        grdClienteFatLivre.applyProperties();
    }

    public TFHBox FHBox23 = new TFHBox();

    private void init_FHBox23() {
        FHBox23.setName("FHBox23");
        FHBox23.setLeft(965);
        FHBox23.setTop(0);
        FHBox23.setWidth(5);
        FHBox23.setHeight(20);
        FHBox23.setBorderStyle("stNone");
        FHBox23.setPaddingTop(0);
        FHBox23.setPaddingLeft(0);
        FHBox23.setPaddingRight(0);
        FHBox23.setPaddingBottom(0);
        FHBox23.setMarginTop(0);
        FHBox23.setMarginLeft(0);
        FHBox23.setMarginRight(0);
        FHBox23.setMarginBottom(0);
        FHBox23.setSpacing(1);
        FHBox23.setFlexVflex("ftFalse");
        FHBox23.setFlexHflex("ftFalse");
        FHBox23.setScrollable(false);
        FHBox23.setBoxShadowConfigHorizontalLength(10);
        FHBox23.setBoxShadowConfigVerticalLength(10);
        FHBox23.setBoxShadowConfigBlurRadius(5);
        FHBox23.setBoxShadowConfigSpreadRadius(0);
        FHBox23.setBoxShadowConfigShadowColor("clBlack");
        FHBox23.setBoxShadowConfigOpacity(75);
        FHBox23.setVAlign("tvTop");
        hBoxFaturamentoLivreLinha001.addChildren(FHBox23);
        FHBox23.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteDiversoFlag);
        sc.getTables().add(item0);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public abstract void pgFlagsChange(final Event<Object> event);

    public abstract void cboSegmentoChange(final Event<Object> event);

    public abstract void cboSegmentoClearClick(final Event<Object> event);

    public void btnIncluirDescontoPorLetraClick(final Event<Object> event) {
        if (btnIncluirDescontoPorLetra.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirDescontoPorLetra");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdDescontoLetraDoubleClick(final Event<Object> event);

    public abstract void grdDescontoLetraalterarDescontoPorLetra(final Event<Object> event);

    public abstract void grdDescontoLetraexcluirDescontoPorLetra(final Event<Object> event);

    public void btnIncluirMargemMinimaMarkupClick(final Event<Object> event) {
        if (btnIncluirMargemMinimaMarkup.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirMargemMinimaMarkup");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdMargemMinimaMarkupDoubleClick(final Event<Object> event);

    public abstract void grdMargemMinimaMarkupalterarMargemMinimaMarkup(final Event<Object> event);

    public abstract void grdMargemMinimaMarkupexcluirMargemMinimaMarkup(final Event<Object> event);

    public void btnIncluirDescontoPorLetraEmpresaClick(final Event<Object> event) {
        if (btnIncluirDescontoPorLetraEmpresa.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirDescontoPorLetraEmpresa");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdDescontoLetraEmpresaDoubleClick(final Event<Object> event);

    public abstract void grdDescontoLetraEmpresaalterarDescontoPorLetraEmpresa(final Event<Object> event);

    public abstract void grdDescontoLetraEmpresaexcluirDescontoPorLetraEmpresa(final Event<Object> event);

    public void btnIncluirMargemMinimaMarkupEmpresaClick(final Event<Object> event) {
        if (btnIncluirMargemMinimaMarkupEmpresa.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirMargemMinimaMarkupEmpresa");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdMargemMinimaMarkupEmpresaDoubleClick(final Event<Object> event);

    public abstract void grdMargemMinimaMarkupEmpresaalterarMargemMinimaMarkupEmpresa(final Event<Object> event);

    public abstract void grdMargemMinimaMarkupEmpresaexcluirMargemMinimaMarkupEmpresa(final Event<Object> event);

    public abstract void chkUsaPrecoFabricaCheck(final Event<Object> event);

    public abstract void chkUsaPrecoGarantiaCheck(final Event<Object> event);

    public abstract void chkAprovaAutoCheck(final Event<Object> event);

    public abstract void chkReservaAutoCheck(final Event<Object> event);

    public abstract void intTempoReservaExit(final Event<Object> event);

    public abstract void chkAtacadistaCheck(final Event<Object> event);

    public abstract void cboRepresentanteChange(final Event<Object> event);

    public abstract void cboRepresentanteClearClick(final Event<Object> event);

    public abstract void cboVendedorChange(final Event<Object> event);

    public abstract void cboVendedorExit(final Event<Object> event);

    public abstract void cboVendedorClearClick(final Event<Object> event);

    public void btnVincularVendedorClick(final Event<Object> event) {
        if (btnVincularVendedor.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVincularVendedor");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnIncluirTransportadoraClick(final Event<Object> event) {
        if (btnIncluirTransportadora.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnIncluirTransportadora");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdClienteTranspexcluirTransportadora(final Event<Object> event);

    public abstract void cboFormaCobChange(final Event<Object> event);

    public abstract void cboFormaCobExit(final Event<Object> event);

    public abstract void cboFormaCobClearClick(final Event<Object> event);

    public void btnAddFormaPgtoClick(final Event<Object> event) {
        if (btnAddFormaPgto.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAddFormaPgto");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdFormasDePagamentoDoubleClick(final Event<Object> event);

    public abstract void grdFormasDePagamentoexcluirFormaDePagamento(final Event<Object> event);

    public void btnAddCondicaoPgtoClick(final Event<Object> event) {
        if (btnAddCondicaoPgto.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAddCondicaoPgto");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grdCondPgtoDoubleClick(final Event<Object> event);

    public abstract void grdCondPgtoexcluirCondicaoDePagamento(final Event<Object> event);

    public abstract void chkFatLivreCheck(final Event<Object> event);

    public abstract void grdClienteFatLivresetFaturamentoLivre(final Event<Object> event);

}