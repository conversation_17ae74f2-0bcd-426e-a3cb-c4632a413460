package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmSelecaoMultipla extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.SelecaoMultiplaRNA rn = null;

    public FrmSelecaoMultipla() {
        try {
            rn = (freedom.bytecode.rn.SelecaoMultiplaRNA) getRN(freedom.bytecode.rn.wizard.SelecaoMultiplaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbSelecaoMultipla();
        init_popGridSelecaoMultipla();
        init_mmSelecionarTodosOsRegistrosGridSelecaoMultipla();
        init_mmSelecionarNenhumRegistroGridSelecaoMultipla();
        init_vBoxPrincipal();
        init_vBoxBotoes();
        init_hBoxBotoesSeparador01();
        init_hBoxBotoes();
        init_hBoxBotoesSeparador02();
        init_btnFechar();
        init_hBoxBotoesSeparador03();
        init_btnAceitar();
        init_hBoxBotoesSeparador04();
        init_vBoxGrade();
        init_hBoxSeparador01();
        init_hBoxGrade();
        init_hBoxSeparador02();
        init_gridSelecaoMultipla();
        init_hBoxSeparador03();
        init_hBoxSeparador04();
        init_FrmSelecaoMultipla();
    }

    public SELECAO_MULTIPLA tbSelecaoMultipla;

    private void init_tbSelecaoMultipla() {
        tbSelecaoMultipla = rn.tbSelecaoMultipla;
        tbSelecaoMultipla.setName("tbSelecaoMultipla");
        tbSelecaoMultipla.setMaxRowCount(200);
        tbSelecaoMultipla.setWKey("5300664;53001");
        tbSelecaoMultipla.setRatioBatchSize(20);
        getTables().put(tbSelecaoMultipla, "tbSelecaoMultipla");
        tbSelecaoMultipla.applyProperties();
    }

    public TFPopupMenu popGridSelecaoMultipla = new TFPopupMenu();

    private void init_popGridSelecaoMultipla() {
        popGridSelecaoMultipla.setName("popGridSelecaoMultipla");
        FrmSelecaoMultipla.addChildren(popGridSelecaoMultipla);
        popGridSelecaoMultipla.applyProperties();
    }

    public TFMenuItem mmSelecionarTodosOsRegistrosGridSelecaoMultipla = new TFMenuItem();

    private void init_mmSelecionarTodosOsRegistrosGridSelecaoMultipla() {
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.setName("mmSelecionarTodosOsRegistrosGridSelecaoMultipla");
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.setCaption("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.setHint("Selecionar todos os registros");
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.setImageIndex(310010);
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarTodosOsRegistrosGridSelecaoMultiplaClick(event);
            processarFlow("FrmSelecaoMultipla", "mmSelecionarTodosOsRegistrosGridSelecaoMultipla", "OnClick");
        });
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.setAccess(false);
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.setCheckmark(false);
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.setIconClass("hashtag");
        popGridSelecaoMultipla.addChildren(mmSelecionarTodosOsRegistrosGridSelecaoMultipla);
        mmSelecionarTodosOsRegistrosGridSelecaoMultipla.applyProperties();
    }

    public TFMenuItem mmSelecionarNenhumRegistroGridSelecaoMultipla = new TFMenuItem();

    private void init_mmSelecionarNenhumRegistroGridSelecaoMultipla() {
        mmSelecionarNenhumRegistroGridSelecaoMultipla.setName("mmSelecionarNenhumRegistroGridSelecaoMultipla");
        mmSelecionarNenhumRegistroGridSelecaoMultipla.setCaption("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroGridSelecaoMultipla.setHint("Selecionar nenhum registro");
        mmSelecionarNenhumRegistroGridSelecaoMultipla.setImageIndex(310011);
        mmSelecionarNenhumRegistroGridSelecaoMultipla.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mmSelecionarNenhumRegistroGridSelecaoMultiplaClick(event);
            processarFlow("FrmSelecaoMultipla", "mmSelecionarNenhumRegistroGridSelecaoMultipla", "OnClick");
        });
        mmSelecionarNenhumRegistroGridSelecaoMultipla.setAccess(false);
        mmSelecionarNenhumRegistroGridSelecaoMultipla.setCheckmark(false);
        mmSelecionarNenhumRegistroGridSelecaoMultipla.setIconClass("hashtag");
        popGridSelecaoMultipla.addChildren(mmSelecionarNenhumRegistroGridSelecaoMultipla);
        mmSelecionarNenhumRegistroGridSelecaoMultipla.applyProperties();
    }

    protected TFForm FrmSelecaoMultipla = this;
    private void init_FrmSelecaoMultipla() {
        FrmSelecaoMultipla.setName("FrmSelecaoMultipla");
        FrmSelecaoMultipla.setCaption("Sele\u00E7\u00E3o M\u00FAltipla");
        FrmSelecaoMultipla.setClientHeight(362);
        FrmSelecaoMultipla.setClientWidth(584);
        FrmSelecaoMultipla.setColor("clBtnFace");
        FrmSelecaoMultipla.setWKey("5300664");
        FrmSelecaoMultipla.setSpacing(0);
        FrmSelecaoMultipla.applyProperties();
    }

    public TFVBox vBoxPrincipal = new TFVBox();

    private void init_vBoxPrincipal() {
        vBoxPrincipal.setName("vBoxPrincipal");
        vBoxPrincipal.setLeft(0);
        vBoxPrincipal.setTop(0);
        vBoxPrincipal.setWidth(584);
        vBoxPrincipal.setHeight(362);
        vBoxPrincipal.setAlign("alClient");
        vBoxPrincipal.setBorderStyle("stNone");
        vBoxPrincipal.setPaddingTop(0);
        vBoxPrincipal.setPaddingLeft(0);
        vBoxPrincipal.setPaddingRight(0);
        vBoxPrincipal.setPaddingBottom(0);
        vBoxPrincipal.setMarginTop(0);
        vBoxPrincipal.setMarginLeft(0);
        vBoxPrincipal.setMarginRight(0);
        vBoxPrincipal.setMarginBottom(0);
        vBoxPrincipal.setSpacing(1);
        vBoxPrincipal.setFlexVflex("ftTrue");
        vBoxPrincipal.setFlexHflex("ftTrue");
        vBoxPrincipal.setScrollable(false);
        vBoxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vBoxPrincipal.setBoxShadowConfigVerticalLength(10);
        vBoxPrincipal.setBoxShadowConfigBlurRadius(5);
        vBoxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vBoxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vBoxPrincipal.setBoxShadowConfigOpacity(75);
        FrmSelecaoMultipla.addChildren(vBoxPrincipal);
        vBoxPrincipal.applyProperties();
    }

    public TFVBox vBoxBotoes = new TFVBox();

    private void init_vBoxBotoes() {
        vBoxBotoes.setName("vBoxBotoes");
        vBoxBotoes.setLeft(0);
        vBoxBotoes.setTop(0);
        vBoxBotoes.setWidth(570);
        vBoxBotoes.setHeight(90);
        vBoxBotoes.setBorderStyle("stNone");
        vBoxBotoes.setPaddingTop(0);
        vBoxBotoes.setPaddingLeft(0);
        vBoxBotoes.setPaddingRight(0);
        vBoxBotoes.setPaddingBottom(0);
        vBoxBotoes.setMarginTop(0);
        vBoxBotoes.setMarginLeft(0);
        vBoxBotoes.setMarginRight(0);
        vBoxBotoes.setMarginBottom(0);
        vBoxBotoes.setSpacing(1);
        vBoxBotoes.setFlexVflex("ftMin");
        vBoxBotoes.setFlexHflex("ftTrue");
        vBoxBotoes.setScrollable(false);
        vBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        vBoxBotoes.setBoxShadowConfigVerticalLength(10);
        vBoxBotoes.setBoxShadowConfigBlurRadius(5);
        vBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        vBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        vBoxBotoes.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxBotoes);
        vBoxBotoes.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador01 = new TFHBox();

    private void init_hBoxBotoesSeparador01() {
        hBoxBotoesSeparador01.setName("hBoxBotoesSeparador01");
        hBoxBotoesSeparador01.setLeft(0);
        hBoxBotoesSeparador01.setTop(0);
        hBoxBotoesSeparador01.setWidth(560);
        hBoxBotoesSeparador01.setHeight(5);
        hBoxBotoesSeparador01.setBorderStyle("stNone");
        hBoxBotoesSeparador01.setPaddingTop(0);
        hBoxBotoesSeparador01.setPaddingLeft(0);
        hBoxBotoesSeparador01.setPaddingRight(0);
        hBoxBotoesSeparador01.setPaddingBottom(0);
        hBoxBotoesSeparador01.setMarginTop(0);
        hBoxBotoesSeparador01.setMarginLeft(0);
        hBoxBotoesSeparador01.setMarginRight(0);
        hBoxBotoesSeparador01.setMarginBottom(0);
        hBoxBotoesSeparador01.setSpacing(1);
        hBoxBotoesSeparador01.setFlexVflex("ftFalse");
        hBoxBotoesSeparador01.setFlexHflex("ftFalse");
        hBoxBotoesSeparador01.setScrollable(false);
        hBoxBotoesSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador01.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador01.setVAlign("tvTop");
        vBoxBotoes.addChildren(hBoxBotoesSeparador01);
        hBoxBotoesSeparador01.applyProperties();
    }

    public TFHBox hBoxBotoes = new TFHBox();

    private void init_hBoxBotoes() {
        hBoxBotoes.setName("hBoxBotoes");
        hBoxBotoes.setLeft(0);
        hBoxBotoes.setTop(6);
        hBoxBotoes.setWidth(560);
        hBoxBotoes.setHeight(70);
        hBoxBotoes.setBorderStyle("stNone");
        hBoxBotoes.setPaddingTop(0);
        hBoxBotoes.setPaddingLeft(0);
        hBoxBotoes.setPaddingRight(0);
        hBoxBotoes.setPaddingBottom(0);
        hBoxBotoes.setMarginTop(0);
        hBoxBotoes.setMarginLeft(0);
        hBoxBotoes.setMarginRight(0);
        hBoxBotoes.setMarginBottom(0);
        hBoxBotoes.setSpacing(1);
        hBoxBotoes.setFlexVflex("ftMin");
        hBoxBotoes.setFlexHflex("ftTrue");
        hBoxBotoes.setScrollable(false);
        hBoxBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoes.setBoxShadowConfigOpacity(75);
        hBoxBotoes.setVAlign("tvTop");
        vBoxBotoes.addChildren(hBoxBotoes);
        hBoxBotoes.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador02 = new TFHBox();

    private void init_hBoxBotoesSeparador02() {
        hBoxBotoesSeparador02.setName("hBoxBotoesSeparador02");
        hBoxBotoesSeparador02.setLeft(0);
        hBoxBotoesSeparador02.setTop(0);
        hBoxBotoesSeparador02.setWidth(5);
        hBoxBotoesSeparador02.setHeight(20);
        hBoxBotoesSeparador02.setBorderStyle("stNone");
        hBoxBotoesSeparador02.setPaddingTop(0);
        hBoxBotoesSeparador02.setPaddingLeft(0);
        hBoxBotoesSeparador02.setPaddingRight(0);
        hBoxBotoesSeparador02.setPaddingBottom(0);
        hBoxBotoesSeparador02.setMarginTop(0);
        hBoxBotoesSeparador02.setMarginLeft(0);
        hBoxBotoesSeparador02.setMarginRight(0);
        hBoxBotoesSeparador02.setMarginBottom(0);
        hBoxBotoesSeparador02.setSpacing(1);
        hBoxBotoesSeparador02.setFlexVflex("ftFalse");
        hBoxBotoesSeparador02.setFlexHflex("ftFalse");
        hBoxBotoesSeparador02.setScrollable(false);
        hBoxBotoesSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador02.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador02.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxBotoesSeparador02);
        hBoxBotoesSeparador02.applyProperties();
    }

    public TFButton btnFechar = new TFButton();

    private void init_btnFechar() {
        btnFechar.setName("btnFechar");
        btnFechar.setLeft(5);
        btnFechar.setTop(0);
        btnFechar.setWidth(65);
        btnFechar.setHeight(53);
        btnFechar.setHint("Fechar");
        btnFechar.setCaption("Fechar");
        btnFechar.setFontColor("clWindowText");
        btnFechar.setFontSize(-11);
        btnFechar.setFontName("Tahoma");
        btnFechar.setFontStyle("[]");
        btnFechar.setLayout("blGlyphTop");
        btnFechar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFecharClick(event);
            processarFlow("FrmSelecaoMultipla", "btnFechar", "OnClick");
        });
        btnFechar.setImageId(700081);
        btnFechar.setColor("clBtnFace");
        btnFechar.setAccess(false);
        btnFechar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnFechar);
        btnFechar.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador03 = new TFHBox();

    private void init_hBoxBotoesSeparador03() {
        hBoxBotoesSeparador03.setName("hBoxBotoesSeparador03");
        hBoxBotoesSeparador03.setLeft(70);
        hBoxBotoesSeparador03.setTop(0);
        hBoxBotoesSeparador03.setWidth(5);
        hBoxBotoesSeparador03.setHeight(20);
        hBoxBotoesSeparador03.setBorderStyle("stNone");
        hBoxBotoesSeparador03.setPaddingTop(0);
        hBoxBotoesSeparador03.setPaddingLeft(0);
        hBoxBotoesSeparador03.setPaddingRight(0);
        hBoxBotoesSeparador03.setPaddingBottom(0);
        hBoxBotoesSeparador03.setMarginTop(0);
        hBoxBotoesSeparador03.setMarginLeft(0);
        hBoxBotoesSeparador03.setMarginRight(0);
        hBoxBotoesSeparador03.setMarginBottom(0);
        hBoxBotoesSeparador03.setSpacing(1);
        hBoxBotoesSeparador03.setFlexVflex("ftFalse");
        hBoxBotoesSeparador03.setFlexHflex("ftFalse");
        hBoxBotoesSeparador03.setScrollable(false);
        hBoxBotoesSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador03.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador03.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxBotoesSeparador03);
        hBoxBotoesSeparador03.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(75);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(65);
        btnAceitar.setHeight(53);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-11);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmSelecaoMultipla", "btnAceitar", "OnClick");
        });
        btnAceitar.setImageId(10);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        hBoxBotoes.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFHBox hBoxBotoesSeparador04 = new TFHBox();

    private void init_hBoxBotoesSeparador04() {
        hBoxBotoesSeparador04.setName("hBoxBotoesSeparador04");
        hBoxBotoesSeparador04.setLeft(140);
        hBoxBotoesSeparador04.setTop(0);
        hBoxBotoesSeparador04.setWidth(5);
        hBoxBotoesSeparador04.setHeight(20);
        hBoxBotoesSeparador04.setBorderStyle("stNone");
        hBoxBotoesSeparador04.setPaddingTop(0);
        hBoxBotoesSeparador04.setPaddingLeft(0);
        hBoxBotoesSeparador04.setPaddingRight(0);
        hBoxBotoesSeparador04.setPaddingBottom(0);
        hBoxBotoesSeparador04.setMarginTop(0);
        hBoxBotoesSeparador04.setMarginLeft(0);
        hBoxBotoesSeparador04.setMarginRight(0);
        hBoxBotoesSeparador04.setMarginBottom(0);
        hBoxBotoesSeparador04.setSpacing(1);
        hBoxBotoesSeparador04.setFlexVflex("ftFalse");
        hBoxBotoesSeparador04.setFlexHflex("ftFalse");
        hBoxBotoesSeparador04.setScrollable(false);
        hBoxBotoesSeparador04.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesSeparador04.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesSeparador04.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesSeparador04.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesSeparador04.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesSeparador04.setBoxShadowConfigOpacity(75);
        hBoxBotoesSeparador04.setVAlign("tvTop");
        hBoxBotoes.addChildren(hBoxBotoesSeparador04);
        hBoxBotoesSeparador04.applyProperties();
    }

    public TFVBox vBoxGrade = new TFVBox();

    private void init_vBoxGrade() {
        vBoxGrade.setName("vBoxGrade");
        vBoxGrade.setLeft(0);
        vBoxGrade.setTop(91);
        vBoxGrade.setWidth(570);
        vBoxGrade.setHeight(130);
        vBoxGrade.setBorderStyle("stNone");
        vBoxGrade.setPaddingTop(0);
        vBoxGrade.setPaddingLeft(0);
        vBoxGrade.setPaddingRight(0);
        vBoxGrade.setPaddingBottom(0);
        vBoxGrade.setMarginTop(0);
        vBoxGrade.setMarginLeft(0);
        vBoxGrade.setMarginRight(0);
        vBoxGrade.setMarginBottom(0);
        vBoxGrade.setSpacing(1);
        vBoxGrade.setFlexVflex("ftTrue");
        vBoxGrade.setFlexHflex("ftTrue");
        vBoxGrade.setScrollable(false);
        vBoxGrade.setBoxShadowConfigHorizontalLength(10);
        vBoxGrade.setBoxShadowConfigVerticalLength(10);
        vBoxGrade.setBoxShadowConfigBlurRadius(5);
        vBoxGrade.setBoxShadowConfigSpreadRadius(0);
        vBoxGrade.setBoxShadowConfigShadowColor("clBlack");
        vBoxGrade.setBoxShadowConfigOpacity(75);
        vBoxPrincipal.addChildren(vBoxGrade);
        vBoxGrade.applyProperties();
    }

    public TFHBox hBoxSeparador01 = new TFHBox();

    private void init_hBoxSeparador01() {
        hBoxSeparador01.setName("hBoxSeparador01");
        hBoxSeparador01.setLeft(0);
        hBoxSeparador01.setTop(0);
        hBoxSeparador01.setWidth(560);
        hBoxSeparador01.setHeight(5);
        hBoxSeparador01.setBorderStyle("stNone");
        hBoxSeparador01.setPaddingTop(0);
        hBoxSeparador01.setPaddingLeft(0);
        hBoxSeparador01.setPaddingRight(0);
        hBoxSeparador01.setPaddingBottom(0);
        hBoxSeparador01.setMarginTop(0);
        hBoxSeparador01.setMarginLeft(0);
        hBoxSeparador01.setMarginRight(0);
        hBoxSeparador01.setMarginBottom(0);
        hBoxSeparador01.setSpacing(1);
        hBoxSeparador01.setFlexVflex("ftFalse");
        hBoxSeparador01.setFlexHflex("ftFalse");
        hBoxSeparador01.setScrollable(false);
        hBoxSeparador01.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador01.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador01.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador01.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador01.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador01.setBoxShadowConfigOpacity(75);
        hBoxSeparador01.setVAlign("tvTop");
        vBoxGrade.addChildren(hBoxSeparador01);
        hBoxSeparador01.applyProperties();
    }

    public TFHBox hBoxGrade = new TFHBox();

    private void init_hBoxGrade() {
        hBoxGrade.setName("hBoxGrade");
        hBoxGrade.setLeft(0);
        hBoxGrade.setTop(6);
        hBoxGrade.setWidth(560);
        hBoxGrade.setHeight(110);
        hBoxGrade.setBorderStyle("stNone");
        hBoxGrade.setPaddingTop(0);
        hBoxGrade.setPaddingLeft(0);
        hBoxGrade.setPaddingRight(0);
        hBoxGrade.setPaddingBottom(0);
        hBoxGrade.setMarginTop(0);
        hBoxGrade.setMarginLeft(0);
        hBoxGrade.setMarginRight(0);
        hBoxGrade.setMarginBottom(0);
        hBoxGrade.setSpacing(1);
        hBoxGrade.setFlexVflex("ftTrue");
        hBoxGrade.setFlexHflex("ftTrue");
        hBoxGrade.setScrollable(false);
        hBoxGrade.setBoxShadowConfigHorizontalLength(10);
        hBoxGrade.setBoxShadowConfigVerticalLength(10);
        hBoxGrade.setBoxShadowConfigBlurRadius(5);
        hBoxGrade.setBoxShadowConfigSpreadRadius(0);
        hBoxGrade.setBoxShadowConfigShadowColor("clBlack");
        hBoxGrade.setBoxShadowConfigOpacity(75);
        hBoxGrade.setVAlign("tvTop");
        vBoxGrade.addChildren(hBoxGrade);
        hBoxGrade.applyProperties();
    }

    public TFHBox hBoxSeparador02 = new TFHBox();

    private void init_hBoxSeparador02() {
        hBoxSeparador02.setName("hBoxSeparador02");
        hBoxSeparador02.setLeft(0);
        hBoxSeparador02.setTop(0);
        hBoxSeparador02.setWidth(5);
        hBoxSeparador02.setHeight(20);
        hBoxSeparador02.setBorderStyle("stNone");
        hBoxSeparador02.setPaddingTop(0);
        hBoxSeparador02.setPaddingLeft(0);
        hBoxSeparador02.setPaddingRight(0);
        hBoxSeparador02.setPaddingBottom(0);
        hBoxSeparador02.setMarginTop(0);
        hBoxSeparador02.setMarginLeft(0);
        hBoxSeparador02.setMarginRight(0);
        hBoxSeparador02.setMarginBottom(0);
        hBoxSeparador02.setSpacing(1);
        hBoxSeparador02.setFlexVflex("ftFalse");
        hBoxSeparador02.setFlexHflex("ftFalse");
        hBoxSeparador02.setScrollable(false);
        hBoxSeparador02.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador02.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador02.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador02.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador02.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador02.setBoxShadowConfigOpacity(75);
        hBoxSeparador02.setVAlign("tvTop");
        hBoxGrade.addChildren(hBoxSeparador02);
        hBoxSeparador02.applyProperties();
    }

    public TFGrid gridSelecaoMultipla = new TFGrid();

    private void init_gridSelecaoMultipla() {
        gridSelecaoMultipla.setName("gridSelecaoMultipla");
        gridSelecaoMultipla.setLeft(5);
        gridSelecaoMultipla.setTop(0);
        gridSelecaoMultipla.setWidth(540);
        gridSelecaoMultipla.setHeight(100);
        gridSelecaoMultipla.setTable(tbSelecaoMultipla);
        gridSelecaoMultipla.setFlexVflex("ftTrue");
        gridSelecaoMultipla.setFlexHflex("ftTrue");
        gridSelecaoMultipla.setPagingEnabled(false);
        gridSelecaoMultipla.setFrozenColumns(0);
        gridSelecaoMultipla.setShowFooter(false);
        gridSelecaoMultipla.setShowHeader(true);
        gridSelecaoMultipla.setMultiSelection(false);
        gridSelecaoMultipla.setGroupingEnabled(false);
        gridSelecaoMultipla.setGroupingExpanded(false);
        gridSelecaoMultipla.setGroupingShowFooter(false);
        gridSelecaoMultipla.setCrosstabEnabled(false);
        gridSelecaoMultipla.setCrosstabGroupType("cgtConcat");
        gridSelecaoMultipla.setEditionEnabled(false);
        gridSelecaoMultipla.setContextMenu(popGridSelecaoMultipla);
        gridSelecaoMultipla.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEL");
        item0.setTitleCaption(" #");
        item0.setWidth(33);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taCenter");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL = 'S'");
        item1.setHint("Registro selecionado");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item1.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridSelecaoMultipladesmarcarRegistroNaGridSelecaoMultipla(event);
            processarFlow("FrmSelecaoMultipla", "item1", "OnClick");
        });
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL = 'N'");
        item2.setHint("Registro n\u00E3o selecionado");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item2.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridSelecaoMultiplamarcarRegistroNaGridSelecaoMultipla(event);
            processarFlow("FrmSelecaoMultipla", "item2", "OnClick");
        });
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(false);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridSelecaoMultipla.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("CODIGO");
        item3.setTitleCaption("C\u00F3digo");
        item3.setWidth(88);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridSelecaoMultipla.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DESCRICAO");
        item4.setTitleCaption("Descri\u00E7\u00E3o");
        item4.setWidth(171);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridSelecaoMultipla.getColumns().add(item4);
        hBoxGrade.addChildren(gridSelecaoMultipla);
        gridSelecaoMultipla.applyProperties();
    }

    public TFHBox hBoxSeparador03 = new TFHBox();

    private void init_hBoxSeparador03() {
        hBoxSeparador03.setName("hBoxSeparador03");
        hBoxSeparador03.setLeft(545);
        hBoxSeparador03.setTop(0);
        hBoxSeparador03.setWidth(5);
        hBoxSeparador03.setHeight(20);
        hBoxSeparador03.setBorderStyle("stNone");
        hBoxSeparador03.setPaddingTop(0);
        hBoxSeparador03.setPaddingLeft(0);
        hBoxSeparador03.setPaddingRight(0);
        hBoxSeparador03.setPaddingBottom(0);
        hBoxSeparador03.setMarginTop(0);
        hBoxSeparador03.setMarginLeft(0);
        hBoxSeparador03.setMarginRight(0);
        hBoxSeparador03.setMarginBottom(0);
        hBoxSeparador03.setSpacing(1);
        hBoxSeparador03.setFlexVflex("ftFalse");
        hBoxSeparador03.setFlexHflex("ftFalse");
        hBoxSeparador03.setScrollable(false);
        hBoxSeparador03.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador03.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador03.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador03.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador03.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador03.setBoxShadowConfigOpacity(75);
        hBoxSeparador03.setVAlign("tvTop");
        hBoxGrade.addChildren(hBoxSeparador03);
        hBoxSeparador03.applyProperties();
    }

    public TFHBox hBoxSeparador04 = new TFHBox();

    private void init_hBoxSeparador04() {
        hBoxSeparador04.setName("hBoxSeparador04");
        hBoxSeparador04.setLeft(0);
        hBoxSeparador04.setTop(117);
        hBoxSeparador04.setWidth(560);
        hBoxSeparador04.setHeight(5);
        hBoxSeparador04.setBorderStyle("stNone");
        hBoxSeparador04.setPaddingTop(0);
        hBoxSeparador04.setPaddingLeft(0);
        hBoxSeparador04.setPaddingRight(0);
        hBoxSeparador04.setPaddingBottom(0);
        hBoxSeparador04.setMarginTop(0);
        hBoxSeparador04.setMarginLeft(0);
        hBoxSeparador04.setMarginRight(0);
        hBoxSeparador04.setMarginBottom(0);
        hBoxSeparador04.setSpacing(1);
        hBoxSeparador04.setFlexVflex("ftFalse");
        hBoxSeparador04.setFlexHflex("ftFalse");
        hBoxSeparador04.setScrollable(false);
        hBoxSeparador04.setBoxShadowConfigHorizontalLength(10);
        hBoxSeparador04.setBoxShadowConfigVerticalLength(10);
        hBoxSeparador04.setBoxShadowConfigBlurRadius(5);
        hBoxSeparador04.setBoxShadowConfigSpreadRadius(0);
        hBoxSeparador04.setBoxShadowConfigShadowColor("clBlack");
        hBoxSeparador04.setBoxShadowConfigOpacity(75);
        hBoxSeparador04.setVAlign("tvTop");
        vBoxGrade.addChildren(hBoxSeparador04);
        hBoxSeparador04.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnFecharClick(final Event<Object> event) {
        if (btnFechar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnFechar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridSelecaoMultipladesmarcarRegistroNaGridSelecaoMultipla(final Event<Object> event);

    public abstract void gridSelecaoMultiplamarcarRegistroNaGridSelecaoMultipla(final Event<Object> event);

    public abstract void mmSelecionarTodosOsRegistrosGridSelecaoMultiplaClick(final Event<Object> event);

    public abstract void mmSelecionarNenhumRegistroGridSelecaoMultiplaClick(final Event<Object> event);

}