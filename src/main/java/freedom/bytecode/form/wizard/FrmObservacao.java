package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmObservacao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ObservacaoRNA rn = null;

    public FrmObservacao() {
        try {
            rn = (freedom.bytecode.rn.ObservacaoRNA) getRN(freedom.bytecode.rn.wizard.ObservacaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_vBoxObsercacao();
        init_hbBoxObservacaoOkCancel();
        init_btnCancel();
        init_vBoxObservacaoOkCancelSeparador01();
        init_btnOk();
        init_memObservacao();
        init_FrmObservacao();
    }

    protected TFForm FrmObservacao = this;
    private void init_FrmObservacao() {
        FrmObservacao.setName("FrmObservacao");
        FrmObservacao.setCaption("Observa\u00E7\u00E3o");
        FrmObservacao.setClientHeight(275);
        FrmObservacao.setClientWidth(524);
        FrmObservacao.setColor("clBtnFace");
        FrmObservacao.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmObservacao", "FrmObservacao", "OnCreate");
        });
        FrmObservacao.setWOrigem("EhMain");
        FrmObservacao.setWKey("7000153");
        FrmObservacao.setSpacing(0);
        FrmObservacao.applyProperties();
    }

    public TFVBox vBoxObsercacao = new TFVBox();

    private void init_vBoxObsercacao() {
        vBoxObsercacao.setName("vBoxObsercacao");
        vBoxObsercacao.setLeft(0);
        vBoxObsercacao.setTop(0);
        vBoxObsercacao.setWidth(524);
        vBoxObsercacao.setHeight(275);
        vBoxObsercacao.setAlign("alClient");
        vBoxObsercacao.setBorderStyle("stNone");
        vBoxObsercacao.setPaddingTop(5);
        vBoxObsercacao.setPaddingLeft(5);
        vBoxObsercacao.setPaddingRight(5);
        vBoxObsercacao.setPaddingBottom(5);
        vBoxObsercacao.setMarginTop(0);
        vBoxObsercacao.setMarginLeft(0);
        vBoxObsercacao.setMarginRight(0);
        vBoxObsercacao.setMarginBottom(0);
        vBoxObsercacao.setSpacing(1);
        vBoxObsercacao.setFlexVflex("ftTrue");
        vBoxObsercacao.setFlexHflex("ftTrue");
        vBoxObsercacao.setScrollable(false);
        vBoxObsercacao.setBoxShadowConfigHorizontalLength(10);
        vBoxObsercacao.setBoxShadowConfigVerticalLength(10);
        vBoxObsercacao.setBoxShadowConfigBlurRadius(5);
        vBoxObsercacao.setBoxShadowConfigSpreadRadius(0);
        vBoxObsercacao.setBoxShadowConfigShadowColor("clBlack");
        vBoxObsercacao.setBoxShadowConfigOpacity(75);
        FrmObservacao.addChildren(vBoxObsercacao);
        vBoxObsercacao.applyProperties();
    }

    public TFHBox hbBoxObservacaoOkCancel = new TFHBox();

    private void init_hbBoxObservacaoOkCancel() {
        hbBoxObservacaoOkCancel.setName("hbBoxObservacaoOkCancel");
        hbBoxObservacaoOkCancel.setLeft(0);
        hbBoxObservacaoOkCancel.setTop(0);
        hbBoxObservacaoOkCancel.setWidth(515);
        hbBoxObservacaoOkCancel.setHeight(65);
        hbBoxObservacaoOkCancel.setAlign("alBottom");
        hbBoxObservacaoOkCancel.setBorderStyle("stNone");
        hbBoxObservacaoOkCancel.setPaddingTop(0);
        hbBoxObservacaoOkCancel.setPaddingLeft(0);
        hbBoxObservacaoOkCancel.setPaddingRight(0);
        hbBoxObservacaoOkCancel.setPaddingBottom(5);
        hbBoxObservacaoOkCancel.setMarginTop(0);
        hbBoxObservacaoOkCancel.setMarginLeft(0);
        hbBoxObservacaoOkCancel.setMarginRight(0);
        hbBoxObservacaoOkCancel.setMarginBottom(0);
        hbBoxObservacaoOkCancel.setSpacing(1);
        hbBoxObservacaoOkCancel.setFlexVflex("ftFalse");
        hbBoxObservacaoOkCancel.setFlexHflex("ftTrue");
        hbBoxObservacaoOkCancel.setScrollable(false);
        hbBoxObservacaoOkCancel.setBoxShadowConfigHorizontalLength(10);
        hbBoxObservacaoOkCancel.setBoxShadowConfigVerticalLength(10);
        hbBoxObservacaoOkCancel.setBoxShadowConfigBlurRadius(5);
        hbBoxObservacaoOkCancel.setBoxShadowConfigSpreadRadius(0);
        hbBoxObservacaoOkCancel.setBoxShadowConfigShadowColor("clBlack");
        hbBoxObservacaoOkCancel.setBoxShadowConfigOpacity(75);
        hbBoxObservacaoOkCancel.setVAlign("tvTop");
        vBoxObsercacao.addChildren(hbBoxObservacaoOkCancel);
        hbBoxObservacaoOkCancel.applyProperties();
    }

    public TFButton btnCancel = new TFButton();

    private void init_btnCancel() {
        btnCancel.setName("btnCancel");
        btnCancel.setLeft(0);
        btnCancel.setTop(0);
        btnCancel.setWidth(66);
        btnCancel.setHeight(55);
        btnCancel.setHint("Voltar");
        btnCancel.setCaption("Voltar");
        btnCancel.setFontColor("clWindowText");
        btnCancel.setFontSize(-11);
        btnCancel.setFontName("Tahoma");
        btnCancel.setFontStyle("[]");
        btnCancel.setLayout("blGlyphTop");
        btnCancel.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnCancelClick(event);
            processarFlow("FrmObservacao", "btnCancel", "OnClick");
        });
        btnCancel.setImageId(430032);
        btnCancel.setColor("clBtnFace");
        btnCancel.setAccess(false);
        btnCancel.setIconReverseDirection(false);
        hbBoxObservacaoOkCancel.addChildren(btnCancel);
        btnCancel.applyProperties();
    }

    public TFVBox vBoxObservacaoOkCancelSeparador01 = new TFVBox();

    private void init_vBoxObservacaoOkCancelSeparador01() {
        vBoxObservacaoOkCancelSeparador01.setName("vBoxObservacaoOkCancelSeparador01");
        vBoxObservacaoOkCancelSeparador01.setLeft(66);
        vBoxObservacaoOkCancelSeparador01.setTop(0);
        vBoxObservacaoOkCancelSeparador01.setWidth(5);
        vBoxObservacaoOkCancelSeparador01.setHeight(20);
        vBoxObservacaoOkCancelSeparador01.setBorderStyle("stNone");
        vBoxObservacaoOkCancelSeparador01.setPaddingTop(0);
        vBoxObservacaoOkCancelSeparador01.setPaddingLeft(0);
        vBoxObservacaoOkCancelSeparador01.setPaddingRight(0);
        vBoxObservacaoOkCancelSeparador01.setPaddingBottom(0);
        vBoxObservacaoOkCancelSeparador01.setMarginTop(0);
        vBoxObservacaoOkCancelSeparador01.setMarginLeft(0);
        vBoxObservacaoOkCancelSeparador01.setMarginRight(0);
        vBoxObservacaoOkCancelSeparador01.setMarginBottom(0);
        vBoxObservacaoOkCancelSeparador01.setSpacing(1);
        vBoxObservacaoOkCancelSeparador01.setFlexVflex("ftFalse");
        vBoxObservacaoOkCancelSeparador01.setFlexHflex("ftFalse");
        vBoxObservacaoOkCancelSeparador01.setScrollable(false);
        vBoxObservacaoOkCancelSeparador01.setBoxShadowConfigHorizontalLength(10);
        vBoxObservacaoOkCancelSeparador01.setBoxShadowConfigVerticalLength(10);
        vBoxObservacaoOkCancelSeparador01.setBoxShadowConfigBlurRadius(5);
        vBoxObservacaoOkCancelSeparador01.setBoxShadowConfigSpreadRadius(0);
        vBoxObservacaoOkCancelSeparador01.setBoxShadowConfigShadowColor("clBlack");
        vBoxObservacaoOkCancelSeparador01.setBoxShadowConfigOpacity(75);
        hbBoxObservacaoOkCancel.addChildren(vBoxObservacaoOkCancelSeparador01);
        vBoxObservacaoOkCancelSeparador01.applyProperties();
    }

    public TFButton btnOk = new TFButton();

    private void init_btnOk() {
        btnOk.setName("btnOk");
        btnOk.setLeft(71);
        btnOk.setTop(0);
        btnOk.setWidth(66);
        btnOk.setHeight(55);
        btnOk.setHint("Salvar");
        btnOk.setCaption("Salvar");
        btnOk.setFontColor("clWindowText");
        btnOk.setFontSize(-11);
        btnOk.setFontName("Tahoma");
        btnOk.setFontStyle("[]");
        btnOk.setLayout("blGlyphTop");
        btnOk.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnOkClick(event);
            processarFlow("FrmObservacao", "btnOk", "OnClick");
        });
        btnOk.setImageId(310032);
        btnOk.setColor("clBtnFace");
        btnOk.setAccess(false);
        btnOk.setIconReverseDirection(false);
        hbBoxObservacaoOkCancel.addChildren(btnOk);
        btnOk.applyProperties();
    }

    public TFMemo memObservacao = new TFMemo();

    private void init_memObservacao() {
        memObservacao.setName("memObservacao");
        memObservacao.setLeft(0);
        memObservacao.setTop(66);
        memObservacao.setWidth(515);
        memObservacao.setHeight(216);
        memObservacao.setHint("Observa\u00E7\u00E3o");
        memObservacao.setAlign("alClient");
        memObservacao.setCharCase("ccNormal");
        memObservacao.setFontColor("clWindowText");
        memObservacao.setFontSize(-13);
        memObservacao.setFontName("Tahoma");
        memObservacao.setFontStyle("[]");
        memObservacao.setMaxlength(0);
        memObservacao.setFlexVflex("ftTrue");
        memObservacao.setFlexHflex("ftTrue");
        memObservacao.setHelpCaption("Observa\u00E7\u00E3o");
        memObservacao.setConstraintCheckWhen("cwImmediate");
        memObservacao.setConstraintCheckType("ctExpression");
        memObservacao.setConstraintFocusOnError(false);
        memObservacao.setConstraintEnableUI(true);
        memObservacao.setConstraintEnabled(false);
        memObservacao.setConstraintFormCheck(true);
        memObservacao.setRequired(false);
        vBoxObsercacao.addChildren(memObservacao);
        memObservacao.applyProperties();
        addValidatable(memObservacao);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnCancelClick(final Event<Object> event) {
        if (btnCancel.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnCancel");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnOkClick(final Event<Object> event) {
        if (btnOk.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnOk");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}