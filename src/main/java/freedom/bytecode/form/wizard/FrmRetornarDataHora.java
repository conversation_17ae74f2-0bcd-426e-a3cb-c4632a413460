package freedom.bytecode.form.wizard;

import freedom.client.event.EventListener;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.client.controls.impl.*;

public abstract class FrmRetornarDataHora extends TFForm {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.RetornarDataHoraRNA rn = null;

    public FrmRetornarDataHora() {
        try {
            rn = (freedom.bytecode.rn.RetornarDataHoraRNA) getRN(freedom.bytecode.rn.wizard.RetornarDataHoraRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FVBox1();
        init_vBoxSelecioneDataHora();
        init_monthCalendarData();
        init_FHBox1();
        init_FHBox2();
        init_timeAgenda();
        init_FHBox3();
        init_btnOk();
        init_FrmRetornarDataHora();
    }

    protected TFForm FrmRetornarDataHora = this;

    private void init_FrmRetornarDataHora() {
        FrmRetornarDataHora.setName("FrmRetornarDataHora");
        FrmRetornarDataHora.setCaption("Selecione uma Data/Hora");
        FrmRetornarDataHora.setClientHeight(342);
        FrmRetornarDataHora.setClientWidth(338);
        FrmRetornarDataHora.setColor("clBtnFace");
        FrmRetornarDataHora.setWKey("7000185");
        FrmRetornarDataHora.setSpacing(0);
        FrmRetornarDataHora.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(338);
        FVBox1.setHeight(342);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(5);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmRetornarDataHora.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFVBox vBoxSelecioneDataHora = new TFVBox();

    private void init_vBoxSelecioneDataHora() {
        vBoxSelecioneDataHora.setName("vBoxSelecioneDataHora");
        vBoxSelecioneDataHora.setLeft(0);
        vBoxSelecioneDataHora.setTop(0);
        vBoxSelecioneDataHora.setWidth(318);
        vBoxSelecioneDataHora.setHeight(288);
        vBoxSelecioneDataHora.setAlign("alTop");
        vBoxSelecioneDataHora.setBorderStyle("stNone");
        vBoxSelecioneDataHora.setPaddingTop(0);
        vBoxSelecioneDataHora.setPaddingLeft(0);
        vBoxSelecioneDataHora.setPaddingRight(0);
        vBoxSelecioneDataHora.setPaddingBottom(0);
        vBoxSelecioneDataHora.setMarginTop(0);
        vBoxSelecioneDataHora.setMarginLeft(0);
        vBoxSelecioneDataHora.setMarginRight(0);
        vBoxSelecioneDataHora.setMarginBottom(0);
        vBoxSelecioneDataHora.setSpacing(1);
        vBoxSelecioneDataHora.setFlexVflex("ftTrue");
        vBoxSelecioneDataHora.setFlexHflex("ftTrue");
        vBoxSelecioneDataHora.setScrollable(false);
        vBoxSelecioneDataHora.setBoxShadowConfigHorizontalLength(10);
        vBoxSelecioneDataHora.setBoxShadowConfigVerticalLength(10);
        vBoxSelecioneDataHora.setBoxShadowConfigBlurRadius(5);
        vBoxSelecioneDataHora.setBoxShadowConfigSpreadRadius(0);
        vBoxSelecioneDataHora.setBoxShadowConfigShadowColor("clBlack");
        vBoxSelecioneDataHora.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(vBoxSelecioneDataHora);
        vBoxSelecioneDataHora.applyProperties();
    }

    public TFMonthCalendar monthCalendarData = new TFMonthCalendar();

    private void init_monthCalendarData() {
        monthCalendarData.setName("monthCalendarData");
        monthCalendarData.setLeft(0);
        monthCalendarData.setTop(0);
        monthCalendarData.setWidth(173);
        monthCalendarData.setHeight(153);
        monthCalendarData.setFlexVflex("ftMin");
        monthCalendarData.setFlexHflex("ftTrue");
        monthCalendarData.setShowTodayLink(true);
        //monthCalendarData.setTodayLinkLabel("Hoje");
        vBoxSelecioneDataHora.addChildren(monthCalendarData);
        monthCalendarData.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(289);
        FHBox1.setWidth(124);
        FHBox1.setHeight(12);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(302);
        FHBox2.setWidth(313);
        FHBox2.setHeight(35);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(12);
        FHBox2.setPaddingRight(12);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FVBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFTime timeAgenda = new TFTime();

    private void init_timeAgenda() {
        timeAgenda.setName("timeAgenda");
        timeAgenda.setLeft(0);
        timeAgenda.setTop(0);
        timeAgenda.setWidth(128);
        timeAgenda.setHeight(24);
        timeAgenda.setFlex(false);
        timeAgenda.setRequired(false);
        timeAgenda.setConstraintCheckWhen("cwImmediate");
        timeAgenda.setConstraintCheckType("ctExpression");
        timeAgenda.setConstraintFocusOnError(false);
        timeAgenda.setConstraintEnableUI(true);
        timeAgenda.setConstraintEnabled(false);
        timeAgenda.setConstraintFormCheck(true);
        timeAgenda.setFormat("HH:mm");
        timeAgenda.setFontColor("clWindowText");
        timeAgenda.setFontSize(-13);
        timeAgenda.setFontName("Tahoma");
        timeAgenda.setFontStyle("[]");
        FHBox2.addChildren(timeAgenda);
        timeAgenda.applyProperties();
        addValidatable(timeAgenda);
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(128);
        FHBox3.setTop(0);
        FHBox3.setWidth(88);
        FHBox3.setHeight(8);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftTrue");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFButton btnOk = new TFButton();

    private void init_btnOk() {
        btnOk.setName("btnOk");
        btnOk.setLeft(216);
        btnOk.setTop(0);
        btnOk.setWidth(92);
        btnOk.setHeight(30);
        btnOk.setCaption("Ok");
        btnOk.setFontColor("clWindowText");
        btnOk.setFontSize(-11);
        btnOk.setFontName("Tahoma");
        btnOk.setFontStyle("[]");
        btnOk.addEventListener("onClick", (EventListener<Event<Object>>) (Event<Object> event) -> {
            btnOkClick(event);
            processarFlow("FrmRetornarDataHora", "btnOk", "OnClick");
        });
        btnOk.setImageId(0);
        btnOk.setColor("clBtnFace");
        btnOk.setAccess(false);
        btnOk.setIconReverseDirection(false);
        FHBox2.addChildren(btnOk);
        btnOk.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnOkClick(final Event<Object> event) {
        if (btnOk.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnOk");
            } catch (NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

}
