package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAssinaturaDigitalVer extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AssinaturaDigitalVerRNA rn = null;

    public FrmAssinaturaDigitalVer() {
        try {
            rn = (freedom.bytecode.rn.AssinaturaDigitalVerRNA) getRN(freedom.bytecode.rn.wizard.AssinaturaDigitalVerRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbSolicitacoesAssinaturas();
        init_vboxPrincipal();
        init_vboxQrCode();
        init_vboxQrCodeEspaco1();
        init_imgQrCode();
        init_vboxQrCodeEspaco2();
        init_hboxLblAssinar();
        init_hboxLblAssinarEspaco1();
        init_lblAssinar();
        init_iconAssinar();
        init_hboxLblAssinarEspaco2();
        init_FrmAssinaturaDigitalVer();
    }

    public SOLICITACOES_ASSINATURAS tbSolicitacoesAssinaturas;

    private void init_tbSolicitacoesAssinaturas() {
        tbSolicitacoesAssinaturas = rn.tbSolicitacoesAssinaturas;
        tbSolicitacoesAssinaturas.setName("tbSolicitacoesAssinaturas");
        tbSolicitacoesAssinaturas.setMaxRowCount(200);
        tbSolicitacoesAssinaturas.setWKey("45509;45501");
        tbSolicitacoesAssinaturas.setRatioBatchSize(20);
        getTables().put(tbSolicitacoesAssinaturas, "tbSolicitacoesAssinaturas");
        tbSolicitacoesAssinaturas.applyProperties();
    }

    protected TFForm FrmAssinaturaDigitalVer = this;
    private void init_FrmAssinaturaDigitalVer() {
        FrmAssinaturaDigitalVer.setName("FrmAssinaturaDigitalVer");
        FrmAssinaturaDigitalVer.setCaption("Ver Assinatura Digital");
        FrmAssinaturaDigitalVer.setClientHeight(286);
        FrmAssinaturaDigitalVer.setClientWidth(313);
        FrmAssinaturaDigitalVer.setColor("clBtnFace");
        FrmAssinaturaDigitalVer.setWOrigem("EhMain");
        FrmAssinaturaDigitalVer.setWKey("45509");
        FrmAssinaturaDigitalVer.setSpacing(0);
        FrmAssinaturaDigitalVer.applyProperties();
    }

    public TFVBox vboxPrincipal = new TFVBox();

    private void init_vboxPrincipal() {
        vboxPrincipal.setName("vboxPrincipal");
        vboxPrincipal.setLeft(0);
        vboxPrincipal.setTop(0);
        vboxPrincipal.setWidth(313);
        vboxPrincipal.setHeight(286);
        vboxPrincipal.setAlign("alClient");
        vboxPrincipal.setBorderStyle("stNone");
        vboxPrincipal.setPaddingTop(5);
        vboxPrincipal.setPaddingLeft(5);
        vboxPrincipal.setPaddingRight(5);
        vboxPrincipal.setPaddingBottom(5);
        vboxPrincipal.setMarginTop(0);
        vboxPrincipal.setMarginLeft(0);
        vboxPrincipal.setMarginRight(0);
        vboxPrincipal.setMarginBottom(0);
        vboxPrincipal.setSpacing(5);
        vboxPrincipal.setFlexVflex("ftTrue");
        vboxPrincipal.setFlexHflex("ftTrue");
        vboxPrincipal.setScrollable(false);
        vboxPrincipal.setBoxShadowConfigHorizontalLength(10);
        vboxPrincipal.setBoxShadowConfigVerticalLength(10);
        vboxPrincipal.setBoxShadowConfigBlurRadius(5);
        vboxPrincipal.setBoxShadowConfigSpreadRadius(0);
        vboxPrincipal.setBoxShadowConfigShadowColor("clBlack");
        vboxPrincipal.setBoxShadowConfigOpacity(75);
        FrmAssinaturaDigitalVer.addChildren(vboxPrincipal);
        vboxPrincipal.applyProperties();
    }

    public TFHBox vboxQrCode = new TFHBox();

    private void init_vboxQrCode() {
        vboxQrCode.setName("vboxQrCode");
        vboxQrCode.setLeft(0);
        vboxQrCode.setTop(0);
        vboxQrCode.setWidth(305);
        vboxQrCode.setHeight(241);
        vboxQrCode.setAlign("alCustom");
        vboxQrCode.setBorderStyle("stNone");
        vboxQrCode.setPaddingTop(0);
        vboxQrCode.setPaddingLeft(0);
        vboxQrCode.setPaddingRight(0);
        vboxQrCode.setPaddingBottom(0);
        vboxQrCode.setMarginTop(0);
        vboxQrCode.setMarginLeft(0);
        vboxQrCode.setMarginRight(0);
        vboxQrCode.setMarginBottom(0);
        vboxQrCode.setSpacing(1);
        vboxQrCode.setFlexVflex("ftTrue");
        vboxQrCode.setFlexHflex("ftTrue");
        vboxQrCode.setScrollable(false);
        vboxQrCode.setBoxShadowConfigHorizontalLength(10);
        vboxQrCode.setBoxShadowConfigVerticalLength(10);
        vboxQrCode.setBoxShadowConfigBlurRadius(5);
        vboxQrCode.setBoxShadowConfigSpreadRadius(0);
        vboxQrCode.setBoxShadowConfigShadowColor("clBlack");
        vboxQrCode.setBoxShadowConfigOpacity(75);
        vboxQrCode.setVAlign("tvTop");
        vboxPrincipal.addChildren(vboxQrCode);
        vboxQrCode.applyProperties();
    }

    public TFHBox vboxQrCodeEspaco1 = new TFHBox();

    private void init_vboxQrCodeEspaco1() {
        vboxQrCodeEspaco1.setName("vboxQrCodeEspaco1");
        vboxQrCodeEspaco1.setLeft(0);
        vboxQrCodeEspaco1.setTop(0);
        vboxQrCodeEspaco1.setWidth(33);
        vboxQrCodeEspaco1.setHeight(41);
        vboxQrCodeEspaco1.setBorderStyle("stNone");
        vboxQrCodeEspaco1.setPaddingTop(0);
        vboxQrCodeEspaco1.setPaddingLeft(0);
        vboxQrCodeEspaco1.setPaddingRight(0);
        vboxQrCodeEspaco1.setPaddingBottom(0);
        vboxQrCodeEspaco1.setMarginTop(0);
        vboxQrCodeEspaco1.setMarginLeft(0);
        vboxQrCodeEspaco1.setMarginRight(0);
        vboxQrCodeEspaco1.setMarginBottom(0);
        vboxQrCodeEspaco1.setSpacing(1);
        vboxQrCodeEspaco1.setFlexVflex("ftFalse");
        vboxQrCodeEspaco1.setFlexHflex("ftTrue");
        vboxQrCodeEspaco1.setScrollable(false);
        vboxQrCodeEspaco1.setBoxShadowConfigHorizontalLength(10);
        vboxQrCodeEspaco1.setBoxShadowConfigVerticalLength(10);
        vboxQrCodeEspaco1.setBoxShadowConfigBlurRadius(5);
        vboxQrCodeEspaco1.setBoxShadowConfigSpreadRadius(0);
        vboxQrCodeEspaco1.setBoxShadowConfigShadowColor("clBlack");
        vboxQrCodeEspaco1.setBoxShadowConfigOpacity(75);
        vboxQrCodeEspaco1.setVAlign("tvTop");
        vboxQrCode.addChildren(vboxQrCodeEspaco1);
        vboxQrCodeEspaco1.applyProperties();
    }

    public TFImage imgQrCode = new TFImage();

    private void init_imgQrCode() {
        imgQrCode.setName("imgQrCode");
        imgQrCode.setLeft(33);
        imgQrCode.setTop(0);
        imgQrCode.setWidth(228);
        imgQrCode.setHeight(220);
        imgQrCode.setTable(tbSolicitacoesAssinaturas);
        imgQrCode.setFieldName("QR_CODE");
        imgQrCode.setBoxSize(0);
        imgQrCode.setGrayScaleOnDisable(false);
        imgQrCode.setFlexVflex("ftTrue");
        imgQrCode.setFlexHflex("ftFalse");
        imgQrCode.setImageId(4600449);
        vboxQrCode.addChildren(imgQrCode);
        imgQrCode.applyProperties();
    }

    public TFHBox vboxQrCodeEspaco2 = new TFHBox();

    private void init_vboxQrCodeEspaco2() {
        vboxQrCodeEspaco2.setName("vboxQrCodeEspaco2");
        vboxQrCodeEspaco2.setLeft(261);
        vboxQrCodeEspaco2.setTop(0);
        vboxQrCodeEspaco2.setWidth(33);
        vboxQrCodeEspaco2.setHeight(41);
        vboxQrCodeEspaco2.setBorderStyle("stNone");
        vboxQrCodeEspaco2.setPaddingTop(0);
        vboxQrCodeEspaco2.setPaddingLeft(0);
        vboxQrCodeEspaco2.setPaddingRight(0);
        vboxQrCodeEspaco2.setPaddingBottom(0);
        vboxQrCodeEspaco2.setMarginTop(0);
        vboxQrCodeEspaco2.setMarginLeft(0);
        vboxQrCodeEspaco2.setMarginRight(0);
        vboxQrCodeEspaco2.setMarginBottom(0);
        vboxQrCodeEspaco2.setSpacing(1);
        vboxQrCodeEspaco2.setFlexVflex("ftFalse");
        vboxQrCodeEspaco2.setFlexHflex("ftTrue");
        vboxQrCodeEspaco2.setScrollable(false);
        vboxQrCodeEspaco2.setBoxShadowConfigHorizontalLength(10);
        vboxQrCodeEspaco2.setBoxShadowConfigVerticalLength(10);
        vboxQrCodeEspaco2.setBoxShadowConfigBlurRadius(5);
        vboxQrCodeEspaco2.setBoxShadowConfigSpreadRadius(0);
        vboxQrCodeEspaco2.setBoxShadowConfigShadowColor("clBlack");
        vboxQrCodeEspaco2.setBoxShadowConfigOpacity(75);
        vboxQrCodeEspaco2.setVAlign("tvTop");
        vboxQrCode.addChildren(vboxQrCodeEspaco2);
        vboxQrCodeEspaco2.applyProperties();
    }

    public TFHBox hboxLblAssinar = new TFHBox();

    private void init_hboxLblAssinar() {
        hboxLblAssinar.setName("hboxLblAssinar");
        hboxLblAssinar.setLeft(0);
        hboxLblAssinar.setTop(242);
        hboxLblAssinar.setWidth(304);
        hboxLblAssinar.setHeight(29);
        hboxLblAssinar.setBorderStyle("stNone");
        hboxLblAssinar.setPaddingTop(0);
        hboxLblAssinar.setPaddingLeft(0);
        hboxLblAssinar.setPaddingRight(0);
        hboxLblAssinar.setPaddingBottom(0);
        hboxLblAssinar.setMarginTop(0);
        hboxLblAssinar.setMarginLeft(0);
        hboxLblAssinar.setMarginRight(0);
        hboxLblAssinar.setMarginBottom(0);
        hboxLblAssinar.setSpacing(8);
        hboxLblAssinar.setFlexVflex("ftFalse");
        hboxLblAssinar.setFlexHflex("ftTrue");
        hboxLblAssinar.setScrollable(false);
        hboxLblAssinar.setBoxShadowConfigHorizontalLength(10);
        hboxLblAssinar.setBoxShadowConfigVerticalLength(10);
        hboxLblAssinar.setBoxShadowConfigBlurRadius(5);
        hboxLblAssinar.setBoxShadowConfigSpreadRadius(0);
        hboxLblAssinar.setBoxShadowConfigShadowColor("clBlack");
        hboxLblAssinar.setBoxShadowConfigOpacity(75);
        hboxLblAssinar.setVAlign("tvTop");
        vboxPrincipal.addChildren(hboxLblAssinar);
        hboxLblAssinar.applyProperties();
    }

    public TFHBox hboxLblAssinarEspaco1 = new TFHBox();

    private void init_hboxLblAssinarEspaco1() {
        hboxLblAssinarEspaco1.setName("hboxLblAssinarEspaco1");
        hboxLblAssinarEspaco1.setLeft(0);
        hboxLblAssinarEspaco1.setTop(0);
        hboxLblAssinarEspaco1.setWidth(33);
        hboxLblAssinarEspaco1.setHeight(21);
        hboxLblAssinarEspaco1.setBorderStyle("stNone");
        hboxLblAssinarEspaco1.setPaddingTop(0);
        hboxLblAssinarEspaco1.setPaddingLeft(0);
        hboxLblAssinarEspaco1.setPaddingRight(0);
        hboxLblAssinarEspaco1.setPaddingBottom(0);
        hboxLblAssinarEspaco1.setMarginTop(0);
        hboxLblAssinarEspaco1.setMarginLeft(0);
        hboxLblAssinarEspaco1.setMarginRight(0);
        hboxLblAssinarEspaco1.setMarginBottom(0);
        hboxLblAssinarEspaco1.setSpacing(1);
        hboxLblAssinarEspaco1.setFlexVflex("ftFalse");
        hboxLblAssinarEspaco1.setFlexHflex("ftTrue");
        hboxLblAssinarEspaco1.setScrollable(false);
        hboxLblAssinarEspaco1.setBoxShadowConfigHorizontalLength(10);
        hboxLblAssinarEspaco1.setBoxShadowConfigVerticalLength(10);
        hboxLblAssinarEspaco1.setBoxShadowConfigBlurRadius(5);
        hboxLblAssinarEspaco1.setBoxShadowConfigSpreadRadius(0);
        hboxLblAssinarEspaco1.setBoxShadowConfigShadowColor("clBlack");
        hboxLblAssinarEspaco1.setBoxShadowConfigOpacity(75);
        hboxLblAssinarEspaco1.setVAlign("tvTop");
        hboxLblAssinar.addChildren(hboxLblAssinarEspaco1);
        hboxLblAssinarEspaco1.applyProperties();
    }

    public TFLabel lblAssinar = new TFLabel();

    private void init_lblAssinar() {
        lblAssinar.setName("lblAssinar");
        lblAssinar.setLeft(33);
        lblAssinar.setTop(0);
        lblAssinar.setWidth(149);
        lblAssinar.setHeight(19);
        lblAssinar.setCaption("Abrir Link Assinatura");
        lblAssinar.setFontColor("clWindowText");
        lblAssinar.setFontSize(-16);
        lblAssinar.setFontName("Tahoma");
        lblAssinar.setFontStyle("[]");
        lblAssinar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            lblAssinarClick(event);
            processarFlow("FrmAssinaturaDigitalVer", "lblAssinar", "OnClick");
        });
        lblAssinar.setHiperLink(true);
        lblAssinar.setVerticalAlignment("taVerticalCenter");
        lblAssinar.setWordBreak(false);
        hboxLblAssinar.addChildren(lblAssinar);
        lblAssinar.applyProperties();
    }

    public TFIconClass iconAssinar = new TFIconClass();

    private void init_iconAssinar() {
        iconAssinar.setName("iconAssinar");
        iconAssinar.setLeft(182);
        iconAssinar.setTop(0);
        iconAssinar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            iconStatusAguardandoAssinaturaClick(event);
            processarFlow("FrmAssinaturaDigitalVer", "iconAssinar", "OnClick");
        });
        iconAssinar.setIconClass("far fa-copy");
        iconAssinar.setSize(18);
        iconAssinar.setColor("clBlue");
        hboxLblAssinar.addChildren(iconAssinar);
        iconAssinar.applyProperties();
    }

    public TFHBox hboxLblAssinarEspaco2 = new TFHBox();

    private void init_hboxLblAssinarEspaco2() {
        hboxLblAssinarEspaco2.setName("hboxLblAssinarEspaco2");
        hboxLblAssinarEspaco2.setLeft(198);
        hboxLblAssinarEspaco2.setTop(0);
        hboxLblAssinarEspaco2.setWidth(37);
        hboxLblAssinarEspaco2.setHeight(21);
        hboxLblAssinarEspaco2.setBorderStyle("stNone");
        hboxLblAssinarEspaco2.setPaddingTop(0);
        hboxLblAssinarEspaco2.setPaddingLeft(0);
        hboxLblAssinarEspaco2.setPaddingRight(0);
        hboxLblAssinarEspaco2.setPaddingBottom(0);
        hboxLblAssinarEspaco2.setMarginTop(0);
        hboxLblAssinarEspaco2.setMarginLeft(0);
        hboxLblAssinarEspaco2.setMarginRight(0);
        hboxLblAssinarEspaco2.setMarginBottom(0);
        hboxLblAssinarEspaco2.setSpacing(1);
        hboxLblAssinarEspaco2.setFlexVflex("ftFalse");
        hboxLblAssinarEspaco2.setFlexHflex("ftTrue");
        hboxLblAssinarEspaco2.setScrollable(false);
        hboxLblAssinarEspaco2.setBoxShadowConfigHorizontalLength(10);
        hboxLblAssinarEspaco2.setBoxShadowConfigVerticalLength(10);
        hboxLblAssinarEspaco2.setBoxShadowConfigBlurRadius(5);
        hboxLblAssinarEspaco2.setBoxShadowConfigSpreadRadius(0);
        hboxLblAssinarEspaco2.setBoxShadowConfigShadowColor("clBlack");
        hboxLblAssinarEspaco2.setBoxShadowConfigOpacity(75);
        hboxLblAssinarEspaco2.setVAlign("tvTop");
        hboxLblAssinar.addChildren(hboxLblAssinarEspaco2);
        hboxLblAssinarEspaco2.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void lblAssinarClick(final Event<Object> event);

    public abstract void iconStatusAguardandoAssinaturaClick(final Event<Object> event);

}