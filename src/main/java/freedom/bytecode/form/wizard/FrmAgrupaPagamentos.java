package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmAgrupaPagamentos extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.AgrupaPagamentosRNA rn = null;

    public FrmAgrupaPagamentos() {
        try {
            rn = (freedom.bytecode.rn.AgrupaPagamentosRNA) getRN(freedom.bytecode.rn.wizard.AgrupaPagamentosRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbEmpresasUsuarios();
        init_tbLeadsEmpresasUsuarios();
        init_tbOrcPreNotasParaAgrupar();
        init_tbLeadsPgtoAgrupado();
        init_tbLeadsPgtoAgrupadoParc();
        init_tbLeadsPgtoAgrupadoParam();
        init_tbLeadsOrcNotasPgtoAgrupados();
        init_tbLeadsFormaPgtoAgrupado();
        init_FPopupMenuSelecaoOrcamentosPreNotas();
        init_mnSelecionarTodos();
        init_mnSelecionarNenhum();
        init_vBoxAgrupaPagamentos();
        init_vBoxTopoFitros();
        init_vBoxTopo();
        init_hBoxTopoBotoes();
        init_vBoxTopoBotoesSeparador();
        init_btnVoltar();
        init_hBoxTopoFiltros1();
        init_vBoxDiv1();
        init_vBoxEmpresa();
        init_vBoxDiv2();
        init_lblEmpresa();
        init_cbbComboEmpresa();
        init_vBoxDiv7();
        init_vBoxNrAgrupamento();
        init_vBoxDiv3();
        init_lblNrAgrupamento();
        init_edtNumeroAgrupamento();
        init_vBoxDiv8();
        init_vBoxDataInicial();
        init_vBoxDiv4();
        init_lblDataInicial();
        init_edtDataInicial();
        init_vBoxDiv9();
        init_vBoxDataFinal();
        init_vBoxDiv5();
        init_lblDataFinal();
        init_edtDataFinal();
        init_vBoxDiv6();
        init_hBoxTopoFiltros2();
        init_vBoxDiv10();
        init_vBoxCpfCnpj();
        init_lblCpfCnpj();
        init_edtCpfCnpj();
        init_vBoxDiv11();
        init_vBoxCliente();
        init_lblNomeCliente();
        init_edtNomeCliente();
        init_vBoxDiv12();
        init_vBoxBtnPesquisarCliente();
        init_vBoxDiv15();
        init_btnPesquisarCliente();
        init_vBoxDiv13();
        init_vBoxBtnPesquisar();
        init_vBoxDiv16();
        init_btnPesquisar();
        init_vBoxDiv14();
        init_pgcAgrupaPagamentos();
        init_tabOrcamentosPreNota();
        init_vBoxOrcamentosPreNota();
        init_hBoxBotoesOrcamentos();
        init_hBoxSelOrcPreNotas();
        init_separador1();
        init_lblSelOrcPreNotas();
        init_separador2();
        init_hBoxOrcPreNotasAgrupadas();
        init_separador3();
        init_lblOrcPreNotasAgrupadas();
        init_separador4();
        init_hBoxFormasPagamento();
        init_separador5();
        init_lblFormasPagamento();
        init_separador6();
        init_separadorTopo();
        init_pgcAgrupamentoDePagamentos();
        init_tabOrcPreNotas();
        init_vBoxOrcPreNotas();
        init_hBoxTopoOrcNotas();
        init_lblTituloOrcNotasAgrupadas();
        init_separador23();
        init_btnSelecao();
        init_separador22();
        init_btnAddOrcNotas();
        init_separador51();
        init_hBoxOrcPreNotasParaAgrupar();
        init_separador19();
        init_vBoxOrcPreNotasParaAgrupar();
        init_gridOrcPreNotasParaAgrupar();
        init_separador20();
        init_separador21();
        init_tabOrcPreNotasSelecionadas();
        init_vBoxOrcPreNotasSelecionadas();
        init_hBoxTopoOrcNotasAgrupadas();
        init_lblTitOrcNotasAgrupadas();
        init_separador49();
        init_btnRemoverTodosItens();
        init_separador24();
        init_btnDefinirFormasPagamento();
        init_separador25();
        init_hBoxOrcNotasSelecionadas();
        init_separador26();
        init_vBoxOrcNotasSelecionadas();
        init_gridOrcPreNotasSelecionadas();
        init_separador27();
        init_separador28();
        init_tabFormasDePagamento();
        init_vBoxFormasDePagamento();
        init_vBoxTopoFormasDePagamento();
        init_lblTituloParamAdiantamento();
        init_hBoxParametrosAdiantamento();
        init_separador30();
        init_vBoxTipoAdiantamento();
        init_lblTipoAdiantamento();
        init_edtTipoAdiantamento();
        init_separador31();
        init_vBoxNatRecDespAdiantamento();
        init_lblNatRecDespAdiantamento();
        init_edtNatRecDespAdiantamento();
        init_separador32();
        init_vBoxNatRecDespCartao();
        init_lblNatRecDespCartao();
        init_edtNatRecDespCartao();
        init_separador33();
        init_hBoxFormasPgtoParcelas();
        init_separador34();
        init_vBoxTotal();
        init_separador43();
        init_lblTotal();
        init_edtTotal();
        init_separador35();
        init_vBoxFormasPgto();
        init_separador44();
        init_lblFormasPgto();
        init_cbbFormasPgto();
        init_separador36();
        init_vBoxQtdParcelas();
        init_separador45();
        init_lblQtdParcelas();
        init_edtQtdParcelas();
        init_separador37();
        init_vBoxValor();
        init_separador46();
        init_lblValor();
        init_edtValor();
        init_vBoxAplicarFormasPgto();
        init_separador47();
        init_btnAplicarFormasPgto();
        init_separador38();
        init_vBoxRemoverFormasPgto();
        init_separador48();
        init_btnRemoverFormasPgto();
        init_separador39();
        init_vBoxConfirmarFormasPgto();
        init_separador50();
        init_btnConfirmarFormasPgto();
        init_separador52();
        init_hBoxFormasPgtoOrcNotas();
        init_separador40();
        init_vBoxFormasPgtoOrcNotas();
        init_gridFormasPgtoOrcNotas();
        init_separador41();
        init_separador42();
        init_tabPagamentosAgrupados();
        init_vBoxPagamentosAgrupados();
        init_hBoxBotoesPgtoAgrupadosTopo();
        init_separador7();
        init_btnExcluirPgto();
        init_separador8();
        init_btnFechamentoParcial();
        init_separador9();
        init_grpBoxOrcPrenotaAgrupadas();
        init_vBoxOrcPrenotaAgrupadas();
        init_hBoxMaisFiltros();
        init_separador10();
        init_lblMaisFiltros();
        init_btnMaisFiltros();
        init_separador11();
        init_separador12();
        init_hBoxOrcPrenotaAgrupadas();
        init_separador13();
        init_gridOrcPrenotaAgrupadas();
        init_separador14();
        init_vBoxFormasPagamentos();
        init_separador17();
        init_hBoxTituloFormasPgto();
        init_lblTituloFormasPgto();
        init_hBoxFormasPagamentos();
        init_separador15();
        init_gridFormasPagamentos();
        init_separador16();
        init_separador18();
        init_FrmAgrupaPagamentos();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios;

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios = rn.tbEmpresasUsuarios;
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("298020;29801");
        tbEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbEmpresasUsuarios, "tbEmpresasUsuarios");
        tbEmpresasUsuarios.applyProperties();
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios;

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios = rn.tbLeadsEmpresasUsuarios;
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.setWKey("298020;29802");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
        getTables().put(tbLeadsEmpresasUsuarios, "tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.applyProperties();
    }

    public PESQ_ORC_PARA_AGRUPAR tbOrcPreNotasParaAgrupar;

    private void init_tbOrcPreNotasParaAgrupar() {
        tbOrcPreNotasParaAgrupar = rn.tbOrcPreNotasParaAgrupar;
        tbOrcPreNotasParaAgrupar.setName("tbOrcPreNotasParaAgrupar");
        tbOrcPreNotasParaAgrupar.setMaxRowCount(200);
        tbOrcPreNotasParaAgrupar.setWKey("298020;29803");
        tbOrcPreNotasParaAgrupar.setRatioBatchSize(20);
        getTables().put(tbOrcPreNotasParaAgrupar, "tbOrcPreNotasParaAgrupar");
        tbOrcPreNotasParaAgrupar.applyProperties();
    }

    public LEADS_PGTO_AGRUPADO tbLeadsPgtoAgrupado;

    private void init_tbLeadsPgtoAgrupado() {
        tbLeadsPgtoAgrupado = rn.tbLeadsPgtoAgrupado;
        tbLeadsPgtoAgrupado.setName("tbLeadsPgtoAgrupado");
        tbLeadsPgtoAgrupado.setMaxRowCount(200);
        tbLeadsPgtoAgrupado.setWKey("298020;29804");
        tbLeadsPgtoAgrupado.setRatioBatchSize(20);
        getTables().put(tbLeadsPgtoAgrupado, "tbLeadsPgtoAgrupado");
        tbLeadsPgtoAgrupado.applyProperties();
    }

    public LEADS_PGTO_AGRUPADO_PARC tbLeadsPgtoAgrupadoParc;

    private void init_tbLeadsPgtoAgrupadoParc() {
        tbLeadsPgtoAgrupadoParc = rn.tbLeadsPgtoAgrupadoParc;
        tbLeadsPgtoAgrupadoParc.setName("tbLeadsPgtoAgrupadoParc");
        tbLeadsPgtoAgrupadoParc.setMaxRowCount(200);
        tbLeadsPgtoAgrupadoParc.setWKey("298020;29805");
        tbLeadsPgtoAgrupadoParc.setRatioBatchSize(20);
        getTables().put(tbLeadsPgtoAgrupadoParc, "tbLeadsPgtoAgrupadoParc");
        tbLeadsPgtoAgrupadoParc.applyProperties();
    }

    public LEADS_PGTO_AGRUPADO_PARAM tbLeadsPgtoAgrupadoParam;

    private void init_tbLeadsPgtoAgrupadoParam() {
        tbLeadsPgtoAgrupadoParam = rn.tbLeadsPgtoAgrupadoParam;
        tbLeadsPgtoAgrupadoParam.setName("tbLeadsPgtoAgrupadoParam");
        tbLeadsPgtoAgrupadoParam.setMaxRowCount(200);
        tbLeadsPgtoAgrupadoParam.setWKey("298020;29806");
        tbLeadsPgtoAgrupadoParam.setRatioBatchSize(20);
        getTables().put(tbLeadsPgtoAgrupadoParam, "tbLeadsPgtoAgrupadoParam");
        tbLeadsPgtoAgrupadoParam.applyProperties();
    }

    public LEADS_ORC_NOTAS_PGTO_AGRUPADOS tbLeadsOrcNotasPgtoAgrupados;

    private void init_tbLeadsOrcNotasPgtoAgrupados() {
        tbLeadsOrcNotasPgtoAgrupados = rn.tbLeadsOrcNotasPgtoAgrupados;
        tbLeadsOrcNotasPgtoAgrupados.setName("tbLeadsOrcNotasPgtoAgrupados");
        tbLeadsOrcNotasPgtoAgrupados.setMaxRowCount(200);
        tbLeadsOrcNotasPgtoAgrupados.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbLeadsOrcNotasPgtoAgrupadosAfterScroll(event);
            processarFlow("FrmAgrupaPagamentos", "tbLeadsOrcNotasPgtoAgrupados", "OnAfterScroll");
        });
        tbLeadsOrcNotasPgtoAgrupados.setWKey("298020;29807");
        tbLeadsOrcNotasPgtoAgrupados.setRatioBatchSize(20);
        getTables().put(tbLeadsOrcNotasPgtoAgrupados, "tbLeadsOrcNotasPgtoAgrupados");
        tbLeadsOrcNotasPgtoAgrupados.applyProperties();
    }

    public LEADS_FORMA_PGTO_AGRUPADO tbLeadsFormaPgtoAgrupado;

    private void init_tbLeadsFormaPgtoAgrupado() {
        tbLeadsFormaPgtoAgrupado = rn.tbLeadsFormaPgtoAgrupado;
        tbLeadsFormaPgtoAgrupado.setName("tbLeadsFormaPgtoAgrupado");
        tbLeadsFormaPgtoAgrupado.setMaxRowCount(200);
        tbLeadsFormaPgtoAgrupado.setWKey("298020;29808");
        tbLeadsFormaPgtoAgrupado.setRatioBatchSize(20);
        getTables().put(tbLeadsFormaPgtoAgrupado, "tbLeadsFormaPgtoAgrupado");
        tbLeadsFormaPgtoAgrupado.applyProperties();
    }

    public TFPopupMenu FPopupMenuSelecaoOrcamentosPreNotas = new TFPopupMenu();

    private void init_FPopupMenuSelecaoOrcamentosPreNotas() {
        FPopupMenuSelecaoOrcamentosPreNotas.setName("FPopupMenuSelecaoOrcamentosPreNotas");
        FrmAgrupaPagamentos.addChildren(FPopupMenuSelecaoOrcamentosPreNotas);
        FPopupMenuSelecaoOrcamentosPreNotas.applyProperties();
    }

    public TFMenuItem mnSelecionarTodos = new TFMenuItem();

    private void init_mnSelecionarTodos() {
        mnSelecionarTodos.setName("mnSelecionarTodos");
        mnSelecionarTodos.setCaption("Selecionar Todos");
        mnSelecionarTodos.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mnSelecionarTodosClick(event);
            processarFlow("FrmAgrupaPagamentos", "mnSelecionarTodos", "OnClick");
        });
        mnSelecionarTodos.setAccess(false);
        mnSelecionarTodos.setCheckmark(false);
        FPopupMenuSelecaoOrcamentosPreNotas.addChildren(mnSelecionarTodos);
        mnSelecionarTodos.applyProperties();
    }

    public TFMenuItem mnSelecionarNenhum = new TFMenuItem();

    private void init_mnSelecionarNenhum() {
        mnSelecionarNenhum.setName("mnSelecionarNenhum");
        mnSelecionarNenhum.setCaption("Selecionar Nenhum");
        mnSelecionarNenhum.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            mnSelecionarNenhumClick(event);
            processarFlow("FrmAgrupaPagamentos", "mnSelecionarNenhum", "OnClick");
        });
        mnSelecionarNenhum.setAccess(false);
        mnSelecionarNenhum.setCheckmark(false);
        FPopupMenuSelecaoOrcamentosPreNotas.addChildren(mnSelecionarNenhum);
        mnSelecionarNenhum.applyProperties();
    }

    protected TFForm FrmAgrupaPagamentos = this;
    private void init_FrmAgrupaPagamentos() {
        FrmAgrupaPagamentos.setName("FrmAgrupaPagamentos");
        FrmAgrupaPagamentos.setCaption("Agrupa Pagamentos");
        FrmAgrupaPagamentos.setClientHeight(652);
        FrmAgrupaPagamentos.setClientWidth(1021);
        FrmAgrupaPagamentos.setColor("clBtnFace");
        FrmAgrupaPagamentos.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmAgrupaPagamentos", "FrmAgrupaPagamentos", "OnCreate");
        });
        FrmAgrupaPagamentos.setWKey("298020");
        FrmAgrupaPagamentos.setSpacing(0);
        FrmAgrupaPagamentos.applyProperties();
    }

    public TFVBox vBoxAgrupaPagamentos = new TFVBox();

    private void init_vBoxAgrupaPagamentos() {
        vBoxAgrupaPagamentos.setName("vBoxAgrupaPagamentos");
        vBoxAgrupaPagamentos.setLeft(0);
        vBoxAgrupaPagamentos.setTop(0);
        vBoxAgrupaPagamentos.setWidth(1021);
        vBoxAgrupaPagamentos.setHeight(652);
        vBoxAgrupaPagamentos.setAlign("alClient");
        vBoxAgrupaPagamentos.setBorderStyle("stNone");
        vBoxAgrupaPagamentos.setPaddingTop(0);
        vBoxAgrupaPagamentos.setPaddingLeft(0);
        vBoxAgrupaPagamentos.setPaddingRight(0);
        vBoxAgrupaPagamentos.setPaddingBottom(0);
        vBoxAgrupaPagamentos.setMarginTop(0);
        vBoxAgrupaPagamentos.setMarginLeft(0);
        vBoxAgrupaPagamentos.setMarginRight(0);
        vBoxAgrupaPagamentos.setMarginBottom(0);
        vBoxAgrupaPagamentos.setSpacing(1);
        vBoxAgrupaPagamentos.setFlexVflex("ftTrue");
        vBoxAgrupaPagamentos.setFlexHflex("ftTrue");
        vBoxAgrupaPagamentos.setScrollable(true);
        vBoxAgrupaPagamentos.setBoxShadowConfigHorizontalLength(10);
        vBoxAgrupaPagamentos.setBoxShadowConfigVerticalLength(10);
        vBoxAgrupaPagamentos.setBoxShadowConfigBlurRadius(5);
        vBoxAgrupaPagamentos.setBoxShadowConfigSpreadRadius(0);
        vBoxAgrupaPagamentos.setBoxShadowConfigShadowColor("clBlack");
        vBoxAgrupaPagamentos.setBoxShadowConfigOpacity(75);
        FrmAgrupaPagamentos.addChildren(vBoxAgrupaPagamentos);
        vBoxAgrupaPagamentos.applyProperties();
    }

    public TFVBox vBoxTopoFitros = new TFVBox();

    private void init_vBoxTopoFitros() {
        vBoxTopoFitros.setName("vBoxTopoFitros");
        vBoxTopoFitros.setLeft(0);
        vBoxTopoFitros.setTop(0);
        vBoxTopoFitros.setWidth(1007);
        vBoxTopoFitros.setHeight(194);
        vBoxTopoFitros.setAlign("alTop");
        vBoxTopoFitros.setBorderStyle("stNone");
        vBoxTopoFitros.setPaddingTop(0);
        vBoxTopoFitros.setPaddingLeft(0);
        vBoxTopoFitros.setPaddingRight(0);
        vBoxTopoFitros.setPaddingBottom(0);
        vBoxTopoFitros.setMarginTop(0);
        vBoxTopoFitros.setMarginLeft(0);
        vBoxTopoFitros.setMarginRight(0);
        vBoxTopoFitros.setMarginBottom(0);
        vBoxTopoFitros.setSpacing(1);
        vBoxTopoFitros.setFlexVflex("ftFalse");
        vBoxTopoFitros.setFlexHflex("ftTrue");
        vBoxTopoFitros.setScrollable(false);
        vBoxTopoFitros.setBoxShadowConfigHorizontalLength(10);
        vBoxTopoFitros.setBoxShadowConfigVerticalLength(10);
        vBoxTopoFitros.setBoxShadowConfigBlurRadius(5);
        vBoxTopoFitros.setBoxShadowConfigSpreadRadius(0);
        vBoxTopoFitros.setBoxShadowConfigShadowColor("clBlack");
        vBoxTopoFitros.setBoxShadowConfigOpacity(75);
        vBoxAgrupaPagamentos.addChildren(vBoxTopoFitros);
        vBoxTopoFitros.applyProperties();
    }

    public TFVBox vBoxTopo = new TFVBox();

    private void init_vBoxTopo() {
        vBoxTopo.setName("vBoxTopo");
        vBoxTopo.setLeft(0);
        vBoxTopo.setTop(0);
        vBoxTopo.setWidth(101);
        vBoxTopo.setHeight(6);
        vBoxTopo.setBorderStyle("stNone");
        vBoxTopo.setPaddingTop(0);
        vBoxTopo.setPaddingLeft(0);
        vBoxTopo.setPaddingRight(0);
        vBoxTopo.setPaddingBottom(0);
        vBoxTopo.setMarginTop(0);
        vBoxTopo.setMarginLeft(0);
        vBoxTopo.setMarginRight(0);
        vBoxTopo.setMarginBottom(0);
        vBoxTopo.setSpacing(1);
        vBoxTopo.setFlexVflex("ftFalse");
        vBoxTopo.setFlexHflex("ftFalse");
        vBoxTopo.setScrollable(false);
        vBoxTopo.setBoxShadowConfigHorizontalLength(10);
        vBoxTopo.setBoxShadowConfigVerticalLength(10);
        vBoxTopo.setBoxShadowConfigBlurRadius(5);
        vBoxTopo.setBoxShadowConfigSpreadRadius(0);
        vBoxTopo.setBoxShadowConfigShadowColor("clBlack");
        vBoxTopo.setBoxShadowConfigOpacity(75);
        vBoxTopoFitros.addChildren(vBoxTopo);
        vBoxTopo.applyProperties();
    }

    public TFHBox hBoxTopoBotoes = new TFHBox();

    private void init_hBoxTopoBotoes() {
        hBoxTopoBotoes.setName("hBoxTopoBotoes");
        hBoxTopoBotoes.setLeft(0);
        hBoxTopoBotoes.setTop(7);
        hBoxTopoBotoes.setWidth(996);
        hBoxTopoBotoes.setHeight(57);
        hBoxTopoBotoes.setBorderStyle("stNone");
        hBoxTopoBotoes.setPaddingTop(0);
        hBoxTopoBotoes.setPaddingLeft(0);
        hBoxTopoBotoes.setPaddingRight(0);
        hBoxTopoBotoes.setPaddingBottom(0);
        hBoxTopoBotoes.setMarginTop(0);
        hBoxTopoBotoes.setMarginLeft(0);
        hBoxTopoBotoes.setMarginRight(0);
        hBoxTopoBotoes.setMarginBottom(0);
        hBoxTopoBotoes.setSpacing(1);
        hBoxTopoBotoes.setFlexVflex("ftFalse");
        hBoxTopoBotoes.setFlexHflex("ftFalse");
        hBoxTopoBotoes.setScrollable(false);
        hBoxTopoBotoes.setBoxShadowConfigHorizontalLength(10);
        hBoxTopoBotoes.setBoxShadowConfigVerticalLength(10);
        hBoxTopoBotoes.setBoxShadowConfigBlurRadius(5);
        hBoxTopoBotoes.setBoxShadowConfigSpreadRadius(0);
        hBoxTopoBotoes.setBoxShadowConfigShadowColor("clBlack");
        hBoxTopoBotoes.setBoxShadowConfigOpacity(75);
        hBoxTopoBotoes.setVAlign("tvTop");
        vBoxTopoFitros.addChildren(hBoxTopoBotoes);
        hBoxTopoBotoes.applyProperties();
    }

    public TFVBox vBoxTopoBotoesSeparador = new TFVBox();

    private void init_vBoxTopoBotoesSeparador() {
        vBoxTopoBotoesSeparador.setName("vBoxTopoBotoesSeparador");
        vBoxTopoBotoesSeparador.setLeft(0);
        vBoxTopoBotoesSeparador.setTop(0);
        vBoxTopoBotoesSeparador.setWidth(10);
        vBoxTopoBotoesSeparador.setHeight(52);
        vBoxTopoBotoesSeparador.setBorderStyle("stNone");
        vBoxTopoBotoesSeparador.setPaddingTop(0);
        vBoxTopoBotoesSeparador.setPaddingLeft(0);
        vBoxTopoBotoesSeparador.setPaddingRight(0);
        vBoxTopoBotoesSeparador.setPaddingBottom(0);
        vBoxTopoBotoesSeparador.setMarginTop(0);
        vBoxTopoBotoesSeparador.setMarginLeft(0);
        vBoxTopoBotoesSeparador.setMarginRight(0);
        vBoxTopoBotoesSeparador.setMarginBottom(0);
        vBoxTopoBotoesSeparador.setSpacing(1);
        vBoxTopoBotoesSeparador.setFlexVflex("ftFalse");
        vBoxTopoBotoesSeparador.setFlexHflex("ftFalse");
        vBoxTopoBotoesSeparador.setScrollable(false);
        vBoxTopoBotoesSeparador.setBoxShadowConfigHorizontalLength(10);
        vBoxTopoBotoesSeparador.setBoxShadowConfigVerticalLength(10);
        vBoxTopoBotoesSeparador.setBoxShadowConfigBlurRadius(5);
        vBoxTopoBotoesSeparador.setBoxShadowConfigSpreadRadius(0);
        vBoxTopoBotoesSeparador.setBoxShadowConfigShadowColor("clBlack");
        vBoxTopoBotoesSeparador.setBoxShadowConfigOpacity(75);
        hBoxTopoBotoes.addChildren(vBoxTopoBotoesSeparador);
        vBoxTopoBotoesSeparador.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(10);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(54);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnVoltar", "OnClick");
        });
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        hBoxTopoBotoes.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox hBoxTopoFiltros1 = new TFHBox();

    private void init_hBoxTopoFiltros1() {
        hBoxTopoFiltros1.setName("hBoxTopoFiltros1");
        hBoxTopoFiltros1.setLeft(0);
        hBoxTopoFiltros1.setTop(65);
        hBoxTopoFiltros1.setWidth(996);
        hBoxTopoFiltros1.setHeight(61);
        hBoxTopoFiltros1.setAlign("alTop");
        hBoxTopoFiltros1.setBorderStyle("stNone");
        hBoxTopoFiltros1.setPaddingTop(0);
        hBoxTopoFiltros1.setPaddingLeft(0);
        hBoxTopoFiltros1.setPaddingRight(0);
        hBoxTopoFiltros1.setPaddingBottom(0);
        hBoxTopoFiltros1.setMarginTop(0);
        hBoxTopoFiltros1.setMarginLeft(0);
        hBoxTopoFiltros1.setMarginRight(0);
        hBoxTopoFiltros1.setMarginBottom(0);
        hBoxTopoFiltros1.setSpacing(1);
        hBoxTopoFiltros1.setFlexVflex("ftFalse");
        hBoxTopoFiltros1.setFlexHflex("ftTrue");
        hBoxTopoFiltros1.setScrollable(false);
        hBoxTopoFiltros1.setBoxShadowConfigHorizontalLength(10);
        hBoxTopoFiltros1.setBoxShadowConfigVerticalLength(10);
        hBoxTopoFiltros1.setBoxShadowConfigBlurRadius(5);
        hBoxTopoFiltros1.setBoxShadowConfigSpreadRadius(0);
        hBoxTopoFiltros1.setBoxShadowConfigShadowColor("clBlack");
        hBoxTopoFiltros1.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.setVAlign("tvTop");
        vBoxTopoFitros.addChildren(hBoxTopoFiltros1);
        hBoxTopoFiltros1.applyProperties();
    }

    public TFVBox vBoxDiv1 = new TFVBox();

    private void init_vBoxDiv1() {
        vBoxDiv1.setName("vBoxDiv1");
        vBoxDiv1.setLeft(0);
        vBoxDiv1.setTop(0);
        vBoxDiv1.setWidth(10);
        vBoxDiv1.setHeight(56);
        vBoxDiv1.setBorderStyle("stNone");
        vBoxDiv1.setPaddingTop(0);
        vBoxDiv1.setPaddingLeft(0);
        vBoxDiv1.setPaddingRight(0);
        vBoxDiv1.setPaddingBottom(0);
        vBoxDiv1.setMarginTop(0);
        vBoxDiv1.setMarginLeft(0);
        vBoxDiv1.setMarginRight(0);
        vBoxDiv1.setMarginBottom(0);
        vBoxDiv1.setSpacing(1);
        vBoxDiv1.setFlexVflex("ftFalse");
        vBoxDiv1.setFlexHflex("ftFalse");
        vBoxDiv1.setScrollable(false);
        vBoxDiv1.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv1.setBoxShadowConfigVerticalLength(10);
        vBoxDiv1.setBoxShadowConfigBlurRadius(5);
        vBoxDiv1.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv1.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv1.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxDiv1);
        vBoxDiv1.applyProperties();
    }

    public TFVBox vBoxEmpresa = new TFVBox();

    private void init_vBoxEmpresa() {
        vBoxEmpresa.setName("vBoxEmpresa");
        vBoxEmpresa.setLeft(10);
        vBoxEmpresa.setTop(0);
        vBoxEmpresa.setWidth(168);
        vBoxEmpresa.setHeight(56);
        vBoxEmpresa.setBorderStyle("stNone");
        vBoxEmpresa.setPaddingTop(0);
        vBoxEmpresa.setPaddingLeft(0);
        vBoxEmpresa.setPaddingRight(0);
        vBoxEmpresa.setPaddingBottom(0);
        vBoxEmpresa.setMarginTop(0);
        vBoxEmpresa.setMarginLeft(0);
        vBoxEmpresa.setMarginRight(0);
        vBoxEmpresa.setMarginBottom(0);
        vBoxEmpresa.setSpacing(1);
        vBoxEmpresa.setFlexVflex("ftFalse");
        vBoxEmpresa.setFlexHflex("ftTrue");
        vBoxEmpresa.setScrollable(false);
        vBoxEmpresa.setBoxShadowConfigHorizontalLength(10);
        vBoxEmpresa.setBoxShadowConfigVerticalLength(10);
        vBoxEmpresa.setBoxShadowConfigBlurRadius(5);
        vBoxEmpresa.setBoxShadowConfigSpreadRadius(0);
        vBoxEmpresa.setBoxShadowConfigShadowColor("clBlack");
        vBoxEmpresa.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxEmpresa);
        vBoxEmpresa.applyProperties();
    }

    public TFVBox vBoxDiv2 = new TFVBox();

    private void init_vBoxDiv2() {
        vBoxDiv2.setName("vBoxDiv2");
        vBoxDiv2.setLeft(0);
        vBoxDiv2.setTop(0);
        vBoxDiv2.setWidth(118);
        vBoxDiv2.setHeight(8);
        vBoxDiv2.setBorderStyle("stNone");
        vBoxDiv2.setPaddingTop(0);
        vBoxDiv2.setPaddingLeft(0);
        vBoxDiv2.setPaddingRight(0);
        vBoxDiv2.setPaddingBottom(0);
        vBoxDiv2.setMarginTop(0);
        vBoxDiv2.setMarginLeft(0);
        vBoxDiv2.setMarginRight(0);
        vBoxDiv2.setMarginBottom(0);
        vBoxDiv2.setSpacing(1);
        vBoxDiv2.setFlexVflex("ftFalse");
        vBoxDiv2.setFlexHflex("ftFalse");
        vBoxDiv2.setScrollable(false);
        vBoxDiv2.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv2.setBoxShadowConfigVerticalLength(10);
        vBoxDiv2.setBoxShadowConfigBlurRadius(5);
        vBoxDiv2.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv2.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv2.setBoxShadowConfigOpacity(75);
        vBoxEmpresa.addChildren(vBoxDiv2);
        vBoxDiv2.applyProperties();
    }

    public TFLabel lblEmpresa = new TFLabel();

    private void init_lblEmpresa() {
        lblEmpresa.setName("lblEmpresa");
        lblEmpresa.setLeft(0);
        lblEmpresa.setTop(9);
        lblEmpresa.setWidth(45);
        lblEmpresa.setHeight(13);
        lblEmpresa.setCaption("Empresa:");
        lblEmpresa.setFontColor("clWindowText");
        lblEmpresa.setFontSize(-11);
        lblEmpresa.setFontName("Tahoma");
        lblEmpresa.setFontStyle("[]");
        lblEmpresa.setVerticalAlignment("taVerticalCenter");
        lblEmpresa.setWordBreak(false);
        vBoxEmpresa.addChildren(lblEmpresa);
        lblEmpresa.applyProperties();
    }

    public TFCombo cbbComboEmpresa = new TFCombo();

    private void init_cbbComboEmpresa() {
        cbbComboEmpresa.setName("cbbComboEmpresa");
        cbbComboEmpresa.setLeft(0);
        cbbComboEmpresa.setTop(23);
        cbbComboEmpresa.setWidth(161);
        cbbComboEmpresa.setHeight(21);
        cbbComboEmpresa.setHint("Empresas");
        cbbComboEmpresa.setLookupTable(tbLeadsEmpresasUsuarios);
        cbbComboEmpresa.setFieldName("COD_EMPRESA");
        cbbComboEmpresa.setLookupKey("COD_EMPRESA");
        cbbComboEmpresa.setLookupDesc("EMPRESA");
        cbbComboEmpresa.setFlex(true);
        cbbComboEmpresa.setReadOnly(true);
        cbbComboEmpresa.setRequired(false);
        cbbComboEmpresa.setPrompt("Selecione");
        cbbComboEmpresa.setConstraintCheckWhen("cwImmediate");
        cbbComboEmpresa.setConstraintCheckType("ctExpression");
        cbbComboEmpresa.setConstraintFocusOnError(false);
        cbbComboEmpresa.setConstraintEnableUI(true);
        cbbComboEmpresa.setConstraintEnabled(false);
        cbbComboEmpresa.setConstraintFormCheck(true);
        cbbComboEmpresa.setClearOnDelKey(true);
        cbbComboEmpresa.setUseClearButton(false);
        cbbComboEmpresa.setHideClearButtonOnNullValue(false);
        vBoxEmpresa.addChildren(cbbComboEmpresa);
        cbbComboEmpresa.applyProperties();
        addValidatable(cbbComboEmpresa);
    }

    public TFVBox vBoxDiv7 = new TFVBox();

    private void init_vBoxDiv7() {
        vBoxDiv7.setName("vBoxDiv7");
        vBoxDiv7.setLeft(178);
        vBoxDiv7.setTop(0);
        vBoxDiv7.setWidth(5);
        vBoxDiv7.setHeight(56);
        vBoxDiv7.setBorderStyle("stNone");
        vBoxDiv7.setPaddingTop(0);
        vBoxDiv7.setPaddingLeft(0);
        vBoxDiv7.setPaddingRight(0);
        vBoxDiv7.setPaddingBottom(0);
        vBoxDiv7.setMarginTop(0);
        vBoxDiv7.setMarginLeft(0);
        vBoxDiv7.setMarginRight(0);
        vBoxDiv7.setMarginBottom(0);
        vBoxDiv7.setSpacing(1);
        vBoxDiv7.setFlexVflex("ftFalse");
        vBoxDiv7.setFlexHflex("ftFalse");
        vBoxDiv7.setScrollable(false);
        vBoxDiv7.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv7.setBoxShadowConfigVerticalLength(10);
        vBoxDiv7.setBoxShadowConfigBlurRadius(5);
        vBoxDiv7.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv7.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv7.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxDiv7);
        vBoxDiv7.applyProperties();
    }

    public TFVBox vBoxNrAgrupamento = new TFVBox();

    private void init_vBoxNrAgrupamento() {
        vBoxNrAgrupamento.setName("vBoxNrAgrupamento");
        vBoxNrAgrupamento.setLeft(183);
        vBoxNrAgrupamento.setTop(0);
        vBoxNrAgrupamento.setWidth(108);
        vBoxNrAgrupamento.setHeight(56);
        vBoxNrAgrupamento.setBorderStyle("stNone");
        vBoxNrAgrupamento.setPaddingTop(0);
        vBoxNrAgrupamento.setPaddingLeft(0);
        vBoxNrAgrupamento.setPaddingRight(0);
        vBoxNrAgrupamento.setPaddingBottom(0);
        vBoxNrAgrupamento.setMarginTop(0);
        vBoxNrAgrupamento.setMarginLeft(0);
        vBoxNrAgrupamento.setMarginRight(0);
        vBoxNrAgrupamento.setMarginBottom(0);
        vBoxNrAgrupamento.setSpacing(1);
        vBoxNrAgrupamento.setFlexVflex("ftFalse");
        vBoxNrAgrupamento.setFlexHflex("ftTrue");
        vBoxNrAgrupamento.setScrollable(false);
        vBoxNrAgrupamento.setBoxShadowConfigHorizontalLength(10);
        vBoxNrAgrupamento.setBoxShadowConfigVerticalLength(10);
        vBoxNrAgrupamento.setBoxShadowConfigBlurRadius(5);
        vBoxNrAgrupamento.setBoxShadowConfigSpreadRadius(0);
        vBoxNrAgrupamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxNrAgrupamento.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxNrAgrupamento);
        vBoxNrAgrupamento.applyProperties();
    }

    public TFVBox vBoxDiv3 = new TFVBox();

    private void init_vBoxDiv3() {
        vBoxDiv3.setName("vBoxDiv3");
        vBoxDiv3.setLeft(0);
        vBoxDiv3.setTop(0);
        vBoxDiv3.setWidth(101);
        vBoxDiv3.setHeight(8);
        vBoxDiv3.setBorderStyle("stNone");
        vBoxDiv3.setPaddingTop(0);
        vBoxDiv3.setPaddingLeft(0);
        vBoxDiv3.setPaddingRight(0);
        vBoxDiv3.setPaddingBottom(0);
        vBoxDiv3.setMarginTop(0);
        vBoxDiv3.setMarginLeft(0);
        vBoxDiv3.setMarginRight(0);
        vBoxDiv3.setMarginBottom(0);
        vBoxDiv3.setSpacing(1);
        vBoxDiv3.setFlexVflex("ftFalse");
        vBoxDiv3.setFlexHflex("ftFalse");
        vBoxDiv3.setScrollable(false);
        vBoxDiv3.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv3.setBoxShadowConfigVerticalLength(10);
        vBoxDiv3.setBoxShadowConfigBlurRadius(5);
        vBoxDiv3.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv3.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv3.setBoxShadowConfigOpacity(75);
        vBoxNrAgrupamento.addChildren(vBoxDiv3);
        vBoxDiv3.applyProperties();
    }

    public TFLabel lblNrAgrupamento = new TFLabel();

    private void init_lblNrAgrupamento() {
        lblNrAgrupamento.setName("lblNrAgrupamento");
        lblNrAgrupamento.setLeft(0);
        lblNrAgrupamento.setTop(9);
        lblNrAgrupamento.setWidth(84);
        lblNrAgrupamento.setHeight(13);
        lblNrAgrupamento.setCaption("N\u00BA Agrupamento:");
        lblNrAgrupamento.setFontColor("clWindowText");
        lblNrAgrupamento.setFontSize(-11);
        lblNrAgrupamento.setFontName("Tahoma");
        lblNrAgrupamento.setFontStyle("[]");
        lblNrAgrupamento.setVerticalAlignment("taVerticalCenter");
        lblNrAgrupamento.setWordBreak(false);
        vBoxNrAgrupamento.addChildren(lblNrAgrupamento);
        lblNrAgrupamento.applyProperties();
    }

    public TFString edtNumeroAgrupamento = new TFString();

    private void init_edtNumeroAgrupamento() {
        edtNumeroAgrupamento.setName("edtNumeroAgrupamento");
        edtNumeroAgrupamento.setLeft(0);
        edtNumeroAgrupamento.setTop(23);
        edtNumeroAgrupamento.setWidth(93);
        edtNumeroAgrupamento.setHeight(24);
        edtNumeroAgrupamento.setFlex(true);
        edtNumeroAgrupamento.setRequired(false);
        edtNumeroAgrupamento.setConstraintCheckWhen("cwImmediate");
        edtNumeroAgrupamento.setConstraintCheckType("ctExpression");
        edtNumeroAgrupamento.setConstraintFocusOnError(false);
        edtNumeroAgrupamento.setConstraintEnableUI(true);
        edtNumeroAgrupamento.setConstraintEnabled(false);
        edtNumeroAgrupamento.setConstraintFormCheck(true);
        edtNumeroAgrupamento.setCharCase("ccNormal");
        edtNumeroAgrupamento.setPwd(false);
        edtNumeroAgrupamento.setMaxlength(0);
        edtNumeroAgrupamento.setFontColor("clWindowText");
        edtNumeroAgrupamento.setFontSize(-13);
        edtNumeroAgrupamento.setFontName("Tahoma");
        edtNumeroAgrupamento.setFontStyle("[]");
        edtNumeroAgrupamento.setSaveLiteralCharacter(false);
        edtNumeroAgrupamento.applyProperties();
        vBoxNrAgrupamento.addChildren(edtNumeroAgrupamento);
        addValidatable(edtNumeroAgrupamento);
    }

    public TFVBox vBoxDiv8 = new TFVBox();

    private void init_vBoxDiv8() {
        vBoxDiv8.setName("vBoxDiv8");
        vBoxDiv8.setLeft(291);
        vBoxDiv8.setTop(0);
        vBoxDiv8.setWidth(5);
        vBoxDiv8.setHeight(56);
        vBoxDiv8.setBorderStyle("stNone");
        vBoxDiv8.setPaddingTop(0);
        vBoxDiv8.setPaddingLeft(0);
        vBoxDiv8.setPaddingRight(0);
        vBoxDiv8.setPaddingBottom(0);
        vBoxDiv8.setMarginTop(0);
        vBoxDiv8.setMarginLeft(0);
        vBoxDiv8.setMarginRight(0);
        vBoxDiv8.setMarginBottom(0);
        vBoxDiv8.setSpacing(1);
        vBoxDiv8.setFlexVflex("ftFalse");
        vBoxDiv8.setFlexHflex("ftFalse");
        vBoxDiv8.setScrollable(false);
        vBoxDiv8.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv8.setBoxShadowConfigVerticalLength(10);
        vBoxDiv8.setBoxShadowConfigBlurRadius(5);
        vBoxDiv8.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv8.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv8.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxDiv8);
        vBoxDiv8.applyProperties();
    }

    public TFVBox vBoxDataInicial = new TFVBox();

    private void init_vBoxDataInicial() {
        vBoxDataInicial.setName("vBoxDataInicial");
        vBoxDataInicial.setLeft(296);
        vBoxDataInicial.setTop(0);
        vBoxDataInicial.setWidth(125);
        vBoxDataInicial.setHeight(56);
        vBoxDataInicial.setBorderStyle("stNone");
        vBoxDataInicial.setPaddingTop(0);
        vBoxDataInicial.setPaddingLeft(0);
        vBoxDataInicial.setPaddingRight(0);
        vBoxDataInicial.setPaddingBottom(0);
        vBoxDataInicial.setMarginTop(0);
        vBoxDataInicial.setMarginLeft(0);
        vBoxDataInicial.setMarginRight(0);
        vBoxDataInicial.setMarginBottom(0);
        vBoxDataInicial.setSpacing(1);
        vBoxDataInicial.setFlexVflex("ftFalse");
        vBoxDataInicial.setFlexHflex("ftFalse");
        vBoxDataInicial.setScrollable(false);
        vBoxDataInicial.setBoxShadowConfigHorizontalLength(10);
        vBoxDataInicial.setBoxShadowConfigVerticalLength(10);
        vBoxDataInicial.setBoxShadowConfigBlurRadius(5);
        vBoxDataInicial.setBoxShadowConfigSpreadRadius(0);
        vBoxDataInicial.setBoxShadowConfigShadowColor("clBlack");
        vBoxDataInicial.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxDataInicial);
        vBoxDataInicial.applyProperties();
    }

    public TFVBox vBoxDiv4 = new TFVBox();

    private void init_vBoxDiv4() {
        vBoxDiv4.setName("vBoxDiv4");
        vBoxDiv4.setLeft(0);
        vBoxDiv4.setTop(0);
        vBoxDiv4.setWidth(93);
        vBoxDiv4.setHeight(8);
        vBoxDiv4.setBorderStyle("stNone");
        vBoxDiv4.setPaddingTop(0);
        vBoxDiv4.setPaddingLeft(0);
        vBoxDiv4.setPaddingRight(0);
        vBoxDiv4.setPaddingBottom(0);
        vBoxDiv4.setMarginTop(0);
        vBoxDiv4.setMarginLeft(0);
        vBoxDiv4.setMarginRight(0);
        vBoxDiv4.setMarginBottom(0);
        vBoxDiv4.setSpacing(1);
        vBoxDiv4.setFlexVflex("ftFalse");
        vBoxDiv4.setFlexHflex("ftFalse");
        vBoxDiv4.setScrollable(false);
        vBoxDiv4.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv4.setBoxShadowConfigVerticalLength(10);
        vBoxDiv4.setBoxShadowConfigBlurRadius(5);
        vBoxDiv4.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv4.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv4.setBoxShadowConfigOpacity(75);
        vBoxDataInicial.addChildren(vBoxDiv4);
        vBoxDiv4.applyProperties();
    }

    public TFLabel lblDataInicial = new TFLabel();

    private void init_lblDataInicial() {
        lblDataInicial.setName("lblDataInicial");
        lblDataInicial.setLeft(0);
        lblDataInicial.setTop(9);
        lblDataInicial.setWidth(57);
        lblDataInicial.setHeight(13);
        lblDataInicial.setCaption("Data Inicial:");
        lblDataInicial.setFontColor("clWindowText");
        lblDataInicial.setFontSize(-11);
        lblDataInicial.setFontName("Tahoma");
        lblDataInicial.setFontStyle("[]");
        lblDataInicial.setVerticalAlignment("taVerticalCenter");
        lblDataInicial.setWordBreak(false);
        vBoxDataInicial.addChildren(lblDataInicial);
        lblDataInicial.applyProperties();
    }

    public TFDate edtDataInicial = new TFDate();

    private void init_edtDataInicial() {
        edtDataInicial.setName("edtDataInicial");
        edtDataInicial.setLeft(0);
        edtDataInicial.setTop(23);
        edtDataInicial.setWidth(120);
        edtDataInicial.setHeight(24);
        edtDataInicial.setFlex(true);
        edtDataInicial.setRequired(false);
        edtDataInicial.setConstraintCheckWhen("cwImmediate");
        edtDataInicial.setConstraintCheckType("ctExpression");
        edtDataInicial.setConstraintFocusOnError(false);
        edtDataInicial.setConstraintEnableUI(true);
        edtDataInicial.setConstraintEnabled(false);
        edtDataInicial.setConstraintFormCheck(true);
        edtDataInicial.setFormat("dd/MM/yyyy");
        edtDataInicial.setShowCheckBox(false);
        vBoxDataInicial.addChildren(edtDataInicial);
        edtDataInicial.applyProperties();
        addValidatable(edtDataInicial);
    }

    public TFVBox vBoxDiv9 = new TFVBox();

    private void init_vBoxDiv9() {
        vBoxDiv9.setName("vBoxDiv9");
        vBoxDiv9.setLeft(421);
        vBoxDiv9.setTop(0);
        vBoxDiv9.setWidth(5);
        vBoxDiv9.setHeight(56);
        vBoxDiv9.setBorderStyle("stNone");
        vBoxDiv9.setPaddingTop(0);
        vBoxDiv9.setPaddingLeft(0);
        vBoxDiv9.setPaddingRight(0);
        vBoxDiv9.setPaddingBottom(0);
        vBoxDiv9.setMarginTop(0);
        vBoxDiv9.setMarginLeft(0);
        vBoxDiv9.setMarginRight(0);
        vBoxDiv9.setMarginBottom(0);
        vBoxDiv9.setSpacing(1);
        vBoxDiv9.setFlexVflex("ftFalse");
        vBoxDiv9.setFlexHflex("ftFalse");
        vBoxDiv9.setScrollable(false);
        vBoxDiv9.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv9.setBoxShadowConfigVerticalLength(10);
        vBoxDiv9.setBoxShadowConfigBlurRadius(5);
        vBoxDiv9.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv9.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv9.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxDiv9);
        vBoxDiv9.applyProperties();
    }

    public TFVBox vBoxDataFinal = new TFVBox();

    private void init_vBoxDataFinal() {
        vBoxDataFinal.setName("vBoxDataFinal");
        vBoxDataFinal.setLeft(426);
        vBoxDataFinal.setTop(0);
        vBoxDataFinal.setWidth(125);
        vBoxDataFinal.setHeight(56);
        vBoxDataFinal.setBorderStyle("stNone");
        vBoxDataFinal.setPaddingTop(0);
        vBoxDataFinal.setPaddingLeft(0);
        vBoxDataFinal.setPaddingRight(0);
        vBoxDataFinal.setPaddingBottom(0);
        vBoxDataFinal.setMarginTop(0);
        vBoxDataFinal.setMarginLeft(0);
        vBoxDataFinal.setMarginRight(0);
        vBoxDataFinal.setMarginBottom(0);
        vBoxDataFinal.setSpacing(1);
        vBoxDataFinal.setFlexVflex("ftFalse");
        vBoxDataFinal.setFlexHflex("ftFalse");
        vBoxDataFinal.setScrollable(false);
        vBoxDataFinal.setBoxShadowConfigHorizontalLength(10);
        vBoxDataFinal.setBoxShadowConfigVerticalLength(10);
        vBoxDataFinal.setBoxShadowConfigBlurRadius(5);
        vBoxDataFinal.setBoxShadowConfigSpreadRadius(0);
        vBoxDataFinal.setBoxShadowConfigShadowColor("clBlack");
        vBoxDataFinal.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxDataFinal);
        vBoxDataFinal.applyProperties();
    }

    public TFVBox vBoxDiv5 = new TFVBox();

    private void init_vBoxDiv5() {
        vBoxDiv5.setName("vBoxDiv5");
        vBoxDiv5.setLeft(0);
        vBoxDiv5.setTop(0);
        vBoxDiv5.setWidth(98);
        vBoxDiv5.setHeight(8);
        vBoxDiv5.setBorderStyle("stNone");
        vBoxDiv5.setPaddingTop(0);
        vBoxDiv5.setPaddingLeft(0);
        vBoxDiv5.setPaddingRight(0);
        vBoxDiv5.setPaddingBottom(0);
        vBoxDiv5.setMarginTop(0);
        vBoxDiv5.setMarginLeft(0);
        vBoxDiv5.setMarginRight(0);
        vBoxDiv5.setMarginBottom(0);
        vBoxDiv5.setSpacing(1);
        vBoxDiv5.setFlexVflex("ftFalse");
        vBoxDiv5.setFlexHflex("ftFalse");
        vBoxDiv5.setScrollable(false);
        vBoxDiv5.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv5.setBoxShadowConfigVerticalLength(10);
        vBoxDiv5.setBoxShadowConfigBlurRadius(5);
        vBoxDiv5.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv5.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv5.setBoxShadowConfigOpacity(75);
        vBoxDataFinal.addChildren(vBoxDiv5);
        vBoxDiv5.applyProperties();
    }

    public TFLabel lblDataFinal = new TFLabel();

    private void init_lblDataFinal() {
        lblDataFinal.setName("lblDataFinal");
        lblDataFinal.setLeft(0);
        lblDataFinal.setTop(9);
        lblDataFinal.setWidth(52);
        lblDataFinal.setHeight(13);
        lblDataFinal.setCaption("Data Final:");
        lblDataFinal.setFontColor("clWindowText");
        lblDataFinal.setFontSize(-11);
        lblDataFinal.setFontName("Tahoma");
        lblDataFinal.setFontStyle("[]");
        lblDataFinal.setVerticalAlignment("taVerticalCenter");
        lblDataFinal.setWordBreak(false);
        vBoxDataFinal.addChildren(lblDataFinal);
        lblDataFinal.applyProperties();
    }

    public TFDate edtDataFinal = new TFDate();

    private void init_edtDataFinal() {
        edtDataFinal.setName("edtDataFinal");
        edtDataFinal.setLeft(0);
        edtDataFinal.setTop(23);
        edtDataFinal.setWidth(120);
        edtDataFinal.setHeight(24);
        edtDataFinal.setFlex(true);
        edtDataFinal.setRequired(false);
        edtDataFinal.setConstraintCheckWhen("cwImmediate");
        edtDataFinal.setConstraintCheckType("ctExpression");
        edtDataFinal.setConstraintFocusOnError(false);
        edtDataFinal.setConstraintEnableUI(true);
        edtDataFinal.setConstraintEnabled(false);
        edtDataFinal.setConstraintFormCheck(true);
        edtDataFinal.setFormat("dd/MM/yyyy");
        edtDataFinal.setShowCheckBox(false);
        vBoxDataFinal.addChildren(edtDataFinal);
        edtDataFinal.applyProperties();
        addValidatable(edtDataFinal);
    }

    public TFVBox vBoxDiv6 = new TFVBox();

    private void init_vBoxDiv6() {
        vBoxDiv6.setName("vBoxDiv6");
        vBoxDiv6.setLeft(551);
        vBoxDiv6.setTop(0);
        vBoxDiv6.setWidth(10);
        vBoxDiv6.setHeight(56);
        vBoxDiv6.setBorderStyle("stNone");
        vBoxDiv6.setPaddingTop(0);
        vBoxDiv6.setPaddingLeft(0);
        vBoxDiv6.setPaddingRight(0);
        vBoxDiv6.setPaddingBottom(0);
        vBoxDiv6.setMarginTop(0);
        vBoxDiv6.setMarginLeft(0);
        vBoxDiv6.setMarginRight(0);
        vBoxDiv6.setMarginBottom(0);
        vBoxDiv6.setSpacing(1);
        vBoxDiv6.setFlexVflex("ftFalse");
        vBoxDiv6.setFlexHflex("ftFalse");
        vBoxDiv6.setScrollable(false);
        vBoxDiv6.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv6.setBoxShadowConfigVerticalLength(10);
        vBoxDiv6.setBoxShadowConfigBlurRadius(5);
        vBoxDiv6.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv6.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv6.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros1.addChildren(vBoxDiv6);
        vBoxDiv6.applyProperties();
    }

    public TFHBox hBoxTopoFiltros2 = new TFHBox();

    private void init_hBoxTopoFiltros2() {
        hBoxTopoFiltros2.setName("hBoxTopoFiltros2");
        hBoxTopoFiltros2.setLeft(0);
        hBoxTopoFiltros2.setTop(127);
        hBoxTopoFiltros2.setWidth(996);
        hBoxTopoFiltros2.setHeight(56);
        hBoxTopoFiltros2.setBorderStyle("stNone");
        hBoxTopoFiltros2.setPaddingTop(0);
        hBoxTopoFiltros2.setPaddingLeft(0);
        hBoxTopoFiltros2.setPaddingRight(0);
        hBoxTopoFiltros2.setPaddingBottom(0);
        hBoxTopoFiltros2.setMarginTop(0);
        hBoxTopoFiltros2.setMarginLeft(0);
        hBoxTopoFiltros2.setMarginRight(0);
        hBoxTopoFiltros2.setMarginBottom(0);
        hBoxTopoFiltros2.setSpacing(1);
        hBoxTopoFiltros2.setFlexVflex("ftFalse");
        hBoxTopoFiltros2.setFlexHflex("ftTrue");
        hBoxTopoFiltros2.setScrollable(false);
        hBoxTopoFiltros2.setBoxShadowConfigHorizontalLength(10);
        hBoxTopoFiltros2.setBoxShadowConfigVerticalLength(10);
        hBoxTopoFiltros2.setBoxShadowConfigBlurRadius(5);
        hBoxTopoFiltros2.setBoxShadowConfigSpreadRadius(0);
        hBoxTopoFiltros2.setBoxShadowConfigShadowColor("clBlack");
        hBoxTopoFiltros2.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.setVAlign("tvTop");
        vBoxTopoFitros.addChildren(hBoxTopoFiltros2);
        hBoxTopoFiltros2.applyProperties();
    }

    public TFVBox vBoxDiv10 = new TFVBox();

    private void init_vBoxDiv10() {
        vBoxDiv10.setName("vBoxDiv10");
        vBoxDiv10.setLeft(0);
        vBoxDiv10.setTop(0);
        vBoxDiv10.setWidth(10);
        vBoxDiv10.setHeight(52);
        vBoxDiv10.setBorderStyle("stNone");
        vBoxDiv10.setPaddingTop(0);
        vBoxDiv10.setPaddingLeft(0);
        vBoxDiv10.setPaddingRight(0);
        vBoxDiv10.setPaddingBottom(0);
        vBoxDiv10.setMarginTop(0);
        vBoxDiv10.setMarginLeft(0);
        vBoxDiv10.setMarginRight(0);
        vBoxDiv10.setMarginBottom(0);
        vBoxDiv10.setSpacing(1);
        vBoxDiv10.setFlexVflex("ftFalse");
        vBoxDiv10.setFlexHflex("ftFalse");
        vBoxDiv10.setScrollable(false);
        vBoxDiv10.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv10.setBoxShadowConfigVerticalLength(10);
        vBoxDiv10.setBoxShadowConfigBlurRadius(5);
        vBoxDiv10.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv10.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv10.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxDiv10);
        vBoxDiv10.applyProperties();
    }

    public TFVBox vBoxCpfCnpj = new TFVBox();

    private void init_vBoxCpfCnpj() {
        vBoxCpfCnpj.setName("vBoxCpfCnpj");
        vBoxCpfCnpj.setLeft(10);
        vBoxCpfCnpj.setTop(0);
        vBoxCpfCnpj.setWidth(282);
        vBoxCpfCnpj.setHeight(52);
        vBoxCpfCnpj.setBorderStyle("stNone");
        vBoxCpfCnpj.setPaddingTop(0);
        vBoxCpfCnpj.setPaddingLeft(0);
        vBoxCpfCnpj.setPaddingRight(0);
        vBoxCpfCnpj.setPaddingBottom(0);
        vBoxCpfCnpj.setMarginTop(0);
        vBoxCpfCnpj.setMarginLeft(0);
        vBoxCpfCnpj.setMarginRight(0);
        vBoxCpfCnpj.setMarginBottom(0);
        vBoxCpfCnpj.setSpacing(1);
        vBoxCpfCnpj.setFlexVflex("ftFalse");
        vBoxCpfCnpj.setFlexHflex("ftTrue");
        vBoxCpfCnpj.setScrollable(false);
        vBoxCpfCnpj.setBoxShadowConfigHorizontalLength(10);
        vBoxCpfCnpj.setBoxShadowConfigVerticalLength(10);
        vBoxCpfCnpj.setBoxShadowConfigBlurRadius(5);
        vBoxCpfCnpj.setBoxShadowConfigSpreadRadius(0);
        vBoxCpfCnpj.setBoxShadowConfigShadowColor("clBlack");
        vBoxCpfCnpj.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxCpfCnpj);
        vBoxCpfCnpj.applyProperties();
    }

    public TFLabel lblCpfCnpj = new TFLabel();

    private void init_lblCpfCnpj() {
        lblCpfCnpj.setName("lblCpfCnpj");
        lblCpfCnpj.setLeft(0);
        lblCpfCnpj.setTop(0);
        lblCpfCnpj.setWidth(52);
        lblCpfCnpj.setHeight(13);
        lblCpfCnpj.setCaption("CPF/CNPJ:");
        lblCpfCnpj.setFontColor("clWindowText");
        lblCpfCnpj.setFontSize(-11);
        lblCpfCnpj.setFontName("Tahoma");
        lblCpfCnpj.setFontStyle("[]");
        lblCpfCnpj.setVerticalAlignment("taVerticalCenter");
        lblCpfCnpj.setWordBreak(false);
        vBoxCpfCnpj.addChildren(lblCpfCnpj);
        lblCpfCnpj.applyProperties();
    }

    public TFString edtCpfCnpj = new TFString();

    private void init_edtCpfCnpj() {
        edtCpfCnpj.setName("edtCpfCnpj");
        edtCpfCnpj.setLeft(0);
        edtCpfCnpj.setTop(14);
        edtCpfCnpj.setWidth(274);
        edtCpfCnpj.setHeight(24);
        edtCpfCnpj.setFlex(true);
        edtCpfCnpj.setRequired(false);
        edtCpfCnpj.setConstraintCheckWhen("cwImmediate");
        edtCpfCnpj.setConstraintCheckType("ctExpression");
        edtCpfCnpj.setConstraintFocusOnError(false);
        edtCpfCnpj.setConstraintEnableUI(true);
        edtCpfCnpj.setConstraintEnabled(false);
        edtCpfCnpj.setConstraintFormCheck(true);
        edtCpfCnpj.setCharCase("ccNormal");
        edtCpfCnpj.setPwd(false);
        edtCpfCnpj.setMaxlength(0);
        edtCpfCnpj.setFontColor("clWindowText");
        edtCpfCnpj.setFontSize(-13);
        edtCpfCnpj.setFontName("Tahoma");
        edtCpfCnpj.setFontStyle("[]");
        edtCpfCnpj.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCpfCnpjEnter(event);
            processarFlow("FrmAgrupaPagamentos", "edtCpfCnpj", "OnEnter");
        });
        edtCpfCnpj.addEventListener("onExit", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtCpfCnpjExit(event);
            processarFlow("FrmAgrupaPagamentos", "edtCpfCnpj", "OnExit");
        });
        edtCpfCnpj.setSaveLiteralCharacter(false);
        edtCpfCnpj.applyProperties();
        vBoxCpfCnpj.addChildren(edtCpfCnpj);
        addValidatable(edtCpfCnpj);
    }

    public TFVBox vBoxDiv11 = new TFVBox();

    private void init_vBoxDiv11() {
        vBoxDiv11.setName("vBoxDiv11");
        vBoxDiv11.setLeft(292);
        vBoxDiv11.setTop(0);
        vBoxDiv11.setWidth(5);
        vBoxDiv11.setHeight(50);
        vBoxDiv11.setBorderStyle("stNone");
        vBoxDiv11.setPaddingTop(0);
        vBoxDiv11.setPaddingLeft(0);
        vBoxDiv11.setPaddingRight(0);
        vBoxDiv11.setPaddingBottom(0);
        vBoxDiv11.setMarginTop(0);
        vBoxDiv11.setMarginLeft(0);
        vBoxDiv11.setMarginRight(0);
        vBoxDiv11.setMarginBottom(0);
        vBoxDiv11.setSpacing(1);
        vBoxDiv11.setFlexVflex("ftFalse");
        vBoxDiv11.setFlexHflex("ftFalse");
        vBoxDiv11.setScrollable(false);
        vBoxDiv11.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv11.setBoxShadowConfigVerticalLength(10);
        vBoxDiv11.setBoxShadowConfigBlurRadius(5);
        vBoxDiv11.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv11.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv11.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxDiv11);
        vBoxDiv11.applyProperties();
    }

    public TFVBox vBoxCliente = new TFVBox();

    private void init_vBoxCliente() {
        vBoxCliente.setName("vBoxCliente");
        vBoxCliente.setLeft(297);
        vBoxCliente.setTop(0);
        vBoxCliente.setWidth(168);
        vBoxCliente.setHeight(52);
        vBoxCliente.setBorderStyle("stNone");
        vBoxCliente.setPaddingTop(0);
        vBoxCliente.setPaddingLeft(0);
        vBoxCliente.setPaddingRight(0);
        vBoxCliente.setPaddingBottom(0);
        vBoxCliente.setMarginTop(0);
        vBoxCliente.setMarginLeft(0);
        vBoxCliente.setMarginRight(0);
        vBoxCliente.setMarginBottom(0);
        vBoxCliente.setSpacing(1);
        vBoxCliente.setFlexVflex("ftFalse");
        vBoxCliente.setFlexHflex("ftTrue");
        vBoxCliente.setScrollable(false);
        vBoxCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxCliente.setBoxShadowConfigVerticalLength(10);
        vBoxCliente.setBoxShadowConfigBlurRadius(5);
        vBoxCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxCliente.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxCliente);
        vBoxCliente.applyProperties();
    }

    public TFLabel lblNomeCliente = new TFLabel();

    private void init_lblNomeCliente() {
        lblNomeCliente.setName("lblNomeCliente");
        lblNomeCliente.setLeft(0);
        lblNomeCliente.setTop(0);
        lblNomeCliente.setWidth(37);
        lblNomeCliente.setHeight(13);
        lblNomeCliente.setCaption("Cliente:");
        lblNomeCliente.setFontColor("clWindowText");
        lblNomeCliente.setFontSize(-11);
        lblNomeCliente.setFontName("Tahoma");
        lblNomeCliente.setFontStyle("[]");
        lblNomeCliente.setVerticalAlignment("taVerticalCenter");
        lblNomeCliente.setWordBreak(false);
        vBoxCliente.addChildren(lblNomeCliente);
        lblNomeCliente.applyProperties();
    }

    public TFString edtNomeCliente = new TFString();

    private void init_edtNomeCliente() {
        edtNomeCliente.setName("edtNomeCliente");
        edtNomeCliente.setLeft(0);
        edtNomeCliente.setTop(14);
        edtNomeCliente.setWidth(160);
        edtNomeCliente.setHeight(24);
        edtNomeCliente.setFlex(true);
        edtNomeCliente.setRequired(false);
        edtNomeCliente.setConstraintCheckWhen("cwImmediate");
        edtNomeCliente.setConstraintCheckType("ctExpression");
        edtNomeCliente.setConstraintFocusOnError(false);
        edtNomeCliente.setConstraintEnableUI(true);
        edtNomeCliente.setConstraintEnabled(false);
        edtNomeCliente.setConstraintFormCheck(true);
        edtNomeCliente.setCharCase("ccNormal");
        edtNomeCliente.setPwd(false);
        edtNomeCliente.setMaxlength(0);
        edtNomeCliente.setEnabled(false);
        edtNomeCliente.setFontColor("clWindowText");
        edtNomeCliente.setFontSize(-13);
        edtNomeCliente.setFontName("Tahoma");
        edtNomeCliente.setFontStyle("[]");
        edtNomeCliente.setSaveLiteralCharacter(false);
        edtNomeCliente.applyProperties();
        vBoxCliente.addChildren(edtNomeCliente);
        addValidatable(edtNomeCliente);
    }

    public TFVBox vBoxDiv12 = new TFVBox();

    private void init_vBoxDiv12() {
        vBoxDiv12.setName("vBoxDiv12");
        vBoxDiv12.setLeft(465);
        vBoxDiv12.setTop(0);
        vBoxDiv12.setWidth(5);
        vBoxDiv12.setHeight(50);
        vBoxDiv12.setBorderStyle("stNone");
        vBoxDiv12.setPaddingTop(0);
        vBoxDiv12.setPaddingLeft(0);
        vBoxDiv12.setPaddingRight(0);
        vBoxDiv12.setPaddingBottom(0);
        vBoxDiv12.setMarginTop(0);
        vBoxDiv12.setMarginLeft(0);
        vBoxDiv12.setMarginRight(0);
        vBoxDiv12.setMarginBottom(0);
        vBoxDiv12.setSpacing(1);
        vBoxDiv12.setFlexVflex("ftFalse");
        vBoxDiv12.setFlexHflex("ftFalse");
        vBoxDiv12.setScrollable(false);
        vBoxDiv12.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv12.setBoxShadowConfigVerticalLength(10);
        vBoxDiv12.setBoxShadowConfigBlurRadius(5);
        vBoxDiv12.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv12.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv12.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxDiv12);
        vBoxDiv12.applyProperties();
    }

    public TFVBox vBoxBtnPesquisarCliente = new TFVBox();

    private void init_vBoxBtnPesquisarCliente() {
        vBoxBtnPesquisarCliente.setName("vBoxBtnPesquisarCliente");
        vBoxBtnPesquisarCliente.setLeft(470);
        vBoxBtnPesquisarCliente.setTop(0);
        vBoxBtnPesquisarCliente.setWidth(28);
        vBoxBtnPesquisarCliente.setHeight(52);
        vBoxBtnPesquisarCliente.setBorderStyle("stNone");
        vBoxBtnPesquisarCliente.setPaddingTop(0);
        vBoxBtnPesquisarCliente.setPaddingLeft(0);
        vBoxBtnPesquisarCliente.setPaddingRight(0);
        vBoxBtnPesquisarCliente.setPaddingBottom(0);
        vBoxBtnPesquisarCliente.setMarginTop(0);
        vBoxBtnPesquisarCliente.setMarginLeft(0);
        vBoxBtnPesquisarCliente.setMarginRight(0);
        vBoxBtnPesquisarCliente.setMarginBottom(0);
        vBoxBtnPesquisarCliente.setSpacing(1);
        vBoxBtnPesquisarCliente.setFlexVflex("ftFalse");
        vBoxBtnPesquisarCliente.setFlexHflex("ftFalse");
        vBoxBtnPesquisarCliente.setScrollable(false);
        vBoxBtnPesquisarCliente.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnPesquisarCliente.setBoxShadowConfigVerticalLength(10);
        vBoxBtnPesquisarCliente.setBoxShadowConfigBlurRadius(5);
        vBoxBtnPesquisarCliente.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnPesquisarCliente.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnPesquisarCliente.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxBtnPesquisarCliente);
        vBoxBtnPesquisarCliente.applyProperties();
    }

    public TFVBox vBoxDiv15 = new TFVBox();

    private void init_vBoxDiv15() {
        vBoxDiv15.setName("vBoxDiv15");
        vBoxDiv15.setLeft(0);
        vBoxDiv15.setTop(0);
        vBoxDiv15.setWidth(26);
        vBoxDiv15.setHeight(15);
        vBoxDiv15.setBorderStyle("stNone");
        vBoxDiv15.setPaddingTop(0);
        vBoxDiv15.setPaddingLeft(0);
        vBoxDiv15.setPaddingRight(0);
        vBoxDiv15.setPaddingBottom(0);
        vBoxDiv15.setMarginTop(0);
        vBoxDiv15.setMarginLeft(0);
        vBoxDiv15.setMarginRight(0);
        vBoxDiv15.setMarginBottom(0);
        vBoxDiv15.setSpacing(1);
        vBoxDiv15.setFlexVflex("ftFalse");
        vBoxDiv15.setFlexHflex("ftFalse");
        vBoxDiv15.setScrollable(false);
        vBoxDiv15.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv15.setBoxShadowConfigVerticalLength(10);
        vBoxDiv15.setBoxShadowConfigBlurRadius(5);
        vBoxDiv15.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv15.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv15.setBoxShadowConfigOpacity(75);
        vBoxBtnPesquisarCliente.addChildren(vBoxDiv15);
        vBoxDiv15.applyProperties();
    }

    public TFButton btnPesquisarCliente = new TFButton();

    private void init_btnPesquisarCliente() {
        btnPesquisarCliente.setName("btnPesquisarCliente");
        btnPesquisarCliente.setLeft(0);
        btnPesquisarCliente.setTop(16);
        btnPesquisarCliente.setWidth(26);
        btnPesquisarCliente.setHeight(28);
        btnPesquisarCliente.setHint("Pesquisa Cliente");
        btnPesquisarCliente.setFontColor("clWindowText");
        btnPesquisarCliente.setFontSize(-11);
        btnPesquisarCliente.setFontName("Tahoma");
        btnPesquisarCliente.setFontStyle("[]");
        btnPesquisarCliente.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClienteClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnPesquisarCliente", "OnClick");
        });
        btnPesquisarCliente.setImageId(0);
        btnPesquisarCliente.setColor("clBtnFace");
        btnPesquisarCliente.setAccess(false);
        btnPesquisarCliente.setIconClass("external-link");
        btnPesquisarCliente.setIconReverseDirection(false);
        vBoxBtnPesquisarCliente.addChildren(btnPesquisarCliente);
        btnPesquisarCliente.applyProperties();
    }

    public TFVBox vBoxDiv13 = new TFVBox();

    private void init_vBoxDiv13() {
        vBoxDiv13.setName("vBoxDiv13");
        vBoxDiv13.setLeft(498);
        vBoxDiv13.setTop(0);
        vBoxDiv13.setWidth(5);
        vBoxDiv13.setHeight(50);
        vBoxDiv13.setBorderStyle("stNone");
        vBoxDiv13.setPaddingTop(0);
        vBoxDiv13.setPaddingLeft(0);
        vBoxDiv13.setPaddingRight(0);
        vBoxDiv13.setPaddingBottom(0);
        vBoxDiv13.setMarginTop(0);
        vBoxDiv13.setMarginLeft(0);
        vBoxDiv13.setMarginRight(0);
        vBoxDiv13.setMarginBottom(0);
        vBoxDiv13.setSpacing(1);
        vBoxDiv13.setFlexVflex("ftFalse");
        vBoxDiv13.setFlexHflex("ftFalse");
        vBoxDiv13.setScrollable(false);
        vBoxDiv13.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv13.setBoxShadowConfigVerticalLength(10);
        vBoxDiv13.setBoxShadowConfigBlurRadius(5);
        vBoxDiv13.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv13.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv13.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxDiv13);
        vBoxDiv13.applyProperties();
    }

    public TFVBox vBoxBtnPesquisar = new TFVBox();

    private void init_vBoxBtnPesquisar() {
        vBoxBtnPesquisar.setName("vBoxBtnPesquisar");
        vBoxBtnPesquisar.setLeft(503);
        vBoxBtnPesquisar.setTop(0);
        vBoxBtnPesquisar.setWidth(86);
        vBoxBtnPesquisar.setHeight(52);
        vBoxBtnPesquisar.setBorderStyle("stNone");
        vBoxBtnPesquisar.setPaddingTop(0);
        vBoxBtnPesquisar.setPaddingLeft(0);
        vBoxBtnPesquisar.setPaddingRight(0);
        vBoxBtnPesquisar.setPaddingBottom(0);
        vBoxBtnPesquisar.setMarginTop(0);
        vBoxBtnPesquisar.setMarginLeft(0);
        vBoxBtnPesquisar.setMarginRight(0);
        vBoxBtnPesquisar.setMarginBottom(0);
        vBoxBtnPesquisar.setSpacing(1);
        vBoxBtnPesquisar.setFlexVflex("ftFalse");
        vBoxBtnPesquisar.setFlexHflex("ftFalse");
        vBoxBtnPesquisar.setScrollable(false);
        vBoxBtnPesquisar.setBoxShadowConfigHorizontalLength(10);
        vBoxBtnPesquisar.setBoxShadowConfigVerticalLength(10);
        vBoxBtnPesquisar.setBoxShadowConfigBlurRadius(5);
        vBoxBtnPesquisar.setBoxShadowConfigSpreadRadius(0);
        vBoxBtnPesquisar.setBoxShadowConfigShadowColor("clBlack");
        vBoxBtnPesquisar.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxBtnPesquisar);
        vBoxBtnPesquisar.applyProperties();
    }

    public TFVBox vBoxDiv16 = new TFVBox();

    private void init_vBoxDiv16() {
        vBoxDiv16.setName("vBoxDiv16");
        vBoxDiv16.setLeft(0);
        vBoxDiv16.setTop(0);
        vBoxDiv16.setWidth(57);
        vBoxDiv16.setHeight(15);
        vBoxDiv16.setBorderStyle("stNone");
        vBoxDiv16.setPaddingTop(0);
        vBoxDiv16.setPaddingLeft(0);
        vBoxDiv16.setPaddingRight(0);
        vBoxDiv16.setPaddingBottom(0);
        vBoxDiv16.setMarginTop(0);
        vBoxDiv16.setMarginLeft(0);
        vBoxDiv16.setMarginRight(0);
        vBoxDiv16.setMarginBottom(0);
        vBoxDiv16.setSpacing(1);
        vBoxDiv16.setFlexVflex("ftFalse");
        vBoxDiv16.setFlexHflex("ftFalse");
        vBoxDiv16.setScrollable(false);
        vBoxDiv16.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv16.setBoxShadowConfigVerticalLength(10);
        vBoxDiv16.setBoxShadowConfigBlurRadius(5);
        vBoxDiv16.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv16.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv16.setBoxShadowConfigOpacity(75);
        vBoxBtnPesquisar.addChildren(vBoxDiv16);
        vBoxDiv16.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(0);
        btnPesquisar.setTop(16);
        btnPesquisar.setWidth(82);
        btnPesquisar.setHeight(28);
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-11);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setImageId(0);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconClass("search");
        btnPesquisar.setIconReverseDirection(false);
        vBoxBtnPesquisar.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFVBox vBoxDiv14 = new TFVBox();

    private void init_vBoxDiv14() {
        vBoxDiv14.setName("vBoxDiv14");
        vBoxDiv14.setLeft(589);
        vBoxDiv14.setTop(0);
        vBoxDiv14.setWidth(10);
        vBoxDiv14.setHeight(52);
        vBoxDiv14.setBorderStyle("stNone");
        vBoxDiv14.setPaddingTop(0);
        vBoxDiv14.setPaddingLeft(0);
        vBoxDiv14.setPaddingRight(0);
        vBoxDiv14.setPaddingBottom(0);
        vBoxDiv14.setMarginTop(0);
        vBoxDiv14.setMarginLeft(0);
        vBoxDiv14.setMarginRight(0);
        vBoxDiv14.setMarginBottom(0);
        vBoxDiv14.setSpacing(1);
        vBoxDiv14.setFlexVflex("ftFalse");
        vBoxDiv14.setFlexHflex("ftFalse");
        vBoxDiv14.setScrollable(false);
        vBoxDiv14.setBoxShadowConfigHorizontalLength(10);
        vBoxDiv14.setBoxShadowConfigVerticalLength(10);
        vBoxDiv14.setBoxShadowConfigBlurRadius(5);
        vBoxDiv14.setBoxShadowConfigSpreadRadius(0);
        vBoxDiv14.setBoxShadowConfigShadowColor("clBlack");
        vBoxDiv14.setBoxShadowConfigOpacity(75);
        hBoxTopoFiltros2.addChildren(vBoxDiv14);
        vBoxDiv14.applyProperties();
    }

    public TFPageControl pgcAgrupaPagamentos = new TFPageControl();

    private void init_pgcAgrupaPagamentos() {
        pgcAgrupaPagamentos.setName("pgcAgrupaPagamentos");
        pgcAgrupaPagamentos.setLeft(0);
        pgcAgrupaPagamentos.setTop(195);
        pgcAgrupaPagamentos.setWidth(1007);
        pgcAgrupaPagamentos.setHeight(442);
        pgcAgrupaPagamentos.setAlign("alClient");
        pgcAgrupaPagamentos.setTabPosition("tpTop");
        pgcAgrupaPagamentos.setFlexVflex("ftTrue");
        pgcAgrupaPagamentos.setFlexHflex("ftTrue");
        pgcAgrupaPagamentos.setRenderStyle("rsTabbed");
        pgcAgrupaPagamentos.applyProperties();
        vBoxAgrupaPagamentos.addChildren(pgcAgrupaPagamentos);
    }

    public TFTabsheet tabOrcamentosPreNota = new TFTabsheet();

    private void init_tabOrcamentosPreNota() {
        tabOrcamentosPreNota.setName("tabOrcamentosPreNota");
        tabOrcamentosPreNota.setCaption("Or\u00E7amentos/Pr\u00E9-Notas");
        tabOrcamentosPreNota.setFontColor("clWindowText");
        tabOrcamentosPreNota.setFontSize(-12);
        tabOrcamentosPreNota.setFontName("Tahoma");
        tabOrcamentosPreNota.setFontStyle("[]");
        tabOrcamentosPreNota.setVisible(true);
        tabOrcamentosPreNota.setClosable(false);
        pgcAgrupaPagamentos.addChildren(tabOrcamentosPreNota);
        tabOrcamentosPreNota.applyProperties();
    }

    public TFVBox vBoxOrcamentosPreNota = new TFVBox();

    private void init_vBoxOrcamentosPreNota() {
        vBoxOrcamentosPreNota.setName("vBoxOrcamentosPreNota");
        vBoxOrcamentosPreNota.setLeft(0);
        vBoxOrcamentosPreNota.setTop(0);
        vBoxOrcamentosPreNota.setWidth(999);
        vBoxOrcamentosPreNota.setHeight(414);
        vBoxOrcamentosPreNota.setAlign("alClient");
        vBoxOrcamentosPreNota.setBorderStyle("stNone");
        vBoxOrcamentosPreNota.setPaddingTop(8);
        vBoxOrcamentosPreNota.setPaddingLeft(0);
        vBoxOrcamentosPreNota.setPaddingRight(0);
        vBoxOrcamentosPreNota.setPaddingBottom(0);
        vBoxOrcamentosPreNota.setMarginTop(0);
        vBoxOrcamentosPreNota.setMarginLeft(0);
        vBoxOrcamentosPreNota.setMarginRight(0);
        vBoxOrcamentosPreNota.setMarginBottom(0);
        vBoxOrcamentosPreNota.setSpacing(1);
        vBoxOrcamentosPreNota.setFlexVflex("ftTrue");
        vBoxOrcamentosPreNota.setFlexHflex("ftTrue");
        vBoxOrcamentosPreNota.setScrollable(true);
        vBoxOrcamentosPreNota.setBoxShadowConfigHorizontalLength(10);
        vBoxOrcamentosPreNota.setBoxShadowConfigVerticalLength(10);
        vBoxOrcamentosPreNota.setBoxShadowConfigBlurRadius(5);
        vBoxOrcamentosPreNota.setBoxShadowConfigSpreadRadius(0);
        vBoxOrcamentosPreNota.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrcamentosPreNota.setBoxShadowConfigOpacity(75);
        tabOrcamentosPreNota.addChildren(vBoxOrcamentosPreNota);
        vBoxOrcamentosPreNota.applyProperties();
    }

    public TFHBox hBoxBotoesOrcamentos = new TFHBox();

    private void init_hBoxBotoesOrcamentos() {
        hBoxBotoesOrcamentos.setName("hBoxBotoesOrcamentos");
        hBoxBotoesOrcamentos.setLeft(0);
        hBoxBotoesOrcamentos.setTop(0);
        hBoxBotoesOrcamentos.setWidth(977);
        hBoxBotoesOrcamentos.setHeight(26);
        hBoxBotoesOrcamentos.setBorderStyle("stNone");
        hBoxBotoesOrcamentos.setPaddingTop(0);
        hBoxBotoesOrcamentos.setPaddingLeft(5);
        hBoxBotoesOrcamentos.setPaddingRight(5);
        hBoxBotoesOrcamentos.setPaddingBottom(0);
        hBoxBotoesOrcamentos.setMarginTop(0);
        hBoxBotoesOrcamentos.setMarginLeft(0);
        hBoxBotoesOrcamentos.setMarginRight(0);
        hBoxBotoesOrcamentos.setMarginBottom(0);
        hBoxBotoesOrcamentos.setSpacing(1);
        hBoxBotoesOrcamentos.setFlexVflex("ftFalse");
        hBoxBotoesOrcamentos.setFlexHflex("ftTrue");
        hBoxBotoesOrcamentos.setScrollable(false);
        hBoxBotoesOrcamentos.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesOrcamentos.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesOrcamentos.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesOrcamentos.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesOrcamentos.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesOrcamentos.setBoxShadowConfigOpacity(75);
        hBoxBotoesOrcamentos.setVAlign("tvTop");
        vBoxOrcamentosPreNota.addChildren(hBoxBotoesOrcamentos);
        hBoxBotoesOrcamentos.applyProperties();
    }

    public TFHBox hBoxSelOrcPreNotas = new TFHBox();

    private void init_hBoxSelOrcPreNotas() {
        hBoxSelOrcPreNotas.setName("hBoxSelOrcPreNotas");
        hBoxSelOrcPreNotas.setLeft(0);
        hBoxSelOrcPreNotas.setTop(0);
        hBoxSelOrcPreNotas.setWidth(154);
        hBoxSelOrcPreNotas.setHeight(22);
        hBoxSelOrcPreNotas.setBorderStyle("stRaised");
        hBoxSelOrcPreNotas.setPaddingTop(0);
        hBoxSelOrcPreNotas.setPaddingLeft(0);
        hBoxSelOrcPreNotas.setPaddingRight(0);
        hBoxSelOrcPreNotas.setPaddingBottom(0);
        hBoxSelOrcPreNotas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxSelOrcPreNotasClick(event);
            processarFlow("FrmAgrupaPagamentos", "hBoxSelOrcPreNotas", "OnClick");
        });
        hBoxSelOrcPreNotas.setMarginTop(0);
        hBoxSelOrcPreNotas.setMarginLeft(0);
        hBoxSelOrcPreNotas.setMarginRight(0);
        hBoxSelOrcPreNotas.setMarginBottom(0);
        hBoxSelOrcPreNotas.setSpacing(1);
        hBoxSelOrcPreNotas.setFlexVflex("ftFalse");
        hBoxSelOrcPreNotas.setFlexHflex("ftTrue");
        hBoxSelOrcPreNotas.setScrollable(false);
        hBoxSelOrcPreNotas.setBoxShadowConfigHorizontalLength(10);
        hBoxSelOrcPreNotas.setBoxShadowConfigVerticalLength(50);
        hBoxSelOrcPreNotas.setBoxShadowConfigBlurRadius(5);
        hBoxSelOrcPreNotas.setBoxShadowConfigSpreadRadius(5);
        hBoxSelOrcPreNotas.setBoxShadowConfigShadowColor("clBlack");
        hBoxSelOrcPreNotas.setBoxShadowConfigOpacity(80);
        hBoxSelOrcPreNotas.setVAlign("tvMiddle");
        hBoxBotoesOrcamentos.addChildren(hBoxSelOrcPreNotas);
        hBoxSelOrcPreNotas.applyProperties();
    }

    public TFHBox separador1 = new TFHBox();

    private void init_separador1() {
        separador1.setName("separador1");
        separador1.setLeft(1);
        separador1.setTop(1);
        separador1.setWidth(10);
        separador1.setHeight(10);
        separador1.setBorderStyle("stNone");
        separador1.setPaddingTop(0);
        separador1.setPaddingLeft(0);
        separador1.setPaddingRight(0);
        separador1.setPaddingBottom(0);
        separador1.setMarginTop(0);
        separador1.setMarginLeft(0);
        separador1.setMarginRight(0);
        separador1.setMarginBottom(0);
        separador1.setSpacing(1);
        separador1.setFlexVflex("ftFalse");
        separador1.setFlexHflex("ftTrue");
        separador1.setScrollable(false);
        separador1.setBoxShadowConfigHorizontalLength(10);
        separador1.setBoxShadowConfigVerticalLength(10);
        separador1.setBoxShadowConfigBlurRadius(5);
        separador1.setBoxShadowConfigSpreadRadius(0);
        separador1.setBoxShadowConfigShadowColor("clBlack");
        separador1.setBoxShadowConfigOpacity(75);
        separador1.setVAlign("tvTop");
        hBoxSelOrcPreNotas.addChildren(separador1);
        separador1.applyProperties();
    }

    public TFLabel lblSelOrcPreNotas = new TFLabel();

    private void init_lblSelOrcPreNotas() {
        lblSelOrcPreNotas.setName("lblSelOrcPreNotas");
        lblSelOrcPreNotas.setLeft(11);
        lblSelOrcPreNotas.setTop(1);
        lblSelOrcPreNotas.setWidth(120);
        lblSelOrcPreNotas.setHeight(13);
        lblSelOrcPreNotas.setCaption("Selecione os Or\u00E7amentos");
        lblSelOrcPreNotas.setFontColor("clWindowText");
        lblSelOrcPreNotas.setFontSize(-11);
        lblSelOrcPreNotas.setFontName("Tahoma");
        lblSelOrcPreNotas.setFontStyle("[]");
        lblSelOrcPreNotas.setVerticalAlignment("taVerticalCenter");
        lblSelOrcPreNotas.setWordBreak(false);
        hBoxSelOrcPreNotas.addChildren(lblSelOrcPreNotas);
        lblSelOrcPreNotas.applyProperties();
    }

    public TFHBox separador2 = new TFHBox();

    private void init_separador2() {
        separador2.setName("separador2");
        separador2.setLeft(131);
        separador2.setTop(1);
        separador2.setWidth(10);
        separador2.setHeight(10);
        separador2.setBorderStyle("stNone");
        separador2.setPaddingTop(0);
        separador2.setPaddingLeft(0);
        separador2.setPaddingRight(0);
        separador2.setPaddingBottom(0);
        separador2.setMarginTop(0);
        separador2.setMarginLeft(0);
        separador2.setMarginRight(0);
        separador2.setMarginBottom(0);
        separador2.setSpacing(1);
        separador2.setFlexVflex("ftFalse");
        separador2.setFlexHflex("ftTrue");
        separador2.setScrollable(false);
        separador2.setBoxShadowConfigHorizontalLength(10);
        separador2.setBoxShadowConfigVerticalLength(10);
        separador2.setBoxShadowConfigBlurRadius(5);
        separador2.setBoxShadowConfigSpreadRadius(0);
        separador2.setBoxShadowConfigShadowColor("clBlack");
        separador2.setBoxShadowConfigOpacity(75);
        separador2.setVAlign("tvTop");
        hBoxSelOrcPreNotas.addChildren(separador2);
        separador2.applyProperties();
    }

    public TFHBox hBoxOrcPreNotasAgrupadas = new TFHBox();

    private void init_hBoxOrcPreNotasAgrupadas() {
        hBoxOrcPreNotasAgrupadas.setName("hBoxOrcPreNotasAgrupadas");
        hBoxOrcPreNotasAgrupadas.setLeft(154);
        hBoxOrcPreNotasAgrupadas.setTop(0);
        hBoxOrcPreNotasAgrupadas.setWidth(154);
        hBoxOrcPreNotasAgrupadas.setHeight(22);
        hBoxOrcPreNotasAgrupadas.setBorderStyle("stRaised");
        hBoxOrcPreNotasAgrupadas.setPaddingTop(0);
        hBoxOrcPreNotasAgrupadas.setPaddingLeft(0);
        hBoxOrcPreNotasAgrupadas.setPaddingRight(0);
        hBoxOrcPreNotasAgrupadas.setPaddingBottom(0);
        hBoxOrcPreNotasAgrupadas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxOrcPreNotasAgrupadasClick(event);
            processarFlow("FrmAgrupaPagamentos", "hBoxOrcPreNotasAgrupadas", "OnClick");
        });
        hBoxOrcPreNotasAgrupadas.setMarginTop(0);
        hBoxOrcPreNotasAgrupadas.setMarginLeft(0);
        hBoxOrcPreNotasAgrupadas.setMarginRight(0);
        hBoxOrcPreNotasAgrupadas.setMarginBottom(0);
        hBoxOrcPreNotasAgrupadas.setSpacing(1);
        hBoxOrcPreNotasAgrupadas.setFlexVflex("ftFalse");
        hBoxOrcPreNotasAgrupadas.setFlexHflex("ftTrue");
        hBoxOrcPreNotasAgrupadas.setScrollable(false);
        hBoxOrcPreNotasAgrupadas.setBoxShadowConfigHorizontalLength(10);
        hBoxOrcPreNotasAgrupadas.setBoxShadowConfigVerticalLength(10);
        hBoxOrcPreNotasAgrupadas.setBoxShadowConfigBlurRadius(5);
        hBoxOrcPreNotasAgrupadas.setBoxShadowConfigSpreadRadius(0);
        hBoxOrcPreNotasAgrupadas.setBoxShadowConfigShadowColor("clBlack");
        hBoxOrcPreNotasAgrupadas.setBoxShadowConfigOpacity(75);
        hBoxOrcPreNotasAgrupadas.setVAlign("tvMiddle");
        hBoxBotoesOrcamentos.addChildren(hBoxOrcPreNotasAgrupadas);
        hBoxOrcPreNotasAgrupadas.applyProperties();
    }

    public TFHBox separador3 = new TFHBox();

    private void init_separador3() {
        separador3.setName("separador3");
        separador3.setLeft(1);
        separador3.setTop(1);
        separador3.setWidth(10);
        separador3.setHeight(10);
        separador3.setBorderStyle("stNone");
        separador3.setPaddingTop(0);
        separador3.setPaddingLeft(0);
        separador3.setPaddingRight(0);
        separador3.setPaddingBottom(0);
        separador3.setMarginTop(0);
        separador3.setMarginLeft(0);
        separador3.setMarginRight(0);
        separador3.setMarginBottom(0);
        separador3.setSpacing(1);
        separador3.setFlexVflex("ftFalse");
        separador3.setFlexHflex("ftTrue");
        separador3.setScrollable(false);
        separador3.setBoxShadowConfigHorizontalLength(10);
        separador3.setBoxShadowConfigVerticalLength(10);
        separador3.setBoxShadowConfigBlurRadius(5);
        separador3.setBoxShadowConfigSpreadRadius(0);
        separador3.setBoxShadowConfigShadowColor("clBlack");
        separador3.setBoxShadowConfigOpacity(75);
        separador3.setVAlign("tvTop");
        hBoxOrcPreNotasAgrupadas.addChildren(separador3);
        separador3.applyProperties();
    }

    public TFLabel lblOrcPreNotasAgrupadas = new TFLabel();

    private void init_lblOrcPreNotasAgrupadas() {
        lblOrcPreNotasAgrupadas.setName("lblOrcPreNotasAgrupadas");
        lblOrcPreNotasAgrupadas.setLeft(11);
        lblOrcPreNotasAgrupadas.setTop(1);
        lblOrcPreNotasAgrupadas.setWidth(123);
        lblOrcPreNotasAgrupadas.setHeight(13);
        lblOrcPreNotasAgrupadas.setCaption("Or\u00E7amentos Selecionados");
        lblOrcPreNotasAgrupadas.setFontColor("clWindowText");
        lblOrcPreNotasAgrupadas.setFontSize(-11);
        lblOrcPreNotasAgrupadas.setFontName("Tahoma");
        lblOrcPreNotasAgrupadas.setFontStyle("[]");
        lblOrcPreNotasAgrupadas.setVerticalAlignment("taVerticalCenter");
        lblOrcPreNotasAgrupadas.setWordBreak(false);
        hBoxOrcPreNotasAgrupadas.addChildren(lblOrcPreNotasAgrupadas);
        lblOrcPreNotasAgrupadas.applyProperties();
    }

    public TFHBox separador4 = new TFHBox();

    private void init_separador4() {
        separador4.setName("separador4");
        separador4.setLeft(134);
        separador4.setTop(1);
        separador4.setWidth(10);
        separador4.setHeight(10);
        separador4.setBorderStyle("stNone");
        separador4.setPaddingTop(0);
        separador4.setPaddingLeft(0);
        separador4.setPaddingRight(0);
        separador4.setPaddingBottom(0);
        separador4.setMarginTop(0);
        separador4.setMarginLeft(0);
        separador4.setMarginRight(0);
        separador4.setMarginBottom(0);
        separador4.setSpacing(1);
        separador4.setFlexVflex("ftFalse");
        separador4.setFlexHflex("ftTrue");
        separador4.setScrollable(false);
        separador4.setBoxShadowConfigHorizontalLength(10);
        separador4.setBoxShadowConfigVerticalLength(10);
        separador4.setBoxShadowConfigBlurRadius(5);
        separador4.setBoxShadowConfigSpreadRadius(0);
        separador4.setBoxShadowConfigShadowColor("clBlack");
        separador4.setBoxShadowConfigOpacity(75);
        separador4.setVAlign("tvTop");
        hBoxOrcPreNotasAgrupadas.addChildren(separador4);
        separador4.applyProperties();
    }

    public TFHBox hBoxFormasPagamento = new TFHBox();

    private void init_hBoxFormasPagamento() {
        hBoxFormasPagamento.setName("hBoxFormasPagamento");
        hBoxFormasPagamento.setLeft(308);
        hBoxFormasPagamento.setTop(0);
        hBoxFormasPagamento.setWidth(140);
        hBoxFormasPagamento.setHeight(22);
        hBoxFormasPagamento.setBorderStyle("stRaised");
        hBoxFormasPagamento.setPaddingTop(0);
        hBoxFormasPagamento.setPaddingLeft(0);
        hBoxFormasPagamento.setPaddingRight(0);
        hBoxFormasPagamento.setPaddingBottom(0);
        hBoxFormasPagamento.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hBoxFormasPagamentoClick(event);
            processarFlow("FrmAgrupaPagamentos", "hBoxFormasPagamento", "OnClick");
        });
        hBoxFormasPagamento.setMarginTop(0);
        hBoxFormasPagamento.setMarginLeft(0);
        hBoxFormasPagamento.setMarginRight(0);
        hBoxFormasPagamento.setMarginBottom(0);
        hBoxFormasPagamento.setSpacing(1);
        hBoxFormasPagamento.setFlexVflex("ftFalse");
        hBoxFormasPagamento.setFlexHflex("ftTrue");
        hBoxFormasPagamento.setScrollable(false);
        hBoxFormasPagamento.setBoxShadowConfigHorizontalLength(10);
        hBoxFormasPagamento.setBoxShadowConfigVerticalLength(10);
        hBoxFormasPagamento.setBoxShadowConfigBlurRadius(5);
        hBoxFormasPagamento.setBoxShadowConfigSpreadRadius(0);
        hBoxFormasPagamento.setBoxShadowConfigShadowColor("clBlack");
        hBoxFormasPagamento.setBoxShadowConfigOpacity(75);
        hBoxFormasPagamento.setVAlign("tvMiddle");
        hBoxBotoesOrcamentos.addChildren(hBoxFormasPagamento);
        hBoxFormasPagamento.applyProperties();
    }

    public TFHBox separador5 = new TFHBox();

    private void init_separador5() {
        separador5.setName("separador5");
        separador5.setLeft(1);
        separador5.setTop(1);
        separador5.setWidth(10);
        separador5.setHeight(10);
        separador5.setBorderStyle("stNone");
        separador5.setPaddingTop(0);
        separador5.setPaddingLeft(0);
        separador5.setPaddingRight(0);
        separador5.setPaddingBottom(0);
        separador5.setMarginTop(0);
        separador5.setMarginLeft(0);
        separador5.setMarginRight(0);
        separador5.setMarginBottom(0);
        separador5.setSpacing(1);
        separador5.setFlexVflex("ftFalse");
        separador5.setFlexHflex("ftTrue");
        separador5.setScrollable(false);
        separador5.setBoxShadowConfigHorizontalLength(10);
        separador5.setBoxShadowConfigVerticalLength(10);
        separador5.setBoxShadowConfigBlurRadius(5);
        separador5.setBoxShadowConfigSpreadRadius(0);
        separador5.setBoxShadowConfigShadowColor("clBlack");
        separador5.setBoxShadowConfigOpacity(75);
        separador5.setVAlign("tvTop");
        hBoxFormasPagamento.addChildren(separador5);
        separador5.applyProperties();
    }

    public TFLabel lblFormasPagamento = new TFLabel();

    private void init_lblFormasPagamento() {
        lblFormasPagamento.setName("lblFormasPagamento");
        lblFormasPagamento.setLeft(11);
        lblFormasPagamento.setTop(1);
        lblFormasPagamento.setWidth(107);
        lblFormasPagamento.setHeight(13);
        lblFormasPagamento.setCaption("Formas de Pagamento");
        lblFormasPagamento.setFontColor("clWindowText");
        lblFormasPagamento.setFontSize(-11);
        lblFormasPagamento.setFontName("Tahoma");
        lblFormasPagamento.setFontStyle("[]");
        lblFormasPagamento.setVerticalAlignment("taVerticalCenter");
        lblFormasPagamento.setWordBreak(false);
        hBoxFormasPagamento.addChildren(lblFormasPagamento);
        lblFormasPagamento.applyProperties();
    }

    public TFHBox separador6 = new TFHBox();

    private void init_separador6() {
        separador6.setName("separador6");
        separador6.setLeft(118);
        separador6.setTop(1);
        separador6.setWidth(10);
        separador6.setHeight(10);
        separador6.setBorderStyle("stNone");
        separador6.setPaddingTop(0);
        separador6.setPaddingLeft(0);
        separador6.setPaddingRight(0);
        separador6.setPaddingBottom(0);
        separador6.setMarginTop(0);
        separador6.setMarginLeft(0);
        separador6.setMarginRight(0);
        separador6.setMarginBottom(0);
        separador6.setSpacing(1);
        separador6.setFlexVflex("ftFalse");
        separador6.setFlexHflex("ftTrue");
        separador6.setScrollable(false);
        separador6.setBoxShadowConfigHorizontalLength(10);
        separador6.setBoxShadowConfigVerticalLength(10);
        separador6.setBoxShadowConfigBlurRadius(5);
        separador6.setBoxShadowConfigSpreadRadius(0);
        separador6.setBoxShadowConfigShadowColor("clBlack");
        separador6.setBoxShadowConfigOpacity(75);
        separador6.setVAlign("tvTop");
        hBoxFormasPagamento.addChildren(separador6);
        separador6.applyProperties();
    }

    public TFHBox separadorTopo = new TFHBox();

    private void init_separadorTopo() {
        separadorTopo.setName("separadorTopo");
        separadorTopo.setLeft(0);
        separadorTopo.setTop(27);
        separadorTopo.setWidth(973);
        separadorTopo.setHeight(8);
        separadorTopo.setAlign("alTop");
        separadorTopo.setBorderStyle("stNone");
        separadorTopo.setPaddingTop(0);
        separadorTopo.setPaddingLeft(0);
        separadorTopo.setPaddingRight(0);
        separadorTopo.setPaddingBottom(0);
        separadorTopo.setMarginTop(0);
        separadorTopo.setMarginLeft(0);
        separadorTopo.setMarginRight(0);
        separadorTopo.setMarginBottom(0);
        separadorTopo.setSpacing(1);
        separadorTopo.setFlexVflex("ftFalse");
        separadorTopo.setFlexHflex("ftFalse");
        separadorTopo.setScrollable(false);
        separadorTopo.setBoxShadowConfigHorizontalLength(10);
        separadorTopo.setBoxShadowConfigVerticalLength(10);
        separadorTopo.setBoxShadowConfigBlurRadius(5);
        separadorTopo.setBoxShadowConfigSpreadRadius(0);
        separadorTopo.setBoxShadowConfigShadowColor("clBlack");
        separadorTopo.setBoxShadowConfigOpacity(75);
        separadorTopo.setVAlign("tvTop");
        vBoxOrcamentosPreNota.addChildren(separadorTopo);
        separadorTopo.applyProperties();
    }

    public TFPageControl pgcAgrupamentoDePagamentos = new TFPageControl();

    private void init_pgcAgrupamentoDePagamentos() {
        pgcAgrupamentoDePagamentos.setName("pgcAgrupamentoDePagamentos");
        pgcAgrupamentoDePagamentos.setLeft(0);
        pgcAgrupamentoDePagamentos.setTop(36);
        pgcAgrupamentoDePagamentos.setWidth(988);
        pgcAgrupamentoDePagamentos.setHeight(359);
        pgcAgrupamentoDePagamentos.setAlign("alClient");
        pgcAgrupamentoDePagamentos.setTabPosition("tpTop");
        pgcAgrupamentoDePagamentos.setFlexVflex("ftTrue");
        pgcAgrupamentoDePagamentos.setFlexHflex("ftTrue");
        pgcAgrupamentoDePagamentos.setRenderStyle("rsCard");
        pgcAgrupamentoDePagamentos.applyProperties();
        vBoxOrcamentosPreNota.addChildren(pgcAgrupamentoDePagamentos);
    }

    public TFTabsheet tabOrcPreNotas = new TFTabsheet();

    private void init_tabOrcPreNotas() {
        tabOrcPreNotas.setName("tabOrcPreNotas");
        tabOrcPreNotas.setCaption("Selecionar Or\u00E7amentos e PreNotas");
        tabOrcPreNotas.setVisible(true);
        tabOrcPreNotas.setClosable(false);
        pgcAgrupamentoDePagamentos.addChildren(tabOrcPreNotas);
        tabOrcPreNotas.applyProperties();
    }

    public TFVBox vBoxOrcPreNotas = new TFVBox();

    private void init_vBoxOrcPreNotas() {
        vBoxOrcPreNotas.setName("vBoxOrcPreNotas");
        vBoxOrcPreNotas.setLeft(0);
        vBoxOrcPreNotas.setTop(0);
        vBoxOrcPreNotas.setWidth(980);
        vBoxOrcPreNotas.setHeight(330);
        vBoxOrcPreNotas.setAlign("alClient");
        vBoxOrcPreNotas.setBorderStyle("stNone");
        vBoxOrcPreNotas.setPaddingTop(0);
        vBoxOrcPreNotas.setPaddingLeft(0);
        vBoxOrcPreNotas.setPaddingRight(0);
        vBoxOrcPreNotas.setPaddingBottom(0);
        vBoxOrcPreNotas.setMarginTop(0);
        vBoxOrcPreNotas.setMarginLeft(0);
        vBoxOrcPreNotas.setMarginRight(0);
        vBoxOrcPreNotas.setMarginBottom(0);
        vBoxOrcPreNotas.setSpacing(1);
        vBoxOrcPreNotas.setFlexVflex("ftTrue");
        vBoxOrcPreNotas.setFlexHflex("ftTrue");
        vBoxOrcPreNotas.setScrollable(true);
        vBoxOrcPreNotas.setBoxShadowConfigHorizontalLength(10);
        vBoxOrcPreNotas.setBoxShadowConfigVerticalLength(10);
        vBoxOrcPreNotas.setBoxShadowConfigBlurRadius(5);
        vBoxOrcPreNotas.setBoxShadowConfigSpreadRadius(0);
        vBoxOrcPreNotas.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrcPreNotas.setBoxShadowConfigOpacity(75);
        tabOrcPreNotas.addChildren(vBoxOrcPreNotas);
        vBoxOrcPreNotas.applyProperties();
    }

    public TFHBox hBoxTopoOrcNotas = new TFHBox();

    private void init_hBoxTopoOrcNotas() {
        hBoxTopoOrcNotas.setName("hBoxTopoOrcNotas");
        hBoxTopoOrcNotas.setLeft(0);
        hBoxTopoOrcNotas.setTop(0);
        hBoxTopoOrcNotas.setWidth(973);
        hBoxTopoOrcNotas.setHeight(32);
        hBoxTopoOrcNotas.setAlign("alTop");
        hBoxTopoOrcNotas.setBorderStyle("stNone");
        hBoxTopoOrcNotas.setPaddingTop(0);
        hBoxTopoOrcNotas.setPaddingLeft(10);
        hBoxTopoOrcNotas.setPaddingRight(0);
        hBoxTopoOrcNotas.setPaddingBottom(0);
        hBoxTopoOrcNotas.setMarginTop(0);
        hBoxTopoOrcNotas.setMarginLeft(0);
        hBoxTopoOrcNotas.setMarginRight(0);
        hBoxTopoOrcNotas.setMarginBottom(0);
        hBoxTopoOrcNotas.setSpacing(1);
        hBoxTopoOrcNotas.setFlexVflex("ftFalse");
        hBoxTopoOrcNotas.setFlexHflex("ftTrue");
        hBoxTopoOrcNotas.setScrollable(false);
        hBoxTopoOrcNotas.setBoxShadowConfigHorizontalLength(10);
        hBoxTopoOrcNotas.setBoxShadowConfigVerticalLength(10);
        hBoxTopoOrcNotas.setBoxShadowConfigBlurRadius(5);
        hBoxTopoOrcNotas.setBoxShadowConfigSpreadRadius(0);
        hBoxTopoOrcNotas.setBoxShadowConfigShadowColor("clBlack");
        hBoxTopoOrcNotas.setBoxShadowConfigOpacity(75);
        hBoxTopoOrcNotas.setVAlign("tvTop");
        vBoxOrcPreNotas.addChildren(hBoxTopoOrcNotas);
        hBoxTopoOrcNotas.applyProperties();
    }

    public TFLabel lblTituloOrcNotasAgrupadas = new TFLabel();

    private void init_lblTituloOrcNotasAgrupadas() {
        lblTituloOrcNotasAgrupadas.setName("lblTituloOrcNotasAgrupadas");
        lblTituloOrcNotasAgrupadas.setLeft(0);
        lblTituloOrcNotasAgrupadas.setTop(0);
        lblTituloOrcNotasAgrupadas.setWidth(221);
        lblTituloOrcNotasAgrupadas.setHeight(13);
        lblTituloOrcNotasAgrupadas.setCaption("Or\u00E7amentos e Pr\u00E9-Notas a serem Agrupadas: ");
        lblTituloOrcNotasAgrupadas.setFontColor("clWindowText");
        lblTituloOrcNotasAgrupadas.setFontSize(-11);
        lblTituloOrcNotasAgrupadas.setFontName("Tahoma");
        lblTituloOrcNotasAgrupadas.setFontStyle("[]");
        lblTituloOrcNotasAgrupadas.setVerticalAlignment("taVerticalCenter");
        lblTituloOrcNotasAgrupadas.setWordBreak(false);
        hBoxTopoOrcNotas.addChildren(lblTituloOrcNotasAgrupadas);
        lblTituloOrcNotasAgrupadas.applyProperties();
    }

    public TFHBox separador23 = new TFHBox();

    private void init_separador23() {
        separador23.setName("separador23");
        separador23.setLeft(221);
        separador23.setTop(0);
        separador23.setWidth(5);
        separador23.setHeight(10);
        separador23.setBorderStyle("stNone");
        separador23.setPaddingTop(0);
        separador23.setPaddingLeft(0);
        separador23.setPaddingRight(0);
        separador23.setPaddingBottom(0);
        separador23.setMarginTop(0);
        separador23.setMarginLeft(0);
        separador23.setMarginRight(0);
        separador23.setMarginBottom(0);
        separador23.setSpacing(1);
        separador23.setFlexVflex("ftFalse");
        separador23.setFlexHflex("ftFalse");
        separador23.setScrollable(false);
        separador23.setBoxShadowConfigHorizontalLength(10);
        separador23.setBoxShadowConfigVerticalLength(10);
        separador23.setBoxShadowConfigBlurRadius(5);
        separador23.setBoxShadowConfigSpreadRadius(0);
        separador23.setBoxShadowConfigShadowColor("clBlack");
        separador23.setBoxShadowConfigOpacity(75);
        separador23.setVAlign("tvTop");
        hBoxTopoOrcNotas.addChildren(separador23);
        separador23.applyProperties();
    }

    public TFIconClass btnSelecao = new TFIconClass();

    private void init_btnSelecao() {
        btnSelecao.setName("btnSelecao");
        btnSelecao.setLeft(226);
        btnSelecao.setTop(0);
        btnSelecao.setHint("Marcar/Desmarcar Todos");
        btnSelecao.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSelecaoClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnSelecao", "OnClick");
        });
        btnSelecao.setIconClass("square-o");
        btnSelecao.setSize(22);
        btnSelecao.setColor("clTeal");
        hBoxTopoOrcNotas.addChildren(btnSelecao);
        btnSelecao.applyProperties();
    }

    public TFHBox separador22 = new TFHBox();

    private void init_separador22() {
        separador22.setName("separador22");
        separador22.setLeft(242);
        separador22.setTop(0);
        separador22.setWidth(6);
        separador22.setHeight(10);
        separador22.setBorderStyle("stNone");
        separador22.setPaddingTop(0);
        separador22.setPaddingLeft(0);
        separador22.setPaddingRight(0);
        separador22.setPaddingBottom(0);
        separador22.setMarginTop(0);
        separador22.setMarginLeft(0);
        separador22.setMarginRight(0);
        separador22.setMarginBottom(0);
        separador22.setSpacing(1);
        separador22.setFlexVflex("ftFalse");
        separador22.setFlexHflex("ftTrue");
        separador22.setScrollable(false);
        separador22.setBoxShadowConfigHorizontalLength(10);
        separador22.setBoxShadowConfigVerticalLength(10);
        separador22.setBoxShadowConfigBlurRadius(5);
        separador22.setBoxShadowConfigSpreadRadius(0);
        separador22.setBoxShadowConfigShadowColor("clBlack");
        separador22.setBoxShadowConfigOpacity(75);
        separador22.setVAlign("tvTop");
        hBoxTopoOrcNotas.addChildren(separador22);
        separador22.applyProperties();
    }

    public TFButton btnAddOrcNotas = new TFButton();

    private void init_btnAddOrcNotas() {
        btnAddOrcNotas.setName("btnAddOrcNotas");
        btnAddOrcNotas.setLeft(248);
        btnAddOrcNotas.setTop(0);
        btnAddOrcNotas.setWidth(78);
        btnAddOrcNotas.setHeight(28);
        btnAddOrcNotas.setCaption("Adicionar");
        btnAddOrcNotas.setFontColor("clWindowText");
        btnAddOrcNotas.setFontSize(-11);
        btnAddOrcNotas.setFontName("Tahoma");
        btnAddOrcNotas.setFontStyle("[]");
        btnAddOrcNotas.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAddOrcNotasClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnAddOrcNotas", "OnClick");
        });
        btnAddOrcNotas.setImageId(0);
        btnAddOrcNotas.setColor("clBtnFace");
        btnAddOrcNotas.setAccess(false);
        btnAddOrcNotas.setIconClass("plus-circle");
        btnAddOrcNotas.setIconReverseDirection(false);
        hBoxTopoOrcNotas.addChildren(btnAddOrcNotas);
        btnAddOrcNotas.applyProperties();
    }

    public TFHBox separador51 = new TFHBox();

    private void init_separador51() {
        separador51.setName("separador51");
        separador51.setLeft(326);
        separador51.setTop(0);
        separador51.setWidth(10);
        separador51.setHeight(10);
        separador51.setBorderStyle("stNone");
        separador51.setPaddingTop(0);
        separador51.setPaddingLeft(0);
        separador51.setPaddingRight(0);
        separador51.setPaddingBottom(0);
        separador51.setMarginTop(0);
        separador51.setMarginLeft(0);
        separador51.setMarginRight(0);
        separador51.setMarginBottom(0);
        separador51.setSpacing(1);
        separador51.setFlexVflex("ftFalse");
        separador51.setFlexHflex("ftFalse");
        separador51.setScrollable(false);
        separador51.setBoxShadowConfigHorizontalLength(10);
        separador51.setBoxShadowConfigVerticalLength(10);
        separador51.setBoxShadowConfigBlurRadius(5);
        separador51.setBoxShadowConfigSpreadRadius(0);
        separador51.setBoxShadowConfigShadowColor("clBlack");
        separador51.setBoxShadowConfigOpacity(75);
        separador51.setVAlign("tvTop");
        hBoxTopoOrcNotas.addChildren(separador51);
        separador51.applyProperties();
    }

    public TFHBox hBoxOrcPreNotasParaAgrupar = new TFHBox();

    private void init_hBoxOrcPreNotasParaAgrupar() {
        hBoxOrcPreNotasParaAgrupar.setName("hBoxOrcPreNotasParaAgrupar");
        hBoxOrcPreNotasParaAgrupar.setLeft(0);
        hBoxOrcPreNotasParaAgrupar.setTop(33);
        hBoxOrcPreNotasParaAgrupar.setWidth(972);
        hBoxOrcPreNotasParaAgrupar.setHeight(262);
        hBoxOrcPreNotasParaAgrupar.setBorderStyle("stNone");
        hBoxOrcPreNotasParaAgrupar.setPaddingTop(0);
        hBoxOrcPreNotasParaAgrupar.setPaddingLeft(0);
        hBoxOrcPreNotasParaAgrupar.setPaddingRight(0);
        hBoxOrcPreNotasParaAgrupar.setPaddingBottom(0);
        hBoxOrcPreNotasParaAgrupar.setMarginTop(0);
        hBoxOrcPreNotasParaAgrupar.setMarginLeft(0);
        hBoxOrcPreNotasParaAgrupar.setMarginRight(0);
        hBoxOrcPreNotasParaAgrupar.setMarginBottom(0);
        hBoxOrcPreNotasParaAgrupar.setSpacing(1);
        hBoxOrcPreNotasParaAgrupar.setFlexVflex("ftTrue");
        hBoxOrcPreNotasParaAgrupar.setFlexHflex("ftTrue");
        hBoxOrcPreNotasParaAgrupar.setScrollable(false);
        hBoxOrcPreNotasParaAgrupar.setBoxShadowConfigHorizontalLength(10);
        hBoxOrcPreNotasParaAgrupar.setBoxShadowConfigVerticalLength(10);
        hBoxOrcPreNotasParaAgrupar.setBoxShadowConfigBlurRadius(5);
        hBoxOrcPreNotasParaAgrupar.setBoxShadowConfigSpreadRadius(0);
        hBoxOrcPreNotasParaAgrupar.setBoxShadowConfigShadowColor("clBlack");
        hBoxOrcPreNotasParaAgrupar.setBoxShadowConfigOpacity(75);
        hBoxOrcPreNotasParaAgrupar.setVAlign("tvTop");
        vBoxOrcPreNotas.addChildren(hBoxOrcPreNotasParaAgrupar);
        hBoxOrcPreNotasParaAgrupar.applyProperties();
    }

    public TFVBox separador19 = new TFVBox();

    private void init_separador19() {
        separador19.setName("separador19");
        separador19.setLeft(0);
        separador19.setTop(0);
        separador19.setWidth(10);
        separador19.setHeight(251);
        separador19.setBorderStyle("stNone");
        separador19.setPaddingTop(0);
        separador19.setPaddingLeft(0);
        separador19.setPaddingRight(0);
        separador19.setPaddingBottom(0);
        separador19.setMarginTop(0);
        separador19.setMarginLeft(0);
        separador19.setMarginRight(0);
        separador19.setMarginBottom(0);
        separador19.setSpacing(1);
        separador19.setFlexVflex("ftFalse");
        separador19.setFlexHflex("ftFalse");
        separador19.setScrollable(false);
        separador19.setBoxShadowConfigHorizontalLength(10);
        separador19.setBoxShadowConfigVerticalLength(10);
        separador19.setBoxShadowConfigBlurRadius(5);
        separador19.setBoxShadowConfigSpreadRadius(0);
        separador19.setBoxShadowConfigShadowColor("clBlack");
        separador19.setBoxShadowConfigOpacity(75);
        hBoxOrcPreNotasParaAgrupar.addChildren(separador19);
        separador19.applyProperties();
    }

    public TFVBox vBoxOrcPreNotasParaAgrupar = new TFVBox();

    private void init_vBoxOrcPreNotasParaAgrupar() {
        vBoxOrcPreNotasParaAgrupar.setName("vBoxOrcPreNotasParaAgrupar");
        vBoxOrcPreNotasParaAgrupar.setLeft(10);
        vBoxOrcPreNotasParaAgrupar.setTop(0);
        vBoxOrcPreNotasParaAgrupar.setWidth(941);
        vBoxOrcPreNotasParaAgrupar.setHeight(249);
        vBoxOrcPreNotasParaAgrupar.setBorderStyle("stNone");
        vBoxOrcPreNotasParaAgrupar.setPaddingTop(0);
        vBoxOrcPreNotasParaAgrupar.setPaddingLeft(0);
        vBoxOrcPreNotasParaAgrupar.setPaddingRight(0);
        vBoxOrcPreNotasParaAgrupar.setPaddingBottom(0);
        vBoxOrcPreNotasParaAgrupar.setMarginTop(0);
        vBoxOrcPreNotasParaAgrupar.setMarginLeft(0);
        vBoxOrcPreNotasParaAgrupar.setMarginRight(0);
        vBoxOrcPreNotasParaAgrupar.setMarginBottom(0);
        vBoxOrcPreNotasParaAgrupar.setSpacing(1);
        vBoxOrcPreNotasParaAgrupar.setFlexVflex("ftTrue");
        vBoxOrcPreNotasParaAgrupar.setFlexHflex("ftTrue");
        vBoxOrcPreNotasParaAgrupar.setScrollable(false);
        vBoxOrcPreNotasParaAgrupar.setBoxShadowConfigHorizontalLength(10);
        vBoxOrcPreNotasParaAgrupar.setBoxShadowConfigVerticalLength(10);
        vBoxOrcPreNotasParaAgrupar.setBoxShadowConfigBlurRadius(5);
        vBoxOrcPreNotasParaAgrupar.setBoxShadowConfigSpreadRadius(0);
        vBoxOrcPreNotasParaAgrupar.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrcPreNotasParaAgrupar.setBoxShadowConfigOpacity(75);
        hBoxOrcPreNotasParaAgrupar.addChildren(vBoxOrcPreNotasParaAgrupar);
        vBoxOrcPreNotasParaAgrupar.applyProperties();
    }

    public TFGrid gridOrcPreNotasParaAgrupar = new TFGrid();

    private void init_gridOrcPreNotasParaAgrupar() {
        gridOrcPreNotasParaAgrupar.setName("gridOrcPreNotasParaAgrupar");
        gridOrcPreNotasParaAgrupar.setLeft(0);
        gridOrcPreNotasParaAgrupar.setTop(0);
        gridOrcPreNotasParaAgrupar.setWidth(930);
        gridOrcPreNotasParaAgrupar.setHeight(224);
        gridOrcPreNotasParaAgrupar.setTable(tbOrcPreNotasParaAgrupar);
        gridOrcPreNotasParaAgrupar.setFlexVflex("ftTrue");
        gridOrcPreNotasParaAgrupar.setFlexHflex("ftTrue");
        gridOrcPreNotasParaAgrupar.setPagingEnabled(false);
        gridOrcPreNotasParaAgrupar.setFrozenColumns(0);
        gridOrcPreNotasParaAgrupar.setShowFooter(true);
        gridOrcPreNotasParaAgrupar.setShowHeader(true);
        gridOrcPreNotasParaAgrupar.setMultiSelection(false);
        gridOrcPreNotasParaAgrupar.setGroupingEnabled(false);
        gridOrcPreNotasParaAgrupar.setGroupingExpanded(false);
        gridOrcPreNotasParaAgrupar.setGroupingShowFooter(false);
        gridOrcPreNotasParaAgrupar.setCrosstabEnabled(false);
        gridOrcPreNotasParaAgrupar.setCrosstabGroupType("cgtConcat");
        gridOrcPreNotasParaAgrupar.setEditionEnabled(false);
        gridOrcPreNotasParaAgrupar.setContextMenu(FPopupMenuSelecaoOrcamentosPreNotas);
        gridOrcPreNotasParaAgrupar.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption("#");
        item0.setWidth(60);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFooterExpression("\"Total:\"");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL='S'");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL='N'");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            selecionaOrcPreNotasParaAgrupar(event);
            processarFlow("FrmAgrupaPagamentos", "item0", "OnClick");
        });
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridOrcPreNotasParaAgrupar.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("COD_EMPRESA");
        item3.setTitleCaption("C\u00F3d. Empresa");
        item3.setWidth(110);
        item3.setVisible(false);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridOrcPreNotasParaAgrupar.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("COD_ORC_MAPA");
        item4.setTitleCaption("Orc. Mapa");
        item4.setWidth(120);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taRight");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(true);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridOrcPreNotasParaAgrupar.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("SALDO_PARA_AGRUPAR");
        item5.setTitleCaption("Total Adiantamento");
        item5.setWidth(140);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taRight");
        item5.setFieldType("ftString");
        item5.setFooterExpression("new String(\"R$ \"+tbOrcPreNotasParaAgrupar.compute(\"SUM\",\"SALDO_PARA_AGRUPAR\").asDecimal()+\"\")");
        item5.setFlexRatio(0);
        item5.setSort(true);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        TFMaskExpression item6 = new TFMaskExpression();
        item6.setExpression("*");
        item6.setEvalType("etExpression");
        item6.setMask("R$ ,##0.00");
        item6.setPadLength(0);
        item6.setPadDirection("pdNone");
        item6.setMaskType("mtDecimal");
        item5.getMasks().add(item6);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridOrcPreNotasParaAgrupar.getColumns().add(item5);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("VLR_AGRUPADO_ANT");
        item7.setTitleCaption("Valor_agrupador Ant");
        item7.setWidth(150);
        item7.setVisible(false);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridOrcPreNotasParaAgrupar.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("COD_CLIENTE");
        item8.setTitleCaption("Cod. Cliente");
        item8.setWidth(150);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridOrcPreNotasParaAgrupar.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("NOME");
        item9.setTitleCaption("Nome");
        item9.setWidth(213);
        item9.setVisible(false);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridOrcPreNotasParaAgrupar.getColumns().add(item9);
        vBoxOrcPreNotasParaAgrupar.addChildren(gridOrcPreNotasParaAgrupar);
        gridOrcPreNotasParaAgrupar.applyProperties();
    }

    public TFVBox separador20 = new TFVBox();

    private void init_separador20() {
        separador20.setName("separador20");
        separador20.setLeft(951);
        separador20.setTop(0);
        separador20.setWidth(10);
        separador20.setHeight(251);
        separador20.setBorderStyle("stNone");
        separador20.setPaddingTop(0);
        separador20.setPaddingLeft(0);
        separador20.setPaddingRight(0);
        separador20.setPaddingBottom(0);
        separador20.setMarginTop(0);
        separador20.setMarginLeft(0);
        separador20.setMarginRight(0);
        separador20.setMarginBottom(0);
        separador20.setSpacing(1);
        separador20.setFlexVflex("ftFalse");
        separador20.setFlexHflex("ftFalse");
        separador20.setScrollable(false);
        separador20.setBoxShadowConfigHorizontalLength(10);
        separador20.setBoxShadowConfigVerticalLength(10);
        separador20.setBoxShadowConfigBlurRadius(5);
        separador20.setBoxShadowConfigSpreadRadius(0);
        separador20.setBoxShadowConfigShadowColor("clBlack");
        separador20.setBoxShadowConfigOpacity(75);
        hBoxOrcPreNotasParaAgrupar.addChildren(separador20);
        separador20.applyProperties();
    }

    public TFHBox separador21 = new TFHBox();

    private void init_separador21() {
        separador21.setName("separador21");
        separador21.setLeft(0);
        separador21.setTop(296);
        separador21.setWidth(973);
        separador21.setHeight(10);
        separador21.setAlign("alBottom");
        separador21.setBorderStyle("stNone");
        separador21.setPaddingTop(0);
        separador21.setPaddingLeft(0);
        separador21.setPaddingRight(0);
        separador21.setPaddingBottom(0);
        separador21.setMarginTop(0);
        separador21.setMarginLeft(0);
        separador21.setMarginRight(0);
        separador21.setMarginBottom(0);
        separador21.setSpacing(1);
        separador21.setFlexVflex("ftFalse");
        separador21.setFlexHflex("ftFalse");
        separador21.setScrollable(false);
        separador21.setBoxShadowConfigHorizontalLength(10);
        separador21.setBoxShadowConfigVerticalLength(10);
        separador21.setBoxShadowConfigBlurRadius(5);
        separador21.setBoxShadowConfigSpreadRadius(0);
        separador21.setBoxShadowConfigShadowColor("clBlack");
        separador21.setBoxShadowConfigOpacity(75);
        separador21.setVAlign("tvTop");
        vBoxOrcPreNotas.addChildren(separador21);
        separador21.applyProperties();
    }

    public TFTabsheet tabOrcPreNotasSelecionadas = new TFTabsheet();

    private void init_tabOrcPreNotasSelecionadas() {
        tabOrcPreNotasSelecionadas.setName("tabOrcPreNotasSelecionadas");
        tabOrcPreNotasSelecionadas.setCaption("Or\u00E7amentos e Pre-Notas Selecionadas");
        tabOrcPreNotasSelecionadas.setVisible(true);
        tabOrcPreNotasSelecionadas.setClosable(false);
        pgcAgrupamentoDePagamentos.addChildren(tabOrcPreNotasSelecionadas);
        tabOrcPreNotasSelecionadas.applyProperties();
    }

    public TFVBox vBoxOrcPreNotasSelecionadas = new TFVBox();

    private void init_vBoxOrcPreNotasSelecionadas() {
        vBoxOrcPreNotasSelecionadas.setName("vBoxOrcPreNotasSelecionadas");
        vBoxOrcPreNotasSelecionadas.setLeft(0);
        vBoxOrcPreNotasSelecionadas.setTop(0);
        vBoxOrcPreNotasSelecionadas.setWidth(980);
        vBoxOrcPreNotasSelecionadas.setHeight(330);
        vBoxOrcPreNotasSelecionadas.setAlign("alClient");
        vBoxOrcPreNotasSelecionadas.setBorderStyle("stNone");
        vBoxOrcPreNotasSelecionadas.setPaddingTop(0);
        vBoxOrcPreNotasSelecionadas.setPaddingLeft(0);
        vBoxOrcPreNotasSelecionadas.setPaddingRight(0);
        vBoxOrcPreNotasSelecionadas.setPaddingBottom(0);
        vBoxOrcPreNotasSelecionadas.setMarginTop(0);
        vBoxOrcPreNotasSelecionadas.setMarginLeft(0);
        vBoxOrcPreNotasSelecionadas.setMarginRight(0);
        vBoxOrcPreNotasSelecionadas.setMarginBottom(0);
        vBoxOrcPreNotasSelecionadas.setSpacing(1);
        vBoxOrcPreNotasSelecionadas.setFlexVflex("ftTrue");
        vBoxOrcPreNotasSelecionadas.setFlexHflex("ftTrue");
        vBoxOrcPreNotasSelecionadas.setScrollable(true);
        vBoxOrcPreNotasSelecionadas.setBoxShadowConfigHorizontalLength(10);
        vBoxOrcPreNotasSelecionadas.setBoxShadowConfigVerticalLength(10);
        vBoxOrcPreNotasSelecionadas.setBoxShadowConfigBlurRadius(5);
        vBoxOrcPreNotasSelecionadas.setBoxShadowConfigSpreadRadius(0);
        vBoxOrcPreNotasSelecionadas.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrcPreNotasSelecionadas.setBoxShadowConfigOpacity(75);
        tabOrcPreNotasSelecionadas.addChildren(vBoxOrcPreNotasSelecionadas);
        vBoxOrcPreNotasSelecionadas.applyProperties();
    }

    public TFHBox hBoxTopoOrcNotasAgrupadas = new TFHBox();

    private void init_hBoxTopoOrcNotasAgrupadas() {
        hBoxTopoOrcNotasAgrupadas.setName("hBoxTopoOrcNotasAgrupadas");
        hBoxTopoOrcNotasAgrupadas.setLeft(0);
        hBoxTopoOrcNotasAgrupadas.setTop(0);
        hBoxTopoOrcNotasAgrupadas.setWidth(973);
        hBoxTopoOrcNotasAgrupadas.setHeight(32);
        hBoxTopoOrcNotasAgrupadas.setAlign("alTop");
        hBoxTopoOrcNotasAgrupadas.setBorderStyle("stNone");
        hBoxTopoOrcNotasAgrupadas.setPaddingTop(0);
        hBoxTopoOrcNotasAgrupadas.setPaddingLeft(10);
        hBoxTopoOrcNotasAgrupadas.setPaddingRight(0);
        hBoxTopoOrcNotasAgrupadas.setPaddingBottom(0);
        hBoxTopoOrcNotasAgrupadas.setMarginTop(0);
        hBoxTopoOrcNotasAgrupadas.setMarginLeft(0);
        hBoxTopoOrcNotasAgrupadas.setMarginRight(0);
        hBoxTopoOrcNotasAgrupadas.setMarginBottom(0);
        hBoxTopoOrcNotasAgrupadas.setSpacing(1);
        hBoxTopoOrcNotasAgrupadas.setFlexVflex("ftFalse");
        hBoxTopoOrcNotasAgrupadas.setFlexHflex("ftTrue");
        hBoxTopoOrcNotasAgrupadas.setScrollable(false);
        hBoxTopoOrcNotasAgrupadas.setBoxShadowConfigHorizontalLength(10);
        hBoxTopoOrcNotasAgrupadas.setBoxShadowConfigVerticalLength(10);
        hBoxTopoOrcNotasAgrupadas.setBoxShadowConfigBlurRadius(5);
        hBoxTopoOrcNotasAgrupadas.setBoxShadowConfigSpreadRadius(0);
        hBoxTopoOrcNotasAgrupadas.setBoxShadowConfigShadowColor("clBlack");
        hBoxTopoOrcNotasAgrupadas.setBoxShadowConfigOpacity(75);
        hBoxTopoOrcNotasAgrupadas.setVAlign("tvTop");
        vBoxOrcPreNotasSelecionadas.addChildren(hBoxTopoOrcNotasAgrupadas);
        hBoxTopoOrcNotasAgrupadas.applyProperties();
    }

    public TFLabel lblTitOrcNotasAgrupadas = new TFLabel();

    private void init_lblTitOrcNotasAgrupadas() {
        lblTitOrcNotasAgrupadas.setName("lblTitOrcNotasAgrupadas");
        lblTitOrcNotasAgrupadas.setLeft(0);
        lblTitOrcNotasAgrupadas.setTop(0);
        lblTitOrcNotasAgrupadas.setWidth(177);
        lblTitOrcNotasAgrupadas.setHeight(13);
        lblTitOrcNotasAgrupadas.setCaption("Or\u00E7amentos e Pr\u00E9-Notas Agrupadas:");
        lblTitOrcNotasAgrupadas.setFontColor("clWindowText");
        lblTitOrcNotasAgrupadas.setFontSize(-11);
        lblTitOrcNotasAgrupadas.setFontName("Tahoma");
        lblTitOrcNotasAgrupadas.setFontStyle("[]");
        lblTitOrcNotasAgrupadas.setVerticalAlignment("taVerticalCenter");
        lblTitOrcNotasAgrupadas.setWordBreak(false);
        hBoxTopoOrcNotasAgrupadas.addChildren(lblTitOrcNotasAgrupadas);
        lblTitOrcNotasAgrupadas.applyProperties();
    }

    public TFHBox separador49 = new TFHBox();

    private void init_separador49() {
        separador49.setName("separador49");
        separador49.setLeft(177);
        separador49.setTop(0);
        separador49.setWidth(6);
        separador49.setHeight(10);
        separador49.setBorderStyle("stNone");
        separador49.setPaddingTop(0);
        separador49.setPaddingLeft(0);
        separador49.setPaddingRight(0);
        separador49.setPaddingBottom(0);
        separador49.setMarginTop(0);
        separador49.setMarginLeft(0);
        separador49.setMarginRight(0);
        separador49.setMarginBottom(0);
        separador49.setSpacing(1);
        separador49.setFlexVflex("ftFalse");
        separador49.setFlexHflex("ftTrue");
        separador49.setScrollable(false);
        separador49.setBoxShadowConfigHorizontalLength(10);
        separador49.setBoxShadowConfigVerticalLength(10);
        separador49.setBoxShadowConfigBlurRadius(5);
        separador49.setBoxShadowConfigSpreadRadius(0);
        separador49.setBoxShadowConfigShadowColor("clBlack");
        separador49.setBoxShadowConfigOpacity(75);
        separador49.setVAlign("tvTop");
        hBoxTopoOrcNotasAgrupadas.addChildren(separador49);
        separador49.applyProperties();
    }

    public TFButton btnRemoverTodosItens = new TFButton();

    private void init_btnRemoverTodosItens() {
        btnRemoverTodosItens.setName("btnRemoverTodosItens");
        btnRemoverTodosItens.setLeft(183);
        btnRemoverTodosItens.setTop(0);
        btnRemoverTodosItens.setWidth(26);
        btnRemoverTodosItens.setHeight(28);
        btnRemoverTodosItens.setHint("Remover Todos");
        btnRemoverTodosItens.setFontColor("clWindowText");
        btnRemoverTodosItens.setFontSize(-11);
        btnRemoverTodosItens.setFontName("Tahoma");
        btnRemoverTodosItens.setFontStyle("[]");
        btnRemoverTodosItens.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnRemoverTodosItensClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnRemoverTodosItens", "OnClick");
        });
        btnRemoverTodosItens.setImageId(0);
        btnRemoverTodosItens.setColor("clBtnFace");
        btnRemoverTodosItens.setAccess(false);
        btnRemoverTodosItens.setIconClass("trash");
        btnRemoverTodosItens.setIconReverseDirection(false);
        hBoxTopoOrcNotasAgrupadas.addChildren(btnRemoverTodosItens);
        btnRemoverTodosItens.applyProperties();
    }

    public TFHBox separador24 = new TFHBox();

    private void init_separador24() {
        separador24.setName("separador24");
        separador24.setLeft(209);
        separador24.setTop(0);
        separador24.setWidth(5);
        separador24.setHeight(10);
        separador24.setBorderStyle("stNone");
        separador24.setPaddingTop(0);
        separador24.setPaddingLeft(0);
        separador24.setPaddingRight(0);
        separador24.setPaddingBottom(0);
        separador24.setMarginTop(0);
        separador24.setMarginLeft(0);
        separador24.setMarginRight(0);
        separador24.setMarginBottom(0);
        separador24.setSpacing(1);
        separador24.setFlexVflex("ftFalse");
        separador24.setFlexHflex("ftFalse");
        separador24.setScrollable(false);
        separador24.setBoxShadowConfigHorizontalLength(10);
        separador24.setBoxShadowConfigVerticalLength(10);
        separador24.setBoxShadowConfigBlurRadius(5);
        separador24.setBoxShadowConfigSpreadRadius(0);
        separador24.setBoxShadowConfigShadowColor("clBlack");
        separador24.setBoxShadowConfigOpacity(75);
        separador24.setVAlign("tvTop");
        hBoxTopoOrcNotasAgrupadas.addChildren(separador24);
        separador24.applyProperties();
    }

    public TFButton btnDefinirFormasPagamento = new TFButton();

    private void init_btnDefinirFormasPagamento() {
        btnDefinirFormasPagamento.setName("btnDefinirFormasPagamento");
        btnDefinirFormasPagamento.setLeft(214);
        btnDefinirFormasPagamento.setTop(0);
        btnDefinirFormasPagamento.setWidth(167);
        btnDefinirFormasPagamento.setHeight(28);
        btnDefinirFormasPagamento.setHint("Definir Formas Pagamento");
        btnDefinirFormasPagamento.setCaption("Definir Formas Pagamento");
        btnDefinirFormasPagamento.setFontColor("clWindowText");
        btnDefinirFormasPagamento.setFontSize(-11);
        btnDefinirFormasPagamento.setFontName("Tahoma");
        btnDefinirFormasPagamento.setFontStyle("[]");
        btnDefinirFormasPagamento.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnDefinirFormasPagamentoClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnDefinirFormasPagamento", "OnClick");
        });
        btnDefinirFormasPagamento.setImageId(0);
        btnDefinirFormasPagamento.setColor("clBtnFace");
        btnDefinirFormasPagamento.setAccess(false);
        btnDefinirFormasPagamento.setIconClass("money");
        btnDefinirFormasPagamento.setIconReverseDirection(false);
        hBoxTopoOrcNotasAgrupadas.addChildren(btnDefinirFormasPagamento);
        btnDefinirFormasPagamento.applyProperties();
    }

    public TFHBox separador25 = new TFHBox();

    private void init_separador25() {
        separador25.setName("separador25");
        separador25.setLeft(381);
        separador25.setTop(0);
        separador25.setWidth(10);
        separador25.setHeight(10);
        separador25.setBorderStyle("stNone");
        separador25.setPaddingTop(0);
        separador25.setPaddingLeft(0);
        separador25.setPaddingRight(0);
        separador25.setPaddingBottom(0);
        separador25.setMarginTop(0);
        separador25.setMarginLeft(0);
        separador25.setMarginRight(0);
        separador25.setMarginBottom(0);
        separador25.setSpacing(1);
        separador25.setFlexVflex("ftFalse");
        separador25.setFlexHflex("ftFalse");
        separador25.setScrollable(false);
        separador25.setBoxShadowConfigHorizontalLength(10);
        separador25.setBoxShadowConfigVerticalLength(10);
        separador25.setBoxShadowConfigBlurRadius(5);
        separador25.setBoxShadowConfigSpreadRadius(0);
        separador25.setBoxShadowConfigShadowColor("clBlack");
        separador25.setBoxShadowConfigOpacity(75);
        separador25.setVAlign("tvTop");
        hBoxTopoOrcNotasAgrupadas.addChildren(separador25);
        separador25.applyProperties();
    }

    public TFHBox hBoxOrcNotasSelecionadas = new TFHBox();

    private void init_hBoxOrcNotasSelecionadas() {
        hBoxOrcNotasSelecionadas.setName("hBoxOrcNotasSelecionadas");
        hBoxOrcNotasSelecionadas.setLeft(0);
        hBoxOrcNotasSelecionadas.setTop(33);
        hBoxOrcNotasSelecionadas.setWidth(964);
        hBoxOrcNotasSelecionadas.setHeight(271);
        hBoxOrcNotasSelecionadas.setBorderStyle("stNone");
        hBoxOrcNotasSelecionadas.setPaddingTop(0);
        hBoxOrcNotasSelecionadas.setPaddingLeft(0);
        hBoxOrcNotasSelecionadas.setPaddingRight(0);
        hBoxOrcNotasSelecionadas.setPaddingBottom(0);
        hBoxOrcNotasSelecionadas.setMarginTop(0);
        hBoxOrcNotasSelecionadas.setMarginLeft(0);
        hBoxOrcNotasSelecionadas.setMarginRight(0);
        hBoxOrcNotasSelecionadas.setMarginBottom(0);
        hBoxOrcNotasSelecionadas.setSpacing(1);
        hBoxOrcNotasSelecionadas.setFlexVflex("ftTrue");
        hBoxOrcNotasSelecionadas.setFlexHflex("ftTrue");
        hBoxOrcNotasSelecionadas.setScrollable(false);
        hBoxOrcNotasSelecionadas.setBoxShadowConfigHorizontalLength(10);
        hBoxOrcNotasSelecionadas.setBoxShadowConfigVerticalLength(10);
        hBoxOrcNotasSelecionadas.setBoxShadowConfigBlurRadius(5);
        hBoxOrcNotasSelecionadas.setBoxShadowConfigSpreadRadius(0);
        hBoxOrcNotasSelecionadas.setBoxShadowConfigShadowColor("clBlack");
        hBoxOrcNotasSelecionadas.setBoxShadowConfigOpacity(75);
        hBoxOrcNotasSelecionadas.setVAlign("tvTop");
        vBoxOrcPreNotasSelecionadas.addChildren(hBoxOrcNotasSelecionadas);
        hBoxOrcNotasSelecionadas.applyProperties();
    }

    public TFVBox separador26 = new TFVBox();

    private void init_separador26() {
        separador26.setName("separador26");
        separador26.setLeft(0);
        separador26.setTop(0);
        separador26.setWidth(10);
        separador26.setHeight(251);
        separador26.setBorderStyle("stNone");
        separador26.setPaddingTop(0);
        separador26.setPaddingLeft(0);
        separador26.setPaddingRight(0);
        separador26.setPaddingBottom(0);
        separador26.setMarginTop(0);
        separador26.setMarginLeft(0);
        separador26.setMarginRight(0);
        separador26.setMarginBottom(0);
        separador26.setSpacing(1);
        separador26.setFlexVflex("ftFalse");
        separador26.setFlexHflex("ftFalse");
        separador26.setScrollable(false);
        separador26.setBoxShadowConfigHorizontalLength(10);
        separador26.setBoxShadowConfigVerticalLength(10);
        separador26.setBoxShadowConfigBlurRadius(5);
        separador26.setBoxShadowConfigSpreadRadius(0);
        separador26.setBoxShadowConfigShadowColor("clBlack");
        separador26.setBoxShadowConfigOpacity(75);
        hBoxOrcNotasSelecionadas.addChildren(separador26);
        separador26.applyProperties();
    }

    public TFVBox vBoxOrcNotasSelecionadas = new TFVBox();

    private void init_vBoxOrcNotasSelecionadas() {
        vBoxOrcNotasSelecionadas.setName("vBoxOrcNotasSelecionadas");
        vBoxOrcNotasSelecionadas.setLeft(10);
        vBoxOrcNotasSelecionadas.setTop(0);
        vBoxOrcNotasSelecionadas.setWidth(929);
        vBoxOrcNotasSelecionadas.setHeight(249);
        vBoxOrcNotasSelecionadas.setBorderStyle("stNone");
        vBoxOrcNotasSelecionadas.setPaddingTop(0);
        vBoxOrcNotasSelecionadas.setPaddingLeft(0);
        vBoxOrcNotasSelecionadas.setPaddingRight(0);
        vBoxOrcNotasSelecionadas.setPaddingBottom(0);
        vBoxOrcNotasSelecionadas.setMarginTop(0);
        vBoxOrcNotasSelecionadas.setMarginLeft(0);
        vBoxOrcNotasSelecionadas.setMarginRight(0);
        vBoxOrcNotasSelecionadas.setMarginBottom(0);
        vBoxOrcNotasSelecionadas.setSpacing(1);
        vBoxOrcNotasSelecionadas.setFlexVflex("ftTrue");
        vBoxOrcNotasSelecionadas.setFlexHflex("ftTrue");
        vBoxOrcNotasSelecionadas.setScrollable(false);
        vBoxOrcNotasSelecionadas.setBoxShadowConfigHorizontalLength(10);
        vBoxOrcNotasSelecionadas.setBoxShadowConfigVerticalLength(10);
        vBoxOrcNotasSelecionadas.setBoxShadowConfigBlurRadius(5);
        vBoxOrcNotasSelecionadas.setBoxShadowConfigSpreadRadius(0);
        vBoxOrcNotasSelecionadas.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrcNotasSelecionadas.setBoxShadowConfigOpacity(75);
        hBoxOrcNotasSelecionadas.addChildren(vBoxOrcNotasSelecionadas);
        vBoxOrcNotasSelecionadas.applyProperties();
    }

    public TFGrid gridOrcPreNotasSelecionadas = new TFGrid();

    private void init_gridOrcPreNotasSelecionadas() {
        gridOrcPreNotasSelecionadas.setName("gridOrcPreNotasSelecionadas");
        gridOrcPreNotasSelecionadas.setLeft(0);
        gridOrcPreNotasSelecionadas.setTop(0);
        gridOrcPreNotasSelecionadas.setWidth(907);
        gridOrcPreNotasSelecionadas.setHeight(224);
        gridOrcPreNotasSelecionadas.setTable(tbLeadsPgtoAgrupado);
        gridOrcPreNotasSelecionadas.setFlexVflex("ftTrue");
        gridOrcPreNotasSelecionadas.setFlexHflex("ftTrue");
        gridOrcPreNotasSelecionadas.setPagingEnabled(false);
        gridOrcPreNotasSelecionadas.setFrozenColumns(0);
        gridOrcPreNotasSelecionadas.setShowFooter(true);
        gridOrcPreNotasSelecionadas.setShowHeader(true);
        gridOrcPreNotasSelecionadas.setMultiSelection(false);
        gridOrcPreNotasSelecionadas.setGroupingEnabled(false);
        gridOrcPreNotasSelecionadas.setGroupingExpanded(false);
        gridOrcPreNotasSelecionadas.setGroupingShowFooter(false);
        gridOrcPreNotasSelecionadas.setCrosstabEnabled(false);
        gridOrcPreNotasSelecionadas.setCrosstabGroupType("cgtConcat");
        gridOrcPreNotasSelecionadas.setEditionEnabled(false);
        gridOrcPreNotasSelecionadas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setTitleCaption("#");
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        TFImageExpression item1 = new TFImageExpression();
        item1.setExpression("SEL='S'");
        item1.setEvalType("etExpression");
        item1.setImageId(310010);
        item0.getImages().add(item1);
        TFImageExpression item2 = new TFImageExpression();
        item2.setExpression("SEL='N'");
        item2.setEvalType("etExpression");
        item2.setImageId(310011);
        item0.getImages().add(item2);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item0);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("ID_PAGAMENTO");
        item3.setTitleCaption("Id. Pagamento");
        item3.setWidth(100);
        item3.setVisible(false);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftString");
        item3.setFooterExpression("\"Totais:\"");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("COD_EMPRESA");
        item4.setTitleCaption("C\u00F3d. Empresa");
        item4.setWidth(100);
        item4.setVisible(false);
        item4.setPrecision(0);
        item4.setTextAlign("taRight");
        item4.setFieldType("ftString");
        item4.setFooterExpression("\"Total:\"");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("NUMERO_OS");
        item5.setTitleCaption("N\u00FAmero Os");
        item5.setWidth(100);
        item5.setVisible(false);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("COD_ORC_MAPA");
        item6.setTitleCaption("N\u00B0 Or\u00E7amento");
        item6.setWidth(100);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taRight");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("COD_CLIENTE");
        item7.setTitleCaption("C\u00F3d. Cliente");
        item7.setWidth(100);
        item7.setVisible(false);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftDecimal");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("TIPO");
        item8.setTitleCaption("Tipo");
        item8.setWidth(100);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("VALOR");
        item9.setTitleCaption("Valor");
        item9.setWidth(100);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taRight");
        item9.setFieldType("ftString");
        item9.setFooterExpression("new String(\"R$ \"+tbLeadsPgtoAgrupado.compute(\"SUM\",\"VALOR\").asDecimal()+\"\")");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        TFMaskExpression item10 = new TFMaskExpression();
        item10.setExpression("*");
        item10.setEvalType("etExpression");
        item10.setMask("R$ ,##0.00");
        item10.setPadLength(0);
        item10.setPadDirection("pdNone");
        item10.setMaskType("mtDecimal");
        item9.getMasks().add(item10);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item9);
        TFGridColumn item11 = new TFGridColumn();
        item11.setVisible(true);
        item11.setPrecision(0);
        item11.setTextAlign("taLeft");
        item11.setFieldType("ftString");
        item11.setFlexRatio(0);
        item11.setSort(false);
        item11.setImageHeader(0);
        item11.setWrap(false);
        item11.setFlex(false);
        TFImageExpression item12 = new TFImageExpression();
        item12.setExpression("*");
        item12.setHint("Remover Item");
        item12.setEvalType("etExpression");
        item12.setImageId(700095);
        item12.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridOrcPreNotasSelecionadasremoveItemAgrupadoGrid(event);
            processarFlow("FrmAgrupaPagamentos", "item12", "OnClick");
        });
        item11.getImages().add(item12);
        item11.setCharCase("ccNormal");
        item11.setBlobConfigMimeType("bmtText");
        item11.setBlobConfigShowType("btImageViewer");
        item11.setShowLabel(true);
        item11.setEditorEditType("etTFString");
        item11.setEditorPrecision(0);
        item11.setEditorMaxLength(100);
        item11.setEditorLookupFilterKey(0);
        item11.setEditorLookupFilterDesc(0);
        item11.setEditorPopupHeight(400);
        item11.setEditorPopupWidth(400);
        item11.setEditorCharCase("ccNormal");
        item11.setEditorEnabled(false);
        item11.setEditorReadOnly(false);
        item11.setCheckedValue("S");
        item11.setUncheckedValue("N");
        item11.setHiperLink(false);
        item11.setEditorConstraintCheckWhen("cwImmediate");
        item11.setEditorConstraintCheckType("ctExpression");
        item11.setEditorConstraintFocusOnError(false);
        item11.setEditorConstraintEnableUI(true);
        item11.setEditorConstraintEnabled(false);
        item11.setEmpty(false);
        item11.setMobileOptsShowMobile(false);
        item11.setMobileOptsOrder(0);
        item11.setBoxSize(0);
        item11.setImageSrcType("istSource");
        gridOrcPreNotasSelecionadas.getColumns().add(item11);
        vBoxOrcNotasSelecionadas.addChildren(gridOrcPreNotasSelecionadas);
        gridOrcPreNotasSelecionadas.applyProperties();
    }

    public TFVBox separador27 = new TFVBox();

    private void init_separador27() {
        separador27.setName("separador27");
        separador27.setLeft(939);
        separador27.setTop(0);
        separador27.setWidth(10);
        separador27.setHeight(251);
        separador27.setBorderStyle("stNone");
        separador27.setPaddingTop(0);
        separador27.setPaddingLeft(0);
        separador27.setPaddingRight(0);
        separador27.setPaddingBottom(0);
        separador27.setMarginTop(0);
        separador27.setMarginLeft(0);
        separador27.setMarginRight(0);
        separador27.setMarginBottom(0);
        separador27.setSpacing(1);
        separador27.setFlexVflex("ftFalse");
        separador27.setFlexHflex("ftFalse");
        separador27.setScrollable(false);
        separador27.setBoxShadowConfigHorizontalLength(10);
        separador27.setBoxShadowConfigVerticalLength(10);
        separador27.setBoxShadowConfigBlurRadius(5);
        separador27.setBoxShadowConfigSpreadRadius(0);
        separador27.setBoxShadowConfigShadowColor("clBlack");
        separador27.setBoxShadowConfigOpacity(75);
        hBoxOrcNotasSelecionadas.addChildren(separador27);
        separador27.applyProperties();
    }

    public TFHBox separador28 = new TFHBox();

    private void init_separador28() {
        separador28.setName("separador28");
        separador28.setLeft(0);
        separador28.setTop(305);
        separador28.setWidth(973);
        separador28.setHeight(10);
        separador28.setAlign("alBottom");
        separador28.setBorderStyle("stNone");
        separador28.setPaddingTop(0);
        separador28.setPaddingLeft(0);
        separador28.setPaddingRight(0);
        separador28.setPaddingBottom(0);
        separador28.setMarginTop(0);
        separador28.setMarginLeft(0);
        separador28.setMarginRight(0);
        separador28.setMarginBottom(0);
        separador28.setSpacing(1);
        separador28.setFlexVflex("ftFalse");
        separador28.setFlexHflex("ftFalse");
        separador28.setScrollable(false);
        separador28.setBoxShadowConfigHorizontalLength(10);
        separador28.setBoxShadowConfigVerticalLength(10);
        separador28.setBoxShadowConfigBlurRadius(5);
        separador28.setBoxShadowConfigSpreadRadius(0);
        separador28.setBoxShadowConfigShadowColor("clBlack");
        separador28.setBoxShadowConfigOpacity(75);
        separador28.setVAlign("tvTop");
        vBoxOrcPreNotasSelecionadas.addChildren(separador28);
        separador28.applyProperties();
    }

    public TFTabsheet tabFormasDePagamento = new TFTabsheet();

    private void init_tabFormasDePagamento() {
        tabFormasDePagamento.setName("tabFormasDePagamento");
        tabFormasDePagamento.setCaption("Formas de Pagamento");
        tabFormasDePagamento.setVisible(true);
        tabFormasDePagamento.setClosable(false);
        pgcAgrupamentoDePagamentos.addChildren(tabFormasDePagamento);
        tabFormasDePagamento.applyProperties();
    }

    public TFVBox vBoxFormasDePagamento = new TFVBox();

    private void init_vBoxFormasDePagamento() {
        vBoxFormasDePagamento.setName("vBoxFormasDePagamento");
        vBoxFormasDePagamento.setLeft(0);
        vBoxFormasDePagamento.setTop(0);
        vBoxFormasDePagamento.setWidth(980);
        vBoxFormasDePagamento.setHeight(330);
        vBoxFormasDePagamento.setAlign("alClient");
        vBoxFormasDePagamento.setBorderStyle("stNone");
        vBoxFormasDePagamento.setPaddingTop(0);
        vBoxFormasDePagamento.setPaddingLeft(0);
        vBoxFormasDePagamento.setPaddingRight(0);
        vBoxFormasDePagamento.setPaddingBottom(0);
        vBoxFormasDePagamento.setMarginTop(0);
        vBoxFormasDePagamento.setMarginLeft(0);
        vBoxFormasDePagamento.setMarginRight(0);
        vBoxFormasDePagamento.setMarginBottom(0);
        vBoxFormasDePagamento.setSpacing(1);
        vBoxFormasDePagamento.setFlexVflex("ftTrue");
        vBoxFormasDePagamento.setFlexHflex("ftTrue");
        vBoxFormasDePagamento.setScrollable(true);
        vBoxFormasDePagamento.setBoxShadowConfigHorizontalLength(10);
        vBoxFormasDePagamento.setBoxShadowConfigVerticalLength(10);
        vBoxFormasDePagamento.setBoxShadowConfigBlurRadius(5);
        vBoxFormasDePagamento.setBoxShadowConfigSpreadRadius(0);
        vBoxFormasDePagamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxFormasDePagamento.setBoxShadowConfigOpacity(75);
        tabFormasDePagamento.addChildren(vBoxFormasDePagamento);
        vBoxFormasDePagamento.applyProperties();
    }

    public TFVBox vBoxTopoFormasDePagamento = new TFVBox();

    private void init_vBoxTopoFormasDePagamento() {
        vBoxTopoFormasDePagamento.setName("vBoxTopoFormasDePagamento");
        vBoxTopoFormasDePagamento.setLeft(0);
        vBoxTopoFormasDePagamento.setTop(0);
        vBoxTopoFormasDePagamento.setWidth(970);
        vBoxTopoFormasDePagamento.setHeight(20);
        vBoxTopoFormasDePagamento.setBorderStyle("stNone");
        vBoxTopoFormasDePagamento.setPaddingTop(0);
        vBoxTopoFormasDePagamento.setPaddingLeft(10);
        vBoxTopoFormasDePagamento.setPaddingRight(0);
        vBoxTopoFormasDePagamento.setPaddingBottom(0);
        vBoxTopoFormasDePagamento.setMarginTop(0);
        vBoxTopoFormasDePagamento.setMarginLeft(0);
        vBoxTopoFormasDePagamento.setMarginRight(0);
        vBoxTopoFormasDePagamento.setMarginBottom(0);
        vBoxTopoFormasDePagamento.setSpacing(1);
        vBoxTopoFormasDePagamento.setFlexVflex("ftFalse");
        vBoxTopoFormasDePagamento.setFlexHflex("ftFalse");
        vBoxTopoFormasDePagamento.setScrollable(false);
        vBoxTopoFormasDePagamento.setBoxShadowConfigHorizontalLength(10);
        vBoxTopoFormasDePagamento.setBoxShadowConfigVerticalLength(10);
        vBoxTopoFormasDePagamento.setBoxShadowConfigBlurRadius(5);
        vBoxTopoFormasDePagamento.setBoxShadowConfigSpreadRadius(0);
        vBoxTopoFormasDePagamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxTopoFormasDePagamento.setBoxShadowConfigOpacity(75);
        vBoxFormasDePagamento.addChildren(vBoxTopoFormasDePagamento);
        vBoxTopoFormasDePagamento.applyProperties();
    }

    public TFLabel lblTituloParamAdiantamento = new TFLabel();

    private void init_lblTituloParamAdiantamento() {
        lblTituloParamAdiantamento.setName("lblTituloParamAdiantamento");
        lblTituloParamAdiantamento.setLeft(0);
        lblTituloParamAdiantamento.setTop(0);
        lblTituloParamAdiantamento.setWidth(173);
        lblTituloParamAdiantamento.setHeight(13);
        lblTituloParamAdiantamento.setCaption("Par\u00E2metros Adiantamento c /Cart\u00E3o");
        lblTituloParamAdiantamento.setFontColor("clWindowText");
        lblTituloParamAdiantamento.setFontSize(-11);
        lblTituloParamAdiantamento.setFontName("Tahoma");
        lblTituloParamAdiantamento.setFontStyle("[]");
        lblTituloParamAdiantamento.setVerticalAlignment("taVerticalCenter");
        lblTituloParamAdiantamento.setWordBreak(false);
        vBoxTopoFormasDePagamento.addChildren(lblTituloParamAdiantamento);
        lblTituloParamAdiantamento.applyProperties();
    }

    public TFHBox hBoxParametrosAdiantamento = new TFHBox();

    private void init_hBoxParametrosAdiantamento() {
        hBoxParametrosAdiantamento.setName("hBoxParametrosAdiantamento");
        hBoxParametrosAdiantamento.setLeft(0);
        hBoxParametrosAdiantamento.setTop(21);
        hBoxParametrosAdiantamento.setWidth(971);
        hBoxParametrosAdiantamento.setHeight(50);
        hBoxParametrosAdiantamento.setBorderStyle("stNone");
        hBoxParametrosAdiantamento.setPaddingTop(0);
        hBoxParametrosAdiantamento.setPaddingLeft(0);
        hBoxParametrosAdiantamento.setPaddingRight(0);
        hBoxParametrosAdiantamento.setPaddingBottom(0);
        hBoxParametrosAdiantamento.setMarginTop(0);
        hBoxParametrosAdiantamento.setMarginLeft(0);
        hBoxParametrosAdiantamento.setMarginRight(0);
        hBoxParametrosAdiantamento.setMarginBottom(0);
        hBoxParametrosAdiantamento.setSpacing(1);
        hBoxParametrosAdiantamento.setFlexVflex("ftFalse");
        hBoxParametrosAdiantamento.setFlexHflex("ftFalse");
        hBoxParametrosAdiantamento.setScrollable(false);
        hBoxParametrosAdiantamento.setBoxShadowConfigHorizontalLength(10);
        hBoxParametrosAdiantamento.setBoxShadowConfigVerticalLength(10);
        hBoxParametrosAdiantamento.setBoxShadowConfigBlurRadius(5);
        hBoxParametrosAdiantamento.setBoxShadowConfigSpreadRadius(0);
        hBoxParametrosAdiantamento.setBoxShadowConfigShadowColor("clBlack");
        hBoxParametrosAdiantamento.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.setVAlign("tvTop");
        vBoxFormasDePagamento.addChildren(hBoxParametrosAdiantamento);
        hBoxParametrosAdiantamento.applyProperties();
    }

    public TFVBox separador30 = new TFVBox();

    private void init_separador30() {
        separador30.setName("separador30");
        separador30.setLeft(0);
        separador30.setTop(0);
        separador30.setWidth(10);
        separador30.setHeight(45);
        separador30.setBorderStyle("stNone");
        separador30.setPaddingTop(0);
        separador30.setPaddingLeft(0);
        separador30.setPaddingRight(0);
        separador30.setPaddingBottom(0);
        separador30.setMarginTop(0);
        separador30.setMarginLeft(0);
        separador30.setMarginRight(0);
        separador30.setMarginBottom(0);
        separador30.setSpacing(1);
        separador30.setFlexVflex("ftFalse");
        separador30.setFlexHflex("ftFalse");
        separador30.setScrollable(false);
        separador30.setBoxShadowConfigHorizontalLength(10);
        separador30.setBoxShadowConfigVerticalLength(10);
        separador30.setBoxShadowConfigBlurRadius(5);
        separador30.setBoxShadowConfigSpreadRadius(0);
        separador30.setBoxShadowConfigShadowColor("clBlack");
        separador30.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.addChildren(separador30);
        separador30.applyProperties();
    }

    public TFVBox vBoxTipoAdiantamento = new TFVBox();

    private void init_vBoxTipoAdiantamento() {
        vBoxTipoAdiantamento.setName("vBoxTipoAdiantamento");
        vBoxTipoAdiantamento.setLeft(10);
        vBoxTipoAdiantamento.setTop(0);
        vBoxTipoAdiantamento.setWidth(100);
        vBoxTipoAdiantamento.setHeight(45);
        vBoxTipoAdiantamento.setBorderStyle("stNone");
        vBoxTipoAdiantamento.setPaddingTop(0);
        vBoxTipoAdiantamento.setPaddingLeft(0);
        vBoxTipoAdiantamento.setPaddingRight(0);
        vBoxTipoAdiantamento.setPaddingBottom(0);
        vBoxTipoAdiantamento.setMarginTop(0);
        vBoxTipoAdiantamento.setMarginLeft(0);
        vBoxTipoAdiantamento.setMarginRight(0);
        vBoxTipoAdiantamento.setMarginBottom(0);
        vBoxTipoAdiantamento.setSpacing(1);
        vBoxTipoAdiantamento.setFlexVflex("ftFalse");
        vBoxTipoAdiantamento.setFlexHflex("ftFalse");
        vBoxTipoAdiantamento.setScrollable(false);
        vBoxTipoAdiantamento.setBoxShadowConfigHorizontalLength(10);
        vBoxTipoAdiantamento.setBoxShadowConfigVerticalLength(10);
        vBoxTipoAdiantamento.setBoxShadowConfigBlurRadius(5);
        vBoxTipoAdiantamento.setBoxShadowConfigSpreadRadius(0);
        vBoxTipoAdiantamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxTipoAdiantamento.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.addChildren(vBoxTipoAdiantamento);
        vBoxTipoAdiantamento.applyProperties();
    }

    public TFLabel lblTipoAdiantamento = new TFLabel();

    private void init_lblTipoAdiantamento() {
        lblTipoAdiantamento.setName("lblTipoAdiantamento");
        lblTipoAdiantamento.setLeft(0);
        lblTipoAdiantamento.setTop(0);
        lblTipoAdiantamento.setWidth(94);
        lblTipoAdiantamento.setHeight(13);
        lblTipoAdiantamento.setCaption("Tipo Adiantamento:");
        lblTipoAdiantamento.setFontColor("clWindowText");
        lblTipoAdiantamento.setFontSize(-11);
        lblTipoAdiantamento.setFontName("Tahoma");
        lblTipoAdiantamento.setFontStyle("[]");
        lblTipoAdiantamento.setVerticalAlignment("taVerticalCenter");
        lblTipoAdiantamento.setWordBreak(false);
        vBoxTipoAdiantamento.addChildren(lblTipoAdiantamento);
        lblTipoAdiantamento.applyProperties();
    }

    public TFString edtTipoAdiantamento = new TFString();

    private void init_edtTipoAdiantamento() {
        edtTipoAdiantamento.setName("edtTipoAdiantamento");
        edtTipoAdiantamento.setLeft(0);
        edtTipoAdiantamento.setTop(14);
        edtTipoAdiantamento.setWidth(93);
        edtTipoAdiantamento.setHeight(24);
        edtTipoAdiantamento.setTable(tbLeadsPgtoAgrupadoParam);
        edtTipoAdiantamento.setFieldName("TIPO_ADIANTAMENTO");
        edtTipoAdiantamento.setFlex(true);
        edtTipoAdiantamento.setRequired(false);
        edtTipoAdiantamento.setConstraintCheckWhen("cwImmediate");
        edtTipoAdiantamento.setConstraintCheckType("ctExpression");
        edtTipoAdiantamento.setConstraintFocusOnError(false);
        edtTipoAdiantamento.setConstraintEnableUI(true);
        edtTipoAdiantamento.setConstraintEnabled(false);
        edtTipoAdiantamento.setConstraintFormCheck(true);
        edtTipoAdiantamento.setCharCase("ccNormal");
        edtTipoAdiantamento.setPwd(false);
        edtTipoAdiantamento.setMaxlength(0);
        edtTipoAdiantamento.setEnabled(false);
        edtTipoAdiantamento.setFontColor("clWindowText");
        edtTipoAdiantamento.setFontSize(-13);
        edtTipoAdiantamento.setFontName("Tahoma");
        edtTipoAdiantamento.setFontStyle("[]");
        edtTipoAdiantamento.setSaveLiteralCharacter(false);
        edtTipoAdiantamento.applyProperties();
        vBoxTipoAdiantamento.addChildren(edtTipoAdiantamento);
        addValidatable(edtTipoAdiantamento);
    }

    public TFVBox separador31 = new TFVBox();

    private void init_separador31() {
        separador31.setName("separador31");
        separador31.setLeft(110);
        separador31.setTop(0);
        separador31.setWidth(5);
        separador31.setHeight(45);
        separador31.setBorderStyle("stNone");
        separador31.setPaddingTop(0);
        separador31.setPaddingLeft(0);
        separador31.setPaddingRight(0);
        separador31.setPaddingBottom(0);
        separador31.setMarginTop(0);
        separador31.setMarginLeft(0);
        separador31.setMarginRight(0);
        separador31.setMarginBottom(0);
        separador31.setSpacing(1);
        separador31.setFlexVflex("ftFalse");
        separador31.setFlexHflex("ftFalse");
        separador31.setScrollable(false);
        separador31.setBoxShadowConfigHorizontalLength(10);
        separador31.setBoxShadowConfigVerticalLength(10);
        separador31.setBoxShadowConfigBlurRadius(5);
        separador31.setBoxShadowConfigSpreadRadius(0);
        separador31.setBoxShadowConfigShadowColor("clBlack");
        separador31.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.addChildren(separador31);
        separador31.applyProperties();
    }

    public TFVBox vBoxNatRecDespAdiantamento = new TFVBox();

    private void init_vBoxNatRecDespAdiantamento() {
        vBoxNatRecDespAdiantamento.setName("vBoxNatRecDespAdiantamento");
        vBoxNatRecDespAdiantamento.setLeft(115);
        vBoxNatRecDespAdiantamento.setTop(0);
        vBoxNatRecDespAdiantamento.setWidth(168);
        vBoxNatRecDespAdiantamento.setHeight(45);
        vBoxNatRecDespAdiantamento.setBorderStyle("stNone");
        vBoxNatRecDespAdiantamento.setPaddingTop(0);
        vBoxNatRecDespAdiantamento.setPaddingLeft(0);
        vBoxNatRecDespAdiantamento.setPaddingRight(0);
        vBoxNatRecDespAdiantamento.setPaddingBottom(0);
        vBoxNatRecDespAdiantamento.setMarginTop(0);
        vBoxNatRecDespAdiantamento.setMarginLeft(0);
        vBoxNatRecDespAdiantamento.setMarginRight(0);
        vBoxNatRecDespAdiantamento.setMarginBottom(0);
        vBoxNatRecDespAdiantamento.setSpacing(1);
        vBoxNatRecDespAdiantamento.setFlexVflex("ftFalse");
        vBoxNatRecDespAdiantamento.setFlexHflex("ftFalse");
        vBoxNatRecDespAdiantamento.setScrollable(false);
        vBoxNatRecDespAdiantamento.setBoxShadowConfigHorizontalLength(10);
        vBoxNatRecDespAdiantamento.setBoxShadowConfigVerticalLength(10);
        vBoxNatRecDespAdiantamento.setBoxShadowConfigBlurRadius(5);
        vBoxNatRecDespAdiantamento.setBoxShadowConfigSpreadRadius(0);
        vBoxNatRecDespAdiantamento.setBoxShadowConfigShadowColor("clBlack");
        vBoxNatRecDespAdiantamento.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.addChildren(vBoxNatRecDespAdiantamento);
        vBoxNatRecDespAdiantamento.applyProperties();
    }

    public TFLabel lblNatRecDespAdiantamento = new TFLabel();

    private void init_lblNatRecDespAdiantamento() {
        lblNatRecDespAdiantamento.setName("lblNatRecDespAdiantamento");
        lblNatRecDespAdiantamento.setLeft(0);
        lblNatRecDespAdiantamento.setTop(0);
        lblNatRecDespAdiantamento.setWidth(163);
        lblNatRecDespAdiantamento.setHeight(13);
        lblNatRecDespAdiantamento.setCaption("Nat.: Rec.: Desp.: Adiantamento:");
        lblNatRecDespAdiantamento.setFontColor("clWindowText");
        lblNatRecDespAdiantamento.setFontSize(-11);
        lblNatRecDespAdiantamento.setFontName("Tahoma");
        lblNatRecDespAdiantamento.setFontStyle("[]");
        lblNatRecDespAdiantamento.setVerticalAlignment("taVerticalCenter");
        lblNatRecDespAdiantamento.setWordBreak(false);
        vBoxNatRecDespAdiantamento.addChildren(lblNatRecDespAdiantamento);
        lblNatRecDespAdiantamento.applyProperties();
    }

    public TFString edtNatRecDespAdiantamento = new TFString();

    private void init_edtNatRecDespAdiantamento() {
        edtNatRecDespAdiantamento.setName("edtNatRecDespAdiantamento");
        edtNatRecDespAdiantamento.setLeft(0);
        edtNatRecDespAdiantamento.setTop(14);
        edtNatRecDespAdiantamento.setWidth(159);
        edtNatRecDespAdiantamento.setHeight(24);
        edtNatRecDespAdiantamento.setTable(tbLeadsPgtoAgrupadoParam);
        edtNatRecDespAdiantamento.setFieldName("NAT_REC_DESP_ADT");
        edtNatRecDespAdiantamento.setFlex(true);
        edtNatRecDespAdiantamento.setRequired(false);
        edtNatRecDespAdiantamento.setConstraintCheckWhen("cwImmediate");
        edtNatRecDespAdiantamento.setConstraintCheckType("ctExpression");
        edtNatRecDespAdiantamento.setConstraintFocusOnError(false);
        edtNatRecDespAdiantamento.setConstraintEnableUI(true);
        edtNatRecDespAdiantamento.setConstraintEnabled(false);
        edtNatRecDespAdiantamento.setConstraintFormCheck(true);
        edtNatRecDespAdiantamento.setCharCase("ccNormal");
        edtNatRecDespAdiantamento.setPwd(false);
        edtNatRecDespAdiantamento.setMaxlength(0);
        edtNatRecDespAdiantamento.setEnabled(false);
        edtNatRecDespAdiantamento.setFontColor("clWindowText");
        edtNatRecDespAdiantamento.setFontSize(-13);
        edtNatRecDespAdiantamento.setFontName("Tahoma");
        edtNatRecDespAdiantamento.setFontStyle("[]");
        edtNatRecDespAdiantamento.setSaveLiteralCharacter(false);
        edtNatRecDespAdiantamento.applyProperties();
        vBoxNatRecDespAdiantamento.addChildren(edtNatRecDespAdiantamento);
        addValidatable(edtNatRecDespAdiantamento);
    }

    public TFVBox separador32 = new TFVBox();

    private void init_separador32() {
        separador32.setName("separador32");
        separador32.setLeft(283);
        separador32.setTop(0);
        separador32.setWidth(5);
        separador32.setHeight(45);
        separador32.setBorderStyle("stNone");
        separador32.setPaddingTop(0);
        separador32.setPaddingLeft(0);
        separador32.setPaddingRight(0);
        separador32.setPaddingBottom(0);
        separador32.setMarginTop(0);
        separador32.setMarginLeft(0);
        separador32.setMarginRight(0);
        separador32.setMarginBottom(0);
        separador32.setSpacing(1);
        separador32.setFlexVflex("ftFalse");
        separador32.setFlexHflex("ftFalse");
        separador32.setScrollable(false);
        separador32.setBoxShadowConfigHorizontalLength(10);
        separador32.setBoxShadowConfigVerticalLength(10);
        separador32.setBoxShadowConfigBlurRadius(5);
        separador32.setBoxShadowConfigSpreadRadius(0);
        separador32.setBoxShadowConfigShadowColor("clBlack");
        separador32.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.addChildren(separador32);
        separador32.applyProperties();
    }

    public TFVBox vBoxNatRecDespCartao = new TFVBox();

    private void init_vBoxNatRecDespCartao() {
        vBoxNatRecDespCartao.setName("vBoxNatRecDespCartao");
        vBoxNatRecDespCartao.setLeft(288);
        vBoxNatRecDespCartao.setTop(0);
        vBoxNatRecDespCartao.setWidth(134);
        vBoxNatRecDespCartao.setHeight(45);
        vBoxNatRecDespCartao.setBorderStyle("stNone");
        vBoxNatRecDespCartao.setPaddingTop(0);
        vBoxNatRecDespCartao.setPaddingLeft(0);
        vBoxNatRecDespCartao.setPaddingRight(0);
        vBoxNatRecDespCartao.setPaddingBottom(0);
        vBoxNatRecDespCartao.setMarginTop(0);
        vBoxNatRecDespCartao.setMarginLeft(0);
        vBoxNatRecDespCartao.setMarginRight(0);
        vBoxNatRecDespCartao.setMarginBottom(0);
        vBoxNatRecDespCartao.setSpacing(1);
        vBoxNatRecDespCartao.setFlexVflex("ftFalse");
        vBoxNatRecDespCartao.setFlexHflex("ftFalse");
        vBoxNatRecDespCartao.setScrollable(false);
        vBoxNatRecDespCartao.setBoxShadowConfigHorizontalLength(10);
        vBoxNatRecDespCartao.setBoxShadowConfigVerticalLength(10);
        vBoxNatRecDespCartao.setBoxShadowConfigBlurRadius(5);
        vBoxNatRecDespCartao.setBoxShadowConfigSpreadRadius(0);
        vBoxNatRecDespCartao.setBoxShadowConfigShadowColor("clBlack");
        vBoxNatRecDespCartao.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.addChildren(vBoxNatRecDespCartao);
        vBoxNatRecDespCartao.applyProperties();
    }

    public TFLabel lblNatRecDespCartao = new TFLabel();

    private void init_lblNatRecDespCartao() {
        lblNatRecDespCartao.setName("lblNatRecDespCartao");
        lblNatRecDespCartao.setLeft(0);
        lblNatRecDespCartao.setTop(0);
        lblNatRecDespCartao.setWidth(129);
        lblNatRecDespCartao.setHeight(13);
        lblNatRecDespCartao.setCaption("Nat.: Rec.: Desp.: Cart\u00E3o:");
        lblNatRecDespCartao.setFontColor("clWindowText");
        lblNatRecDespCartao.setFontSize(-11);
        lblNatRecDespCartao.setFontName("Tahoma");
        lblNatRecDespCartao.setFontStyle("[]");
        lblNatRecDespCartao.setVerticalAlignment("taVerticalCenter");
        lblNatRecDespCartao.setWordBreak(false);
        vBoxNatRecDespCartao.addChildren(lblNatRecDespCartao);
        lblNatRecDespCartao.applyProperties();
    }

    public TFString edtNatRecDespCartao = new TFString();

    private void init_edtNatRecDespCartao() {
        edtNatRecDespCartao.setName("edtNatRecDespCartao");
        edtNatRecDespCartao.setLeft(0);
        edtNatRecDespCartao.setTop(14);
        edtNatRecDespCartao.setWidth(126);
        edtNatRecDespCartao.setHeight(24);
        edtNatRecDespCartao.setTable(tbLeadsPgtoAgrupadoParam);
        edtNatRecDespCartao.setFieldName("NAT_REC_DESP_CART");
        edtNatRecDespCartao.setFlex(false);
        edtNatRecDespCartao.setRequired(false);
        edtNatRecDespCartao.setConstraintCheckWhen("cwImmediate");
        edtNatRecDespCartao.setConstraintCheckType("ctExpression");
        edtNatRecDespCartao.setConstraintFocusOnError(false);
        edtNatRecDespCartao.setConstraintEnableUI(true);
        edtNatRecDespCartao.setConstraintEnabled(false);
        edtNatRecDespCartao.setConstraintFormCheck(true);
        edtNatRecDespCartao.setCharCase("ccNormal");
        edtNatRecDespCartao.setPwd(false);
        edtNatRecDespCartao.setMaxlength(0);
        edtNatRecDespCartao.setEnabled(false);
        edtNatRecDespCartao.setFontColor("clWindowText");
        edtNatRecDespCartao.setFontSize(-13);
        edtNatRecDespCartao.setFontName("Tahoma");
        edtNatRecDespCartao.setFontStyle("[]");
        edtNatRecDespCartao.setSaveLiteralCharacter(false);
        edtNatRecDespCartao.applyProperties();
        vBoxNatRecDespCartao.addChildren(edtNatRecDespCartao);
        addValidatable(edtNatRecDespCartao);
    }

    public TFVBox separador33 = new TFVBox();

    private void init_separador33() {
        separador33.setName("separador33");
        separador33.setLeft(422);
        separador33.setTop(0);
        separador33.setWidth(5);
        separador33.setHeight(45);
        separador33.setBorderStyle("stNone");
        separador33.setPaddingTop(0);
        separador33.setPaddingLeft(0);
        separador33.setPaddingRight(0);
        separador33.setPaddingBottom(0);
        separador33.setMarginTop(0);
        separador33.setMarginLeft(0);
        separador33.setMarginRight(0);
        separador33.setMarginBottom(0);
        separador33.setSpacing(1);
        separador33.setFlexVflex("ftFalse");
        separador33.setFlexHflex("ftFalse");
        separador33.setScrollable(false);
        separador33.setBoxShadowConfigHorizontalLength(10);
        separador33.setBoxShadowConfigVerticalLength(10);
        separador33.setBoxShadowConfigBlurRadius(5);
        separador33.setBoxShadowConfigSpreadRadius(0);
        separador33.setBoxShadowConfigShadowColor("clBlack");
        separador33.setBoxShadowConfigOpacity(75);
        hBoxParametrosAdiantamento.addChildren(separador33);
        separador33.applyProperties();
    }

    public TFHBox hBoxFormasPgtoParcelas = new TFHBox();

    private void init_hBoxFormasPgtoParcelas() {
        hBoxFormasPgtoParcelas.setName("hBoxFormasPgtoParcelas");
        hBoxFormasPgtoParcelas.setLeft(0);
        hBoxFormasPgtoParcelas.setTop(72);
        hBoxFormasPgtoParcelas.setWidth(972);
        hBoxFormasPgtoParcelas.setHeight(60);
        hBoxFormasPgtoParcelas.setAlign("alTop");
        hBoxFormasPgtoParcelas.setBorderStyle("stNone");
        hBoxFormasPgtoParcelas.setPaddingTop(0);
        hBoxFormasPgtoParcelas.setPaddingLeft(0);
        hBoxFormasPgtoParcelas.setPaddingRight(0);
        hBoxFormasPgtoParcelas.setPaddingBottom(0);
        hBoxFormasPgtoParcelas.setMarginTop(0);
        hBoxFormasPgtoParcelas.setMarginLeft(0);
        hBoxFormasPgtoParcelas.setMarginRight(0);
        hBoxFormasPgtoParcelas.setMarginBottom(0);
        hBoxFormasPgtoParcelas.setSpacing(1);
        hBoxFormasPgtoParcelas.setFlexVflex("ftFalse");
        hBoxFormasPgtoParcelas.setFlexHflex("ftTrue");
        hBoxFormasPgtoParcelas.setScrollable(false);
        hBoxFormasPgtoParcelas.setBoxShadowConfigHorizontalLength(10);
        hBoxFormasPgtoParcelas.setBoxShadowConfigVerticalLength(10);
        hBoxFormasPgtoParcelas.setBoxShadowConfigBlurRadius(5);
        hBoxFormasPgtoParcelas.setBoxShadowConfigSpreadRadius(0);
        hBoxFormasPgtoParcelas.setBoxShadowConfigShadowColor("clBlack");
        hBoxFormasPgtoParcelas.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.setVAlign("tvTop");
        vBoxFormasDePagamento.addChildren(hBoxFormasPgtoParcelas);
        hBoxFormasPgtoParcelas.applyProperties();
    }

    public TFVBox separador34 = new TFVBox();

    private void init_separador34() {
        separador34.setName("separador34");
        separador34.setLeft(0);
        separador34.setTop(0);
        separador34.setWidth(10);
        separador34.setHeight(55);
        separador34.setBorderStyle("stNone");
        separador34.setPaddingTop(0);
        separador34.setPaddingLeft(0);
        separador34.setPaddingRight(0);
        separador34.setPaddingBottom(0);
        separador34.setMarginTop(0);
        separador34.setMarginLeft(0);
        separador34.setMarginRight(0);
        separador34.setMarginBottom(0);
        separador34.setSpacing(1);
        separador34.setFlexVflex("ftFalse");
        separador34.setFlexHflex("ftFalse");
        separador34.setScrollable(false);
        separador34.setBoxShadowConfigHorizontalLength(10);
        separador34.setBoxShadowConfigVerticalLength(10);
        separador34.setBoxShadowConfigBlurRadius(5);
        separador34.setBoxShadowConfigSpreadRadius(0);
        separador34.setBoxShadowConfigShadowColor("clBlack");
        separador34.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(separador34);
        separador34.applyProperties();
    }

    public TFVBox vBoxTotal = new TFVBox();

    private void init_vBoxTotal() {
        vBoxTotal.setName("vBoxTotal");
        vBoxTotal.setLeft(10);
        vBoxTotal.setTop(0);
        vBoxTotal.setWidth(80);
        vBoxTotal.setHeight(55);
        vBoxTotal.setBorderStyle("stNone");
        vBoxTotal.setPaddingTop(0);
        vBoxTotal.setPaddingLeft(0);
        vBoxTotal.setPaddingRight(0);
        vBoxTotal.setPaddingBottom(0);
        vBoxTotal.setMarginTop(0);
        vBoxTotal.setMarginLeft(0);
        vBoxTotal.setMarginRight(0);
        vBoxTotal.setMarginBottom(0);
        vBoxTotal.setSpacing(1);
        vBoxTotal.setFlexVflex("ftFalse");
        vBoxTotal.setFlexHflex("ftFalse");
        vBoxTotal.setScrollable(false);
        vBoxTotal.setBoxShadowConfigHorizontalLength(10);
        vBoxTotal.setBoxShadowConfigVerticalLength(10);
        vBoxTotal.setBoxShadowConfigBlurRadius(5);
        vBoxTotal.setBoxShadowConfigSpreadRadius(0);
        vBoxTotal.setBoxShadowConfigShadowColor("clBlack");
        vBoxTotal.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(vBoxTotal);
        vBoxTotal.applyProperties();
    }

    public TFVBox separador43 = new TFVBox();

    private void init_separador43() {
        separador43.setName("separador43");
        separador43.setLeft(0);
        separador43.setTop(0);
        separador43.setWidth(58);
        separador43.setHeight(8);
        separador43.setBorderStyle("stNone");
        separador43.setPaddingTop(0);
        separador43.setPaddingLeft(0);
        separador43.setPaddingRight(0);
        separador43.setPaddingBottom(0);
        separador43.setMarginTop(0);
        separador43.setMarginLeft(0);
        separador43.setMarginRight(0);
        separador43.setMarginBottom(0);
        separador43.setSpacing(1);
        separador43.setFlexVflex("ftFalse");
        separador43.setFlexHflex("ftFalse");
        separador43.setScrollable(false);
        separador43.setBoxShadowConfigHorizontalLength(10);
        separador43.setBoxShadowConfigVerticalLength(10);
        separador43.setBoxShadowConfigBlurRadius(5);
        separador43.setBoxShadowConfigSpreadRadius(0);
        separador43.setBoxShadowConfigShadowColor("clBlack");
        separador43.setBoxShadowConfigOpacity(75);
        vBoxTotal.addChildren(separador43);
        separador43.applyProperties();
    }

    public TFLabel lblTotal = new TFLabel();

    private void init_lblTotal() {
        lblTotal.setName("lblTotal");
        lblTotal.setLeft(0);
        lblTotal.setTop(9);
        lblTotal.setWidth(28);
        lblTotal.setHeight(13);
        lblTotal.setCaption("Total:");
        lblTotal.setFontColor("clWindowText");
        lblTotal.setFontSize(-11);
        lblTotal.setFontName("Tahoma");
        lblTotal.setFontStyle("[]");
        lblTotal.setVerticalAlignment("taVerticalCenter");
        lblTotal.setWordBreak(false);
        vBoxTotal.addChildren(lblTotal);
        lblTotal.applyProperties();
    }

    public TFDecimal edtTotal = new TFDecimal();

    private void init_edtTotal() {
        edtTotal.setName("edtTotal");
        edtTotal.setLeft(0);
        edtTotal.setTop(23);
        edtTotal.setWidth(75);
        edtTotal.setHeight(24);
        edtTotal.setFlex(false);
        edtTotal.setRequired(false);
        edtTotal.setConstraintCheckWhen("cwImmediate");
        edtTotal.setConstraintCheckType("ctExpression");
        edtTotal.setConstraintFocusOnError(false);
        edtTotal.setConstraintEnableUI(true);
        edtTotal.setConstraintEnabled(false);
        edtTotal.setConstraintFormCheck(true);
        edtTotal.setMaxlength(0);
        edtTotal.setPrecision(0);
        edtTotal.setEnabled(false);
        edtTotal.setFontColor("clWindowText");
        edtTotal.setFontSize(-13);
        edtTotal.setFontName("Tahoma");
        edtTotal.setFontStyle("[]");
        edtTotal.setAlignment("taRightJustify");
        vBoxTotal.addChildren(edtTotal);
        edtTotal.applyProperties();
        addValidatable(edtTotal);
    }

    public TFVBox separador35 = new TFVBox();

    private void init_separador35() {
        separador35.setName("separador35");
        separador35.setLeft(90);
        separador35.setTop(0);
        separador35.setWidth(5);
        separador35.setHeight(55);
        separador35.setBorderStyle("stNone");
        separador35.setPaddingTop(0);
        separador35.setPaddingLeft(0);
        separador35.setPaddingRight(0);
        separador35.setPaddingBottom(0);
        separador35.setMarginTop(0);
        separador35.setMarginLeft(0);
        separador35.setMarginRight(0);
        separador35.setMarginBottom(0);
        separador35.setSpacing(1);
        separador35.setFlexVflex("ftFalse");
        separador35.setFlexHflex("ftFalse");
        separador35.setScrollable(false);
        separador35.setBoxShadowConfigHorizontalLength(10);
        separador35.setBoxShadowConfigVerticalLength(10);
        separador35.setBoxShadowConfigBlurRadius(5);
        separador35.setBoxShadowConfigSpreadRadius(0);
        separador35.setBoxShadowConfigShadowColor("clBlack");
        separador35.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(separador35);
        separador35.applyProperties();
    }

    public TFVBox vBoxFormasPgto = new TFVBox();

    private void init_vBoxFormasPgto() {
        vBoxFormasPgto.setName("vBoxFormasPgto");
        vBoxFormasPgto.setLeft(95);
        vBoxFormasPgto.setTop(0);
        vBoxFormasPgto.setWidth(115);
        vBoxFormasPgto.setHeight(55);
        vBoxFormasPgto.setBorderStyle("stNone");
        vBoxFormasPgto.setPaddingTop(0);
        vBoxFormasPgto.setPaddingLeft(0);
        vBoxFormasPgto.setPaddingRight(0);
        vBoxFormasPgto.setPaddingBottom(0);
        vBoxFormasPgto.setMarginTop(0);
        vBoxFormasPgto.setMarginLeft(0);
        vBoxFormasPgto.setMarginRight(0);
        vBoxFormasPgto.setMarginBottom(0);
        vBoxFormasPgto.setSpacing(1);
        vBoxFormasPgto.setFlexVflex("ftFalse");
        vBoxFormasPgto.setFlexHflex("ftFalse");
        vBoxFormasPgto.setScrollable(false);
        vBoxFormasPgto.setBoxShadowConfigHorizontalLength(10);
        vBoxFormasPgto.setBoxShadowConfigVerticalLength(10);
        vBoxFormasPgto.setBoxShadowConfigBlurRadius(5);
        vBoxFormasPgto.setBoxShadowConfigSpreadRadius(0);
        vBoxFormasPgto.setBoxShadowConfigShadowColor("clBlack");
        vBoxFormasPgto.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(vBoxFormasPgto);
        vBoxFormasPgto.applyProperties();
    }

    public TFVBox separador44 = new TFVBox();

    private void init_separador44() {
        separador44.setName("separador44");
        separador44.setLeft(0);
        separador44.setTop(0);
        separador44.setWidth(101);
        separador44.setHeight(8);
        separador44.setBorderStyle("stNone");
        separador44.setPaddingTop(0);
        separador44.setPaddingLeft(0);
        separador44.setPaddingRight(0);
        separador44.setPaddingBottom(0);
        separador44.setMarginTop(0);
        separador44.setMarginLeft(0);
        separador44.setMarginRight(0);
        separador44.setMarginBottom(0);
        separador44.setSpacing(1);
        separador44.setFlexVflex("ftFalse");
        separador44.setFlexHflex("ftFalse");
        separador44.setScrollable(false);
        separador44.setBoxShadowConfigHorizontalLength(10);
        separador44.setBoxShadowConfigVerticalLength(10);
        separador44.setBoxShadowConfigBlurRadius(5);
        separador44.setBoxShadowConfigSpreadRadius(0);
        separador44.setBoxShadowConfigShadowColor("clBlack");
        separador44.setBoxShadowConfigOpacity(75);
        vBoxFormasPgto.addChildren(separador44);
        separador44.applyProperties();
    }

    public TFLabel lblFormasPgto = new TFLabel();

    private void init_lblFormasPgto() {
        lblFormasPgto.setName("lblFormasPgto");
        lblFormasPgto.setLeft(0);
        lblFormasPgto.setTop(9);
        lblFormasPgto.setWidth(107);
        lblFormasPgto.setHeight(13);
        lblFormasPgto.setCaption("Formas de Pagamento");
        lblFormasPgto.setFontColor("clWindowText");
        lblFormasPgto.setFontSize(-11);
        lblFormasPgto.setFontName("Tahoma");
        lblFormasPgto.setFontStyle("[]");
        lblFormasPgto.setVerticalAlignment("taVerticalCenter");
        lblFormasPgto.setWordBreak(false);
        vBoxFormasPgto.addChildren(lblFormasPgto);
        lblFormasPgto.applyProperties();
    }

    public TFCombo cbbFormasPgto = new TFCombo();

    private void init_cbbFormasPgto() {
        cbbFormasPgto.setName("cbbFormasPgto");
        cbbFormasPgto.setLeft(0);
        cbbFormasPgto.setTop(23);
        cbbFormasPgto.setWidth(110);
        cbbFormasPgto.setHeight(22);
        cbbFormasPgto.setFlex(true);
        cbbFormasPgto.setListOptions("2 - D\u00E9bito=2;3 - Cr\u00E9dito=3;4 - PIX-Sitef=4");
        cbbFormasPgto.setReadOnly(true);
        cbbFormasPgto.setRequired(false);
        cbbFormasPgto.setPrompt("Selecione");
        cbbFormasPgto.setConstraintCheckWhen("cwImmediate");
        cbbFormasPgto.setConstraintCheckType("ctExpression");
        cbbFormasPgto.setConstraintFocusOnError(false);
        cbbFormasPgto.setConstraintEnableUI(true);
        cbbFormasPgto.setConstraintEnabled(false);
        cbbFormasPgto.setConstraintFormCheck(true);
        cbbFormasPgto.setClearOnDelKey(true);
        cbbFormasPgto.setUseClearButton(false);
        cbbFormasPgto.setHideClearButtonOnNullValue(false);
        cbbFormasPgto.addEventListener("onChange", (EventListener<Event<Object>>)(Event<Object> event) -> {
            cbbFormasPgtoChange(event);
            processarFlow("FrmAgrupaPagamentos", "cbbFormasPgto", "OnChange");
        });
        vBoxFormasPgto.addChildren(cbbFormasPgto);
        cbbFormasPgto.applyProperties();
        addValidatable(cbbFormasPgto);
    }

    public TFVBox separador36 = new TFVBox();

    private void init_separador36() {
        separador36.setName("separador36");
        separador36.setLeft(210);
        separador36.setTop(0);
        separador36.setWidth(5);
        separador36.setHeight(55);
        separador36.setBorderStyle("stNone");
        separador36.setPaddingTop(0);
        separador36.setPaddingLeft(0);
        separador36.setPaddingRight(0);
        separador36.setPaddingBottom(0);
        separador36.setMarginTop(0);
        separador36.setMarginLeft(0);
        separador36.setMarginRight(0);
        separador36.setMarginBottom(0);
        separador36.setSpacing(1);
        separador36.setFlexVflex("ftFalse");
        separador36.setFlexHflex("ftFalse");
        separador36.setScrollable(false);
        separador36.setBoxShadowConfigHorizontalLength(10);
        separador36.setBoxShadowConfigVerticalLength(10);
        separador36.setBoxShadowConfigBlurRadius(5);
        separador36.setBoxShadowConfigSpreadRadius(0);
        separador36.setBoxShadowConfigShadowColor("clBlack");
        separador36.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(separador36);
        separador36.applyProperties();
    }

    public TFVBox vBoxQtdParcelas = new TFVBox();

    private void init_vBoxQtdParcelas() {
        vBoxQtdParcelas.setName("vBoxQtdParcelas");
        vBoxQtdParcelas.setLeft(215);
        vBoxQtdParcelas.setTop(0);
        vBoxQtdParcelas.setWidth(55);
        vBoxQtdParcelas.setHeight(55);
        vBoxQtdParcelas.setHint("Quantidade de Parcelas.");
        vBoxQtdParcelas.setBorderStyle("stNone");
        vBoxQtdParcelas.setPaddingTop(0);
        vBoxQtdParcelas.setPaddingLeft(0);
        vBoxQtdParcelas.setPaddingRight(0);
        vBoxQtdParcelas.setPaddingBottom(0);
        vBoxQtdParcelas.setMarginTop(0);
        vBoxQtdParcelas.setMarginLeft(0);
        vBoxQtdParcelas.setMarginRight(0);
        vBoxQtdParcelas.setMarginBottom(0);
        vBoxQtdParcelas.setSpacing(1);
        vBoxQtdParcelas.setFlexVflex("ftFalse");
        vBoxQtdParcelas.setFlexHflex("ftFalse");
        vBoxQtdParcelas.setScrollable(false);
        vBoxQtdParcelas.setBoxShadowConfigHorizontalLength(10);
        vBoxQtdParcelas.setBoxShadowConfigVerticalLength(10);
        vBoxQtdParcelas.setBoxShadowConfigBlurRadius(5);
        vBoxQtdParcelas.setBoxShadowConfigSpreadRadius(0);
        vBoxQtdParcelas.setBoxShadowConfigShadowColor("clBlack");
        vBoxQtdParcelas.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(vBoxQtdParcelas);
        vBoxQtdParcelas.applyProperties();
    }

    public TFVBox separador45 = new TFVBox();

    private void init_separador45() {
        separador45.setName("separador45");
        separador45.setLeft(0);
        separador45.setTop(0);
        separador45.setWidth(50);
        separador45.setHeight(8);
        separador45.setBorderStyle("stNone");
        separador45.setPaddingTop(0);
        separador45.setPaddingLeft(0);
        separador45.setPaddingRight(0);
        separador45.setPaddingBottom(0);
        separador45.setMarginTop(0);
        separador45.setMarginLeft(0);
        separador45.setMarginRight(0);
        separador45.setMarginBottom(0);
        separador45.setSpacing(1);
        separador45.setFlexVflex("ftFalse");
        separador45.setFlexHflex("ftFalse");
        separador45.setScrollable(false);
        separador45.setBoxShadowConfigHorizontalLength(10);
        separador45.setBoxShadowConfigVerticalLength(10);
        separador45.setBoxShadowConfigBlurRadius(5);
        separador45.setBoxShadowConfigSpreadRadius(0);
        separador45.setBoxShadowConfigShadowColor("clBlack");
        separador45.setBoxShadowConfigOpacity(75);
        vBoxQtdParcelas.addChildren(separador45);
        separador45.applyProperties();
    }

    public TFLabel lblQtdParcelas = new TFLabel();

    private void init_lblQtdParcelas() {
        lblQtdParcelas.setName("lblQtdParcelas");
        lblQtdParcelas.setLeft(0);
        lblQtdParcelas.setTop(9);
        lblQtdParcelas.setWidth(44);
        lblQtdParcelas.setHeight(13);
        lblQtdParcelas.setCaption("Parcelas:");
        lblQtdParcelas.setFontColor("clWindowText");
        lblQtdParcelas.setFontSize(-11);
        lblQtdParcelas.setFontName("Tahoma");
        lblQtdParcelas.setFontStyle("[]");
        lblQtdParcelas.setVerticalAlignment("taVerticalCenter");
        lblQtdParcelas.setWordBreak(false);
        vBoxQtdParcelas.addChildren(lblQtdParcelas);
        lblQtdParcelas.applyProperties();
    }

    public TFString edtQtdParcelas = new TFString();

    private void init_edtQtdParcelas() {
        edtQtdParcelas.setName("edtQtdParcelas");
        edtQtdParcelas.setLeft(0);
        edtQtdParcelas.setTop(23);
        edtQtdParcelas.setWidth(50);
        edtQtdParcelas.setHeight(24);
        edtQtdParcelas.setHint("Quantidade de Parcelas.");
        edtQtdParcelas.setFlex(true);
        edtQtdParcelas.setRequired(false);
        edtQtdParcelas.setConstraintCheckWhen("cwImmediate");
        edtQtdParcelas.setConstraintCheckType("ctExpression");
        edtQtdParcelas.setConstraintFocusOnError(false);
        edtQtdParcelas.setConstraintEnableUI(true);
        edtQtdParcelas.setConstraintEnabled(false);
        edtQtdParcelas.setConstraintFormCheck(true);
        edtQtdParcelas.setCharCase("ccNormal");
        edtQtdParcelas.setPwd(false);
        edtQtdParcelas.setMaxlength(0);
        edtQtdParcelas.setFontColor("clWindowText");
        edtQtdParcelas.setFontSize(-13);
        edtQtdParcelas.setFontName("Tahoma");
        edtQtdParcelas.setFontStyle("[]");
        edtQtdParcelas.setSaveLiteralCharacter(false);
        edtQtdParcelas.applyProperties();
        vBoxQtdParcelas.addChildren(edtQtdParcelas);
        addValidatable(edtQtdParcelas);
    }

    public TFVBox separador37 = new TFVBox();

    private void init_separador37() {
        separador37.setName("separador37");
        separador37.setLeft(270);
        separador37.setTop(0);
        separador37.setWidth(5);
        separador37.setHeight(55);
        separador37.setBorderStyle("stNone");
        separador37.setPaddingTop(0);
        separador37.setPaddingLeft(0);
        separador37.setPaddingRight(0);
        separador37.setPaddingBottom(0);
        separador37.setMarginTop(0);
        separador37.setMarginLeft(0);
        separador37.setMarginRight(0);
        separador37.setMarginBottom(0);
        separador37.setSpacing(1);
        separador37.setFlexVflex("ftFalse");
        separador37.setFlexHflex("ftFalse");
        separador37.setScrollable(false);
        separador37.setBoxShadowConfigHorizontalLength(10);
        separador37.setBoxShadowConfigVerticalLength(10);
        separador37.setBoxShadowConfigBlurRadius(5);
        separador37.setBoxShadowConfigSpreadRadius(0);
        separador37.setBoxShadowConfigShadowColor("clBlack");
        separador37.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(separador37);
        separador37.applyProperties();
    }

    public TFVBox vBoxValor = new TFVBox();

    private void init_vBoxValor() {
        vBoxValor.setName("vBoxValor");
        vBoxValor.setLeft(275);
        vBoxValor.setTop(0);
        vBoxValor.setWidth(80);
        vBoxValor.setHeight(55);
        vBoxValor.setBorderStyle("stNone");
        vBoxValor.setPaddingTop(0);
        vBoxValor.setPaddingLeft(0);
        vBoxValor.setPaddingRight(0);
        vBoxValor.setPaddingBottom(0);
        vBoxValor.setMarginTop(0);
        vBoxValor.setMarginLeft(0);
        vBoxValor.setMarginRight(0);
        vBoxValor.setMarginBottom(0);
        vBoxValor.setSpacing(1);
        vBoxValor.setFlexVflex("ftFalse");
        vBoxValor.setFlexHflex("ftFalse");
        vBoxValor.setScrollable(false);
        vBoxValor.setBoxShadowConfigHorizontalLength(10);
        vBoxValor.setBoxShadowConfigVerticalLength(10);
        vBoxValor.setBoxShadowConfigBlurRadius(5);
        vBoxValor.setBoxShadowConfigSpreadRadius(0);
        vBoxValor.setBoxShadowConfigShadowColor("clBlack");
        vBoxValor.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(vBoxValor);
        vBoxValor.applyProperties();
    }

    public TFVBox separador46 = new TFVBox();

    private void init_separador46() {
        separador46.setName("separador46");
        separador46.setLeft(0);
        separador46.setTop(0);
        separador46.setWidth(75);
        separador46.setHeight(8);
        separador46.setBorderStyle("stNone");
        separador46.setPaddingTop(0);
        separador46.setPaddingLeft(0);
        separador46.setPaddingRight(0);
        separador46.setPaddingBottom(0);
        separador46.setMarginTop(0);
        separador46.setMarginLeft(0);
        separador46.setMarginRight(0);
        separador46.setMarginBottom(0);
        separador46.setSpacing(1);
        separador46.setFlexVflex("ftFalse");
        separador46.setFlexHflex("ftFalse");
        separador46.setScrollable(false);
        separador46.setBoxShadowConfigHorizontalLength(10);
        separador46.setBoxShadowConfigVerticalLength(10);
        separador46.setBoxShadowConfigBlurRadius(5);
        separador46.setBoxShadowConfigSpreadRadius(0);
        separador46.setBoxShadowConfigShadowColor("clBlack");
        separador46.setBoxShadowConfigOpacity(75);
        vBoxValor.addChildren(separador46);
        separador46.applyProperties();
    }

    public TFLabel lblValor = new TFLabel();

    private void init_lblValor() {
        lblValor.setName("lblValor");
        lblValor.setLeft(0);
        lblValor.setTop(9);
        lblValor.setWidth(44);
        lblValor.setHeight(13);
        lblValor.setCaption("Valor R$:");
        lblValor.setFontColor("clWindowText");
        lblValor.setFontSize(-11);
        lblValor.setFontName("Tahoma");
        lblValor.setFontStyle("[]");
        lblValor.setVerticalAlignment("taVerticalCenter");
        lblValor.setWordBreak(false);
        vBoxValor.addChildren(lblValor);
        lblValor.applyProperties();
    }

    public TFDecimal edtValor = new TFDecimal();

    private void init_edtValor() {
        edtValor.setName("edtValor");
        edtValor.setLeft(0);
        edtValor.setTop(23);
        edtValor.setWidth(75);
        edtValor.setHeight(24);
        edtValor.setFlex(false);
        edtValor.setRequired(false);
        edtValor.setConstraintCheckWhen("cwImmediate");
        edtValor.setConstraintCheckType("ctExpression");
        edtValor.setConstraintFocusOnError(false);
        edtValor.setConstraintEnableUI(true);
        edtValor.setConstraintEnabled(false);
        edtValor.setConstraintFormCheck(true);
        edtValor.setMaxlength(0);
        edtValor.setPrecision(0);
        edtValor.setFontColor("clWindowText");
        edtValor.setFontSize(-13);
        edtValor.setFontName("Tahoma");
        edtValor.setFontStyle("[]");
        edtValor.setAlignment("taRightJustify");
        edtValor.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtValorEnter(event);
            processarFlow("FrmAgrupaPagamentos", "edtValor", "OnEnter");
        });
        vBoxValor.addChildren(edtValor);
        edtValor.applyProperties();
        addValidatable(edtValor);
    }

    public TFVBox vBoxAplicarFormasPgto = new TFVBox();

    private void init_vBoxAplicarFormasPgto() {
        vBoxAplicarFormasPgto.setName("vBoxAplicarFormasPgto");
        vBoxAplicarFormasPgto.setLeft(355);
        vBoxAplicarFormasPgto.setTop(0);
        vBoxAplicarFormasPgto.setWidth(28);
        vBoxAplicarFormasPgto.setHeight(55);
        vBoxAplicarFormasPgto.setHint("Aplicar Formas de Pagamento");
        vBoxAplicarFormasPgto.setBorderStyle("stNone");
        vBoxAplicarFormasPgto.setPaddingTop(0);
        vBoxAplicarFormasPgto.setPaddingLeft(0);
        vBoxAplicarFormasPgto.setPaddingRight(0);
        vBoxAplicarFormasPgto.setPaddingBottom(0);
        vBoxAplicarFormasPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAplicarFormasPagamentosClick(event);
            processarFlow("FrmAgrupaPagamentos", "vBoxAplicarFormasPgto", "OnClick");
        });
        vBoxAplicarFormasPgto.setMarginTop(0);
        vBoxAplicarFormasPgto.setMarginLeft(0);
        vBoxAplicarFormasPgto.setMarginRight(0);
        vBoxAplicarFormasPgto.setMarginBottom(0);
        vBoxAplicarFormasPgto.setSpacing(1);
        vBoxAplicarFormasPgto.setFlexVflex("ftFalse");
        vBoxAplicarFormasPgto.setFlexHflex("ftFalse");
        vBoxAplicarFormasPgto.setScrollable(false);
        vBoxAplicarFormasPgto.setBoxShadowConfigHorizontalLength(10);
        vBoxAplicarFormasPgto.setBoxShadowConfigVerticalLength(10);
        vBoxAplicarFormasPgto.setBoxShadowConfigBlurRadius(5);
        vBoxAplicarFormasPgto.setBoxShadowConfigSpreadRadius(0);
        vBoxAplicarFormasPgto.setBoxShadowConfigShadowColor("clBlack");
        vBoxAplicarFormasPgto.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(vBoxAplicarFormasPgto);
        vBoxAplicarFormasPgto.applyProperties();
    }

    public TFVBox separador47 = new TFVBox();

    private void init_separador47() {
        separador47.setName("separador47");
        separador47.setLeft(0);
        separador47.setTop(0);
        separador47.setWidth(24);
        separador47.setHeight(24);
        separador47.setBorderStyle("stNone");
        separador47.setPaddingTop(0);
        separador47.setPaddingLeft(0);
        separador47.setPaddingRight(0);
        separador47.setPaddingBottom(0);
        separador47.setMarginTop(0);
        separador47.setMarginLeft(0);
        separador47.setMarginRight(0);
        separador47.setMarginBottom(0);
        separador47.setSpacing(1);
        separador47.setFlexVflex("ftFalse");
        separador47.setFlexHflex("ftFalse");
        separador47.setScrollable(false);
        separador47.setBoxShadowConfigHorizontalLength(10);
        separador47.setBoxShadowConfigVerticalLength(10);
        separador47.setBoxShadowConfigBlurRadius(5);
        separador47.setBoxShadowConfigSpreadRadius(0);
        separador47.setBoxShadowConfigShadowColor("clBlack");
        separador47.setBoxShadowConfigOpacity(75);
        vBoxAplicarFormasPgto.addChildren(separador47);
        separador47.applyProperties();
    }

    public TFIconClass btnAplicarFormasPgto = new TFIconClass();

    private void init_btnAplicarFormasPgto() {
        btnAplicarFormasPgto.setName("btnAplicarFormasPgto");
        btnAplicarFormasPgto.setLeft(0);
        btnAplicarFormasPgto.setTop(25);
        btnAplicarFormasPgto.setHint("Aplicar Formas de Pagamento");
        btnAplicarFormasPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAplicarFormasPagamentosClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnAplicarFormasPgto", "OnClick");
        });
        btnAplicarFormasPgto.setIconClass("plus-square");
        btnAplicarFormasPgto.setSize(24);
        btnAplicarFormasPgto.setColor("clTeal");
        vBoxAplicarFormasPgto.addChildren(btnAplicarFormasPgto);
        btnAplicarFormasPgto.applyProperties();
    }

    public TFVBox separador38 = new TFVBox();

    private void init_separador38() {
        separador38.setName("separador38");
        separador38.setLeft(383);
        separador38.setTop(0);
        separador38.setWidth(5);
        separador38.setHeight(55);
        separador38.setBorderStyle("stNone");
        separador38.setPaddingTop(0);
        separador38.setPaddingLeft(0);
        separador38.setPaddingRight(0);
        separador38.setPaddingBottom(0);
        separador38.setVisible(false);
        separador38.setMarginTop(0);
        separador38.setMarginLeft(0);
        separador38.setMarginRight(0);
        separador38.setMarginBottom(0);
        separador38.setSpacing(1);
        separador38.setFlexVflex("ftFalse");
        separador38.setFlexHflex("ftFalse");
        separador38.setScrollable(false);
        separador38.setBoxShadowConfigHorizontalLength(10);
        separador38.setBoxShadowConfigVerticalLength(10);
        separador38.setBoxShadowConfigBlurRadius(5);
        separador38.setBoxShadowConfigSpreadRadius(0);
        separador38.setBoxShadowConfigShadowColor("clBlack");
        separador38.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(separador38);
        separador38.applyProperties();
    }

    public TFVBox vBoxRemoverFormasPgto = new TFVBox();

    private void init_vBoxRemoverFormasPgto() {
        vBoxRemoverFormasPgto.setName("vBoxRemoverFormasPgto");
        vBoxRemoverFormasPgto.setLeft(388);
        vBoxRemoverFormasPgto.setTop(0);
        vBoxRemoverFormasPgto.setWidth(28);
        vBoxRemoverFormasPgto.setHeight(55);
        vBoxRemoverFormasPgto.setHint("Remover Todos");
        vBoxRemoverFormasPgto.setBorderStyle("stNone");
        vBoxRemoverFormasPgto.setPaddingTop(0);
        vBoxRemoverFormasPgto.setPaddingLeft(0);
        vBoxRemoverFormasPgto.setPaddingRight(0);
        vBoxRemoverFormasPgto.setPaddingBottom(0);
        vBoxRemoverFormasPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnRemoverFormasPagamentosClick(event);
            processarFlow("FrmAgrupaPagamentos", "vBoxRemoverFormasPgto", "OnClick");
        });
        vBoxRemoverFormasPgto.setMarginTop(0);
        vBoxRemoverFormasPgto.setMarginLeft(0);
        vBoxRemoverFormasPgto.setMarginRight(0);
        vBoxRemoverFormasPgto.setMarginBottom(0);
        vBoxRemoverFormasPgto.setSpacing(1);
        vBoxRemoverFormasPgto.setFlexVflex("ftFalse");
        vBoxRemoverFormasPgto.setFlexHflex("ftFalse");
        vBoxRemoverFormasPgto.setScrollable(false);
        vBoxRemoverFormasPgto.setBoxShadowConfigHorizontalLength(10);
        vBoxRemoverFormasPgto.setBoxShadowConfigVerticalLength(10);
        vBoxRemoverFormasPgto.setBoxShadowConfigBlurRadius(5);
        vBoxRemoverFormasPgto.setBoxShadowConfigSpreadRadius(0);
        vBoxRemoverFormasPgto.setBoxShadowConfigShadowColor("clBlack");
        vBoxRemoverFormasPgto.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(vBoxRemoverFormasPgto);
        vBoxRemoverFormasPgto.applyProperties();
    }

    public TFVBox separador48 = new TFVBox();

    private void init_separador48() {
        separador48.setName("separador48");
        separador48.setLeft(0);
        separador48.setTop(0);
        separador48.setWidth(22);
        separador48.setHeight(24);
        separador48.setBorderStyle("stNone");
        separador48.setPaddingTop(0);
        separador48.setPaddingLeft(0);
        separador48.setPaddingRight(0);
        separador48.setPaddingBottom(0);
        separador48.setMarginTop(0);
        separador48.setMarginLeft(0);
        separador48.setMarginRight(0);
        separador48.setMarginBottom(0);
        separador48.setSpacing(1);
        separador48.setFlexVflex("ftFalse");
        separador48.setFlexHflex("ftFalse");
        separador48.setScrollable(false);
        separador48.setBoxShadowConfigHorizontalLength(10);
        separador48.setBoxShadowConfigVerticalLength(10);
        separador48.setBoxShadowConfigBlurRadius(5);
        separador48.setBoxShadowConfigSpreadRadius(0);
        separador48.setBoxShadowConfigShadowColor("clBlack");
        separador48.setBoxShadowConfigOpacity(75);
        vBoxRemoverFormasPgto.addChildren(separador48);
        separador48.applyProperties();
    }

    public TFIconClass btnRemoverFormasPgto = new TFIconClass();

    private void init_btnRemoverFormasPgto() {
        btnRemoverFormasPgto.setName("btnRemoverFormasPgto");
        btnRemoverFormasPgto.setLeft(0);
        btnRemoverFormasPgto.setTop(25);
        btnRemoverFormasPgto.setHint("Remover Todos");
        btnRemoverFormasPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnRemoverFormasPagamentosClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnRemoverFormasPgto", "OnClick");
        });
        btnRemoverFormasPgto.setIconClass("trash");
        btnRemoverFormasPgto.setSize(24);
        btnRemoverFormasPgto.setColor("clTeal");
        vBoxRemoverFormasPgto.addChildren(btnRemoverFormasPgto);
        btnRemoverFormasPgto.applyProperties();
    }

    public TFVBox separador39 = new TFVBox();

    private void init_separador39() {
        separador39.setName("separador39");
        separador39.setLeft(416);
        separador39.setTop(0);
        separador39.setWidth(5);
        separador39.setHeight(55);
        separador39.setBorderStyle("stNone");
        separador39.setPaddingTop(0);
        separador39.setPaddingLeft(0);
        separador39.setPaddingRight(0);
        separador39.setPaddingBottom(0);
        separador39.setMarginTop(0);
        separador39.setMarginLeft(0);
        separador39.setMarginRight(0);
        separador39.setMarginBottom(0);
        separador39.setSpacing(1);
        separador39.setFlexVflex("ftFalse");
        separador39.setFlexHflex("ftTrue");
        separador39.setScrollable(false);
        separador39.setBoxShadowConfigHorizontalLength(10);
        separador39.setBoxShadowConfigVerticalLength(10);
        separador39.setBoxShadowConfigBlurRadius(5);
        separador39.setBoxShadowConfigSpreadRadius(0);
        separador39.setBoxShadowConfigShadowColor("clBlack");
        separador39.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(separador39);
        separador39.applyProperties();
    }

    public TFVBox vBoxConfirmarFormasPgto = new TFVBox();

    private void init_vBoxConfirmarFormasPgto() {
        vBoxConfirmarFormasPgto.setName("vBoxConfirmarFormasPgto");
        vBoxConfirmarFormasPgto.setLeft(421);
        vBoxConfirmarFormasPgto.setTop(0);
        vBoxConfirmarFormasPgto.setWidth(92);
        vBoxConfirmarFormasPgto.setHeight(55);
        vBoxConfirmarFormasPgto.setBorderStyle("stNone");
        vBoxConfirmarFormasPgto.setPaddingTop(0);
        vBoxConfirmarFormasPgto.setPaddingLeft(0);
        vBoxConfirmarFormasPgto.setPaddingRight(0);
        vBoxConfirmarFormasPgto.setPaddingBottom(0);
        vBoxConfirmarFormasPgto.setMarginTop(0);
        vBoxConfirmarFormasPgto.setMarginLeft(0);
        vBoxConfirmarFormasPgto.setMarginRight(0);
        vBoxConfirmarFormasPgto.setMarginBottom(0);
        vBoxConfirmarFormasPgto.setSpacing(1);
        vBoxConfirmarFormasPgto.setFlexVflex("ftFalse");
        vBoxConfirmarFormasPgto.setFlexHflex("ftFalse");
        vBoxConfirmarFormasPgto.setScrollable(false);
        vBoxConfirmarFormasPgto.setBoxShadowConfigHorizontalLength(10);
        vBoxConfirmarFormasPgto.setBoxShadowConfigVerticalLength(10);
        vBoxConfirmarFormasPgto.setBoxShadowConfigBlurRadius(5);
        vBoxConfirmarFormasPgto.setBoxShadowConfigSpreadRadius(0);
        vBoxConfirmarFormasPgto.setBoxShadowConfigShadowColor("clBlack");
        vBoxConfirmarFormasPgto.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(vBoxConfirmarFormasPgto);
        vBoxConfirmarFormasPgto.applyProperties();
    }

    public TFVBox separador50 = new TFVBox();

    private void init_separador50() {
        separador50.setName("separador50");
        separador50.setLeft(0);
        separador50.setTop(0);
        separador50.setWidth(57);
        separador50.setHeight(21);
        separador50.setBorderStyle("stNone");
        separador50.setPaddingTop(0);
        separador50.setPaddingLeft(0);
        separador50.setPaddingRight(0);
        separador50.setPaddingBottom(0);
        separador50.setMarginTop(0);
        separador50.setMarginLeft(0);
        separador50.setMarginRight(0);
        separador50.setMarginBottom(0);
        separador50.setSpacing(1);
        separador50.setFlexVflex("ftFalse");
        separador50.setFlexHflex("ftFalse");
        separador50.setScrollable(false);
        separador50.setBoxShadowConfigHorizontalLength(10);
        separador50.setBoxShadowConfigVerticalLength(10);
        separador50.setBoxShadowConfigBlurRadius(5);
        separador50.setBoxShadowConfigSpreadRadius(0);
        separador50.setBoxShadowConfigShadowColor("clBlack");
        separador50.setBoxShadowConfigOpacity(75);
        vBoxConfirmarFormasPgto.addChildren(separador50);
        separador50.applyProperties();
    }

    public TFButton btnConfirmarFormasPgto = new TFButton();

    private void init_btnConfirmarFormasPgto() {
        btnConfirmarFormasPgto.setName("btnConfirmarFormasPgto");
        btnConfirmarFormasPgto.setLeft(0);
        btnConfirmarFormasPgto.setTop(22);
        btnConfirmarFormasPgto.setWidth(88);
        btnConfirmarFormasPgto.setHeight(28);
        btnConfirmarFormasPgto.setCaption("Confirmar");
        btnConfirmarFormasPgto.setEnabled(false);
        btnConfirmarFormasPgto.setFontColor("clWindowText");
        btnConfirmarFormasPgto.setFontSize(-11);
        btnConfirmarFormasPgto.setFontName("Tahoma");
        btnConfirmarFormasPgto.setFontStyle("[]");
        btnConfirmarFormasPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnConfirmarFormasPgtoClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnConfirmarFormasPgto", "OnClick");
        });
        btnConfirmarFormasPgto.setImageId(16);
        btnConfirmarFormasPgto.setColor("clBtnFace");
        btnConfirmarFormasPgto.setAccess(false);
        btnConfirmarFormasPgto.setIconReverseDirection(false);
        vBoxConfirmarFormasPgto.addChildren(btnConfirmarFormasPgto);
        btnConfirmarFormasPgto.applyProperties();
    }

    public TFVBox separador52 = new TFVBox();

    private void init_separador52() {
        separador52.setName("separador52");
        separador52.setLeft(513);
        separador52.setTop(0);
        separador52.setWidth(10);
        separador52.setHeight(55);
        separador52.setBorderStyle("stNone");
        separador52.setPaddingTop(0);
        separador52.setPaddingLeft(0);
        separador52.setPaddingRight(0);
        separador52.setPaddingBottom(0);
        separador52.setMarginTop(0);
        separador52.setMarginLeft(0);
        separador52.setMarginRight(0);
        separador52.setMarginBottom(0);
        separador52.setSpacing(1);
        separador52.setFlexVflex("ftFalse");
        separador52.setFlexHflex("ftFalse");
        separador52.setScrollable(false);
        separador52.setBoxShadowConfigHorizontalLength(10);
        separador52.setBoxShadowConfigVerticalLength(10);
        separador52.setBoxShadowConfigBlurRadius(5);
        separador52.setBoxShadowConfigSpreadRadius(0);
        separador52.setBoxShadowConfigShadowColor("clBlack");
        separador52.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoParcelas.addChildren(separador52);
        separador52.applyProperties();
    }

    public TFHBox hBoxFormasPgtoOrcNotas = new TFHBox();

    private void init_hBoxFormasPgtoOrcNotas() {
        hBoxFormasPgtoOrcNotas.setName("hBoxFormasPgtoOrcNotas");
        hBoxFormasPgtoOrcNotas.setLeft(0);
        hBoxFormasPgtoOrcNotas.setTop(133);
        hBoxFormasPgtoOrcNotas.setWidth(971);
        hBoxFormasPgtoOrcNotas.setHeight(174);
        hBoxFormasPgtoOrcNotas.setBorderStyle("stNone");
        hBoxFormasPgtoOrcNotas.setPaddingTop(0);
        hBoxFormasPgtoOrcNotas.setPaddingLeft(0);
        hBoxFormasPgtoOrcNotas.setPaddingRight(0);
        hBoxFormasPgtoOrcNotas.setPaddingBottom(0);
        hBoxFormasPgtoOrcNotas.setMarginTop(0);
        hBoxFormasPgtoOrcNotas.setMarginLeft(0);
        hBoxFormasPgtoOrcNotas.setMarginRight(0);
        hBoxFormasPgtoOrcNotas.setMarginBottom(0);
        hBoxFormasPgtoOrcNotas.setSpacing(1);
        hBoxFormasPgtoOrcNotas.setFlexVflex("ftTrue");
        hBoxFormasPgtoOrcNotas.setFlexHflex("ftTrue");
        hBoxFormasPgtoOrcNotas.setScrollable(false);
        hBoxFormasPgtoOrcNotas.setBoxShadowConfigHorizontalLength(10);
        hBoxFormasPgtoOrcNotas.setBoxShadowConfigVerticalLength(10);
        hBoxFormasPgtoOrcNotas.setBoxShadowConfigBlurRadius(5);
        hBoxFormasPgtoOrcNotas.setBoxShadowConfigSpreadRadius(0);
        hBoxFormasPgtoOrcNotas.setBoxShadowConfigShadowColor("clBlack");
        hBoxFormasPgtoOrcNotas.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoOrcNotas.setVAlign("tvTop");
        vBoxFormasDePagamento.addChildren(hBoxFormasPgtoOrcNotas);
        hBoxFormasPgtoOrcNotas.applyProperties();
    }

    public TFVBox separador40 = new TFVBox();

    private void init_separador40() {
        separador40.setName("separador40");
        separador40.setLeft(0);
        separador40.setTop(0);
        separador40.setWidth(10);
        separador40.setHeight(132);
        separador40.setBorderStyle("stNone");
        separador40.setPaddingTop(0);
        separador40.setPaddingLeft(0);
        separador40.setPaddingRight(0);
        separador40.setPaddingBottom(0);
        separador40.setMarginTop(0);
        separador40.setMarginLeft(0);
        separador40.setMarginRight(0);
        separador40.setMarginBottom(0);
        separador40.setSpacing(1);
        separador40.setFlexVflex("ftTrue");
        separador40.setFlexHflex("ftFalse");
        separador40.setScrollable(false);
        separador40.setBoxShadowConfigHorizontalLength(10);
        separador40.setBoxShadowConfigVerticalLength(10);
        separador40.setBoxShadowConfigBlurRadius(5);
        separador40.setBoxShadowConfigSpreadRadius(0);
        separador40.setBoxShadowConfigShadowColor("clBlack");
        separador40.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoOrcNotas.addChildren(separador40);
        separador40.applyProperties();
    }

    public TFVBox vBoxFormasPgtoOrcNotas = new TFVBox();

    private void init_vBoxFormasPgtoOrcNotas() {
        vBoxFormasPgtoOrcNotas.setName("vBoxFormasPgtoOrcNotas");
        vBoxFormasPgtoOrcNotas.setLeft(10);
        vBoxFormasPgtoOrcNotas.setTop(0);
        vBoxFormasPgtoOrcNotas.setWidth(867);
        vBoxFormasPgtoOrcNotas.setHeight(164);
        vBoxFormasPgtoOrcNotas.setBorderStyle("stNone");
        vBoxFormasPgtoOrcNotas.setPaddingTop(0);
        vBoxFormasPgtoOrcNotas.setPaddingLeft(0);
        vBoxFormasPgtoOrcNotas.setPaddingRight(0);
        vBoxFormasPgtoOrcNotas.setPaddingBottom(0);
        vBoxFormasPgtoOrcNotas.setMarginTop(0);
        vBoxFormasPgtoOrcNotas.setMarginLeft(0);
        vBoxFormasPgtoOrcNotas.setMarginRight(0);
        vBoxFormasPgtoOrcNotas.setMarginBottom(0);
        vBoxFormasPgtoOrcNotas.setSpacing(1);
        vBoxFormasPgtoOrcNotas.setFlexVflex("ftTrue");
        vBoxFormasPgtoOrcNotas.setFlexHflex("ftTrue");
        vBoxFormasPgtoOrcNotas.setScrollable(true);
        vBoxFormasPgtoOrcNotas.setBoxShadowConfigHorizontalLength(10);
        vBoxFormasPgtoOrcNotas.setBoxShadowConfigVerticalLength(10);
        vBoxFormasPgtoOrcNotas.setBoxShadowConfigBlurRadius(5);
        vBoxFormasPgtoOrcNotas.setBoxShadowConfigSpreadRadius(0);
        vBoxFormasPgtoOrcNotas.setBoxShadowConfigShadowColor("clBlack");
        vBoxFormasPgtoOrcNotas.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoOrcNotas.addChildren(vBoxFormasPgtoOrcNotas);
        vBoxFormasPgtoOrcNotas.applyProperties();
    }

    public TFGrid gridFormasPgtoOrcNotas = new TFGrid();

    private void init_gridFormasPgtoOrcNotas() {
        gridFormasPgtoOrcNotas.setName("gridFormasPgtoOrcNotas");
        gridFormasPgtoOrcNotas.setLeft(0);
        gridFormasPgtoOrcNotas.setTop(0);
        gridFormasPgtoOrcNotas.setWidth(840);
        gridFormasPgtoOrcNotas.setHeight(143);
        gridFormasPgtoOrcNotas.setAlign("alClient");
        gridFormasPgtoOrcNotas.setTable(tbLeadsPgtoAgrupadoParc);
        gridFormasPgtoOrcNotas.setFlexVflex("ftTrue");
        gridFormasPgtoOrcNotas.setFlexHflex("ftTrue");
        gridFormasPgtoOrcNotas.setPagingEnabled(false);
        gridFormasPgtoOrcNotas.setFrozenColumns(0);
        gridFormasPgtoOrcNotas.setShowFooter(true);
        gridFormasPgtoOrcNotas.setShowHeader(true);
        gridFormasPgtoOrcNotas.setMultiSelection(false);
        gridFormasPgtoOrcNotas.setGroupingEnabled(false);
        gridFormasPgtoOrcNotas.setGroupingExpanded(false);
        gridFormasPgtoOrcNotas.setGroupingShowFooter(false);
        gridFormasPgtoOrcNotas.setCrosstabEnabled(false);
        gridFormasPgtoOrcNotas.setCrosstabGroupType("cgtConcat");
        gridFormasPgtoOrcNotas.setEditionEnabled(false);
        gridFormasPgtoOrcNotas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEQUENCIA_CARTAO");
        item0.setTitleCaption("Seq. Cart\u00E3o");
        item0.setWidth(100);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TIPO_PARCELA_CARTAO");
        item1.setTitleCaption("D\u00E9b. /Cr\u00E9d. /Pix");
        item1.setWidth(106);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("QTDE_PARCELA_CARTAO");
        item2.setTitleCaption("Qtd. Parcelas");
        item2.setWidth(95);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taRight");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("VALOR");
        item3.setTitleCaption("Valor Total Cart\u00E3o");
        item3.setWidth(128);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftString");
        item3.setFooterExpression("new String(\"R$ \"+tbLeadsPgtoAgrupadoParc.compute(\"SUM\",\"VALOR\").asDecimal()+\"\")");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        TFMaskExpression item4 = new TFMaskExpression();
        item4.setExpression("*");
        item4.setEvalType("etExpression");
        item4.setMask("R$ ,##0.00");
        item4.setPadLength(0);
        item4.setPadDirection("pdNone");
        item4.setMaskType("mtDecimal");
        item3.getMasks().add(item4);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item3);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("COD_EMPRESA");
        item5.setTitleCaption("C\u00F3d. Empresa");
        item5.setWidth(100);
        item5.setVisible(false);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("COD_EVENTO");
        item6.setTitleCaption("Cod. Evento");
        item6.setWidth(100);
        item6.setVisible(false);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("ORIGEM_EVENTO");
        item7.setTitleCaption("Origem Evento");
        item7.setWidth(120);
        item7.setVisible(false);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("COD_FORMA_PGTO");
        item8.setTitleCaption("C\u00F3d. Forma Pgto");
        item8.setWidth(130);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("OBSERVACAO");
        item9.setTitleCaption("Observa\u00E7\u00E3o");
        item9.setWidth(220);
        item9.setVisible(false);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item9);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("ID_PAGAMENTO");
        item10.setTitleCaption("Id. Pagamento");
        item10.setWidth(100);
        item10.setVisible(false);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setCheckedValue("S");
        item10.setUncheckedValue("N");
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item10);
        TFGridColumn item11 = new TFGridColumn();
        item11.setVisible(true);
        item11.setPrecision(0);
        item11.setTextAlign("taLeft");
        item11.setFieldType("ftString");
        item11.setFlexRatio(0);
        item11.setSort(false);
        item11.setImageHeader(0);
        item11.setWrap(false);
        item11.setFlex(false);
        TFImageExpression item12 = new TFImageExpression();
        item12.setExpression("*");
        item12.setHint("Remover Item Pagamento");
        item12.setEvalType("etExpression");
        item12.setImageId(700095);
        item12.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            gridFormasPgtoOrcNotasbtnRemoverFormasPagamentosClick(event);
            processarFlow("FrmAgrupaPagamentos", "item12", "OnClick");
        });
        item11.getImages().add(item12);
        item11.setCharCase("ccNormal");
        item11.setBlobConfigMimeType("bmtText");
        item11.setBlobConfigShowType("btImageViewer");
        item11.setShowLabel(true);
        item11.setEditorEditType("etTFString");
        item11.setEditorPrecision(0);
        item11.setEditorMaxLength(100);
        item11.setEditorLookupFilterKey(0);
        item11.setEditorLookupFilterDesc(0);
        item11.setEditorPopupHeight(400);
        item11.setEditorPopupWidth(400);
        item11.setEditorCharCase("ccNormal");
        item11.setEditorEnabled(false);
        item11.setEditorReadOnly(false);
        item11.setCheckedValue("S");
        item11.setUncheckedValue("N");
        item11.setHiperLink(false);
        item11.setEditorConstraintCheckWhen("cwImmediate");
        item11.setEditorConstraintCheckType("ctExpression");
        item11.setEditorConstraintFocusOnError(false);
        item11.setEditorConstraintEnableUI(true);
        item11.setEditorConstraintEnabled(false);
        item11.setEmpty(false);
        item11.setMobileOptsShowMobile(false);
        item11.setMobileOptsOrder(0);
        item11.setBoxSize(0);
        item11.setImageSrcType("istSource");
        gridFormasPgtoOrcNotas.getColumns().add(item11);
        vBoxFormasPgtoOrcNotas.addChildren(gridFormasPgtoOrcNotas);
        gridFormasPgtoOrcNotas.applyProperties();
    }

    public TFVBox separador41 = new TFVBox();

    private void init_separador41() {
        separador41.setName("separador41");
        separador41.setLeft(877);
        separador41.setTop(0);
        separador41.setWidth(10);
        separador41.setHeight(132);
        separador41.setBorderStyle("stNone");
        separador41.setPaddingTop(0);
        separador41.setPaddingLeft(0);
        separador41.setPaddingRight(0);
        separador41.setPaddingBottom(0);
        separador41.setMarginTop(0);
        separador41.setMarginLeft(0);
        separador41.setMarginRight(0);
        separador41.setMarginBottom(0);
        separador41.setSpacing(1);
        separador41.setFlexVflex("ftTrue");
        separador41.setFlexHflex("ftFalse");
        separador41.setScrollable(false);
        separador41.setBoxShadowConfigHorizontalLength(10);
        separador41.setBoxShadowConfigVerticalLength(10);
        separador41.setBoxShadowConfigBlurRadius(5);
        separador41.setBoxShadowConfigSpreadRadius(0);
        separador41.setBoxShadowConfigShadowColor("clBlack");
        separador41.setBoxShadowConfigOpacity(75);
        hBoxFormasPgtoOrcNotas.addChildren(separador41);
        separador41.applyProperties();
    }

    public TFHBox separador42 = new TFHBox();

    private void init_separador42() {
        separador42.setName("separador42");
        separador42.setLeft(0);
        separador42.setTop(308);
        separador42.setWidth(926);
        separador42.setHeight(10);
        separador42.setAlign("alBottom");
        separador42.setBorderStyle("stNone");
        separador42.setPaddingTop(0);
        separador42.setPaddingLeft(0);
        separador42.setPaddingRight(0);
        separador42.setPaddingBottom(0);
        separador42.setMarginTop(0);
        separador42.setMarginLeft(0);
        separador42.setMarginRight(0);
        separador42.setMarginBottom(0);
        separador42.setSpacing(1);
        separador42.setFlexVflex("ftFalse");
        separador42.setFlexHflex("ftFalse");
        separador42.setScrollable(false);
        separador42.setBoxShadowConfigHorizontalLength(10);
        separador42.setBoxShadowConfigVerticalLength(10);
        separador42.setBoxShadowConfigBlurRadius(5);
        separador42.setBoxShadowConfigSpreadRadius(0);
        separador42.setBoxShadowConfigShadowColor("clBlack");
        separador42.setBoxShadowConfigOpacity(75);
        separador42.setVAlign("tvTop");
        vBoxFormasDePagamento.addChildren(separador42);
        separador42.applyProperties();
    }

    public TFTabsheet tabPagamentosAgrupados = new TFTabsheet();

    private void init_tabPagamentosAgrupados() {
        tabPagamentosAgrupados.setName("tabPagamentosAgrupados");
        tabPagamentosAgrupados.setCaption("Pagamentos Agrupados");
        tabPagamentosAgrupados.setFontColor("clWindowText");
        tabPagamentosAgrupados.setFontSize(-12);
        tabPagamentosAgrupados.setFontName("Tahoma");
        tabPagamentosAgrupados.setFontStyle("[]");
        tabPagamentosAgrupados.setVisible(true);
        tabPagamentosAgrupados.setClosable(false);
        pgcAgrupaPagamentos.addChildren(tabPagamentosAgrupados);
        tabPagamentosAgrupados.applyProperties();
    }

    public TFVBox vBoxPagamentosAgrupados = new TFVBox();

    private void init_vBoxPagamentosAgrupados() {
        vBoxPagamentosAgrupados.setName("vBoxPagamentosAgrupados");
        vBoxPagamentosAgrupados.setLeft(0);
        vBoxPagamentosAgrupados.setTop(0);
        vBoxPagamentosAgrupados.setWidth(999);
        vBoxPagamentosAgrupados.setHeight(414);
        vBoxPagamentosAgrupados.setAlign("alClient");
        vBoxPagamentosAgrupados.setBorderStyle("stNone");
        vBoxPagamentosAgrupados.setPaddingTop(8);
        vBoxPagamentosAgrupados.setPaddingLeft(0);
        vBoxPagamentosAgrupados.setPaddingRight(0);
        vBoxPagamentosAgrupados.setPaddingBottom(0);
        vBoxPagamentosAgrupados.setMarginTop(0);
        vBoxPagamentosAgrupados.setMarginLeft(0);
        vBoxPagamentosAgrupados.setMarginRight(0);
        vBoxPagamentosAgrupados.setMarginBottom(0);
        vBoxPagamentosAgrupados.setSpacing(1);
        vBoxPagamentosAgrupados.setFlexVflex("ftTrue");
        vBoxPagamentosAgrupados.setFlexHflex("ftTrue");
        vBoxPagamentosAgrupados.setScrollable(true);
        vBoxPagamentosAgrupados.setBoxShadowConfigHorizontalLength(10);
        vBoxPagamentosAgrupados.setBoxShadowConfigVerticalLength(10);
        vBoxPagamentosAgrupados.setBoxShadowConfigBlurRadius(5);
        vBoxPagamentosAgrupados.setBoxShadowConfigSpreadRadius(0);
        vBoxPagamentosAgrupados.setBoxShadowConfigShadowColor("clBlack");
        vBoxPagamentosAgrupados.setBoxShadowConfigOpacity(75);
        tabPagamentosAgrupados.addChildren(vBoxPagamentosAgrupados);
        vBoxPagamentosAgrupados.applyProperties();
    }

    public TFHBox hBoxBotoesPgtoAgrupadosTopo = new TFHBox();

    private void init_hBoxBotoesPgtoAgrupadosTopo() {
        hBoxBotoesPgtoAgrupadosTopo.setName("hBoxBotoesPgtoAgrupadosTopo");
        hBoxBotoesPgtoAgrupadosTopo.setLeft(0);
        hBoxBotoesPgtoAgrupadosTopo.setTop(0);
        hBoxBotoesPgtoAgrupadosTopo.setWidth(991);
        hBoxBotoesPgtoAgrupadosTopo.setHeight(32);
        hBoxBotoesPgtoAgrupadosTopo.setAlign("alTop");
        hBoxBotoesPgtoAgrupadosTopo.setBorderStyle("stNone");
        hBoxBotoesPgtoAgrupadosTopo.setPaddingTop(0);
        hBoxBotoesPgtoAgrupadosTopo.setPaddingLeft(0);
        hBoxBotoesPgtoAgrupadosTopo.setPaddingRight(0);
        hBoxBotoesPgtoAgrupadosTopo.setPaddingBottom(0);
        hBoxBotoesPgtoAgrupadosTopo.setMarginTop(0);
        hBoxBotoesPgtoAgrupadosTopo.setMarginLeft(0);
        hBoxBotoesPgtoAgrupadosTopo.setMarginRight(0);
        hBoxBotoesPgtoAgrupadosTopo.setMarginBottom(0);
        hBoxBotoesPgtoAgrupadosTopo.setSpacing(1);
        hBoxBotoesPgtoAgrupadosTopo.setFlexVflex("ftFalse");
        hBoxBotoesPgtoAgrupadosTopo.setFlexHflex("ftTrue");
        hBoxBotoesPgtoAgrupadosTopo.setScrollable(false);
        hBoxBotoesPgtoAgrupadosTopo.setBoxShadowConfigHorizontalLength(10);
        hBoxBotoesPgtoAgrupadosTopo.setBoxShadowConfigVerticalLength(10);
        hBoxBotoesPgtoAgrupadosTopo.setBoxShadowConfigBlurRadius(5);
        hBoxBotoesPgtoAgrupadosTopo.setBoxShadowConfigSpreadRadius(0);
        hBoxBotoesPgtoAgrupadosTopo.setBoxShadowConfigShadowColor("clBlack");
        hBoxBotoesPgtoAgrupadosTopo.setBoxShadowConfigOpacity(75);
        hBoxBotoesPgtoAgrupadosTopo.setVAlign("tvTop");
        vBoxPagamentosAgrupados.addChildren(hBoxBotoesPgtoAgrupadosTopo);
        hBoxBotoesPgtoAgrupadosTopo.applyProperties();
    }

    public TFHBox separador7 = new TFHBox();

    private void init_separador7() {
        separador7.setName("separador7");
        separador7.setLeft(0);
        separador7.setTop(0);
        separador7.setWidth(10);
        separador7.setHeight(26);
        separador7.setBorderStyle("stNone");
        separador7.setPaddingTop(0);
        separador7.setPaddingLeft(0);
        separador7.setPaddingRight(0);
        separador7.setPaddingBottom(0);
        separador7.setMarginTop(0);
        separador7.setMarginLeft(0);
        separador7.setMarginRight(0);
        separador7.setMarginBottom(0);
        separador7.setSpacing(1);
        separador7.setFlexVflex("ftFalse");
        separador7.setFlexHflex("ftFalse");
        separador7.setScrollable(false);
        separador7.setBoxShadowConfigHorizontalLength(10);
        separador7.setBoxShadowConfigVerticalLength(10);
        separador7.setBoxShadowConfigBlurRadius(5);
        separador7.setBoxShadowConfigSpreadRadius(0);
        separador7.setBoxShadowConfigShadowColor("clBlack");
        separador7.setBoxShadowConfigOpacity(75);
        separador7.setVAlign("tvTop");
        hBoxBotoesPgtoAgrupadosTopo.addChildren(separador7);
        separador7.applyProperties();
    }

    public TFButton btnExcluirPgto = new TFButton();

    private void init_btnExcluirPgto() {
        btnExcluirPgto.setName("btnExcluirPgto");
        btnExcluirPgto.setLeft(10);
        btnExcluirPgto.setTop(0);
        btnExcluirPgto.setWidth(140);
        btnExcluirPgto.setHeight(28);
        btnExcluirPgto.setCaption("Excluir Agrupamento");
        btnExcluirPgto.setEnabled(false);
        btnExcluirPgto.setFontColor("clWindowText");
        btnExcluirPgto.setFontSize(-11);
        btnExcluirPgto.setFontName("Tahoma");
        btnExcluirPgto.setFontStyle("[]");
        btnExcluirPgto.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirPgtoClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnExcluirPgto", "OnClick");
        });
        btnExcluirPgto.setImageId(8);
        btnExcluirPgto.setColor("clBtnFace");
        btnExcluirPgto.setAccess(false);
        btnExcluirPgto.setIconReverseDirection(false);
        hBoxBotoesPgtoAgrupadosTopo.addChildren(btnExcluirPgto);
        btnExcluirPgto.applyProperties();
    }

    public TFHBox separador8 = new TFHBox();

    private void init_separador8() {
        separador8.setName("separador8");
        separador8.setLeft(150);
        separador8.setTop(0);
        separador8.setWidth(5);
        separador8.setHeight(26);
        separador8.setBorderStyle("stNone");
        separador8.setPaddingTop(0);
        separador8.setPaddingLeft(0);
        separador8.setPaddingRight(0);
        separador8.setPaddingBottom(0);
        separador8.setMarginTop(0);
        separador8.setMarginLeft(0);
        separador8.setMarginRight(0);
        separador8.setMarginBottom(0);
        separador8.setSpacing(1);
        separador8.setFlexVflex("ftFalse");
        separador8.setFlexHflex("ftFalse");
        separador8.setScrollable(false);
        separador8.setBoxShadowConfigHorizontalLength(10);
        separador8.setBoxShadowConfigVerticalLength(10);
        separador8.setBoxShadowConfigBlurRadius(5);
        separador8.setBoxShadowConfigSpreadRadius(0);
        separador8.setBoxShadowConfigShadowColor("clBlack");
        separador8.setBoxShadowConfigOpacity(75);
        separador8.setVAlign("tvTop");
        hBoxBotoesPgtoAgrupadosTopo.addChildren(separador8);
        separador8.applyProperties();
    }

    public TFButton btnFechamentoParcial = new TFButton();

    private void init_btnFechamentoParcial() {
        btnFechamentoParcial.setName("btnFechamentoParcial");
        btnFechamentoParcial.setLeft(155);
        btnFechamentoParcial.setTop(0);
        btnFechamentoParcial.setWidth(140);
        btnFechamentoParcial.setHeight(28);
        btnFechamentoParcial.setCaption("Fechamento Parcial");
        btnFechamentoParcial.setEnabled(false);
        btnFechamentoParcial.setFontColor("clWindowText");
        btnFechamentoParcial.setFontSize(-11);
        btnFechamentoParcial.setFontName("Tahoma");
        btnFechamentoParcial.setFontStyle("[]");
        btnFechamentoParcial.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnFechamentoParcialClick(event);
            processarFlow("FrmAgrupaPagamentos", "btnFechamentoParcial", "OnClick");
        });
        btnFechamentoParcial.setImageId(16);
        btnFechamentoParcial.setColor("clBtnFace");
        btnFechamentoParcial.setAccess(false);
        btnFechamentoParcial.setIconReverseDirection(false);
        hBoxBotoesPgtoAgrupadosTopo.addChildren(btnFechamentoParcial);
        btnFechamentoParcial.applyProperties();
    }

    public TFHBox separador9 = new TFHBox();

    private void init_separador9() {
        separador9.setName("separador9");
        separador9.setLeft(295);
        separador9.setTop(0);
        separador9.setWidth(10);
        separador9.setHeight(26);
        separador9.setBorderStyle("stNone");
        separador9.setPaddingTop(0);
        separador9.setPaddingLeft(0);
        separador9.setPaddingRight(0);
        separador9.setPaddingBottom(0);
        separador9.setMarginTop(0);
        separador9.setMarginLeft(0);
        separador9.setMarginRight(0);
        separador9.setMarginBottom(0);
        separador9.setSpacing(1);
        separador9.setFlexVflex("ftFalse");
        separador9.setFlexHflex("ftFalse");
        separador9.setScrollable(false);
        separador9.setBoxShadowConfigHorizontalLength(10);
        separador9.setBoxShadowConfigVerticalLength(10);
        separador9.setBoxShadowConfigBlurRadius(5);
        separador9.setBoxShadowConfigSpreadRadius(0);
        separador9.setBoxShadowConfigShadowColor("clBlack");
        separador9.setBoxShadowConfigOpacity(75);
        separador9.setVAlign("tvTop");
        hBoxBotoesPgtoAgrupadosTopo.addChildren(separador9);
        separador9.applyProperties();
    }

    public TFGroupbox grpBoxOrcPrenotaAgrupadas = new TFGroupbox();

    private void init_grpBoxOrcPrenotaAgrupadas() {
        grpBoxOrcPrenotaAgrupadas.setName("grpBoxOrcPrenotaAgrupadas");
        grpBoxOrcPrenotaAgrupadas.setLeft(0);
        grpBoxOrcPrenotaAgrupadas.setTop(33);
        grpBoxOrcPrenotaAgrupadas.setWidth(992);
        grpBoxOrcPrenotaAgrupadas.setHeight(185);
        grpBoxOrcPrenotaAgrupadas.setAlign("alTop");
        grpBoxOrcPrenotaAgrupadas.setCaption("OS, Or\u00E7amentos e Pr\u00E9-Notas Agrupadas ");
        grpBoxOrcPrenotaAgrupadas.setFontColor("clWindowText");
        grpBoxOrcPrenotaAgrupadas.setFontSize(-11);
        grpBoxOrcPrenotaAgrupadas.setFontName("Tahoma");
        grpBoxOrcPrenotaAgrupadas.setFontStyle("[]");
        grpBoxOrcPrenotaAgrupadas.setFlexVflex("ftFalse");
        grpBoxOrcPrenotaAgrupadas.setFlexHflex("ftTrue");
        grpBoxOrcPrenotaAgrupadas.setScrollable(false);
        grpBoxOrcPrenotaAgrupadas.setClosable(true);
        grpBoxOrcPrenotaAgrupadas.setClosed(true);
        grpBoxOrcPrenotaAgrupadas.addEventListener("onClose", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grpBoxOrcPrenotaAgrupadasClose(event);
            processarFlow("FrmAgrupaPagamentos", "grpBoxOrcPrenotaAgrupadas", "OnClose");
        });
        grpBoxOrcPrenotaAgrupadas.setOrient("coHorizontal");
        grpBoxOrcPrenotaAgrupadas.setStyle("grp3D");
        grpBoxOrcPrenotaAgrupadas.setHeaderImageId(31001);
        vBoxPagamentosAgrupados.addChildren(grpBoxOrcPrenotaAgrupadas);
        grpBoxOrcPrenotaAgrupadas.applyProperties();
    }

    public TFVBox vBoxOrcPrenotaAgrupadas = new TFVBox();

    private void init_vBoxOrcPrenotaAgrupadas() {
        vBoxOrcPrenotaAgrupadas.setName("vBoxOrcPrenotaAgrupadas");
        vBoxOrcPrenotaAgrupadas.setLeft(2);
        vBoxOrcPrenotaAgrupadas.setTop(15);
        vBoxOrcPrenotaAgrupadas.setWidth(988);
        vBoxOrcPrenotaAgrupadas.setHeight(168);
        vBoxOrcPrenotaAgrupadas.setAlign("alClient");
        vBoxOrcPrenotaAgrupadas.setBorderStyle("stNone");
        vBoxOrcPrenotaAgrupadas.setPaddingTop(0);
        vBoxOrcPrenotaAgrupadas.setPaddingLeft(0);
        vBoxOrcPrenotaAgrupadas.setPaddingRight(0);
        vBoxOrcPrenotaAgrupadas.setPaddingBottom(0);
        vBoxOrcPrenotaAgrupadas.setMarginTop(0);
        vBoxOrcPrenotaAgrupadas.setMarginLeft(0);
        vBoxOrcPrenotaAgrupadas.setMarginRight(0);
        vBoxOrcPrenotaAgrupadas.setMarginBottom(0);
        vBoxOrcPrenotaAgrupadas.setSpacing(1);
        vBoxOrcPrenotaAgrupadas.setFlexVflex("ftFalse");
        vBoxOrcPrenotaAgrupadas.setFlexHflex("ftTrue");
        vBoxOrcPrenotaAgrupadas.setScrollable(false);
        vBoxOrcPrenotaAgrupadas.setBoxShadowConfigHorizontalLength(10);
        vBoxOrcPrenotaAgrupadas.setBoxShadowConfigVerticalLength(10);
        vBoxOrcPrenotaAgrupadas.setBoxShadowConfigBlurRadius(5);
        vBoxOrcPrenotaAgrupadas.setBoxShadowConfigSpreadRadius(0);
        vBoxOrcPrenotaAgrupadas.setBoxShadowConfigShadowColor("clBlack");
        vBoxOrcPrenotaAgrupadas.setBoxShadowConfigOpacity(75);
        grpBoxOrcPrenotaAgrupadas.addChildren(vBoxOrcPrenotaAgrupadas);
        vBoxOrcPrenotaAgrupadas.applyProperties();
    }

    public TFHBox hBoxMaisFiltros = new TFHBox();

    private void init_hBoxMaisFiltros() {
        hBoxMaisFiltros.setName("hBoxMaisFiltros");
        hBoxMaisFiltros.setLeft(0);
        hBoxMaisFiltros.setTop(0);
        hBoxMaisFiltros.setWidth(140);
        hBoxMaisFiltros.setHeight(25);
        hBoxMaisFiltros.setHint("Filtros");
        hBoxMaisFiltros.setAlign("alTop");
        hBoxMaisFiltros.setBorderStyle("stNone");
        hBoxMaisFiltros.setPaddingTop(0);
        hBoxMaisFiltros.setPaddingLeft(0);
        hBoxMaisFiltros.setPaddingRight(0);
        hBoxMaisFiltros.setPaddingBottom(0);
        hBoxMaisFiltros.setVisible(false);
        hBoxMaisFiltros.setMarginTop(0);
        hBoxMaisFiltros.setMarginLeft(0);
        hBoxMaisFiltros.setMarginRight(0);
        hBoxMaisFiltros.setMarginBottom(0);
        hBoxMaisFiltros.setSpacing(1);
        hBoxMaisFiltros.setFlexVflex("ftFalse");
        hBoxMaisFiltros.setFlexHflex("ftTrue");
        hBoxMaisFiltros.setScrollable(false);
        hBoxMaisFiltros.setBoxShadowConfigHorizontalLength(10);
        hBoxMaisFiltros.setBoxShadowConfigVerticalLength(10);
        hBoxMaisFiltros.setBoxShadowConfigBlurRadius(5);
        hBoxMaisFiltros.setBoxShadowConfigSpreadRadius(0);
        hBoxMaisFiltros.setBoxShadowConfigShadowColor("clBlack");
        hBoxMaisFiltros.setBoxShadowConfigOpacity(75);
        hBoxMaisFiltros.setVAlign("tvTop");
        vBoxOrcPrenotaAgrupadas.addChildren(hBoxMaisFiltros);
        hBoxMaisFiltros.applyProperties();
    }

    public TFHBox separador10 = new TFHBox();

    private void init_separador10() {
        separador10.setName("separador10");
        separador10.setLeft(0);
        separador10.setTop(0);
        separador10.setWidth(10);
        separador10.setHeight(16);
        separador10.setAlign("alRight");
        separador10.setBorderStyle("stNone");
        separador10.setPaddingTop(0);
        separador10.setPaddingLeft(0);
        separador10.setPaddingRight(0);
        separador10.setPaddingBottom(0);
        separador10.setMarginTop(0);
        separador10.setMarginLeft(0);
        separador10.setMarginRight(0);
        separador10.setMarginBottom(0);
        separador10.setSpacing(1);
        separador10.setFlexVflex("ftFalse");
        separador10.setFlexHflex("ftTrue");
        separador10.setScrollable(false);
        separador10.setBoxShadowConfigHorizontalLength(10);
        separador10.setBoxShadowConfigVerticalLength(10);
        separador10.setBoxShadowConfigBlurRadius(5);
        separador10.setBoxShadowConfigSpreadRadius(0);
        separador10.setBoxShadowConfigShadowColor("clBlack");
        separador10.setBoxShadowConfigOpacity(75);
        separador10.setVAlign("tvTop");
        hBoxMaisFiltros.addChildren(separador10);
        separador10.applyProperties();
    }

    public TFLabel lblMaisFiltros = new TFLabel();

    private void init_lblMaisFiltros() {
        lblMaisFiltros.setName("lblMaisFiltros");
        lblMaisFiltros.setLeft(10);
        lblMaisFiltros.setTop(0);
        lblMaisFiltros.setWidth(60);
        lblMaisFiltros.setHeight(13);
        lblMaisFiltros.setCaption("Mais Filtros: ");
        lblMaisFiltros.setFontColor("clWindowText");
        lblMaisFiltros.setFontSize(-11);
        lblMaisFiltros.setFontName("Tahoma");
        lblMaisFiltros.setFontStyle("[]");
        lblMaisFiltros.setVerticalAlignment("taVerticalCenter");
        lblMaisFiltros.setWordBreak(false);
        hBoxMaisFiltros.addChildren(lblMaisFiltros);
        lblMaisFiltros.applyProperties();
    }

    public TFButton btnMaisFiltros = new TFButton();

    private void init_btnMaisFiltros() {
        btnMaisFiltros.setName("btnMaisFiltros");
        btnMaisFiltros.setLeft(70);
        btnMaisFiltros.setTop(0);
        btnMaisFiltros.setWidth(20);
        btnMaisFiltros.setHeight(20);
        btnMaisFiltros.setHint("Mais Filtros");
        btnMaisFiltros.setFontColor("clWindowText");
        btnMaisFiltros.setFontSize(-11);
        btnMaisFiltros.setFontName("Tahoma");
        btnMaisFiltros.setFontStyle("[]");
        btnMaisFiltros.setImageId(5300469);
        btnMaisFiltros.setColor("clHighlightText");
        btnMaisFiltros.setAccess(false);
        btnMaisFiltros.setIconReverseDirection(false);
        hBoxMaisFiltros.addChildren(btnMaisFiltros);
        btnMaisFiltros.applyProperties();
    }

    public TFHBox separador11 = new TFHBox();

    private void init_separador11() {
        separador11.setName("separador11");
        separador11.setLeft(90);
        separador11.setTop(0);
        separador11.setWidth(10);
        separador11.setHeight(16);
        separador11.setAlign("alRight");
        separador11.setBorderStyle("stNone");
        separador11.setPaddingTop(0);
        separador11.setPaddingLeft(0);
        separador11.setPaddingRight(0);
        separador11.setPaddingBottom(0);
        separador11.setMarginTop(0);
        separador11.setMarginLeft(0);
        separador11.setMarginRight(0);
        separador11.setMarginBottom(0);
        separador11.setSpacing(1);
        separador11.setFlexVflex("ftFalse");
        separador11.setFlexHflex("ftFalse");
        separador11.setScrollable(false);
        separador11.setBoxShadowConfigHorizontalLength(10);
        separador11.setBoxShadowConfigVerticalLength(10);
        separador11.setBoxShadowConfigBlurRadius(5);
        separador11.setBoxShadowConfigSpreadRadius(0);
        separador11.setBoxShadowConfigShadowColor("clBlack");
        separador11.setBoxShadowConfigOpacity(75);
        separador11.setVAlign("tvTop");
        hBoxMaisFiltros.addChildren(separador11);
        separador11.applyProperties();
    }

    public TFHBox separador12 = new TFHBox();

    private void init_separador12() {
        separador12.setName("separador12");
        separador12.setLeft(0);
        separador12.setTop(26);
        separador12.setWidth(983);
        separador12.setHeight(5);
        separador12.setAlign("alTop");
        separador12.setBorderStyle("stNone");
        separador12.setPaddingTop(0);
        separador12.setPaddingLeft(0);
        separador12.setPaddingRight(0);
        separador12.setPaddingBottom(0);
        separador12.setMarginTop(0);
        separador12.setMarginLeft(0);
        separador12.setMarginRight(0);
        separador12.setMarginBottom(0);
        separador12.setSpacing(1);
        separador12.setFlexVflex("ftFalse");
        separador12.setFlexHflex("ftFalse");
        separador12.setScrollable(false);
        separador12.setBoxShadowConfigHorizontalLength(10);
        separador12.setBoxShadowConfigVerticalLength(10);
        separador12.setBoxShadowConfigBlurRadius(5);
        separador12.setBoxShadowConfigSpreadRadius(0);
        separador12.setBoxShadowConfigShadowColor("clBlack");
        separador12.setBoxShadowConfigOpacity(75);
        separador12.setVAlign("tvTop");
        vBoxOrcPrenotaAgrupadas.addChildren(separador12);
        separador12.applyProperties();
    }

    public TFHBox hBoxOrcPrenotaAgrupadas = new TFHBox();

    private void init_hBoxOrcPrenotaAgrupadas() {
        hBoxOrcPrenotaAgrupadas.setName("hBoxOrcPrenotaAgrupadas");
        hBoxOrcPrenotaAgrupadas.setLeft(0);
        hBoxOrcPrenotaAgrupadas.setTop(32);
        hBoxOrcPrenotaAgrupadas.setWidth(968);
        hBoxOrcPrenotaAgrupadas.setHeight(135);
        hBoxOrcPrenotaAgrupadas.setAlign("alTop");
        hBoxOrcPrenotaAgrupadas.setBorderStyle("stNone");
        hBoxOrcPrenotaAgrupadas.setPaddingTop(0);
        hBoxOrcPrenotaAgrupadas.setPaddingLeft(0);
        hBoxOrcPrenotaAgrupadas.setPaddingRight(0);
        hBoxOrcPrenotaAgrupadas.setPaddingBottom(0);
        hBoxOrcPrenotaAgrupadas.setMarginTop(0);
        hBoxOrcPrenotaAgrupadas.setMarginLeft(0);
        hBoxOrcPrenotaAgrupadas.setMarginRight(0);
        hBoxOrcPrenotaAgrupadas.setMarginBottom(0);
        hBoxOrcPrenotaAgrupadas.setSpacing(1);
        hBoxOrcPrenotaAgrupadas.setFlexVflex("ftFalse");
        hBoxOrcPrenotaAgrupadas.setFlexHflex("ftTrue");
        hBoxOrcPrenotaAgrupadas.setScrollable(false);
        hBoxOrcPrenotaAgrupadas.setBoxShadowConfigHorizontalLength(10);
        hBoxOrcPrenotaAgrupadas.setBoxShadowConfigVerticalLength(10);
        hBoxOrcPrenotaAgrupadas.setBoxShadowConfigBlurRadius(5);
        hBoxOrcPrenotaAgrupadas.setBoxShadowConfigSpreadRadius(0);
        hBoxOrcPrenotaAgrupadas.setBoxShadowConfigShadowColor("clBlack");
        hBoxOrcPrenotaAgrupadas.setBoxShadowConfigOpacity(75);
        hBoxOrcPrenotaAgrupadas.setVAlign("tvTop");
        vBoxOrcPrenotaAgrupadas.addChildren(hBoxOrcPrenotaAgrupadas);
        hBoxOrcPrenotaAgrupadas.applyProperties();
    }

    public TFVBox separador13 = new TFVBox();

    private void init_separador13() {
        separador13.setName("separador13");
        separador13.setLeft(0);
        separador13.setTop(0);
        separador13.setWidth(10);
        separador13.setHeight(130);
        separador13.setBorderStyle("stNone");
        separador13.setPaddingTop(0);
        separador13.setPaddingLeft(0);
        separador13.setPaddingRight(0);
        separador13.setPaddingBottom(0);
        separador13.setMarginTop(0);
        separador13.setMarginLeft(0);
        separador13.setMarginRight(0);
        separador13.setMarginBottom(0);
        separador13.setSpacing(1);
        separador13.setFlexVflex("ftFalse");
        separador13.setFlexHflex("ftFalse");
        separador13.setScrollable(false);
        separador13.setBoxShadowConfigHorizontalLength(10);
        separador13.setBoxShadowConfigVerticalLength(10);
        separador13.setBoxShadowConfigBlurRadius(5);
        separador13.setBoxShadowConfigSpreadRadius(0);
        separador13.setBoxShadowConfigShadowColor("clBlack");
        separador13.setBoxShadowConfigOpacity(75);
        hBoxOrcPrenotaAgrupadas.addChildren(separador13);
        separador13.applyProperties();
    }

    public TFGrid gridOrcPrenotaAgrupadas = new TFGrid();

    private void init_gridOrcPrenotaAgrupadas() {
        gridOrcPrenotaAgrupadas.setName("gridOrcPrenotaAgrupadas");
        gridOrcPrenotaAgrupadas.setLeft(10);
        gridOrcPrenotaAgrupadas.setTop(0);
        gridOrcPrenotaAgrupadas.setWidth(912);
        gridOrcPrenotaAgrupadas.setHeight(130);
        gridOrcPrenotaAgrupadas.setTable(tbLeadsOrcNotasPgtoAgrupados);
        gridOrcPrenotaAgrupadas.setFlexVflex("ftTrue");
        gridOrcPrenotaAgrupadas.setFlexHflex("ftTrue");
        gridOrcPrenotaAgrupadas.setPagingEnabled(false);
        gridOrcPrenotaAgrupadas.setFrozenColumns(0);
        gridOrcPrenotaAgrupadas.setShowFooter(false);
        gridOrcPrenotaAgrupadas.setShowHeader(true);
        gridOrcPrenotaAgrupadas.setMultiSelection(false);
        gridOrcPrenotaAgrupadas.setGroupingEnabled(false);
        gridOrcPrenotaAgrupadas.setGroupingExpanded(false);
        gridOrcPrenotaAgrupadas.setGroupingShowFooter(false);
        gridOrcPrenotaAgrupadas.setCrosstabEnabled(false);
        gridOrcPrenotaAgrupadas.setCrosstabGroupType("cgtConcat");
        gridOrcPrenotaAgrupadas.setEditionEnabled(false);
        gridOrcPrenotaAgrupadas.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("ID_PAGAMENTO");
        item0.setTitleCaption("Id. Pagamento");
        item0.setWidth(66);
        item0.setVisible(false);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TIPO");
        item1.setTitleCaption("Tipo");
        item1.setWidth(48);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("COD_EMPRESA");
        item2.setTitleCaption("Emp.");
        item2.setWidth(40);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("COD_CLIENTE");
        item3.setTitleCaption("C\u00F3d. Cliente");
        item3.setWidth(99);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("DATA");
        item4.setTitleCaption("Data");
        item4.setWidth(88);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taLeft");
        item4.setFieldType("ftDate");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("USUARIO");
        item5.setTitleCaption("Usu\u00E1rio");
        item5.setWidth(63);
        item5.setVisible(true);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("VALOR");
        item6.setTitleCaption("Valor");
        item6.setWidth(63);
        item6.setVisible(true);
        item6.setPrecision(0);
        item6.setTextAlign("taRight");
        item6.setFieldType("ftDecimal");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("DESCR");
        item7.setTitleCaption("Descri\u00E7\u00E3o");
        item7.setWidth(276);
        item7.setVisible(true);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("TIPO_ADIANTAMENTO");
        item8.setTitleCaption("Tipo Adiantamento");
        item8.setWidth(125);
        item8.setVisible(true);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("NAT_REC_DESP_ADT");
        item9.setTitleCaption("Nat. Rec. Desp. Adt");
        item9.setWidth(125);
        item9.setVisible(true);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setHint("Natureza Rec Despesa Adiantamento");
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item9);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("NAT_REC_DESP_CART");
        item10.setTitleCaption("Nat. Rec. Desp. Cart");
        item10.setWidth(125);
        item10.setVisible(true);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setCheckedValue("S");
        item10.setUncheckedValue("N");
        item10.setHiperLink(false);
        item10.setHint("Natureza Rec Despesa Cart\u00E3o");
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        gridOrcPrenotaAgrupadas.getColumns().add(item10);
        hBoxOrcPrenotaAgrupadas.addChildren(gridOrcPrenotaAgrupadas);
        gridOrcPrenotaAgrupadas.applyProperties();
    }

    public TFVBox separador14 = new TFVBox();

    private void init_separador14() {
        separador14.setName("separador14");
        separador14.setLeft(922);
        separador14.setTop(0);
        separador14.setWidth(10);
        separador14.setHeight(130);
        separador14.setBorderStyle("stNone");
        separador14.setPaddingTop(0);
        separador14.setPaddingLeft(0);
        separador14.setPaddingRight(0);
        separador14.setPaddingBottom(0);
        separador14.setMarginTop(0);
        separador14.setMarginLeft(0);
        separador14.setMarginRight(0);
        separador14.setMarginBottom(0);
        separador14.setSpacing(1);
        separador14.setFlexVflex("ftFalse");
        separador14.setFlexHflex("ftFalse");
        separador14.setScrollable(false);
        separador14.setBoxShadowConfigHorizontalLength(10);
        separador14.setBoxShadowConfigVerticalLength(10);
        separador14.setBoxShadowConfigBlurRadius(5);
        separador14.setBoxShadowConfigSpreadRadius(0);
        separador14.setBoxShadowConfigShadowColor("clBlack");
        separador14.setBoxShadowConfigOpacity(75);
        hBoxOrcPrenotaAgrupadas.addChildren(separador14);
        separador14.applyProperties();
    }

    public TFVBox vBoxFormasPagamentos = new TFVBox();

    private void init_vBoxFormasPagamentos() {
        vBoxFormasPagamentos.setName("vBoxFormasPagamentos");
        vBoxFormasPagamentos.setLeft(0);
        vBoxFormasPagamentos.setTop(219);
        vBoxFormasPagamentos.setWidth(988);
        vBoxFormasPagamentos.setHeight(180);
        vBoxFormasPagamentos.setAlign("alClient");
        vBoxFormasPagamentos.setBorderStyle("stNone");
        vBoxFormasPagamentos.setPaddingTop(0);
        vBoxFormasPagamentos.setPaddingLeft(0);
        vBoxFormasPagamentos.setPaddingRight(0);
        vBoxFormasPagamentos.setPaddingBottom(0);
        vBoxFormasPagamentos.setMarginTop(0);
        vBoxFormasPagamentos.setMarginLeft(0);
        vBoxFormasPagamentos.setMarginRight(0);
        vBoxFormasPagamentos.setMarginBottom(0);
        vBoxFormasPagamentos.setSpacing(1);
        vBoxFormasPagamentos.setFlexVflex("ftFalse");
        vBoxFormasPagamentos.setFlexHflex("ftTrue");
        vBoxFormasPagamentos.setScrollable(false);
        vBoxFormasPagamentos.setBoxShadowConfigHorizontalLength(10);
        vBoxFormasPagamentos.setBoxShadowConfigVerticalLength(10);
        vBoxFormasPagamentos.setBoxShadowConfigBlurRadius(5);
        vBoxFormasPagamentos.setBoxShadowConfigSpreadRadius(0);
        vBoxFormasPagamentos.setBoxShadowConfigShadowColor("clBlack");
        vBoxFormasPagamentos.setBoxShadowConfigOpacity(75);
        vBoxPagamentosAgrupados.addChildren(vBoxFormasPagamentos);
        vBoxFormasPagamentos.applyProperties();
    }

    public TFHBox separador17 = new TFHBox();

    private void init_separador17() {
        separador17.setName("separador17");
        separador17.setLeft(0);
        separador17.setTop(0);
        separador17.setWidth(983);
        separador17.setHeight(6);
        separador17.setAlign("alTop");
        separador17.setBorderStyle("stNone");
        separador17.setPaddingTop(0);
        separador17.setPaddingLeft(0);
        separador17.setPaddingRight(0);
        separador17.setPaddingBottom(0);
        separador17.setMarginTop(0);
        separador17.setMarginLeft(0);
        separador17.setMarginRight(0);
        separador17.setMarginBottom(0);
        separador17.setSpacing(1);
        separador17.setFlexVflex("ftFalse");
        separador17.setFlexHflex("ftFalse");
        separador17.setScrollable(false);
        separador17.setBoxShadowConfigHorizontalLength(10);
        separador17.setBoxShadowConfigVerticalLength(10);
        separador17.setBoxShadowConfigBlurRadius(5);
        separador17.setBoxShadowConfigSpreadRadius(0);
        separador17.setBoxShadowConfigShadowColor("clBlack");
        separador17.setBoxShadowConfigOpacity(75);
        separador17.setVAlign("tvTop");
        vBoxFormasPagamentos.addChildren(separador17);
        separador17.applyProperties();
    }

    public TFHBox hBoxTituloFormasPgto = new TFHBox();

    private void init_hBoxTituloFormasPgto() {
        hBoxTituloFormasPgto.setName("hBoxTituloFormasPgto");
        hBoxTituloFormasPgto.setLeft(0);
        hBoxTituloFormasPgto.setTop(7);
        hBoxTituloFormasPgto.setWidth(415);
        hBoxTituloFormasPgto.setHeight(20);
        hBoxTituloFormasPgto.setBorderStyle("stNone");
        hBoxTituloFormasPgto.setPaddingTop(0);
        hBoxTituloFormasPgto.setPaddingLeft(10);
        hBoxTituloFormasPgto.setPaddingRight(0);
        hBoxTituloFormasPgto.setPaddingBottom(0);
        hBoxTituloFormasPgto.setMarginTop(0);
        hBoxTituloFormasPgto.setMarginLeft(0);
        hBoxTituloFormasPgto.setMarginRight(0);
        hBoxTituloFormasPgto.setMarginBottom(0);
        hBoxTituloFormasPgto.setSpacing(1);
        hBoxTituloFormasPgto.setFlexVflex("ftFalse");
        hBoxTituloFormasPgto.setFlexHflex("ftFalse");
        hBoxTituloFormasPgto.setScrollable(false);
        hBoxTituloFormasPgto.setBoxShadowConfigHorizontalLength(10);
        hBoxTituloFormasPgto.setBoxShadowConfigVerticalLength(10);
        hBoxTituloFormasPgto.setBoxShadowConfigBlurRadius(5);
        hBoxTituloFormasPgto.setBoxShadowConfigSpreadRadius(0);
        hBoxTituloFormasPgto.setBoxShadowConfigShadowColor("clBlack");
        hBoxTituloFormasPgto.setBoxShadowConfigOpacity(75);
        hBoxTituloFormasPgto.setVAlign("tvTop");
        vBoxFormasPagamentos.addChildren(hBoxTituloFormasPgto);
        hBoxTituloFormasPgto.applyProperties();
    }

    public TFLabel lblTituloFormasPgto = new TFLabel();

    private void init_lblTituloFormasPgto() {
        lblTituloFormasPgto.setName("lblTituloFormasPgto");
        lblTituloFormasPgto.setLeft(0);
        lblTituloFormasPgto.setTop(0);
        lblTituloFormasPgto.setWidth(264);
        lblTituloFormasPgto.setHeight(13);
        lblTituloFormasPgto.setCaption("Formas de Pagamento c /Cart\u00E3o [D\u00E9bito/Cr\u00E9dito] e PIX");
        lblTituloFormasPgto.setFontColor("clWindowText");
        lblTituloFormasPgto.setFontSize(-11);
        lblTituloFormasPgto.setFontName("Tahoma");
        lblTituloFormasPgto.setFontStyle("[]");
        lblTituloFormasPgto.setVerticalAlignment("taVerticalCenter");
        lblTituloFormasPgto.setWordBreak(false);
        hBoxTituloFormasPgto.addChildren(lblTituloFormasPgto);
        lblTituloFormasPgto.applyProperties();
    }

    public TFHBox hBoxFormasPagamentos = new TFHBox();

    private void init_hBoxFormasPagamentos() {
        hBoxFormasPagamentos.setName("hBoxFormasPagamentos");
        hBoxFormasPagamentos.setLeft(0);
        hBoxFormasPagamentos.setTop(28);
        hBoxFormasPagamentos.setWidth(968);
        hBoxFormasPagamentos.setHeight(145);
        hBoxFormasPagamentos.setAlign("alTop");
        hBoxFormasPagamentos.setBorderStyle("stNone");
        hBoxFormasPagamentos.setPaddingTop(0);
        hBoxFormasPagamentos.setPaddingLeft(0);
        hBoxFormasPagamentos.setPaddingRight(0);
        hBoxFormasPagamentos.setPaddingBottom(0);
        hBoxFormasPagamentos.setMarginTop(0);
        hBoxFormasPagamentos.setMarginLeft(0);
        hBoxFormasPagamentos.setMarginRight(0);
        hBoxFormasPagamentos.setMarginBottom(0);
        hBoxFormasPagamentos.setSpacing(1);
        hBoxFormasPagamentos.setFlexVflex("ftTrue");
        hBoxFormasPagamentos.setFlexHflex("ftTrue");
        hBoxFormasPagamentos.setScrollable(false);
        hBoxFormasPagamentos.setBoxShadowConfigHorizontalLength(10);
        hBoxFormasPagamentos.setBoxShadowConfigVerticalLength(10);
        hBoxFormasPagamentos.setBoxShadowConfigBlurRadius(5);
        hBoxFormasPagamentos.setBoxShadowConfigSpreadRadius(0);
        hBoxFormasPagamentos.setBoxShadowConfigShadowColor("clBlack");
        hBoxFormasPagamentos.setBoxShadowConfigOpacity(75);
        hBoxFormasPagamentos.setVAlign("tvTop");
        vBoxFormasPagamentos.addChildren(hBoxFormasPagamentos);
        hBoxFormasPagamentos.applyProperties();
    }

    public TFVBox separador15 = new TFVBox();

    private void init_separador15() {
        separador15.setName("separador15");
        separador15.setLeft(0);
        separador15.setTop(0);
        separador15.setWidth(10);
        separador15.setHeight(134);
        separador15.setBorderStyle("stNone");
        separador15.setPaddingTop(0);
        separador15.setPaddingLeft(0);
        separador15.setPaddingRight(0);
        separador15.setPaddingBottom(0);
        separador15.setMarginTop(0);
        separador15.setMarginLeft(0);
        separador15.setMarginRight(0);
        separador15.setMarginBottom(0);
        separador15.setSpacing(1);
        separador15.setFlexVflex("ftTrue");
        separador15.setFlexHflex("ftFalse");
        separador15.setScrollable(false);
        separador15.setBoxShadowConfigHorizontalLength(10);
        separador15.setBoxShadowConfigVerticalLength(10);
        separador15.setBoxShadowConfigBlurRadius(5);
        separador15.setBoxShadowConfigSpreadRadius(0);
        separador15.setBoxShadowConfigShadowColor("clBlack");
        separador15.setBoxShadowConfigOpacity(75);
        hBoxFormasPagamentos.addChildren(separador15);
        separador15.applyProperties();
    }

    public TFGrid gridFormasPagamentos = new TFGrid();

    private void init_gridFormasPagamentos() {
        gridFormasPagamentos.setName("gridFormasPagamentos");
        gridFormasPagamentos.setLeft(10);
        gridFormasPagamentos.setTop(0);
        gridFormasPagamentos.setWidth(912);
        gridFormasPagamentos.setHeight(134);
        gridFormasPagamentos.setTable(tbLeadsFormaPgtoAgrupado);
        gridFormasPagamentos.setFlexVflex("ftTrue");
        gridFormasPagamentos.setFlexHflex("ftTrue");
        gridFormasPagamentos.setPagingEnabled(false);
        gridFormasPagamentos.setFrozenColumns(0);
        gridFormasPagamentos.setShowFooter(false);
        gridFormasPagamentos.setShowHeader(true);
        gridFormasPagamentos.setMultiSelection(false);
        gridFormasPagamentos.setGroupingEnabled(false);
        gridFormasPagamentos.setGroupingExpanded(false);
        gridFormasPagamentos.setGroupingShowFooter(false);
        gridFormasPagamentos.setCrosstabEnabled(false);
        gridFormasPagamentos.setCrosstabGroupType("cgtConcat");
        gridFormasPagamentos.setEditionEnabled(false);
        gridFormasPagamentos.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("SEQUENCIA_CARTAO");
        item0.setTitleCaption("Seq.");
        item0.setWidth(40);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setCheckedValue("S");
        item0.setUncheckedValue("N");
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TIPO_PARCELA_CARTAO");
        item1.setTitleCaption("Tipo Cart\u00E3o");
        item1.setWidth(17);
        item1.setVisible(false);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setCheckedValue("S");
        item1.setUncheckedValue("N");
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("DESC_TIPO_CARTAO");
        item2.setTitleCaption("Tipo Cart\u00E3o D/C/PIX");
        item2.setWidth(192);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(false);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setCheckedValue("S");
        item2.setUncheckedValue("N");
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("QTDE_PARCELA_CARTAO");
        item3.setTitleCaption("Qtde. Parcelas");
        item3.setWidth(97);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taRight");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(false);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setCheckedValue("S");
        item3.setUncheckedValue("N");
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("VALOR");
        item4.setTitleCaption("Valor");
        item4.setWidth(80);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taRight");
        item4.setFieldType("ftDecimal");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(false);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(true);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setCheckedValue("S");
        item4.setUncheckedValue("N");
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item4);
        TFGridColumn item5 = new TFGridColumn();
        item5.setFieldName("COD_EMPRESA");
        item5.setTitleCaption("C\u00F3d. Empresa");
        item5.setWidth(86);
        item5.setVisible(false);
        item5.setPrecision(0);
        item5.setTextAlign("taLeft");
        item5.setFieldType("ftString");
        item5.setFlexRatio(0);
        item5.setSort(false);
        item5.setImageHeader(0);
        item5.setWrap(false);
        item5.setFlex(false);
        item5.setCharCase("ccNormal");
        item5.setBlobConfigMimeType("bmtText");
        item5.setBlobConfigShowType("btImageViewer");
        item5.setShowLabel(true);
        item5.setEditorEditType("etTFString");
        item5.setEditorPrecision(0);
        item5.setEditorMaxLength(100);
        item5.setEditorLookupFilterKey(0);
        item5.setEditorLookupFilterDesc(0);
        item5.setEditorPopupHeight(400);
        item5.setEditorPopupWidth(400);
        item5.setEditorCharCase("ccNormal");
        item5.setEditorEnabled(false);
        item5.setEditorReadOnly(false);
        item5.setCheckedValue("S");
        item5.setUncheckedValue("N");
        item5.setHiperLink(false);
        item5.setEditorConstraintCheckWhen("cwImmediate");
        item5.setEditorConstraintCheckType("ctExpression");
        item5.setEditorConstraintFocusOnError(false);
        item5.setEditorConstraintEnableUI(true);
        item5.setEditorConstraintEnabled(false);
        item5.setEmpty(false);
        item5.setMobileOptsShowMobile(false);
        item5.setMobileOptsOrder(0);
        item5.setBoxSize(0);
        item5.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item5);
        TFGridColumn item6 = new TFGridColumn();
        item6.setFieldName("COD_EVENTO");
        item6.setTitleCaption("Id. Pagamento");
        item6.setWidth(65);
        item6.setVisible(false);
        item6.setPrecision(0);
        item6.setTextAlign("taLeft");
        item6.setFieldType("ftString");
        item6.setFlexRatio(0);
        item6.setSort(false);
        item6.setImageHeader(0);
        item6.setWrap(false);
        item6.setFlex(false);
        item6.setCharCase("ccNormal");
        item6.setBlobConfigMimeType("bmtText");
        item6.setBlobConfigShowType("btImageViewer");
        item6.setShowLabel(true);
        item6.setEditorEditType("etTFString");
        item6.setEditorPrecision(0);
        item6.setEditorMaxLength(100);
        item6.setEditorLookupFilterKey(0);
        item6.setEditorLookupFilterDesc(0);
        item6.setEditorPopupHeight(400);
        item6.setEditorPopupWidth(400);
        item6.setEditorCharCase("ccNormal");
        item6.setEditorEnabled(false);
        item6.setEditorReadOnly(false);
        item6.setCheckedValue("S");
        item6.setUncheckedValue("N");
        item6.setHiperLink(false);
        item6.setEditorConstraintCheckWhen("cwImmediate");
        item6.setEditorConstraintCheckType("ctExpression");
        item6.setEditorConstraintFocusOnError(false);
        item6.setEditorConstraintEnableUI(true);
        item6.setEditorConstraintEnabled(false);
        item6.setEmpty(false);
        item6.setMobileOptsShowMobile(false);
        item6.setMobileOptsOrder(0);
        item6.setBoxSize(0);
        item6.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item6);
        TFGridColumn item7 = new TFGridColumn();
        item7.setFieldName("ORIGEM_EVENTO");
        item7.setTitleCaption("Origem Evento");
        item7.setWidth(103);
        item7.setVisible(false);
        item7.setPrecision(0);
        item7.setTextAlign("taLeft");
        item7.setFieldType("ftString");
        item7.setFlexRatio(0);
        item7.setSort(false);
        item7.setImageHeader(0);
        item7.setWrap(false);
        item7.setFlex(false);
        item7.setCharCase("ccNormal");
        item7.setBlobConfigMimeType("bmtText");
        item7.setBlobConfigShowType("btImageViewer");
        item7.setShowLabel(true);
        item7.setEditorEditType("etTFString");
        item7.setEditorPrecision(0);
        item7.setEditorMaxLength(100);
        item7.setEditorLookupFilterKey(0);
        item7.setEditorLookupFilterDesc(0);
        item7.setEditorPopupHeight(400);
        item7.setEditorPopupWidth(400);
        item7.setEditorCharCase("ccNormal");
        item7.setEditorEnabled(false);
        item7.setEditorReadOnly(false);
        item7.setCheckedValue("S");
        item7.setUncheckedValue("N");
        item7.setHiperLink(false);
        item7.setEditorConstraintCheckWhen("cwImmediate");
        item7.setEditorConstraintCheckType("ctExpression");
        item7.setEditorConstraintFocusOnError(false);
        item7.setEditorConstraintEnableUI(true);
        item7.setEditorConstraintEnabled(false);
        item7.setEmpty(false);
        item7.setMobileOptsShowMobile(false);
        item7.setMobileOptsOrder(0);
        item7.setBoxSize(0);
        item7.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item7);
        TFGridColumn item8 = new TFGridColumn();
        item8.setFieldName("COD_FORMA_PGTO");
        item8.setTitleCaption("C\u00F3d. Forma Pagamento");
        item8.setWidth(147);
        item8.setVisible(false);
        item8.setPrecision(0);
        item8.setTextAlign("taLeft");
        item8.setFieldType("ftString");
        item8.setFlexRatio(0);
        item8.setSort(false);
        item8.setImageHeader(0);
        item8.setWrap(false);
        item8.setFlex(false);
        item8.setCharCase("ccNormal");
        item8.setBlobConfigMimeType("bmtText");
        item8.setBlobConfigShowType("btImageViewer");
        item8.setShowLabel(true);
        item8.setEditorEditType("etTFString");
        item8.setEditorPrecision(0);
        item8.setEditorMaxLength(100);
        item8.setEditorLookupFilterKey(0);
        item8.setEditorLookupFilterDesc(0);
        item8.setEditorPopupHeight(400);
        item8.setEditorPopupWidth(400);
        item8.setEditorCharCase("ccNormal");
        item8.setEditorEnabled(false);
        item8.setEditorReadOnly(false);
        item8.setCheckedValue("S");
        item8.setUncheckedValue("N");
        item8.setHiperLink(false);
        item8.setEditorConstraintCheckWhen("cwImmediate");
        item8.setEditorConstraintCheckType("ctExpression");
        item8.setEditorConstraintFocusOnError(false);
        item8.setEditorConstraintEnableUI(true);
        item8.setEditorConstraintEnabled(false);
        item8.setEmpty(false);
        item8.setMobileOptsShowMobile(false);
        item8.setMobileOptsOrder(0);
        item8.setBoxSize(0);
        item8.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item8);
        TFGridColumn item9 = new TFGridColumn();
        item9.setFieldName("OBSERVACAO");
        item9.setTitleCaption("Observa\u00E7\u00E3o");
        item9.setWidth(111);
        item9.setVisible(false);
        item9.setPrecision(0);
        item9.setTextAlign("taLeft");
        item9.setFieldType("ftString");
        item9.setFlexRatio(0);
        item9.setSort(false);
        item9.setImageHeader(0);
        item9.setWrap(false);
        item9.setFlex(false);
        item9.setCharCase("ccNormal");
        item9.setBlobConfigMimeType("bmtText");
        item9.setBlobConfigShowType("btImageViewer");
        item9.setShowLabel(true);
        item9.setEditorEditType("etTFString");
        item9.setEditorPrecision(0);
        item9.setEditorMaxLength(100);
        item9.setEditorLookupFilterKey(0);
        item9.setEditorLookupFilterDesc(0);
        item9.setEditorPopupHeight(400);
        item9.setEditorPopupWidth(400);
        item9.setEditorCharCase("ccNormal");
        item9.setEditorEnabled(false);
        item9.setEditorReadOnly(false);
        item9.setCheckedValue("S");
        item9.setUncheckedValue("N");
        item9.setHiperLink(false);
        item9.setEditorConstraintCheckWhen("cwImmediate");
        item9.setEditorConstraintCheckType("ctExpression");
        item9.setEditorConstraintFocusOnError(false);
        item9.setEditorConstraintEnableUI(true);
        item9.setEditorConstraintEnabled(false);
        item9.setEmpty(false);
        item9.setMobileOptsShowMobile(false);
        item9.setMobileOptsOrder(0);
        item9.setBoxSize(0);
        item9.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item9);
        TFGridColumn item10 = new TFGridColumn();
        item10.setFieldName("ID_PAGAMENTO");
        item10.setTitleCaption("Id. Pagamento");
        item10.setWidth(40);
        item10.setVisible(false);
        item10.setPrecision(0);
        item10.setTextAlign("taLeft");
        item10.setFieldType("ftString");
        item10.setFlexRatio(0);
        item10.setSort(false);
        item10.setImageHeader(0);
        item10.setWrap(false);
        item10.setFlex(false);
        item10.setCharCase("ccNormal");
        item10.setBlobConfigMimeType("bmtText");
        item10.setBlobConfigShowType("btImageViewer");
        item10.setShowLabel(true);
        item10.setEditorEditType("etTFString");
        item10.setEditorPrecision(0);
        item10.setEditorMaxLength(100);
        item10.setEditorLookupFilterKey(0);
        item10.setEditorLookupFilterDesc(0);
        item10.setEditorPopupHeight(400);
        item10.setEditorPopupWidth(400);
        item10.setEditorCharCase("ccNormal");
        item10.setEditorEnabled(false);
        item10.setEditorReadOnly(false);
        item10.setCheckedValue("S");
        item10.setUncheckedValue("N");
        item10.setHiperLink(false);
        item10.setEditorConstraintCheckWhen("cwImmediate");
        item10.setEditorConstraintCheckType("ctExpression");
        item10.setEditorConstraintFocusOnError(false);
        item10.setEditorConstraintEnableUI(true);
        item10.setEditorConstraintEnabled(false);
        item10.setEmpty(false);
        item10.setMobileOptsShowMobile(false);
        item10.setMobileOptsOrder(0);
        item10.setBoxSize(0);
        item10.setImageSrcType("istSource");
        gridFormasPagamentos.getColumns().add(item10);
        hBoxFormasPagamentos.addChildren(gridFormasPagamentos);
        gridFormasPagamentos.applyProperties();
    }

    public TFVBox separador16 = new TFVBox();

    private void init_separador16() {
        separador16.setName("separador16");
        separador16.setLeft(922);
        separador16.setTop(0);
        separador16.setWidth(10);
        separador16.setHeight(134);
        separador16.setBorderStyle("stNone");
        separador16.setPaddingTop(0);
        separador16.setPaddingLeft(0);
        separador16.setPaddingRight(0);
        separador16.setPaddingBottom(0);
        separador16.setMarginTop(0);
        separador16.setMarginLeft(0);
        separador16.setMarginRight(0);
        separador16.setMarginBottom(0);
        separador16.setSpacing(1);
        separador16.setFlexVflex("ftTrue");
        separador16.setFlexHflex("ftFalse");
        separador16.setScrollable(false);
        separador16.setBoxShadowConfigHorizontalLength(10);
        separador16.setBoxShadowConfigVerticalLength(10);
        separador16.setBoxShadowConfigBlurRadius(5);
        separador16.setBoxShadowConfigSpreadRadius(0);
        separador16.setBoxShadowConfigShadowColor("clBlack");
        separador16.setBoxShadowConfigOpacity(75);
        hBoxFormasPagamentos.addChildren(separador16);
        separador16.applyProperties();
    }

    public TFHBox separador18 = new TFHBox();

    private void init_separador18() {
        separador18.setName("separador18");
        separador18.setLeft(0);
        separador18.setTop(400);
        separador18.setWidth(343);
        separador18.setHeight(6);
        separador18.setAlign("alBottom");
        separador18.setBorderStyle("stNone");
        separador18.setPaddingTop(0);
        separador18.setPaddingLeft(0);
        separador18.setPaddingRight(0);
        separador18.setPaddingBottom(0);
        separador18.setMarginTop(0);
        separador18.setMarginLeft(0);
        separador18.setMarginRight(0);
        separador18.setMarginBottom(0);
        separador18.setSpacing(1);
        separador18.setFlexVflex("ftFalse");
        separador18.setFlexHflex("ftFalse");
        separador18.setScrollable(false);
        separador18.setBoxShadowConfigHorizontalLength(10);
        separador18.setBoxShadowConfigVerticalLength(10);
        separador18.setBoxShadowConfigBlurRadius(5);
        separador18.setBoxShadowConfigSpreadRadius(0);
        separador18.setBoxShadowConfigShadowColor("clBlack");
        separador18.setBoxShadowConfigOpacity(75);
        separador18.setVAlign("tvTop");
        vBoxPagamentosAgrupados.addChildren(separador18);
        separador18.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtCpfCnpjEnter(final Event<Object> event);

    public abstract void edtCpfCnpjExit(final Event<Object> event);

    public void btnPesquisarClienteClick(final Event<Object> event) {
        if (btnPesquisarCliente.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisarCliente");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void hBoxSelOrcPreNotasClick(final Event<Object> event);

    public abstract void hBoxOrcPreNotasAgrupadasClick(final Event<Object> event);

    public abstract void hBoxFormasPagamentoClick(final Event<Object> event);

    public abstract void btnSelecaoClick(final Event<Object> event);

    public void btnAddOrcNotasClick(final Event<Object> event) {
        if (btnAddOrcNotas.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAddOrcNotas");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void selecionaOrcPreNotasParaAgrupar(final Event<Object> event);

    public void btnRemoverTodosItensClick(final Event<Object> event) {
        if (btnRemoverTodosItens.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnRemoverTodosItens");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnDefinirFormasPagamentoClick(final Event<Object> event) {
        if (btnDefinirFormasPagamento.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnDefinirFormasPagamento");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridOrcPreNotasSelecionadasremoveItemAgrupadoGrid(final Event<Object> event);

    public abstract void cbbFormasPgtoChange(final Event<Object> event);

    public abstract void edtValorEnter(final Event<Object> event);

    public abstract void btnAplicarFormasPagamentosClick(final Event<Object> event);

    public abstract void btnRemoverFormasPagamentosClick(final Event<Object> event);

    public void btnConfirmarFormasPgtoClick(final Event<Object> event) {
        if (btnConfirmarFormasPgto.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnConfirmarFormasPgto");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void gridFormasPgtoOrcNotasbtnRemoverFormasPagamentosClick(final Event<Object> event);

    public void btnExcluirPgtoClick(final Event<Object> event) {
        if (btnExcluirPgto.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluirPgto");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnFechamentoParcialClick(final Event<Object> event) {
        if (btnFechamentoParcial.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnFechamentoParcial");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void grpBoxOrcPrenotaAgrupadasClose(final Event<Object> event);

    public abstract void mnSelecionarTodosClick(final Event<Object> event);

    public abstract void mnSelecionarNenhumClick(final Event<Object> event);

    public abstract void tbLeadsOrcNotasPgtoAgrupadosAfterScroll(final Event<Object> event);

}