package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmFichaItemEliminarReserva extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.FichaItemEliminarReservaRNA rn = null;

    public FrmFichaItemEliminarReserva() {
        try {
            rn = (freedom.bytecode.rn.FichaItemEliminarReservaRNA) getRN(freedom.bytecode.rn.wizard.FichaItemEliminarReservaRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FRadiogroupParcial();
        init_FHBox1();
        init_btnExcluir();
        init_FHBox2();
        init_FVBox2();
        init_FLabel1();
        init_edtControleReserva();
        init_FVBox4();
        init_FLabel2();
        init_edtCodigoItem();
        init_FVBox5();
        init_lblQtdeReservada();
        init_edtQtdeReservada();
        init_FVBox6();
        init_lblQtdeASerLiberada();
        init_edtQtdeEliminarReserva();
        init_FVBox3();
        init_FVBox1();
        init_rdButtonEliminarTotal();
        init_rdButtonEliminarParcial();
        init_FVBox7();
        init_chkVendaPendente();
        init_FVBox8();
        init_lblObservacao();
        init_edtObservacao();
        init_FrmFichaItemEliminarReserva();
    }

    public TFRadioGroup FRadiogroupParcial = new TFRadioGroup();

    private void init_FRadiogroupParcial() {
        FRadiogroupParcial.setName("FRadiogroupParcial");
        FrmFichaItemEliminarReserva.addChildren(FRadiogroupParcial);
        FRadiogroupParcial.applyProperties();
    }

    protected TFForm FrmFichaItemEliminarReserva = this;
    private void init_FrmFichaItemEliminarReserva() {
        FrmFichaItemEliminarReserva.setName("FrmFichaItemEliminarReserva");
        FrmFichaItemEliminarReserva.setCaption("Eliminando Reserva");
        FrmFichaItemEliminarReserva.setClientHeight(334);
        FrmFichaItemEliminarReserva.setClientWidth(615);
        FrmFichaItemEliminarReserva.setColor("clBtnFace");
        FrmFichaItemEliminarReserva.addEventListener("onCreate", (EventListener<Event<Object>>)(Event<Object> event) -> {
            FFormCreate(event);
            processarFlow("FrmFichaItemEliminarReserva", "FrmFichaItemEliminarReserva", "OnCreate");
        });
        FrmFichaItemEliminarReserva.setWOrigem("EhMain");
        FrmFichaItemEliminarReserva.setWKey("5300420");
        FrmFichaItemEliminarReserva.setSpacing(0);
        FrmFichaItemEliminarReserva.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(0);
        FHBox1.setWidth(615);
        FHBox1.setHeight(61);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(3);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(2);
        FHBox1.setMarginLeft(3);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftFalse");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FrmFichaItemEliminarReserva.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFButton btnExcluir = new TFButton();

    private void init_btnExcluir() {
        btnExcluir.setName("btnExcluir");
        btnExcluir.setLeft(0);
        btnExcluir.setTop(0);
        btnExcluir.setWidth(65);
        btnExcluir.setHeight(53);
        btnExcluir.setHint("Confirmar a elimina\u00E7\u00E3o da  reserva.");
        btnExcluir.setCaption("Confirmar");
        btnExcluir.setFontColor("clWindowText");
        btnExcluir.setFontSize(-11);
        btnExcluir.setFontName("Tahoma");
        btnExcluir.setFontStyle("[]");
        btnExcluir.setLayout("blGlyphTop");
        btnExcluir.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnExcluirClick(event);
            processarFlow("FrmFichaItemEliminarReserva", "btnExcluir", "OnClick");
        });
        btnExcluir.setImageId(0);
        btnExcluir.setColor("clBtnFace");
        btnExcluir.setAccess(false);
        btnExcluir.setIconClass("trash");
        btnExcluir.setIconReverseDirection(false);
        FHBox1.addChildren(btnExcluir);
        btnExcluir.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(60);
        FHBox2.setWidth(601);
        FHBox2.setHeight(59);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(5);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FrmFichaItemEliminarReserva.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(118);
        FVBox2.setHeight(54);
        FVBox2.setBorderStyle("stSingleLine");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(5);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(40);
        FLabel1.setHeight(13);
        FLabel1.setHint("Quantidade a ser liberada");
        FLabel1.setCaption("Reserva");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox2.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFInteger edtControleReserva = new TFInteger();

    private void init_edtControleReserva() {
        edtControleReserva.setName("edtControleReserva");
        edtControleReserva.setLeft(0);
        edtControleReserva.setTop(14);
        edtControleReserva.setWidth(98);
        edtControleReserva.setHeight(24);
        edtControleReserva.setHint("Nr. reserva");
        edtControleReserva.setFlex(false);
        edtControleReserva.setRequired(false);
        edtControleReserva.setConstraintCheckWhen("cwImmediate");
        edtControleReserva.setConstraintCheckType("ctExpression");
        edtControleReserva.setConstraintFocusOnError(false);
        edtControleReserva.setConstraintEnableUI(true);
        edtControleReserva.setConstraintEnabled(false);
        edtControleReserva.setConstraintFormCheck(true);
        edtControleReserva.setMaxlength(0);
        edtControleReserva.setEnabled(false);
        edtControleReserva.setFontColor("clWindowText");
        edtControleReserva.setFontSize(-13);
        edtControleReserva.setFontName("Tahoma");
        edtControleReserva.setFontStyle("[]");
        edtControleReserva.setAlignment("taRightJustify");
        FVBox2.addChildren(edtControleReserva);
        edtControleReserva.applyProperties();
        addValidatable(edtControleReserva);
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(118);
        FVBox4.setTop(0);
        FVBox4.setWidth(220);
        FVBox4.setHeight(54);
        FVBox4.setBorderStyle("stSingleLine");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(5);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftFalse");
        FVBox4.setFlexHflex("ftFalse");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(0);
        FLabel2.setWidth(33);
        FLabel2.setHeight(13);
        FLabel2.setHint("Quantidade a ser liberada");
        FLabel2.setCaption("C\u00F3digo");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox4.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFString edtCodigoItem = new TFString();

    private void init_edtCodigoItem() {
        edtCodigoItem.setName("edtCodigoItem");
        edtCodigoItem.setLeft(0);
        edtCodigoItem.setTop(14);
        edtCodigoItem.setWidth(205);
        edtCodigoItem.setHeight(24);
        edtCodigoItem.setFlex(false);
        edtCodigoItem.setRequired(false);
        edtCodigoItem.setPrompt("CodigoItem");
        edtCodigoItem.setConstraintCheckWhen("cwImmediate");
        edtCodigoItem.setConstraintCheckType("ctExpression");
        edtCodigoItem.setConstraintFocusOnError(false);
        edtCodigoItem.setConstraintEnableUI(true);
        edtCodigoItem.setConstraintEnabled(false);
        edtCodigoItem.setConstraintFormCheck(true);
        edtCodigoItem.setCharCase("ccNormal");
        edtCodigoItem.setPwd(false);
        edtCodigoItem.setMaxlength(0);
        edtCodigoItem.setEnabled(false);
        edtCodigoItem.setFontColor("clWindowText");
        edtCodigoItem.setFontSize(-13);
        edtCodigoItem.setFontName("Tahoma");
        edtCodigoItem.setFontStyle("[]");
        edtCodigoItem.setSaveLiteralCharacter(false);
        edtCodigoItem.applyProperties();
        FVBox4.addChildren(edtCodigoItem);
        addValidatable(edtCodigoItem);
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(338);
        FVBox5.setTop(0);
        FVBox5.setWidth(120);
        FVBox5.setHeight(54);
        FVBox5.setBorderStyle("stSingleLine");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(5);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftFalse");
        FVBox5.setFlexHflex("ftFalse");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFLabel lblQtdeReservada = new TFLabel();

    private void init_lblQtdeReservada() {
        lblQtdeReservada.setName("lblQtdeReservada");
        lblQtdeReservada.setLeft(0);
        lblQtdeReservada.setTop(0);
        lblQtdeReservada.setWidth(79);
        lblQtdeReservada.setHeight(13);
        lblQtdeReservada.setHint("Quantidade reservada");
        lblQtdeReservada.setCaption("Qtde Reservada");
        lblQtdeReservada.setFontColor("clWindowText");
        lblQtdeReservada.setFontSize(-11);
        lblQtdeReservada.setFontName("Tahoma");
        lblQtdeReservada.setFontStyle("[]");
        lblQtdeReservada.setVerticalAlignment("taVerticalCenter");
        lblQtdeReservada.setWordBreak(false);
        FVBox5.addChildren(lblQtdeReservada);
        lblQtdeReservada.applyProperties();
    }

    public TFInteger edtQtdeReservada = new TFInteger();

    private void init_edtQtdeReservada() {
        edtQtdeReservada.setName("edtQtdeReservada");
        edtQtdeReservada.setLeft(0);
        edtQtdeReservada.setTop(14);
        edtQtdeReservada.setWidth(78);
        edtQtdeReservada.setHeight(24);
        edtQtdeReservada.setHint("Qtde a ser liberada");
        edtQtdeReservada.setFieldName("QTDE");
        edtQtdeReservada.setFlex(false);
        edtQtdeReservada.setRequired(false);
        edtQtdeReservada.setConstraintCheckWhen("cwImmediate");
        edtQtdeReservada.setConstraintCheckType("ctExpression");
        edtQtdeReservada.setConstraintFocusOnError(false);
        edtQtdeReservada.setConstraintEnableUI(true);
        edtQtdeReservada.setConstraintEnabled(false);
        edtQtdeReservada.setConstraintFormCheck(true);
        edtQtdeReservada.setMaxlength(0);
        edtQtdeReservada.setEnabled(false);
        edtQtdeReservada.setFontColor("clWindowText");
        edtQtdeReservada.setFontSize(-13);
        edtQtdeReservada.setFontName("Tahoma");
        edtQtdeReservada.setFontStyle("[]");
        edtQtdeReservada.setAlignment("taRightJustify");
        FVBox5.addChildren(edtQtdeReservada);
        edtQtdeReservada.applyProperties();
        addValidatable(edtQtdeReservada);
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(458);
        FVBox6.setTop(0);
        FVBox6.setWidth(120);
        FVBox6.setHeight(54);
        FVBox6.setBorderStyle("stSingleLine");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(5);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftFalse");
        FVBox6.setFlexHflex("ftFalse");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        FHBox2.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFLabel lblQtdeASerLiberada = new TFLabel();

    private void init_lblQtdeASerLiberada() {
        lblQtdeASerLiberada.setName("lblQtdeASerLiberada");
        lblQtdeASerLiberada.setLeft(0);
        lblQtdeASerLiberada.setTop(0);
        lblQtdeASerLiberada.setWidth(95);
        lblQtdeASerLiberada.setHeight(13);
        lblQtdeASerLiberada.setHint("Quantidade a ser liberada");
        lblQtdeASerLiberada.setCaption("Qtde a ser Liberada");
        lblQtdeASerLiberada.setFontColor("clWindowText");
        lblQtdeASerLiberada.setFontSize(-11);
        lblQtdeASerLiberada.setFontName("Tahoma");
        lblQtdeASerLiberada.setFontStyle("[]");
        lblQtdeASerLiberada.setVerticalAlignment("taVerticalCenter");
        lblQtdeASerLiberada.setWordBreak(false);
        FVBox6.addChildren(lblQtdeASerLiberada);
        lblQtdeASerLiberada.applyProperties();
    }

    public TFInteger edtQtdeEliminarReserva = new TFInteger();

    private void init_edtQtdeEliminarReserva() {
        edtQtdeEliminarReserva.setName("edtQtdeEliminarReserva");
        edtQtdeEliminarReserva.setLeft(0);
        edtQtdeEliminarReserva.setTop(14);
        edtQtdeEliminarReserva.setWidth(94);
        edtQtdeEliminarReserva.setHeight(24);
        edtQtdeEliminarReserva.setHint("Qtde a ser liberada");
        edtQtdeEliminarReserva.setFlex(false);
        edtQtdeEliminarReserva.setRequired(false);
        edtQtdeEliminarReserva.setConstraintCheckWhen("cwImmediate");
        edtQtdeEliminarReserva.setConstraintCheckType("ctExpression");
        edtQtdeEliminarReserva.setConstraintFocusOnError(false);
        edtQtdeEliminarReserva.setConstraintEnableUI(true);
        edtQtdeEliminarReserva.setConstraintEnabled(false);
        edtQtdeEliminarReserva.setConstraintFormCheck(true);
        edtQtdeEliminarReserva.setMaxlength(0);
        edtQtdeEliminarReserva.setEnabled(false);
        edtQtdeEliminarReserva.setFontColor("clWindowText");
        edtQtdeEliminarReserva.setFontSize(-13);
        edtQtdeEliminarReserva.setFontName("Tahoma");
        edtQtdeEliminarReserva.setFontStyle("[]");
        edtQtdeEliminarReserva.setAlignment("taRightJustify");
        FVBox6.addChildren(edtQtdeEliminarReserva);
        edtQtdeEliminarReserva.applyProperties();
        addValidatable(edtQtdeEliminarReserva);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(2);
        FVBox3.setTop(120);
        FVBox3.setWidth(581);
        FVBox3.setHeight(115);
        FVBox3.setBorderStyle("stSingleLine");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FrmFichaItemEliminarReserva.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(576);
        FVBox1.setHeight(64);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(10);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(0);
        FVBox1.setFlexVflex("ftFalse");
        FVBox1.setFlexHflex("ftFalse");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFRadioButton rdButtonEliminarTotal = new TFRadioButton();

    private void init_rdButtonEliminarTotal() {
        rdButtonEliminarTotal.setName("rdButtonEliminarTotal");
        rdButtonEliminarTotal.setLeft(0);
        rdButtonEliminarTotal.setTop(0);
        rdButtonEliminarTotal.setWidth(257);
        rdButtonEliminarTotal.setHeight(17);
        rdButtonEliminarTotal.setCaption("Eliminar a reserva por completo");
        rdButtonEliminarTotal.setChecked(true);
        rdButtonEliminarTotal.setFontColor("clWindowText");
        rdButtonEliminarTotal.setFontSize(-11);
        rdButtonEliminarTotal.setFontName("Tahoma");
        rdButtonEliminarTotal.setFontStyle("[]");
        rdButtonEliminarTotal.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            rdButtonEliminarTotalCheck(event);
            processarFlow("FrmFichaItemEliminarReserva", "rdButtonEliminarTotal", "OnCheck");
        });
        rdButtonEliminarTotal.setRadioGroup(FRadiogroupParcial);
        FVBox1.addChildren(rdButtonEliminarTotal);
        rdButtonEliminarTotal.applyProperties();
    }

    public TFRadioButton rdButtonEliminarParcial = new TFRadioButton();

    private void init_rdButtonEliminarParcial() {
        rdButtonEliminarParcial.setName("rdButtonEliminarParcial");
        rdButtonEliminarParcial.setLeft(0);
        rdButtonEliminarParcial.setTop(18);
        rdButtonEliminarParcial.setWidth(369);
        rdButtonEliminarParcial.setHeight(17);
        rdButtonEliminarParcial.setCaption("Eliminar parcialmente (Especificar a Quantidade a ser liberada)");
        rdButtonEliminarParcial.setEnabled(false);
        rdButtonEliminarParcial.setFontColor("clWindowText");
        rdButtonEliminarParcial.setFontSize(-11);
        rdButtonEliminarParcial.setFontName("Tahoma");
        rdButtonEliminarParcial.setFontStyle("[]");
        rdButtonEliminarParcial.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            rdButtonEliminarParcialCheck(event);
            processarFlow("FrmFichaItemEliminarReserva", "rdButtonEliminarParcial", "OnCheck");
        });
        rdButtonEliminarParcial.setRadioGroup(FRadiogroupParcial);
        FVBox1.addChildren(rdButtonEliminarParcial);
        rdButtonEliminarParcial.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(65);
        FVBox7.setWidth(576);
        FVBox7.setHeight(41);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(2);
        FVBox7.setPaddingLeft(5);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftFalse");
        FVBox7.setFlexHflex("ftFalse");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        FVBox3.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFCheckBox chkVendaPendente = new TFCheckBox();

    private void init_chkVendaPendente() {
        chkVendaPendente.setName("chkVendaPendente");
        chkVendaPendente.setLeft(0);
        chkVendaPendente.setTop(0);
        chkVendaPendente.setWidth(363);
        chkVendaPendente.setHeight(27);
        chkVendaPendente.setHint("Eliminar Venda Pendente, se houver");
        chkVendaPendente.setCaption("Eliminar Venda Pendente, se houver");
        chkVendaPendente.setFontColor("clWindowText");
        chkVendaPendente.setFontSize(-11);
        chkVendaPendente.setFontName("Tahoma");
        chkVendaPendente.setFontStyle("[]");
        chkVendaPendente.setVisible(false);
        chkVendaPendente.setVerticalAlignment("taVerticalCenter");
        FVBox7.addChildren(chkVendaPendente);
        chkVendaPendente.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(2);
        FVBox8.setTop(234);
        FVBox8.setWidth(580);
        FVBox8.setHeight(61);
        FVBox8.setBorderStyle("stSingleLine");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(5);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftFalse");
        FVBox8.setFlexHflex("ftFalse");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        FrmFichaItemEliminarReserva.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFLabel lblObservacao = new TFLabel();

    private void init_lblObservacao() {
        lblObservacao.setName("lblObservacao");
        lblObservacao.setLeft(0);
        lblObservacao.setTop(0);
        lblObservacao.setWidth(58);
        lblObservacao.setHeight(13);
        lblObservacao.setHint("Quantidade a ser liberada");
        lblObservacao.setCaption("Observa\u00E7\u00E3o");
        lblObservacao.setFontColor("clWindowText");
        lblObservacao.setFontSize(-11);
        lblObservacao.setFontName("Tahoma");
        lblObservacao.setFontStyle("[]");
        lblObservacao.setVerticalAlignment("taVerticalCenter");
        lblObservacao.setWordBreak(false);
        FVBox8.addChildren(lblObservacao);
        lblObservacao.applyProperties();
    }

    public TFString edtObservacao = new TFString();

    private void init_edtObservacao() {
        edtObservacao.setName("edtObservacao");
        edtObservacao.setLeft(0);
        edtObservacao.setTop(14);
        edtObservacao.setWidth(569);
        edtObservacao.setHeight(24);
        edtObservacao.setFieldName("OBSERVACAO");
        edtObservacao.setFlex(false);
        edtObservacao.setRequired(false);
        edtObservacao.setConstraintCheckWhen("cwImmediate");
        edtObservacao.setConstraintCheckType("ctExpression");
        edtObservacao.setConstraintFocusOnError(false);
        edtObservacao.setConstraintEnableUI(true);
        edtObservacao.setConstraintEnabled(false);
        edtObservacao.setConstraintFormCheck(true);
        edtObservacao.setCharCase("ccNormal");
        edtObservacao.setPwd(false);
        edtObservacao.setMaxlength(0);
        edtObservacao.setFontColor("clWindowText");
        edtObservacao.setFontSize(-13);
        edtObservacao.setFontName("Tahoma");
        edtObservacao.setFontStyle("[]");
        edtObservacao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtObservacaoEnter(event);
            processarFlow("FrmFichaItemEliminarReserva", "edtObservacao", "OnEnter");
        });
        edtObservacao.setSaveLiteralCharacter(false);
        edtObservacao.applyProperties();
        FVBox8.addChildren(edtObservacao);
        addValidatable(edtObservacao);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void FFormCreate(final Event<Object> event);

    public void btnExcluirClick(final Event<Object> event) {
        if (btnExcluir.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnExcluir");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void rdButtonEliminarTotalCheck(final Event<Object> event);

    public abstract void rdButtonEliminarParcialCheck(final Event<Object> event);

    public abstract void edtObservacaoEnter(final Event<Object> event);

}