package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmClientesProfissao extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.ClientesProfissaoRNA rn = null;

    public FrmClientesProfissao() {
        try {
            rn = (freedom.bytecode.rn.ClientesProfissaoRNA) getRN(freedom.bytecode.rn.wizard.ClientesProfissaoRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClientesProfissao();
        init_FVBox1();
        init_FHBox11();
        init_btnPesquisar();
        init_FHBox4();
        init_btnAceitar();
        init_FHBox2();
        init_btnVoltar();
        init_FHBox1();
        init_edtDescricaoProfissao();
        init_gridProfissoes();
        init_FrmClientesProfissao();
    }

    public CLIENTES_PROFISSAO tbClientesProfissao;

    private void init_tbClientesProfissao() {
        tbClientesProfissao = rn.tbClientesProfissao;
        tbClientesProfissao.setName("tbClientesProfissao");
        tbClientesProfissao.setMaxRowCount(0);
        tbClientesProfissao.setWKey("4600175;46001");
        tbClientesProfissao.setRatioBatchSize(20);
        getTables().put(tbClientesProfissao, "tbClientesProfissao");
        tbClientesProfissao.applyProperties();
    }

    protected TFForm FrmClientesProfissao = this;
    private void init_FrmClientesProfissao() {
        FrmClientesProfissao.setName("FrmClientesProfissao");
        FrmClientesProfissao.setCaption("Profiss\u00F5es & Ramos de Atividade");
        FrmClientesProfissao.setClientHeight(295);
        FrmClientesProfissao.setClientWidth(556);
        FrmClientesProfissao.setColor("clBtnFace");
        FrmClientesProfissao.setWKey("4600175");
        FrmClientesProfissao.setSpacing(0);
        FrmClientesProfissao.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(556);
        FVBox1.setHeight(295);
        FVBox1.setAlign("alClient");
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(5);
        FVBox1.setPaddingLeft(5);
        FVBox1.setPaddingRight(5);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmClientesProfissao.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(556);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(0);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(1);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        FVBox1.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFButton btnPesquisar = new TFButton();

    private void init_btnPesquisar() {
        btnPesquisar.setName("btnPesquisar");
        btnPesquisar.setLeft(0);
        btnPesquisar.setTop(0);
        btnPesquisar.setWidth(60);
        btnPesquisar.setHeight(56);
        btnPesquisar.setHint("Pesquisar");
        btnPesquisar.setAlign("alLeft");
        btnPesquisar.setCaption("Pesquisar");
        btnPesquisar.setFontColor("clWindowText");
        btnPesquisar.setFontSize(-13);
        btnPesquisar.setFontName("Tahoma");
        btnPesquisar.setFontStyle("[]");
        btnPesquisar.setLayout("blGlyphTop");
        btnPesquisar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnPesquisarClick(event);
            processarFlow("FrmClientesProfissao", "btnPesquisar", "OnClick");
        });
        btnPesquisar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000002224944415478DAAD954D481561148667840C347F1229DD0822680B4B"
 + "282368D12A90144522027321B8B44D8128B6085CA508091A486D021191D4853F"
 + "F8B30B42022D085D184482D4E6128982284A747B4E9DF0F03933772EDC0F5E5E"
 + "BE33EF39EF9CEF67C6F72286EFFBE7A13250020E40026C2593C95F5ECCE10714"
 + "95D86D30022A42F2C640274689B40CA85D08BD079762BE600FE8C72899D280E2"
 + "B20C5F414EDCF6758C53BF35D280E267A16D70D13C5B07ED608302C7BA74D2E1"
 + "033004B28CB60BCD4094813CEC34F17E693FAC75F4C5FA02A5267C01F98F00AD"
 + "5F00EFC66DD93149984E5E93D71E64700F9E34B13C84FB71169FDC87D00B133A"
 + "E31E61319886EFEA7C0E41539CE29A2CF764C7842AC9FFE21A6CC1E53AEF4030"
 + "928681ECE16F13BA45FE3B57B307E7EBBC0DC1685C032D600F4203F90BAEC10A"
 + "7C53E7CF103C49A3783674644237C85F750D9EC2BD3A97F52C8EBA994E722DB4"
 + "664292FBD33590CFC2A689D5235A8CB9FE9FC0150D7D23AF2C4827C2CFA05263"
 + "72CCCA117F4F61D007759B50333933A7742ABEECFDBB999E3191E3BAE42E17DA"
 + "73D02BD062C272518B8296D67EEC1E43CF9DE7B2272FB5C35C7007348634D542"
 + "FD8950033579040DA65AFF8871CA24E88723CB35E59DEC49D81806F36039CAC4"
 + "0FCAD48DAF02F7411DA80687E00378036629B2ABDAABD0C730934083740726D7"
 + "D4DC8EBFA72A23066AE25E3A195919335093EBD0FF4FC53A1DD464D4404DE4B7"
 + "5B03DECAAFF60FF9CFC91D70A0B6C30000000049454E44AE426082");
        btnPesquisar.setImageId(27001);
        btnPesquisar.setColor("clBtnFace");
        btnPesquisar.setAccess(false);
        btnPesquisar.setIconReverseDirection(false);
        FHBox11.addChildren(btnPesquisar);
        btnPesquisar.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(60);
        FHBox4.setTop(0);
        FHBox4.setWidth(7);
        FHBox4.setHeight(50);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftFalse");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        FHBox11.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFButton btnAceitar = new TFButton();

    private void init_btnAceitar() {
        btnAceitar.setName("btnAceitar");
        btnAceitar.setLeft(67);
        btnAceitar.setTop(0);
        btnAceitar.setWidth(60);
        btnAceitar.setHeight(56);
        btnAceitar.setHint("Aceitar");
        btnAceitar.setAlign("alLeft");
        btnAceitar.setCaption("Aceitar");
        btnAceitar.setFontColor("clWindowText");
        btnAceitar.setFontSize(-13);
        btnAceitar.setFontName("Tahoma");
        btnAceitar.setFontStyle("[]");
        btnAceitar.setLayout("blGlyphTop");
        btnAceitar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnAceitarClick(event);
            processarFlow("FrmClientesProfissao", "btnAceitar", "OnClick");
        });
        btnAceitar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515"
 + "0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3"
 + "1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7"
 + "95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC"
 + "101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D"
 + "1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537"
 + "A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC"
 + "C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB"
 + "482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB"
 + "1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D"
 + "C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F"
 + "8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082");
        btnAceitar.setImageId(700088);
        btnAceitar.setColor("clBtnFace");
        btnAceitar.setAccess(false);
        btnAceitar.setIconReverseDirection(false);
        FHBox11.addChildren(btnAceitar);
        btnAceitar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(127);
        FHBox2.setTop(0);
        FHBox2.setWidth(7);
        FHBox2.setHeight(48);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftFalse");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        FHBox11.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(134);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar Tela");
        btnVoltar.setAlign("alLeft");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-13);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmClientesProfissao", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(0);
        FHBox1.setTop(62);
        FHBox1.setWidth(556);
        FHBox1.setHeight(44);
        FHBox1.setAlign("alTop");
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(1);
        FHBox1.setFlexVflex("ftFalse");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        FVBox1.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFString edtDescricaoProfissao = new TFString();

    private void init_edtDescricaoProfissao() {
        edtDescricaoProfissao.setName("edtDescricaoProfissao");
        edtDescricaoProfissao.setLeft(0);
        edtDescricaoProfissao.setTop(0);
        edtDescricaoProfissao.setWidth(376);
        edtDescricaoProfissao.setHeight(24);
        edtDescricaoProfissao.setFlex(false);
        edtDescricaoProfissao.setRequired(false);
        edtDescricaoProfissao.setPrompt("Digite a profiss\u00E3o ou ramo de atividade");
        edtDescricaoProfissao.setConstraintCheckWhen("cwImmediate");
        edtDescricaoProfissao.setConstraintCheckType("ctExpression");
        edtDescricaoProfissao.setConstraintFocusOnError(false);
        edtDescricaoProfissao.setConstraintEnableUI(true);
        edtDescricaoProfissao.setConstraintEnabled(false);
        edtDescricaoProfissao.setConstraintFormCheck(true);
        edtDescricaoProfissao.setCharCase("ccNormal");
        edtDescricaoProfissao.setPwd(false);
        edtDescricaoProfissao.setMaxlength(0);
        edtDescricaoProfissao.setFontColor("clWindowText");
        edtDescricaoProfissao.setFontSize(-13);
        edtDescricaoProfissao.setFontName("Tahoma");
        edtDescricaoProfissao.setFontStyle("[]");
        edtDescricaoProfissao.addEventListener("onEnter", (EventListener<Event<Object>>)(Event<Object> event) -> {
            edtDescricaoProfissaoEnter(event);
            processarFlow("FrmClientesProfissao", "edtDescricaoProfissao", "OnEnter");
        });
        edtDescricaoProfissao.setSaveLiteralCharacter(false);
        edtDescricaoProfissao.applyProperties();
        FHBox1.addChildren(edtDescricaoProfissao);
        addValidatable(edtDescricaoProfissao);
    }

    public TFGrid gridProfissoes = new TFGrid();

    private void init_gridProfissoes() {
        gridProfissoes.setName("gridProfissoes");
        gridProfissoes.setLeft(0);
        gridProfissoes.setTop(107);
        gridProfissoes.setWidth(551);
        gridProfissoes.setHeight(184);
        gridProfissoes.setTable(tbClientesProfissao);
        gridProfissoes.setFlexVflex("ftTrue");
        gridProfissoes.setFlexHflex("ftTrue");
        gridProfissoes.setPagingEnabled(true);
        gridProfissoes.setFrozenColumns(0);
        gridProfissoes.setShowFooter(false);
        gridProfissoes.setShowHeader(true);
        gridProfissoes.setMultiSelection(false);
        gridProfissoes.setGroupingEnabled(false);
        gridProfissoes.setGroupingExpanded(false);
        gridProfissoes.setGroupingShowFooter(false);
        gridProfissoes.setCrosstabEnabled(false);
        gridProfissoes.setCrosstabGroupType("cgtConcat");
        gridProfissoes.setEditionEnabled(false);
        gridProfissoes.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("DESCRICAO_INIT");
        item0.setTitleCaption("Descri\u00E7\u00E3o");
        item0.setWidth(353);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(false);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(true);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        gridProfissoes.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("TIPO_DESC");
        item1.setTitleCaption("Tipo");
        item1.setWidth(162);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(false);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        gridProfissoes.getColumns().add(item1);
        FVBox1.addChildren(gridProfissoes);
        gridProfissoes.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnPesquisarClick(final Event<Object> event) {
        if (btnPesquisar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnPesquisar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnAceitarClick(final Event<Object> event) {
        if (btnAceitar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnAceitar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void edtDescricaoProfissaoEnter(final Event<Object> event);

}