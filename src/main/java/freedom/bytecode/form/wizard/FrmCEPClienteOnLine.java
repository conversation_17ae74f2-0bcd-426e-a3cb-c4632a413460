package freedom.bytecode.form.wizard;
import freedom.client.controls.delg.*;
import freedom.client.event.EventListener;
import freedom.data.Value;
import freedom.acesso.IAccessValidator;
import freedom.acesso.AccessValidatorFactory;
import freedom.bytecode.rn.wizard.*;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;
import freedom.client.util.ExceptionEngine;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.select.annotation.Wire;
import org.zkoss.zul.Window;
import java.util.HashMap;
import java.util.Map;
import org.zkoss.zk.ui.Executions;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrmCEPClienteOnLine extends TFForm  {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.CEPClienteOnLineRNA rn = null;

    public FrmCEPClienteOnLine() {
        try {
            rn = (freedom.bytecode.rn.CEPClienteOnLineRNA) getRN(freedom.bytecode.rn.wizard.CEPClienteOnLineRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_tbClientesEndereco();
        init_tbClientesEnderecoIe();
        init_tbClientesEnderecoTemp();
        init_tbClientesEnderecoIeTemp();
        init_tbCidades();
        init_menuImagens();
        init_FMenuItem3();
        init_FMenuItem4();
        init_vBoxTela();
        init_FHBox11();
        init_FHBox7();
        init_btnVoltar();
        init_btnSalvar();
        init_vBoxEnderecos();
        init_hBoxTitEndRes();
        init_vBoxlblEndResidencial();
        init_hBoxlblEndResidencial();
        init_FVBox6();
        init_FHBox1();
        init_FVBox44();
        init_lblEndResidencial();
        init_FVBox45();
        init_lblCEPResidencial();
        init_FVBox60();
        init_FVBox38();
        init_vBoxOptEndRes();
        init_chkAceitarMudancaRes();
        init_FHBox2();
        init_hbCEPOnLineAceitarRes();
        init_FVBox4();
        init_lblAceitarRes();
        init_FVBox5();
        init_hbCEPOnLineManterRes();
        init_FVBox53();
        init_lblManterRes();
        init_FVBox54();
        init_FVBox31();
        init_boxEndRes();
        init_vBoxEnderecoRes();
        init_hBoxEndRes();
        init_FVBox58();
        init_FLabel1();
        init_FLabel2();
        init_FHBox6();
        init_FVBox61();
        init_vBoxCidadeRes();
        init_vBoxCepEndRes();
        init_lblCepResAtual();
        init_lblCepResCorreio();
        init_vBoxRuaEndRes();
        init_lblRuaResCad();
        init_lblRuaResCorreios();
        init_vBoxNumeroRes();
        init_lblNumeroRes();
        init_hBoxNumeroResCad();
        init_edNumeroResCad();
        init_FVBox14();
        init_lblNumeroResCorreios();
        init_FVBox18();
        init_FVBox55();
        init_FHBox10();
        init_vBoxUFRuaEndRes();
        init_lblUFResCad();
        init_lblUFResCorreios();
        init_FVBox25();
        init_lblCidadeResCad();
        init_lblCidadeResCorreios();
        init_vBoxBairroRes();
        init_lblBairroResCad();
        init_lblBairroResCorreios();
        init_vBoxComplementoRes();
        init_lblComplementoRes();
        init_hBoxComplementoRes();
        init_edComplementoResCad();
        init_FVBox23();
        init_lblComplementoResCorreios();
        init_FVBox24();
        init_hBoxTitEndCom();
        init_vBoxlblEndComercial();
        init_hBoxlblEndComercial();
        init_FVBox2();
        init_hBoxlblEndComercial02();
        init_FVBox42();
        init_lblEndComercial();
        init_FVBox43();
        init_lblCEPCom();
        init_FVBox69();
        init_FVBox17();
        init_vBoxOptEndCom();
        init_chkAceitarMudancaCom();
        init_FHBox4();
        init_hbCEPOnLineAceitarCom();
        init_FVBox19();
        init_lblAceitarCom();
        init_FVBox20();
        init_hbCEPOnLineManterCom();
        init_FVBox21();
        init_lblRecusarCom();
        init_FVBox22();
        init_FVBox32();
        init_boxEndCom();
        init_vBoxEnderecoCom();
        init_hBoxEndCom();
        init_FVBox71();
        init_FLabel5();
        init_FLabel7();
        init_vBoxCidadeCom();
        init_vBoxCepEndCom();
        init_lblCepComAtual();
        init_lblCepComCorreio();
        init_vBoxRuaEndCom();
        init_lblRuaComCad();
        init_lblRuaComCorreios();
        init_vBoxNumeroCom();
        init_lblNumeroCom();
        init_hBoxNumeroComCad();
        init_edNumeroComCad();
        init_FVBox56();
        init_lblNumeroComCorreios();
        init_FVBox26();
        init_FVBox16();
        init_FHBox8();
        init_vBoxUFRuaEndCom();
        init_lblUFComCad();
        init_lblUFComCorreios();
        init_vBoxDivEnd16();
        init_lblCidadeComCad();
        init_lblCidadeComCorreios();
        init_vBoxBairroCom();
        init_lblBairroComCad();
        init_lblBairroComCorreios();
        init_vBoxComplementoCom();
        init_lblComplementoCom();
        init_hBoxComplementoCom();
        init_edComplementoComCad();
        init_FVBox3();
        init_lblComplementoComCorreios();
        init_FVBox27();
        init_hBoxTitEndCob();
        init_vBoxlblEndCob();
        init_hBoxlblEndCob();
        init_FVBox8();
        init_hBoxlblEndCob02();
        init_FVBox1();
        init_lblEndCob();
        init_FVBox41();
        init_lblCEPCob();
        init_FVBox70();
        init_FVBox39();
        init_vBoxOptEndCob();
        init_chkAceitarMudancaCobr();
        init_FHBox5();
        init_hbCEPOnLineAceitarCob();
        init_FVBox10();
        init_lblAceitarCob();
        init_FVBox11();
        init_hbCEPOnLineManterCob();
        init_FVBox12();
        init_lblManterCob();
        init_FVBox13();
        init_FVBox34();
        init_boxEndCob();
        init_vBoxEnderecoCob();
        init_hBoxEndCob();
        init_FVBox73();
        init_FLabel3();
        init_FLabel4();
        init_vBoxCidadeCob();
        init_vBoxCepEndCobr();
        init_lblCepCobAtual();
        init_lblCepCobCorreio();
        init_vBoxRuaEndCob();
        init_lblRuaCobCad();
        init_lblRuaCobCorreios();
        init_vBoxNumeroCob();
        init_lblNumeroCob();
        init_hBoxNumeroCobCad();
        init_edNumeroCobCad();
        init_FVBox51();
        init_lblNumeroCobCorreios();
        init_FVBox29();
        init_FVBox30();
        init_FHBox3();
        init_vBoxUFEndCob();
        init_lblUFCobCad();
        init_lblUFCobCorreios();
        init_FVBox28();
        init_lblCidadeCobCad();
        init_lblCidadeCobCorreios();
        init_vBoxBairroCob();
        init_lblBairroCobCad();
        init_lblBairroCobCorreios();
        init_FVBox33();
        init_FLabel24();
        init_hBoxComplementoCob();
        init_edComplementoCobCad();
        init_FVBox50();
        init_lblComplementoCobCorreios();
        init_FVBox52();
        init_hBoxTitEndIE();
        init_vBoxlblEndIE();
        init_hBoxlblEndIE();
        init_FVBox7();
        init_hBoxlblEndIE02();
        init_FVBox9();
        init_lblEndIE();
        init_FVBox35();
        init_FVBox36();
        init_vBoxOptEndIE();
        init_FHBox12();
        init_hbCEPOnLineAceitarIE();
        init_FVBox40();
        init_lblAceitarIE();
        init_FVBox46();
        init_hbCEPOnLineManterIE();
        init_FVBox47();
        init_lblManterIE();
        init_FVBox48();
        init_FVBox49();
        init_boxEndIE();
        init_FVBox37();
        init_grdEndIE();
        init_FHBox14();
        init_FVBox57();
        init_lblUFCadIE();
        init_lblUFCorreiosIE();
        init_vBoxCidadeIE();
        init_FVBox59();
        init_lblCidadeCadIE();
        init_lblCidadeCorreiosIE();
        init_vBoxNumeroIE();
        init_FLabel6();
        init_hBoxNumeroCadIE();
        init_edNumeroCadIE();
        init_FVBox15();
        init_lblNumeroCorreiosIE();
        init_FVBox62();
        init_FVBox63();
        init_FHBox17();
        init_FVBox64();
        init_lblRuaCadIE();
        init_lblRuaCorreiosIE();
        init_FVBox65();
        init_lblBairroCadIE();
        init_lblBairroCorreiosIE();
        init_FVBox66();
        init_FLabel12();
        init_FHBox18();
        init_edComplementoCadIE();
        init_FVBox67();
        init_lblComplementoCorreiosIE();
        init_FVBox68();
        init_sc();
        init_FrmCEPClienteOnLine();
    }

    public CLIENTES_ENDERECO tbClientesEndereco;

    private void init_tbClientesEndereco() {
        tbClientesEndereco = rn.tbClientesEndereco;
        tbClientesEndereco.setName("tbClientesEndereco");
        tbClientesEndereco.setMaxRowCount(0);
        tbClientesEndereco.setWKey("4600193;46001");
        tbClientesEndereco.setRatioBatchSize(20);
        getTables().put(tbClientesEndereco, "tbClientesEndereco");
        tbClientesEndereco.applyProperties();
    }

    public CLIENTES_ENDERECO_IE tbClientesEnderecoIe;

    private void init_tbClientesEnderecoIe() {
        tbClientesEnderecoIe = rn.tbClientesEnderecoIe;
        tbClientesEnderecoIe.setName("tbClientesEnderecoIe");
        tbClientesEnderecoIe.setMaxRowCount(0);
        tbClientesEnderecoIe.addEventListener("onAfterScroll", (EventListener<Event<Object>>) (Event<Object> event) -> {
            tbClientesEnderecoIeAfterScroll(event);
            processarFlow("FrmCEPClienteOnLine", "tbClientesEnderecoIe", "OnAfterScroll");
        });
        tbClientesEnderecoIe.setWKey("4600193;46002");
        tbClientesEnderecoIe.setRatioBatchSize(20);
        getTables().put(tbClientesEnderecoIe, "tbClientesEnderecoIe");
        tbClientesEnderecoIe.applyProperties();
    }

    public DUAL tbClientesEnderecoTemp;

    private void init_tbClientesEnderecoTemp() {
        tbClientesEnderecoTemp = rn.tbClientesEnderecoTemp;
        tbClientesEnderecoTemp.setName("tbClientesEnderecoTemp");
        TFTableField item83 = new TFTableField();
        item83.setName("COD_CLIENTE");
        item83.setCalculated(true);
        item83.setUpdatable(false);
        item83.setPrimaryKey(false);
        item83.setFieldType("ftInteger");
        item83.setJSONConfigNullOnEmpty(false);
        item83.setCaption("COD_CLIENTE");
        tbClientesEnderecoTemp.getFieldDefs().add(item83);
        TFTableField item84 = new TFTableField();
        item84.setName("CEP_RES");
        item84.setCalculated(true);
        item84.setUpdatable(false);
        item84.setPrimaryKey(false);
        item84.setFieldType("ftString");
        item84.setJSONConfigNullOnEmpty(false);
        item84.setCaption("CEP_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item84);
        TFTableField item85 = new TFTableField();
        item85.setName("UF_RES");
        item85.setCalculated(true);
        item85.setUpdatable(false);
        item85.setPrimaryKey(false);
        item85.setFieldType("ftString");
        item85.setJSONConfigNullOnEmpty(false);
        item85.setCaption("UF_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item85);
        TFTableField item86 = new TFTableField();
        item86.setName("COD_CID_RES");
        item86.setCalculated(true);
        item86.setUpdatable(false);
        item86.setPrimaryKey(false);
        item86.setFieldType("ftInteger");
        item86.setJSONConfigNullOnEmpty(false);
        item86.setCaption("COD_CID_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item86);
        TFTableField item87 = new TFTableField();
        item87.setName("RUA_RES");
        item87.setCalculated(true);
        item87.setUpdatable(false);
        item87.setPrimaryKey(false);
        item87.setFieldType("ftString");
        item87.setJSONConfigNullOnEmpty(false);
        item87.setCaption("RUA_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item87);
        TFTableField item88 = new TFTableField();
        item88.setName("FACHADA_RES");
        item88.setCalculated(true);
        item88.setUpdatable(false);
        item88.setPrimaryKey(false);
        item88.setFieldType("ftString");
        item88.setJSONConfigNullOnEmpty(false);
        item88.setCaption("FACHADA_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item88);
        TFTableField item89 = new TFTableField();
        item89.setName("BAIRRO_RES");
        item89.setCalculated(true);
        item89.setUpdatable(false);
        item89.setPrimaryKey(false);
        item89.setFieldType("ftString");
        item89.setJSONConfigNullOnEmpty(false);
        item89.setCaption("BAIRRO_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item89);
        TFTableField item90 = new TFTableField();
        item90.setName("COMPLEMENTO_RES");
        item90.setCalculated(true);
        item90.setUpdatable(false);
        item90.setPrimaryKey(false);
        item90.setFieldType("ftString");
        item90.setJSONConfigNullOnEmpty(false);
        item90.setCaption("COMPLEMENTO_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item90);
        TFTableField item91 = new TFTableField();
        item91.setName("CEP_COM");
        item91.setCalculated(true);
        item91.setUpdatable(false);
        item91.setPrimaryKey(false);
        item91.setFieldType("ftString");
        item91.setJSONConfigNullOnEmpty(false);
        item91.setCaption("CEP_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item91);
        TFTableField item92 = new TFTableField();
        item92.setName("UF_COM");
        item92.setCalculated(true);
        item92.setUpdatable(false);
        item92.setPrimaryKey(false);
        item92.setFieldType("ftString");
        item92.setJSONConfigNullOnEmpty(false);
        item92.setCaption("UF_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item92);
        TFTableField item93 = new TFTableField();
        item93.setName("COD_CID_COM");
        item93.setCalculated(true);
        item93.setUpdatable(false);
        item93.setPrimaryKey(false);
        item93.setFieldType("ftInteger");
        item93.setJSONConfigNullOnEmpty(false);
        item93.setCaption("COD_CID_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item93);
        TFTableField item94 = new TFTableField();
        item94.setName("RUA_COM");
        item94.setCalculated(true);
        item94.setUpdatable(false);
        item94.setPrimaryKey(false);
        item94.setFieldType("ftString");
        item94.setJSONConfigNullOnEmpty(false);
        item94.setCaption("RUA_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item94);
        TFTableField item95 = new TFTableField();
        item95.setName("FACHADA_COM");
        item95.setCalculated(true);
        item95.setUpdatable(false);
        item95.setPrimaryKey(false);
        item95.setFieldType("ftString");
        item95.setJSONConfigNullOnEmpty(false);
        item95.setCaption("FACHADA_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item95);
        TFTableField item96 = new TFTableField();
        item96.setName("BAIRRO_COM");
        item96.setCalculated(true);
        item96.setUpdatable(false);
        item96.setPrimaryKey(false);
        item96.setFieldType("ftString");
        item96.setJSONConfigNullOnEmpty(false);
        item96.setCaption("BAIRRO_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item96);
        TFTableField item97 = new TFTableField();
        item97.setName("COMPLEMENTO_COM");
        item97.setCalculated(true);
        item97.setUpdatable(false);
        item97.setPrimaryKey(false);
        item97.setFieldType("ftString");
        item97.setJSONConfigNullOnEmpty(false);
        item97.setCaption("COMPLEMENTO_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item97);
        TFTableField item98 = new TFTableField();
        item98.setName("CEP_COBRANCA");
        item98.setCalculated(true);
        item98.setUpdatable(false);
        item98.setPrimaryKey(false);
        item98.setFieldType("ftString");
        item98.setJSONConfigNullOnEmpty(false);
        item98.setCaption("CEP_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item98);
        TFTableField item99 = new TFTableField();
        item99.setName("UF_COBRANCA");
        item99.setCalculated(true);
        item99.setUpdatable(false);
        item99.setPrimaryKey(false);
        item99.setFieldType("ftString");
        item99.setJSONConfigNullOnEmpty(false);
        item99.setCaption("UF_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item99);
        TFTableField item100 = new TFTableField();
        item100.setName("COD_CID_COBRANCA");
        item100.setCalculated(true);
        item100.setUpdatable(false);
        item100.setPrimaryKey(false);
        item100.setFieldType("ftInteger");
        item100.setJSONConfigNullOnEmpty(false);
        item100.setCaption("COD_CID_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item100);
        TFTableField item101 = new TFTableField();
        item101.setName("RUA_COBRANCA");
        item101.setCalculated(true);
        item101.setUpdatable(false);
        item101.setPrimaryKey(false);
        item101.setFieldType("ftString");
        item101.setJSONConfigNullOnEmpty(false);
        item101.setCaption("RUA_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item101);
        TFTableField item102 = new TFTableField();
        item102.setName("FACHADA_COBRANCA");
        item102.setCalculated(true);
        item102.setUpdatable(false);
        item102.setPrimaryKey(false);
        item102.setFieldType("ftString");
        item102.setJSONConfigNullOnEmpty(false);
        item102.setCaption("FACHADA_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item102);
        TFTableField item103 = new TFTableField();
        item103.setName("BAIRRO_COBRANCA");
        item103.setCalculated(true);
        item103.setUpdatable(false);
        item103.setPrimaryKey(false);
        item103.setFieldType("ftString");
        item103.setJSONConfigNullOnEmpty(false);
        item103.setCaption("BAIRRO_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item103);
        TFTableField item104 = new TFTableField();
        item104.setName("COMPLEMENTO_COBRANCA");
        item104.setCalculated(true);
        item104.setUpdatable(false);
        item104.setPrimaryKey(false);
        item104.setFieldType("ftString");
        item104.setJSONConfigNullOnEmpty(false);
        item104.setCaption("COMPLEMENTO_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item104);
        TFTableField item105 = new TFTableField();
        item105.setName("CIDADE_RES");
        item105.setCalculated(true);
        item105.setUpdatable(false);
        item105.setPrimaryKey(false);
        item105.setFieldType("ftString");
        item105.setJSONConfigNullOnEmpty(false);
        item105.setCaption("CIDADE_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item105);
        TFTableField item106 = new TFTableField();
        item106.setName("CIDADE_COB");
        item106.setCalculated(true);
        item106.setUpdatable(false);
        item106.setPrimaryKey(false);
        item106.setFieldType("ftString");
        item106.setJSONConfigNullOnEmpty(false);
        item106.setCaption("CIDADE_COB");
        tbClientesEnderecoTemp.getFieldDefs().add(item106);
        TFTableField item107 = new TFTableField();
        item107.setName("CIDADE_COM");
        item107.setCalculated(true);
        item107.setUpdatable(false);
        item107.setPrimaryKey(false);
        item107.setFieldType("ftString");
        item107.setJSONConfigNullOnEmpty(false);
        item107.setCaption("CIDADE_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item107);
        TFTableField item108 = new TFTableField();
        item108.setName("CODCIDADEIBGE_RES");
        item108.setCalculated(true);
        item108.setUpdatable(false);
        item108.setPrimaryKey(false);
        item108.setFieldType("ftInteger");
        item108.setJSONConfigNullOnEmpty(false);
        item108.setCaption("CODCIDADEIBGE_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item108);
        TFTableField item109 = new TFTableField();
        item109.setName("CODCIDADEIBGE_COM");
        item109.setCalculated(true);
        item109.setUpdatable(false);
        item109.setPrimaryKey(false);
        item109.setFieldType("ftInteger");
        item109.setJSONConfigNullOnEmpty(false);
        item109.setCaption("CODCIDADEIBGE_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item109);
        TFTableField item110 = new TFTableField();
        item110.setName("CODCIDADEIBGE_COB");
        item110.setCalculated(true);
        item110.setUpdatable(false);
        item110.setPrimaryKey(false);
        item110.setFieldType("ftInteger");
        item110.setJSONConfigNullOnEmpty(false);
        item110.setCaption("CODCIDADEIBGE_COB");
        tbClientesEnderecoTemp.getFieldDefs().add(item110);
        tbClientesEnderecoTemp.setMaxRowCount(0);
        tbClientesEnderecoTemp.setWKey("4600193;46004");
        tbClientesEnderecoTemp.setRatioBatchSize(20);
        getTables().put(tbClientesEnderecoTemp, "tbClientesEnderecoTemp");
        tbClientesEnderecoTemp.applyProperties();
    }

    public DUAL tbClientesEnderecoIeTemp;

    private void init_tbClientesEnderecoIeTemp() {
        tbClientesEnderecoIeTemp = rn.tbClientesEnderecoIeTemp;
        tbClientesEnderecoIeTemp.setName("tbClientesEnderecoIeTemp");
        TFTableField item111 = new TFTableField();
        item111.setName("COD_CLIENTE");
        item111.setCalculated(true);
        item111.setUpdatable(false);
        item111.setPrimaryKey(false);
        item111.setFieldType("ftInteger");
        item111.setJSONConfigNullOnEmpty(false);
        item111.setCaption("COD_CLIENTE");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item111);
        TFTableField item112 = new TFTableField();
        item112.setName("INSCRICAO_ESTADUAL");
        item112.setCalculated(true);
        item112.setUpdatable(false);
        item112.setPrimaryKey(false);
        item112.setFieldType("ftString");
        item112.setJSONConfigNullOnEmpty(false);
        item112.setCaption("INSCRICAO_ESTADUAL");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item112);
        TFTableField item113 = new TFTableField();
        item113.setName("CEP");
        item113.setCalculated(true);
        item113.setUpdatable(false);
        item113.setPrimaryKey(false);
        item113.setFieldType("ftString");
        item113.setJSONConfigNullOnEmpty(false);
        item113.setCaption("CEP");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item113);
        TFTableField item114 = new TFTableField();
        item114.setName("UF");
        item114.setCalculated(true);
        item114.setUpdatable(false);
        item114.setPrimaryKey(false);
        item114.setFieldType("ftString");
        item114.setJSONConfigNullOnEmpty(false);
        item114.setCaption("UF");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item114);
        TFTableField item115 = new TFTableField();
        item115.setName("RUA");
        item115.setCalculated(true);
        item115.setUpdatable(false);
        item115.setPrimaryKey(false);
        item115.setFieldType("ftString");
        item115.setJSONConfigNullOnEmpty(false);
        item115.setCaption("RUA");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item115);
        TFTableField item116 = new TFTableField();
        item116.setName("CIDADE");
        item116.setCalculated(true);
        item116.setUpdatable(false);
        item116.setPrimaryKey(false);
        item116.setFieldType("ftString");
        item116.setJSONConfigNullOnEmpty(false);
        item116.setCaption("CIDADE");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item116);
        TFTableField item117 = new TFTableField();
        item117.setName("BAIRRO");
        item117.setCalculated(true);
        item117.setUpdatable(false);
        item117.setPrimaryKey(false);
        item117.setFieldType("ftString");
        item117.setJSONConfigNullOnEmpty(false);
        item117.setCaption("BAIRRO");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item117);
        TFTableField item118 = new TFTableField();
        item118.setName("COMPLEMENTO");
        item118.setCalculated(true);
        item118.setUpdatable(false);
        item118.setPrimaryKey(false);
        item118.setFieldType("ftString");
        item118.setJSONConfigNullOnEmpty(false);
        item118.setCaption("COMPLEMENTO");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item118);
        TFTableField item119 = new TFTableField();
        item119.setName("FACHADA");
        item119.setCalculated(true);
        item119.setUpdatable(false);
        item119.setPrimaryKey(false);
        item119.setFieldType("ftString");
        item119.setJSONConfigNullOnEmpty(false);
        item119.setCaption("FACHADA");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item119);
        TFTableField item120 = new TFTableField();
        item120.setName("COD_CIDADES");
        item120.setCalculated(true);
        item120.setUpdatable(false);
        item120.setPrimaryKey(false);
        item120.setFieldType("ftInteger");
        item120.setJSONConfigNullOnEmpty(false);
        item120.setCaption("COD_CIDADES");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item120);
        tbClientesEnderecoIeTemp.setMaxRowCount(200);
        tbClientesEnderecoIeTemp.setWKey("4600193;46005");
        tbClientesEnderecoIeTemp.setRatioBatchSize(20);
        getTables().put(tbClientesEnderecoIeTemp, "tbClientesEnderecoIeTemp");
        tbClientesEnderecoIeTemp.applyProperties();
    }

    public CIDADES tbCidades;

    private void init_tbCidades() {
        tbCidades = rn.tbCidades;
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("4600193;46006");
        tbCidades.setRatioBatchSize(20);
        getTables().put(tbCidades, "tbCidades");
        tbCidades.applyProperties();
    }

    public TFPopupMenu menuImagens = new TFPopupMenu();

    private void init_menuImagens() {
        menuImagens.setName("menuImagens");
        FrmCEPClienteOnLine.addChildren(menuImagens);
        menuImagens.applyProperties();
    }

    public TFMenuItem FMenuItem3 = new TFMenuItem();

    private void init_FMenuItem3() {
        FMenuItem3.setName("FMenuItem3");
        FMenuItem3.setCaption("FMenuItem3");
        FMenuItem3.setImageIndex(700099);
        FMenuItem3.setAccess(false);
        FMenuItem3.setCheckmark(false);
        menuImagens.addChildren(FMenuItem3);
        FMenuItem3.applyProperties();
    }

    public TFMenuItem FMenuItem4 = new TFMenuItem();

    private void init_FMenuItem4() {
        FMenuItem4.setName("FMenuItem4");
        FMenuItem4.setCaption("FMenuItem4");
        FMenuItem4.setImageIndex(7000118);
        FMenuItem4.setAccess(false);
        FMenuItem4.setCheckmark(false);
        menuImagens.addChildren(FMenuItem4);
        FMenuItem4.applyProperties();
    }

    protected TFForm FrmCEPClienteOnLine = this;
    private void init_FrmCEPClienteOnLine() {
        FrmCEPClienteOnLine.setName("FrmCEPClienteOnLine");
        FrmCEPClienteOnLine.setCaption("CEP do Cliente - OnLine [Integra\u00E7\u00E3o API ViaCEP]");
        FrmCEPClienteOnLine.setClientHeight(762);
        FrmCEPClienteOnLine.setClientWidth(604);
        FrmCEPClienteOnLine.setColor("clBtnFace");
        FrmCEPClienteOnLine.setWKey("4600193");
        FrmCEPClienteOnLine.setSpacing(0);
        FrmCEPClienteOnLine.applyProperties();
    }

    public TFVBox vBoxTela = new TFVBox();

    private void init_vBoxTela() {
        vBoxTela.setName("vBoxTela");
        vBoxTela.setLeft(0);
        vBoxTela.setTop(0);
        vBoxTela.setWidth(604);
        vBoxTela.setHeight(762);
        vBoxTela.setAlign("alClient");
        vBoxTela.setBorderStyle("stNone");
        vBoxTela.setPaddingTop(0);
        vBoxTela.setPaddingLeft(0);
        vBoxTela.setPaddingRight(0);
        vBoxTela.setPaddingBottom(0);
        vBoxTela.setMarginTop(0);
        vBoxTela.setMarginLeft(0);
        vBoxTela.setMarginRight(0);
        vBoxTela.setMarginBottom(0);
        vBoxTela.setSpacing(1);
        vBoxTela.setFlexVflex("ftFalse");
        vBoxTela.setFlexHflex("ftTrue");
        vBoxTela.setScrollable(false);
        vBoxTela.setBoxShadowConfigHorizontalLength(10);
        vBoxTela.setBoxShadowConfigVerticalLength(10);
        vBoxTela.setBoxShadowConfigBlurRadius(5);
        vBoxTela.setBoxShadowConfigSpreadRadius(0);
        vBoxTela.setBoxShadowConfigShadowColor("clBlack");
        vBoxTela.setBoxShadowConfigOpacity(75);
        FrmCEPClienteOnLine.addChildren(vBoxTela);
        vBoxTela.applyProperties();
    }

    public TFHBox FHBox11 = new TFHBox();

    private void init_FHBox11() {
        FHBox11.setName("FHBox11");
        FHBox11.setLeft(0);
        FHBox11.setTop(0);
        FHBox11.setWidth(468);
        FHBox11.setHeight(61);
        FHBox11.setAlign("alTop");
        FHBox11.setBorderStyle("stNone");
        FHBox11.setPaddingTop(5);
        FHBox11.setPaddingLeft(0);
        FHBox11.setPaddingRight(0);
        FHBox11.setPaddingBottom(0);
        FHBox11.setMarginTop(0);
        FHBox11.setMarginLeft(0);
        FHBox11.setMarginRight(0);
        FHBox11.setMarginBottom(0);
        FHBox11.setSpacing(5);
        FHBox11.setFlexVflex("ftFalse");
        FHBox11.setFlexHflex("ftTrue");
        FHBox11.setScrollable(false);
        FHBox11.setBoxShadowConfigHorizontalLength(10);
        FHBox11.setBoxShadowConfigVerticalLength(10);
        FHBox11.setBoxShadowConfigBlurRadius(5);
        FHBox11.setBoxShadowConfigSpreadRadius(0);
        FHBox11.setBoxShadowConfigShadowColor("clBlack");
        FHBox11.setBoxShadowConfigOpacity(75);
        FHBox11.setVAlign("tvTop");
        vBoxTela.addChildren(FHBox11);
        FHBox11.applyProperties();
    }

    public TFHBox FHBox7 = new TFHBox();

    private void init_FHBox7() {
        FHBox7.setName("FHBox7");
        FHBox7.setLeft(0);
        FHBox7.setTop(0);
        FHBox7.setWidth(2);
        FHBox7.setHeight(50);
        FHBox7.setBorderStyle("stNone");
        FHBox7.setPaddingTop(0);
        FHBox7.setPaddingLeft(0);
        FHBox7.setPaddingRight(0);
        FHBox7.setPaddingBottom(0);
        FHBox7.setMarginTop(0);
        FHBox7.setMarginLeft(0);
        FHBox7.setMarginRight(0);
        FHBox7.setMarginBottom(0);
        FHBox7.setSpacing(1);
        FHBox7.setFlexVflex("ftFalse");
        FHBox7.setFlexHflex("ftFalse");
        FHBox7.setScrollable(false);
        FHBox7.setBoxShadowConfigHorizontalLength(10);
        FHBox7.setBoxShadowConfigVerticalLength(10);
        FHBox7.setBoxShadowConfigBlurRadius(5);
        FHBox7.setBoxShadowConfigSpreadRadius(0);
        FHBox7.setBoxShadowConfigShadowColor("clBlack");
        FHBox7.setBoxShadowConfigOpacity(75);
        FHBox7.setVAlign("tvTop");
        FHBox11.addChildren(FHBox7);
        FHBox7.applyProperties();
    }

    public TFButton btnVoltar = new TFButton();

    private void init_btnVoltar() {
        btnVoltar.setName("btnVoltar");
        btnVoltar.setLeft(2);
        btnVoltar.setTop(0);
        btnVoltar.setWidth(60);
        btnVoltar.setHeight(56);
        btnVoltar.setHint("Voltar");
        btnVoltar.setCaption("Voltar");
        btnVoltar.setFontColor("clWindowText");
        btnVoltar.setFontSize(-16);
        btnVoltar.setFontName("Tahoma");
        btnVoltar.setFontStyle("[]");
        btnVoltar.setLayout("blGlyphTop");
        btnVoltar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnVoltarClick(event);
            processarFlow("FrmCEPClienteOnLine", "btnVoltar", "OnClick");
        });
        btnVoltar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A"
 + "FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174"
 + "290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5"
 + "086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8"
 + "152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648"
 + "F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D"
 + "86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402"
 + "B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8"
 + "AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364"
 + "EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D"
 + "AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437"
 + "736BB6EF9B710000000049454E44AE426082");
        btnVoltar.setImageId(700081);
        btnVoltar.setColor("clBtnFace");
        btnVoltar.setAccess(false);
        btnVoltar.setIconReverseDirection(false);
        FHBox11.addChildren(btnVoltar);
        btnVoltar.applyProperties();
    }

    public TFButton btnSalvar = new TFButton();

    private void init_btnSalvar() {
        btnSalvar.setName("btnSalvar");
        btnSalvar.setLeft(62);
        btnSalvar.setTop(0);
        btnSalvar.setWidth(66);
        btnSalvar.setHeight(56);
        btnSalvar.setHint("Salvar Altera\u00E7\u00F5es Aceitas");
        btnSalvar.setCaption("Salvar");
        btnSalvar.setFontColor("clWindowText");
        btnSalvar.setFontSize(-16);
        btnSalvar.setFontName("Tahoma");
        btnSalvar.setFontStyle("[]");
        btnSalvar.setLayout("blGlyphTop");
        btnSalvar.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            btnSalvarClick(event);
            processarFlow("FrmCEPClienteOnLine", "btnSalvar", "OnClick");
        });
        btnSalvar.setPngImageData("89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D"
 + "F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3"
 + "0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220"
 + "AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950"
 + "4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5"
 + "5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37"
 + "35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067"
 + "603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7"
 + "29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B"
 + "DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA"
 + "B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B"
 + "A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194"
 + "B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002"
 + "EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715"
 + "87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B"
 + "AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9"
 + "32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66"
 + "5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E"
 + "4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05"
 + "05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF"
 + "0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76"
 + "093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0"
 + "C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65"
 + "C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70"
 + "9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D"
 + "80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12"
 + "B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8"
 + "6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087"
 + "94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF"
 + "0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34"
 + "2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2"
 + "134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000"
 + "0049454E44AE426082");
        btnSalvar.setImageId(4);
        btnSalvar.setColor("clBtnFace");
        btnSalvar.setAccess(false);
        btnSalvar.setIconReverseDirection(false);
        FHBox11.addChildren(btnSalvar);
        btnSalvar.applyProperties();
    }

    public TFVBox vBoxEnderecos = new TFVBox();

    private void init_vBoxEnderecos() {
        vBoxEnderecos.setName("vBoxEnderecos");
        vBoxEnderecos.setLeft(0);
        vBoxEnderecos.setTop(62);
        vBoxEnderecos.setWidth(468);
        vBoxEnderecos.setHeight(900);
        vBoxEnderecos.setBorderStyle("stNone");
        vBoxEnderecos.setPaddingTop(0);
        vBoxEnderecos.setPaddingLeft(0);
        vBoxEnderecos.setPaddingRight(0);
        vBoxEnderecos.setPaddingBottom(0);
        vBoxEnderecos.setMarginTop(0);
        vBoxEnderecos.setMarginLeft(0);
        vBoxEnderecos.setMarginRight(0);
        vBoxEnderecos.setMarginBottom(0);
        vBoxEnderecos.setSpacing(1);
        vBoxEnderecos.setFlexVflex("ftFalse");
        vBoxEnderecos.setFlexHflex("ftTrue");
        vBoxEnderecos.setScrollable(false);
        vBoxEnderecos.setBoxShadowConfigHorizontalLength(10);
        vBoxEnderecos.setBoxShadowConfigVerticalLength(10);
        vBoxEnderecos.setBoxShadowConfigBlurRadius(5);
        vBoxEnderecos.setBoxShadowConfigSpreadRadius(0);
        vBoxEnderecos.setBoxShadowConfigShadowColor("clBlack");
        vBoxEnderecos.setBoxShadowConfigOpacity(75);
        vBoxTela.addChildren(vBoxEnderecos);
        vBoxEnderecos.applyProperties();
    }

    public TFHBox hBoxTitEndRes = new TFHBox();

    private void init_hBoxTitEndRes() {
        hBoxTitEndRes.setName("hBoxTitEndRes");
        hBoxTitEndRes.setLeft(0);
        hBoxTitEndRes.setTop(0);
        hBoxTitEndRes.setWidth(464);
        hBoxTitEndRes.setHeight(50);
        hBoxTitEndRes.setBorderStyle("stNone");
        hBoxTitEndRes.setPaddingTop(0);
        hBoxTitEndRes.setPaddingLeft(0);
        hBoxTitEndRes.setPaddingRight(0);
        hBoxTitEndRes.setPaddingBottom(0);
        hBoxTitEndRes.setMarginTop(0);
        hBoxTitEndRes.setMarginLeft(0);
        hBoxTitEndRes.setMarginRight(0);
        hBoxTitEndRes.setMarginBottom(0);
        hBoxTitEndRes.setSpacing(1);
        hBoxTitEndRes.setFlexVflex("ftFalse");
        hBoxTitEndRes.setFlexHflex("ftTrue");
        hBoxTitEndRes.setScrollable(false);
        hBoxTitEndRes.setBoxShadowConfigHorizontalLength(10);
        hBoxTitEndRes.setBoxShadowConfigVerticalLength(10);
        hBoxTitEndRes.setBoxShadowConfigBlurRadius(5);
        hBoxTitEndRes.setBoxShadowConfigSpreadRadius(0);
        hBoxTitEndRes.setBoxShadowConfigShadowColor("clBlack");
        hBoxTitEndRes.setBoxShadowConfigOpacity(75);
        hBoxTitEndRes.setVAlign("tvTop");
        vBoxEnderecos.addChildren(hBoxTitEndRes);
        hBoxTitEndRes.applyProperties();
    }

    public TFVBox vBoxlblEndResidencial = new TFVBox();

    private void init_vBoxlblEndResidencial() {
        vBoxlblEndResidencial.setName("vBoxlblEndResidencial");
        vBoxlblEndResidencial.setLeft(0);
        vBoxlblEndResidencial.setTop(0);
        vBoxlblEndResidencial.setWidth(462);
        vBoxlblEndResidencial.setHeight(45);
        vBoxlblEndResidencial.setBorderStyle("stNone");
        vBoxlblEndResidencial.setPaddingTop(10);
        vBoxlblEndResidencial.setPaddingLeft(0);
        vBoxlblEndResidencial.setPaddingRight(0);
        vBoxlblEndResidencial.setPaddingBottom(0);
        vBoxlblEndResidencial.setMarginTop(0);
        vBoxlblEndResidencial.setMarginLeft(0);
        vBoxlblEndResidencial.setMarginRight(0);
        vBoxlblEndResidencial.setMarginBottom(0);
        vBoxlblEndResidencial.setSpacing(1);
        vBoxlblEndResidencial.setFlexVflex("ftFalse");
        vBoxlblEndResidencial.setFlexHflex("ftTrue");
        vBoxlblEndResidencial.setScrollable(false);
        vBoxlblEndResidencial.setBoxShadowConfigHorizontalLength(10);
        vBoxlblEndResidencial.setBoxShadowConfigVerticalLength(10);
        vBoxlblEndResidencial.setBoxShadowConfigBlurRadius(5);
        vBoxlblEndResidencial.setBoxShadowConfigSpreadRadius(0);
        vBoxlblEndResidencial.setBoxShadowConfigShadowColor("clBlack");
        vBoxlblEndResidencial.setBoxShadowConfigOpacity(75);
        hBoxTitEndRes.addChildren(vBoxlblEndResidencial);
        vBoxlblEndResidencial.applyProperties();
    }

    public TFHBox hBoxlblEndResidencial = new TFHBox();

    private void init_hBoxlblEndResidencial() {
        hBoxlblEndResidencial.setName("hBoxlblEndResidencial");
        hBoxlblEndResidencial.setLeft(0);
        hBoxlblEndResidencial.setTop(0);
        hBoxlblEndResidencial.setWidth(457);
        hBoxlblEndResidencial.setHeight(40);
        hBoxlblEndResidencial.setBorderStyle("stNone");
        hBoxlblEndResidencial.setPaddingTop(2);
        hBoxlblEndResidencial.setPaddingLeft(0);
        hBoxlblEndResidencial.setPaddingRight(0);
        hBoxlblEndResidencial.setPaddingBottom(0);
        hBoxlblEndResidencial.setMarginTop(0);
        hBoxlblEndResidencial.setMarginLeft(0);
        hBoxlblEndResidencial.setMarginRight(0);
        hBoxlblEndResidencial.setMarginBottom(0);
        hBoxlblEndResidencial.setSpacing(0);
        hBoxlblEndResidencial.setFlexVflex("ftTrue");
        hBoxlblEndResidencial.setFlexHflex("ftTrue");
        hBoxlblEndResidencial.setScrollable(false);
        hBoxlblEndResidencial.setBoxShadowConfigHorizontalLength(10);
        hBoxlblEndResidencial.setBoxShadowConfigVerticalLength(10);
        hBoxlblEndResidencial.setBoxShadowConfigBlurRadius(5);
        hBoxlblEndResidencial.setBoxShadowConfigSpreadRadius(0);
        hBoxlblEndResidencial.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblEndResidencial.setBoxShadowConfigOpacity(75);
        hBoxlblEndResidencial.setVAlign("tvTop");
        vBoxlblEndResidencial.addChildren(hBoxlblEndResidencial);
        hBoxlblEndResidencial.applyProperties();
    }

    public TFVBox FVBox6 = new TFVBox();

    private void init_FVBox6() {
        FVBox6.setName("FVBox6");
        FVBox6.setLeft(0);
        FVBox6.setTop(0);
        FVBox6.setWidth(7);
        FVBox6.setHeight(20);
        FVBox6.setBorderStyle("stNone");
        FVBox6.setPaddingTop(0);
        FVBox6.setPaddingLeft(0);
        FVBox6.setPaddingRight(0);
        FVBox6.setPaddingBottom(0);
        FVBox6.setMarginTop(0);
        FVBox6.setMarginLeft(0);
        FVBox6.setMarginRight(0);
        FVBox6.setMarginBottom(0);
        FVBox6.setSpacing(1);
        FVBox6.setFlexVflex("ftTrue");
        FVBox6.setFlexHflex("ftFalse");
        FVBox6.setScrollable(false);
        FVBox6.setBoxShadowConfigHorizontalLength(10);
        FVBox6.setBoxShadowConfigVerticalLength(10);
        FVBox6.setBoxShadowConfigBlurRadius(5);
        FVBox6.setBoxShadowConfigSpreadRadius(0);
        FVBox6.setBoxShadowConfigShadowColor("clBlack");
        FVBox6.setBoxShadowConfigOpacity(75);
        hBoxlblEndResidencial.addChildren(FVBox6);
        FVBox6.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(7);
        FHBox1.setTop(0);
        FHBox1.setWidth(256);
        FHBox1.setHeight(32);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(5);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(0);
        FHBox1.setFlexVflex("ftTrue");
        FHBox1.setFlexHflex("ftMin");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FHBox1.setVAlign("tvTop");
        hBoxlblEndResidencial.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFVBox FVBox44 = new TFVBox();

    private void init_FVBox44() {
        FVBox44.setName("FVBox44");
        FVBox44.setLeft(0);
        FVBox44.setTop(0);
        FVBox44.setWidth(7);
        FVBox44.setHeight(20);
        FVBox44.setBorderStyle("stNone");
        FVBox44.setPaddingTop(0);
        FVBox44.setPaddingLeft(0);
        FVBox44.setPaddingRight(0);
        FVBox44.setPaddingBottom(0);
        FVBox44.setMarginTop(0);
        FVBox44.setMarginLeft(0);
        FVBox44.setMarginRight(0);
        FVBox44.setMarginBottom(0);
        FVBox44.setSpacing(1);
        FVBox44.setFlexVflex("ftTrue");
        FVBox44.setFlexHflex("ftTrue");
        FVBox44.setScrollable(false);
        FVBox44.setBoxShadowConfigHorizontalLength(10);
        FVBox44.setBoxShadowConfigVerticalLength(10);
        FVBox44.setBoxShadowConfigBlurRadius(5);
        FVBox44.setBoxShadowConfigSpreadRadius(0);
        FVBox44.setBoxShadowConfigShadowColor("clBlack");
        FVBox44.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox44);
        FVBox44.applyProperties();
    }

    public TFLabel lblEndResidencial = new TFLabel();

    private void init_lblEndResidencial() {
        lblEndResidencial.setName("lblEndResidencial");
        lblEndResidencial.setLeft(7);
        lblEndResidencial.setTop(0);
        lblEndResidencial.setWidth(78);
        lblEndResidencial.setHeight(19);
        lblEndResidencial.setCaption("Residencial");
        lblEndResidencial.setColor("clBtnFace");
        lblEndResidencial.setFontColor("13392431");
        lblEndResidencial.setFontSize(-16);
        lblEndResidencial.setFontName("Tahoma");
        lblEndResidencial.setFontStyle("[]");
        lblEndResidencial.setVerticalAlignment("taVerticalCenter");
        lblEndResidencial.setWordBreak(false);
        FHBox1.addChildren(lblEndResidencial);
        lblEndResidencial.applyProperties();
    }

    public TFVBox FVBox45 = new TFVBox();

    private void init_FVBox45() {
        FVBox45.setName("FVBox45");
        FVBox45.setLeft(85);
        FVBox45.setTop(0);
        FVBox45.setWidth(30);
        FVBox45.setHeight(20);
        FVBox45.setBorderStyle("stNone");
        FVBox45.setPaddingTop(0);
        FVBox45.setPaddingLeft(0);
        FVBox45.setPaddingRight(0);
        FVBox45.setPaddingBottom(0);
        FVBox45.setMarginTop(0);
        FVBox45.setMarginLeft(0);
        FVBox45.setMarginRight(0);
        FVBox45.setMarginBottom(0);
        FVBox45.setSpacing(1);
        FVBox45.setFlexVflex("ftTrue");
        FVBox45.setFlexHflex("ftFalse");
        FVBox45.setScrollable(false);
        FVBox45.setBoxShadowConfigHorizontalLength(10);
        FVBox45.setBoxShadowConfigVerticalLength(10);
        FVBox45.setBoxShadowConfigBlurRadius(5);
        FVBox45.setBoxShadowConfigSpreadRadius(0);
        FVBox45.setBoxShadowConfigShadowColor("clBlack");
        FVBox45.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox45);
        FVBox45.applyProperties();
    }

    public TFLabel lblCEPResidencial = new TFLabel();

    private void init_lblCEPResidencial() {
        lblCEPResidencial.setName("lblCEPResidencial");
        lblCEPResidencial.setLeft(115);
        lblCEPResidencial.setTop(0);
        lblCEPResidencial.setWidth(28);
        lblCEPResidencial.setHeight(19);
        lblCEPResidencial.setCaption("CEP");
        lblCEPResidencial.setColor("clBtnFace");
        lblCEPResidencial.setFontColor("13392431");
        lblCEPResidencial.setFontSize(-16);
        lblCEPResidencial.setFontName("Tahoma");
        lblCEPResidencial.setFontStyle("[]");
        lblCEPResidencial.setVisible(false);
        lblCEPResidencial.setVerticalAlignment("taVerticalCenter");
        lblCEPResidencial.setWordBreak(false);
        FHBox1.addChildren(lblCEPResidencial);
        lblCEPResidencial.applyProperties();
    }

    public TFVBox FVBox60 = new TFVBox();

    private void init_FVBox60() {
        FVBox60.setName("FVBox60");
        FVBox60.setLeft(143);
        FVBox60.setTop(0);
        FVBox60.setWidth(7);
        FVBox60.setHeight(20);
        FVBox60.setBorderStyle("stNone");
        FVBox60.setPaddingTop(0);
        FVBox60.setPaddingLeft(0);
        FVBox60.setPaddingRight(0);
        FVBox60.setPaddingBottom(0);
        FVBox60.setMarginTop(0);
        FVBox60.setMarginLeft(0);
        FVBox60.setMarginRight(0);
        FVBox60.setMarginBottom(0);
        FVBox60.setSpacing(1);
        FVBox60.setFlexVflex("ftTrue");
        FVBox60.setFlexHflex("ftTrue");
        FVBox60.setScrollable(false);
        FVBox60.setBoxShadowConfigHorizontalLength(10);
        FVBox60.setBoxShadowConfigVerticalLength(10);
        FVBox60.setBoxShadowConfigBlurRadius(5);
        FVBox60.setBoxShadowConfigSpreadRadius(0);
        FVBox60.setBoxShadowConfigShadowColor("clBlack");
        FVBox60.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox60);
        FVBox60.applyProperties();
    }

    public TFVBox FVBox38 = new TFVBox();

    private void init_FVBox38() {
        FVBox38.setName("FVBox38");
        FVBox38.setLeft(263);
        FVBox38.setTop(0);
        FVBox38.setWidth(7);
        FVBox38.setHeight(20);
        FVBox38.setBorderStyle("stNone");
        FVBox38.setPaddingTop(0);
        FVBox38.setPaddingLeft(0);
        FVBox38.setPaddingRight(0);
        FVBox38.setPaddingBottom(0);
        FVBox38.setMarginTop(0);
        FVBox38.setMarginLeft(0);
        FVBox38.setMarginRight(0);
        FVBox38.setMarginBottom(0);
        FVBox38.setSpacing(1);
        FVBox38.setFlexVflex("ftTrue");
        FVBox38.setFlexHflex("ftTrue");
        FVBox38.setScrollable(false);
        FVBox38.setBoxShadowConfigHorizontalLength(10);
        FVBox38.setBoxShadowConfigVerticalLength(10);
        FVBox38.setBoxShadowConfigBlurRadius(5);
        FVBox38.setBoxShadowConfigSpreadRadius(0);
        FVBox38.setBoxShadowConfigShadowColor("clBlack");
        FVBox38.setBoxShadowConfigOpacity(75);
        hBoxlblEndResidencial.addChildren(FVBox38);
        FVBox38.applyProperties();
    }

    public TFVBox vBoxOptEndRes = new TFVBox();

    private void init_vBoxOptEndRes() {
        vBoxOptEndRes.setName("vBoxOptEndRes");
        vBoxOptEndRes.setLeft(270);
        vBoxOptEndRes.setTop(0);
        vBoxOptEndRes.setWidth(165);
        vBoxOptEndRes.setHeight(35);
        vBoxOptEndRes.setBorderStyle("stNone");
        vBoxOptEndRes.setPaddingTop(4);
        vBoxOptEndRes.setPaddingLeft(0);
        vBoxOptEndRes.setPaddingRight(0);
        vBoxOptEndRes.setPaddingBottom(0);
        vBoxOptEndRes.setMarginTop(0);
        vBoxOptEndRes.setMarginLeft(0);
        vBoxOptEndRes.setMarginRight(0);
        vBoxOptEndRes.setMarginBottom(0);
        vBoxOptEndRes.setSpacing(1);
        vBoxOptEndRes.setFlexVflex("ftFalse");
        vBoxOptEndRes.setFlexHflex("ftFalse");
        vBoxOptEndRes.setScrollable(false);
        vBoxOptEndRes.setBoxShadowConfigHorizontalLength(10);
        vBoxOptEndRes.setBoxShadowConfigVerticalLength(10);
        vBoxOptEndRes.setBoxShadowConfigBlurRadius(5);
        vBoxOptEndRes.setBoxShadowConfigSpreadRadius(0);
        vBoxOptEndRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxOptEndRes.setBoxShadowConfigOpacity(75);
        hBoxlblEndResidencial.addChildren(vBoxOptEndRes);
        vBoxOptEndRes.applyProperties();
    }

    public TFCheckBox chkAceitarMudancaRes = new TFCheckBox();

    private void init_chkAceitarMudancaRes() {
        chkAceitarMudancaRes.setName("chkAceitarMudancaRes");
        chkAceitarMudancaRes.setLeft(0);
        chkAceitarMudancaRes.setTop(0);
        chkAceitarMudancaRes.setWidth(143);
        chkAceitarMudancaRes.setHeight(17);
        chkAceitarMudancaRes.setCaption("Aceitar Correios");
        chkAceitarMudancaRes.setFontColor("clWindowText");
        chkAceitarMudancaRes.setFontSize(-13);
        chkAceitarMudancaRes.setFontName("Tahoma");
        chkAceitarMudancaRes.setFontStyle("[]");
        chkAceitarMudancaRes.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkAceitarMudancaResCheck(event);
            processarFlow("FrmCEPClienteOnLine", "chkAceitarMudancaRes", "OnCheck");
        });
        chkAceitarMudancaRes.setVerticalAlignment("taAlignTop");
        vBoxOptEndRes.addChildren(chkAceitarMudancaRes);
        chkAceitarMudancaRes.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(0);
        FHBox2.setTop(18);
        FHBox2.setWidth(147);
        FHBox2.setHeight(30);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setVisible(false);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox2.setVAlign("tvTop");
        vBoxOptEndRes.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFHBox hbCEPOnLineAceitarRes = new TFHBox();

    private void init_hbCEPOnLineAceitarRes() {
        hbCEPOnLineAceitarRes.setName("hbCEPOnLineAceitarRes");
        hbCEPOnLineAceitarRes.setLeft(0);
        hbCEPOnLineAceitarRes.setTop(0);
        hbCEPOnLineAceitarRes.setWidth(62);
        hbCEPOnLineAceitarRes.setHeight(25);
        hbCEPOnLineAceitarRes.setBorderStyle("stNone");
        hbCEPOnLineAceitarRes.setColor("clSilver");
        hbCEPOnLineAceitarRes.setPaddingTop(5);
        hbCEPOnLineAceitarRes.setPaddingLeft(0);
        hbCEPOnLineAceitarRes.setPaddingRight(0);
        hbCEPOnLineAceitarRes.setPaddingBottom(0);
        hbCEPOnLineAceitarRes.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineAceitarResClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineAceitarRes", "OnClick");
        });
        hbCEPOnLineAceitarRes.setMarginTop(0);
        hbCEPOnLineAceitarRes.setMarginLeft(0);
        hbCEPOnLineAceitarRes.setMarginRight(0);
        hbCEPOnLineAceitarRes.setMarginBottom(0);
        hbCEPOnLineAceitarRes.setSpacing(0);
        hbCEPOnLineAceitarRes.setFlexVflex("ftTrue");
        hbCEPOnLineAceitarRes.setFlexHflex("ftTrue");
        hbCEPOnLineAceitarRes.setScrollable(false);
        hbCEPOnLineAceitarRes.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineAceitarRes.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineAceitarRes.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineAceitarRes.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineAceitarRes.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineAceitarRes.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarRes.setVAlign("tvTop");
        FHBox2.addChildren(hbCEPOnLineAceitarRes);
        hbCEPOnLineAceitarRes.applyProperties();
    }

    public TFVBox FVBox4 = new TFVBox();

    private void init_FVBox4() {
        FVBox4.setName("FVBox4");
        FVBox4.setLeft(0);
        FVBox4.setTop(0);
        FVBox4.setWidth(7);
        FVBox4.setHeight(20);
        FVBox4.setBorderStyle("stNone");
        FVBox4.setPaddingTop(0);
        FVBox4.setPaddingLeft(0);
        FVBox4.setPaddingRight(0);
        FVBox4.setPaddingBottom(0);
        FVBox4.setMarginTop(0);
        FVBox4.setMarginLeft(0);
        FVBox4.setMarginRight(0);
        FVBox4.setMarginBottom(0);
        FVBox4.setSpacing(1);
        FVBox4.setFlexVflex("ftTrue");
        FVBox4.setFlexHflex("ftTrue");
        FVBox4.setScrollable(false);
        FVBox4.setBoxShadowConfigHorizontalLength(10);
        FVBox4.setBoxShadowConfigVerticalLength(10);
        FVBox4.setBoxShadowConfigBlurRadius(5);
        FVBox4.setBoxShadowConfigSpreadRadius(0);
        FVBox4.setBoxShadowConfigShadowColor("clBlack");
        FVBox4.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarRes.addChildren(FVBox4);
        FVBox4.applyProperties();
    }

    public TFLabel lblAceitarRes = new TFLabel();

    private void init_lblAceitarRes() {
        lblAceitarRes.setName("lblAceitarRes");
        lblAceitarRes.setLeft(7);
        lblAceitarRes.setTop(0);
        lblAceitarRes.setWidth(38);
        lblAceitarRes.setHeight(14);
        lblAceitarRes.setCaption("Aceitar");
        lblAceitarRes.setFontColor("clWindowText");
        lblAceitarRes.setFontSize(-12);
        lblAceitarRes.setFontName("Tahoma");
        lblAceitarRes.setFontStyle("[]");
        lblAceitarRes.setVerticalAlignment("taVerticalCenter");
        lblAceitarRes.setWordBreak(false);
        hbCEPOnLineAceitarRes.addChildren(lblAceitarRes);
        lblAceitarRes.applyProperties();
    }

    public TFVBox FVBox5 = new TFVBox();

    private void init_FVBox5() {
        FVBox5.setName("FVBox5");
        FVBox5.setLeft(45);
        FVBox5.setTop(0);
        FVBox5.setWidth(7);
        FVBox5.setHeight(19);
        FVBox5.setBorderStyle("stNone");
        FVBox5.setPaddingTop(0);
        FVBox5.setPaddingLeft(0);
        FVBox5.setPaddingRight(0);
        FVBox5.setPaddingBottom(0);
        FVBox5.setMarginTop(0);
        FVBox5.setMarginLeft(0);
        FVBox5.setMarginRight(0);
        FVBox5.setMarginBottom(0);
        FVBox5.setSpacing(1);
        FVBox5.setFlexVflex("ftTrue");
        FVBox5.setFlexHflex("ftTrue");
        FVBox5.setScrollable(false);
        FVBox5.setBoxShadowConfigHorizontalLength(10);
        FVBox5.setBoxShadowConfigVerticalLength(10);
        FVBox5.setBoxShadowConfigBlurRadius(5);
        FVBox5.setBoxShadowConfigSpreadRadius(0);
        FVBox5.setBoxShadowConfigShadowColor("clBlack");
        FVBox5.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarRes.addChildren(FVBox5);
        FVBox5.applyProperties();
    }

    public TFHBox hbCEPOnLineManterRes = new TFHBox();

    private void init_hbCEPOnLineManterRes() {
        hbCEPOnLineManterRes.setName("hbCEPOnLineManterRes");
        hbCEPOnLineManterRes.setLeft(62);
        hbCEPOnLineManterRes.setTop(0);
        hbCEPOnLineManterRes.setWidth(78);
        hbCEPOnLineManterRes.setHeight(25);
        hbCEPOnLineManterRes.setBorderStyle("stNone");
        hbCEPOnLineManterRes.setPaddingTop(5);
        hbCEPOnLineManterRes.setPaddingLeft(0);
        hbCEPOnLineManterRes.setPaddingRight(0);
        hbCEPOnLineManterRes.setPaddingBottom(0);
        hbCEPOnLineManterRes.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineManterResClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineManterRes", "OnClick");
        });
        hbCEPOnLineManterRes.setMarginTop(0);
        hbCEPOnLineManterRes.setMarginLeft(0);
        hbCEPOnLineManterRes.setMarginRight(0);
        hbCEPOnLineManterRes.setMarginBottom(0);
        hbCEPOnLineManterRes.setSpacing(0);
        hbCEPOnLineManterRes.setFlexVflex("ftTrue");
        hbCEPOnLineManterRes.setFlexHflex("ftTrue");
        hbCEPOnLineManterRes.setScrollable(false);
        hbCEPOnLineManterRes.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineManterRes.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineManterRes.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineManterRes.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineManterRes.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineManterRes.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterRes.setVAlign("tvTop");
        FHBox2.addChildren(hbCEPOnLineManterRes);
        hbCEPOnLineManterRes.applyProperties();
    }

    public TFVBox FVBox53 = new TFVBox();

    private void init_FVBox53() {
        FVBox53.setName("FVBox53");
        FVBox53.setLeft(0);
        FVBox53.setTop(0);
        FVBox53.setWidth(7);
        FVBox53.setHeight(20);
        FVBox53.setBorderStyle("stNone");
        FVBox53.setPaddingTop(0);
        FVBox53.setPaddingLeft(0);
        FVBox53.setPaddingRight(0);
        FVBox53.setPaddingBottom(0);
        FVBox53.setMarginTop(0);
        FVBox53.setMarginLeft(0);
        FVBox53.setMarginRight(0);
        FVBox53.setMarginBottom(0);
        FVBox53.setSpacing(1);
        FVBox53.setFlexVflex("ftTrue");
        FVBox53.setFlexHflex("ftTrue");
        FVBox53.setScrollable(false);
        FVBox53.setBoxShadowConfigHorizontalLength(10);
        FVBox53.setBoxShadowConfigVerticalLength(10);
        FVBox53.setBoxShadowConfigBlurRadius(5);
        FVBox53.setBoxShadowConfigSpreadRadius(0);
        FVBox53.setBoxShadowConfigShadowColor("clBlack");
        FVBox53.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterRes.addChildren(FVBox53);
        FVBox53.applyProperties();
    }

    public TFLabel lblManterRes = new TFLabel();

    private void init_lblManterRes() {
        lblManterRes.setName("lblManterRes");
        lblManterRes.setLeft(7);
        lblManterRes.setTop(0);
        lblManterRes.setWidth(38);
        lblManterRes.setHeight(14);
        lblManterRes.setCaption("Manter");
        lblManterRes.setFontColor("clWindowText");
        lblManterRes.setFontSize(-12);
        lblManterRes.setFontName("Tahoma");
        lblManterRes.setFontStyle("[]");
        lblManterRes.setVerticalAlignment("taVerticalCenter");
        lblManterRes.setWordBreak(false);
        hbCEPOnLineManterRes.addChildren(lblManterRes);
        lblManterRes.applyProperties();
    }

    public TFVBox FVBox54 = new TFVBox();

    private void init_FVBox54() {
        FVBox54.setName("FVBox54");
        FVBox54.setLeft(45);
        FVBox54.setTop(0);
        FVBox54.setWidth(7);
        FVBox54.setHeight(20);
        FVBox54.setBorderStyle("stNone");
        FVBox54.setPaddingTop(0);
        FVBox54.setPaddingLeft(0);
        FVBox54.setPaddingRight(0);
        FVBox54.setPaddingBottom(0);
        FVBox54.setMarginTop(0);
        FVBox54.setMarginLeft(0);
        FVBox54.setMarginRight(0);
        FVBox54.setMarginBottom(0);
        FVBox54.setSpacing(1);
        FVBox54.setFlexVflex("ftTrue");
        FVBox54.setFlexHflex("ftTrue");
        FVBox54.setScrollable(false);
        FVBox54.setBoxShadowConfigHorizontalLength(10);
        FVBox54.setBoxShadowConfigVerticalLength(10);
        FVBox54.setBoxShadowConfigBlurRadius(5);
        FVBox54.setBoxShadowConfigSpreadRadius(0);
        FVBox54.setBoxShadowConfigShadowColor("clBlack");
        FVBox54.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterRes.addChildren(FVBox54);
        FVBox54.applyProperties();
    }

    public TFVBox FVBox31 = new TFVBox();

    private void init_FVBox31() {
        FVBox31.setName("FVBox31");
        FVBox31.setLeft(435);
        FVBox31.setTop(0);
        FVBox31.setWidth(10);
        FVBox31.setHeight(35);
        FVBox31.setBorderStyle("stNone");
        FVBox31.setPaddingTop(0);
        FVBox31.setPaddingLeft(0);
        FVBox31.setPaddingRight(0);
        FVBox31.setPaddingBottom(0);
        FVBox31.setMarginTop(0);
        FVBox31.setMarginLeft(0);
        FVBox31.setMarginRight(0);
        FVBox31.setMarginBottom(0);
        FVBox31.setSpacing(1);
        FVBox31.setFlexVflex("ftFalse");
        FVBox31.setFlexHflex("ftFalse");
        FVBox31.setScrollable(false);
        FVBox31.setBoxShadowConfigHorizontalLength(10);
        FVBox31.setBoxShadowConfigVerticalLength(10);
        FVBox31.setBoxShadowConfigBlurRadius(5);
        FVBox31.setBoxShadowConfigSpreadRadius(0);
        FVBox31.setBoxShadowConfigShadowColor("clBlack");
        FVBox31.setBoxShadowConfigOpacity(75);
        hBoxlblEndResidencial.addChildren(FVBox31);
        FVBox31.applyProperties();
    }

    public TFHBox boxEndRes = new TFHBox();

    private void init_boxEndRes() {
        boxEndRes.setName("boxEndRes");
        boxEndRes.setLeft(0);
        boxEndRes.setTop(51);
        boxEndRes.setWidth(769);
        boxEndRes.setHeight(28);
        boxEndRes.setBorderStyle("stNone");
        boxEndRes.setPaddingTop(5);
        boxEndRes.setPaddingLeft(0);
        boxEndRes.setPaddingRight(0);
        boxEndRes.setPaddingBottom(0);
        boxEndRes.setMarginTop(0);
        boxEndRes.setMarginLeft(0);
        boxEndRes.setMarginRight(0);
        boxEndRes.setMarginBottom(0);
        boxEndRes.setSpacing(1);
        boxEndRes.setFlexVflex("ftMin");
        boxEndRes.setFlexHflex("ftTrue");
        boxEndRes.setScrollable(false);
        boxEndRes.setBoxShadowConfigHorizontalLength(10);
        boxEndRes.setBoxShadowConfigVerticalLength(10);
        boxEndRes.setBoxShadowConfigBlurRadius(5);
        boxEndRes.setBoxShadowConfigSpreadRadius(0);
        boxEndRes.setBoxShadowConfigShadowColor("clBlack");
        boxEndRes.setBoxShadowConfigOpacity(75);
        boxEndRes.setVAlign("tvTop");
        vBoxEnderecos.addChildren(boxEndRes);
        boxEndRes.applyProperties();
    }

    public TFVBox vBoxEnderecoRes = new TFVBox();

    private void init_vBoxEnderecoRes() {
        vBoxEnderecoRes.setName("vBoxEnderecoRes");
        vBoxEnderecoRes.setLeft(0);
        vBoxEnderecoRes.setTop(0);
        vBoxEnderecoRes.setWidth(764);
        vBoxEnderecoRes.setHeight(195);
        vBoxEnderecoRes.setBorderStyle("stNone");
        vBoxEnderecoRes.setPaddingTop(0);
        vBoxEnderecoRes.setPaddingLeft(10);
        vBoxEnderecoRes.setPaddingRight(0);
        vBoxEnderecoRes.setPaddingBottom(0);
        vBoxEnderecoRes.setMarginTop(0);
        vBoxEnderecoRes.setMarginLeft(0);
        vBoxEnderecoRes.setMarginRight(0);
        vBoxEnderecoRes.setMarginBottom(0);
        vBoxEnderecoRes.setSpacing(1);
        vBoxEnderecoRes.setFlexVflex("ftMin");
        vBoxEnderecoRes.setFlexHflex("ftTrue");
        vBoxEnderecoRes.setScrollable(false);
        vBoxEnderecoRes.setBoxShadowConfigHorizontalLength(10);
        vBoxEnderecoRes.setBoxShadowConfigVerticalLength(10);
        vBoxEnderecoRes.setBoxShadowConfigBlurRadius(5);
        vBoxEnderecoRes.setBoxShadowConfigSpreadRadius(0);
        vBoxEnderecoRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxEnderecoRes.setBoxShadowConfigOpacity(75);
        boxEndRes.addChildren(vBoxEnderecoRes);
        vBoxEnderecoRes.applyProperties();
    }

    public TFHBox hBoxEndRes = new TFHBox();

    private void init_hBoxEndRes() {
        hBoxEndRes.setName("hBoxEndRes");
        hBoxEndRes.setLeft(0);
        hBoxEndRes.setTop(0);
        hBoxEndRes.setWidth(759);
        hBoxEndRes.setHeight(94);
        hBoxEndRes.setBorderStyle("stNone");
        hBoxEndRes.setPaddingTop(0);
        hBoxEndRes.setPaddingLeft(0);
        hBoxEndRes.setPaddingRight(0);
        hBoxEndRes.setPaddingBottom(0);
        hBoxEndRes.setMarginTop(0);
        hBoxEndRes.setMarginLeft(0);
        hBoxEndRes.setMarginRight(0);
        hBoxEndRes.setMarginBottom(0);
        hBoxEndRes.setSpacing(3);
        hBoxEndRes.setFlexVflex("ftMin");
        hBoxEndRes.setFlexHflex("ftTrue");
        hBoxEndRes.setScrollable(false);
        hBoxEndRes.setBoxShadowConfigHorizontalLength(10);
        hBoxEndRes.setBoxShadowConfigVerticalLength(10);
        hBoxEndRes.setBoxShadowConfigBlurRadius(5);
        hBoxEndRes.setBoxShadowConfigSpreadRadius(0);
        hBoxEndRes.setBoxShadowConfigShadowColor("clBlack");
        hBoxEndRes.setBoxShadowConfigOpacity(75);
        hBoxEndRes.setVAlign("tvTop");
        vBoxEnderecoRes.addChildren(hBoxEndRes);
        hBoxEndRes.applyProperties();
    }

    public TFVBox FVBox58 = new TFVBox();

    private void init_FVBox58() {
        FVBox58.setName("FVBox58");
        FVBox58.setLeft(0);
        FVBox58.setTop(0);
        FVBox58.setWidth(45);
        FVBox58.setHeight(98);
        FVBox58.setBorderStyle("stNone");
        FVBox58.setPaddingTop(0);
        FVBox58.setPaddingLeft(0);
        FVBox58.setPaddingRight(0);
        FVBox58.setPaddingBottom(0);
        FVBox58.setMarginTop(0);
        FVBox58.setMarginLeft(0);
        FVBox58.setMarginRight(0);
        FVBox58.setMarginBottom(0);
        FVBox58.setSpacing(1);
        FVBox58.setFlexVflex("ftTrue");
        FVBox58.setFlexHflex("ftFalse");
        FVBox58.setScrollable(false);
        FVBox58.setBoxShadowConfigHorizontalLength(10);
        FVBox58.setBoxShadowConfigVerticalLength(10);
        FVBox58.setBoxShadowConfigBlurRadius(5);
        FVBox58.setBoxShadowConfigSpreadRadius(0);
        FVBox58.setBoxShadowConfigShadowColor("clBlack");
        FVBox58.setBoxShadowConfigOpacity(75);
        hBoxEndRes.addChildren(FVBox58);
        FVBox58.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(0);
        FLabel1.setTop(0);
        FLabel1.setWidth(25);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Atual");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taVerticalCenter");
        FLabel1.setWordBreak(false);
        FVBox58.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(0);
        FLabel2.setTop(14);
        FLabel2.setWidth(40);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Correios");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taVerticalCenter");
        FLabel2.setWordBreak(false);
        FVBox58.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFHBox FHBox6 = new TFHBox();

    private void init_FHBox6() {
        FHBox6.setName("FHBox6");
        FHBox6.setLeft(45);
        FHBox6.setTop(0);
        FHBox6.setWidth(403);
        FHBox6.setHeight(89);
        FHBox6.setBorderStyle("stNone");
        FHBox6.setPaddingTop(0);
        FHBox6.setPaddingLeft(0);
        FHBox6.setPaddingRight(0);
        FHBox6.setPaddingBottom(0);
        FHBox6.setMarginTop(0);
        FHBox6.setMarginLeft(0);
        FHBox6.setMarginRight(0);
        FHBox6.setMarginBottom(0);
        FHBox6.setSpacing(1);
        FHBox6.setFlexVflex("ftFalse");
        FHBox6.setFlexHflex("ftFalse");
        FHBox6.setScrollable(false);
        FHBox6.setBoxShadowConfigHorizontalLength(10);
        FHBox6.setBoxShadowConfigVerticalLength(10);
        FHBox6.setBoxShadowConfigBlurRadius(5);
        FHBox6.setBoxShadowConfigSpreadRadius(0);
        FHBox6.setBoxShadowConfigShadowColor("clBlack");
        FHBox6.setBoxShadowConfigOpacity(75);
        FHBox6.setVAlign("tvTop");
        hBoxEndRes.addChildren(FHBox6);
        FHBox6.applyProperties();
    }

    public TFVBox FVBox61 = new TFVBox();

    private void init_FVBox61() {
        FVBox61.setName("FVBox61");
        FVBox61.setLeft(0);
        FVBox61.setTop(0);
        FVBox61.setWidth(184);
        FVBox61.setHeight(83);
        FVBox61.setBorderStyle("stNone");
        FVBox61.setPaddingTop(0);
        FVBox61.setPaddingLeft(0);
        FVBox61.setPaddingRight(0);
        FVBox61.setPaddingBottom(0);
        FVBox61.setMarginTop(0);
        FVBox61.setMarginLeft(0);
        FVBox61.setMarginRight(0);
        FVBox61.setMarginBottom(0);
        FVBox61.setSpacing(1);
        FVBox61.setFlexVflex("ftFalse");
        FVBox61.setFlexHflex("ftFalse");
        FVBox61.setScrollable(false);
        FVBox61.setBoxShadowConfigHorizontalLength(10);
        FVBox61.setBoxShadowConfigVerticalLength(10);
        FVBox61.setBoxShadowConfigBlurRadius(5);
        FVBox61.setBoxShadowConfigSpreadRadius(0);
        FVBox61.setBoxShadowConfigShadowColor("clBlack");
        FVBox61.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox61);
        FVBox61.applyProperties();
    }

    public TFVBox vBoxCidadeRes = new TFVBox();

    private void init_vBoxCidadeRes() {
        vBoxCidadeRes.setName("vBoxCidadeRes");
        vBoxCidadeRes.setLeft(0);
        vBoxCidadeRes.setTop(0);
        vBoxCidadeRes.setWidth(136);
        vBoxCidadeRes.setHeight(103);
        vBoxCidadeRes.setBorderStyle("stNone");
        vBoxCidadeRes.setPaddingTop(0);
        vBoxCidadeRes.setPaddingLeft(10);
        vBoxCidadeRes.setPaddingRight(0);
        vBoxCidadeRes.setPaddingBottom(0);
        vBoxCidadeRes.setMarginTop(0);
        vBoxCidadeRes.setMarginLeft(0);
        vBoxCidadeRes.setMarginRight(0);
        vBoxCidadeRes.setMarginBottom(0);
        vBoxCidadeRes.setSpacing(3);
        vBoxCidadeRes.setFlexVflex("ftMin");
        vBoxCidadeRes.setFlexHflex("ftTrue");
        vBoxCidadeRes.setScrollable(false);
        vBoxCidadeRes.setBoxShadowConfigHorizontalLength(10);
        vBoxCidadeRes.setBoxShadowConfigVerticalLength(10);
        vBoxCidadeRes.setBoxShadowConfigBlurRadius(5);
        vBoxCidadeRes.setBoxShadowConfigSpreadRadius(0);
        vBoxCidadeRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxCidadeRes.setBoxShadowConfigOpacity(75);
        FVBox61.addChildren(vBoxCidadeRes);
        vBoxCidadeRes.applyProperties();
    }

    public TFVBox vBoxCepEndRes = new TFVBox();

    private void init_vBoxCepEndRes() {
        vBoxCepEndRes.setName("vBoxCepEndRes");
        vBoxCepEndRes.setLeft(0);
        vBoxCepEndRes.setTop(0);
        vBoxCepEndRes.setWidth(130);
        vBoxCepEndRes.setHeight(43);
        vBoxCepEndRes.setBorderStyle("stNone");
        vBoxCepEndRes.setPaddingTop(0);
        vBoxCepEndRes.setPaddingLeft(0);
        vBoxCepEndRes.setPaddingRight(0);
        vBoxCepEndRes.setPaddingBottom(0);
        vBoxCepEndRes.setMarginTop(0);
        vBoxCepEndRes.setMarginLeft(0);
        vBoxCepEndRes.setMarginRight(0);
        vBoxCepEndRes.setMarginBottom(0);
        vBoxCepEndRes.setSpacing(1);
        vBoxCepEndRes.setFlexVflex("ftFalse");
        vBoxCepEndRes.setFlexHflex("ftTrue");
        vBoxCepEndRes.setScrollable(false);
        vBoxCepEndRes.setBoxShadowConfigHorizontalLength(10);
        vBoxCepEndRes.setBoxShadowConfigVerticalLength(10);
        vBoxCepEndRes.setBoxShadowConfigBlurRadius(5);
        vBoxCepEndRes.setBoxShadowConfigSpreadRadius(0);
        vBoxCepEndRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxCepEndRes.setBoxShadowConfigOpacity(75);
        vBoxCidadeRes.addChildren(vBoxCepEndRes);
        vBoxCepEndRes.applyProperties();
    }

    public TFLabel lblCepResAtual = new TFLabel();

    private void init_lblCepResAtual() {
        lblCepResAtual.setName("lblCepResAtual");
        lblCepResAtual.setLeft(0);
        lblCepResAtual.setTop(0);
        lblCepResAtual.setWidth(120);
        lblCepResAtual.setHeight(13);
        lblCepResAtual.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCepResAtual.setFontColor("clRed");
        lblCepResAtual.setFontSize(-11);
        lblCepResAtual.setFontName("Tahoma");
        lblCepResAtual.setFontStyle("[]");
        lblCepResAtual.setFieldName("CEP_RES");
        lblCepResAtual.setTable(tbClientesEndereco);
        lblCepResAtual.setVerticalAlignment("taVerticalCenter");
        lblCepResAtual.setWordBreak(false);
        vBoxCepEndRes.addChildren(lblCepResAtual);
        lblCepResAtual.applyProperties();
    }

    public TFLabel lblCepResCorreio = new TFLabel();

    private void init_lblCepResCorreio() {
        lblCepResCorreio.setName("lblCepResCorreio");
        lblCepResCorreio.setLeft(0);
        lblCepResCorreio.setTop(14);
        lblCepResCorreio.setWidth(120);
        lblCepResCorreio.setHeight(13);
        lblCepResCorreio.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCepResCorreio.setFontColor("13392431");
        lblCepResCorreio.setFontSize(-11);
        lblCepResCorreio.setFontName("Tahoma");
        lblCepResCorreio.setFontStyle("[]");
        lblCepResCorreio.setFieldName("CEP_RES");
        lblCepResCorreio.setTable(tbClientesEnderecoTemp);
        lblCepResCorreio.setVerticalAlignment("taVerticalCenter");
        lblCepResCorreio.setWordBreak(false);
        vBoxCepEndRes.addChildren(lblCepResCorreio);
        lblCepResCorreio.applyProperties();
    }

    public TFVBox vBoxRuaEndRes = new TFVBox();

    private void init_vBoxRuaEndRes() {
        vBoxRuaEndRes.setName("vBoxRuaEndRes");
        vBoxRuaEndRes.setLeft(0);
        vBoxRuaEndRes.setTop(44);
        vBoxRuaEndRes.setWidth(151);
        vBoxRuaEndRes.setHeight(37);
        vBoxRuaEndRes.setBorderStyle("stNone");
        vBoxRuaEndRes.setPaddingTop(0);
        vBoxRuaEndRes.setPaddingLeft(0);
        vBoxRuaEndRes.setPaddingRight(0);
        vBoxRuaEndRes.setPaddingBottom(0);
        vBoxRuaEndRes.setMarginTop(0);
        vBoxRuaEndRes.setMarginLeft(0);
        vBoxRuaEndRes.setMarginRight(0);
        vBoxRuaEndRes.setMarginBottom(0);
        vBoxRuaEndRes.setSpacing(1);
        vBoxRuaEndRes.setFlexVflex("ftFalse");
        vBoxRuaEndRes.setFlexHflex("ftMin");
        vBoxRuaEndRes.setScrollable(false);
        vBoxRuaEndRes.setBoxShadowConfigHorizontalLength(10);
        vBoxRuaEndRes.setBoxShadowConfigVerticalLength(10);
        vBoxRuaEndRes.setBoxShadowConfigBlurRadius(5);
        vBoxRuaEndRes.setBoxShadowConfigSpreadRadius(0);
        vBoxRuaEndRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxRuaEndRes.setBoxShadowConfigOpacity(75);
        vBoxCidadeRes.addChildren(vBoxRuaEndRes);
        vBoxRuaEndRes.applyProperties();
    }

    public TFLabel lblRuaResCad = new TFLabel();

    private void init_lblRuaResCad() {
        lblRuaResCad.setName("lblRuaResCad");
        lblRuaResCad.setLeft(0);
        lblRuaResCad.setTop(0);
        lblRuaResCad.setWidth(120);
        lblRuaResCad.setHeight(13);
        lblRuaResCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaResCad.setFontColor("clRed");
        lblRuaResCad.setFontSize(-11);
        lblRuaResCad.setFontName("Tahoma");
        lblRuaResCad.setFontStyle("[]");
        lblRuaResCad.setFieldName("RUA_RES");
        lblRuaResCad.setTable(tbClientesEndereco);
        lblRuaResCad.setVerticalAlignment("taVerticalCenter");
        lblRuaResCad.setWordBreak(false);
        vBoxRuaEndRes.addChildren(lblRuaResCad);
        lblRuaResCad.applyProperties();
    }

    public TFLabel lblRuaResCorreios = new TFLabel();

    private void init_lblRuaResCorreios() {
        lblRuaResCorreios.setName("lblRuaResCorreios");
        lblRuaResCorreios.setLeft(0);
        lblRuaResCorreios.setTop(14);
        lblRuaResCorreios.setWidth(120);
        lblRuaResCorreios.setHeight(13);
        lblRuaResCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaResCorreios.setFontColor("13392431");
        lblRuaResCorreios.setFontSize(-11);
        lblRuaResCorreios.setFontName("Tahoma");
        lblRuaResCorreios.setFontStyle("[]");
        lblRuaResCorreios.setFieldName("RUA_RES");
        lblRuaResCorreios.setTable(tbClientesEnderecoTemp);
        lblRuaResCorreios.setVerticalAlignment("taVerticalCenter");
        lblRuaResCorreios.setWordBreak(false);
        vBoxRuaEndRes.addChildren(lblRuaResCorreios);
        lblRuaResCorreios.applyProperties();
    }

    public TFVBox vBoxNumeroRes = new TFVBox();

    private void init_vBoxNumeroRes() {
        vBoxNumeroRes.setName("vBoxNumeroRes");
        vBoxNumeroRes.setLeft(0);
        vBoxNumeroRes.setTop(82);
        vBoxNumeroRes.setWidth(107);
        vBoxNumeroRes.setHeight(78);
        vBoxNumeroRes.setBorderStyle("stNone");
        vBoxNumeroRes.setPaddingTop(0);
        vBoxNumeroRes.setPaddingLeft(0);
        vBoxNumeroRes.setPaddingRight(0);
        vBoxNumeroRes.setPaddingBottom(0);
        vBoxNumeroRes.setVisible(false);
        vBoxNumeroRes.setMarginTop(0);
        vBoxNumeroRes.setMarginLeft(0);
        vBoxNumeroRes.setMarginRight(0);
        vBoxNumeroRes.setMarginBottom(0);
        vBoxNumeroRes.setSpacing(3);
        vBoxNumeroRes.setFlexVflex("ftFalse");
        vBoxNumeroRes.setFlexHflex("ftTrue");
        vBoxNumeroRes.setScrollable(false);
        vBoxNumeroRes.setBoxShadowConfigHorizontalLength(10);
        vBoxNumeroRes.setBoxShadowConfigVerticalLength(10);
        vBoxNumeroRes.setBoxShadowConfigBlurRadius(5);
        vBoxNumeroRes.setBoxShadowConfigSpreadRadius(0);
        vBoxNumeroRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxNumeroRes.setBoxShadowConfigOpacity(75);
        vBoxCidadeRes.addChildren(vBoxNumeroRes);
        vBoxNumeroRes.applyProperties();
    }

    public TFLabel lblNumeroRes = new TFLabel();

    private void init_lblNumeroRes() {
        lblNumeroRes.setName("lblNumeroRes");
        lblNumeroRes.setLeft(0);
        lblNumeroRes.setTop(0);
        lblNumeroRes.setWidth(37);
        lblNumeroRes.setHeight(13);
        lblNumeroRes.setCaption("N\u00FAmero");
        lblNumeroRes.setFontColor("clWindowText");
        lblNumeroRes.setFontSize(-11);
        lblNumeroRes.setFontName("Tahoma");
        lblNumeroRes.setFontStyle("[]");
        lblNumeroRes.setVerticalAlignment("taVerticalCenter");
        lblNumeroRes.setWordBreak(false);
        vBoxNumeroRes.addChildren(lblNumeroRes);
        lblNumeroRes.applyProperties();
    }

    public TFHBox hBoxNumeroResCad = new TFHBox();

    private void init_hBoxNumeroResCad() {
        hBoxNumeroResCad.setName("hBoxNumeroResCad");
        hBoxNumeroResCad.setLeft(0);
        hBoxNumeroResCad.setTop(14);
        hBoxNumeroResCad.setWidth(98);
        hBoxNumeroResCad.setHeight(35);
        hBoxNumeroResCad.setBorderStyle("stNone");
        hBoxNumeroResCad.setPaddingTop(0);
        hBoxNumeroResCad.setPaddingLeft(0);
        hBoxNumeroResCad.setPaddingRight(0);
        hBoxNumeroResCad.setPaddingBottom(0);
        hBoxNumeroResCad.setMarginTop(0);
        hBoxNumeroResCad.setMarginLeft(0);
        hBoxNumeroResCad.setMarginRight(0);
        hBoxNumeroResCad.setMarginBottom(0);
        hBoxNumeroResCad.setSpacing(3);
        hBoxNumeroResCad.setFlexVflex("ftFalse");
        hBoxNumeroResCad.setFlexHflex("ftFalse");
        hBoxNumeroResCad.setScrollable(false);
        hBoxNumeroResCad.setBoxShadowConfigHorizontalLength(10);
        hBoxNumeroResCad.setBoxShadowConfigVerticalLength(10);
        hBoxNumeroResCad.setBoxShadowConfigBlurRadius(5);
        hBoxNumeroResCad.setBoxShadowConfigSpreadRadius(0);
        hBoxNumeroResCad.setBoxShadowConfigShadowColor("clBlack");
        hBoxNumeroResCad.setBoxShadowConfigOpacity(75);
        hBoxNumeroResCad.setVAlign("tvTop");
        vBoxNumeroRes.addChildren(hBoxNumeroResCad);
        hBoxNumeroResCad.applyProperties();
    }

    public TFString edNumeroResCad = new TFString();

    private void init_edNumeroResCad() {
        edNumeroResCad.setName("edNumeroResCad");
        edNumeroResCad.setLeft(0);
        edNumeroResCad.setTop(0);
        edNumeroResCad.setWidth(81);
        edNumeroResCad.setHeight(24);
        edNumeroResCad.setTable(tbClientesEndereco);
        edNumeroResCad.setFieldName("FACHADA_RES");
        edNumeroResCad.setFlex(false);
        edNumeroResCad.setRequired(false);
        edNumeroResCad.setConstraintCheckWhen("cwImmediate");
        edNumeroResCad.setConstraintCheckType("ctExpression");
        edNumeroResCad.setConstraintFocusOnError(false);
        edNumeroResCad.setConstraintEnableUI(true);
        edNumeroResCad.setConstraintEnabled(false);
        edNumeroResCad.setConstraintFormCheck(true);
        edNumeroResCad.setCharCase("ccNormal");
        edNumeroResCad.setPwd(false);
        edNumeroResCad.setMaxlength(5);
        edNumeroResCad.setFontColor("clWindowText");
        edNumeroResCad.setFontSize(-13);
        edNumeroResCad.setFontName("Tahoma");
        edNumeroResCad.setFontStyle("[]");
        edNumeroResCad.setSaveLiteralCharacter(false);
        edNumeroResCad.applyProperties();
        hBoxNumeroResCad.addChildren(edNumeroResCad);
        addValidatable(edNumeroResCad);
    }

    public TFVBox FVBox14 = new TFVBox();

    private void init_FVBox14() {
        FVBox14.setName("FVBox14");
        FVBox14.setLeft(81);
        FVBox14.setTop(0);
        FVBox14.setWidth(12);
        FVBox14.setHeight(30);
        FVBox14.setBorderStyle("stNone");
        FVBox14.setPaddingTop(0);
        FVBox14.setPaddingLeft(0);
        FVBox14.setPaddingRight(0);
        FVBox14.setPaddingBottom(0);
        FVBox14.setMarginTop(0);
        FVBox14.setMarginLeft(0);
        FVBox14.setMarginRight(0);
        FVBox14.setMarginBottom(0);
        FVBox14.setSpacing(1);
        FVBox14.setFlexVflex("ftFalse");
        FVBox14.setFlexHflex("ftFalse");
        FVBox14.setScrollable(false);
        FVBox14.setBoxShadowConfigHorizontalLength(10);
        FVBox14.setBoxShadowConfigVerticalLength(10);
        FVBox14.setBoxShadowConfigBlurRadius(5);
        FVBox14.setBoxShadowConfigSpreadRadius(0);
        FVBox14.setBoxShadowConfigShadowColor("clBlack");
        FVBox14.setBoxShadowConfigOpacity(75);
        hBoxNumeroResCad.addChildren(FVBox14);
        FVBox14.applyProperties();
    }

    public TFLabel lblNumeroResCorreios = new TFLabel();

    private void init_lblNumeroResCorreios() {
        lblNumeroResCorreios.setName("lblNumeroResCorreios");
        lblNumeroResCorreios.setLeft(0);
        lblNumeroResCorreios.setTop(50);
        lblNumeroResCorreios.setWidth(120);
        lblNumeroResCorreios.setHeight(13);
        lblNumeroResCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblNumeroResCorreios.setFontColor("13392431");
        lblNumeroResCorreios.setFontSize(-11);
        lblNumeroResCorreios.setFontName("Tahoma");
        lblNumeroResCorreios.setFontStyle("[]");
        lblNumeroResCorreios.setVisible(false);
        lblNumeroResCorreios.setFieldName("FACHADA_RES");
        lblNumeroResCorreios.setTable(tbClientesEnderecoTemp);
        lblNumeroResCorreios.setVerticalAlignment("taVerticalCenter");
        lblNumeroResCorreios.setWordBreak(false);
        vBoxNumeroRes.addChildren(lblNumeroResCorreios);
        lblNumeroResCorreios.applyProperties();
    }

    public TFVBox FVBox18 = new TFVBox();

    private void init_FVBox18() {
        FVBox18.setName("FVBox18");
        FVBox18.setLeft(0);
        FVBox18.setTop(64);
        FVBox18.setWidth(98);
        FVBox18.setHeight(9);
        FVBox18.setBorderStyle("stNone");
        FVBox18.setPaddingTop(0);
        FVBox18.setPaddingLeft(0);
        FVBox18.setPaddingRight(0);
        FVBox18.setPaddingBottom(0);
        FVBox18.setMarginTop(0);
        FVBox18.setMarginLeft(0);
        FVBox18.setMarginRight(0);
        FVBox18.setMarginBottom(0);
        FVBox18.setSpacing(1);
        FVBox18.setFlexVflex("ftFalse");
        FVBox18.setFlexHflex("ftFalse");
        FVBox18.setScrollable(false);
        FVBox18.setBoxShadowConfigHorizontalLength(10);
        FVBox18.setBoxShadowConfigVerticalLength(10);
        FVBox18.setBoxShadowConfigBlurRadius(5);
        FVBox18.setBoxShadowConfigSpreadRadius(0);
        FVBox18.setBoxShadowConfigShadowColor("clBlack");
        FVBox18.setBoxShadowConfigOpacity(75);
        vBoxNumeroRes.addChildren(FVBox18);
        FVBox18.applyProperties();
    }

    public TFVBox FVBox55 = new TFVBox();

    private void init_FVBox55() {
        FVBox55.setName("FVBox55");
        FVBox55.setLeft(184);
        FVBox55.setTop(0);
        FVBox55.setWidth(214);
        FVBox55.setHeight(84);
        FVBox55.setBorderStyle("stNone");
        FVBox55.setPaddingTop(0);
        FVBox55.setPaddingLeft(10);
        FVBox55.setPaddingRight(0);
        FVBox55.setPaddingBottom(0);
        FVBox55.setMarginTop(0);
        FVBox55.setMarginLeft(0);
        FVBox55.setMarginRight(0);
        FVBox55.setMarginBottom(0);
        FVBox55.setSpacing(1);
        FVBox55.setFlexVflex("ftMin");
        FVBox55.setFlexHflex("ftTrue");
        FVBox55.setScrollable(false);
        FVBox55.setBoxShadowConfigHorizontalLength(10);
        FVBox55.setBoxShadowConfigVerticalLength(10);
        FVBox55.setBoxShadowConfigBlurRadius(5);
        FVBox55.setBoxShadowConfigSpreadRadius(0);
        FVBox55.setBoxShadowConfigShadowColor("clBlack");
        FVBox55.setBoxShadowConfigOpacity(75);
        FHBox6.addChildren(FVBox55);
        FVBox55.applyProperties();
    }

    public TFHBox FHBox10 = new TFHBox();

    private void init_FHBox10() {
        FHBox10.setName("FHBox10");
        FHBox10.setLeft(0);
        FHBox10.setTop(0);
        FHBox10.setWidth(317);
        FHBox10.setHeight(43);
        FHBox10.setBorderStyle("stNone");
        FHBox10.setPaddingTop(0);
        FHBox10.setPaddingLeft(0);
        FHBox10.setPaddingRight(0);
        FHBox10.setPaddingBottom(0);
        FHBox10.setMarginTop(0);
        FHBox10.setMarginLeft(0);
        FHBox10.setMarginRight(0);
        FHBox10.setMarginBottom(0);
        FHBox10.setSpacing(1);
        FHBox10.setFlexVflex("ftFalse");
        FHBox10.setFlexHflex("ftTrue");
        FHBox10.setScrollable(false);
        FHBox10.setBoxShadowConfigHorizontalLength(10);
        FHBox10.setBoxShadowConfigVerticalLength(10);
        FHBox10.setBoxShadowConfigBlurRadius(5);
        FHBox10.setBoxShadowConfigSpreadRadius(0);
        FHBox10.setBoxShadowConfigShadowColor("clBlack");
        FHBox10.setBoxShadowConfigOpacity(75);
        FHBox10.setVAlign("tvTop");
        FVBox55.addChildren(FHBox10);
        FHBox10.applyProperties();
    }

    public TFVBox vBoxUFRuaEndRes = new TFVBox();

    private void init_vBoxUFRuaEndRes() {
        vBoxUFRuaEndRes.setName("vBoxUFRuaEndRes");
        vBoxUFRuaEndRes.setLeft(0);
        vBoxUFRuaEndRes.setTop(0);
        vBoxUFRuaEndRes.setWidth(28);
        vBoxUFRuaEndRes.setHeight(42);
        vBoxUFRuaEndRes.setBorderStyle("stNone");
        vBoxUFRuaEndRes.setPaddingTop(0);
        vBoxUFRuaEndRes.setPaddingLeft(0);
        vBoxUFRuaEndRes.setPaddingRight(0);
        vBoxUFRuaEndRes.setPaddingBottom(0);
        vBoxUFRuaEndRes.setMarginTop(0);
        vBoxUFRuaEndRes.setMarginLeft(0);
        vBoxUFRuaEndRes.setMarginRight(0);
        vBoxUFRuaEndRes.setMarginBottom(0);
        vBoxUFRuaEndRes.setSpacing(3);
        vBoxUFRuaEndRes.setFlexVflex("ftMin");
        vBoxUFRuaEndRes.setFlexHflex("ftFalse");
        vBoxUFRuaEndRes.setScrollable(false);
        vBoxUFRuaEndRes.setBoxShadowConfigHorizontalLength(10);
        vBoxUFRuaEndRes.setBoxShadowConfigVerticalLength(10);
        vBoxUFRuaEndRes.setBoxShadowConfigBlurRadius(5);
        vBoxUFRuaEndRes.setBoxShadowConfigSpreadRadius(0);
        vBoxUFRuaEndRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxUFRuaEndRes.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(vBoxUFRuaEndRes);
        vBoxUFRuaEndRes.applyProperties();
    }

    public TFLabel lblUFResCad = new TFLabel();

    private void init_lblUFResCad() {
        lblUFResCad.setName("lblUFResCad");
        lblUFResCad.setLeft(0);
        lblUFResCad.setTop(0);
        lblUFResCad.setWidth(120);
        lblUFResCad.setHeight(13);
        lblUFResCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFResCad.setFontColor("clRed");
        lblUFResCad.setFontSize(-11);
        lblUFResCad.setFontName("Tahoma");
        lblUFResCad.setFontStyle("[]");
        lblUFResCad.setFieldName("UF_RES");
        lblUFResCad.setTable(tbClientesEndereco);
        lblUFResCad.setVerticalAlignment("taVerticalCenter");
        lblUFResCad.setWordBreak(false);
        vBoxUFRuaEndRes.addChildren(lblUFResCad);
        lblUFResCad.applyProperties();
    }

    public TFLabel lblUFResCorreios = new TFLabel();

    private void init_lblUFResCorreios() {
        lblUFResCorreios.setName("lblUFResCorreios");
        lblUFResCorreios.setLeft(0);
        lblUFResCorreios.setTop(14);
        lblUFResCorreios.setWidth(120);
        lblUFResCorreios.setHeight(13);
        lblUFResCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFResCorreios.setFontColor("13392431");
        lblUFResCorreios.setFontSize(-11);
        lblUFResCorreios.setFontName("Tahoma");
        lblUFResCorreios.setFontStyle("[]");
        lblUFResCorreios.setFieldName("UF_RES");
        lblUFResCorreios.setTable(tbClientesEnderecoTemp);
        lblUFResCorreios.setVerticalAlignment("taVerticalCenter");
        lblUFResCorreios.setWordBreak(false);
        vBoxUFRuaEndRes.addChildren(lblUFResCorreios);
        lblUFResCorreios.applyProperties();
    }

    public TFVBox FVBox25 = new TFVBox();

    private void init_FVBox25() {
        FVBox25.setName("FVBox25");
        FVBox25.setLeft(28);
        FVBox25.setTop(0);
        FVBox25.setWidth(107);
        FVBox25.setHeight(43);
        FVBox25.setBorderStyle("stNone");
        FVBox25.setPaddingTop(0);
        FVBox25.setPaddingLeft(0);
        FVBox25.setPaddingRight(0);
        FVBox25.setPaddingBottom(0);
        FVBox25.setMarginTop(0);
        FVBox25.setMarginLeft(0);
        FVBox25.setMarginRight(0);
        FVBox25.setMarginBottom(0);
        FVBox25.setSpacing(1);
        FVBox25.setFlexVflex("ftFalse");
        FVBox25.setFlexHflex("ftTrue");
        FVBox25.setScrollable(false);
        FVBox25.setBoxShadowConfigHorizontalLength(10);
        FVBox25.setBoxShadowConfigVerticalLength(10);
        FVBox25.setBoxShadowConfigBlurRadius(5);
        FVBox25.setBoxShadowConfigSpreadRadius(0);
        FVBox25.setBoxShadowConfigShadowColor("clBlack");
        FVBox25.setBoxShadowConfigOpacity(75);
        FHBox10.addChildren(FVBox25);
        FVBox25.applyProperties();
    }

    public TFLabel lblCidadeResCad = new TFLabel();

    private void init_lblCidadeResCad() {
        lblCidadeResCad.setName("lblCidadeResCad");
        lblCidadeResCad.setLeft(0);
        lblCidadeResCad.setTop(0);
        lblCidadeResCad.setWidth(120);
        lblCidadeResCad.setHeight(13);
        lblCidadeResCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeResCad.setFontColor("clRed");
        lblCidadeResCad.setFontSize(-11);
        lblCidadeResCad.setFontName("Tahoma");
        lblCidadeResCad.setFontStyle("[]");
        lblCidadeResCad.setFieldName("CIDADE_RES");
        lblCidadeResCad.setTable(tbClientesEndereco);
        lblCidadeResCad.setVerticalAlignment("taVerticalCenter");
        lblCidadeResCad.setWordBreak(false);
        FVBox25.addChildren(lblCidadeResCad);
        lblCidadeResCad.applyProperties();
    }

    public TFLabel lblCidadeResCorreios = new TFLabel();

    private void init_lblCidadeResCorreios() {
        lblCidadeResCorreios.setName("lblCidadeResCorreios");
        lblCidadeResCorreios.setLeft(0);
        lblCidadeResCorreios.setTop(14);
        lblCidadeResCorreios.setWidth(120);
        lblCidadeResCorreios.setHeight(13);
        lblCidadeResCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeResCorreios.setFontColor("13392431");
        lblCidadeResCorreios.setFontSize(-11);
        lblCidadeResCorreios.setFontName("Tahoma");
        lblCidadeResCorreios.setFontStyle("[]");
        lblCidadeResCorreios.setFieldName("CIDADE_RES");
        lblCidadeResCorreios.setTable(tbClientesEnderecoTemp);
        lblCidadeResCorreios.setVerticalAlignment("taVerticalCenter");
        lblCidadeResCorreios.setWordBreak(false);
        FVBox25.addChildren(lblCidadeResCorreios);
        lblCidadeResCorreios.applyProperties();
    }

    public TFVBox vBoxBairroRes = new TFVBox();

    private void init_vBoxBairroRes() {
        vBoxBairroRes.setName("vBoxBairroRes");
        vBoxBairroRes.setLeft(0);
        vBoxBairroRes.setTop(44);
        vBoxBairroRes.setWidth(120);
        vBoxBairroRes.setHeight(37);
        vBoxBairroRes.setBorderStyle("stNone");
        vBoxBairroRes.setPaddingTop(0);
        vBoxBairroRes.setPaddingLeft(2);
        vBoxBairroRes.setPaddingRight(0);
        vBoxBairroRes.setPaddingBottom(0);
        vBoxBairroRes.setMarginTop(0);
        vBoxBairroRes.setMarginLeft(0);
        vBoxBairroRes.setMarginRight(0);
        vBoxBairroRes.setMarginBottom(0);
        vBoxBairroRes.setSpacing(1);
        vBoxBairroRes.setFlexVflex("ftMin");
        vBoxBairroRes.setFlexHflex("ftTrue");
        vBoxBairroRes.setScrollable(false);
        vBoxBairroRes.setBoxShadowConfigHorizontalLength(10);
        vBoxBairroRes.setBoxShadowConfigVerticalLength(10);
        vBoxBairroRes.setBoxShadowConfigBlurRadius(5);
        vBoxBairroRes.setBoxShadowConfigSpreadRadius(0);
        vBoxBairroRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxBairroRes.setBoxShadowConfigOpacity(75);
        FVBox55.addChildren(vBoxBairroRes);
        vBoxBairroRes.applyProperties();
    }

    public TFLabel lblBairroResCad = new TFLabel();

    private void init_lblBairroResCad() {
        lblBairroResCad.setName("lblBairroResCad");
        lblBairroResCad.setLeft(0);
        lblBairroResCad.setTop(0);
        lblBairroResCad.setWidth(120);
        lblBairroResCad.setHeight(13);
        lblBairroResCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroResCad.setFontColor("clRed");
        lblBairroResCad.setFontSize(-11);
        lblBairroResCad.setFontName("Tahoma");
        lblBairroResCad.setFontStyle("[]");
        lblBairroResCad.setFieldName("BAIRRO_RES");
        lblBairroResCad.setTable(tbClientesEndereco);
        lblBairroResCad.setVerticalAlignment("taVerticalCenter");
        lblBairroResCad.setWordBreak(false);
        vBoxBairroRes.addChildren(lblBairroResCad);
        lblBairroResCad.applyProperties();
    }

    public TFLabel lblBairroResCorreios = new TFLabel();

    private void init_lblBairroResCorreios() {
        lblBairroResCorreios.setName("lblBairroResCorreios");
        lblBairroResCorreios.setLeft(0);
        lblBairroResCorreios.setTop(14);
        lblBairroResCorreios.setWidth(120);
        lblBairroResCorreios.setHeight(13);
        lblBairroResCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroResCorreios.setFontColor("13392431");
        lblBairroResCorreios.setFontSize(-11);
        lblBairroResCorreios.setFontName("Tahoma");
        lblBairroResCorreios.setFontStyle("[]");
        lblBairroResCorreios.setFieldName("BAIRRO_RES");
        lblBairroResCorreios.setTable(tbClientesEnderecoTemp);
        lblBairroResCorreios.setVerticalAlignment("taVerticalCenter");
        lblBairroResCorreios.setWordBreak(false);
        vBoxBairroRes.addChildren(lblBairroResCorreios);
        lblBairroResCorreios.applyProperties();
    }

    public TFVBox vBoxComplementoRes = new TFVBox();

    private void init_vBoxComplementoRes() {
        vBoxComplementoRes.setName("vBoxComplementoRes");
        vBoxComplementoRes.setLeft(0);
        vBoxComplementoRes.setTop(95);
        vBoxComplementoRes.setWidth(455);
        vBoxComplementoRes.setHeight(94);
        vBoxComplementoRes.setBorderStyle("stNone");
        vBoxComplementoRes.setPaddingTop(0);
        vBoxComplementoRes.setPaddingLeft(55);
        vBoxComplementoRes.setPaddingRight(0);
        vBoxComplementoRes.setPaddingBottom(0);
        vBoxComplementoRes.setMarginTop(0);
        vBoxComplementoRes.setMarginLeft(0);
        vBoxComplementoRes.setMarginRight(0);
        vBoxComplementoRes.setMarginBottom(0);
        vBoxComplementoRes.setSpacing(3);
        vBoxComplementoRes.setFlexVflex("ftTrue");
        vBoxComplementoRes.setFlexHflex("ftTrue");
        vBoxComplementoRes.setScrollable(false);
        vBoxComplementoRes.setBoxShadowConfigHorizontalLength(10);
        vBoxComplementoRes.setBoxShadowConfigVerticalLength(10);
        vBoxComplementoRes.setBoxShadowConfigBlurRadius(5);
        vBoxComplementoRes.setBoxShadowConfigSpreadRadius(0);
        vBoxComplementoRes.setBoxShadowConfigShadowColor("clBlack");
        vBoxComplementoRes.setBoxShadowConfigOpacity(75);
        vBoxEnderecoRes.addChildren(vBoxComplementoRes);
        vBoxComplementoRes.applyProperties();
    }

    public TFLabel lblComplementoRes = new TFLabel();

    private void init_lblComplementoRes() {
        lblComplementoRes.setName("lblComplementoRes");
        lblComplementoRes.setLeft(0);
        lblComplementoRes.setTop(0);
        lblComplementoRes.setWidth(65);
        lblComplementoRes.setHeight(13);
        lblComplementoRes.setCaption("Complemento");
        lblComplementoRes.setFontColor("clWindowText");
        lblComplementoRes.setFontSize(-11);
        lblComplementoRes.setFontName("Tahoma");
        lblComplementoRes.setFontStyle("[]");
        lblComplementoRes.setVerticalAlignment("taVerticalCenter");
        lblComplementoRes.setWordBreak(false);
        vBoxComplementoRes.addChildren(lblComplementoRes);
        lblComplementoRes.applyProperties();
    }

    public TFHBox hBoxComplementoRes = new TFHBox();

    private void init_hBoxComplementoRes() {
        hBoxComplementoRes.setName("hBoxComplementoRes");
        hBoxComplementoRes.setLeft(0);
        hBoxComplementoRes.setTop(14);
        hBoxComplementoRes.setWidth(159);
        hBoxComplementoRes.setHeight(35);
        hBoxComplementoRes.setBorderStyle("stNone");
        hBoxComplementoRes.setPaddingTop(0);
        hBoxComplementoRes.setPaddingLeft(0);
        hBoxComplementoRes.setPaddingRight(0);
        hBoxComplementoRes.setPaddingBottom(0);
        hBoxComplementoRes.setMarginTop(0);
        hBoxComplementoRes.setMarginLeft(0);
        hBoxComplementoRes.setMarginRight(0);
        hBoxComplementoRes.setMarginBottom(0);
        hBoxComplementoRes.setSpacing(3);
        hBoxComplementoRes.setFlexVflex("ftFalse");
        hBoxComplementoRes.setFlexHflex("ftTrue");
        hBoxComplementoRes.setScrollable(false);
        hBoxComplementoRes.setBoxShadowConfigHorizontalLength(10);
        hBoxComplementoRes.setBoxShadowConfigVerticalLength(10);
        hBoxComplementoRes.setBoxShadowConfigBlurRadius(5);
        hBoxComplementoRes.setBoxShadowConfigSpreadRadius(0);
        hBoxComplementoRes.setBoxShadowConfigShadowColor("clBlack");
        hBoxComplementoRes.setBoxShadowConfigOpacity(75);
        hBoxComplementoRes.setVAlign("tvTop");
        vBoxComplementoRes.addChildren(hBoxComplementoRes);
        hBoxComplementoRes.applyProperties();
    }

    public TFString edComplementoResCad = new TFString();

    private void init_edComplementoResCad() {
        edComplementoResCad.setName("edComplementoResCad");
        edComplementoResCad.setLeft(0);
        edComplementoResCad.setTop(0);
        edComplementoResCad.setWidth(121);
        edComplementoResCad.setHeight(24);
        edComplementoResCad.setTable(tbClientesEndereco);
        edComplementoResCad.setFieldName("COMPLEMENTO_RES");
        edComplementoResCad.setFlex(true);
        edComplementoResCad.setRequired(false);
        edComplementoResCad.setConstraintCheckWhen("cwImmediate");
        edComplementoResCad.setConstraintCheckType("ctExpression");
        edComplementoResCad.setConstraintFocusOnError(false);
        edComplementoResCad.setConstraintEnableUI(true);
        edComplementoResCad.setConstraintEnabled(false);
        edComplementoResCad.setConstraintFormCheck(true);
        edComplementoResCad.setCharCase("ccNormal");
        edComplementoResCad.setPwd(false);
        edComplementoResCad.setMaxlength(30);
        edComplementoResCad.setFontColor("clWindowText");
        edComplementoResCad.setFontSize(-13);
        edComplementoResCad.setFontName("Tahoma");
        edComplementoResCad.setFontStyle("[]");
        edComplementoResCad.setSaveLiteralCharacter(false);
        edComplementoResCad.applyProperties();
        hBoxComplementoRes.addChildren(edComplementoResCad);
        addValidatable(edComplementoResCad);
    }

    public TFVBox FVBox23 = new TFVBox();

    private void init_FVBox23() {
        FVBox23.setName("FVBox23");
        FVBox23.setLeft(121);
        FVBox23.setTop(0);
        FVBox23.setWidth(12);
        FVBox23.setHeight(30);
        FVBox23.setBorderStyle("stNone");
        FVBox23.setPaddingTop(0);
        FVBox23.setPaddingLeft(0);
        FVBox23.setPaddingRight(0);
        FVBox23.setPaddingBottom(0);
        FVBox23.setMarginTop(0);
        FVBox23.setMarginLeft(0);
        FVBox23.setMarginRight(0);
        FVBox23.setMarginBottom(0);
        FVBox23.setSpacing(1);
        FVBox23.setFlexVflex("ftFalse");
        FVBox23.setFlexHflex("ftFalse");
        FVBox23.setScrollable(false);
        FVBox23.setBoxShadowConfigHorizontalLength(10);
        FVBox23.setBoxShadowConfigVerticalLength(10);
        FVBox23.setBoxShadowConfigBlurRadius(5);
        FVBox23.setBoxShadowConfigSpreadRadius(0);
        FVBox23.setBoxShadowConfigShadowColor("clBlack");
        FVBox23.setBoxShadowConfigOpacity(75);
        hBoxComplementoRes.addChildren(FVBox23);
        FVBox23.applyProperties();
    }

    public TFLabel lblComplementoResCorreios = new TFLabel();

    private void init_lblComplementoResCorreios() {
        lblComplementoResCorreios.setName("lblComplementoResCorreios");
        lblComplementoResCorreios.setLeft(0);
        lblComplementoResCorreios.setTop(50);
        lblComplementoResCorreios.setWidth(120);
        lblComplementoResCorreios.setHeight(13);
        lblComplementoResCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblComplementoResCorreios.setFontColor("13392431");
        lblComplementoResCorreios.setFontSize(-11);
        lblComplementoResCorreios.setFontName("Tahoma");
        lblComplementoResCorreios.setFontStyle("[]");
        lblComplementoResCorreios.setFieldName("COMPLEMENTO_RES");
        lblComplementoResCorreios.setTable(tbClientesEnderecoTemp);
        lblComplementoResCorreios.setVerticalAlignment("taVerticalCenter");
        lblComplementoResCorreios.setWordBreak(false);
        vBoxComplementoRes.addChildren(lblComplementoResCorreios);
        lblComplementoResCorreios.applyProperties();
    }

    public TFVBox FVBox24 = new TFVBox();

    private void init_FVBox24() {
        FVBox24.setName("FVBox24");
        FVBox24.setLeft(0);
        FVBox24.setTop(64);
        FVBox24.setWidth(185);
        FVBox24.setHeight(9);
        FVBox24.setBorderStyle("stNone");
        FVBox24.setPaddingTop(0);
        FVBox24.setPaddingLeft(0);
        FVBox24.setPaddingRight(0);
        FVBox24.setPaddingBottom(0);
        FVBox24.setMarginTop(0);
        FVBox24.setMarginLeft(0);
        FVBox24.setMarginRight(0);
        FVBox24.setMarginBottom(0);
        FVBox24.setSpacing(1);
        FVBox24.setFlexVflex("ftFalse");
        FVBox24.setFlexHflex("ftFalse");
        FVBox24.setScrollable(false);
        FVBox24.setBoxShadowConfigHorizontalLength(10);
        FVBox24.setBoxShadowConfigVerticalLength(10);
        FVBox24.setBoxShadowConfigBlurRadius(5);
        FVBox24.setBoxShadowConfigSpreadRadius(0);
        FVBox24.setBoxShadowConfigShadowColor("clBlack");
        FVBox24.setBoxShadowConfigOpacity(75);
        vBoxComplementoRes.addChildren(FVBox24);
        FVBox24.applyProperties();
    }

    public TFHBox hBoxTitEndCom = new TFHBox();

    private void init_hBoxTitEndCom() {
        hBoxTitEndCom.setName("hBoxTitEndCom");
        hBoxTitEndCom.setLeft(0);
        hBoxTitEndCom.setTop(80);
        hBoxTitEndCom.setWidth(503);
        hBoxTitEndCom.setHeight(51);
        hBoxTitEndCom.setBorderStyle("stNone");
        hBoxTitEndCom.setPaddingTop(0);
        hBoxTitEndCom.setPaddingLeft(0);
        hBoxTitEndCom.setPaddingRight(0);
        hBoxTitEndCom.setPaddingBottom(0);
        hBoxTitEndCom.setMarginTop(0);
        hBoxTitEndCom.setMarginLeft(0);
        hBoxTitEndCom.setMarginRight(0);
        hBoxTitEndCom.setMarginBottom(0);
        hBoxTitEndCom.setSpacing(1);
        hBoxTitEndCom.setFlexVflex("ftFalse");
        hBoxTitEndCom.setFlexHflex("ftTrue");
        hBoxTitEndCom.setScrollable(false);
        hBoxTitEndCom.setBoxShadowConfigHorizontalLength(10);
        hBoxTitEndCom.setBoxShadowConfigVerticalLength(10);
        hBoxTitEndCom.setBoxShadowConfigBlurRadius(5);
        hBoxTitEndCom.setBoxShadowConfigSpreadRadius(0);
        hBoxTitEndCom.setBoxShadowConfigShadowColor("clBlack");
        hBoxTitEndCom.setBoxShadowConfigOpacity(75);
        hBoxTitEndCom.setVAlign("tvTop");
        vBoxEnderecos.addChildren(hBoxTitEndCom);
        hBoxTitEndCom.applyProperties();
    }

    public TFVBox vBoxlblEndComercial = new TFVBox();

    private void init_vBoxlblEndComercial() {
        vBoxlblEndComercial.setName("vBoxlblEndComercial");
        vBoxlblEndComercial.setLeft(0);
        vBoxlblEndComercial.setTop(0);
        vBoxlblEndComercial.setWidth(499);
        vBoxlblEndComercial.setHeight(45);
        vBoxlblEndComercial.setBorderStyle("stNone");
        vBoxlblEndComercial.setPaddingTop(10);
        vBoxlblEndComercial.setPaddingLeft(0);
        vBoxlblEndComercial.setPaddingRight(0);
        vBoxlblEndComercial.setPaddingBottom(0);
        vBoxlblEndComercial.setMarginTop(0);
        vBoxlblEndComercial.setMarginLeft(0);
        vBoxlblEndComercial.setMarginRight(0);
        vBoxlblEndComercial.setMarginBottom(0);
        vBoxlblEndComercial.setSpacing(1);
        vBoxlblEndComercial.setFlexVflex("ftFalse");
        vBoxlblEndComercial.setFlexHflex("ftTrue");
        vBoxlblEndComercial.setScrollable(false);
        vBoxlblEndComercial.setBoxShadowConfigHorizontalLength(10);
        vBoxlblEndComercial.setBoxShadowConfigVerticalLength(10);
        vBoxlblEndComercial.setBoxShadowConfigBlurRadius(5);
        vBoxlblEndComercial.setBoxShadowConfigSpreadRadius(0);
        vBoxlblEndComercial.setBoxShadowConfigShadowColor("clBlack");
        vBoxlblEndComercial.setBoxShadowConfigOpacity(75);
        hBoxTitEndCom.addChildren(vBoxlblEndComercial);
        vBoxlblEndComercial.applyProperties();
    }

    public TFHBox hBoxlblEndComercial = new TFHBox();

    private void init_hBoxlblEndComercial() {
        hBoxlblEndComercial.setName("hBoxlblEndComercial");
        hBoxlblEndComercial.setLeft(0);
        hBoxlblEndComercial.setTop(0);
        hBoxlblEndComercial.setWidth(495);
        hBoxlblEndComercial.setHeight(40);
        hBoxlblEndComercial.setBorderStyle("stNone");
        hBoxlblEndComercial.setPaddingTop(3);
        hBoxlblEndComercial.setPaddingLeft(0);
        hBoxlblEndComercial.setPaddingRight(0);
        hBoxlblEndComercial.setPaddingBottom(0);
        hBoxlblEndComercial.setMarginTop(0);
        hBoxlblEndComercial.setMarginLeft(0);
        hBoxlblEndComercial.setMarginRight(0);
        hBoxlblEndComercial.setMarginBottom(0);
        hBoxlblEndComercial.setSpacing(0);
        hBoxlblEndComercial.setFlexVflex("ftTrue");
        hBoxlblEndComercial.setFlexHflex("ftTrue");
        hBoxlblEndComercial.setScrollable(false);
        hBoxlblEndComercial.setBoxShadowConfigHorizontalLength(10);
        hBoxlblEndComercial.setBoxShadowConfigVerticalLength(10);
        hBoxlblEndComercial.setBoxShadowConfigBlurRadius(5);
        hBoxlblEndComercial.setBoxShadowConfigSpreadRadius(0);
        hBoxlblEndComercial.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblEndComercial.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial.setVAlign("tvTop");
        vBoxlblEndComercial.addChildren(hBoxlblEndComercial);
        hBoxlblEndComercial.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(0);
        FVBox2.setTop(0);
        FVBox2.setWidth(7);
        FVBox2.setHeight(20);
        FVBox2.setBorderStyle("stNone");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(1);
        FVBox2.setFlexVflex("ftTrue");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFHBox hBoxlblEndComercial02 = new TFHBox();

    private void init_hBoxlblEndComercial02() {
        hBoxlblEndComercial02.setName("hBoxlblEndComercial02");
        hBoxlblEndComercial02.setLeft(7);
        hBoxlblEndComercial02.setTop(0);
        hBoxlblEndComercial02.setWidth(256);
        hBoxlblEndComercial02.setHeight(32);
        hBoxlblEndComercial02.setBorderStyle("stNone");
        hBoxlblEndComercial02.setPaddingTop(5);
        hBoxlblEndComercial02.setPaddingLeft(0);
        hBoxlblEndComercial02.setPaddingRight(0);
        hBoxlblEndComercial02.setPaddingBottom(0);
        hBoxlblEndComercial02.setMarginTop(0);
        hBoxlblEndComercial02.setMarginLeft(0);
        hBoxlblEndComercial02.setMarginRight(0);
        hBoxlblEndComercial02.setMarginBottom(0);
        hBoxlblEndComercial02.setSpacing(0);
        hBoxlblEndComercial02.setFlexVflex("ftTrue");
        hBoxlblEndComercial02.setFlexHflex("ftMin");
        hBoxlblEndComercial02.setScrollable(false);
        hBoxlblEndComercial02.setBoxShadowConfigHorizontalLength(10);
        hBoxlblEndComercial02.setBoxShadowConfigVerticalLength(10);
        hBoxlblEndComercial02.setBoxShadowConfigBlurRadius(5);
        hBoxlblEndComercial02.setBoxShadowConfigSpreadRadius(0);
        hBoxlblEndComercial02.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblEndComercial02.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial02.setVAlign("tvTop");
        hBoxlblEndComercial.addChildren(hBoxlblEndComercial02);
        hBoxlblEndComercial02.applyProperties();
    }

    public TFVBox FVBox42 = new TFVBox();

    private void init_FVBox42() {
        FVBox42.setName("FVBox42");
        FVBox42.setLeft(0);
        FVBox42.setTop(0);
        FVBox42.setWidth(7);
        FVBox42.setHeight(20);
        FVBox42.setBorderStyle("stNone");
        FVBox42.setPaddingTop(0);
        FVBox42.setPaddingLeft(0);
        FVBox42.setPaddingRight(0);
        FVBox42.setPaddingBottom(0);
        FVBox42.setMarginTop(0);
        FVBox42.setMarginLeft(0);
        FVBox42.setMarginRight(0);
        FVBox42.setMarginBottom(0);
        FVBox42.setSpacing(1);
        FVBox42.setFlexVflex("ftTrue");
        FVBox42.setFlexHflex("ftTrue");
        FVBox42.setScrollable(false);
        FVBox42.setBoxShadowConfigHorizontalLength(10);
        FVBox42.setBoxShadowConfigVerticalLength(10);
        FVBox42.setBoxShadowConfigBlurRadius(5);
        FVBox42.setBoxShadowConfigSpreadRadius(0);
        FVBox42.setBoxShadowConfigShadowColor("clBlack");
        FVBox42.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial02.addChildren(FVBox42);
        FVBox42.applyProperties();
    }

    public TFLabel lblEndComercial = new TFLabel();

    private void init_lblEndComercial() {
        lblEndComercial.setName("lblEndComercial");
        lblEndComercial.setLeft(7);
        lblEndComercial.setTop(0);
        lblEndComercial.setWidth(70);
        lblEndComercial.setHeight(19);
        lblEndComercial.setCaption("Comercial");
        lblEndComercial.setColor("clBtnFace");
        lblEndComercial.setFontColor("13392431");
        lblEndComercial.setFontSize(-16);
        lblEndComercial.setFontName("Tahoma");
        lblEndComercial.setFontStyle("[]");
        lblEndComercial.setVerticalAlignment("taVerticalCenter");
        lblEndComercial.setWordBreak(false);
        hBoxlblEndComercial02.addChildren(lblEndComercial);
        lblEndComercial.applyProperties();
    }

    public TFVBox FVBox43 = new TFVBox();

    private void init_FVBox43() {
        FVBox43.setName("FVBox43");
        FVBox43.setLeft(77);
        FVBox43.setTop(0);
        FVBox43.setWidth(30);
        FVBox43.setHeight(20);
        FVBox43.setBorderStyle("stNone");
        FVBox43.setPaddingTop(0);
        FVBox43.setPaddingLeft(0);
        FVBox43.setPaddingRight(0);
        FVBox43.setPaddingBottom(0);
        FVBox43.setMarginTop(0);
        FVBox43.setMarginLeft(0);
        FVBox43.setMarginRight(0);
        FVBox43.setMarginBottom(0);
        FVBox43.setSpacing(1);
        FVBox43.setFlexVflex("ftTrue");
        FVBox43.setFlexHflex("ftFalse");
        FVBox43.setScrollable(false);
        FVBox43.setBoxShadowConfigHorizontalLength(10);
        FVBox43.setBoxShadowConfigVerticalLength(10);
        FVBox43.setBoxShadowConfigBlurRadius(5);
        FVBox43.setBoxShadowConfigSpreadRadius(0);
        FVBox43.setBoxShadowConfigShadowColor("clBlack");
        FVBox43.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial02.addChildren(FVBox43);
        FVBox43.applyProperties();
    }

    public TFLabel lblCEPCom = new TFLabel();

    private void init_lblCEPCom() {
        lblCEPCom.setName("lblCEPCom");
        lblCEPCom.setLeft(107);
        lblCEPCom.setTop(0);
        lblCEPCom.setWidth(28);
        lblCEPCom.setHeight(19);
        lblCEPCom.setCaption("CEP");
        lblCEPCom.setColor("clBtnFace");
        lblCEPCom.setFontColor("13392431");
        lblCEPCom.setFontSize(-16);
        lblCEPCom.setFontName("Tahoma");
        lblCEPCom.setFontStyle("[]");
        lblCEPCom.setVisible(false);
        lblCEPCom.setVerticalAlignment("taVerticalCenter");
        lblCEPCom.setWordBreak(false);
        hBoxlblEndComercial02.addChildren(lblCEPCom);
        lblCEPCom.applyProperties();
    }

    public TFVBox FVBox69 = new TFVBox();

    private void init_FVBox69() {
        FVBox69.setName("FVBox69");
        FVBox69.setLeft(135);
        FVBox69.setTop(0);
        FVBox69.setWidth(7);
        FVBox69.setHeight(20);
        FVBox69.setBorderStyle("stNone");
        FVBox69.setPaddingTop(0);
        FVBox69.setPaddingLeft(0);
        FVBox69.setPaddingRight(0);
        FVBox69.setPaddingBottom(0);
        FVBox69.setMarginTop(0);
        FVBox69.setMarginLeft(0);
        FVBox69.setMarginRight(0);
        FVBox69.setMarginBottom(0);
        FVBox69.setSpacing(1);
        FVBox69.setFlexVflex("ftTrue");
        FVBox69.setFlexHflex("ftTrue");
        FVBox69.setScrollable(false);
        FVBox69.setBoxShadowConfigHorizontalLength(10);
        FVBox69.setBoxShadowConfigVerticalLength(10);
        FVBox69.setBoxShadowConfigBlurRadius(5);
        FVBox69.setBoxShadowConfigSpreadRadius(0);
        FVBox69.setBoxShadowConfigShadowColor("clBlack");
        FVBox69.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial02.addChildren(FVBox69);
        FVBox69.applyProperties();
    }

    public TFVBox FVBox17 = new TFVBox();

    private void init_FVBox17() {
        FVBox17.setName("FVBox17");
        FVBox17.setLeft(263);
        FVBox17.setTop(0);
        FVBox17.setWidth(7);
        FVBox17.setHeight(20);
        FVBox17.setBorderStyle("stNone");
        FVBox17.setPaddingTop(0);
        FVBox17.setPaddingLeft(0);
        FVBox17.setPaddingRight(0);
        FVBox17.setPaddingBottom(0);
        FVBox17.setMarginTop(0);
        FVBox17.setMarginLeft(0);
        FVBox17.setMarginRight(0);
        FVBox17.setMarginBottom(0);
        FVBox17.setSpacing(1);
        FVBox17.setFlexVflex("ftTrue");
        FVBox17.setFlexHflex("ftTrue");
        FVBox17.setScrollable(false);
        FVBox17.setBoxShadowConfigHorizontalLength(10);
        FVBox17.setBoxShadowConfigVerticalLength(10);
        FVBox17.setBoxShadowConfigBlurRadius(5);
        FVBox17.setBoxShadowConfigSpreadRadius(0);
        FVBox17.setBoxShadowConfigShadowColor("clBlack");
        FVBox17.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial.addChildren(FVBox17);
        FVBox17.applyProperties();
    }

    public TFVBox vBoxOptEndCom = new TFVBox();

    private void init_vBoxOptEndCom() {
        vBoxOptEndCom.setName("vBoxOptEndCom");
        vBoxOptEndCom.setLeft(270);
        vBoxOptEndCom.setTop(0);
        vBoxOptEndCom.setWidth(165);
        vBoxOptEndCom.setHeight(35);
        vBoxOptEndCom.setBorderStyle("stNone");
        vBoxOptEndCom.setPaddingTop(4);
        vBoxOptEndCom.setPaddingLeft(0);
        vBoxOptEndCom.setPaddingRight(0);
        vBoxOptEndCom.setPaddingBottom(0);
        vBoxOptEndCom.setMarginTop(0);
        vBoxOptEndCom.setMarginLeft(0);
        vBoxOptEndCom.setMarginRight(0);
        vBoxOptEndCom.setMarginBottom(0);
        vBoxOptEndCom.setSpacing(1);
        vBoxOptEndCom.setFlexVflex("ftFalse");
        vBoxOptEndCom.setFlexHflex("ftFalse");
        vBoxOptEndCom.setScrollable(false);
        vBoxOptEndCom.setBoxShadowConfigHorizontalLength(10);
        vBoxOptEndCom.setBoxShadowConfigVerticalLength(10);
        vBoxOptEndCom.setBoxShadowConfigBlurRadius(5);
        vBoxOptEndCom.setBoxShadowConfigSpreadRadius(0);
        vBoxOptEndCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxOptEndCom.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial.addChildren(vBoxOptEndCom);
        vBoxOptEndCom.applyProperties();
    }

    public TFCheckBox chkAceitarMudancaCom = new TFCheckBox();

    private void init_chkAceitarMudancaCom() {
        chkAceitarMudancaCom.setName("chkAceitarMudancaCom");
        chkAceitarMudancaCom.setLeft(0);
        chkAceitarMudancaCom.setTop(0);
        chkAceitarMudancaCom.setWidth(138);
        chkAceitarMudancaCom.setHeight(17);
        chkAceitarMudancaCom.setCaption("Aceitar Correios");
        chkAceitarMudancaCom.setFontColor("clWindowText");
        chkAceitarMudancaCom.setFontSize(-13);
        chkAceitarMudancaCom.setFontName("Tahoma");
        chkAceitarMudancaCom.setFontStyle("[]");
        chkAceitarMudancaCom.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkAceitarMudancaComCheck(event);
            processarFlow("FrmCEPClienteOnLine", "chkAceitarMudancaCom", "OnCheck");
        });
        chkAceitarMudancaCom.setVerticalAlignment("taAlignTop");
        vBoxOptEndCom.addChildren(chkAceitarMudancaCom);
        chkAceitarMudancaCom.applyProperties();
    }

    public TFHBox FHBox4 = new TFHBox();

    private void init_FHBox4() {
        FHBox4.setName("FHBox4");
        FHBox4.setLeft(0);
        FHBox4.setTop(18);
        FHBox4.setWidth(147);
        FHBox4.setHeight(30);
        FHBox4.setBorderStyle("stNone");
        FHBox4.setPaddingTop(0);
        FHBox4.setPaddingLeft(0);
        FHBox4.setPaddingRight(0);
        FHBox4.setPaddingBottom(0);
        FHBox4.setVisible(false);
        FHBox4.setMarginTop(0);
        FHBox4.setMarginLeft(0);
        FHBox4.setMarginRight(0);
        FHBox4.setMarginBottom(0);
        FHBox4.setSpacing(1);
        FHBox4.setFlexVflex("ftFalse");
        FHBox4.setFlexHflex("ftTrue");
        FHBox4.setScrollable(false);
        FHBox4.setBoxShadowConfigHorizontalLength(10);
        FHBox4.setBoxShadowConfigVerticalLength(10);
        FHBox4.setBoxShadowConfigBlurRadius(5);
        FHBox4.setBoxShadowConfigSpreadRadius(0);
        FHBox4.setBoxShadowConfigShadowColor("clBlack");
        FHBox4.setBoxShadowConfigOpacity(75);
        FHBox4.setVAlign("tvTop");
        vBoxOptEndCom.addChildren(FHBox4);
        FHBox4.applyProperties();
    }

    public TFHBox hbCEPOnLineAceitarCom = new TFHBox();

    private void init_hbCEPOnLineAceitarCom() {
        hbCEPOnLineAceitarCom.setName("hbCEPOnLineAceitarCom");
        hbCEPOnLineAceitarCom.setLeft(0);
        hbCEPOnLineAceitarCom.setTop(0);
        hbCEPOnLineAceitarCom.setWidth(62);
        hbCEPOnLineAceitarCom.setHeight(25);
        hbCEPOnLineAceitarCom.setBorderStyle("stNone");
        hbCEPOnLineAceitarCom.setColor("clSilver");
        hbCEPOnLineAceitarCom.setPaddingTop(5);
        hbCEPOnLineAceitarCom.setPaddingLeft(0);
        hbCEPOnLineAceitarCom.setPaddingRight(0);
        hbCEPOnLineAceitarCom.setPaddingBottom(0);
        hbCEPOnLineAceitarCom.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineAceitarComClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineAceitarCom", "OnClick");
        });
        hbCEPOnLineAceitarCom.setMarginTop(0);
        hbCEPOnLineAceitarCom.setMarginLeft(0);
        hbCEPOnLineAceitarCom.setMarginRight(0);
        hbCEPOnLineAceitarCom.setMarginBottom(0);
        hbCEPOnLineAceitarCom.setSpacing(0);
        hbCEPOnLineAceitarCom.setFlexVflex("ftTrue");
        hbCEPOnLineAceitarCom.setFlexHflex("ftTrue");
        hbCEPOnLineAceitarCom.setScrollable(false);
        hbCEPOnLineAceitarCom.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineAceitarCom.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineAceitarCom.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineAceitarCom.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineAceitarCom.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineAceitarCom.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarCom.setVAlign("tvTop");
        FHBox4.addChildren(hbCEPOnLineAceitarCom);
        hbCEPOnLineAceitarCom.applyProperties();
    }

    public TFVBox FVBox19 = new TFVBox();

    private void init_FVBox19() {
        FVBox19.setName("FVBox19");
        FVBox19.setLeft(0);
        FVBox19.setTop(0);
        FVBox19.setWidth(7);
        FVBox19.setHeight(20);
        FVBox19.setBorderStyle("stNone");
        FVBox19.setPaddingTop(0);
        FVBox19.setPaddingLeft(0);
        FVBox19.setPaddingRight(0);
        FVBox19.setPaddingBottom(0);
        FVBox19.setMarginTop(0);
        FVBox19.setMarginLeft(0);
        FVBox19.setMarginRight(0);
        FVBox19.setMarginBottom(0);
        FVBox19.setSpacing(1);
        FVBox19.setFlexVflex("ftTrue");
        FVBox19.setFlexHflex("ftTrue");
        FVBox19.setScrollable(false);
        FVBox19.setBoxShadowConfigHorizontalLength(10);
        FVBox19.setBoxShadowConfigVerticalLength(10);
        FVBox19.setBoxShadowConfigBlurRadius(5);
        FVBox19.setBoxShadowConfigSpreadRadius(0);
        FVBox19.setBoxShadowConfigShadowColor("clBlack");
        FVBox19.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarCom.addChildren(FVBox19);
        FVBox19.applyProperties();
    }

    public TFLabel lblAceitarCom = new TFLabel();

    private void init_lblAceitarCom() {
        lblAceitarCom.setName("lblAceitarCom");
        lblAceitarCom.setLeft(7);
        lblAceitarCom.setTop(0);
        lblAceitarCom.setWidth(38);
        lblAceitarCom.setHeight(14);
        lblAceitarCom.setCaption("Aceitar");
        lblAceitarCom.setFontColor("clWindowText");
        lblAceitarCom.setFontSize(-12);
        lblAceitarCom.setFontName("Tahoma");
        lblAceitarCom.setFontStyle("[]");
        lblAceitarCom.setVerticalAlignment("taVerticalCenter");
        lblAceitarCom.setWordBreak(false);
        hbCEPOnLineAceitarCom.addChildren(lblAceitarCom);
        lblAceitarCom.applyProperties();
    }

    public TFVBox FVBox20 = new TFVBox();

    private void init_FVBox20() {
        FVBox20.setName("FVBox20");
        FVBox20.setLeft(45);
        FVBox20.setTop(0);
        FVBox20.setWidth(7);
        FVBox20.setHeight(20);
        FVBox20.setBorderStyle("stNone");
        FVBox20.setPaddingTop(0);
        FVBox20.setPaddingLeft(0);
        FVBox20.setPaddingRight(0);
        FVBox20.setPaddingBottom(0);
        FVBox20.setMarginTop(0);
        FVBox20.setMarginLeft(0);
        FVBox20.setMarginRight(0);
        FVBox20.setMarginBottom(0);
        FVBox20.setSpacing(1);
        FVBox20.setFlexVflex("ftTrue");
        FVBox20.setFlexHflex("ftTrue");
        FVBox20.setScrollable(false);
        FVBox20.setBoxShadowConfigHorizontalLength(10);
        FVBox20.setBoxShadowConfigVerticalLength(10);
        FVBox20.setBoxShadowConfigBlurRadius(5);
        FVBox20.setBoxShadowConfigSpreadRadius(0);
        FVBox20.setBoxShadowConfigShadowColor("clBlack");
        FVBox20.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarCom.addChildren(FVBox20);
        FVBox20.applyProperties();
    }

    public TFHBox hbCEPOnLineManterCom = new TFHBox();

    private void init_hbCEPOnLineManterCom() {
        hbCEPOnLineManterCom.setName("hbCEPOnLineManterCom");
        hbCEPOnLineManterCom.setLeft(62);
        hbCEPOnLineManterCom.setTop(0);
        hbCEPOnLineManterCom.setWidth(78);
        hbCEPOnLineManterCom.setHeight(25);
        hbCEPOnLineManterCom.setBorderStyle("stNone");
        hbCEPOnLineManterCom.setPaddingTop(5);
        hbCEPOnLineManterCom.setPaddingLeft(0);
        hbCEPOnLineManterCom.setPaddingRight(0);
        hbCEPOnLineManterCom.setPaddingBottom(0);
        hbCEPOnLineManterCom.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineManterComClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineManterCom", "OnClick");
        });
        hbCEPOnLineManterCom.setMarginTop(0);
        hbCEPOnLineManterCom.setMarginLeft(0);
        hbCEPOnLineManterCom.setMarginRight(0);
        hbCEPOnLineManterCom.setMarginBottom(0);
        hbCEPOnLineManterCom.setSpacing(0);
        hbCEPOnLineManterCom.setFlexVflex("ftTrue");
        hbCEPOnLineManterCom.setFlexHflex("ftTrue");
        hbCEPOnLineManterCom.setScrollable(false);
        hbCEPOnLineManterCom.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineManterCom.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineManterCom.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineManterCom.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineManterCom.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineManterCom.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterCom.setVAlign("tvTop");
        FHBox4.addChildren(hbCEPOnLineManterCom);
        hbCEPOnLineManterCom.applyProperties();
    }

    public TFVBox FVBox21 = new TFVBox();

    private void init_FVBox21() {
        FVBox21.setName("FVBox21");
        FVBox21.setLeft(0);
        FVBox21.setTop(0);
        FVBox21.setWidth(7);
        FVBox21.setHeight(20);
        FVBox21.setBorderStyle("stNone");
        FVBox21.setPaddingTop(0);
        FVBox21.setPaddingLeft(0);
        FVBox21.setPaddingRight(0);
        FVBox21.setPaddingBottom(0);
        FVBox21.setMarginTop(0);
        FVBox21.setMarginLeft(0);
        FVBox21.setMarginRight(0);
        FVBox21.setMarginBottom(0);
        FVBox21.setSpacing(1);
        FVBox21.setFlexVflex("ftTrue");
        FVBox21.setFlexHflex("ftTrue");
        FVBox21.setScrollable(false);
        FVBox21.setBoxShadowConfigHorizontalLength(10);
        FVBox21.setBoxShadowConfigVerticalLength(10);
        FVBox21.setBoxShadowConfigBlurRadius(5);
        FVBox21.setBoxShadowConfigSpreadRadius(0);
        FVBox21.setBoxShadowConfigShadowColor("clBlack");
        FVBox21.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterCom.addChildren(FVBox21);
        FVBox21.applyProperties();
    }

    public TFLabel lblRecusarCom = new TFLabel();

    private void init_lblRecusarCom() {
        lblRecusarCom.setName("lblRecusarCom");
        lblRecusarCom.setLeft(7);
        lblRecusarCom.setTop(0);
        lblRecusarCom.setWidth(38);
        lblRecusarCom.setHeight(14);
        lblRecusarCom.setCaption("Manter");
        lblRecusarCom.setFontColor("clWindowText");
        lblRecusarCom.setFontSize(-12);
        lblRecusarCom.setFontName("Tahoma");
        lblRecusarCom.setFontStyle("[]");
        lblRecusarCom.setVerticalAlignment("taVerticalCenter");
        lblRecusarCom.setWordBreak(false);
        hbCEPOnLineManterCom.addChildren(lblRecusarCom);
        lblRecusarCom.applyProperties();
    }

    public TFVBox FVBox22 = new TFVBox();

    private void init_FVBox22() {
        FVBox22.setName("FVBox22");
        FVBox22.setLeft(45);
        FVBox22.setTop(0);
        FVBox22.setWidth(7);
        FVBox22.setHeight(20);
        FVBox22.setBorderStyle("stNone");
        FVBox22.setPaddingTop(0);
        FVBox22.setPaddingLeft(0);
        FVBox22.setPaddingRight(0);
        FVBox22.setPaddingBottom(0);
        FVBox22.setMarginTop(0);
        FVBox22.setMarginLeft(0);
        FVBox22.setMarginRight(0);
        FVBox22.setMarginBottom(0);
        FVBox22.setSpacing(1);
        FVBox22.setFlexVflex("ftTrue");
        FVBox22.setFlexHflex("ftTrue");
        FVBox22.setScrollable(false);
        FVBox22.setBoxShadowConfigHorizontalLength(10);
        FVBox22.setBoxShadowConfigVerticalLength(10);
        FVBox22.setBoxShadowConfigBlurRadius(5);
        FVBox22.setBoxShadowConfigSpreadRadius(0);
        FVBox22.setBoxShadowConfigShadowColor("clBlack");
        FVBox22.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterCom.addChildren(FVBox22);
        FVBox22.applyProperties();
    }

    public TFVBox FVBox32 = new TFVBox();

    private void init_FVBox32() {
        FVBox32.setName("FVBox32");
        FVBox32.setLeft(435);
        FVBox32.setTop(0);
        FVBox32.setWidth(10);
        FVBox32.setHeight(35);
        FVBox32.setBorderStyle("stNone");
        FVBox32.setPaddingTop(0);
        FVBox32.setPaddingLeft(0);
        FVBox32.setPaddingRight(0);
        FVBox32.setPaddingBottom(0);
        FVBox32.setMarginTop(0);
        FVBox32.setMarginLeft(0);
        FVBox32.setMarginRight(0);
        FVBox32.setMarginBottom(0);
        FVBox32.setSpacing(1);
        FVBox32.setFlexVflex("ftFalse");
        FVBox32.setFlexHflex("ftFalse");
        FVBox32.setScrollable(false);
        FVBox32.setBoxShadowConfigHorizontalLength(10);
        FVBox32.setBoxShadowConfigVerticalLength(10);
        FVBox32.setBoxShadowConfigBlurRadius(5);
        FVBox32.setBoxShadowConfigSpreadRadius(0);
        FVBox32.setBoxShadowConfigShadowColor("clBlack");
        FVBox32.setBoxShadowConfigOpacity(75);
        hBoxlblEndComercial.addChildren(FVBox32);
        FVBox32.applyProperties();
    }

    public TFHBox boxEndCom = new TFHBox();

    private void init_boxEndCom() {
        boxEndCom.setName("boxEndCom");
        boxEndCom.setLeft(0);
        boxEndCom.setTop(132);
        boxEndCom.setWidth(768);
        boxEndCom.setHeight(200);
        boxEndCom.setBorderStyle("stNone");
        boxEndCom.setPaddingTop(5);
        boxEndCom.setPaddingLeft(0);
        boxEndCom.setPaddingRight(0);
        boxEndCom.setPaddingBottom(0);
        boxEndCom.setMarginTop(0);
        boxEndCom.setMarginLeft(0);
        boxEndCom.setMarginRight(0);
        boxEndCom.setMarginBottom(0);
        boxEndCom.setSpacing(1);
        boxEndCom.setFlexVflex("ftMin");
        boxEndCom.setFlexHflex("ftTrue");
        boxEndCom.setScrollable(false);
        boxEndCom.setBoxShadowConfigHorizontalLength(10);
        boxEndCom.setBoxShadowConfigVerticalLength(10);
        boxEndCom.setBoxShadowConfigBlurRadius(5);
        boxEndCom.setBoxShadowConfigSpreadRadius(0);
        boxEndCom.setBoxShadowConfigShadowColor("clBlack");
        boxEndCom.setBoxShadowConfigOpacity(75);
        boxEndCom.setVAlign("tvTop");
        vBoxEnderecos.addChildren(boxEndCom);
        boxEndCom.applyProperties();
    }

    public TFVBox vBoxEnderecoCom = new TFVBox();

    private void init_vBoxEnderecoCom() {
        vBoxEnderecoCom.setName("vBoxEnderecoCom");
        vBoxEnderecoCom.setLeft(0);
        vBoxEnderecoCom.setTop(0);
        vBoxEnderecoCom.setWidth(763);
        vBoxEnderecoCom.setHeight(195);
        vBoxEnderecoCom.setBorderStyle("stNone");
        vBoxEnderecoCom.setPaddingTop(0);
        vBoxEnderecoCom.setPaddingLeft(10);
        vBoxEnderecoCom.setPaddingRight(0);
        vBoxEnderecoCom.setPaddingBottom(0);
        vBoxEnderecoCom.setMarginTop(0);
        vBoxEnderecoCom.setMarginLeft(0);
        vBoxEnderecoCom.setMarginRight(0);
        vBoxEnderecoCom.setMarginBottom(0);
        vBoxEnderecoCom.setSpacing(1);
        vBoxEnderecoCom.setFlexVflex("ftMin");
        vBoxEnderecoCom.setFlexHflex("ftTrue");
        vBoxEnderecoCom.setScrollable(false);
        vBoxEnderecoCom.setBoxShadowConfigHorizontalLength(10);
        vBoxEnderecoCom.setBoxShadowConfigVerticalLength(10);
        vBoxEnderecoCom.setBoxShadowConfigBlurRadius(5);
        vBoxEnderecoCom.setBoxShadowConfigSpreadRadius(0);
        vBoxEnderecoCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxEnderecoCom.setBoxShadowConfigOpacity(75);
        boxEndCom.addChildren(vBoxEnderecoCom);
        vBoxEnderecoCom.applyProperties();
    }

    public TFHBox hBoxEndCom = new TFHBox();

    private void init_hBoxEndCom() {
        hBoxEndCom.setName("hBoxEndCom");
        hBoxEndCom.setLeft(0);
        hBoxEndCom.setTop(0);
        hBoxEndCom.setWidth(758);
        hBoxEndCom.setHeight(93);
        hBoxEndCom.setBorderStyle("stNone");
        hBoxEndCom.setPaddingTop(0);
        hBoxEndCom.setPaddingLeft(0);
        hBoxEndCom.setPaddingRight(0);
        hBoxEndCom.setPaddingBottom(0);
        hBoxEndCom.setMarginTop(0);
        hBoxEndCom.setMarginLeft(0);
        hBoxEndCom.setMarginRight(0);
        hBoxEndCom.setMarginBottom(0);
        hBoxEndCom.setSpacing(3);
        hBoxEndCom.setFlexVflex("ftMin");
        hBoxEndCom.setFlexHflex("ftTrue");
        hBoxEndCom.setScrollable(false);
        hBoxEndCom.setBoxShadowConfigHorizontalLength(10);
        hBoxEndCom.setBoxShadowConfigVerticalLength(10);
        hBoxEndCom.setBoxShadowConfigBlurRadius(5);
        hBoxEndCom.setBoxShadowConfigSpreadRadius(0);
        hBoxEndCom.setBoxShadowConfigShadowColor("clBlack");
        hBoxEndCom.setBoxShadowConfigOpacity(75);
        hBoxEndCom.setVAlign("tvTop");
        vBoxEnderecoCom.addChildren(hBoxEndCom);
        hBoxEndCom.applyProperties();
    }

    public TFVBox FVBox71 = new TFVBox();

    private void init_FVBox71() {
        FVBox71.setName("FVBox71");
        FVBox71.setLeft(0);
        FVBox71.setTop(0);
        FVBox71.setWidth(45);
        FVBox71.setHeight(98);
        FVBox71.setBorderStyle("stNone");
        FVBox71.setPaddingTop(0);
        FVBox71.setPaddingLeft(0);
        FVBox71.setPaddingRight(0);
        FVBox71.setPaddingBottom(0);
        FVBox71.setMarginTop(0);
        FVBox71.setMarginLeft(0);
        FVBox71.setMarginRight(0);
        FVBox71.setMarginBottom(0);
        FVBox71.setSpacing(1);
        FVBox71.setFlexVflex("ftMin");
        FVBox71.setFlexHflex("ftFalse");
        FVBox71.setScrollable(false);
        FVBox71.setBoxShadowConfigHorizontalLength(10);
        FVBox71.setBoxShadowConfigVerticalLength(10);
        FVBox71.setBoxShadowConfigBlurRadius(5);
        FVBox71.setBoxShadowConfigSpreadRadius(0);
        FVBox71.setBoxShadowConfigShadowColor("clBlack");
        FVBox71.setBoxShadowConfigOpacity(75);
        hBoxEndCom.addChildren(FVBox71);
        FVBox71.applyProperties();
    }

    public TFLabel FLabel5 = new TFLabel();

    private void init_FLabel5() {
        FLabel5.setName("FLabel5");
        FLabel5.setLeft(0);
        FLabel5.setTop(0);
        FLabel5.setWidth(25);
        FLabel5.setHeight(13);
        FLabel5.setCaption("Atual");
        FLabel5.setFontColor("clWindowText");
        FLabel5.setFontSize(-11);
        FLabel5.setFontName("Tahoma");
        FLabel5.setFontStyle("[]");
        FLabel5.setVerticalAlignment("taVerticalCenter");
        FLabel5.setWordBreak(false);
        FVBox71.addChildren(FLabel5);
        FLabel5.applyProperties();
    }

    public TFLabel FLabel7 = new TFLabel();

    private void init_FLabel7() {
        FLabel7.setName("FLabel7");
        FLabel7.setLeft(0);
        FLabel7.setTop(14);
        FLabel7.setWidth(40);
        FLabel7.setHeight(13);
        FLabel7.setCaption("Correios");
        FLabel7.setFontColor("clWindowText");
        FLabel7.setFontSize(-11);
        FLabel7.setFontName("Tahoma");
        FLabel7.setFontStyle("[]");
        FLabel7.setVerticalAlignment("taVerticalCenter");
        FLabel7.setWordBreak(false);
        FVBox71.addChildren(FLabel7);
        FLabel7.applyProperties();
    }

    public TFVBox vBoxCidadeCom = new TFVBox();

    private void init_vBoxCidadeCom() {
        vBoxCidadeCom.setName("vBoxCidadeCom");
        vBoxCidadeCom.setLeft(45);
        vBoxCidadeCom.setTop(0);
        vBoxCidadeCom.setWidth(187);
        vBoxCidadeCom.setHeight(87);
        vBoxCidadeCom.setBorderStyle("stNone");
        vBoxCidadeCom.setPaddingTop(0);
        vBoxCidadeCom.setPaddingLeft(10);
        vBoxCidadeCom.setPaddingRight(0);
        vBoxCidadeCom.setPaddingBottom(0);
        vBoxCidadeCom.setMarginTop(0);
        vBoxCidadeCom.setMarginLeft(0);
        vBoxCidadeCom.setMarginRight(0);
        vBoxCidadeCom.setMarginBottom(0);
        vBoxCidadeCom.setSpacing(3);
        vBoxCidadeCom.setFlexVflex("ftMin");
        vBoxCidadeCom.setFlexHflex("ftFalse");
        vBoxCidadeCom.setScrollable(false);
        vBoxCidadeCom.setBoxShadowConfigHorizontalLength(10);
        vBoxCidadeCom.setBoxShadowConfigVerticalLength(10);
        vBoxCidadeCom.setBoxShadowConfigBlurRadius(5);
        vBoxCidadeCom.setBoxShadowConfigSpreadRadius(0);
        vBoxCidadeCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxCidadeCom.setBoxShadowConfigOpacity(75);
        hBoxEndCom.addChildren(vBoxCidadeCom);
        vBoxCidadeCom.applyProperties();
    }

    public TFVBox vBoxCepEndCom = new TFVBox();

    private void init_vBoxCepEndCom() {
        vBoxCepEndCom.setName("vBoxCepEndCom");
        vBoxCepEndCom.setLeft(0);
        vBoxCepEndCom.setTop(0);
        vBoxCepEndCom.setWidth(151);
        vBoxCepEndCom.setHeight(43);
        vBoxCepEndCom.setBorderStyle("stNone");
        vBoxCepEndCom.setPaddingTop(0);
        vBoxCepEndCom.setPaddingLeft(0);
        vBoxCepEndCom.setPaddingRight(0);
        vBoxCepEndCom.setPaddingBottom(0);
        vBoxCepEndCom.setMarginTop(0);
        vBoxCepEndCom.setMarginLeft(0);
        vBoxCepEndCom.setMarginRight(0);
        vBoxCepEndCom.setMarginBottom(0);
        vBoxCepEndCom.setSpacing(1);
        vBoxCepEndCom.setFlexVflex("ftFalse");
        vBoxCepEndCom.setFlexHflex("ftTrue");
        vBoxCepEndCom.setScrollable(false);
        vBoxCepEndCom.setBoxShadowConfigHorizontalLength(10);
        vBoxCepEndCom.setBoxShadowConfigVerticalLength(10);
        vBoxCepEndCom.setBoxShadowConfigBlurRadius(5);
        vBoxCepEndCom.setBoxShadowConfigSpreadRadius(0);
        vBoxCepEndCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxCepEndCom.setBoxShadowConfigOpacity(75);
        vBoxCidadeCom.addChildren(vBoxCepEndCom);
        vBoxCepEndCom.applyProperties();
    }

    public TFLabel lblCepComAtual = new TFLabel();

    private void init_lblCepComAtual() {
        lblCepComAtual.setName("lblCepComAtual");
        lblCepComAtual.setLeft(0);
        lblCepComAtual.setTop(0);
        lblCepComAtual.setWidth(120);
        lblCepComAtual.setHeight(13);
        lblCepComAtual.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCepComAtual.setFontColor("clRed");
        lblCepComAtual.setFontSize(-11);
        lblCepComAtual.setFontName("Tahoma");
        lblCepComAtual.setFontStyle("[]");
        lblCepComAtual.setFieldName("CEP_COM");
        lblCepComAtual.setTable(tbClientesEndereco);
        lblCepComAtual.setVerticalAlignment("taVerticalCenter");
        lblCepComAtual.setWordBreak(false);
        vBoxCepEndCom.addChildren(lblCepComAtual);
        lblCepComAtual.applyProperties();
    }

    public TFLabel lblCepComCorreio = new TFLabel();

    private void init_lblCepComCorreio() {
        lblCepComCorreio.setName("lblCepComCorreio");
        lblCepComCorreio.setLeft(0);
        lblCepComCorreio.setTop(14);
        lblCepComCorreio.setWidth(120);
        lblCepComCorreio.setHeight(13);
        lblCepComCorreio.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCepComCorreio.setFontColor("13392431");
        lblCepComCorreio.setFontSize(-11);
        lblCepComCorreio.setFontName("Tahoma");
        lblCepComCorreio.setFontStyle("[]");
        lblCepComCorreio.setFieldName("CEP_COM");
        lblCepComCorreio.setTable(tbClientesEnderecoTemp);
        lblCepComCorreio.setVerticalAlignment("taVerticalCenter");
        lblCepComCorreio.setWordBreak(false);
        vBoxCepEndCom.addChildren(lblCepComCorreio);
        lblCepComCorreio.applyProperties();
    }

    public TFVBox vBoxRuaEndCom = new TFVBox();

    private void init_vBoxRuaEndCom() {
        vBoxRuaEndCom.setName("vBoxRuaEndCom");
        vBoxRuaEndCom.setLeft(0);
        vBoxRuaEndCom.setTop(44);
        vBoxRuaEndCom.setWidth(151);
        vBoxRuaEndCom.setHeight(37);
        vBoxRuaEndCom.setBorderStyle("stNone");
        vBoxRuaEndCom.setPaddingTop(0);
        vBoxRuaEndCom.setPaddingLeft(0);
        vBoxRuaEndCom.setPaddingRight(0);
        vBoxRuaEndCom.setPaddingBottom(0);
        vBoxRuaEndCom.setMarginTop(0);
        vBoxRuaEndCom.setMarginLeft(0);
        vBoxRuaEndCom.setMarginRight(0);
        vBoxRuaEndCom.setMarginBottom(0);
        vBoxRuaEndCom.setSpacing(1);
        vBoxRuaEndCom.setFlexVflex("ftFalse");
        vBoxRuaEndCom.setFlexHflex("ftMin");
        vBoxRuaEndCom.setScrollable(false);
        vBoxRuaEndCom.setBoxShadowConfigHorizontalLength(10);
        vBoxRuaEndCom.setBoxShadowConfigVerticalLength(10);
        vBoxRuaEndCom.setBoxShadowConfigBlurRadius(5);
        vBoxRuaEndCom.setBoxShadowConfigSpreadRadius(0);
        vBoxRuaEndCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxRuaEndCom.setBoxShadowConfigOpacity(75);
        vBoxCidadeCom.addChildren(vBoxRuaEndCom);
        vBoxRuaEndCom.applyProperties();
    }

    public TFLabel lblRuaComCad = new TFLabel();

    private void init_lblRuaComCad() {
        lblRuaComCad.setName("lblRuaComCad");
        lblRuaComCad.setLeft(0);
        lblRuaComCad.setTop(0);
        lblRuaComCad.setWidth(120);
        lblRuaComCad.setHeight(13);
        lblRuaComCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaComCad.setFontColor("clRed");
        lblRuaComCad.setFontSize(-11);
        lblRuaComCad.setFontName("Tahoma");
        lblRuaComCad.setFontStyle("[]");
        lblRuaComCad.setFieldName("RUA_COM");
        lblRuaComCad.setTable(tbClientesEndereco);
        lblRuaComCad.setVerticalAlignment("taVerticalCenter");
        lblRuaComCad.setWordBreak(false);
        vBoxRuaEndCom.addChildren(lblRuaComCad);
        lblRuaComCad.applyProperties();
    }

    public TFLabel lblRuaComCorreios = new TFLabel();

    private void init_lblRuaComCorreios() {
        lblRuaComCorreios.setName("lblRuaComCorreios");
        lblRuaComCorreios.setLeft(0);
        lblRuaComCorreios.setTop(14);
        lblRuaComCorreios.setWidth(120);
        lblRuaComCorreios.setHeight(13);
        lblRuaComCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaComCorreios.setFontColor("13392431");
        lblRuaComCorreios.setFontSize(-11);
        lblRuaComCorreios.setFontName("Tahoma");
        lblRuaComCorreios.setFontStyle("[]");
        lblRuaComCorreios.setFieldName("RUA_COM");
        lblRuaComCorreios.setTable(tbClientesEnderecoTemp);
        lblRuaComCorreios.setVerticalAlignment("taVerticalCenter");
        lblRuaComCorreios.setWordBreak(false);
        vBoxRuaEndCom.addChildren(lblRuaComCorreios);
        lblRuaComCorreios.applyProperties();
    }

    public TFVBox vBoxNumeroCom = new TFVBox();

    private void init_vBoxNumeroCom() {
        vBoxNumeroCom.setName("vBoxNumeroCom");
        vBoxNumeroCom.setLeft(0);
        vBoxNumeroCom.setTop(82);
        vBoxNumeroCom.setWidth(107);
        vBoxNumeroCom.setHeight(81);
        vBoxNumeroCom.setBorderStyle("stNone");
        vBoxNumeroCom.setPaddingTop(0);
        vBoxNumeroCom.setPaddingLeft(0);
        vBoxNumeroCom.setPaddingRight(0);
        vBoxNumeroCom.setPaddingBottom(0);
        vBoxNumeroCom.setVisible(false);
        vBoxNumeroCom.setMarginTop(0);
        vBoxNumeroCom.setMarginLeft(0);
        vBoxNumeroCom.setMarginRight(0);
        vBoxNumeroCom.setMarginBottom(0);
        vBoxNumeroCom.setSpacing(3);
        vBoxNumeroCom.setFlexVflex("ftFalse");
        vBoxNumeroCom.setFlexHflex("ftTrue");
        vBoxNumeroCom.setScrollable(false);
        vBoxNumeroCom.setBoxShadowConfigHorizontalLength(10);
        vBoxNumeroCom.setBoxShadowConfigVerticalLength(10);
        vBoxNumeroCom.setBoxShadowConfigBlurRadius(5);
        vBoxNumeroCom.setBoxShadowConfigSpreadRadius(0);
        vBoxNumeroCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxNumeroCom.setBoxShadowConfigOpacity(75);
        vBoxCidadeCom.addChildren(vBoxNumeroCom);
        vBoxNumeroCom.applyProperties();
    }

    public TFLabel lblNumeroCom = new TFLabel();

    private void init_lblNumeroCom() {
        lblNumeroCom.setName("lblNumeroCom");
        lblNumeroCom.setLeft(0);
        lblNumeroCom.setTop(0);
        lblNumeroCom.setWidth(37);
        lblNumeroCom.setHeight(13);
        lblNumeroCom.setCaption("N\u00FAmero");
        lblNumeroCom.setFontColor("clWindowText");
        lblNumeroCom.setFontSize(-11);
        lblNumeroCom.setFontName("Tahoma");
        lblNumeroCom.setFontStyle("[]");
        lblNumeroCom.setVerticalAlignment("taVerticalCenter");
        lblNumeroCom.setWordBreak(false);
        vBoxNumeroCom.addChildren(lblNumeroCom);
        lblNumeroCom.applyProperties();
    }

    public TFHBox hBoxNumeroComCad = new TFHBox();

    private void init_hBoxNumeroComCad() {
        hBoxNumeroComCad.setName("hBoxNumeroComCad");
        hBoxNumeroComCad.setLeft(0);
        hBoxNumeroComCad.setTop(14);
        hBoxNumeroComCad.setWidth(98);
        hBoxNumeroComCad.setHeight(35);
        hBoxNumeroComCad.setBorderStyle("stNone");
        hBoxNumeroComCad.setPaddingTop(0);
        hBoxNumeroComCad.setPaddingLeft(0);
        hBoxNumeroComCad.setPaddingRight(0);
        hBoxNumeroComCad.setPaddingBottom(0);
        hBoxNumeroComCad.setMarginTop(0);
        hBoxNumeroComCad.setMarginLeft(0);
        hBoxNumeroComCad.setMarginRight(0);
        hBoxNumeroComCad.setMarginBottom(0);
        hBoxNumeroComCad.setSpacing(3);
        hBoxNumeroComCad.setFlexVflex("ftFalse");
        hBoxNumeroComCad.setFlexHflex("ftFalse");
        hBoxNumeroComCad.setScrollable(false);
        hBoxNumeroComCad.setBoxShadowConfigHorizontalLength(10);
        hBoxNumeroComCad.setBoxShadowConfigVerticalLength(10);
        hBoxNumeroComCad.setBoxShadowConfigBlurRadius(5);
        hBoxNumeroComCad.setBoxShadowConfigSpreadRadius(0);
        hBoxNumeroComCad.setBoxShadowConfigShadowColor("clBlack");
        hBoxNumeroComCad.setBoxShadowConfigOpacity(75);
        hBoxNumeroComCad.setVAlign("tvTop");
        vBoxNumeroCom.addChildren(hBoxNumeroComCad);
        hBoxNumeroComCad.applyProperties();
    }

    public TFString edNumeroComCad = new TFString();

    private void init_edNumeroComCad() {
        edNumeroComCad.setName("edNumeroComCad");
        edNumeroComCad.setLeft(0);
        edNumeroComCad.setTop(0);
        edNumeroComCad.setWidth(81);
        edNumeroComCad.setHeight(24);
        edNumeroComCad.setTable(tbClientesEndereco);
        edNumeroComCad.setFieldName("FACHADA_COM");
        edNumeroComCad.setFlex(true);
        edNumeroComCad.setRequired(false);
        edNumeroComCad.setConstraintCheckWhen("cwImmediate");
        edNumeroComCad.setConstraintCheckType("ctExpression");
        edNumeroComCad.setConstraintFocusOnError(false);
        edNumeroComCad.setConstraintEnableUI(true);
        edNumeroComCad.setConstraintEnabled(false);
        edNumeroComCad.setConstraintFormCheck(true);
        edNumeroComCad.setCharCase("ccNormal");
        edNumeroComCad.setPwd(false);
        edNumeroComCad.setMaxlength(5);
        edNumeroComCad.setFontColor("clWindowText");
        edNumeroComCad.setFontSize(-13);
        edNumeroComCad.setFontName("Tahoma");
        edNumeroComCad.setFontStyle("[]");
        edNumeroComCad.setSaveLiteralCharacter(false);
        edNumeroComCad.applyProperties();
        hBoxNumeroComCad.addChildren(edNumeroComCad);
        addValidatable(edNumeroComCad);
    }

    public TFVBox FVBox56 = new TFVBox();

    private void init_FVBox56() {
        FVBox56.setName("FVBox56");
        FVBox56.setLeft(81);
        FVBox56.setTop(0);
        FVBox56.setWidth(12);
        FVBox56.setHeight(30);
        FVBox56.setBorderStyle("stNone");
        FVBox56.setPaddingTop(0);
        FVBox56.setPaddingLeft(0);
        FVBox56.setPaddingRight(0);
        FVBox56.setPaddingBottom(0);
        FVBox56.setMarginTop(0);
        FVBox56.setMarginLeft(0);
        FVBox56.setMarginRight(0);
        FVBox56.setMarginBottom(0);
        FVBox56.setSpacing(1);
        FVBox56.setFlexVflex("ftFalse");
        FVBox56.setFlexHflex("ftFalse");
        FVBox56.setScrollable(false);
        FVBox56.setBoxShadowConfigHorizontalLength(10);
        FVBox56.setBoxShadowConfigVerticalLength(10);
        FVBox56.setBoxShadowConfigBlurRadius(5);
        FVBox56.setBoxShadowConfigSpreadRadius(0);
        FVBox56.setBoxShadowConfigShadowColor("clBlack");
        FVBox56.setBoxShadowConfigOpacity(75);
        hBoxNumeroComCad.addChildren(FVBox56);
        FVBox56.applyProperties();
    }

    public TFLabel lblNumeroComCorreios = new TFLabel();

    private void init_lblNumeroComCorreios() {
        lblNumeroComCorreios.setName("lblNumeroComCorreios");
        lblNumeroComCorreios.setLeft(0);
        lblNumeroComCorreios.setTop(50);
        lblNumeroComCorreios.setWidth(120);
        lblNumeroComCorreios.setHeight(13);
        lblNumeroComCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblNumeroComCorreios.setFontColor("13392431");
        lblNumeroComCorreios.setFontSize(-11);
        lblNumeroComCorreios.setFontName("Tahoma");
        lblNumeroComCorreios.setFontStyle("[]");
        lblNumeroComCorreios.setVisible(false);
        lblNumeroComCorreios.setFieldName("FACHADA_COM");
        lblNumeroComCorreios.setTable(tbClientesEnderecoTemp);
        lblNumeroComCorreios.setVerticalAlignment("taVerticalCenter");
        lblNumeroComCorreios.setWordBreak(false);
        vBoxNumeroCom.addChildren(lblNumeroComCorreios);
        lblNumeroComCorreios.applyProperties();
    }

    public TFVBox FVBox26 = new TFVBox();

    private void init_FVBox26() {
        FVBox26.setName("FVBox26");
        FVBox26.setLeft(0);
        FVBox26.setTop(64);
        FVBox26.setWidth(98);
        FVBox26.setHeight(9);
        FVBox26.setBorderStyle("stNone");
        FVBox26.setPaddingTop(0);
        FVBox26.setPaddingLeft(0);
        FVBox26.setPaddingRight(0);
        FVBox26.setPaddingBottom(0);
        FVBox26.setMarginTop(0);
        FVBox26.setMarginLeft(0);
        FVBox26.setMarginRight(0);
        FVBox26.setMarginBottom(0);
        FVBox26.setSpacing(1);
        FVBox26.setFlexVflex("ftFalse");
        FVBox26.setFlexHflex("ftFalse");
        FVBox26.setScrollable(false);
        FVBox26.setBoxShadowConfigHorizontalLength(10);
        FVBox26.setBoxShadowConfigVerticalLength(10);
        FVBox26.setBoxShadowConfigBlurRadius(5);
        FVBox26.setBoxShadowConfigSpreadRadius(0);
        FVBox26.setBoxShadowConfigShadowColor("clBlack");
        FVBox26.setBoxShadowConfigOpacity(75);
        vBoxNumeroCom.addChildren(FVBox26);
        FVBox26.applyProperties();
    }

    public TFVBox FVBox16 = new TFVBox();

    private void init_FVBox16() {
        FVBox16.setName("FVBox16");
        FVBox16.setLeft(232);
        FVBox16.setTop(0);
        FVBox16.setWidth(282);
        FVBox16.setHeight(86);
        FVBox16.setBorderStyle("stNone");
        FVBox16.setPaddingTop(0);
        FVBox16.setPaddingLeft(10);
        FVBox16.setPaddingRight(0);
        FVBox16.setPaddingBottom(0);
        FVBox16.setMarginTop(0);
        FVBox16.setMarginLeft(0);
        FVBox16.setMarginRight(0);
        FVBox16.setMarginBottom(0);
        FVBox16.setSpacing(1);
        FVBox16.setFlexVflex("ftTrue");
        FVBox16.setFlexHflex("ftTrue");
        FVBox16.setScrollable(false);
        FVBox16.setBoxShadowConfigHorizontalLength(10);
        FVBox16.setBoxShadowConfigVerticalLength(10);
        FVBox16.setBoxShadowConfigBlurRadius(5);
        FVBox16.setBoxShadowConfigSpreadRadius(0);
        FVBox16.setBoxShadowConfigShadowColor("clBlack");
        FVBox16.setBoxShadowConfigOpacity(75);
        hBoxEndCom.addChildren(FVBox16);
        FVBox16.applyProperties();
    }

    public TFHBox FHBox8 = new TFHBox();

    private void init_FHBox8() {
        FHBox8.setName("FHBox8");
        FHBox8.setLeft(0);
        FHBox8.setTop(0);
        FHBox8.setWidth(323);
        FHBox8.setHeight(43);
        FHBox8.setBorderStyle("stNone");
        FHBox8.setPaddingTop(0);
        FHBox8.setPaddingLeft(0);
        FHBox8.setPaddingRight(0);
        FHBox8.setPaddingBottom(0);
        FHBox8.setMarginTop(0);
        FHBox8.setMarginLeft(0);
        FHBox8.setMarginRight(0);
        FHBox8.setMarginBottom(0);
        FHBox8.setSpacing(1);
        FHBox8.setFlexVflex("ftFalse");
        FHBox8.setFlexHflex("ftTrue");
        FHBox8.setScrollable(false);
        FHBox8.setBoxShadowConfigHorizontalLength(10);
        FHBox8.setBoxShadowConfigVerticalLength(10);
        FHBox8.setBoxShadowConfigBlurRadius(5);
        FHBox8.setBoxShadowConfigSpreadRadius(0);
        FHBox8.setBoxShadowConfigShadowColor("clBlack");
        FHBox8.setBoxShadowConfigOpacity(75);
        FHBox8.setVAlign("tvTop");
        FVBox16.addChildren(FHBox8);
        FHBox8.applyProperties();
    }

    public TFVBox vBoxUFRuaEndCom = new TFVBox();

    private void init_vBoxUFRuaEndCom() {
        vBoxUFRuaEndCom.setName("vBoxUFRuaEndCom");
        vBoxUFRuaEndCom.setLeft(0);
        vBoxUFRuaEndCom.setTop(0);
        vBoxUFRuaEndCom.setWidth(28);
        vBoxUFRuaEndCom.setHeight(46);
        vBoxUFRuaEndCom.setBorderStyle("stNone");
        vBoxUFRuaEndCom.setPaddingTop(0);
        vBoxUFRuaEndCom.setPaddingLeft(0);
        vBoxUFRuaEndCom.setPaddingRight(0);
        vBoxUFRuaEndCom.setPaddingBottom(0);
        vBoxUFRuaEndCom.setMarginTop(0);
        vBoxUFRuaEndCom.setMarginLeft(0);
        vBoxUFRuaEndCom.setMarginRight(0);
        vBoxUFRuaEndCom.setMarginBottom(0);
        vBoxUFRuaEndCom.setSpacing(3);
        vBoxUFRuaEndCom.setFlexVflex("ftMin");
        vBoxUFRuaEndCom.setFlexHflex("ftFalse");
        vBoxUFRuaEndCom.setScrollable(false);
        vBoxUFRuaEndCom.setBoxShadowConfigHorizontalLength(10);
        vBoxUFRuaEndCom.setBoxShadowConfigVerticalLength(10);
        vBoxUFRuaEndCom.setBoxShadowConfigBlurRadius(5);
        vBoxUFRuaEndCom.setBoxShadowConfigSpreadRadius(0);
        vBoxUFRuaEndCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxUFRuaEndCom.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(vBoxUFRuaEndCom);
        vBoxUFRuaEndCom.applyProperties();
    }

    public TFLabel lblUFComCad = new TFLabel();

    private void init_lblUFComCad() {
        lblUFComCad.setName("lblUFComCad");
        lblUFComCad.setLeft(0);
        lblUFComCad.setTop(0);
        lblUFComCad.setWidth(120);
        lblUFComCad.setHeight(13);
        lblUFComCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFComCad.setFontColor("clRed");
        lblUFComCad.setFontSize(-11);
        lblUFComCad.setFontName("Tahoma");
        lblUFComCad.setFontStyle("[]");
        lblUFComCad.setFieldName("UF_COM");
        lblUFComCad.setTable(tbClientesEndereco);
        lblUFComCad.setVerticalAlignment("taVerticalCenter");
        lblUFComCad.setWordBreak(false);
        vBoxUFRuaEndCom.addChildren(lblUFComCad);
        lblUFComCad.applyProperties();
    }

    public TFLabel lblUFComCorreios = new TFLabel();

    private void init_lblUFComCorreios() {
        lblUFComCorreios.setName("lblUFComCorreios");
        lblUFComCorreios.setLeft(0);
        lblUFComCorreios.setTop(14);
        lblUFComCorreios.setWidth(120);
        lblUFComCorreios.setHeight(13);
        lblUFComCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFComCorreios.setFontColor("13392431");
        lblUFComCorreios.setFontSize(-11);
        lblUFComCorreios.setFontName("Tahoma");
        lblUFComCorreios.setFontStyle("[]");
        lblUFComCorreios.setFieldName("UF_COM");
        lblUFComCorreios.setTable(tbClientesEnderecoTemp);
        lblUFComCorreios.setVerticalAlignment("taVerticalCenter");
        lblUFComCorreios.setWordBreak(false);
        vBoxUFRuaEndCom.addChildren(lblUFComCorreios);
        lblUFComCorreios.applyProperties();
    }

    public TFVBox vBoxDivEnd16 = new TFVBox();

    private void init_vBoxDivEnd16() {
        vBoxDivEnd16.setName("vBoxDivEnd16");
        vBoxDivEnd16.setLeft(28);
        vBoxDivEnd16.setTop(0);
        vBoxDivEnd16.setWidth(107);
        vBoxDivEnd16.setHeight(43);
        vBoxDivEnd16.setBorderStyle("stNone");
        vBoxDivEnd16.setPaddingTop(0);
        vBoxDivEnd16.setPaddingLeft(0);
        vBoxDivEnd16.setPaddingRight(0);
        vBoxDivEnd16.setPaddingBottom(0);
        vBoxDivEnd16.setMarginTop(0);
        vBoxDivEnd16.setMarginLeft(0);
        vBoxDivEnd16.setMarginRight(0);
        vBoxDivEnd16.setMarginBottom(0);
        vBoxDivEnd16.setSpacing(1);
        vBoxDivEnd16.setFlexVflex("ftFalse");
        vBoxDivEnd16.setFlexHflex("ftTrue");
        vBoxDivEnd16.setScrollable(false);
        vBoxDivEnd16.setBoxShadowConfigHorizontalLength(10);
        vBoxDivEnd16.setBoxShadowConfigVerticalLength(10);
        vBoxDivEnd16.setBoxShadowConfigBlurRadius(5);
        vBoxDivEnd16.setBoxShadowConfigSpreadRadius(0);
        vBoxDivEnd16.setBoxShadowConfigShadowColor("clBlack");
        vBoxDivEnd16.setBoxShadowConfigOpacity(75);
        FHBox8.addChildren(vBoxDivEnd16);
        vBoxDivEnd16.applyProperties();
    }

    public TFLabel lblCidadeComCad = new TFLabel();

    private void init_lblCidadeComCad() {
        lblCidadeComCad.setName("lblCidadeComCad");
        lblCidadeComCad.setLeft(0);
        lblCidadeComCad.setTop(0);
        lblCidadeComCad.setWidth(120);
        lblCidadeComCad.setHeight(13);
        lblCidadeComCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeComCad.setFontColor("clRed");
        lblCidadeComCad.setFontSize(-11);
        lblCidadeComCad.setFontName("Tahoma");
        lblCidadeComCad.setFontStyle("[]");
        lblCidadeComCad.setFieldName("CIDADE_COM");
        lblCidadeComCad.setTable(tbClientesEndereco);
        lblCidadeComCad.setVerticalAlignment("taVerticalCenter");
        lblCidadeComCad.setWordBreak(false);
        vBoxDivEnd16.addChildren(lblCidadeComCad);
        lblCidadeComCad.applyProperties();
    }

    public TFLabel lblCidadeComCorreios = new TFLabel();

    private void init_lblCidadeComCorreios() {
        lblCidadeComCorreios.setName("lblCidadeComCorreios");
        lblCidadeComCorreios.setLeft(0);
        lblCidadeComCorreios.setTop(14);
        lblCidadeComCorreios.setWidth(120);
        lblCidadeComCorreios.setHeight(13);
        lblCidadeComCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeComCorreios.setFontColor("13392431");
        lblCidadeComCorreios.setFontSize(-11);
        lblCidadeComCorreios.setFontName("Tahoma");
        lblCidadeComCorreios.setFontStyle("[]");
        lblCidadeComCorreios.setFieldName("CIDADE_COM");
        lblCidadeComCorreios.setTable(tbClientesEnderecoTemp);
        lblCidadeComCorreios.setVerticalAlignment("taVerticalCenter");
        lblCidadeComCorreios.setWordBreak(false);
        vBoxDivEnd16.addChildren(lblCidadeComCorreios);
        lblCidadeComCorreios.applyProperties();
    }

    public TFVBox vBoxBairroCom = new TFVBox();

    private void init_vBoxBairroCom() {
        vBoxBairroCom.setName("vBoxBairroCom");
        vBoxBairroCom.setLeft(0);
        vBoxBairroCom.setTop(44);
        vBoxBairroCom.setWidth(120);
        vBoxBairroCom.setHeight(37);
        vBoxBairroCom.setBorderStyle("stNone");
        vBoxBairroCom.setPaddingTop(0);
        vBoxBairroCom.setPaddingLeft(2);
        vBoxBairroCom.setPaddingRight(0);
        vBoxBairroCom.setPaddingBottom(0);
        vBoxBairroCom.setMarginTop(0);
        vBoxBairroCom.setMarginLeft(0);
        vBoxBairroCom.setMarginRight(0);
        vBoxBairroCom.setMarginBottom(0);
        vBoxBairroCom.setSpacing(1);
        vBoxBairroCom.setFlexVflex("ftMin");
        vBoxBairroCom.setFlexHflex("ftTrue");
        vBoxBairroCom.setScrollable(false);
        vBoxBairroCom.setBoxShadowConfigHorizontalLength(10);
        vBoxBairroCom.setBoxShadowConfigVerticalLength(10);
        vBoxBairroCom.setBoxShadowConfigBlurRadius(5);
        vBoxBairroCom.setBoxShadowConfigSpreadRadius(0);
        vBoxBairroCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxBairroCom.setBoxShadowConfigOpacity(75);
        FVBox16.addChildren(vBoxBairroCom);
        vBoxBairroCom.applyProperties();
    }

    public TFLabel lblBairroComCad = new TFLabel();

    private void init_lblBairroComCad() {
        lblBairroComCad.setName("lblBairroComCad");
        lblBairroComCad.setLeft(0);
        lblBairroComCad.setTop(0);
        lblBairroComCad.setWidth(120);
        lblBairroComCad.setHeight(13);
        lblBairroComCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroComCad.setFontColor("clRed");
        lblBairroComCad.setFontSize(-11);
        lblBairroComCad.setFontName("Tahoma");
        lblBairroComCad.setFontStyle("[]");
        lblBairroComCad.setFieldName("BAIRRO_COM");
        lblBairroComCad.setTable(tbClientesEndereco);
        lblBairroComCad.setVerticalAlignment("taVerticalCenter");
        lblBairroComCad.setWordBreak(false);
        vBoxBairroCom.addChildren(lblBairroComCad);
        lblBairroComCad.applyProperties();
    }

    public TFLabel lblBairroComCorreios = new TFLabel();

    private void init_lblBairroComCorreios() {
        lblBairroComCorreios.setName("lblBairroComCorreios");
        lblBairroComCorreios.setLeft(0);
        lblBairroComCorreios.setTop(14);
        lblBairroComCorreios.setWidth(120);
        lblBairroComCorreios.setHeight(13);
        lblBairroComCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroComCorreios.setFontColor("13392431");
        lblBairroComCorreios.setFontSize(-11);
        lblBairroComCorreios.setFontName("Tahoma");
        lblBairroComCorreios.setFontStyle("[]");
        lblBairroComCorreios.setFieldName("BAIRRO_COM");
        lblBairroComCorreios.setTable(tbClientesEnderecoTemp);
        lblBairroComCorreios.setVerticalAlignment("taVerticalCenter");
        lblBairroComCorreios.setWordBreak(false);
        vBoxBairroCom.addChildren(lblBairroComCorreios);
        lblBairroComCorreios.applyProperties();
    }

    public TFVBox vBoxComplementoCom = new TFVBox();

    private void init_vBoxComplementoCom() {
        vBoxComplementoCom.setName("vBoxComplementoCom");
        vBoxComplementoCom.setLeft(0);
        vBoxComplementoCom.setTop(94);
        vBoxComplementoCom.setWidth(456);
        vBoxComplementoCom.setHeight(77);
        vBoxComplementoCom.setBorderStyle("stNone");
        vBoxComplementoCom.setPaddingTop(0);
        vBoxComplementoCom.setPaddingLeft(55);
        vBoxComplementoCom.setPaddingRight(0);
        vBoxComplementoCom.setPaddingBottom(0);
        vBoxComplementoCom.setMarginTop(0);
        vBoxComplementoCom.setMarginLeft(0);
        vBoxComplementoCom.setMarginRight(0);
        vBoxComplementoCom.setMarginBottom(0);
        vBoxComplementoCom.setSpacing(3);
        vBoxComplementoCom.setFlexVflex("ftMin");
        vBoxComplementoCom.setFlexHflex("ftTrue");
        vBoxComplementoCom.setScrollable(false);
        vBoxComplementoCom.setBoxShadowConfigHorizontalLength(10);
        vBoxComplementoCom.setBoxShadowConfigVerticalLength(10);
        vBoxComplementoCom.setBoxShadowConfigBlurRadius(5);
        vBoxComplementoCom.setBoxShadowConfigSpreadRadius(0);
        vBoxComplementoCom.setBoxShadowConfigShadowColor("clBlack");
        vBoxComplementoCom.setBoxShadowConfigOpacity(75);
        vBoxEnderecoCom.addChildren(vBoxComplementoCom);
        vBoxComplementoCom.applyProperties();
    }

    public TFLabel lblComplementoCom = new TFLabel();

    private void init_lblComplementoCom() {
        lblComplementoCom.setName("lblComplementoCom");
        lblComplementoCom.setLeft(0);
        lblComplementoCom.setTop(0);
        lblComplementoCom.setWidth(65);
        lblComplementoCom.setHeight(13);
        lblComplementoCom.setCaption("Complemento");
        lblComplementoCom.setFontColor("clWindowText");
        lblComplementoCom.setFontSize(-11);
        lblComplementoCom.setFontName("Tahoma");
        lblComplementoCom.setFontStyle("[]");
        lblComplementoCom.setVerticalAlignment("taVerticalCenter");
        lblComplementoCom.setWordBreak(false);
        vBoxComplementoCom.addChildren(lblComplementoCom);
        lblComplementoCom.applyProperties();
    }

    public TFHBox hBoxComplementoCom = new TFHBox();

    private void init_hBoxComplementoCom() {
        hBoxComplementoCom.setName("hBoxComplementoCom");
        hBoxComplementoCom.setLeft(0);
        hBoxComplementoCom.setTop(14);
        hBoxComplementoCom.setWidth(159);
        hBoxComplementoCom.setHeight(35);
        hBoxComplementoCom.setBorderStyle("stNone");
        hBoxComplementoCom.setPaddingTop(0);
        hBoxComplementoCom.setPaddingLeft(0);
        hBoxComplementoCom.setPaddingRight(0);
        hBoxComplementoCom.setPaddingBottom(0);
        hBoxComplementoCom.setMarginTop(0);
        hBoxComplementoCom.setMarginLeft(0);
        hBoxComplementoCom.setMarginRight(0);
        hBoxComplementoCom.setMarginBottom(0);
        hBoxComplementoCom.setSpacing(3);
        hBoxComplementoCom.setFlexVflex("ftFalse");
        hBoxComplementoCom.setFlexHflex("ftTrue");
        hBoxComplementoCom.setScrollable(false);
        hBoxComplementoCom.setBoxShadowConfigHorizontalLength(10);
        hBoxComplementoCom.setBoxShadowConfigVerticalLength(10);
        hBoxComplementoCom.setBoxShadowConfigBlurRadius(5);
        hBoxComplementoCom.setBoxShadowConfigSpreadRadius(0);
        hBoxComplementoCom.setBoxShadowConfigShadowColor("clBlack");
        hBoxComplementoCom.setBoxShadowConfigOpacity(75);
        hBoxComplementoCom.setVAlign("tvTop");
        vBoxComplementoCom.addChildren(hBoxComplementoCom);
        hBoxComplementoCom.applyProperties();
    }

    public TFString edComplementoComCad = new TFString();

    private void init_edComplementoComCad() {
        edComplementoComCad.setName("edComplementoComCad");
        edComplementoComCad.setLeft(0);
        edComplementoComCad.setTop(0);
        edComplementoComCad.setWidth(121);
        edComplementoComCad.setHeight(21);
        edComplementoComCad.setTable(tbClientesEndereco);
        edComplementoComCad.setFieldName("COMPLEMENTO_COM");
        edComplementoComCad.setFlex(true);
        edComplementoComCad.setRequired(false);
        edComplementoComCad.setConstraintCheckWhen("cwImmediate");
        edComplementoComCad.setConstraintCheckType("ctExpression");
        edComplementoComCad.setConstraintFocusOnError(false);
        edComplementoComCad.setConstraintEnableUI(true);
        edComplementoComCad.setConstraintEnabled(false);
        edComplementoComCad.setConstraintFormCheck(true);
        edComplementoComCad.setCharCase("ccNormal");
        edComplementoComCad.setPwd(false);
        edComplementoComCad.setMaxlength(30);
        edComplementoComCad.setFontColor("clWindowText");
        edComplementoComCad.setFontSize(-11);
        edComplementoComCad.setFontName("Tahoma");
        edComplementoComCad.setFontStyle("[]");
        edComplementoComCad.setSaveLiteralCharacter(false);
        edComplementoComCad.applyProperties();
        hBoxComplementoCom.addChildren(edComplementoComCad);
        addValidatable(edComplementoComCad);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(121);
        FVBox3.setTop(0);
        FVBox3.setWidth(12);
        FVBox3.setHeight(30);
        FVBox3.setBorderStyle("stNone");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(1);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        hBoxComplementoCom.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel lblComplementoComCorreios = new TFLabel();

    private void init_lblComplementoComCorreios() {
        lblComplementoComCorreios.setName("lblComplementoComCorreios");
        lblComplementoComCorreios.setLeft(0);
        lblComplementoComCorreios.setTop(50);
        lblComplementoComCorreios.setWidth(120);
        lblComplementoComCorreios.setHeight(13);
        lblComplementoComCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblComplementoComCorreios.setFontColor("13392431");
        lblComplementoComCorreios.setFontSize(-11);
        lblComplementoComCorreios.setFontName("Tahoma");
        lblComplementoComCorreios.setFontStyle("[]");
        lblComplementoComCorreios.setFieldName("COMPLEMENTO_COM");
        lblComplementoComCorreios.setTable(tbClientesEnderecoTemp);
        lblComplementoComCorreios.setVerticalAlignment("taVerticalCenter");
        lblComplementoComCorreios.setWordBreak(false);
        vBoxComplementoCom.addChildren(lblComplementoComCorreios);
        lblComplementoComCorreios.applyProperties();
    }

    public TFVBox FVBox27 = new TFVBox();

    private void init_FVBox27() {
        FVBox27.setName("FVBox27");
        FVBox27.setLeft(0);
        FVBox27.setTop(64);
        FVBox27.setWidth(185);
        FVBox27.setHeight(9);
        FVBox27.setBorderStyle("stNone");
        FVBox27.setPaddingTop(0);
        FVBox27.setPaddingLeft(0);
        FVBox27.setPaddingRight(0);
        FVBox27.setPaddingBottom(0);
        FVBox27.setMarginTop(0);
        FVBox27.setMarginLeft(0);
        FVBox27.setMarginRight(0);
        FVBox27.setMarginBottom(0);
        FVBox27.setSpacing(1);
        FVBox27.setFlexVflex("ftFalse");
        FVBox27.setFlexHflex("ftFalse");
        FVBox27.setScrollable(false);
        FVBox27.setBoxShadowConfigHorizontalLength(10);
        FVBox27.setBoxShadowConfigVerticalLength(10);
        FVBox27.setBoxShadowConfigBlurRadius(5);
        FVBox27.setBoxShadowConfigSpreadRadius(0);
        FVBox27.setBoxShadowConfigShadowColor("clBlack");
        FVBox27.setBoxShadowConfigOpacity(75);
        vBoxComplementoCom.addChildren(FVBox27);
        FVBox27.applyProperties();
    }

    public TFHBox hBoxTitEndCob = new TFHBox();

    private void init_hBoxTitEndCob() {
        hBoxTitEndCob.setName("hBoxTitEndCob");
        hBoxTitEndCob.setLeft(0);
        hBoxTitEndCob.setTop(333);
        hBoxTitEndCob.setWidth(503);
        hBoxTitEndCob.setHeight(51);
        hBoxTitEndCob.setBorderStyle("stNone");
        hBoxTitEndCob.setPaddingTop(0);
        hBoxTitEndCob.setPaddingLeft(0);
        hBoxTitEndCob.setPaddingRight(0);
        hBoxTitEndCob.setPaddingBottom(0);
        hBoxTitEndCob.setMarginTop(0);
        hBoxTitEndCob.setMarginLeft(0);
        hBoxTitEndCob.setMarginRight(0);
        hBoxTitEndCob.setMarginBottom(0);
        hBoxTitEndCob.setSpacing(1);
        hBoxTitEndCob.setFlexVflex("ftFalse");
        hBoxTitEndCob.setFlexHflex("ftTrue");
        hBoxTitEndCob.setScrollable(false);
        hBoxTitEndCob.setBoxShadowConfigHorizontalLength(10);
        hBoxTitEndCob.setBoxShadowConfigVerticalLength(10);
        hBoxTitEndCob.setBoxShadowConfigBlurRadius(5);
        hBoxTitEndCob.setBoxShadowConfigSpreadRadius(0);
        hBoxTitEndCob.setBoxShadowConfigShadowColor("clBlack");
        hBoxTitEndCob.setBoxShadowConfigOpacity(75);
        hBoxTitEndCob.setVAlign("tvTop");
        vBoxEnderecos.addChildren(hBoxTitEndCob);
        hBoxTitEndCob.applyProperties();
    }

    public TFVBox vBoxlblEndCob = new TFVBox();

    private void init_vBoxlblEndCob() {
        vBoxlblEndCob.setName("vBoxlblEndCob");
        vBoxlblEndCob.setLeft(0);
        vBoxlblEndCob.setTop(0);
        vBoxlblEndCob.setWidth(499);
        vBoxlblEndCob.setHeight(45);
        vBoxlblEndCob.setBorderStyle("stNone");
        vBoxlblEndCob.setPaddingTop(10);
        vBoxlblEndCob.setPaddingLeft(0);
        vBoxlblEndCob.setPaddingRight(0);
        vBoxlblEndCob.setPaddingBottom(0);
        vBoxlblEndCob.setMarginTop(0);
        vBoxlblEndCob.setMarginLeft(0);
        vBoxlblEndCob.setMarginRight(0);
        vBoxlblEndCob.setMarginBottom(0);
        vBoxlblEndCob.setSpacing(1);
        vBoxlblEndCob.setFlexVflex("ftFalse");
        vBoxlblEndCob.setFlexHflex("ftTrue");
        vBoxlblEndCob.setScrollable(false);
        vBoxlblEndCob.setBoxShadowConfigHorizontalLength(10);
        vBoxlblEndCob.setBoxShadowConfigVerticalLength(10);
        vBoxlblEndCob.setBoxShadowConfigBlurRadius(5);
        vBoxlblEndCob.setBoxShadowConfigSpreadRadius(0);
        vBoxlblEndCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxlblEndCob.setBoxShadowConfigOpacity(75);
        hBoxTitEndCob.addChildren(vBoxlblEndCob);
        vBoxlblEndCob.applyProperties();
    }

    public TFHBox hBoxlblEndCob = new TFHBox();

    private void init_hBoxlblEndCob() {
        hBoxlblEndCob.setName("hBoxlblEndCob");
        hBoxlblEndCob.setLeft(0);
        hBoxlblEndCob.setTop(0);
        hBoxlblEndCob.setWidth(495);
        hBoxlblEndCob.setHeight(40);
        hBoxlblEndCob.setBorderStyle("stNone");
        hBoxlblEndCob.setPaddingTop(3);
        hBoxlblEndCob.setPaddingLeft(0);
        hBoxlblEndCob.setPaddingRight(0);
        hBoxlblEndCob.setPaddingBottom(0);
        hBoxlblEndCob.setMarginTop(0);
        hBoxlblEndCob.setMarginLeft(0);
        hBoxlblEndCob.setMarginRight(0);
        hBoxlblEndCob.setMarginBottom(0);
        hBoxlblEndCob.setSpacing(0);
        hBoxlblEndCob.setFlexVflex("ftTrue");
        hBoxlblEndCob.setFlexHflex("ftTrue");
        hBoxlblEndCob.setScrollable(false);
        hBoxlblEndCob.setBoxShadowConfigHorizontalLength(10);
        hBoxlblEndCob.setBoxShadowConfigVerticalLength(10);
        hBoxlblEndCob.setBoxShadowConfigBlurRadius(5);
        hBoxlblEndCob.setBoxShadowConfigSpreadRadius(0);
        hBoxlblEndCob.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblEndCob.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob.setVAlign("tvTop");
        vBoxlblEndCob.addChildren(hBoxlblEndCob);
        hBoxlblEndCob.applyProperties();
    }

    public TFVBox FVBox8 = new TFVBox();

    private void init_FVBox8() {
        FVBox8.setName("FVBox8");
        FVBox8.setLeft(0);
        FVBox8.setTop(0);
        FVBox8.setWidth(7);
        FVBox8.setHeight(20);
        FVBox8.setBorderStyle("stNone");
        FVBox8.setPaddingTop(0);
        FVBox8.setPaddingLeft(0);
        FVBox8.setPaddingRight(0);
        FVBox8.setPaddingBottom(0);
        FVBox8.setMarginTop(0);
        FVBox8.setMarginLeft(0);
        FVBox8.setMarginRight(0);
        FVBox8.setMarginBottom(0);
        FVBox8.setSpacing(1);
        FVBox8.setFlexVflex("ftFalse");
        FVBox8.setFlexHflex("ftFalse");
        FVBox8.setScrollable(false);
        FVBox8.setBoxShadowConfigHorizontalLength(10);
        FVBox8.setBoxShadowConfigVerticalLength(10);
        FVBox8.setBoxShadowConfigBlurRadius(5);
        FVBox8.setBoxShadowConfigSpreadRadius(0);
        FVBox8.setBoxShadowConfigShadowColor("clBlack");
        FVBox8.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob.addChildren(FVBox8);
        FVBox8.applyProperties();
    }

    public TFHBox hBoxlblEndCob02 = new TFHBox();

    private void init_hBoxlblEndCob02() {
        hBoxlblEndCob02.setName("hBoxlblEndCob02");
        hBoxlblEndCob02.setLeft(7);
        hBoxlblEndCob02.setTop(0);
        hBoxlblEndCob02.setWidth(256);
        hBoxlblEndCob02.setHeight(32);
        hBoxlblEndCob02.setBorderStyle("stNone");
        hBoxlblEndCob02.setPaddingTop(5);
        hBoxlblEndCob02.setPaddingLeft(0);
        hBoxlblEndCob02.setPaddingRight(0);
        hBoxlblEndCob02.setPaddingBottom(0);
        hBoxlblEndCob02.setMarginTop(0);
        hBoxlblEndCob02.setMarginLeft(0);
        hBoxlblEndCob02.setMarginRight(0);
        hBoxlblEndCob02.setMarginBottom(0);
        hBoxlblEndCob02.setSpacing(0);
        hBoxlblEndCob02.setFlexVflex("ftTrue");
        hBoxlblEndCob02.setFlexHflex("ftMin");
        hBoxlblEndCob02.setScrollable(false);
        hBoxlblEndCob02.setBoxShadowConfigHorizontalLength(10);
        hBoxlblEndCob02.setBoxShadowConfigVerticalLength(10);
        hBoxlblEndCob02.setBoxShadowConfigBlurRadius(5);
        hBoxlblEndCob02.setBoxShadowConfigSpreadRadius(0);
        hBoxlblEndCob02.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblEndCob02.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob02.setVAlign("tvTop");
        hBoxlblEndCob.addChildren(hBoxlblEndCob02);
        hBoxlblEndCob02.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(0);
        FVBox1.setTop(0);
        FVBox1.setWidth(7);
        FVBox1.setHeight(20);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(1);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob02.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFLabel lblEndCob = new TFLabel();

    private void init_lblEndCob() {
        lblEndCob.setName("lblEndCob");
        lblEndCob.setLeft(7);
        lblEndCob.setTop(0);
        lblEndCob.setWidth(66);
        lblEndCob.setHeight(19);
        lblEndCob.setCaption("Cobran\u00E7a");
        lblEndCob.setColor("clBtnFace");
        lblEndCob.setFontColor("13392431");
        lblEndCob.setFontSize(-16);
        lblEndCob.setFontName("Tahoma");
        lblEndCob.setFontStyle("[]");
        lblEndCob.setVerticalAlignment("taVerticalCenter");
        lblEndCob.setWordBreak(false);
        hBoxlblEndCob02.addChildren(lblEndCob);
        lblEndCob.applyProperties();
    }

    public TFVBox FVBox41 = new TFVBox();

    private void init_FVBox41() {
        FVBox41.setName("FVBox41");
        FVBox41.setLeft(73);
        FVBox41.setTop(0);
        FVBox41.setWidth(30);
        FVBox41.setHeight(20);
        FVBox41.setBorderStyle("stNone");
        FVBox41.setPaddingTop(0);
        FVBox41.setPaddingLeft(0);
        FVBox41.setPaddingRight(0);
        FVBox41.setPaddingBottom(0);
        FVBox41.setMarginTop(0);
        FVBox41.setMarginLeft(0);
        FVBox41.setMarginRight(0);
        FVBox41.setMarginBottom(0);
        FVBox41.setSpacing(1);
        FVBox41.setFlexVflex("ftTrue");
        FVBox41.setFlexHflex("ftFalse");
        FVBox41.setScrollable(false);
        FVBox41.setBoxShadowConfigHorizontalLength(10);
        FVBox41.setBoxShadowConfigVerticalLength(10);
        FVBox41.setBoxShadowConfigBlurRadius(5);
        FVBox41.setBoxShadowConfigSpreadRadius(0);
        FVBox41.setBoxShadowConfigShadowColor("clBlack");
        FVBox41.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob02.addChildren(FVBox41);
        FVBox41.applyProperties();
    }

    public TFLabel lblCEPCob = new TFLabel();

    private void init_lblCEPCob() {
        lblCEPCob.setName("lblCEPCob");
        lblCEPCob.setLeft(103);
        lblCEPCob.setTop(0);
        lblCEPCob.setWidth(28);
        lblCEPCob.setHeight(19);
        lblCEPCob.setCaption("CEP");
        lblCEPCob.setColor("clBtnFace");
        lblCEPCob.setFontColor("13392431");
        lblCEPCob.setFontSize(-16);
        lblCEPCob.setFontName("Tahoma");
        lblCEPCob.setFontStyle("[]");
        lblCEPCob.setVisible(false);
        lblCEPCob.setVerticalAlignment("taVerticalCenter");
        lblCEPCob.setWordBreak(false);
        hBoxlblEndCob02.addChildren(lblCEPCob);
        lblCEPCob.applyProperties();
    }

    public TFVBox FVBox70 = new TFVBox();

    private void init_FVBox70() {
        FVBox70.setName("FVBox70");
        FVBox70.setLeft(131);
        FVBox70.setTop(0);
        FVBox70.setWidth(7);
        FVBox70.setHeight(20);
        FVBox70.setBorderStyle("stNone");
        FVBox70.setPaddingTop(0);
        FVBox70.setPaddingLeft(0);
        FVBox70.setPaddingRight(0);
        FVBox70.setPaddingBottom(0);
        FVBox70.setMarginTop(0);
        FVBox70.setMarginLeft(0);
        FVBox70.setMarginRight(0);
        FVBox70.setMarginBottom(0);
        FVBox70.setSpacing(1);
        FVBox70.setFlexVflex("ftTrue");
        FVBox70.setFlexHflex("ftTrue");
        FVBox70.setScrollable(false);
        FVBox70.setBoxShadowConfigHorizontalLength(10);
        FVBox70.setBoxShadowConfigVerticalLength(10);
        FVBox70.setBoxShadowConfigBlurRadius(5);
        FVBox70.setBoxShadowConfigSpreadRadius(0);
        FVBox70.setBoxShadowConfigShadowColor("clBlack");
        FVBox70.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob02.addChildren(FVBox70);
        FVBox70.applyProperties();
    }

    public TFVBox FVBox39 = new TFVBox();

    private void init_FVBox39() {
        FVBox39.setName("FVBox39");
        FVBox39.setLeft(263);
        FVBox39.setTop(0);
        FVBox39.setWidth(7);
        FVBox39.setHeight(20);
        FVBox39.setBorderStyle("stNone");
        FVBox39.setPaddingTop(0);
        FVBox39.setPaddingLeft(0);
        FVBox39.setPaddingRight(0);
        FVBox39.setPaddingBottom(0);
        FVBox39.setMarginTop(0);
        FVBox39.setMarginLeft(0);
        FVBox39.setMarginRight(0);
        FVBox39.setMarginBottom(0);
        FVBox39.setSpacing(1);
        FVBox39.setFlexVflex("ftTrue");
        FVBox39.setFlexHflex("ftTrue");
        FVBox39.setScrollable(false);
        FVBox39.setBoxShadowConfigHorizontalLength(10);
        FVBox39.setBoxShadowConfigVerticalLength(10);
        FVBox39.setBoxShadowConfigBlurRadius(5);
        FVBox39.setBoxShadowConfigSpreadRadius(0);
        FVBox39.setBoxShadowConfigShadowColor("clBlack");
        FVBox39.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob.addChildren(FVBox39);
        FVBox39.applyProperties();
    }

    public TFVBox vBoxOptEndCob = new TFVBox();

    private void init_vBoxOptEndCob() {
        vBoxOptEndCob.setName("vBoxOptEndCob");
        vBoxOptEndCob.setLeft(270);
        vBoxOptEndCob.setTop(0);
        vBoxOptEndCob.setWidth(165);
        vBoxOptEndCob.setHeight(35);
        vBoxOptEndCob.setBorderStyle("stNone");
        vBoxOptEndCob.setPaddingTop(4);
        vBoxOptEndCob.setPaddingLeft(0);
        vBoxOptEndCob.setPaddingRight(0);
        vBoxOptEndCob.setPaddingBottom(0);
        vBoxOptEndCob.setMarginTop(0);
        vBoxOptEndCob.setMarginLeft(0);
        vBoxOptEndCob.setMarginRight(0);
        vBoxOptEndCob.setMarginBottom(0);
        vBoxOptEndCob.setSpacing(1);
        vBoxOptEndCob.setFlexVflex("ftFalse");
        vBoxOptEndCob.setFlexHflex("ftFalse");
        vBoxOptEndCob.setScrollable(false);
        vBoxOptEndCob.setBoxShadowConfigHorizontalLength(10);
        vBoxOptEndCob.setBoxShadowConfigVerticalLength(10);
        vBoxOptEndCob.setBoxShadowConfigBlurRadius(5);
        vBoxOptEndCob.setBoxShadowConfigSpreadRadius(0);
        vBoxOptEndCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxOptEndCob.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob.addChildren(vBoxOptEndCob);
        vBoxOptEndCob.applyProperties();
    }

    public TFCheckBox chkAceitarMudancaCobr = new TFCheckBox();

    private void init_chkAceitarMudancaCobr() {
        chkAceitarMudancaCobr.setName("chkAceitarMudancaCobr");
        chkAceitarMudancaCobr.setLeft(0);
        chkAceitarMudancaCobr.setTop(0);
        chkAceitarMudancaCobr.setWidth(147);
        chkAceitarMudancaCobr.setHeight(17);
        chkAceitarMudancaCobr.setCaption("Aceitar Correios");
        chkAceitarMudancaCobr.setFontColor("clWindowText");
        chkAceitarMudancaCobr.setFontSize(-13);
        chkAceitarMudancaCobr.setFontName("Tahoma");
        chkAceitarMudancaCobr.setFontStyle("[]");
        chkAceitarMudancaCobr.addEventListener("onCheck", (EventListener<Event<Object>>)(Event<Object> event) -> {
            chkAceitarMudancaCobrCheck(event);
            processarFlow("FrmCEPClienteOnLine", "chkAceitarMudancaCobr", "OnCheck");
        });
        chkAceitarMudancaCobr.setVerticalAlignment("taAlignTop");
        vBoxOptEndCob.addChildren(chkAceitarMudancaCobr);
        chkAceitarMudancaCobr.applyProperties();
    }

    public TFHBox FHBox5 = new TFHBox();

    private void init_FHBox5() {
        FHBox5.setName("FHBox5");
        FHBox5.setLeft(0);
        FHBox5.setTop(18);
        FHBox5.setWidth(147);
        FHBox5.setHeight(30);
        FHBox5.setBorderStyle("stNone");
        FHBox5.setPaddingTop(0);
        FHBox5.setPaddingLeft(0);
        FHBox5.setPaddingRight(0);
        FHBox5.setPaddingBottom(0);
        FHBox5.setVisible(false);
        FHBox5.setMarginTop(0);
        FHBox5.setMarginLeft(0);
        FHBox5.setMarginRight(0);
        FHBox5.setMarginBottom(0);
        FHBox5.setSpacing(1);
        FHBox5.setFlexVflex("ftFalse");
        FHBox5.setFlexHflex("ftTrue");
        FHBox5.setScrollable(false);
        FHBox5.setBoxShadowConfigHorizontalLength(10);
        FHBox5.setBoxShadowConfigVerticalLength(10);
        FHBox5.setBoxShadowConfigBlurRadius(5);
        FHBox5.setBoxShadowConfigSpreadRadius(0);
        FHBox5.setBoxShadowConfigShadowColor("clBlack");
        FHBox5.setBoxShadowConfigOpacity(75);
        FHBox5.setVAlign("tvTop");
        vBoxOptEndCob.addChildren(FHBox5);
        FHBox5.applyProperties();
    }

    public TFHBox hbCEPOnLineAceitarCob = new TFHBox();

    private void init_hbCEPOnLineAceitarCob() {
        hbCEPOnLineAceitarCob.setName("hbCEPOnLineAceitarCob");
        hbCEPOnLineAceitarCob.setLeft(0);
        hbCEPOnLineAceitarCob.setTop(0);
        hbCEPOnLineAceitarCob.setWidth(62);
        hbCEPOnLineAceitarCob.setHeight(25);
        hbCEPOnLineAceitarCob.setBorderStyle("stNone");
        hbCEPOnLineAceitarCob.setColor("clSilver");
        hbCEPOnLineAceitarCob.setPaddingTop(5);
        hbCEPOnLineAceitarCob.setPaddingLeft(0);
        hbCEPOnLineAceitarCob.setPaddingRight(0);
        hbCEPOnLineAceitarCob.setPaddingBottom(0);
        hbCEPOnLineAceitarCob.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineAceitarCobClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineAceitarCob", "OnClick");
        });
        hbCEPOnLineAceitarCob.setMarginTop(0);
        hbCEPOnLineAceitarCob.setMarginLeft(0);
        hbCEPOnLineAceitarCob.setMarginRight(0);
        hbCEPOnLineAceitarCob.setMarginBottom(0);
        hbCEPOnLineAceitarCob.setSpacing(0);
        hbCEPOnLineAceitarCob.setFlexVflex("ftTrue");
        hbCEPOnLineAceitarCob.setFlexHflex("ftTrue");
        hbCEPOnLineAceitarCob.setScrollable(false);
        hbCEPOnLineAceitarCob.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineAceitarCob.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineAceitarCob.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineAceitarCob.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineAceitarCob.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineAceitarCob.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarCob.setVAlign("tvTop");
        FHBox5.addChildren(hbCEPOnLineAceitarCob);
        hbCEPOnLineAceitarCob.applyProperties();
    }

    public TFVBox FVBox10 = new TFVBox();

    private void init_FVBox10() {
        FVBox10.setName("FVBox10");
        FVBox10.setLeft(0);
        FVBox10.setTop(0);
        FVBox10.setWidth(7);
        FVBox10.setHeight(20);
        FVBox10.setBorderStyle("stNone");
        FVBox10.setPaddingTop(0);
        FVBox10.setPaddingLeft(0);
        FVBox10.setPaddingRight(0);
        FVBox10.setPaddingBottom(0);
        FVBox10.setMarginTop(0);
        FVBox10.setMarginLeft(0);
        FVBox10.setMarginRight(0);
        FVBox10.setMarginBottom(0);
        FVBox10.setSpacing(1);
        FVBox10.setFlexVflex("ftTrue");
        FVBox10.setFlexHflex("ftTrue");
        FVBox10.setScrollable(false);
        FVBox10.setBoxShadowConfigHorizontalLength(10);
        FVBox10.setBoxShadowConfigVerticalLength(10);
        FVBox10.setBoxShadowConfigBlurRadius(5);
        FVBox10.setBoxShadowConfigSpreadRadius(0);
        FVBox10.setBoxShadowConfigShadowColor("clBlack");
        FVBox10.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarCob.addChildren(FVBox10);
        FVBox10.applyProperties();
    }

    public TFLabel lblAceitarCob = new TFLabel();

    private void init_lblAceitarCob() {
        lblAceitarCob.setName("lblAceitarCob");
        lblAceitarCob.setLeft(7);
        lblAceitarCob.setTop(0);
        lblAceitarCob.setWidth(38);
        lblAceitarCob.setHeight(14);
        lblAceitarCob.setCaption("Aceitar");
        lblAceitarCob.setFontColor("clWindowText");
        lblAceitarCob.setFontSize(-12);
        lblAceitarCob.setFontName("Tahoma");
        lblAceitarCob.setFontStyle("[]");
        lblAceitarCob.setVerticalAlignment("taVerticalCenter");
        lblAceitarCob.setWordBreak(false);
        hbCEPOnLineAceitarCob.addChildren(lblAceitarCob);
        lblAceitarCob.applyProperties();
    }

    public TFVBox FVBox11 = new TFVBox();

    private void init_FVBox11() {
        FVBox11.setName("FVBox11");
        FVBox11.setLeft(45);
        FVBox11.setTop(0);
        FVBox11.setWidth(7);
        FVBox11.setHeight(19);
        FVBox11.setBorderStyle("stNone");
        FVBox11.setPaddingTop(0);
        FVBox11.setPaddingLeft(0);
        FVBox11.setPaddingRight(0);
        FVBox11.setPaddingBottom(0);
        FVBox11.setMarginTop(0);
        FVBox11.setMarginLeft(0);
        FVBox11.setMarginRight(0);
        FVBox11.setMarginBottom(0);
        FVBox11.setSpacing(1);
        FVBox11.setFlexVflex("ftTrue");
        FVBox11.setFlexHflex("ftTrue");
        FVBox11.setScrollable(false);
        FVBox11.setBoxShadowConfigHorizontalLength(10);
        FVBox11.setBoxShadowConfigVerticalLength(10);
        FVBox11.setBoxShadowConfigBlurRadius(5);
        FVBox11.setBoxShadowConfigSpreadRadius(0);
        FVBox11.setBoxShadowConfigShadowColor("clBlack");
        FVBox11.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarCob.addChildren(FVBox11);
        FVBox11.applyProperties();
    }

    public TFHBox hbCEPOnLineManterCob = new TFHBox();

    private void init_hbCEPOnLineManterCob() {
        hbCEPOnLineManterCob.setName("hbCEPOnLineManterCob");
        hbCEPOnLineManterCob.setLeft(62);
        hbCEPOnLineManterCob.setTop(0);
        hbCEPOnLineManterCob.setWidth(78);
        hbCEPOnLineManterCob.setHeight(25);
        hbCEPOnLineManterCob.setBorderStyle("stNone");
        hbCEPOnLineManterCob.setPaddingTop(5);
        hbCEPOnLineManterCob.setPaddingLeft(0);
        hbCEPOnLineManterCob.setPaddingRight(0);
        hbCEPOnLineManterCob.setPaddingBottom(0);
        hbCEPOnLineManterCob.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineManterCobClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineManterCob", "OnClick");
        });
        hbCEPOnLineManterCob.setMarginTop(0);
        hbCEPOnLineManterCob.setMarginLeft(0);
        hbCEPOnLineManterCob.setMarginRight(0);
        hbCEPOnLineManterCob.setMarginBottom(0);
        hbCEPOnLineManterCob.setSpacing(0);
        hbCEPOnLineManterCob.setFlexVflex("ftTrue");
        hbCEPOnLineManterCob.setFlexHflex("ftTrue");
        hbCEPOnLineManterCob.setScrollable(false);
        hbCEPOnLineManterCob.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineManterCob.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineManterCob.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineManterCob.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineManterCob.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineManterCob.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterCob.setVAlign("tvTop");
        FHBox5.addChildren(hbCEPOnLineManterCob);
        hbCEPOnLineManterCob.applyProperties();
    }

    public TFVBox FVBox12 = new TFVBox();

    private void init_FVBox12() {
        FVBox12.setName("FVBox12");
        FVBox12.setLeft(0);
        FVBox12.setTop(0);
        FVBox12.setWidth(7);
        FVBox12.setHeight(20);
        FVBox12.setBorderStyle("stNone");
        FVBox12.setPaddingTop(0);
        FVBox12.setPaddingLeft(0);
        FVBox12.setPaddingRight(0);
        FVBox12.setPaddingBottom(0);
        FVBox12.setMarginTop(0);
        FVBox12.setMarginLeft(0);
        FVBox12.setMarginRight(0);
        FVBox12.setMarginBottom(0);
        FVBox12.setSpacing(1);
        FVBox12.setFlexVflex("ftTrue");
        FVBox12.setFlexHflex("ftTrue");
        FVBox12.setScrollable(false);
        FVBox12.setBoxShadowConfigHorizontalLength(10);
        FVBox12.setBoxShadowConfigVerticalLength(10);
        FVBox12.setBoxShadowConfigBlurRadius(5);
        FVBox12.setBoxShadowConfigSpreadRadius(0);
        FVBox12.setBoxShadowConfigShadowColor("clBlack");
        FVBox12.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterCob.addChildren(FVBox12);
        FVBox12.applyProperties();
    }

    public TFLabel lblManterCob = new TFLabel();

    private void init_lblManterCob() {
        lblManterCob.setName("lblManterCob");
        lblManterCob.setLeft(7);
        lblManterCob.setTop(0);
        lblManterCob.setWidth(38);
        lblManterCob.setHeight(14);
        lblManterCob.setCaption("Manter");
        lblManterCob.setFontColor("clWindowText");
        lblManterCob.setFontSize(-12);
        lblManterCob.setFontName("Tahoma");
        lblManterCob.setFontStyle("[]");
        lblManterCob.setVerticalAlignment("taVerticalCenter");
        lblManterCob.setWordBreak(false);
        hbCEPOnLineManterCob.addChildren(lblManterCob);
        lblManterCob.applyProperties();
    }

    public TFVBox FVBox13 = new TFVBox();

    private void init_FVBox13() {
        FVBox13.setName("FVBox13");
        FVBox13.setLeft(45);
        FVBox13.setTop(0);
        FVBox13.setWidth(7);
        FVBox13.setHeight(20);
        FVBox13.setBorderStyle("stNone");
        FVBox13.setPaddingTop(0);
        FVBox13.setPaddingLeft(0);
        FVBox13.setPaddingRight(0);
        FVBox13.setPaddingBottom(0);
        FVBox13.setMarginTop(0);
        FVBox13.setMarginLeft(0);
        FVBox13.setMarginRight(0);
        FVBox13.setMarginBottom(0);
        FVBox13.setSpacing(1);
        FVBox13.setFlexVflex("ftTrue");
        FVBox13.setFlexHflex("ftTrue");
        FVBox13.setScrollable(false);
        FVBox13.setBoxShadowConfigHorizontalLength(10);
        FVBox13.setBoxShadowConfigVerticalLength(10);
        FVBox13.setBoxShadowConfigBlurRadius(5);
        FVBox13.setBoxShadowConfigSpreadRadius(0);
        FVBox13.setBoxShadowConfigShadowColor("clBlack");
        FVBox13.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterCob.addChildren(FVBox13);
        FVBox13.applyProperties();
    }

    public TFVBox FVBox34 = new TFVBox();

    private void init_FVBox34() {
        FVBox34.setName("FVBox34");
        FVBox34.setLeft(435);
        FVBox34.setTop(0);
        FVBox34.setWidth(10);
        FVBox34.setHeight(35);
        FVBox34.setBorderStyle("stNone");
        FVBox34.setPaddingTop(0);
        FVBox34.setPaddingLeft(0);
        FVBox34.setPaddingRight(0);
        FVBox34.setPaddingBottom(0);
        FVBox34.setMarginTop(0);
        FVBox34.setMarginLeft(0);
        FVBox34.setMarginRight(0);
        FVBox34.setMarginBottom(0);
        FVBox34.setSpacing(1);
        FVBox34.setFlexVflex("ftFalse");
        FVBox34.setFlexHflex("ftFalse");
        FVBox34.setScrollable(false);
        FVBox34.setBoxShadowConfigHorizontalLength(10);
        FVBox34.setBoxShadowConfigVerticalLength(10);
        FVBox34.setBoxShadowConfigBlurRadius(5);
        FVBox34.setBoxShadowConfigSpreadRadius(0);
        FVBox34.setBoxShadowConfigShadowColor("clBlack");
        FVBox34.setBoxShadowConfigOpacity(75);
        hBoxlblEndCob.addChildren(FVBox34);
        FVBox34.applyProperties();
    }

    public TFHBox boxEndCob = new TFHBox();

    private void init_boxEndCob() {
        boxEndCob.setName("boxEndCob");
        boxEndCob.setLeft(0);
        boxEndCob.setTop(385);
        boxEndCob.setWidth(503);
        boxEndCob.setHeight(200);
        boxEndCob.setBorderStyle("stNone");
        boxEndCob.setPaddingTop(5);
        boxEndCob.setPaddingLeft(0);
        boxEndCob.setPaddingRight(0);
        boxEndCob.setPaddingBottom(0);
        boxEndCob.setMarginTop(0);
        boxEndCob.setMarginLeft(0);
        boxEndCob.setMarginRight(0);
        boxEndCob.setMarginBottom(0);
        boxEndCob.setSpacing(1);
        boxEndCob.setFlexVflex("ftMin");
        boxEndCob.setFlexHflex("ftTrue");
        boxEndCob.setScrollable(false);
        boxEndCob.setBoxShadowConfigHorizontalLength(10);
        boxEndCob.setBoxShadowConfigVerticalLength(10);
        boxEndCob.setBoxShadowConfigBlurRadius(5);
        boxEndCob.setBoxShadowConfigSpreadRadius(0);
        boxEndCob.setBoxShadowConfigShadowColor("clBlack");
        boxEndCob.setBoxShadowConfigOpacity(75);
        boxEndCob.setVAlign("tvTop");
        vBoxEnderecos.addChildren(boxEndCob);
        boxEndCob.applyProperties();
    }

    public TFVBox vBoxEnderecoCob = new TFVBox();

    private void init_vBoxEnderecoCob() {
        vBoxEnderecoCob.setName("vBoxEnderecoCob");
        vBoxEnderecoCob.setLeft(0);
        vBoxEnderecoCob.setTop(0);
        vBoxEnderecoCob.setWidth(763);
        vBoxEnderecoCob.setHeight(200);
        vBoxEnderecoCob.setBorderStyle("stNone");
        vBoxEnderecoCob.setPaddingTop(0);
        vBoxEnderecoCob.setPaddingLeft(10);
        vBoxEnderecoCob.setPaddingRight(0);
        vBoxEnderecoCob.setPaddingBottom(0);
        vBoxEnderecoCob.setMarginTop(0);
        vBoxEnderecoCob.setMarginLeft(0);
        vBoxEnderecoCob.setMarginRight(0);
        vBoxEnderecoCob.setMarginBottom(0);
        vBoxEnderecoCob.setSpacing(1);
        vBoxEnderecoCob.setFlexVflex("ftMin");
        vBoxEnderecoCob.setFlexHflex("ftTrue");
        vBoxEnderecoCob.setScrollable(false);
        vBoxEnderecoCob.setBoxShadowConfigHorizontalLength(10);
        vBoxEnderecoCob.setBoxShadowConfigVerticalLength(10);
        vBoxEnderecoCob.setBoxShadowConfigBlurRadius(5);
        vBoxEnderecoCob.setBoxShadowConfigSpreadRadius(0);
        vBoxEnderecoCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxEnderecoCob.setBoxShadowConfigOpacity(75);
        boxEndCob.addChildren(vBoxEnderecoCob);
        vBoxEnderecoCob.applyProperties();
    }

    public TFHBox hBoxEndCob = new TFHBox();

    private void init_hBoxEndCob() {
        hBoxEndCob.setName("hBoxEndCob");
        hBoxEndCob.setLeft(0);
        hBoxEndCob.setTop(0);
        hBoxEndCob.setWidth(758);
        hBoxEndCob.setHeight(92);
        hBoxEndCob.setBorderStyle("stNone");
        hBoxEndCob.setPaddingTop(0);
        hBoxEndCob.setPaddingLeft(0);
        hBoxEndCob.setPaddingRight(0);
        hBoxEndCob.setPaddingBottom(0);
        hBoxEndCob.setMarginTop(0);
        hBoxEndCob.setMarginLeft(0);
        hBoxEndCob.setMarginRight(0);
        hBoxEndCob.setMarginBottom(0);
        hBoxEndCob.setSpacing(3);
        hBoxEndCob.setFlexVflex("ftMin");
        hBoxEndCob.setFlexHflex("ftTrue");
        hBoxEndCob.setScrollable(false);
        hBoxEndCob.setBoxShadowConfigHorizontalLength(10);
        hBoxEndCob.setBoxShadowConfigVerticalLength(10);
        hBoxEndCob.setBoxShadowConfigBlurRadius(5);
        hBoxEndCob.setBoxShadowConfigSpreadRadius(0);
        hBoxEndCob.setBoxShadowConfigShadowColor("clBlack");
        hBoxEndCob.setBoxShadowConfigOpacity(75);
        hBoxEndCob.setVAlign("tvTop");
        vBoxEnderecoCob.addChildren(hBoxEndCob);
        hBoxEndCob.applyProperties();
    }

    public TFVBox FVBox73 = new TFVBox();

    private void init_FVBox73() {
        FVBox73.setName("FVBox73");
        FVBox73.setLeft(0);
        FVBox73.setTop(0);
        FVBox73.setWidth(45);
        FVBox73.setHeight(98);
        FVBox73.setBorderStyle("stNone");
        FVBox73.setPaddingTop(0);
        FVBox73.setPaddingLeft(0);
        FVBox73.setPaddingRight(0);
        FVBox73.setPaddingBottom(0);
        FVBox73.setMarginTop(0);
        FVBox73.setMarginLeft(0);
        FVBox73.setMarginRight(0);
        FVBox73.setMarginBottom(0);
        FVBox73.setSpacing(1);
        FVBox73.setFlexVflex("ftMin");
        FVBox73.setFlexHflex("ftFalse");
        FVBox73.setScrollable(false);
        FVBox73.setBoxShadowConfigHorizontalLength(10);
        FVBox73.setBoxShadowConfigVerticalLength(10);
        FVBox73.setBoxShadowConfigBlurRadius(5);
        FVBox73.setBoxShadowConfigSpreadRadius(0);
        FVBox73.setBoxShadowConfigShadowColor("clBlack");
        FVBox73.setBoxShadowConfigOpacity(75);
        hBoxEndCob.addChildren(FVBox73);
        FVBox73.applyProperties();
    }

    public TFLabel FLabel3 = new TFLabel();

    private void init_FLabel3() {
        FLabel3.setName("FLabel3");
        FLabel3.setLeft(0);
        FLabel3.setTop(0);
        FLabel3.setWidth(25);
        FLabel3.setHeight(13);
        FLabel3.setCaption("Atual");
        FLabel3.setFontColor("clWindowText");
        FLabel3.setFontSize(-11);
        FLabel3.setFontName("Tahoma");
        FLabel3.setFontStyle("[]");
        FLabel3.setVerticalAlignment("taVerticalCenter");
        FLabel3.setWordBreak(false);
        FVBox73.addChildren(FLabel3);
        FLabel3.applyProperties();
    }

    public TFLabel FLabel4 = new TFLabel();

    private void init_FLabel4() {
        FLabel4.setName("FLabel4");
        FLabel4.setLeft(0);
        FLabel4.setTop(14);
        FLabel4.setWidth(40);
        FLabel4.setHeight(13);
        FLabel4.setCaption("Correios");
        FLabel4.setFontColor("clWindowText");
        FLabel4.setFontSize(-11);
        FLabel4.setFontName("Tahoma");
        FLabel4.setFontStyle("[]");
        FLabel4.setVerticalAlignment("taVerticalCenter");
        FLabel4.setWordBreak(false);
        FVBox73.addChildren(FLabel4);
        FLabel4.applyProperties();
    }

    public TFVBox vBoxCidadeCob = new TFVBox();

    private void init_vBoxCidadeCob() {
        vBoxCidadeCob.setName("vBoxCidadeCob");
        vBoxCidadeCob.setLeft(45);
        vBoxCidadeCob.setTop(0);
        vBoxCidadeCob.setWidth(187);
        vBoxCidadeCob.setHeight(129);
        vBoxCidadeCob.setBorderStyle("stNone");
        vBoxCidadeCob.setPaddingTop(0);
        vBoxCidadeCob.setPaddingLeft(10);
        vBoxCidadeCob.setPaddingRight(0);
        vBoxCidadeCob.setPaddingBottom(0);
        vBoxCidadeCob.setMarginTop(0);
        vBoxCidadeCob.setMarginLeft(0);
        vBoxCidadeCob.setMarginRight(0);
        vBoxCidadeCob.setMarginBottom(0);
        vBoxCidadeCob.setSpacing(3);
        vBoxCidadeCob.setFlexVflex("ftMin");
        vBoxCidadeCob.setFlexHflex("ftFalse");
        vBoxCidadeCob.setScrollable(false);
        vBoxCidadeCob.setBoxShadowConfigHorizontalLength(10);
        vBoxCidadeCob.setBoxShadowConfigVerticalLength(10);
        vBoxCidadeCob.setBoxShadowConfigBlurRadius(5);
        vBoxCidadeCob.setBoxShadowConfigSpreadRadius(0);
        vBoxCidadeCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxCidadeCob.setBoxShadowConfigOpacity(75);
        hBoxEndCob.addChildren(vBoxCidadeCob);
        vBoxCidadeCob.applyProperties();
    }

    public TFVBox vBoxCepEndCobr = new TFVBox();

    private void init_vBoxCepEndCobr() {
        vBoxCepEndCobr.setName("vBoxCepEndCobr");
        vBoxCepEndCobr.setLeft(0);
        vBoxCepEndCobr.setTop(0);
        vBoxCepEndCobr.setWidth(151);
        vBoxCepEndCobr.setHeight(43);
        vBoxCepEndCobr.setBorderStyle("stNone");
        vBoxCepEndCobr.setPaddingTop(0);
        vBoxCepEndCobr.setPaddingLeft(0);
        vBoxCepEndCobr.setPaddingRight(0);
        vBoxCepEndCobr.setPaddingBottom(0);
        vBoxCepEndCobr.setMarginTop(0);
        vBoxCepEndCobr.setMarginLeft(0);
        vBoxCepEndCobr.setMarginRight(0);
        vBoxCepEndCobr.setMarginBottom(0);
        vBoxCepEndCobr.setSpacing(1);
        vBoxCepEndCobr.setFlexVflex("ftFalse");
        vBoxCepEndCobr.setFlexHflex("ftTrue");
        vBoxCepEndCobr.setScrollable(false);
        vBoxCepEndCobr.setBoxShadowConfigHorizontalLength(10);
        vBoxCepEndCobr.setBoxShadowConfigVerticalLength(10);
        vBoxCepEndCobr.setBoxShadowConfigBlurRadius(5);
        vBoxCepEndCobr.setBoxShadowConfigSpreadRadius(0);
        vBoxCepEndCobr.setBoxShadowConfigShadowColor("clBlack");
        vBoxCepEndCobr.setBoxShadowConfigOpacity(75);
        vBoxCidadeCob.addChildren(vBoxCepEndCobr);
        vBoxCepEndCobr.applyProperties();
    }

    public TFLabel lblCepCobAtual = new TFLabel();

    private void init_lblCepCobAtual() {
        lblCepCobAtual.setName("lblCepCobAtual");
        lblCepCobAtual.setLeft(0);
        lblCepCobAtual.setTop(0);
        lblCepCobAtual.setWidth(120);
        lblCepCobAtual.setHeight(13);
        lblCepCobAtual.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCepCobAtual.setFontColor("clRed");
        lblCepCobAtual.setFontSize(-11);
        lblCepCobAtual.setFontName("Tahoma");
        lblCepCobAtual.setFontStyle("[]");
        lblCepCobAtual.setFieldName("CEP_COBRANCA");
        lblCepCobAtual.setTable(tbClientesEndereco);
        lblCepCobAtual.setVerticalAlignment("taVerticalCenter");
        lblCepCobAtual.setWordBreak(false);
        vBoxCepEndCobr.addChildren(lblCepCobAtual);
        lblCepCobAtual.applyProperties();
    }

    public TFLabel lblCepCobCorreio = new TFLabel();

    private void init_lblCepCobCorreio() {
        lblCepCobCorreio.setName("lblCepCobCorreio");
        lblCepCobCorreio.setLeft(0);
        lblCepCobCorreio.setTop(14);
        lblCepCobCorreio.setWidth(120);
        lblCepCobCorreio.setHeight(13);
        lblCepCobCorreio.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCepCobCorreio.setFontColor("13392431");
        lblCepCobCorreio.setFontSize(-11);
        lblCepCobCorreio.setFontName("Tahoma");
        lblCepCobCorreio.setFontStyle("[]");
        lblCepCobCorreio.setFieldName("CEP_COBRANCA");
        lblCepCobCorreio.setTable(tbClientesEnderecoTemp);
        lblCepCobCorreio.setVerticalAlignment("taVerticalCenter");
        lblCepCobCorreio.setWordBreak(false);
        vBoxCepEndCobr.addChildren(lblCepCobCorreio);
        lblCepCobCorreio.applyProperties();
    }

    public TFVBox vBoxRuaEndCob = new TFVBox();

    private void init_vBoxRuaEndCob() {
        vBoxRuaEndCob.setName("vBoxRuaEndCob");
        vBoxRuaEndCob.setLeft(0);
        vBoxRuaEndCob.setTop(44);
        vBoxRuaEndCob.setWidth(151);
        vBoxRuaEndCob.setHeight(37);
        vBoxRuaEndCob.setBorderStyle("stNone");
        vBoxRuaEndCob.setPaddingTop(0);
        vBoxRuaEndCob.setPaddingLeft(0);
        vBoxRuaEndCob.setPaddingRight(0);
        vBoxRuaEndCob.setPaddingBottom(0);
        vBoxRuaEndCob.setMarginTop(0);
        vBoxRuaEndCob.setMarginLeft(0);
        vBoxRuaEndCob.setMarginRight(0);
        vBoxRuaEndCob.setMarginBottom(0);
        vBoxRuaEndCob.setSpacing(3);
        vBoxRuaEndCob.setFlexVflex("ftMin");
        vBoxRuaEndCob.setFlexHflex("ftTrue");
        vBoxRuaEndCob.setScrollable(false);
        vBoxRuaEndCob.setBoxShadowConfigHorizontalLength(10);
        vBoxRuaEndCob.setBoxShadowConfigVerticalLength(10);
        vBoxRuaEndCob.setBoxShadowConfigBlurRadius(5);
        vBoxRuaEndCob.setBoxShadowConfigSpreadRadius(0);
        vBoxRuaEndCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxRuaEndCob.setBoxShadowConfigOpacity(75);
        vBoxCidadeCob.addChildren(vBoxRuaEndCob);
        vBoxRuaEndCob.applyProperties();
    }

    public TFLabel lblRuaCobCad = new TFLabel();

    private void init_lblRuaCobCad() {
        lblRuaCobCad.setName("lblRuaCobCad");
        lblRuaCobCad.setLeft(0);
        lblRuaCobCad.setTop(0);
        lblRuaCobCad.setWidth(120);
        lblRuaCobCad.setHeight(13);
        lblRuaCobCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaCobCad.setFontColor("clRed");
        lblRuaCobCad.setFontSize(-11);
        lblRuaCobCad.setFontName("Tahoma");
        lblRuaCobCad.setFontStyle("[]");
        lblRuaCobCad.setFieldName("RUA_COBRANCA");
        lblRuaCobCad.setTable(tbClientesEndereco);
        lblRuaCobCad.setVerticalAlignment("taVerticalCenter");
        lblRuaCobCad.setWordBreak(false);
        vBoxRuaEndCob.addChildren(lblRuaCobCad);
        lblRuaCobCad.applyProperties();
    }

    public TFLabel lblRuaCobCorreios = new TFLabel();

    private void init_lblRuaCobCorreios() {
        lblRuaCobCorreios.setName("lblRuaCobCorreios");
        lblRuaCobCorreios.setLeft(0);
        lblRuaCobCorreios.setTop(14);
        lblRuaCobCorreios.setWidth(120);
        lblRuaCobCorreios.setHeight(13);
        lblRuaCobCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaCobCorreios.setFontColor("13392431");
        lblRuaCobCorreios.setFontSize(-11);
        lblRuaCobCorreios.setFontName("Tahoma");
        lblRuaCobCorreios.setFontStyle("[]");
        lblRuaCobCorreios.setFieldName("RUA_COBRANCA");
        lblRuaCobCorreios.setTable(tbClientesEnderecoTemp);
        lblRuaCobCorreios.setVerticalAlignment("taVerticalCenter");
        lblRuaCobCorreios.setWordBreak(false);
        vBoxRuaEndCob.addChildren(lblRuaCobCorreios);
        lblRuaCobCorreios.applyProperties();
    }

    public TFVBox vBoxNumeroCob = new TFVBox();

    private void init_vBoxNumeroCob() {
        vBoxNumeroCob.setName("vBoxNumeroCob");
        vBoxNumeroCob.setLeft(0);
        vBoxNumeroCob.setTop(82);
        vBoxNumeroCob.setWidth(107);
        vBoxNumeroCob.setHeight(30);
        vBoxNumeroCob.setBorderStyle("stNone");
        vBoxNumeroCob.setPaddingTop(0);
        vBoxNumeroCob.setPaddingLeft(0);
        vBoxNumeroCob.setPaddingRight(0);
        vBoxNumeroCob.setPaddingBottom(0);
        vBoxNumeroCob.setVisible(false);
        vBoxNumeroCob.setMarginTop(0);
        vBoxNumeroCob.setMarginLeft(0);
        vBoxNumeroCob.setMarginRight(0);
        vBoxNumeroCob.setMarginBottom(0);
        vBoxNumeroCob.setSpacing(3);
        vBoxNumeroCob.setFlexVflex("ftFalse");
        vBoxNumeroCob.setFlexHflex("ftTrue");
        vBoxNumeroCob.setScrollable(false);
        vBoxNumeroCob.setBoxShadowConfigHorizontalLength(10);
        vBoxNumeroCob.setBoxShadowConfigVerticalLength(10);
        vBoxNumeroCob.setBoxShadowConfigBlurRadius(5);
        vBoxNumeroCob.setBoxShadowConfigSpreadRadius(0);
        vBoxNumeroCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxNumeroCob.setBoxShadowConfigOpacity(75);
        vBoxCidadeCob.addChildren(vBoxNumeroCob);
        vBoxNumeroCob.applyProperties();
    }

    public TFLabel lblNumeroCob = new TFLabel();

    private void init_lblNumeroCob() {
        lblNumeroCob.setName("lblNumeroCob");
        lblNumeroCob.setLeft(0);
        lblNumeroCob.setTop(0);
        lblNumeroCob.setWidth(37);
        lblNumeroCob.setHeight(13);
        lblNumeroCob.setCaption("N\u00FAmero");
        lblNumeroCob.setFontColor("clWindowText");
        lblNumeroCob.setFontSize(-11);
        lblNumeroCob.setFontName("Tahoma");
        lblNumeroCob.setFontStyle("[]");
        lblNumeroCob.setVerticalAlignment("taVerticalCenter");
        lblNumeroCob.setWordBreak(false);
        vBoxNumeroCob.addChildren(lblNumeroCob);
        lblNumeroCob.applyProperties();
    }

    public TFHBox hBoxNumeroCobCad = new TFHBox();

    private void init_hBoxNumeroCobCad() {
        hBoxNumeroCobCad.setName("hBoxNumeroCobCad");
        hBoxNumeroCobCad.setLeft(0);
        hBoxNumeroCobCad.setTop(14);
        hBoxNumeroCobCad.setWidth(98);
        hBoxNumeroCobCad.setHeight(35);
        hBoxNumeroCobCad.setBorderStyle("stNone");
        hBoxNumeroCobCad.setPaddingTop(0);
        hBoxNumeroCobCad.setPaddingLeft(0);
        hBoxNumeroCobCad.setPaddingRight(0);
        hBoxNumeroCobCad.setPaddingBottom(0);
        hBoxNumeroCobCad.setMarginTop(0);
        hBoxNumeroCobCad.setMarginLeft(0);
        hBoxNumeroCobCad.setMarginRight(0);
        hBoxNumeroCobCad.setMarginBottom(0);
        hBoxNumeroCobCad.setSpacing(3);
        hBoxNumeroCobCad.setFlexVflex("ftFalse");
        hBoxNumeroCobCad.setFlexHflex("ftFalse");
        hBoxNumeroCobCad.setScrollable(false);
        hBoxNumeroCobCad.setBoxShadowConfigHorizontalLength(10);
        hBoxNumeroCobCad.setBoxShadowConfigVerticalLength(10);
        hBoxNumeroCobCad.setBoxShadowConfigBlurRadius(5);
        hBoxNumeroCobCad.setBoxShadowConfigSpreadRadius(0);
        hBoxNumeroCobCad.setBoxShadowConfigShadowColor("clBlack");
        hBoxNumeroCobCad.setBoxShadowConfigOpacity(75);
        hBoxNumeroCobCad.setVAlign("tvTop");
        vBoxNumeroCob.addChildren(hBoxNumeroCobCad);
        hBoxNumeroCobCad.applyProperties();
    }

    public TFString edNumeroCobCad = new TFString();

    private void init_edNumeroCobCad() {
        edNumeroCobCad.setName("edNumeroCobCad");
        edNumeroCobCad.setLeft(0);
        edNumeroCobCad.setTop(0);
        edNumeroCobCad.setWidth(81);
        edNumeroCobCad.setHeight(21);
        edNumeroCobCad.setTable(tbClientesEndereco);
        edNumeroCobCad.setFieldName("FACHADA_COBRANCA");
        edNumeroCobCad.setFlex(true);
        edNumeroCobCad.setRequired(false);
        edNumeroCobCad.setConstraintCheckWhen("cwImmediate");
        edNumeroCobCad.setConstraintCheckType("ctExpression");
        edNumeroCobCad.setConstraintFocusOnError(false);
        edNumeroCobCad.setConstraintEnableUI(true);
        edNumeroCobCad.setConstraintEnabled(false);
        edNumeroCobCad.setConstraintFormCheck(true);
        edNumeroCobCad.setCharCase("ccNormal");
        edNumeroCobCad.setPwd(false);
        edNumeroCobCad.setMaxlength(5);
        edNumeroCobCad.setFontColor("clWindowText");
        edNumeroCobCad.setFontSize(-11);
        edNumeroCobCad.setFontName("Tahoma");
        edNumeroCobCad.setFontStyle("[]");
        edNumeroCobCad.setSaveLiteralCharacter(false);
        edNumeroCobCad.applyProperties();
        hBoxNumeroCobCad.addChildren(edNumeroCobCad);
        addValidatable(edNumeroCobCad);
    }

    public TFVBox FVBox51 = new TFVBox();

    private void init_FVBox51() {
        FVBox51.setName("FVBox51");
        FVBox51.setLeft(81);
        FVBox51.setTop(0);
        FVBox51.setWidth(12);
        FVBox51.setHeight(30);
        FVBox51.setBorderStyle("stNone");
        FVBox51.setPaddingTop(0);
        FVBox51.setPaddingLeft(0);
        FVBox51.setPaddingRight(0);
        FVBox51.setPaddingBottom(0);
        FVBox51.setMarginTop(0);
        FVBox51.setMarginLeft(0);
        FVBox51.setMarginRight(0);
        FVBox51.setMarginBottom(0);
        FVBox51.setSpacing(1);
        FVBox51.setFlexVflex("ftFalse");
        FVBox51.setFlexHflex("ftFalse");
        FVBox51.setScrollable(false);
        FVBox51.setBoxShadowConfigHorizontalLength(10);
        FVBox51.setBoxShadowConfigVerticalLength(10);
        FVBox51.setBoxShadowConfigBlurRadius(5);
        FVBox51.setBoxShadowConfigSpreadRadius(0);
        FVBox51.setBoxShadowConfigShadowColor("clBlack");
        FVBox51.setBoxShadowConfigOpacity(75);
        hBoxNumeroCobCad.addChildren(FVBox51);
        FVBox51.applyProperties();
    }

    public TFLabel lblNumeroCobCorreios = new TFLabel();

    private void init_lblNumeroCobCorreios() {
        lblNumeroCobCorreios.setName("lblNumeroCobCorreios");
        lblNumeroCobCorreios.setLeft(0);
        lblNumeroCobCorreios.setTop(50);
        lblNumeroCobCorreios.setWidth(120);
        lblNumeroCobCorreios.setHeight(13);
        lblNumeroCobCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblNumeroCobCorreios.setFontColor("13392431");
        lblNumeroCobCorreios.setFontSize(-11);
        lblNumeroCobCorreios.setFontName("Tahoma");
        lblNumeroCobCorreios.setFontStyle("[]");
        lblNumeroCobCorreios.setVisible(false);
        lblNumeroCobCorreios.setFieldName("FACHADA_COBRANCA");
        lblNumeroCobCorreios.setTable(tbClientesEnderecoTemp);
        lblNumeroCobCorreios.setVerticalAlignment("taVerticalCenter");
        lblNumeroCobCorreios.setWordBreak(false);
        vBoxNumeroCob.addChildren(lblNumeroCobCorreios);
        lblNumeroCobCorreios.applyProperties();
    }

    public TFVBox FVBox29 = new TFVBox();

    private void init_FVBox29() {
        FVBox29.setName("FVBox29");
        FVBox29.setLeft(0);
        FVBox29.setTop(64);
        FVBox29.setWidth(98);
        FVBox29.setHeight(9);
        FVBox29.setBorderStyle("stNone");
        FVBox29.setPaddingTop(0);
        FVBox29.setPaddingLeft(0);
        FVBox29.setPaddingRight(0);
        FVBox29.setPaddingBottom(0);
        FVBox29.setMarginTop(0);
        FVBox29.setMarginLeft(0);
        FVBox29.setMarginRight(0);
        FVBox29.setMarginBottom(0);
        FVBox29.setSpacing(1);
        FVBox29.setFlexVflex("ftFalse");
        FVBox29.setFlexHflex("ftFalse");
        FVBox29.setScrollable(false);
        FVBox29.setBoxShadowConfigHorizontalLength(10);
        FVBox29.setBoxShadowConfigVerticalLength(10);
        FVBox29.setBoxShadowConfigBlurRadius(5);
        FVBox29.setBoxShadowConfigSpreadRadius(0);
        FVBox29.setBoxShadowConfigShadowColor("clBlack");
        FVBox29.setBoxShadowConfigOpacity(75);
        vBoxNumeroCob.addChildren(FVBox29);
        FVBox29.applyProperties();
    }

    public TFVBox FVBox30 = new TFVBox();

    private void init_FVBox30() {
        FVBox30.setName("FVBox30");
        FVBox30.setLeft(232);
        FVBox30.setTop(0);
        FVBox30.setWidth(322);
        FVBox30.setHeight(125);
        FVBox30.setBorderStyle("stNone");
        FVBox30.setPaddingTop(0);
        FVBox30.setPaddingLeft(10);
        FVBox30.setPaddingRight(0);
        FVBox30.setPaddingBottom(0);
        FVBox30.setMarginTop(0);
        FVBox30.setMarginLeft(0);
        FVBox30.setMarginRight(0);
        FVBox30.setMarginBottom(0);
        FVBox30.setSpacing(1);
        FVBox30.setFlexVflex("ftTrue");
        FVBox30.setFlexHflex("ftTrue");
        FVBox30.setScrollable(false);
        FVBox30.setBoxShadowConfigHorizontalLength(10);
        FVBox30.setBoxShadowConfigVerticalLength(10);
        FVBox30.setBoxShadowConfigBlurRadius(5);
        FVBox30.setBoxShadowConfigSpreadRadius(0);
        FVBox30.setBoxShadowConfigShadowColor("clBlack");
        FVBox30.setBoxShadowConfigOpacity(75);
        hBoxEndCob.addChildren(FVBox30);
        FVBox30.applyProperties();
    }

    public TFHBox FHBox3 = new TFHBox();

    private void init_FHBox3() {
        FHBox3.setName("FHBox3");
        FHBox3.setLeft(0);
        FHBox3.setTop(0);
        FHBox3.setWidth(317);
        FHBox3.setHeight(43);
        FHBox3.setBorderStyle("stNone");
        FHBox3.setPaddingTop(0);
        FHBox3.setPaddingLeft(0);
        FHBox3.setPaddingRight(0);
        FHBox3.setPaddingBottom(0);
        FHBox3.setMarginTop(0);
        FHBox3.setMarginLeft(0);
        FHBox3.setMarginRight(0);
        FHBox3.setMarginBottom(0);
        FHBox3.setSpacing(1);
        FHBox3.setFlexVflex("ftFalse");
        FHBox3.setFlexHflex("ftTrue");
        FHBox3.setScrollable(false);
        FHBox3.setBoxShadowConfigHorizontalLength(10);
        FHBox3.setBoxShadowConfigVerticalLength(10);
        FHBox3.setBoxShadowConfigBlurRadius(5);
        FHBox3.setBoxShadowConfigSpreadRadius(0);
        FHBox3.setBoxShadowConfigShadowColor("clBlack");
        FHBox3.setBoxShadowConfigOpacity(75);
        FHBox3.setVAlign("tvTop");
        FVBox30.addChildren(FHBox3);
        FHBox3.applyProperties();
    }

    public TFVBox vBoxUFEndCob = new TFVBox();

    private void init_vBoxUFEndCob() {
        vBoxUFEndCob.setName("vBoxUFEndCob");
        vBoxUFEndCob.setLeft(0);
        vBoxUFEndCob.setTop(0);
        vBoxUFEndCob.setWidth(28);
        vBoxUFEndCob.setHeight(41);
        vBoxUFEndCob.setBorderStyle("stNone");
        vBoxUFEndCob.setPaddingTop(0);
        vBoxUFEndCob.setPaddingLeft(0);
        vBoxUFEndCob.setPaddingRight(0);
        vBoxUFEndCob.setPaddingBottom(0);
        vBoxUFEndCob.setMarginTop(0);
        vBoxUFEndCob.setMarginLeft(0);
        vBoxUFEndCob.setMarginRight(0);
        vBoxUFEndCob.setMarginBottom(0);
        vBoxUFEndCob.setSpacing(3);
        vBoxUFEndCob.setFlexVflex("ftFalse");
        vBoxUFEndCob.setFlexHflex("ftFalse");
        vBoxUFEndCob.setScrollable(false);
        vBoxUFEndCob.setBoxShadowConfigHorizontalLength(10);
        vBoxUFEndCob.setBoxShadowConfigVerticalLength(10);
        vBoxUFEndCob.setBoxShadowConfigBlurRadius(5);
        vBoxUFEndCob.setBoxShadowConfigSpreadRadius(0);
        vBoxUFEndCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxUFEndCob.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(vBoxUFEndCob);
        vBoxUFEndCob.applyProperties();
    }

    public TFLabel lblUFCobCad = new TFLabel();

    private void init_lblUFCobCad() {
        lblUFCobCad.setName("lblUFCobCad");
        lblUFCobCad.setLeft(0);
        lblUFCobCad.setTop(0);
        lblUFCobCad.setWidth(120);
        lblUFCobCad.setHeight(13);
        lblUFCobCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFCobCad.setFontColor("clRed");
        lblUFCobCad.setFontSize(-11);
        lblUFCobCad.setFontName("Tahoma");
        lblUFCobCad.setFontStyle("[]");
        lblUFCobCad.setFieldName("UF_COBRANCA");
        lblUFCobCad.setTable(tbClientesEndereco);
        lblUFCobCad.setVerticalAlignment("taVerticalCenter");
        lblUFCobCad.setWordBreak(false);
        vBoxUFEndCob.addChildren(lblUFCobCad);
        lblUFCobCad.applyProperties();
    }

    public TFLabel lblUFCobCorreios = new TFLabel();

    private void init_lblUFCobCorreios() {
        lblUFCobCorreios.setName("lblUFCobCorreios");
        lblUFCobCorreios.setLeft(0);
        lblUFCobCorreios.setTop(14);
        lblUFCobCorreios.setWidth(120);
        lblUFCobCorreios.setHeight(13);
        lblUFCobCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFCobCorreios.setFontColor("13392431");
        lblUFCobCorreios.setFontSize(-11);
        lblUFCobCorreios.setFontName("Tahoma");
        lblUFCobCorreios.setFontStyle("[]");
        lblUFCobCorreios.setFieldName("UF_COBRANCA");
        lblUFCobCorreios.setTable(tbClientesEnderecoTemp);
        lblUFCobCorreios.setVerticalAlignment("taVerticalCenter");
        lblUFCobCorreios.setWordBreak(false);
        vBoxUFEndCob.addChildren(lblUFCobCorreios);
        lblUFCobCorreios.applyProperties();
    }

    public TFVBox FVBox28 = new TFVBox();

    private void init_FVBox28() {
        FVBox28.setName("FVBox28");
        FVBox28.setLeft(28);
        FVBox28.setTop(0);
        FVBox28.setWidth(107);
        FVBox28.setHeight(43);
        FVBox28.setBorderStyle("stNone");
        FVBox28.setPaddingTop(0);
        FVBox28.setPaddingLeft(0);
        FVBox28.setPaddingRight(0);
        FVBox28.setPaddingBottom(0);
        FVBox28.setMarginTop(0);
        FVBox28.setMarginLeft(0);
        FVBox28.setMarginRight(0);
        FVBox28.setMarginBottom(0);
        FVBox28.setSpacing(3);
        FVBox28.setFlexVflex("ftFalse");
        FVBox28.setFlexHflex("ftTrue");
        FVBox28.setScrollable(false);
        FVBox28.setBoxShadowConfigHorizontalLength(10);
        FVBox28.setBoxShadowConfigVerticalLength(10);
        FVBox28.setBoxShadowConfigBlurRadius(5);
        FVBox28.setBoxShadowConfigSpreadRadius(0);
        FVBox28.setBoxShadowConfigShadowColor("clBlack");
        FVBox28.setBoxShadowConfigOpacity(75);
        FHBox3.addChildren(FVBox28);
        FVBox28.applyProperties();
    }

    public TFLabel lblCidadeCobCad = new TFLabel();

    private void init_lblCidadeCobCad() {
        lblCidadeCobCad.setName("lblCidadeCobCad");
        lblCidadeCobCad.setLeft(0);
        lblCidadeCobCad.setTop(0);
        lblCidadeCobCad.setWidth(120);
        lblCidadeCobCad.setHeight(13);
        lblCidadeCobCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeCobCad.setFontColor("clRed");
        lblCidadeCobCad.setFontSize(-11);
        lblCidadeCobCad.setFontName("Tahoma");
        lblCidadeCobCad.setFontStyle("[]");
        lblCidadeCobCad.setFieldName("CIDADE_COB");
        lblCidadeCobCad.setTable(tbClientesEndereco);
        lblCidadeCobCad.setVerticalAlignment("taVerticalCenter");
        lblCidadeCobCad.setWordBreak(false);
        FVBox28.addChildren(lblCidadeCobCad);
        lblCidadeCobCad.applyProperties();
    }

    public TFLabel lblCidadeCobCorreios = new TFLabel();

    private void init_lblCidadeCobCorreios() {
        lblCidadeCobCorreios.setName("lblCidadeCobCorreios");
        lblCidadeCobCorreios.setLeft(0);
        lblCidadeCobCorreios.setTop(14);
        lblCidadeCobCorreios.setWidth(120);
        lblCidadeCobCorreios.setHeight(13);
        lblCidadeCobCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeCobCorreios.setFontColor("13392431");
        lblCidadeCobCorreios.setFontSize(-11);
        lblCidadeCobCorreios.setFontName("Tahoma");
        lblCidadeCobCorreios.setFontStyle("[]");
        lblCidadeCobCorreios.setFieldName("CIDADE_COB");
        lblCidadeCobCorreios.setTable(tbClientesEnderecoTemp);
        lblCidadeCobCorreios.setVerticalAlignment("taVerticalCenter");
        lblCidadeCobCorreios.setWordBreak(false);
        FVBox28.addChildren(lblCidadeCobCorreios);
        lblCidadeCobCorreios.applyProperties();
    }

    public TFVBox vBoxBairroCob = new TFVBox();

    private void init_vBoxBairroCob() {
        vBoxBairroCob.setName("vBoxBairroCob");
        vBoxBairroCob.setLeft(0);
        vBoxBairroCob.setTop(44);
        vBoxBairroCob.setWidth(120);
        vBoxBairroCob.setHeight(37);
        vBoxBairroCob.setBorderStyle("stNone");
        vBoxBairroCob.setPaddingTop(0);
        vBoxBairroCob.setPaddingLeft(2);
        vBoxBairroCob.setPaddingRight(0);
        vBoxBairroCob.setPaddingBottom(0);
        vBoxBairroCob.setMarginTop(0);
        vBoxBairroCob.setMarginLeft(0);
        vBoxBairroCob.setMarginRight(0);
        vBoxBairroCob.setMarginBottom(0);
        vBoxBairroCob.setSpacing(3);
        vBoxBairroCob.setFlexVflex("ftMin");
        vBoxBairroCob.setFlexHflex("ftTrue");
        vBoxBairroCob.setScrollable(false);
        vBoxBairroCob.setBoxShadowConfigHorizontalLength(10);
        vBoxBairroCob.setBoxShadowConfigVerticalLength(10);
        vBoxBairroCob.setBoxShadowConfigBlurRadius(5);
        vBoxBairroCob.setBoxShadowConfigSpreadRadius(0);
        vBoxBairroCob.setBoxShadowConfigShadowColor("clBlack");
        vBoxBairroCob.setBoxShadowConfigOpacity(75);
        FVBox30.addChildren(vBoxBairroCob);
        vBoxBairroCob.applyProperties();
    }

    public TFLabel lblBairroCobCad = new TFLabel();

    private void init_lblBairroCobCad() {
        lblBairroCobCad.setName("lblBairroCobCad");
        lblBairroCobCad.setLeft(0);
        lblBairroCobCad.setTop(0);
        lblBairroCobCad.setWidth(120);
        lblBairroCobCad.setHeight(13);
        lblBairroCobCad.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroCobCad.setFontColor("clRed");
        lblBairroCobCad.setFontSize(-11);
        lblBairroCobCad.setFontName("Tahoma");
        lblBairroCobCad.setFontStyle("[]");
        lblBairroCobCad.setFieldName("BAIRRO_COBRANCA");
        lblBairroCobCad.setTable(tbClientesEndereco);
        lblBairroCobCad.setVerticalAlignment("taVerticalCenter");
        lblBairroCobCad.setWordBreak(false);
        vBoxBairroCob.addChildren(lblBairroCobCad);
        lblBairroCobCad.applyProperties();
    }

    public TFLabel lblBairroCobCorreios = new TFLabel();

    private void init_lblBairroCobCorreios() {
        lblBairroCobCorreios.setName("lblBairroCobCorreios");
        lblBairroCobCorreios.setLeft(0);
        lblBairroCobCorreios.setTop(14);
        lblBairroCobCorreios.setWidth(120);
        lblBairroCobCorreios.setHeight(13);
        lblBairroCobCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroCobCorreios.setFontColor("13392431");
        lblBairroCobCorreios.setFontSize(-11);
        lblBairroCobCorreios.setFontName("Tahoma");
        lblBairroCobCorreios.setFontStyle("[]");
        lblBairroCobCorreios.setFieldName("BAIRRO_COBRANCA");
        lblBairroCobCorreios.setTable(tbClientesEnderecoTemp);
        lblBairroCobCorreios.setVerticalAlignment("taVerticalCenter");
        lblBairroCobCorreios.setWordBreak(false);
        vBoxBairroCob.addChildren(lblBairroCobCorreios);
        lblBairroCobCorreios.applyProperties();
    }

    public TFVBox FVBox33 = new TFVBox();

    private void init_FVBox33() {
        FVBox33.setName("FVBox33");
        FVBox33.setLeft(0);
        FVBox33.setTop(93);
        FVBox33.setWidth(317);
        FVBox33.setHeight(77);
        FVBox33.setBorderStyle("stNone");
        FVBox33.setPaddingTop(0);
        FVBox33.setPaddingLeft(55);
        FVBox33.setPaddingRight(0);
        FVBox33.setPaddingBottom(0);
        FVBox33.setMarginTop(0);
        FVBox33.setMarginLeft(0);
        FVBox33.setMarginRight(0);
        FVBox33.setMarginBottom(0);
        FVBox33.setSpacing(3);
        FVBox33.setFlexVflex("ftMin");
        FVBox33.setFlexHflex("ftTrue");
        FVBox33.setScrollable(false);
        FVBox33.setBoxShadowConfigHorizontalLength(10);
        FVBox33.setBoxShadowConfigVerticalLength(10);
        FVBox33.setBoxShadowConfigBlurRadius(5);
        FVBox33.setBoxShadowConfigSpreadRadius(0);
        FVBox33.setBoxShadowConfigShadowColor("clBlack");
        FVBox33.setBoxShadowConfigOpacity(75);
        vBoxEnderecoCob.addChildren(FVBox33);
        FVBox33.applyProperties();
    }

    public TFLabel FLabel24 = new TFLabel();

    private void init_FLabel24() {
        FLabel24.setName("FLabel24");
        FLabel24.setLeft(0);
        FLabel24.setTop(0);
        FLabel24.setWidth(65);
        FLabel24.setHeight(13);
        FLabel24.setCaption("Complemento");
        FLabel24.setFontColor("clWindowText");
        FLabel24.setFontSize(-11);
        FLabel24.setFontName("Tahoma");
        FLabel24.setFontStyle("[]");
        FLabel24.setVerticalAlignment("taVerticalCenter");
        FLabel24.setWordBreak(false);
        FVBox33.addChildren(FLabel24);
        FLabel24.applyProperties();
    }

    public TFHBox hBoxComplementoCob = new TFHBox();

    private void init_hBoxComplementoCob() {
        hBoxComplementoCob.setName("hBoxComplementoCob");
        hBoxComplementoCob.setLeft(0);
        hBoxComplementoCob.setTop(14);
        hBoxComplementoCob.setWidth(274);
        hBoxComplementoCob.setHeight(35);
        hBoxComplementoCob.setBorderStyle("stNone");
        hBoxComplementoCob.setPaddingTop(0);
        hBoxComplementoCob.setPaddingLeft(0);
        hBoxComplementoCob.setPaddingRight(0);
        hBoxComplementoCob.setPaddingBottom(0);
        hBoxComplementoCob.setMarginTop(0);
        hBoxComplementoCob.setMarginLeft(0);
        hBoxComplementoCob.setMarginRight(0);
        hBoxComplementoCob.setMarginBottom(0);
        hBoxComplementoCob.setSpacing(3);
        hBoxComplementoCob.setFlexVflex("ftFalse");
        hBoxComplementoCob.setFlexHflex("ftTrue");
        hBoxComplementoCob.setScrollable(false);
        hBoxComplementoCob.setBoxShadowConfigHorizontalLength(10);
        hBoxComplementoCob.setBoxShadowConfigVerticalLength(10);
        hBoxComplementoCob.setBoxShadowConfigBlurRadius(5);
        hBoxComplementoCob.setBoxShadowConfigSpreadRadius(0);
        hBoxComplementoCob.setBoxShadowConfigShadowColor("clBlack");
        hBoxComplementoCob.setBoxShadowConfigOpacity(75);
        hBoxComplementoCob.setVAlign("tvTop");
        FVBox33.addChildren(hBoxComplementoCob);
        hBoxComplementoCob.applyProperties();
    }

    public TFString edComplementoCobCad = new TFString();

    private void init_edComplementoCobCad() {
        edComplementoCobCad.setName("edComplementoCobCad");
        edComplementoCobCad.setLeft(0);
        edComplementoCobCad.setTop(0);
        edComplementoCobCad.setWidth(83);
        edComplementoCobCad.setHeight(21);
        edComplementoCobCad.setTable(tbClientesEndereco);
        edComplementoCobCad.setFieldName("COMPLEMENTO_COBRANCA");
        edComplementoCobCad.setFlex(true);
        edComplementoCobCad.setRequired(false);
        edComplementoCobCad.setConstraintCheckWhen("cwImmediate");
        edComplementoCobCad.setConstraintCheckType("ctExpression");
        edComplementoCobCad.setConstraintFocusOnError(false);
        edComplementoCobCad.setConstraintEnableUI(true);
        edComplementoCobCad.setConstraintEnabled(false);
        edComplementoCobCad.setConstraintFormCheck(true);
        edComplementoCobCad.setCharCase("ccNormal");
        edComplementoCobCad.setPwd(false);
        edComplementoCobCad.setMaxlength(30);
        edComplementoCobCad.setFontColor("clWindowText");
        edComplementoCobCad.setFontSize(-11);
        edComplementoCobCad.setFontName("Tahoma");
        edComplementoCobCad.setFontStyle("[]");
        edComplementoCobCad.setSaveLiteralCharacter(false);
        edComplementoCobCad.applyProperties();
        hBoxComplementoCob.addChildren(edComplementoCobCad);
        addValidatable(edComplementoCobCad);
    }

    public TFVBox FVBox50 = new TFVBox();

    private void init_FVBox50() {
        FVBox50.setName("FVBox50");
        FVBox50.setLeft(83);
        FVBox50.setTop(0);
        FVBox50.setWidth(12);
        FVBox50.setHeight(30);
        FVBox50.setBorderStyle("stNone");
        FVBox50.setPaddingTop(0);
        FVBox50.setPaddingLeft(0);
        FVBox50.setPaddingRight(0);
        FVBox50.setPaddingBottom(0);
        FVBox50.setMarginTop(0);
        FVBox50.setMarginLeft(0);
        FVBox50.setMarginRight(0);
        FVBox50.setMarginBottom(0);
        FVBox50.setSpacing(1);
        FVBox50.setFlexVflex("ftFalse");
        FVBox50.setFlexHflex("ftFalse");
        FVBox50.setScrollable(false);
        FVBox50.setBoxShadowConfigHorizontalLength(10);
        FVBox50.setBoxShadowConfigVerticalLength(10);
        FVBox50.setBoxShadowConfigBlurRadius(5);
        FVBox50.setBoxShadowConfigSpreadRadius(0);
        FVBox50.setBoxShadowConfigShadowColor("clBlack");
        FVBox50.setBoxShadowConfigOpacity(75);
        hBoxComplementoCob.addChildren(FVBox50);
        FVBox50.applyProperties();
    }

    public TFLabel lblComplementoCobCorreios = new TFLabel();

    private void init_lblComplementoCobCorreios() {
        lblComplementoCobCorreios.setName("lblComplementoCobCorreios");
        lblComplementoCobCorreios.setLeft(0);
        lblComplementoCobCorreios.setTop(50);
        lblComplementoCobCorreios.setWidth(120);
        lblComplementoCobCorreios.setHeight(13);
        lblComplementoCobCorreios.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblComplementoCobCorreios.setFontColor("13392431");
        lblComplementoCobCorreios.setFontSize(-11);
        lblComplementoCobCorreios.setFontName("Tahoma");
        lblComplementoCobCorreios.setFontStyle("[]");
        lblComplementoCobCorreios.setFieldName("COMPLEMENTO_COBRANCA");
        lblComplementoCobCorreios.setTable(tbClientesEnderecoTemp);
        lblComplementoCobCorreios.setVerticalAlignment("taVerticalCenter");
        lblComplementoCobCorreios.setWordBreak(false);
        FVBox33.addChildren(lblComplementoCobCorreios);
        lblComplementoCobCorreios.applyProperties();
    }

    public TFVBox FVBox52 = new TFVBox();

    private void init_FVBox52() {
        FVBox52.setName("FVBox52");
        FVBox52.setLeft(0);
        FVBox52.setTop(64);
        FVBox52.setWidth(117);
        FVBox52.setHeight(9);
        FVBox52.setBorderStyle("stNone");
        FVBox52.setPaddingTop(0);
        FVBox52.setPaddingLeft(0);
        FVBox52.setPaddingRight(0);
        FVBox52.setPaddingBottom(0);
        FVBox52.setMarginTop(0);
        FVBox52.setMarginLeft(0);
        FVBox52.setMarginRight(0);
        FVBox52.setMarginBottom(0);
        FVBox52.setSpacing(1);
        FVBox52.setFlexVflex("ftFalse");
        FVBox52.setFlexHflex("ftFalse");
        FVBox52.setScrollable(false);
        FVBox52.setBoxShadowConfigHorizontalLength(10);
        FVBox52.setBoxShadowConfigVerticalLength(10);
        FVBox52.setBoxShadowConfigBlurRadius(5);
        FVBox52.setBoxShadowConfigSpreadRadius(0);
        FVBox52.setBoxShadowConfigShadowColor("clBlack");
        FVBox52.setBoxShadowConfigOpacity(75);
        FVBox33.addChildren(FVBox52);
        FVBox52.applyProperties();
    }

    public TFHBox hBoxTitEndIE = new TFHBox();

    private void init_hBoxTitEndIE() {
        hBoxTitEndIE.setName("hBoxTitEndIE");
        hBoxTitEndIE.setLeft(0);
        hBoxTitEndIE.setTop(586);
        hBoxTitEndIE.setWidth(503);
        hBoxTitEndIE.setHeight(51);
        hBoxTitEndIE.setBorderStyle("stNone");
        hBoxTitEndIE.setPaddingTop(0);
        hBoxTitEndIE.setPaddingLeft(0);
        hBoxTitEndIE.setPaddingRight(0);
        hBoxTitEndIE.setPaddingBottom(0);
        hBoxTitEndIE.setMarginTop(0);
        hBoxTitEndIE.setMarginLeft(0);
        hBoxTitEndIE.setMarginRight(0);
        hBoxTitEndIE.setMarginBottom(0);
        hBoxTitEndIE.setSpacing(1);
        hBoxTitEndIE.setFlexVflex("ftFalse");
        hBoxTitEndIE.setFlexHflex("ftTrue");
        hBoxTitEndIE.setScrollable(false);
        hBoxTitEndIE.setBoxShadowConfigHorizontalLength(10);
        hBoxTitEndIE.setBoxShadowConfigVerticalLength(10);
        hBoxTitEndIE.setBoxShadowConfigBlurRadius(5);
        hBoxTitEndIE.setBoxShadowConfigSpreadRadius(0);
        hBoxTitEndIE.setBoxShadowConfigShadowColor("clBlack");
        hBoxTitEndIE.setBoxShadowConfigOpacity(75);
        hBoxTitEndIE.setVAlign("tvTop");
        vBoxEnderecos.addChildren(hBoxTitEndIE);
        hBoxTitEndIE.applyProperties();
    }

    public TFVBox vBoxlblEndIE = new TFVBox();

    private void init_vBoxlblEndIE() {
        vBoxlblEndIE.setName("vBoxlblEndIE");
        vBoxlblEndIE.setLeft(0);
        vBoxlblEndIE.setTop(0);
        vBoxlblEndIE.setWidth(499);
        vBoxlblEndIE.setHeight(45);
        vBoxlblEndIE.setBorderStyle("stNone");
        vBoxlblEndIE.setPaddingTop(10);
        vBoxlblEndIE.setPaddingLeft(0);
        vBoxlblEndIE.setPaddingRight(0);
        vBoxlblEndIE.setPaddingBottom(0);
        vBoxlblEndIE.setMarginTop(0);
        vBoxlblEndIE.setMarginLeft(0);
        vBoxlblEndIE.setMarginRight(0);
        vBoxlblEndIE.setMarginBottom(0);
        vBoxlblEndIE.setSpacing(1);
        vBoxlblEndIE.setFlexVflex("ftFalse");
        vBoxlblEndIE.setFlexHflex("ftTrue");
        vBoxlblEndIE.setScrollable(false);
        vBoxlblEndIE.setBoxShadowConfigHorizontalLength(10);
        vBoxlblEndIE.setBoxShadowConfigVerticalLength(10);
        vBoxlblEndIE.setBoxShadowConfigBlurRadius(5);
        vBoxlblEndIE.setBoxShadowConfigSpreadRadius(0);
        vBoxlblEndIE.setBoxShadowConfigShadowColor("clBlack");
        vBoxlblEndIE.setBoxShadowConfigOpacity(75);
        hBoxTitEndIE.addChildren(vBoxlblEndIE);
        vBoxlblEndIE.applyProperties();
    }

    public TFHBox hBoxlblEndIE = new TFHBox();

    private void init_hBoxlblEndIE() {
        hBoxlblEndIE.setName("hBoxlblEndIE");
        hBoxlblEndIE.setLeft(0);
        hBoxlblEndIE.setTop(0);
        hBoxlblEndIE.setWidth(495);
        hBoxlblEndIE.setHeight(40);
        hBoxlblEndIE.setBorderStyle("stNone");
        hBoxlblEndIE.setPaddingTop(3);
        hBoxlblEndIE.setPaddingLeft(0);
        hBoxlblEndIE.setPaddingRight(0);
        hBoxlblEndIE.setPaddingBottom(0);
        hBoxlblEndIE.setMarginTop(0);
        hBoxlblEndIE.setMarginLeft(0);
        hBoxlblEndIE.setMarginRight(0);
        hBoxlblEndIE.setMarginBottom(0);
        hBoxlblEndIE.setSpacing(0);
        hBoxlblEndIE.setFlexVflex("ftTrue");
        hBoxlblEndIE.setFlexHflex("ftTrue");
        hBoxlblEndIE.setScrollable(false);
        hBoxlblEndIE.setBoxShadowConfigHorizontalLength(10);
        hBoxlblEndIE.setBoxShadowConfigVerticalLength(10);
        hBoxlblEndIE.setBoxShadowConfigBlurRadius(5);
        hBoxlblEndIE.setBoxShadowConfigSpreadRadius(0);
        hBoxlblEndIE.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblEndIE.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE.setVAlign("tvTop");
        vBoxlblEndIE.addChildren(hBoxlblEndIE);
        hBoxlblEndIE.applyProperties();
    }

    public TFVBox FVBox7 = new TFVBox();

    private void init_FVBox7() {
        FVBox7.setName("FVBox7");
        FVBox7.setLeft(0);
        FVBox7.setTop(0);
        FVBox7.setWidth(7);
        FVBox7.setHeight(20);
        FVBox7.setBorderStyle("stNone");
        FVBox7.setPaddingTop(0);
        FVBox7.setPaddingLeft(0);
        FVBox7.setPaddingRight(0);
        FVBox7.setPaddingBottom(0);
        FVBox7.setMarginTop(0);
        FVBox7.setMarginLeft(0);
        FVBox7.setMarginRight(0);
        FVBox7.setMarginBottom(0);
        FVBox7.setSpacing(1);
        FVBox7.setFlexVflex("ftTrue");
        FVBox7.setFlexHflex("ftFalse");
        FVBox7.setScrollable(false);
        FVBox7.setBoxShadowConfigHorizontalLength(10);
        FVBox7.setBoxShadowConfigVerticalLength(10);
        FVBox7.setBoxShadowConfigBlurRadius(5);
        FVBox7.setBoxShadowConfigSpreadRadius(0);
        FVBox7.setBoxShadowConfigShadowColor("clBlack");
        FVBox7.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE.addChildren(FVBox7);
        FVBox7.applyProperties();
    }

    public TFHBox hBoxlblEndIE02 = new TFHBox();

    private void init_hBoxlblEndIE02() {
        hBoxlblEndIE02.setName("hBoxlblEndIE02");
        hBoxlblEndIE02.setLeft(7);
        hBoxlblEndIE02.setTop(0);
        hBoxlblEndIE02.setWidth(159);
        hBoxlblEndIE02.setHeight(32);
        hBoxlblEndIE02.setBorderStyle("stNone");
        hBoxlblEndIE02.setPaddingTop(5);
        hBoxlblEndIE02.setPaddingLeft(0);
        hBoxlblEndIE02.setPaddingRight(0);
        hBoxlblEndIE02.setPaddingBottom(0);
        hBoxlblEndIE02.setMarginTop(0);
        hBoxlblEndIE02.setMarginLeft(0);
        hBoxlblEndIE02.setMarginRight(0);
        hBoxlblEndIE02.setMarginBottom(0);
        hBoxlblEndIE02.setSpacing(0);
        hBoxlblEndIE02.setFlexVflex("ftTrue");
        hBoxlblEndIE02.setFlexHflex("ftFalse");
        hBoxlblEndIE02.setScrollable(false);
        hBoxlblEndIE02.setBoxShadowConfigHorizontalLength(10);
        hBoxlblEndIE02.setBoxShadowConfigVerticalLength(10);
        hBoxlblEndIE02.setBoxShadowConfigBlurRadius(5);
        hBoxlblEndIE02.setBoxShadowConfigSpreadRadius(0);
        hBoxlblEndIE02.setBoxShadowConfigShadowColor("clBlack");
        hBoxlblEndIE02.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE02.setVAlign("tvTop");
        hBoxlblEndIE.addChildren(hBoxlblEndIE02);
        hBoxlblEndIE02.applyProperties();
    }

    public TFVBox FVBox9 = new TFVBox();

    private void init_FVBox9() {
        FVBox9.setName("FVBox9");
        FVBox9.setLeft(0);
        FVBox9.setTop(0);
        FVBox9.setWidth(7);
        FVBox9.setHeight(20);
        FVBox9.setBorderStyle("stNone");
        FVBox9.setPaddingTop(0);
        FVBox9.setPaddingLeft(0);
        FVBox9.setPaddingRight(0);
        FVBox9.setPaddingBottom(0);
        FVBox9.setMarginTop(0);
        FVBox9.setMarginLeft(0);
        FVBox9.setMarginRight(0);
        FVBox9.setMarginBottom(0);
        FVBox9.setSpacing(1);
        FVBox9.setFlexVflex("ftTrue");
        FVBox9.setFlexHflex("ftTrue");
        FVBox9.setScrollable(false);
        FVBox9.setBoxShadowConfigHorizontalLength(10);
        FVBox9.setBoxShadowConfigVerticalLength(10);
        FVBox9.setBoxShadowConfigBlurRadius(5);
        FVBox9.setBoxShadowConfigSpreadRadius(0);
        FVBox9.setBoxShadowConfigShadowColor("clBlack");
        FVBox9.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE02.addChildren(FVBox9);
        FVBox9.applyProperties();
    }

    public TFLabel lblEndIE = new TFLabel();

    private void init_lblEndIE() {
        lblEndIE.setName("lblEndIE");
        lblEndIE.setLeft(7);
        lblEndIE.setTop(0);
        lblEndIE.setWidth(127);
        lblEndIE.setHeight(19);
        lblEndIE.setCaption("Inscri\u00E7\u00E3o Estadual");
        lblEndIE.setColor("clBtnFace");
        lblEndIE.setFontColor("13392431");
        lblEndIE.setFontSize(-16);
        lblEndIE.setFontName("Tahoma");
        lblEndIE.setFontStyle("[]");
        lblEndIE.setVerticalAlignment("taVerticalCenter");
        lblEndIE.setWordBreak(false);
        hBoxlblEndIE02.addChildren(lblEndIE);
        lblEndIE.applyProperties();
    }

    public TFVBox FVBox35 = new TFVBox();

    private void init_FVBox35() {
        FVBox35.setName("FVBox35");
        FVBox35.setLeft(134);
        FVBox35.setTop(0);
        FVBox35.setWidth(7);
        FVBox35.setHeight(20);
        FVBox35.setBorderStyle("stNone");
        FVBox35.setPaddingTop(0);
        FVBox35.setPaddingLeft(0);
        FVBox35.setPaddingRight(0);
        FVBox35.setPaddingBottom(0);
        FVBox35.setMarginTop(0);
        FVBox35.setMarginLeft(0);
        FVBox35.setMarginRight(0);
        FVBox35.setMarginBottom(0);
        FVBox35.setSpacing(1);
        FVBox35.setFlexVflex("ftTrue");
        FVBox35.setFlexHflex("ftTrue");
        FVBox35.setScrollable(false);
        FVBox35.setBoxShadowConfigHorizontalLength(10);
        FVBox35.setBoxShadowConfigVerticalLength(10);
        FVBox35.setBoxShadowConfigBlurRadius(5);
        FVBox35.setBoxShadowConfigSpreadRadius(0);
        FVBox35.setBoxShadowConfigShadowColor("clBlack");
        FVBox35.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE02.addChildren(FVBox35);
        FVBox35.applyProperties();
    }

    public TFVBox FVBox36 = new TFVBox();

    private void init_FVBox36() {
        FVBox36.setName("FVBox36");
        FVBox36.setLeft(166);
        FVBox36.setTop(0);
        FVBox36.setWidth(7);
        FVBox36.setHeight(20);
        FVBox36.setBorderStyle("stNone");
        FVBox36.setPaddingTop(0);
        FVBox36.setPaddingLeft(0);
        FVBox36.setPaddingRight(0);
        FVBox36.setPaddingBottom(0);
        FVBox36.setMarginTop(0);
        FVBox36.setMarginLeft(0);
        FVBox36.setMarginRight(0);
        FVBox36.setMarginBottom(0);
        FVBox36.setSpacing(1);
        FVBox36.setFlexVflex("ftTrue");
        FVBox36.setFlexHflex("ftTrue");
        FVBox36.setScrollable(false);
        FVBox36.setBoxShadowConfigHorizontalLength(10);
        FVBox36.setBoxShadowConfigVerticalLength(10);
        FVBox36.setBoxShadowConfigBlurRadius(5);
        FVBox36.setBoxShadowConfigSpreadRadius(0);
        FVBox36.setBoxShadowConfigShadowColor("clBlack");
        FVBox36.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE.addChildren(FVBox36);
        FVBox36.applyProperties();
    }

    public TFVBox vBoxOptEndIE = new TFVBox();

    private void init_vBoxOptEndIE() {
        vBoxOptEndIE.setName("vBoxOptEndIE");
        vBoxOptEndIE.setLeft(173);
        vBoxOptEndIE.setTop(0);
        vBoxOptEndIE.setWidth(204);
        vBoxOptEndIE.setHeight(35);
        vBoxOptEndIE.setBorderStyle("stNone");
        vBoxOptEndIE.setPaddingTop(0);
        vBoxOptEndIE.setPaddingLeft(0);
        vBoxOptEndIE.setPaddingRight(0);
        vBoxOptEndIE.setPaddingBottom(0);
        vBoxOptEndIE.setVisible(false);
        vBoxOptEndIE.setMarginTop(0);
        vBoxOptEndIE.setMarginLeft(0);
        vBoxOptEndIE.setMarginRight(0);
        vBoxOptEndIE.setMarginBottom(0);
        vBoxOptEndIE.setSpacing(1);
        vBoxOptEndIE.setFlexVflex("ftFalse");
        vBoxOptEndIE.setFlexHflex("ftFalse");
        vBoxOptEndIE.setScrollable(false);
        vBoxOptEndIE.setBoxShadowConfigHorizontalLength(10);
        vBoxOptEndIE.setBoxShadowConfigVerticalLength(10);
        vBoxOptEndIE.setBoxShadowConfigBlurRadius(5);
        vBoxOptEndIE.setBoxShadowConfigSpreadRadius(0);
        vBoxOptEndIE.setBoxShadowConfigShadowColor("clBlack");
        vBoxOptEndIE.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE.addChildren(vBoxOptEndIE);
        vBoxOptEndIE.applyProperties();
    }

    public TFHBox FHBox12 = new TFHBox();

    private void init_FHBox12() {
        FHBox12.setName("FHBox12");
        FHBox12.setLeft(0);
        FHBox12.setTop(0);
        FHBox12.setWidth(199);
        FHBox12.setHeight(30);
        FHBox12.setBorderStyle("stNone");
        FHBox12.setPaddingTop(0);
        FHBox12.setPaddingLeft(0);
        FHBox12.setPaddingRight(0);
        FHBox12.setPaddingBottom(0);
        FHBox12.setMarginTop(0);
        FHBox12.setMarginLeft(0);
        FHBox12.setMarginRight(0);
        FHBox12.setMarginBottom(0);
        FHBox12.setSpacing(1);
        FHBox12.setFlexVflex("ftFalse");
        FHBox12.setFlexHflex("ftTrue");
        FHBox12.setScrollable(false);
        FHBox12.setBoxShadowConfigHorizontalLength(10);
        FHBox12.setBoxShadowConfigVerticalLength(10);
        FHBox12.setBoxShadowConfigBlurRadius(5);
        FHBox12.setBoxShadowConfigSpreadRadius(0);
        FHBox12.setBoxShadowConfigShadowColor("clBlack");
        FHBox12.setBoxShadowConfigOpacity(75);
        FHBox12.setVAlign("tvTop");
        vBoxOptEndIE.addChildren(FHBox12);
        FHBox12.applyProperties();
    }

    public TFHBox hbCEPOnLineAceitarIE = new TFHBox();

    private void init_hbCEPOnLineAceitarIE() {
        hbCEPOnLineAceitarIE.setName("hbCEPOnLineAceitarIE");
        hbCEPOnLineAceitarIE.setLeft(0);
        hbCEPOnLineAceitarIE.setTop(0);
        hbCEPOnLineAceitarIE.setWidth(98);
        hbCEPOnLineAceitarIE.setHeight(25);
        hbCEPOnLineAceitarIE.setBorderStyle("stNone");
        hbCEPOnLineAceitarIE.setColor("clSilver");
        hbCEPOnLineAceitarIE.setPaddingTop(5);
        hbCEPOnLineAceitarIE.setPaddingLeft(0);
        hbCEPOnLineAceitarIE.setPaddingRight(0);
        hbCEPOnLineAceitarIE.setPaddingBottom(0);
        hbCEPOnLineAceitarIE.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineAceitarIEClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineAceitarIE", "OnClick");
        });
        hbCEPOnLineAceitarIE.setMarginTop(0);
        hbCEPOnLineAceitarIE.setMarginLeft(0);
        hbCEPOnLineAceitarIE.setMarginRight(0);
        hbCEPOnLineAceitarIE.setMarginBottom(0);
        hbCEPOnLineAceitarIE.setSpacing(0);
        hbCEPOnLineAceitarIE.setFlexVflex("ftTrue");
        hbCEPOnLineAceitarIE.setFlexHflex("ftTrue");
        hbCEPOnLineAceitarIE.setScrollable(false);
        hbCEPOnLineAceitarIE.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineAceitarIE.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineAceitarIE.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineAceitarIE.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineAceitarIE.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineAceitarIE.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarIE.setVAlign("tvTop");
        FHBox12.addChildren(hbCEPOnLineAceitarIE);
        hbCEPOnLineAceitarIE.applyProperties();
    }

    public TFVBox FVBox40 = new TFVBox();

    private void init_FVBox40() {
        FVBox40.setName("FVBox40");
        FVBox40.setLeft(0);
        FVBox40.setTop(0);
        FVBox40.setWidth(7);
        FVBox40.setHeight(20);
        FVBox40.setBorderStyle("stNone");
        FVBox40.setPaddingTop(0);
        FVBox40.setPaddingLeft(0);
        FVBox40.setPaddingRight(0);
        FVBox40.setPaddingBottom(0);
        FVBox40.setMarginTop(0);
        FVBox40.setMarginLeft(0);
        FVBox40.setMarginRight(0);
        FVBox40.setMarginBottom(0);
        FVBox40.setSpacing(1);
        FVBox40.setFlexVflex("ftTrue");
        FVBox40.setFlexHflex("ftTrue");
        FVBox40.setScrollable(false);
        FVBox40.setBoxShadowConfigHorizontalLength(10);
        FVBox40.setBoxShadowConfigVerticalLength(10);
        FVBox40.setBoxShadowConfigBlurRadius(5);
        FVBox40.setBoxShadowConfigSpreadRadius(0);
        FVBox40.setBoxShadowConfigShadowColor("clBlack");
        FVBox40.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarIE.addChildren(FVBox40);
        FVBox40.applyProperties();
    }

    public TFLabel lblAceitarIE = new TFLabel();

    private void init_lblAceitarIE() {
        lblAceitarIE.setName("lblAceitarIE");
        lblAceitarIE.setLeft(7);
        lblAceitarIE.setTop(0);
        lblAceitarIE.setWidth(76);
        lblAceitarIE.setHeight(14);
        lblAceitarIE.setCaption("Aceitar Todos");
        lblAceitarIE.setFontColor("clWindowText");
        lblAceitarIE.setFontSize(-12);
        lblAceitarIE.setFontName("Tahoma");
        lblAceitarIE.setFontStyle("[]");
        lblAceitarIE.setVerticalAlignment("taVerticalCenter");
        lblAceitarIE.setWordBreak(false);
        hbCEPOnLineAceitarIE.addChildren(lblAceitarIE);
        lblAceitarIE.applyProperties();
    }

    public TFVBox FVBox46 = new TFVBox();

    private void init_FVBox46() {
        FVBox46.setName("FVBox46");
        FVBox46.setLeft(83);
        FVBox46.setTop(0);
        FVBox46.setWidth(7);
        FVBox46.setHeight(19);
        FVBox46.setBorderStyle("stNone");
        FVBox46.setPaddingTop(0);
        FVBox46.setPaddingLeft(0);
        FVBox46.setPaddingRight(0);
        FVBox46.setPaddingBottom(0);
        FVBox46.setMarginTop(0);
        FVBox46.setMarginLeft(0);
        FVBox46.setMarginRight(0);
        FVBox46.setMarginBottom(0);
        FVBox46.setSpacing(1);
        FVBox46.setFlexVflex("ftTrue");
        FVBox46.setFlexHflex("ftTrue");
        FVBox46.setScrollable(false);
        FVBox46.setBoxShadowConfigHorizontalLength(10);
        FVBox46.setBoxShadowConfigVerticalLength(10);
        FVBox46.setBoxShadowConfigBlurRadius(5);
        FVBox46.setBoxShadowConfigSpreadRadius(0);
        FVBox46.setBoxShadowConfigShadowColor("clBlack");
        FVBox46.setBoxShadowConfigOpacity(75);
        hbCEPOnLineAceitarIE.addChildren(FVBox46);
        FVBox46.applyProperties();
    }

    public TFHBox hbCEPOnLineManterIE = new TFHBox();

    private void init_hbCEPOnLineManterIE() {
        hbCEPOnLineManterIE.setName("hbCEPOnLineManterIE");
        hbCEPOnLineManterIE.setLeft(98);
        hbCEPOnLineManterIE.setTop(0);
        hbCEPOnLineManterIE.setWidth(96);
        hbCEPOnLineManterIE.setHeight(25);
        hbCEPOnLineManterIE.setBorderStyle("stNone");
        hbCEPOnLineManterIE.setPaddingTop(5);
        hbCEPOnLineManterIE.setPaddingLeft(0);
        hbCEPOnLineManterIE.setPaddingRight(0);
        hbCEPOnLineManterIE.setPaddingBottom(0);
        hbCEPOnLineManterIE.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            hbCEPOnLineManterIEClick(event);
            processarFlow("FrmCEPClienteOnLine", "hbCEPOnLineManterIE", "OnClick");
        });
        hbCEPOnLineManterIE.setMarginTop(0);
        hbCEPOnLineManterIE.setMarginLeft(0);
        hbCEPOnLineManterIE.setMarginRight(0);
        hbCEPOnLineManterIE.setMarginBottom(0);
        hbCEPOnLineManterIE.setSpacing(0);
        hbCEPOnLineManterIE.setFlexVflex("ftTrue");
        hbCEPOnLineManterIE.setFlexHflex("ftTrue");
        hbCEPOnLineManterIE.setScrollable(false);
        hbCEPOnLineManterIE.setBoxShadowConfigHorizontalLength(10);
        hbCEPOnLineManterIE.setBoxShadowConfigVerticalLength(10);
        hbCEPOnLineManterIE.setBoxShadowConfigBlurRadius(5);
        hbCEPOnLineManterIE.setBoxShadowConfigSpreadRadius(0);
        hbCEPOnLineManterIE.setBoxShadowConfigShadowColor("clBlack");
        hbCEPOnLineManterIE.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterIE.setVAlign("tvTop");
        FHBox12.addChildren(hbCEPOnLineManterIE);
        hbCEPOnLineManterIE.applyProperties();
    }

    public TFVBox FVBox47 = new TFVBox();

    private void init_FVBox47() {
        FVBox47.setName("FVBox47");
        FVBox47.setLeft(0);
        FVBox47.setTop(0);
        FVBox47.setWidth(7);
        FVBox47.setHeight(20);
        FVBox47.setBorderStyle("stNone");
        FVBox47.setPaddingTop(0);
        FVBox47.setPaddingLeft(0);
        FVBox47.setPaddingRight(0);
        FVBox47.setPaddingBottom(0);
        FVBox47.setMarginTop(0);
        FVBox47.setMarginLeft(0);
        FVBox47.setMarginRight(0);
        FVBox47.setMarginBottom(0);
        FVBox47.setSpacing(1);
        FVBox47.setFlexVflex("ftTrue");
        FVBox47.setFlexHflex("ftTrue");
        FVBox47.setScrollable(false);
        FVBox47.setBoxShadowConfigHorizontalLength(10);
        FVBox47.setBoxShadowConfigVerticalLength(10);
        FVBox47.setBoxShadowConfigBlurRadius(5);
        FVBox47.setBoxShadowConfigSpreadRadius(0);
        FVBox47.setBoxShadowConfigShadowColor("clBlack");
        FVBox47.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterIE.addChildren(FVBox47);
        FVBox47.applyProperties();
    }

    public TFLabel lblManterIE = new TFLabel();

    private void init_lblManterIE() {
        lblManterIE.setName("lblManterIE");
        lblManterIE.setLeft(7);
        lblManterIE.setTop(0);
        lblManterIE.setWidth(76);
        lblManterIE.setHeight(14);
        lblManterIE.setCaption("Manter Todos");
        lblManterIE.setFontColor("clWindowText");
        lblManterIE.setFontSize(-12);
        lblManterIE.setFontName("Tahoma");
        lblManterIE.setFontStyle("[]");
        lblManterIE.setVerticalAlignment("taVerticalCenter");
        lblManterIE.setWordBreak(false);
        hbCEPOnLineManterIE.addChildren(lblManterIE);
        lblManterIE.applyProperties();
    }

    public TFVBox FVBox48 = new TFVBox();

    private void init_FVBox48() {
        FVBox48.setName("FVBox48");
        FVBox48.setLeft(83);
        FVBox48.setTop(0);
        FVBox48.setWidth(7);
        FVBox48.setHeight(20);
        FVBox48.setBorderStyle("stNone");
        FVBox48.setPaddingTop(0);
        FVBox48.setPaddingLeft(0);
        FVBox48.setPaddingRight(0);
        FVBox48.setPaddingBottom(0);
        FVBox48.setMarginTop(0);
        FVBox48.setMarginLeft(0);
        FVBox48.setMarginRight(0);
        FVBox48.setMarginBottom(0);
        FVBox48.setSpacing(1);
        FVBox48.setFlexVflex("ftTrue");
        FVBox48.setFlexHflex("ftTrue");
        FVBox48.setScrollable(false);
        FVBox48.setBoxShadowConfigHorizontalLength(10);
        FVBox48.setBoxShadowConfigVerticalLength(10);
        FVBox48.setBoxShadowConfigBlurRadius(5);
        FVBox48.setBoxShadowConfigSpreadRadius(0);
        FVBox48.setBoxShadowConfigShadowColor("clBlack");
        FVBox48.setBoxShadowConfigOpacity(75);
        hbCEPOnLineManterIE.addChildren(FVBox48);
        FVBox48.applyProperties();
    }

    public TFVBox FVBox49 = new TFVBox();

    private void init_FVBox49() {
        FVBox49.setName("FVBox49");
        FVBox49.setLeft(377);
        FVBox49.setTop(0);
        FVBox49.setWidth(5);
        FVBox49.setHeight(35);
        FVBox49.setBorderStyle("stNone");
        FVBox49.setPaddingTop(0);
        FVBox49.setPaddingLeft(0);
        FVBox49.setPaddingRight(0);
        FVBox49.setPaddingBottom(0);
        FVBox49.setVisible(false);
        FVBox49.setMarginTop(0);
        FVBox49.setMarginLeft(0);
        FVBox49.setMarginRight(0);
        FVBox49.setMarginBottom(0);
        FVBox49.setSpacing(1);
        FVBox49.setFlexVflex("ftFalse");
        FVBox49.setFlexHflex("ftFalse");
        FVBox49.setScrollable(false);
        FVBox49.setBoxShadowConfigHorizontalLength(10);
        FVBox49.setBoxShadowConfigVerticalLength(10);
        FVBox49.setBoxShadowConfigBlurRadius(5);
        FVBox49.setBoxShadowConfigSpreadRadius(0);
        FVBox49.setBoxShadowConfigShadowColor("clBlack");
        FVBox49.setBoxShadowConfigOpacity(75);
        hBoxlblEndIE.addChildren(FVBox49);
        FVBox49.applyProperties();
    }

    public TFHBox boxEndIE = new TFHBox();

    private void init_boxEndIE() {
        boxEndIE.setName("boxEndIE");
        boxEndIE.setLeft(0);
        boxEndIE.setTop(638);
        boxEndIE.setWidth(768);
        boxEndIE.setHeight(284);
        boxEndIE.setBorderStyle("stNone");
        boxEndIE.setPaddingTop(5);
        boxEndIE.setPaddingLeft(0);
        boxEndIE.setPaddingRight(0);
        boxEndIE.setPaddingBottom(0);
        boxEndIE.setMarginTop(0);
        boxEndIE.setMarginLeft(0);
        boxEndIE.setMarginRight(0);
        boxEndIE.setMarginBottom(0);
        boxEndIE.setSpacing(1);
        boxEndIE.setFlexVflex("ftMin");
        boxEndIE.setFlexHflex("ftTrue");
        boxEndIE.setScrollable(false);
        boxEndIE.setBoxShadowConfigHorizontalLength(10);
        boxEndIE.setBoxShadowConfigVerticalLength(10);
        boxEndIE.setBoxShadowConfigBlurRadius(5);
        boxEndIE.setBoxShadowConfigSpreadRadius(0);
        boxEndIE.setBoxShadowConfigShadowColor("clBlack");
        boxEndIE.setBoxShadowConfigOpacity(75);
        boxEndIE.setVAlign("tvTop");
        vBoxEnderecos.addChildren(boxEndIE);
        boxEndIE.applyProperties();
    }

    public TFVBox FVBox37 = new TFVBox();

    private void init_FVBox37() {
        FVBox37.setName("FVBox37");
        FVBox37.setLeft(0);
        FVBox37.setTop(0);
        FVBox37.setWidth(763);
        FVBox37.setHeight(279);
        FVBox37.setBorderStyle("stNone");
        FVBox37.setPaddingTop(0);
        FVBox37.setPaddingLeft(10);
        FVBox37.setPaddingRight(10);
        FVBox37.setPaddingBottom(0);
        FVBox37.setMarginTop(0);
        FVBox37.setMarginLeft(0);
        FVBox37.setMarginRight(0);
        FVBox37.setMarginBottom(0);
        FVBox37.setSpacing(1);
        FVBox37.setFlexVflex("ftMin");
        FVBox37.setFlexHflex("ftTrue");
        FVBox37.setScrollable(false);
        FVBox37.setBoxShadowConfigHorizontalLength(10);
        FVBox37.setBoxShadowConfigVerticalLength(10);
        FVBox37.setBoxShadowConfigBlurRadius(5);
        FVBox37.setBoxShadowConfigSpreadRadius(0);
        FVBox37.setBoxShadowConfigShadowColor("clBlack");
        FVBox37.setBoxShadowConfigOpacity(75);
        boxEndIE.addChildren(FVBox37);
        FVBox37.applyProperties();
    }

    public TFGrid grdEndIE = new TFGrid();

    private void init_grdEndIE() {
        grdEndIE.setName("grdEndIE");
        grdEndIE.setLeft(0);
        grdEndIE.setTop(0);
        grdEndIE.setWidth(756);
        grdEndIE.setHeight(132);
        grdEndIE.setTable(tbClientesEnderecoIe);
        grdEndIE.setFlexVflex("ftFalse");
        grdEndIE.setFlexHflex("ftTrue");
        grdEndIE.setPagingEnabled(true);
        grdEndIE.setFrozenColumns(0);
        grdEndIE.setShowFooter(false);
        grdEndIE.setShowHeader(true);
        grdEndIE.setMultiSelection(false);
        grdEndIE.setGroupingEnabled(false);
        grdEndIE.setGroupingExpanded(false);
        grdEndIE.setGroupingShowFooter(false);
        grdEndIE.setCrosstabEnabled(false);
        grdEndIE.setCrosstabGroupType("cgtConcat");
        grdEndIE.setEditionEnabled(false);
        grdEndIE.setNoBorder(false);
        TFGridColumn item0 = new TFGridColumn();
        item0.setFieldName("INSCRICAO_ESTADUAL");
        item0.setTitleCaption("Insc. Estadual");
        item0.setWidth(115);
        item0.setVisible(true);
        item0.setPrecision(0);
        item0.setTextAlign("taLeft");
        item0.setFieldType("ftString");
        item0.setFlexRatio(0);
        item0.setSort(true);
        item0.setImageHeader(0);
        item0.setWrap(false);
        item0.setFlex(false);
        item0.setCharCase("ccNormal");
        item0.setBlobConfigMimeType("bmtText");
        item0.setBlobConfigShowType("btImageViewer");
        item0.setShowLabel(true);
        item0.setEditorEditType("etTFString");
        item0.setEditorPrecision(0);
        item0.setEditorMaxLength(100);
        item0.setEditorLookupFilterKey(0);
        item0.setEditorLookupFilterDesc(0);
        item0.setEditorPopupHeight(400);
        item0.setEditorPopupWidth(400);
        item0.setEditorCharCase("ccNormal");
        item0.setEditorEnabled(false);
        item0.setEditorReadOnly(false);
        item0.setHiperLink(false);
        item0.setEditorConstraintCheckWhen("cwImmediate");
        item0.setEditorConstraintCheckType("ctExpression");
        item0.setEditorConstraintFocusOnError(false);
        item0.setEditorConstraintEnableUI(true);
        item0.setEditorConstraintEnabled(false);
        item0.setEmpty(false);
        item0.setMobileOptsShowMobile(false);
        item0.setMobileOptsOrder(0);
        item0.setBoxSize(0);
        item0.setImageSrcType("istSource");
        grdEndIE.getColumns().add(item0);
        TFGridColumn item1 = new TFGridColumn();
        item1.setFieldName("CEP");
        item1.setWidth(82);
        item1.setVisible(true);
        item1.setPrecision(0);
        item1.setTextAlign("taLeft");
        item1.setFieldType("ftString");
        item1.setFlexRatio(0);
        item1.setSort(true);
        item1.setImageHeader(0);
        item1.setWrap(false);
        item1.setFlex(false);
        item1.setCharCase("ccNormal");
        item1.setBlobConfigMimeType("bmtText");
        item1.setBlobConfigShowType("btImageViewer");
        item1.setShowLabel(true);
        item1.setEditorEditType("etTFString");
        item1.setEditorPrecision(0);
        item1.setEditorMaxLength(100);
        item1.setEditorLookupFilterKey(0);
        item1.setEditorLookupFilterDesc(0);
        item1.setEditorPopupHeight(400);
        item1.setEditorPopupWidth(400);
        item1.setEditorCharCase("ccNormal");
        item1.setEditorEnabled(false);
        item1.setEditorReadOnly(false);
        item1.setHiperLink(false);
        item1.setEditorConstraintCheckWhen("cwImmediate");
        item1.setEditorConstraintCheckType("ctExpression");
        item1.setEditorConstraintFocusOnError(false);
        item1.setEditorConstraintEnableUI(true);
        item1.setEditorConstraintEnabled(false);
        item1.setEmpty(false);
        item1.setMobileOptsShowMobile(false);
        item1.setMobileOptsOrder(0);
        item1.setBoxSize(0);
        item1.setImageSrcType("istSource");
        grdEndIE.getColumns().add(item1);
        TFGridColumn item2 = new TFGridColumn();
        item2.setFieldName("UF");
        item2.setWidth(45);
        item2.setVisible(true);
        item2.setPrecision(0);
        item2.setTextAlign("taLeft");
        item2.setFieldType("ftString");
        item2.setFlexRatio(0);
        item2.setSort(true);
        item2.setImageHeader(0);
        item2.setWrap(false);
        item2.setFlex(false);
        item2.setCharCase("ccNormal");
        item2.setBlobConfigMimeType("bmtText");
        item2.setBlobConfigShowType("btImageViewer");
        item2.setShowLabel(true);
        item2.setEditorEditType("etTFString");
        item2.setEditorPrecision(0);
        item2.setEditorMaxLength(100);
        item2.setEditorLookupFilterKey(0);
        item2.setEditorLookupFilterDesc(0);
        item2.setEditorPopupHeight(400);
        item2.setEditorPopupWidth(400);
        item2.setEditorCharCase("ccNormal");
        item2.setEditorEnabled(false);
        item2.setEditorReadOnly(false);
        item2.setHiperLink(false);
        item2.setEditorConstraintCheckWhen("cwImmediate");
        item2.setEditorConstraintCheckType("ctExpression");
        item2.setEditorConstraintFocusOnError(false);
        item2.setEditorConstraintEnableUI(true);
        item2.setEditorConstraintEnabled(false);
        item2.setEmpty(false);
        item2.setMobileOptsShowMobile(false);
        item2.setMobileOptsOrder(0);
        item2.setBoxSize(0);
        item2.setImageSrcType("istSource");
        grdEndIE.getColumns().add(item2);
        TFGridColumn item3 = new TFGridColumn();
        item3.setFieldName("CIDADE");
        item3.setTitleCaption("Cidade");
        item3.setWidth(124);
        item3.setVisible(true);
        item3.setPrecision(0);
        item3.setTextAlign("taLeft");
        item3.setFieldType("ftString");
        item3.setFlexRatio(0);
        item3.setSort(true);
        item3.setImageHeader(0);
        item3.setWrap(false);
        item3.setFlex(false);
        item3.setCharCase("ccNormal");
        item3.setBlobConfigMimeType("bmtText");
        item3.setBlobConfigShowType("btImageViewer");
        item3.setShowLabel(true);
        item3.setEditorEditType("etTFString");
        item3.setEditorPrecision(0);
        item3.setEditorMaxLength(100);
        item3.setEditorLookupFilterKey(0);
        item3.setEditorLookupFilterDesc(0);
        item3.setEditorPopupHeight(400);
        item3.setEditorPopupWidth(400);
        item3.setEditorCharCase("ccNormal");
        item3.setEditorEnabled(false);
        item3.setEditorReadOnly(false);
        item3.setHiperLink(false);
        item3.setEditorConstraintCheckWhen("cwImmediate");
        item3.setEditorConstraintCheckType("ctExpression");
        item3.setEditorConstraintFocusOnError(false);
        item3.setEditorConstraintEnableUI(true);
        item3.setEditorConstraintEnabled(false);
        item3.setEmpty(false);
        item3.setMobileOptsShowMobile(false);
        item3.setMobileOptsOrder(0);
        item3.setBoxSize(0);
        item3.setImageSrcType("istSource");
        grdEndIE.getColumns().add(item3);
        TFGridColumn item4 = new TFGridColumn();
        item4.setFieldName("MANTER");
        item4.setTitleCaption("Status");
        item4.setWidth(70);
        item4.setVisible(true);
        item4.setPrecision(0);
        item4.setTextAlign("taCenter");
        item4.setFieldType("ftString");
        item4.setFlexRatio(0);
        item4.setSort(false);
        item4.setImageHeader(0);
        item4.setWrap(false);
        item4.setFlex(true);
        TFImageExpression item5 = new TFImageExpression();
        item5.setExpression("MANTER = 'S'");
        item5.setHint("Manter");
        item5.setEvalType("etExpression");
        item5.setImageId(7000117);
        item5.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdEndIEmanterAceitarEndIE(event);
            processarFlow("FrmCEPClienteOnLine", "item5", "OnClick");
        });
        item4.getImages().add(item5);
        TFImageExpression item6 = new TFImageExpression();
        item6.setExpression("MANTER = 'X'");
        item6.setHint("Aceitar");
        item6.setEvalType("etExpression");
        item6.setImageId(7000118);
        item6.addEventListener("onClick", (EventListener<Event<Object>>)(Event<Object> event) -> {
            grdEndIEmanterAceitarEndIE(event);
            processarFlow("FrmCEPClienteOnLine", "item6", "OnClick");
        });
        item4.getImages().add(item6);
        item4.setCharCase("ccNormal");
        item4.setBlobConfigMimeType("bmtText");
        item4.setBlobConfigShowType("btImageViewer");
        item4.setShowLabel(false);
        item4.setEditorEditType("etTFString");
        item4.setEditorPrecision(0);
        item4.setEditorMaxLength(100);
        item4.setEditorLookupFilterKey(0);
        item4.setEditorLookupFilterDesc(0);
        item4.setEditorPopupHeight(400);
        item4.setEditorPopupWidth(400);
        item4.setEditorCharCase("ccNormal");
        item4.setEditorEnabled(false);
        item4.setEditorReadOnly(false);
        item4.setHiperLink(false);
        item4.setEditorConstraintCheckWhen("cwImmediate");
        item4.setEditorConstraintCheckType("ctExpression");
        item4.setEditorConstraintFocusOnError(false);
        item4.setEditorConstraintEnableUI(true);
        item4.setEditorConstraintEnabled(false);
        item4.setEmpty(false);
        item4.setMobileOptsShowMobile(false);
        item4.setMobileOptsOrder(0);
        item4.setBoxSize(0);
        item4.setImageSrcType("istSource");
        grdEndIE.getColumns().add(item4);
        FVBox37.addChildren(grdEndIE);
        grdEndIE.applyProperties();
    }

    public TFHBox FHBox14 = new TFHBox();

    private void init_FHBox14() {
        FHBox14.setName("FHBox14");
        FHBox14.setLeft(0);
        FHBox14.setTop(133);
        FHBox14.setWidth(758);
        FHBox14.setHeight(141);
        FHBox14.setBorderStyle("stNone");
        FHBox14.setPaddingTop(0);
        FHBox14.setPaddingLeft(0);
        FHBox14.setPaddingRight(0);
        FHBox14.setPaddingBottom(0);
        FHBox14.setMarginTop(0);
        FHBox14.setMarginLeft(0);
        FHBox14.setMarginRight(0);
        FHBox14.setMarginBottom(0);
        FHBox14.setSpacing(3);
        FHBox14.setFlexVflex("ftMin");
        FHBox14.setFlexHflex("ftTrue");
        FHBox14.setScrollable(false);
        FHBox14.setBoxShadowConfigHorizontalLength(10);
        FHBox14.setBoxShadowConfigVerticalLength(10);
        FHBox14.setBoxShadowConfigBlurRadius(5);
        FHBox14.setBoxShadowConfigSpreadRadius(0);
        FHBox14.setBoxShadowConfigShadowColor("clBlack");
        FHBox14.setBoxShadowConfigOpacity(75);
        FHBox14.setVAlign("tvTop");
        FVBox37.addChildren(FHBox14);
        FHBox14.applyProperties();
    }

    public TFVBox FVBox57 = new TFVBox();

    private void init_FVBox57() {
        FVBox57.setName("FVBox57");
        FVBox57.setLeft(0);
        FVBox57.setTop(0);
        FVBox57.setWidth(28);
        FVBox57.setHeight(110);
        FVBox57.setBorderStyle("stNone");
        FVBox57.setPaddingTop(0);
        FVBox57.setPaddingLeft(0);
        FVBox57.setPaddingRight(0);
        FVBox57.setPaddingBottom(0);
        FVBox57.setMarginTop(0);
        FVBox57.setMarginLeft(0);
        FVBox57.setMarginRight(0);
        FVBox57.setMarginBottom(0);
        FVBox57.setSpacing(3);
        FVBox57.setFlexVflex("ftFalse");
        FVBox57.setFlexHflex("ftFalse");
        FVBox57.setScrollable(false);
        FVBox57.setBoxShadowConfigHorizontalLength(10);
        FVBox57.setBoxShadowConfigVerticalLength(10);
        FVBox57.setBoxShadowConfigBlurRadius(5);
        FVBox57.setBoxShadowConfigSpreadRadius(0);
        FVBox57.setBoxShadowConfigShadowColor("clBlack");
        FVBox57.setBoxShadowConfigOpacity(75);
        FHBox14.addChildren(FVBox57);
        FVBox57.applyProperties();
    }

    public TFLabel lblUFCadIE = new TFLabel();

    private void init_lblUFCadIE() {
        lblUFCadIE.setName("lblUFCadIE");
        lblUFCadIE.setLeft(0);
        lblUFCadIE.setTop(0);
        lblUFCadIE.setWidth(120);
        lblUFCadIE.setHeight(13);
        lblUFCadIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFCadIE.setFontColor("clRed");
        lblUFCadIE.setFontSize(-11);
        lblUFCadIE.setFontName("Tahoma");
        lblUFCadIE.setFontStyle("[]");
        lblUFCadIE.setFieldName("UF");
        lblUFCadIE.setTable(tbClientesEnderecoIe);
        lblUFCadIE.setVerticalAlignment("taVerticalCenter");
        lblUFCadIE.setWordBreak(false);
        FVBox57.addChildren(lblUFCadIE);
        lblUFCadIE.applyProperties();
    }

    public TFLabel lblUFCorreiosIE = new TFLabel();

    private void init_lblUFCorreiosIE() {
        lblUFCorreiosIE.setName("lblUFCorreiosIE");
        lblUFCorreiosIE.setLeft(0);
        lblUFCorreiosIE.setTop(14);
        lblUFCorreiosIE.setWidth(120);
        lblUFCorreiosIE.setHeight(13);
        lblUFCorreiosIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblUFCorreiosIE.setFontColor("13392431");
        lblUFCorreiosIE.setFontSize(-11);
        lblUFCorreiosIE.setFontName("Tahoma");
        lblUFCorreiosIE.setFontStyle("[]");
        lblUFCorreiosIE.setFieldName("UF");
        lblUFCorreiosIE.setTable(tbClientesEnderecoIeTemp);
        lblUFCorreiosIE.setVerticalAlignment("taVerticalCenter");
        lblUFCorreiosIE.setWordBreak(false);
        FVBox57.addChildren(lblUFCorreiosIE);
        lblUFCorreiosIE.applyProperties();
    }

    public TFVBox vBoxCidadeIE = new TFVBox();

    private void init_vBoxCidadeIE() {
        vBoxCidadeIE.setName("vBoxCidadeIE");
        vBoxCidadeIE.setLeft(28);
        vBoxCidadeIE.setTop(0);
        vBoxCidadeIE.setWidth(112);
        vBoxCidadeIE.setHeight(128);
        vBoxCidadeIE.setBorderStyle("stNone");
        vBoxCidadeIE.setPaddingTop(0);
        vBoxCidadeIE.setPaddingLeft(10);
        vBoxCidadeIE.setPaddingRight(0);
        vBoxCidadeIE.setPaddingBottom(0);
        vBoxCidadeIE.setMarginTop(0);
        vBoxCidadeIE.setMarginLeft(0);
        vBoxCidadeIE.setMarginRight(0);
        vBoxCidadeIE.setMarginBottom(0);
        vBoxCidadeIE.setSpacing(3);
        vBoxCidadeIE.setFlexVflex("ftMin");
        vBoxCidadeIE.setFlexHflex("ftFalse");
        vBoxCidadeIE.setScrollable(false);
        vBoxCidadeIE.setBoxShadowConfigHorizontalLength(10);
        vBoxCidadeIE.setBoxShadowConfigVerticalLength(10);
        vBoxCidadeIE.setBoxShadowConfigBlurRadius(5);
        vBoxCidadeIE.setBoxShadowConfigSpreadRadius(0);
        vBoxCidadeIE.setBoxShadowConfigShadowColor("clBlack");
        vBoxCidadeIE.setBoxShadowConfigOpacity(75);
        FHBox14.addChildren(vBoxCidadeIE);
        vBoxCidadeIE.applyProperties();
    }

    public TFVBox FVBox59 = new TFVBox();

    private void init_FVBox59() {
        FVBox59.setName("FVBox59");
        FVBox59.setLeft(0);
        FVBox59.setTop(0);
        FVBox59.setWidth(107);
        FVBox59.setHeight(43);
        FVBox59.setBorderStyle("stNone");
        FVBox59.setPaddingTop(0);
        FVBox59.setPaddingLeft(0);
        FVBox59.setPaddingRight(0);
        FVBox59.setPaddingBottom(0);
        FVBox59.setMarginTop(0);
        FVBox59.setMarginLeft(0);
        FVBox59.setMarginRight(0);
        FVBox59.setMarginBottom(0);
        FVBox59.setSpacing(3);
        FVBox59.setFlexVflex("ftFalse");
        FVBox59.setFlexHflex("ftTrue");
        FVBox59.setScrollable(false);
        FVBox59.setBoxShadowConfigHorizontalLength(10);
        FVBox59.setBoxShadowConfigVerticalLength(10);
        FVBox59.setBoxShadowConfigBlurRadius(5);
        FVBox59.setBoxShadowConfigSpreadRadius(0);
        FVBox59.setBoxShadowConfigShadowColor("clBlack");
        FVBox59.setBoxShadowConfigOpacity(75);
        vBoxCidadeIE.addChildren(FVBox59);
        FVBox59.applyProperties();
    }

    public TFLabel lblCidadeCadIE = new TFLabel();

    private void init_lblCidadeCadIE() {
        lblCidadeCadIE.setName("lblCidadeCadIE");
        lblCidadeCadIE.setLeft(0);
        lblCidadeCadIE.setTop(0);
        lblCidadeCadIE.setWidth(120);
        lblCidadeCadIE.setHeight(13);
        lblCidadeCadIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeCadIE.setFontColor("clRed");
        lblCidadeCadIE.setFontSize(-11);
        lblCidadeCadIE.setFontName("Tahoma");
        lblCidadeCadIE.setFontStyle("[]");
        lblCidadeCadIE.setFieldName("CIDADE");
        lblCidadeCadIE.setTable(tbClientesEnderecoIe);
        lblCidadeCadIE.setVerticalAlignment("taVerticalCenter");
        lblCidadeCadIE.setWordBreak(false);
        FVBox59.addChildren(lblCidadeCadIE);
        lblCidadeCadIE.applyProperties();
    }

    public TFLabel lblCidadeCorreiosIE = new TFLabel();

    private void init_lblCidadeCorreiosIE() {
        lblCidadeCorreiosIE.setName("lblCidadeCorreiosIE");
        lblCidadeCorreiosIE.setLeft(0);
        lblCidadeCorreiosIE.setTop(14);
        lblCidadeCorreiosIE.setWidth(120);
        lblCidadeCorreiosIE.setHeight(13);
        lblCidadeCorreiosIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblCidadeCorreiosIE.setFontColor("13392431");
        lblCidadeCorreiosIE.setFontSize(-11);
        lblCidadeCorreiosIE.setFontName("Tahoma");
        lblCidadeCorreiosIE.setFontStyle("[]");
        lblCidadeCorreiosIE.setFieldName("CIDADE");
        lblCidadeCorreiosIE.setTable(tbClientesEnderecoIeTemp);
        lblCidadeCorreiosIE.setVerticalAlignment("taVerticalCenter");
        lblCidadeCorreiosIE.setWordBreak(false);
        FVBox59.addChildren(lblCidadeCorreiosIE);
        lblCidadeCorreiosIE.applyProperties();
    }

    public TFVBox vBoxNumeroIE = new TFVBox();

    private void init_vBoxNumeroIE() {
        vBoxNumeroIE.setName("vBoxNumeroIE");
        vBoxNumeroIE.setLeft(0);
        vBoxNumeroIE.setTop(44);
        vBoxNumeroIE.setWidth(107);
        vBoxNumeroIE.setHeight(78);
        vBoxNumeroIE.setBorderStyle("stNone");
        vBoxNumeroIE.setPaddingTop(0);
        vBoxNumeroIE.setPaddingLeft(0);
        vBoxNumeroIE.setPaddingRight(0);
        vBoxNumeroIE.setPaddingBottom(0);
        vBoxNumeroIE.setMarginTop(0);
        vBoxNumeroIE.setMarginLeft(0);
        vBoxNumeroIE.setMarginRight(0);
        vBoxNumeroIE.setMarginBottom(0);
        vBoxNumeroIE.setSpacing(3);
        vBoxNumeroIE.setFlexVflex("ftFalse");
        vBoxNumeroIE.setFlexHflex("ftTrue");
        vBoxNumeroIE.setScrollable(false);
        vBoxNumeroIE.setBoxShadowConfigHorizontalLength(10);
        vBoxNumeroIE.setBoxShadowConfigVerticalLength(10);
        vBoxNumeroIE.setBoxShadowConfigBlurRadius(5);
        vBoxNumeroIE.setBoxShadowConfigSpreadRadius(0);
        vBoxNumeroIE.setBoxShadowConfigShadowColor("clBlack");
        vBoxNumeroIE.setBoxShadowConfigOpacity(75);
        vBoxCidadeIE.addChildren(vBoxNumeroIE);
        vBoxNumeroIE.applyProperties();
    }

    public TFLabel FLabel6 = new TFLabel();

    private void init_FLabel6() {
        FLabel6.setName("FLabel6");
        FLabel6.setLeft(0);
        FLabel6.setTop(0);
        FLabel6.setWidth(37);
        FLabel6.setHeight(13);
        FLabel6.setCaption("N\u00FAmero");
        FLabel6.setFontColor("clWindowText");
        FLabel6.setFontSize(-11);
        FLabel6.setFontName("Tahoma");
        FLabel6.setFontStyle("[]");
        FLabel6.setVerticalAlignment("taVerticalCenter");
        FLabel6.setWordBreak(false);
        vBoxNumeroIE.addChildren(FLabel6);
        FLabel6.applyProperties();
    }

    public TFHBox hBoxNumeroCadIE = new TFHBox();

    private void init_hBoxNumeroCadIE() {
        hBoxNumeroCadIE.setName("hBoxNumeroCadIE");
        hBoxNumeroCadIE.setLeft(0);
        hBoxNumeroCadIE.setTop(14);
        hBoxNumeroCadIE.setWidth(98);
        hBoxNumeroCadIE.setHeight(35);
        hBoxNumeroCadIE.setBorderStyle("stNone");
        hBoxNumeroCadIE.setPaddingTop(0);
        hBoxNumeroCadIE.setPaddingLeft(0);
        hBoxNumeroCadIE.setPaddingRight(0);
        hBoxNumeroCadIE.setPaddingBottom(0);
        hBoxNumeroCadIE.setMarginTop(0);
        hBoxNumeroCadIE.setMarginLeft(0);
        hBoxNumeroCadIE.setMarginRight(0);
        hBoxNumeroCadIE.setMarginBottom(0);
        hBoxNumeroCadIE.setSpacing(3);
        hBoxNumeroCadIE.setFlexVflex("ftFalse");
        hBoxNumeroCadIE.setFlexHflex("ftFalse");
        hBoxNumeroCadIE.setScrollable(false);
        hBoxNumeroCadIE.setBoxShadowConfigHorizontalLength(10);
        hBoxNumeroCadIE.setBoxShadowConfigVerticalLength(10);
        hBoxNumeroCadIE.setBoxShadowConfigBlurRadius(5);
        hBoxNumeroCadIE.setBoxShadowConfigSpreadRadius(0);
        hBoxNumeroCadIE.setBoxShadowConfigShadowColor("clBlack");
        hBoxNumeroCadIE.setBoxShadowConfigOpacity(75);
        hBoxNumeroCadIE.setVAlign("tvTop");
        vBoxNumeroIE.addChildren(hBoxNumeroCadIE);
        hBoxNumeroCadIE.applyProperties();
    }

    public TFString edNumeroCadIE = new TFString();

    private void init_edNumeroCadIE() {
        edNumeroCadIE.setName("edNumeroCadIE");
        edNumeroCadIE.setLeft(0);
        edNumeroCadIE.setTop(0);
        edNumeroCadIE.setWidth(81);
        edNumeroCadIE.setHeight(21);
        edNumeroCadIE.setTable(tbClientesEnderecoIe);
        edNumeroCadIE.setFieldName("FACHADA");
        edNumeroCadIE.setFlex(true);
        edNumeroCadIE.setRequired(false);
        edNumeroCadIE.setConstraintCheckWhen("cwImmediate");
        edNumeroCadIE.setConstraintCheckType("ctExpression");
        edNumeroCadIE.setConstraintFocusOnError(false);
        edNumeroCadIE.setConstraintEnableUI(true);
        edNumeroCadIE.setConstraintEnabled(false);
        edNumeroCadIE.setConstraintFormCheck(true);
        edNumeroCadIE.setCharCase("ccNormal");
        edNumeroCadIE.setPwd(false);
        edNumeroCadIE.setMaxlength(5);
        edNumeroCadIE.setFontColor("clWindowText");
        edNumeroCadIE.setFontSize(-11);
        edNumeroCadIE.setFontName("Tahoma");
        edNumeroCadIE.setFontStyle("[]");
        edNumeroCadIE.setSaveLiteralCharacter(false);
        edNumeroCadIE.applyProperties();
        hBoxNumeroCadIE.addChildren(edNumeroCadIE);
        addValidatable(edNumeroCadIE);
    }

    public TFVBox FVBox15 = new TFVBox();

    private void init_FVBox15() {
        FVBox15.setName("FVBox15");
        FVBox15.setLeft(81);
        FVBox15.setTop(0);
        FVBox15.setWidth(12);
        FVBox15.setHeight(30);
        FVBox15.setBorderStyle("stNone");
        FVBox15.setPaddingTop(0);
        FVBox15.setPaddingLeft(0);
        FVBox15.setPaddingRight(0);
        FVBox15.setPaddingBottom(0);
        FVBox15.setMarginTop(0);
        FVBox15.setMarginLeft(0);
        FVBox15.setMarginRight(0);
        FVBox15.setMarginBottom(0);
        FVBox15.setSpacing(1);
        FVBox15.setFlexVflex("ftFalse");
        FVBox15.setFlexHflex("ftFalse");
        FVBox15.setScrollable(false);
        FVBox15.setBoxShadowConfigHorizontalLength(10);
        FVBox15.setBoxShadowConfigVerticalLength(10);
        FVBox15.setBoxShadowConfigBlurRadius(5);
        FVBox15.setBoxShadowConfigSpreadRadius(0);
        FVBox15.setBoxShadowConfigShadowColor("clBlack");
        FVBox15.setBoxShadowConfigOpacity(75);
        hBoxNumeroCadIE.addChildren(FVBox15);
        FVBox15.applyProperties();
    }

    public TFLabel lblNumeroCorreiosIE = new TFLabel();

    private void init_lblNumeroCorreiosIE() {
        lblNumeroCorreiosIE.setName("lblNumeroCorreiosIE");
        lblNumeroCorreiosIE.setLeft(0);
        lblNumeroCorreiosIE.setTop(50);
        lblNumeroCorreiosIE.setWidth(120);
        lblNumeroCorreiosIE.setHeight(13);
        lblNumeroCorreiosIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblNumeroCorreiosIE.setFontColor("13392431");
        lblNumeroCorreiosIE.setFontSize(-11);
        lblNumeroCorreiosIE.setFontName("Tahoma");
        lblNumeroCorreiosIE.setFontStyle("[]");
        lblNumeroCorreiosIE.setVisible(false);
        lblNumeroCorreiosIE.setFieldName("FACHADA");
        lblNumeroCorreiosIE.setTable(tbClientesEnderecoIeTemp);
        lblNumeroCorreiosIE.setVerticalAlignment("taVerticalCenter");
        lblNumeroCorreiosIE.setWordBreak(false);
        vBoxNumeroIE.addChildren(lblNumeroCorreiosIE);
        lblNumeroCorreiosIE.applyProperties();
    }

    public TFVBox FVBox62 = new TFVBox();

    private void init_FVBox62() {
        FVBox62.setName("FVBox62");
        FVBox62.setLeft(0);
        FVBox62.setTop(64);
        FVBox62.setWidth(98);
        FVBox62.setHeight(9);
        FVBox62.setBorderStyle("stNone");
        FVBox62.setPaddingTop(0);
        FVBox62.setPaddingLeft(0);
        FVBox62.setPaddingRight(0);
        FVBox62.setPaddingBottom(0);
        FVBox62.setMarginTop(0);
        FVBox62.setMarginLeft(0);
        FVBox62.setMarginRight(0);
        FVBox62.setMarginBottom(0);
        FVBox62.setSpacing(1);
        FVBox62.setFlexVflex("ftFalse");
        FVBox62.setFlexHflex("ftFalse");
        FVBox62.setScrollable(false);
        FVBox62.setBoxShadowConfigHorizontalLength(10);
        FVBox62.setBoxShadowConfigVerticalLength(10);
        FVBox62.setBoxShadowConfigBlurRadius(5);
        FVBox62.setBoxShadowConfigSpreadRadius(0);
        FVBox62.setBoxShadowConfigShadowColor("clBlack");
        FVBox62.setBoxShadowConfigOpacity(75);
        vBoxNumeroIE.addChildren(FVBox62);
        FVBox62.applyProperties();
    }

    public TFVBox FVBox63 = new TFVBox();

    private void init_FVBox63() {
        FVBox63.setName("FVBox63");
        FVBox63.setLeft(140);
        FVBox63.setTop(0);
        FVBox63.setWidth(322);
        FVBox63.setHeight(127);
        FVBox63.setBorderStyle("stNone");
        FVBox63.setPaddingTop(0);
        FVBox63.setPaddingLeft(10);
        FVBox63.setPaddingRight(0);
        FVBox63.setPaddingBottom(0);
        FVBox63.setMarginTop(0);
        FVBox63.setMarginLeft(0);
        FVBox63.setMarginRight(0);
        FVBox63.setMarginBottom(0);
        FVBox63.setSpacing(1);
        FVBox63.setFlexVflex("ftTrue");
        FVBox63.setFlexHflex("ftTrue");
        FVBox63.setScrollable(false);
        FVBox63.setBoxShadowConfigHorizontalLength(10);
        FVBox63.setBoxShadowConfigVerticalLength(10);
        FVBox63.setBoxShadowConfigBlurRadius(5);
        FVBox63.setBoxShadowConfigSpreadRadius(0);
        FVBox63.setBoxShadowConfigShadowColor("clBlack");
        FVBox63.setBoxShadowConfigOpacity(75);
        FHBox14.addChildren(FVBox63);
        FVBox63.applyProperties();
    }

    public TFHBox FHBox17 = new TFHBox();

    private void init_FHBox17() {
        FHBox17.setName("FHBox17");
        FHBox17.setLeft(0);
        FHBox17.setTop(0);
        FHBox17.setWidth(317);
        FHBox17.setHeight(43);
        FHBox17.setBorderStyle("stNone");
        FHBox17.setPaddingTop(0);
        FHBox17.setPaddingLeft(0);
        FHBox17.setPaddingRight(0);
        FHBox17.setPaddingBottom(0);
        FHBox17.setMarginTop(0);
        FHBox17.setMarginLeft(0);
        FHBox17.setMarginRight(0);
        FHBox17.setMarginBottom(0);
        FHBox17.setSpacing(1);
        FHBox17.setFlexVflex("ftFalse");
        FHBox17.setFlexHflex("ftTrue");
        FHBox17.setScrollable(false);
        FHBox17.setBoxShadowConfigHorizontalLength(10);
        FHBox17.setBoxShadowConfigVerticalLength(10);
        FHBox17.setBoxShadowConfigBlurRadius(5);
        FHBox17.setBoxShadowConfigSpreadRadius(0);
        FHBox17.setBoxShadowConfigShadowColor("clBlack");
        FHBox17.setBoxShadowConfigOpacity(75);
        FHBox17.setVAlign("tvTop");
        FVBox63.addChildren(FHBox17);
        FHBox17.applyProperties();
    }

    public TFVBox FVBox64 = new TFVBox();

    private void init_FVBox64() {
        FVBox64.setName("FVBox64");
        FVBox64.setLeft(0);
        FVBox64.setTop(0);
        FVBox64.setWidth(151);
        FVBox64.setHeight(37);
        FVBox64.setBorderStyle("stNone");
        FVBox64.setPaddingTop(0);
        FVBox64.setPaddingLeft(0);
        FVBox64.setPaddingRight(0);
        FVBox64.setPaddingBottom(0);
        FVBox64.setMarginTop(0);
        FVBox64.setMarginLeft(0);
        FVBox64.setMarginRight(0);
        FVBox64.setMarginBottom(0);
        FVBox64.setSpacing(3);
        FVBox64.setFlexVflex("ftFalse");
        FVBox64.setFlexHflex("ftTrue");
        FVBox64.setScrollable(false);
        FVBox64.setBoxShadowConfigHorizontalLength(10);
        FVBox64.setBoxShadowConfigVerticalLength(10);
        FVBox64.setBoxShadowConfigBlurRadius(5);
        FVBox64.setBoxShadowConfigSpreadRadius(0);
        FVBox64.setBoxShadowConfigShadowColor("clBlack");
        FVBox64.setBoxShadowConfigOpacity(75);
        FHBox17.addChildren(FVBox64);
        FVBox64.applyProperties();
    }

    public TFLabel lblRuaCadIE = new TFLabel();

    private void init_lblRuaCadIE() {
        lblRuaCadIE.setName("lblRuaCadIE");
        lblRuaCadIE.setLeft(0);
        lblRuaCadIE.setTop(0);
        lblRuaCadIE.setWidth(120);
        lblRuaCadIE.setHeight(13);
        lblRuaCadIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaCadIE.setFontColor("clRed");
        lblRuaCadIE.setFontSize(-11);
        lblRuaCadIE.setFontName("Tahoma");
        lblRuaCadIE.setFontStyle("[]");
        lblRuaCadIE.setFieldName("RUA");
        lblRuaCadIE.setTable(tbClientesEnderecoIe);
        lblRuaCadIE.setVerticalAlignment("taVerticalCenter");
        lblRuaCadIE.setWordBreak(false);
        FVBox64.addChildren(lblRuaCadIE);
        lblRuaCadIE.applyProperties();
    }

    public TFLabel lblRuaCorreiosIE = new TFLabel();

    private void init_lblRuaCorreiosIE() {
        lblRuaCorreiosIE.setName("lblRuaCorreiosIE");
        lblRuaCorreiosIE.setLeft(0);
        lblRuaCorreiosIE.setTop(14);
        lblRuaCorreiosIE.setWidth(120);
        lblRuaCorreiosIE.setHeight(13);
        lblRuaCorreiosIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblRuaCorreiosIE.setFontColor("13392431");
        lblRuaCorreiosIE.setFontSize(-11);
        lblRuaCorreiosIE.setFontName("Tahoma");
        lblRuaCorreiosIE.setFontStyle("[]");
        lblRuaCorreiosIE.setFieldName("RUA");
        lblRuaCorreiosIE.setTable(tbClientesEnderecoIeTemp);
        lblRuaCorreiosIE.setVerticalAlignment("taVerticalCenter");
        lblRuaCorreiosIE.setWordBreak(false);
        FVBox64.addChildren(lblRuaCorreiosIE);
        lblRuaCorreiosIE.applyProperties();
    }

    public TFVBox FVBox65 = new TFVBox();

    private void init_FVBox65() {
        FVBox65.setName("FVBox65");
        FVBox65.setLeft(151);
        FVBox65.setTop(0);
        FVBox65.setWidth(120);
        FVBox65.setHeight(37);
        FVBox65.setBorderStyle("stNone");
        FVBox65.setPaddingTop(0);
        FVBox65.setPaddingLeft(17);
        FVBox65.setPaddingRight(0);
        FVBox65.setPaddingBottom(0);
        FVBox65.setMarginTop(0);
        FVBox65.setMarginLeft(0);
        FVBox65.setMarginRight(0);
        FVBox65.setMarginBottom(0);
        FVBox65.setSpacing(3);
        FVBox65.setFlexVflex("ftFalse");
        FVBox65.setFlexHflex("ftTrue");
        FVBox65.setScrollable(false);
        FVBox65.setBoxShadowConfigHorizontalLength(10);
        FVBox65.setBoxShadowConfigVerticalLength(10);
        FVBox65.setBoxShadowConfigBlurRadius(5);
        FVBox65.setBoxShadowConfigSpreadRadius(0);
        FVBox65.setBoxShadowConfigShadowColor("clBlack");
        FVBox65.setBoxShadowConfigOpacity(75);
        FHBox17.addChildren(FVBox65);
        FVBox65.applyProperties();
    }

    public TFLabel lblBairroCadIE = new TFLabel();

    private void init_lblBairroCadIE() {
        lblBairroCadIE.setName("lblBairroCadIE");
        lblBairroCadIE.setLeft(0);
        lblBairroCadIE.setTop(0);
        lblBairroCadIE.setWidth(120);
        lblBairroCadIE.setHeight(13);
        lblBairroCadIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroCadIE.setFontColor("clRed");
        lblBairroCadIE.setFontSize(-11);
        lblBairroCadIE.setFontName("Tahoma");
        lblBairroCadIE.setFontStyle("[]");
        lblBairroCadIE.setFieldName("BAIRRO");
        lblBairroCadIE.setTable(tbClientesEnderecoIe);
        lblBairroCadIE.setVerticalAlignment("taVerticalCenter");
        lblBairroCadIE.setWordBreak(false);
        FVBox65.addChildren(lblBairroCadIE);
        lblBairroCadIE.applyProperties();
    }

    public TFLabel lblBairroCorreiosIE = new TFLabel();

    private void init_lblBairroCorreiosIE() {
        lblBairroCorreiosIE.setName("lblBairroCorreiosIE");
        lblBairroCorreiosIE.setLeft(0);
        lblBairroCorreiosIE.setTop(14);
        lblBairroCorreiosIE.setWidth(120);
        lblBairroCorreiosIE.setHeight(13);
        lblBairroCorreiosIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblBairroCorreiosIE.setFontColor("13392431");
        lblBairroCorreiosIE.setFontSize(-11);
        lblBairroCorreiosIE.setFontName("Tahoma");
        lblBairroCorreiosIE.setFontStyle("[]");
        lblBairroCorreiosIE.setFieldName("BAIRRO");
        lblBairroCorreiosIE.setTable(tbClientesEnderecoIeTemp);
        lblBairroCorreiosIE.setVerticalAlignment("taVerticalCenter");
        lblBairroCorreiosIE.setWordBreak(false);
        FVBox65.addChildren(lblBairroCorreiosIE);
        lblBairroCorreiosIE.applyProperties();
    }

    public TFVBox FVBox66 = new TFVBox();

    private void init_FVBox66() {
        FVBox66.setName("FVBox66");
        FVBox66.setLeft(0);
        FVBox66.setTop(44);
        FVBox66.setWidth(317);
        FVBox66.setHeight(77);
        FVBox66.setBorderStyle("stNone");
        FVBox66.setPaddingTop(0);
        FVBox66.setPaddingLeft(0);
        FVBox66.setPaddingRight(0);
        FVBox66.setPaddingBottom(0);
        FVBox66.setMarginTop(0);
        FVBox66.setMarginLeft(0);
        FVBox66.setMarginRight(0);
        FVBox66.setMarginBottom(0);
        FVBox66.setSpacing(3);
        FVBox66.setFlexVflex("ftMin");
        FVBox66.setFlexHflex("ftTrue");
        FVBox66.setScrollable(false);
        FVBox66.setBoxShadowConfigHorizontalLength(10);
        FVBox66.setBoxShadowConfigVerticalLength(10);
        FVBox66.setBoxShadowConfigBlurRadius(5);
        FVBox66.setBoxShadowConfigSpreadRadius(0);
        FVBox66.setBoxShadowConfigShadowColor("clBlack");
        FVBox66.setBoxShadowConfigOpacity(75);
        FVBox63.addChildren(FVBox66);
        FVBox66.applyProperties();
    }

    public TFLabel FLabel12 = new TFLabel();

    private void init_FLabel12() {
        FLabel12.setName("FLabel12");
        FLabel12.setLeft(0);
        FLabel12.setTop(0);
        FLabel12.setWidth(65);
        FLabel12.setHeight(13);
        FLabel12.setCaption("Complemento");
        FLabel12.setFontColor("clWindowText");
        FLabel12.setFontSize(-11);
        FLabel12.setFontName("Tahoma");
        FLabel12.setFontStyle("[]");
        FLabel12.setVerticalAlignment("taVerticalCenter");
        FLabel12.setWordBreak(false);
        FVBox66.addChildren(FLabel12);
        FLabel12.applyProperties();
    }

    public TFHBox FHBox18 = new TFHBox();

    private void init_FHBox18() {
        FHBox18.setName("FHBox18");
        FHBox18.setLeft(0);
        FHBox18.setTop(14);
        FHBox18.setWidth(274);
        FHBox18.setHeight(35);
        FHBox18.setBorderStyle("stNone");
        FHBox18.setPaddingTop(0);
        FHBox18.setPaddingLeft(0);
        FHBox18.setPaddingRight(0);
        FHBox18.setPaddingBottom(0);
        FHBox18.setMarginTop(0);
        FHBox18.setMarginLeft(0);
        FHBox18.setMarginRight(0);
        FHBox18.setMarginBottom(0);
        FHBox18.setSpacing(3);
        FHBox18.setFlexVflex("ftFalse");
        FHBox18.setFlexHflex("ftTrue");
        FHBox18.setScrollable(false);
        FHBox18.setBoxShadowConfigHorizontalLength(10);
        FHBox18.setBoxShadowConfigVerticalLength(10);
        FHBox18.setBoxShadowConfigBlurRadius(5);
        FHBox18.setBoxShadowConfigSpreadRadius(0);
        FHBox18.setBoxShadowConfigShadowColor("clBlack");
        FHBox18.setBoxShadowConfigOpacity(75);
        FHBox18.setVAlign("tvTop");
        FVBox66.addChildren(FHBox18);
        FHBox18.applyProperties();
    }

    public TFString edComplementoCadIE = new TFString();

    private void init_edComplementoCadIE() {
        edComplementoCadIE.setName("edComplementoCadIE");
        edComplementoCadIE.setLeft(0);
        edComplementoCadIE.setTop(0);
        edComplementoCadIE.setWidth(83);
        edComplementoCadIE.setHeight(21);
        edComplementoCadIE.setTable(tbClientesEnderecoIe);
        edComplementoCadIE.setFieldName("COMPLEMENTO");
        edComplementoCadIE.setFlex(true);
        edComplementoCadIE.setRequired(false);
        edComplementoCadIE.setConstraintCheckWhen("cwImmediate");
        edComplementoCadIE.setConstraintCheckType("ctExpression");
        edComplementoCadIE.setConstraintFocusOnError(false);
        edComplementoCadIE.setConstraintEnableUI(true);
        edComplementoCadIE.setConstraintEnabled(false);
        edComplementoCadIE.setConstraintFormCheck(true);
        edComplementoCadIE.setCharCase("ccNormal");
        edComplementoCadIE.setPwd(false);
        edComplementoCadIE.setMaxlength(30);
        edComplementoCadIE.setFontColor("clWindowText");
        edComplementoCadIE.setFontSize(-11);
        edComplementoCadIE.setFontName("Tahoma");
        edComplementoCadIE.setFontStyle("[]");
        edComplementoCadIE.setSaveLiteralCharacter(false);
        edComplementoCadIE.applyProperties();
        FHBox18.addChildren(edComplementoCadIE);
        addValidatable(edComplementoCadIE);
    }

    public TFVBox FVBox67 = new TFVBox();

    private void init_FVBox67() {
        FVBox67.setName("FVBox67");
        FVBox67.setLeft(83);
        FVBox67.setTop(0);
        FVBox67.setWidth(12);
        FVBox67.setHeight(30);
        FVBox67.setBorderStyle("stNone");
        FVBox67.setPaddingTop(0);
        FVBox67.setPaddingLeft(0);
        FVBox67.setPaddingRight(0);
        FVBox67.setPaddingBottom(0);
        FVBox67.setMarginTop(0);
        FVBox67.setMarginLeft(0);
        FVBox67.setMarginRight(0);
        FVBox67.setMarginBottom(0);
        FVBox67.setSpacing(1);
        FVBox67.setFlexVflex("ftFalse");
        FVBox67.setFlexHflex("ftFalse");
        FVBox67.setScrollable(false);
        FVBox67.setBoxShadowConfigHorizontalLength(10);
        FVBox67.setBoxShadowConfigVerticalLength(10);
        FVBox67.setBoxShadowConfigBlurRadius(5);
        FVBox67.setBoxShadowConfigSpreadRadius(0);
        FVBox67.setBoxShadowConfigShadowColor("clBlack");
        FVBox67.setBoxShadowConfigOpacity(75);
        FHBox18.addChildren(FVBox67);
        FVBox67.applyProperties();
    }

    public TFLabel lblComplementoCorreiosIE = new TFLabel();

    private void init_lblComplementoCorreiosIE() {
        lblComplementoCorreiosIE.setName("lblComplementoCorreiosIE");
        lblComplementoCorreiosIE.setLeft(0);
        lblComplementoCorreiosIE.setTop(50);
        lblComplementoCorreiosIE.setWidth(120);
        lblComplementoCorreiosIE.setHeight(13);
        lblComplementoCorreiosIE.setCaption("*Mensagem de Valida\u00E7\u00E3o");
        lblComplementoCorreiosIE.setFontColor("13392431");
        lblComplementoCorreiosIE.setFontSize(-11);
        lblComplementoCorreiosIE.setFontName("Tahoma");
        lblComplementoCorreiosIE.setFontStyle("[]");
        lblComplementoCorreiosIE.setFieldName("COMPLEMENTO");
        lblComplementoCorreiosIE.setTable(tbClientesEnderecoIeTemp);
        lblComplementoCorreiosIE.setVerticalAlignment("taVerticalCenter");
        lblComplementoCorreiosIE.setWordBreak(false);
        FVBox66.addChildren(lblComplementoCorreiosIE);
        lblComplementoCorreiosIE.applyProperties();
    }

    public TFVBox FVBox68 = new TFVBox();

    private void init_FVBox68() {
        FVBox68.setName("FVBox68");
        FVBox68.setLeft(0);
        FVBox68.setTop(64);
        FVBox68.setWidth(117);
        FVBox68.setHeight(9);
        FVBox68.setBorderStyle("stNone");
        FVBox68.setPaddingTop(0);
        FVBox68.setPaddingLeft(0);
        FVBox68.setPaddingRight(0);
        FVBox68.setPaddingBottom(0);
        FVBox68.setMarginTop(0);
        FVBox68.setMarginLeft(0);
        FVBox68.setMarginRight(0);
        FVBox68.setMarginBottom(0);
        FVBox68.setSpacing(1);
        FVBox68.setFlexVflex("ftFalse");
        FVBox68.setFlexHflex("ftFalse");
        FVBox68.setScrollable(false);
        FVBox68.setBoxShadowConfigHorizontalLength(10);
        FVBox68.setBoxShadowConfigVerticalLength(10);
        FVBox68.setBoxShadowConfigBlurRadius(5);
        FVBox68.setBoxShadowConfigSpreadRadius(0);
        FVBox68.setBoxShadowConfigShadowColor("clBlack");
        FVBox68.setBoxShadowConfigOpacity(75);
        FVBox66.addChildren(FVBox68);
        FVBox68.applyProperties();
    }

    public TFSchema sc;

    private void init_sc() {
        sc = rn.sc;
        sc.setName("sc");
        TFSchemaItem item7 = new TFSchemaItem();
        item7.setTable(tbClientesEndereco);
        sc.getTables().add(item7);
        TFSchemaItem item8 = new TFSchemaItem();
        item8.setTable(tbClientesEnderecoIe);
        sc.getTables().add(item8);
        TFSchemaItem item9 = new TFSchemaItem();
        item9.setTable(tbCidades);
        sc.getTables().add(item9);
        sc.applyProperties();
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public void btnVoltarClick(final Event<Object> event) {
        if (btnVoltar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnVoltar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public void btnSalvarClick(final Event<Object> event) {
        if (btnSalvar.isAccess()) {
            try {
                //validar o acesso
                IAccessValidator validator = AccessValidatorFactory.getInstance();
                validator.validarAcesso(Long.parseLong(this.getWKey()), "btnSalvar");
            } catch(NumberFormatException | DataException e) {
                ExceptionEngine.register(e);
            }
        }
    }

    public abstract void chkAceitarMudancaResCheck(final Event<Object> event);

    public abstract void hbCEPOnLineAceitarResClick(final Event<Object> event);

    public abstract void hbCEPOnLineManterResClick(final Event<Object> event);

    public abstract void chkAceitarMudancaComCheck(final Event<Object> event);

    public abstract void hbCEPOnLineAceitarComClick(final Event<Object> event);

    public abstract void hbCEPOnLineManterComClick(final Event<Object> event);

    public abstract void chkAceitarMudancaCobrCheck(final Event<Object> event);

    public abstract void hbCEPOnLineAceitarCobClick(final Event<Object> event);

    public abstract void hbCEPOnLineManterCobClick(final Event<Object> event);

    public abstract void hbCEPOnLineAceitarIEClick(final Event<Object> event);

    public abstract void hbCEPOnLineManterIEClick(final Event<Object> event);

    public abstract void grdEndIEmanterAceitarEndIE(final Event<Object> event);

    public abstract void tbClientesEnderecoIeAfterScroll(final Event<Object> event);

}