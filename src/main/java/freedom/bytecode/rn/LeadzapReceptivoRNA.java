package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.LeadzapReceptivoRNW;
import freedom.data.DataException;

public class LeadzapReceptivoRNA extends LeadzapReceptivoRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void carregaDados() throws DataException {
        tbLeadzapMenu.close();
        tbLeadzapMenu.clearFilters();
        tbLeadzapMenu.clearParams();
        tbLeadzapMenu.setOrderBy("MENU_DESCRICAO");
        tbLeadzapMenu.open();
    }

    public void carregaTemplateQualificado(int id) throws DataException {
        tbTemplateQualificado.close();
        tbTemplateQualificado.clearFilters();
        tbTemplateQualificado.clearParams();
        if (id > 0) {
            tbTemplateQualificado.addFilter("ID_EMAIL_MODELO");
            tbTemplateQualificado.addParam("ID_EMAIL_MODELO", id);
        }
        tbTemplateQualificado.open();
    }

    public void carregaTemplateLead(int id) throws DataException {
        tbTemplateLead.close();
        tbTemplateLead.clearFilters();
        tbTemplateLead.clearParams();
        if (id > 0) {
            tbTemplateLead.addFilter("ID_EMAIL_MODELO");
            tbTemplateLead.addParam("ID_EMAIL_MODELO", id);
        }
        tbTemplateLead.open();
    }

    public void carregaDadosItem(int idMenu) throws DataException {
        tbLeadzapItem.close();
        tbLeadzapItem.clearFilters();
        tbLeadzapItem.clearParams();
        tbLeadzapItem.addFilter("ID_MENU");
        tbLeadzapItem.addParam("ID_MENU", idMenu);
        tbLeadzapItem.setOrderBy("SEQUENCIA");
        tbLeadzapItem.open();
    }

    public void carregaLeadArea() throws DataException {
        tbLeadzapArea.close();
        tbLeadzapArea.clearFilters();
        tbLeadzapArea.clearParams();
        tbLeadzapArea.setOrderBy("DESCRICAO");
        tbLeadzapArea.open();
    }

    public void carregaTime() throws DataException {
        tbTime.close();
        tbTime.clearFilters();
        tbTime.clearParams();
        tbTime.open();
    }

    public void carregaTipoEvento(int codGrupo) throws DataException {
        tbEventosTipo.close();
        tbEventosTipo.clearFilters();
        tbEventosTipo.clearParams();
        if (codGrupo > 0) {
            tbEventosTipo.addFilter("COD_GRUPO");
            tbEventosTipo.addParam("COD_GRUPO", codGrupo);
        }
        tbEventosTipo.open();
    }

    public void validaMembroTime(int idTime) throws DataException {
        tbLeadzapTemMembrosTime.close();
        tbLeadzapTemMembrosTime.clearFilters();
        tbLeadzapTemMembrosTime.clearParams();
        tbLeadzapTemMembrosTime.addParam("ID_TIME", idTime);
        tbLeadzapTemMembrosTime.open();
    }

    public void validaSetorMembroTime(int idTime) throws DataException {
        tbLeadzapSetorMembroTime.close();
        tbLeadzapSetorMembroTime.clearFilters();
        tbLeadzapSetorMembroTime.clearParams();
        tbLeadzapSetorMembroTime.addFilter("ID_TIME");
        tbLeadzapSetorMembroTime.addParam("ID_TIME", idTime);
        tbLeadzapSetorMembroTime.open();
    }

    public void carregaTemplateLeadAprov(int id) throws DataException {
        tbTemplateLeadAprov.close();
        tbTemplateLeadAprov.clearFilters();
        tbTemplateLeadAprov.clearParams();
        if (id > 0) {
            tbTemplateLeadAprov.addFilter("ID_EMAIL_MODELO");
            tbTemplateLeadAprov.addParam("ID_EMAIL_MODELO", id);
        }
        tbTemplateLeadAprov.open();
    }

    public void carregaTemplateLeadReprov(int id) throws DataException {
        tbTemplateLeadReprov.close();
        tbTemplateLeadReprov.clearFilters();
        tbTemplateLeadReprov.clearParams();
        if (id > 0) {
            tbTemplateLeadReprov.addFilter("ID_EMAIL_MODELO");
            tbTemplateLeadReprov.addParam("ID_EMAIL_MODELO", id);
        }
        tbTemplateLeadReprov.open();
    }

    public void filtrarCadastroWhatsApp(Integer idMenu) throws DataException {
        tbCadastroWhatsapp.close();
        tbCadastroWhatsapp.clearFilters();
        tbCadastroWhatsapp.clearParams();
        tbCadastroWhatsapp.addFilter("ID_MENU;STATUS");
        tbCadastroWhatsapp.addParam("ID_MENU", idMenu);
        tbCadastroWhatsapp.addParam("STATUS", "S");
        tbCadastroWhatsapp.openWithCols("ID_CELULAR");
    }

    public void filtrarWhatsAppEmpresa(Integer idCelular) throws DataException {
        tbWhatsappEmpresa.close();
        tbWhatsappEmpresa.clearFilters();
        tbWhatsappEmpresa.clearParams();
        tbWhatsappEmpresa.addFilter("ID_CELULAR");
        tbWhatsappEmpresa.addParam("ID_CELULAR", idCelular);
        tbWhatsappEmpresa.setGroupBy("A.COD_EMPRESA");
        tbWhatsappEmpresa.openWithCols("COD_EMPRESA");
    }

    public void filtrarTimeEmpresa(Integer idTime) throws DataException {
        tbReceptTimesMembrosEmpUser.close();
        tbReceptTimesMembrosEmpUser.clearFilters();
        tbReceptTimesMembrosEmpUser.clearParams();
        tbReceptTimesMembrosEmpUser.addParam("ID_TIME", idTime);
        tbReceptTimesMembrosEmpUser.setGroupBy("EU.COD_EMPRESA");
        tbReceptTimesMembrosEmpUser.open();
    }

}
