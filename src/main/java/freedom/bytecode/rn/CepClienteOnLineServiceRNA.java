package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.CepClienteOnLineServiceRNW;
import freedom.data.DataException;
import freedom.data.Value;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import javax.net.ssl.SSLContext;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.security.cert.X509Certificate;

import static freedom.bytecode.form.FrmCadastroRapidoClienteA.removerAcentos;

public class CepClienteOnLineServiceRNA extends CepClienteOnLineServiceRNW {
    private static final long serialVersionUID = 20130827081850L;
    private JSONObject retJsonCepDados;

    public void pesquisarTipoEndCliente(Double codCliente) throws DataException {
        tbClienteTpEnd.close();
        tbClienteTpEnd.addParam("COD_CLIENTE", codCliente);
        tbClienteTpEnd.open();
    }

    public void filtrarClienteEndereco(Double codCliente) throws DataException {
        tbClientesEndereco.close();
        tbClientesEndereco.clearFilters();
        tbClientesEndereco.clearParams();
        tbClientesEndereco.addFilter("COD_CLIENTE");
        tbClientesEndereco.addParam("COD_CLIENTE", codCliente);
        tbClientesEndereco.open();

        tbClientesEnderecoTemp.close();
        tbClientesEnderecoTemp.clearFilters();
        tbClientesEnderecoTemp.clearParams();
        tbClientesEnderecoTemp.open();

        tbClientesEnderecoIe.close();
        tbClientesEnderecoIe.clearFilters();
        tbClientesEnderecoIe.clearParams();
        tbClientesEnderecoIe.addFilter("COD_CLIENTE");
        tbClientesEnderecoIe.addParam("COD_CLIENTE", codCliente);
        tbClientesEnderecoIe.open();

        tbClientesEnderecoIeTemp.close();
        tbClientesEnderecoIeTemp.clearFilters();
        tbClientesEnderecoIeTemp.clearParams();
        tbClientesEnderecoIeTemp.open();
    }

    public boolean getDadosCEPOnLine(Double codCliente,
                                     String strCEP,
                                     String inscEstadual,
                                     Integer codCidade,
                                     Integer tpEndereco) throws Exception {
        boolean ret = false;
        boolean erroAPI = false;
        if (strCEP.isEmpty()) {
            ret = false;
            return ret;
        }
        if (strCEP.trim().length() < 8) {
            ret = false;
            return ret;
        }
        if (strCEP.trim().length() > 8) {
            ret = false;
            return ret;
        }
//        if (codCidade == 0) {
//            ret = false;
//            return ret;
//        }
        String strUrl = "http://viacep.com.br/ws/" + strCEP + "/json/";
        try {
            URL url = new URL(strUrl);
            URLConnection connection = url.openConnection();
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(15000);
            connection.connect();
        } catch (IOException e) {
            e.printStackTrace();
            ret = false;
            return ret;
        }
        String xResposta;
        URL url = new URL(strUrl);
        URLConnection urlConnection = url.openConnection();
        urlConnection.setConnectTimeout(15000);
        urlConnection.setReadTimeout(15000);
        InputStream is = urlConnection.getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));

        StringBuilder jsonSb = new StringBuilder();

        br.lines().forEach(l -> jsonSb.append(l.trim()));
        xResposta = jsonSb.toString();
        JSONObject json = new JSONObject(xResposta);
        if (json.has("erro")) {
            erroAPI = json.getBoolean("erro");
        } else if (json.has("cep")) {
            if (null != tpEndereco) {
                if (tbClientesEnderecoTemp.isEmpty()) {
                    tbClientesEnderecoTemp.append();
                } else {
                    tbClientesEnderecoTemp.edit();
                }
                tbClientesEnderecoTemp.setField("COD_CLIENTE", codCliente);
                tbClientesEnderecoTemp.setField("CEP", strCEP);
                tbClientesEnderecoTemp.setField("UF", removerAcentos(json.getString("uf").trim().toUpperCase()));
                if (json.getString("logradouro").trim().length() > 50) {
                    tbClientesEnderecoTemp.setField("RUA", removerAcentos(json.getString("logradouro").substring(0, 50).trim().toUpperCase()));
                } else {
                    tbClientesEnderecoTemp.setField("RUA", removerAcentos(json.getString("logradouro").trim().toUpperCase()));
                }
                tbClientesEnderecoTemp.setField("CIDADE", removerAcentos(json.getString("localidade").toUpperCase().trim().toUpperCase()));
                if (json.getString("bairro").trim().length() > 30) {
                    tbClientesEnderecoTemp.setField("BAIRRO", removerAcentos(json.getString("bairro").substring(0, 30).trim().toUpperCase()));
                } else {
                    tbClientesEnderecoTemp.setField("BAIRRO", removerAcentos(json.getString("bairro").trim().toUpperCase()));
                }
                tbClientesEnderecoTemp.setField("COMPLEMENTO", removerAcentos(json.getString("complemento").trim().toUpperCase()));
                tbClientesEnderecoTemp.setField("CODCIDADEIBGE", removerAcentos(json.getString("ibge")));
                tbClientesEnderecoTemp.post();
            }
            ret = true;
        } else {
            ret = false;
        }
        if (erroAPI) {
            if (tbClientesEnderecoTemp.isEmpty()) {
                tbClientesEnderecoTemp.append();
            } else {
                tbClientesEnderecoTemp.edit();
            }
            tbClientesEnderecoTemp.setField("COD_CLIENTE", codCliente);
            tbClientesEnderecoTemp.setField("CEP", strCEP);
            tbClientesEnderecoTemp.setField("UF", "NE");
            tbClientesEnderecoTemp.setField("RUA", "Não Encontrado");
            tbClientesEnderecoTemp.setField("CIDADE", "Não Encontrado");
            tbClientesEnderecoTemp.setField("BAIRRO", "Não Encontrado");
            tbClientesEnderecoTemp.setField("COMPLEMENTO", "Não Encontrado");
            tbClientesEnderecoTemp.setField("CODCIDADEIBGE", 0);
            tbClientesEnderecoTemp.post();
            return false;
        } else {
            return ret;
        }
    }

    public Integer getCodCidade(String strUF,
                                String strCidade,
                                String strCEP,
                                Integer codIBGE,
                                Value qtdeReg) throws DataException {
        Integer codCidade;
        tbCidades.close();
        tbCidades.clearFilters();
        tbCidades.clearParams();
        if (!strUF.isEmpty()) {
            tbCidades.addFilter("UF");
            tbCidades.addParam("UF", strUF.trim().toUpperCase());
        }
        if (!strCidade.isEmpty()) {
            tbCidades.addFilter("DESCRICAO");
            tbCidades.addParam("DESCRICAO", removerAcentos(strCidade.trim().toUpperCase()));
        }
        if (!strCEP.isEmpty()) {
            tbCidades.addFilter("CEP");
            tbCidades.addParam("CEP", strCEP.trim().toUpperCase());
        }
        if (codIBGE > 0) {
            tbCidades.addFilter("CODCIDADEIBGE");
            tbCidades.addParam("CODCIDADEIBGE", codIBGE);
        }
        tbCidades.open();
        if (!tbCidades.isEmpty() && tbCidades.count() == 1) {
            codCidade = tbCidades.getCOD_CIDADES().asInteger();
            qtdeReg.setValue(tbCidades.count());
        } else {
            tbCidades.close();
            tbCidades.clearFilters();
            tbCidades.clearParams();
            if (!strUF.isEmpty()) {
                tbCidades.addFilter("UF");
                tbCidades.addParam("UF", strUF.trim().toUpperCase());
            }
            if (!strCidade.isEmpty()) {
                tbCidades.addFilter("DESCRICAO");
                tbCidades.addParam("DESCRICAO", removerAcentos(strCidade.trim().toUpperCase()));
            }
            if (codIBGE > 0) {
                tbCidades.addFilter("CODCIDADEIBGE");
                tbCidades.addParam("CODCIDADEIBGE", codIBGE);
            }
            tbCidades.open();
            if (!tbCidades.isEmpty() && tbCidades.count() == 1) {
                codCidade = tbCidades.getCOD_CIDADES().asInteger();
                qtdeReg.setValue(tbCidades.count());
            } else {
                tbCidades.close();
                tbCidades.clearFilters();
                tbCidades.clearParams();
                if (!strUF.isEmpty()) {
                    tbCidades.addFilter("UF");
                    tbCidades.addParam("UF", strUF.trim().toUpperCase());
                }
                if (!strCidade.isEmpty()) {
                    tbCidades.addFilter("DESCRICAO");
                    tbCidades.addParam("DESCRICAO", removerAcentos(strCidade.trim().toUpperCase()));
                }
                if (!strCEP.isEmpty()) {
                    tbCidades.addFilter("CEP");
                    tbCidades.addParam("CEP", strCEP.trim().toUpperCase());
                }
                tbCidades.open();
                if (!tbCidades.isEmpty() && tbCidades.count() == 1) {
                    codCidade = tbCidades.getCOD_CIDADES().asInteger();
                    qtdeReg.setValue(tbCidades.count());
                } else {
                    tbCidades.close();
                    tbCidades.clearFilters();
                    tbCidades.clearParams();
                    if (!strUF.isEmpty()) {
                        tbCidades.addFilter("UF");
                        tbCidades.addParam("UF", strUF.trim().toUpperCase());
                    }
                    if (!strCidade.isEmpty()) {
                        tbCidades.addFilter("DESCRICAO_EXATA");
                        tbCidades.addParam("DESCRICAO_EXATA", removerAcentos(strCidade.trim()));
                    }
                    tbCidades.open();
                    if (!tbCidades.isEmpty() && tbCidades.count() == 1) {
                        codCidade = tbCidades.getCOD_CIDADES().asInteger();
                        qtdeReg.setValue(tbCidades.count());
                    } else {
                        codCidade = 0;
                        qtdeReg.setValue(tbCidades.count());
                    }
                }
            }
        }
        return codCidade;
    }

    public boolean servicoOk(String strCEP, Boolean allEnd, Value oRetMensagem) throws Exception {
        boolean ret = false;
        boolean erroAPI = false;
        if (strCEP.isEmpty()) {
            ret = false;
            return ret;
        }
        if (strCEP.trim().length() < 8) {
            oRetMensagem.setValue("CEP Inválido.");
            ret = false;
            return ret;
        }
        if (strCEP.trim().length() > 8) {
            oRetMensagem.setValue("CEP Inválido.");
            ret = false;
            return ret;
        }
        String strUrl;
        if (allEnd) {
            strUrl = "http://viacep.com.br/";
            try {
                methodPing(strUrl, oRetMensagem);
            } catch (IOException e) {
                e.printStackTrace();
                strUrl = "https://viacep.com.br/";
                try {
                    methodPing(strUrl, oRetMensagem);
                } catch (IOException ex) {
                    ex.printStackTrace();
                    oRetMensagem.setValue("Dica: Servidor de Aplicação CRM Service não conseguiu se conectar a API \"https://viacep.com.br/\". Verifique com seu Departamento de TI.");
                    ret = false;
                    return ret;
                }
            }
            if (!oRetMensagem.isNull()) {
                ret = false;
                return ret;
            } else {
                strUrl = "http://viacep.com.br/ws/" + strCEP + "/json/";
                try {
                    methodPing(strUrl, oRetMensagem);
                } catch (IOException e) {
                    e.printStackTrace();
                    strUrl = "https://viacep.com.br/ws/" + strCEP + "/json/";
                    try {
                        methodPing(strUrl, oRetMensagem);
                    } catch (IOException ex) {
                        ex.printStackTrace();
                        oRetMensagem.setValue("Dica: Servidor de Aplicação CRM Service não conseguiu conectar-se a API \"https://viacep.com.br/\". Verifique com seu Departamento de TI.");
                        ret = false;
                        return ret;
                    }
                }
                if (!oRetMensagem.isNull()) {
                    ret = false;
                    return ret;
                }
            }
        } else {
            strUrl = "http://viacep.com.br/";
            try {
                URL url = new URL(strUrl);
                URLConnection connection = url.openConnection();
                connection.setConnectTimeout(2000);
                connection.setReadTimeout(2000);
                connection.connect();
            } catch (IOException e) {
                e.printStackTrace();
                oRetMensagem.setValue("Dica: Servidor de Aplicação CRM Service não conseguiu conectar-se a API \"https://viacep.com.br/\". Verifique com seu Departamento de TI.");
                ret = false;
                return ret;
            }
            strUrl = "http://viacep.com.br/ws/" + strCEP + "/json/";
            try {
                URL url = new URL(strUrl);
                URLConnection connection = url.openConnection();
                connection.setConnectTimeout(2000);
                connection.setReadTimeout(2000);
                connection.connect();
            } catch (IOException e) {
                e.printStackTrace();
                oRetMensagem.setValue("CEP não encontrado na API dos correios.");
                ret = false;
                return ret;
            }
        }
        try {
            String xResposta;
            URL url = new URL(strUrl);
            URLConnection urlConnection = url.openConnection();
            urlConnection.setConnectTimeout(2000);
            urlConnection.setReadTimeout(2000);
            InputStream is = urlConnection.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));

            StringBuilder jsonSb = new StringBuilder();

            br.lines().forEach(l -> jsonSb.append(l.trim()));

            xResposta = jsonSb.toString();
            JSONObject json = new JSONObject(xResposta);
            if (json.has("erro")) {
                erroAPI = json.getBoolean("erro");
            } else if (json.has("cep")) {
                ret = true;
            } else {
                ret = false;
            }
            if (erroAPI) {
                oRetMensagem.setValue("CEP não encontrado na API dos correios.");
                return false;
            } else {
                this.retJsonCepDados = json;
                return ret;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            oRetMensagem.setValue("Dica: Servidor de Aplicação CRM Service não conseguiu conectar-se a API \"https://viacep.com.br/\". Verifique com seu Departamento de TI.");
            return false;
        }
    }

    public JSONObject retJsonCepDados() {
        return this.retJsonCepDados;
    }

    public HttpResponse methodPing(String strUrl, Value oRetMensagem) throws Exception {
        HttpGet httpget = new HttpGet(strUrl);
        httpget.addHeader("Content-Type", "charset=utf-8");

        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, (TrustStrategy) (final X509Certificate[] chain, final String authType) -> true)
                .build();

        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(2000)
                .setConnectionRequestTimeout(2000)
                .setSocketTimeout(2000).build();

        CloseableHttpClient client = HttpClientBuilder.create().setSSLSocketFactory(sslsf).useSystemProperties().setDefaultRequestConfig(config).build();
        HttpResponse response = client.execute(httpget);
        int code;
        try {
            code = response.getStatusLine().getStatusCode();
            if (code == 401 || code == 403) {
                System.out.println("Access token is invalid or expired. Regenerating access token....");
            } else {
                HttpEntity entity = response.getEntity();
                String strResponse = null;
                if (entity != null) {
                    strResponse = EntityUtils.toString(entity);
                }
                System.out.println("strResponse:" + strResponse);
            }
        } catch (ClientProtocolException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            oRetMensagem.setValue("Dica: Servidor de Aplicação CRM Service não conseguiu conectar-se a API \"https://viacep.com.br/\". Verifique com seu Departamento de TI.");
        } catch (IOException e) {
            e.printStackTrace();
            oRetMensagem.setValue("Dica: Servidor de Aplicação CRM Service não conseguiu conectar-se a API \"https://viacep.com.br/\". Verifique com seu Departamento de TI.");
        } finally {
            httpget.releaseConnection();
        }
        return response;
    }

}
