package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.GerencialPainelPreferenciaRNW;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class GerencialPainelPreferenciaRNA extends GerencialPainelPreferenciaRNW {
    private static final long serialVersionUID = 20130827081850L;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    public void carregaDados() throws DataException {
        tbPainelGerencialUsuario.close();
        tbPainelGerencialUsuario.clearFilters();
        tbPainelGerencialUsuario.clearParams();
        tbPainelGerencialUsuario.addFilter("ATIVO;USUARIO_LOGADO");
        tbPainelGerencialUsuario.addParam("ATIVO", "S");
        tbPainelGerencialUsuario.addParam("USUARIO_LOGADO", usuarioLogado);
        tbPainelGerencialUsuario.open();
    }

    public void carregaIndicadores(Integer idPainel) throws DataException {
//      Sempre limpar a grid da quebra, pq ao mudar painel, so limpa indicadores
        tbGridQuebraInd.close();
        tbGridIndicadores.close();
        tbGridIndicadores.clearFilters();
        tbGridIndicadores.clearParams();
        tbGridIndicadores.addFilter("ID_PAINEL");
        tbGridIndicadores.addParam("ID_PAINEL", idPainel);
        tbGridIndicadores.open();
    }

    public void carregaEmpresasUsuarios() throws DataException {
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.addFilter("NOME");
        tbEmpresasUsuarios.addParam("NOME", usuarioLogado);
        tbEmpresasUsuarios.open();
    }

    public void carregaQuebraIndicadores(int idIndicador, int idGrupoClasse) throws DataException {
        tbGridQuebraInd.close();
        tbGridQuebraInd.clearFilters();
        tbGridQuebraInd.clearParams();
        tbGridQuebraInd.addFilter("ID_INDICADOR;ID_GRUPO_CLASSE");
        tbGridQuebraInd.addParam("ID_INDICADOR", idIndicador);
        tbGridQuebraInd.addParam("ID_GRUPO_CLASSE", idGrupoClasse);
        tbGridQuebraInd.open();
    }

    public void atualizaPreferencia(int idPainel, int idIndicador, int idQuebra) throws DataException {

        tbPreferencia.close();
        tbPreferencia.clearFilters();
        tbPreferencia.clearParams();
        tbPreferencia.addFilter("ID_PAINEL;ID_INDICADOR;NOME");
        tbPreferencia.addParam("ID_PAINEL", idPainel);
        tbPreferencia.addParam("ID_INDICADOR", idIndicador);
        tbPreferencia.addParam("NOME", usuarioLogado);
        tbPreferencia.open();

        if (tbPreferencia.count() == 0) {
            tbPreferencia.close();
            tbPreferencia.clearFilters();
            tbPreferencia.clearParams();
            tbPreferencia.addFilter("ID_PAINEL;NOME");
            tbPreferencia.addParam("ID_PAINEL", idPainel);
            tbPreferencia.addParam("NOME", usuarioLogado);
            tbPreferencia.open();
        }

        if (tbPreferencia.count() > 0) {
            tbPreferencia.delete();
            tbPreferencia.post();
        }

        tbPreferencia.append();
        tbPreferencia.setID_PAINEL(idPainel);
        tbPreferencia.setNOME(usuarioLogado);
        if (idIndicador > 0) {
            tbPreferencia.setID_INDICADOR(idIndicador);
        }
        if (idQuebra > 0) {
            tbPreferencia.setID_QUEBRA(idQuebra);
        }
        tbPreferencia.post();
        tbPreferencia.applyUpdates();

    }

    public void atualizaPreferenciaUsuario(int idPainel) throws DataException {
        carregaEmpresasUsuarios();
        if (tbEmpresasUsuarios.count() == 1) {
            tbEmpresasUsuarios.edit();
            if (idPainel > 0) {
                tbEmpresasUsuarios.setID_PAINEL(idPainel);
            } else {
                tbEmpresasUsuarios.setID_PAINEL(null);
            }
            tbEmpresasUsuarios.post();
            tbEmpresasUsuarios.applyUpdates();
        }
    }

}
