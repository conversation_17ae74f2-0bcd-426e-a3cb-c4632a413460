package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.IntegracaoSapRNW;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.pkg.PkgCrmPartsRNA;
import freedom.util.pkg.PkgRbSapRNA;

public class IntegracaoSapRNA extends IntegracaoSapRNW {
    private static final long serialVersionUID = 20130827081850L;

    private final PkgRbSapRNA pkgRbSapRNA = new PkgRbSapRNA();

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public String gerarFilaConsultaOrcamCc(Double codCliente, Double codEmpresa, Double codOrcMapa, String consultaCliBloq, String resultCompleto, String geraMov, String usuarioConsulta, Value idFila, Value valorCc) throws DataException {
        return pkgRbSapRNA.gerarConsultaOrcamCc(codCliente, codEmpresa, codOrcMapa, consultaCliBloq, resultC<PERSON>ple<PERSON>, gera<PERSON><PERSON>, usuarioConsulta, idFila, valorCc);
    }

    public String consultaStatusFila(Double idFila, Value aMensagem, Value aDetalhar) throws DataException {
        return pkgRbSapRNA.retornoConsultaCc(idFila, aMensagem, aDetalhar);
    }

    public String cancelarFilaConsultaCC(Double idFila, String timeout) throws DataException {
        return pkgRbSapRNA.cancelafilaconsultacc(idFila, timeout);
    }
}
