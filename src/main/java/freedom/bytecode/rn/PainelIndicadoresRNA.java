package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.PainelIndicadoresRNW;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;

public class PainelIndicadoresRNA extends PainelIndicadoresRNW {

    private static final long serialVersionUID = 20130827081850L;

    private int idGrupoClasse = 0;

    public void carregaComboPainel(int idGrupoClasse) throws DataException {
        this.idGrupoClasse = idGrupoClasse;
        this.tbComboGrupo.close();
        this.tbComboGrupo.clearFilters();
        this.tbComboGrupo.clearParams();
        this.tbComboGrupo.setFilterID_GRUPO_CLASSE(idGrupoClasse);
        this.tbComboGrupo.open();
    }

    public void carregaGridPainel(String ativo,
                                  double idGrupo) throws DataException {
        this.tbListaPainel.close();
        this.tbListaPainel.clearFilters();
        this.tbListaPainel.clearParams();
        this.tbListaPainel.setFilterATIVO(ativo);
        this.tbListaPainel.setFilterID_GRUPO_CLASSE(this.idGrupoClasse);
        if (idGrupo > 0) {
            this.tbListaPainel.setFilterID_GRUPO(idGrupo);
        }
        this.tbListaPainel.open();
    }

    public void pesquisarIndicadores(int idPainel) throws DataException {
        this.tbGridIndicadores.close();
        this.tbGridIndicadores.clearFilters();
        this.tbGridIndicadores.clearParams();
        this.tbGridIndicadores.setFilterID_PAINEL(idPainel);
        this.tbGridIndicadores.open();
    }

    public void pesquisarAcessoFuncao(int idPainel) throws DataException {
        this.tbGridAcessoFuncao.close();
        this.tbGridAcessoFuncao.clearFilters();
        this.tbGridAcessoFuncao.clearParams();
        this.tbGridAcessoFuncao.setFilterID_PAINEL(idPainel);
        this.tbGridAcessoFuncao.open();
    }

    public void pesquisaPainelIndicador(int idPainel,
                                        int idGrupo,
                                        int idIndicador,
                                        boolean order,
                                        int ordem) throws DataException {
        this.tbPainelIndicador.close();
        this.tbPainelIndicador.clearFilters();
        this.tbPainelIndicador.clearParams();
        this.tbPainelIndicador.setFilterID_PAINEL(idPainel);
        if (order) {
            this.tbPainelIndicador.setFilterDIF_ID_INDICADOR(idIndicador);
            this.tbPainelIndicador.setFilterORDEM_MAIOR_IGUAL(ordem);
        } else {
            if (idGrupo > 0) {
                this.tbPainelIndicador.setFilterID_GRUPO(idGrupo);
            }
            if (idIndicador > 0) {
                this.tbPainelIndicador.setFilterID_INDICADOR(idIndicador);
            }
        }
        this.tbPainelIndicador.setOrderBy("DISPLAY_ORDER");
        this.tbPainelIndicador.open();
    }

    public void pesquisaPainelAcessoFuncao(int idPainel,
                                           int codFuncao) throws DataException {
        this.tbPainelAcessoFuncao.close();
        this.tbPainelAcessoFuncao.clearFilters();
        this.tbPainelAcessoFuncao.clearParams();
        this.tbPainelAcessoFuncao.setFilterID_PAINEL(idPainel);
        if (codFuncao > 0) {
            this.tbPainelAcessoFuncao.setFilterCOD_FUNCAO(codFuncao);
        }
        this.tbPainelAcessoFuncao.open();
    }

    public void pesquisaPainelSemafaro(int idPainel) throws DataException {
        this.tbPainelSemaforo.close();
        this.tbPainelSemaforo.clearFilters();
        this.tbPainelSemaforo.clearParams();
        this.tbPainelSemaforo.setFilterID_PAINEL(idPainel);
        this.tbPainelSemaforo.open();
    }

    public void excluirPainel(int idPainel) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            this.tbPainelAcessoFuncao.setSession(session);
            this.pesquisaPainelAcessoFuncao(idPainel,
                    0);
            this.tbPainelIndicador.setSession(session);
            this.pesquisaPainelIndicador(idPainel,
                    0,
                    0,
                    false,
                    0);
            this.tbPainelSemaforo.setSession(session);
            this.pesquisaPainelSemafaro(idPainel);
            this.tbPainel.setSession(session);
            this.tbPainel.close();
            this.tbPainel.clearFilters();
            this.tbPainel.clearParams();
            this.tbPainel.setFilterID_PAINEL(idPainel);
            this.tbPainel.open();
            int tbPainelAcessoFuncaoCount = this.tbPainelAcessoFuncao.count();
            if (tbPainelAcessoFuncaoCount > 0) {
                this.tbPainelAcessoFuncao.first();
                while (Boolean.FALSE.equals(this.tbPainelAcessoFuncao.eof())) {
                    this.tbPainelAcessoFuncao.delete();
                    this.tbPainelAcessoFuncao.applyUpdates();
                }
            }
            int tbPainelIndicadorCount = this.tbPainelIndicador.count();
            if (tbPainelIndicadorCount > 0) {
                this.tbPainelIndicador.first();
                while (Boolean.FALSE.equals(this.tbPainelIndicador.eof())) {
                    this.tbPainelIndicador.delete();
                    this.tbPainelIndicador.applyUpdates();
                }
            }
            int tbPainelSemaforoCount = this.tbPainelSemaforo.count();
            if (tbPainelSemaforoCount > 0) {
                this.tbPainelSemaforo.first();
                while (Boolean.FALSE.equals(this.tbPainelSemaforo.eof())) {
                    this.tbPainelSemaforo.delete();
                    this.tbPainelSemaforo.applyUpdates();
                }
            }
            this.tbPainel.delete();
            this.tbPainel.applyUpdates();
            session.commit();
        } catch (DataException dataException) {
            session.rollback();
            throw dataException;
        } finally {
            session.close();
        }
    }

    public void alterarOrdemIndicadores(int idPainel,
                                        int idIndicador,
                                        int order,
                                        String direcao) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        int newOrder;
        try {
            session.open();
            this.tbPainelIndicador.setSession(session);
            this.pesquisaPainelIndicador(idPainel,
                    0,
                    idIndicador,
                    false,
                    0);
            if (direcao.equals("Acima")) {
                if (order > 1) {
                    newOrder = order - 1;
                    this.atualizaOrderPainelIndicador(newOrder);
                    this.tbPainelIndicador.applyUpdates();
                    this.tbPainelIndicador.setSession(session);
                    this.pesquisaPainelIndicador(idPainel,
                            0,
                            idIndicador,
                            true,
                            newOrder);
                    newOrder += 1;
                    this.tbPainelIndicador.first();
                    while (Boolean.FALSE.equals(this.tbPainelIndicador.eof())) {
                        this.atualizaOrderPainelIndicador(newOrder);
                        newOrder += 1;
                        this.tbPainelIndicador.next();
                    }
                }
            } else {
                newOrder = order + 1;
                int idIndicadorAnt = this.carregaRegistroAnteriorIndicador(idPainel,
                        newOrder);
                this.pesquisaPainelIndicador(idPainel,
                        0,
                        idIndicador,
                        true,
                        newOrder);
                int tbPainelIndicadorCount = this.tbPainelIndicador.count();
                if (tbPainelIndicadorCount > 0) {
                    this.pesquisaPainelIndicador(idPainel,
                            0,
                            idIndicador,
                            false,
                            0);
                    /*atualiza registro atual para o proximo*/
                    this.atualizaOrderPainelIndicador(newOrder);
                    this.tbPainelIndicador.applyUpdates();
                    /*atualiza o proximo para ordem anterior*/
                    this.pesquisaPainelIndicador(idPainel,
                            0,
                            idIndicadorAnt,
                            false,
                            0);
                    this.atualizaOrderPainelIndicador(order);
                    this.tbPainelIndicador.applyUpdates();
                    /*Corrige demais ordem*/
                    this.tbPainelIndicador.setSession(session);
                    this.pesquisaPainelIndicador(idPainel,
                            0,
                            idIndicador,
                            true,
                            newOrder);
                    newOrder += 1;
                    this.tbPainelIndicador.first();
                    while (Boolean.FALSE.equals(this.tbPainelIndicador.eof())) {
                        this.atualizaOrderPainelIndicador(newOrder);
                        newOrder += 1;
                        this.tbPainelIndicador.next();
                    }
                }
            }
            this.tbPainelIndicador.applyUpdates();
            session.commit();
        } catch (DataException dataException) {
            session.rollback();
            throw dataException;
        } finally {
            session.close();
        }
    }

    private int carregaRegistroAnteriorIndicador(int idPainel,
                                                 int ordem) throws DataException {
        this.tbPainelIndicador.close();
        this.tbPainelIndicador.clearFilters();
        this.tbPainelIndicador.clearParams();
        this.tbPainelIndicador.setFilterID_PAINEL(idPainel);
        this.tbPainelIndicador.setFilterDISPLAY_ORDER(ordem);
        this.tbPainelIndicador.setOrderBy("DISPLAY_ORDER");
        this.tbPainelIndicador.open();
        return this.tbPainelIndicador.getID_INDICADOR().asInteger();
    }

    private void atualizaOrderPainelIndicador(int order) throws DataException {
        this.tbPainelIndicador.edit();
        this.tbPainelIndicador.setDISPLAY_ORDER(Integer.toString(order));
        this.tbPainelIndicador.post();
    }

}