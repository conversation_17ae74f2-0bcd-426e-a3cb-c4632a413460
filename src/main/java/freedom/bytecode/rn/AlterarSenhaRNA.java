package freedom.bytecode.rn;

import freedom.bytecode.cursor.EMPRESAS_USUARIOS;
import freedom.bytecode.rn.wizard.AlterarSenhaRNW;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.util.ApplicationUtil;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class AlterarSenhaRNA extends AlterarSenhaRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final LoginRNA login;

    private final String dataSource = ApplicationUtil.getValue(
            "DATASOURCE_SELECIONADO"
    );

    public AlterarSenhaRNA() {
        this.login = new LoginRNA();
    }

    public void alterarSenha(
            String usuario
            ,String senhaAtual
            ,String senhaNova
            ,String confirmaSenha
    ) throws Exception {
        if (senhaAtual.trim().equals(senhaNova)) {
            throw new DataException("A senha atual não pode ser a mesma que a nova senha.");
        }
        if (!senhaNova.trim().equals(confirmaSenha)) {
            throw new DataException("A nova senha não confere com a confirmação da senha.");
        }
        String validou = this.login.validarLogin(
                usuario
                ,senhaAtual
                ,this.dataSource
                ,false
        );
        if (!validou.isEmpty()) {
            throw new DataException(
                    validou
            );
        }
        this.trocarSenha(
                usuario
                ,this.dataSource
                ,senhaNova
        );
    }

    private void trocarSenha(
            String usuario
            ,String schema
            ,String novaSenha
    ) throws Exception {
        PreparedStatement preparedStatement;
        Connection connection = null;
        try {
            ISession session;
            session = SessionFactory.getInstance().getSession(schema);
            session.open();
            connection = session.getConn();
            preparedStatement = connection.prepareStatement(
                    "ALTER USER "
                            + usuario
                            + " IDENTIFIED BY "
                            + novaSenha.replace(
                                    "\""
                            ,""
                    ).replace(
                            ";"
                            ,""
                    )
            );
            preparedStatement.setEscapeProcessing(
                    false
            );
            preparedStatement.execute();
        } catch (
                DataException dataException
        ) {
            throw new DataException("A senha atual não confere.");
        } catch (
                SQLException exception
        ) {
            throw new Exception(
                    "Erro ao alterar senha."
                            + System.lineSeparator()
                            + "Tente novamente."
            );
        } finally {
            try {
                if ((connection != null)
                        && (!connection.isClosed())) {
                    connection.close();
                }
            } catch (
                    Exception exception
            ) {
                // aqui nao me interessa tratar erros
            }
        }
    }

    public boolean isUsuarioObrigadoATrocarSenhaAoLogar(
            String loginUsuario
    ) {
        boolean retFuncao = false;
        try {
            EMPRESAS_USUARIOS tbEmpresaUsuarios = new EMPRESAS_USUARIOS(
                    "tbEmpresaUsuarios"
            );
            tbEmpresaUsuarios.close();
            tbEmpresaUsuarios.clearFilters();
            tbEmpresaUsuarios.clearParams();
            tbEmpresaUsuarios.setFilterNOME(
                    loginUsuario
            );
            tbEmpresaUsuarios.open();
            boolean tbEmpresaUsuariosNotEmpty = !tbEmpresaUsuarios.isEmpty();
            if (tbEmpresaUsuariosNotEmpty) {
                retFuncao = tbEmpresaUsuarios.getTROCAR_SENHA_LOGAR().asString().equals("S");
            }
            tbEmpresaUsuarios.close();
        } catch (
                DataException dataException
        ) {
            retFuncao = false;
        }
        return retFuncao;
    }

    public void registrarATrocaDeSenhaDoUsuario(
            String loginUsuario
    ) throws DataException {
        EMPRESAS_USUARIOS tbEmpresaUsuarios = new EMPRESAS_USUARIOS(
                "tbEmpresaUsuarios"
        );
        tbEmpresaUsuarios.close();
        tbEmpresaUsuarios.clearFilters();
        tbEmpresaUsuarios.clearParams();
        tbEmpresaUsuarios.setFilterNOME(
                loginUsuario
        );
        tbEmpresaUsuarios.open();
        boolean tbEmpresaUsuariosNotEmpty = !tbEmpresaUsuarios.isEmpty();
        if (tbEmpresaUsuariosNotEmpty) {
            tbEmpresaUsuarios.edit();
            tbEmpresaUsuarios.setTROCAR_SENHA_LOGAR(
                    "N"
            );
            tbEmpresaUsuarios.post();
            tbEmpresaUsuarios.applyUpdates();
            tbEmpresaUsuarios.commitUpdates();
        }
        tbEmpresaUsuarios.close();
    }

}