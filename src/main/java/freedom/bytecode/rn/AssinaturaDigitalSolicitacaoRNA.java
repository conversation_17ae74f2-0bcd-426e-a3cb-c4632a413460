package freedom.bytecode.rn;
import freedom.data.DataException;

import freedom.bytecode.rn.wizard.AssinaturaDigitalSolicitacaoRNW;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import gudusoft.gsqlparser.ESetScope;
import org.json.JSONObject;

public class AssinaturaDigitalSolicitacaoRNA extends AssinaturaDigitalSolicitacaoRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public void filtrarTbSolicitacoesAssinaturas(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros, Double idEnvelope) throws DataException {
        String valorCodigo = CrmAssinaturaDigitalUtils.getValorCodigo(tipoAssinatura, jsonParametros);
        tbSolicitacoesAssinaturas.close();
        tbSolicitacoesAssinaturas.clearFilters();
        tbSolicitacoesAssinaturas.clearParams();
        tbSolicitacoesAssinaturas.addParam("TIPO_DOCUMENTO", tipoAssinatura.getTipo());
        tbSolicitacoesAssinaturas.addParam("VALOR_CODIGO", valorCodigo);
        if (idEnvelope > 0) {
            tbSolicitacoesAssinaturas.setFilterID_ENVELOPE(idEnvelope);
        }else{
            tbSolicitacoesAssinaturas.setFilterASSINATURA_NOVA("");
        }
        tbSolicitacoesAssinaturas.open();
    }

    public Double getIdDestinatario() {
        return tbSolicitacoesAssinaturas.getID_DESTINATARIO().asDecimal();
    }

    /**
     * abre o cursor nbsapi_envelope_fila_resumo
     * @param tipoAssinatura
     * @param jsonParametros
     * @param idEnvelope pode ser null, e caso seja null então busca o ultimo envelope enviado, e caso não existe abre como assinatura pendente
     * @throws DataException
     */
    public void filtrarTbNbsapiEnvelopeFilaResumo(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros, Double idEnvelope) throws DataException {
        String valorCodigo = CrmAssinaturaDigitalUtils.getValorCodigo(tbAssinaturaDigital.getTIPO_CODIGO().asString(), jsonParametros);
        tbNbsapiEnvelopeFilaResumo.close();
        tbNbsapiEnvelopeFilaResumo.clearFilters();
        tbNbsapiEnvelopeFilaResumo.clearParams();
        tbNbsapiEnvelopeFilaResumo.setFilterTIPO_ASSINATURA(tipoAssinatura.getTipo());
        tbNbsapiEnvelopeFilaResumo.setFilterVALOR_CODIGO(valorCodigo);
        if (idEnvelope != null) {
            tbNbsapiEnvelopeFilaResumo.setFilterID_ENVELOPE(idEnvelope);
        }else{
            tbNbsapiEnvelopeFilaResumo.setFilterASSINATURA_MAIS_RECENTE_ATIVA(tipoAssinatura.getTipo(), valorCodigo);
        }
        tbNbsapiEnvelopeFilaResumo.open();
    }

    public boolean tbNbsapiEnvelopeFilaResumoIsEmpty() {
        return !this.tbNbsapiEnvelopeFilaResumo.isActive() || this.tbNbsapiEnvelopeFilaResumo.isEmpty();
    }

    public void filtrarTbAssinaturaDigital(TipoAssinaturaStrategy tipoAssinatura) throws DataException {
        tbAssinaturaDigital.close();
        tbAssinaturaDigital.clearFilters();
        tbAssinaturaDigital.clearParams();
        tbAssinaturaDigital.setFilterTIPO_ASSINATURA(tipoAssinatura.getTipo());
        tbAssinaturaDigital.open();
    }

    public void filtrarTbAssinaturaDigitalDocumentos() throws DataException {
        tbAssinaturaDigitalDoc.close();
        tbAssinaturaDigitalDoc.clearFilters();
        tbAssinaturaDigitalDoc.clearParams();
        tbAssinaturaDigitalDoc.setFilterID_ASSINATURA_DIGITAL(tbAssinaturaDigital.getID_ASSINATURA_DIGITAL().asDecimal());
        tbAssinaturaDigitalDoc.open();
    }

    public void filtrarTbNbsapiDocumentosAssinados() throws DataException {
        tbNbsapiDocumentosAssinados.close();
        tbNbsapiDocumentosAssinados.clearFilters();
        tbNbsapiDocumentosAssinados.clearParams();
        tbNbsapiDocumentosAssinados.setFilterENVELOPE_ID(tbNbsapiEnvelopeFilaResumo.getID().asDecimal());
        tbNbsapiDocumentosAssinados.open();
    }

    public boolean tbAssinaturaDigitalDocIsEmpty() {
        return !this.tbAssinaturaDigitalDoc.isActive() || this.tbAssinaturaDigitalDoc.isEmpty();
    }


    public boolean tbAssinaturaDigitalIsEmpty() {
        return !this.tbAssinaturaDigital.isActive() || this.tbAssinaturaDigital.isEmpty();
    }

    public boolean tbSolicitacoesAssinaturasIsEmpty() {
        return !this.tbSolicitacoesAssinaturas.isActive() || this.tbSolicitacoesAssinaturas.isEmpty();
    }

    public Double getIdEnvelope() {
        return tbNbsapiEnvelopeFilaResumo.getID().asDecimal();
    }
}
