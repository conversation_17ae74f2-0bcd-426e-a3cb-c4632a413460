package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.LimiteFinanceiroRNW;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.pkg.PkgCrmPartsRNA;

public class LimiteFinanceiroRNA extends LimiteFinanceiroRNW  {
    private static final long serialVersionUID = 20130827081850L;
    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void openLimiteCliente(double codCliente) throws DataException {
        if (tbLimiteCliente.isActive()){
            tbLimiteCliente.close();
        }
        tbLimiteCliente.setFilterCOD_CLIENTE(codCliente);
        tbLimiteCliente.open();
    }

    public String infoCadastroIrregular(double codCliente) throws DataException {
       return pkgCrmPartsRNA.getCadastroIrregular(codCliente, null);
    }

    public boolean podeExibirMotivoBloqueio(double codEmpresaUsuarioLogado) throws DataException {
        return (pkgCrmPartsRNA.getParametro(codEmpresaUsuarioLogado,
                "PARAMETROS_SISFIN",
                "OBRIGAR_MOTIVO_BLOQUEIO").equals("S"));
    }

    public boolean usaPluginConsultaNBS(Double codEmpresa) {
        try {
            return pkgCrmPartsRNA.getParametro(codEmpresa, "PARM_SYS3", "USA_PLUGIN_CONSULTA_NBS").equalsIgnoreCase("S");
        } catch (DataException e) {
            return false;
        }
    }

    public String infoSituacaoCadastralIntegracao(double codEmpresa, double codClienteApi, String tipoPessoa, String ie, Value aRfbSituacao, Value aRfbCadastroIrregular, Value aSintegraSituacao, Value aSintegraCadastroIsento, Value aSintegraCadastroIrregular, Value aSintegraMultiplasIe) throws DataException {
        return pkgCrmPartsRNA.getSituacaoCadastral(codEmpresa,
                codClienteApi,
                tipoPessoa,
                ie,
                aRfbSituacao,
                aRfbCadastroIrregular,
                aSintegraSituacao,
                aSintegraCadastroIsento,
                aSintegraCadastroIrregular,
                aSintegraMultiplasIe);
    }
}
