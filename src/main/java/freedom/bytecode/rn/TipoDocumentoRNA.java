package freedom.bytecode.rn;

import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;

import java.rmi.Remote;
import java.rmi.RemoteException;

import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;

import java.rmi.registry.*;

import freedom.commons.lang.IWorkList;
import freedom.util.EmpresaUtil;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.TipoDocumentoRNW;

public class TipoDocumentoRNA extends TipoDocumentoRNW {
    private static final long serialVersionUID = 20130827081850L;

    public String getModulo() {
        if (EmpresaUtil.isCrmParts()) {
            return "P";
        } else if (EmpresaUtil.isCrmService()) {
            return "S";
        } else {
            return "G";
        }
    }

    public void consultarTipoDoc(String ativo) throws DataException {
        tbTipoDocumento.close();
        tbTipoDocumento.clearFilters();
        tbTipoDocumento.clearParams();
        tbTipoDocumento.addFilter("MODULO");
        tbTipoDocumento.addParam("MODULO", getModulo());
        if (!ativo.equals("T")) {
            tbTipoDocumento.addFilter("ATIVO");
            tbTipoDocumento.addParam("ATIVO", ativo);
        }
        tbTipoDocumento.setOrderBy("DESCRICAO");
        tbTipoDocumento.open();
    }

    public Integer getIdTipoDocumento() throws DataException {
        tbTipoDocumentoID.close();
        tbTipoDocumentoID.clearFilters();
        tbTipoDocumentoID.clearParams();
        tbTipoDocumentoID.open();

        return tbTipoDocumentoID.sqlCompute("MAX", "ID_TIPO_DOCUMENTO").asInteger() + 1;
    }
}
