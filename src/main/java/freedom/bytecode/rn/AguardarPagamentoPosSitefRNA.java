package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AguardarPagamentoPosSitefRNW;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
public class AguardarPagamentoPosSitefRNA extends AguardarPagamentoPosSitefRNW  {
    private static final long serialVersionUID = 20130827081850L;

    private final String usuarioLogado = EmpresaUtil.getUserLogged();
    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    private boolean perguntaNFPosSitef = false;
    private String tempoPosSitefAprovado(Double codEmpresa) {
        try {
            return this.pkgCrmPartsRNA.getParametro(codEmpresa,
                    "PARM_SYS2",
                    "TEMPO_POS_SITEF_APROVADO").trim();
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
                try {
                    return this.pkgCrmPartsRNA.getParametro(codEmpresa,
                            "PARM_SYS2",
                            "TEMPO_POS_SITEF_APROVADO").trim();
                } catch (Exception ignore) {
                }
            }

        }
        return " ";
    }

    public long aguardeTempoPosSitefAprovado(Double codEmpresa) {
        long tempo;
        try {
            tempo = Long.parseLong(tempoPosSitefAprovado(codEmpresa).trim());
        } catch (NumberFormatException e) {
            tempo = 0L;
        }
        if (tempo < 1) {
            tempo = 1L;
        }
        return tempo;
    }

    public String gravarPagamentoPOS(Double codEmpresa, Double codOrcMapa, Double codCliente, Value idPagamento) {
        String respFunc;
        Double status = 0.0;
        Double codOperacao = 1.0;
        Double totalNota = 0.0;
        String tipoNf = "I";

        try {
            respFunc = pkgCrmPartsRNA.gravarPagamentoPos(
                    codEmpresa,
                    null,
                    null,
                    codCliente,
                    status,
                    null,
                    codOperacao,
                    null,
                    codOrcMapa,
                    null,
                    totalNota,
                    tipoNf,
                    idPagamento);
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
                try {
                    respFunc = pkgCrmPartsRNA.gravarPagamentoPos(
                            codEmpresa,
                            null,
                            null,
                            codCliente,
                            status,
                            null,
                            codOperacao,
                            null,
                            codOrcMapa,
                            null,
                            totalNota,
                            tipoNf,
                            idPagamento);
                } catch (Exception ee) {
                    respFunc = "N";
                }
            } else {
                respFunc = "N";
            }
        }
        return respFunc;
    }

    public String apagarPagamentoPOS(Double aIdPagamento){
        String respFunc;
        try {
            respFunc =  pkgCrmPartsRNA.apagarPagamentoPos(aIdPagamento);
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
                try {
                    respFunc = pkgCrmPartsRNA.apagarPagamentoPos(aIdPagamento);
                } catch (Exception ee) {
                    respFunc = "N";
                }
            }  else {
                respFunc = "N";
            }
        }
        return respFunc;
    }


    public String consultarPagamentoPos(Double idPagamento) {
        Value aPerguntaNfPosSitef = new Value(null);
        String respFunc;
        try {
            respFunc = pkgCrmPartsRNA.getStatusPagamentoPosSitef(idPagamento, this.usuarioLogado, aPerguntaNfPosSitef);
            this.perguntaNFPosSitef = aPerguntaNfPosSitef.asString().equals("S");
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
                try {
                    respFunc = pkgCrmPartsRNA.getStatusPagamentoPosSitef(idPagamento, this.usuarioLogado, aPerguntaNfPosSitef);
                    this.perguntaNFPosSitef = aPerguntaNfPosSitef.asString().equals("S");
                } catch (Exception ee) {
                    respFunc = "N";
                }
            }  else {
                respFunc = "N";
            }
        }
        return respFunc;
    }

    public boolean perguntarSePodeEmitirNFSemSitef() {
        return  this.perguntaNFPosSitef;
    }
}
