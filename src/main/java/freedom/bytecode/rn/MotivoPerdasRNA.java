package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.MotivoPerdasRNW;
import freedom.data.DataException;
import freedom.data.SequenceUtil;

public class MotivoPerdasRNA extends MotivoPerdasRNW  {

    private static final long serialVersionUID = 20130827081850L;

    @Override
    public void setPKFK() throws DataException {
        if (this.tbDescartes.getCOD_DESCARTE().isNull()) {
            this.tbDescartes.edit();
            this.tbDescartes.setCOD_DESCARTE(SequenceUtil.nextVal("SEQ_CRM_COD_DESCARTE"));
            this.tbDescartes.post();
        }
    }

    public void executaFiltroPrincipal(String filtroDepartamento,
                                       String filtroAtivo,
                                       String filtroPodeVisualizarNaVendaPerdida) throws DataException {
        this.tbDescartes.clearFilters();
        if ((!filtroDepartamento.equals(""))
                && (!filtroDepartamento.equals("T"))) {
            this.tbDescartes.setFilterDEPARTAMENTO(filtroDepartamento);
        }
        if ((!filtroAtivo.equals(""))
                && (!filtroAtivo.equals("T"))) {
            this.tbDescartes.setFilterATIVO(filtroAtivo);
        }
        if ((!filtroPodeVisualizarNaVendaPerdida.equals(""))
                && (!filtroPodeVisualizarNaVendaPerdida.equals("T"))) {
            this.tbDescartes.setFilterEXCLUSIVO_DESCARTE_AUTO(filtroPodeVisualizarNaVendaPerdida);
        }
        this.tbDescartes.setFilterDEPTO_CRMPARTS(" ");
    }

}
