package freedom.bytecode.rn;

import freedom.data.DataException;

import freedom.bytecode.rn.wizard.PesquisarEmpresasUsuariosRNW;
import freedom.util.EmpresaUtil;

public class PesquisarEmpresasUsuariosRNA extends PesquisarEmpresasUsuariosRNW {

    private static final long serialVersionUID = 20130827081850L;

    private String usuarioLogado = EmpresaUtil.getUserLogged();
    private int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    public void abrirTabelas() throws DataException {
        tbFiltroEmpresas.close();
        tbFiltroEmpresas.clearFilters();
        tbFiltroEmpresas.clearParams();
        tbFiltroEmpresas.addParam("COD_EMPRESA_USUARIO", codEmpresaUsuarioLogado);
        tbFiltroEmpresas.addParam("USUARIO", usuarioLogado);
        tbFiltroEmpresas.open();
    }

    public void pesquisar(String nomeCompleto, int codEmpresa) throws DataException {
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.setMaxRowCount(0);
        if (nomeCompleto.trim().length() > 0) {
            tbEmpresasUsuarios.addFilter("NOME_COMPLETO");
            tbEmpresasUsuarios.addParam("NOME_COMPLETO", "%" + nomeCompleto + "%");
        }

        if (codEmpresa > 0) {
            tbEmpresasUsuarios.addFilter("COD_EMPRESA");
            tbEmpresasUsuarios.addParam("COD_EMPRESA", codEmpresa);
        }
        tbEmpresasUsuarios.setOrderBy("A3.NOME_COMPLETO");
        tbEmpresasUsuarios.open();
    }

}
