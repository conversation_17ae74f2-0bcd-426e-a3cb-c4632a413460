package freedom.bytecode.rn;

import freedom.data.DataException;

import freedom.bytecode.rn.wizard.CadastroRapidoClienteEnderecoRNW;
import freedom.data.Value;
import freedom.util.pkg.PkgCrmPartsRNA;

public class CadastroRapidoClienteEnderecoRNA extends CadastroRapidoClienteEnderecoRNW  {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void openUfCidades(String uf,
                              double codCidade) throws DataException {
        this.tbUf.close();
        this.tbUf.open();
        this.openCidades(uf);
        this.tbCidades.locate("COD_CIDADES",
                codCidade);
    }

    public void openCidades(String uf) throws DataException {
        this.tbCidades.close();
        this.tbCidades.setMaxRowCount(0);
        this.tbCidades.setFilterUF(uf);
        this.tbCidades.open();
    }

    public String infoCadastroIrregular(double codCliente,
                                        String inscricao) throws DataException {
        return this.pkgCrmPartsRNA.getCadastroIrregular(codCliente,
                inscricao);
    }

    public String infoSituacaoCadastralIntegracao(double codEmpresa,
                                                  double codClienteApi,
                                                  String tipoPessoa,
                                                  String ie,
                                                  Value rfbSituacao,
                                                  Value rfbCadastroIrregular,
                                                  Value sintegraSituacao,
                                                  Value sintegraCadastroIsento,
                                                  Value sintegraCadastroIrregular,
                                                  Value sintegraMultiplasIe) throws DataException {
        return this.pkgCrmPartsRNA.getSituacaoCadastral(codEmpresa,
                codClienteApi,
                tipoPessoa,
                ie,
                rfbSituacao,
                rfbCadastroIrregular,
                sintegraSituacao,
                sintegraCadastroIsento,
                sintegraCadastroIrregular,
                sintegraMultiplasIe);
    }
}
