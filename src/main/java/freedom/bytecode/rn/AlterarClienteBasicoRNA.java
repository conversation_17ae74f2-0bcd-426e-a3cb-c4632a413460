package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AlterarClienteBasicoRNW;
import freedom.data.DataException;

public class AlterarClienteBasicoRNA extends AlterarClienteBasicoRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void filtrarClientes(long codCliente) throws DataException {
        tbClientes.close();
        tbClientes.clearFilters();
        tbClientes.clearParams();
        tbClientes.addFilter("COD_CLIENTE");
        tbClientes.addParam("COD_CLIENTE", codCliente);
        tbClientes.open();
    }

}
