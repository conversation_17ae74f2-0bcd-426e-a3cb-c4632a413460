package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AdicionarDescLetraClienteRNW;
import freedom.data.DataException;

public class AdicionarDescLetraClienteRNA extends AdicionarDescLetraClienteRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarLetrasDeDeconto() throws DataException {
        this.tbItensPerDescFlag.close();
        this.tbItensPerDescFlag.clearFilters();
        this.tbItensPerDescFlag.clearParams();
        this.tbItensPerDescFlag.open();
    }

}
