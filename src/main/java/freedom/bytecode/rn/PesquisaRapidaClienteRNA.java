package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.EmpresaUtil;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.PesquisaRapidaClienteRNW;
import freedom.util.pkg.PkgCrmPartsRNA;

public class PesquisaRapidaClienteRNA extends PesquisaRapidaClienteRNW  {
    private static final long serialVersionUID = 20130827081850L;

    private static final String COD_EMPRESA = "COD_EMPRESA";

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    public void filtrarCliente(String filtrarPor) throws DataException {
        this.tbLeadsConsultaClientes.close();
        this.tbLeadsConsultaClientes.setCriteria(null);
        this.tbLeadsConsultaClientes.clearFilters();
        this.tbLeadsConsultaClientes.clearParams();
        this.tbLeadsConsultaClientes.setFilterFILTO_NOME_COD_FONE(filtrarPor);
        this.tbLeadsConsultaClientes.addParam(COD_EMPRESA, codEmpresaUsuarioLogado);
        this.tbLeadsConsultaClientes.open();
        this.tbLeadsConsultaClientes.setCriteria("COD_CLIENTE > 1");
        this.tbLeadsConsultaClientes.filter();
    }
}
