package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AgrupaPagamentosRNW;
import freedom.client.controls.impl.TFTable;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsA;
import freedom.util.pkg.PkgCrmPartsRNA;

import java.util.Date;

public class AgrupaPagamentosRNA extends AgrupaPagamentosRNW  {
    private static final long serialVersionUID = 20130827081850L;
    private static final String COD_EMPRESA = "COD_EMPRESA";
    private final String usuarioLogado = EmpresaUtil.getUserLogged();
    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();
    private final PkgCrmPartsA pkgCrmPartsA = new PkgCrmPartsA();

    public void abrirTabelas() throws DataException{
        this.tbEmpresasUsuarios.close();
        this.tbEmpresasUsuarios.setFilterNOME(this.usuarioLogado);
        this.tbEmpresasUsuarios.open();
        this.tbLeadsEmpresasUsuarios.close();
        this.tbLeadsEmpresasUsuarios.addParam("USUARIO", this.usuarioLogado);
        this.tbLeadsEmpresasUsuarios.addParam("COD_EMPRESA_USUARIO", this.tbEmpresasUsuarios.getCOD_EMPRESA());
        this.tbLeadsEmpresasUsuarios.open();
        this.tbLeadsEmpresasUsuarios.locate(COD_EMPRESA, this.tbEmpresasUsuarios.getCOD_EMPRESA());

    }

    public void openLeadsPgto(int codEmpresa) throws DataException {
        this.tbLeadsPgtoAgrupado.close();
        this.tbLeadsPgtoAgrupado.clearParams();
        this.tbLeadsPgtoAgrupado.addParam("ID_PAGAMENTO", -999L);
        this.tbLeadsPgtoAgrupado.open();

        this.tbLeadsPgtoAgrupadoParc.close();
        this.tbLeadsPgtoAgrupadoParc.clearParams();
        this.tbLeadsPgtoAgrupadoParc.addParam("ID_PAGAMENTO", -999L);
        this.tbLeadsPgtoAgrupadoParc.open();

        this.tbLeadsPgtoAgrupadoParam.close();
        this.tbLeadsPgtoAgrupadoParam.clearParams();
        this.tbLeadsPgtoAgrupadoParam.addFilter("COD_EMPRESA");
        this.tbLeadsPgtoAgrupadoParam.addParam("COD_EMPRESA", codEmpresa);
        this.tbLeadsPgtoAgrupadoParam.addFilter("TIPO");
        this.tbLeadsPgtoAgrupadoParam.addParam("TIPO", "PC");
        this.tbLeadsPgtoAgrupadoParam.open();

    }

    public boolean openLeadsOrcNotasPgtoAgrupados(double codEmpresa,
                                               double codCliente,
                                               Date dataInicial,
                                               Date dataFinal
                                               ) throws DataException{

        this.tbLeadsOrcNotasPgtoAgrupados.close();
        this.tbLeadsOrcNotasPgtoAgrupados.clearFilters();
        this.tbLeadsOrcNotasPgtoAgrupados.clearParams();

        this.tbLeadsOrcNotasPgtoAgrupados.addFilter("COD_EMPRESA");
        this.tbLeadsOrcNotasPgtoAgrupados.addParam("COD_EMPRESA", codEmpresa);
        this.tbLeadsOrcNotasPgtoAgrupados.addFilter("COD_CLIENTE");
        this.tbLeadsOrcNotasPgtoAgrupados.addParam("COD_CLIENTE", codCliente);
        this.tbLeadsOrcNotasPgtoAgrupados.addFilter("DATA_STARTSWITH");
        this.tbLeadsOrcNotasPgtoAgrupados.addParam("DATA_STARTSWITH", dataInicial);
        this.tbLeadsOrcNotasPgtoAgrupados.addFilter("DATA_ENDSWITH");
        this.tbLeadsOrcNotasPgtoAgrupados.addParam("DATA_ENDSWITH", dataFinal);
        this.tbLeadsOrcNotasPgtoAgrupados.open();

        return !this.tbLeadsOrcNotasPgtoAgrupados.isEmpty();
    }

    public void openLeadsPgtoAgrupado() throws DataException {
        this.tbLeadsPgtoAgrupado.close();
        this.tbLeadsPgtoAgrupado.clearParams();
        this.tbLeadsPgtoAgrupado.addParam("ID_PAGAMENTO", -999L);
        this.tbLeadsPgtoAgrupado.open();
    }
    public void openLeadsPgtoAgrupParc() throws DataException{
        this.tbLeadsPgtoAgrupadoParc.close();
        this.tbLeadsPgtoAgrupadoParc.clearParams();
        this.tbLeadsPgtoAgrupadoParc.addParam("ID_PAGAMENTO", -999L);
        this.tbLeadsPgtoAgrupadoParc.open();
    }

    public void addNotaParaAgrupamento(int codEmpresa,
                                      int nrOs,
                                      int codOrcMapa,
                                      double codCliente,
                                      String tipo,
                                      double valor) throws DataException {

        this.tbLeadsPgtoAgrupado.append();
        this.tbLeadsPgtoAgrupado.setCOD_EMPRESA(codEmpresa);
        this.tbLeadsPgtoAgrupado.setCOD_ORC_MAPA(codOrcMapa);
        this.tbLeadsPgtoAgrupado.setCOD_CLIENTE(codCliente);
        this.tbLeadsPgtoAgrupado.setTIPO(tipo);
        this.tbLeadsPgtoAgrupado.setVALOR(valor);
        this.tbLeadsPgtoAgrupado.post();
    }

    public double carregaValorTotal() throws DataException{
        double valor = 0.0;

        this.tbLeadsPgtoAgrupado.first();
        while(!this.tbLeadsPgtoAgrupado.eof()){
            valor += this.tbLeadsPgtoAgrupado.getVALOR().asDecimal();
            this.tbLeadsPgtoAgrupado.next();
        }

        return valor;
    }

    public void removeItemAgrupado() throws DataException {
        this.tbLeadsPgtoAgrupado.delete();
        this.tbLeadsPgtoAgrupado.post();
    }

    public void removeItemFormaPgto() throws DataException {
        this.tbLeadsPgtoAgrupadoParc.delete();
        this.tbLeadsPgtoAgrupadoParc.post();
    }

    public boolean temNota(int codOrcMapa, TFTable table) throws DataException {
        table.first();
        while(!table.eof()){
            int codMapa = table.getField("COD_ORC_MAPA").asInteger();
            if(codOrcMapa == codMapa){
               return true;
            }
            table.next();
        }

        return false;
    }

    public boolean pesquisaOrcamentos(int codEmpresa,
                                   int nrAgrupamento,
                                   Date dataInicial,
                                   Date dataFinal,
                                   double codCliente) throws DataException{

        this.tbOrcPreNotasParaAgrupar.close();
        this.tbOrcPreNotasParaAgrupar.clearFilters();
        this.tbOrcPreNotasParaAgrupar.clearParams();

        this.tbOrcPreNotasParaAgrupar.setFilterCOD_EMPRESA(codEmpresa);
        this.tbOrcPreNotasParaAgrupar.setFilterCOD_CLIENTE(codCliente);

        this.tbOrcPreNotasParaAgrupar.setFilterDATA_STARTSWITH(dataInicial);
        this.tbOrcPreNotasParaAgrupar.setFilterDATA_ENDSWITH(dataFinal);
        this.tbOrcPreNotasParaAgrupar.open();

        openLeadsPgto(codEmpresa);
        openLeadsPgtoAgrupado();
        openLeadsPgtoAgrupParc();

        if(this.tbOrcPreNotasParaAgrupar.isEmpty()){
            this.tbOrcPreNotasParaAgrupar.append();
            this.tbOrcPreNotasParaAgrupar.setSALDO_PARA_AGRUPAR(0);
            this.tbOrcPreNotasParaAgrupar.post();

            this.tbOrcPreNotasParaAgrupar.close();
            this.tbOrcPreNotasParaAgrupar.clearFilters();
            this.tbOrcPreNotasParaAgrupar.clearParams();

            this.tbOrcPreNotasParaAgrupar.setFilterCOD_EMPRESA(codEmpresa);
            this.tbOrcPreNotasParaAgrupar.setFilterCOD_CLIENTE(codCliente);

            this.tbOrcPreNotasParaAgrupar.setFilterDATA_STARTSWITH(dataInicial);
            this.tbOrcPreNotasParaAgrupar.setFilterDATA_ENDSWITH(dataFinal);
            this.tbOrcPreNotasParaAgrupar.open();

            return !tbOrcPreNotasParaAgrupar.isEmpty();
        }


        return !tbOrcPreNotasParaAgrupar.isEmpty();
    }

    public void aplicarPgtoParc(String tipoParcPag, int parcelas, double valor, int seq) throws DataException{
        this.tbLeadsPgtoAgrupadoParc.append();
        this.tbLeadsPgtoAgrupadoParc.setSEQUENCIA_CARTAO(seq);
        this.tbLeadsPgtoAgrupadoParc.setTIPO_PARCELA_CARTAO(tipoParcPag);
        this.tbLeadsPgtoAgrupadoParc.setCOD_FORMA_PGTO(0);
        this.tbLeadsPgtoAgrupadoParc.setQTDE_PARCELA_CARTAO(parcelas);
        this.tbLeadsPgtoAgrupadoParc.setVALOR(valor);
        this.tbLeadsPgtoAgrupadoParc.post();
    }

    public String confirmarPgtos(double codEmpresa)  {
        String retFuncao = "";
        try {
            Double idPagamento = addPgtoAgrupado(codEmpresa);
            if (idPagamento > 0) {
                retFuncao = pkgCrmPartsRNA.pagamentoAgrupadoGravar(idPagamento,codEmpresa, "I");
            }
        } catch (DataException e) {
            throw new RuntimeException(e);
        }

        return retFuncao;
    }

    public String excluirPgtoAgrupado(double idPagamento, double codEmpresa){
        String retFuncao = "";
        try {
            if (idPagamento > 0) {
                retFuncao = pkgCrmPartsRNA.pagamentoAgrupadoGravar(idPagamento,codEmpresa, "E");
            }else{
                retFuncao = "Agrupamento não informado!";
            }
        } catch (DataException e) {
            throw new RuntimeException(e);
        }

        return retFuncao;
    }

    public String fechamentoParcial(double idPagamento, String origemAgrupamento, double codEmpresa) throws DataException{
            String retFuncao = "";
            try {
                if (idPagamento > 0) {
                    retFuncao = pkgCrmPartsRNA.pagamentoAgrupadoParcial(idPagamento,origemAgrupamento, codEmpresa);
                }else{
                    retFuncao = "Agrupamento não encontrado!";
                }
            } catch (DataException e) {
                throw new RuntimeException(e);
            }

            return retFuncao;
    }

    public double carregaValorParcTotal() throws DataException{
        double valorParcTotal = 0.0;

        if(!this.tbLeadsPgtoAgrupadoParc.isEmpty()){
            this.tbLeadsPgtoAgrupadoParc.first();
            while(!this.tbLeadsPgtoAgrupadoParc.eof()){
                valorParcTotal += this.tbLeadsPgtoAgrupadoParc.getVALOR().asDecimal();
                this.tbLeadsPgtoAgrupadoParc.next();
            }
        }

        return valorParcTotal;
    }

    public boolean existePixInformado() throws DataException{
        boolean retFuncao = false;

        if(!this.tbLeadsPgtoAgrupadoParc.isEmpty()){
            this.tbLeadsPgtoAgrupadoParc.first();
            while(!this.tbLeadsPgtoAgrupadoParc.eof()){
                int tipo_parc_cartao = this.tbLeadsPgtoAgrupadoParc.getTIPO_PARCELA_CARTAO().asInteger();
                if(tipo_parc_cartao == 4){
                    retFuncao = true;
                    return retFuncao;
                }
                this.tbLeadsPgtoAgrupadoParc.next();
            }
        }
        return retFuncao;
    }
    private Double addPgtoAgrupado(double codEmpresa) throws DataException {
        Value idPagamento = new Value(null);

        String tipo           = this.tbLeadsPgtoAgrupadoParam.getTIPO().asString();
        String tipoAdt        = this.tbLeadsPgtoAgrupadoParam.getTIPO_ADIANTAMENTO().asString();
        double codCliente     = this.tbLeadsPgtoAgrupado.getCOD_CLIENTE().asDecimal();
        double natRecDespAdt  = this.tbLeadsPgtoAgrupadoParam.getNAT_REC_DESP_ADT().asDecimal();
        double natRecDespCart = this.tbLeadsPgtoAgrupadoParam.getNAT_REC_DESP_CART().asDecimal();
        double valorParcTotal = carregaValorParcTotal();

        ISession session;
        session = SessionFactory.getInstance().getSession();

        try {
            session.open();
            this.testarRetorno(
                    this.pkgCrmPartsA
                            .pagamentoAgrupadoAdd(
                                    session,
                                    codEmpresa,
                                    codCliente,
                                    this.usuarioLogado,
                                    tipo,
                                    valorParcTotal,
                                    tipoAdt,
                                    natRecDespAdt,
                                    natRecDespCart,
                                    idPagamento)
            );

            double seqCartao = 0.0;

            // adicionar as parcelas
            this.tbLeadsPgtoAgrupadoParc.first();
            while (!this.tbLeadsPgtoAgrupadoParc.eof()){

                seqCartao           += 1;
                double valor        = this.tbLeadsPgtoAgrupadoParc.getVALOR().asDecimal();
                double qtdeParcelas = this.tbLeadsPgtoAgrupadoParc.getQTDE_PARCELA_CARTAO().asDecimal();
                String observacao   = this.tbLeadsPgtoAgrupadoParc.getOBSERVACAO().asString();
                String tipoParcela  = this.tbLeadsPgtoAgrupadoParc.getTIPO_PARCELA_CARTAO().asString();
                double codFormaPgto = this.tbLeadsPgtoAgrupadoParc.getCOD_FORMA_PGTO().asInteger();

                this.testarRetorno(
                        this.pkgCrmPartsA
                                .pagamentoAgrupadoParcAdd(
                                        session,
                                        idPagamento.asDecimal(),
                                        codEmpresa,
                                        codFormaPgto,
                                        tipoParcela,
                                        qtdeParcelas,
                                        valor,
                                        observacao,
                                        seqCartao)
                );

                this.tbLeadsPgtoAgrupadoParc.next();
            }

            /*Aqui vai adicionar agrupamento */
            this.tbLeadsPgtoAgrupado .first();
            while (!this.tbLeadsPgtoAgrupado.eof()){

                String documento        = this.tbLeadsPgtoAgrupado.getCOD_ORC_MAPA().asString();
                double valorAdtAgrupado = this.tbLeadsPgtoAgrupado.getVALOR().asDecimal();

                this.testarRetorno(this.pkgCrmPartsA
                        .pagamentoAgrupadoMultAdd(
                                session,
                                idPagamento.asDecimal(),
                                codEmpresa,
                                tipo,
                                documento,
                                valorAdtAgrupado)
                );

                this.tbLeadsPgtoAgrupado.next();
            }

            session.commit();

        } catch (DataException dataException){
            session.rollback();
            throw dataException;
        } finally {
            session.close();
        }

        return idPagamento.asDecimal();
    }


    private void testarRetorno(String aRetFuncao) throws DataException {
        if (!aRetFuncao.equals("S")) {
            throw new DataException(aRetFuncao);
        }
    }

    public boolean openLeadsFormaPgtoAgrupado() throws DataException {
        double idPagamento = this.tbLeadsOrcNotasPgtoAgrupados.getID_PAGAMENTO().asDecimal();

        this.tbLeadsFormaPgtoAgrupado.close();
        this.tbLeadsFormaPgtoAgrupado.setFilterID_PAGAMENTO(idPagamento);
        this.tbLeadsFormaPgtoAgrupado.open();

        return !this.tbLeadsFormaPgtoAgrupado.isEmpty();
    }

}