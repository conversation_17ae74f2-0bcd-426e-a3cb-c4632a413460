package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.PgtoVendaInfRNW;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
import freedom.util.pkg.PkgCrmServiceFaturarRNA;
import freedom.util.pkg.PkgUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class PgtoVendaInfRNA extends PgtoVendaInfRNW {
    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();
    private final PkgCrmServiceFaturarRNA pkgCrmServiceFaturarRNA = new PkgCrmServiceFaturarRNA();
    private Double codEmpresa;
    private Double codOrcMapa;
    private double valorCartaoIncluir = 0.0;
    private double valorChequeIncluir = 0.0;
    private String usuarioLogado = EmpresaUtil.getUserLogged();

    private final Value valorCheque = new Value(null);
    private final Value valorCartao = new Value(null);
    private final Value valorBoleto = new Value(null);
    private final Value valorCartaoInformado = new Value(null);
    private final Value valorChequeInformado = new Value(null);
    private final Value boletoValido = new Value(null);
    private final Value valorPix = new Value(null);
    private final Value integrarPosSitef = new Value(null);

    private final Value digitarCartao = new Value(null);

    private final Value digitarBoleto = new Value(null);

    private Integer nrParcelas = 0;
    private Boolean erroValidarInfPgto = false;
    private boolean ehOrcamento = false;
    private double codFormaPgto = 0.0;
    private Double codCondicaoPagamento = 0.0;
    private Double numeroOs = 0.0;
    private boolean digitandoCartaoPosSitef = false;

    public String validarInfoPgtoVendaOS(Double codEmpresa, Double numeroOs) {
        this.numeroOs = numeroOs;
        this.codEmpresa = codEmpresa;
        this.codOrcMapa = 0.0;
        try {
            return pkgCrmServiceFaturarRNA.validarInfoPgtoVenda(this.codEmpresa, this.numeroOs, this.usuarioLogado, this.valorCheque, this.valorChequeInformado, this.valorCartao, this.valorCartaoInformado, this.valorBoleto, this.boletoValido, this.integrarPosSitef, this.valorPix, this.digitarCartao, this.digitarBoleto);
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
                try {
                    return pkgCrmServiceFaturarRNA.validarInfoPgtoVenda(this.codEmpresa, this.numeroOs, this.usuarioLogado, this.valorCheque, this.valorChequeInformado, this.valorCartao, this.valorCartaoInformado, this.valorBoleto, this.boletoValido, this.integrarPosSitef, this.valorPix, this.digitarCartao, this.digitarBoleto);
                } catch (DataException ex) {
                    return ex.getMessage();
                }
            };
            return e.getMessage();
        }
    }
    public String validarinfoPgtoVendaOrcMapa(Double codEmpresa, Double codOrcMapa) {
        this.numeroOs = 0.0;
        this.codEmpresa = codEmpresa;
        this.codOrcMapa = codOrcMapa;

        return validarInfoPgtoVenda();
    }

    public String validarInfoPgtoVenda(){
        this.erroValidarInfPgto = false;
        try {
            return pkgCrmPartsRNA.validarInfoPgtoVenda(this.codEmpresa, this.codOrcMapa,  this.numeroOs,  this.usuarioLogado, this.valorCheque, this.valorChequeInformado, this.valorCartao, this.valorCartaoInformado, this.valorBoleto, this.boletoValido, this.integrarPosSitef, this.valorPix, this.digitarCartao, this.digitarBoleto);
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
            try {
                    return pkgCrmPartsRNA.validarInfoPgtoVenda(this.codEmpresa, this.codOrcMapa,  this.numeroOs,  this.usuarioLogado, this.valorCheque, this.valorChequeInformado, this.valorCartao, this.valorCartaoInformado, this.valorBoleto, this.boletoValido, this.integrarPosSitef, this.valorPix, this.digitarCartao, this.digitarBoleto);
            } catch (DataException ex) {
                    this.erroValidarInfPgto = false;
                    return ex.getMessage();
            }
        }
            return e.getMessage();
        }
    }

    public void setEhOrcamento(boolean flag) {
        this.ehOrcamento = flag;
    }

    public boolean isEhOrcamento() {
        return ehOrcamento;
    }

    public String validarInfPgtoVenda() {
        if (this.erroValidarInfPgto) {
            return "Não foi possível iniciar validação de pagamento Venda.";
        }
        if ((valorBoleto.asDecimal() + valorCartao.asDecimal() + valorCheque.asDecimal()) > 0.0) {
            return "S";
        } else {
            return "N";
        }
    }

    public Double getValorCartao() {
        return valorCartao.asDecimal() + valorCartaoIncluir;
    }

    public Double getValorCheque() {
        return valorCheque.asDecimal() + valorChequeIncluir;
    }

    public Double getValorBoleto() {
        return valorBoleto.asDecimal();
    }

    public void abreCartoesInformados() throws DataException {
        tbLeadsPgtoVendaInfCartao.close();
        tbLeadsPgtoVendaInfCartao.setFilterCOD_EMPRESA(this.codEmpresa);
        tbLeadsPgtoVendaInfCartao.setFilterCOD_ORC_MAPA(this.codOrcMapa);
        tbLeadsPgtoVendaInfCartao.open();
    }

    public void abreBoletosInformados() throws DataException {
        tbLeadsPgtoVendaInfBoleto.close();
        tbLeadsPgtoVendaInfBoleto.setFilterCOD_EMPRESA(this.codEmpresa);
        tbLeadsPgtoVendaInfBoleto.setFilterCOD_ORC_MAPA(this.codOrcMapa);
        tbLeadsPgtoVendaInfBoleto.open();
    }

    public void abreChequesInformados() throws DataException {
        tbLeadsFormaPgtoCheque.close();
        tbLeadsFormaPgtoCheque.setFilterCOD_EMPRESA(this.codEmpresa);
        tbLeadsFormaPgtoCheque.setFilterCOD_ORC_MAPA(this.codOrcMapa);
        tbLeadsFormaPgtoCheque.open();

        tbLeadsPgtoVendaInfCheque.close();
        tbLeadsPgtoVendaInfCheque.setFilterCOD_EMPRESA(this.codEmpresa);
        tbLeadsPgtoVendaInfCheque.setFilterCOD_ORC_MAPA(this.codOrcMapa);
        tbLeadsPgtoVendaInfCheque.open();
    }

    public Double getValorFaltaCartao() {
        return valorCartao.asDecimal() + valorCartaoIncluir - valorCartaoInformado.asDecimal();
    }


    public void filtrarListaCartoes(Integer nrParcelas) throws DataException {
        if (!this.nrParcelas.equals(nrParcelas)) {
            tbLeadsCartoes.close();
            tbLeadsCartoes.setFilterCOD_EMPRESA(this.codEmpresa);
            tbLeadsCartoes.setFilterNR_PARCELAS(nrParcelas);
            tbLeadsCartoes.open();
            this.nrParcelas = nrParcelas;
        }
    }

    public Boolean podeConfirmar() {
        if (getValorFaltaCartao() > 0.0) {
            return isIncluindoCartoes() && isEhOrcamento() && (getValorFaltaCartao() == valorCartaoIncluir);
        } else if (getValorFaltaCartao() < 0.0) {
            return false;
        } else if (getValorFaltaCheque() > 0.0) {
            return false;
        } else if (getValorFaltaCheque() < 0.0) {
            return false;
        } else {
            return !getFaltaNossoNumero();
        }
    }

    public boolean cartaoJaLancado(Integer codCartao, String nrAut) {
        try {
            return (tbLeadsPgtoVendaInfCartao.locate("COD_CARTAO_CREDITO, NR_AUTORIZACAO_VISA", codCartao, nrAut));
        } catch (DataException e) {
            return true;
        }
    }

    public String incluirCartao(Double codCartaoCredito,
                                Double valor,
                                Double nrParcelas,
                                String nrAutorizacao) throws DataException {
        return pkgCrmPartsRNA.cartaoInfPgtoIncluir(this.codEmpresa, this.codOrcMapa, this.codFormaPgto, codCartaoCredito, valor, nrParcelas, nrAutorizacao);
    }

    public void atualizaValoresInformadosCartao() throws DataException {
        validarInfoPgtoVenda();
        abreCartoesInformados();
    }

    public void atualizaValoresInformadosBoleto() throws DataException {
        validarInfoPgtoVenda();
        abreBoletosInformados();
    }


    public void excluirCartoes() throws DataException {
        pkgCrmPartsRNA.cartaoInfPgtoExcluir(this.codEmpresa, this.codOrcMapa);
    }

    public boolean getFaltaNossoNumero() {
        return !((valorBoleto.asDecimal() <= 0) || boletoValido.asString().equals("S"));
    }

    public String atualizaBoletoNossoNumero(Double codFormaPgto, Double parcela, String nossoNumero) throws DataException {
        return pkgCrmPartsRNA.boletoInfPgtoNossoNr(this.codEmpresa, this.codOrcMapa, codFormaPgto, parcela, nossoNumero);
    }

    public String excluirCheque(Double codPagamentoInf) throws DataException {
        return pkgCrmPartsRNA.chequeInfPgtoExcluir(this.codEmpresa, this.codOrcMapa, codPagamentoInf);
    }

    public String incluirCheques() throws DataException {
        return pkgCrmPartsRNA.chequeInfPgtoIncluir(this.codEmpresa, this.codOrcMapa, this.codFormaPgto, this.codCondicaoPagamento, getValorFaltaCheque());
    }

    public Double getValorFaltaCheque() {
        return valorCheque.asDecimal() + valorChequeIncluir - valorChequeInformado.asDecimal();
    }

    public void atualizaValoresInformadosCheque() throws DataException {
        validarInfoPgtoVenda();
        abreChequesInformados();
    }

    public String atualizaInfoCheque(String nrCheque, Double valor, Date dtVencimento) throws DataException {
        return pkgCrmPartsRNA.chequeInfPgtoAlterar(this.codEmpresa, this.codOrcMapa, this.tbLeadsPgtoVendaInfCheque.getCOD_PAGAMENTO_INF().asDecimal(), nrCheque, valor, dtVencimento);
    }

    public void setValorCartaoIncluir(double valorCartaoIncluir, double codFormaPgto) {
        this.valorCartaoIncluir = valorCartaoIncluir;
        this.codFormaPgto = codFormaPgto;
    }

    public void setValorChequeIncluir(double valorChequeIncluir, double codFormaPgto, double codCondPgto) {
        this.valorChequeIncluir = valorChequeIncluir;
        this.codFormaPgto = codFormaPgto;
        this.codCondicaoPagamento = codCondPgto;
    }

    public boolean isIncluindoCartoes() {
        return (this.valorCartaoIncluir > 0.0 || this.digitandoCartaoPosSitef ) ;
    }

    public boolean isIncluindoCheques() {
        return this.valorChequeIncluir > 0.0;
    }

    public String alterarNrAutorizacaoCartao(Double codEmpresa, Double codOrcMapa, Double codFormaPgto, Double codCartaoCredito, String nrAutorizacao) {
        try {
            return pkgCrmPartsRNA.cartaoInfPgtoNrAut(codEmpresa, codOrcMapa, codFormaPgto, codCartaoCredito, nrAutorizacao);
        } catch (DataException e) {
            return e.getMessage();
        }
    }

    public boolean faltaPreencherNrAutorizacao() {
        boolean faltaNrAut = false;
        try {
            this.tbLeadsPgtoVendaInfCartao.first();
            while (Boolean.FALSE.equals(this.tbLeadsPgtoVendaInfCartao.eof())) {
                if (StringUtils.isBlank(this.tbLeadsPgtoVendaInfCartao.getNR_AUTORIZACAO_VISA().asString())) {
                    faltaNrAut = true;
                }
                this.tbLeadsPgtoVendaInfCartao.next();
            }
        } catch (DataException e) {
            faltaNrAut = true;
        }
        return faltaNrAut;

    }

    public Boolean temPagamentoPosSitef() {
        return integrarPosSitef.asString().equals("S");
    }

    public boolean perguntaDigitarCartao() {
        return (integrarPosSitef.asString().equals("S") && digitarCartao.asString().equals("S"));
    }

    public void setValorDigitarCartaoIncluir() {

        this.erroValidarInfPgto = false;
        try {
            this.codFormaPgto = pkgCrmPartsRNA.getFormaPgtoCartaoInfo(this.codEmpresa, this.codOrcMapa);
        } catch (DataException e) {
            if (PkgUtil.isOracleErrorORA04068(e)) {
                try {
                    this.codFormaPgto = pkgCrmPartsRNA.getFormaPgtoCartaoInfo(this.codEmpresa, this.codOrcMapa);
                } catch (DataException ex) {
                    this.erroValidarInfPgto = false;
                    this.codFormaPgto = 0;
                }
            }
        }
        //this.valorCartaoIncluir = getValorCartao();
        this.digitandoCartaoPosSitef = true;
    }

    public boolean isIncluindoCartaoSitef() {
        return this.digitandoCartaoPosSitef;
    }

    public boolean emitirNFComPosSitefAprovado() {
        try {
            String respFunc = pkgCrmPartsRNA.getParametro(this.codEmpresa, "PARM_SYS2", "NF_POS_SITEF_APROVADO");
            return "S".equals(respFunc);
        } catch (DataException e) {
            if (PkgUtil.isOracleErrorORA04068(e)) {
                try {
                    String respFunc = pkgCrmPartsRNA.getParametro(this.codEmpresa, "PARM_SYS2", "NF_POS_SITEF_APROVADO");
                    return "S".equals(respFunc);
                } catch (DataException ex) {
                    return true;
                }
            } else {
                return true;
            }
        }
    }
}