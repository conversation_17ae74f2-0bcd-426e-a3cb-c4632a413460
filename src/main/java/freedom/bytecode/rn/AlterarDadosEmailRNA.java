package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AlterarDadosEmailRNW;
import encrypt.criptografia.Cript;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class AlterarDadosEmailRNA extends AlterarDadosEmailRNW {

    private static final long serialVersionUID = 20130827081850L;
    private final EmpresaUtil util = new EmpresaUtil();
    private final Cript crp = new Cript();

    public void filtrarEmpresasUsuarios(String nome) throws DataException {
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.addFilter("NOME");
        tbEmpresasUsuarios.addParam("NOME", nome);
        tbEmpresasUsuarios.open();
    }

    public String validarDadosEmail(String senhaNova, String senhaConfirmar, String senhaAntiga) {

        if (tbEmpresasUsuarios.getEMAIL().asString().trim().equals("")) {
            return "e-mail não pode ser vázio.";
        }

        if (senhaNova.trim().equals("")) {
            return "Informe a senha.";
        }

        if (senhaConfirmar.trim().equals("")) {
            return "Informe a senha à confirmar.";
        }

        if (!senhaNova.equals(senhaConfirmar)) {
            return "Senha não confere com a confirmação";
        }

        String senhaOld = tbEmpresasUsuarios.getEMAIL_SENHA().asString().trim();
        if (!senhaOld.equals("")) {
            senhaOld = crp.decript(senhaOld);

            if (!senhaOld.equals(senhaAntiga)) {
                return "Senha antiga não confere.";
            }
        }

        return "";
    }

    public void salvarDadosEmail(String senha) throws DataException {
        tbEmpresasUsuarios.edit();
        tbEmpresasUsuarios.setEMAIL_SENHA(crp.cript(senha));
        tbEmpresasUsuarios.post();
        util.salvar(sc);
        tbEmpresasUsuarios.commitUpdates();
    }

}
