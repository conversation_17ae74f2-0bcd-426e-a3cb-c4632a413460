package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.PerfilRNW;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

public class PerfilRNA extends PerfilRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final EmpresaUtil empresaUtil = new EmpresaUtil();

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void filtrarUsuarioPerfil(String usuario) throws DataException {
        //Filtrar Primeiro tabela auxiliar
        this.tbSetorVenda.close();
        this.tbSetorVenda.open();
        this.tbProdutoSegmento.close();
        this.tbProdutoSegmento.open();
        this.tbPerfil.close();
        this.tbPerfil.clearFilters();
        this.tbPerfil.clearParams();
        this.tbPerfil.setFilterNOME_EQUALS(usuario);
        this.tbPerfil.open();
        this.carregarAssinatura(usuario);
    }

    public void salvarFoto(String usuario) throws DataException {
        this.tbUsuarioFoto.close();
        this.tbUsuarioFoto.clearFilters();
        this.tbUsuarioFoto.clearParams();
        this.tbUsuarioFoto.setFilterNOME(usuario.trim().toUpperCase());
        this.tbUsuarioFoto.open();
        boolean tbUsuarioFotoEmpty = this.tbUsuarioFoto.isEmpty();
        if (tbUsuarioFotoEmpty) {
            this.tbUsuarioFoto.append();
            this.tbUsuarioFoto.setNOME(usuario.trim().toUpperCase());
        } else {
            this.tbUsuarioFoto.edit();
        }
        this.tbUsuarioFoto.setFOTO(this.tbPerfil.getFOTO());
        this.tbUsuarioFoto.setFOTO_ICONE(this.tbPerfil.getFOTO_ICONE());
        this.tbUsuarioFoto.post();
        this.empresaUtil.salvar(this.sc);
    }

    public void removerFoto(String usuario) throws DataException {
        this.tbUsuarioFoto.close();
        this.tbUsuarioFoto.clearFilters();
        this.tbUsuarioFoto.clearParams();
        this.tbUsuarioFoto.setFilterNOME(usuario.trim().toUpperCase());
        this.tbUsuarioFoto.open();
        boolean tbUsuarioFotoEmpty = this.tbUsuarioFoto.isEmpty();
        if (!tbUsuarioFotoEmpty) {
            this.tbUsuarioFoto.delete();
        }
        this.empresaUtil.salvar(this.sc);
    }

    public void salvarAssinaturaPerfil(byte[] assinatura) throws DataException {
        tbEmpresasUsuarios.edit();
        tbEmpresasUsuarios.setASSINATURA(assinatura);
        tbEmpresasUsuarios.post();
        tbEmpresasUsuarios.applyUpdates();
    }

    public void carregarAssinatura(String usuario) throws DataException {
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.addFilter("NOME");
        tbEmpresasUsuarios.addParam("NOME", usuario.trim().toUpperCase());
        tbEmpresasUsuarios.open();
    }

    public void openLocalEstoque(Double codEmpresa) throws DataException {
        tbLocalEstoque.close();
        tbLocalEstoque.addFilter("COD_EMPRESA");
        tbLocalEstoque.addParam("COD_EMPRESA", codEmpresa);
        tbLocalEstoque.open();
    }

    /**
     * @param loginUsuario Login do usuário cujo código do acesso será verificado<hr>
     * @param codAcesso Código do acesso que será verificado para o login do usuário<br><hr>
     * @return Se tiver o acesso será retornado "S". Senão será retornada uma mensagem para exibir para o usuário.<br><hr>
     * @throws DataException Para erros de banco
     */
    public String validarAcesso(String loginUsuario,
                                String codAcesso) throws DataException {
        return this.pkgCrmPartsRNA.validarAcesso(loginUsuario,
                codAcesso);
    }

}