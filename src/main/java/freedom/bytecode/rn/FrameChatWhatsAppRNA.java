package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.FrameChatWhatsAppRNW;
import freedom.data.DataException;

import java.util.Date;

public class FrameChatWhatsAppRNA extends FrameChatWhatsAppRNW {

    private static final long serialVersionUID = 20130827081850L;

    public boolean filtrarCadastroWhatsApp(int codEmpresa) throws DataException {
        this.tbConsultaNumberWhats.close();
        this.tbConsultaNumberWhats.clearFilters();
        this.tbConsultaNumberWhats.clearParams();
        this.tbConsultaNumberWhats.setFilterCOD_EMPRESA(codEmpresa);
        this.tbConsultaNumberWhats.setFilterSTATUS("S");
        this.tbConsultaNumberWhats.open();
        if (this.tbConsultaNumberWhats.count() > 0) {
            this.tbCadastroWhatsapp.close();
            this.tbCadastroWhatsapp.clearFilters();
            this.tbCadastroWhatsapp.clearParams();
            this.tbCadastroWhatsapp.setFilterID_CELULAR(this.tbConsultaNumberWhats.getID_CELULAR().asLong());
            this.tbCadastroWhatsapp.open();
        }
        return this.tbConsultaNumberWhats.count() > 0;
    }

    public void salvarAtendimentoWhats(int codEmpresa,
                                       double codEvento,
                                       String foneCliente,
                                       String chatToken,
                                       String usuario) throws DataException {
        if (this.tbWhatsappAtendimento.count() == 0) {
            this.tbWhatsappAtendimento.append();
            this.tbWhatsappAtendimento.setSEQ_ATENDIMENTO(1);
        } else {
            this.tbWhatsappAtendimento.edit();
        }
        this.tbWhatsappAtendimento.setCOD_EMPRESA(codEmpresa);
        this.tbWhatsappAtendimento.setCOD_EVENTO(codEvento);
        this.tbWhatsappAtendimento.setDATA(new Date());
        this.tbWhatsappAtendimento.setRESPONSAVEL(usuario);
        this.tbWhatsappAtendimento.setFONE_CLIENTE(foneCliente);
        this.tbWhatsappAtendimento.setSTATUS("SENT");
        this.tbWhatsappAtendimento.setLINK(chatToken);
        this.tbWhatsappAtendimento.setAGUARDA_ATENDIMENTO("N");
        this.tbWhatsappAtendimento.post();
        this.tbWhatsappAtendimento.applyUpdates();
        this.tbWhatsappAtendimento.commitUpdates();

        if (this.tbEventos.getSTATUS().asString().equals("P")) {
            this.tbEventos.edit();
            this.tbEventos.setDATA_NOVO_CONTATO(new Date());
            this.tbEventos.post();
            this.tbEventos.applyUpdates();
            this.tbEventos.commitUpdates();
        }
    }

    public void filtrarWhatsAtendimento(int codEmpresa, double codEvento) throws DataException {
        this.tbWhatsappAtendimento.close();
        this.tbWhatsappAtendimento.clearFilters();
        this.tbWhatsappAtendimento.clearParams();
        this.tbWhatsappAtendimento.setFilterCOD_EMPRESA(codEmpresa);
        this.tbWhatsappAtendimento.setFilterCOD_EVENTO(codEvento);
        this.tbWhatsappAtendimento.open();

        this.tbEventos.close();
        this.tbEventos.clearFilters();
        this.tbEventos.clearParams();
        this.tbEventos.addFilter("COD_EMPRESA;COD_EVENTO");
        this.tbEventos.addParam("COD_EMPRESA", codEmpresa);
        this.tbEventos.addParam("COD_EVENTO", codEvento);
        this.tbEventos.open();
    }

    public String getKeyHashCode() throws DataException {
        this.tbKeyHashApiChat.close();
        this.tbKeyHashApiChat.clearFilters();
        this.tbKeyHashApiChat.clearParams();
        this.tbKeyHashApiChat.open();
        return this.tbKeyHashApiChat.getKEY().asString();
    }

}