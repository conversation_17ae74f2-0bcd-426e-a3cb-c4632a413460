package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ClientesProfissaoRNW;
import freedom.data.DataException;

public class ClientesProfissaoRNA extends ClientesProfissaoRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void filtrarClientesProfissao(String desc) throws DataException {
        tbClientesProfissao.close();
        tbClientesProfissao.clearFilters();
        tbClientesProfissao.clearParams();
        if (!desc.trim().equals("")) {
            tbClientesProfissao.addFilter("DESCRICAO");
            tbClientesProfissao.addParam("DESCRICAO", desc);
        }
        tbClientesProfissao.open();
    }

}
