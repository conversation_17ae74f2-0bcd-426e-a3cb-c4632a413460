package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AlterarEnderecoClienteRNW;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.pkg.PkgCrmPartsRNA;

public class AlterarEnderecoClienteRNA extends AlterarEnderecoClienteRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void filtrarEnderecoCliente(long codCliente, long tpEndereco) throws DataException {
        tbUf.close();
        tbUf.clearFilters();
        tbUf.clearParams();
        tbUf.open();

        tbAlteraEnderecoCliente.close();
        tbAlteraEnderecoCliente.clearParams();
        tbAlteraEnderecoCliente.clearFilters();
        tbAlteraEnderecoCliente.addParam("COD_CLIENTE", codCliente);
        tbAlteraEnderecoCliente.addParam("COD_TIPO_ENDERECO", tpEndereco);
        tbAlteraEnderecoCliente.open();

        tbCidades.close();
        tbCidades.clearFilters();
        tbCidades.clearParams();
        tbCidades.addFilter("UF");
        tbCidades.addParam("UF", tbAlteraEnderecoCliente.getUF().asString());
        tbCidades.open();
    }

    public void filtrarCidade(String uf) throws DataException {
        if (!uf.isEmpty()) {
            this.tbCidades.close();
            this.tbCidades.clearFilters();
            this.tbCidades.clearParams();
            this.tbCidades.setFilterUF(uf);
            this.tbCidades.open();
        }
    }

    public String alterarEndCliente(Value aCampoFoco) throws DataException {
        String retFuncao;
        try {
            Double aCodcliente = tbAlteraEnderecoCliente.getCOD_CLIENTE().asDecimal();
            Double aTpendereco = tbAlteraEnderecoCliente.getCOD_TIPO_ENDERECO().asDecimal();
            String aUf = tbAlteraEnderecoCliente.getUF().asString();
            Double aCodcidade = tbAlteraEnderecoCliente.getCOD_CIDADES().asDecimal();
            String aInscEstad = tbAlteraEnderecoCliente.getINSCRICAO_ESTADUAL().asString();
            String aBairro = tbAlteraEnderecoCliente.getBAIRRO().asString();
            String aRua = tbAlteraEnderecoCliente.getRUA().asString();
            String aNumero = tbAlteraEnderecoCliente.getNUMERO().asString();
            String aComplemento = tbAlteraEnderecoCliente.getCOMPLEMENTO().asString();
            String aCaixapostal = tbAlteraEnderecoCliente.getCX_POSTAL().asString();
            String aContato = tbAlteraEnderecoCliente.getCONTATO().asString();
            retFuncao = this.pkgCrmPartsRNA.alterarEnderecoCliente(aCodcliente,
                    aTpendereco,
                    aUf,
                    aCodcidade,
                    aInscEstad,
                    aBairro,
                    aRua,
                    aNumero,
                    aComplemento,
                    aCaixapostal,
                    aContato,
                    aCampoFoco);

        } catch (DataException e) {
            retFuncao = e.getMessage();
        }
        return retFuncao;
    }
}
