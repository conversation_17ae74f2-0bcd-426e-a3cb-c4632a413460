package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ParametroRNW;
import freedom.client.controls.impl.TFCombo;
import freedom.client.controls.impl.TFTable;
import freedom.client.controls.util.MakeListOptions;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.data.impl.View;
import freedom.util.TableUtil;
import org.apache.commons.lang.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

public class ParametroRNA extends ParametroRNW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String ID_SISTEMA = "ID_SISTEMA";

    private static final String COD_EMPRESA = "COD_EMPRESA";

    private static final String VALOR = "VALOR";

    private static final String DESCRICAO = "DESCRICAO";

    private String codEmpresasFixadas = "";

    public ParametroRNA() throws DataException {
        this.loadParams();
    }

    private void loadParams() throws DataException {
        this.tbCrmParmFluxo.clearFilters();
        this.tbCrmParmFluxo.clearParams();
        this.tbParmSys.clearFilters();
        this.tbParmSys.clearParams();
        this.tbParmSys2.clearFilters();
        this.tbParmSys2.clearParams();
        this.tbParmSys3.clearFilters();
        this.tbParmSys3.clearParams();
        if (StringUtils.isNotBlank(this.codEmpresasFixadas)) {
            this.tbCrmParmFluxo.setFilterFIXAR_EMP_SELECIONADAS(this.codEmpresasFixadas);
            this.tbParmSys.setFilterFIXAR_EMP_SELECIONADAS(this.codEmpresasFixadas);
            this.tbParmSys2.setFilterFIXAR_EMP_SELECIONADAS(this.codEmpresasFixadas);
            this.tbParmSys3.setFilterFIXAR_EMP_SELECIONADAS(this.codEmpresasFixadas);
        }
        this.tbCrmParmFluxo.open();
        this.tbParmSys.open();
        this.tbParmSys2.open();
        this.tbParmSys3.open();
        this.tbEmpresasTemp.open();
    }

    public Value getParametro(EnTipoParm tipoParm,
                              int codEmpresa,
                              String nomeParametro) throws DataException {
        if (codEmpresa == 0) {
            throw new DataException("Informe o Código da Empresa.");
        }
        if (nomeParametro.trim().isEmpty()) {
            throw new DataException("Informe o nome do Parâmetro");
        }
        Value result = null;
        try {
            if (tipoParm == EnTipoParm.CRM_PARM_FLUXO) {
                this.filtrarParm(this.tbCrmParmFluxo,
                        codEmpresa);
                result = this.tbCrmParmFluxo.getField(nomeParametro.trim());
            } else if (tipoParm == EnTipoParm.PARM_SYS) {
                this.filtrarParm(this.tbParmSys,
                        codEmpresa);
                result = this.tbParmSys.getField(nomeParametro.trim());
            } else if (tipoParm == EnTipoParm.PARM_SYS2) {
                this.filtrarParm(this.tbParmSys2,
                        codEmpresa);
                result = this.tbParmSys2.getField(nomeParametro.trim());
            } else if (tipoParm == EnTipoParm.PARM_SYS3) {
                this.filtrarParm(this.tbParmSys3,
                        codEmpresa);
                result = this.tbParmSys3.getField(nomeParametro.trim());
            }
        } catch (Exception exception) {
            throw new DataException("Erro ao retornar parâmetro " +
                    exception.getMessage());
        }
        return result;
    }

    public void filtrarSistema() throws DataException {
        this.tbParametroSistema.close();
        this.tbParametroSistema.clearFilters();
        this.tbParametroSistema.clearParams();
        this.tbParametroSistema.open();
    }

    public void filtrarGrupo(int idSistema) throws DataException {
        this.tbParametroGrupo.close();
        this.tbParametroGrupo.clearFilters();
        this.tbParametroGrupo.clearParams();
        if (idSistema > 0) {
            this.tbParametroGrupo.addFilter("EXISTE_PARAM_SISTEMA");
            this.tbParametroGrupo.addParam(ID_SISTEMA, idSistema);
        }
        this.tbParametroGrupo.setOrderBy("A.DESCRICAO");
        this.tbParametroGrupo.open();
    }

    public void filtrarEmpresas() throws DataException {
        this.tbEmpresas.close();
        this.tbEmpresas.clearFilters();
        this.tbEmpresas.clearParams();
        this.tbEmpresas.setFilterCOD_MATRIZ_MAIOR(" ");
        this.tbEmpresas.setFilterSTATUS("S");
        this.tbEmpresas.open();
    }

    public void filtrarParametroSys(int idSistema,
                                    int idGrupo,
                                    String descricao,
                                    boolean smtParSemValorDef) throws DataException {
        this.tbParametroSys.close();
        this.tbParametroSys.clearFilters();
        this.tbParametroSys.clearParams();
        if (!descricao.trim().isEmpty()) {
            this.tbParametroSys.setFilterDESCRICAO(descricao);
        }
        if (idSistema > 0) {
            this.tbParametroSys.setFilterID_SISTEMA(idSistema);
        }
        if (idGrupo > 0) {
            this.tbParametroSys.setFilterID_GRUPO(idGrupo);
        }
        this.tbParametroSys.setOrderBy("B.DESCRICAO, A.DESCRICAO");
        this.tbParametroSys.open();
        if (smtParSemValorDef) {
            try {
                this.tbParametroSys.setAttribute("TAG", 1);
                this.tbParametroSys.first();
                View vParametroSys = this.tbParametroSys.getView();
                String tabela;
                String campo;
                String valor;
                while (Boolean.FALSE.equals(vParametroSys.eof())) {
                    tabela = vParametroSys.getField("TABELA_DELPHI").asString().trim().toUpperCase();
                    campo = vParametroSys.getField("CAMPO_DELPHI").asString().trim().toUpperCase();
                    TFTable table = getTableParm(tabela);
                    if (table != null) {
                        this.filtrarParm(table,
                                0);
                        table.first();
                        while (Boolean.FALSE.equals(table.eof())) {
                            if (TableUtil.getExistField(table,
                                    campo)) {
                                valor = table.getField(campo).asString().trim();
                                if (!valor.isEmpty()) {
                                    this.tbParametroSys.gotoBookmark(vParametroSys.getBookmark());
                                    this.tbParametroSys.delete();
                                    break;
                                }
                            }
                            table.next();
                        }
                    }
                    vParametroSys.next();
                }
            } finally {
                this.tbParametroSys.setAttribute("TAG",
                        0);
                this.tbParametroSys.first();
            }
        }
    }

    public void setFilterEmpresasSelecionadas(String codEmpSel) throws DataException {
        this.codEmpresasFixadas = codEmpSel;
        this.loadParams();
    }

    public void filtrarParametroValor(long idEmpresa) throws DataException {
        TFCombo combo = new TFCombo();
        String tabela = this.tbParametroSys.getTABELA_DELPHI().asString().trim().toUpperCase();
        String campo = this.tbParametroSys.getCAMPO_DELPHI().asString().trim().toUpperCase();
        String objeto = this.tbParametroSys.getOBJETO().asString().trim();
        DateFormat horaFormat = new SimpleDateFormat("HH:mm");
        this.tbParamEmpresaValues.close();
        if ((!tabela.isEmpty())
                && (!campo.isEmpty())) {
            TFTable table = getTableParm(tabela);
            if (table != null) {
                this.filtrarParm(table,
                        idEmpresa);
                table.first();
                while (Boolean.FALSE.equals(table.eof())) {
                    int codEmp = table.getField(ParametroRNA.COD_EMPRESA).asInteger();
                    boolean existField = TableUtil.getExistField(table,
                            campo);
                    if (existField) {
                        String valor = table.getField(campo).asString();
                        String nomeEmpresa = this.getNomeEmpresa(codEmp);
                        this.tbParamEmpresaValues.append();
                        this.tbParamEmpresaValues.setField("CHECK",
                                "N");
                        this.tbParamEmpresaValues.setField(ParametroRNA.COD_EMPRESA,
                                codEmp);
                        this.tbParamEmpresaValues.setField("NOME_EMPRESA",
                                nomeEmpresa);
                        this.tbParamEmpresaValues.setField(ParametroRNA.VALOR,
                                valor);
                        //Retorno o valor Descritivo
                        switch (objeto) {
                            case "S": // String
                            case "N": // Numérico
                            case "I": // Inteiro
                            case "D": // Data
                                this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO,
                                        valor);
                                break;
                            case "B": // CheckBox
                                if (valor.trim().equals("S")) {
                                    this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO,
                                            "Sim");
                                } else {
                                    this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO,
                                            "Não");
                                }
                                break;
                            case "C": // ComboBox
                                this.setComboLookup(combo);
                                combo.setValue(valor);
                                this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO,
                                        combo.getText());
                                break;
                            case "L": // List Options ComboBox
                                String listOption = this.tbParametroSys.getLIST_OPTION().asString().trim();
                                if (!listOption.isEmpty()) {
                                    combo.setListOptions(listOption);
                                    combo.setValue(valor);
                                    this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO,
                                            combo.getText());
                                }
                                break;
                            case "H": // Hora
                                if (table.getField(campo).isNull()) {
                                    this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO, null);
                                } else {
                                    this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO, horaFormat.format(table.getField(campo).asDate()));
                                    this.tbParamEmpresaValues.setField(ParametroRNA.VALOR, table.getField(campo));
                                }
                                break;
                            case "P": // Password
                                this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO,
                                        "*****");
                                break;
                            case "A": // Arquivo
                                this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO,
                                        "(file)");
                                break;
                            case "M": // Memo para CLOB
                                if (valor.isEmpty()) {
                                    this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO, null);
                                } else {
                                    String descricao = (valor.trim().length() < 10 ? valor.trim() : valor.trim().substring(0, 10) + "...");
                                    this.tbParamEmpresaValues.setField(ParametroRNA.DESCRICAO, descricao);
                                }
                                break;
                            default:
                                break;
                        }
                        this.tbParamEmpresaValues.post();
                    } else {
                        throw new DataException("Campo: "
                                + campo
                                + " não encontrado tabela "
                                + tabela);
                    }
                    table.next();
                }
            }
        }
        this.tbParamEmpresaValues.first();
    }

    private void filtrarParm(TFTable table,
                             long idEmpresa) throws DataException {
        table.setCriteria(null);
        table.filter();
        if (idEmpresa > 0) {
            table.setCriteria("COD_EMPRESA = "
                    + idEmpresa);
        }
        table.filter();
    }

    private TFTable getTableParm(String tabela) {
        TFTable table = null;
        switch (tabela) {
            case "PARM_SYS":
                table = this.tbParmSys;
                break;
            case "PARM_SYS2":
                table = this.tbParmSys2;
                break;
            case "PARM_SYS3":
                table = this.tbParmSys3;
                break;
            case "CRM_PARM_FLUXO":
                table = this.tbCrmParmFluxo;
                break;
            default:
                break;
        }
        return table;
    }

    private String getNomeEmpresa(int codEmpresa) throws DataException {
        this.tbEmpresasTemp.setCriteria(null);
        this.tbEmpresasTemp.filter();
        this.tbEmpresasTemp.setCriteria("COD_EMPRESA = "
                + codEmpresa);
        this.tbEmpresasTemp.filter();
        boolean tbEmpresasTempEmpty = this.tbEmpresasTemp.isEmpty();
        if (tbEmpresasTempEmpty) {
            return "não encontrou empresa";
        }
        return this.tbEmpresasTemp.getNOME_EMPRESA_ABR().asString();
    }

    public void salvarAlteracoesParametros(String tipoAlteracao) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            this.sc.setSession(session);
            String tabela = this.tbParametroSys.getTABELA_DELPHI().asString().trim().toUpperCase();
            String campo = this.tbParametroSys.getCAMPO_DELPHI().asString().trim().toUpperCase();
            String tipoCampo = this.tbParametroSys.getOBJETO().asString().trim();
            int idEmpresa;
            TFTable table = getTableParm(tabela);
            switch (tipoAlteracao) {
                case "T":
                    this.tbParamEmpresaValues.first();
                    while (Boolean.FALSE.equals(this.tbParamEmpresaValues.eof())) {
                        String check = this.tbParamEmpresaValues.getField("CHECK").asString();
                        if (check.equals("S")) {
                            idEmpresa = this.tbParamEmpresaValues.getField(COD_EMPRESA).asInteger();
                            this.filtrarParm(table,
                                    idEmpresa);
                            boolean tableEmpty = table.isEmpty();
                            if (!tableEmpty) {
                                this.setValueTable(table,
                                        campo,
                                        tipoCampo);
                                table.applyUpdates();
                            }
                        }
                        this.tbParamEmpresaValues.next();
                    }
                    break;
                case "E":
                    idEmpresa = this.tbParamEmpresaValues.getField(COD_EMPRESA).asInteger();
                    this.filtrarParm(table,
                            idEmpresa);
                    boolean tableEmpty = table.isEmpty();
                    if (!tableEmpty) {
                        this.setValueTable(table,
                                campo,
                                tipoCampo);
                        table.applyUpdates();
                    }
                    break;
                default:
                    break;
            }
            session.commit();
            this.loadParams();
        } catch (DataException dataException) {
            session.rollback();
            throw dataException;
        } finally {
            session.close();
        }
    }

    private void setValueTable(TFTable table,
                               String campo,
                               String objeto) throws DataException {
        table.edit();
        switch (objeto) {
            case "I":
                table.setField(campo,
                        this.tbParamEmpresaValues.getField(VALOR).asInteger());
                break;
            case "N":
                table.setField(campo,
                        this.tbParamEmpresaValues.getField(VALOR).asDecimal());
                break;
            case "D":
                table.setField(campo,
                        this.tbParamEmpresaValues.getField(VALOR).asDate());
                break;
            default:
                table.setField(campo,
                        this.tbParamEmpresaValues.getField(VALOR));
                break;
        }
        table.post();
    }

    public boolean exibirMensagemAoMarcarSegundaEmpresaParametroParmSysVpMotivoExclusaoOrc() throws DataException {
        View vEmpresas = this.tbParamEmpresaValues.getView();
        vEmpresas.first();
        int contadorRegistrosChecados = 0;
        while (!vEmpresas.eof()) {
            String check = vEmpresas.getField("CHECK").asString();
            if (check.equals("S")) {
                String lookupFilter = this.tbParametroSys.getLOOKUP_FILTER().asString();
                int primeiraOcorrencia = lookupFilter.indexOf(":COD_EMPRESA");
                if (primeiraOcorrencia != -1) {
                        contadorRegistrosChecados++;
                }
            }
            vEmpresas.next();
        }
        return contadorRegistrosChecados > 1;
    }


    public void setComboLookup(TFCombo comboBox) throws DataException {
        String filter = this.tbParametroSys.getLOOKUP_FILTER().asString().trim();
        if (!filter.trim().isEmpty()
                && (filter.toUpperCase().indexOf(":COD_EMPRESA") > 0)) {
            String codEmpresa = this.tbParamEmpresaValues.getCOD_EMPRESA().asString();
            if (codEmpresa.trim().isEmpty()) {
                codEmpresa = "0";
            }
            filter = filter.replaceAll("(?i):COD_EMPRESA",
                    codEmpresa);
        }
        //Caso deseje que saia no log o Select informar no construtor o valor true MakeListOptions(true)
        try {
            String lo = new MakeListOptions(true).getListOptionsFrom(
                    this.tbParametroSys.getLOOKUP_TABLE().asString(),
                    this.tbParametroSys.getLOOKUP_KEY_FIELD().asString(),
                    this.tbParametroSys.getLOOKUP_DISPLAY_FIELD().asString(),
                    filter);
            comboBox.setListOptions(lo);
            comboBox.setFlex(true);
        } catch (DataException dataException) {
            throw new DataException("Erro ao configurar ListOption Campo: "
                    + this.tbParametroSys.getCAMPO_DELPHI().asString()
                    + " "
                    + dataException.getMessage());
        }
    }

}