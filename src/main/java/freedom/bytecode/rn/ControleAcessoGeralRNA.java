package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ControleAcessoGeralRNW;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class ControleAcessoGeralRNA extends ControleAcessoGeralRNW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String COD_FUNCAO = "COD_FUNCAO";

    private final EmpresaUtil empresaUtil = new EmpresaUtil();

    public void filtrarControleAcesso(int codFuncao,
                                      String codSistema,
                                      String descricao) throws DataException {
        this.tbControleAcesso.close();
        this.tbControleAcesso.clearFilters();
        this.tbControleAcesso.clearParams();
        this.tbControleAcesso.addParam("COD_SISTEMA", codSistema);
        this.tbControleAcesso.addParam(COD_FUNCAO, codFuncao);
        if (descricao.length() > 0) {
            this.tbControleAcesso.setFilterDESCRICAO(descricao);
        }
        this.tbControleAcesso.open();
    }

    public void filtrarEmpresasFuncoes() throws DataException {
        this.tbEmpresasFuncoes.close();
        this.tbEmpresasFuncoes.clearFilters();
        this.tbEmpresasFuncoes.clearParams();
        this.tbEmpresasFuncoes.open();
    }

    public void setPermissao(boolean permissao,
                             int codFuncao,
                             String codAcesso) throws DataException {
        //Atribui permissão de acesso
        if (permissao) {
            this.tbSistemaAcessoFuncao.close();
            this.tbSistemaAcessoFuncao.append();
            this.tbSistemaAcessoFuncao.setCOD_ACESSO(codAcesso);
            this.tbSistemaAcessoFuncao.setCOD_FUNCAO(codFuncao);
            this.tbSistemaAcessoFuncao.post();
            this.empresaUtil.salvar(this.sc);
            this.tbSistemaAcessoFuncao.commitUpdates();
        } else {
            //Remove permissão de acesso
            this.tbSistemaAcessoFuncao.close();
            this.tbSistemaAcessoFuncao.setFilterCOD_ACESSO(codAcesso);
            this.tbSistemaAcessoFuncao.setFilterCOD_FUNCAO(codFuncao);
            this.tbSistemaAcessoFuncao.open();
            this.tbSistemaAcessoFuncao.delete();
            this.empresaUtil.salvar(this.sc);
            this.tbSistemaAcessoFuncao.commitUpdates();
        }
    }

    public void carregarGridAgentesFuncao(int codFuncao,
                                          String pesquisaAvancadaLogin,
                                          String pesquisaAvancadaNomeCompleto,
                                          long pesquisaAvancadaCodEmpresa,
                                          int pesquisaAvancadaCodDepartamento,
                                          int pesquisaAvancadaCodDivisao,
                                          String pesquisaAvancadaCPF,
                                          String pesquisaAvancadaAtivo,
                                          String pesquisaAvancadaCadBD) throws DataException {
        this.tbPerfilUsuarios.close();
        this.tbPerfilUsuarios.clearFilters();
        this.tbPerfilUsuarios.clearParams();
        this.tbPerfilUsuarios.setFilterCOD_FUNCAO_EQUALS(codFuncao);
        if (!pesquisaAvancadaLogin.equals("")) {
            this.tbPerfilUsuarios.setFilterNOME_ANYPOSITION(pesquisaAvancadaLogin + "%");
        }
        if (!pesquisaAvancadaNomeCompleto.equals("")) {
            this.tbPerfilUsuarios.setFilterNOME_COMPLETO_ANYPOSITION(pesquisaAvancadaNomeCompleto + "%");
        }
        if (pesquisaAvancadaCodEmpresa > 0L) {
            this.tbPerfilUsuarios.setFilterCOD_EMPRESA_EQUALS(pesquisaAvancadaCodEmpresa);
        }
        if (pesquisaAvancadaCodDepartamento > 0) {
            this.tbPerfilUsuarios.setFilterCOD_EMPRESA_DEPARTAMENTO_EQUALS(pesquisaAvancadaCodDepartamento);
        }
        if (pesquisaAvancadaCodDivisao > 0) {
            this.tbPerfilUsuarios.setFilterCOD_EMPRESA_DIVISAO_EQUALS(pesquisaAvancadaCodDivisao);
        }
        if (!pesquisaAvancadaCPF.equals("")) {
            this.tbPerfilUsuarios.setFilterCPF_ANYPOSITION(pesquisaAvancadaCPF + "%");
        }
        if (!pesquisaAvancadaAtivo.equals("T")) {
            this.tbPerfilUsuarios.setFilterATIVO_EQUALS(pesquisaAvancadaAtivo);
        }
        if (!pesquisaAvancadaCadBD.equals("T")) {
            this.tbPerfilUsuarios.setFilterCAD_BD_EQUALS(pesquisaAvancadaCadBD);
        }
        this.tbPerfilUsuarios.open();
    }

    public void salvarDadosAgendaFuncao() throws DataException {
        this.tbEmpresasUsuarios.close();
        this.tbEmpresasUsuarios.clearFilters();
        this.tbEmpresasUsuarios.clearParams();
        String nome = this.tbPerfilUsuarios.getNOME().asString();
        this.tbEmpresasUsuarios.setFilterNOME(nome);
        this.tbEmpresasUsuarios.open();
        this.tbEmpresasUsuarios.edit();
        String fone = this.tbPerfilUsuarios.getFONE().asString();
        this.tbEmpresasUsuarios.setFONE(fone);
        String email = this.tbPerfilUsuarios.getEMAIL().asString();
        this.tbEmpresasUsuarios.setEMAIL(email);
        String tipoVendedor = this.tbPerfilUsuarios.getTIPO_VENDEDOR().asString();
        this.tbEmpresasUsuarios.setTIPO_VENDEDOR(tipoVendedor);
        String ramal = this.tbPerfilUsuarios.getRAMAL().asString();
        this.tbEmpresasUsuarios.setRAMAL(ramal);
        this.tbEmpresasUsuarios.post();
        this.tbUsuarioFoto.close();
        this.tbUsuarioFoto.clearFilters();
        this.tbUsuarioFoto.clearParams();
        this.tbUsuarioFoto.setFilterNOME(nome);
        this.tbUsuarioFoto.open();
        boolean fotoNula = this.tbPerfilUsuarios.getFOTO().isNull();
        boolean fotoVazia = this.tbUsuarioFoto.isEmpty();
        if (!fotoNula) {
            if (fotoVazia) {
                this.tbUsuarioFoto.append();
                this.tbUsuarioFoto.setNOME(nome);
            } else {
                this.tbUsuarioFoto.edit();
            }
            this.tbUsuarioFoto.setFOTO(this.tbPerfilUsuarios.getFOTO());
            this.tbUsuarioFoto.setFOTO_ICONE(this.tbPerfilUsuarios.getFOTO_ICONE());
            this.tbUsuarioFoto.post();
        } else {
            if (!fotoVazia) {
                this.tbUsuarioFoto.delete();
            }
        }
        this.empresaUtil.salvar(this.sc);
        this.tbEmpresasUsuarios.commitUpdates();
        this.tbUsuarioFoto.commitUpdates();
    }

}
