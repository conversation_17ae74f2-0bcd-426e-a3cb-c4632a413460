package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.CadastroIndicadoresRNW;
import freedom.data.DataException;

public class CadastroIndicadoresRNA extends CadastroIndicadoresRNW {
    private static final long serialVersionUID = 20130827081850L;
    private int idGrupoClasse;

    public void carregaComboGrupoInd(int idGrupoClasse) throws DataException {
        this.idGrupoClasse = idGrupoClasse;
        tbComboGrupoIndicadores.close();
        tbComboGrupoIndicadores.clearFilters();
        tbComboGrupoIndicadores.clearParams();
        tbComboGrupoIndicadores.addFilter("ID_GRUPO_CLASSE");
        tbComboGrupoIndicadores.addParam("ID_GRUPO_CLASSE", idGrupoClasse);
        tbComboGrupoIndicadores.open();
    }

    public void carregaGridIndicadores(Integer idPainel, Integer idIndicadorClasse) throws DataException {
        tbGridGrupoIndicadores.close();
        tbGridGrupoIndicadores.clearFilters();
        tbGridGrupoIndicadores.clearParams();
        tbGridGrupoIndicadores.addFilter("ID_GRUPO_CLASSE;ID_PAINEL;ID_INDICADOR_CLASSE");
        tbGridGrupoIndicadores.addParam("ID_GRUPO_CLASSE", idGrupoClasse);
        tbGridGrupoIndicadores.addParam("ID_PAINEL", idPainel);
        tbGridGrupoIndicadores.addParam("ID_INDICADOR_CLASSE", idIndicadorClasse);
        tbGridGrupoIndicadores.open();
    }

    public int displayOrder(Integer idPainel, Integer idGrupo) throws DataException {
        tbMaxDisplayOrder.close();
        tbMaxDisplayOrder.clearFilters();
        tbMaxDisplayOrder.clearParams();
        tbMaxDisplayOrder.addFilter("ID_PAINEL;ID_GRUPO");
        tbMaxDisplayOrder.addParam("ID_PAINEL", idPainel);
        tbMaxDisplayOrder.addParam("ID_GRUPO", idGrupo);
        tbMaxDisplayOrder.open();

        return tbMaxDisplayOrder.getDISPLAY_ORDER().asInteger();
    }
}
