package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.TimeMembrosRNW;
import freedom.data.DataException;

import freedom.bytecode.cursor.*;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.RowState;
import freedom.data.SequenceUtil;
import freedom.data.TableState;
import freedom.util.WorkListFactory;
import java.util.Date;

public class TimeMembrosRNA extends TimeMembrosRNW {

    private static final long serialVersionUID = 20130827081850L;

    public TableState operRN = TableState.QUERYING;

    public ISession getSession() throws DataException {
        ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

    public void incluir() throws DataException {
        tbTime.append();
        tbTime.setATIVO("S");
    }

    public void alterar() throws DataException {
        if (!tbTime.isEmpty()) {
            tbTime.edit();
        }
    }

    public void cancelar() throws DataException {
        tbTime.cancelUpdates();
        tbTimeMembro.cancelUpdates();
    }

    public void excluir() throws DataException {
        ISession s = getSession();
        try {
            excluir(s);
            s.commit();
        } catch (DataException de) {
            s.rollback();
            throw de;
        } finally {
            closeSession(s);
        }
    }

    public void excluir(ISession session) throws DataException {
        beforeExcluir(session);
        sc.setSession(session);
        sc.applyUpdates();
        afterExcluir(session);
        tbTime.commitUpdates();
        tbTimeMembro.commitUpdates();
    }

    public void excluiTableMaster() throws DataException {

        tbTimeMembro.first();
        while (!tbTimeMembro.eof()) {
            excluir43001();
        }

        tbTime.delete();

    }

    public void abreTabelaAux() throws DataException {
        ISession s = getSession();
        try {
            setSession(s);
            tbTimeMembro.setOrderBy("NOME");
            tbTime.refreshRecord();
        } finally {
            closeSession(s);
        }
    }



    public void setOperRN(TableState oper)  {
        operRN = oper;
    }

    public void salvar() throws DataException {
        ISession s = getSession();
        try {
            salvar(s);
            s.commit();
        } catch (DataException de) {
            alterar();
            s.rollback();
            throw de;
        } finally {
            closeSession(s);
        }
    }

    public void salvar(ISession session) throws DataException {
        setPKFK();
        validaDados();
        setSession(session);
        beforeSalvar(session);
        sc.setSession(session);
        sc.applyUpdates();
        afterSalvar(session);
        tbTime.commitUpdates();
        tbTimeMembro.commitUpdates();
    }

    public void beforeExcluir(ISession session) throws DataException {

    }

    public void afterExcluir(ISession session) throws DataException {

    }

    public void beforeSalvar(ISession session) throws DataException {

    }

    public void afterSalvar(ISession session) throws DataException {

    }

    public void validaDados() throws DataException {
        validaCrmTime();
        validaCrmTimeMembro();
    }

    public void setPKFK() throws DataException {

        tbTimeMembro.first();
        while (!tbTimeMembro.eof()) {
            if(tbTimeMembro.getID_TIME().isNull()) {
                tbTimeMembro.edit();
                tbTimeMembro.setID_TIME(tbTime.getID_TIME());
                tbTimeMembro.post();
            }
            tbTimeMembro.next();
        }
    }

    // este metodo facilita nas rotinas de processamento ja passando o session para todas as tabelas
    // envolvidas no processamento.
    public void setSession(ISession session) throws DataException {
        tbTime.setSession(session);
        tbTimeMembro.setSession(session);
        tbEmpresas.setSession(session);
        tbEmpresasFuncoes.setSession(session);
        tbTimeAgentesDisponiveis.setSession(session);
    }


    public void validaCrmTime() throws DataException {
        if ( !tbTime.isActive()
                || tbTime.isEmpty()) {
            return;
        }

        if (tbTime.getID_TIME().isNull()) {
            throw new DataException("Campo Id. Time de Time é de preenchimento obrigatório");
        }

        if (tbTime.getDESCRICAO().isNull()) {
            throw new DataException("Campo Descrição de Time é de preenchimento obrigatório");
        }
    }

    public void validaCrmTimeMembro() throws DataException {
        if ( !tbTimeMembro.isActive()
                || tbTimeMembro.isEmpty()) {
            return;
        }

        tbTimeMembro.first();
        while (!tbTimeMembro.eof()) {
            if (tbTimeMembro.getRowState() == RowState.INSERTED || tbTimeMembro.getRowState() == RowState.MODIFIED) {

                if (tbTimeMembro.getID_TIME().isNull()) {
                    throw new DataException("Campo Id. Time de Time Membro é de preenchimento obrigatório");
                }

                if (tbTimeMembro.getNOME().isNull()) {
                    throw new DataException("Campo Nome de Time Membro é de preenchimento obrigatório");
                }
            }
            tbTimeMembro.next();
        }
    }


    // desabilita controls e master table das tabelas da transação
    public void disableTables() throws DataException {
        tbTime.disableControls();
        tbTimeMembro.disableControls();
        tbTime.disableMasterTable();
    }

    // habilita controls e master table das tabelas da transação
    public void enableTables() throws DataException {
        tbTime.enableControls();
        tbTimeMembro.enableControls();
        tbTime.enableMasterTable();
    }


    /**
     * Metodo inlcui um novo registro tabela tbTimeMembro
     * @throws DataException
     */
    public void incluir43001() throws DataException {
        tbTimeMembro.append();
        tbTimeMembro.post();            // grid editavel precisa do post para mostrar a nova linha
        tbTimeMembro.edit();
        setPKFK43001();
        tbTimeMembro.post();       // grid editavel precisa do post para mostrar a linha
    }

    /**
     * Metodo altera registro tabela tbTimeMembro
     * @throws DataException
     */
    public void alterar43001() throws DataException {
        if (!tbTimeMembro.isEmpty()) {
            tbTimeMembro.edit();
        }
    }

    /**
     * Metodo exclui registro tabela tbTimeMembro
     * @throws DataException
     */
    public void excluir43001() throws DataException {
        try {
            if (!tbTimeMembro.isEmpty()) {
                tbTimeMembro.delete();
            }
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo cancela registro tabela tbTimeMembro
     * @throws DataException
     */
    public void cancelar43001() throws DataException {
        try {
            tbTimeMembro.cancel();
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo confirma registro tabela tbTimeMembro
     * @throws DataException
     */
    public void confirmar43001() throws DataException {
        try {
            tbTimeMembro.post();
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo SetPkFk tabela tbTimeMembro
     * @throws DataException
     */
    public void setPKFK43001() throws DataException {
        tbTimeMembro.edit();
        tbTimeMembro.setID_TIME(tbTime.getID_TIME());
    }

    public void filtrarEmpresas() throws DataException {
        tbEmpresas.close();
        tbEmpresas.clearFilters();
        tbEmpresas.clearParams();
        tbEmpresas.addFilter("COD_MATRIZ_MAIOR;STATUS");
        tbEmpresas.addParam("STATUS", "S");
        tbEmpresas.setOrderBy("A.NOME");
        tbEmpresas.open();
    }

    public void filtrarEmpresasFuncoes() throws DataException {
        tbEmpresasFuncoes.close();
        tbEmpresasFuncoes.clearFilters();
        tbEmpresasFuncoes.clearParams();
        tbEmpresasFuncoes.open();
    }

    public void filtrarAgentesDisponiveis(int codEmpresa, int codFuncao) throws DataException {
        tbTimeAgentesDisponiveis.close();
        tbTimeAgentesDisponiveis.clearFilters();
        tbTimeAgentesDisponiveis.clearParams();
        tbTimeAgentesDisponiveis.addFilter("NOT_MEMBRO_SEL; DEMITIDO");
        tbTimeAgentesDisponiveis.addParam("DEMITIDO", "N");

        if (codEmpresa > 0) {
            tbTimeAgentesDisponiveis.addFilter("COD_EMPRESA");
            tbTimeAgentesDisponiveis.addParam("COD_EMPRESA", codEmpresa);
        }

        if (codFuncao > 0) {
            tbTimeAgentesDisponiveis.addFilter("COD_FUNCAO");
            tbTimeAgentesDisponiveis.addParam("COD_FUNCAO", codFuncao);
        }

        tbTimeAgentesDisponiveis.open();

    }

    /**
     * filtro os times templates
     * @throws DataException
     */
    public void filtrarTimeTemplate() throws DataException {
        tbTimeTemplate.close();
        tbTimeTemplate.clearFilters();
        tbTimeTemplate.clearParams();
        tbTimeTemplate.setOrderBy("DESCRICAO ASC");
        tbTimeTemplate.open();
        tbTimeTemplate.first();
    }

    /***
     * retorna o id_template do template selecionado
     * @return
     */
    public int getIDTemplateCombo(){
        return tbTimeTemplate.getID_TEMPLATE().asInteger();
    }

    /**
     * faço uma copia da tabela tbTimeMembro para manter sincrono os resgistros com a tabela
     * tbTimeMembroTemplateCruzado, fazendo apenas um filtro.
     * @throws DataException
     */
    public void sincronizarTbTimeMembroTemplateDisponivel() throws DataException {
        //tbTimeMembroTemplateDisponivel.close();
        tbTimeMembroTemplateDisponivel.copyFrom(tbTimeMembro, false, true);
        tbTimeMembroTemplateDisponivel.setCriteria("ID_TEMPLATE is null");
        tbTimeMembroTemplateDisponivel.filter();
        tbTimeMembroTemplateDisponivel.locate("NOME", tbTimeMembro.getNOME());
    }

    /**
     * Retorna o nome(chave primaria composta) do membro da tabela tbTimeMembrosTemplateCruzado
     * @return
     */
    public String getNomeTbTimeMembroTemplateDisponivel(){
        return tbTimeMembroTemplateDisponivel.getNOME().asString();
    }

    /**
     * valida se a tabela tbTimeMembroTemplateDisponivel está vazia
     * @return
     */
    public boolean isEmptyTbTimeMembroTemplateDisponivel(){
        return tbTimeMembroTemplateDisponivel.isEmpty();
    }

    /**
     * faço uma copia da tabela tbTimeMembro para manter sincrono os resgistros com a tabela
     * tbTimeMembroTemplateCruzado, fazendo apenas um filtro.
     * @throws DataException
     */
    public void sincronizarTbTimeMembroTemplateCruzado() throws DataException {
        tbTimeMembroTemplateCruzado.copyFrom(tbTimeMembro, false, true);
        tbTimeMembroTemplateCruzado.setCriteria("ID_TEMPLATE is not null");
        tbTimeMembroTemplateCruzado.filter();
        tbTimeMembroTemplateCruzado.locate("NOME", tbTimeMembro.getNOME());
    }

    /**
     * Retorna o nome(chave primaria composta) do membro da tabela tbTimeMembrosTemplateCruzado
     * @return
     */
    public String getNomeTbTimeMembroTemplateCruzado(){
        return tbTimeMembroTemplateCruzado.getNOME().asString();
    }

    /**
     * valida se a tabela tbTimeMembroTemplateCruzado está vazia
     * @return
     */
    public boolean isEmptyTbTimeMembroTemplateCruzado(){
        return tbTimeMembroTemplateCruzado.isEmpty();
    }


    /**
     * atualiza o id_template da tabela tbTimeTemplate
     * @param idTemplate
     * @param nome
     * @throws DataException
     */
    public void setIdTemplateTbTimeMembro(Integer idTemplate, String descricaoTemplate, String nome) throws DataException {
        tbTimeMembro.disableControls();
        tbTimeMembro.disableMasterTable();
        tbTimeMembro.locate("NOME",nome);
        tbTimeMembro.edit();
        tbTimeMembro.setID_TEMPLATE(idTemplate);
        tbTimeMembro.setTEMPLATE_DESCRICAO(descricaoTemplate);
        tbTimeMembro.post();
        tbTimeMembro.enableMasterTable();
        tbTimeMembro.enableControls();
    }





}
