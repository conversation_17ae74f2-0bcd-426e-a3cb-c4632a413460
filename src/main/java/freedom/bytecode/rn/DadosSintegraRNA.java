package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.DadosSintegraRNW;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
import org.apache.commons.lang.StringUtils;

public class DadosSintegraRNA extends DadosSintegraRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    public void openDadosConsultaApi(String cpfCnpj, String inscricaoEstadual) throws DataException {
        String tipoCadRFB = cpfCnpj.length() > 11 ? "RFB_JUR" : "RFB_FIS";
        tbDadosCadastraisReceita.close();
        tbDadosCadastraisReceita.addParam("CPF_CNPJ", cpfCnpj);
        tbDadosCadastraisReceita.addParam("INSCRICAO_ESTADUAL", inscricaoEstadual);
        tbDadosCadastraisReceita.addParam("CAD_TIPO", tipoCadRFB);
        tbDadosCadastraisReceita.open();
        tbDadosCadastraisSintegra.close();
        tbDadosCadastraisSintegra.addParam("CPF_CNPJ", cpfCnpj);
        tbDadosCadastraisSintegra.addParam("INSCRICAO_ESTADUAL", inscricaoEstadual);
        tbDadosCadastraisSintegra.addParam("CAD_TIPO", "SINTEGRA");
        tbDadosCadastraisSintegra.open();
    }

    public String getUrlConsultaNbs(Double codEmpresa) {
        try {
            return pkgCrmPartsRNA.getUrlPluginConsultaNbs(codEmpresa);
        } catch (DataException e) {
            return "";
        }
    }

    public Boolean buscaCliente(Double codCliente) throws DataException {
        tbDadosFisicos.close();
        tbDadosFisicos.clearFilters();
        tbDadosFisicos.clearParams();
        tbDadosFisicos.addFilter("COD_CLIENTE");
        tbDadosFisicos.addParam("COD_CLIENTE", codCliente);
        tbDadosFisicos.open();
        return !tbDadosFisicos.isEmpty();
    }

    public void openPendenciasOrcamento(double codEmpresa, double codEvento) throws DataException {
        tbLeadsPendenciaCadIntegracao.close();
        tbLeadsPendenciaCadIntegracao.setFilterCOD_EMPRESA(codEmpresa);
        tbLeadsPendenciaCadIntegracao.setFilterCOD_EVENTO(codEvento);
        tbLeadsPendenciaCadIntegracao.open();
    }

    public String getSchemaAtual() throws DataException {

        tbSchemaAtual.close();
        tbSchemaAtual.clearFilters();
        tbSchemaAtual.clearParams();
        tbSchemaAtual.open();

        return tbSchemaAtual.getSCHEMA_NAME().asString().toUpperCase();
    }


    public String baixarPendenciasIntegracaoCadastro(double codCliente) throws DataException {
        return pkgCrmPartsRNA.baixarPendenciasCadIntegra(codCliente);
    }

    public boolean cadastroIntegracaoJaAtualizado(
            double codEmpresa
            ,double codCliente
            ,String inscricaoEstadual
    ) {
        try {
            return this.pkgCrmPartsRNA.cadastroIntegracaoAtualizado(
                    codEmpresa
                    ,codCliente
                    ,inscricaoEstadual).equals("S");
        } catch (DataException dataException) {
            dataException.printStackTrace();
            return false;
        }
    }

    public boolean podeFazerConsultaPluguinApi() {
        try {
            return pkgCrmPartsRNA.validarAcesso(usuarioLogado,
                    "K0618").equalsIgnoreCase("S");
        } catch (Exception e) {
            return false;
        }
    }

    public boolean podeForcarConsultaPluguinApi() {
        try {
            return pkgCrmPartsRNA.validarAcesso(usuarioLogado,
                    "K0619").equalsIgnoreCase("S");
        } catch (Exception e) {
            return false;
        }
    }

    public boolean usaPluginConsultaNBS(double codEmpresa) {
        try {
            return pkgCrmPartsRNA.getParametro(codEmpresa, "PARM_SYS3", "USA_PLUGIN_CONSULTA_NBS").equalsIgnoreCase("S");
        } catch (DataException e) {
            return false;
        }
    }

    public String atualizarCadastroIntegrado(double codEmpresa, double codCliente, String inscricao, double idConsultaNbs, String msgConsultaNbs) throws DataException {
        return pkgCrmPartsRNA.atualizarCadastroIntegrado(codEmpresa, codCliente, inscricao, idConsultaNbs, msgConsultaNbs);
    }

    public String infoCadastroIrregular(double codCliente, String inscricao) throws DataException {
        return pkgCrmPartsRNA.getCadastroIrregular(codCliente, inscricao);
    }

    public String infoSituacaoCadastralIntegracao(double codEmpresa, double codClienteApi, String tipoPessoa, String ie, Value aRfbSituacao, Value aRfbCadastroIrregular, Value aSintegraSituacao, Value aSintegraCadastroIsento, Value aSintegraCadastroIrregular, Value aSintegraMultiplasIe) throws DataException {
        return pkgCrmPartsRNA.getSituacaoCadastral(codEmpresa,
                codClienteApi,
                tipoPessoa,
                ie,
                aRfbSituacao,
                aRfbCadastroIrregular,
                aSintegraSituacao,
                aSintegraCadastroIsento,
                aSintegraCadastroIrregular,
                aSintegraMultiplasIe);
    }

    public String validarRetornoApiConsulta(Double idConsultaNBS) {

        try {
            return pkgCrmPartsRNA.validarRetornoApiConsulta(idConsultaNBS);
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
                try {
                    return pkgCrmPartsRNA.validarRetornoApiConsulta(idConsultaNBS);
                } catch (DataException ex) {
                    return ex.getMessage();
                }
            } else {
                return e.getMessage();
            }
        }
    }

    public String validarMensagemConsultaIntegracao(Double codEmpresa, Double codCliente, String inscricao, Double idConsultaNBS, String mensagemIntegracao) {
        try {
            return pkgCrmPartsRNA.validarMensagemConsultaApi(codEmpresa, codCliente, inscricao, idConsultaNBS, mensagemIntegracao);
        } catch (DataException e) {
            return mensagemIntegracao;
        }
    }

    public String validaalteracaoIEIsenta(double codCliente, String ie) {
        if (StringUtils.isNotBlank(ie)
                && !ie.equals("ISENTO")) {
            return ie;
        }
        try {
            tbDadosJuridicos.close();
            tbDadosJuridicos.setFilterCOD_CLIENTE(codCliente);
            tbDadosJuridicos.open();
        } catch (DataException e) {
            return ie;
        }
        return tbDadosJuridicos.getINSC_ESTADUAL().asString();
    }

    public String padronizarInscricaoEstadual(String uf, String inscricao) throws DataException {
        return pkgCrmPartsRNA.padronizarInscricaoEstadual(uf, inscricao);
    }

    public String validarCadastroConsulta(String cpfCnpj, String inscricao) {
        try {
            return pkgCrmPartsRNA.validarCadastroConsulta(cpfCnpj, inscricao);
        } catch (DataException e) {
            if (e.getMessage().toUpperCase().contains("ORA-04068")) {
                try {
                    return pkgCrmPartsRNA.validarCadastroConsulta(cpfCnpj, inscricao);
                } catch (DataException exc) {
                    return  "Falhou ao validar cadastro consultado!" + "\n" +exc.getMessage();
                }
            } else {
                    return "Falhou ao validar cadastro consultado!!!";
            }
        }
    }
}
