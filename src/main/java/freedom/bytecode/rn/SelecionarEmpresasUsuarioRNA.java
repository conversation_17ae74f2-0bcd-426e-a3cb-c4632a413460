package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.SelecionarEmpresasUsuarioRNW;
import freedom.data.DataException;
import freedom.data.impl.View;

import java.util.ArrayList;
import java.util.List;

public class SelecionarEmpresasUsuarioRNA extends SelecionarEmpresasUsuarioRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarGrdEmpresas(
            String usuario
            ,long codEmpresaUsuario
    ) throws DataException {
        this.tbEmpresas.close();
        this.tbEmpresas.clearFilters();
        this.tbEmpresas.clearParams();
        this.tbEmpresas.addParam(
                "USUARIO"
                ,usuario
        );
        this.tbEmpresas.addParam(
                "COD_EMPRESA_USUARIO"
                ,codEmpresaUsuario
        );
        this.tbEmpresas.open();
    }

    public List<Long> getListaDeCodigosDeEmpresasSelecionadas() throws DataException {
        ArrayList<Long> retFuncao = new ArrayList<>();
        View vTbEmpresas = this.tbEmpresas.getView();
        vTbEmpresas.first();
        while (Boolean.FALSE.equals(vTbEmpresas.eof())) {
            String sel = vTbEmpresas.getField("SEL").asString();
            if (sel.equals("S")) {
                long codigoDaEmpresa = vTbEmpresas.getField("COD_EMPRESA").asLong();
                retFuncao.add(
                        codigoDaEmpresa
                );
            }
            vTbEmpresas.next();
        }
        return retFuncao;
    }

    public void marcarDesmarcarGrdEmpresas() throws DataException {
        String sel = this.tbEmpresas.getSEL().asString();
        this.tbEmpresas.edit();
        if (sel.equals("S")) {
            this.tbEmpresas.setSEL("N");
        } else {
            this.tbEmpresas.setSEL("S");
        }
        this.tbEmpresas.post();
    }

    public void selecionarEmpresasNaGrdEmpresas(
            List<Long> listaDeCodigosDeEmpresas
    ) throws DataException {
        this.tbEmpresas.first();
        for (Long elementoDaLista : listaDeCodigosDeEmpresas) {
            this.tbEmpresas.first();
            boolean localizouRegistro = this.tbEmpresas.locate(
                    "COD_EMPRESA"
                    ,elementoDaLista
            );
            if (localizouRegistro) {
                this.tbEmpresas.edit();
                this.tbEmpresas.setSEL("S");
                this.tbEmpresas.post();
            }
        }
        this.tbEmpresas.first();
    }

}
