package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.FichaItemEliminarReservaRNW;
import freedom.data.DataException;
import freedom.util.pkg.PkgCrmPartsRNA;

public class FichaItemEliminarReservaRNA extends FichaItemEliminarReservaRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void excluirReservaParcialItem(
            double codEmpresa
            ,double controleReserva
            ,double qtdeExc
            ,String observacao
            ,String loginUsuarioLogado
    ) throws DataException {
        this.pkgCrmPartsRNA.excluirItensReservasParcial(
                codEmpresa
                ,controleReserva
                ,loginUsuarioLogado
                ,"M"
                ,qtdeExc
                ,observacao
        );
    }

}