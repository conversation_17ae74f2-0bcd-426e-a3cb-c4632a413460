/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.bytecode.rn;

import freedom.client.controls.IFocusable;
import freedom.client.controls.impl.TFGroupbox;
import freedom.client.controls.impl.TFHBox;
import freedom.client.controls.impl.TFLabel;
import freedom.client.controls.impl.TFTabsheet;
import freedom.client.controls.impl.TFVBox;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public class CampoFoco {

    @Getter
    private String field;
    @Getter
    private IFocusable edit;
    @Getter
    private TFGroupbox groupBox;
    @Getter
    private TFLabel lblMensagem;
    @Getter
    private TFTabsheet tabSheet;
    private TFVBox vBox;
    private TFHBox hBoxPai;

    public CampoFoco(String f, IFocusable e, TFLabel l) {
        this.field = f;
        this.edit = e;
        this.lblMensagem = l;
    }

    public CampoFoco(String f, IFocusable e, TFGroupbox g, TFLabel l) {
        this.field = f;
        this.edit = e;
        this.groupBox = g;
        this.lblMensagem = l;
    }

    public CampoFoco(String f, IFocusable e, TFTabsheet t, TFLabel l) {
        this.field = f;
        this.edit = e;
        this.tabSheet = t;
        this.lblMensagem = l;
    }

    public CampoFoco(String f, IFocusable e, TFTabsheet t, TFLabel l, TFVBox v) {
        this.field = f;
        this.edit = e;
        this.tabSheet = t;
        this.lblMensagem = l;
        this.vBox = v;
        this.hBoxPai = null;
    }

    public CampoFoco(String f, IFocusable e, TFTabsheet t, TFLabel l, TFVBox v, TFHBox pai) {
        this.field = f;
        this.edit = e;
        this.tabSheet = t;
        this.lblMensagem = l;
        this.vBox = v;
        this.hBoxPai = pai;
    }

    public CampoFoco(String f, IFocusable e, TFLabel l, TFVBox v) {
        this.field = f;
        this.edit = e;
        this.lblMensagem = l;
        this.vBox = v;
        this.hBoxPai = null;
    }

    public CampoFoco(String f, IFocusable e, TFLabel l, TFVBox v, TFHBox pai) {
        this.field = f;
        this.edit = e;
        this.lblMensagem = l;
        this.vBox = v;
        this.hBoxPai = pai;
    }

    public TFVBox getBox() {return vBox; }

    public TFHBox getHbox() {
        return hBoxPai;
    }

}
