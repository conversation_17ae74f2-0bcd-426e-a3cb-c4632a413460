package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.CadastroRapidoClienteProdRuralRNW;
import freedom.data.DataException;
import freedom.util.pkg.PkgCrmPartsRNA;

public class CadastroRapidoClienteProdRuralRNA extends CadastroRapidoClienteProdRuralRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void abreTabelas() throws DataException {
        if (!this.tbUf.isActive()) {
            this.tbUf.close();
            this.tbUf.setOrderBy("DESCRICAO");
            this.tbUf.open();
        }
    }

    public String padronizarInscricaoEstadual(String uf,
                                              String inscricaoEstadual) throws DataException {
        return this.pkgCrmPartsRNA.padronizarInscricaoEstadual(uf,
                inscricaoEstadual);
    }
}
