package freedom.bytecode.rn;

import freedom.bytecode.cursor.BUSCA_CLI_ESP_MARG_EMP;
import freedom.bytecode.rn.wizard.AdicMargMinMarkupCliEmpRNW;
import freedom.data.DataException;

public class AdicMargMinMarkupCliEmpRNA extends AdicMargMinMarkupCliEmpRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarEmpresas() throws DataException {
        this.tbEmpresas.close();
        this.tbEmpresas.clearFilters();
        this.tbEmpresas.clearParams();
        this.tbEmpresas.setOrderBy("E.NOME");
        this.tbEmpresas.open();
    }

    public void incluirAlterarMargemMinimaMarkupClienteEmpresa(double codCliente,
                                                               double codEmpresa,
                                                               double margemMinima) throws DataException {
        BUSCA_CLI_ESP_MARG_EMP tbCliEspMargEmp = new BUSCA_CLI_ESP_MARG_EMP("tbCliEspMargEmp");
        tbCliEspMargEmp.close();
        tbCliEspMargEmp.clearFilters();
        tbCliEspMargEmp.clearParams();
        tbCliEspMargEmp.setFilterCOD_CLIENTE(codCliente);
        tbCliEspMargEmp.setFilterCOD_EMPRESA(codEmpresa);
        tbCliEspMargEmp.open();
        if(Boolean.TRUE.equals(tbCliEspMargEmp.isEmpty())){
            tbCliEspMargEmp.append();
        } else {
            tbCliEspMargEmp.edit();
        }
        tbCliEspMargEmp.setCOD_CLIENTE(codCliente);
        tbCliEspMargEmp.setCOD_EMPRESA(codEmpresa);
        tbCliEspMargEmp.setMARGEM_MINIMA(margemMinima);
        tbCliEspMargEmp.post();
        tbCliEspMargEmp.applyUpdates();
        tbCliEspMargEmp.commitUpdates();
        tbCliEspMargEmp.close();
    }

}
