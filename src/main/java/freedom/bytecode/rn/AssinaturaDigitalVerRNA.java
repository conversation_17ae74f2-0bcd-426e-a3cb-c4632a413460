package freedom.bytecode.rn;
import freedom.data.DataException;

import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;

import freedom.bytecode.rn.wizard.AssinaturaDigitalVerRNW;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import org.json.JSONObject;

public class AssinaturaDigitalVerRNA extends AssinaturaDigitalVerRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public void filtrarTbSolicitacoesAssinaturas(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros, String tagSignatario) throws DataException {
        String valorCodigo = CrmAssinaturaDigitalUtils.getValorCodigo(tipoAssinatura, jsonParametros);
        tbSolicitacoesAssinaturas.close();
        tbSolicitacoesAssinaturas.clearFilters();
        tbSolicitacoesAssinaturas.clearParams();
        tbSolicitacoesAssinaturas.addParam("TIPO_DOCUMENTO", tipoAssinatura.getTipo());
        tbSolicitacoesAssinaturas.addParam("VALOR_CODIGO", valorCodigo);
        tbSolicitacoesAssinaturas.open();
    }

    public boolean tbSolicitacoesAssinaturasIsEmpty() throws DataException {
        return tbSolicitacoesAssinaturas.isEmpty() || !tbSolicitacoesAssinaturas.isActive();
    }

}
