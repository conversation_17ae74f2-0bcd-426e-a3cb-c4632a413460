package freedom.bytecode.rn;

import freedom.bytecode.cursor.*;
import freedom.bytecode.rn.wizard.EnviarEmailRNW;
import freedom.client.controls.impl.*;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.Value;
import freedom.report.ExportReportType;
import freedom.util.EmpresaUtil;
import freedom.util.PackageResult;
import freedom.util.pkg.PkgCrmPartsA;
import freedom.util.pkg.PkgCrmPartsRNA;
import java.io.ByteArrayOutputStream;

public class EnviarEmailRNA extends EnviarEmailRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    private final PkgCrmPartsA pkgCrmPartsA = new PkgCrmPartsA();

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private Double codEmpresa;

    private Double codEvento;

    private Double codOrcMapa = 0.0;

    public void setCodEmpresa(Double codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public void setCodEvento(Double codEvento) {
        this.codEvento = codEvento;
    }

    public void setCodOrcMapa(Double codOrcMapa) {
        this.codOrcMapa = codOrcMapa;
    }

    private void enviarEmailEvento(
            String emailPara,
            String emailAssunto,
            String emailMsg,
            String emailMsgPure) throws DataException {
        Value iSeqIdMailFluxo = new Value(null);
        this.pkgCrmPartsRNA.enviarEmail(this.codEmpresa,
                this.codEvento,
                this.usuarioLogado,
                emailPara,
                emailAssunto,
                emailMsg,
                emailMsgPure,
                iSeqIdMailFluxo);
    }

    private TFReport createReport(String name,
                                  String reportFile) {
        TFReport report = new TFReport();
        report.clearParams();
        report.setName(name);
        report.setReportFile(reportFile);
        return report;
    }

    private void gravarOrcamentoAnexo(ISession session,
                                      Value iSeqIdMailFluxo) throws DataException {
        ORC_MAPA tbOrcAux = new ORC_MAPA("tbOrcTemp");
        tbOrcAux.close();
        tbOrcAux.setSession(session);
        tbOrcAux.setFilterCOD_EMPRESA(this.codEmpresa);
        tbOrcAux.setFilterCOD_ORC_MAPA(this.codOrcMapa);
        tbOrcAux.open();
        String nomeRel = "/crmparts/orcMapa.jasper";
        if (tbOrcAux.getSEQUENCIA_DAV().asDecimal() > 0) {
            nomeRel = "/crmparts/orcMapaPaf.jasper";
        }
        TFReport report = createReport("orcamento", nomeRel);
        report.addParam("COD_EMPRESA", codEmpresa.longValue());
        report.addParam("COD_ORC_MAPA", codOrcMapa.longValue());
        report.addParam("POR_CODIGO", 1); //Ordenar por código
        report.addParam("IMPRIMIR_CODIGO", 0); //Nao vai imprimir o código dos itens
        String logMonitor = "Relatório \"" + nomeRel.replace("/crmparts/", "")
                .replace(".jasper", "") + "\"" + System.lineSeparator()
                + System.lineSeparator()
                + "Parâmetro \"COD_EMPRESA\" Valor \"" + codEmpresa + "\"" + System.lineSeparator()
                + System.lineSeparator()
                + "Parâmetro \"COD_ORC_MAPA\" Valor \"" + codOrcMapa + "\""
                + System.lineSeparator()
                + "Parâmetro \"POR_CODIGO\" Valor \"1\""
                + System.lineSeparator()
                + "Parâmetro \"IMPRIMIR_CODIGO\" Valor \"0\"";
        freedom.util.FRLogger.log(logMonitor, this.getClass());
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            report.runToStream(byteArrayOutputStream,
                    ExportReportType.PDF);
        } catch (Exception exception) {
            throw new DataException(exception.getMessage());
        }
        try {
            this.tbEmailFluxoAnexo.setSession(session);
            this.tbEmailFluxoAnexo.append();
            this.tbEmailFluxoAnexo.setID_MAIL_ANEXO(SequenceUtil.nextVal("SEQ_ID_MAIL_ANEXO"));
            this.tbEmailFluxoAnexo.setSEQ_ID_MAIL_FLUXO(iSeqIdMailFluxo.asDecimal());
            this.tbEmailFluxoAnexo.setANEXO(byteArrayOutputStream.toByteArray());
            this.tbEmailFluxoAnexo.setNOME_ARQUIVO("orc_n_" + this.codOrcMapa.intValue());
            this.tbEmailFluxoAnexo.setEXTENSAO_ARQUIVO("pdf");
            this.tbEmailFluxoAnexo.post();
            this.tbEmailFluxoAnexo.applyUpdates();
        } finally {
            try {
                byteArrayOutputStream.close();
            } catch (Exception exception) {
                throw new DataException(exception.getMessage());
            }
        }
    }

    public boolean faltaPedirAprovacaoCliente() throws DataException {
        CRM_PENDENCIA_EVENTO tbCrmPendenciaEvento = new CRM_PENDENCIA_EVENTO("tbCrmPendenciaEvento");
        tbCrmPendenciaEvento.setFilterCOD_EMPRESA(this.codEmpresa);
        tbCrmPendenciaEvento.setFilterCOD_EVENTO(this.codEvento);
        tbCrmPendenciaEvento.setFilterID_PENDENCIA(2);
        tbCrmPendenciaEvento.open();
        return tbCrmPendenciaEvento.isEmpty();
    }

    private void enviarEmailOrcamento(
            String emailPara,
            String emailAssunto,
            String emailMsg,
            String emailMsgPure,
            boolean solicitarAprCliente) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            Value seqIdMailFluxo = new Value(null);
            session.open();
            PackageResult.testarRetorno(this.pkgCrmPartsA.enviarEmail(session,
                    this.codEmpresa,
                    this.codEvento,
                    this.usuarioLogado,
                    emailPara,
                    emailAssunto,
                    emailMsg,
                    emailMsgPure,
                    seqIdMailFluxo));
            this.gravarOrcamentoAnexo(session,
                    seqIdMailFluxo);
            if (solicitarAprCliente) {
                PackageResult.testarRetorno(this.pkgCrmPartsA.solicitarAprCliente(session,
                        this.codEmpresa,
                        this.codOrcMapa,
                        this.usuarioLogado));
            }
            session.commit();
        } catch (DataException dataException) {
            session.rollback();
            throw dataException;
        } finally {
            session.close();
        }
    }

    public void enviarEmail(
            String emailPara,
            String emailAssunto,
            String emailMsg,
            String emailMsgPure,
            boolean solicitarAprCliente) throws DataException {
        if (this.codOrcMapa != null
                && this.codOrcMapa > 0.0) {
            this.enviarEmailOrcamento(emailPara,
                    emailAssunto,
                    emailMsg,
                    emailMsgPure,
                    solicitarAprCliente);
        } else {
            this.enviarEmailEvento(emailPara,
                    emailAssunto,
                    emailMsg,
                    emailMsgPure);
        }
    }

}