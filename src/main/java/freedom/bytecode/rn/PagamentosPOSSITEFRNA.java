package freedom.bytecode.rn;

import freedom.data.DataException;
import java.util.Date;
import freedom.util.Constantes;
import freedom.bytecode.rn.wizard.PagamentosPOSSITEFRNW;
import freedom.util.pkg.PkgCrmPartsRNA;

public class PagamentosPOSSITEFRNA extends PagamentosPOSSITEFRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarCboEmpresa(
            String usuario
            ,long codEmpresa
    ) throws DataException {
        this.tbEmpresas.close();
        this.tbEmpresas.clearFilters();
        this.tbEmpresas.clearParams();
        this.tbEmpresas.addParam(
                Constantes.USUARIO
                ,usuario
        );
        this.tbEmpresas.addParam(
                Constantes.COD_EMPRESA_USUARIO
                ,codEmpresa
        );
        this.tbEmpresas.open();
    }

    public void carregarGrdPagamentos(
            long codEmpresa
            , long codCliente
            , long codOrcMapa
            , Date dataEmissao
    ) throws DataException {
        this.tbPagamentosPosSitef.close();
        this.tbPagamentosPosSitef.clearFilters();
        this.tbPagamentosPosSitef.clearParams();
        if (codEmpresa > 0L) {
            this.tbPagamentosPosSitef.setFilterCOD_EMPRESA_EQUALS(
                    codEmpresa
            );
        }
        if (codCliente > 0L) {
            this.tbPagamentosPosSitef.setFilterCOD_CLIENTE_EQUALS(
                    codCliente
            );
        }
        if (codOrcMapa > 0L) {
            this.tbPagamentosPosSitef.setFilterCOD_ORC_MAPA_EQUALS(
                    codOrcMapa
            );
        }
        if (dataEmissao != null) {
            this.tbPagamentosPosSitef.setFilterEMISSAO_DATA_EQUALS(
                    dataEmissao
            );
        }
        this.tbPagamentosPosSitef.open();
    }

    public void carregarGrdParcelas(
            long idPagamento
    ) throws DataException {
        this.tbParcelasPgtoPosSitef.close();
        this.tbParcelasPgtoPosSitef.clearFilters();
        this.tbParcelasPgtoPosSitef.clearParams();
        this.tbParcelasPgtoPosSitef.setFilterID_PAGAMENTO_EQUALS(
                idPagamento
        );
        this.tbParcelasPgtoPosSitef.open();
    }

    public String apagarPagamentoPOSSITEF(
            long idPagamento
    ) throws DataException {
        PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();
        return pkgCrmPartsRNA.apagarPagamentoPos(
                (double) idPagamento
        );
    }

}
