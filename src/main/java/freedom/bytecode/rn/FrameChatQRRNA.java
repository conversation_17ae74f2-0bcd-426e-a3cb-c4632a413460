package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.FrameChatQRRNW;
public class FrameChatQRRNA extends FrameChatQRRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public boolean filtrarCadastroWhatsApp(int idCelular) throws DataException {
        this.tbCadastroWhatsapp.close();
        this.tbCadastroWhatsapp.clearFilters();
        this.tbCadastroWhatsapp.clearParams();
        this.tbCadastroWhatsapp.setFilterID_CELULAR(idCelular);
        this.tbCadastroWhatsapp.open();
        return  this.tbCadastroWhatsapp.count() > 0;
    }
}
