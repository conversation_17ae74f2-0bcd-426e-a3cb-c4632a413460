package freedom.bytecode.rn;

import freedom.bytecode.cursor.GRAFICO_INDICADORES_DIARIO;
import freedom.bytecode.cursor.GRAFICO_INDICADORES_QUEBRA;
import freedom.bytecode.cursor.GRAFICO_IND_ULT_12_MES;
import freedom.bytecode.rn.wizard.GerencialPainelRNW;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.FRLogger;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

public class GerencialPainelRNA extends GerencialPainelRNW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String WHERE_V_COD_EMPRESA = " where v.cod_empresa = ";

    private static final String WHERE_V_COD_EMPRESA_IN_SELECT_E_COD_EMPRESA = " where v.cod_empresa in (select e.cod_empresa ";

    private static final String FROM_CRM_EMPRESA_FUNCAO_CEF = "    from crm_empresa_funcao cef,  ";

    private static final String EMPRESAS_E = "         empresas e,              ";

    private static final String EMPRESAS_USUARIOS_EU = "         empresas_usuarios eu,    ";

    private static final String EMPRESAS_FUNCOES_F = "         empresas_funcoes f       ";

    private static final String WHERE_EU_COD_FUNCAO_F_COD_FUNCAO = "    where eu.cod_funcao = f.cod_funcao ";

    private static final String AND_F_COD_FUNCAO_CEF_COD_FUNCAO = "    and f.cod_funcao = cef.cod_funcao  ";

    private static final String AND_E_COD_EMPRESA_CEF_COD_EMPRESA_ACESSO = "    and e.cod_empresa = cef.cod_empresa_acesso ";

    private static final String AND_EU_NOME = "    and eu.nome = '";

    private static final String GROUP_BY_E_COD_EMPRESA = "    group by e.cod_empresa) ";

    private static final String CAMPO_CHAVE_NO_SQL_RETORNANDO_NULL_OU_VAZIO = "Campo Chave, no SQL, retornando null ou vazio.";

    private final String usuarioLogado = EmpresaUtil.getUserLogged();

    private final Integer codEmpresaUsuarioLog = EmpresaUtil.getCodEmpresaUserLogged();

    public void carregaPainelUsuario(int idGrupoClasse) throws DataException {
        this.tbPainelGerencialUsuario.close();
        this.tbPainelGerencialUsuario.clearFilters();
        this.tbPainelGerencialUsuario.clearParams();
        this.tbPainelGerencialUsuario.setFilterATIVO("S");
        this.tbPainelGerencialUsuario.setFilterUSUARIO_LOGADO(this.usuarioLogado);
        this.tbPainelGerencialUsuario.setFilterID_GRUPO_CLASSE_EQUALS(idGrupoClasse);
        this.tbPainelGerencialUsuario.open();
        this.tbAno.close();
        this.tbAno.open();
    }

    public void carregaComboEmpresa() throws DataException {
        this.tbEmpresaCruzadasFuncao.close();
        this.tbEmpresaCruzadasFuncao.clearFilters();
        this.tbEmpresaCruzadasFuncao.clearParams();
        this.tbEmpresaCruzadasFuncao.setFilterUSUARIO_LOGADO(this.usuarioLogado);
        this.tbEmpresaCruzadasFuncao.setFilterCOD_EMPRESA(this.codEmpresaUsuarioLog);
        this.tbEmpresaCruzadasFuncao.open();
    }

    public void atualizaPainel(int idPainel,
                               int codEmpresa,
                               String anoMes) throws DataException {
        this.tbGridGerencialIndicadores.close();
        this.tbGridGerencialIndicadores.clearFilters();
        this.tbGridGerencialIndicadores.clearParams();
        this.tbGridGerencialIndicadores.addParam("ID_PAINEL", idPainel);
        this.tbGridGerencialIndicadores.addParam("COD_EMPRESA", codEmpresa);
        this.tbGridGerencialIndicadores.addParam("ANO_MES", anoMes);
        this.tbGridGerencialIndicadores.addParam("USUARIO_LOGADO", this.usuarioLogado);
        if (codEmpresa > 0) {
            this.tbGridGerencialIndicadores.addParam("TP", 1);
        } else {
            this.tbGridGerencialIndicadores.addParam("TP", 0);
        }
        this.tbGridGerencialIndicadores.open();
    }

    public void carregaGraficoMensal(int codEmpresa,
                                     int ano,
                                     int mes,
                                     String view,
                                     String campo) throws DataException {
        this.tbGraficoIndicadoresMes.close();
        this.tbGraficoIndicadoresMes.clearFilters();
        this.tbGraficoIndicadoresMes.clearParams();
        this.tbGraficoIndicadoresMes.addParam("COD_EMPRESA", codEmpresa);
        this.tbGraficoIndicadoresMes.addParam("ANO", ano);
        this.tbGraficoIndicadoresMes.addParam("MES", mes);
        this.tbGraficoIndicadoresMes.addParam("VVIEW", view);
        this.tbGraficoIndicadoresMes.addParam("CAMPO", campo);
        this.tbGraficoIndicadoresMes.addParam("USUARIO_LOGADO", this.usuarioLogado);
        if (codEmpresa > 0) {
            this.tbGraficoIndicadoresMes.addParam("TP", 1);
        } else {
            this.tbGraficoIndicadoresMes.addParam("TP", 0);
        }
        this.tbGraficoIndicadoresMes.open();
    }

    public GRAFICO_INDICADORES_DIARIO getTableGraficoDiario(String campo,
                                                            String view,
                                                            int codEmpresa,
                                                            int ano,
                                                            int mes,
                                                            String periodo) throws DataException {
        ISession session = null;
        ResultSet rs = null;
        GRAFICO_INDICADORES_DIARIO tbRetorno = new GRAFICO_INDICADORES_DIARIO("tbGraficoIndicadoresDiario");
        PreparedStatement ps = null;
        try {
            try {
                session = SessionFactory.getInstance().getSession();
                session.open();
                String dataIni;
                String dataFim;
                String dataFimDiaAnt = "";
                if (periodo.equals("U")) {
                    dataIni = "trunc(sysdate) - 30 ";
                    dataFim = "trunc(sysdate) ";
                    dataFimDiaAnt = "(trunc(sysdate) -1)";
                } else {
                    dataIni = "to_date('01/" + mes + "/" + ano + "', 'DD/MM/YYYY')";
                    dataFim = "Last_day(" + dataIni + ")";
                }
                String sql;
                if (codEmpresa > 0) {
                    sql = "select  e.cod_empresa ||'-'||e.nome as cod_empresa, ";
                    sql = sql + "r.captionDiaMes AS CAPTION_DIAMES, SUM(" + campo + ") as QTDE, r.data ";
                } else {
                    sql = "select  r.cod_empresa, ";
                    sql = sql + "r.captionDiaMes AS CAPTION_DIAMES, SUM(" + campo + ") as QTDE, r.data ";
                }
                sql = sql + "from ( ";
                sql = sql + "SELECT 0 AS " + campo + ", ";
                sql = sql + dataIni + " + LEVEL - 1 as data, ";
                if (periodo.equals("U")) {
                    sql = sql + "to_char(" + dataIni + " + LEVEL - 1,'DD/MON') as captionDiaMes, ";
                } else {
                    sql = sql + "to_char(" + dataIni + " + LEVEL - 1,'DD') as captionDiaMes, ";
                }
                if (codEmpresa > 0) {
                    sql = sql + codEmpresa + " as cod_Empresa ";
                } else {
                    sql = sql + "'Todas as Empresas' as cod_Empresa ";
                }
                sql = sql + "from dual ";
                if (periodo.equals("U")) {
                    sql = sql + "CONNECT BY LEVEL <= " + dataFim + " - (" + dataIni + ")";
                } else {
                    sql = sql + "CONNECT BY LEVEL <= (" + dataFim + "+1) - (" + dataIni + ")";
                }
                sql = sql + " union ";
                sql = sql + " ";
                if (periodo.equals("U")) {
                    sql = sql + "select v." + campo + ", v.data, to_char(v.data,'DD/MON') as captionDiaMes, ";
                } else {
                    sql = sql + "select v." + campo + ", v.data, to_char(v.data,'DD') as captionDiaMes, ";
                }
                if (codEmpresa > 0) {
                    sql = sql + " v.cod_empresa ";
                } else {
                    sql = sql + " 'Todas as Empresas' as cod_empresa ";
                }
                sql = sql + "   from " + view + " v ";
                if (codEmpresa > 0) {
                    sql = sql + GerencialPainelRNA.WHERE_V_COD_EMPRESA + codEmpresa;
                } else {
                    sql = sql + GerencialPainelRNA.WHERE_V_COD_EMPRESA_IN_SELECT_E_COD_EMPRESA +
                            GerencialPainelRNA.FROM_CRM_EMPRESA_FUNCAO_CEF +
                            GerencialPainelRNA.EMPRESAS_E +
                            GerencialPainelRNA.EMPRESAS_USUARIOS_EU +
                            GerencialPainelRNA.EMPRESAS_FUNCOES_F +
                            GerencialPainelRNA.WHERE_EU_COD_FUNCAO_F_COD_FUNCAO +
                            GerencialPainelRNA.AND_F_COD_FUNCAO_CEF_COD_FUNCAO +
                            GerencialPainelRNA.AND_E_COD_EMPRESA_CEF_COD_EMPRESA_ACESSO +
                            GerencialPainelRNA.AND_EU_NOME + this.usuarioLogado + "' " +
                            GerencialPainelRNA.GROUP_BY_E_COD_EMPRESA;
                }
                if (periodo.equals("U")) {
                    sql = sql + "   and v.data between " + dataIni + " AND " + dataFimDiaAnt;
                } else {
                    sql = sql + "   and v.data between " + dataIni + " AND " + dataFim;
                }
                if (codEmpresa > 0) {
                    sql = sql + " ) r ,  empresas e ";
                    sql = sql + " where r.cod_empresa = e.cod_empresa ";
                    sql = sql + " GROUP BY  r.data, e.cod_empresa, r.captionDiaMes, e.nome";
                } else {
                    sql = sql + " ) r ";
                    sql = sql + " GROUP BY  r.data, r.cod_empresa, r.captionDiaMes";
                }
                sql = sql + " ORDER BY r.DATA ";
                FRLogger.log(sql,
                        this.getClass());
                ps = session.getConn().prepareStatement(sql);
                rs = ps.executeQuery();
                while (rs.next()) {
                    String codEmp = rs.getString(1);
                    String caption = rs.getString(2);
                    int qtde = rs.getInt(3);
                    Date datar = rs.getDate(4);
                    String desc = "1";
                    if (datar != null) {
                        tbRetorno.append();
                        tbRetorno.setCOD_EMPRESA(codEmp);
                        tbRetorno.setCAPTION_DIAMES(caption);
                        tbRetorno.setQTDE(qtde);
                        tbRetorno.setDATA(datar);
                        tbRetorno.setQUEBRA_DESCRICAO(desc);
                        tbRetorno.post();
                        continue;
                    }
                    throw new DataException(GerencialPainelRNA.CAMPO_CHAVE_NO_SQL_RETORNANDO_NULL_OU_VAZIO);
                }
            } finally {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
                session.close();
            }
        } catch (SQLException | DataException var16) {
            if (var16 instanceof DataException) {
                throw (DataException) var16;
            }
            throw new DataException(var16.getMessage(), var16);
        }
        return tbRetorno;
    }

    public void carregaComboGraficoQuebra(int codEmpresa,
                                          int ano,
                                          int mes,
                                          int idIndicador) throws DataException {
        this.tbComboQuebra.close();
        this.tbComboQuebra.clearFilters();
        this.tbComboQuebra.clearParams();
        this.tbComboQuebra.setFilterID_INDICADOR(idIndicador);
        this.tbComboQuebra.open();
    }

    public void carregaGraficoQuebra(String campo,
                                     String view,
                                     String quebra,
                                     int codEmpresa,
                                     String anoMes) throws DataException {
        this.tbGraficoIndicadoresQuebra.disableControls();
        this.tbGraficoIndicadoresQuebra.copyFrom(this.getTableGraficoQuebra(campo,
                        view,
                        quebra,
                        codEmpresa,
                        anoMes),
                true,
                true);
        this.tbGraficoIndicadoresQuebra.enableControls();
    }

    public GRAFICO_INDICADORES_QUEBRA getTableGraficoQuebra(String campo,
                                                            String view,
                                                            String quebra,
                                                            int codEmpresa,
                                                            String anoMes) throws DataException {
        ISession session = null;
        ResultSet rs = null;
        GRAFICO_INDICADORES_QUEBRA tbRetorno = new GRAFICO_INDICADORES_QUEBRA("tbGraficoIndicadoresQuebra");
        PreparedStatement ps = null;
        try {
            try {
                session = SessionFactory.getInstance().getSession();
                session.open();
                String sql = "Select v.ano_mes, v.cod_empresa, v.quebra_descricao ||' {'||v." + campo + "||'}' as quebra_descricao, v." + campo + " as qtde ";
                sql = sql + "from " + view + " v ";
                if (codEmpresa > 0) {
                    sql = sql + GerencialPainelRNA.WHERE_V_COD_EMPRESA + codEmpresa;
                } else {
                    sql = sql + GerencialPainelRNA.WHERE_V_COD_EMPRESA_IN_SELECT_E_COD_EMPRESA +
                            GerencialPainelRNA.FROM_CRM_EMPRESA_FUNCAO_CEF +
                            GerencialPainelRNA.EMPRESAS_E +
                            GerencialPainelRNA.EMPRESAS_USUARIOS_EU +
                            GerencialPainelRNA.EMPRESAS_FUNCOES_F +
                            GerencialPainelRNA.WHERE_EU_COD_FUNCAO_F_COD_FUNCAO +
                            GerencialPainelRNA.AND_F_COD_FUNCAO_CEF_COD_FUNCAO +
                            GerencialPainelRNA.AND_E_COD_EMPRESA_CEF_COD_EMPRESA_ACESSO +
                            GerencialPainelRNA.AND_EU_NOME + this.usuarioLogado + "' " +
                            GerencialPainelRNA.GROUP_BY_E_COD_EMPRESA;
                }
                sql = sql + "   and v.ano_mes = '" + anoMes + "'";
                if (!quebra.equals("")) {
                    sql = sql + "   and v.quebra_descricao in (" + quebra + ")";
                }
                sql = sql + " ORDER BY v." + campo + " desc ";
                FRLogger.log(sql,
                        this.getClass());
                ps = session.getConn().prepareStatement(sql);
                rs = ps.executeQuery();
                while (rs.next()) {
                    String anoMes2 = rs.getString(1);
                    int codEmp = rs.getInt(2);
                    String quebraDescricao = rs.getString(3);
                    int qtde = rs.getInt(4);
                    if (anoMes2 != null) {
                        tbRetorno.append();
                        tbRetorno.setANO_MES(anoMes2);
                        tbRetorno.setCOD_EMPRESA(codEmp);
                        tbRetorno.setQTDE(qtde);
                        tbRetorno.setQUEBRA_DESCRICAO(quebraDescricao);
                        tbRetorno.post();
                        continue;
                    }
                    throw new DataException(GerencialPainelRNA.CAMPO_CHAVE_NO_SQL_RETORNANDO_NULL_OU_VAZIO);
                }
            } finally {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
                session.close();
            }
        } catch (SQLException | DataException var16) {
            if (var16 instanceof DataException) {
                throw (DataException) var16;
            }
            throw new DataException(var16.getMessage(),
                    var16);
        }
        return tbRetorno;
    }

    public GRAFICO_IND_ULT_12_MES getTableGraficoUltimos12Meses(String campo,
                                                                String view,
                                                                int codEmpresa,
                                                                int ano,
                                                                int mes) throws DataException {
        ISession session = null;
        ResultSet rs = null;
        GRAFICO_IND_ULT_12_MES tbRetorno = new GRAFICO_IND_ULT_12_MES("tbGraficoIndUlt12Mes");
        PreparedStatement ps = null;
        try {
            try {
                session = SessionFactory.getInstance().getSession();
                session.open();
                String data = "trunc(sysdate)";
                String sql = "SELECT A.COD_EMPRESA, ";
                sql = sql + "A.MES_ANO, ";
                sql = sql + "SUM(A.QTDE) QTDE ";
                sql = sql + " FROM ( ";
                if (codEmpresa > 0) {
                    sql = sql + " SELECT  " + codEmpresa + " as cod_Empresa, ";
                } else {
                    sql = sql + " SELECT  'Todas as Empresas' as cod_empresa, ";
                }
                sql = sql + " To_char(Add_months(Trunc(Add_months(" + data + ",-12), 'MONTH'), LEVEL - 1),'YYYY/MM') as MES_ANO, ";
                sql = sql + " 0 AS Qtde ";
                sql = sql + " from dual ";
                sql = sql + " CONNECT BY LEVEL <= 12 ";
                sql = sql + " union ";
                sql = sql + " ";
                sql = sql + " ";
                if (codEmpresa > 0) {
                    sql = sql + "select v.cod_empresa, v.ano_mes, v." + campo + " as QTDE ";
                } else {
                    sql = sql + "select 'Todas as Empresas' as cod_empresa, v.ano_mes, v." + campo + " as QTDE ";
                }
                sql = sql + "   from " + view + " v ";
                if (codEmpresa > 0) {
                    sql = sql + GerencialPainelRNA.WHERE_V_COD_EMPRESA + codEmpresa;
                } else {
                    sql = sql + GerencialPainelRNA.WHERE_V_COD_EMPRESA_IN_SELECT_E_COD_EMPRESA +
                            GerencialPainelRNA.FROM_CRM_EMPRESA_FUNCAO_CEF +
                            GerencialPainelRNA.EMPRESAS_E +
                            GerencialPainelRNA.EMPRESAS_USUARIOS_EU +
                            GerencialPainelRNA.EMPRESAS_FUNCOES_F +
                            GerencialPainelRNA.WHERE_EU_COD_FUNCAO_F_COD_FUNCAO +
                            GerencialPainelRNA.AND_F_COD_FUNCAO_CEF_COD_FUNCAO +
                            GerencialPainelRNA.AND_E_COD_EMPRESA_CEF_COD_EMPRESA_ACESSO +
                            GerencialPainelRNA.AND_EU_NOME + usuarioLogado + "' " +
                            GerencialPainelRNA.GROUP_BY_E_COD_EMPRESA;
                }
                sql = sql + " and v.Ano_mes in (SELECT A.MES_ANO ";
                sql = sql + " FROM (SELECT To_char(Add_months(Trunc(Add_months(" + data + ",-12), 'MONTH'), LEVEL - 1),'YYYY/MM') AS MES_ANO ";
                sql = sql + " FROM dual ";
                sql = sql + " CONNECT BY LEVEL <= 12) A ) ";
                sql = sql + " )A ";
                sql = sql + " GROUP BY A.COD_EMPRESA, A.MES_ANO ";
                sql = sql + " ORDER BY A.MES_ANO ";
                FRLogger.log(sql,
                        this.getClass());
                ps = session.getConn().prepareStatement(sql);
                rs = ps.executeQuery();
                while (rs.next()) {
                    String codEmp = rs.getString(1);
                    String anomes = rs.getString(2);
                    int qtde = rs.getInt(3);
                    if (anomes != null) {
                        tbRetorno.append();
                        tbRetorno.setCOD_EMPRESA(codEmp);
                        tbRetorno.setMES_ANO(anomes);
                        tbRetorno.setQTDE(qtde);
                        tbRetorno.post();
                        continue;
                    }
                    throw new DataException(GerencialPainelRNA.CAMPO_CHAVE_NO_SQL_RETORNANDO_NULL_OU_VAZIO);
                }
            } finally {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
                session.close();
            }
        } catch (SQLException |
                 DataException var16) {
            if (var16 instanceof DataException) {
                throw (DataException) var16;
            }
            throw new DataException(var16.getMessage(), var16);
        }
        return tbRetorno;
    }

}