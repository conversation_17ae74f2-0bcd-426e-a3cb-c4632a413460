package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.SelecionarEmpresaRNW;
import freedom.util.pkg.PkgCrmPartsRNA;

public class SelecionarEmpresaRNA extends SelecionarEmpresaRNW  {
    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkCrmPartsRna = new PkgCrmPartsRNA();

    public void filtrarComboUf(String usuarioLogado, int codEmpresaLogado) throws DataException {
        tbLeadsEmpresasUsuariosUf.close();
        tbLeadsEmpresasUsuariosUf.addParam("USUARIO", usuarioLogado);
        tbLeadsEmpresasUsuariosUf.addParam("COD_EMPRESA_USUARIO", codEmpresaLogado);
        tbLeadsEmpresasUsuariosUf.open();

    }

    public void filtrarComboCidade(String usuarioLogado, int codEmpresaLogado) throws DataException {
        tbLeadsEmpresasUsuariosCidade.close();
        tbLeadsEmpresasUsuariosCidade.addParam("USUARIO", usuarioLogado);
        tbLeadsEmpresasUsuariosCidade.addParam("COD_EMPRESA_USUARIO", codEmpresaLogado);
        tbLeadsEmpresasUsuariosCidade.open();
    }

    public void filtrarLeadsEmpresasUsuario(String usuarioLogado, int codEmpresaLogado, int codEmpresaEvento) throws DataException {
        this.tbLeadsEmpresasUsuarios.close();
        this.tbLeadsEmpresasUsuarios.addParam("USUARIO", usuarioLogado);
        this.tbLeadsEmpresasUsuarios.addParam("COD_EMPRESA_USUARIO", codEmpresaLogado);
        this.tbLeadsEmpresasUsuarios.open();
        this.tbLeadsEmpresasUsuarios.locate("COD_EMPRESA", codEmpresaEvento);
    }

    public void recortarCidadePorEstado(String uf) throws DataException {
        tbLeadsEmpresasUsuariosCidade.setCriteria("CIDADE='NENHUM'");
        if(!uf.equals("")){
            tbLeadsEmpresasUsuariosCidade.setCriteria(("ESTADO = '" + uf + "'"));
        }
        tbLeadsEmpresasUsuariosCidade.filter();
    }

    public void recortarEmpresaPorUfCidade(String textoEmpresa, String uf, String cidade, int codEmpresaEvento) throws DataException {
        String filtroAplicado = " 1 = 1";
        if(!uf.equals("")){
            filtroAplicado += " AND ESTADO = '" + uf + "'";
            if(!cidade.equals("")){
                filtroAplicado += " AND CIDADE = '" + cidade + "'";
            }
        }
        if (!textoEmpresa.equals("")){
            filtroAplicado += " AND EMPRESA LIKE '%" + textoEmpresa + "%'";
        }

        tbLeadsEmpresasUsuarios.setCriteria(filtroAplicado);
        tbLeadsEmpresasUsuarios.filter();

        tbLeadsEmpresasUsuarios.sort("COD_EMPRESA", "ASC");
        if (!tbLeadsEmpresasUsuarios.locate("COD_EMPRESA", codEmpresaEvento)){
            tbLeadsEmpresasUsuarios.first();
        }
    }

    public void carregaTipoMidia() throws DataException {
        tbTipoMidia.close();
        tbTipoMidia.open();
    }

    public void carregaMidia(int codTipo) throws DataException {
        tbMidia.close();
        tbMidia.clearFilters();
        tbMidia.addFilter("ATIVO;COD_TIPO");
        tbMidia.addParam("ATIVO","S");
        tbMidia.addParam("COD_TIPO",codTipo);
        tbMidia.open();
    }

    public boolean getObrigaMidia(Double codEmpresa)  throws DataException {
        return pkCrmPartsRna.getParametro(codEmpresa, "PARM_SYS3", "OBRIGA_MIDIA_AGENDAMENTO").equals("S");
    }

}
