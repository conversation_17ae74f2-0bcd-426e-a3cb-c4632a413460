package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.AssinaturaRNW;
public class AssinaturaRNA extends AssinaturaRNW  {
    private static final long serialVersionUID = 20130827081850L;
}
