package freedom.bytecode.rn;

import freedom.bytecode.cursor.EMPRESAS_USUARIOS;
import freedom.bytecode.rn.wizard.HomeRNW;
import freedom.client.controls.IParentComponent;
import freedom.client.controls.ITFForm;
import freedom.client.controls.impl.TFMenu;
import freedom.client.controls.impl.TFMenuItem;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.ExceptionEngine;
import freedom.client.util.FormUtil;
import freedom.commons.lang.IWorkList;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.data.impl.Row;
import freedom.util.EmpresaUtil;
import freedom.util.FRLogger;
import freedom.util.SystemUtil;
import freedom.util.WorkListFactory;
import freedom.util.pkg.PkgCrmPartsRNA;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static freedom.util.CastUtil.asString;

public class HomeRNA extends HomeRNW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String ID_PROJETO = "ID_PROJETO";

    private static final String ID_MENU_SUPER = "ID_MENU_SUPER";

    private static final String ID_MENU = "ID_MENU";

    private static final String DESCRICAO = "DESCRICAO";

    private static final String ID_IMAGEM = "ID_IMAGEM";

    private static final String ID_FORM = "ID_FORM";

    private static final String ON_CLICK = "onClick";

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    private final IWorkList wl = WorkListFactory.getInstance();

    public void loadMenu(TFMenu mnu,
                         int idProjeto,
                         int codFuncao,
                         String codSistema,
                         EventListener listener,
                         boolean callForm,
                         boolean imageFormDefault) throws Exception {
        //Atualizar para buscar os menus do dictionary e atualizar BD Cliente.
        boolean atualizaMenu = this.isAtualizarMenu();
        if (atualizaMenu) {
            this.atualizarMenu(idProjeto);
        }
        this.tbMenuAcesso.close();
        this.tbMenuAcesso.clearFilters();
        this.tbMenuAcesso.clearParams();
        this.tbMenuAcesso.addParam(ID_PROJETO,
                idProjeto);
        this.tbMenuAcesso.addParam("COD_FUNCAO", codFuncao);
        this.tbMenuAcesso.addParam("COD_SISTEMA", codSistema);
        this.tbMenuAcesso.addParam("MENU_ACESSO", "S");
        this.tbMenuAcesso.open();

        mnu.clear();
        List<TFMenuItem> allNodes = new LinkedList<>();

        // gerar todos os itens do menu sem processar o parent
        for (Row row : this.tbMenuAcesso.getRows().getVisible()) {
            TFMenuItem mnui = new TFMenuItem();
            final String compName = "mnu"
                    + row.getField(ID_MENU_SUPER).asString()
                    + row.getField(ID_MENU).asString();

            mnui.setName(compName);
            mnui.setCaption(row.getField(DESCRICAO).asString());
            mnui.setAttribute(ID_MENU,
                    row.getField(ID_MENU).value());
            mnui.setAttribute(ID_MENU_SUPER,
                    row.getField(ID_MENU_SUPER).value());
            if (row.getField(ID_IMAGEM).asInteger() > 0) {
                mnui.setImageIndex(row.getField(ID_IMAGEM).asInteger());
            }

            if (!row.getField(ID_FORM).isNull()) {
                Integer idForm = row.getField(ID_FORM).asInteger();
                mnui.setAttribute(ID_FORM,
                        idForm);
                if ((!imageFormDefault)
                        && (row.getField(ID_IMAGEM).asInteger() == 0)) {
                    mnui.setImageIndex(-1);
                }
                // adicionar o evento onClick
                if (listener != null) {
                    mnui.addEventListener(ON_CLICK,
                            event -> {
                                try {
                                    ITFForm formCall = null;
                                    String formName;
                                    if (callForm) {
                                        ITFForm form = FormUtil.getForm(row.getField(ID_FORM).asInteger());
                                        formCall = form;
                                        formName = form.getName();
                                        FormUtil.createTab(form,
                                                null,
                                                true);
                                    } else {
                                        formName = row.getField(DESCRICAO).asString();
                                    }
                                    String idImagem = (row.getField(ID_IMAGEM).isNull()) ? "19001" : row.getField(ID_IMAGEM).asString();
                                    listener.onEvent(new Event<>(ON_CLICK,
                                            mnui,
                                            new String[] {
                                                    formName,
                                                    row.getField(DESCRICAO).asString(),
                                                    idImagem,
                                                    asString(idForm),
                                                    "F"
                                            },
                                            formCall));
                                    if (callForm) {
                                        IWorkFlow flow = WorkFlowFactory.getInstance();
                                        flow.continueFlow(formName,
                                                compName,
                                                "OnClick");
                                    }
                                } catch (Exception exception) {
                                    ExceptionEngine.register(exception);
                                }
                            });
                    mnui.addEventListener("onDoubleClick",
                            event -> {});
                }
                allNodes.add(mnui);
            } else {
                // remover ítens que são nodes de outros nodes e que não tem nenhum form dentro
                boolean hasChildrens = tbMenuAcesso.locate(ID_MENU_SUPER,
                        row.getField(ID_MENU).asInteger());
                if (hasChildrens) {
                    if (listener != null) {
                        mnui.addEventListener(ON_CLICK,
                                event -> {
                                    try {
                                        String menuName = row.getField(DESCRICAO).asString();
                                        String idImagem = (row.getField(ID_IMAGEM).isNull()) ? "19001" : row.getField(ID_IMAGEM).asString();
                                        String idMenu = row.getField(ID_MENU).asString();
                                        listener.onEvent(new Event<>(ON_CLICK,
                                                mnui,
                                                new String[] {
                                                        menuName,
                                                        row.getField(DESCRICAO).asString(),
                                                        idImagem,
                                                        asString(idMenu),
                                                        "M"
                                                },
                                                null));
                                    } catch (Exception exception) {
                                        ExceptionEngine.register(exception);
                                    }
                                });
                        mnui.addEventListener("onDoubleClick",
                                event -> {
                                });
                    }
                    allNodes.add(mnui);
                }
            }
        }
        // localizar os parents dos itens do menu
        allNodes.forEach(node -> findParent(mnu,
                node,
                allNodes));
        int totalNodes;
        do {
            totalNodes = allNodes.size();
            Arrays.asList(allNodes.toArray(new TFMenuItem[0])).parallelStream().forEach(node -> {
                /*
                if (node.getChildren().isEmpty() && node.getAttribute("ID_FORM") == null) {
                    allNodes.remove(node);
                    node.clearParent();
                }
                */
            });
        } while (totalNodes != allNodes.size());
        tbMenuAcesso.close();
        tbMenu.close();
        tbForm.close();
        tbProjeto.close();
    }


    private void findParent(IParentComponent rootNode,
                            IParentComponent node,
                            List<TFMenuItem> allNodes) {
        for (IParentComponent parent : allNodes) {
            if (node != parent
                    && parent.getAttribute(ID_MENU).equals(node.getAttribute(ID_MENU_SUPER))) {
                parent.addChildren(node);
                break;
            }
        }
        // quando nao tem parent é filho do menu raiz
        if (!node.hasParent()) {
            rootNode.addChildren(node);
        }
    }

    private boolean verificarAcessoPai(String codAcessoPai) throws DataException {
        boolean acessoPai = false;
        if (!codAcessoPai.isEmpty()) {
            this.tbSistemaAcesso.setCriteria(null);
            this.tbSistemaAcesso.filter();
            this.tbSistemaAcesso.setCriteria("COD_ACESSO = '" + codAcessoPai + "'");
            this.tbSistemaAcesso.filter();
            String codAcessoPaiPesq;
            if (this.tbSistemaAcesso.count() > 0) {
                acessoPai = !this.tbSistemaAcesso.getCOD_ACESSO_FUNCAO().asString().trim().isEmpty();
                if (acessoPai) {
                    codAcessoPaiPesq = this.tbSistemaAcesso.getCOD_ACESSO_PAI().asString().trim();
                    if (!codAcessoPaiPesq.isEmpty()) {
                        acessoPai = this.verificarAcessoPai(codAcessoPaiPesq);
                    }
                }
                if (!acessoPai) {
                    return false;
                }
            }
        }
        return acessoPai;
    }

    public void buscarPor(String valorBuscar) {

    }

    public void buscarCodigoPor(String valorBuscar) {

    }

    public boolean callBackMenuAcesso(String codAcesso,
                                      int codFuncao,
                                      Value codAcessoFuncao) {
        return false;
    }

    /**
     * Rotina para atualizar as tabelas de menus, forms, projetos e módulos.A
     * rotina irá se conectar no dictionary e ler as tabelas de menu, forms,
     * projetos e módulos e atualizar as tabelas no banco oracle cliente.
     *
     * @param idProjeto idProjeto
     * */
    public void atualizarMenu(int idProjeto) throws DataException {
        ISession sessionDic = null;
        ISession session = null;
        String schema = this.wl.get(IWorkList.DATASOURCE_DEFAULT).asString();
        try {
            session = SessionFactory.getInstance().getSession();
            session.open();
            sessionDic = SessionFactory.getInstance().getSession(schema + "_d");
            sessionDic.open();
            this.atualizarProjeto(sessionDic,
                    session);
            this.atualizarModulo(sessionDic,
                    session);
            this.atualizarForm(sessionDic,
                    session);
            this.atualizarMenus(sessionDic,
                    session);
            if (idProjeto > 0) {
                this.updateAtualizarProjeto(idProjeto,
                        session);
            }
            session.commit();
        } catch (DataException dataException) {
            if (session != null) {
                session.rollback();
            }
            throw new DataException(dataException.getMessage());
        } finally {
            if (sessionDic != null) {
                sessionDic.close();
            }
            if (session != null) {
                session.close();
            }
        }
    }

    private void atualizarProjeto(ISession sessionDic,
                                  ISession session) throws DataException {
        // Consulto os projetos no banco do dictionary
        this.tbProjetoConnDic.close();
        this.tbProjetoConnDic.setMaxRowCount(0);
        this.tbProjetoConnDic.clearFilters();
        this.tbProjetoConnDic.clearParams();
        this.tbProjetoConnDic.setSession(sessionDic);
        this.tbProjetoConnDic.openWithCols(ID_PROJETO,
                DESCRICAO);
        //Consulto os projetos no banco de dados cliente
        this.tbProjeto.close();
        this.tbProjeto.setMaxRowCount(0);
        this.tbProjeto.clearFilters();
        this.tbProjeto.clearParams();
        this.tbProjeto.setSession(session);
        this.tbProjeto.openWithCols(ID_PROJETO,
                DESCRICAO);
        while (Boolean.FALSE.equals(this.tbProjetoConnDic.eof())) {
            if (this.tbProjeto.locate(ID_PROJETO,
                    this.tbProjetoConnDic.getID_PROJETO().asInteger())) {
                this.tbProjeto.edit();
            } else {
                this.tbProjeto.append();
                this.tbProjeto.setATUALIZAR_MENU("N");
                this.tbProjeto.setID_PROJETO(this.tbProjetoConnDic.getID_PROJETO());
            }
            this.tbProjeto.setDESCRICAO(this.tbProjetoConnDic.getDESCRICAO());
            this.tbProjeto.post();
            this.tbProjetoConnDic.next();
        }
        this.tbProjeto.applyUpdates();
    }

    private void atualizarModulo(ISession sessionDic,
                                 ISession session) throws DataException {
        this.tbModuloConnDic.close();
        this.tbModuloConnDic.setMaxRowCount(0);
        this.tbModuloConnDic.clearFilters();
        this.tbModuloConnDic.clearParams();
        this.tbModuloConnDic.setSession(sessionDic);
        this.tbModuloConnDic.open();
        this.tbModulo.close();
        this.tbModulo.setMaxRowCount(0);
        this.tbModulo.clearFilters();
        this.tbModulo.clearParams();
        this.tbModulo.setSession(session);
        this.tbModulo.open();
        while (Boolean.FALSE.equals(this.tbModuloConnDic.eof())) {
            if (this.tbModulo.locate("ID_PROJETO, ID_MODULO",
                    this.tbModuloConnDic.getID_PROJETO().asInteger(),
                    this.tbModuloConnDic.getID_MODULO().asInteger())) {
                this.tbModulo.edit();
            } else {
                this.tbModulo.append();
                this.tbModulo.setID_PROJETO(this.tbModuloConnDic.getID_PROJETO());
                this.tbModulo.setID_MODULO(this.tbModuloConnDic.getID_MODULO());
            }
            this.tbModulo.setDESCRICAO(this.tbModuloConnDic.getDESCRICAO());
            this.tbModulo.post();
            this.tbModuloConnDic.next();
        }
        this.tbModulo.applyUpdates();
    }

    private void atualizarForm(ISession sessionDic,
                               ISession session) throws DataException {
        this.tbFormConnDic.close();
        this.tbFormConnDic.setMaxRowCount(0);
        this.tbFormConnDic.clearFilters();
        this.tbFormConnDic.clearParams();
        this.tbFormConnDic.setSession(sessionDic);
        this.tbFormConnDic.addFilter("EH_TEMPLATE");
        this.tbFormConnDic.open();
        this.tbForm.close();
        this.tbForm.setMaxRowCount(0);
        this.tbForm.clearFilters();
        this.tbForm.clearParams();
        this.tbForm.setSession(session);
        this.tbForm.open();
        while (Boolean.FALSE.equals(this.tbFormConnDic.eof())) {
            if (this.tbForm.locate(ID_FORM,
                    this.tbFormConnDic.getID_FORM().asInteger())) {
                this.tbForm.edit();
            } else {
                this.tbForm.append();
                this.tbForm.setID_FORM(this.tbFormConnDic.getID_FORM().asInteger());
            }
            this.tbForm.setID_PROJETO(this.tbFormConnDic.getID_PROJETO());
            this.tbForm.setID_MODULO(this.tbFormConnDic.getID_MODULO());
            this.tbForm.setTITLE(this.tbFormConnDic.getTITLE());
            this.tbForm.setNAME(this.tbFormConnDic.getNAME());
            this.tbForm.post();
            this.tbFormConnDic.next();
        }
        //Remover os forms que não existem mais
        this.tbForm.first();
        while (Boolean.FALSE.equals(this.tbForm.eof())) {
            if (!this.tbFormConnDic.locate(ID_FORM,
                    this.tbForm.getID_FORM().asInteger())) {
                this.removeFormDepMenu(session,
                        this.tbForm.getID_FORM().asInteger());
                this.tbForm.delete();
            } else {
                this.tbForm.next();
            }
        }
        this.tbForm.applyUpdates();
    }

    private void removeFormDepMenu(ISession session,
                                   int idForm) throws DataException {
        this.tbFrMenu.close();
        this.tbFrMenu.setMaxRowCount(0);
        this.tbFrMenu.clearFilters();
        this.tbFrMenu.clearParams();
        this.tbFrMenu.setSession(session);
        this.tbFrMenu.setFilterID_FORM(idForm);
        this.tbFrMenu.open();
        while (Boolean.FALSE.equals(this.tbFrMenu.eof())) {
            this.tbFrMenu.edit();
            this.tbFrMenu.setID_FORM(null);
            this.tbFrMenu.post();
            this.tbFrMenu.next();
        }
        this.tbFrMenu.applyUpdates();
    }

    private void atualizarMenus(ISession sessionDic,
                                ISession session) throws DataException {
        this.tbFrMenuConnDic.close();
        this.tbFrMenuConnDic.setMaxRowCount(0);
        this.tbFrMenuConnDic.clearFilters();
        this.tbFrMenuConnDic.clearParams();
        this.tbFrMenuConnDic.setSession(sessionDic);
        this.tbFrMenuConnDic.open();
        this.tbFrMenu.close();
        this.tbFrMenu.setMaxRowCount(0);
        this.tbFrMenu.clearFilters();
        this.tbFrMenu.clearParams();
        this.tbFrMenu.setSession(session);
        this.tbFrMenu.open();
        while (Boolean.FALSE.equals(this.tbFrMenuConnDic.eof())) {
            if (this.tbFrMenu.locate(ID_MENU,
                    this.tbFrMenuConnDic.getID_MENU().asInteger())) {
                this.tbFrMenu.edit();
            } else {
                this.tbFrMenu.append();
                this.tbFrMenu.setID_MENU(this.tbFrMenuConnDic.getID_MENU());
            }
            this.tbFrMenu.setDESCRICAO(this.tbFrMenuConnDic.getDESCRICAO());
            this.tbFrMenu.setCOD_ACESSO(this.tbFrMenuConnDic.getCOD_ACESSO());
            this.tbFrMenu.setID_FORM(this.tbFrMenuConnDic.getID_FORM());
            this.tbFrMenu.setID_MENU_SUPER(this.tbFrMenuConnDic.getID_MENU_SUPER());
            this.tbFrMenu.setID_IMAGEM(this.tbFrMenuConnDic.getID_IMAGEM());
            this.tbFrMenu.setORDEM(this.tbFrMenuConnDic.getORDEM());
            this.tbFrMenu.post();
            this.tbFrMenuConnDic.next();
        }
        this.tbFrMenu.first();
        while (Boolean.FALSE.equals(tbFrMenu.eof())) {
            if (!this.tbFrMenuConnDic.locate(ID_MENU,
                    this.tbFrMenu.getID_MENU().asInteger())) {
                this.tbFrMenu.delete();
            } else {
                this.tbFrMenu.next();
            }
        }
        this.tbFrMenu.applyUpdates();
    }

    private void updateAtualizarProjeto(int idProjeto,
                                        ISession session) throws DataException {
        this.filtrarProjeto(idProjeto,
                session);
        this.tbProjeto.edit();
        this.tbProjeto.setATUALIZAR_MENU("N");
        this.tbProjeto.post();
        this.tbProjeto.applyUpdates();
    }

    private boolean isAtualizarMenu() {
        if (!SystemUtil.isLinux()) {
            FRLogger.log("Rotina de atualização dos dados de dicionário FR_ ignorada pois o SO é diferente de Linux.",
                    this.getClass());
            return false;
        }
        return true;
    }

    private void filtrarProjeto(int idProjeto, ISession session) throws DataException {
        this.tbProjeto.close();
        if (session != null) {
            this.tbProjeto.setSession(session);
        }
        this.tbProjeto.clearFilters();
        this.tbProjeto.clearParams();
        this.tbProjeto.setFilterID_PROJETO(idProjeto);
        this.tbProjeto.open();
    }

    public String getPadraoPesquisaitem() {
        double codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();
        try {
            return this.pkgCrmPartsRNA.getParametro(codEmpresaUsuarioLogado,
                    "PARM_SYS3",
                    "PESQ_ITEM_PADRAO_PARTS");
        } catch (DataException e) {
            return "";
        }
    }

    /**
     * boolean retFuncao = this.rn.isParmSys3UsarCRMParts(
     *     codEmpresa
     * );
     * @param codEmpresa Código da empresa do parâmetro a ser verificado
     * @return true ou false
     */
    public boolean isParmSys3UsarCRMParts(
            double codEmpresa
    ) {
        try {
            return(
                    this.pkgCrmPartsRNA.getParametro(
                            codEmpresa
                            , "PARM_SYS3"                          /* Nome da tabela do Oracle */
                            ,"USAR_CRM_PARTS").equals("S") /* Nome da coluna da tabela do Oracle */
            );
        } catch (
                DataException dataException
        ) {
            return false;
        }
    }

    public boolean isUsuarioObrigadoATrocarSenhaAoLogar(
            String loginUsuario
    ) {
        boolean retFuncao = false;
        try {
            EMPRESAS_USUARIOS tbEmpresaUsuarios = new EMPRESAS_USUARIOS(
                    "tbEmpresaUsuarios"
            );
            tbEmpresaUsuarios.close();
            tbEmpresaUsuarios.clearFilters();
            tbEmpresaUsuarios.clearParams();
            tbEmpresaUsuarios.setFilterNOME(
                    loginUsuario
            );
            tbEmpresaUsuarios.open();
            boolean tbEmpresaUsuariosNotEmpty = !tbEmpresaUsuarios.isEmpty();
            if (tbEmpresaUsuariosNotEmpty) {
                retFuncao = tbEmpresaUsuarios.getTROCAR_SENHA_LOGAR().asString().equals("S");
            }
            tbEmpresaUsuarios.close();
        } catch (
                DataException dataException
        ) {
            retFuncao = false;
        }
        return retFuncao;
    }

}
