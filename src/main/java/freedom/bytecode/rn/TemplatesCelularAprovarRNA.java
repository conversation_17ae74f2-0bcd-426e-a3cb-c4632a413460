package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.TemplatesCelularAprovarRNW;
import freedom.data.DataException;

public class TemplatesCelularAprovarRNA extends TemplatesCelularAprovarRNW {
    private static final long serialVersionUID = 20130827081850L;

    public void filtrarCelular() throws DataException {
        tbCelularAtivosDisparos.close();
        tbCelularAtivosDisparos.clearFilters();
        tbCelularAtivosDisparos.clearParams();
        tbCelularAtivosDisparos.open();
    }

}
