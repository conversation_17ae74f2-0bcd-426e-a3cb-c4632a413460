package freedom.bytecode.rn;
import freedom.data.DataException;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalUtils;

import freedom.bytecode.rn.wizard.AssinaturaDigitaHistoricoRNW;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import org.json.JSONObject;

public class AssinaturaDigitaHistoricoRNA extends AssinaturaDigitaHistoricoRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public void filtrarTbAssinaturaDigital(TipoAssinaturaStrategy tipoAssinatura) throws DataException {
        tbAssinaturaDigital.close();
        tbAssinaturaDigital.clearFilters();
        tbAssinaturaDigital.clearParams();
        tbAssinaturaDigital.setFilterTIPO_ASSINATURA(tipoAssinatura.getTipo());
        tbAssinaturaDigital.open();
    }

    public boolean tbAssinaturaDigitalIsEmpty() {
        return !this.tbAssinaturaDigital.isActive() || this.tbAssinaturaDigital.isEmpty();
    }

    public void filtrarTbNbsapiEnvelopesLog(TipoAssinaturaStrategy tipoAssinatura, JSONObject jsonParametros) throws DataException {
        String codigo = CrmAssinaturaDigitalUtils.getValorCodigo(tbAssinaturaDigital.getTIPO_CODIGO().asString(), jsonParametros);
        tbNbsapiEnvelopesLog.close();
        tbNbsapiEnvelopesLog.clearFilters();
        tbNbsapiEnvelopesLog.clearParams();
        tbNbsapiEnvelopesLog.setFilterTIPO_ASSINATURA(tipoAssinatura.getTipo());
        tbNbsapiEnvelopesLog.setFilterVALOR_CODIGO(codigo);
        tbNbsapiEnvelopesLog.setOrderBy("DATA_LOG DESC");
        tbNbsapiEnvelopesLog.open();
    }
}
