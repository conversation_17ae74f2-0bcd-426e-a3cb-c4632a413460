package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.PesquisaClienteRNW;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
import org.apache.commons.lang.StringUtils;

public class PesquisaClienteRNA extends PesquisaClienteRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA;

    private final int codEmpresaUsuarioLogado = EmpresaUtil.getCodEmpresaUserLogged();

    private String creditoCorporativo;

    public PesquisaClienteRNA() {
        this.pkgCrmPartsRNA = new PkgCrmPartsRNA();
    }

    public void carregarGrdEndereco(
            double codCliente
    ) throws DataException {
        if (codCliente == 0.0) {
            return;
        }
        this.tbLeadsEnderecoCliente.close();
        this.tbLeadsEnderecoCliente.clearParams();
        this.tbLeadsEnderecoCliente.clearFilters();
        this.tbLeadsEnderecoCliente.addParam(
                Constantes.COD_CLIENTE
                ,codCliente
        );
        this.tbLeadsEnderecoCliente.addParam(
                Constantes.COD_EMPRESA
                ,this.codEmpresaUsuarioLogado
        );
        this.tbLeadsEnderecoCliente.open();
    }

    public void carregarGrdClientes(
            String filtrarPor
    ) throws DataException {
        this.tbLeadsConsultaClientes.close();
        this.tbLeadsConsultaClientes.setCriteria(
                null
        );
        this.tbLeadsConsultaClientes.clearFilters();
        this.tbLeadsConsultaClientes.clearParams();
        this.tbLeadsConsultaClientes.setFilterFILTO_NOME_COD_FONE(
                filtrarPor
        );
        this.tbLeadsConsultaClientes.addParam(
                Constantes.COD_EMPRESA
                ,this.codEmpresaUsuarioLogado
        );
        this.tbLeadsConsultaClientes.setMaxRowCount(
                1001
        );
        this.tbLeadsConsultaClientes.setFilterNUMERO_DE_REGISTROS_EQUALS(
                1000
        );
        this.tbLeadsConsultaClientes.setOrderBy(
                "CLI.NOME"
        );
        this.tbLeadsConsultaClientes.open();
        this.tbLeadsConsultaClientes.setCriteria(
                "COD_CLIENTE > 1"
        );
        this.tbLeadsConsultaClientes.filter();
        boolean tbLeadsConsultaClientesEmpty = this.tbLeadsConsultaClientes.isEmpty();
        if (tbLeadsConsultaClientesEmpty) {
            this.tbLeadsEnderecoCliente.close();
            this.tbListFoneCliente.close();
        }
    }

    public boolean existeEvento(double codCliente,
                                double codEmpresa,
                                String usuarioRespEvent) throws DataException {
        boolean retFuncao;
        this.tbCrmpartsExisteEvento.close();
        this.tbCrmpartsExisteEvento.clearFilters();
        this.tbCrmpartsExisteEvento.clearParams();
        this.tbCrmpartsExisteEvento.setFilterCOD_CLIENTE(codCliente);
        this.tbCrmpartsExisteEvento.setFilterCOD_EMPRESA(codEmpresa);
        if (!usuarioRespEvent.isEmpty()) {
            this.tbCrmpartsExisteEvento.setFilterRESPONSAVEL_PELO_EVENTO(usuarioRespEvent);
        }
        this.tbCrmpartsExisteEvento.setFilterSTATUS("P");
        this.tbCrmpartsExisteEvento.open();
        retFuncao = !tbCrmpartsExisteEvento.isEmpty();
        this.tbCrmpartsExisteEvento.close();
        return retFuncao;
    }

    public String getObservacaoVendaCliente(
            double codCliente
    ) throws DataException {
        return this.pkgCrmPartsRNA.getObsVendaCliente(
                codCliente
        );
    }

    public boolean usaCreditoCorporativo(int codEmpresaUsuarioLogado) {
        if (StringUtils.isBlank(this.creditoCorporativo)) {
            try {
                this.creditoCorporativo = pkgCrmPartsRNA.getParametro((double) codEmpresaUsuarioLogado,
                        "PARM_SYS3",
                        "CREDITO_CORPORATIVO");
            } catch (DataException e) {
                this.creditoCorporativo = "N";
            }
        }
        return this.creditoCorporativo.equals("S");
    }

    public String excluirClienteSelecionado(long codCliente) throws DataException {
        return this.pkgCrmPartsRNA.excluirCliente((double) codCliente);
    }

}