package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.PesquisaTransportadoraRNW;
import freedom.data.DataException;

public class PesquisaTransportadoraRNA extends PesquisaTransportadoraRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarGridTransportadora(
            long codTransportadora
            ,String descricaoTransportadora
            ,String cNPJCPF
    ) throws DataException {
        this.tbTransportadoras.close();
        this.tbTransportadoras.clearFilters();
        this.tbTransportadoras.clearParams();
        if (codTransportadora > 0L) {
            this.tbTransportadoras.setFilterCOD_TRANSPORTADORA(
                    codTransportadora
            );
        }
        if (!descricaoTransportadora.isEmpty()) {
            this.tbTransportadoras.setFilterDESCRICAO_TRANSPORTADORA(
                    descricaoTransportadora
            );
        }
        if (!cNPJCPF.isEmpty()) {
            this.tbTransportadoras.setFilterCNPJ_CPF(
                    cNPJCPF
            );
        }
        this.tbTransportadoras.setOrderBy("A.DESCRICAO");
        this.tbTransportadoras.setMaxRowCount(0);
        this.tbTransportadoras.open();
    }

}
