package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.LogsRNW;
public class LogsRNA extends LogsRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public void buscaLogsCliente(double codCliente, int codEmpresaUser) throws DataException{
        tbCadRapClienteLogs.close();
        tbCadRapClienteLogs.clearParams();
        tbCadRapClienteLogs.addParam("COD_CLIENTE", codCliente);
        tbCadRapClienteLogs.addParam("COD_EMPRESA_USUARIO_LOGADO",codEmpresaUser);
        tbCadRapClienteLogs.open();

        tbGridCadRapCliLogs.close();
        tbGridCadRapCliLogs.clearParams();
        tbGridCadRapCliLogs.addParam("COD_CLIENTE", codCliente);
        tbGridCadRapCliLogs.addParam("COD_EMPRESA_USUARIO_LOGADO",codEmpresaUser);
        tbGridCadRapCliLogs.open();
    }

}
