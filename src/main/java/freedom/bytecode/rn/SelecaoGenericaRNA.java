package freedom.bytecode.rn;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import freedom.bytecode.rn.wizard.SelecaoGenericaRNW;
public class SelecaoGenericaRNA extends SelecaoGenericaRNW  {
    private static final long serialVersionUID = 20130827081850L;
    String lookupTable;
    String lookupKeyField;
    String lookupDescricaoField;
    String lookupFilter;
    int numeroMaximoRegistro;

    /**
     * Define os valores iniciais da tabela a ser montada.
     *
     * @param lookupTable O nome da tabela a ser pesquisada.
     * @param lookupKeyField O nome do campo chave da tabela.
     * @param lookupDescricaoField O nome do campo de descrição da tabela.
     * @param lookupFilter O filtro a ser aplicado na tabela.
     * @param numeroMaximoRegistro O número máximo de registros a serem retornados.
     */
    public void setValoresIniciais(String lookupTable, String lookupKeyField, String lookupDescricaoField, String lookupFilter, int numeroMaximoRegistro){
        // Define o nome da tabela a ser pesquisada
        this.lookupTable = lookupTable;

        // Define o nome do campo chave da tabela
        this.lookupKeyField = lookupKeyField;

        // Define o nome do campo de descrição da tabela
        this.lookupDescricaoField = lookupDescricaoField;

        // Define o filtro a ser aplicado na tabela
        this.lookupFilter = lookupFilter;

        // Define o número máximo de registros a serem retornados
        this.numeroMaximoRegistro = numeroMaximoRegistro;
    }

    public String getLookupKey(){
        return tbSelecaoGenerica.getLOOKUP_KEY().asString();
    }

    public String getLookupDescricao(){
        return tbSelecaoGenerica.getLOOKUP_DESCRICAO().asString();
    }


    public void filtrarTbTSelecaoGenerica(String filterKey, String filterDescricao) throws DataException {
        tbSelecaoGenerica.disableControls();
        tbSelecaoGenerica.copyFrom(getTableGenerica(filterKey, filterDescricao),true, true);
        tbSelecaoGenerica.enableControls();
    }

    public void marcarRegistroDaGrade() throws DataException {
        this.tbSelecaoGenerica.edit();
        this.tbSelecaoGenerica.setEHSELECIONADO("S");
        this.tbSelecaoGenerica.post();
    }

    public void desmarcarRegistroDaGrade() throws DataException {
        this.tbSelecaoGenerica.edit();
        this.tbSelecaoGenerica.setEHSELECIONADO("N");
        this.tbSelecaoGenerica.post();
    }

    public SELECAO_GENERICA getTableGenerica(String filterLookupKey, String filterLookupDescricao) throws DataException {
        boolean needClose = false;
        ISession session= null;
        ResultSet rs = null;
        SELECAO_GENERICA tbRetorno = new SELECAO_GENERICA("tbSelecaoGenericaRetorno");
        PreparedStatement ps = null;
        if (this.lookupTable != null && !lookupTable.trim().isEmpty() && !this.lookupTable.contains(";")) {
            if (this.lookupKeyField != null && !this.lookupKeyField.trim().isEmpty() && !this.lookupKeyField.contains(";")) {
                if (this.lookupDescricaoField != null && !this.lookupDescricaoField.trim().isEmpty() && !this.lookupDescricaoField.contains(";")) {
                    if (this.lookupFilter != null && this.lookupFilter.contains(";")) {
                        throw new DataException("Filtro inválido");
                    } else {
                        try {
                            try {
                                if (session == null) {
                                    needClose = true;
                                    session = SessionFactory.getInstance().getSession();
                                    session.open();
                                }

                                String sql = "select " + this.lookupKeyField + ", " + this.lookupDescricaoField + " from " + this.lookupTable;
                                sql = sql + " where 1 = 1";
                                if (numeroMaximoRegistro>0){
                                    sql = sql + " and rownum <= " + this.numeroMaximoRegistro;
                                }
                                if (!filterLookupKey.equals("")){
                                    sql = sql + " and  UPPER(" + this.lookupKeyField + ") like UPPER('%"+ filterLookupKey +"%')";
                                }
                                if (!filterLookupDescricao.equals("")){
                                    sql = sql + " and  UPPER(" + this.lookupDescricaoField + ") like UPPER('%"+ filterLookupDescricao +"%')";
                                }
                                if (this.lookupFilter != null && !this.lookupFilter.trim().isEmpty()) {
                                    sql = sql + " and (" + this.lookupFilter + ")";
                                }
                                sql = sql + " order by " + this.lookupDescricaoField;

                                System.out.println(sql);

                                ps = session.getConn().prepareStatement(sql);
                                rs = ps.executeQuery();

                                while(rs.next()) {
                                    String lookupKey = rs.getString(1);
                                    String lookupDescricao = rs.getString(2);
                                    if (lookupKey != null && !lookupKey.trim().isEmpty()) {
                                        if (lookupDescricao != null && !lookupDescricao.trim().isEmpty()) {
                                            tbRetorno.append();
                                            tbRetorno.setEHSELECIONADO("N");
                                            tbRetorno.setLOOKUP_KEY(lookupKey.replaceAll("=|;", ""));
                                            tbRetorno.setLOOKUP_DESCRICAO(lookupDescricao);
                                            tbRetorno.post();
                                            continue;
                                        }
                                        throw new DataException(" Campo Descrição, no SQL, retornando null ou vazio.");
                                    }
                                    throw new DataException("Campo Chave, no SQL, retornando null ou vazio.");
                                }
                            } finally {
                                if (rs != null) {
                                    rs.close();
                                }

                                if (ps != null) {
                                    ps.close();
                                }

                                if (needClose) {
                                    session.close();
                                }

                            }
                        } catch (SQLException | DataException var16) {
                            if (var16 instanceof DataException) {
                                throw (DataException)var16;
                            }

                            throw new DataException(var16.getMessage(), var16);
                        }

                        return tbRetorno;
                    }
                } else {
                    throw new DataException("lookupDescricaoField inválido");
                }
            } else {
                throw new DataException("lookupKeyField inválido");
            }
        } else {
            throw new DataException("Nome de tabela inválido");
        }
    }
}
