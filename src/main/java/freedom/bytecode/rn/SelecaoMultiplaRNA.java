package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.SelecaoMultiplaRNW;
import freedom.data.DataException;
import freedom.data.impl.View;

import java.util.StringJoiner;

public class SelecaoMultiplaRNA extends SelecaoMultiplaRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void selecionarTodosOsRegistrosDaGrade() throws DataException {
        String codigoDoRegistroAtual = this.tbSelecaoMultipla.getCODIGO().asString();
        this.tbSelecaoMultipla.disableControls();
        try {
            //region Selecionar todos os registros da grade - Início
            this.tbSelecaoMultipla.first();
            while (Boolean.FALSE.equals(this.tbSelecaoMultipla.eof())) {
                this.tbSelecaoMultipla.edit();
                this.tbSelecaoMultipla.setSEL("S");
                this.tbSelecaoMultipla.post();
                this.tbSelecaoMultipla.next();
            }
            this.tbSelecaoMultipla.first();
            //endregion
            //region Posiciona o cursor no registro que o usuário havia focado
            while (Boolean.FALSE.equals(this.tbSelecaoMultipla.eof())) {
                String codigo = this.tbSelecaoMultipla.getCODIGO().asString();
                if (codigo.equals(codigoDoRegistroAtual)) {
                    return;
                } else {
                    this.tbSelecaoMultipla.next();
                }
            }
            //endregion
        } finally {
            this.tbSelecaoMultipla.enableControls();
        }
    }

    public void selecionarNenhumRegistroDaGrade() throws DataException {
        String codigoDoRegistroAtual = this.tbSelecaoMultipla.getCODIGO().asString();
        this.tbSelecaoMultipla.disableControls();
        try {
            //region Selecionar todos os registros da grade
            this.tbSelecaoMultipla.first();
            while (Boolean.FALSE.equals(this.tbSelecaoMultipla.eof())) {
                this.tbSelecaoMultipla.edit();
                this.tbSelecaoMultipla.setSEL("N");
                this.tbSelecaoMultipla.post();
                this.tbSelecaoMultipla.next();
            }
            this.tbSelecaoMultipla.first();
            //endregion
            //region Posiciona o cursor no registro que o usuário havia focado
            while (Boolean.FALSE.equals(this.tbSelecaoMultipla.eof())) {
                String codigo = this.tbSelecaoMultipla.getCODIGO().asString();
                if (codigo.equals(codigoDoRegistroAtual)) {
                    return;
                } else {
                    this.tbSelecaoMultipla.next();
                }
            }
            //endregion
        } finally {
            this.tbSelecaoMultipla.enableControls();
        }
    }

    public void marcarRegistroDaGrade() throws DataException {
        this.tbSelecaoMultipla.edit();
        this.tbSelecaoMultipla.setSEL("S");
        this.tbSelecaoMultipla.post();
    }

    public void desmarcarRegistroDaGrade() throws DataException {
        this.tbSelecaoMultipla.edit();
        this.tbSelecaoMultipla.setSEL("N");
        this.tbSelecaoMultipla.post();
    }

    public int getNumeroDeRegistrosSelecionadosNaGrade() throws DataException {
        int retFuncao = 0;
        View vSelecaoMultipla = this.tbSelecaoMultipla.getView();
        vSelecaoMultipla.first();
        while (Boolean.FALSE.equals(vSelecaoMultipla.eof())) {
            String sel = vSelecaoMultipla.getField("SEL").asString();
            if (sel.equals("S")) {
                retFuncao++;
            }
            vSelecaoMultipla.next();
        }
        return retFuncao;
    }

    public String[] getVetorDeStringContendoOsCodigosDosRegistrosSelecionadosNaGrade() throws DataException {
        int numeroDeRegistrosSelecionadosNaGrade = this.getNumeroDeRegistrosSelecionadosNaGrade();
        if (numeroDeRegistrosSelecionadosNaGrade == 0) {
            return new String[0];
        } else {
            String[] retFuncao = new String[numeroDeRegistrosSelecionadosNaGrade];
            View vSelecaoMultipla = tbSelecaoMultipla.getView();
            vSelecaoMultipla.first();
            int indiceDoVetor = 0;
            while (Boolean.FALSE.equals(vSelecaoMultipla.eof())) {
                String sel = vSelecaoMultipla.getField("SEL").asString();
                if (sel.equals("S")) {
                    retFuncao[indiceDoVetor] = vSelecaoMultipla.getField("CODIGO").asString();
                    indiceDoVetor++;
                }
                vSelecaoMultipla.next();
            }
            return retFuncao;
        }
    }

    public String getStringContendoOsCodigosDosRegistrosSelecionadosNaGradeSeparadosPorVirgulaEEspaco() throws DataException {
        int numeroDeRegistrosSelecionadosNaGrade = this.getNumeroDeRegistrosSelecionadosNaGrade();
        if (numeroDeRegistrosSelecionadosNaGrade == 0) {
            return null;
        } else {
            StringJoiner retFuncao = new StringJoiner(", ");
            View vSelecaoMultipla = tbSelecaoMultipla.getView();
            vSelecaoMultipla.first();
            while (Boolean.FALSE.equals(vSelecaoMultipla.eof())) {
                String sel = vSelecaoMultipla.getField("SEL").asString();
                if (sel.equals("S")) {
                    retFuncao.add(vSelecaoMultipla.getField("CODIGO").asString());
                }
                vSelecaoMultipla.next();
            }
            return retFuncao.toString();
        }
    }

}
