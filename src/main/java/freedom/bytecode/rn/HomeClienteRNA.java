package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.HomeClienteRNW;

public class HomeClienteRNA extends HomeClienteRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void getCliente(long codCliente) throws Exception {
        this.tbClienteDiverso.setFilterCOD_CLIENTE(codCliente);
        this.tbClienteDiverso.open();
        this.tbClienteSelecionarEmpresas.setFilterCOD_CLIENTE(codCliente);
        this.tbClienteSelecionarEmpresas.setOrderBy("NOME");
        this.tbClienteSelecionarEmpresas.open();
    }

}
