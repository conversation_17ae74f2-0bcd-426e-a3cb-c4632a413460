package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.CadastroLeadzapRNW;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.RowState;
import freedom.data.SequenceUtil;
import freedom.data.TableState;

public class CadastroLeadzapRNA extends CadastroLeadzapRNW {

    private static final long serialVersionUID = 20130827081850L;


    //------- inicio classe W

    public TableState operRN = TableState.QUERYING;

    public ISession getSession() throws DataException {
        ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

    public void incluir() throws DataException {
        tbCadastroWhatsapp.append();

    }

    public void alterar() throws DataException {
        if (!tbCadastroWhatsapp.isEmpty()) {
            tbCadastroWhatsapp.edit();
        }
    }

    public void cancelar() throws DataException {
        tbCadastroWhatsapp.cancelUpdates();
        tbWhatsappEmpresa.cancelUpdates();
        tbCadastroWhatsappCidades.cancelUpdates();
    }

    public void excluir() throws DataException {
        ISession s = getSession();
        try {
            excluir(s);
            s.commit();
        } catch (DataException de) {
            s.rollback();
            throw de;
        } finally {
            closeSession(s);
        }
    }

    public void excluir(ISession session) throws DataException {
        beforeExcluir(session);
        scCrmCadastroWhatsapp.setSession(session);
        scCrmCadastroWhatsapp.applyUpdates();
        afterExcluir(session);
        tbCadastroWhatsapp.commitUpdates();
        tbWhatsappEmpresa.commitUpdates();
        tbCadastroWhatsappCidades.commitUpdates();
    }

    public void excluiTableMaster() throws DataException {

        tbWhatsappEmpresa.first();
        while (!tbWhatsappEmpresa.eof()) {
            excluir46001();
        }

        tbCadastroWhatsapp.delete();

    }

    public void abreTabelaAux() throws DataException {
        ISession s = getSession();
        try {
            setSession(s);
            tbEmpresas.setOrderBy("NOME");
            tbEmpresas.close();
            tbEmpresas.open();
            tbCadastroWhatsapp.refreshRecord();
        } finally {
            closeSession(s);
        }
    }



    public void setOperRN(TableState oper)  {
        operRN = oper;
    }

    public void salvar() throws DataException {
        ISession s = getSession();
        try {
            salvar(s);
            s.commit();
        } catch (DataException de) {
            alterar();
            s.rollback();
            throw de;
        } finally {
            closeSession(s);
        }
    }

    public void salvar(ISession session) throws DataException {
        setPKFK();
        validaDados();
        setSession(session);
        beforeSalvar(session);
        scCrmCadastroWhatsapp.setSession(session);
        scCrmCadastroWhatsapp.applyUpdates();
        afterSalvar(session);
        tbCadastroWhatsapp.commitUpdates();
        tbWhatsappEmpresa.commitUpdates();
        tbCadastroWhatsappCidades.commitUpdates();
    }

    public void beforeExcluir(ISession session) throws DataException {

    }

    public void afterExcluir(ISession session) throws DataException {

    }

    public void afterSalvar(ISession session) throws DataException {

    }

    public void validaDados() throws DataException {
        validaCrmCadastroWhatsapp();
        validaCrmWhatsappEmpresa();
        validaCrmCadastroWhatsappCidades();
    }

    public void setPKFK() throws DataException {
        if (tbCadastroWhatsapp.getID_CELULAR().isNull()) {
            tbCadastroWhatsapp.edit();
            tbCadastroWhatsapp.setID_CELULAR(SequenceUtil.nextVal("SEQ_CRM_CADASTRO_WHATSAPP"));
            tbCadastroWhatsapp.post();
        }
        tbWhatsappEmpresa.first();
        while (!tbWhatsappEmpresa.eof()) {
            if(tbWhatsappEmpresa.getID_CELULAR().isNull()) {
                tbWhatsappEmpresa.edit();
                tbWhatsappEmpresa.setID_CELULAR(tbCadastroWhatsapp.getID_CELULAR());
                tbWhatsappEmpresa.post();
            }
            tbWhatsappEmpresa.next();
        }
    }

    // este metodo facilita nas rotinas de processamento ja passando o session para todas as tabelas
    // envolvidas no processamento.
    public void setSession(ISession session) throws DataException {
        tbCadastroWhatsapp.setSession(session);
        tbWhatsappEmpresa.setSession(session);
        tbEmpresas.setSession(session);
        tbWhatsappParametrosEmpresa.setSession(session);
        tbCadastroWhatsappValidar.setSession(session);
        tbEmpresasCruzaLeadZap.setSession(session);
        tbWhatsappLog.setSession(session);
        tbCadWhatsappAtivo.setSession(session);
        tbUf.setSession(session);
        tbCidades.setSession(session);
        tbCadastroWhatsappCidades.setSession(session);
        tbLeadzapMenu.setSession(session);
    }

    public void validaCrmWhatsappEmpresa() throws DataException {
        if ( !tbWhatsappEmpresa.isActive()
                || tbWhatsappEmpresa.isEmpty()) {
            return;
        }

        tbWhatsappEmpresa.first();
        while (!tbWhatsappEmpresa.eof()) {
            if (tbWhatsappEmpresa.getRowState() == RowState.INSERTED || tbWhatsappEmpresa.getRowState() == RowState.MODIFIED) {

                if (tbWhatsappEmpresa.getID_CELULAR().isNull()) {
                    throw new DataException("Campo Id. Celular de Whatsapp Empresa é de preenchimento obrigatório");
                }

                if (tbWhatsappEmpresa.getCOD_EMPRESA().isNull()) {
                    throw new DataException("Campo Cód. Empresa de Whatsapp Empresa é de preenchimento obrigatório");
                }
            }
            tbWhatsappEmpresa.next();
        }
    }

    public void validaCrmCadastroWhatsappCidades() throws DataException {
        if ( !tbCadastroWhatsappCidades.isActive()
                || tbCadastroWhatsappCidades.isEmpty()) {
            return;
        }

        if (tbCadastroWhatsappCidades.getUF().isNull()) {
            throw new DataException("Campo Uf de Cadastro Whatsapp Cidades é de preenchimento obrigatório");
        }

        if (tbCadastroWhatsappCidades.getID_CELULAR().isNull()) {
            throw new DataException("Campo Id. Celular de Cadastro Whatsapp Cidades é de preenchimento obrigatório");
        }

        if (tbCadastroWhatsappCidades.getCOD_CIDADES().isNull()) {
            throw new DataException("Campo Cód. Cidades de Cadastro Whatsapp Cidades é de preenchimento obrigatório");
        }
    }


    // desabilita controls e master table das tabelas da transação
    public void disableTables() throws DataException {
        tbCadastroWhatsapp.disableControls();
        tbWhatsappEmpresa.disableControls();
        tbCadastroWhatsappCidades.disableControls();
        tbCadastroWhatsapp.disableMasterTable();
    }

    // habilita controls e master table das tabelas da transação
    public void enableTables() throws DataException {
        tbCadastroWhatsapp.enableControls();
        tbWhatsappEmpresa.enableControls();
        tbCadastroWhatsappCidades.enableControls();
        tbCadastroWhatsapp.enableMasterTable();
    }


    /**
     * Metodo inlcui um novo registro tabela tbWhatsappEmpresa
     * @throws DataException
     */
    public void incluir46001() throws DataException {
        tbWhatsappEmpresa.append();
        tbWhatsappEmpresa.post();            // grid editavel precisa do post para mostrar a nova linha
        tbWhatsappEmpresa.edit();

        setPKFK46001();
        tbWhatsappEmpresa.post();       // grid editavel precisa do post para mostrar a linha
    }

    /**
     * Metodo altera registro tabela tbWhatsappEmpresa
     * @throws DataException
     */
    public void alterar46001() throws DataException {
        if (!tbWhatsappEmpresa.isEmpty()) {
            tbWhatsappEmpresa.edit();
        }
    }

    /**
     * Metodo exclui registro tabela tbWhatsappEmpresa
     * @throws DataException
     */
    public void excluir46001() throws DataException {
        try {
            if (!tbWhatsappEmpresa.isEmpty()) {
                tbWhatsappEmpresa.delete();
            }
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo cancela registro tabela tbWhatsappEmpresa
     * @throws DataException
     */
    public void cancelar46001() throws DataException {
        try {
            tbWhatsappEmpresa.cancel();
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo confirma registro tabela tbWhatsappEmpresa
     * @throws DataException
     */
    public void confirmar46001() throws DataException {
        try {
            tbWhatsappEmpresa.post();
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo SetPkFk tabela tbWhatsappEmpresa
     * @throws DataException
     */
    public void setPKFK46001() throws DataException {
        tbWhatsappEmpresa.edit();

        tbWhatsappEmpresa.setID_CELULAR(tbCadastroWhatsapp.getID_CELULAR());
    }

    //------- fim classe w

    public void beforeSalvar(ISession session) throws DataException {

    }

    public void validaCrmCadastroWhatsapp() throws DataException {
        if (tbCadastroWhatsapp.isEmpty()) {
            return;
        }

        if (tbCadastroWhatsapp.getDESCRICAO().isNull()) {
            throw new DataException("Campo Nome é de preenchimento obrigatório");
        }

        if (tbCadastroWhatsapp.getCELULAR().isNull()) {
            throw new DataException("Campo Celular de Cadastro Whatsapp é de preenchimento obrigatório");
        }

        if (tbCadastroWhatsapp.getCOD_EMPRESA().isNull()) {
            throw new DataException("Campo Cód. Empresa de Cadastro Whatsapp é de preenchimento obrigatório");
        }

        if (tbCadastroWhatsapp.getID_CELULAR().isNull()) {
            throw new DataException("Campo Id. Celular de Cadastro Whatsapp é de preenchimento obrigatório");
        }
    }

    public void filtrarEmpresasFiltrarCruzamento() throws DataException {
        tbEmpresasCruzaLeadZap.close();
        tbEmpresasCruzaLeadZap.clearFilters();
        tbEmpresasCruzaLeadZap.clearParams();
        tbEmpresasCruzaLeadZap.addFilter("NOT_CRUZAMENTO_WHATSAPP;STATUS;COD_MATRIZ");
        tbEmpresasCruzaLeadZap.addParam("STATUS", "S");
        tbEmpresasCruzaLeadZap.setOrderBy("NOME");
        tbEmpresasCruzaLeadZap.open();
    }

    public void filtrarLog(Integer idCelular) throws DataException {
        tbWhatsappLog.close();
        tbWhatsappLog.clearFilters();
        tbWhatsappLog.clearParams();
        tbWhatsappLog.addFilter("ID_CELULAR");
        tbWhatsappLog.addParam("ID_CELULAR", idCelular);
        tbWhatsappLog.setOrderBy("A.DATA_OCORRENCIA DESC");
        tbWhatsappLog.open();
    }

    public void carregarComboUf() throws DataException {
        tbUf.close();
        tbUf.clearFilters();
        tbUf.clearParams();
        tbUf.setOrderBy("UF");
        tbUf.open();
    }

    public void carregarGridCidades(String uf, String cidades, int idcelular) throws DataException {
        tbCidades.close();
        tbCidades.clearFilters();
        tbCidades.clearParams();
        tbCidades.addFilter("UF;NAO_EXISTE_CAD_WHATS_CID");
        tbCidades.addParam("UF", uf);
        tbCidades.addParam("ID_CELULAR", idcelular);
        if (!cidades.equals("")) {
            tbCidades.addFilter("DESCRICAO");
            tbCidades.addParam("DESCRICAO", cidades);
        }
        tbCidades.open();
    }

    public void carregaCidadesCruzadas(int idCelular) throws DataException {
        tbCadastroWhatsappCidades.close();
        tbCadastroWhatsappCidades.clearFilters();
        tbCadastroWhatsappCidades.clearParams();
        tbCadastroWhatsappCidades.addFilter("ID_CELULAR");
        tbCadastroWhatsappCidades.addParam("ID_CELULAR", idCelular);
        tbCadastroWhatsappCidades.open();
    }

    public void addCidadesCruzadas(int idCelular,
                                   String uf,
                                   int codCidades) throws DataException {
        tbCadastroWhatsappCidades.append();
        tbCadastroWhatsappCidades.setID_WHATS_CIDADE(SequenceUtil.nextVal("SEQ_CRM_CADASTRO_WHATSAPP_CIDA"));
        tbCadastroWhatsappCidades.setID_CELULAR(idCelular);
        tbCadastroWhatsappCidades.setUF(uf);
        tbCadastroWhatsappCidades.setCOD_CIDADES(codCidades);
        tbCadastroWhatsappCidades.setDESCRICAO(tbCidades.getDESCRICAO().asString());
        tbCadastroWhatsappCidades.post();
        tbCidades.delete();
    }

    ;

    public void delCidadesCruzadas() throws DataException {
        tbCadastroWhatsappCidades.delete();
    }

    public void carregarLeadzapMenu() throws DataException {
        tbLeadzapMenu.close();
        tbLeadzapMenu.clearFilters();
        tbLeadzapMenu.clearParams();
        tbLeadzapMenu.open();
    }
}
