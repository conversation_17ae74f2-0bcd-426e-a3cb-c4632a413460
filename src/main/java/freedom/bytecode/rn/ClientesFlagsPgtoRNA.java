package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ClientesFlagsPgtoRNW;
import freedom.data.DataException;
import freedom.util.CondicaoDePagamento;
import lombok.Getter;
import lombok.Setter;

public class ClientesFlagsPgtoRNA extends ClientesFlagsPgtoRNW  {

    private static final long serialVersionUID = 20130827081850L;

    @Setter
    private double codEmpresaFiltrada;

    @Setter
    private double codClienteFiltrado;

    @Setter
    private double codDepartamentoFiltrado;

    @Setter
    private double codFormaDePagamentoFiltrada;

    @Setter
    private String codTipoDaFormaDePagamentoFiltrada;

    @Setter
    private double codCondicaoDePagamentoFiltrada;

    @Setter
    private String formaDePagamentoExclusivaFiltrada;

    @Setter
    private String condicaoDePagamentoExclusivaFiltrada;

    private int numeroDeRegistrosSelecionadosNaGrade;

    @Getter
    private CondicaoDePagamento[] vetorDeRegistrosSelecionadasNaGrade;

    public ClientesFlagsPgtoRNA() {
        this.codEmpresaFiltrada = 0.0;
        this.codClienteFiltrado = 0.0;
        this.codDepartamentoFiltrado = 0.0;
        this.codFormaDePagamentoFiltrada = 0.0;
        this.codTipoDaFormaDePagamentoFiltrada = "";
        this.codCondicaoDePagamentoFiltrada = 0.0;
        this.formaDePagamentoExclusivaFiltrada = "";
        this.condicaoDePagamentoExclusivaFiltrada = "";
        this.numeroDeRegistrosSelecionadosNaGrade = 0;
    }

    public void carregarEmpresasDoFiltro() throws DataException {
        if (this.tbEmpresasFiliaisSel.isActive()) {
            this.tbEmpresasFiliaisSel.close();
        }
        this.tbEmpresasFiliaisSel.clearFilters();
        this.tbEmpresasFiliaisSel.clearParams();
        this.tbEmpresasFiliaisSel.open();
    }

    public void carregarDepartamentosDoFiltro() throws DataException {
        if (this.tbListaEmpresasDepartamentos.isActive()) {
            this.tbListaEmpresasDepartamentos.close();
        }
        if (this.codEmpresaFiltrada > 0.0) {
            this.tbListaEmpresasDepartamentos.clearFilters();
            this.tbListaEmpresasDepartamentos.clearParams();
            this.tbListaEmpresasDepartamentos.setFilterEMP_CODIGO_EQUALS(this.codEmpresaFiltrada);
            this.tbListaEmpresasDepartamentos.setOrderBy("EMPRESAS_DEPARTAMENTOS.DESCRICAO");
            this.tbListaEmpresasDepartamentos.open();
        }
    }

    public void carregarFormasDePagamentosDoFiltro() throws DataException {
        if (this.tbListaFormasPgto.isActive()) {
            this.tbListaFormasPgto.close();
        }
        if ((this.codEmpresaFiltrada > 0.0)
                && (this.codDepartamentoFiltrado > 0.0)) {
            this.tbListaFormasPgto.clearFilters();
            this.tbListaFormasPgto.clearParams();
            this.tbListaFormasPgto.setFilterEMP_CODIGO(this.codEmpresaFiltrada);
            this.tbListaFormasPgto.setFilterDPTO_CODIGO(this.codDepartamentoFiltrado);
            if (!this.codTipoDaFormaDePagamentoFiltrada.isEmpty()) {
                this.tbListaFormasPgto.setFilterFORMA_TIPO_CODIGO(this.codTipoDaFormaDePagamentoFiltrada);
            }
            if (!this.formaDePagamentoExclusivaFiltrada.isEmpty()) {
                this.tbListaFormasPgto.setFilterEH_EXCLUSIVA_CLIENTE(this.formaDePagamentoExclusivaFiltrada);
            }
            this.tbListaFormasPgto.setFilterATIVA("S");
            this.tbListaFormasPgto.addFilter("OMITIR_CORTESIA_E_FINANCIAMENTO");
            this.tbListaFormasPgto.addFilter("TEM_CONDICAO_DE_PAGAMENTO");
            this.tbListaFormasPgto.setOrderBy("FORMA_PGTO.DESCRICAO");
            this.tbListaFormasPgto.open();
        }
    }

    public void carregarTiposDasFormasDePagamentosDoFiltro() throws DataException {
        if (this.tbListaTipoPgtoNbs.isActive()) {
            this.tbListaTipoPgtoNbs.close();
        }
        this.tbListaTipoPgtoNbs.clearFilters();
        this.tbListaTipoPgtoNbs.clearParams();
        this.tbListaTipoPgtoNbs.addFilter("OMITIR_CORTESIA_E_FINANCIAMENTO");
        this.tbListaTipoPgtoNbs.setOrderBy("DESC_COD_TIPO_PGTO_NBS");
        this.tbListaTipoPgtoNbs.open();
    }

    public void carregarCondicoesDePagamentosDoFiltro() throws DataException {
        if (this.tbClienteFormaPgtoDisp1.isActive()) {
            this.tbClienteFormaPgtoDisp1.close();
        }
        if ((this.codEmpresaFiltrada > 0.0)
                && (this.codDepartamentoFiltrado > 0.0)
                && (this.codFormaDePagamentoFiltrada > 0.0)) {
            this.tbClienteFormaPgtoDisp1.clearFilters();
            this.tbClienteFormaPgtoDisp1.clearParams();
            this.tbClienteFormaPgtoDisp1.setFilterCOD_CLIENTE_FILTRADO(this.codClienteFiltrado);
            this.tbClienteFormaPgtoDisp1.setFilterCOD_EMPRESA(this.codEmpresaFiltrada);
            this.tbClienteFormaPgtoDisp1.setFilterCOD_EMPRESA_DEPARTAMENTO(this.codDepartamentoFiltrado);
            this.tbClienteFormaPgtoDisp1.setFilterCOD_FORMA_PGTO(this.codFormaDePagamentoFiltrada);
            if (!this.codTipoDaFormaDePagamentoFiltrada.isEmpty()) {
                this.tbClienteFormaPgtoDisp1.setFilterCOD_TIPO_FORMA_PGTO(this.codTipoDaFormaDePagamentoFiltrada);
            }
            if (!this.formaDePagamentoExclusivaFiltrada.isEmpty()) {
                this.tbClienteFormaPgtoDisp1.setFilterFORMA_PGTO_EXCLUSIVA(this.formaDePagamentoExclusivaFiltrada);
            }
            if (!this.condicaoDePagamentoExclusivaFiltrada.isEmpty()) {
                this.tbClienteFormaPgtoDisp1.setFilterCONDICAO_PAGAMENTO_EXCLUSIVA(this.condicaoDePagamentoExclusivaFiltrada);
            }
            this.tbClienteFormaPgtoDisp1.open();
        }
    }

    public void carregarGradeDeCondicoesDePagamento() throws DataException {
        if (this.tbClienteFormaPgtoDisp.isActive()) {
            this.tbClienteFormaPgtoDisp.close();
        }
        this.tbClienteFormaPgtoDisp.clearFilters();
        this.tbClienteFormaPgtoDisp.clearParams();
        this.tbClienteFormaPgtoDisp.setFilterCOD_CLIENTE_FILTRADO(this.codClienteFiltrado);
        if (this.codEmpresaFiltrada > 0.0) {
            this.tbClienteFormaPgtoDisp.setFilterCOD_EMPRESA(this.codEmpresaFiltrada);
        }
        if (this.codDepartamentoFiltrado > 0.0) {
            this.tbClienteFormaPgtoDisp.setFilterCOD_EMPRESA_DEPARTAMENTO(this.codDepartamentoFiltrado);
        }
        if (this.codFormaDePagamentoFiltrada > 0.0) {
            this.tbClienteFormaPgtoDisp.setFilterCOD_FORMA_PGTO(this.codFormaDePagamentoFiltrada);
        }
        if (!this.codTipoDaFormaDePagamentoFiltrada.isEmpty()) {
            this.tbClienteFormaPgtoDisp.setFilterCOD_TIPO_FORMA_PGTO(this.codTipoDaFormaDePagamentoFiltrada);
        }
        if (this.codCondicaoDePagamentoFiltrada > 0.0) {
            this.tbClienteFormaPgtoDisp.setFilterCOD_CONDICAO_PAGAMENTO(this.codCondicaoDePagamentoFiltrada);
        }
        if (!this.formaDePagamentoExclusivaFiltrada.isEmpty()) {
            this.tbClienteFormaPgtoDisp.setFilterFORMA_PGTO_EXCLUSIVA(this.formaDePagamentoExclusivaFiltrada);
        }
        if (!this.condicaoDePagamentoExclusivaFiltrada.isEmpty()) {
            this.tbClienteFormaPgtoDisp.setFilterCONDICAO_PAGAMENTO_EXCLUSIVA(this.condicaoDePagamentoExclusivaFiltrada);
        }
        this.tbClienteFormaPgtoDisp.open();
    }

    public void salvar() throws DataException {
        this.numeroDeRegistrosSelecionadosNaGrade = 0;
        this.tbClienteFormaPgtoDisp.first();
        while (Boolean.FALSE.equals(this.tbClienteFormaPgtoDisp.eof())) {
            if (this.tbClienteFormaPgtoDisp.getSEL().asString().equals("S")) {
                this.numeroDeRegistrosSelecionadosNaGrade += 1;
            }
            this.tbClienteFormaPgtoDisp.next();
        }
        if (this.numeroDeRegistrosSelecionadosNaGrade > 0) {
            this.vetorDeRegistrosSelecionadasNaGrade = new CondicaoDePagamento[this.numeroDeRegistrosSelecionadosNaGrade];
            this.tbClienteFormaPgtoDisp.first();
            for (int contadorDoLoop = 0; contadorDoLoop < this.numeroDeRegistrosSelecionadosNaGrade; contadorDoLoop++) {
                this.vetorDeRegistrosSelecionadasNaGrade[contadorDoLoop] = new CondicaoDePagamento(this.tbClienteFormaPgtoDisp.getCOD_EMPRESA().asLong(),
                        this.tbClienteFormaPgtoDisp.getCOD_EMPRESA_DEPARTAMENTO().asLong(),
                        this.tbClienteFormaPgtoDisp.getCOD_FORMA_PGTO().asLong(),
                        this.tbClienteFormaPgtoDisp.getCOD_CONDICAO_PAGAMENTO().asLong());
                    this.tbClienteFormaPgtoDisp.next();
            }
        }
    }

}
