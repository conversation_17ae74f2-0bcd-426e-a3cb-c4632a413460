package freedom.bytecode.rn;

import freedom.bytecode.cursor.CLIENTE_DIVERSO;
import freedom.bytecode.rn.wizard.BtoBEnviarLinkRNW;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.PackageResult;
import freedom.util.RandomAlphaNumeric;
import freedom.util.pkg.PkgCrmPartsBtobRNA;
import freedom.util.pkg.PkgCrmPartsRNA;

public class BtoBEnviarLinkRNA extends BtoBEnviarLinkRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkCrmPartsRnA;

    public String email = "";

    public long codEvento = 0;

    public long idLink = 0;

    public int codEmpresa = 0;

    public long codClienteEvento = 0;

    private String senha = "";

    public BtoBEnviarLinkRNA() {
        pkCrmPartsRnA = new PkgCrmPartsRNA();
    }

    public void getFilaDaVez(String nome) throws DataException {
        if (nome == null || nome.trim().isEmpty()) {
            return;
        }
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.addFilter("NOME");
        tbEmpresasUsuarios.addParam("NOME", nome);
        tbEmpresasUsuarios.open();

        setVendedorResponsavel(tbEmpresasUsuarios.getNOME().asString());

    }

    public void getClientesEmail(String email) throws DataException {
        tbBtobClientesPequisaEmail.close();
        tbBtobClientesPequisaEmail.clearParams();
        tbBtobClientesPequisaEmail.clearFilters();
        tbBtobClientesPequisaEmail.addFilter("ENDERECO_ELETRONICO");
        tbBtobClientesPequisaEmail.addParam("ENDERECO_ELETRONICO", email);
        tbBtobClientesPequisaEmail.open();

    }

    private void setVendedorResponsavel(String vendedor) throws DataException {
        CLIENTE_DIVERSO cliDiv = new CLIENTE_DIVERSO("cliDiv");
        cliDiv.setFilterCOD_CLIENTE(this.codClienteEvento);
        cliDiv.open();
        cliDiv.edit();
        cliDiv.setVENDEDOR_RESPONSAVEL(vendedor);
        cliDiv.post();
        cliDiv.applyUpdates();
    }

    public void getFilaDaVez(long codEmpresa) throws DataException {
        if (codEmpresa == 0) {
            return;
        }
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.addFilter("COD_EMPRESA");
        tbEmpresasUsuarios.addParam("COD_EMPRESA", codEmpresa);
        tbEmpresasUsuarios.setOrderBy("FILA_VEZ_CRMPARTS desc");
        tbEmpresasUsuarios.open();

        setVendedorResponsavel(tbEmpresasUsuarios.getNOME().asString());
    }

    public void filtrarEmpresasCliente(long codCliente) throws DataException {
        if (codCliente == 0) {
            return;
        }
        this.tbEmpresasClienteEnxerga.close();
        this.tbEmpresasClienteEnxerga.clearParams();
        this.tbEmpresasClienteEnxerga.clearFilters();
        this.tbEmpresasClienteEnxerga.setFilterCOD_CLIENTE(codCliente);
        this.tbEmpresasClienteEnxerga.setOrderBy("y.COD_EMPRESA");
        this.tbEmpresasClienteEnxerga.open();
    }

    public void filtrarCliente(long codCliente) throws DataException {
        if (codCliente == 0) {
            return;
        }
        this.tbClienteDiverso.close();
        this.tbClienteDiverso.clearParams();
        this.tbClienteDiverso.clearFilters();
        this.tbClienteDiverso.setFilterCOD_CLIENTE(codCliente);
        this.tbClienteDiverso.open();
    }

    public void filtrarEnderecosCadastrados(long codCliente) throws DataException {
        if (codCliente == 0) {
            return;
        }
        this.tbBtobEnderecosCadastrados.close();
        this.tbBtobEnderecosCadastrados.clearParams();
        this.tbBtobEnderecosCadastrados.clearFilters();
        this.tbBtobEnderecosCadastrados.setFilterCOD_CLIENTE(codCliente);
        this.tbBtobEnderecosCadastrados.open();
    }

    public void filtrarEmpresasClienteEnxergaSelecionado(long codCliente) throws DataException {
        if (codCliente == 0) {
            return;
        }
        this.tbEmpresasClientesEnxerga.close();
        this.tbEmpresasClientesEnxerga.clearParams();
        this.tbEmpresasClientesEnxerga.clearFilters();
        this.tbEmpresasClientesEnxerga.setFilterCOD_CLIENTE(codCliente);
        this.tbEmpresasClientesEnxerga.open();
    }

    public void filtrarEnderecoEntrega(long codCliente) throws DataException {
        if (codCliente == 0) {
            return;
        }
        this.tbBtobEnderecoClienteEntrega.close();
        this.tbBtobEnderecoClienteEntrega.clearParams();
        this.tbBtobEnderecoClienteEntrega.clearFilters();
        this.tbBtobEnderecoClienteEntrega.setFilterCOD_CLIENTE(codCliente);
        this.tbBtobEnderecoClienteEntrega.open();
    }

    public void filtrarEnderecoFatura(long codCliente) throws DataException {
        if (codCliente == 0) {
            return;
        }
        this.tbBtobEnderecoClienteFatura.close();
        this.tbBtobEnderecoClienteFatura.clearParams();
        this.tbBtobEnderecoClienteFatura.clearFilters();
        this.tbBtobEnderecoClienteFatura.setFilterCOD_CLIENTE(codCliente);
        this.tbBtobEnderecoClienteFatura.open();
    }

    public void enviarEmail() throws DataException {
        PerfilRNA perfilRNA = new PerfilRNA();
        perfilRNA.filtrarUsuarioPerfil(EmpresaUtil.getUserLogged());
        this.tbBtobLink.close();
        this.tbBtobLink.setFilterID_LINK(1);
        this.tbBtobLink.open();
        String body = "<h2>Segue o seu acesso ao sistema da sua concessionária " + perfilRNA.tbPerfil.getNOME_EMPRESA().asString() + "</h2>\n"
                + "<p>Para acessar clique <a href=\"" + this.tbBtobLink.getLINK().asString() + "\">aqui</a>.</p>\n"
                + "<p>Seu Usuário: <strong>" + this.email.toLowerCase() + "</strong></p>"
                + "<p>Sua Senha: <strong>" + this.senha + "</strong></p>";
        PackageResult.testarRetorno(this.pkCrmPartsRnA.enviarEmailSenha(
                (double) this.codEmpresa,
                (double) this.codEvento,
                EmpresaUtil.getUserLogged(),
                this.email,
                "Link acesso ao sistema CRM Parts",
                body));
    }

    public Value salvar(int tpEntrega) throws Exception {
        Value retorno = null;
        senha = RandomAlphaNumeric.randomString(8);
        PkgCrmPartsBtobRNA pk = new PkgCrmPartsBtobRNA();
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            retorno = pk.incluircadastro(session, 1,
                    tbBtobEnderecoClienteEntrega.getCOD_CLIENTE().asLong(),
                    senha,
                    email,
                    tbBtobEnderecoClienteFatura.getCOD_TIPO_ENDERECO().asInteger(),
                    tbBtobEnderecoClienteFatura.getINSCRICAO_ESTADUAL().asString(),
                    tbBtobEnderecoClienteEntrega.getCOD_TIPO_ENDERECO().asInteger(),
                    tbBtobEnderecoClienteEntrega.getINSCRICAO_ESTADUAL().asString(),
                    tpEntrega);

            try {
                idLink = Long.parseLong(retorno.asString());
                if (idLink > 0) {
                    tbEmpresasClienteEnxerga.first();
                    retorno = new Value(pk.limparempresas(session, Long.parseLong(retorno.asString())));
                    if (retorno.asString().equals("S")) {
                        while (Boolean.FALSE.equals(tbEmpresasClienteEnxerga.eof())) {
                            if (tbEmpresasClienteEnxerga.getCHECKED().asString().equals("S")) {
                                retorno = new Value(pk.incluirempresas(session,
                                        idLink,
                                        tbEmpresasClienteEnxerga.getCOD_EMPRESA().asInteger(),
                                        tbEmpresasClienteEnxerga.getPRINCIPAL().asString()));
                                if (retorno.asString().equals("S")) {
                                    tbEmpresasClienteEnxerga.next();
                                } else {
                                    session.rollback();
                                    return retorno;
                                }
                            } else {
                                tbEmpresasClienteEnxerga.next();
                            }
                        }
                    } else {
                        session.rollback();
                        return retorno;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                session.rollback();
                return retorno;
            }
            session.commit();
        } catch (DataException e) {
            e.printStackTrace();
            session.rollback();
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
            return retorno;
        }
    }

}
