package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ConsultaSimplesEventoAbertoRNW;
import freedom.data.DataException;
import static freedom.util.CastUtil.asLong;

public class ConsultaSimplesEventoAbertoRNA extends ConsultaSimplesEventoAbertoRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void filtrarEvento(int tipoFiltro,
                              String filtrarPor,
                              long codEmpresa,
                              String usuarioRespEvent) throws DataException {
        this.tbCrmpartsConsultaEvento.close();
        this.tbCrmpartsConsultaEvento.clearFilters();
        this.tbCrmpartsConsultaEvento.clearParams();
        this.tbCrmpartsConsultaEvento.setFilterCOD_EMPRESA(codEmpresa);
        if (!usuarioRespEvent.isEmpty()) {
            this.tbCrmpartsConsultaEvento.setFilterRESPONSAVEL_PELO_EVENTO(usuarioRespEvent);
        }
        this.tbCrmpartsConsultaEvento.setFilterSTATUS("P");
        if (tipoFiltro == 1) {
            //Fone
            this.tbCrmpartsConsultaEvento.setFilterFONE_CLIENTE_AVULSO(filtrarPor.trim());
        } else if (tipoFiltro == 2) {
            //e-mail
            this.tbCrmpartsConsultaEvento.setFilterEMAIL_CLIENTE_AVULSO(filtrarPor.trim());
        } else if ((tipoFiltro == 3)
                && (!filtrarPor.trim().isEmpty())) {
                tbCrmpartsConsultaEvento.setFilterCOD_CLIENTE(asLong(filtrarPor));
        }
        this.tbCrmpartsConsultaEvento.open();
    }

}
