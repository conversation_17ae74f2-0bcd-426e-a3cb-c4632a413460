package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ClientesFlagsServAcessoRNW;
import freedom.data.DataException;

public class ClientesFlagsServAcessoRNA extends ClientesFlagsServAcessoRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void carregaDados() throws DataException {
        //Grupo
        tbClienteFlagGrupo.close();
        tbClienteFlagGrupo.clearFilters();
        tbClienteFlagGrupo.clearParams();
        tbClienteFlagGrupo.open();

        //Funcao
        tbEmpresasFuncoes.close();
        tbEmpresasFuncoes.clearFilters();
        tbEmpresasFuncoes.clearParams();
        tbEmpresasFuncoes.open();
    }

    public void carregaComboUsuario(int codFuncao) throws DataException {
        //Nome Usuarios
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.addFilter("COD_FUNCAO");
        tbEmpresasUsuarios.addParam("COD_FUNCAO", codFuncao);
        tbEmpresasUsuarios.open();
    }

    public void carregaGrid(int codGrupo, int codFuncao, String codNome) throws DataException {

        //Grid ClienteFlagsAcesso
        tbClienteFlagAcesso.close();
        tbClienteFlagAcesso.clearFilters();
        tbClienteFlagAcesso.clearParams();
        if (codGrupo > 0) {
            tbClienteFlagAcesso.addFilter("ID_GRUPO");
            tbClienteFlagAcesso.addParam("ID_GRUPO", codGrupo);
        }
        if (codFuncao > 0) {
            tbClienteFlagAcesso.addFilter("COD_FUNCAO");
            tbClienteFlagAcesso.addParam("COD_FUNCAO", codFuncao);
        }
        if (!codNome.isEmpty()) {
            tbClienteFlagAcesso.addFilter("NOME");
            tbClienteFlagAcesso.addParam("NOME", codNome);
        }
        tbClienteFlagAcesso.open();
    }
}
