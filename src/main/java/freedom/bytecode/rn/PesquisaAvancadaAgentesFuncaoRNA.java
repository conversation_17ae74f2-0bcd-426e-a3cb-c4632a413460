package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.PesquisaAvancadaAgentesFuncaoRNW;
import freedom.data.DataException;

public class PesquisaAvancadaAgentesFuncaoRNA extends PesquisaAvancadaAgentesFuncaoRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarCboEmpresa() throws DataException {
        this.tbEmpresas.close();
        this.tbEmpresas.clearFilters();
        this.tbEmpresas.clearParams();
        this.tbEmpresas.setOrderBy("A.NOME");
        this.tbEmpresas.open();
    }

    public void carregarCboDepartamento(long codEmpresa) throws DataException {
        this.tbDepartamentos.close();
        this.tbDepartamentos.clearFilters();
        this.tbDepartamentos.clearParams();
        this.tbDepartamentos.setFilterEMP_CODIGO_EQUALS(codEmpresa);
        this.tbDepartamentos.open();
    }

    public void carregarCboDivisao(long codEmpresa,
                                   int codDepartamento) throws DataException {
        this.tbDivisoes.close();
        this.tbDivisoes.clearFilters();
        this.tbDivisoes.clearParams();
        this.tbDivisoes.setFilterCOD_EMPRESA_EQUALS(codEmpresa);
        this.tbDivisoes.setFilterCOD_EMPRESA_DEPARTAMENTO_EQUALS(codDepartamento);
        this.tbDivisoes.open();
    }

}