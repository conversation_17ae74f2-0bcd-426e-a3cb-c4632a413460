package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.HistoricoFichaClienteRNW;
public class HistoricoFichaClienteRNA extends HistoricoFichaClienteRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public void carregarDadosEvento(Double codCliente) throws DataException {
        tbFichaClienteEventos.close();
        tbFichaClienteEventos.clearFilters();
        tbFichaClienteEventos.clearParams();
        tbFichaClienteEventos.setFilterCOD_CLIENTE(codCliente);
        tbFichaClienteEventos.open();
    }

    public boolean isCarregadoDadosEvento(){
        return tbFichaClienteEventos.isActive();
    }
    public void carregarDadosVeiculos(Double codCliente) throws DataException {
        tbFichaClienteVeiculos.close();
        tbFichaClienteVeiculos.clearFilters();
        tbFichaClienteVeiculos.clearParams();
        tbFichaClienteVeiculos.setFilterCOD_CLIENTE(codCliente);
        tbFichaClienteVeiculos.open();
    }

    public boolean isCarregadoDadosVeiculos(){
        return tbFichaClienteVeiculos.isActive();
    }

    public void carregarDadosOsVeiculosCombo(Double codCliente) throws DataException {
        tbFichaClienteOsVeiculosCombo.close();
        tbFichaClienteOsVeiculosCombo.clearFilters();
        tbFichaClienteOsVeiculosCombo.clearParams();
        tbFichaClienteOsVeiculosCombo.setFilterCOD_CLIENTE(codCliente);
        tbFichaClienteOsVeiculosCombo.open();
    }
    public boolean isCarregadoDadosOsVeiculosCombo(){
        return tbFichaClienteOsVeiculosCombo.isActive();
    }

    public void carregarDadosOsOrc(Double codCliente, Double codModelo, Double codProduto, String chassi) throws DataException {
        tbFichaClienteOsOrc.close();
        tbFichaClienteOsOrc.clearFilters();
        tbFichaClienteOsOrc.clearParams();
        tbFichaClienteOsOrc.setFilterCOD_CLIENTE(codCliente);
        if (codModelo != null){
            tbFichaClienteOsOrc.setFilterCOD_MODELO(codModelo);
        }
        if (codProduto != null){
            tbFichaClienteOsOrc.setFilterCOD_PRODUTO(codProduto);
        }
        if (chassi != null){
            tbFichaClienteOsOrc.setFilterCHASSI(chassi);
        }
        tbFichaClienteOsOrc.open();
    }

    public boolean isCarregadoDadosOsOrc(){
        return tbFichaClienteOsVeiculosCombo.isActive();
    }

    public String getChassi(){
        return tbFichaClienteOsVeiculosCombo.getCHASSI().asString();
    }

    public double getModelo(){
        return tbFichaClienteOsVeiculosCombo.getCOD_MODELO().asDecimal();
    }

    public double getProduto(){
        return tbFichaClienteOsVeiculosCombo.getCOD_PRODUTO().asDecimal();
    }

    public void carregarVeiculosTabServicosCombo2(Double codCliente) throws DataException {
        tbFichaClienteOsVeiculosCombo2.close();
        tbFichaClienteOsVeiculosCombo2.clearFilters();
        tbFichaClienteOsVeiculosCombo2.clearParams();
        tbFichaClienteOsVeiculosCombo2.setFilterCOD_CLIENTE(codCliente);
        tbFichaClienteOsVeiculosCombo2.open();
    }
    public boolean isCarregadoVeiculosTabServicosCombo2(){
        return tbFichaClienteOsVeiculosCombo2.isActive();
    }

    public String getChassiCombo2(){
        return tbFichaClienteOsVeiculosCombo2.getCHASSI().asString();
    }

    public double getModeloCombo2(){
        return tbFichaClienteOsVeiculosCombo2.getCOD_MODELO().asDecimal();
    }

    public double getProdutoCombo2(){
        return tbFichaClienteOsVeiculosCombo2.getCOD_PRODUTO().asDecimal();
    }

    public void carregarDadosOsOrcCombo(Double codCliente, Double codModelo, Double codProduto, String chassi) throws DataException {
        tbFichaClienteOsOrcCombo.close();
        tbFichaClienteOsOrcCombo.clearFilters();
        tbFichaClienteOsOrcCombo.clearParams();
        tbFichaClienteOsOrcCombo.setFilterCOD_CLIENTE(codCliente);
        if (codModelo != null){
            tbFichaClienteOsOrcCombo.setFilterCOD_MODELO(codModelo);
        }
        if (codProduto != null){
            tbFichaClienteOsOrcCombo.setFilterCOD_PRODUTO(codProduto);
        }
        if (chassi != null){
            tbFichaClienteOsOrcCombo.setFilterCHASSI(chassi);
        }
        tbFichaClienteOsOrcCombo.open();
    }
    public boolean isCarregadoDadosOsOrcCombo(){
        return tbFichaClienteOsOrcCombo.isActive();
    }

    public double getNumeroOsCombo(){
        return tbFichaClienteOsOrcCombo.getNUMERO_OS().asDecimal();
    }

    public double getCodEmpresaCombo(){
        return tbFichaClienteOsOrcCombo.getCOD_EMPRESA().asDecimal();
    }

    public void carregarDadosServico(Double codCliente, Double codModelo, Double codProduto, String chassi, Double numeroOs, Double codEmpresa) throws DataException {
        tbFichaClienteServicos.close();
        tbFichaClienteServicos.clearFilters();
        tbFichaClienteServicos.clearParams();
        tbFichaClienteServicos.setFilterCOD_CLIENTE(codCliente);
        if (codModelo != null){
            tbFichaClienteServicos.setFilterCOD_MODELO(codModelo);
        }
        if (codProduto != null){
            tbFichaClienteServicos.setFilterCOD_PRODUTO(codProduto);
        }
        if (chassi != null){
            tbFichaClienteServicos.setFilterCHASSI(chassi);
        }
        if (numeroOs != null){
            tbFichaClienteServicos.setFilterNUMERO_OS(numeroOs);
        }
        if (codEmpresa != null){
            tbFichaClienteServicos.setFilterCOD_EMPRESA(codEmpresa);
        }
        tbFichaClienteServicos.open();
    }

    public void carregarDadosPecas(Double codCliente, Double codModelo, Double codProduto, String chassi, Double numeroOs, Double codEmpresa) throws DataException {
        tbFichaClientePecas.close();
        tbFichaClientePecas.clearFilters();
        tbFichaClientePecas.clearParams();
        tbFichaClientePecas.setFilterCOD_CLIENTE(codCliente);
        if (codModelo != null){
            tbFichaClientePecas.setFilterCOD_MODELO(codModelo);
        }
        if (codProduto != null){
            tbFichaClientePecas.setFilterCOD_PRODUTO(codProduto);
        }
        if (chassi != null){
            tbFichaClientePecas.setFilterCHASSI(chassi);
        }
        if (numeroOs != null){
            tbFichaClientePecas.setFilterNUMERO_OS(numeroOs);
        }
        if (codEmpresa != null){
            tbFichaClientePecas.setFilterCOD_EMPRESA(codEmpresa);
        }
        tbFichaClientePecas.open();
    }

}
