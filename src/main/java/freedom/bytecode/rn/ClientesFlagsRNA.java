package freedom.bytecode.rn;

import freedom.bytecode.cursor.*;
import freedom.bytecode.rn.wizard.ClientesFlagsRNW;
import freedom.data.DataException;
import freedom.util.Constantes;
import freedom.util.FRLogger;
import freedom.util.StringUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

public class ClientesFlagsRNA extends ClientesFlagsRNW  {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkCrmPartsRNA = new PkgCrmPartsRNA();

    public void carregarCboFormaCob(
            String usuarioLogado
    ) throws DataException {
        this.tbFormaCobrancaFlag.close();
        this.tbFormaCobrancaFlag.addParam("USUARIO_LOGADO", usuarioLogado);
        this.tbFormaCobrancaFlag.open();
    }

    public void carregarCboVendedor() throws DataException {
        this.tbVendedorRespFlag.close();
        this.tbVendedorRespFlag.open();
    }

    public void carregarCboRepresentante() throws DataException {
        this.tbBuscaRepresentanteCliente.close();
        this.tbBuscaRepresentanteCliente.clearFilters();
        this.tbBuscaRepresentanteCliente.clearParams();
        this.tbBuscaRepresentanteCliente.setFilterNAO_DEMITIDO(" ");
        this.tbBuscaRepresentanteCliente.setOrderBy("NOME_LOGIN");
        this.tbBuscaRepresentanteCliente.open();
    }

    public void carregarLblNomeCliente(
            long codCliente
    ) throws DataException {
        this.tbClienteDiversoFlag.close();
        this.tbClienteDiversoFlag.clearFilters();
        this.tbClienteDiversoFlag.clearParams();
        this.tbClienteDiversoFlag.addParam(Constantes.COD_CLIENTE, codCliente);
        this.tbClienteDiversoFlag.open();
        this.tbClienteDiversoFlag.edit();
    }

    public void carregarGrdClienteFatLivre(
            long codCliente
    ) throws DataException {
        this.tbClientesParamEmpFlag.close();
        this.tbClientesParamEmpFlag.addParam(Constantes.COD_CLIENTE, codCliente);
        this.tbClientesParamEmpFlag.open();
        this.tbClientesParamEmpFlag.edit();
    }

    public void carregarGrdDescontoLetraEmpresa(
            long codCliente
    ) throws DataException {
        this.tbDescEmp.close();
        this.tbDescEmp.addParam(Constantes.COD_CLIENTE, codCliente);
        this.tbDescEmp.open();
    }

    public void carregarCboSegmento(
            long codCliente
    ) throws DataException {
        this.tbDadosJuridicosFlag.close();
        this.tbDadosJuridicosFlag.addParam(Constantes.COD_CLIENTE, codCliente);
        this.tbDadosJuridicosFlag.open();
    }

    public void carregarCboSegmentoLookupTable() throws DataException {
        this.tbClienteSegmento.close();
        this.tbClienteSegmento.open();
    }

    public void carregarGrdVendedoresAssociados(
            long codCliente
            ,String codigosDasEmpresasDoUsuario
    ) throws DataException{
        this.tbListaClienteResponsavel.close();
        this.tbListaClienteResponsavel.clearFilters();
        this.tbListaClienteResponsavel.clearParams();
        this.tbListaClienteResponsavel.setFilterCOD_CLIENTE_EQUALS(codCliente);
        this.tbListaClienteResponsavel.setFilterCOD_EMPRESA_CONTAINS(codigosDasEmpresasDoUsuario);
        this.tbListaClienteResponsavel.open();
    }

    public void carregarGrdCondPgto(
            long codCliente
    ) throws DataException {
        this.tbClienteFormaPgtoFlag.close();
        this.tbClienteFormaPgtoFlag.addParam(Constantes.COD_CLIENTE, codCliente);
        this.tbClienteFormaPgtoFlag.open();
    }

    public void carregarGrdFormasDePagamento(
            long codCliente
    ) throws DataException {
        this.tbListaClienteFormasPagamento.close();
        if (codCliente > 0L) {
            this.tbListaClienteFormasPagamento.setFilterCLIENTE_CODIGO(codCliente);
        }
        this.tbListaClienteFormasPagamento.open();
    }

    public void incluirClienteCondicaoDePagamento(
            long codEmpresa
            ,long codEmpresaDepartamento
            ,long codFormaPagamento
            ,long codCondicaoPagamento
            ,long codCliente
    ) throws DataException {
        this.tbClienteFormasPgto.close();
        this.tbClienteFormasPgto.clearFilters();
        this.tbClienteFormasPgto.clearParams();
        this.tbClienteFormasPgto.setFilterCOD_CLIENTE(
                codCliente
        );
        this.tbClienteFormasPgto.setFilterCOD_FORMA_PGTO(
                codFormaPagamento
        );
        this.tbClienteFormasPgto.setFilterCOD_CONDICAO_PAGAMENTO(
                codCondicaoPagamento
        );
        this.tbClienteFormasPgto.setFilterCOD_EMPRESA(
                codEmpresa
        );
        this.tbClienteFormasPgto.setFilterCOD_EMPRESA_DEPARTAMENTO(
                codEmpresaDepartamento
        );
        this.tbClienteFormasPgto.open();
        boolean tbClienteFormasPgtoEmpty = this.tbClienteFormasPgto.isEmpty();
        if (tbClienteFormasPgtoEmpty) {
            this.tbClienteFormasPgto.append();
            this.tbClienteFormasPgto.setCOD_CLIENTE(
                    codCliente
            );
            this.tbClienteFormasPgto.setCOD_EMPRESA(
                    codEmpresa
            );
            this.tbClienteFormasPgto.setCOD_EMPRESA_DEPARTAMENTO(
                    codEmpresaDepartamento
            );
            this.tbClienteFormasPgto.setCOD_FORMA_PGTO(
                    codFormaPagamento
            );
            this.tbClienteFormasPgto.setCOD_CONDICAO_PAGAMENTO(
                    codCondicaoPagamento
            );
            this.tbClienteFormasPgto.post();
            this.tbClienteFormasPgto.applyUpdates();
            this.tbClienteFormasPgto.commitUpdates();
        }
        this.tbClienteFormasPgto.close();
    }

    public void excluirClienteCondicaoDePagamento(
            long codEmpresa
            ,long codEmpresaDepartamento
            ,long codFormaPagamento
            ,long codCondicaoPagamento
            ,long codCliente
    ) throws DataException {
        this.tbClienteFormasPgto.close();
        this.tbClienteFormasPgto.clearFilters();
        this.tbClienteFormasPgto.clearParams();
        this.tbClienteFormasPgto.setFilterCOD_CLIENTE(
                codCliente
        );
        this.tbClienteFormasPgto.setFilterCOD_FORMA_PGTO(
                codFormaPagamento
        );
        this.tbClienteFormasPgto.setFilterCOD_CONDICAO_PAGAMENTO(
                codCondicaoPagamento
        );
        this.tbClienteFormasPgto.setFilterCOD_EMPRESA(
                codEmpresa
        );
        this.tbClienteFormasPgto.setFilterCOD_EMPRESA_DEPARTAMENTO(
                codEmpresaDepartamento
        );
        this.tbClienteFormasPgto.open();
        boolean tbClienteFormasPgtoNotEmpty = !this.tbClienteFormasPgto.isEmpty();
        if (tbClienteFormasPgtoNotEmpty) {
            this.tbClienteFormasPgto.delete();
            this.tbClienteFormasPgto.applyUpdates();
            this.tbClienteFormasPgto.commitUpdates();
        }
        this.tbClienteFormasPgto.close();
    }

    public void setFaturamentoLivre(
            long codCliente
    ) throws DataException {
        this.tbClientesParamEmpFlag.edit();
        String faturamentoLivre = this.tbClientesParamEmpFlag.getFATURAMENTO_LIVRE().asString();
        if (faturamentoLivre.equals("N")) {
            this.tbClientesParamEmpFlag.setFATURAMENTO_LIVRE("S");
        } else {
            this.tbClientesParamEmpFlag.setFATURAMENTO_LIVRE("N");
        }
        this.tbClientesParamEmpFlag.post();
        CLIENTES_PARAM_EMPRESAS tbCliParamEmpAux = new CLIENTES_PARAM_EMPRESAS("tbClientesParaEmp");
        tbCliParamEmpAux.setFilterCOD_CLIENTE(
                codCliente
        );
        long codEmpresa = this.tbClientesParamEmpFlag.getCOD_EMPRESA().asLong();
        tbCliParamEmpAux.setFilterCOD_EMPRESA(
                codEmpresa
        );
        tbCliParamEmpAux.open();
        boolean tbCliParamEmpAuxEmpty = tbCliParamEmpAux.isEmpty();
        if (tbCliParamEmpAuxEmpty) {
            tbCliParamEmpAux.append();
            tbCliParamEmpAux.setCOD_CLIENTE(
                    codCliente
            );
            tbCliParamEmpAux.setCOD_EMPRESA(
                    codEmpresa
            );
        } else {
            tbCliParamEmpAux.edit();
        }
        tbCliParamEmpAux.setFATURAMENTO_LIVRE(
                faturamentoLivre
        );
        tbCliParamEmpAux.post();
        tbCliParamEmpAux.applyUpdates();
    }

    public void setRepresentante(
            String representanteCliente
    ) throws DataException {
        if (representanteCliente.trim().isEmpty()) {
            representanteCliente = null;
        }
        this.tbClienteDiversoFlag.edit();
        this.tbClienteDiversoFlag.setREPRESENTANTE(representanteCliente);
        this.tbClienteDiversoFlag.post();
    }

    public void salvarClienteFlag() throws DataException {
        this.tbClienteDiversoFlag.post();
        this.tbClienteDiversoFlag.applyUpdates();
        this.tbClienteDiversoFlag.commitUpdates();
        this.tbClienteDiversoFlag.open();
        this.tbClienteDiversoFlag.edit();
    }

    public void incluirClienteFormasPagamento(
            long codFormaPgto
            ,long codEmpresa
            ,long codEmpresaDepartamento
            ,long codCliente
    ) throws DataException {
        String registroJaExiste = this.pesquisarClienteFormasPagamento(
                codFormaPgto
                ,codEmpresa
                ,codEmpresaDepartamento
                ,codCliente
        );
        if (registroJaExiste.equals("N")) {
            this.pkCrmPartsRNA.incClienteFormasPagamento(
                    (double) codCliente
                    ,(double) codFormaPgto
                    ,(double) codEmpresa
                    ,(double) codEmpresaDepartamento
            );
        }
    }

    public String pesquisarClienteFormasPagamento(
            long codFormaPgto
            ,long codEmpresa
            ,long codEmpresaDepartamento
            ,long codCliente
    ) throws DataException {
        String retFuncao;
        retFuncao = this.pkCrmPartsRNA.pesqClienteFormasPagamento(
                (double) codCliente
                ,(double) codFormaPgto
                ,(double) codEmpresa
                ,(double) codEmpresaDepartamento
        );
        return retFuncao;
    }

    public String excluirClienteFormasPagamento(
            long codFormaPgto
            ,long codEmpresa
            ,long codEmpresaDepartamento
            ,long codCliente
    ) throws DataException {
        String retFuncao;
        retFuncao = this.pkCrmPartsRNA.excClienteFormasPagamento(
                (double) codCliente
                ,(double) codFormaPgto
                ,(double) codEmpresa
                ,(double) codEmpresaDepartamento
        );
        return retFuncao;
    }

    public void carregarEmpresasParaAColunaDaGradeDescontoPorLetraEmpresa() throws DataException {
        this.tbEmpresasFiliaisSel.close();
        this.tbEmpresasFiliaisSel.clearFilters();
        this.tbEmpresasFiliaisSel.clearParams();
        this.tbEmpresasFiliaisSel.setOrderBy("NOMEECODIGODAEMPRESA");
        this.tbEmpresasFiliaisSel.open();
    }

    public void incluirOuAlterarLetraDeDescontoParaOCliente(
            String codigoDaLetraDeDesconto
            ,double descontoParaOCliente
            ,long codCliente
    ) throws DataException {
        FRLogger.log("codigoDaLetraDeDesconto '" + codigoDaLetraDeDesconto + "'", this.getClass());
        FRLogger.log("descontoParaOCliente '" + descontoParaOCliente + "'", this.getClass());
        FRLogger.log("this.codCliente '" + codCliente + "'", this.getClass());
        CLIENTES_DESCONTOS_FLAG tbClientesDescontosFlagIncluirAlterar = new CLIENTES_DESCONTOS_FLAG("tbClientesDescontosFlagIncluirAlterar");
        tbClientesDescontosFlagIncluirAlterar.close();
        tbClientesDescontosFlagIncluirAlterar.clearFilters();
        tbClientesDescontosFlagIncluirAlterar.clearParams();
        tbClientesDescontosFlagIncluirAlterar.setFilterCOD_CLIENTE(
                codCliente
        );
        tbClientesDescontosFlagIncluirAlterar.setFilterLETRA(
                codigoDaLetraDeDesconto
        );
        tbClientesDescontosFlagIncluirAlterar.open();
        boolean tbClientesDescontosFlagIncluirAlterarEmpty = tbClientesDescontosFlagIncluirAlterar.isEmpty();
        if (tbClientesDescontosFlagIncluirAlterarEmpty) {
            tbClientesDescontosFlagIncluirAlterar.append();
        } else {
            tbClientesDescontosFlagIncluirAlterar.edit();
        }
        tbClientesDescontosFlagIncluirAlterar.setCOD_CLIENTE(
                codCliente
        );
        tbClientesDescontosFlagIncluirAlterar.setLETRA(
                codigoDaLetraDeDesconto
        );
        tbClientesDescontosFlagIncluirAlterar.setPER_DESC(
                descontoParaOCliente
        );
        tbClientesDescontosFlagIncluirAlterar.post();
        tbClientesDescontosFlagIncluirAlterar.applyUpdates();
        tbClientesDescontosFlagIncluirAlterar.commitUpdates();
        tbClientesDescontosFlagIncluirAlterar.close();
    }

    public void incluirOuAlterarLetraDeDescontoParaOClientePorEmpresa(
            String codigoDaLetraDeDesconto
            ,double descontoParaOCliente
            ,long codigoDaEmpresa
            ,long codCliente
    ) throws DataException {
        this.tbDescEmp.close();
        this.tbDescEmp.clearFilters();
        this.tbDescEmp.clearParams();
        this.tbDescEmp.setFilterCOD_CLIENTE(
                codCliente
        );
        this.tbDescEmp.setFilterLETRA(
                codigoDaLetraDeDesconto
        );
        this.tbDescEmp.setFilterCOD_EMPRESA(
                codigoDaEmpresa
        );
        this.tbDescEmp.open();
        boolean tbDescEmpEmpty = this.tbDescEmp.isEmpty();
        if (tbDescEmpEmpty) {
            this.tbDescEmp.append();
        } else {
            this.tbDescEmp.edit();
        }
        this.tbDescEmp.setCOD_CLIENTE(
                codCliente
        );
        this.tbDescEmp.setLETRA(
                codigoDaLetraDeDesconto
        );
        this.tbDescEmp.setPER_DESC(
                descontoParaOCliente
        );
        this.tbDescEmp.setCOD_EMPRESA(
                codigoDaEmpresa
        );
        this.tbDescEmp.post();
        this.tbDescEmp.applyUpdates();
        this.tbDescEmp.commitUpdates();
        this.tbDescEmp.close();
    }

    public void carregarGrdDescontoLetra(
            long codCliente
    ) throws DataException {
        this.tbClientesDescontosFlag.close();
        this.tbClientesDescontosFlag.clearFilters();
        this.tbClientesDescontosFlag.clearParams();
        this.tbClientesDescontosFlag.addParam(Constantes.COD_CLIENTE, codCliente);
        this.tbClientesDescontosFlag.setOrderBy("CD.LETRA");
        this.tbClientesDescontosFlag.open();
    }

    public void carregarGradeEmpresaDescontoPorLetra(
            long codCliente
    ) throws DataException {
        this.tbDescEmp.close();
        this.tbDescEmp.clearFilters();
        this.tbDescEmp.clearParams();
        this.tbDescEmp.addParam(
                Constantes.COD_CLIENTE
                ,codCliente
        );
        this.tbDescEmp.setOrderBy(
                "CDE.COD_EMPRESA, CDE.LETRA"
        );
        this.tbDescEmp.open();
    }

    public void excluirDescontoPorLetra() throws DataException {
        this.tbClientesDescontosFlag.cancelUpdates();
        this.tbClientesDescontosFlag.delete();
        this.tbClientesDescontosFlag.applyUpdates();
        this.tbClientesDescontosFlag.commitUpdates();
        this.tbClientesDescontosFlag.setOrderBy("CD.LETRA");
        this.tbClientesDescontosFlag.open();
    }

    public void excluirDescontoPorLetraEmpresa() throws DataException {
        this.tbDescEmp.cancelUpdates();
        this.tbDescEmp.delete();
        this.tbDescEmp.applyUpdates();
        this.tbDescEmp.commitUpdates();
        this.tbDescEmp.setOrderBy("CDE.LETRA");
        this.tbDescEmp.open();
    }

    public void carregarGrdMargemMinimaMarkup(
            long codCliente
    ) throws DataException {
        this.tbClientesEspeciaisMargem.close();
        this.tbClientesEspeciaisMargem.clearFilters();
        this.tbClientesEspeciaisMargem.clearParams();
        this.tbClientesEspeciaisMargem.setFilterCOD_CLIENTE(
                codCliente
        );
        this.tbClientesEspeciaisMargem.open();
    }

    public void excluirMargemMinimaMarkup() throws DataException {
        this.tbClientesEspeciaisMargem.delete();
        this.tbClientesEspeciaisMargem.applyUpdates();
        this.tbClientesEspeciaisMargem.commitUpdates();
        this.tbClientesEspeciaisMargem.open();
    }

    public void incluirOuAlterarMargemMinimaMarkupParaOCliente(
            double margemMinimaMarkup
            ,long codCliente
    ) throws DataException {
        this.tbClientesEspeciaisMargem.close();
        this.tbClientesEspeciaisMargem.clearFilters();
        this.tbClientesEspeciaisMargem.clearParams();
        this.tbClientesEspeciaisMargem.setFilterCOD_CLIENTE(
                codCliente
        );
        this.tbClientesEspeciaisMargem.open();
        boolean tbClientesEspeciaisMargemEmpty = this.tbClientesEspeciaisMargem.isEmpty();
        if (tbClientesEspeciaisMargemEmpty) {
            this.tbClientesEspeciaisMargem.append();
        } else {
            this.tbClientesEspeciaisMargem.edit();
        }
        this.tbClientesEspeciaisMargem.setCOD_CLIENTE(
                codCliente
        );
        this.tbClientesEspeciaisMargem.setMARGEM_MINIMA(
                margemMinimaMarkup
        );
        this.tbClientesEspeciaisMargem.post();
        this.tbClientesEspeciaisMargem.applyUpdates();
        this.tbClientesEspeciaisMargem.commitUpdates();
        this.tbClientesEspeciaisMargem.close();
    }

    public void carregarGradeMargemMinimaMarkupEmpresa(
            long codCliente
    ) throws DataException {
        this.tbCliEspMargEmp.close();
        this.tbCliEspMargEmp.clearFilters();
        this.tbCliEspMargEmp.clearParams();
        this.tbCliEspMargEmp.setFilterCOD_CLIENTE(codCliente);
        this.tbCliEspMargEmp.setOrderBy("EMPRESAS.NOME");
        this.tbCliEspMargEmp.open();
    }

    public boolean excluirMargemMinimaMarkupEmpresa(long codCliente,
                                                    long codEmpresa) throws DataException {
        boolean retFuncao;
        BUSCA_CLI_ESP_MARG_EMP tbCliEspMargEmpExclusao = new BUSCA_CLI_ESP_MARG_EMP("tbCliEspMargEmpExclusao");
        tbCliEspMargEmpExclusao.close();
        tbCliEspMargEmpExclusao.clearFilters();
        tbCliEspMargEmpExclusao.clearParams();
        tbCliEspMargEmpExclusao.setFilterCOD_CLIENTE(codCliente);
        tbCliEspMargEmpExclusao.setFilterCOD_EMPRESA(codEmpresa);
        tbCliEspMargEmpExclusao.open();
        boolean tbCliEspMargEmpExclusaoEmpty = tbCliEspMargEmpExclusao.isEmpty();
        if (tbCliEspMargEmpExclusaoEmpty) {
            retFuncao = false;
        } else {
            tbCliEspMargEmpExclusao.delete();
            tbCliEspMargEmpExclusao.post();
            tbCliEspMargEmpExclusao.applyUpdates();
            tbCliEspMargEmpExclusao.commitUpdates();
            retFuncao = true;
        }
        tbCliEspMargEmpExclusao.close();
        return retFuncao;
    }

    public void selecionarRegistroGradeMargemMinimaMarkupEmpresa(long codCliente,
                                                                 long codEmpresa) throws DataException {
        this.tbCliEspMargEmp.first();
        while (Boolean.FALSE.equals(this.tbCliEspMargEmp.eof())) {
            long codClienteSelecionado = this.tbCliEspMargEmp.getCOD_CLIENTE().asLong();
            long codEmpresaSelecionada = this.tbCliEspMargEmp.getCOD_EMPRESA().asLong();
            if ((codClienteSelecionado == codCliente)
                    && (codEmpresaSelecionada == codEmpresa)) {
                break;
            }
            this.tbCliEspMargEmp.next();
        }
    }

    public void carregarGradeTransportadora(long codCliente) throws DataException {
        this.tbClienteTransportadoraFlag.close();
        this.tbClienteTransportadoraFlag.clearFilters();
        this.tbClienteTransportadoraFlag.clearParams();
        if (codCliente > 0L) {
            this.tbClienteTransportadoraFlag.setFilterCOD_CLIENTE(codCliente);
        }
        this.tbClienteTransportadoraFlag.setOrderBy("T.DESCRICAO");
        this.tbClienteTransportadoraFlag.setMaxRowCount(0);
        this.tbClienteTransportadoraFlag.open();
    }

    public void incluirTransportadoraCliente(long codTransportadora,
                                             long codCliente) throws DataException {
        CLIENTE_TRANSPORTADORA_FLAG tbClienteTransportadoraInclusao = new CLIENTE_TRANSPORTADORA_FLAG("tbClienteTransportadoraInclusao");
        tbClienteTransportadoraInclusao.close();
        tbClienteTransportadoraInclusao.clearFilters();
        tbClienteTransportadoraInclusao.clearParams();
        tbClienteTransportadoraInclusao.setFilterCOD_TRANSPORTADORA(codTransportadora);
        tbClienteTransportadoraInclusao.setFilterCOD_CLIENTE(codCliente);
        tbClienteTransportadoraInclusao.open();
        boolean tbClienteTransportadoraInclusaoEmpty = tbClienteTransportadoraInclusao.isEmpty();
        if (tbClienteTransportadoraInclusaoEmpty) {
            tbClienteTransportadoraInclusao.append();
            tbClienteTransportadoraInclusao.setCOD_TRANSPORTADORA(codTransportadora);
            tbClienteTransportadoraInclusao.setCOD_CLIENTE(codCliente);
            tbClienteTransportadoraInclusao.post();
            tbClienteTransportadoraInclusao.applyUpdates();
            tbClienteTransportadoraInclusao.commitUpdates();
        }
        tbClienteTransportadoraInclusao.close();
    }

    public void excluirTransportadoraCliente(long codTransportadora,
                                             long codCliente) throws DataException {
        CLIENTE_TRANSPORTADORA_FLAG tbClienteTransportadoraExclusao = new CLIENTE_TRANSPORTADORA_FLAG("tbClienteTransportadoraExclusao");
        tbClienteTransportadoraExclusao.close();
        tbClienteTransportadoraExclusao.clearFilters();
        tbClienteTransportadoraExclusao.clearParams();
        tbClienteTransportadoraExclusao.setFilterCOD_TRANSPORTADORA(codTransportadora);
        tbClienteTransportadoraExclusao.setFilterCOD_CLIENTE(codCliente);
        tbClienteTransportadoraExclusao.open();
        boolean tbClienteTransportadoraExclusaoNotEmpty = !tbClienteTransportadoraExclusao.isEmpty();
        if (tbClienteTransportadoraExclusaoNotEmpty) {
            tbClienteTransportadoraExclusao.delete();
            tbClienteTransportadoraExclusao.post();
            tbClienteTransportadoraExclusao.applyUpdates();
            tbClienteTransportadoraExclusao.commitUpdates();
        }
        tbClienteTransportadoraExclusao.close();
    }

    public String getCodigosDasEmpresasDoUsuario(
            String loginUsuario
            ,long codEmpresa
    ) throws DataException {
        LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios = new LEADS_EMPRESAS_USUARIOS("tbBuscaLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.close();
        tbLeadsEmpresasUsuarios.clearFilters();
        tbLeadsEmpresasUsuarios.clearParams();
        tbLeadsEmpresasUsuarios.addParam("USUARIO", loginUsuario);
        tbLeadsEmpresasUsuarios.addParam("COD_EMPRESA_USUARIO", codEmpresa);
        tbLeadsEmpresasUsuarios.open();
        return StringUtil.concatenarRegistros(
                tbLeadsEmpresasUsuarios
                ,"COD_EMPRESA"
                ,", "
        );
    }

    public void alterarSegmento(
            long codSegmento
    ) throws DataException {
        this.tbDadosJuridicosFlag.edit();
        if (codSegmento == 0L) {
            this.tbDadosJuridicosFlag.setID_SEGMENTO(
                    null
            );
        } else {
            this.tbDadosJuridicosFlag.setID_SEGMENTO(
                    codSegmento
            );
        }
        this.tbDadosJuridicosFlag.post();
        this.tbDadosJuridicosFlag.applyUpdates();
        this.tbDadosJuridicosFlag.commitUpdates();
    }

}