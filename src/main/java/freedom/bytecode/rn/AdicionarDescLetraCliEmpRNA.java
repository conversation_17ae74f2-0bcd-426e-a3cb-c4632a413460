package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AdicionarDescLetraCliEmpRNW;
import freedom.data.DataException;

public class AdicionarDescLetraCliEmpRNA extends AdicionarDescLetraCliEmpRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarLetrasDeDeconto() throws DataException {
        if (this.tbItensPerDescFlag.isActive()) {
            this.tbItensPerDescFlag.close();
        }
        this.tbItensPerDescFlag.clearFilters();
        this.tbItensPerDescFlag.clearParams();
        this.tbItensPerDescFlag.open();
    }

    public void carregarEmpresaLetraDesconto() throws DataException {
        if (this.tbEmpresasFiliaisSel.isActive()) {
            this.tbEmpresasFiliaisSel.close();
        }
        this.tbEmpresasFiliaisSel.clearFilters();
        this.tbEmpresasFiliaisSel.clearParams();
        this.tbEmpresasFiliaisSel.open();
    }

}
