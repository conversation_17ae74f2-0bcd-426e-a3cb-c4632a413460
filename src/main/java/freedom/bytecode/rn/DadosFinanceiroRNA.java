package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.DadosFinanceiroRNW;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DadosFinanceiroRNA extends DadosFinanceiroRNW {
    private static final long serialVersionUID = 20130827081850L;

    public String buscarDadosFinanceirosClientes(Double codCliente) throws DataException {
        tbDadosFinanceiroCliente.close();
        tbDadosFinanceiroCliente.clearFilters();
        tbDadosFinanceiroCliente.clearParams();
        tbDadosFinanceiroCliente.addParam("COD_EMPRESA", EmpresaUtil.getCodEmpresaUserLogged());
        tbDadosFinanceiroCliente.addParam("COD_CLIENTE", codCliente);
        tbDadosFinanceiroCliente.open();
        tbDadosFinanceiroCliente.edit();
        String DadosFinanceiro = tbDadosFinanceiroCliente.getDADOS_CREDITO().toString();

        /*simulando dados para teste, no banco não temos muitos clientes com esses dados*/

        //DadosFinanceiro = "[Limite_Credito] Duplicata Pend.: 14980,62 Cheque Pend.:  Nota S/Fatura.:  Limite de Credito: 32000 Disponivel: 17019,38 Venc. Cadastro:  Bloqueado: N Motivo Ultimo Bloqueio: ";
        //DadosFinanceiro = "[Limite_Credito] Duplicata Pend.: 14980,62 Cheque Pend.:  Nota S/Fatura.:  Limite de Credito: 32000 Disponivel: 17019,38 Venc. Cadastro: 25/03/2023 Bloqueado: S Motivo Ultimo Bloqueio: TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE TESTE";
        //DadosFinanceiro = "[Limite_Credito] Duplicata Pend.: 12485,67 Cheque Pend.: 415 Nota S/Fatura.: 45 Limite de Credito: 32000,52 Disponivel: 14785 Venc. Cadastro: 25/03/2023 Bloqueado: N Motivo Ultimo Bloqueio: 125.56 ";


        /* *
         * 1 - Foi utilizado regex para separar as informações de retorno do banco, pois na consulta do dataSet
         *     retornarmos todos os dados em um unico campo {DADOS_CREDITO}
         * 2 - Regex: é ultilizado para capturar padrões em strings
         * 3 - Caso necessário dar uma estudada rapida em Regex Java, e como capturar grupos em Regex;
         * */

        boolean encontrado = false;
        Matcher m = Pattern.compile("Duplicata Pend\\.: ([\\w,.-]*) ").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setDUPLICATA_PENDENTE(m.group(1));
                encontrado = true;
            }
        }

        m = Pattern.compile("Cheque Pend\\.: ([\\w,.-]*) ").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setCHEQUE_PENDENTE(m.group(1));
                encontrado = true;
            }
        }

        m = Pattern.compile("Nota S/Fatura\\.: ([\\w,.-]*) ").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setNOTA_SEM_FATURA(m.group(1));
                encontrado = true;
            }
        }

        m = Pattern.compile("Limite de Credito: ([\\w,.-]*) ").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setLIMITE_CREDITO(m.group(1));
                encontrado = true;
            }
        }

        m = Pattern.compile("Disponivel: ([\\w,.-]*) ").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setDISPONIVEL(m.group(1));
                encontrado = true;
            }
        }

        m = Pattern.compile("Venc\\. Cadastro: ([\\w,./-]*) ").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setVENC_CADASTRO(m.group(1));
                encontrado = true;
            }
        }

        m = Pattern.compile("Bloqueado: ([\\w]*) ").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setBLOQUEADO(m.group(1));
                encontrado = true;
            }
        }

        m = Pattern.compile("Motivo Ultimo Bloqueio: (.*)").matcher(DadosFinanceiro);;
        if (m.find()) {
            if (!m.group(1).equals("")) {
                tbDadosFinanceiroCliente.setMOTIVO_ULTIMO_BLOQUEIO(m.group(1));
                encontrado = true;
            }
        }

        if (!encontrado){
            return DadosFinanceiro;
        }

        tbDadosFinanceiroCliente.post();
        //solicitou para pegar limite_credito e bloqueado da tabela cliente diversos
        tbClienteDiverso.close();
        tbClienteDiverso.clearFilters();
        tbClienteDiverso.setFilterCOD_CLIENTE(codCliente);
        tbClienteDiverso.open();
        return "";
    }


    public boolean ClienteEstaBloqueado(){
        return tbDadosFinanceiroCliente.getBLOQUEADO().equals("S");
    }


}
