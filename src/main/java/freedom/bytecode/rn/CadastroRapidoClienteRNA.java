package freedom.bytecode.rn;

import freedom.bytecode.cursor.BUSCA_CLIENTE_CONTATO;
import freedom.bytecode.cursor.CIDADES;
import freedom.bytecode.cursor.CLIENTES;
import freedom.bytecode.cursor.CLIENTE_DIVERSO;
import freedom.bytecode.rn.wizard.CadastroRapidoClienteRNW;
import freedom.util.pkg.PkgEmpresaRNA;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkPublicVarA;
import freedom.util.pkg.PkgCrmPartsRNA;
import freedom.util.pkg.PkgRbSapRNA;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class CadastroRapidoClienteRNA extends CadastroRapidoClienteRNW {

    private static final long serialVersionUID = 20130827081850L;

    private static final String DESCRICAO = "DESCRICAO";

    private static final String PARM_SYS_3 = "PARM_SYS3";

    private final PkgEmpresaRNA pkEmpresaRna = new PkgEmpresaRNA();

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    private final PkgRbSapRNA pkgRbSapRNA = new PkgRbSapRNA();

    private final PkPublicVarA pkPublicVarA = new PkPublicVarA();
    private String creditoCorporativo;

    public boolean buscarCliente(Double codCliente) throws DataException {
        tbRegimeEspecialTributacao.close();
        tbRegimeEspecialTributacao.open();

        tbClientes.close();
        tbClientes.clearFilters();
        tbClientes.clearParams();
        tbClientes.setFilterCOD_CLIENTE(codCliente);
        tbClientes.open();

        tbDadosFisicos.clearFilters();
        tbDadosFisicos.clearParams();
        tbDadosFisicos.setFilterCOD_CLIENTE(codCliente);
        tbDadosFisicos.open();

        tbDadosJuridicos.clearFilters();
        tbDadosJuridicos.clearParams();
        tbDadosJuridicos.setFilterCOD_CLIENTE(codCliente);
        tbDadosJuridicos.open();

        tbClienteDiverso.clearFilters();
        tbClienteDiverso.clearParams();
        tbClienteDiverso.setFilterCOD_CLIENTE(codCliente);
        tbClienteDiverso.open();

        tbClienteEnderecoInscricao.close();
        tbClienteEnderecoInscricao.setFilterCOD_CLIENTE(codCliente);
        tbClienteEnderecoInscricao.open();

        return !tbClientes.isEmpty();
    }

    public void abrirTabelaAux(String tipoCliente) throws DataException {
        this.tbParmSys.close();
        this.tbParmSys.clearFilters();
        this.tbParmSys.clearParams();
        this.tbParmSys.setFilterCOD_EMPRESA(EmpresaUtil.getCodEmpresaUserLogged());
        this.tbParmSys.open();
        this.tbUf1.setOrderBy(CadastroRapidoClienteRNA.DESCRICAO);
        this.tbUf1.open();
        this.tbUf2.setOrderBy(CadastroRapidoClienteRNA.DESCRICAO);
        this.tbUf2.open();
        this.tbUf3.setOrderBy(CadastroRapidoClienteRNA.DESCRICAO);
        this.tbUf3.open();
        this.tbEstadoCivil.setOrderBy(CadastroRapidoClienteRNA.DESCRICAO);
        this.tbEstadoCivil.open();
        this.tbEscolaridade.setOrderBy(CadastroRapidoClienteRNA.DESCRICAO);
        this.tbEscolaridade.open();
        this.tbClientesSexo.close();
        this.tbClientesSexo.open();
        this.tbClientesRamo.setOrderBy(CadastroRapidoClienteRNA.DESCRICAO);
        this.tbClientesRamo.open();
        this.tbNacionalidade.setOrderBy("DESCRICAO_NACIONALIDADE");
        this.tbNacionalidade.open();
        this.tbClientesProfissaoInclusao.close();
        this.tbClientesProfissaoInclusao.clearFilters();
        this.tbClientesProfissaoInclusao.clearParams();
        this.tbClientesProfissaoInclusao.setFilterTIPO(tipoCliente.equals("F") ? "P" : "R");
        this.tbClientesProfissaoInclusao.open();
        boolean tbClientesNotEmpty = !this.tbClientes.isEmpty();
        if (tbClientesNotEmpty) {
            this.abrirRapCliContato(this.tbClientes.getCOD_CLIENTE().asDecimal());
        }
    }

    public void abrirRapCliContato(double codCliente) throws DataException {
        tbRapCliContato.close();
        tbRapCliContato.clearFilters();
        tbRapCliContato.clearParams();
        tbRapCliContato.setFilterCOD_CLIENTE(codCliente);
        tbRapCliContato.setFilterEH_COMPRADOR("S");
        tbRapCliContato.open();
        boolean tbRapCliContatoNotEmpty = !this.tbRapCliContato.isEmpty();
        if (tbRapCliContatoNotEmpty) {
            return;
        }
        tbRapCliContato.close();
        tbRapCliContato.clearFilters();
        tbRapCliContato.clearParams();
        tbRapCliContato.setFilterCOD_CLIENTE(codCliente);
        tbRapCliContato.open();
    }

    public void pesquisarCidadesUf(CIDADES tbCidade,
                                   String uf) throws DataException {
        boolean tbCidadeEmpty = tbCidade.isEmpty();
        if (!tbCidade.getUF().asString().equals(uf)
                || tbCidadeEmpty) {
            tbCidade.close();
            tbCidade.setMaxRowCount(0);
            tbCidade.clearParams();
            tbCidade.clearFilters();
            tbCidade.setFilterUF(uf);
            tbCidade.open();
        }
    }

    public String incluirCliente(
            String pCodclasse,
            Double pCodigo,
            String pNome,
            Double pCodempresa,
            String pCpfCnpj,
            String pTelecontato,
            String pCrmFone,
            String pCrmEmail,
            String pCrmSms,
            String pCrmMala,
            String pCartaodotz,
            String pCodsexo,
            Double pCodestcivil,
            Date pAniversario,
            String pRgnumero,
            String pRgemissor,
            Date pRgdataemissao,
            String pCodRamo,
            String pInscestadual,
            String pInscmunicipal,
            Double pCodnacional,
            Double pCodprofissao,
            String pUfRes,
            Double pCodCidRes,
            String pBairroRes,
            String pRuaRes,
            String pCepRes,
            String pComplemRes,
            String pUfCom,
            Double pCodCidCom,
            String pBairroCom,
            String pRuaCom,
            String pCepCom,
            String pComplemCom,
            String pUfCob,
            Double pCodCidCob,
            String pBairroCob,
            String pRuaCob,
            String pCepCob,
            String pComplemCob,
            String pPrefixoCel,
            String pTelefoneCel,
            String pPrefixoCom,
            String pTelefoneCom,
            String pPrefixoRes,
            String pTelefoneRes,
            String pPrefixoFax,
            String pTelefoneFax,
            String pEmail,
            String pEmailnfe,
            String pTelehoracont,
            String pFachadaRes,
            String pFachadaCom,
            String pFachadaCob,
            Double pTipoEnd01,
            Double pTipoEnd02,
            Double pTipoEnd03,
            String pFacebook,
            String pTwitter,
            String pEmpresasite,
            String pNumerocno,
            String pNomefantasia,
            String pCodsubtribiss,
            Double pRegimeesptrib,
            String pCnae,
            String pSovalidar,
            String pInscricaoIE,
            String pNomepropriedadeIE,
            String pUfIE,
            Double pCodCidIE,
            String pBairroIE,
            String pRuaIE,
            String pCepIE,
            String pComplemIE,
            String pFachadaIE,
            String pCxpostalIE,
            String pContatoIE,
            String pAtivoIE,
            Double pServidorPublico,
            Double pPoliticamenteExposto,
            Double pEscolaridade,
            Double pNroSuframa,
            Date pDataFundacao,
            String pConcessionaria,
            String pUsuarioLogado,
            Double pcodMidia,
            Value oCampofoco) throws DataException {
        String retFuncao;
        try {
            if (pCodnacional <= 0.00) {
                pCodnacional = 36.0; // pkg não tem tratamento - Padrão Brasil
            }
            if (pCodprofissao <= 0.00) {
                pCodprofissao = null;  // pkg não tem tratamento - Não aceita 0.00
            }
            retFuncao = pkEmpresaRna.incluirClienteCadRapido(
                    pCodclasse,
                    pCodigo,
                    pNome,
                    pCodempresa,
                    pCpfCnpj,
                    pTelecontato,
                    pCrmFone,
                    pCrmEmail,
                    pCrmSms,
                    pCrmMala,
                    pCartaodotz,
                    pCodsexo,
                    pCodestcivil,
                    pAniversario,
                    pRgnumero,
                    pRgemissor,
                    pRgdataemissao,
                    pCodRamo,
                    pInscestadual,
                    pInscmunicipal,
                    pCodnacional,
                    pCodprofissao,
                    pUfRes,
                    pCodCidRes,
                    pBairroRes,
                    pRuaRes,
                    pCepRes,
                    pComplemRes,
                    pUfCom,
                    pCodCidCom,
                    pBairroCom,
                    pRuaCom,
                    pCepCom,
                    pComplemCom,
                    pUfCob,
                    pCodCidCob,
                    pBairroCob,
                    pRuaCob,
                    pCepCob,
                    pComplemCob,
                    pPrefixoCel,
                    pTelefoneCel,
                    pPrefixoCom,
                    pTelefoneCom,
                    pPrefixoRes,
                    pTelefoneRes,
                    pPrefixoFax,
                    pTelefoneFax,
                    pEmail,
                    pEmailnfe,
                    pTelehoracont,
                    pFachadaRes,
                    pFachadaCom,
                    pFachadaCob,
                    pTipoEnd01,
                    pTipoEnd02,
                    pTipoEnd03,
                    pFacebook,
                    pTwitter,
                    pEmpresasite,
                    pNumerocno,
                    pNomefantasia,
                    pCodsubtribiss,
                    pRegimeesptrib,
                    pCnae,
                    pSovalidar,
                    oCampofoco,
                    pInscricaoIE,
                    pNomepropriedadeIE,
                    pUfIE,
                    pCodCidIE,
                    pBairroIE,
                    pRuaIE,
                    pCepIE,
                    pComplemIE,
                    pFachadaIE,
                    pCxpostalIE,
                    pContatoIE,
                    pAtivoIE,
                    pServidorPublico,
                    pPoliticamenteExposto,
                    pEscolaridade,
                    pNroSuframa,
                    pDataFundacao,
                    pConcessionaria,
                    pUsuarioLogado,
                    pcodMidia);
        } catch (DataException e) {
            retFuncao = e.getMessage();
        }
        return retFuncao;
    }

    public void incluirClienteLog(Double pCodCliente,
                                  String pUsuarioLogado) throws DataException {
        this.pkEmpresaRna.incluirClienteLog(pCodCliente,
                pUsuarioLogado);
    }

    public void gerarSapCliente(Double codEmpresa,
                                Double codCliente) throws DataException {
        Value aCodMovimento = new Value(null);
        this.pkgRbSapRNA.gerarSapCliente(codEmpresa,
                codCliente,
                aCodMovimento,
                "S");
    }

    public boolean buscaDadosCliente(String documento) throws DataException {
        tbConsultaNbsPessoaJuridica.close();
        tbConsultaNbsPessoaJuridica.setFilterCNPJ(documento);
        tbConsultaNbsPessoaJuridica.open();

        tbConsultaNbsSintegraDados.close();
        tbConsultaNbsSintegraDados.setFilterCPF_CNPJ(documento);
        tbConsultaNbsSintegraDados.open();

        return !tbConsultaNbsPessoaJuridica.isEmpty();
    }

    public boolean buscaDadosClientePf(String documento, String inscricao) throws DataException {
        tbConsultaNbsSintegraDados.close();
        tbConsultaNbsPessoaFisica.close();

        tbConsultaNbsPessoaFisica.setFilterCPF(documento);
        tbConsultaNbsPessoaFisica.open();

        if (StringUtils.isBlank(inscricao)) {
            return !tbConsultaNbsPessoaFisica.isEmpty();
        }
        tbConsultaNbsSintegraDados.setFilterCPF_CNPJ(documento);
        tbConsultaNbsSintegraDados.setFilterINSCRICAO_ESTADUAL(inscricao);
        tbConsultaNbsSintegraDados.open();
        return !tbConsultaNbsSintegraDados.isEmpty();
    }

    public boolean buscaCliente(Double codCliente) throws DataException {

        CLIENTES tbCnsCliente = new CLIENTES("tbCnsCliente");
        tbCnsCliente.close();
        tbCnsCliente.setFilterCOD_CLIENTE(codCliente);
        tbCnsCliente.open();

        return !tbCnsCliente.isEmpty();
    }

    public boolean usaPluginConsultaNBS(Double codEmpresa) {
        try {
            return this.pkgCrmPartsRNA.getParametro(codEmpresa,
                    CadastroRapidoClienteRNA.PARM_SYS_3,
                    "USA_PLUGIN_CONSULTA_NBS").equalsIgnoreCase("S");
        } catch (DataException dataException) {
            return false;
        }
    }

    public boolean ehRodobensParam(Double codEmpresa) {
        try {
            return pkgCrmPartsRNA.getParametro(codEmpresa, PARM_SYS_3, "EH_RODOBENS").equalsIgnoreCase("S");
        } catch (DataException e) {
            return false;
        }
    }


    public String getUrlConsultaNbs(Double codEmpresa) {
        try {
            return pkgCrmPartsRNA.getUrlPluginConsultaNbs(codEmpresa);
        } catch (DataException e) {
            return "";
        }
    }

    public void openConsultaDadosRetornoApi(Double idCns,
                                            String tipoPessoa) throws DataException {
        if (tipoPessoa.equals("J")) {
            tbConsultaNbsPessoaJuridica.close();
            tbConsultaNbsPessoaJuridica.setFilterID_CONSULTA_NBS(idCns);
            tbConsultaNbsPessoaJuridica.open();
        } else {
            tbConsultaNbsPessoaFisica.close();
            tbConsultaNbsPessoaFisica.setFilterID_CONSULTA_NBS(idCns);
            tbConsultaNbsPessoaFisica.open();
        }

        tbConsultaNbsSintegraDados.close();
        tbConsultaNbsSintegraDados.setFilterID_CONSULTA_NBS(idCns);
        tbConsultaNbsSintegraDados.open();

        tbConsultaNbsSintegraSimples.close();
        tbConsultaNbsSintegraSimples.setFilterID_CONSULTA_NBS(idCns);
        tbConsultaNbsSintegraSimples.open();


        /* Tratar a consulta aos dados Sintegra*/
        /*Tratar consulta aos erros do sistema*/
    }

    public String getSchemaAtual() throws DataException {

        tbSchemaAtual.close();
        tbSchemaAtual.clearFilters();
        tbSchemaAtual.clearParams();
        tbSchemaAtual.open();

        return tbSchemaAtual.getSCHEMA_NAME().asString();
    }

    public String atualizarCadastroCliente(double codCliente, String inscricaoEstadual, double codEmpresa) throws DataException {
        return pkgCrmPartsRNA.atualizarValidadeCadastro(codCliente, inscricaoEstadual, codEmpresa);
    }

    public String camposParaValidacao(double codEmpresaUsuarioLogado, String ehAlteracao) throws DataException {
        return pkEmpresaRna.camposValidacaoCadCliente(codEmpresaUsuarioLogado, ehAlteracao);
    }

    public boolean ehSituacaoValidaCadastroSintegra(String situacaoCadastral, String acao) throws DataException {
        return pkgCrmPartsRNA.validarSituacaoCadSintegra(situacaoCadastral, acao).equalsIgnoreCase("S");
    }

    public String infoSituacaoCadastralIntegracao(double codEmpresa,
                                                  double codClienteApi,
                                                  String tipoPessoa,
                                                  String ie,
                                                  Value rfbSituacao,
                                                  Value rfbCadastroIrregular,
                                                  Value sintegraSituacao,
                                                  Value sintegraCadastroIsento,
                                                  Value sintegraCadastroIrregular,
                                                  Value sintegraMultiplasIe) throws DataException {
        return this.pkgCrmPartsRNA.getSituacaoCadastral(codEmpresa,
                codClienteApi,
                tipoPessoa,
                ie,
                rfbSituacao,
                rfbCadastroIrregular,
                sintegraSituacao,
                sintegraCadastroIsento,
                sintegraCadastroIrregular,
                sintegraMultiplasIe);
    }

    public String validarMensagemConsultaIntegracao(Double codEmpresa, Double codCliente, String inscricao, Double idConsultaNBS, String mensagemIntegracao) {
        try {
            return pkgCrmPartsRNA.validarMensagemConsultaApi(codEmpresa, codCliente, inscricao, idConsultaNBS, mensagemIntegracao);
        } catch (DataException e) {
            return mensagemIntegracao;
        }
    }

    public String validarRetornoApiConsulta(Double idConsultaNBS) throws DataException {
        return pkgCrmPartsRNA.validarRetornoApiConsulta(idConsultaNBS);
    }

    public boolean podeAlterarCadastro(boolean ehCrmParts, String usuarioLogado) {
        if (ehCrmParts) {
            try {
                String retFuncao = pkgCrmPartsRNA.validarAcesso(usuarioLogado,
                        "K0234");
                return retFuncao.equals("S");
            } catch (Exception e) {
                return false;
            }
        } else {
            return true;
        }
    }

    public boolean podeAlterarNomeRazSocCadastro(
            boolean ehCrmParts
            ,String usuarioLogado
    ) {
        if (!ehCrmParts) {
            return true;
        }
        try {
            String retFuncao = this.pkgCrmPartsRNA.validarAcesso(
                    usuarioLogado
                    ,"K0282"
            );
            return !retFuncao.equals(
                    "S"
            );
        } catch (Exception exception) {
            return true;
        }
    }

    public boolean podeAcessarLogsCliente(String usuarioLogado) {
        try {
            return pkgCrmPartsRNA.validarAcesso(usuarioLogado,
                    "K1015").equalsIgnoreCase("S");
        } catch (Exception e) {
            return false;
        }
    }

    public boolean podeAcessarCreditoCorporativo(String usuarioLogado) {
        try {
            return pkgCrmPartsRNA.validarAcesso(usuarioLogado,
                    "K1016").equalsIgnoreCase("S");
        } catch (Exception e) {
            return false;
        }
    }

    public String padronizarInscricaoEstadual(String uf, String inscricao) throws DataException {
        return pkgCrmPartsRNA.padronizarInscricaoEstadual(uf, inscricao);
    }

    public boolean usaCreditoCorporativo(int codEmpresaUsuarioLogado) {
        if (StringUtils.isBlank(this.creditoCorporativo)) {
            try {
                this.creditoCorporativo = pkgCrmPartsRNA.getParametro((double) codEmpresaUsuarioLogado,
                        PARM_SYS_3,
                        "CREDITO_CORPORATIVO");
            } catch (DataException e) {
                this.creditoCorporativo = "N";
            }
        }
        return this.creditoCorporativo.equals("S");
    }

    public void applyUpdatesCadastroCliente(boolean ehPessoaFisica,
                                            String usuarioLogado) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        try {
            session.open();
            this.pkPublicVarA.setUser(session,
                    usuarioLogado);
            this.tbClientes.setSession(session);
            this.tbClientes.edit();
            this.tbClientes.post();
            this.tbClienteDiverso.setSession(session);
            this.tbClienteDiverso.edit();
            this.tbClienteDiverso.post();
            this.tbClienteEnderecoInscricao.setSession(session);
            this.tbClienteEnderecoInscricao.edit();
            this.tbClienteEnderecoInscricao.post();
            if (ehPessoaFisica) {
                this.tbDadosFisicos.setSession(session);
                this.tbDadosFisicos.edit();
                this.tbDadosFisicos.post();
            } else {
                this.tbDadosJuridicos.setSession(session);
                this.tbDadosJuridicos.edit();
                this.tbDadosJuridicos.post();
            }
            this.tbClientes.applyUpdates();
            this.tbClienteDiverso.applyUpdates();
            this.tbClienteEnderecoInscricao.applyUpdates();
            if (ehPessoaFisica) {
                this.tbDadosFisicos.applyUpdates();
            } else {
                this.tbDadosJuridicos.applyUpdates();
            }
            session.commit();
        } catch (DataException dataException) {
            session.rollback();
            throw dataException;
        } finally {
            session.close();
        }
    }

    public boolean possuiContato(double codCliente) throws DataException {
        boolean retFuncao;
        BUSCA_CLIENTE_CONTATO tbClienteContato = new BUSCA_CLIENTE_CONTATO("tbClienteContato");
        tbClienteContato.close();
        tbClienteContato.clearFilters();
        tbClienteContato.clearParams();
        tbClienteContato.setFilterCOD_CLIENTE_EQUALS(codCliente);
        tbClienteContato.setFilterCOD_CLIENTE_MAIOR_QUE_ZERO(" ");
        tbClienteContato.open();
        retFuncao = !tbClienteContato.isEmpty();
        tbClienteContato.close();
        return retFuncao;
    }

    /**
     * boolean retFuncao = this.rn.isObrigatorioMotivoBloqueio(codEmpresa);
     *
     * @param codEmpresa Código da empresa do parâmetro a ser verificado
     * @return true ou false
     */
    public boolean isObrigatorioInformarContatoCRMPartsParmSys2ForcarContato(double codEmpresa) {
        try {
            return (this.pkgCrmPartsRNA.getParametro(codEmpresa,
                    "PARM_SYS2",             /* Nome da tabela do Oracle */
                    "FORCAR_CONTATO").equals("S")); /* Nome da coluna da tabela do Oracle */
        } catch (DataException dataException) {
            return false;
        }
    }

    public boolean isProdutorRural(String codigoCliente) {
        boolean retFuncao = false;
        try {
            CLIENTE_DIVERSO tbClienteDiverso = new CLIENTE_DIVERSO("tbClienteDiverso");
            tbClienteDiverso.close();
            tbClienteDiverso.clearFilters();
            tbClienteDiverso.clearParams();
            tbClienteDiverso.setFilterCOD_CLIENTE(codigoCliente);
            tbClienteDiverso.open();

            boolean tbClienteDiversoNotEmpty = !tbClienteDiverso.isEmpty();
            if (tbClienteDiversoNotEmpty) {
                retFuncao = true;
            }
            tbClienteDiverso.close();
        } catch (DataException dataException) {
            return retFuncao;
        }
        return retFuncao;
    }

    public void carregarCboMidia() throws DataException {
        this.tbMidia.close();
        this.tbMidia.clearFilters();
        this.tbMidia.clearParams();
        this.tbMidia.setFilterATIVO(
                "S"
        );
        this.tbMidia.open();
    }

}