package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.CadastroPainelIndicadoresRNW;
import freedom.data.DataException;

public class CadastroPainelIndicadoresRNA extends CadastroPainelIndicadoresRNW {
    private static final long serialVersionUID = 20130827081850L;

    public void carregaComboGrupo(Integer idGrupoClasse) throws DataException {
        tbComboGrupo.close();
        tbComboGrupo.clearFilters();
        tbComboGrupo.clearParams();
        tbComboGrupo.addFilter("ID_GRUPO_CLASSE");
        tbComboGrupo.addParam("ID_GRUPO_CLASSE", idGrupoClasse);
        tbComboGrupo.open();
    }

    public void carregaPainel(Integer idPainel) throws DataException {
        tbPainel.close();
        tbPainel.clearFilters();
        tbPainel.clearParams();
        tbPainel.addFilter("ID_PAINEL");
        tbPainel.addParam("ID_PAINEL", idPainel);
        tbPainel.open();
    }

    public int getIdPainel() throws DataException {
        tbMaxIdPainel.close();
        tbMaxIdPainel.open();

        return tbMaxIdPainel.getID_PAINEL().asInteger();
    }
}
