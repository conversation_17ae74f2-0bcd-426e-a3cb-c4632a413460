package freedom.bytecode.rn;

import freedom.bytecode.cursor.FORNECEDOR_ESTOQUE;
import freedom.bytecode.cursor.ITENS;
import freedom.bytecode.rn.wizard.BloquearDesbloquearItemCompraRNW;
import freedom.data.DataException;
import freedom.data.impl.View;
import freedom.util.EmpresaUtil;
import freedom.util.StringUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

import java.util.ArrayList;
import java.util.List;

public class BloquearDesbloquearItemCompraRNA extends BloquearDesbloquearItemCompraRNW  {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void carregarGrdEmpresas (
            String loginUsuario
            ,String codItem
            ,long codFornecedor
    ) throws DataException {
        List<Long> listaDosCodigosDasEmpresasDoUsuario = EmpresaUtil.getListaCodigosEmpresasUsuario(
                loginUsuario
        );
        String codigosDasEmpresasDoUsuarioSeparadasPorVirgula = StringUtil.delimitarElementosListaLong(
                listaDosCodigosDasEmpresasDoUsuario
        );
        this.tbItensCustosCompraBloq.close();
        this.tbItensCustosCompraBloq.clearFilters();
        this.tbItensCustosCompraBloq.clearParams();
        this.tbItensCustosCompraBloq.setFilterCOD_ITEM_EQUALS(
                codItem
        );
        this.tbItensCustosCompraBloq.setFilterCOD_FORNECEDOR_EQUALS(
                codFornecedor
        );
        this.tbItensCustosCompraBloq.addFilter(
                "COD_EMPRESA_CONTAINS"
        );
        this.tbItensCustosCompraBloq.addParam(
                "COD_EMPRESA_CONTAINS"
                ,codigosDasEmpresasDoUsuarioSeparadasPorVirgula
                ,true
        );
        this.tbItensCustosCompraBloq.open();
    }

    public void marcarDesmarcarGrdEmpresas() throws DataException {
        String sel = this.tbItensCustosCompraBloq.getSEL().asString();
        this.tbItensCustosCompraBloq.edit();
        if (sel.equals("S")) {
            this.tbItensCustosCompraBloq.setSEL("N");
        } else {
            this.tbItensCustosCompraBloq.setSEL("S");
        }
        this.tbItensCustosCompraBloq.post();
    }

    public List<Long> getEmpresasSelecionadasGrdEmpresas() throws DataException {
        List<Long> retFuncao = new ArrayList<>();
        View vTbItensCustosCompraBloq = this.tbItensCustosCompraBloq.getView();
        vTbItensCustosCompraBloq.first();
        while (Boolean.FALSE.equals(vTbItensCustosCompraBloq.eof())) {
            String sel = vTbItensCustosCompraBloq.getField("SEL").asString();
            if (sel.equals("S")) {
                long codigoDaEmpresa = vTbItensCustosCompraBloq.getField("COD_EMPRESA").asLong();
                retFuncao.add(
                        codigoDaEmpresa
                );
            }
            vTbItensCustosCompraBloq.next();
        }
        return retFuncao;
    }

    public String bloquearDesbloquearItemCompra(
            String codItem
            ,long codFornecedor
            ,long codEmpresa
            ,String bloquearBDesbloquearD
            ,String observacao
            ,String loginUsuario
    ) throws DataException {
        return this.pkgCrmPartsRNA.bloquearDesbloqItemCompra(
                codItem
                ,(double) codFornecedor
                ,(double) codEmpresa
                ,bloquearBDesbloquearD
                ,observacao
                ,loginUsuario
        );
    }

    public String bloquearDesbloquearItemCompraParaTodasEmpresasSelecionadas(
            String codItem
            ,long codFornecedor
            ,String bloquearBDesbloquearD
            ,String observacao
            ,String loginUsuario
    ) throws DataException {
        String retFuncao = null;
        View vTbItensCustosCompraBloq = this.tbItensCustosCompraBloq.getView();
        vTbItensCustosCompraBloq.first();
        while (Boolean.FALSE.equals(vTbItensCustosCompraBloq.eof())) {
            String sel = vTbItensCustosCompraBloq.getField("SEL").asString();
            if (sel.equals("S")) {
                long codEmpresa = vTbItensCustosCompraBloq.getField("COD_EMPRESA").asLong();
                retFuncao = this.bloquearDesbloquearItemCompra(
                        codItem
                        ,codFornecedor
                        ,codEmpresa
                        ,bloquearBDesbloquearD
                        ,observacao
                        ,loginUsuario
                );
                if (!retFuncao.equals("S")) {
                    break;
                }
            }
            vTbItensCustosCompraBloq.next();
        }
        return retFuncao;
    }

    public String getDescricaoItem(
            String codItem
    ) throws DataException {
        String retFuncao = null;
        ITENS tbItens = new ITENS(
                "tbItens"
        );
        tbItens.close();
        tbItens.clearFilters();
        tbItens.clearParams();
        tbItens.setFilterCOD_ITEM(
                codItem
        );
        tbItens.open();
        boolean tbItensNotEmpty = !tbItens.isEmpty();
        if (tbItensNotEmpty) {
            retFuncao = tbItens.getDESCRICAO().asString().trim();
        }
        tbItens.close();
        return retFuncao;
    }

    public String getNomeFornecedor(
            long codFornecedor
    ) throws DataException {
        String retFuncao = null;
        FORNECEDOR_ESTOQUE tbFornecedorEstoque = new FORNECEDOR_ESTOQUE(
                "tbFornecedorEstoque"
        );
        tbFornecedorEstoque.close();
        tbFornecedorEstoque.clearFilters();
        tbFornecedorEstoque.clearParams();
        tbFornecedorEstoque.setFilterCOD_FORNECEDOR(
                codFornecedor
        );
        tbFornecedorEstoque.open();
        boolean tbFornecedorEstoqueNotEmpty = !tbFornecedorEstoque.isEmpty();
        if (tbFornecedorEstoqueNotEmpty) {
            retFuncao = tbFornecedorEstoque.getNOME_FORNECEDOR().asString().trim();
        }
        tbFornecedorEstoque.close();
        return retFuncao;
    }

}
