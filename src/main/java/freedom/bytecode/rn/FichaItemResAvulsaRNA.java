package freedom.bytecode.rn;

import freedom.bytecode.cursor.BUSCA_DEPARA_EMPRESAS_DEPARTAM;
import freedom.bytecode.cursor.BUSCA_EMPRESAS_DIVISOES;
import freedom.bytecode.cursor.BUSCA_EMPRESAS_USUARIOS;
import freedom.bytecode.rn.wizard.FichaItemResAvulsaRNW;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.util.Constantes;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsA;
import freedom.util.pkg.PkgCrmPartsRNA;

public class FichaItemResAvulsaRNA extends FichaItemResAvulsaRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsA pkgCrmPartsA = new PkgCrmPartsA();

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public String salvarReservaAvulsa(
            String loginUsuarioLogado
            ,long codEmpresaDestino
    ) throws DataException {
        String retFuncao;
        try {
            ISession session;
            session = SessionFactory.getInstance().getSession();
            try {
                session.open();
                long seqReserva = SequenceUtil.nextVal(
                        "SEQ_RESERVA"
                );
                this.tbItensReservasPendencias.setSession(
                        session
                );
                this.tbItensReservasPendencias.setCONTROLE_RESERVA(
                        seqReserva
                );
                long codEmpresaUsuario = EmpresaUtil.getCodEmpresaUserLogged();
                if (codEmpresaUsuario != codEmpresaDestino) {
                    long codDivisao = this.getItensReservasPendenciasCodDivisaoDestino(
                            loginUsuarioLogado
                            ,codEmpresaDestino
                    );
                    this.tbItensReservasPendencias.setCOD_EMPRESA_DIVISAO(
                            codDivisao
                    );
                }
                this.tbItensReservasPendencias.post();
                this.tbItensReservasPendencias.applyUpdates();
                double controleReserva = this.tbItensReservasPendencias.getCONTROLE_RESERVA().asDecimal();
                double codEmpresa = this.tbItensReservasPendencias.getCOD_EMPRESA().asDecimal();
                retFuncao = this.pkgCrmPartsA.salvarResAvulsa(
                        session
                        ,controleReserva
                        ,codEmpresa
                );
                if (retFuncao.equals("S")) {
                    session.commit();
                    this.tbItensReservasPendencias.commitUpdates();
                } else {
                    throw new DataException(retFuncao);
                }
            } catch (
                    DataException dataException
            ) {
                session.rollback();
                throw dataException;
            } finally {
                session.close();
            }
        } catch (
                DataException dataException
        ) {
            retFuncao = dataException.getMessage();
        }
        return retFuncao;
    }

    public void openEmpresas(
            String usuarioLogado
            ,double codEmpresa
    ) throws DataException {
        this.tbLeadsEmpresasUsuarios.close();
        this.tbLeadsEmpresasUsuarios.addParam("USUARIO", usuarioLogado);
        this.tbLeadsEmpresasUsuarios.addParam("COD_EMPRESA_USUARIO", codEmpresa);
        this.tbLeadsEmpresasUsuarios.open();
    }

    private long getItensReservasPendenciasCodDivisaoDestino(
            String loginUsuarioLogado
            ,long codEmpresaDestino
    ) throws DataException {
        long retFuncao;
        //region Obtém os dados de "Perfil do usuário" (EMPRESAS_USUARIOS)
        long codEmpresaUsuario;
        long codDepartamentoUsuario;
        BUSCA_EMPRESAS_USUARIOS tbEmpresasUsuarios = new BUSCA_EMPRESAS_USUARIOS(
                "tbEmpresasUsuarios"
        );
        tbEmpresasUsuarios.close();
        tbEmpresasUsuarios.clearFilters();
        tbEmpresasUsuarios.clearParams();
        tbEmpresasUsuarios.setFilterNOME(
                loginUsuarioLogado
        );
        tbEmpresasUsuarios.open();
        boolean tbEmpresasUsuariosEmpty = tbEmpresasUsuarios.isEmpty();
        if (tbEmpresasUsuariosEmpty) {
            String mensagem = (
                    "O usuário \""
                            + loginUsuarioLogado
                            + "\" não foi encontrado."
            );
            throw new DataException(
                    mensagem
            );
        }
        codEmpresaUsuario = tbEmpresasUsuarios.getCOD_EMPRESA().asLong();
        codDepartamentoUsuario = tbEmpresasUsuarios.getCOD_EMPRESA_DEPARTAMENTO().asLong();
        tbEmpresasUsuarios.close();
        //endregion
        //region Obtém os dados de "De-Para Depto./Emp." [Tabelas > Lead > De-Para Depto./Emp.] (DEPARA_EMPRESAS_DEPARTAMENTOS)
        long codDepartamentoDestino;
        BUSCA_DEPARA_EMPRESAS_DEPARTAM tbDeparaEmpresasDepartamentos = new BUSCA_DEPARA_EMPRESAS_DEPARTAM(
                "tbDeparaEmpresasDepartamentos"
        );
        tbDeparaEmpresasDepartamentos.close();
        tbDeparaEmpresasDepartamentos.clearFilters();
        tbDeparaEmpresasDepartamentos.clearParams();
        tbDeparaEmpresasDepartamentos.setFilterCOD_EMPRESA_EQUALS(
                codEmpresaUsuario
        );
        tbDeparaEmpresasDepartamentos.setFilterCOD_EMPRESA_DEPARTAMENTO_EQUALS(
                codDepartamentoUsuario
        );
        tbDeparaEmpresasDepartamentos.setFilterOUT_COD_EMPRESA_EQUALS(
                codEmpresaDestino
        );
        tbDeparaEmpresasDepartamentos.open();
        boolean tbBuscaDeparaEmpresasDepartamentosEmpty = tbDeparaEmpresasDepartamentos.isEmpty();
        if (tbBuscaDeparaEmpresasDepartamentosEmpty) {
            String mensagem = (
                    "O registro não foi encontrado no formulário \"De-Para Depto./Emp.\" [Tabelas > Lead > De-Para Depto./Emp.] (DEPARA_EMPRESAS_DEPARTAMENTOS) para a empresa \""
                            + codEmpresaUsuario
                            + "\" departamento \""
                            + codDepartamentoUsuario
                            + "\" empresa destino \""
                            + codEmpresaDestino
                            + "\"."
            );
            throw new DataException(
                    mensagem
            );
        }
        codDepartamentoDestino = tbDeparaEmpresasDepartamentos.getOUT_COD_EMPRESA_DEPARTAMENTO().asLong();
        tbDeparaEmpresasDepartamentos.close();
        //endregion
        //region Obtém os dados de EMPRESAS_DIVISOES
        BUSCA_EMPRESAS_DIVISOES tbDivisoes = new BUSCA_EMPRESAS_DIVISOES(
                "tbDivisoes"
        );
        tbDivisoes.close();
        tbDivisoes.clearFilters();
        tbDivisoes.clearParams();
        tbDivisoes.setFilterCOD_EMPRESA_EQUALS(
                codEmpresaDestino
        );
        tbDivisoes.setFilterCOD_EMPRESA_DEPARTAMENTO_EQUALS(
                codDepartamentoDestino
        );
        tbDivisoes.open();
        boolean tbDivisoesEmpty = tbDivisoes.isEmpty();
        if (tbDivisoesEmpty) {
            String mensagem = (
                    "O registro nao foi encontrado no formulário \"Divisão\" [Módulo NBS_USER > Divisão] (EMPRESAS_DIVISOES) para a empresa \""
                            + codEmpresaDestino
                            + "\" departamento \""
                            + codDepartamentoDestino
                            + "\"."
            );
            throw new DataException(
                    mensagem
            );
        }
        retFuncao = tbDivisoes.getCOD_EMPRESA_DIVISAO().asLong();
        tbDivisoes.close();
        //endregion
        return retFuncao;
    }

    public boolean getParmSys3CrmpartsObrPesqCliResAvul(
            double codEmpresa
    ) {
        try {
            return this.pkgCrmPartsRNA.getParametro(
                    codEmpresa
                    , Constantes.PARM_SYS_3
                    ,"CRMPARTS_OBR_PESQ_CLI_RES_AVUL"
            ).equals("S");
        } catch (
                Exception exception
        ) {
            return false;
        }
    }

}