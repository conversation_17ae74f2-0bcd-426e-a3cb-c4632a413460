package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.TemplatesRNW;
import freedom.connection.ISession;
import encrypt.criptografia.Cript;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.MensagemWhatsAppZenvia;

public class TemplatesRNA extends TemplatesRNW {

    private static final long serialVersionUID = 20130827081850L;
    private final Cript cript = new Cript();

    public void filtrarCadastroWhats(int idCelular) throws DataException {
        tbCadastroWhatsapp.close();
        tbCadastroWhatsapp.clearFilters();
        tbCadastroWhatsapp.clearParams();
        tbCadastroWhatsapp.addFilter("ID_CELULAR");
        tbCadastroWhatsapp.addParam("ID_CELULAR", idCelular);
        tbCadastroWhatsapp.open();
    }

    public String criarTemplateZenvia(int idCelular, String nomeTemplate, String template,
                                      Value mensageOut) throws Exception {

        String id = "";
        filtrarCadastroWhats(idCelular);

        if (tbCadastroWhatsapp.count() > 0) {
            MensagemWhatsAppZenvia zapZenvia = new MensagemWhatsAppZenvia("N");

            String numberFrom = zapZenvia.ajustaFone(tbCadastroWhatsapp.getCELULAR().asString(), "L");

            if (numberFrom.contains("Fone")) {
                mensageOut.setValue(numberFrom);
                return "";
            }

            String url = tbCadastroWhatsapp.getURL_API().asString();
            String usuario = tbCadastroWhatsapp.getAPI_USUARIO().asString();
            String emailNotificacao = tbCadastroWhatsapp.getAPI_EMAIL_NOTIFICACAO().asString().trim();

            if (usuario.trim().equals("")) {
                mensageOut.setValue("Usuário não informado cadastro(LeadZap) para empresa " + idCelular);
                return "";
            }

            String senha = cript.decript(tbCadastroWhatsapp.getAPI_SENHA().asString());

            String tokenAuth =
                    zapZenvia.getTokenAut(
                            url
                            , usuario, senha, mensageOut);

            if (!tokenAuth.equals("")) {

                String token = tbCadastroWhatsapp.getX_API_TOKEN().asString();

                id = zapZenvia.criarTemplate(url, token, tokenAuth, nomeTemplate, template,
                        numberFrom, emailNotificacao, mensageOut);
            }
        } else {
            mensageOut.setValue("Não encontrado nenhum cadastro(LeadZap) ativo para empresa " + idCelular);
        }
        return id;
    }

    public String excluirTemplateZenvia(int codEmpresa, int idTemplateInterno) throws Exception {
        filtrarCadastroWhats(codEmpresa);

        if (tbCadastroWhatsapp.count() > 0) {
            Value mensageOut = new Value(null);

            MensagemWhatsAppZenvia zapZenvia = new MensagemWhatsAppZenvia("N");

            String numberFrom = zapZenvia.ajustaFone(tbCadastroWhatsapp.getCELULAR().asString(), "L");

            if (numberFrom.contains("Fone")) {
                return numberFrom;
            }

            String url = tbCadastroWhatsapp.getURL_API().asString();
            String usuario = tbCadastroWhatsapp.getAPI_USUARIO().asString();

            if (usuario.trim().equals("")) {
                mensageOut.setValue("Usuário não informado cadastro(LeadZap) para empresa " + codEmpresa);
                return "";
            }

            String senha = cript.decript(tbCadastroWhatsapp.getAPI_SENHA().asString());

            String tokenAuth = zapZenvia.getTokenAut(
                    url
                    , usuario, senha, mensageOut);

            if (!tokenAuth.equals("")) {

                String token = tbCadastroWhatsapp.getX_API_TOKEN().asString();

                return zapZenvia.excluirTemplate(url, token, tokenAuth, idTemplateInterno);
            }
        } else {
            return "Não encontrado nenhum cadastro(LeadZap) ativo para empresa ";
        }
        return "Não foi possível excluir o template.";
    }

    @Override
    public void beforeSalvar(ISession session) throws DataException {
        super.beforeSalvar(session); //To change body of generated methods, choose Tools | Templates.

        if (tbEmailModelo.getID_NBSAPI_MESSAGE_TEMPLATE().asInteger() > 0) {
            tbNbsapiMessageTemplate.close();
            tbNbsapiMessageTemplate.clearFilters();
            tbNbsapiMessageTemplate.clearParams();
            tbNbsapiMessageTemplate.addFilter("ID_NBSAPI_MESSAGE_TEMPLATE");
            tbNbsapiMessageTemplate.addParam("ID_NBSAPI_MESSAGE_TEMPLATE", tbEmailModelo.getID_NBSAPI_MESSAGE_TEMPLATE());
            tbNbsapiMessageTemplate.open();
            if (tbNbsapiMessageTemplate.count() > 0) {
                tbNbsapiMessageTemplate.edit();
                tbNbsapiMessageTemplate.setEH_CHAT(tbEmailModelo.getEH_CHAT());
                tbNbsapiMessageTemplate.post();
            }
        }

    }

}
