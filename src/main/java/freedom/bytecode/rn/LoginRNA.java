package freedom.bytecode.rn;

import encrypt.criptografia.RpcClient;
import freedom.bytecode.rn.wizard.LoginRNW;
import freedom.client.controls.impl.TFTable;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.util.ApplicationUtil;
import freedom.util.FRLogger;

public class LoginRNA extends LoginRNW {

    private static final long serialVersionUID = 20130827081850L;

    /**
     * Validação 1: verifica se usuario pode logar no banco selecionado
     * Validação 2: Validacao de schema
     * somente executado sistema NBS estiver em multiplos schemas no mesmo banco
     * Explicação desta parte da validacao:
     * <p>
     * 1. validar se tabela sys.nbs_schema existe e tem ao menos um registro
     * se nao existir ou estiver vazia, nao precisa validar cruzamento de schema com o usuario
     * IMPORTANTE:
     * 1. a tabela sys.nbs_schema pode nao existir no cliente (em configuracao nao multischema)
     * 2. o usuario da conexao do pool, precisa ter direito de acesso à tabela sys.nbs_schema
     * Se nao tiver, NAO VAI POSSIVEL fazer a validacao de cruzamento, e o PROGRAMA VAI PERMITIR o acesso do usuario
     * <p>
     * Nota: se tabela nao existir ou se usuario do pool de conexao nao tiver acesso a esta tabela,
     * vai dar erro '0RA-00942 : Table or View dos not exist'
     * <p>
     * Caso o erro 'ORA-00942' ocorra, considera a validacao valida ja que não é possivel saber se
     * a tabela sys.nbs_schema nao existe no banco OU se é o usuario de conexao que nao tem acesso à tabela
     * Neste caso, o controle de acesso deve fazer o restante da seguranca
     */
    public String validarLogin(
            String usuario
            ,String senha
            ,String schema
            ,boolean validarLoginSchema
    ) throws Exception {
        ISession session = null;
        ISession sessionAut = null;
        String result = "";
        try {
            session = SessionFactory.getInstance().getSession(schema);
            session.open();
            sessionAut = SessionFactory.getInstance().getSession(schema + "_aut");
            //Valido usuário
            sessionAut.open(
                    usuario
                    ,senha
            );
            if (validarLoginSchema) {
                result = validaLoginSchema(
                        session
                        ,usuario
                        ,schema
                );
            }
        } catch (
                DataException dataException
        ) {
            if (dataException.getMessage().contains("ORA-01017")) {
                boolean validaSenhaSeguro = this.validarSenhaSeguro(
                        usuario
                        ,senha
                );
                if (!validaSenhaSeguro) {
                    result = "NBS-0415 - Erro ao autenticar: usuário ou senha inválido.";
                }
            } else {
                result = (
                        "NBS-0435 - Erro: "
                                + dataException.getMessage()
                );
            }
        } finally {
            if (session != null) {
                session.close();
            }
            if (sessionAut != null) {
                sessionAut.close();
            }
        }
        return result;
    }

    private boolean validarSenhaSeguro(
            String usuario
            ,String senha
    ) throws Exception {
        boolean result = false;
        boolean seguro = false;
        this.tbTables.close();
        this.tbTables.clearFilters();
        this.tbTables.clearParams();
        this.tbTables.setFilterTABLE_NAME(
                "NBS_SERVIDOR"
        );
        this.tbTables.setFilterOWNER(
                "SYS"
        );
        this.tbTables.open();
        boolean cSys = this.tbTables.count() > 0;
        try {
            TFTable tableServidor;
            if (cSys) {
                tableServidor = this.tbSysServidor;
            } else {
                tableServidor = this.tbServidor;
            }
            if (tableServidor != null) {
                tableServidor.close();
                tableServidor.clearFilters();
                tableServidor.clearParams();
                tableServidor.addFilter(
                        "SERVIDOR_TIPO_ID"
                );
                tableServidor.addParam(
                        "SERVIDOR_TIPO_ID"
                        ,1
                );
                tableServidor.setOrderBy(
                        "A.DATA DESC"
                );
                tableServidor.open();
                if (tableServidor.count() > 0) {
                    String ativo = tableServidor.getField("ATIVO").asString();
                    String seg = tableServidor.getField("SEGURO").asString();
                    if ((ativo.equals("S"))
                            && (seg.equals("S"))) {
                        seguro = true;
                    }
                }
            }
            if (seguro) {
                TFTable tableServidorUser;
                if (cSys) {
                    tableServidorUser = this.tbSysServidorUser;
                } else {
                    tableServidorUser = this.tbServidorUser;
                }
                if (tableServidorUser != null) {
                    tableServidorUser.close();
                    tableServidorUser.clearFilters();
                    tableServidorUser.clearParams();
                    tableServidorUser.addFilter(
                            "NOME"
                    );
                    tableServidorUser.addParam(
                            "NOME"
                            ,usuario.toUpperCase()
                    ); //JWG
                    tableServidorUser.open();
                    if (tableServidorUser.count() > 0) {
                        String senhaCript = RpcClient.Criptografar(
                                senha
                        );
                        String senhaSalva = tableServidorUser.getField("PW_USER").asString();
                        result = senhaCript.equals(
                                senhaSalva
                        );
                    }
                }
            }
        } catch (
                DataException dataException
        ) {
            result = false;
        }
        return result;
    }

    private String validaLoginSchema(
            ISession session
            ,String usuario
            ,String schemaName
    ) {
        String result = "";
        boolean usaSchema = false;
        try {
            this.tbSchema.setSession(
                    session
            );
            this.tbSchema.close();
            this.tbSchema.open();
            usaSchema = (this.tbSchema.count() > 0);
        } catch (
                DataException dataException
        ) {
            if (dataException.getMessage().contains("ORA-00942")) {
                // nao joga excessao aqui
                FRLogger.log(
                        ("NBS-0315 - Erro ao autenticar usuario "
                                + usuario
                                + ": Erro ao validar se existe schema na tabela nbs_schema: "
                                + dataException.getMessage())
                        , this.getClass()
                );
            } else {
                result = (
                        "NBS-0325 - Erro ao autenticar: Não foi possivel realizar a validacao do Schema do Usuário devido a um erro. Detalhe do Erro: "
                                + dataException.getMessage()
                );
            }
        }
        // 2.precisa validar cruzamento de usario e schema
        if (usaSchema) {
            try {
                this.tbSchemaUser.setSession(
                        session
                );
                this.tbSchemaUser.close();
                this.tbSchemaUser.clearFilters();
                this.tbSchemaUser.clearParams();
                this.tbSchemaUser.setFilterSCHEMA_USER(
                        usuario.toUpperCase()
                );
                this.tbSchemaUser.setFilterSCHEMA_NAME(
                        schemaName.toUpperCase()
                );
                this.tbSchemaUser.open();
                if (this.tbSchemaUser.count() == 0) {
                    result = "NBS-0335 - Erro ao autenticar: Usuário sem permissão para acessar o schema selecionado";
                } else {
                    ApplicationUtil.setValue(
                            "SCHEMA_NAME"
                            ,this.tbSchemaUser.getSCHEMA_NAME().asString()
                    );
                }
            } catch (
                    DataException dataException
            ) {
                result = (
                        "NBS-0345 - Erro ao autenticar: Não foi possivel realizar a validacao do Schema do Usuário devido a um erro. Detalhe do Erro: "
                                + dataException.getMessage()
                );
            }
        }
        return result;
    }

}
