package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.CadastroAcessoFuncaoRNW;
import freedom.data.DataException;

public class CadastroAcessoFuncaoRNA extends CadastroAcessoFuncaoRNW {
    private static final long serialVersionUID = 20130827081850L;

    public void carregaGridAcessoFuncao(Integer idPainel) throws DataException {
        tbGridCadAcessoFunc.close();
        tbGridCadAcessoFunc.clearFilters();
        tbGridCadAcessoFunc.clearParams();
        tbGridCadAcessoFunc.addFilter("NOT_EXISTS_PAINEL");
        tbGridCadAcessoFunc.addParam("ID_PAINEL", idPainel);
        tbGridCadAcessoFunc.open();
    }

    public void carregaPainelAcessoFuncao(Integer codFuncao, Integer idPainel) throws DataException {
        tbPainelAcessoFuncao.close();
        tbPainelAcessoFuncao.clearFilters();
        tbPainelAcessoFuncao.clearParams();
        tbPainelAcessoFuncao.addFilter("ID_PAINEL:COD_FUNCAO");
        tbPainelAcessoFuncao.addParam("ID_PAINEL", idPainel);
        tbPainelAcessoFuncao.addParam("COD_FUNCAO", codFuncao);
        tbPainelAcessoFuncao.open();
    }
}
