package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ClientesFlagsServRNW;
import freedom.client.controls.impl.TFCombo;
import freedom.client.controls.impl.TFTable;
import freedom.client.controls.util.MakeListOptions;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.TableUtil;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;

public class ClientesFlagsServRNA extends ClientesFlagsServRNW {

    private static final long serialVersionUID = 20130827081850L;
//    private String usuarioLogado = EmpresaUtil.getUserLogged();

    public void consultaClienteDiverso(Double codCliente) throws DataException {
        tbClienteDiverso.close();
        tbClienteDiverso.clearFilters();
        tbClienteDiverso.clearParams();
        tbClienteDiverso.addFilter("COD_CLIENTE");
        tbClienteDiverso.addParam("COD_CLIENTE", codCliente);
        tbClienteDiverso.open();
    }

    public void consultaClientes(Double codCliente) throws DataException {
        tbClientes.close();
        tbClientes.clearFilters();
        tbClientes.clearParams();
        tbClientes.addFilter("COD_CLIENTE");
        tbClientes.addParam("COD_CLIENTE", codCliente);
        tbClientes.open();
    }

    public void consultaEndCliIE(Double codCliente) throws DataException {
        tbClientesEnderecoIe.close();
        tbClientesEnderecoIe.clearFilters();
        tbClientesEnderecoIe.clearParams();
        tbClientesEnderecoIe.addFilter("COD_CLIENTE");
        tbClientesEnderecoIe.addParam("COD_CLIENTE", codCliente);
        tbClientesEnderecoIe.open();
    }

    public void carregaCombo() throws DataException {
        tbClienteFlagGrupo.close();
        tbClienteFlagGrupo.clearFilters();
        tbClienteFlagGrupo.clearParams();
        tbClienteFlagGrupo.open();
    }

    public void filtraGrid(int idGrupo, String descricao) throws DataException {
        tbClienteFlag.close();
        tbClienteFlag.clearFilters();
        tbClienteFlag.clearParams();
        tbClienteFlag.addFilter("ID_GRUPO;DESCRICAO");
        tbClienteFlag.addParam("ID_GRUPO", idGrupo);
        tbClienteFlag.addParam("DESCRICAO", descricao);
        tbClienteFlag.open();

//        filtraValornew();
    }

    public void filtraValor() throws DataException {
        try {
            Value valor = new Value("");
            TFCombo combo = new TFCombo();
            DateFormat horaFormat = new SimpleDateFormat("HH:mm");
            DecimalFormat df = new DecimalFormat("##0.00");

            tbClienteFlag.disableControls();

            while (!tbClienteFlag.eof()) {
                String tabela = tbClienteFlag.getTABELA_DELPHI().asString().trim().toUpperCase();
                String campo = tbClienteFlag.getCAMPO_DELPHI().asString().trim().toUpperCase();
                String objeto = tbClienteFlag.getOBJETO().asString().trim();
                String eEmpresa = tbClienteFlag.getPOR_EMPRESA().asString().trim();
                valor = new Value("");

                if (eEmpresa.equals("N")) {
                    if (tabela.equals("CLIENTE_DIVERSO")) {
                        if (TableUtil.getExistField(tbClienteDiverso, campo)) {
                            valor = tbClienteDiverso.getField(campo);
                        } else {
                            valor = new Value("ERRO: Campo: " + campo + " não encontrado tabela " + tabela);
                        }
                    } else {
                        if ((!tabela.equals("")) && (!campo.equals(""))) {
                            TFTable table = getTableParm(tabela);
                            if (table != null) {
                                filtrarParm(table, 0, tbClienteDiverso.getCOD_CLIENTE().asDecimal());
                                if (TableUtil.getExistField(table, campo)) {
                                    valor = table.getField(campo);
                                } else {
                                    valor = new Value("ERRO: Campo: " + campo + " não encontrado tabela " + tabela);
                                }
                            }
                        }
                    }

                    tbClienteFlag.edit();

                    //Retorno o valor Descritivo
                    String listOption = tbClienteFlag.getLIST_OPTION().asString().trim();
                    switch (objeto) {
                        case "S": // String
                            tbClienteFlag.setField("VALOR", valor);
                            tbClienteFlag.setField("VALOR_GRID", valor);
                            break;
                        case "B": // CheckBox
                            if (!listOption.equals("")) {
                                combo.setListOptions(listOption);
                                combo.setValue(valor);
                                tbClienteFlag.setField("VALOR", valor);
                                tbClienteFlag.setField("VALOR_GRID", combo.getText());
                            } else {
                                if (valor.toString().trim().equals("S")) {
                                    tbClienteFlag.setField("VALOR", "S");
                                    tbClienteFlag.setField("VALOR_GRID", "Sim");
                                } else {
                                    tbClienteFlag.setField("VALOR", "N");
                                    tbClienteFlag.setField("VALOR_GRID", "Não");
                                }
                            }
                            break;
                        case "C": // ComboBox
                            setComboLookup(combo, tbClienteFlag, tbClienteFlag);
                            combo.setValue(valor);
                            tbClienteFlag.setField("VALOR", valor);
                            tbClienteFlag.setField("VALOR_GRID", combo.getText());
                            break;
                        case "L": // List Options ComboBox
                            if (!listOption.equals("")) {
                                combo.setListOptions(listOption);
                                combo.setValue(valor);
                                tbClienteFlag.setField("VALOR", valor);
                                tbClienteFlag.setField("VALOR_GRID", combo.getText());
                            }
                            break;
                        case "N": // Númerico
                            String valorF = "";
                            if (valor.asDecimal() > 0) {
                                valorF = df.format(valor.asDecimal());
                            }
                            tbClienteFlag.setField("VALOR", valor);
                            tbClienteFlag.setField("VALOR_GRID", valorF);
                            break;
                        case "I": // Inteiro
                            tbClienteFlag.setField("VALOR", valor);
                            tbClienteFlag.setField("VALOR_GRID", valor);
                            break;
                        case "D": // Data
                            tbClienteFlag.setField("VALOR", valor);
                            tbClienteFlag.setField("VALOR_GRID", valor);
                            break;
                        case "H": // Hora
                            if (valor.toString().isEmpty()) {
                                tbClienteFlag.setField("VALOR", null);
                            } else {
                                tbClienteFlag.setField("VALOR", horaFormat.format(valor.asDate()));
                                tbClienteFlag.setField("VALOR_GRID", horaFormat.format(valor.asDate()));
                            }
                            break;
                        case "P": // Password
                            tbClienteFlag.setField("VALOR", "*****");
                            tbClienteFlag.setField("VALOR_GRID", "*****");
                            break;
                        case "A":
                            tbClienteFlag.setField("VALOR", "(file)");
                            tbClienteFlag.setField("VALOR_GRID", null);
                            break;
                        default:
                            tbClienteFlag.setField("VALOR", null);
                            tbClienteFlag.setField("VALOR_GRID", null);
                            break;
                    }
                } else {
                    tbClienteFlag.setField("VALOR", valor);
                    tbClienteFlag.setField("VALOR_GRID", valor);
                }

                tbClienteFlag.post();
                tbClienteFlag.next();
            }
        } finally {
            tbClienteFlag.enableControls();
            tbClienteFlag.first();
        }
    }

    private TFTable getTableParm(String tabela) {
        TFTable table = null;

        switch (tabela) {
            case "CLIENTE_CPAGAR_ICMS_SUB_EMP":
                table = tbClienteCpagarIcmsSubEmp;
                break;
            case "ACESSORIO_EMP":
                table = tbAcessorioEmp;
                break;
            case "INDUSTRIA_EMP":
                table = tbIndustriaEmp;
                break;
            case "CLIENTE_DIVERSO":
                table = tbClienteDiverso;
                break;
            case "CLIENTES":
                table = tbClientes;
                break;
            case "FORNEC_DESCONTA_ICMSST_EMP":
                table = tbFornecDescontaIcmsstEmp;
                break;
            case "CLIENTE_ISS_EMPRESA":
                table = tbClienteIssEmpresa;
                break;
            case "CLIENTE_ISENTO_ISS_EMPRESA":
                table = tbClienteIsentoIssEmpresa;
                break;
            case "CLIENTE_IGNORA_PJ_RETER_ISS":
                table = tbClienteIgnoraPjReterIss;
                break;
            case "FORNEC_SUB_ICMS_EMP":
                table = tbFornecSubIcmsEmp;
                break;
            case "FORNEC_IVA_EMP":
                table = tbFornecIvaEmp;
                break;
        }
        return table;
    }

    public void filtrarParm(TFTable table, int idEmpresa, Double codCliente) throws DataException {
        table.setCriteria(null);
        table.filter();
        if (idEmpresa > 0) {
            table.setCriteria("COD_EMPRESA = " + idEmpresa);
        }
        if (codCliente > 0) {
            table.setCriteria("COD_CLIENTE = " + codCliente);
        }
        table.filter();
    }

    public void setComboLookup(TFCombo comboBox, TFTable tableParmSys, TFTable tableParmEmpValues) throws DataException {

        String filter = tableParmSys.getField("LOOKUP_FILTER").asString().trim();

        if (!filter.trim().equals("")) {
            if (filter.toUpperCase().indexOf(":COD_EMPRESA") > 0) {
                String codEmpresa = tableParmEmpValues.getField("COD_EMPRESA").asString();

                if (codEmpresa.trim().equals("")) {
                    codEmpresa = "0";
                }

                filter = filter.replaceAll("(?i):COD_EMPRESA", codEmpresa);
            }
        }

        //Caso deseje que saia no log o Select informar no construtor o valor true MakeListOptions(true)
        try {
            String lo = new MakeListOptions().getListOptionsFrom(
                    tableParmSys.getField("LOOKUP_TABLE").asString(),
                    tableParmSys.getField("LOOKUP_KEY_FIELD").asString(),
                    tableParmSys.getField("LOOKUP_DISPLAY_FIELD").asString(),
                    filter);

            comboBox.setListOptions(lo);
            comboBox.setFlex(true);
        } catch (DataException e) {
            throw new DataException("Erro ao configurar ListOption Campo: " + tbClienteFlag.getCAMPO_DELPHI() + " " + e.getMessage());
        }
    }

    public boolean salvarAlteracoes(String tipoAlteracao, int idEmpresa, Double codCliente, Value valor) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();
        Boolean sucesso = false;
        try {
            session.open();

            String tabela = tbClienteFlag.getTABELA_DELPHI().asString().trim().toUpperCase();
            String campo = tbClienteFlag.getCAMPO_DELPHI().asString().trim().toUpperCase();
            String tipoCampo = tbClienteFlag.getOBJETO().asString().trim();

            TFTable table = getTableParm(tabela);
            table.setSession(session);

            getDataSet(table, codCliente, 0);
            filtrarParm(table, idEmpresa, codCliente);
            if (!table.isEmpty()) {
                setValueTable(table, campo, tipoCampo, valor);
                table.applyUpdates();
            }

            session.commit();
            table.commitUpdates();
            return sucesso = true;
        } catch (DataException e) {
            session.rollback();
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    private void setValueTable(TFTable table, String campo, String objeto, Value valor) throws DataException {
        table.edit();
        switch (objeto) {
            case "I":
                table.setField(campo, valor.asInteger());
                break;
            case "N":
                table.setField(campo, valor.asDecimal());
                break;
            case "D":
                table.setField(campo, valor.asDate());
                break;
            default:
                table.setField(campo, valor);
                break;
        }

        switch (campo.toUpperCase()) {
            case "ISENTO_ICMS":
                if (valor.toString().equals("S") && tbClienteDiverso.getNAO_DESC_ICMS_ISEN().isNull()) {
                    table.setField("NAO_DESC_ICMS_ISEN", "N");
                }
                break;
            case "FLAG_EH_SHOW_ROOM":
                if (valor.toString().equals("S")) {
                    table.setField("COD_EMPRESA_SHOW_ROOM", 0);
                    table.setField("COD_EMPRESA_SHOW_ROOM_FILIAL", 0);
                } else {
                    table.setField("COD_EMPRESA_SHOW_ROOM", null);
                    table.setField("COD_EMPRESA_SHOW_ROOM_FILIAL", null);
                }
                break;
        }

        table.post();
    }

    private void getDataSet(TFTable tabela, Double codCliente, int codEmpresa) throws DataException {
        TFTable table = tabela;
        table.close();
        table.clearFilters();
        table.clearParams();
        table.addFilter("COD_CLIENTE");
        table.addParam("COD_CLIENTE", codCliente);
        if (codEmpresa > 0) {
            table.addFilter("COD_EMPRESA");
            table.addParam("COD_EMPRESA", codEmpresa);
        }
        table.open();
    }
}
