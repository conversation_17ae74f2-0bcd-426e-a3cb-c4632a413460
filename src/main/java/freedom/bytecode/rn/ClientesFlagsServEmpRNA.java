package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ClientesFlagsServEmpRNW;
import freedom.client.controls.impl.TFTable;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.TableUtil;
import java.text.DecimalFormat;

public class ClientesFlagsServEmpRNA extends ClientesFlagsServEmpRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void carregaDefault(String mode) throws DataException {
        tbClienteflagempresa.close();
        tbClienteflagempresa.clearFilters();
        tbClienteflagempresa.clearParams();
        if (mode.equals("DSP")) {
            tbClienteflagempresa.addFilter("MAIOR_COD_EMPRESA");
            tbClienteflagempresa.addParam("MAIOR_COD_EMPRESA", 0);
        }
        tbClienteflagempresa.open();
    }

    public void carregaDados(String tabelaNome, String campo, String objeto, Double codCliente,
            String mode) throws DataException {

        Value valor = new Value(null);
        carregaDefault(mode);
        campo = campo.toUpperCase();
        DecimalFormat df = new DecimalFormat("##0.00");

        if (!tabelaNome.equals("")) {
            TFTable table = getTableParm(tabelaNome);
            getDataSet(table, codCliente, 0);
            if (table != null) {
                filtrarParm(table, 0, codCliente);

                table.first();

                while (!table.eof()) {

                    int codEmp = table.getField("COD_EMPRESA").asInteger();

                    if (!campo.equals("")) {
                        if (TableUtil.getExistField(table, campo.toUpperCase())) {
                            valor = table.getField(campo);
                        } else {
                            throw new DataException("Campo: " + campo + " não encontrado tabela " + tabelaNome);
                        }
                    }

                    String nomeEmpresa = getNomeEmpresa(codEmp);

                    tbClienteflagempresa.append();
                    tbClienteflagempresa.setField("COD_EMPRESA", codEmp);
                    tbClienteflagempresa.setField("COD_CLIENTE", codCliente);
                    tbClienteflagempresa.setField("EMPRESA", nomeEmpresa);
                    tbClienteflagempresa.setField("VALOR", valor);

                    //Retorno o valor Descritivo
                    switch (objeto) {
                        case "S": // String
                            tbClienteflagempresa.setField("VALOR_GRID", valor.toString());
                            break;
                        case "B": // CheckBox
                            if (valor.toString().equals("S")) {
                                tbClienteflagempresa.setField("VALOR_GRID", "Sim");
                            } else {
                                tbClienteflagempresa.setField("VALOR_GRID", "Não");
                            }
                            break;
                        case "N": // Númerico
                            String valorF = "";
                            if (valor.asDecimal() >= 0) {
                                valorF = df.format(valor.asDecimal());
                            }
                            tbClienteflagempresa.setField("VALOR_GRID", valorF);
                            break;
                    }
                    tbClienteflagempresa.post();
                    table.next();
                }
                tbClienteflagempresa.sort("COD_EMPRESA", "");
            }
        }
    }

    private TFTable getTableParm(String tabela) throws DataException {
        TFTable table = null;

        switch (tabela.toUpperCase().trim()) {
            case "CLIENTE_CPAGAR_ICMS_SUB_EMP":
                table = tbClienteCpagarIcmsSubEmp;
                break;
            case "ACESSORIO_EMP":
                table = tbAcessorioEmp;
                break;
            case "INDUSTRIA_EMP":
                table = tbIndustriaEmp;
                break;
            case "CLIENTE_DIVERSO":
                table = tbClienteDiverso;
                break;
            case "FORNEC_DESCONTA_ICMSST_EMP":
                table = tbFornecDescontaIcmsstEmp;
                break;
            case "CLIENTE_ISS_EMPRESA":
                table = tbClienteIssEmpresa;
                break;
            case "CLIENTE_ISENTO_ISS_EMPRESA":
                table = tbClienteIsentoIssEmpresa;
                break;
            case "CLIENTE_IGNORA_PJ_RETER_ISS":
                table = tbClienteIgnoraPjReterIss;
                break;
            case "FORNEC_SUB_ICMS_EMP":
                table = tbFornecSubIcmsEmp;
                break;
            case "FORNEC_IVA_EMP":
                table = tbFornecIvaEmp;
                break;
        }
        return table;
    }

    private void getDataSet(TFTable tabela, Double codCliente, int codEmpresa) throws DataException {
        TFTable table = tabela;
        table.close();
        table.clearFilters();
        table.clearParams();
        table.addFilter("COD_CLIENTE");
        table.addParam("COD_CLIENTE", codCliente);
        if (codEmpresa > 0) {
            table.addFilter("COD_EMPRESA");
            table.addParam("COD_EMPRESA", codEmpresa);
        }
        table.open();
    }

    public void filtrarParm(TFTable table, int codEmpresa, Double codCliente) throws DataException {
        table.setCriteria(null);
        table.filter();
        if (codEmpresa > 0) {
            table.setCriteria("COD_EMPRESA = " + codEmpresa);
        }
        if (codCliente > 0) {
            table.setCriteria("COD_CLIENTE = " + codCliente);
        }
        table.filter();
    }

    private String getNomeEmpresa(int codEmpresa) throws DataException {
        tbEmpresas.close();
        tbEmpresas.clearParams();
        tbEmpresas.clearFilters();
        tbEmpresas.addFilter("COD_EMPRESA");
        tbEmpresas.addParam("COD_EMPRESA", codEmpresa);
        tbEmpresas.open();
        if (tbEmpresas.isEmpty()) {
            return "nao achou empresa";
        }
        return tbEmpresas.getNOME_EMPRESA_ABR().asString();
    }

    public void salvarAlteracoes(String modoAlteracao, String tabelaNome, String campo,
            String objeto, int codEmpresa, Double codCliente, Value valor) throws DataException {
        ISession session;
        session = SessionFactory.getInstance().getSession();

        try {
            session.open();

            TFTable table = getTableParm(tabelaNome);
            getDataSet(table, codCliente, codEmpresa);
            table.setSession(session);

            switch (modoAlteracao) {
                case "INS":
                    table.append();
                    table.setField("COD_EMPRESA", codEmpresa);
                    table.setField("COD_CLIENTE", codCliente);
                    if (!campo.equals("")) {
                        if (objeto.equals("B")) {
                            valor = new Value("S");
                            table.setField(campo.toUpperCase(), valor.asString());
                        } else {
                            table.setField(campo.toUpperCase(), 0);
                        }
                    }
                    table.post();
                    break;
                case "UPD":
                    table.edit();
                    if (!valor.isEmpty()) {
                        switch (objeto) {
                            case "B":
                                table.setField(campo.toUpperCase(), valor.asString());
                                break;
                            case "N":
                                table.setField(campo.toUpperCase(), valor.asDecimal());
                                break;
                            default:
                                table.setField(campo.toUpperCase(), valor);
                                break;
                        }
                    }
                    table.post();
                    break;
                case "DLT":
                    table.delete();
                    break;
            }
            table.applyUpdates();

            session.commit();
            table.commitUpdates();
        } catch (DataException e) {
            session.rollback();
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }
}
