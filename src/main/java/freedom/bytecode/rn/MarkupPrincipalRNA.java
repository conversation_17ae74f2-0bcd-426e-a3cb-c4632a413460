package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.MarkupPrincipalRNW;
import freedom.data.SequenceUtil;

public class MarkupPrincipalRNA extends MarkupPrincipalRNW {
    private static final long serialVersionUID = 20130827081850L;

    private int idMarkupModelo;

    public void carregarGridMarKupModelo(String Pdescricao, int Pid) throws Exception {
        if (!tbMarkupModelo.isEmpty()) {
            idMarkupModelo = tbMarkupModelo.getID_MARKUP().asInteger();
        }
        tbMarkupModelo.close();
        tbMarkupModelo.setOrderBy("DESCRICAO");
        tbMarkupModelo.clearFilters();
        if (Pid > 0) {
            tbMarkupModelo.setFilterID_MARKUP(Pid);
        }
        if (!Pdescricao.isEmpty()) {
            tbMarkupModelo.setFilterDESCRICAO(Pdescricao);
        }
        tbMarkupModelo.open();
        tbMarkupModelo.locate("ID_MARKUP", idMarkupModelo);
    }

    public void novoMarkupModelo() throws Exception {
        tbMarkupModelo.append();
        tbMarkupModelo.setID_MARKUP(SequenceUtil.nextVal("SEQ_CP_MARKUP_MODELO"));
        idMarkupModelo = tbMarkupModelo.getID_MARKUP().asInteger();
        tbMarkupModelo.setTIPO_CUSTO("C");
        tbMarkupTipo.sort("ID_MARKUP_TIPO", "desc");
        tbMarkupTipo.first();
        while (!tbMarkupTipo.eof()) {
            tbMarkup.append();
            tbMarkup.setID_MARKUP(idMarkupModelo);
            tbMarkup.setID_MARKUP_TIPO(tbMarkupTipo.getID_MARKUP_TIPO());
            tbMarkup.setTIPO_COMISSAO("V");
            tbMarkup.post();
            tbMarkupTipo.next();
        }
    }

    public void alterarMarkupModelo() throws Exception {
        idMarkupModelo = tbMarkupModelo.getID_MARKUP().asInteger();
        tbMarkupModelo.edit();
    }

    public void cancelarAlteracaoMarkupModelo() throws Exception {
        tbMarkupModelo.cancel();
        tbMarkup.cancel();

    }

    public void excluirMarkupModelo() throws Exception {
        tbMarkup.first();

        while (!tbMarkup.eof()) {
            tbMarkup.delete();
        }

        tbMarkup.applyUpdates();
        tbMarkupModelo.delete();
        tbMarkupModelo.applyUpdates();
        tbMarkupModelo.commitUpdates();
        tbMarkup.commitUpdates();
    }

    public void salvarMarkupModelo() throws Exception {
        idMarkupModelo = tbMarkupModelo.getID_MARKUP().asInteger();
        tbMarkupModelo.post();
        tbMarkupModelo.applyUpdates();
        tbMarkup.applyUpdates();
        tbMarkupModelo.commitUpdates();
        tbMarkup.commitUpdates();
    }

    public void selecionarRegistroAnteriorGridMarkupModelo() throws Exception {
        tbMarkupModelo.locate("ID_MARKUP", idMarkupModelo);
    }

    public boolean nenhumRegistroGridMarkupModeloSelecionado() throws Exception {
        return tbMarkupModelo.isEmpty();
    }

    public boolean isExisteCrmFluxoVinculado() throws Exception {
        if (this.tbParmFluxo.isActive()) {
            this.tbParmFluxo.close();
        }
        this.tbParmFluxo.clearFilters();
        this.tbParmFluxo.clearParams();
        this.tbParmFluxo.setFilterMARKUP_RELACIONADO(tbMarkupModelo.getID_MARKUP().asInteger());
        this.tbParmFluxo.open();
        if (this.tbParmFluxo.count() > 0) {
            return true;
        }
        this.tbParmFluxo.close();
        return false;
    }

    public void AlterarMarkup() throws Exception {
        tbMarkup.edit();
    }

    public void cancelarMarkup() throws Exception {
        tbMarkup.cancel();
    }

    public void salvarMarkup() throws Exception {
        tbMarkup.post();
    }

    public void carregarDadosMarkup() throws Exception {
        tbMarkup.close();
        tbMarkup.clearFilters();
        tbMarkup.clearParams();
        tbMarkup.setFilterID_MARKUP(tbMarkupModelo.getID_MARKUP().asInteger());
        tbMarkup.setOrderBy("ID_MARKUP_TIPO");
        tbMarkup.open();
    }

    public void carregaDadosMarkupTipo() throws Exception {
        tbMarkupTipo.close();
        tbMarkupTipo.clearFilters();
        tbMarkupTipo.clearParams();
        tbMarkupTipo.setOrderBy("ID_MARKUP_TIPO");
        tbMarkupTipo.open();
    }

    public boolean nenhumRegistroGridMarkupSelecionado() throws Exception {
        return tbMarkup.isEmpty();
    }

}
