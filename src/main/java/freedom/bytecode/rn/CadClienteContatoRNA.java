package freedom.bytecode.rn;

import freedom.bytecode.cursor.BUSCA_CLIENTE_CONTATO;
import freedom.bytecode.cursor.CLIENTE_CONTATO;
import freedom.bytecode.rn.wizard.CadClienteContatoRNW;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.util.DateUtils;
import freedom.util.pkg.PkgCrmPartsRNA;

import java.util.Date;

public class CadClienteContatoRNA extends CadClienteContatoRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void carregarGrdContatos(long codCliente,
                                    String contato,
                                    String responsavelPelaPesquisaDaFabrica,
                                    String areaDeContato) throws DataException {
        this.tbClienteContato.close();
        this.tbClienteContato.clearFilters();
        this.tbClienteContato.clearParams();
        this.tbClienteContato.setFilterCOD_CLIENTE_EQUALS(codCliente);
        if (!contato.isEmpty()) {
//            this.tbClienteContato.setFilterCONTATO_ANYPOSITION(contato + "%");
            this.tbClienteContato.setFilterCONTATO(contato);
        }
        if (!responsavelPelaPesquisaDaFabrica.isEmpty()) {
            this.tbClienteContato.setFilterEH_PESQUISA_EQUALS(responsavelPelaPesquisaDaFabrica);
        }
        if (!areaDeContato.isEmpty()) {
            this.tbClienteContato.setFilterAREA_CONTATO_EQUALS(areaDeContato);
        }
        this.tbClienteContato.setOrderBy("NVL(CLIENTE_CONTATO.RESP_GARANTIA_MONTADORA, 'N') DESC, CLIENTE_CONTATO.CONTATO");
        this.tbClienteContato.open();
    }

    public void incluirAlterarContato(long codCliente,
                                      long idContato,
                                      String contato,
                                      String sexo,
                                      String area,
                                      long cpf,
                                      Date nascimento,
                                      String cnh,
                                      String passaporte,
                                      String rg,
                                      String rgOrgaoEmissor,
                                      Date rgDataEmissao,
                                      int politicamenteExposto,
                                      String dddResidencial,
                                      String telefoneResidencial,
                                      String dddCelular,
                                      String telefoneCelular,
                                      String dddWhatsapp,
                                      String telefoneWhatsapp,
                                      String email,
                                      String ehComprador,
                                      String ehPreposto,
                                      String responsavelPelaPesquisaDaFabrica,
                                      String ehResponsavelPelaGarantiaDaMontadora,
                                      String funcao,
                                      String time,
                                      String hobby,
                                      String usuarioLogado) throws DataException {
        BUSCA_CLIENTE_CONTATO tbClienteContatoInclusaoAlteracao = new BUSCA_CLIENTE_CONTATO("tbClienteContatoInclusaoAlteracao");
        tbClienteContatoInclusaoAlteracao.close();
        tbClienteContatoInclusaoAlteracao.clearFilters();
        tbClienteContatoInclusaoAlteracao.clearParams();
        tbClienteContatoInclusaoAlteracao.setFilterID_CONTATO_EQUALS(idContato);
        tbClienteContatoInclusaoAlteracao.setFilterCONTATO_ANYPOSITION(contato);
        tbClienteContatoInclusaoAlteracao.open();
        boolean tbClienteContatoInclusaoAlteracaoEmpty = tbClienteContatoInclusaoAlteracao.isEmpty();
        if (tbClienteContatoInclusaoAlteracaoEmpty) {
            tbClienteContatoInclusaoAlteracao.append();
            idContato = SequenceUtil.nextVal("SEQ_CLIENTE_CONTATO");
            tbClienteContatoInclusaoAlteracao.setID_CONTATO(idContato);
            Date dataAtual = DateUtils.getDbDate();
            tbClienteContatoInclusaoAlteracao.setDATA_CADASTRO(dataAtual);
            tbClienteContatoInclusaoAlteracao.setUSUARIO_CADASTRO(usuarioLogado);
        } else {
            tbClienteContatoInclusaoAlteracao.edit();
            tbClienteContatoInclusaoAlteracao.setID_CONTATO(idContato);
            tbClienteContatoInclusaoAlteracao.setUSUARIO_ULTIMA_ATUALIZACAO(usuarioLogado);
            Date dataAtual = DateUtils.getDbDate();
            tbClienteContatoInclusaoAlteracao.setDATA_ULTIMA_ATUALIZACAO(dataAtual);
        }
        tbClienteContatoInclusaoAlteracao.setID_CONTATO(idContato);
        tbClienteContatoInclusaoAlteracao.setCOD_CLIENTE(codCliente);
        tbClienteContatoInclusaoAlteracao.setCONTATO(contato);
        tbClienteContatoInclusaoAlteracao.setCOD_SEXO(sexo);
        tbClienteContatoInclusaoAlteracao.setAREA_CONTATO(area);
        if (cpf == 0L) {
            tbClienteContatoInclusaoAlteracao.setCPF(null);
        } else {
            tbClienteContatoInclusaoAlteracao.setCPF(cpf);
        }
        tbClienteContatoInclusaoAlteracao.setANIVERSARIO(nascimento);
        tbClienteContatoInclusaoAlteracao.setCNH(cnh);
        tbClienteContatoInclusaoAlteracao.setPASSAPORTE_CARTEIRACIVIL(passaporte);
        tbClienteContatoInclusaoAlteracao.setRG(rg);
        tbClienteContatoInclusaoAlteracao.setORGAO_EMISSOR(rgOrgaoEmissor);
        tbClienteContatoInclusaoAlteracao.setDATA_EMISSAO(rgDataEmissao);
        tbClienteContatoInclusaoAlteracao.setPOLITICAMENTE_EXPOSTO(politicamenteExposto);
        tbClienteContatoInclusaoAlteracao.setPREFIXO_RES(dddResidencial);
        tbClienteContatoInclusaoAlteracao.setFONE(telefoneResidencial);
        tbClienteContatoInclusaoAlteracao.setPREFIXO_CEL(dddCelular);
        tbClienteContatoInclusaoAlteracao.setCELULAR(telefoneCelular);
        tbClienteContatoInclusaoAlteracao.setPREFIXO_WHATSAPP(dddWhatsapp);
        tbClienteContatoInclusaoAlteracao.setWHATSAPP(telefoneWhatsapp);
        tbClienteContatoInclusaoAlteracao.setEMAIL(email);
        tbClienteContatoInclusaoAlteracao.setEH_COMPRADOR(ehComprador);
        tbClienteContatoInclusaoAlteracao.setEH_PREPOSTO(ehPreposto);
        tbClienteContatoInclusaoAlteracao.setEH_PESQUISA(responsavelPelaPesquisaDaFabrica);
        tbClienteContatoInclusaoAlteracao.setRESP_GARANTIA_MONTADORA(ehResponsavelPelaGarantiaDaMontadora);
        tbClienteContatoInclusaoAlteracao.setFUNCAO(funcao);
        tbClienteContatoInclusaoAlteracao.setTIME_ESPORTE(time);
        tbClienteContatoInclusaoAlteracao.setHOBBY(hobby);
        tbClienteContatoInclusaoAlteracao.post();
        tbClienteContatoInclusaoAlteracao.applyUpdates();
        tbClienteContatoInclusaoAlteracao.commitUpdates();
    }

    public void excluirContato() throws DataException {
        this.tbClienteContato.delete();
        this.tbClienteContato.post();
        this.tbClienteContato.applyUpdates();
        this.tbClienteContato.commitUpdates();
    }

    public boolean existeAlgumContatoComprador(long codCliente) throws DataException {
        CLIENTE_CONTATO tbClienteContato = new CLIENTE_CONTATO("tbClienteContato");
        tbClienteContato.close();
        tbClienteContato.clearFilters();
        tbClienteContato.clearParams();
        tbClienteContato.setFilterCOD_CLIENTE(codCliente);
        tbClienteContato.setFilterEH_COMPRADOR_EQUALS("S");
        tbClienteContato.open();
        boolean tbClienteContatoNotEmpty = !tbClienteContato.isEmpty();
        tbClienteContato.close();
        return tbClienteContatoNotEmpty;
    }

    public boolean isCPFValido(String cpf) throws DataException {
        String retFuncao = this.pkgCrmPartsRNA.validarCpf(cpf);
        return retFuncao.equals("S");
    }

    public void carregarCboAreaDeContato() throws DataException {
        this.tbClienteContatoTipo.setMaxRowCount(0);
        this.tbClienteContatoTipo.close();
        this.tbClienteContatoTipo.clearFilters();
        this.tbClienteContatoTipo.clearParams();
        this.tbClienteContatoTipo.setFilterATIVO_EQUALS("S");
        this.tbClienteContatoTipo.open();
    }

    public void carregarCboFiltroAreaDeContato() throws DataException {
        this.tbFiltroClienteContatoTipo.setMaxRowCount(0);
        this.tbFiltroClienteContatoTipo.close();
        this.tbFiltroClienteContatoTipo.clearFilters();
        this.tbFiltroClienteContatoTipo.clearParams();
        this.tbFiltroClienteContatoTipo.setFilterATIVO_EQUALS("S");
        this.tbFiltroClienteContatoTipo.open();
    }

}