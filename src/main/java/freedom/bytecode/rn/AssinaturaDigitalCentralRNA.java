package freedom.bytecode.rn;
import freedom.data.DataException;

import java.util.Date;

import freedom.data.impl.View;

import freedom.bytecode.rn.wizard.AssinaturaDigitalCentralRNW;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;

public class AssinaturaDigitalCentralRNA extends AssinaturaDigitalCentralRNW  {
    private static final long serialVersionUID = 20130827081850L;

    /* para trazer somente as linha do ultimo envelope enviado (quando há mais de um) utilizo o criteria
    * optei por fazer aqui pois fazer no banco poderia sair muito caro */
    String FILTRO_PEGAR_ULTIMO_ENVELOPE = "ROW_NUM = 1";

    public void filtrarTbCentralAssinaturaGrid(int codEmpresa, TipoAssinaturaStrategy tipoAssinatura, String nomeOuDocumento , String tipoPeriodo, Date dataInicial, Date dataFinal) throws DataException {
        tbCentralAssinaturaGrid.setCriteria(null);
        tbCentralAssinaturaGrid.filter();
        tbCentralAssinaturaGrid.close();
        tbCentralAssinaturaGrid.clearFilters();
        tbCentralAssinaturaGrid.clearParams();
        if (codEmpresa > 0){
            tbCentralAssinaturaGrid.setFilterCOD_EMPRESA(codEmpresa);
        }
        if (tipoAssinatura != null){
            tbCentralAssinaturaGrid.setFilterTIPO_ASSINATURA(tipoAssinatura.getTipo());
        }
        if (!nomeOuDocumento.isEmpty()){
            tbCentralAssinaturaGrid.setFilterNOME_OU_DOCUMENTO(nomeOuDocumento);
        }
        switch (tipoPeriodo) {
            case "HOJE":
                tbCentralAssinaturaGrid.setFilterENVIADAS_HOJE("");
                break;
            case "ONTEM":
                tbCentralAssinaturaGrid.setFilterENVIADAS_ONTEM("");
                break;
            case "SEMANA":
                tbCentralAssinaturaGrid.setFilterESTA_SEMANA("");
                break;
            case "MES":
                tbCentralAssinaturaGrid.setFilterESTE_MES("");
                break;
            case "MES_ANTERIOR":
                tbCentralAssinaturaGrid.setFilterMES_ANTERIOR("");
                break;
            case "PERIODO":
                if (dataInicial != null && dataFinal != null) {
                    tbCentralAssinaturaGrid.setFilterPERIODO(dataInicial, dataFinal);
                }
                break;
        }
        tbCentralAssinaturaGrid.setOrderBy("DATA_ENVIO DESC NULLS LAST");
        tbCentralAssinaturaGrid.open();
    }


    public void filtrarEmpresasUsuarios(int codEmpresa, String usuario) throws DataException {
        tbLeadsEmpresasUsuarios.close();
        tbLeadsEmpresasUsuarios.clearFilters();
        tbLeadsEmpresasUsuarios.clearParams();
        tbLeadsEmpresasUsuarios.addParam("USUARIO", usuario.trim());
        tbLeadsEmpresasUsuarios.addParam("COD_EMPRESA_USUARIO", codEmpresa);
        tbLeadsEmpresasUsuarios.open();
    }

    public void filtrarTbAssinaturaDigital() throws DataException {
        tbAssinaturaDigital.close();
        tbAssinaturaDigital.clearFilters();
        tbAssinaturaDigital.clearParams();
        tbAssinaturaDigital.open();
    }

    /**
     * Filtra as assinaturas da central de acordo com o status, porém esse filtro é feito sobre a consulta do banco (criteria)
     * e não diretamente no banco de dados
     * @param filter
     * @throws DataException
     */
    public void filtrarAssinaturaPorStatus(String filter) throws DataException {
        tbCentralAssinaturaGrid.setCriteria(filter/* + " AND " + FILTRO_PEGAR_ULTIMO_ENVELOPE*/) ;
        tbCentralAssinaturaGrid.filter();
    }

    /**
     * Calcula o total de registros de acordo com o filtro passado
     * @param filter
     * @return
     * @throws DataException
     */
    public int calcularTotal(String filter) throws DataException {
        View v = tbCentralAssinaturaGrid.select(filter /*+ " AND " + FILTRO_PEGAR_ULTIMO_ENVELOPE*/);
        return v.compute("COUNT", "DESC_STATUS").asInteger();
    }

}
