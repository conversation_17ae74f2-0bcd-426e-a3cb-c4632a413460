/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.96
   Class Main  : ResultCreditoCorporativo
   Analista    : ROGERIOK
   Data Created: 25/05/2023 21:50:07
   Data Changed: 25/05/2023 22:21:40
   Data Geracao: 29/05/2023 09:25:08
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ResultCreditoCorporativoRNW extends ResultCreditoCorporativoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



