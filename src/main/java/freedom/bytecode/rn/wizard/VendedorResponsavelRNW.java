/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.29
   Class Main  : VendedorResponsavel
   Analista    : GIORDANNY
   Data Created: 30/12/1899 00:00:00
   Data Changed: 11/07/2024 13:07:00
   Data Geracao: 11/07/2024 13:07:08
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class VendedorResponsavelRNW extends VendedorResponsavelRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



