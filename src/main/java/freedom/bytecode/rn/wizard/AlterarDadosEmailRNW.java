/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.4.9
   Class Main  : AlterarDadosEmail
   Analista    : EMERSON
   Data Created: 06/11/2019 13:49:43
   Data Changed: 06/11/2019 14:07:51
   Data Geracao: 06/11/2019 14:08:57
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AlterarDadosEmailRNW extends AlterarDadosEmailRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



