/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.4.88
   Class Main  : PesquisarEmpresasUsuarios
   Analista    : EMERSON
   Data Created: 15/07/2020 10:41:20
   Data Changed: 15/07/2020 11:11:38
   Data Geracao: 15/07/2020 11:11:46
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class PesquisarEmpresasUsuariosRNW extends PesquisarEmpresasUsuariosRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



