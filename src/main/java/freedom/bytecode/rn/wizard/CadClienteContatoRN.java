package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadClienteContatoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadClienteContatoRN() {
        init_tbClienteContato();
        init_tbClienteContatoTipo();
        init_tbFiltroClienteContatoTipo();
    }

    public BUSCA_CLIENTE_CONTATO tbClienteContato = new BUSCA_CLIENTE_CONTATO("tbClienteContato");

    private void init_tbClienteContato() {
        tbClienteContato.setName("tbClienteContato");
        tbClienteContato.setMaxRowCount(200);
        tbClienteContato.setWKey("4600220;44802");
        tbClienteContato.setRatioBatchSize(20);
    }

    public BUSCA_CLIENTE_CONTATO_TIPO tbClienteContatoTipo = new BUSCA_CLIENTE_CONTATO_TIPO("tbClienteContatoTipo");

    private void init_tbClienteContatoTipo() {
        tbClienteContatoTipo.setName("tbClienteContatoTipo");
        tbClienteContatoTipo.setMaxRowCount(200);
        tbClienteContatoTipo.setWKey("4600220;44803");
        tbClienteContatoTipo.setRatioBatchSize(20);
    }

    public BUSCA_CLIENTE_CONTATO_TIPO tbFiltroClienteContatoTipo = new BUSCA_CLIENTE_CONTATO_TIPO("tbFiltroClienteContatoTipo");

    private void init_tbFiltroClienteContatoTipo() {
        tbFiltroClienteContatoTipo.setName("tbFiltroClienteContatoTipo");
        tbFiltroClienteContatoTipo.setMaxRowCount(200);
        tbFiltroClienteContatoTipo.setWKey("4600220;44804");
        tbFiltroClienteContatoTipo.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}