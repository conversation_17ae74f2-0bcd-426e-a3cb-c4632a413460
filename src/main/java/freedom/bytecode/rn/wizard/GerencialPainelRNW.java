/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.26
   Class Main  : GerencialPainel
   Analista    : JOANDERSON.GUARIM
   Data Created: 25/04/2024 07:47:07
   Data Changed: 24/05/2024 14:22:15
   Data Geracao: 24/05/2024 14:22:32
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class GerencialPainelRNW extends GerencialPainelRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



