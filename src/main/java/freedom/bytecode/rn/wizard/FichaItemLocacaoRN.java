package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FichaItemLocacaoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public FichaItemLocacaoRN() {
        init_tbItemLocacaoDispEmp();
        init_tbLocalEstoque();
    }

    public ITEM_LOCACAO_DISP_EMP tbItemLocacaoDispEmp = new ITEM_LOCACAO_DISP_EMP("tbItemLocacaoDispEmp");

    private void init_tbItemLocacaoDispEmp() {
        tbItemLocacaoDispEmp.setName("tbItemLocacaoDispEmp");
        tbItemLocacaoDispEmp.setMaxRowCount(0);
        tbItemLocacaoDispEmp.setWKey("310045;31001");
        tbItemLocacaoDispEmp.setRatioBatchSize(20);
    }

    public LOCAL_ESTOQUE tbLocalEstoque = new LOCAL_ESTOQUE("tbLocalEstoque");

    private void init_tbLocalEstoque() {
        tbLocalEstoque.setName("tbLocalEstoque");
        tbLocalEstoque.setMaxRowCount(200);
        tbLocalEstoque.setWKey("310045;31002");
        tbLocalEstoque.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}