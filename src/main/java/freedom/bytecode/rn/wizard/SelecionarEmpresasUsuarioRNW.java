/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.41
   Class Main  : SelecionarEmpresasUsuario
   Analista    : GIORDANNY
   Data Created: 31/03/2025 13:12:19
   Data Changed: 31/03/2025 13:33:43
   Data Geracao: 31/03/2025 13:33:46
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class SelecionarEmpresasUsuarioRNW extends SelecionarEmpresasUsuarioRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



