package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class TimeTemplateHorariosRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public TimeTemplateHorariosRN() {
        init_tbTimeTemplateHorario();
        init_tbTimeTemplate();
    }

    public TIME_TEMPLATE_HORARIO_SQL tbTimeTemplateHorario = new TIME_TEMPLATE_HORARIO_SQL("tbTimeTemplateHorario");

    private void init_tbTimeTemplateHorario() {
        tbTimeTemplateHorario.setName("tbTimeTemplateHorario");
        tbTimeTemplateHorario.setMaxRowCount(200);
        tbTimeTemplateHorario.setWKey("24509;24503");
        tbTimeTemplateHorario.setRatioBatchSize(20);
    }

    public TIME_TEMPLATE_SQL tbTimeTemplate = new TIME_TEMPLATE_SQL("tbTimeTemplate");

    private void init_tbTimeTemplate() {
        tbTimeTemplate.setName("tbTimeTemplate");
        tbTimeTemplate.setMaxRowCount(200);
        tbTimeTemplate.setWKey("24509;24505");
        tbTimeTemplate.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}