package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class GerencialPainelPreferenciaRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public GerencialPainelPreferenciaRN() {
        init_tbPainelGerencialUsuario();
        init_tbGridIndicadores();
        init_tbGridQuebraInd();
        init_tbEmpresasUsuarios();
        init_tbPreferencia();
    }

    public PAINEL_GERENCIAL_USUARIO tbPainelGerencialUsuario = new PAINEL_GERENCIAL_USUARIO("tbPainelGerencialUsuario");

    private void init_tbPainelGerencialUsuario() {
        tbPainelGerencialUsuario.setName("tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.setMaxRowCount(200);
        tbPainelGerencialUsuario.setWKey("382036;38201");
        tbPainelGerencialUsuario.setRatioBatchSize(20);
    }

    public BSC_GRID_INDICADORES tbGridIndicadores = new BSC_GRID_INDICADORES("tbGridIndicadores");

    private void init_tbGridIndicadores() {
        tbGridIndicadores.setName("tbGridIndicadores");
        tbGridIndicadores.setMaxRowCount(200);
        tbGridIndicadores.setWKey("382036;38202");
        tbGridIndicadores.setRatioBatchSize(20);
    }

    public BSC_GRID_QUEBRA_IND tbGridQuebraInd = new BSC_GRID_QUEBRA_IND("tbGridQuebraInd");

    private void init_tbGridQuebraInd() {
        tbGridQuebraInd.setName("tbGridQuebraInd");
        tbGridQuebraInd.setMaxRowCount(200);
        tbGridQuebraInd.setWKey("382036;38203");
        tbGridQuebraInd.setRatioBatchSize(20);
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("382036;38204");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public BSC_PREFERENCIA tbPreferencia = new BSC_PREFERENCIA("tbPreferencia");

    private void init_tbPreferencia() {
        tbPreferencia.setName("tbPreferencia");
        tbPreferencia.setMaxRowCount(200);
        tbPreferencia.setWKey("382036;38205");
        tbPreferencia.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}