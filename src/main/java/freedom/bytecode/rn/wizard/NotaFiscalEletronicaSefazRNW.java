/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.30
   Class Main  : NotaFiscalEletronicaSefaz
   Analista    : ROGERIOK
   Data Created: 30/12/1899 00:00:00
   Data Changed: 23/07/2024 14:52:52
   Data Geracao: 23/07/2024 14:52:56
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class NotaFiscalEletronicaSefazRNW extends NotaFiscalEletronicaSefazRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



