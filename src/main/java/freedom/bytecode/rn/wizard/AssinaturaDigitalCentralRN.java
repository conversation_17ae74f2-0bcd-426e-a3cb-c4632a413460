package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class AssinaturaDigitalCentralRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public AssinaturaDigitalCentralRN() {
        init_tbCentralAssinaturaGrid();
        init_tbLeadsEmpresasUsuarios();
        init_tbAssinaturaDigital();
    }

    public CENTRAL_ASSINATURA_GRID tbCentralAssinaturaGrid = new CENTRAL_ASSINATURA_GRID("tbCentralAssinaturaGrid");

    private void init_tbCentralAssinaturaGrid() {
        tbCentralAssinaturaGrid.setName("tbCentralAssinaturaGrid");
        tbCentralAssinaturaGrid.setMaxRowCount(0);
        tbCentralAssinaturaGrid.setWKey("455010;47401");
        tbCentralAssinaturaGrid.setRatioBatchSize(20);
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios = new LEADS_EMPRESAS_USUARIOS("tbLeadsEmpresasUsuarios");

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.setWKey("455010;47402");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
    }

    public CRM_ASSINATURA_DIGITAL tbAssinaturaDigital = new CRM_ASSINATURA_DIGITAL("tbAssinaturaDigital");

    private void init_tbAssinaturaDigital() {
        tbAssinaturaDigital.setName("tbAssinaturaDigital");
        tbAssinaturaDigital.setMaxRowCount(200);
        tbAssinaturaDigital.setWKey("455010;47403");
        tbAssinaturaDigital.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}