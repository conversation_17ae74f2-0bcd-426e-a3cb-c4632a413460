/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.46
   Class Main  : TemplatesCelularAprovar
   Analista    : EMERSON
   Data Created: 18/08/2022 14:34:16
   Data Changed: 18/08/2022 14:44:18
   Data Geracao: 18/08/2022 14:45:14
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class TemplatesCelularAprovarRN<PERSON> extends TemplatesCelularAprovarRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



