/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Server - Versao: ********
   Class Main  : TimeTemplateHorarios
   Analista    : LUIZ.RIPARDO
   Data Created: 30/12/1899 00:00:00
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 15/07/2024 10:50:38
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class TimeTemplateHorariosRNW extends TimeTemplateHorariosRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



