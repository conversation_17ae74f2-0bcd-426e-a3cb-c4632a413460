package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class AlterarClienteBasicoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public AlterarClienteBasicoRN() {
        init_tbClientes();
        init_sc();
    }

    public CLIENTES tbClientes = new CLIENTES("tbClientes");

    private void init_tbClientes() {
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("7000150;70001");
        tbClientes.setDeltaMode("dmAll");
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClientes);
        sc.getTables().add(item0);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}