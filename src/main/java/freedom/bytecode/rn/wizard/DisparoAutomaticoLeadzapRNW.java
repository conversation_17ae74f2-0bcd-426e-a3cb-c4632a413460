/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.4.50
   Class Main  : DisparoAutomaticoLeadzap
   Analista    : EMERSON
   Data Created: 10/12/2019 13:39:16
   Data Changed: 10/02/2020 11:18:27
   Data Geracao: 10/02/2020 11:18:43
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.bytecode.cursor.*;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.RowState;
import freedom.data.SequenceUtil;
import freedom.data.TableState;
import freedom.util.WorkListFactory;

public abstract class DisparoAutomaticoLeadzapRNW extends DisparoAutomaticoLeadzapRN {

    public TableState operRN = TableState.QUERYING;

    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }
	
    public void incluir() throws DataException {
        tbDisparo.append();
        tbDisparo.setSISTEMA("P");
    }

    public void alterar() throws DataException {
        if (!tbDisparo.isEmpty()) {
            tbDisparo.edit();    
        }
    }        

    public void cancelar() throws DataException {
        tbDisparo.cancelUpdates();
        tbDisparoChatbot.cancelUpdates();
    }        
    
    public void excluir() throws DataException {        
	ISession s = getSession();
        try {		    
    	    excluir(s);
	        s.commit();
	} catch (DataException de) {
	    s.rollback();
	    throw de;
	} finally {
        closeSession(s);
	}
    }

    public void excluir(ISession session) throws DataException {
        beforeExcluir(session);
        scCrmDisparo.setSession(session);        
        scCrmDisparo.applyUpdates();                
        afterExcluir(session);
        tbDisparo.commitUpdates();
        tbDisparoChatbot.commitUpdates();
    }

    public void excluiTableMaster() throws DataException {
        
        tbDisparoChatbot.first();
        while (!tbDisparoChatbot.eof()) {
            excluir46001();
        }

        tbDisparo.delete();

    }

    public void abreTabelaAux() throws DataException {
        ISession s = getSession();
        try {                
            setSession(s);
            tbEmailModelo.close();
            tbEmailModelo.open();
            tbCadastroWhatsapp.setOrderBy("DESCRICAO");
            tbCadastroWhatsapp.close();
            tbCadastroWhatsapp.open();
            tbDisparo.refreshRecord();
        } finally {
            closeSession(s); 
        }        
    }



    public void setOperRN(TableState oper)  {
        operRN = oper; 
    }

    public void salvar() throws DataException {
	ISession s = getSession();		
        try {                 
            salvar(s);
            s.commit();
        } catch (DataException de) {
            alterar();
            s.rollback();
            throw de;
        } finally {
            closeSession(s);
        }
    }

    public void salvar(ISession session) throws DataException {
         setPKFK();
         validaDados();
         beforeSalvar(session);
         scCrmDisparo.setSession(session);        
         scCrmDisparo.applyUpdates();                
         afterSalvar(session);
         tbDisparo.commitUpdates();
        tbDisparoChatbot.commitUpdates();
    }

    public void beforeExcluir(ISession session) throws DataException {        
        
    }

    public void afterExcluir(ISession session) throws DataException {        
        
    }

    public void beforeSalvar(ISession session) throws DataException {        
        
    }

    public void afterSalvar(ISession session) throws DataException {        
        
    }

    public void validaDados() throws DataException {        
        validaCrmDisparo();
        validaCrmDisparoChatbot();
    }
    
    public void setPKFK() throws DataException {
        if (tbDisparo.getID_DISPARO().isNull()) {
            tbDisparo.edit();
            tbDisparo.setID_DISPARO(SequenceUtil.nextVal("SEQ_CRM_DISPARO"));
            tbDisparo.post();
        }
        tbDisparoChatbot.first();
        while (!tbDisparoChatbot.eof()) {
            if(tbDisparoChatbot.getID_DISPARO().isNull()) { 
                tbDisparoChatbot.edit();
                tbDisparoChatbot.setID_DISPARO(tbDisparo.getID_DISPARO());
                tbDisparoChatbot.post();
            }
            tbDisparoChatbot.next();
        }
    }

    // este metodo facilita nas rotinas de processamento ja passando o session para todas as tabelas
    // envolvidas no processamento. 
    public void setSession(ISession session) throws DataException {
        tbDisparo.setSession(session);
        tbDisparoChatbot.setSession(session);
        tbCadastroWhatsapp.setSession(session);
        tbEmailModelo.setSession(session);
        tbDescartes.setSession(session);
    }        
    
    
    public void validaCrmDisparo() throws DataException {
        if (tbDisparo.isEmpty()) return;

        if (tbDisparo.getID_DISPARO().isNull()) {
             throw new DataException("Campo Id. Disparo de Disparo é de preenchimento obrigatório");
        }

        if (tbDisparo.getDESCRICAO().isNull()) {
             throw new DataException("Campo Descrição de Disparo é de preenchimento obrigatório");
        }

        if (tbDisparo.getTEMPLATE().isNull()) {
             throw new DataException("Campo Template de Disparo é de preenchimento obrigatório");
        }

        if (tbDisparo.getSISTEMA().isNull()) {
             throw new DataException("Campo Sistema de Disparo é de preenchimento obrigatório");
        }
    }

    public void validaCrmDisparoChatbot() throws DataException {
        if (tbDisparoChatbot.isEmpty()) return;

        tbDisparoChatbot.first();
        while (!tbDisparoChatbot.eof()) {
            if (tbDisparoChatbot.getRowState() == RowState.INSERTED || tbDisparoChatbot.getRowState() == RowState.MODIFIED) {

                if (tbDisparoChatbot.getDESCRICAO_FIXA().isNull()) {
                     throw new DataException("Campo Descrição Fixa de Disparo Chatbot é de preenchimento obrigatório");
                }
            }
            tbDisparoChatbot.next();
        }
    }

       
    /**
     * Metodo inlcui um novo registro tabela tbDisparoChatbot
     * @throws DataException
     */    
    public void incluir46001() throws DataException {
        tbDisparoChatbot.append();
        tbDisparoChatbot.post();            // grid editavel precisa do post para mostrar a nova linha
        tbDisparoChatbot.edit();
        
        setPKFK46001();
        tbDisparoChatbot.post();       // grid editavel precisa do post para mostrar a linha
    }

    /**
     * Metodo altera registro tabela tbDisparoChatbot
     * @throws DataException
     */        
    public void alterar46001() throws DataException {
        if (!tbDisparoChatbot.isEmpty()) {
            tbDisparoChatbot.edit();    
        }
    }        

    /**
     * Metodo exclui registro tabela tbDisparoChatbot
     * @throws DataException
     */            
    public void excluir46001() throws DataException {
        try {
            if (!tbDisparoChatbot.isEmpty()) {
                tbDisparoChatbot.delete();
            } 
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo cancela registro tabela tbDisparoChatbot
     * @throws DataException
     */                
    public void cancelar46001() throws DataException {
        try {
            tbDisparoChatbot.cancel();
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }
    
    /**
     * Metodo confirma registro tabela tbDisparoChatbot
     * @throws DataException
     */                    
    public void confirmar46001() throws DataException {
        try {
            tbDisparoChatbot.post();
        } catch (DataException e) {
            throw new DataException(e.getMessage());
        }
    }

    /**
     * Metodo SetPkFk tabela tbDisparoChatbot
     * @throws DataException
     */                        
    public void setPKFK46001() throws DataException {
        tbDisparoChatbot.edit();    
        
        tbDisparoChatbot.setID_DISPARO(tbDisparo.getID_DISPARO());
    }    

}

