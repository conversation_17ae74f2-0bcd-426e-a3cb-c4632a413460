package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PesquisaAvancadaAgentesFuncaoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PesquisaAvancadaAgentesFuncaoRN() {
        init_tbDepartamentos();
        init_tbEmpresas();
        init_tbDivisoes();
    }

    public LISTA_EMPRESAS_DEPARTAMENTOS tbDepartamentos = new LISTA_EMPRESAS_DEPARTAMENTOS("tbDepartamentos");

    private void init_tbDepartamentos() {
        tbDepartamentos.setName("tbDepartamentos");
        tbDepartamentos.setMaxRowCount(200);
        tbDepartamentos.setWKey("37501219;37502");
        tbDepartamentos.setRatioBatchSize(20);
    }

    public BUSCA_EMPRESAS tbEmpresas = new BUSCA_EMPRESAS("tbEmpresas");

    private void init_tbEmpresas() {
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("37501219;37503");
        tbEmpresas.setRatioBatchSize(20);
    }

    public BUSCA_EMPRESAS_DIVISOES tbDivisoes = new BUSCA_EMPRESAS_DIVISOES("tbDivisoes");

    private void init_tbDivisoes() {
        tbDivisoes.setName("tbDivisoes");
        tbDivisoes.setMaxRowCount(200);
        tbDivisoes.setWKey("37501219;37504");
        tbDivisoes.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}