/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.18
   Class Main  : CadastroIndicadores
   Analista    : JOANDERSON.GUARIM
   Data Created: 25/04/2024 08:37:04
   Data Changed: 25/04/2024 10:10:52
   Data Geracao: 25/04/2024 10:12:32
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CadastroIndicadoresRNW extends CadastroIndicadoresRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



