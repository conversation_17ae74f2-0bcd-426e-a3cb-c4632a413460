/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.4
   Class Main  : ClientesFlagsServEmp
   Analista    : JOANDERSON.GUARIM
   Data Created: 27/09/2021 12:54:07
   Data Changed: 05/10/2021 14:39:38
   Data Geracao: 05/10/2021 14:39:56
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ClientesFlagsServEmpRN<PERSON> extends ClientesFlagsServEmpRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



