/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.4
   Class Main  : ClientesFlagsServAcesso
   Analista    : JOANDERSON.GUARIM
   Data Created: 30/09/2021 09:19:33
   Data Changed: 30/09/2021 10:36:58
   Data Geracao: 30/09/2021 10:37:34
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ClientesFlagsServAcessoRNW extends ClientesFlagsServAcessoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



