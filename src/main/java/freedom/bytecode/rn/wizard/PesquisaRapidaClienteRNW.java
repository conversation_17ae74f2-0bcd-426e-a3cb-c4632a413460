/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.11
   Class Main  : PesquisaRapidaCliente
   Analista    : SILVARAFA
   Data Created: 04/12/2023 17:28:06
   Data Changed: 04/12/2023 17:31:12
   Data Geracao: 04/12/2023 17:31:22
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class PesquisaRapidaClienteRNW extends PesquisaRapidaClienteRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



