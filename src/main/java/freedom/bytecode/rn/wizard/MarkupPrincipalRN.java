package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class MarkupPrincipalRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public MarkupPrincipalRN() {
        init_tbMarkupModelo();
        init_tbMarkup();
        init_tbMarkupTipo();
        init_tbMarkup1();
        init_tbParmFluxo();
        init_sc();
    }

    public CP_MARKUP_MODELO tbMarkupModelo = new CP_MARKUP_MODELO("tbMarkupModelo");

    private void init_tbMarkupModelo() {
        tbMarkupModelo.setName("tbMarkupModelo");
        tbMarkupModelo.setMaxRowCount(200);
        tbMarkupModelo.setWKey("4600650;46004");
        tbMarkupModelo.setRatioBatchSize(20);
    }

    public CP_MARKUP tbMarkup = new CP_MARKUP("tbMarkup");

    private void init_tbMarkup() {
        tbMarkup.setName("tbMarkup");
        tbMarkup.setMaxRowCount(200);
        tbMarkup.setWKey("4600650;46002");
        tbMarkup.setRatioBatchSize(20);
    }

    public CP_MARKUP_TIPO tbMarkupTipo = new CP_MARKUP_TIPO("tbMarkupTipo");

    private void init_tbMarkupTipo() {
        tbMarkupTipo.setName("tbMarkupTipo");
        tbMarkupTipo.setMaxRowCount(200);
        tbMarkupTipo.setWKey("4600650;46003");
        tbMarkupTipo.setRatioBatchSize(20);
    }

    public CP_MARKUP tbMarkup1 = new CP_MARKUP("tbMarkup1");

    private void init_tbMarkup1() {
        tbMarkup1.setName("tbMarkup1");
        tbMarkup1.setMaxRowCount(200);
        tbMarkup1.setWKey("4600650;46005");
        tbMarkup1.setRatioBatchSize(20);
    }

    public CRM_PARM_FLUXO tbParmFluxo = new CRM_PARM_FLUXO("tbParmFluxo");

    private void init_tbParmFluxo() {
        tbParmFluxo.setName("tbParmFluxo");
        tbParmFluxo.setMaxRowCount(200);
        tbParmFluxo.setWKey("4600650;46007");
        tbParmFluxo.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item3 = new TFSchemaItem();
        item3.setTable(tbMarkup);
        sc.getTables().add(item3);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}