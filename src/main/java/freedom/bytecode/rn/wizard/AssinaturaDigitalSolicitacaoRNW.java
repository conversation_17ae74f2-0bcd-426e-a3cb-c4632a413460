/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : AssinaturaDigitalSolicitacao
   Analista    : LUIZ.RIPARDO
   Data Created: 30/12/1899 00:00:00
   Data Changed: 07/11/2024 12:44:35
   Data Geracao: 07/11/2024 12:48:00
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AssinaturaDigitalSolicitacaoRNW extends AssinaturaDigitalSolicitacaoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



