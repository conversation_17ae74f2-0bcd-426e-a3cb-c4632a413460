/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : MensagemGenerica
   Analista    : LUIZ.RIPARDO
   Data Created: 12/06/2025 10:24:36
   Data Changed: 12/06/2025 11:00:11
   Data Geracao: 12/06/2025 11:00:32
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class MensagemGenericaRNW extends MensagemGenericaRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



