package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class ControleAcessoGeralRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public ControleAcessoGeralRN() {
        init_tbControleAcesso();
        init_tbEmpresasFuncoes();
        init_tbSistemaAcessoFuncao();
        init_tbPerfilUsuarios();
        init_tbEmpresasUsuarios();
        init_tbUsuarioFoto();
        init_sc();
    }

    public CONTROLE_ACESSO tbControleAcesso = new CONTROLE_ACESSO("tbControleAcesso");

    private void init_tbControleAcesso() {
        tbControleAcesso.setName("tbControleAcesso");
        tbControleAcesso.setMaxRowCount(0);
        tbControleAcesso.setWKey("7000188;70002");
        tbControleAcesso.setRatioBatchSize(20);
    }

    public EMPRESAS_FUNCOES tbEmpresasFuncoes = new EMPRESAS_FUNCOES("tbEmpresasFuncoes");

    private void init_tbEmpresasFuncoes() {
        tbEmpresasFuncoes.setName("tbEmpresasFuncoes");
        tbEmpresasFuncoes.setMaxRowCount(0);
        tbEmpresasFuncoes.setWKey("7000188;70003");
        tbEmpresasFuncoes.setRatioBatchSize(20);
    }

    public SISTEMA_ACESSO_FUNCAO tbSistemaAcessoFuncao = new SISTEMA_ACESSO_FUNCAO("tbSistemaAcessoFuncao");

    private void init_tbSistemaAcessoFuncao() {
        tbSistemaAcessoFuncao.setName("tbSistemaAcessoFuncao");
        tbSistemaAcessoFuncao.setMaxRowCount(0);
        tbSistemaAcessoFuncao.setWKey("7000188;70004");
        tbSistemaAcessoFuncao.setRatioBatchSize(20);
    }

    public PERFIL tbPerfilUsuarios = new PERFIL("tbPerfilUsuarios");

    private void init_tbPerfilUsuarios() {
        tbPerfilUsuarios.setName("tbPerfilUsuarios");
        tbPerfilUsuarios.setMaxRowCount(0);
        tbPerfilUsuarios.setWKey("7000188;70005");
        tbPerfilUsuarios.setRatioBatchSize(20);
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(0);
        tbEmpresasUsuarios.setWKey("7000188;70006");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public USUARIO_FOTO tbUsuarioFoto = new USUARIO_FOTO("tbUsuarioFoto");

    private void init_tbUsuarioFoto() {
        tbUsuarioFoto.setName("tbUsuarioFoto");
        tbUsuarioFoto.setMaxRowCount(0);
        tbUsuarioFoto.setWKey("7000188;70007");
        tbUsuarioFoto.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbSistemaAcessoFuncao);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbEmpresasUsuarios);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbUsuarioFoto);
        sc.getTables().add(item2);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}