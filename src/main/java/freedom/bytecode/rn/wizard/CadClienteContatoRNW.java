/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.35
   Class Main  : CadClienteContato
   Analista    : GIORDANNY
   Data Created: 30/12/1899 00:00:00
   Data Changed: 06/09/2024 14:11:20
   Data Geracao: 06/09/2024 14:11:27
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CadClienteContatoRNW extends CadClienteContatoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



