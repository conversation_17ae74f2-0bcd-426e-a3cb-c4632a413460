/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.3.27
   Class Main  : AlterarEnderecoCliente
   Analista    : RAMIRES
   Data Created: 29/08/2019 15:51:40
   Data Changed: 30/08/2019 14:22:16
   Data Geracao: 30/08/2019 15:20:57
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AlterarEnderecoClienteRNW extends AlterarEnderecoClienteRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



