/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.13
   Class Main  : AguardarPagamentoPosSitef
   Analista    : ROGERIOK
   Data Created: 07/12/2023 16:21:45
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 07/12/2023 16:31:12
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AguardarPagamentoPosSitefRNW extends AguardarPagamentoPosSitefRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



