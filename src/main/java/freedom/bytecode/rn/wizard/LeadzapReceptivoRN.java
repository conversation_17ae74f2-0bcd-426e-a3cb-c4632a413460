package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class LeadzapReceptivoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public LeadzapReceptivoRN() {
        init_tbLeadzapItem();
        init_tbLeadzapArea();
        init_tbLeadzapMenu();
        init_tbTemplateQualificado();
        init_tbTemplateLead();
        init_tbTime();
        init_tbEventosTipo();
        init_tbLeadzapTemMembrosTime();
        init_tbLeadzapSetorMembroTime();
        init_tbTemplateLeadAprov();
        init_tbTemplateLeadReprov();
        init_tbCadastroWhatsapp();
        init_tbWhatsappEmpresa();
        init_tbReceptTimesMembrosEmpUser();
    }

    public CRM_LEADZAP_ITEM tbLeadzapItem = new CRM_LEADZAP_ITEM("tbLeadzapItem");

    private void init_tbLeadzapItem() {
        tbLeadzapItem.setName("tbLeadzapItem");
        tbLeadzapItem.setMaxRowCount(200);
        tbLeadzapItem.setWKey("4600506;46001");
        tbLeadzapItem.setRatioBatchSize(20);
    }

    public CRM_LEADZAP_AREA tbLeadzapArea = new CRM_LEADZAP_AREA("tbLeadzapArea");

    private void init_tbLeadzapArea() {
        tbLeadzapArea.setName("tbLeadzapArea");
        tbLeadzapArea.setMaxRowCount(200);
        tbLeadzapArea.setWKey("4600506;46002");
        tbLeadzapArea.setRatioBatchSize(20);
    }

    public CRM_LEADZAP_MENU tbLeadzapMenu = new CRM_LEADZAP_MENU("tbLeadzapMenu");

    private void init_tbLeadzapMenu() {
        tbLeadzapMenu.setName("tbLeadzapMenu");
        tbLeadzapMenu.setMaxRowCount(200);
        tbLeadzapMenu.setWKey("4600506;46003");
        tbLeadzapMenu.setRatioBatchSize(20);
    }

    public CRM_EMAIL_MODELO tbTemplateQualificado = new CRM_EMAIL_MODELO("tbTemplateQualificado");

    private void init_tbTemplateQualificado() {
        tbTemplateQualificado.setName("tbTemplateQualificado");
        tbTemplateQualificado.setMaxRowCount(200);
        tbTemplateQualificado.setWKey("4600506;46004");
        tbTemplateQualificado.setRatioBatchSize(20);
    }

    public CRM_EMAIL_MODELO tbTemplateLead = new CRM_EMAIL_MODELO("tbTemplateLead");

    private void init_tbTemplateLead() {
        tbTemplateLead.setName("tbTemplateLead");
        tbTemplateLead.setMaxRowCount(200);
        tbTemplateLead.setWKey("4600506;46005");
        tbTemplateLead.setRatioBatchSize(20);
    }

    public CRM_TIME tbTime = new CRM_TIME("tbTime");

    private void init_tbTime() {
        tbTime.setName("tbTime");
        tbTime.setMaxRowCount(200);
        tbTime.setWKey("4600506;46006");
        tbTime.setRatioBatchSize(20);
    }

    public CRM_EVENTOS_TIPO tbEventosTipo = new CRM_EVENTOS_TIPO("tbEventosTipo");

    private void init_tbEventosTipo() {
        tbEventosTipo.setName("tbEventosTipo");
        tbEventosTipo.setMaxRowCount(200);
        tbEventosTipo.setWKey("4600506;46007");
        tbEventosTipo.setRatioBatchSize(20);
    }

    public LEADZAP_TEM_MEMBROS_TIME tbLeadzapTemMembrosTime = new LEADZAP_TEM_MEMBROS_TIME("tbLeadzapTemMembrosTime");

    private void init_tbLeadzapTemMembrosTime() {
        tbLeadzapTemMembrosTime.setName("tbLeadzapTemMembrosTime");
        tbLeadzapTemMembrosTime.setMaxRowCount(200);
        tbLeadzapTemMembrosTime.setWKey("4600506;46008");
        tbLeadzapTemMembrosTime.setRatioBatchSize(20);
    }

    public LEADZAP_SETOR_MEMBRO_TIME tbLeadzapSetorMembroTime = new LEADZAP_SETOR_MEMBRO_TIME("tbLeadzapSetorMembroTime");

    private void init_tbLeadzapSetorMembroTime() {
        tbLeadzapSetorMembroTime.setName("tbLeadzapSetorMembroTime");
        tbLeadzapSetorMembroTime.setMaxRowCount(200);
        tbLeadzapSetorMembroTime.setWKey("4600506;46009");
        tbLeadzapSetorMembroTime.setRatioBatchSize(20);
    }

    public CRM_EMAIL_MODELO tbTemplateLeadAprov = new CRM_EMAIL_MODELO("tbTemplateLeadAprov");

    private void init_tbTemplateLeadAprov() {
        tbTemplateLeadAprov.setName("tbTemplateLeadAprov");
        tbTemplateLeadAprov.setMaxRowCount(200);
        tbTemplateLeadAprov.setWKey("4600506;460010");
        tbTemplateLeadAprov.setRatioBatchSize(20);
    }

    public CRM_EMAIL_MODELO tbTemplateLeadReprov = new CRM_EMAIL_MODELO("tbTemplateLeadReprov");

    private void init_tbTemplateLeadReprov() {
        tbTemplateLeadReprov.setName("tbTemplateLeadReprov");
        tbTemplateLeadReprov.setMaxRowCount(200);
        tbTemplateLeadReprov.setWKey("4600506;460011");
        tbTemplateLeadReprov.setRatioBatchSize(20);
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp = new CRM_CADASTRO_WHATSAPP("tbCadastroWhatsapp");

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("4600506;460012");
        tbCadastroWhatsapp.setRatioBatchSize(20);
    }

    public CRM_WHATSAPP_EMPRESA tbWhatsappEmpresa = new CRM_WHATSAPP_EMPRESA("tbWhatsappEmpresa");

    private void init_tbWhatsappEmpresa() {
        tbWhatsappEmpresa.setName("tbWhatsappEmpresa");
        tbWhatsappEmpresa.setMaxRowCount(200);
        tbWhatsappEmpresa.setWKey("4600506;460013");
        tbWhatsappEmpresa.setRatioBatchSize(20);
    }

    public RECEPT_TIMES_MEMBROS_EMP_USER tbReceptTimesMembrosEmpUser = new RECEPT_TIMES_MEMBROS_EMP_USER("tbReceptTimesMembrosEmpUser");

    private void init_tbReceptTimesMembrosEmpUser() {
        tbReceptTimesMembrosEmpUser.setName("tbReceptTimesMembrosEmpUser");
        tbReceptTimesMembrosEmpUser.setMaxRowCount(200);
        tbReceptTimesMembrosEmpUser.setWKey("4600506;460014");
        tbReceptTimesMembrosEmpUser.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}