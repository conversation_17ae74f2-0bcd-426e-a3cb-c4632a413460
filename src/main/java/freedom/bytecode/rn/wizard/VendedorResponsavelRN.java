package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class VendedorResponsavelRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public VendedorResponsavelRN() {
        init_tbEmpresasCadastro();
        init_tbEmpresasUsuarios();
        init_tbLeadsVendedores();
        init_tbListaClienteResponsavel();
        init_tbClienteResponsavel();
        init_tbVendedoresListagem();
        init_tbEmpresasListagem();
        init_tbVendedoresCadastro();
        init_scClienteResponsavel();
    }

    public LEADS_EMPRESAS_USUARIOS tbEmpresasCadastro = new LEADS_EMPRESAS_USUARIOS("tbEmpresasCadastro");

    private void init_tbEmpresasCadastro() {
        tbEmpresasCadastro.setName("tbEmpresasCadastro");
        tbEmpresasCadastro.setMaxRowCount(200);
        tbEmpresasCadastro.setWKey("284017;28401");
        tbEmpresasCadastro.setRatioBatchSize(20);
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("284017;28403");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public LEADS_VENDEDORES tbLeadsVendedores = new LEADS_VENDEDORES("tbLeadsVendedores");

    private void init_tbLeadsVendedores() {
        tbLeadsVendedores.setName("tbLeadsVendedores");
        tbLeadsVendedores.setMaxRowCount(200);
        tbLeadsVendedores.setWKey("284017;28404");
        tbLeadsVendedores.setRatioBatchSize(20);
    }

    public LISTA_CLIENTE_RESPONSAVEL tbListaClienteResponsavel = new LISTA_CLIENTE_RESPONSAVEL("tbListaClienteResponsavel");

    private void init_tbListaClienteResponsavel() {
        tbListaClienteResponsavel.setName("tbListaClienteResponsavel");
        tbListaClienteResponsavel.setMaxRowCount(200);
        tbListaClienteResponsavel.setWKey("284017;28405");
        tbListaClienteResponsavel.setRatioBatchSize(20);
    }

    public CLIENTE_RESPONSAVEL tbClienteResponsavel = new CLIENTE_RESPONSAVEL("tbClienteResponsavel");

    private void init_tbClienteResponsavel() {
        tbClienteResponsavel.setName("tbClienteResponsavel");
        tbClienteResponsavel.setMaxRowCount(200);
        tbClienteResponsavel.setWKey("284017;28407");
        tbClienteResponsavel.setRatioBatchSize(20);
    }

    public LISTAR_VENDEDORES tbVendedoresListagem = new LISTAR_VENDEDORES("tbVendedoresListagem");

    private void init_tbVendedoresListagem() {
        tbVendedoresListagem.setName("tbVendedoresListagem");
        tbVendedoresListagem.setMaxRowCount(200);
        tbVendedoresListagem.setWKey("284017;28408");
        tbVendedoresListagem.setRatioBatchSize(20);
    }

    public LEADS_EMPRESAS_USUARIOS tbEmpresasListagem = new LEADS_EMPRESAS_USUARIOS("tbEmpresasListagem");

    private void init_tbEmpresasListagem() {
        tbEmpresasListagem.setName("tbEmpresasListagem");
        tbEmpresasListagem.setMaxRowCount(200);
        tbEmpresasListagem.setWKey("284017;42201");
        tbEmpresasListagem.setRatioBatchSize(20);
    }

    public LISTAR_VENDEDORES tbVendedoresCadastro = new LISTAR_VENDEDORES("tbVendedoresCadastro");

    private void init_tbVendedoresCadastro() {
        tbVendedoresCadastro.setName("tbVendedoresCadastro");
        tbVendedoresCadastro.setMaxRowCount(200);
        tbVendedoresCadastro.setWKey("284017;42203");
        tbVendedoresCadastro.setRatioBatchSize(20);
    }


    public TFSchema scClienteResponsavel = new TFSchema();

    private void init_scClienteResponsavel() {
        scClienteResponsavel.setName("scClienteResponsavel");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteResponsavel);
        scClienteResponsavel.getTables().add(item0);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}