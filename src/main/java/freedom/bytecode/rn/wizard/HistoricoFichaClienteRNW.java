/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : HistoricoFichaCliente
   Analista    : LUIZ.RIPARDO
   Data Created: 25/05/2023 09:51:10
   Data Changed: 26/05/2023 13:23:46
   Data Geracao: 26/05/2023 13:35:45
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class HistoricoFichaClienteRNW extends HistoricoFichaClienteRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



