package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CEPClienteOnLineRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CEPClienteOnLineRN() {
        init_tbClientesEndereco();
        init_tbClientesEnderecoIe();
        init_tbClientesEnderecoTemp();
        init_tbClientesEnderecoIeTemp();
        init_tbCidades();
        init_sc();
    }

    public CLIENTES_ENDERECO tbClientesEndereco = new CLIENTES_ENDERECO("tbClientesEndereco");

    private void init_tbClientesEndereco() {
        tbClientesEndereco.setName("tbClientesEndereco");
        tbClientesEndereco.setMaxRowCount(0);
        tbClientesEndereco.setWKey("4600193;46001");
        tbClientesEndereco.setRatioBatchSize(20);
    }

    public CLIENTES_ENDERECO_IE tbClientesEnderecoIe = new CLIENTES_ENDERECO_IE("tbClientesEnderecoIe");

    private void init_tbClientesEnderecoIe() {
        tbClientesEnderecoIe.setName("tbClientesEnderecoIe");
        tbClientesEnderecoIe.setMaxRowCount(0);
        tbClientesEnderecoIe.setWKey("4600193;46002");
        tbClientesEnderecoIe.setRatioBatchSize(20);
    }

    public DUAL tbClientesEnderecoTemp = new DUAL("tbClientesEnderecoTemp");

    private void init_tbClientesEnderecoTemp() {
        tbClientesEnderecoTemp.setName("tbClientesEnderecoTemp");
        TFTableField item76 = new TFTableField();
        item76.setName("COD_CLIENTE");
        item76.setCalculated(true);
        item76.setUpdatable(false);
        item76.setPrimaryKey(false);
        item76.setFieldType("ftInteger");
        item76.setJSONConfigNullOnEmpty(false);
        item76.setCaption("COD_CLIENTE");
        tbClientesEnderecoTemp.getFieldDefs().add(item76);
        TFTableField item77 = new TFTableField();
        item77.setName("CEP_RES");
        item77.setCalculated(true);
        item77.setUpdatable(false);
        item77.setPrimaryKey(false);
        item77.setFieldType("ftString");
        item77.setJSONConfigNullOnEmpty(false);
        item77.setCaption("CEP_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item77);
        TFTableField item78 = new TFTableField();
        item78.setName("UF_RES");
        item78.setCalculated(true);
        item78.setUpdatable(false);
        item78.setPrimaryKey(false);
        item78.setFieldType("ftString");
        item78.setJSONConfigNullOnEmpty(false);
        item78.setCaption("UF_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item78);
        TFTableField item79 = new TFTableField();
        item79.setName("COD_CID_RES");
        item79.setCalculated(true);
        item79.setUpdatable(false);
        item79.setPrimaryKey(false);
        item79.setFieldType("ftInteger");
        item79.setJSONConfigNullOnEmpty(false);
        item79.setCaption("COD_CID_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item79);
        TFTableField item80 = new TFTableField();
        item80.setName("RUA_RES");
        item80.setCalculated(true);
        item80.setUpdatable(false);
        item80.setPrimaryKey(false);
        item80.setFieldType("ftString");
        item80.setJSONConfigNullOnEmpty(false);
        item80.setCaption("RUA_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item80);
        TFTableField item81 = new TFTableField();
        item81.setName("FACHADA_RES");
        item81.setCalculated(true);
        item81.setUpdatable(false);
        item81.setPrimaryKey(false);
        item81.setFieldType("ftString");
        item81.setJSONConfigNullOnEmpty(false);
        item81.setCaption("FACHADA_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item81);
        TFTableField item82 = new TFTableField();
        item82.setName("BAIRRO_RES");
        item82.setCalculated(true);
        item82.setUpdatable(false);
        item82.setPrimaryKey(false);
        item82.setFieldType("ftString");
        item82.setJSONConfigNullOnEmpty(false);
        item82.setCaption("BAIRRO_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item82);
        TFTableField item83 = new TFTableField();
        item83.setName("COMPLEMENTO_RES");
        item83.setCalculated(true);
        item83.setUpdatable(false);
        item83.setPrimaryKey(false);
        item83.setFieldType("ftString");
        item83.setJSONConfigNullOnEmpty(false);
        item83.setCaption("COMPLEMENTO_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item83);
        TFTableField item84 = new TFTableField();
        item84.setName("CEP_COM");
        item84.setCalculated(true);
        item84.setUpdatable(false);
        item84.setPrimaryKey(false);
        item84.setFieldType("ftString");
        item84.setJSONConfigNullOnEmpty(false);
        item84.setCaption("CEP_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item84);
        TFTableField item85 = new TFTableField();
        item85.setName("UF_COM");
        item85.setCalculated(true);
        item85.setUpdatable(false);
        item85.setPrimaryKey(false);
        item85.setFieldType("ftString");
        item85.setJSONConfigNullOnEmpty(false);
        item85.setCaption("UF_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item85);
        TFTableField item86 = new TFTableField();
        item86.setName("COD_CID_COM");
        item86.setCalculated(true);
        item86.setUpdatable(false);
        item86.setPrimaryKey(false);
        item86.setFieldType("ftInteger");
        item86.setJSONConfigNullOnEmpty(false);
        item86.setCaption("COD_CID_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item86);
        TFTableField item87 = new TFTableField();
        item87.setName("RUA_COM");
        item87.setCalculated(true);
        item87.setUpdatable(false);
        item87.setPrimaryKey(false);
        item87.setFieldType("ftString");
        item87.setJSONConfigNullOnEmpty(false);
        item87.setCaption("RUA_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item87);
        TFTableField item88 = new TFTableField();
        item88.setName("FACHADA_COM");
        item88.setCalculated(true);
        item88.setUpdatable(false);
        item88.setPrimaryKey(false);
        item88.setFieldType("ftString");
        item88.setJSONConfigNullOnEmpty(false);
        item88.setCaption("FACHADA_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item88);
        TFTableField item89 = new TFTableField();
        item89.setName("BAIRRO_COM");
        item89.setCalculated(true);
        item89.setUpdatable(false);
        item89.setPrimaryKey(false);
        item89.setFieldType("ftString");
        item89.setJSONConfigNullOnEmpty(false);
        item89.setCaption("BAIRRO_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item89);
        TFTableField item90 = new TFTableField();
        item90.setName("COMPLEMENTO_COM");
        item90.setCalculated(true);
        item90.setUpdatable(false);
        item90.setPrimaryKey(false);
        item90.setFieldType("ftString");
        item90.setJSONConfigNullOnEmpty(false);
        item90.setCaption("COMPLEMENTO_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item90);
        TFTableField item91 = new TFTableField();
        item91.setName("CEP_COBRANCA");
        item91.setCalculated(true);
        item91.setUpdatable(false);
        item91.setPrimaryKey(false);
        item91.setFieldType("ftString");
        item91.setJSONConfigNullOnEmpty(false);
        item91.setCaption("CEP_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item91);
        TFTableField item92 = new TFTableField();
        item92.setName("UF_COBRANCA");
        item92.setCalculated(true);
        item92.setUpdatable(false);
        item92.setPrimaryKey(false);
        item92.setFieldType("ftString");
        item92.setJSONConfigNullOnEmpty(false);
        item92.setCaption("UF_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item92);
        TFTableField item93 = new TFTableField();
        item93.setName("COD_CID_COBRANCA");
        item93.setCalculated(true);
        item93.setUpdatable(false);
        item93.setPrimaryKey(false);
        item93.setFieldType("ftInteger");
        item93.setJSONConfigNullOnEmpty(false);
        item93.setCaption("COD_CID_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item93);
        TFTableField item94 = new TFTableField();
        item94.setName("RUA_COBRANCA");
        item94.setCalculated(true);
        item94.setUpdatable(false);
        item94.setPrimaryKey(false);
        item94.setFieldType("ftString");
        item94.setJSONConfigNullOnEmpty(false);
        item94.setCaption("RUA_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item94);
        TFTableField item95 = new TFTableField();
        item95.setName("FACHADA_COBRANCA");
        item95.setCalculated(true);
        item95.setUpdatable(false);
        item95.setPrimaryKey(false);
        item95.setFieldType("ftString");
        item95.setJSONConfigNullOnEmpty(false);
        item95.setCaption("FACHADA_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item95);
        TFTableField item96 = new TFTableField();
        item96.setName("BAIRRO_COBRANCA");
        item96.setCalculated(true);
        item96.setUpdatable(false);
        item96.setPrimaryKey(false);
        item96.setFieldType("ftString");
        item96.setJSONConfigNullOnEmpty(false);
        item96.setCaption("BAIRRO_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item96);
        TFTableField item97 = new TFTableField();
        item97.setName("COMPLEMENTO_COBRANCA");
        item97.setCalculated(true);
        item97.setUpdatable(false);
        item97.setPrimaryKey(false);
        item97.setFieldType("ftString");
        item97.setJSONConfigNullOnEmpty(false);
        item97.setCaption("COMPLEMENTO_COBRANCA");
        tbClientesEnderecoTemp.getFieldDefs().add(item97);
        TFTableField item98 = new TFTableField();
        item98.setName("CIDADE_RES");
        item98.setCalculated(true);
        item98.setUpdatable(false);
        item98.setPrimaryKey(false);
        item98.setFieldType("ftString");
        item98.setJSONConfigNullOnEmpty(false);
        item98.setCaption("CIDADE_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item98);
        TFTableField item99 = new TFTableField();
        item99.setName("CIDADE_COB");
        item99.setCalculated(true);
        item99.setUpdatable(false);
        item99.setPrimaryKey(false);
        item99.setFieldType("ftString");
        item99.setJSONConfigNullOnEmpty(false);
        item99.setCaption("CIDADE_COB");
        tbClientesEnderecoTemp.getFieldDefs().add(item99);
        TFTableField item100 = new TFTableField();
        item100.setName("CIDADE_COM");
        item100.setCalculated(true);
        item100.setUpdatable(false);
        item100.setPrimaryKey(false);
        item100.setFieldType("ftString");
        item100.setJSONConfigNullOnEmpty(false);
        item100.setCaption("CIDADE_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item100);
        TFTableField item101 = new TFTableField();
        item101.setName("CODCIDADEIBGE_RES");
        item101.setCalculated(true);
        item101.setUpdatable(false);
        item101.setPrimaryKey(false);
        item101.setFieldType("ftInteger");
        item101.setJSONConfigNullOnEmpty(false);
        item101.setCaption("CODCIDADEIBGE_RES");
        tbClientesEnderecoTemp.getFieldDefs().add(item101);
        TFTableField item102 = new TFTableField();
        item102.setName("CODCIDADEIBGE_COM");
        item102.setCalculated(true);
        item102.setUpdatable(false);
        item102.setPrimaryKey(false);
        item102.setFieldType("ftInteger");
        item102.setJSONConfigNullOnEmpty(false);
        item102.setCaption("CODCIDADEIBGE_COM");
        tbClientesEnderecoTemp.getFieldDefs().add(item102);
        TFTableField item103 = new TFTableField();
        item103.setName("CODCIDADEIBGE_COB");
        item103.setCalculated(true);
        item103.setUpdatable(false);
        item103.setPrimaryKey(false);
        item103.setFieldType("ftInteger");
        item103.setJSONConfigNullOnEmpty(false);
        item103.setCaption("CODCIDADEIBGE_COB");
        tbClientesEnderecoTemp.getFieldDefs().add(item103);
        tbClientesEnderecoTemp.setMaxRowCount(0);
        tbClientesEnderecoTemp.setWKey("4600193;46004");
        tbClientesEnderecoTemp.setRatioBatchSize(20);
    }

    public DUAL tbClientesEnderecoIeTemp = new DUAL("tbClientesEnderecoIeTemp");

    private void init_tbClientesEnderecoIeTemp() {
        tbClientesEnderecoIeTemp.setName("tbClientesEnderecoIeTemp");
        TFTableField item104 = new TFTableField();
        item104.setName("COD_CLIENTE");
        item104.setCalculated(true);
        item104.setUpdatable(false);
        item104.setPrimaryKey(false);
        item104.setFieldType("ftInteger");
        item104.setJSONConfigNullOnEmpty(false);
        item104.setCaption("COD_CLIENTE");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item104);
        TFTableField item105 = new TFTableField();
        item105.setName("INSCRICAO_ESTADUAL");
        item105.setCalculated(true);
        item105.setUpdatable(false);
        item105.setPrimaryKey(false);
        item105.setFieldType("ftString");
        item105.setJSONConfigNullOnEmpty(false);
        item105.setCaption("INSCRICAO_ESTADUAL");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item105);
        TFTableField item106 = new TFTableField();
        item106.setName("CEP");
        item106.setCalculated(true);
        item106.setUpdatable(false);
        item106.setPrimaryKey(false);
        item106.setFieldType("ftString");
        item106.setJSONConfigNullOnEmpty(false);
        item106.setCaption("CEP");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item106);
        TFTableField item107 = new TFTableField();
        item107.setName("UF");
        item107.setCalculated(true);
        item107.setUpdatable(false);
        item107.setPrimaryKey(false);
        item107.setFieldType("ftString");
        item107.setJSONConfigNullOnEmpty(false);
        item107.setCaption("UF");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item107);
        TFTableField item108 = new TFTableField();
        item108.setName("RUA");
        item108.setCalculated(true);
        item108.setUpdatable(false);
        item108.setPrimaryKey(false);
        item108.setFieldType("ftString");
        item108.setJSONConfigNullOnEmpty(false);
        item108.setCaption("RUA");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item108);
        TFTableField item109 = new TFTableField();
        item109.setName("CIDADE");
        item109.setCalculated(true);
        item109.setUpdatable(false);
        item109.setPrimaryKey(false);
        item109.setFieldType("ftString");
        item109.setJSONConfigNullOnEmpty(false);
        item109.setCaption("CIDADE");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item109);
        TFTableField item110 = new TFTableField();
        item110.setName("BAIRRO");
        item110.setCalculated(true);
        item110.setUpdatable(false);
        item110.setPrimaryKey(false);
        item110.setFieldType("ftString");
        item110.setJSONConfigNullOnEmpty(false);
        item110.setCaption("BAIRRO");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item110);
        TFTableField item111 = new TFTableField();
        item111.setName("COMPLEMENTO");
        item111.setCalculated(true);
        item111.setUpdatable(false);
        item111.setPrimaryKey(false);
        item111.setFieldType("ftString");
        item111.setJSONConfigNullOnEmpty(false);
        item111.setCaption("COMPLEMENTO");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item111);
        TFTableField item112 = new TFTableField();
        item112.setName("FACHADA");
        item112.setCalculated(true);
        item112.setUpdatable(false);
        item112.setPrimaryKey(false);
        item112.setFieldType("ftString");
        item112.setJSONConfigNullOnEmpty(false);
        item112.setCaption("FACHADA");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item112);
        TFTableField item113 = new TFTableField();
        item113.setName("COD_CIDADES");
        item113.setCalculated(true);
        item113.setUpdatable(false);
        item113.setPrimaryKey(false);
        item113.setFieldType("ftInteger");
        item113.setJSONConfigNullOnEmpty(false);
        item113.setCaption("COD_CIDADES");
        tbClientesEnderecoIeTemp.getFieldDefs().add(item113);
        tbClientesEnderecoIeTemp.setMaxRowCount(200);
        tbClientesEnderecoIeTemp.setWKey("4600193;46005");
        tbClientesEnderecoIeTemp.setRatioBatchSize(20);
    }

    public CIDADES tbCidades = new CIDADES("tbCidades");

    private void init_tbCidades() {
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("4600193;46006");
        tbCidades.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClientesEndereco);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbClientesEnderecoIe);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbCidades);
        sc.getTables().add(item2);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}