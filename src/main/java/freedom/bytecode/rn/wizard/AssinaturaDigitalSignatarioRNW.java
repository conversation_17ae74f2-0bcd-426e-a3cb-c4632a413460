/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : AssinaturaDigitalSignatario
   Analista    : LUIZ.RIPARDO
   Data Created: 25/10/2024 10:23:19
   Data Changed: 25/10/2024 10:26:03
   Data Geracao: 25/10/2024 10:35:03
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AssinaturaDigitalSignatarioRNW extends AssinaturaDigitalSignatarioRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



