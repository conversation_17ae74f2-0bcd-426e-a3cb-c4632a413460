/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.81
   Class Main  : LimiteFinanceiro
   Analista    : ROGERIOK
   Data Created: 25/01/2023 19:31:32
   Data Changed: 25/01/2023 19:36:37
   Data Geracao: 25/01/2023 19:41:51
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class LimiteFinanceiroRNW extends LimiteFinanceiroRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



