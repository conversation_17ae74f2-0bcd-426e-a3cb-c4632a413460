/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.95
   Class Main  : AdicMargMinMarkupCliEmp
   Analista    : GIORDANNY
   Data Created: 08/05/2023 15:31:48
   Data Changed: 08/05/2023 16:24:39
   Data Geracao: 08/05/2023 16:24:46
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AdicMargMinMarkupCliEmpRN<PERSON> extends AdicMargMinMarkupCliEmpRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



