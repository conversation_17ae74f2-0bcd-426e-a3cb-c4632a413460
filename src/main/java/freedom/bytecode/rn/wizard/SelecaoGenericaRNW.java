/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : SelecaoGenerica
   Analista    : LUIZ.RIPARDO
   Data Created: 10/04/2024 10:26:29
   Data Changed: 11/04/2024 16:11:56
   Data Geracao: 11/04/2024 16:12:03
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class SelecaoGenericaRNW extends SelecaoGenericaRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



