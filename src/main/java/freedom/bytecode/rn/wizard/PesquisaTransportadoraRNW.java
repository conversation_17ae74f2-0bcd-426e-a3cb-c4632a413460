/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.95
   Class Main  : PesquisaTransportadora
   Analista    : GIORDANNY
   Data Created: 10/05/2023 13:23:54
   Data Changed: 10/05/2023 16:46:35
   Data Geracao: 10/05/2023 16:46:43
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class PesquisaTransportadoraRNW extends PesquisaTransportadoraRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



