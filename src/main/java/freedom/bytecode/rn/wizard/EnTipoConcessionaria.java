package freedom.bytecode.rn.wizard;

import freedom.data.DataException;
import freedom.util.pkg.PkgCrmPartsRNA;

import java.util.HashMap;
import java.util.Map;

public enum EnTipoConcessionaria {
    NAO_CADASTRADO(-1),
    FIAT(1),
    GM(2),
    FORD(3),
    VOLKSWAGEN(4),
    TOYOTA(5),
    CASE(6),
    SCANIA(7),
    RENAULT(8),
    HONDA_MOTO(9),
    OUTROS(10),
    YAMAHA(11),
    HONDA_AUTO(12),
    MITSUBISH(13),
    PEUGEOT(14),
    BMW(15),
    MERCEDES(16),
    NOMA(17),
    SUZUKI_MOTOS(18),
    IVEC<PERSON>(19),
    MASSEY_FERGUSSON(20),
    CITROEN(21),
    <PERSON>DI(22),
    SUNDOW(23),
    NEW_HOLLAND(24),
    VOLKS_CAMINHOES(25),
    <PERSON><PERSON>(26),
    <PERSON><PERSON><PERSON><PERSON>(27),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(28),
    <PERSON><PERSON><PERSON>(29),
    <PERSON><PERSON><PERSON>(30),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(31),
    <PERSON><PERSON><PERSON><PERSON><PERSON>DODGERA<PERSON>(32),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(33),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(34),
    <PERSON><PERSON><PERSON>(35),
    SU<PERSON>RU(36),
    FERRARI(37),
    JAC_MOTORS(38),
    CHANA_MOTORS(39),
    JAGUAR(40),
    SSANGYONG(41),
    VOLVO(42),
    JEEP(43),
    MULTIMARCAS(44),
    HYUN<PERSON>I(45),
    LIUGONG(46),
    <PERSON>ARLEYDAVIDSON(47),
    NISSAN(48),
    TRIUMPH(49),
    AUTO_CENTER(50),
    TROLLER(51),
    DUCATI(52),
    ASTON_MARTIN(53),
    LS_MTRON(54),
    TRAXX(55),
    INDIAN(56),
    JCB(57),
    LEXUS(58),
    VALTRA(59),
    FENDT(60),
    DS_AUTOMOBILES(61),
    BYD_AUTOMOVEIS(62),
    BYD_ONIBUS(63),
    HORSCH(64),
    GWM(65),
    ZEEKR(70);


    private final int value;

    EnTipoConcessionaria(int value) {
        this.value = value;
    }

    private static final Map<Integer, EnTipoConcessionaria> map = new HashMap<Integer, EnTipoConcessionaria>();

    static {
        for (EnTipoConcessionaria enTipoRevenda : EnTipoConcessionaria.values()) {
            map.put(enTipoRevenda.value, enTipoRevenda);
        }
    }

    public static EnTipoConcessionaria valueOf(int tipoConcessionaria) {
        return map.getOrDefault(tipoConcessionaria, NAO_CADASTRADO);
    }

    public static EnTipoConcessionaria getTipoConcessionariaPorCodEmpresa(Double codEmpresa) throws DataException {
        PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();
        int tipoConc = Integer.parseInt(pkgCrmPartsRNA.getParametro(codEmpresa, "PARM_SYS", "TIPO_CONCESSIONARIA"));
        return EnTipoConcessionaria.valueOf(tipoConc);
    }

}
