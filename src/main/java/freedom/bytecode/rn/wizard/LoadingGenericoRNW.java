/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : LoadingGenerico
   Analista    : LUIZ.RIPARDO
   Data Created: 25/10/2024 16:23:55
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 25/10/2024 16:24:47
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class LoadingGenericoRNW extends LoadingGenericoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



