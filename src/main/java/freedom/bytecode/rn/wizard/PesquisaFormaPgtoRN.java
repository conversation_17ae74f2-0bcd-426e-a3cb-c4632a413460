package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PesquisaFormaPgtoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PesquisaFormaPgtoRN() {
        init_tbListaFormasPgto();
        init_tbEmpresasFiliaisSel();
        init_tbListaEmpresasDepartamentos();
        init_tbListaFormasPgto1();
        init_tbListaTipoPgtoNbs();
    }

    public LISTA_FORMAS_PGTO tbListaFormasPgto = new LISTA_FORMAS_PGTO("tbListaFormasPgto");

    private void init_tbListaFormasPgto() {
        tbListaFormasPgto.setName("tbListaFormasPgto");
        tbListaFormasPgto.setMaxRowCount(0);
        tbListaFormasPgto.setWKey("5300550;53001");
        tbListaFormasPgto.setRatioBatchSize(20);
    }

    public EMPRESAS_FILIAIS_SEL tbEmpresasFiliaisSel = new EMPRESAS_FILIAIS_SEL("tbEmpresasFiliaisSel");

    private void init_tbEmpresasFiliaisSel() {
        tbEmpresasFiliaisSel.setName("tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.setMaxRowCount(0);
        tbEmpresasFiliaisSel.setWKey("5300550;53002");
        tbEmpresasFiliaisSel.setRatioBatchSize(20);
    }

    public LISTA_EMPRESAS_DEPARTAMENTOS tbListaEmpresasDepartamentos = new LISTA_EMPRESAS_DEPARTAMENTOS("tbListaEmpresasDepartamentos");

    private void init_tbListaEmpresasDepartamentos() {
        tbListaEmpresasDepartamentos.setName("tbListaEmpresasDepartamentos");
        tbListaEmpresasDepartamentos.setMaxRowCount(0);
        tbListaEmpresasDepartamentos.setWKey("5300550;53003");
        tbListaEmpresasDepartamentos.setRatioBatchSize(20);
    }

    public LISTA_FORMAS_PGTO tbListaFormasPgto1 = new LISTA_FORMAS_PGTO("tbListaFormasPgto1");

    private void init_tbListaFormasPgto1() {
        tbListaFormasPgto1.setName("tbListaFormasPgto1");
        tbListaFormasPgto1.setMaxRowCount(0);
        tbListaFormasPgto1.setWKey("5300550;53004");
        tbListaFormasPgto1.setRatioBatchSize(20);
    }

    public LISTA_TIPO_PGTO_NBS tbListaTipoPgtoNbs = new LISTA_TIPO_PGTO_NBS("tbListaTipoPgtoNbs");

    private void init_tbListaTipoPgtoNbs() {
        tbListaTipoPgtoNbs.setName("tbListaTipoPgtoNbs");
        tbListaTipoPgtoNbs.setMaxRowCount(0);
        tbListaTipoPgtoNbs.setWKey("5300550;53005");
        tbListaTipoPgtoNbs.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}