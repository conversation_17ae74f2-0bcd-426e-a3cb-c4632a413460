/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Server - Versao: ********
   Class Main  : MarkupPrincipal
   Analista    : LUIZ.RIPARDO
   Data Created: 09/08/2022 08:24:07
   Data Changed: 12/08/2022 16:00:54
   Data Geracao: 15/08/2022 14:50:09
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class MarkupPrincipalRNW extends MarkupPrincipalRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



