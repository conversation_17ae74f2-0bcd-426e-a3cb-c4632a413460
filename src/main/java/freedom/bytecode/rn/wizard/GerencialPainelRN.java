package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class GerencialPainelRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public GerencialPainelRN() {
        init_tbPainelGerencialUsuario();
        init_tbEmpresaCruzadasFuncao();
        init_tbGridGerencialIndicadores();
        init_tbAno();
        init_tbGraficoIndicadoresMes();
        init_tbGraficoIndicadoresDiario();
        init_tbComboQuebra();
        init_tbSelecaoGenerica();
        init_tbGraficoIndicadoresQuebra();
        init_tbSelecaoGenericaSelecionada();
        init_tbGraficoIndUlt12Mes();
    }

    public PAINEL_GERENCIAL_USUARIO tbPainelGerencialUsuario = new PAINEL_GERENCIAL_USUARIO("tbPainelGerencialUsuario");

    private void init_tbPainelGerencialUsuario() {
        tbPainelGerencialUsuario.setName("tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.setMaxRowCount(200);
        tbPainelGerencialUsuario.setWKey("382032;38203");
        tbPainelGerencialUsuario.setRatioBatchSize(20);
    }

    public EMPRESA_CRUZADAS_FUNCAO tbEmpresaCruzadasFuncao = new EMPRESA_CRUZADAS_FUNCAO("tbEmpresaCruzadasFuncao");

    private void init_tbEmpresaCruzadasFuncao() {
        tbEmpresaCruzadasFuncao.setName("tbEmpresaCruzadasFuncao");
        tbEmpresaCruzadasFuncao.setMaxRowCount(200);
        tbEmpresaCruzadasFuncao.setWKey("382032;38204");
        tbEmpresaCruzadasFuncao.setRatioBatchSize(20);
    }

    public GRID_GERENCIAL_INDICADORES tbGridGerencialIndicadores = new GRID_GERENCIAL_INDICADORES("tbGridGerencialIndicadores");

    private void init_tbGridGerencialIndicadores() {
        tbGridGerencialIndicadores.setName("tbGridGerencialIndicadores");
        tbGridGerencialIndicadores.setMaxRowCount(200);
        tbGridGerencialIndicadores.setWKey("382032;38205");
        tbGridGerencialIndicadores.setRatioBatchSize(20);
    }

    public ANO tbAno = new ANO("tbAno");

    private void init_tbAno() {
        tbAno.setName("tbAno");
        tbAno.setMaxRowCount(200);
        tbAno.setWKey("382032;38206");
        tbAno.setRatioBatchSize(20);
    }

    public GRAFICO_INDICADORES_MES tbGraficoIndicadoresMes = new GRAFICO_INDICADORES_MES("tbGraficoIndicadoresMes");

    private void init_tbGraficoIndicadoresMes() {
        tbGraficoIndicadoresMes.setName("tbGraficoIndicadoresMes");
        tbGraficoIndicadoresMes.setMaxRowCount(200);
        tbGraficoIndicadoresMes.setWKey("382032;38207");
        tbGraficoIndicadoresMes.setRatioBatchSize(20);
    }

    public GRAFICO_INDICADORES_DIARIO tbGraficoIndicadoresDiario = new GRAFICO_INDICADORES_DIARIO("tbGraficoIndicadoresDiario");

    private void init_tbGraficoIndicadoresDiario() {
        tbGraficoIndicadoresDiario.setName("tbGraficoIndicadoresDiario");
        tbGraficoIndicadoresDiario.setMaxRowCount(200);
        tbGraficoIndicadoresDiario.setWKey("382032;38208");
        tbGraficoIndicadoresDiario.setRatioBatchSize(20);
    }

    public BSC_COMBO_QUEBRA tbComboQuebra = new BSC_COMBO_QUEBRA("tbComboQuebra");

    private void init_tbComboQuebra() {
        tbComboQuebra.setName("tbComboQuebra");
        tbComboQuebra.setMaxRowCount(200);
        tbComboQuebra.setWKey("382032;38209");
        tbComboQuebra.setRatioBatchSize(20);
    }

    public SELECAO_GENERICA tbSelecaoGenerica = new SELECAO_GENERICA("tbSelecaoGenerica");

    private void init_tbSelecaoGenerica() {
        tbSelecaoGenerica.setName("tbSelecaoGenerica");
        tbSelecaoGenerica.setMaxRowCount(200);
        tbSelecaoGenerica.setWKey("382032;382010");
        tbSelecaoGenerica.setRatioBatchSize(20);
    }

    public GRAFICO_INDICADORES_QUEBRA tbGraficoIndicadoresQuebra = new GRAFICO_INDICADORES_QUEBRA("tbGraficoIndicadoresQuebra");

    private void init_tbGraficoIndicadoresQuebra() {
        tbGraficoIndicadoresQuebra.setName("tbGraficoIndicadoresQuebra");
        tbGraficoIndicadoresQuebra.setMaxRowCount(200);
        tbGraficoIndicadoresQuebra.setWKey("382032;382011");
        tbGraficoIndicadoresQuebra.setRatioBatchSize(20);
    }

    public SELECAO_GENERICA tbSelecaoGenericaSelecionada = new SELECAO_GENERICA("tbSelecaoGenericaSelecionada");

    private void init_tbSelecaoGenericaSelecionada() {
        tbSelecaoGenericaSelecionada.setName("tbSelecaoGenericaSelecionada");
        tbSelecaoGenericaSelecionada.setMaxRowCount(200);
        tbSelecaoGenericaSelecionada.setWKey("382032;382012");
        tbSelecaoGenericaSelecionada.setRatioBatchSize(20);
    }

    public GRAFICO_IND_ULT_12_MES tbGraficoIndUlt12Mes = new GRAFICO_IND_ULT_12_MES("tbGraficoIndUlt12Mes");

    private void init_tbGraficoIndUlt12Mes() {
        tbGraficoIndUlt12Mes.setName("tbGraficoIndUlt12Mes");
        tbGraficoIndUlt12Mes.setMaxRowCount(200);
        tbGraficoIndUlt12Mes.setWKey("382032;382013");
        tbGraficoIndUlt12Mes.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}