/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.13
   Class Main  : AgrupaPagamentos
   Analista    : SILVARAFA
   Data Created: 27/11/2023 16:19:30
   Data Changed: 17/01/2024 11:58:30
   Data Geracao: 17/01/2024 11:58:41
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AgrupaPagamentosRNW extends AgrupaPagamentosRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



