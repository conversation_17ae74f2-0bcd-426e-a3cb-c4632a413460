/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.5.71
   Class Main  : FichaItemEliminarReserva
   Analista    : ROGERIOK
   Data Created: 21/05/2021 07:45:42
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 21/05/2021 07:48:59
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class FichaItemEliminarReservaRNW extends FichaItemEliminarReservaRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



