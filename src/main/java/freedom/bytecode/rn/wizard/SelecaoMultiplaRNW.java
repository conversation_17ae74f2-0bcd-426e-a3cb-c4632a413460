/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Server - Versao: ********
   Class Main  : SelecaoMultipla
   Analista    : GIORDANNY
   Data Created: 18/08/2022 14:01:30
   Data Changed: 18/08/2022 15:11:49
   Data Geracao: 19/08/2022 13:39:46
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class SelecaoMultiplaRNW extends SelecaoMultiplaRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



