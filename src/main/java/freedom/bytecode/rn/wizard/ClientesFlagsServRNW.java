/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.4
   Class Main  : ClientesFlagsServ
   Analista    : JOANDERSON.GUARIM
   Data Created: 16/09/2021 16:59:28
   Data Changed: 30/09/2021 16:41:26
   Data Geracao: 30/09/2021 16:41:52
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ClientesFlagsServRNW extends ClientesFlagsServRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



