/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.43
   Class Main  : ControleAcessoGeral
   Analista    : GIORDANNY
   Data Created: 30/12/1899 00:00:00
   Data Changed: 02/06/2025 10:11:40
   Data Geracao: 02/06/2025 10:11:47
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ControleAcessoGeralRNW extends ControleAcessoGeralRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



