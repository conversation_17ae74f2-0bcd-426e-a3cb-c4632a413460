/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.44
   Class Main  : AdicionarDescLetraCliente
   Analista    : GIORDANNY
   Data Created: 01/08/2022 10:23:31
   Data Changed: 02/08/2022 13:16:30
   Data Geracao: 03/08/2022 15:40:49
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AdicionarDescLetraClienteRN<PERSON> extends AdicionarDescLetraClienteRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



