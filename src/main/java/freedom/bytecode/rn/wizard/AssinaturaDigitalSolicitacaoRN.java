package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class AssinaturaDigitalSolicitacaoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public AssinaturaDigitalSolicitacaoRN() {
        init_tbSolicitacoesAssinaturas();
        init_tbNbsapiEnvelopeFilaResumo();
        init_tbAssinaturaDigital();
        init_tbNbsapiDocumentos();
        init_tbAssinaturaDigitalDoc();
        init_tbNbsapiDocumentosAssinados();
        init_tbNbsapiEnvelopeFilaRank();
    }

    public SOLICITACOES_ASSINATURAS tbSolicitacoesAssinaturas = new SOLICITACOES_ASSINATURAS("tbSolicitacoesAssinaturas");

    private void init_tbSolicitacoesAssinaturas() {
        tbSolicitacoesAssinaturas.setName("tbSolicitacoesAssinaturas");
        TFTableField item15 = new TFTableField();
        item15.setName("EDITAR");
        item15.setCalculated(true);
        item15.setUpdatable(false);
        item15.setPrimaryKey(false);
        item15.setFieldType("ftString");
        item15.setJSONConfigNullOnEmpty(false);
        item15.setCaption("EDITAR");
        tbSolicitacoesAssinaturas.getFieldDefs().add(item15);
        TFTableField item16 = new TFTableField();
        item16.setName("VER");
        item16.setCalculated(true);
        item16.setUpdatable(false);
        item16.setPrimaryKey(false);
        item16.setFieldType("ftString");
        item16.setJSONConfigNullOnEmpty(false);
        item16.setCaption("VER");
        tbSolicitacoesAssinaturas.getFieldDefs().add(item16);
        TFTableField item17 = new TFTableField();
        item17.setName("ASSINAR");
        item17.setCalculated(true);
        item17.setUpdatable(false);
        item17.setPrimaryKey(false);
        item17.setFieldType("ftString");
        item17.setJSONConfigNullOnEmpty(false);
        item17.setCaption("ASSINAR");
        tbSolicitacoesAssinaturas.getFieldDefs().add(item17);
        tbSolicitacoesAssinaturas.setMaxRowCount(200);
        tbSolicitacoesAssinaturas.setWKey("45506;45501");
        tbSolicitacoesAssinaturas.setRatioBatchSize(20);
    }

    public NBSAPI_ENVELOPE_FILA_RESUMO tbNbsapiEnvelopeFilaResumo = new NBSAPI_ENVELOPE_FILA_RESUMO("tbNbsapiEnvelopeFilaResumo");

    private void init_tbNbsapiEnvelopeFilaResumo() {
        tbNbsapiEnvelopeFilaResumo.setName("tbNbsapiEnvelopeFilaResumo");
        tbNbsapiEnvelopeFilaResumo.setMaxRowCount(200);
        tbNbsapiEnvelopeFilaResumo.setWKey("45506;45503");
        tbNbsapiEnvelopeFilaResumo.setRatioBatchSize(20);
    }

    public CRM_ASSINATURA_DIGITAL tbAssinaturaDigital = new CRM_ASSINATURA_DIGITAL("tbAssinaturaDigital");

    private void init_tbAssinaturaDigital() {
        tbAssinaturaDigital.setName("tbAssinaturaDigital");
        tbAssinaturaDigital.setMaxRowCount(200);
        tbAssinaturaDigital.setWKey("45506;45504");
        tbAssinaturaDigital.setRatioBatchSize(20);
    }

    public NBSAPI_DOCUMENTOS tbNbsapiDocumentos = new NBSAPI_DOCUMENTOS("tbNbsapiDocumentos");

    private void init_tbNbsapiDocumentos() {
        tbNbsapiDocumentos.setName("tbNbsapiDocumentos");
        tbNbsapiDocumentos.setMaxRowCount(200);
        tbNbsapiDocumentos.setWKey("45506;45505");
        tbNbsapiDocumentos.setRatioBatchSize(20);
    }

    public CRM_ASSINATURA_DIGITAL_DOC tbAssinaturaDigitalDoc = new CRM_ASSINATURA_DIGITAL_DOC("tbAssinaturaDigitalDoc");

    private void init_tbAssinaturaDigitalDoc() {
        tbAssinaturaDigitalDoc.setName("tbAssinaturaDigitalDoc");
        tbAssinaturaDigitalDoc.setMaxRowCount(200);
        tbAssinaturaDigitalDoc.setWKey("45506;45506");
        tbAssinaturaDigitalDoc.setRatioBatchSize(20);
    }

    public NBSAPI_DOCUMENTOS_ASSINADOS tbNbsapiDocumentosAssinados = new NBSAPI_DOCUMENTOS_ASSINADOS("tbNbsapiDocumentosAssinados");

    private void init_tbNbsapiDocumentosAssinados() {
        tbNbsapiDocumentosAssinados.setName("tbNbsapiDocumentosAssinados");
        tbNbsapiDocumentosAssinados.setMaxRowCount(200);
        tbNbsapiDocumentosAssinados.setWKey("45506;45507");
        tbNbsapiDocumentosAssinados.setRatioBatchSize(20);
    }

    public NBSAPI_ENVELOPE_FILA_RANK tbNbsapiEnvelopeFilaRank = new NBSAPI_ENVELOPE_FILA_RANK("tbNbsapiEnvelopeFilaRank");

    private void init_tbNbsapiEnvelopeFilaRank() {
        tbNbsapiEnvelopeFilaRank.setName("tbNbsapiEnvelopeFilaRank");
        tbNbsapiEnvelopeFilaRank.setMaxRowCount(200);
        tbNbsapiEnvelopeFilaRank.setWKey("45506;47408");
        tbNbsapiEnvelopeFilaRank.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}