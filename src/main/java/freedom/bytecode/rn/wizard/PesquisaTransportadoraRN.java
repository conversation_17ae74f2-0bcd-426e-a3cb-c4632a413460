package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PesquisaTransportadoraRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PesquisaTransportadoraRN() {
        init_tbTransportadoras();
        init_scTransportadoras();
    }

    public TRANSPORTADORAS tbTransportadoras = new TRANSPORTADORAS("tbTransportadoras");

    private void init_tbTransportadoras() {
        tbTransportadoras.setName("tbTransportadoras");
        tbTransportadoras.setMaxRowCount(200);
        tbTransportadoras.setWKey("1610882;16101");
        tbTransportadoras.setRatioBatchSize(20);
    }


    public TFSchema scTransportadoras = new TFSchema();

    private void init_scTransportadoras() {
        scTransportadoras.setName("scTransportadoras");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbTransportadoras);
        scTransportadoras.getTables().add(item0);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}