/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : AssinaturaDigitalVer
   Analista    : LUIZ.RIPARDO
   Data Created: 02/10/2024 14:12:21
   Data Changed: 04/10/2024 13:03:41
   Data Geracao: 04/10/2024 13:03:48
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AssinaturaDigitalVerRNW extends AssinaturaDigitalVerRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



