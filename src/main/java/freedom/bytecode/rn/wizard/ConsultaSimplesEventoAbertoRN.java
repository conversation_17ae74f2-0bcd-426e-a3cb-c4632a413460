package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class ConsultaSimplesEventoAbertoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public ConsultaSimplesEventoAbertoRN() {
        init_tbCrmpartsConsultaEvento();
    }

    public CRMPARTS_CONSULTA_EVENTO tbCrmpartsConsultaEvento = new CRMPARTS_CONSULTA_EVENTO("tbCrmpartsConsultaEvento");

    private void init_tbCrmpartsConsultaEvento() {
        tbCrmpartsConsultaEvento.setName("tbCrmpartsConsultaEvento");
        tbCrmpartsConsultaEvento.setMaxRowCount(200);
        tbCrmpartsConsultaEvento.setWKey("7000149;70001");
        tbCrmpartsConsultaEvento.setDeltaMode("dmChanged");
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}