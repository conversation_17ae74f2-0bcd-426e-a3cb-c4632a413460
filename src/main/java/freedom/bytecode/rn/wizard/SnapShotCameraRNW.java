/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.91
   Class Main  : SnapShotCamera
   Analista    : JOANDERSON.GUARIM
   Data Created: 30/12/1899 00:00:00
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 01/06/2023 08:52:43
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class SnapShotCameraRNW extends SnapShotCameraRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



