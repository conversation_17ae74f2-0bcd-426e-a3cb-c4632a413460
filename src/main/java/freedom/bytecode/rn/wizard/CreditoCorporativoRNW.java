/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.0
   Class Main  : CreditoCorporativo
   Analista    : SILVARAFA
   Data Created: 15/06/2023 15:31:51
   Data Changed: 16/06/2023 11:20:19
   Data Geracao: 16/06/2023 11:20:29
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CreditoCorporativoRNW extends CreditoCorporativoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



