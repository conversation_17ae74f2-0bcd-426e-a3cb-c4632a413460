/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.4.52
   Class Main  : ConsultaCidade
   Analista    : RAMIRES
   Data Created: 03/04/2020 13:54:19
   Data Changed: 03/04/2020 14:50:39
   Data Geracao: 03/04/2020 15:17:04
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ConsultaCidadeRNW extends ConsultaCidadeRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



