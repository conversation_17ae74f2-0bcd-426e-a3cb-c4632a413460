package freedom.bytecode.rn.wizard;
import freedom.bytecode.cursor.CRMPARTS_NFE_MOVIMENTO;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import freedom.util.assinaturaDigital.strategy.TipoAssinaturaStrategy;
import org.apache.commons.lang.WordUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

public enum EnTipoAssinaturaEmpresas{
    OS_TERMO_LGPD("OS_TERMO_LGPD", "B0284",""){
        @Override
        public JSONObject getJsonParametrosFromCodigoValores(String stringCodigoValores) {
            String[] listaParametros = stringCodigoValores.split(";");
            return getJsonParametros(Double.valueOf(listaParametros[0]), Double.valueOf(listaParametros[1]));
        }

        @Override
        public JSONObject getJsonParametros(Object... parametros) {
            JSONObject jsonRetorno = new JSONObject();
            if (parametros == null || parametros.length < 2) {
                throw new IllegalArgumentException("Os parâmetros 'COD_CLIENTE' e 'COD_EMPRESA' são obrigatórios.");
            }
            if (!(parametros[0] instanceof Number)) {
                throw new IllegalArgumentException("O primeiro parâmetro 'COD_CLIENTE' deve ser um número (int, Integer, double, Double, etc.).");
            }
            if (!(parametros[1] instanceof Number)) {
                throw new IllegalArgumentException("O segundo parâmetro 'COD_EMPRESA' deve ser um número (int, Integer, etc.).");
            }

            jsonRetorno.put("COD_CLIENTE", BigDecimal.valueOf(((Number) parametros[0]).doubleValue()).toBigInteger());
            jsonRetorno.put("COD_EMPRESA", Integer.valueOf(((Number) parametros[1]).intValue()));
            return jsonRetorno;
        }

        @Override
        public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String StringCodigoValores) {
            return new ArrayList<>();
        }
    },
    NFE_CANHOTO("NFE_CANHOTO", "B0285", ""){
        @Override
        public JSONObject getJsonParametrosFromCodigoValores(String StringCodigoValores) {
            String[] listaParametros = StringCodigoValores.split(";");
            return getJsonParametros(Double.valueOf(listaParametros[0]), Double.valueOf(listaParametros[1]), listaParametros[2]);
        }

        @Override
        public JSONObject getJsonParametros(Object... parametros) {
            JSONObject jsonRetorno = new JSONObject();
            if (parametros == null || parametros.length < 3) {
                throw new IllegalArgumentException("Os parâmetros 'CONTROLE', 'COD_EMPRESA' e SERIE são obrigatórios.");
            }
            if (!(parametros[0] instanceof Number)) {
                throw new IllegalArgumentException("O primeiro parâmetro 'CONTROLE' deve ser um número (int, Integer, double, Double, etc.).");
            }
            if (!(parametros[1] instanceof Number)) {
                throw new IllegalArgumentException("O segundo parâmetro 'COD_EMPRESA' deve ser um número (int, Integer, etc.).");
            }
            if (!(parametros[2] instanceof String)) {
                throw new IllegalArgumentException("O Terceiro parâmetro 'SERIE' deve ser um texto (String, etc.).");
            }
            jsonRetorno.put("CONTROLE", BigDecimal.valueOf(((Number) parametros[0]).doubleValue()).toBigInteger());
            jsonRetorno.put("COD_EMPRESA", Integer.valueOf(((Number) parametros[1]).intValue()));
            jsonRetorno.put("SERIE",  parametros[2]);
            return jsonRetorno;
        }

        @Override
        public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String StringCodigoValores) throws DataException {
            CRMPARTS_NFE_MOVIMENTO tbCrmpartsNfeMovimentoTemp = getCrmpartsNfeMovimento(StringCodigoValores);
            boolean podeEmitirNfe = !tbCrmpartsNfeMovimentoTemp.isEmpty();
            boolean temPDF = tbCrmpartsNfeMovimentoTemp.getTEM_PDF().asString().equals("S");
            if (!podeEmitirNfe) {
                throw new DataException("NFe ainda não disponível para assinatura digital.");
            }
            if(!temPDF){
                throw new DataException("Arquivo NFe(Pdf) não gerado!");
            }
            List<CrmAssinaturaDigitalDocumento> listaDocumentos = new ArrayList<>();
            CrmAssinaturaDigitalDocumento documento = new CrmAssinaturaDigitalDocumento("nfe_canhoto.pdf", tbCrmpartsNfeMovimentoTemp.getPDF_NOTA().toString(), "#CLIENTE");
            listaDocumentos.add(documento);
            return listaDocumentos;
        }

        @NotNull
        private CRMPARTS_NFE_MOVIMENTO getCrmpartsNfeMovimento(String StringCodigoValores) throws DataException {
            JSONObject jsonParametros = getJsonParametrosFromCodigoValores(StringCodigoValores);
            CRMPARTS_NFE_MOVIMENTO tbCrmpartsNfeMovimentoTemp = new CRMPARTS_NFE_MOVIMENTO("tbCrmpartsNfeMovimentoTemp");
            tbCrmpartsNfeMovimentoTemp.close();
            tbCrmpartsNfeMovimentoTemp.clearFilters();
            tbCrmpartsNfeMovimentoTemp.clearParams();
            tbCrmpartsNfeMovimentoTemp.setFilterCOD_EMPRESA(jsonParametros.get("COD_EMPRESA"));
            tbCrmpartsNfeMovimentoTemp.setFilterCONTROLE(jsonParametros.get("CONTROLE"));
            tbCrmpartsNfeMovimentoTemp.setFilterSERIE(jsonParametros.get("SERIE"));
            tbCrmpartsNfeMovimentoTemp.open();
            return tbCrmpartsNfeMovimentoTemp;
        }
    },
    OS_ABERTURA("OS_ABERTURA", "", ""){
        @Override
        public JSONObject getJsonParametrosFromCodigoValores(String stringCodigoValores) {
            String[] listaParametros = stringCodigoValores.split(";");
            return getJsonParametros(Double.valueOf(listaParametros[0]), Double.valueOf(listaParametros[1]));
        }
        @Override
        public JSONObject getJsonParametros(Object... parametros) {
            JSONObject jsonRetorno = new JSONObject();
            if (parametros == null || parametros.length < 2) {
                throw new IllegalArgumentException("Os parâmetros 'NUMERO_OS' e 'COD_EMPRESA' são obrigatórios.");
            }
            if (!(parametros[0] instanceof Number)) {
                throw new IllegalArgumentException("O primeiro parâmetro 'NUMERO_OS' deve ser um número (int, Integer, double, Double, etc.).");
            }
            if (!(parametros[1] instanceof Number)) {
                throw new IllegalArgumentException("O segundo parâmetro 'COD_EMPRESA' deve ser um número (int, Integer, etc.).");
            }
            jsonRetorno.put("NUMERO_OS", BigDecimal.valueOf(((Number) parametros[0]).doubleValue()).toBigInteger());
            jsonRetorno.put("COD_EMPRESA", Integer.valueOf(((Number) parametros[1]).intValue()));
            /* parametros para o relatorio jasper */
            try {
                EnTipoConcessionaria enTipoConcessionaria = EnTipoConcessionaria.getTipoConcessionariaPorCodEmpresa(((Number) jsonRetorno.get("COD_EMPRESA")).doubleValue());
                switch (enTipoConcessionaria) {
                    case CITROEN:
                    case PEUGEOT:
                        jsonRetorno.put("MARCA", enTipoConcessionaria.name());
                        break;
                }
            } catch (DataException ignored) {}
            return jsonRetorno;
        }

        @Override
        public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException {
            return new ArrayList<>();
        }
    },
    OS_CHECKLIST_RECEPCAO("OS_CHECKLIST_RECEPCAO", "", ""){
        @Override
        public JSONObject getJsonParametros(Object... parametros) {
            JSONObject jsonRetorno = new JSONObject();
            EnTipoConcessionaria tipoConcessionaria;
            String tituloCabecalho = "";

            if (parametros == null || parametros.length < 4) {
                throw new IllegalArgumentException("Os parâmetros 'NUMERO_OS' e 'COD_EMPRESA' são obrigatórios.");
            }
            if (!(parametros[0] instanceof Number)) {
                throw new IllegalArgumentException("O primeiro parâmetro 'NUMERO_OS' deve ser um número (int, Integer, double, Double, etc.).");
            }
            if (!(parametros[1] instanceof Number)) {
                throw new IllegalArgumentException("O segundo parâmetro 'COD_EMPRESA' deve ser um número (int, Integer, etc.).");
            }
            if (!(parametros[2] instanceof String)) {
                throw new IllegalArgumentException("O terceiro parâmetro 'TIPO' deve ser um texto (String, etc.).");
            }
            if (!(parametros[3] instanceof String)) {
                throw new IllegalArgumentException("O quarto parâmetro 'APLICACAO' deve ser um texto (String, etc.).");
            }

            try {
                tipoConcessionaria = EnTipoConcessionaria.getTipoConcessionariaPorCodEmpresa(((Number) parametros[1]).doubleValue());
                if (Objects.requireNonNull(tipoConcessionaria) == EnTipoConcessionaria.MERCEDES) {
                    switch (parametros[3].toString()) {
                        //defini um titulo para o cabeçalho para mercedes
                        case "R": //recepção checkIn
                            tituloCabecalho = "Ficha de Entrada";
                            break;
                        case "O": //oficina checkin
                            tituloCabecalho = "Check List Técnico";
                            break;
                    }
                }
            } catch (DataException ignored) {
            }
            jsonRetorno.put("NUMERO_OS", BigDecimal.valueOf(((Number) parametros[0]).doubleValue()).toBigInteger());
            jsonRetorno.put("COD_EMPRESA", Integer.valueOf(((Number) parametros[1]).intValue()));
            jsonRetorno.put("TIPO",parametros[2]);
            jsonRetorno.put("APLICACAO", parametros[3]);
            /* parametros para o relatorio jasper */
            jsonRetorno.put("IMP_FOTO_CHK_LIST", "S");
            jsonRetorno.put("IMP_ITEM_AGRUPADO_FOTO", "S");
            jsonRetorno.put("COD_SERVICO", "E");
            jsonRetorno.put("TITULO_CABECALHO", tituloCabecalho);
            jsonRetorno.put("MODALIDADE", "OS");
            jsonRetorno.put("COD_AGENDA", "0");
            return jsonRetorno;
        }

        @Override
        public JSONObject getJsonParametrosFromCodigoValores(String stringCodigoValores) {
            String[] listaParametros = stringCodigoValores.split(";");
            return getJsonParametros(Integer.valueOf(listaParametros[0]), Integer.valueOf(listaParametros[1]), listaParametros[2], listaParametros[3]);
        }

        @Override
        public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException {
            return new ArrayList<>();
        }
    },
    OS_CHECKLIST_OFICINA("OS_CHECKLIST_OFICINA", "", ""){
        @Override
        public JSONObject getJsonParametros(Object... parametros) {
            JSONObject jsonRetorno = new JSONObject();
            EnTipoConcessionaria tipoConcessionaria;
            String tituloCabecalho = "";

            if (parametros == null || parametros.length < 4) {
                throw new IllegalArgumentException("Os parâmetros 'NUMERO_OS' e 'COD_EMPRESA' são obrigatórios.");
            }
            if (!(parametros[0] instanceof Number)) {
                throw new IllegalArgumentException("O primeiro parâmetro 'NUMERO_OS' deve ser um número (int, Integer, double, Double, etc.).");
            }
            if (!(parametros[1] instanceof Number)) {
                throw new IllegalArgumentException("O segundo parâmetro 'COD_EMPRESA' deve ser um número (int, Integer, etc.).");
            }
            if (!(parametros[2] instanceof String)) {
                throw new IllegalArgumentException("O terceiro parâmetro 'TIPO' deve ser um texto (String, etc.).");
            }
            if (!(parametros[3] instanceof String)) {
                throw new IllegalArgumentException("O quarto parâmetro 'APLICACAO' deve ser um texto (String, etc.).");
            }

            try {
                tipoConcessionaria = EnTipoConcessionaria.getTipoConcessionariaPorCodEmpresa(((Number) parametros[1]).doubleValue());
                if (Objects.requireNonNull(tipoConcessionaria) == EnTipoConcessionaria.MERCEDES) {
                    switch (parametros[3].toString()) {
                        //defini um titulo para o cabeçalho para mercedes
                        case "R": //recepção checkIn
                            tituloCabecalho = "Ficha de Entrada";
                            break;
                        case "O": //oficina checkin
                            tituloCabecalho = "Check List Técnico";
                            break;
                    }
                }
            } catch (DataException ignored) {
            }
            jsonRetorno.put("NUMERO_OS", BigDecimal.valueOf(((Number) parametros[0]).doubleValue()).toBigInteger());
            jsonRetorno.put("COD_EMPRESA", Integer.valueOf(((Number) parametros[1]).intValue()));
            jsonRetorno.put("TIPO",parametros[2]);
            jsonRetorno.put("APLICACAO", parametros[3]);
            /* parametros para o relatorio jasper */
            jsonRetorno.put("IMP_FOTO_CHK_LIST", "S");
            jsonRetorno.put("IMP_ITEM_AGRUPADO_FOTO", "S");
            jsonRetorno.put("COD_SERVICO", "E");
            jsonRetorno.put("TITULO_CABECALHO", tituloCabecalho);
            jsonRetorno.put("MODALIDADE", "OS");
            jsonRetorno.put("COD_AGENDA", "0");
            return jsonRetorno;
        }

        @Override
        public JSONObject getJsonParametrosFromCodigoValores(String stringCodigoValores) {
            String[] listaParametros = stringCodigoValores.split(";");
            return getJsonParametros(Integer.valueOf(listaParametros[0]), Integer.valueOf(listaParametros[1]), listaParametros[2], listaParametros[3]);
        }

        @Override
        public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException {
            return new ArrayList<>();
        }
    },
    OS_CHECKLIST_ENTREGA("OS_CHECKLIST_ENTREGA", "", ""){
        @Override
        public JSONObject getJsonParametros(Object... parametros) {
            JSONObject jsonRetorno = new JSONObject();
            EnTipoConcessionaria tipoConcessionaria;
            String tituloCabecalho = "";

            if (parametros == null || parametros.length < 4) {
                throw new IllegalArgumentException("Os parâmetros 'NUMERO_OS' e 'COD_EMPRESA' são obrigatórios.");
            }
            if (!(parametros[0] instanceof Number)) {
                throw new IllegalArgumentException("O primeiro parâmetro 'NUMERO_OS' deve ser um número (int, Integer, double, Double, etc.).");
            }
            if (!(parametros[1] instanceof Number)) {
                throw new IllegalArgumentException("O segundo parâmetro 'COD_EMPRESA' deve ser um número (int, Integer, etc.).");
            }
            if (!(parametros[2] instanceof String)) {
                throw new IllegalArgumentException("O terceiro parâmetro 'TIPO' deve ser um texto (String, etc.).");
            }
            if (!(parametros[3] instanceof String)) {
                throw new IllegalArgumentException("O quarto parâmetro 'APLICACAO' deve ser um texto (String, etc.).");
            }

            try {
                tipoConcessionaria = EnTipoConcessionaria.getTipoConcessionariaPorCodEmpresa(((Number) parametros[1]).doubleValue());
                if (Objects.requireNonNull(tipoConcessionaria) == EnTipoConcessionaria.MERCEDES) {
                    switch (parametros[3].toString()) {
                        //defini um titulo para o cabeçalho para mercedes
                        case "R": //recepção checkIn
                            tituloCabecalho = "Ficha de Entrada";
                            break;
                        case "O": //oficina checkin
                            tituloCabecalho = "Check List Técnico";
                            break;
                    }
                }
            } catch (DataException ignored) {
            }
            jsonRetorno.put("NUMERO_OS", BigDecimal.valueOf(((Number) parametros[0]).doubleValue()).toBigInteger());
            jsonRetorno.put("COD_EMPRESA", Integer.valueOf(((Number) parametros[1]).intValue()));
            jsonRetorno.put("TIPO",parametros[2]);
            jsonRetorno.put("APLICACAO", parametros[3]);
            /* parametros para o relatorio jasper */
            jsonRetorno.put("IMP_FOTO_CHK_LIST", "S");
            jsonRetorno.put("IMP_ITEM_AGRUPADO_FOTO", "S");
            jsonRetorno.put("COD_SERVICO", "E");
            jsonRetorno.put("TITULO_CABECALHO", tituloCabecalho);
            jsonRetorno.put("MODALIDADE", "OS");
            jsonRetorno.put("COD_AGENDA", "0");
            return jsonRetorno;
        }

        @Override
        public JSONObject getJsonParametrosFromCodigoValores(String stringCodigoValores) {
            String[] listaParametros = stringCodigoValores.split(";");
            return getJsonParametros(Integer.valueOf(listaParametros[0]), Integer.valueOf(listaParametros[1]), listaParametros[2], listaParametros[3]);
        }
        @Override
        public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException {
            return new ArrayList<>();
        }
    };

    final String tipoAssinatura;
    final String acessoCrmService;
    final String acessoCrmParts;
    EnTipoAssinaturaEmpresas(String tipoAssinatura, String acessoCrmService, String acessoCrmParts) {
        this.tipoAssinatura = tipoAssinatura;
        this.acessoCrmService = acessoCrmService;
        this.acessoCrmParts = acessoCrmParts;
    }

    /**
     * Retorna o tipo de assinatura
     * @return retorna o tipo de assinatura
     */
    public String getDescricao() {
        return this.tipoAssinatura;
    }

    /**
     * json de parametros para abrir a assinatura
     * @param parametros
     * @return retorna um json de parametros para abrir a assinatura
     */
    public abstract JSONObject getJsonParametros(Object... parametros);

    /**
     * Json de parametros montado apartir do codigo de valores
     * @param stringCodigoValores
     * @return json com os valores necessários para abrir a assinatura
     */
    public abstract JSONObject getJsonParametrosFromCodigoValores(String stringCodigoValores);

    /**
     * documentos avulso que devem ser adicionados no envelope da assinatura
     * @param stringCodigoValores
     * @return
     * @throws DataException
     */
    public abstract List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException;

    /**
     * Retorna o tipo de assinatura
     * @param tipoAssinatura
     * @return retorna o tipo de assinatura
     */
    public static EnTipoAssinaturaEmpresas getTipoAssinatura(String tipoAssinatura) {
        for (EnTipoAssinaturaEmpresas assinatura : EnTipoAssinaturaEmpresas.values()) {
            if (assinatura.getDescricao().equals(tipoAssinatura)) {
                return assinatura;
            }
        }
        return null;
    }

    /**
     * valida o acesso para assinatura digital do tipo informado
     * @return retorna se tem acesso, obs: retorna true se não tiver um codigo de acesso
     */
    public boolean validarAcessoTipoAssinatura() {
        String codAcesso = EmpresaUtil.isCrmService() ? this.acessoCrmService : this.acessoCrmParts;
        if (!codAcesso.isEmpty() && !EmpresaUtil.validarAcesso(codAcesso,false)){
            EmpresaUtil.showMessage("Permissão", "Sem acesso para assinatura digital do tipo: " + WordUtils.capitalizeFully(this.tipoAssinatura.replace("_"," ")) + ". ACESSO {"+ codAcesso +"}");
            return false;
        }
        return true;
    }
}
