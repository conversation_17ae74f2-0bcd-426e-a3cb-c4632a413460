/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.42
   Class Main  : BloquearDesbloquearItemCompra
   Analista    : GIORDANNY
   Data Created: 01/04/2025 16:22:21
   Data Changed: 02/04/2025 10:24:23
   Data Geracao: 02/04/2025 10:24:27
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class BloquearDesbloquearItemCompraRNW extends BloquearDesbloquearItemCompraRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



