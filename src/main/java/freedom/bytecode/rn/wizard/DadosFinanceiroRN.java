package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class DadosFinanceiroRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public DadosFinanceiroRN() {
        init_tbDadosFinanceiroCliente();
        init_tbClienteDiverso();
    }

    public DADOS_FINANCEIRO_CLIENTE tbDadosFinanceiroCliente = new DADOS_FINANCEIRO_CLIENTE("tbDadosFinanceiroCliente");

    private void init_tbDadosFinanceiroCliente() {
        tbDadosFinanceiroCliente.setName("tbDadosFinanceiroCliente");
        tbDadosFinanceiroCliente.setMaxRowCount(200);
        tbDadosFinanceiroCliente.setWKey("4600700;46001");
        tbDadosFinanceiroCliente.setRatioBatchSize(20);
    }

    public CLIENTE_DIVERSO tbClienteDiverso = new CLIENTE_DIVERSO("tbClienteDiverso");

    private void init_tbClienteDiverso() {
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("4600700;46002");
        tbClienteDiverso.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}