/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.1
   Class Main  : ClienteEnderecoPorInscricao
   Analista    : ROGERIOK
   Data Created: 30/12/1899 00:00:00
   Data Changed: 05/07/2023 16:47:39
   Data Geracao: 05/07/2023 16:48:12
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ClienteEnderecoPorInscricaoRNW extends ClienteEnderecoPorInscricaoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



