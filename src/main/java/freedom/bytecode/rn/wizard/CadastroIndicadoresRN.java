package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadastroIndicadoresRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadastroIndicadoresRN() {
        init_tbComboGrupoIndicadores();
        init_tbPainelIndicador();
        init_tbGridGrupoIndicadores();
        init_tbMaxDisplayOrder();
    }

    public BSC_COMBO_GRUPO_INDICADORES tbComboGrupoIndicadores = new BSC_COMBO_GRUPO_INDICADORES("tbComboGrupoIndicadores");

    private void init_tbComboGrupoIndicadores() {
        tbComboGrupoIndicadores.setName("tbComboGrupoIndicadores");
        tbComboGrupoIndicadores.setMaxRowCount(200);
        tbComboGrupoIndicadores.setWKey("382033;38201");
        tbComboGrupoIndicadores.setRatioBatchSize(20);
    }

    public BSC_PAINEL_INDICADOR tbPainelIndicador = new BSC_PAINEL_INDICADOR("tbPainelIndicador");

    private void init_tbPainelIndicador() {
        tbPainelIndicador.setName("tbPainelIndicador");
        tbPainelIndicador.setMaxRowCount(200);
        tbPainelIndicador.setWKey("382033;38203");
        tbPainelIndicador.setRatioBatchSize(20);
    }

    public BSC_GRID_GRUPO_INDICADORES tbGridGrupoIndicadores = new BSC_GRID_GRUPO_INDICADORES("tbGridGrupoIndicadores");

    private void init_tbGridGrupoIndicadores() {
        tbGridGrupoIndicadores.setName("tbGridGrupoIndicadores");
        tbGridGrupoIndicadores.setMaxRowCount(200);
        tbGridGrupoIndicadores.setWKey("382033;38204");
        tbGridGrupoIndicadores.setRatioBatchSize(20);
    }

    public BSC_MAX_DISPLAY_ORDER tbMaxDisplayOrder = new BSC_MAX_DISPLAY_ORDER("tbMaxDisplayOrder");

    private void init_tbMaxDisplayOrder() {
        tbMaxDisplayOrder.setName("tbMaxDisplayOrder");
        tbMaxDisplayOrder.setMaxRowCount(200);
        tbMaxDisplayOrder.setWKey("382033;38205");
        tbMaxDisplayOrder.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}