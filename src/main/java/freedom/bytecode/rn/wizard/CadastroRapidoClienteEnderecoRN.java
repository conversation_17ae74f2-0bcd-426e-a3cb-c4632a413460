package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadastroRapidoClienteEnderecoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadastroRapidoClienteEnderecoRN() {
        init_tbUf();
        init_tbCidades();
    }

    public UF tbUf = new UF("tbUf");

    private void init_tbUf() {
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(200);
        tbUf.setWKey("5300739;53001");
        tbUf.setRatioBatchSize(20);
    }

    public CIDADES tbCidades = new CIDADES("tbCidades");

    private void init_tbCidades() {
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("5300739;53002");
        tbCidades.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}