/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Server - Versao: 1.0.7.47
   Class Main  : CadastroRapidoCliente
   Analista    : GIORDANNY
   Data Created: 30/12/1899 00:00:00
   Data Changed: 03/07/2025 11:18:45
   Data Geracao: 03/07/2025 11:19:13
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CadastroRapidoClienteRNW extends CadastroRapidoClienteRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



