/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Server - Versao: 1.0.6.47
   Class Main  : CepClienteOnLineService
   Analista    : JOANDERSON.GUARIM
   Data Created: 23/09/2022 13:19:05
   Data Changed: 26/09/2022 09:14:51
   Data Geracao: 26/09/2022 09:15:04
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CepClienteOnLineServiceRN<PERSON> extends CepClienteOnLineServiceRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



