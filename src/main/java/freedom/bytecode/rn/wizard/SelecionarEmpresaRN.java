package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class SelecionarEmpresaRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public SelecionarEmpresaRN() {
        init_tbLeadsEmpresasUsuarios();
        init_tbEmpresasUsuarios();
        init_tbLeadsEmpresasUsuariosUf();
        init_tbLeadsEmpresasUsuariosCidade();
        init_tbTipoMidia();
        init_tbMidia();
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios = new LEADS_EMPRESAS_USUARIOS("tbLeadsEmpresasUsuarios");

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.setWKey("430061;43001");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("430061;43002");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public LEADS_EMPRESAS_USUARIOS_UF tbLeadsEmpresasUsuariosUf = new LEADS_EMPRESAS_USUARIOS_UF("tbLeadsEmpresasUsuariosUf");

    private void init_tbLeadsEmpresasUsuariosUf() {
        tbLeadsEmpresasUsuariosUf.setName("tbLeadsEmpresasUsuariosUf");
        tbLeadsEmpresasUsuariosUf.setMaxRowCount(200);
        tbLeadsEmpresasUsuariosUf.setWKey("430061;22204");
        tbLeadsEmpresasUsuariosUf.setRatioBatchSize(20);
    }

    public LEADS_EMPRESAS_USUARIOS_CIDADE tbLeadsEmpresasUsuariosCidade = new LEADS_EMPRESAS_USUARIOS_CIDADE("tbLeadsEmpresasUsuariosCidade");

    private void init_tbLeadsEmpresasUsuariosCidade() {
        tbLeadsEmpresasUsuariosCidade.setName("tbLeadsEmpresasUsuariosCidade");
        tbLeadsEmpresasUsuariosCidade.setMaxRowCount(200);
        tbLeadsEmpresasUsuariosCidade.setWKey("430061;22205");
        tbLeadsEmpresasUsuariosCidade.setRatioBatchSize(20);
    }

    public CRM_TIPO_MIDIA tbTipoMidia = new CRM_TIPO_MIDIA("tbTipoMidia");

    private void init_tbTipoMidia() {
        tbTipoMidia.setName("tbTipoMidia");
        tbTipoMidia.setMaxRowCount(200);
        tbTipoMidia.setWKey("430061;41101");
        tbTipoMidia.setRatioBatchSize(20);
    }

    public MIDIA tbMidia = new MIDIA("tbMidia");

    private void init_tbMidia() {
        tbMidia.setName("tbMidia");
        tbMidia.setMaxRowCount(200);
        tbMidia.setWKey("430061;41102");
        tbMidia.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}