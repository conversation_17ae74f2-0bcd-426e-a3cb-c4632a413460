/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.41
   Class Main  : AlterarItemBasico
   Analista    : GIORDANNY
   Data Created: 30/12/1899 00:00:00
   Data Changed: 01/04/2025 11:02:33
   Data Geracao: 01/04/2025 11:02:44
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AlterarItemBasicoRNW extends AlterarItemBasicoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



