package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class ClienteEnderecoPorInscricaoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public ClienteEnderecoPorInscricaoRN() {
        init_tbClientes();
        init_tbClienteDiverso();
        init_tbClienteEnderecoInscricao();
        init_tbDadosFisicos();
        init_tbConsultaNbsPessoaFisica();
        init_tbConsultaNbsPessoaJuridica();
        init_tbConsultaNbsSintegraDados();
        init_tbConsultaNbsSintegraSimples();
        init_tbCidadesInscricao();
        init_tbSchemaAtual();
    }

    public CLIENTES tbClientes = new CLIENTES("tbClientes");

    private void init_tbClientes() {
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("5300786;53001");
        tbClientes.setRatioBatchSize(20);
    }

    public CLIENTE_DIVERSO tbClienteDiverso = new CLIENTE_DIVERSO("tbClienteDiverso");

    private void init_tbClienteDiverso() {
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("5300786;53002");
        tbClienteDiverso.setRatioBatchSize(20);
    }

    public CLIENTE_ENDERECO_INSCRICAO tbClienteEnderecoInscricao = new CLIENTE_ENDERECO_INSCRICAO("tbClienteEnderecoInscricao");

    private void init_tbClienteEnderecoInscricao() {
        tbClienteEnderecoInscricao.setName("tbClienteEnderecoInscricao");
        tbClienteEnderecoInscricao.setMaxRowCount(200);
        tbClienteEnderecoInscricao.setWKey("5300786;53003");
        tbClienteEnderecoInscricao.setRatioBatchSize(20);
    }

    public DADOS_FISICOS tbDadosFisicos = new DADOS_FISICOS("tbDadosFisicos");

    private void init_tbDadosFisicos() {
        tbDadosFisicos.setName("tbDadosFisicos");
        tbDadosFisicos.setMaxRowCount(200);
        tbDadosFisicos.setWKey("5300786;10401");
        tbDadosFisicos.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_PESSOA_FISICA tbConsultaNbsPessoaFisica = new CONSULTA_NBS_PESSOA_FISICA("tbConsultaNbsPessoaFisica");

    private void init_tbConsultaNbsPessoaFisica() {
        tbConsultaNbsPessoaFisica.setName("tbConsultaNbsPessoaFisica");
        tbConsultaNbsPessoaFisica.setMaxRowCount(200);
        tbConsultaNbsPessoaFisica.setWKey("5300786;10402");
        tbConsultaNbsPessoaFisica.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_PESSOA_JURIDICA tbConsultaNbsPessoaJuridica = new CONSULTA_NBS_PESSOA_JURIDICA("tbConsultaNbsPessoaJuridica");

    private void init_tbConsultaNbsPessoaJuridica() {
        tbConsultaNbsPessoaJuridica.setName("tbConsultaNbsPessoaJuridica");
        tbConsultaNbsPessoaJuridica.setMaxRowCount(200);
        tbConsultaNbsPessoaJuridica.setWKey("5300786;10403");
        tbConsultaNbsPessoaJuridica.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_SINTEGRA_DADOS tbConsultaNbsSintegraDados = new CONSULTA_NBS_SINTEGRA_DADOS("tbConsultaNbsSintegraDados");

    private void init_tbConsultaNbsSintegraDados() {
        tbConsultaNbsSintegraDados.setName("tbConsultaNbsSintegraDados");
        tbConsultaNbsSintegraDados.setMaxRowCount(200);
        tbConsultaNbsSintegraDados.setWKey("5300786;10404");
        tbConsultaNbsSintegraDados.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_SINTEGRA_SIMPLES tbConsultaNbsSintegraSimples = new CONSULTA_NBS_SINTEGRA_SIMPLES("tbConsultaNbsSintegraSimples");

    private void init_tbConsultaNbsSintegraSimples() {
        tbConsultaNbsSintegraSimples.setName("tbConsultaNbsSintegraSimples");
        tbConsultaNbsSintegraSimples.setMaxRowCount(200);
        tbConsultaNbsSintegraSimples.setWKey("5300786;10405");
        tbConsultaNbsSintegraSimples.setRatioBatchSize(20);
    }

    public CIDADES tbCidadesInscricao = new CIDADES("tbCidadesInscricao");

    private void init_tbCidadesInscricao() {
        tbCidadesInscricao.setName("tbCidadesInscricao");
        tbCidadesInscricao.setMaxRowCount(200);
        tbCidadesInscricao.setWKey("5300786;10406");
        tbCidadesInscricao.setRatioBatchSize(20);
    }

    public SCHEMA_ATUAL tbSchemaAtual = new SCHEMA_ATUAL("tbSchemaAtual");

    private void init_tbSchemaAtual() {
        tbSchemaAtual.setName("tbSchemaAtual");
        tbSchemaAtual.setMaxRowCount(200);
        tbSchemaAtual.setWKey("5300786;10407");
        tbSchemaAtual.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}