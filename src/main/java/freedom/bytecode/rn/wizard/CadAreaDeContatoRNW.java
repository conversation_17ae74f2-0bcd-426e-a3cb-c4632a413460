/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.35
   Class Main  : CadAreaDeContato
   Analista    : GIORDANNY
   Data Created: 06/09/2024 08:51:42
   Data Changed: 06/09/2024 13:36:26
   Data Geracao: 06/09/2024 13:36:40
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.bytecode.cursor.*;
import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.RowState;
import freedom.data.SequenceUtil;
import freedom.data.TableState;
import freedom.util.WorkListFactory;
import java.util.Date;

public abstract class CadAreaDeContatoRNW extends CadAreaDeContatoRN {

    protected TableState operRN = TableState.QUERYING;

    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }
	
    public void incluir() throws DataException {
	    operRN = TableState.INSERTING; 
        tbClienteContatoTipo.append();
        tbClienteContatoTipo.setATIVO('S');
    }

    public void alterar() throws DataException {
        if (!tbClienteContatoTipo.isEmpty()) {
		    operRN = TableState.MODIFYING; 
            tbClienteContatoTipo.edit();    
        } else {
		    throw new DataException("Tabela esta vazia!!!");
		}
    }        

    public void cancelar() throws DataException {
        operRN = TableState.QUERYING;
        tbClienteContatoTipo.cancelUpdates();
    }        
    
    public void excluir() throws DataException {        
	    ISession s = getSession();
        operRN = TableState.DELETING;  	    
        try {	            
    	    excluir(s);
	        s.commit();
			operRN = TableState.QUERYING;
	} catch (DataException de) {
	    s.rollback();
	    throw de;
	} finally {
        closeSession(s);
	}
    }

    public void excluir(ISession session) throws DataException {	    
        beforeExcluir(session);
        scClienteContatoTipo.setSession(session);        
        scClienteContatoTipo.applyUpdates();                
        afterExcluir(session);
        tbClienteContatoTipo.commitUpdates();
    }

    public void excluiTableMaster() throws DataException {
        
        tbClienteContatoTipo.delete();

    }

    public void abreTabelaAux() throws DataException {
        ISession s = getSession();
        try {                
            setSession(s);
            tbSimNao.close();
            tbSimNao.open();
            tbSimNaoFt.setSession(s);
            tbSimNaoFt.close();
            tbSimNaoFt.open();
            tbSimNaoFt.setSession(null);
            tbClienteContatoTipo.refreshRecord();
        } finally {
            closeSession(s); 
        }        
    }



    public void setOperRN(TableState oper)  {
        operRN = oper; 
    }

    public TableState getOperRN()  {
        return operRN; 
    }

    public void salvar() throws DataException {
	ISession s = getSession();		
        try {                 
            salvar(s);
            s.commit();
			operRN = TableState.QUERYING;
        } catch (DataException de) {
            alterar();
            s.rollback();
            throw de;
        } finally {
            closeSession(s);
        }
    }

    public void salvar(ISession session) throws DataException {
         setPKFK();
         validaDados();
         setSession(session);
         beforeSalvar(session);
         scClienteContatoTipo.setSession(session);        
         scClienteContatoTipo.applyUpdates();                
         afterSalvar(session);
         tbClienteContatoTipo.commitUpdates();
    }

    public void beforeExcluir(ISession session) throws DataException {        
        
    }

    public void afterExcluir(ISession session) throws DataException {        
        
    }

    public void beforeSalvar(ISession session) throws DataException {        
        
    }

    public void afterSalvar(ISession session) throws DataException {        
        
    }

    public void validaDados() throws DataException {        
        validaClienteContatoTipo();
    }
    
    public void setPKFK() throws DataException {
        
        
    }

    // este metodo facilita nas rotinas de processamento ja passando o session para todas as tabelas
    // envolvidas no processamento. 
    public void setSession(ISession session) throws DataException {
        tbClienteContatoTipo.setSession(session);
        tbSimNao.setSession(session);
    }        
    
    
    public void validaClienteContatoTipo() throws DataException {
        if ( !tbClienteContatoTipo.isActive()
            || tbClienteContatoTipo.isEmpty()) {
                 return;
        }

        if (tbClienteContatoTipo.getAREA_CONTATO().isNull()) {
             throw new DataException("Campo Código de Cliente Contato Tipo é de preenchimento obrigatório");
        }

        if (tbClienteContatoTipo.getDESCRICAO().isNull()) {
             throw new DataException("Campo Descrição de Cliente Contato Tipo é de preenchimento obrigatório");
        }

        if (tbClienteContatoTipo.getATIVO().isNull()) {
             throw new DataException("Campo Ativo de Cliente Contato Tipo é de preenchimento obrigatório");
        }
        if ((tbClienteContatoTipo.getRowState() == RowState.INSERTED || 
            (tbClienteContatoTipo.getRowState() == RowState.MODIFIED && 
            (! tbClienteContatoTipo.getField("AREA_CONTATO").oldValue().isNull()) && 
            ! tbClienteContatoTipo.getField("AREA_CONTATO").equals(tbClienteContatoTipo.getField("AREA_CONTATO").oldValue()))) && 
            ( new CLIENTE_CONTATO_TIPO("CLIENTE_CONTATO_TIPO").obtem(tbClienteContatoTipo.getField("AREA_CONTATO").asString()) != null)) {
            throw new DataException("Registro Já Cadastrado...");
        }
    }


    // desabilita controls e master table das tabelas da transação
    public void disableTables() throws DataException {
        tbClienteContatoTipo.disableControls();
        tbClienteContatoTipo.disableMasterTable();
    }

    // habilita controls e master table das tabelas da transação
    public void enableTables() throws DataException {
        tbClienteContatoTipo.enableControls();
        tbClienteContatoTipo.enableMasterTable();
    }

}

