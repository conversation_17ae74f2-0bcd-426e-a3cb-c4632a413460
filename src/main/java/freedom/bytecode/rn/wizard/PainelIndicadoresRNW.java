/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.18
   Class Main  : PainelIndicadores
   Analista    : JOANDERSON.GUARIM
   Data Created: 24/04/2024 11:50:25
   Data Changed: 13/05/2024 15:24:58
   Data Geracao: 13/05/2024 15:25:17
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class PainelIndicadoresRNW extends PainelIndicadoresRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



