/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.4.50
   Class Main  : ClientesProfissao
   Analista    : EMERSON
   Data Created: 18/02/2020 16:18:02
   Data Changed: 19/02/2020 10:01:01
   Data Geracao: 19/02/2020 10:01:10
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ClientesProfissaoRNW extends ClientesProfissaoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



