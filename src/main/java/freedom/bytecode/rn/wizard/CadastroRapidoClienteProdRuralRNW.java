/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Server - Versao: 1.0.6.47
   Class Main  : CadastroRapidoClienteProdRural
   Analista    : ROGERIOK
   Data Created: 13/10/2022 21:48:46
   Data Changed: 14/10/2022 08:25:29
   Data Geracao: 14/10/2022 08:25:33
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CadastroRapidoClienteProdRuralRNW extends CadastroRapidoClienteProdRuralRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



