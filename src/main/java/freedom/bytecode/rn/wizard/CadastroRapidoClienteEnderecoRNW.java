/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.85
   Class Main  : CadastroRapidoClienteEndereco
   Analista    : ROGERIOK
   Data Created: 17/10/2022 00:00:00
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 09/02/2023 11:19:37
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CadastroRapidoClienteEnderecoRNW extends CadastroRapidoClienteEnderecoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



