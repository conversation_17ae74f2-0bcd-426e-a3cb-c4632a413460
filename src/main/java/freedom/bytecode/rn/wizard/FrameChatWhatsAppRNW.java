/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.9
   Class Main  : FrameChatWhatsApp
   Analista    : EMERSON
   Data Created: 30/12/1899 00:00:00
   Data Changed: 20/12/2023 15:20:26
   Data Geracao: 20/12/2023 15:26:00
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class FrameChatWhatsAppRNW extends FrameChatWhatsAppRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



