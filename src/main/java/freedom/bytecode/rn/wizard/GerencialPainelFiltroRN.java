package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class GerencialPainelFiltroRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public GerencialPainelFiltroRN() {
        init_tbAno();
        init_tbPainelGerencialUsuario();
        init_tbEmpresaCruzadasFuncao();
    }

    public ANO tbAno = new ANO("tbAno");

    private void init_tbAno() {
        tbAno.setName("tbAno");
        tbAno.setMaxRowCount(200);
        tbAno.setWKey("382035;38201");
        tbAno.setRatioBatchSize(20);
    }

    public PAINEL_GERENCIAL_USUARIO tbPainelGerencialUsuario = new PAINEL_GERENCIAL_USUARIO("tbPainelGerencialUsuario");

    private void init_tbPainelGerencialUsuario() {
        tbPainelGerencialUsuario.setName("tbPainelGerencialUsuario");
        tbPainelGerencialUsuario.setMaxRowCount(200);
        tbPainelGerencialUsuario.setWKey("382035;38202");
        tbPainelGerencialUsuario.setRatioBatchSize(20);
    }

    public EMPRESA_CRUZADAS_FUNCAO tbEmpresaCruzadasFuncao = new EMPRESA_CRUZADAS_FUNCAO("tbEmpresaCruzadasFuncao");

    private void init_tbEmpresaCruzadasFuncao() {
        tbEmpresaCruzadasFuncao.setName("tbEmpresaCruzadasFuncao");
        tbEmpresaCruzadasFuncao.setMaxRowCount(200);
        tbEmpresaCruzadasFuncao.setWKey("382035;38203");
        tbEmpresaCruzadasFuncao.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}