/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.44
   Class Main  : AdicionarDescLetraCliEmp
   Analista    : GIORDANNY
   Data Created: 03/08/2022 13:03:33
   Data Changed: 03/08/2022 13:35:27
   Data Geracao: 04/08/2022 08:28:19
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AdicionarDescLetraCliEmpRN<PERSON> extends AdicionarDescLetraCliEmpRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



