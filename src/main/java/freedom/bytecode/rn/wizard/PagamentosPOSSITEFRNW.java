/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.43
   Class Main  : PagamentosPOSSITEF
   Analista    : GIORDANNY
   Data Created: 21/05/2025 10:08:09
   Data Changed: 21/05/2025 13:53:55
   Data Geracao: 21/05/2025 13:54:48
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class PagamentosPOSSITEFRNW extends PagamentosPOSSITEFRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



