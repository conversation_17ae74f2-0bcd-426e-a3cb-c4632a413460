package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class ClientesFlagsPgtoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public ClientesFlagsPgtoRN() {
        init_tbClienteFormaPgtoDisp();
        init_tbEmpresas();
        init_tbEmpresasDepartamentos();
        init_tbFormaPgto();
        init_tbEmpresasFiliaisSel();
        init_tbListaEmpresasDepartamentos();
        init_tbListaTipoPgtoNbs();
        init_tbListaFormasPgto();
        init_tbClienteFormaPgtoDisp1();
    }

    public CLIENTE_FORMA_PGTO_DISP tbClienteFormaPgtoDisp = new CLIENTE_FORMA_PGTO_DISP("tbClienteFormaPgtoDisp");

    private void init_tbClienteFormaPgtoDisp() {
        tbClienteFormaPgtoDisp.setName("tbClienteFormaPgtoDisp");
        tbClienteFormaPgtoDisp.setMaxRowCount(0);
        tbClienteFormaPgtoDisp.setWKey("310037;31001");
        tbClienteFormaPgtoDisp.setRatioBatchSize(20);
    }

    public EMPRESAS tbEmpresas = new EMPRESAS("tbEmpresas");

    private void init_tbEmpresas() {
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("310037;31002");
        tbEmpresas.setRatioBatchSize(20);
    }

    public EMPRESAS_DEPARTAMENTOS tbEmpresasDepartamentos = new EMPRESAS_DEPARTAMENTOS("tbEmpresasDepartamentos");

    private void init_tbEmpresasDepartamentos() {
        tbEmpresasDepartamentos.setName("tbEmpresasDepartamentos");
        tbEmpresasDepartamentos.setMaxRowCount(0);
        tbEmpresasDepartamentos.setWKey("310037;31003");
        tbEmpresasDepartamentos.setRatioBatchSize(20);
    }

    public FORMA_PGTO tbFormaPgto = new FORMA_PGTO("tbFormaPgto");

    private void init_tbFormaPgto() {
        tbFormaPgto.setName("tbFormaPgto");
        tbFormaPgto.setMaxRowCount(0);
        tbFormaPgto.setWKey("310037;31004");
        tbFormaPgto.setRatioBatchSize(20);
    }

    public EMPRESAS_FILIAIS_SEL tbEmpresasFiliaisSel = new EMPRESAS_FILIAIS_SEL("tbEmpresasFiliaisSel");

    private void init_tbEmpresasFiliaisSel() {
        tbEmpresasFiliaisSel.setName("tbEmpresasFiliaisSel");
        tbEmpresasFiliaisSel.setMaxRowCount(200);
        tbEmpresasFiliaisSel.setWKey("310037;53001");
        tbEmpresasFiliaisSel.setRatioBatchSize(20);
    }

    public LISTA_EMPRESAS_DEPARTAMENTOS tbListaEmpresasDepartamentos = new LISTA_EMPRESAS_DEPARTAMENTOS("tbListaEmpresasDepartamentos");

    private void init_tbListaEmpresasDepartamentos() {
        tbListaEmpresasDepartamentos.setName("tbListaEmpresasDepartamentos");
        tbListaEmpresasDepartamentos.setMaxRowCount(200);
        tbListaEmpresasDepartamentos.setWKey("310037;53002");
        tbListaEmpresasDepartamentos.setRatioBatchSize(20);
    }

    public LISTA_TIPO_PGTO_NBS tbListaTipoPgtoNbs = new LISTA_TIPO_PGTO_NBS("tbListaTipoPgtoNbs");

    private void init_tbListaTipoPgtoNbs() {
        tbListaTipoPgtoNbs.setName("tbListaTipoPgtoNbs");
        tbListaTipoPgtoNbs.setMaxRowCount(200);
        tbListaTipoPgtoNbs.setWKey("310037;53003");
        tbListaTipoPgtoNbs.setRatioBatchSize(20);
    }

    public LISTA_FORMAS_PGTO tbListaFormasPgto = new LISTA_FORMAS_PGTO("tbListaFormasPgto");

    private void init_tbListaFormasPgto() {
        tbListaFormasPgto.setName("tbListaFormasPgto");
        tbListaFormasPgto.setMaxRowCount(200);
        tbListaFormasPgto.setWKey("310037;53004");
        tbListaFormasPgto.setRatioBatchSize(20);
    }

    public CLIENTE_FORMA_PGTO_DISP tbClienteFormaPgtoDisp1 = new CLIENTE_FORMA_PGTO_DISP("tbClienteFormaPgtoDisp1");

    private void init_tbClienteFormaPgtoDisp1() {
        tbClienteFormaPgtoDisp1.setName("tbClienteFormaPgtoDisp1");
        tbClienteFormaPgtoDisp1.setMaxRowCount(200);
        tbClienteFormaPgtoDisp1.setWKey("310037;53005");
        tbClienteFormaPgtoDisp1.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}