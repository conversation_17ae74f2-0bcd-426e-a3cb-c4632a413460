package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PainelIndicadoresRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PainelIndicadoresRN() {
        init_tbComboGrupo();
        init_tbListaPainel();
        init_tbPainel();
        init_tbPainelAcessoFuncao();
        init_tbPainelIndicador();
        init_tbGridIndicadores();
        init_tbGridAcessoFuncao();
        init_tbPainelSemaforo();
    }

    public BSC_COMBO_GRUPO tbComboGrupo = new BSC_COMBO_GRUPO("tbComboGrupo");

    private void init_tbComboGrupo() {
        tbComboGrupo.setName("tbComboGrupo");
        tbComboGrupo.setMaxRowCount(200);
        tbComboGrupo.setWKey("382028;38201");
        tbComboGrupo.setRatioBatchSize(20);
    }

    public BSC_LISTA_PAINEL tbListaPainel = new BSC_LISTA_PAINEL("tbListaPainel");

    private void init_tbListaPainel() {
        tbListaPainel.setName("tbListaPainel");
        tbListaPainel.setMaxRowCount(200);
        tbListaPainel.setWKey("382028;38202");
        tbListaPainel.setRatioBatchSize(20);
    }

    public BSC_PAINEL tbPainel = new BSC_PAINEL("tbPainel");

    private void init_tbPainel() {
        tbPainel.setName("tbPainel");
        tbPainel.setMaxRowCount(200);
        tbPainel.setWKey("382028;38203");
        tbPainel.setRatioBatchSize(20);
    }

    public BSC_PAINEL_ACESSO_FUNCAO tbPainelAcessoFuncao = new BSC_PAINEL_ACESSO_FUNCAO("tbPainelAcessoFuncao");

    private void init_tbPainelAcessoFuncao() {
        tbPainelAcessoFuncao.setName("tbPainelAcessoFuncao");
        tbPainelAcessoFuncao.setMaxRowCount(200);
        tbPainelAcessoFuncao.setWKey("382028;38204");
        tbPainelAcessoFuncao.setRatioBatchSize(20);
    }

    public BSC_PAINEL_INDICADOR tbPainelIndicador = new BSC_PAINEL_INDICADOR("tbPainelIndicador");

    private void init_tbPainelIndicador() {
        tbPainelIndicador.setName("tbPainelIndicador");
        tbPainelIndicador.setMaxRowCount(200);
        tbPainelIndicador.setWKey("382028;38205");
        tbPainelIndicador.setRatioBatchSize(20);
    }

    public BSC_GRID_INDICADORES tbGridIndicadores = new BSC_GRID_INDICADORES("tbGridIndicadores");

    private void init_tbGridIndicadores() {
        tbGridIndicadores.setName("tbGridIndicadores");
        tbGridIndicadores.setMaxRowCount(200);
        tbGridIndicadores.setWKey("382028;38207");
        tbGridIndicadores.setRatioBatchSize(20);
    }

    public BSC_GRID_ACESSO_FUNCAO tbGridAcessoFuncao = new BSC_GRID_ACESSO_FUNCAO("tbGridAcessoFuncao");

    private void init_tbGridAcessoFuncao() {
        tbGridAcessoFuncao.setName("tbGridAcessoFuncao");
        tbGridAcessoFuncao.setMaxRowCount(200);
        tbGridAcessoFuncao.setWKey("382028;38208");
        tbGridAcessoFuncao.setRatioBatchSize(20);
    }

    public BSC_PAINEL_SEMAFORO tbPainelSemaforo = new BSC_PAINEL_SEMAFORO("tbPainelSemaforo");

    private void init_tbPainelSemaforo() {
        tbPainelSemaforo.setName("tbPainelSemaforo");
        tbPainelSemaforo.setMaxRowCount(200);
        tbPainelSemaforo.setWKey("382028;38209");
        tbPainelSemaforo.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}