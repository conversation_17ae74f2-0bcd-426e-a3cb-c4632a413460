package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadAreaDeContatoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadAreaDeContatoRN() {
        init_tbClienteContatoTipo();
        init_tbSimNao();
        init_tbSimNaoFt();
        init_scClienteContatoTipo();
    }

    public CLIENTE_CONTATO_TIPO tbClienteContatoTipo = new CLIENTE_CONTATO_TIPO("tbClienteContatoTipo");

    private void init_tbClienteContatoTipo() {
        tbClienteContatoTipo.setName("tbClienteContatoTipo");
        tbClienteContatoTipo.setMaxRowCount(200);
        tbClienteContatoTipo.setWKey("44801360;44801");
        tbClienteContatoTipo.setRatioBatchSize(20);
    }

    public SIM_NAO tbSimNao = new SIM_NAO("tbSimNao");

    private void init_tbSimNao() {
        tbSimNao.setName("tbSimNao");
        tbSimNao.setMaxRowCount(200);
        tbSimNao.setWKey("44801360;44802");
        tbSimNao.setRatioBatchSize(20);
    }

    public SIM_NAO tbSimNaoFt = new SIM_NAO("tbSimNaoFt");

    private void init_tbSimNaoFt() {
        tbSimNaoFt.setName("tbSimNaoFt");
        tbSimNaoFt.setMaxRowCount(200);
        tbSimNaoFt.setWKey("44801360;44801");
        tbSimNaoFt.setRatioBatchSize(20);
    }


    public TFSchema scClienteContatoTipo = new TFSchema();

    private void init_scClienteContatoTipo() {
        scClienteContatoTipo.setName("scClienteContatoTipo");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteContatoTipo);
        scClienteContatoTipo.getTables().add(item0);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}