/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.3.24
   Class Main  : RetornarDataHora
   Analista    : EMERSON
   Data Created: 17/05/2019 10:48:21
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 17/05/2019 10:50:14
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class RetornarDataHoraRNW extends RetornarDataHoraRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



