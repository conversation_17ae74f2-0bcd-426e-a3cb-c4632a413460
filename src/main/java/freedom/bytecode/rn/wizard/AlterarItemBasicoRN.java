package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class AlterarItemBasicoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public AlterarItemBasicoRN() {
        init_tbItens();
        init_tbItensFornecedor();
        init_tbItensGrupoInterno();
        init_tbItensSubGrupo();
        init_tbFabricante();
        init_tbItensUnidadeMedida();
        init_tbItemSituacaoEspecial();
        init_tbMarca();
        init_tbListaLetraDescontoGeral();
        init_tbItensOrigem();
        init_tbItensHistBloqueio();
        init_tbBuscaItensFornecedor();
        init_sc();
    }

    public ITENS tbItens = new ITENS("tbItens");

    private void init_tbItens() {
        tbItens.setName("tbItens");
        tbItens.setMaxRowCount(200);
        tbItens.setWKey("7000173;70001");
        tbItens.setRatioBatchSize(20);
    }

    public ITENS_FORNECEDOR tbItensFornecedor = new ITENS_FORNECEDOR("tbItensFornecedor");

    private void init_tbItensFornecedor() {
        tbItensFornecedor.setName("tbItensFornecedor");
        tbItensFornecedor.setMaxRowCount(1000);
        tbItensFornecedor.setWKey("7000173;70002");
        tbItensFornecedor.setRatioBatchSize(20);
    }

    public ITENS_GRUPO_INTERNO tbItensGrupoInterno = new ITENS_GRUPO_INTERNO("tbItensGrupoInterno");

    private void init_tbItensGrupoInterno() {
        tbItensGrupoInterno.setName("tbItensGrupoInterno");
        tbItensGrupoInterno.setMaxRowCount(200);
        tbItensGrupoInterno.setWKey("7000173;70003");
        tbItensGrupoInterno.setRatioBatchSize(20);
    }

    public ITENS_SUB_GRUPO tbItensSubGrupo = new ITENS_SUB_GRUPO("tbItensSubGrupo");

    private void init_tbItensSubGrupo() {
        tbItensSubGrupo.setName("tbItensSubGrupo");
        tbItensSubGrupo.setMaxRowCount(200);
        tbItensSubGrupo.setWKey("7000173;70004");
        tbItensSubGrupo.setRatioBatchSize(20);
    }

    public FABRICANTE tbFabricante = new FABRICANTE("tbFabricante");

    private void init_tbFabricante() {
        tbFabricante.setName("tbFabricante");
        tbFabricante.setMaxRowCount(2000);
        tbFabricante.setWKey("7000173;70005");
        tbFabricante.setRatioBatchSize(20);
    }

    public ITENS_UNIDADE_MEDIDA tbItensUnidadeMedida = new ITENS_UNIDADE_MEDIDA("tbItensUnidadeMedida");

    private void init_tbItensUnidadeMedida() {
        tbItensUnidadeMedida.setName("tbItensUnidadeMedida");
        tbItensUnidadeMedida.setMaxRowCount(200);
        tbItensUnidadeMedida.setWKey("7000173;70006");
        tbItensUnidadeMedida.setRatioBatchSize(20);
    }

    public ITEM_SITUACAO_ESPECIAL tbItemSituacaoEspecial = new ITEM_SITUACAO_ESPECIAL("tbItemSituacaoEspecial");

    private void init_tbItemSituacaoEspecial() {
        tbItemSituacaoEspecial.setName("tbItemSituacaoEspecial");
        tbItemSituacaoEspecial.setMaxRowCount(200);
        tbItemSituacaoEspecial.setWKey("7000173;70007");
        tbItemSituacaoEspecial.setRatioBatchSize(20);
    }

    public MARCA tbMarca = new MARCA("tbMarca");

    private void init_tbMarca() {
        tbMarca.setName("tbMarca");
        tbMarca.setMaxRowCount(200);
        tbMarca.setWKey("7000173;24301");
        tbMarca.setRatioBatchSize(20);
    }

    public LISTA_LETRA_DESCONTO_GERAL tbListaLetraDescontoGeral = new LISTA_LETRA_DESCONTO_GERAL("tbListaLetraDescontoGeral");

    private void init_tbListaLetraDescontoGeral() {
        tbListaLetraDescontoGeral.setName("tbListaLetraDescontoGeral");
        tbListaLetraDescontoGeral.setMaxRowCount(200);
        tbListaLetraDescontoGeral.setWKey("7000173;27402");
        tbListaLetraDescontoGeral.setRatioBatchSize(20);
    }

    public BUSCA_ITENS_ORIGEM tbItensOrigem = new BUSCA_ITENS_ORIGEM("tbItensOrigem");

    private void init_tbItensOrigem() {
        tbItensOrigem.setName("tbItensOrigem");
        tbItensOrigem.setMaxRowCount(0);
        tbItensOrigem.setWKey("7000173;31101");
        tbItensOrigem.setRatioBatchSize(20);
    }

    public ITENS_HIST_BLOQUEIO tbItensHistBloqueio = new ITENS_HIST_BLOQUEIO("tbItensHistBloqueio");

    private void init_tbItensHistBloqueio() {
        tbItensHistBloqueio.setName("tbItensHistBloqueio");
        tbItensHistBloqueio.setMaxRowCount(200);
        tbItensHistBloqueio.setWKey("7000173;47703");
        tbItensHistBloqueio.setRatioBatchSize(20);
    }

    public BUSCA_ITENS_FORNECEDOR tbBuscaItensFornecedor = new BUSCA_ITENS_FORNECEDOR("tbBuscaItensFornecedor");

    private void init_tbBuscaItensFornecedor() {
        tbBuscaItensFornecedor.setName("tbBuscaItensFornecedor");
        tbBuscaItensFornecedor.setMaxRowCount(200);
        tbBuscaItensFornecedor.setWKey("7000173;47704");
        tbBuscaItensFornecedor.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbItens);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbItensFornecedor);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbItensHistBloqueio);
        sc.getTables().add(item2);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}