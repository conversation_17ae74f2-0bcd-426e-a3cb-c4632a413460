package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class ClientesFlagsServEmpRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public ClientesFlagsServEmpRN() {
        init_tbClienteCpagarIcmsSubEmp();
        init_tbAcessorioEmp();
        init_tbIndustriaEmp();
        init_tbClienteDiverso();
        init_tbFornecDescontaIcmsstEmp();
        init_tbClienteIssEmpresa();
        init_tbClienteIsentoIssEmpresa();
        init_tbClienteIgnoraPjReterIss();
        init_tbFornecSubIcmsEmp();
        init_tbFornecIvaEmp();
        init_tbClienteflagempresa();
        init_tbEmpresas();
        init_sc();
    }

    public CLIENTE_CPAGAR_ICMS_SUB_EMP tbClienteCpagarIcmsSubEmp = new CLIENTE_CPAGAR_ICMS_SUB_EMP("tbClienteCpagarIcmsSubEmp");

    private void init_tbClienteCpagarIcmsSubEmp() {
        tbClienteCpagarIcmsSubEmp.setName("tbClienteCpagarIcmsSubEmp");
        tbClienteCpagarIcmsSubEmp.setMaxRowCount(200);
        tbClienteCpagarIcmsSubEmp.setWKey("4600452;46001");
        tbClienteCpagarIcmsSubEmp.setRatioBatchSize(20);
    }

    public ACESSORIO_EMP tbAcessorioEmp = new ACESSORIO_EMP("tbAcessorioEmp");

    private void init_tbAcessorioEmp() {
        tbAcessorioEmp.setName("tbAcessorioEmp");
        tbAcessorioEmp.setMaxRowCount(200);
        tbAcessorioEmp.setWKey("4600452;46002");
        tbAcessorioEmp.setRatioBatchSize(20);
    }

    public INDUSTRIA_EMP tbIndustriaEmp = new INDUSTRIA_EMP("tbIndustriaEmp");

    private void init_tbIndustriaEmp() {
        tbIndustriaEmp.setName("tbIndustriaEmp");
        tbIndustriaEmp.setMaxRowCount(200);
        tbIndustriaEmp.setWKey("4600452;46003");
        tbIndustriaEmp.setRatioBatchSize(20);
    }

    public CLIENTE_DIVERSO tbClienteDiverso = new CLIENTE_DIVERSO("tbClienteDiverso");

    private void init_tbClienteDiverso() {
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("4600452;46004");
        tbClienteDiverso.setRatioBatchSize(20);
    }

    public FORNEC_DESCONTA_ICMSST_EMP tbFornecDescontaIcmsstEmp = new FORNEC_DESCONTA_ICMSST_EMP("tbFornecDescontaIcmsstEmp");

    private void init_tbFornecDescontaIcmsstEmp() {
        tbFornecDescontaIcmsstEmp.setName("tbFornecDescontaIcmsstEmp");
        tbFornecDescontaIcmsstEmp.setMaxRowCount(200);
        tbFornecDescontaIcmsstEmp.setWKey("4600452;46005");
        tbFornecDescontaIcmsstEmp.setRatioBatchSize(20);
    }

    public CLIENTE_ISS_EMPRESA tbClienteIssEmpresa = new CLIENTE_ISS_EMPRESA("tbClienteIssEmpresa");

    private void init_tbClienteIssEmpresa() {
        tbClienteIssEmpresa.setName("tbClienteIssEmpresa");
        tbClienteIssEmpresa.setMaxRowCount(200);
        tbClienteIssEmpresa.setWKey("4600452;46006");
        tbClienteIssEmpresa.setRatioBatchSize(20);
    }

    public CLIENTE_ISENTO_ISS_EMPRESA tbClienteIsentoIssEmpresa = new CLIENTE_ISENTO_ISS_EMPRESA("tbClienteIsentoIssEmpresa");

    private void init_tbClienteIsentoIssEmpresa() {
        tbClienteIsentoIssEmpresa.setName("tbClienteIsentoIssEmpresa");
        tbClienteIsentoIssEmpresa.setMaxRowCount(200);
        tbClienteIsentoIssEmpresa.setWKey("4600452;46007");
        tbClienteIsentoIssEmpresa.setRatioBatchSize(20);
    }

    public CLIENTE_IGNORA_PJ_RETER_ISS tbClienteIgnoraPjReterIss = new CLIENTE_IGNORA_PJ_RETER_ISS("tbClienteIgnoraPjReterIss");

    private void init_tbClienteIgnoraPjReterIss() {
        tbClienteIgnoraPjReterIss.setName("tbClienteIgnoraPjReterIss");
        tbClienteIgnoraPjReterIss.setMaxRowCount(200);
        tbClienteIgnoraPjReterIss.setWKey("4600452;46008");
        tbClienteIgnoraPjReterIss.setRatioBatchSize(20);
    }

    public FORNEC_SUB_ICMS_EMP tbFornecSubIcmsEmp = new FORNEC_SUB_ICMS_EMP("tbFornecSubIcmsEmp");

    private void init_tbFornecSubIcmsEmp() {
        tbFornecSubIcmsEmp.setName("tbFornecSubIcmsEmp");
        tbFornecSubIcmsEmp.setMaxRowCount(200);
        tbFornecSubIcmsEmp.setWKey("4600452;46009");
        tbFornecSubIcmsEmp.setRatioBatchSize(20);
    }

    public FORNEC_IVA_EMP tbFornecIvaEmp = new FORNEC_IVA_EMP("tbFornecIvaEmp");

    private void init_tbFornecIvaEmp() {
        tbFornecIvaEmp.setName("tbFornecIvaEmp");
        tbFornecIvaEmp.setMaxRowCount(200);
        tbFornecIvaEmp.setWKey("4600452;460010");
        tbFornecIvaEmp.setRatioBatchSize(20);
    }

    public CLIENTEFLAGEMPRESA tbClienteflagempresa = new CLIENTEFLAGEMPRESA("tbClienteflagempresa");

    private void init_tbClienteflagempresa() {
        tbClienteflagempresa.setName("tbClienteflagempresa");
        tbClienteflagempresa.setMaxRowCount(200);
        tbClienteflagempresa.setWKey("4600452;460011");
        tbClienteflagempresa.setRatioBatchSize(20);
    }

    public EMPRESAS tbEmpresas = new EMPRESAS("tbEmpresas");

    private void init_tbEmpresas() {
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("4600452;460013");
        tbEmpresas.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteCpagarIcmsSubEmp);
        sc.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbAcessorioEmp);
        sc.getTables().add(item1);
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbIndustriaEmp);
        sc.getTables().add(item2);
        TFSchemaItem item3 = new TFSchemaItem();
        item3.setTable(tbClienteDiverso);
        sc.getTables().add(item3);
        TFSchemaItem item4 = new TFSchemaItem();
        item4.setTable(tbFornecDescontaIcmsstEmp);
        sc.getTables().add(item4);
        TFSchemaItem item5 = new TFSchemaItem();
        item5.setTable(tbClienteIssEmpresa);
        sc.getTables().add(item5);
        TFSchemaItem item6 = new TFSchemaItem();
        item6.setTable(tbClienteIsentoIssEmpresa);
        sc.getTables().add(item6);
        TFSchemaItem item7 = new TFSchemaItem();
        item7.setTable(tbClienteIgnoraPjReterIss);
        sc.getTables().add(item7);
        TFSchemaItem item8 = new TFSchemaItem();
        item8.setTable(tbFornecSubIcmsEmp);
        sc.getTables().add(item8);
        TFSchemaItem item9 = new TFSchemaItem();
        item9.setTable(tbFornecIvaEmp);
        sc.getTables().add(item9);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}