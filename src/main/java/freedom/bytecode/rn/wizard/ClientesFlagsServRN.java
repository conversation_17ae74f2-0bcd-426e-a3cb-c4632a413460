package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class ClientesFlagsServRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public ClientesFlagsServRN() {
        init_tbClienteFlagGrupo();
        init_tbClienteDiverso();
        init_tbClienteCpagarIcmsSubEmp();
        init_tbClienteIssEmpresa();
        init_tbClienteIsentoIssEmpresa();
        init_tbAcessorioEmp();
        init_tbIndustriaEmp();
        init_tbFornecDescontaIcmsstEmp();
        init_tbClienteIgnoraPjReterIss();
        init_tbFornecSubIcmsEmp();
        init_tbFornecIvaEmp();
        init_tbClienteFlag();
        init_tbClientes();
        init_tbClientesEnderecoIe();
        init_sc();
    }

    public CLIENTE_FLAG_GRUPO tbClienteFlagGrupo = new CLIENTE_FLAG_GRUPO("tbClienteFlagGrupo");

    private void init_tbClienteFlagGrupo() {
        tbClienteFlagGrupo.setName("tbClienteFlagGrupo");
        tbClienteFlagGrupo.setMaxRowCount(200);
        tbClienteFlagGrupo.setWKey("4600445;46002");
        tbClienteFlagGrupo.setRatioBatchSize(20);
    }

    public CLIENTE_DIVERSO tbClienteDiverso = new CLIENTE_DIVERSO("tbClienteDiverso");

    private void init_tbClienteDiverso() {
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("4600445;46003");
        tbClienteDiverso.setRatioBatchSize(20);
    }

    public CLIENTE_CPAGAR_ICMS_SUB_EMP tbClienteCpagarIcmsSubEmp = new CLIENTE_CPAGAR_ICMS_SUB_EMP("tbClienteCpagarIcmsSubEmp");

    private void init_tbClienteCpagarIcmsSubEmp() {
        tbClienteCpagarIcmsSubEmp.setName("tbClienteCpagarIcmsSubEmp");
        tbClienteCpagarIcmsSubEmp.setMaxRowCount(200);
        tbClienteCpagarIcmsSubEmp.setWKey("4600445;46004");
        tbClienteCpagarIcmsSubEmp.setRatioBatchSize(20);
    }

    public CLIENTE_ISS_EMPRESA tbClienteIssEmpresa = new CLIENTE_ISS_EMPRESA("tbClienteIssEmpresa");

    private void init_tbClienteIssEmpresa() {
        tbClienteIssEmpresa.setName("tbClienteIssEmpresa");
        tbClienteIssEmpresa.setMaxRowCount(200);
        tbClienteIssEmpresa.setWKey("4600445;46005");
        tbClienteIssEmpresa.setRatioBatchSize(20);
    }

    public CLIENTE_ISENTO_ISS_EMPRESA tbClienteIsentoIssEmpresa = new CLIENTE_ISENTO_ISS_EMPRESA("tbClienteIsentoIssEmpresa");

    private void init_tbClienteIsentoIssEmpresa() {
        tbClienteIsentoIssEmpresa.setName("tbClienteIsentoIssEmpresa");
        tbClienteIsentoIssEmpresa.setMaxRowCount(200);
        tbClienteIsentoIssEmpresa.setWKey("4600445;46006");
        tbClienteIsentoIssEmpresa.setRatioBatchSize(20);
    }

    public ACESSORIO_EMP tbAcessorioEmp = new ACESSORIO_EMP("tbAcessorioEmp");

    private void init_tbAcessorioEmp() {
        tbAcessorioEmp.setName("tbAcessorioEmp");
        tbAcessorioEmp.setMaxRowCount(200);
        tbAcessorioEmp.setWKey("4600445;46007");
        tbAcessorioEmp.setRatioBatchSize(20);
    }

    public INDUSTRIA_EMP tbIndustriaEmp = new INDUSTRIA_EMP("tbIndustriaEmp");

    private void init_tbIndustriaEmp() {
        tbIndustriaEmp.setName("tbIndustriaEmp");
        tbIndustriaEmp.setMaxRowCount(200);
        tbIndustriaEmp.setWKey("4600445;46008");
        tbIndustriaEmp.setRatioBatchSize(20);
    }

    public FORNEC_DESCONTA_ICMSST_EMP tbFornecDescontaIcmsstEmp = new FORNEC_DESCONTA_ICMSST_EMP("tbFornecDescontaIcmsstEmp");

    private void init_tbFornecDescontaIcmsstEmp() {
        tbFornecDescontaIcmsstEmp.setName("tbFornecDescontaIcmsstEmp");
        tbFornecDescontaIcmsstEmp.setMaxRowCount(200);
        tbFornecDescontaIcmsstEmp.setWKey("4600445;46009");
        tbFornecDescontaIcmsstEmp.setRatioBatchSize(20);
    }

    public CLIENTE_IGNORA_PJ_RETER_ISS tbClienteIgnoraPjReterIss = new CLIENTE_IGNORA_PJ_RETER_ISS("tbClienteIgnoraPjReterIss");

    private void init_tbClienteIgnoraPjReterIss() {
        tbClienteIgnoraPjReterIss.setName("tbClienteIgnoraPjReterIss");
        tbClienteIgnoraPjReterIss.setMaxRowCount(200);
        tbClienteIgnoraPjReterIss.setWKey("4600445;460010");
        tbClienteIgnoraPjReterIss.setRatioBatchSize(20);
    }

    public FORNEC_SUB_ICMS_EMP tbFornecSubIcmsEmp = new FORNEC_SUB_ICMS_EMP("tbFornecSubIcmsEmp");

    private void init_tbFornecSubIcmsEmp() {
        tbFornecSubIcmsEmp.setName("tbFornecSubIcmsEmp");
        tbFornecSubIcmsEmp.setMaxRowCount(200);
        tbFornecSubIcmsEmp.setWKey("4600445;460011");
        tbFornecSubIcmsEmp.setRatioBatchSize(20);
    }

    public FORNEC_IVA_EMP tbFornecIvaEmp = new FORNEC_IVA_EMP("tbFornecIvaEmp");

    private void init_tbFornecIvaEmp() {
        tbFornecIvaEmp.setName("tbFornecIvaEmp");
        tbFornecIvaEmp.setMaxRowCount(200);
        tbFornecIvaEmp.setWKey("4600445;460012");
        tbFornecIvaEmp.setRatioBatchSize(20);
    }

    public CLIENTE_FLAG tbClienteFlag = new CLIENTE_FLAG("tbClienteFlag");

    private void init_tbClienteFlag() {
        tbClienteFlag.setName("tbClienteFlag");
        TFTableField item56 = new TFTableField();
        item56.setName("VALOR");
        item56.setCalculated(true);
        item56.setUpdatable(false);
        item56.setPrimaryKey(false);
        item56.setFieldType("ftString");
        item56.setJSONConfigNullOnEmpty(false);
        item56.setCaption("VALOR");
        tbClienteFlag.getFieldDefs().add(item56);
        TFTableField item57 = new TFTableField();
        item57.setName("VALOR_GRID");
        item57.setCalculated(true);
        item57.setUpdatable(false);
        item57.setPrimaryKey(false);
        item57.setFieldType("ftString");
        item57.setJSONConfigNullOnEmpty(false);
        item57.setCaption("VALOR_GRID");
        tbClienteFlag.getFieldDefs().add(item57);
        tbClienteFlag.setMaxRowCount(200);
        tbClienteFlag.setWKey("4600445;460013");
        tbClienteFlag.setRatioBatchSize(20);
    }

    public CLIENTES tbClientes = new CLIENTES("tbClientes");

    private void init_tbClientes() {
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("4600445;460014");
        tbClientes.setRatioBatchSize(20);
    }

    public CLIENTES_ENDERECO_IE tbClientesEnderecoIe = new CLIENTES_ENDERECO_IE("tbClientesEnderecoIe");

    private void init_tbClientesEnderecoIe() {
        tbClientesEnderecoIe.setName("tbClientesEnderecoIe");
        tbClientesEnderecoIe.setMaxRowCount(200);
        tbClientesEnderecoIe.setWKey("4600445;460015");
        tbClientesEnderecoIe.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item2 = new TFSchemaItem();
        item2.setTable(tbClienteDiverso);
        sc.getTables().add(item2);
        TFSchemaItem item3 = new TFSchemaItem();
        item3.setTable(tbClienteCpagarIcmsSubEmp);
        sc.getTables().add(item3);
        TFSchemaItem item4 = new TFSchemaItem();
        item4.setTable(tbClienteIssEmpresa);
        sc.getTables().add(item4);
        TFSchemaItem item5 = new TFSchemaItem();
        item5.setTable(tbClienteIsentoIssEmpresa);
        sc.getTables().add(item5);
        TFSchemaItem item6 = new TFSchemaItem();
        item6.setTable(tbAcessorioEmp);
        sc.getTables().add(item6);
        TFSchemaItem item7 = new TFSchemaItem();
        item7.setTable(tbIndustriaEmp);
        sc.getTables().add(item7);
        TFSchemaItem item8 = new TFSchemaItem();
        item8.setTable(tbFornecDescontaIcmsstEmp);
        sc.getTables().add(item8);
        TFSchemaItem item9 = new TFSchemaItem();
        item9.setTable(tbClienteIgnoraPjReterIss);
        sc.getTables().add(item9);
        TFSchemaItem item10 = new TFSchemaItem();
        item10.setTable(tbFornecSubIcmsEmp);
        sc.getTables().add(item10);
        TFSchemaItem item11 = new TFSchemaItem();
        item11.setTable(tbFornecIvaEmp);
        sc.getTables().add(item11);
        TFSchemaItem item12 = new TFSchemaItem();
        item12.setTable(tbClienteFlag);
        sc.getTables().add(item12);
        TFSchemaItem item13 = new TFSchemaItem();
        item13.setTable(tbClientes);
        sc.getTables().add(item13);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}