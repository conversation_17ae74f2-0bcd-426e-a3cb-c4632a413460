/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.75
   Class Main  : FichaItemResAvulsa
   Analista    : SILVARAFA
   Data Created: 01/03/2019 13:17:40
   Data Changed: 21/11/2022 12:50:29
   Data Geracao: 21/11/2022 12:56:38
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class FichaItemResAvulsaRNW extends FichaItemResAvulsaRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



