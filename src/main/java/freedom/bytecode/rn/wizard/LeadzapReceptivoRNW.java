/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : LeadzapReceptivo
   Analista    : LUIZ.RIPARDO
   Data Created: 30/12/1899 00:00:00
   Data Changed: 16/06/2023 08:46:52
   Data Geracao: 19/06/2023 10:29:34
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class LeadzapReceptivoRNW extends LeadzapReceptivoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



