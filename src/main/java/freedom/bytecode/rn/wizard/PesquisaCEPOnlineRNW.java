/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.42
   Class Main  : PesquisaCEPOnline
   Analista    : GIORDANNY
   Data Created: 29/04/2025 09:50:25
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 29/04/2025 09:50:56
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class PesquisaCEPOnlineRNW extends PesquisaCEPOnlineRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



