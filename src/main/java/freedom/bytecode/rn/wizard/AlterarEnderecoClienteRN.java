package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class AlterarEnderecoClienteRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public AlterarEnderecoClienteRN() {
        init_tbAlteraEnderecoCliente();
        init_tbUf();
        init_tbCidades();
    }

    public ALTERA_ENDERECO_CLIENTE tbAlteraEnderecoCliente = new ALTERA_ENDERECO_CLIENTE("tbAlteraEnderecoCliente");

    private void init_tbAlteraEnderecoCliente() {
        tbAlteraEnderecoCliente.setName("tbAlteraEnderecoCliente");
        tbAlteraEnderecoCliente.setMaxRowCount(200);
        tbAlteraEnderecoCliente.setWKey("430063;43001");
        tbAlteraEnderecoCliente.setDeltaMode("dmChanged");
    }

    public UF tbUf = new UF("tbUf");

    private void init_tbUf() {
        tbUf.setName("tbUf");
        tbUf.setMaxRowCount(200);
        tbUf.setWKey("430063;43002");
        tbUf.setDeltaMode("dmChanged");
    }

    public CIDADES tbCidades = new CIDADES("tbCidades");

    private void init_tbCidades() {
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("430063;43003");
        tbCidades.setDeltaMode("dmChanged");
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}