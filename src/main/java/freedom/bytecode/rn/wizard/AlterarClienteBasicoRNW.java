/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.3.27
   Class Main  : AlterarClienteBasico
   Analista    : RAMIRES
   Data Created: 21/08/2018 14:31:00
   Data Changed: 30/08/2019 07:41:21
   Data Geracao: 30/08/2019 14:36:13
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AlterarClienteBasicoRNW extends AlterarClienteBasicoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



