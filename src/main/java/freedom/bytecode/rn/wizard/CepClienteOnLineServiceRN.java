package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CepClienteOnLineServiceRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CepClienteOnLineServiceRN() {
        init_tbClienteTpEnd();
        init_tbClientesEndereco();
        init_tbClientesEnderecoIe();
        init_tbClientesEnderecoTemp();
        init_tbClientesEnderecoIeTemp();
        init_tbCidades();
    }

    public CLIENTE_TP_END tbClienteTpEnd = new CLIENTE_TP_END("tbClienteTpEnd");

    private void init_tbClienteTpEnd() {
        tbClienteTpEnd.setName("tbClienteTpEnd");
        tbClienteTpEnd.setMaxRowCount(200);
        tbClienteTpEnd.setWKey("4600715;46001");
        tbClienteTpEnd.setRatioBatchSize(20);
    }

    public CLIENTES_ENDERECO tbClientesEndereco = new CLIENTES_ENDERECO("tbClientesEndereco");

    private void init_tbClientesEndereco() {
        tbClientesEndereco.setName("tbClientesEndereco");
        tbClientesEndereco.setMaxRowCount(200);
        tbClientesEndereco.setWKey("4600715;46002");
        tbClientesEndereco.setRatioBatchSize(20);
    }

    public CLIENTES_ENDERECO_IE tbClientesEnderecoIe = new CLIENTES_ENDERECO_IE("tbClientesEnderecoIe");

    private void init_tbClientesEnderecoIe() {
        tbClientesEnderecoIe.setName("tbClientesEnderecoIe");
        tbClientesEnderecoIe.setMaxRowCount(200);
        tbClientesEnderecoIe.setWKey("4600715;46003");
        tbClientesEnderecoIe.setRatioBatchSize(20);
    }

    public DUAL tbClientesEnderecoTemp = new DUAL("tbClientesEnderecoTemp");

    private void init_tbClientesEnderecoTemp() {
        tbClientesEnderecoTemp.setName("tbClientesEnderecoTemp");
        TFTableField item6 = new TFTableField();
        item6.setName("COD_CLIENTE");
        item6.setCalculated(true);
        item6.setUpdatable(false);
        item6.setPrimaryKey(false);
        item6.setFieldType("ftDecimal");
        item6.setJSONConfigNullOnEmpty(false);
        item6.setCaption("COD_CLIENTE");
        tbClientesEnderecoTemp.getFieldDefs().add(item6);
        TFTableField item7 = new TFTableField();
        item7.setName("CEP");
        item7.setCalculated(true);
        item7.setUpdatable(false);
        item7.setPrimaryKey(false);
        item7.setFieldType("ftString");
        item7.setJSONConfigNullOnEmpty(false);
        item7.setCaption("CEP");
        tbClientesEnderecoTemp.getFieldDefs().add(item7);
        TFTableField item8 = new TFTableField();
        item8.setName("UF");
        item8.setCalculated(true);
        item8.setUpdatable(false);
        item8.setPrimaryKey(false);
        item8.setFieldType("ftString");
        item8.setJSONConfigNullOnEmpty(false);
        item8.setCaption("UF");
        tbClientesEnderecoTemp.getFieldDefs().add(item8);
        TFTableField item9 = new TFTableField();
        item9.setName("RUA");
        item9.setCalculated(true);
        item9.setUpdatable(false);
        item9.setPrimaryKey(false);
        item9.setFieldType("ftString");
        item9.setJSONConfigNullOnEmpty(false);
        item9.setCaption("RUA");
        tbClientesEnderecoTemp.getFieldDefs().add(item9);
        TFTableField item10 = new TFTableField();
        item10.setName("CIDADE");
        item10.setCalculated(true);
        item10.setUpdatable(false);
        item10.setPrimaryKey(false);
        item10.setFieldType("ftString");
        item10.setJSONConfigNullOnEmpty(false);
        item10.setCaption("CIDADE");
        tbClientesEnderecoTemp.getFieldDefs().add(item10);
        TFTableField item11 = new TFTableField();
        item11.setName("BAIRRO");
        item11.setCalculated(true);
        item11.setUpdatable(false);
        item11.setPrimaryKey(false);
        item11.setFieldType("ftString");
        item11.setJSONConfigNullOnEmpty(false);
        item11.setCaption("BAIRRO");
        tbClientesEnderecoTemp.getFieldDefs().add(item11);
        TFTableField item12 = new TFTableField();
        item12.setName("COMPLEMENTO");
        item12.setCalculated(true);
        item12.setUpdatable(false);
        item12.setPrimaryKey(false);
        item12.setFieldType("ftString");
        item12.setJSONConfigNullOnEmpty(false);
        item12.setCaption("COMPLEMENTO");
        tbClientesEnderecoTemp.getFieldDefs().add(item12);
        TFTableField item13 = new TFTableField();
        item13.setName("CODCIDADEIBGE");
        item13.setCalculated(true);
        item13.setUpdatable(false);
        item13.setPrimaryKey(false);
        item13.setFieldType("ftInteger");
        item13.setJSONConfigNullOnEmpty(false);
        item13.setCaption("CODCIDADEIBGE");
        tbClientesEnderecoTemp.getFieldDefs().add(item13);
        tbClientesEnderecoTemp.setMaxRowCount(200);
        tbClientesEnderecoTemp.setWKey("4600715;46004");
        tbClientesEnderecoTemp.setRatioBatchSize(20);
    }

    public DUAL tbClientesEnderecoIeTemp = new DUAL("tbClientesEnderecoIeTemp");

    private void init_tbClientesEnderecoIeTemp() {
        tbClientesEnderecoIeTemp.setName("tbClientesEnderecoIeTemp");
        tbClientesEnderecoIeTemp.setMaxRowCount(200);
        tbClientesEnderecoIeTemp.setWKey("4600715;46005");
        tbClientesEnderecoIeTemp.setRatioBatchSize(20);
    }

    public CIDADES tbCidades = new CIDADES("tbCidades");

    private void init_tbCidades() {
        tbCidades.setName("tbCidades");
        tbCidades.setMaxRowCount(200);
        tbCidades.setWKey("4600715;46006");
        tbCidades.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}