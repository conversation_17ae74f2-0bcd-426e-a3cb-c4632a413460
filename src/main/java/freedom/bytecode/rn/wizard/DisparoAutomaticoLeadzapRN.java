package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class DisparoAutomaticoLeadzapRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public DisparoAutomaticoLeadzapRN() {
        init_tbDisparo();
        init_tbDisparoChatbot();
        init_tbCadastroWhatsapp();
        init_tbEmailModelo();
        init_tbDescartes();
        init_scCrmDisparo();
    }

    public CRM_DISPARO tbDisparo = new CRM_DISPARO("tbDisparo");

    private void init_tbDisparo() {
        tbDisparo.setName("tbDisparo");
        tbDisparo.setMaxRowCount(200);
        tbDisparo.setWKey("440091;44001");
        tbDisparo.setRatioBatchSize(20);
    }

    public CRM_DISPARO_CHATBOT tbDisparoChatbot = new CRM_DISPARO_CHATBOT("tbDisparoChatbot");

    private void init_tbDisparoChatbot() {
        tbDisparoChatbot.setName("tbDisparoChatbot");
        tbDisparoChatbot.setMasterFields("ID_DISPARO");
        tbDisparoChatbot.setDetailFilters("ID_DISPARO");
        tbDisparoChatbot.setMaxRowCount(0);
        tbDisparoChatbot.setMasterTable(tbDisparo);
        tbDisparoChatbot.setWKey("440091;46001");
        tbDisparoChatbot.setRatioBatchSize(20);
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp = new CRM_CADASTRO_WHATSAPP("tbCadastroWhatsapp");

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("440091;46003");
        tbCadastroWhatsapp.setRatioBatchSize(20);
    }

    public CRM_EMAIL_MODELO tbEmailModelo = new CRM_EMAIL_MODELO("tbEmailModelo");

    private void init_tbEmailModelo() {
        tbEmailModelo.setName("tbEmailModelo");
        tbEmailModelo.setMaxRowCount(200);
        tbEmailModelo.setWKey("440091;44002");
        tbEmailModelo.setRatioBatchSize(20);
    }

    public CRM_DESCARTES tbDescartes = new CRM_DESCARTES("tbDescartes");

    private void init_tbDescartes() {
        tbDescartes.setName("tbDescartes");
        tbDescartes.setMaxRowCount(200);
        tbDescartes.setWKey("440091;46002");
        tbDescartes.setRatioBatchSize(20);
    }


    public TFSchema scCrmDisparo = new TFSchema();

    private void init_scCrmDisparo() {
        scCrmDisparo.setName("scCrmDisparo");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbDisparo);
        scCrmDisparo.getTables().add(item0);
        TFSchemaItem item1 = new TFSchemaItem();
        item1.setTable(tbDisparoChatbot);
        scCrmDisparo.getTables().add(item1);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}