package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadastroPainelIndicadoresRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadastroPainelIndicadoresRN() {
        init_tbPainel();
        init_tbComboGrupo();
        init_tbMaxIdPainel();
    }

    public BSC_PAINEL tbPainel = new BSC_PAINEL("tbPainel");

    private void init_tbPainel() {
        tbPainel.setName("tbPainel");
        tbPainel.setMaxRowCount(200);
        tbPainel.setWKey("382029;38201");
        tbPainel.setRatioBatchSize(20);
    }

    public BSC_COMBO_GRUPO tbComboGrupo = new BSC_COMBO_GRUPO("tbComboGrupo");

    private void init_tbComboGrupo() {
        tbComboGrupo.setName("tbComboGrupo");
        tbComboGrupo.setMaxRowCount(200);
        tbComboGrupo.setWKey("382029;38202");
        tbComboGrupo.setRatioBatchSize(20);
    }

    public GET_MAX_ID_PAINEL tbMaxIdPainel = new GET_MAX_ID_PAINEL("tbMaxIdPainel");

    private void init_tbMaxIdPainel() {
        tbMaxIdPainel.setName("tbMaxIdPainel");
        tbMaxIdPainel.setMaxRowCount(200);
        tbMaxIdPainel.setWKey("382029;38203");
        tbMaxIdPainel.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}