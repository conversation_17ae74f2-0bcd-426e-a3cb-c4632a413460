/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: ********
   Class Main  : CadastroLeadzap
   Analista    : LUIZ.RIPARDO
   Data Created: 30/12/1899 00:00:00
   Data Changed: 10/04/2025 11:42:55
   Data Geracao: 10/04/2025 11:43:04
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CadastroLeadzapRNW extends CadastroLeadzapRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



