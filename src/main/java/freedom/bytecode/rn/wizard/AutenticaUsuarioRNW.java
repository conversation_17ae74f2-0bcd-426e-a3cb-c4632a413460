/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.47
   Class Main  : AutenticaUsuario
   Analista    : JOANDERSON.GUARIM
   Data Created: 11/08/2022 07:42:01
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 11/08/2022 07:46:37
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AutenticaUsuarioRNW extends AutenticaUsuarioRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



