package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PesquisaClienteRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PesquisaClienteRN() {
        init_tbLeadsConsultaClientes();
        init_tbLeadsEnderecoCliente();
        init_tbCrmpartsExisteEvento();
        init_tbListFoneCliente();
        init_tbClientesDescontos();
        init_tbCrmpartsGridClienteEspecial();
    }

    public LEADS_CONSULTA_CLIENTES tbLeadsConsultaClientes = new LEADS_CONSULTA_CLIENTES("tbLeadsConsultaClientes");

    private void init_tbLeadsConsultaClientes() {
        tbLeadsConsultaClientes.setName("tbLeadsConsultaClientes");
        tbLeadsConsultaClientes.setMaxRowCount(1000);
        tbLeadsConsultaClientes.setWKey("7000122;70001");
        tbLeadsConsultaClientes.setRatioBatchSize(20);
    }

    public LEADS_ENDERECO_CLIENTE tbLeadsEnderecoCliente = new LEADS_ENDERECO_CLIENTE("tbLeadsEnderecoCliente");

    private void init_tbLeadsEnderecoCliente() {
        tbLeadsEnderecoCliente.setName("tbLeadsEnderecoCliente");
        TFTableField item6 = new TFTableField();
        item6.setName("CHECKED");
        item6.setCalculated(true);
        item6.setUpdatable(false);
        item6.setPrimaryKey(false);
        item6.setFieldType("ftString");
        item6.setJSONConfigNullOnEmpty(false);
        item6.setCaption("CHECKED");
        tbLeadsEnderecoCliente.getFieldDefs().add(item6);
        tbLeadsEnderecoCliente.setMaxRowCount(200);
        tbLeadsEnderecoCliente.setWKey("7000122;70002");
        tbLeadsEnderecoCliente.setRatioBatchSize(20);
    }

    public CRMPARTS_EXISTE_EVENTO tbCrmpartsExisteEvento = new CRMPARTS_EXISTE_EVENTO("tbCrmpartsExisteEvento");

    private void init_tbCrmpartsExisteEvento() {
        tbCrmpartsExisteEvento.setName("tbCrmpartsExisteEvento");
        tbCrmpartsExisteEvento.setMaxRowCount(200);
        tbCrmpartsExisteEvento.setWKey("7000122;70004");
        tbCrmpartsExisteEvento.setRatioBatchSize(20);
    }

    public DUAL tbListFoneCliente = new DUAL("tbListFoneCliente");

    private void init_tbListFoneCliente() {
        tbListFoneCliente.setName("tbListFoneCliente");
        TFTableField item0 = new TFTableField();
        item0.setName("TIPO_FONE");
        item0.setCalculated(true);
        item0.setUpdatable(false);
        item0.setPrimaryKey(false);
        item0.setFieldType("ftString");
        item0.setJSONConfigNullOnEmpty(false);
        item0.setCaption("TIPO_FONE");
        tbListFoneCliente.getFieldDefs().add(item0);
        TFTableField item1 = new TFTableField();
        item1.setName("FONE");
        item1.setCalculated(true);
        item1.setUpdatable(false);
        item1.setPrimaryKey(false);
        item1.setFieldType("ftString");
        item1.setJSONConfigNullOnEmpty(false);
        item1.setCaption("FONE");
        tbListFoneCliente.getFieldDefs().add(item1);
        tbListFoneCliente.setMaxRowCount(200);
        tbListFoneCliente.setWKey("7000122;70006");
        tbListFoneCliente.setRatioBatchSize(20);
    }

    public CLIENTES_DESCONTOS tbClientesDescontos = new CLIENTES_DESCONTOS("tbClientesDescontos");

    private void init_tbClientesDescontos() {
        tbClientesDescontos.setName("tbClientesDescontos");
        tbClientesDescontos.setMaxRowCount(200);
        tbClientesDescontos.setWKey("7000122;31001");
        tbClientesDescontos.setRatioBatchSize(20);
    }

    public CRMPARTS_GRID_CLIENTE_ESPECIAL tbCrmpartsGridClienteEspecial = new CRMPARTS_GRID_CLIENTE_ESPECIAL("tbCrmpartsGridClienteEspecial");

    private void init_tbCrmpartsGridClienteEspecial() {
        tbCrmpartsGridClienteEspecial.setName("tbCrmpartsGridClienteEspecial");
        tbCrmpartsGridClienteEspecial.setMaxRowCount(200);
        tbCrmpartsGridClienteEspecial.setWKey("7000122;53002");
        tbCrmpartsGridClienteEspecial.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}