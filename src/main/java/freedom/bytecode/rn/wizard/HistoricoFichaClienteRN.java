package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class HistoricoFichaClienteRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public HistoricoFichaClienteRN() {
        init_tbFichaClienteEventos();
        init_tbFichaClienteVeiculos();
        init_tbFichaClienteOsVeiculosCombo();
        init_tbFichaClienteOsOrc();
        init_tbFichaClienteOsVeiculosCombo2();
        init_tbFichaClienteOsOrcCombo();
        init_tbFichaClienteServicos();
        init_tbFichaClientePecas();
    }

    public FICHA_CLIENTE_EVENTOS tbFichaClienteEventos = new FICHA_CLIENTE_EVENTOS("tbFichaClienteEventos");

    private void init_tbFichaClienteEventos() {
        tbFichaClienteEventos.setName("tbFichaClienteEventos");
        tbFichaClienteEventos.setMaxRowCount(200);
        tbFichaClienteEventos.setWKey("16507;16502");
        tbFichaClienteEventos.setRatioBatchSize(20);
    }

    public FICHA_CLIENTE_VEICULOS tbFichaClienteVeiculos = new FICHA_CLIENTE_VEICULOS("tbFichaClienteVeiculos");

    private void init_tbFichaClienteVeiculos() {
        tbFichaClienteVeiculos.setName("tbFichaClienteVeiculos");
        tbFichaClienteVeiculos.setMaxRowCount(200);
        tbFichaClienteVeiculos.setWKey("16507;16503");
        tbFichaClienteVeiculos.setRatioBatchSize(20);
    }

    public FICHA_CLIENTE_OS_VEICULOS tbFichaClienteOsVeiculosCombo = new FICHA_CLIENTE_OS_VEICULOS("tbFichaClienteOsVeiculosCombo");

    private void init_tbFichaClienteOsVeiculosCombo() {
        tbFichaClienteOsVeiculosCombo.setName("tbFichaClienteOsVeiculosCombo");
        tbFichaClienteOsVeiculosCombo.setMaxRowCount(200);
        tbFichaClienteOsVeiculosCombo.setWKey("16507;16504");
        tbFichaClienteOsVeiculosCombo.setRatioBatchSize(20);
    }

    public FICHA_CLIENTE_OS_ORC tbFichaClienteOsOrc = new FICHA_CLIENTE_OS_ORC("tbFichaClienteOsOrc");

    private void init_tbFichaClienteOsOrc() {
        tbFichaClienteOsOrc.setName("tbFichaClienteOsOrc");
        tbFichaClienteOsOrc.setMaxRowCount(200);
        tbFichaClienteOsOrc.setWKey("16507;16505");
        tbFichaClienteOsOrc.setRatioBatchSize(20);
    }

    public FICHA_CLIENTE_OS_VEICULOS tbFichaClienteOsVeiculosCombo2 = new FICHA_CLIENTE_OS_VEICULOS("tbFichaClienteOsVeiculosCombo2");

    private void init_tbFichaClienteOsVeiculosCombo2() {
        tbFichaClienteOsVeiculosCombo2.setName("tbFichaClienteOsVeiculosCombo2");
        tbFichaClienteOsVeiculosCombo2.setMaxRowCount(200);
        tbFichaClienteOsVeiculosCombo2.setWKey("16507;16506");
        tbFichaClienteOsVeiculosCombo2.setRatioBatchSize(20);
    }

    public FICHA_CLIENTE_OS_ORC tbFichaClienteOsOrcCombo = new FICHA_CLIENTE_OS_ORC("tbFichaClienteOsOrcCombo");

    private void init_tbFichaClienteOsOrcCombo() {
        tbFichaClienteOsOrcCombo.setName("tbFichaClienteOsOrcCombo");
        tbFichaClienteOsOrcCombo.setMaxRowCount(200);
        tbFichaClienteOsOrcCombo.setWKey("16507;16507");
        tbFichaClienteOsOrcCombo.setRatioBatchSize(20);
    }

    public FICHA_CLIENTE_SERVICOS tbFichaClienteServicos = new FICHA_CLIENTE_SERVICOS("tbFichaClienteServicos");

    private void init_tbFichaClienteServicos() {
        tbFichaClienteServicos.setName("tbFichaClienteServicos");
        tbFichaClienteServicos.setMaxRowCount(200);
        tbFichaClienteServicos.setWKey("16507;16508");
        tbFichaClienteServicos.setRatioBatchSize(20);
    }

    public FICHA_CLIENTE_PECAS tbFichaClientePecas = new FICHA_CLIENTE_PECAS("tbFichaClientePecas");

    private void init_tbFichaClientePecas() {
        tbFichaClientePecas.setName("tbFichaClientePecas");
        tbFichaClientePecas.setMaxRowCount(200);
        tbFichaClientePecas.setWKey("16507;16509");
        tbFichaClientePecas.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}