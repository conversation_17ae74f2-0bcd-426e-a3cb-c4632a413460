/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.50
   Class Main  : PesquisaFormaPgto
   Analista    : ROGERIOK
   Data Created: 14/06/2022 08:46:31
   Data Changed: 16/06/2022 10:41:20
   Data Geracao: 26/08/2022 15:18:08
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class PesquisaFormaPgtoRNW extends PesquisaFormaPgtoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



