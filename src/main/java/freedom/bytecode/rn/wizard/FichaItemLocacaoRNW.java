/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.86
   Class Main  : FichaItemLocacao
   Analista    : JOANDERSON.GUARIM
   Data Created: 28/02/2019 00:00:00
   Data Changed: 23/03/2023 15:24:37
   Data Geracao: 23/03/2023 15:24:57
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class FichaItemLocacaoRNW extends FichaItemLocacaoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



