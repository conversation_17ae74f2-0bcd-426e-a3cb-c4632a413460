/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.47
   Class Main  : SelecionarPeriodo
   Analista    : GIORDANNY
   Data Created: 17/10/2022 10:34:27
   Data Changed: 30/12/1899 00:00:00
   Data Geracao: 17/10/2022 10:54:03
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class SelecionarPeriodoRNW extends SelecionarPeriodoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



