package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadastroRapidoClienteRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadastroRapidoClienteRN() {
        init_tbClientesProfissaoInclusao();
        init_tbUf1();
        init_tbUf2();
        init_tbUf3();
        init_tbCidades1();
        init_tbCidades2();
        init_tbCidades3();
        init_tbNacionalidade();
        init_tbEstadoCivil();
        init_tbClientesRamo();
        init_tbDadosJuridicos();
        init_tbDadosFisicos();
        init_tbClientes();
        init_tbClienteDiverso();
        init_tbParmSys();
        init_tbClientesSexo();
        init_tbRegimeEspecialTributacao();
        init_tbRapCliContato();
        init_tbConsultaNbsPessoaJuridica();
        init_tbConsultaNbsPessoaFisica();
        init_tbConsultaNbsSintegraSimples();
        init_tbConsultaNbsSintegraDados();
        init_tbConsultaNbsSocios();
        init_tbConsultaNbsOutrasAtividades();
        init_tbClienteEnderecoInscricao();
        init_tbCidadesInscricao();
        init_tbSchemaAtual();
        init_tbEscolaridade();
        init_tbMidia();
        init_scClienteDiverso();
    }

    public CLIENTES_PROFISSAO_INCLUSAO tbClientesProfissaoInclusao = new CLIENTES_PROFISSAO_INCLUSAO("tbClientesProfissaoInclusao");

    private void init_tbClientesProfissaoInclusao() {
        tbClientesProfissaoInclusao.setName("tbClientesProfissaoInclusao");
        tbClientesProfissaoInclusao.setMaxRowCount(0);
        tbClientesProfissaoInclusao.setWKey("7000195;44001");
        tbClientesProfissaoInclusao.setRatioBatchSize(20);
    }

    public UF tbUf1 = new UF("tbUf1");

    private void init_tbUf1() {
        tbUf1.setName("tbUf1");
        tbUf1.setMaxRowCount(0);
        tbUf1.setWKey("7000195;440011");
        tbUf1.setRatioBatchSize(20);
    }

    public UF tbUf2 = new UF("tbUf2");

    private void init_tbUf2() {
        tbUf2.setName("tbUf2");
        tbUf2.setMaxRowCount(0);
        tbUf2.setWKey("7000195;440012");
        tbUf2.setRatioBatchSize(20);
    }

    public UF tbUf3 = new UF("tbUf3");

    private void init_tbUf3() {
        tbUf3.setName("tbUf3");
        tbUf3.setMaxRowCount(0);
        tbUf3.setWKey("7000195;440013");
        tbUf3.setRatioBatchSize(20);
    }

    public CIDADES tbCidades1 = new CIDADES("tbCidades1");

    private void init_tbCidades1() {
        tbCidades1.setName("tbCidades1");
        tbCidades1.setMasterFields("UF");
        tbCidades1.setDetailFilters("UF");
        tbCidades1.setMaxRowCount(0);
        tbCidades1.setMasterTable(tbUf1);
        tbCidades1.setWKey("7000195;440015");
        tbCidades1.setRatioBatchSize(20);
    }

    public CIDADES tbCidades2 = new CIDADES("tbCidades2");

    private void init_tbCidades2() {
        tbCidades2.setName("tbCidades2");
        tbCidades2.setMasterFields("UF");
        tbCidades2.setDetailFilters("UF");
        tbCidades2.setMaxRowCount(0);
        tbCidades2.setMasterTable(tbUf2);
        tbCidades2.setWKey("7000195;440016");
        tbCidades2.setRatioBatchSize(20);
    }

    public CIDADES tbCidades3 = new CIDADES("tbCidades3");

    private void init_tbCidades3() {
        tbCidades3.setName("tbCidades3");
        tbCidades3.setMasterFields("UF");
        tbCidades3.setDetailFilters("UF");
        tbCidades3.setMaxRowCount(0);
        tbCidades3.setMasterTable(tbUf3);
        tbCidades3.setWKey("7000195;440017");
        tbCidades3.setRatioBatchSize(20);
    }

    public NACIONALIDADE tbNacionalidade = new NACIONALIDADE("tbNacionalidade");

    private void init_tbNacionalidade() {
        tbNacionalidade.setName("tbNacionalidade");
        tbNacionalidade.setMaxRowCount(0);
        tbNacionalidade.setWKey("7000195;440019");
        tbNacionalidade.setRatioBatchSize(20);
    }

    public ESTADO_CIVIL tbEstadoCivil = new ESTADO_CIVIL("tbEstadoCivil");

    private void init_tbEstadoCivil() {
        tbEstadoCivil.setName("tbEstadoCivil");
        tbEstadoCivil.setMaxRowCount(200);
        tbEstadoCivil.setWKey("7000195;440020");
        tbEstadoCivil.setRatioBatchSize(20);
    }

    public CLIENTES_RAMO tbClientesRamo = new CLIENTES_RAMO("tbClientesRamo");

    private void init_tbClientesRamo() {
        tbClientesRamo.setName("tbClientesRamo");
        tbClientesRamo.setMaxRowCount(200);
        tbClientesRamo.setWKey("7000195;440021");
        tbClientesRamo.setRatioBatchSize(20);
    }

    public DADOS_JURIDICOS tbDadosJuridicos = new DADOS_JURIDICOS("tbDadosJuridicos");

    private void init_tbDadosJuridicos() {
        tbDadosJuridicos.setName("tbDadosJuridicos");
        tbDadosJuridicos.setMaxRowCount(200);
        tbDadosJuridicos.setWKey("7000195;440022");
        tbDadosJuridicos.setRatioBatchSize(20);
    }

    public DADOS_FISICOS tbDadosFisicos = new DADOS_FISICOS("tbDadosFisicos");

    private void init_tbDadosFisicos() {
        tbDadosFisicos.setName("tbDadosFisicos");
        tbDadosFisicos.setMaxRowCount(200);
        tbDadosFisicos.setWKey("7000195;440023");
        tbDadosFisicos.setRatioBatchSize(20);
    }

    public CLIENTES tbClientes = new CLIENTES("tbClientes");

    private void init_tbClientes() {
        tbClientes.setName("tbClientes");
        tbClientes.setMaxRowCount(200);
        tbClientes.setWKey("7000195;440024");
        tbClientes.setRatioBatchSize(20);
    }

    public CLIENTE_DIVERSO tbClienteDiverso = new CLIENTE_DIVERSO("tbClienteDiverso");

    private void init_tbClienteDiverso() {
        tbClienteDiverso.setName("tbClienteDiverso");
        tbClienteDiverso.setMaxRowCount(200);
        tbClienteDiverso.setWKey("7000195;440025");
        tbClienteDiverso.setRatioBatchSize(20);
    }

    public PARM_SYS tbParmSys = new PARM_SYS("tbParmSys");

    private void init_tbParmSys() {
        tbParmSys.setName("tbParmSys");
        tbParmSys.setMaxRowCount(200);
        tbParmSys.setWKey("7000195;43002");
        tbParmSys.setRatioBatchSize(20);
    }

    public CLIENTES_SEXO tbClientesSexo = new CLIENTES_SEXO("tbClientesSexo");

    private void init_tbClientesSexo() {
        tbClientesSexo.setName("tbClientesSexo");
        tbClientesSexo.setMaxRowCount(200);
        tbClientesSexo.setWKey("7000195;43003");
        tbClientesSexo.setRatioBatchSize(20);
    }

    public REGIME_ESPECIAL_TRIBUTACAO tbRegimeEspecialTributacao = new REGIME_ESPECIAL_TRIBUTACAO("tbRegimeEspecialTributacao");

    private void init_tbRegimeEspecialTributacao() {
        tbRegimeEspecialTributacao.setName("tbRegimeEspecialTributacao");
        tbRegimeEspecialTributacao.setMaxRowCount(200);
        tbRegimeEspecialTributacao.setWKey("7000195;46004");
        tbRegimeEspecialTributacao.setRatioBatchSize(20);
    }

    public CAD_RAP_CLI_CONTATO tbRapCliContato = new CAD_RAP_CLI_CONTATO("tbRapCliContato");

    private void init_tbRapCliContato() {
        tbRapCliContato.setName("tbRapCliContato");
        tbRapCliContato.setMaxRowCount(200);
        tbRapCliContato.setWKey("7000195;46005");
        tbRapCliContato.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_PESSOA_JURIDICA tbConsultaNbsPessoaJuridica = new CONSULTA_NBS_PESSOA_JURIDICA("tbConsultaNbsPessoaJuridica");

    private void init_tbConsultaNbsPessoaJuridica() {
        tbConsultaNbsPessoaJuridica.setName("tbConsultaNbsPessoaJuridica");
        tbConsultaNbsPessoaJuridica.setMaxRowCount(200);
        tbConsultaNbsPessoaJuridica.setWKey("7000195;53006");
        tbConsultaNbsPessoaJuridica.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_PESSOA_FISICA tbConsultaNbsPessoaFisica = new CONSULTA_NBS_PESSOA_FISICA("tbConsultaNbsPessoaFisica");

    private void init_tbConsultaNbsPessoaFisica() {
        tbConsultaNbsPessoaFisica.setName("tbConsultaNbsPessoaFisica");
        tbConsultaNbsPessoaFisica.setMaxRowCount(200);
        tbConsultaNbsPessoaFisica.setWKey("7000195;53007");
        tbConsultaNbsPessoaFisica.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_SINTEGRA_SIMPLES tbConsultaNbsSintegraSimples = new CONSULTA_NBS_SINTEGRA_SIMPLES("tbConsultaNbsSintegraSimples");

    private void init_tbConsultaNbsSintegraSimples() {
        tbConsultaNbsSintegraSimples.setName("tbConsultaNbsSintegraSimples");
        tbConsultaNbsSintegraSimples.setMaxRowCount(200);
        tbConsultaNbsSintegraSimples.setWKey("7000195;53008");
        tbConsultaNbsSintegraSimples.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_SINTEGRA_DADOS tbConsultaNbsSintegraDados = new CONSULTA_NBS_SINTEGRA_DADOS("tbConsultaNbsSintegraDados");

    private void init_tbConsultaNbsSintegraDados() {
        tbConsultaNbsSintegraDados.setName("tbConsultaNbsSintegraDados");
        tbConsultaNbsSintegraDados.setMaxRowCount(200);
        tbConsultaNbsSintegraDados.setWKey("7000195;53009");
        tbConsultaNbsSintegraDados.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_SOCIOS tbConsultaNbsSocios = new CONSULTA_NBS_SOCIOS("tbConsultaNbsSocios");

    private void init_tbConsultaNbsSocios() {
        tbConsultaNbsSocios.setName("tbConsultaNbsSocios");
        tbConsultaNbsSocios.setMaxRowCount(200);
        tbConsultaNbsSocios.setWKey("7000195;530010");
        tbConsultaNbsSocios.setRatioBatchSize(20);
    }

    public CONSULTA_NBS_OUTRAS_ATIVIDADES tbConsultaNbsOutrasAtividades = new CONSULTA_NBS_OUTRAS_ATIVIDADES("tbConsultaNbsOutrasAtividades");

    private void init_tbConsultaNbsOutrasAtividades() {
        tbConsultaNbsOutrasAtividades.setName("tbConsultaNbsOutrasAtividades");
        tbConsultaNbsOutrasAtividades.setMaxRowCount(200);
        tbConsultaNbsOutrasAtividades.setWKey("7000195;530011");
        tbConsultaNbsOutrasAtividades.setRatioBatchSize(20);
    }

    public CLIENTE_ENDERECO_INSCRICAO tbClienteEnderecoInscricao = new CLIENTE_ENDERECO_INSCRICAO("tbClienteEnderecoInscricao");

    private void init_tbClienteEnderecoInscricao() {
        tbClienteEnderecoInscricao.setName("tbClienteEnderecoInscricao");
        tbClienteEnderecoInscricao.setMaxRowCount(200);
        tbClienteEnderecoInscricao.setWKey("7000195;530012");
        tbClienteEnderecoInscricao.setRatioBatchSize(20);
    }

    public CIDADES tbCidadesInscricao = new CIDADES("tbCidadesInscricao");

    private void init_tbCidadesInscricao() {
        tbCidadesInscricao.setName("tbCidadesInscricao");
        tbCidadesInscricao.setMaxRowCount(200);
        tbCidadesInscricao.setWKey("7000195;530013");
        tbCidadesInscricao.setRatioBatchSize(20);
    }

    public SCHEMA_ATUAL tbSchemaAtual = new SCHEMA_ATUAL("tbSchemaAtual");

    private void init_tbSchemaAtual() {
        tbSchemaAtual.setName("tbSchemaAtual");
        tbSchemaAtual.setMaxRowCount(200);
        tbSchemaAtual.setWKey("7000195;530014");
        tbSchemaAtual.setRatioBatchSize(20);
    }

    public ESCOLARIDADE tbEscolaridade = new ESCOLARIDADE("tbEscolaridade");

    private void init_tbEscolaridade() {
        tbEscolaridade.setName("tbEscolaridade");
        tbEscolaridade.setMaxRowCount(200);
        tbEscolaridade.setWKey("7000195;13801");
        tbEscolaridade.setRatioBatchSize(20);
    }

    public BUSCA_MIDIA tbMidia = new BUSCA_MIDIA("tbMidia");

    private void init_tbMidia() {
        tbMidia.setName("tbMidia");
        tbMidia.setMaxRowCount(200);
        tbMidia.setWKey("7000195;552017");
        tbMidia.setRatioBatchSize(20);
    }


    public TFSchema scClienteDiverso = new TFSchema();

    private void init_scClienteDiverso() {
        scClienteDiverso.setName("scClienteDiverso");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbClienteDiverso);
        scClienteDiverso.getTables().add(item0);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}