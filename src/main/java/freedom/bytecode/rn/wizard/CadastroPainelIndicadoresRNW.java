/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.18
   Class Main  : CadastroPainelIndicadores
   Analista    : JOANDERSON.GUARIM
   Data Created: 24/04/2024 16:28:49
   Data Changed: 25/04/2024 08:51:26
   Data Geracao: 25/04/2024 08:52:09
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CadastroPainelIndicadoresRNW extends CadastroPainelIndicadoresRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



