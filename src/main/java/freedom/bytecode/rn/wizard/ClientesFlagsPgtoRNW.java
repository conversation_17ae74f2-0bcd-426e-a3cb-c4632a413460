/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.41
   Class Main  : ClientesFlagsPgto
   Analista    : GIORDANNY
   Data Created: 12/02/2019 12:39:44
   Data Changed: 05/07/2022 08:41:27
   Data Geracao: 05/07/2022 08:41:53
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class ClientesFlagsPgtoRNW extends ClientesFlagsPgtoRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



