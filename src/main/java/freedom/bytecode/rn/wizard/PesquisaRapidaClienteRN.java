package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PesquisaRapidaClienteRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PesquisaRapidaClienteRN() {
        init_tbLeadsConsultaClientes();
    }

    public LEADS_CONSULTA_CLIENTES tbLeadsConsultaClientes = new LEADS_CONSULTA_CLIENTES("tbLeadsConsultaClientes");

    private void init_tbLeadsConsultaClientes() {
        tbLeadsConsultaClientes.setName("tbLeadsConsultaClientes");
        tbLeadsConsultaClientes.setMaxRowCount(200);
        tbLeadsConsultaClientes.setWKey("298021;29801");
        tbLeadsConsultaClientes.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}