package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class AssinaturaDigitaHistoricoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public AssinaturaDigitaHistoricoRN() {
        init_tbNbsapiEnvelopesLog();
        init_tbAssinaturaDigital();
    }

    public NBSAPI_ENVELOPES_LOG tbNbsapiEnvelopesLog = new NBSAPI_ENVELOPES_LOG("tbNbsapiEnvelopesLog");

    private void init_tbNbsapiEnvelopesLog() {
        tbNbsapiEnvelopesLog.setName("tbNbsapiEnvelopesLog");
        tbNbsapiEnvelopesLog.setMaxRowCount(0);
        tbNbsapiEnvelopesLog.setWKey("469012;46901");
        tbNbsapiEnvelopesLog.setRatioBatchSize(20);
    }

    public CRM_ASSINATURA_DIGITAL tbAssinaturaDigital = new CRM_ASSINATURA_DIGITAL("tbAssinaturaDigital");

    private void init_tbAssinaturaDigital() {
        tbAssinaturaDigital.setName("tbAssinaturaDigital");
        tbAssinaturaDigital.setMaxRowCount(200);
        tbAssinaturaDigital.setWKey("469012;46902");
        tbAssinaturaDigital.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}