/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.18
   Class Main  : GerencialPainelPreferencia
   Analista    : JOANDERSON.GUARIM
   Data Created: 29/04/2024 10:34:44
   Data Changed: 15/05/2024 08:58:40
   Data Geracao: 15/05/2024 09:14:27
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class GerencialPainelPreferenciaRNW extends GerencialPainelPreferenciaRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



