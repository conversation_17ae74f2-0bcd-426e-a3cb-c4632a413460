package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class AgrupaPagamentosRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public AgrupaPagamentosRN() {
        init_tbEmpresasUsuarios();
        init_tbLeadsEmpresasUsuarios();
        init_tbOrcPreNotasParaAgrupar();
        init_tbLeadsPgtoAgrupado();
        init_tbLeadsPgtoAgrupadoParc();
        init_tbLeadsPgtoAgrupadoParam();
        init_tbLeadsOrcNotasPgtoAgrupados();
        init_tbLeadsFormaPgtoAgrupado();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("298020;29801");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios = new LEADS_EMPRESAS_USUARIOS("tbLeadsEmpresasUsuarios");

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.setWKey("298020;29802");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
    }

    public PESQ_ORC_PARA_AGRUPAR tbOrcPreNotasParaAgrupar = new PESQ_ORC_PARA_AGRUPAR("tbOrcPreNotasParaAgrupar");

    private void init_tbOrcPreNotasParaAgrupar() {
        tbOrcPreNotasParaAgrupar.setName("tbOrcPreNotasParaAgrupar");
        tbOrcPreNotasParaAgrupar.setMaxRowCount(200);
        tbOrcPreNotasParaAgrupar.setWKey("298020;29803");
        tbOrcPreNotasParaAgrupar.setRatioBatchSize(20);
    }

    public LEADS_PGTO_AGRUPADO tbLeadsPgtoAgrupado = new LEADS_PGTO_AGRUPADO("tbLeadsPgtoAgrupado");

    private void init_tbLeadsPgtoAgrupado() {
        tbLeadsPgtoAgrupado.setName("tbLeadsPgtoAgrupado");
        tbLeadsPgtoAgrupado.setMaxRowCount(200);
        tbLeadsPgtoAgrupado.setWKey("298020;29804");
        tbLeadsPgtoAgrupado.setRatioBatchSize(20);
    }

    public LEADS_PGTO_AGRUPADO_PARC tbLeadsPgtoAgrupadoParc = new LEADS_PGTO_AGRUPADO_PARC("tbLeadsPgtoAgrupadoParc");

    private void init_tbLeadsPgtoAgrupadoParc() {
        tbLeadsPgtoAgrupadoParc.setName("tbLeadsPgtoAgrupadoParc");
        tbLeadsPgtoAgrupadoParc.setMaxRowCount(200);
        tbLeadsPgtoAgrupadoParc.setWKey("298020;29805");
        tbLeadsPgtoAgrupadoParc.setRatioBatchSize(20);
    }

    public LEADS_PGTO_AGRUPADO_PARAM tbLeadsPgtoAgrupadoParam = new LEADS_PGTO_AGRUPADO_PARAM("tbLeadsPgtoAgrupadoParam");

    private void init_tbLeadsPgtoAgrupadoParam() {
        tbLeadsPgtoAgrupadoParam.setName("tbLeadsPgtoAgrupadoParam");
        tbLeadsPgtoAgrupadoParam.setMaxRowCount(200);
        tbLeadsPgtoAgrupadoParam.setWKey("298020;29806");
        tbLeadsPgtoAgrupadoParam.setRatioBatchSize(20);
    }

    public LEADS_ORC_NOTAS_PGTO_AGRUPADOS tbLeadsOrcNotasPgtoAgrupados = new LEADS_ORC_NOTAS_PGTO_AGRUPADOS("tbLeadsOrcNotasPgtoAgrupados");

    private void init_tbLeadsOrcNotasPgtoAgrupados() {
        tbLeadsOrcNotasPgtoAgrupados.setName("tbLeadsOrcNotasPgtoAgrupados");
        tbLeadsOrcNotasPgtoAgrupados.setMaxRowCount(200);
        tbLeadsOrcNotasPgtoAgrupados.setWKey("298020;29807");
        tbLeadsOrcNotasPgtoAgrupados.setRatioBatchSize(20);
    }

    public LEADS_FORMA_PGTO_AGRUPADO tbLeadsFormaPgtoAgrupado = new LEADS_FORMA_PGTO_AGRUPADO("tbLeadsFormaPgtoAgrupado");

    private void init_tbLeadsFormaPgtoAgrupado() {
        tbLeadsFormaPgtoAgrupado.setName("tbLeadsFormaPgtoAgrupado");
        tbLeadsFormaPgtoAgrupado.setMaxRowCount(200);
        tbLeadsFormaPgtoAgrupado.setWKey("298020;29808");
        tbLeadsFormaPgtoAgrupado.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}