package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FrameChatWhatsAppRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public FrameChatWhatsAppRN() {
        init_tbConsultaNumberWhats();
        init_tbWhatsappAtendimento();
        init_tbKeyHashApiChat();
        init_tbCadastroWhatsapp();
        init_tbEventos();
        init_sc();
    }

    public CRM_CONSULTA_NUMBER_WHATS tbConsultaNumberWhats = new CRM_CONSULTA_NUMBER_WHATS("tbConsultaNumberWhats");

    private void init_tbConsultaNumberWhats() {
        tbConsultaNumberWhats.setName("tbConsultaNumberWhats");
        tbConsultaNumberWhats.setMaxRowCount(200);
        tbConsultaNumberWhats.setWKey("4600408;46002");
        tbConsultaNumberWhats.setRatioBatchSize(20);
    }

    public CRM_WHATSAPP_ATENDIMENTO tbWhatsappAtendimento = new CRM_WHATSAPP_ATENDIMENTO("tbWhatsappAtendimento");

    private void init_tbWhatsappAtendimento() {
        tbWhatsappAtendimento.setName("tbWhatsappAtendimento");
        tbWhatsappAtendimento.setMaxRowCount(200);
        tbWhatsappAtendimento.setWKey("4600408;46003");
        tbWhatsappAtendimento.setRatioBatchSize(20);
    }

    public GET_KEY_HASH_API_CHAT tbKeyHashApiChat = new GET_KEY_HASH_API_CHAT("tbKeyHashApiChat");

    private void init_tbKeyHashApiChat() {
        tbKeyHashApiChat.setName("tbKeyHashApiChat");
        tbKeyHashApiChat.setMaxRowCount(200);
        tbKeyHashApiChat.setWKey("4600408;46004");
        tbKeyHashApiChat.setRatioBatchSize(20);
    }

    public CRM_CADASTRO_WHATSAPP tbCadastroWhatsapp = new CRM_CADASTRO_WHATSAPP("tbCadastroWhatsapp");

    private void init_tbCadastroWhatsapp() {
        tbCadastroWhatsapp.setName("tbCadastroWhatsapp");
        tbCadastroWhatsapp.setMaxRowCount(200);
        tbCadastroWhatsapp.setWKey("4600408;46005");
        tbCadastroWhatsapp.setRatioBatchSize(20);
    }

    public CRM_EVENTOS tbEventos = new CRM_EVENTOS("tbEventos");

    private void init_tbEventos() {
        tbEventos.setName("tbEventos");
        tbEventos.setMaxRowCount(200);
        tbEventos.setWKey("4600408;29601");
        tbEventos.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item0 = new TFSchemaItem();
        item0.setTable(tbWhatsappAtendimento);
        sc.getTables().add(item0);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}