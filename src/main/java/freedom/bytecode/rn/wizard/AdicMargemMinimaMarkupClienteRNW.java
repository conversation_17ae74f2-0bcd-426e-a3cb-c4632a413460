/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.6.47
   Class Main  : AdicMargemMinimaMarkupCliente
   Analista    : GIORDANNY
   Data Created: 30/09/2022 15:26:59
   Data Changed: 30/09/2022 15:30:16
   Data Geracao: 30/09/2022 15:40:04
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class AdicMargemMinimaMarkupClienteR<PERSON><PERSON> extends AdicMargemMinimaMarkupClienteRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



