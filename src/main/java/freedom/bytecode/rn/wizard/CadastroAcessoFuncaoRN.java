package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class CadastroAcessoFuncaoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public CadastroAcessoFuncaoRN() {
        init_tbGridCadAcessoFunc();
        init_tbPainelAcessoFuncao();
    }

    public BSC_GRID_CAD_ACESSO_FUNC tbGridCadAcessoFunc = new BSC_GRID_CAD_ACESSO_FUNC("tbGridCadAcessoFunc");

    private void init_tbGridCadAcessoFunc() {
        tbGridCadAcessoFunc.setName("tbGridCadAcessoFunc");
        tbGridCadAcessoFunc.setMaxRowCount(200);
        tbGridCadAcessoFunc.setWKey("382034;38201");
        tbGridCadAcessoFunc.setRatioBatchSize(20);
    }

    public BSC_PAINEL_ACESSO_FUNCAO tbPainelAcessoFuncao = new BSC_PAINEL_ACESSO_FUNCAO("tbPainelAcessoFuncao");

    private void init_tbPainelAcessoFuncao() {
        tbPainelAcessoFuncao.setName("tbPainelAcessoFuncao");
        tbPainelAcessoFuncao.setMaxRowCount(200);
        tbPainelAcessoFuncao.setWKey("382034;38202");
        tbPainelAcessoFuncao.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}