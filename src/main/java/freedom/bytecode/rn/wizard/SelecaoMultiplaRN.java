package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class SelecaoMultiplaRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public SelecaoMultiplaRN() {
        init_tbSelecaoMultipla();
    }

    public SELECAO_MULTIPLA tbSelecaoMultipla = new SELECAO_MULTIPLA("tbSelecaoMultipla");

    private void init_tbSelecaoMultipla() {
        tbSelecaoMultipla.setName("tbSelecaoMultipla");
        tbSelecaoMultipla.setMaxRowCount(200);
        tbSelecaoMultipla.setWKey("5300664;53001");
        tbSelecaoMultipla.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}