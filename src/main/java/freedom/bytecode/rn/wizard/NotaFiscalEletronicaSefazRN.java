package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class NotaFiscalEletronicaSefazRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public NotaFiscalEletronicaSefazRN() {
        init_tbCrmpartsNfeMovimento();
        init_tbMovimento();
        init_tbLeadsNfeMensagem();
    }

    public CRMPARTS_NFE_MOVIMENTO tbCrmpartsNfeMovimento = new CRMPARTS_NFE_MOVIMENTO("tbCrmpartsNfeMovimento");

    private void init_tbCrmpartsNfeMovimento() {
        tbCrmpartsNfeMovimento.setName("tbCrmpartsNfeMovimento");
        tbCrmpartsNfeMovimento.setMaxRowCount(200);
        tbCrmpartsNfeMovimento.setWKey("5300656;53001");
        tbCrmpartsNfeMovimento.setRatioBatchSize(20);
    }

    public NFE_MOVIMENTO tbMovimento = new NFE_MOVIMENTO("tbMovimento");

    private void init_tbMovimento() {
        tbMovimento.setName("tbMovimento");
        tbMovimento.setMaxRowCount(200);
        tbMovimento.setWKey("5300656;53002");
        tbMovimento.setRatioBatchSize(20);
    }

    public LEADS_NFE_MENSAGEM tbLeadsNfeMensagem = new LEADS_NFE_MENSAGEM("tbLeadsNfeMensagem");

    private void init_tbLeadsNfeMensagem() {
        tbLeadsNfeMensagem.setName("tbLeadsNfeMensagem");
        tbLeadsNfeMensagem.setMaxRowCount(200);
        tbLeadsNfeMensagem.setWKey("5300656;53003");
        tbLeadsNfeMensagem.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}