package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class ClientesFlagsServAcessoRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public ClientesFlagsServAcessoRN() {
        init_tbEmpresasFuncoes();
        init_tbClienteFlagGrupo();
        init_tbEmpresasUsuarios();
        init_tbClienteFlagAcesso();
        init_sc();
    }

    public EMPRESAS_FUNCOES tbEmpresasFuncoes = new EMPRESAS_FUNCOES("tbEmpresasFuncoes");

    private void init_tbEmpresasFuncoes() {
        tbEmpresasFuncoes.setName("tbEmpresasFuncoes");
        tbEmpresasFuncoes.setMaxRowCount(200);
        tbEmpresasFuncoes.setWKey("4600456;46001");
        tbEmpresasFuncoes.setRatioBatchSize(20);
    }

    public CLIENTE_FLAG_GRUPO tbClienteFlagGrupo = new CLIENTE_FLAG_GRUPO("tbClienteFlagGrupo");

    private void init_tbClienteFlagGrupo() {
        tbClienteFlagGrupo.setName("tbClienteFlagGrupo");
        tbClienteFlagGrupo.setMaxRowCount(200);
        tbClienteFlagGrupo.setWKey("4600456;46002");
        tbClienteFlagGrupo.setRatioBatchSize(20);
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("4600456;46004");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public CLIENTE_FLAG_ACESSO tbClienteFlagAcesso = new CLIENTE_FLAG_ACESSO("tbClienteFlagAcesso");

    private void init_tbClienteFlagAcesso() {
        tbClienteFlagAcesso.setName("tbClienteFlagAcesso");
        tbClienteFlagAcesso.setMaxRowCount(200);
        tbClienteFlagAcesso.setWKey("4600456;46005");
        tbClienteFlagAcesso.setRatioBatchSize(20);
    }


    public TFSchema sc = new TFSchema();

    private void init_sc() {
        sc.setName("sc");
        TFSchemaItem item5 = new TFSchemaItem();
        item5.setTable(tbClienteFlagAcesso);
        sc.getTables().add(item5);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}