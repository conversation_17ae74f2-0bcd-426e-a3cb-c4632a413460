package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PesquisarEmpresasUsuariosRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PesquisarEmpresasUsuariosRN() {
        init_tbEmpresasUsuarios();
        init_tbFiltroEmpresas();
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("4600265;46001");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public LEADS_EMPRESAS_USUARIOS tbFiltroEmpresas = new LEADS_EMPRESAS_USUARIOS("tbFiltroEmpresas");

    private void init_tbFiltroEmpresas() {
        tbFiltroEmpresas.setName("tbFiltroEmpresas");
        tbFiltroEmpresas.setMaxRowCount(200);
        tbFiltroEmpresas.setWKey("4600265;46002");
        tbFiltroEmpresas.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}