/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.4.58
   Class Main  : CEPClienteOnLine
   Analista    : RAMIRES
   Data Created: 27/03/2020 09:49:56
   Data Changed: 14/04/2020 08:01:23
   Data Geracao: 17/04/2020 10:05:29
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class CEPClienteOnLineRNW extends CEPClienteOnLineRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



