package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class FichaItemResAvulsaRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public FichaItemResAvulsaRN() {
        init_tbEstoque();
        init_tbItensReservasPendencias();
        init_tbEmpresasUsuarios();
        init_tbFornecedorEstoqueItem();
        init_tbItens();
        init_tbLeadsEmpresasUsuarios();
        init_scFornecedorEstoqueItem();
    }

    public ESTOQUE tbEstoque = new ESTOQUE("tbEstoque");

    private void init_tbEstoque() {
        tbEstoque.setName("tbEstoque");
        tbEstoque.setMaxRowCount(200);
        tbEstoque.setWKey("310046;31001");
        tbEstoque.setRatioBatchSize(20);
    }

    public ITENS_RESERVAS_PENDENCIAS tbItensReservasPendencias = new ITENS_RESERVAS_PENDENCIAS("tbItensReservasPendencias");

    private void init_tbItensReservasPendencias() {
        tbItensReservasPendencias.setName("tbItensReservasPendencias");
        tbItensReservasPendencias.setMaxRowCount(200);
        tbItensReservasPendencias.setWKey("310046;31002");
        tbItensReservasPendencias.setRatioBatchSize(20);
    }

    public EMPRESAS_USUARIOS tbEmpresasUsuarios = new EMPRESAS_USUARIOS("tbEmpresasUsuarios");

    private void init_tbEmpresasUsuarios() {
        tbEmpresasUsuarios.setName("tbEmpresasUsuarios");
        tbEmpresasUsuarios.setMaxRowCount(200);
        tbEmpresasUsuarios.setWKey("310046;31003");
        tbEmpresasUsuarios.setRatioBatchSize(20);
    }

    public FORNECEDOR_ESTOQUE_ITEM tbFornecedorEstoqueItem = new FORNECEDOR_ESTOQUE_ITEM("tbFornecedorEstoqueItem");

    private void init_tbFornecedorEstoqueItem() {
        tbFornecedorEstoqueItem.setName("tbFornecedorEstoqueItem");
        tbFornecedorEstoqueItem.setMaxRowCount(200);
        tbFornecedorEstoqueItem.setWKey("310046;53001");
        tbFornecedorEstoqueItem.setRatioBatchSize(20);
    }

    public ITENS tbItens = new ITENS("tbItens");

    private void init_tbItens() {
        tbItens.setName("tbItens");
        tbItens.setMaxRowCount(200);
        tbItens.setWKey("310046;53003");
        tbItens.setRatioBatchSize(20);
    }

    public LEADS_EMPRESAS_USUARIOS tbLeadsEmpresasUsuarios = new LEADS_EMPRESAS_USUARIOS("tbLeadsEmpresasUsuarios");

    private void init_tbLeadsEmpresasUsuarios() {
        tbLeadsEmpresasUsuarios.setName("tbLeadsEmpresasUsuarios");
        tbLeadsEmpresasUsuarios.setMaxRowCount(200);
        tbLeadsEmpresasUsuarios.setWKey("310046;53004");
        tbLeadsEmpresasUsuarios.setRatioBatchSize(20);
    }


    public TFSchema scFornecedorEstoqueItem = new TFSchema();

    private void init_scFornecedorEstoqueItem() {
        scFornecedorEstoqueItem.setName("scFornecedorEstoqueItem");
        TFSchemaItem item46 = new TFSchemaItem();
        item46.setTable(tbFornecedorEstoqueItem);
        scFornecedorEstoqueItem.getTables().add(item46);
        TFSchemaItem item47 = new TFSchemaItem();
        item47.setTable(tbItens);
        scFornecedorEstoqueItem.getTables().add(item47);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}