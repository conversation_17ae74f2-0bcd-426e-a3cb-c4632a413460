/* -------------------------------------------------------------------------
   Projeto Freedom - Server - Versao: 1.0.7.18
   Class Main  : GerencialPainelFiltro
   Analista    : JOANDERSON.GUARIM
   Data Created: 29/04/2024 08:05:08
   Data Changed: 29/04/2024 08:38:14
   Data Geracao: 29/04/2024 08:38:30
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class GerencialPainelFiltroRNW extends GerencialPainelFiltroRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



