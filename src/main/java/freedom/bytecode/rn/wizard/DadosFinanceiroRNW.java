/* -------------------------------------------------------------------------
   Pro<PERSON>o Freedom - Server - Versao: ********
   Class Main  : DadosFinanceiro
   Analista    : LUIZ.RIPARDO
   Data Created: 06/09/2022 00:00:00
   Data Changed: 27/03/2023 11:12:40
   Data Geracao: 27/03/2023 11:13:22
  -------------------------------------------------------------------------- */

package freedom.bytecode.rn.wizard;

import freedom.connection.ISession;
import freedom.connection.SessionFactory;
import freedom.data.DataException;
import freedom.data.impl.DeltaTO;
import freedom.data.RowState;

public abstract class DadosFinanceiroRNW extends DadosFinanceiroRN {
    
    public ISession getSession() throws DataException {
	ISession s = SessionFactory.getInstance().getSession();
        s.open();
        return s;
    }

    public void closeSession(ISession s) throws DataException {
        s.close();
    }

}



