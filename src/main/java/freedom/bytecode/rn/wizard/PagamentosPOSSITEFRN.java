package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class PagamentosPOSSITEFRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public PagamentosPOSSITEFRN() {
        init_tbEmpresas();
        init_tbPagamentosPosSitef();
        init_tbParcelasPgtoPosSitef();
    }

    public LEADS_EMPRESAS_USUARIOS tbEmpresas = new LEADS_EMPRESAS_USUARIOS("tbEmpresas");

    private void init_tbEmpresas() {
        tbEmpresas.setName("tbEmpresas");
        tbEmpresas.setMaxRowCount(200);
        tbEmpresas.setWKey("53601464;53601");
        tbEmpresas.setRatioBatchSize(20);
    }

    public BUSCA_PAGAMENTOS_POS_SITEF tbPagamentosPosSitef = new BUSCA_PAGAMENTOS_POS_SITEF("tbPagamentosPosSitef");

    private void init_tbPagamentosPosSitef() {
        tbPagamentosPosSitef.setName("tbPagamentosPosSitef");
        tbPagamentosPosSitef.setMaxRowCount(200);
        tbPagamentosPosSitef.setWKey("53601464;53602");
        tbPagamentosPosSitef.setRatioBatchSize(20);
    }

    public BUSCA_PARCELAS_PGTO_POS_SITEF tbParcelasPgtoPosSitef = new BUSCA_PARCELAS_PGTO_POS_SITEF("tbParcelasPgtoPosSitef");

    private void init_tbParcelasPgtoPosSitef() {
        tbParcelasPgtoPosSitef.setName("tbParcelasPgtoPosSitef");
        tbParcelasPgtoPosSitef.setMaxRowCount(200);
        tbParcelasPgtoPosSitef.setWKey("53601464;53603");
        tbParcelasPgtoPosSitef.setRatioBatchSize(20);
    }


    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}