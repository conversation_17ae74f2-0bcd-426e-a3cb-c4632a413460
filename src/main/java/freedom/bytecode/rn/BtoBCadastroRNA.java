package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.BtoBCadastroRNW;
import freedom.client.util.FreedomUtilities;
import freedom.data.SequenceUtil;
public class BtoBCadastroRNA extends BtoBCadastroRNW  {
    private static final long serialVersionUID = 20130827081850L;
    
    @Override
    public void setPKFK() throws DataException {        
        /*if (tbBtobLink.getID_LINK().asString().trim().isEmpty()){
            tbBtobLink.edit();
            tbBtobLink.setID_LINK(SequenceUtil.nextVal("SEQ_BTOB_LINK"));
            tbBtobLink.post();
        }*/
    }
}
