package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.SistemaAcessoRNW;
import freedom.data.DataException;

public class SistemaAcessoRNA extends SistemaAcessoRNW {

    private static final long serialVersionUID = 20130827081850L;

    public boolean verificarAcesso(String usuario, String codAcesso) throws DataException {

        tbVerificarAcesso.close();
        tbVerificarAcesso.clearFilters();
        tbVerificarAcesso.clearParams();
        tbVerificarAcesso.addFilter("USUARIO; COD_ACESSO");
        tbVerificarAcesso.addParam("USUARIO", usuario);
        tbVerificarAcesso.addParam("COD_ACESSO", codAcesso);
        tbVerificarAcesso.open();

        return tbVerificarAcesso.getACESSO().asString().trim().equals("S");
    }

}
