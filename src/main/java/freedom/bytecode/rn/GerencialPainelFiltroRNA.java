package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.GerencialPainelFiltroRNW;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;

public class GerencialPainelFiltroRNA extends GerencialPainelFiltroRNW {
    private static final long serialVersionUID = 20130827081850L;
    private final String usuarioLogado = EmpresaUtil.getUserLogged();
    private final Integer codEmpresaUsuarioLog = EmpresaUtil.getCodEmpresaUserLogged();

    public void carregaPainelUsuario() throws DataException {
        tbPainelGerencialUsuario.close();
        tbPainelGerencialUsuario.clearFilters();
        tbPainelGerencialUsuario.clearParams();
        tbPainelGerencialUsuario.addFilter("ATIVO;USUARIO_LOGADO");
        tbPainelGerencialUsuario.addParam("ATIVO", "S");
        tbPainelGerencialUsuario.addParam("USUARIO_LOGADO", usuarioLogado);
        tbPainelGerencialUsuario.open();
    }

    public void carregaComboEmpresa() throws DataException {
        tbEmpresaCruzadasFuncao.close();
        tbEmpresaCruzadasFuncao.clearFilters();
        tbEmpresaCruzadasFuncao.clearParams();
        tbEmpresaCruzadasFuncao.addFilter("USUARIO_LOGADO;COD_EMPRESA");
        tbEmpresaCruzadasFuncao.addParam("USUARIO_LOGADO", usuarioLogado);
        tbEmpresaCruzadasFuncao.addParam("COD_EMPRESA", codEmpresaUsuarioLog);
        tbEmpresaCruzadasFuncao.open();

        tbAno.close();
        tbAno.open();
    }
}
