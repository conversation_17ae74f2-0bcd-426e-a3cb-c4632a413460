package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.AlterarItemBasicoRNW;
import freedom.data.DataException;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;

public class AlterarItemBasicoRNA extends AlterarItemBasicoRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public void filtrarItem(
            String codItem
            ,double codFornecedor
    ) throws DataException {
        this.tbItens.close();
        this.tbItens.clearFilters();
        this.tbItens.clearParams();
        this.tbItens.setFilterCOD_ITEM(
                codItem
        );
        this.tbItens.open();
        this.tbItensFornecedor.close();
        this.tbItensFornecedor.clearFilters();
        this.tbItensFornecedor.clearParams();
        this.tbItensFornecedor.setFilterCOD_ITEM(
                codItem
        );
        this.tbItensFornecedor.setFilterCOD_FORNECEDOR(
                codFornecedor
        );
        this.tbItensFornecedor.open();
    }

    public void filtrarGrupo() throws DataException {
        this.tbItensGrupoInterno.close();
        this.tbItensGrupoInterno.clearFilters();
        this.tbItensGrupoInterno.clearParams();
        this.tbItensGrupoInterno.open();
    }

    public void filtrarSubGrupo(int codGrupo) throws DataException {
        this.tbItensSubGrupo.close();
        this.tbItensSubGrupo.clearFilters();
        this.tbItensSubGrupo.clearParams();
        this.tbItensSubGrupo.setFilterCOD_GRUPO_INTERNO(codGrupo);
        this.tbItensSubGrupo.open();
    }

    public void filtrarFabricante() throws DataException {
        this.tbFabricante.close();
        this.tbFabricante.setMaxRowCount(0);
        this.tbFabricante.clearFilters();
        this.tbFabricante.clearParams();
        this.tbFabricante.setOrderBy("NOME");
        this.tbFabricante.open();
    }

    public void filtrarMarca() throws DataException {
        this.tbMarca.close();
        this.tbMarca.setMaxRowCount(0);
        this.tbMarca.clearFilters();
        this.tbMarca.clearParams();
        this.tbMarca.setOrderBy("DESCRICAO");
        this.tbMarca.open();
    }

    public void filtrarUnidadeMedida() throws DataException {
        this.tbItensUnidadeMedida.close();
        this.tbItensUnidadeMedida.clearFilters();
        this.tbItensUnidadeMedida.clearParams();
        this.tbItensUnidadeMedida.open();
    }

    public void filtrarSituacaoEspecial() throws DataException {
        this.tbItemSituacaoEspecial.close();
        this.tbItemSituacaoEspecial.clearFilters();
        this.tbItemSituacaoEspecial.clearParams();
        this.tbItemSituacaoEspecial.open();
    }

    public void filtrarLetraDesconto() throws DataException {
        this.tbListaLetraDescontoGeral.close();
        this.tbListaLetraDescontoGeral.clearFilters();
        this.tbListaLetraDescontoGeral.clearParams();
        this.tbListaLetraDescontoGeral.open();
    }

    public void salvar() throws DataException {
        EmpresaUtil empresaUtil = new EmpresaUtil();
        empresaUtil.salvar(
                this.sc
        );
    }

    public void carregarOrigem() throws DataException {
        this.tbItensOrigem.close();
        this.tbItensOrigem.clearFilters();
        this.tbItensOrigem.clearParams();
        this.tbItensOrigem.open();
    }

    /**
     * @param loginUsuario Login do usuário cujo código do acesso será verificado<hr>
     * @param codAcesso Código do acesso que será verificado para o login do usuário<br><hr>
     * @return Se tiver o acesso será retornado "S". Senão será retornada uma mensagem para exibir para o usuário.<br><hr>
     * @throws DataException Para erros de banco
     */
    public String validarAcesso(String loginUsuario,
                                String codAcesso) throws DataException {
        return this.pkgCrmPartsRNA.validarAcesso(loginUsuario,
                codAcesso);
    }

    public void atualizarItensFornecedorComprasBloqueadas(
            String comprasBloqueadasSN
    ) throws DataException {
        this.tbItensFornecedor.edit();
        this.tbItensFornecedor.setCOMPRAS_BLOQUEADAS(
                comprasBloqueadasSN
        );
        this.tbItensFornecedor.post();
    }

}