package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.PesquisaFormaPgtoRNW;
import freedom.data.DataException;
import lombok.Setter;

public class PesquisaFormaPgtoRNA extends PesquisaFormaPgtoRNW  {

    @Setter
    private double codEmpresaFiltrada;

    @Setter
    private double codDepartamentoFiltrado;

    @Setter
    private double codFormaDePagamentoFiltrada;

    private double codClienteFiltrado;

    @Setter
    private String codTipoFormaPagamentoFiltrada;

    @Setter
    private String exclusivaFiltrada;

    private static final long serialVersionUID = 20130827081850L;

    public PesquisaFormaPgtoRNA() {
        this.codEmpresaFiltrada = 0.0;
        this.codDepartamentoFiltrado = 0.0;
        this.codFormaDePagamentoFiltrada = 0.0;
        this.codTipoFormaPagamentoFiltrada = "";
        this.exclusivaFiltrada = "";
    }

    public void carregarEmpresasDoFiltro() throws DataException {
        boolean tbEmpresasFiliaisSelActive = this.tbEmpresasFiliaisSel.isActive();
        if (tbEmpresasFiliaisSelActive) {
            this.tbEmpresasFiliaisSel.close();
        }
        this.tbEmpresasFiliaisSel.clearFilters();
        this.tbEmpresasFiliaisSel.clearParams();
        this.tbEmpresasFiliaisSel.open();
    }

    public void carregarDepartamentosDoFiltro() throws DataException {
        boolean tbListaEmpresasDepartamentosActive = this.tbListaEmpresasDepartamentos.isActive();
        if (tbListaEmpresasDepartamentosActive) {
            this.tbListaEmpresasDepartamentos.close();
        }
        this.tbListaEmpresasDepartamentos.clearFilters();
        this.tbListaEmpresasDepartamentos.clearParams();
        if (this.codEmpresaFiltrada > 0.0) {
            this.tbListaEmpresasDepartamentos.setFilterEMP_CODIGO_EQUALS(
                    this.codEmpresaFiltrada);
            this.tbListaEmpresasDepartamentos.setOrderBy(
                    "DPTO_DESCRICAO"
            );
            this.tbListaEmpresasDepartamentos.open();
        }
    }

    public void carregarTiposDeFormasDePagamentoDoFiltro() throws DataException {
        boolean tbListaTipoPgtoNbsActive = this.tbListaTipoPgtoNbs.isActive();
        if (tbListaTipoPgtoNbsActive) {
            this.tbListaTipoPgtoNbs.close();
        }
        this.tbListaTipoPgtoNbs.clearFilters();
        this.tbListaTipoPgtoNbs.clearParams();
        this.tbListaTipoPgtoNbs.open();
    }

    public void carregarFormasDePagamentoDoFiltro() throws DataException {
        boolean tbListaFormasPgto1Active = this.tbListaFormasPgto1.isActive();
        if (tbListaFormasPgto1Active) {
            this.tbListaFormasPgto1.close();
        }
        this.tbListaFormasPgto1.clearFilters();
        this.tbListaFormasPgto1.clearParams();
        if (!((this.codEmpresaFiltrada > 0.0)
                && (this.codDepartamentoFiltrado > 0.0))) {
            return;
        }
        this.tbListaFormasPgto1.setFilterEMP_CODIGO(
                this.codEmpresaFiltrada
        );
        this.tbListaFormasPgto1.setFilterDPTO_CODIGO(
                this.codDepartamentoFiltrado
        );
        boolean codTipoFormaPagamentoFiltradaNotEmpty = !this.codTipoFormaPagamentoFiltrada.isEmpty();
        if (codTipoFormaPagamentoFiltradaNotEmpty) {
            this.tbListaFormasPgto1.setFilterFORMA_TIPO_CODIGO(
                    this.codTipoFormaPagamentoFiltrada
            );
        }
        boolean exclusivaFiltradaNotEmpty = !this.exclusivaFiltrada.isEmpty();
        if (exclusivaFiltradaNotEmpty) {
            this.tbListaFormasPgto1.setFilterEH_EXCLUSIVA_CLIENTE(
                    this.exclusivaFiltrada
            );
        }
        this.tbListaFormasPgto1.setOrderBy("FORMA_DESCRICAO_COD");
        this.tbListaFormasPgto1.open();
    }

    public void carregarFormasDePagamentoDaGrade() throws DataException {
        boolean tbListaFormasPgtoActive = this.tbListaFormasPgto.isActive();
        if (tbListaFormasPgtoActive) {
            this.tbListaFormasPgto.close();
        }
        this.tbListaFormasPgto.clearFilters();
        this.tbListaFormasPgto.clearParams();
        this.tbListaFormasPgto.setFilterCOD_CLIENTE_FILTRADO(
                this.codClienteFiltrado
        );
        if (this.codEmpresaFiltrada > 0.0) {
            this.tbListaFormasPgto.setFilterEMP_CODIGO(
                    this.codEmpresaFiltrada
            );
        }
        if (this.codDepartamentoFiltrado > 0.0) {
            this.tbListaFormasPgto.setFilterDPTO_CODIGO(
                    this.codDepartamentoFiltrado
            );
        }
        boolean codTipoFormaPagamentoFiltradaNotEmpty = !this.codTipoFormaPagamentoFiltrada.isEmpty();
        if (codTipoFormaPagamentoFiltradaNotEmpty) {
            this.tbListaFormasPgto.setFilterFORMA_TIPO_CODIGO(
                    this.codTipoFormaPagamentoFiltrada
            );
        }
        boolean exclusivaFiltradaNotEmpty = !this.exclusivaFiltrada.isEmpty();
        if (exclusivaFiltradaNotEmpty) {
            this.tbListaFormasPgto.setFilterEH_EXCLUSIVA_CLIENTE(
                    this.exclusivaFiltrada
            );
        }
        if (this.codFormaDePagamentoFiltrada > 0.0) {
            this.tbListaFormasPgto.setFilterFORMA_CODIGO(
                    this.codFormaDePagamentoFiltrada
            );
        }
        this.tbListaFormasPgto.setFilterATIVA(
                "S"
        );
        this.tbListaFormasPgto.setOrderBy(
                "EMP_NOME, DPTO_DESCRICAO, FORMA_DESCRICAO"
        );
        this.tbListaFormasPgto.open();
    }

    public void setCodClienteFiltrado(
            long codClienteFiltrado
    ) {
        this.codClienteFiltrado = codClienteFiltrado;
    }
}
