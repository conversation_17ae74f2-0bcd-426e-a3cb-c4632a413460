package freedom.bytecode.rn;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.client.event.*;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.ResultCreditoCorporativoRNW;
public class ResultCreditoCorporativoRNA extends ResultCreditoCorporativoRNW  {
    private static final long serialVersionUID = 20130827081850L;

    public void abreConsultaClienteCreditoCorporativo(Double codCliente) {
        tbResultCC.close();
        tbResultCC.addParam("COD_CLIENTE", codCliente);
        try {
            tbResultCC.open();
        } catch (DataException e) {
            tbResultCC.close();
        }
    }
}
