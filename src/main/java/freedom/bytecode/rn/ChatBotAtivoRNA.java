package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.ChatBotAtivoRNW;
import freedom.data.DataException;
import freedom.data.SequenceUtil;
import freedom.data.impl.View;

public class ChatBotAtivoRNA extends ChatBotAtivoRNW {
    private static final long serialVersionUID = 20130827081850L;
    int idDisparo = 0;

    public boolean nenhumRegistroGridDisparoSelecionado() throws DataException {
        return tbDisparo2.isEmpty();
    }

    public boolean nenhumRegistroGridDisparoChatBotSelecionado() throws DataException {
        return tbDisparoChatbot.isEmpty();
    }

    public boolean nenhumRegistroGridDisparoTemplateEmpresaSelecionado() throws DataException {
        return tbDisparoTemplateEmpresa.isEmpty();
    }

    public void carregarEmailModelo() throws DataException {
        tbEmailModelo.close();
        tbEmailModelo.clearFilters();
        tbEmailModelo.clearParams();
        tbEmailModelo.setOrderBy("MODELO");
        tbEmailModelo.open();
    }

    public void carregarCadastroWhatsapp() throws DataException {
        tbCadastroWhatsapp.close();
        tbCadastroWhatsapp.clearFilters();
        tbCadastroWhatsapp.clearParams();
        tbCadastroWhatsapp.setOrderBy("ID_CELULAR");
        tbCadastroWhatsapp.open();
    }

    public void carregarDescartes() throws DataException {
        tbDescartes.close();
        tbDescartes.clearFilters();
        tbDescartes.clearParams();
        tbDescartes.setOrderBy("DESCRICAO_DESCARTE");
        tbDescartes.open();
    }

    public void carregarEmpresas() throws DataException {
        tbEmpresas.close();
        tbEmpresas.clearFilters();
        tbEmpresas.clearParams();
        tbEmpresas.setFilterCOD_MATRIZ("");
        tbEmpresas.setFilterSTATUS("S");
        tbEmpresas.setOrderBy("NOME");
        tbEmpresas.open();
    }


    public void carregarGridDisparo(String Pdescricao, boolean PAtivo) throws DataException {
        if (!tbDisparo2.isEmpty()) {
            idDisparo = tbDisparo2.getID_DISPARO().asInteger();
        }
        tbDisparo2.close();
        tbDisparo2.setOrderBy("DESCRICAO");
        tbDisparo2.clearFilters();
        tbDisparo2.setFilterSISTEMA("S");
        if (PAtivo) {
            tbDisparo2.setFilterATIVO("S");
        } else {
            tbDisparo2.setFilterATIVO("N");
        }
        if (!Pdescricao.isEmpty()) {
            tbDisparo2.setFilterDESCRICAO(Pdescricao);
        }
        tbDisparo2.open();
        tbDisparo2.locate("ID_DISPARO", idDisparo);
    }

    public void selecionarRegistroAnteriorGridDisparo() throws DataException {
        tbDisparo2.locate("ID_DISPARO", idDisparo);
    }

    public void carregarDadosDisparoChatBot() throws DataException {
        tbDisparoChatbot.close();
        tbDisparoChatbot.clearFilters();
        tbDisparoChatbot.clearParams();
        tbDisparoChatbot.setFilterID_DISPARO(tbDisparo2.getID_DISPARO().asInteger());
        tbDisparoChatbot.setOrderBy("ID_ITEM");
        tbDisparoChatbot.open();
    }

    public void carregarDadosDisparoTemplateEmpresa() throws DataException {
        tbDisparoTemplateEmpresa.close();
        tbDisparoTemplateEmpresa.clearFilters();
        tbDisparoTemplateEmpresa.clearParams();
        tbDisparoTemplateEmpresa.setFilterID_DISPARO(tbDisparo2.getID_DISPARO().asInteger());
        tbDisparoTemplateEmpresa.setOrderBy("COD_EMPRESA");
        tbDisparoTemplateEmpresa.open();
    }

    public void alterarDisparo() throws DataException {
        tbDisparo2.edit();
    }

    public void cancelarAlteracaoDisparo() throws DataException {
        tbDisparo2.cancel();
        tbDisparoChatbot.cancel();
        tbDisparoTemplateEmpresa.cancel();
        tbDisparo2.cancelUpdates();
        tbDisparoChatbot.cancelUpdates();
        tbDisparoTemplateEmpresa.cancelUpdates();
    }

    public void salvarDisparo() throws DataException {
        idDisparo = tbDisparo2.getID_DISPARO().asInteger();
        tbDisparo2.post();
        tbDisparo2.applyUpdates();
        tbDisparo2.commitUpdates();
        tbDisparoChatbot.applyUpdates();
        tbDisparoChatbot.commitUpdates();
        tbDisparoTemplateEmpresa.applyUpdates();
        tbDisparoTemplateEmpresa.commitUpdates();
    }

    public void salvarChatBot() throws DataException {
        tbDisparoChatbot.post();
    }

    public void alterarChatBot() throws DataException {
        tbDisparoChatbot.edit();
    }

    public void cancelarChatBot() throws DataException {
        tbDisparoChatbot.cancel();
    }

    public Integer validarMascara() throws DataException {
        Integer mascara = tbDisparoChatbot.getMASCARA().asInteger();
        Integer idItem = tbDisparoChatbot.getID_ITEM().asInteger();

        View v = tbDisparoChatbot.getView();
        v.first();
        while (!v.eof() && !tbDisparoChatbot.getMASCARA().isNull()) {
            if (!v.getField("ID_ITEM").asInteger().equals(idItem)) {
                if (mascara.equals(v.getField("MASCARA").asInteger())) {
                    return v.getField("ID_ITEM").asInteger();
                }
            }
            v.next();
        }
        return -1;
    }

    public void novoTemplate() throws DataException {
        tbDisparoTemplateEmpresa.append();
        tbDisparoTemplateEmpresa.setDISPARO_TEMPLATE_EMPRESA_ID(SequenceUtil.nextVal("DISPARO_TEMPLATE_EMPRESA_SEQ"));
        tbDisparoTemplateEmpresa.setID_DISPARO(tbDisparo2.getID_DISPARO().asInteger());
    }

    public void salvarTemplate(String nomeEmpresa) throws DataException {
        tbDisparoTemplateEmpresa.edit();
        tbDisparoTemplateEmpresa.setCOD_EMPRESA_NOME_EMPRESA(nomeEmpresa);
        tbDisparoTemplateEmpresa.post();
    }

    public void alterarTemplate() throws DataException {
        tbDisparoTemplateEmpresa.edit();
    }

    public void cancelarTemplate() throws DataException {
        tbDisparoTemplateEmpresa.cancel();
        tbDisparoTemplateEmpresa.refreshRecord();
    }

    public void deletarNovoTempleteCancelado() throws DataException {
        tbDisparoTemplateEmpresa.delete();
    }

    public boolean validarExistenciaEmpresaTemplate(int codEmpresa, int idTemplate) throws DataException {
        tbDisparoTemplateEmpresa.edit();
        tbDisparoTemplateEmpresa.post();
        View v = tbDisparoTemplateEmpresa.getView().select("COD_EMPRESA = " + codEmpresa + " AND TEMPLATE = " + idTemplate);

        if (v.count() > 1) {
            return true;
        }
        return false;
    }

    public void excluirDisparoTemplateEmpresa() throws Exception {
        tbDisparoTemplateEmpresa.delete();
    }

    public String validarEmpresaXTemplete(int codEmpresa, int idTemplate) throws DataException {

        View v = tbEmailModelo.getView().select("ID_EMAIL_MODELO = " + idTemplate);

        if (!v.getField("STATUS_ZENVIA").asString().equals("Aprovado")) {
            return "Este template não está Aprovado e portanto não pode ser utilizado pela empresa";
        }

        String numeroCelular = v.getField("CELULAR_APROVOU_TEMPLATE").asString();

        numeroCelular.replace("+", "");
        if (numeroCelular.substring(0, 2).equals("55")) {
            numeroCelular = numeroCelular.substring(2);
        }

        tbCadastroWhatsapp.close();
        tbCadastroWhatsapp.clearFilters();
        tbCadastroWhatsapp.clearParams();
        tbCadastroWhatsapp.setFilterCELULAR(numeroCelular);
        tbCadastroWhatsapp.open();

        if (tbCadastroWhatsapp.count() == 0) {
            tbCadastroWhatsapp.close();
            tbCadastroWhatsapp.clearFilters();
            tbCadastroWhatsapp.clearParams();
            tbCadastroWhatsapp.setFilterCELULAR("55" + numeroCelular);
            tbCadastroWhatsapp.open();
        }

        tbWhatsappEmpresa.close();
        tbWhatsappEmpresa.clearFilters();
        tbWhatsappEmpresa.clearParams();
        tbWhatsappEmpresa.setFilterID_CELULAR(tbCadastroWhatsapp.getID_CELULAR().asInteger());
        tbWhatsappEmpresa.setFilterCOD_EMPRESA(codEmpresa);

        tbWhatsappEmpresa.open();

        if (!(tbWhatsappEmpresa.count() > 0)) {
            return "A empresa informada para este template não está vinculada ao " +
                    "cadastro de leadzap. Vincule essa empresa ao cadastro de leadzap" +
                    " antes de continuar.";
        }
        return "";
    }
    //ao salvar fazer validação:
    //1 - pegar id template, pesquisar o status se está autorizado
    //2 - pegar o numero de celular vinculado ao template
    //4 - filtrar esse celualar na tabela cadastroWhatsapp
    //5 - pegar o id_celular e filtrar na tabela whatsapp_empresa

}
