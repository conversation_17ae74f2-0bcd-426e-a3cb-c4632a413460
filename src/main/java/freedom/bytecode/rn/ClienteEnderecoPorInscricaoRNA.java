package freedom.bytecode.rn;

import freedom.bytecode.cursor.CIDADES;
import freedom.bytecode.rn.wizard.ClienteEnderecoPorInscricaoRNW;
import freedom.data.DataException;
import freedom.data.Value;
import freedom.util.pkg.PkgCrmPartsRNA;
import org.apache.commons.lang.StringUtils;

public class ClienteEnderecoPorInscricaoRNA extends ClienteEnderecoPorInscricaoRNW {

    private static final long serialVersionUID = 20130827081850L;

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    public boolean consultaCliente(Double codCliente) throws DataException {

        tbClientes.close();
        tbClientes.setFilterCOD_CLIENTE(codCliente);
        tbClientes.open();

        tbClienteDiverso.close();
        tbClienteDiverso.setFilterCOD_CLIENTE(codCliente);
        tbClienteDiverso.open();

        tbDadosFisicos.close();
        tbDadosFisicos.setFilterCOD_CLIENTE(codCliente);
        tbDadosFisicos.open();

        refreshEnderecosPorInscricao(codCliente);

        return !tbClientes.isEmpty();
    }

    public void refreshEnderecosPorInscricao(Double codCliente) throws DataException {
        tbClienteEnderecoInscricao.close();
        tbClienteEnderecoInscricao.setFilterCOD_CLIENTE(codCliente);
        tbClienteEnderecoInscricao.open();
    }


    public boolean usaPluginConsultaNBS(double codEmpresa) {
        try {
            return pkgCrmPartsRNA.getParametro(codEmpresa, "PARM_SYS3", "USA_PLUGIN_CONSULTA_NBS").equalsIgnoreCase("S");
        } catch (DataException e) {
            return false;
        }
    }

    public String getUrlConsultaNbs(double codEmpresa) {
        try {
            return pkgCrmPartsRNA.getUrlPluginConsultaNbs(codEmpresa);
        } catch (DataException e) {
            return "";
        }
    }

    public String podeExcluirEnderecoPorInscricao(Double codCliente, String inscricaoEstadual) {
        try {
            return pkgCrmPartsRNA.podeExcluirEndPorInscricao(codCliente, inscricaoEstadual);
        } catch (DataException e) {
            return "Não foi possível verificar a exclusão do endereço! " + e.getMessage();
        }
    }

    public String excluirEnderecoPorInscricao(Double codCliente, String inscricaoEstadual) throws DataException {
            return pkgCrmPartsRNA.excluirEndPorInscricao(codCliente, inscricaoEstadual);
        }

    public boolean buscaDadosClientePf(String documento, String inscricao) throws DataException {
        tbConsultaNbsSintegraDados.close();
        tbConsultaNbsPessoaFisica.close();

        tbConsultaNbsPessoaFisica.setFilterCPF(documento);
        tbConsultaNbsPessoaFisica.open();

        if (StringUtils.isBlank(inscricao)){
            return !tbConsultaNbsPessoaFisica.isEmpty();
        }
        tbConsultaNbsSintegraDados.setFilterCPF_CNPJ(documento);
        tbConsultaNbsSintegraDados.setFilterINSCRICAO_ESTADUAL(inscricao);
        tbConsultaNbsSintegraDados.open();
        return !tbConsultaNbsSintegraDados.isEmpty();
    }

    public void pesquisarCidadesUf(CIDADES tbCidade,
                                   String uf) throws DataException {
        boolean tbCidadeEmpty = tbCidade.isEmpty();
        if (!tbCidade.getUF().asString().equals(uf)
                || tbCidadeEmpty) {
            tbCidade.close();
            tbCidade.setMaxRowCount(0);
            tbCidade.clearParams();
            tbCidade.clearFilters();
            tbCidade.setFilterUF(uf);
            tbCidade.open();
        }
    }

    public void openConsultaDadosRetornoApi(Double idCns,
                                            String tipoPessoa) throws DataException {
        if (tipoPessoa.equals("J")) {
            tbConsultaNbsPessoaJuridica.close();
            tbConsultaNbsPessoaJuridica.setFilterID_CONSULTA_NBS(idCns);
            tbConsultaNbsPessoaJuridica.open();
        } else {
            tbConsultaNbsPessoaFisica.close();
            tbConsultaNbsPessoaFisica.setFilterID_CONSULTA_NBS(idCns);
            tbConsultaNbsPessoaFisica.open();
        }

        tbConsultaNbsSintegraDados.close();
        tbConsultaNbsSintegraDados.setFilterID_CONSULTA_NBS(idCns);
        tbConsultaNbsSintegraDados.open();

        tbConsultaNbsSintegraSimples.close();
        tbConsultaNbsSintegraSimples.setFilterID_CONSULTA_NBS(idCns);
        tbConsultaNbsSintegraSimples.open();
        /* Tratar a consulta aos dados Sintegra*/
        /*Tratar consulta aos erros do sistema*/
    }

    public String getSchemaAtual() throws DataException {

        tbSchemaAtual.close();
        tbSchemaAtual.clearFilters();
        tbSchemaAtual.clearParams();
        tbSchemaAtual.open();

        return tbSchemaAtual.getSCHEMA_NAME().asString();
    }

    public String infoSituacaoCadastralIntegracao(double codEmpresa,
                                                  double codClienteApi,
                                                  String tipoPessoa,
                                                  String ie,
                                                  Value aRfbSituacao,
                                                  Value aRfbCadastroIrregular,
                                                  Value aSintegraSituacao,
                                                  Value aSintegraCadastroIsento,
                                                  Value aSintegraCadastroIrregular,
                                                  Value aSintegraMultiplasIe) throws DataException {
        return pkgCrmPartsRNA.getSituacaoCadastral(codEmpresa,
                codClienteApi,
                tipoPessoa,
                ie,
                aRfbSituacao,
                aRfbCadastroIrregular,
                aSintegraSituacao,
                aSintegraCadastroIsento,
                aSintegraCadastroIrregular,
                aSintegraMultiplasIe);
    }

    public boolean podeAlterarCadastro(String usuarioLogado) {
        try {
            String retFuncao = this.pkgCrmPartsRNA.validarAcesso(usuarioLogado,
                    "K0234");
            return retFuncao.equals("S");
        } catch (DataException dataException) {
            return false;
        }
    }

    public boolean podeAlterarFlagProdutorRural(String usuarioLogado) {
        try {
            String retFuncao = this.pkgCrmPartsRNA.validarAcesso(usuarioLogado,
                    "K0283");
            return retFuncao.equals("S");
        } catch (DataException dataException) {
            return false;
        }

    }

    public String aplicarFlagProdutorRural() {
        try {
            this.tbClienteDiverso.post();
            this.tbClienteDiverso.applyUpdates();
            this.tbClienteDiverso.commitUpdates();
            return "S";
        } catch (DataException dataException) {
            return "Falhou marcar flag produtor Rural"
                    + System.lineSeparator()
                    + dataException.getMessage();
        }
    }

}
