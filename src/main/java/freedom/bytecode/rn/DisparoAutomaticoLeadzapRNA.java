package freedom.bytecode.rn;

import freedom.bytecode.rn.wizard.DisparoAutomaticoLeadzapRNW;
import freedom.data.DataException;
import freedom.data.impl.View;

public class DisparoAutomaticoLeadzapRNA extends DisparoAutomaticoLeadzapRNW {

    private static final long serialVersionUID = 20130827081850L;

    public void filtrarDescartes() throws DataException {
        tbDescartes.close();
        tbDescartes.clearFilters();
        tbDescartes.clearParams();
        tbDescartes.addFilter("ATIVO");
        tbDescartes.addParam("ATIVO", "S");
        tbDescartes.open();
    }

    public void filtrarCadastroWhatsApp() throws DataException {
        tbCadastroWhatsapp.close();
        tbCadastroWhatsapp.clearFilters();
        tbCadastroWhatsapp.clearParams();
        tbCadastroWhatsapp.open();
    }

    public boolean validarMascara() throws DataException {
        Integer mascara = tbDisparoChatbot.getMASCARA().asInteger();
        Integer idItem = tbDisparoChatbot.getID_ITEM().asInteger();

        View v = tbDisparoChatbot.getView();
        v.first();
        while (!v.eof()) {
            if (!v.getField("ID_ITEM").asInteger().equals(idItem)) {
                if (mascara.equals(v.getField("MASCARA").asInteger())) {
                    return false;
                }
            }
            v.next();
        }
        return true;
    }

}
