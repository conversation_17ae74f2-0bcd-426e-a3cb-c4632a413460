package freedom.bytecode.rn;

import freedom.connection.ISession;
import freedom.data.DataException;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import freedom.bytecode.rn.wizard.LoginClienteRNW;
import freedom.connection.SessionFactory;
import freedom.util.ApplicationUtil;

public class LoginClienteRNA extends LoginClienteRNW {

    private static final long serialVersionUID = 20130827081850L;
    private final IWorkList wl = WorkListFactory.getInstance();


    /*  validacao 1: verifica se usuario pode logar no banco selecionado

     * Validação 2: Validacao de schema
     * somente executado sistema NBS estiver em multiplos schemas no mesmo banco
     * Explicação desta parte da validacao:
     *
     * 1. validar se tabela sys.nbs_schema existe e tem ao menos um registro
     *    se nao existir ou estiver vazia, nao precisa validar cruzamento de schema com o usuario
     * IMPORTANTE:
     * 1. a tabela sys.nbs_schema pode nao existir no clientes (em configuracao nao multischema)
     * 2. o usuario da conexao do pool, precisa ter direito de acesso a tabela sys.nbs_schema
     *     Se nao tiver, NAO VAI POSSIVEL fazer a validacao de cruzamento, e o PROGRAMA VAI PERMITIR o acesso do usuario
     *
     * Nota: se tabela nao existir ou se usuario do pool de conexao nao tiver acesso a esta tabela,
     * vai dar erro '0RA-00942 : Table or View dos not exist'
     *
     * Caso o erro 'ORA-00942' ocorra, considera a validacao valida ja que não é possivel saber se
     * a tabela sys.nbs_schema nao existe no banco OU se é o usuario de conexao que nao tem acesso a tabela
     * Neste caso, o controle de acesso deve fazer o restante da seguranca
     */
    public String validarLogin(String usuarioCliente, String senhaCliente, String usuario, String senha, String schema, boolean validarLoginSchema) throws Exception {
        ISession session = null;
        ISession sessionAut = null;
        String result = "";

        try {
            session = SessionFactory.getInstance().getSession(schema);
            session.open();

            sessionAut = SessionFactory.getInstance().getSession(schema + "_aut");

//            oracle.jdbc.pool.OracleDataSource ds = new oracle.jdbc.pool.OracleDataSource();
//            ds.setURL(wl.sysget("URL_ORACLE_TEST").asString());
//
//            if (ds.getURL().isEmpty()) {
//                result = "NBS-0425 -Parâmetro URL_ORACLE_TEST não preenchido no arquivo fserver.xml";
//            }
//            ds.setDatabaseName(schema);
//            con = ds.getConnection(usuario, senha);
            sessionAut.open(usuario, senha);

            //tbBtobCadastro.close();
            tbBtobCadastro.setSession(session);
            tbBtobCadastro.addFilter("EMAIL");
            tbBtobCadastro.addFilter("SENHA");
            tbBtobCadastro.addParam("EMAIL", usuarioCliente);
            tbBtobCadastro.addParam("SENHA", senhaCliente);
            tbBtobCadastro.open();
            
            if (tbBtobCadastro.isEmpty()){
                result = "NBS-0415 - Erro ao autenticar: usuário ou senha inválido.";
            } else {
                wl.put("COD_CLIENTE_LOGADO", tbBtobCadastro.getCOD_CLIENTE().asLong());
            }
            
        } catch (DataException e) {
            result = "NBS-0435 - Erro: " + e.getMessage();
        } finally {
            if (session != null) {
                session.close();
            }

            if (sessionAut != null) {
                sessionAut.close();
            }
        }

        return result;
    }
}
