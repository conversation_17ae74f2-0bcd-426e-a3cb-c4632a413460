package freedom.bytecode.rn;
import freedom.client.controls.impl.TFTable;
import freedom.data.SequenceUtil;
import freedom.data.DataException;

import java.util.Date;
import java.util.Map;

import freedom.data.impl.RowType;
import freedom.util.DateUtil;

import freedom.bytecode.rn.wizard.TimeTemplateHorariosRNW;

public class TimeTemplateHorariosRNA extends TimeTemplateHorariosRNW  {
    private static final long serialVersionUID = 20130827081850L;

    /**
     * Se simplemesmente der o cancel e for registro novo adicionado com o append ele deleta o registro
     * então crio uma variavel de beckup, para quando eu editar o horario e der o cancelar, não precisar utilizar
     * o table.cancel do cursor
     */
    private RowType horarioBackupCancel = null;
    private void setBackupHorario(RowType row){
        this.horarioBackupCancel = row;
    }

    private RowType getBackupHorario(){
        return this.horarioBackupCancel;
    }

    /**
     * <PERSON>ção recebe uma tabela e altera os registro atual da tabela por um registro de beckup.
     * Alternativa para o table.cancel que retorna para o registro inicial do banco de dados.
     * Exemplo:
     *      RowType linhaBackup = tbExemplo.toRowType;
     *      cancelTabelaComBeckup(tbExemplo, linhaBackup);
     * @param tabelaAtual Tabela que terá o registro atual substituido por registro de beckup
     * @param registroBackup Registro de beckup
     * @throws DataException
     */
    private void cancelTabelaComBeckup(TFTable tabelaAtual, RowType registroBackup) throws DataException {
        SubstituirRegistroAtual(tabelaAtual, registroBackup);
    }

    /**
     * Recebe um registro e altera o registro atual da tabela, por esse registro
     * Exemplo:;
     *      RowType linhaSubstitutiva = tbExemplo.toRowType;
     *      SubstituirRegistroAtual(tbExemplo, linhaSubstitutiva);
     * @param tabelaAtual Tabela que deseja substituir o registro
     * @param registroBackup Registro substitutivo
     * @throws DataException
     */
    private void SubstituirRegistroAtual(TFTable tabelaAtual, RowType registroBackup) throws DataException {
        if (tabelaAtual == null && tabelaAtual.isEmpty()){
            return;
        }
        if (registroBackup == null){
            return;
        }
        tabelaAtual.edit();
        for (Map.Entry<String, Object> column : registroBackup.getItems().get(0).getValues().entrySet()){
            try {
                tabelaAtual.setField(column.getKey(), column.getValue());
            } catch (DataException e) {
                throw new DataException("Erro ao voltar backup: " + e.getMessage());
            }
        }
        tabelaAtual.post();
    }


    public void filtrarTbTimeTemplate() throws DataException {
        tbTimeTemplate.close();
        tbTimeTemplate.clearFilters();
        tbTimeTemplate.clearParams();
        tbTimeTemplate.open();
    }

    /***
     * retorna o id_template do template selecionado
     * @return
     */
    public int getIDTemplate(){
        return tbTimeTemplate.getID_TEMPLATE().asInteger();
    }


    public void selecionarTbTimeTemplate(int idTemplate) throws DataException {
        tbTimeTemplate.locate("ID_TEMPLATE",idTemplate);
    }

    public void novoTbTimeTemplate() throws DataException {
        int idTemplate = (int) SequenceUtil.nextVal("SEQ_ID_TIME_TEMPLATE");
        tbTimeTemplate.append();
        tbTimeTemplate.setID_TEMPLATE(idTemplate);
        tbTimeTemplate.disableControls();
        tbTimeTemplate.post();
        tbTimeTemplateHorario.disableControls();
        tbTimeTemplate.enableControls();
        /* já adiciono os 7 dias da semana ao template */
        for (int i = 1; i <= 7; i++) {
            tbTimeTemplateHorario.append();
            tbTimeTemplateHorario.setID_TEMPLATE(idTemplate);
            switch (i){
                case 1:
                    tbTimeTemplateHorario.setNOME_DIA_SEMANA("Domingo");
                    break;
                case 2:
                    tbTimeTemplateHorario.setNOME_DIA_SEMANA("Segunda-Feira");
                    break;
                case 3:
                    tbTimeTemplateHorario.setNOME_DIA_SEMANA("Terça-Feira");
                    break;
                case 4:
                    tbTimeTemplateHorario.setNOME_DIA_SEMANA("Quarta-Feira");
                    break;
                case 5:
                    tbTimeTemplateHorario.setNOME_DIA_SEMANA("Quinta-Feira");
                    break;
                case 6:
                    tbTimeTemplateHorario.setNOME_DIA_SEMANA("Sexta-Feira");
                    break;
                case 7:
                    tbTimeTemplateHorario.setNOME_DIA_SEMANA("Sábado");
                    break;
            }
            Date dataInicio = DateUtil.getDataHoraDoTexto("01/01/2000 07:30:00");
            Date dataFim = DateUtil.getDataHoraDoTexto("01/01/2000 17:30:00");
            tbTimeTemplateHorario.setDIA_SEMANA(i);
            tbTimeTemplateHorario.setINICIO(dataInicio);
            tbTimeTemplateHorario.setFIM(dataFim);
            tbTimeTemplateHorario.post();
        }
        tbTimeTemplateHorario.sort("DIA_SEMANA","ASC");
        tbTimeTemplateHorario.enableControls();
    }

    public void editarTbTimeTemplate() throws DataException {
        tbTimeTemplate.edit();
    }

    public boolean tbTimeTemplateIsEmpty(){
        return tbTimeTemplate.isEmpty();
    }

    public void salvarDados() throws DataException {
        tbTimeTemplate.post();
        tbTimeTemplate.applyUpdates();
        tbTimeTemplate.commitUpdates();
        tbTimeTemplateHorario.post();
        tbTimeTemplateHorario.applyUpdates();
        tbTimeTemplateHorario.commitUpdates();
        tbTimeTemplate.refreshRecord();
        tbTimeTemplateHorario.refreshRecord();
    }

    public void filtrarTbTimeTemplateHorario() throws DataException {
        int idTemplate = tbTimeTemplate.getID_TEMPLATE().asInteger();
        tbTimeTemplateHorario.close();
        tbTimeTemplateHorario.clearFilters();
        tbTimeTemplateHorario.clearParams();
        tbTimeTemplateHorario.setFilterID_TEMPLATE(idTemplate);
        tbTimeTemplateHorario.open();
    }


    public void cancelarAlteracaoTimeTemplate() throws DataException {
        tbTimeTemplate.cancel();
        tbTimeTemplateHorario.cancel();
        tbTimeTemplate.cancelUpdates();
        tbTimeTemplateHorario.cancelUpdates();
    }

    public void editarHorarioTimeTemplate() throws DataException {
        /* guardo de backup do horario */
        setBackupHorario(tbTimeTemplateHorario.toRowType());
        tbTimeTemplateHorario.edit();
    }

    public boolean tbTimeTemplateHorarioIsEmpty(){
        return !tbTimeTemplateHorario.isActive() || tbTimeTemplateHorario.isEmpty();
    }

    public void salvarHorarioTimeTemplate() throws DataException {
        tbTimeTemplateHorario.post();
    }

    public void cancelarAlteracaoHorarioTimeTemplate() throws DataException {
        /* atribuo backup do horario */
        RowType backupHorario = this.getBackupHorario();
        cancelTabelaComBeckup(tbTimeTemplateHorario, backupHorario);
        setBackupHorario(null);
    }

    public void tbTimeTemplatelimparCamposHorarios() throws DataException {
        //tbTimeTemplateHorario.edit();
        tbTimeTemplateHorario.setINICIO(null);
        tbTimeTemplateHorario.setFIM(null);
        //tbTimeTemplateHorario.post();
    }

}
