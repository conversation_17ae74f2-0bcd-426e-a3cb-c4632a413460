package freedom.bytecode.rn;
import freedom.data.DataException;
import freedom.util.pkg.PkgCrmPartsRNA;


import freedom.bytecode.rn.wizard.NotaFiscalEletronicaSefazRNW;
public class NotaFiscalEletronicaSefazRNA extends NotaFiscalEletronicaSefazRNW  {
    private static final long serialVersionUID = 20130827081850L;
    private static final String NFE_CONTINGENCIA = "nfe_contingencia";

    private static final String NFE_FALHA = "nfe_falha";

    private static final String NFE_APROVADA = "nfe_aprovada";

    private final PkgCrmPartsRNA pkgCrmPartsRNA = new PkgCrmPartsRNA();

    private String nfeStatus;

    private String nfeDescricaoStatus;

    public void iniciarRn(double codEmpresa,
                          double controle,
                          String serie) throws DataException {
        this.openNFEMensagem(codEmpresa,
                controle,
                serie);
    }

    public void abrirNFEMovimento(double codEmpresa,
                                  double controle,
                                  String serie) throws DataException {
        this.tbCrmpartsNfeMovimento.close();
        this.tbCrmpartsNfeMovimento.clearFilters();
        this.tbCrmpartsNfeMovimento.clearParams();
        this.tbCrmpartsNfeMovimento.setFilterCOD_EMPRESA(codEmpresa);
        this.tbCrmpartsNfeMovimento.setFilterCONTROLE(controle);
        this.tbCrmpartsNfeMovimento.setFilterSERIE(serie);
        this.tbCrmpartsNfeMovimento.open();
    }

    public void abrirNFEMovimentoComOrcMapa(double codEmpresa,
                                            double codOrcMapa) throws DataException {
        this.tbCrmpartsNfeMovimento.close();
        this.tbCrmpartsNfeMovimento.clearFilters();
        this.tbCrmpartsNfeMovimento.clearParams();
        this.tbCrmpartsNfeMovimento.setFilterCOD_EMPRESA(codEmpresa);
        this.tbCrmpartsNfeMovimento.setFilterCOD_ORC_MAPA(codOrcMapa);
        this.tbCrmpartsNfeMovimento.open();
    }

    public void atualizarStatusNFE(double codEmpresa,
                                   double controle,
                                   String serie) {
        try {
            this.nfeStatus = this.pkgCrmPartsRNA.consultarStatusNfe(codEmpresa,
                    controle,
                    serie);
        } catch (DataException dataException) {
            this.nfeStatus = "indefinido";
        }
        switch (this.nfeStatus) {
            case "fila_envio":
                this.nfeDescricaoStatus = "Aguardando em fila de envio";
                break;
            case "fila_inutilizacao":
                this.nfeDescricaoStatus = "Aguardando inutilização";
                break;
            case "fila_protocolo":
                this.nfeDescricaoStatus = "Aguardando consulta protocolo";
                break;
            case "nfe_fila":
                this.nfeDescricaoStatus = "Processando fila";
                break;
            case NotaFiscalEletronicaSefazRNA.NFE_CONTINGENCIA:
                this.nfeDescricaoStatus = "NF-e aprovada em contingência";
                break;
            case NotaFiscalEletronicaSefazRNA.NFE_FALHA:
                this.nfeDescricaoStatus = "Falha na emissão";
                break;
            case "nfe_rejeicao":
                this.nfeDescricaoStatus = "NF-e rejeitada pela Sefaz";
                break;
            case NotaFiscalEletronicaSefazRNA.NFE_APROVADA:
                this.nfeDescricaoStatus = "NF-e aprovada pela Sefaz!";
                break;
            case "nfe_cancelada":
                this.nfeDescricaoStatus = "NF-e cancelada pela Sefaz!";
                break;
            case "nfe_inutilizada":
                this.nfeDescricaoStatus = "NF-e inutilizada pela Sefaz!";
                break;
            case "nfe_movimento":
                this.nfeDescricaoStatus = "Sem movimento";
                break;
            default:
                this.nfeDescricaoStatus = "Processando";
                break;
        }
    }

    public boolean aguardaAprovarNFE(double codEmpresa,
                                     double controle,
                                     String serie) throws DataException {
        this.atualizarStatusNFE(codEmpresa,
                controle,
                serie);
        boolean wait = !this.nfeStatus.equals(NotaFiscalEletronicaSefazRNA.NFE_CONTINGENCIA)
                && !this.nfeStatus.equals(NotaFiscalEletronicaSefazRNA.NFE_FALHA)
                && !this.nfeStatus.equals("nfe_rejeicao")
                && !this.nfeStatus.equals(NotaFiscalEletronicaSefazRNA.NFE_APROVADA)
                && !this.nfeStatus.equals("nfe_cancelada")
                && !this.nfeStatus.equals("nfe_inutilizada")
                && !this.nfeStatus.equals("erro_inutilizacao");
        if (!wait) {
            this.openNFEMensagem(codEmpresa,
                    controle,
                    serie);
        }
        return wait;
    }

    public void openNFEMensagem(double codEmpresa,
                                double controle,
                                String serie) throws DataException {
        this.tbMovimento.close();
        this.tbMovimento.setFilterID_EMPRESA(codEmpresa);
        this.tbMovimento.setFilterNUMR_CONTROLE(controle);
        this.tbMovimento.setFilterSERIE_NBS(serie);
        this.tbMovimento.setFilterTIPO_NF(2);
        this.tbMovimento.open();
        this.tbLeadsNfeMensagem.close();
        this.tbLeadsNfeMensagem.addParam("ID_MOVIMENTO", this.tbMovimento.getID_MOVIMENTO().asLong());
        this.tbLeadsNfeMensagem.addParam("NUMR_CONTROLE", this.tbMovimento.getNUMR_CONTROLE().asLong());
        this.tbLeadsNfeMensagem.addParam("ID_EMPRESA", this.tbMovimento.getID_EMPRESA().asLong());
        this.tbLeadsNfeMensagem.addParam("SERIE_NFE", this.tbMovimento.getSERIE_NFE().asLong());
        this.tbLeadsNfeMensagem.open();
    }

    public boolean isNFEAprovada() {
        return this.nfeStatus.equals(NotaFiscalEletronicaSefazRNA.NFE_APROVADA)
                || this.nfeStatus.equals(NotaFiscalEletronicaSefazRNA.NFE_CONTINGENCIA);
    }

    public boolean isNFEFalha() {
        return this.nfeStatus.equals(NotaFiscalEletronicaSefazRNA.NFE_FALHA);
    }

    public boolean isNFEAprovada(double codEmpresa,
                                 double controle,
                                 String serie) {
        this.atualizarStatusNFE(codEmpresa,
                controle,
                serie);
        return this.isNFEAprovada();
    }

    public void reenviarNFE(double codEmpresa,
                            double controle,
                            String serie) {
        boolean nfeNaoAprovada = !this.isNFEAprovada(codEmpresa,
                controle,
                serie);
        if (nfeNaoAprovada) {
            try {
                this.pkgCrmPartsRNA.reenviarNfe(codEmpresa,
                        controle,
                        serie);
            } catch (DataException dataException) {
                dataException.printStackTrace();
            }
        }
    }

    public String getNFEDescricaoStatus() {
        return this.nfeDescricaoStatus;
    }

    public boolean aguardaGerarPDF(double codEmpresa,
                                   double controle,
                                   String serie) {
        try {
            this.abrirNFEMovimento(codEmpresa,
                    controle,
                    serie);
            return !this.tbCrmpartsNfeMovimento.getTEM_PDF().asString().equals("S");
        } catch (DataException dataException) {
            return false;
        }
    }

    public String gerarFilaPDF(double codEmpresa,
                               double controle,
                               String serie) {
        try {
            return this.pkgCrmPartsRNA.gerarImpressaoNfe(codEmpresa,
                    controle,
                    serie,
                    "NF-e");
        } catch (DataException dataException) {
            dataException.printStackTrace();
            return "N";
        }
    }
}

