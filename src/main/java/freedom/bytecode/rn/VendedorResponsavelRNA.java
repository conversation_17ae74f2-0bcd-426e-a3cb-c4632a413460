package freedom.bytecode.rn;

import freedom.bytecode.cursor.CLIENTE_RESPONSAVEL;
import freedom.bytecode.rn.wizard.VendedorResponsavelRNW;
import freedom.data.DataException;
import freedom.util.StringUtil;

public class VendedorResponsavelRNA extends VendedorResponsavelRNW  {

    private static final long serialVersionUID = 20130827081850L;

    public void carregarCboEmpresa(String loginUsuario,
                                   long codEmpresa) throws DataException {
        this.tbEmpresasListagem.close();
        this.tbEmpresasListagem.clearFilters();
        this.tbEmpresasListagem.clearParams();
        this.tbEmpresasListagem.addParam("USUARIO", loginUsuario);
        this.tbEmpresasListagem.addParam("COD_EMPRESA_USUARIO", codEmpresa);
        this.tbEmpresasListagem.open();
    }

    public void carregarCboAltEmpresa(String loginUsuario,
                                      long codEmpresa) throws DataException {
        this.tbEmpresasCadastro.close();
        this.tbEmpresasCadastro.clearFilters();
        this.tbEmpresasCadastro.clearParams();
        this.tbEmpresasCadastro.addParam("USUARIO", loginUsuario);
        this.tbEmpresasCadastro.addParam("COD_EMPRESA_USUARIO", codEmpresa);
        this.tbEmpresasCadastro.open();
    }

    public void inserirClienteResponsavel(long codEmpresa,
                                          long codCliente,
                                          int sistema,
                                          int processo,
                                          int temperatura,
                                          String responsavel) throws DataException {
        CLIENTE_RESPONSAVEL tbInserirClienteResponsavel = new CLIENTE_RESPONSAVEL("tbInserirClienteResponsavel");
        tbInserirClienteResponsavel.close();
        tbInserirClienteResponsavel.clearFilters();
        tbInserirClienteResponsavel.clearParams();
        tbInserirClienteResponsavel.setFilterCOD_EMPRESA_EQUALS(codEmpresa);
        tbInserirClienteResponsavel.setFilterCOD_CLIENTE_EQUALS(codCliente);
        tbInserirClienteResponsavel.setFilterSISTEMA_EQUALS(sistema);
        tbInserirClienteResponsavel.setFilterPROCESSO_EQUALS(processo);
        tbInserirClienteResponsavel.setFilterTEMPERATURA_EQUALS(temperatura);
        tbInserirClienteResponsavel.open();
        boolean tbInserirClienteResponsavelEmpty = tbInserirClienteResponsavel.isEmpty();
        if (tbInserirClienteResponsavelEmpty) {
            tbInserirClienteResponsavel.append();
            tbInserirClienteResponsavel.setCOD_EMPRESA(codEmpresa);
            tbInserirClienteResponsavel.setCOD_CLIENTE(codCliente);
            tbInserirClienteResponsavel.setSISTEMA(sistema);
            tbInserirClienteResponsavel.setPROCESSO(processo);
            tbInserirClienteResponsavel.setTEMPERATURA(temperatura);
            tbInserirClienteResponsavel.setRESPONSAVEL(responsavel);
            tbInserirClienteResponsavel.post();
            tbInserirClienteResponsavel.applyUpdates();
            tbInserirClienteResponsavel.commitUpdates();
        }
        tbInserirClienteResponsavel.close();
    }

    public void alterarClienteResponsavel(long codEmpresa,
                                          long codCliente,
                                          int sistema,
                                          int processo,
                                          int temperatura,
                                          String responsavel) throws DataException {
        boolean temRegistro = this.temVendedores(codEmpresa,
                codCliente,
                sistema,
                processo);
        if (temRegistro) {
            this.tbClienteResponsavel.edit();
            this.tbClienteResponsavel.setCOD_EMPRESA(codEmpresa);
            this.tbClienteResponsavel.setCOD_CLIENTE(codCliente);
            this.tbClienteResponsavel.setSISTEMA(sistema);
            this.tbClienteResponsavel.setPROCESSO(processo);
            this.tbClienteResponsavel.setTEMPERATURA(temperatura);
            this.tbClienteResponsavel.setRESPONSAVEL(responsavel);
            this.tbClienteResponsavel.post();
            this.tbClienteResponsavel.applyUpdates();
            this.tbClienteResponsavel.commitUpdates();
        }
    }

    public boolean temVendedores(long codEmpresa,
                                 long codCliente,
                                 int sistema,
                                 int processo) throws DataException{
        this.tbClienteResponsavel.close();
        this.tbClienteResponsavel.clearFilters();
        this.tbClienteResponsavel.clearParams();
        this.tbClienteResponsavel.setFilterCOD_EMPRESA_EQUALS(codEmpresa);
        this.tbClienteResponsavel.setFilterCOD_CLIENTE_EQUALS(codCliente);
        this.tbClienteResponsavel.setFilterSISTEMA_EQUALS(sistema);
        this.tbClienteResponsavel.setFilterPROCESSO_EQUALS(processo);
        this.tbClienteResponsavel.open();
        return !tbClienteResponsavel.isEmpty();
    }

    public void carregarCboAltVendedor(long codEmpresa,
                                       int processo,
                                       String codigosDasEmpresasDoUsuario) throws DataException{
        this.tbVendedoresCadastro.close();
        this.tbVendedoresCadastro.setMaxRowCount(0);
        this.tbVendedoresCadastro.clearFilters();
        this.tbVendedoresCadastro.clearParams();
        if (codEmpresa > 0L) {
            this.tbVendedoresCadastro.setFilterCOD_EMPRESA_CONTAINS(codEmpresa);
        }
        if (processo > 0) {
            switch (processo) {
                case 1:
                    this.tbVendedoresCadastro.setFilterRESPONSAVEL_CAMINHAO_EQUALS("S");
                    break;
                case 2:
                    this.tbVendedoresCadastro.setFilterRESPONSAVEL_VANS_EQUALS("S");
                    break;
                case 3:
                    this.tbVendedoresCadastro.setFilterRESPONSAVEL_ONIBUS_EQUALS("S");
                    break;
                case 4:
                    this.tbVendedoresCadastro.setFilterRESPONSAVEL_VEND_INT_EQUALS("S");
                    break;
                case 5:
                    this.tbVendedoresCadastro.setFilterRESPONSAVEL_VEND_EXT_EQUALS("S");
                    break;
                case 6:
                    this.tbVendedoresCadastro.setFilterRESPONSAVEL_VEND_PNEU_EQUALS("S");
                    break;
                default:
                    break;
            }
        }
        if (!codigosDasEmpresasDoUsuario.isEmpty()) {
            this.tbVendedoresCadastro.setFilterCOD_EMPRESA_CONTAINS(codigosDasEmpresasDoUsuario);
        }
        this.tbVendedoresCadastro.open();
    }

    public void carregarCboVendedor(long codEmpresa,
                                    int processo,
                                    String codigosDasEmpresasDoUsuario) throws DataException{
        this.tbVendedoresListagem.close();
        this.tbVendedoresListagem.setMaxRowCount(0);
        this.tbVendedoresListagem.clearFilters();
        this.tbVendedoresListagem.clearParams();
        if (codEmpresa > 0L) {
            this.tbVendedoresListagem.setFilterCOD_EMPRESA_EQUALS(codEmpresa);
        }
        if (processo > 0) {
            switch (processo) {
                case 1:
                    this.tbVendedoresListagem.setFilterRESPONSAVEL_CAMINHAO_EQUALS("S");
                    break;
                case 2:
                    this.tbVendedoresListagem.setFilterRESPONSAVEL_VANS_EQUALS("S");
                    break;
                case 3:
                    this.tbVendedoresListagem.setFilterRESPONSAVEL_ONIBUS_EQUALS("S");
                    break;
                case 4:
                    this.tbVendedoresListagem.setFilterRESPONSAVEL_VEND_INT_EQUALS("S");
                    break;
                case 5:
                    this.tbVendedoresListagem.setFilterRESPONSAVEL_VEND_EXT_EQUALS("S");
                    break;
                case 6:
                    this.tbVendedoresListagem.setFilterRESPONSAVEL_VEND_PNEU_EQUALS("S");
                    break;
                default:
                    break;
            }
        }
        if (!codigosDasEmpresasDoUsuario.isEmpty()) {
            this.tbVendedoresListagem.setFilterCOD_EMPRESA_CONTAINS(codigosDasEmpresasDoUsuario);
        }
        this.tbVendedoresListagem.open();
    }

    public void carregarGridVendedores(long codCliente,
                                       String codigosDasEmpresasDoUsuario,
                                       long codEmpresaFiltrada,
                                       int codSistemaFiltrado,
                                       int codProcessoFiltrado,
                                       int codTemperaturaFiltrada,
                                       String loginVendedorFiltrado) throws DataException{
        this.tbListaClienteResponsavel.close();
        this.tbListaClienteResponsavel.clearFilters();
        this.tbListaClienteResponsavel.clearParams();
        this.tbListaClienteResponsavel.setFilterCOD_CLIENTE_EQUALS(codCliente);
        this.tbListaClienteResponsavel.setFilterCOD_EMPRESA_CONTAINS(codigosDasEmpresasDoUsuario);
        if (codEmpresaFiltrada > 0L) {
            this.tbListaClienteResponsavel.setFilterCOD_EMPRESA_EQUALS(codEmpresaFiltrada);
        }
        if (codSistemaFiltrado > 0) {
            this.tbListaClienteResponsavel.setFilterSISTEMA_EQUALS(codSistemaFiltrado);
        }
        if (codProcessoFiltrado > 0) {
            this.tbListaClienteResponsavel.setFilterPROCESSO_EQUALS(codProcessoFiltrado);
        }
        if (codTemperaturaFiltrada > 0) {
            this.tbListaClienteResponsavel.setFilterTEMPERATURA_EQUALS(codTemperaturaFiltrada);
        }
        if (!loginVendedorFiltrado.isEmpty()) {
            this.tbListaClienteResponsavel.setFilterVENDEDOR_EQUALS(loginVendedorFiltrado);
        }
        this.tbListaClienteResponsavel.open();
    }

    public void excluirClienteResponsavel(long codEmpresa,
                                          long codCliente,
                                          int sistema,
                                          int processo) throws DataException {
        CLIENTE_RESPONSAVEL tbExcluirClienteResponsavel = new CLIENTE_RESPONSAVEL("tbExcluirClienteResponsavel");
        tbExcluirClienteResponsavel.close();
        tbExcluirClienteResponsavel.clearFilters();
        tbExcluirClienteResponsavel.clearParams();
        tbExcluirClienteResponsavel.setFilterCOD_EMPRESA_EQUALS(codEmpresa);
        tbExcluirClienteResponsavel.setFilterCOD_CLIENTE_EQUALS(codCliente);
        tbExcluirClienteResponsavel.setFilterSISTEMA_EQUALS(sistema);
        tbExcluirClienteResponsavel.setFilterPROCESSO_EQUALS(processo);
        tbExcluirClienteResponsavel.open();
        boolean tbExcluirClienteResponsavelNotEmpty = !tbExcluirClienteResponsavel.isEmpty();
        if (tbExcluirClienteResponsavelNotEmpty) {
            tbExcluirClienteResponsavel.delete();
            tbExcluirClienteResponsavel.applyUpdates();
            tbExcluirClienteResponsavel.commitUpdates();
        }
        tbExcluirClienteResponsavel.close();
    }

    public String getCodigosDasEmpresasDoUsuario() throws DataException {
        return StringUtil.concatenarRegistros(this.tbEmpresasListagem,
                "COD_EMPRESA",
                ", ");
    }

}