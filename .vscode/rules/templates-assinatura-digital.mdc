---
description: 
globs: 
alwaysApply: false
---
# Templates para Criação de Assinaturas Digitais

## ?? **IMPORTANTE: Configuração do Banco de Dados**

**Antes de usar qualquer template, as seguintes tabelas DEVEM ser alimentadas:**

### **Tabelas Principais:**
- **CRM_ASSINATURA_DIGITAL** - Configuração principal da assinatura
- **CRM_ASSINATURA_DIGITAL_PARM** - Parâmetros da assinatura
- **CRM_ASSINATURA_DIGITAL_SIGN** - Signatários habilitados
- **CRM_ASSINATURA_DIGITAL_DOC** - Documentos fixos (opcional)
- **CRM_ASSINATURA_DIGITAL_SISTEMA** - Sistema associado (opcional)

### **Tabelas de Documentos:**
- **NBSAPI_ENVELOPES_FILA** - Fila de envelopes para assinatura
- **NBSAPI_ENVELOPES_LOG** - Log de operações dos envelopes

---

## Template 1: Estratégia Simple (Sem Documentos)

```java
package freedom.util.assinaturaDigital.strategy;

import freedom.util.EmpresaUtil;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class NomeAssinaturaStrategy implements TipoAssinaturaStrategy {
    
    @Override
    public String getTipo() {
        return "NOME_ASSINATURA";
    }

    @Override
    public String getTipoCodigo() {
        return "PARAM1;PARAM2";
    }

    @Override
    public String getCodigoAcesso() {
        return EmpresaUtil.isCrmService() ? "CODIGO_ACESSO" : "";
    }

    @Override
    public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) {
        // Retorna lista vazia para assinaturas que não geram documentos
        return new ArrayList<>();
    }

    @Override
    public JSONObject getJsonParametrosFixos(JSONObject jsonRetorno) {
        // Adiciona parâmetros fixos para o relatório
        jsonRetorno.put("ASSINAR_DIGITALMENTE", "S");
        return jsonRetorno;
    }
}
```

### **Configuração no Banco (Template 1):**

```sql
-- 1. Inserir na CRM_ASSINATURA_DIGITAL
INSERT INTO CRM_ASSINATURA_DIGITAL (
    ID_ASSINATURA_DIGITAL, TIPO_ASSINATURA, DESCRICAO, TIPO_CODIGO, ID_SISTEMA
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.NEXTVAL, 'NOME_ASSINATURA', 'Descrição da Assinatura', 'PARAM1;PARAM2', 1
);

-- 2. Inserir parâmetros na CRM_ASSINATURA_DIGITAL_PARM
INSERT INTO CRM_ASSINATURA_DIGITAL_PARM (
    ID_ASSINATURA_DIGITAL, NOME_PARAMETRO, EH_TIPO_CODIGO, ORDEM_TIPO_CODIGO, TIPO_VALOR
) VALUES 
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM1', 'S', 1, 'I'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM2', 'S', 2, 'I');

-- 3. Inserir signatários na CRM_ASSINATURA_DIGITAL_SIGN
INSERT INTO CRM_ASSINATURA_DIGITAL_SIGN (
    ID_ASSINATURA_DIGITAL, TAG_DOCUMENTO, LOOKUP_TABLE, LOOKUP_FIELD, SIGNATARIO_TIPO, DESCRICAO
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#CLIENTE', 'CLIENTES', 'CPF_CNPJ', 'CLIENTE', 'Cliente'
);
```

---

## Template 2: Estratégia com Geração de Relatório

```java
package freedom.util.assinaturaDigital.strategy;

import freedom.util.EmpresaUtil;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import freedom.util.JasperReportUtil;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class NomeAssinaturaComRelatorioStrategy implements TipoAssinaturaStrategy {
    
    @Override
    public String getTipo() {
        return "NOME_ASSINATURA_RELATORIO";
    }

    @Override
    public String getTipoCodigo() {
        return "PARAM1;PARAM2";
    }

    @Override
    public String getCodigoAcesso() {
        return EmpresaUtil.isCrmService() ? "CODIGO_ACESSO" : "";
    }

    @Override
    public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException {
        try {
            // Obter parâmetros da string de códigos
            JSONObject jsonParametros = getJsonParametrosFromCodigoValores(stringCodigoValores);
            
            // Extrair parâmetros específicos
            double param1 = jsonParametros.getInt("PARAM1");
            double param2 = jsonParametros.getInt("PARAM2");
            
            // Gerar documento PDF
            byte[] documentoPdf = gerarRelatorio(param1, param2, jsonParametros);
            
            // Criar lista de documentos
            List<CrmAssinaturaDigitalDocumento> listaDocumentos = new ArrayList<>();
            CrmAssinaturaDigitalDocumento documento = new CrmAssinaturaDigitalDocumento(
                "nome_arquivo.pdf", 
                documentoPdf, 
                "#IDENTIFICADOR_DOCUMENTO"
            );
            listaDocumentos.add(documento);
            
            return listaDocumentos;
            
        } catch (Exception e) {
            throw new DataException("Erro ao gerar documentos para assinatura: " + e.getMessage(), e);
        }
    }

    private byte[] gerarRelatorio(double param1, double param2, JSONObject parametros) throws DataException {
        try {
            // Configurar parâmetros do relatório
            JSONObject parametrosRelatorio = new JSONObject();
            parametrosRelatorio.put("PARAM1", param1);
            parametrosRelatorio.put("PARAM2", param2);
            
            // Caminho do arquivo .jasper
            String caminhoRelatorio = "/caminho/para/relatorio.jasper";
            
            // Gerar relatório usando JasperReportUtil
            return JasperReportUtil.exportarRelatorio(
                caminhoRelatorio,
                parametrosRelatorio,
                "PDF"
            );
            
        } catch (Exception e) {
            throw new DataException("Erro ao gerar relatório: " + e.getMessage(), e);
        }
    }
}
```

### **Configuração no Banco (Template 2):**

```sql
-- 1. Inserir na CRM_ASSINATURA_DIGITAL
INSERT INTO CRM_ASSINATURA_DIGITAL (
    ID_ASSINATURA_DIGITAL, TIPO_ASSINATURA, DESCRICAO, TIPO_CODIGO, ID_SISTEMA
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.NEXTVAL, 'NOME_ASSINATURA_RELATORIO', 'Assinatura com Relatório', 'PARAM1;PARAM2', 1
);

-- 2. Inserir parâmetros na CRM_ASSINATURA_DIGITAL_PARM
INSERT INTO CRM_ASSINATURA_DIGITAL_PARM (
    ID_ASSINATURA_DIGITAL, NOME_PARAMETRO, EH_TIPO_CODIGO, ORDEM_TIPO_CODIGO, TIPO_VALOR
) VALUES 
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM1', 'S', 1, 'I'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM2', 'S', 2, 'I');

-- 3. Inserir signatários na CRM_ASSINATURA_DIGITAL_SIGN
INSERT INTO CRM_ASSINATURA_DIGITAL_SIGN (
    ID_ASSINATURA_DIGITAL, TAG_DOCUMENTO, LOOKUP_TABLE, LOOKUP_FIELD, SIGNATARIO_TIPO, DESCRICAO
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#IDENTIFICADOR_DOCUMENTO', 'TABELA_SIGNATARIOS', 'CAMPO_ID', 'TIPO_SIGNATARIO', 'Descrição'
);
```

---

## Template 3: Estratégia com Classe Base

```java
package freedom.util.assinaturaDigital.strategy;

import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import freedom.util.assinaturaDigital.NaoEncontrouDocumentoException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class NomeAssinaturaComumStrategy extends CrmAssinaturaDigitalComum {
    
    public NomeAssinaturaComumStrategy() {
        super("NOME_ASSINATURA_COMUM");
    }

    @Override
    public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException, NaoEncontrouDocumentoException {
        try {
            // Obter parâmetros usando método da classe base
            JSONObject jsonParametros = getJsonParametrosFromCodigoValores(stringCodigoValores);
            
            // Lógica específica para gerar documentos
            List<CrmAssinaturaDigitalDocumento> documentos = gerarDocumentosEspecificos(jsonParametros);
            
            return documentos;
            
        } catch (Exception e) {
            throw new DataException("Erro ao gerar documentos: " + e.getMessage(), e);
        }
    }

    private List<CrmAssinaturaDigitalDocumento> gerarDocumentosEspecificos(JSONObject parametros) throws DataException {
        List<CrmAssinaturaDigitalDocumento> documentos = new ArrayList<>();
        
        // Implementar lógica específica aqui
        // Exemplo: gerar múltiplos documentos baseados em parâmetros
        
        return documentos;
    }
}
```

### **Configuração no Banco (Template 3):**

```sql
-- 1. Inserir na CRM_ASSINATURA_DIGITAL
INSERT INTO CRM_ASSINATURA_DIGITAL (
    ID_ASSINATURA_DIGITAL, TIPO_ASSINATURA, DESCRICAO, TIPO_CODIGO, ID_SISTEMA
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.NEXTVAL, 'NOME_ASSINATURA_COMUM', 'Assinatura Comum', 'PARAM1;PARAM2', 1
);

-- 2. Inserir parâmetros na CRM_ASSINATURA_DIGITAL_PARM
INSERT INTO CRM_ASSINATURA_DIGITAL_PARM (
    ID_ASSINATURA_DIGITAL, NOME_PARAMETRO, EH_TIPO_CODIGO, ORDEM_TIPO_CODIGO, TIPO_VALOR
) VALUES 
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM1', 'S', 1, 'I'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM2', 'S', 2, 'I');

-- 3. Inserir signatários na CRM_ASSINATURA_DIGITAL_SIGN
INSERT INTO CRM_ASSINATURA_DIGITAL_SIGN (
    ID_ASSINATURA_DIGITAL, TAG_DOCUMENTO, LOOKUP_TABLE, LOOKUP_FIELD, SIGNATARIO_TIPO, DESCRICAO
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#DOCUMENTO', 'TABELA_SIGNATARIOS', 'CAMPO_ID', 'TIPO_SIGNATARIO', 'Descrição'
);
```

---

## Template 4: Estratégia com Validações Avançadas

```java
package freedom.util.assinaturaDigital.strategy;

import freedom.util.EmpresaUtil;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class NomeAssinaturaValidadaStrategy implements TipoAssinaturaStrategy {
    
    @Override
    public String getTipo() {
        return "NOME_ASSINATURA_VALIDADA";
    }

    @Override
    public String getTipoCodigo() {
        return "PARAM1;PARAM2;PARAM3";
    }

    @Override
    public String getCodigoAcesso() {
        return EmpresaUtil.isCrmService() ? "CODIGO_ACESSO" : "";
    }

    @Override
    public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException, NaoEncontrouDocumentoException {
        // Validar parâmetros antes de processar
        validarParametros(stringCodigoValores);
        
        // Obter parâmetros
        JSONObject jsonParametros = getJsonParametrosFromCodigoValores(stringCodigoValores);
        
        // Validar regras de negócio
        validarRegrasNegocio(jsonParametros);
        
        // Gerar documentos
        return gerarDocumentos(jsonParametros);
    }

    private void validarParametros(String stringCodigoValores) throws DataException {
        if (stringCodigoValores == null || stringCodigoValores.trim().isEmpty()) {
            throw new DataException("String de códigos não pode ser nula ou vazia");
        }
        
        String[] parametros = stringCodigoValores.split(";");
        if (parametros.length != 3) {
            throw new DataException("Número incorreto de parâmetros. Esperado: 3, Recebido: " + parametros.length);
        }
        
        // Validar cada parâmetro individualmente
        for (int i = 0; i < parametros.length; i++) {
            if (parametros[i] == null || parametros[i].trim().isEmpty()) {
                throw new DataException("Parâmetro " + (i + 1) + " não pode ser nulo ou vazio");
            }
        }
    }

    private void validarRegrasNegocio(JSONObject parametros) throws DataException {
        // Implementar validações específicas do negócio
        // Exemplo: verificar se o documento existe, se tem permissão, etc.
        
        double param1 = parametros.getInt("PARAM1");
        if (param1 <= 0) {
            throw new DataException("PARAM1 deve ser maior que zero");
        }
        
        // Adicionar outras validações conforme necessário
    }

    private List<CrmAssinaturaDigitalDocumento> gerarDocumentos(JSONObject parametros) throws DataException {
        List<CrmAssinaturaDigitalDocumento> documentos = new ArrayList<>();
        
        try {
            // Lógica para gerar documentos
            // Pode incluir múltiplos documentos ou documentos condicionais
            
            CrmAssinaturaDigitalDocumento documento = new CrmAssinaturaDigitalDocumento(
                "documento_principal.pdf",
                gerarPDFDocumento(parametros),
                "#DOC_PRINCIPAL"
            );
            documentos.add(documento);
            
            // Adicionar documentos adicionais se necessário
            if (parametros.has("GERAR_ANEXO") && parametros.getBoolean("GERAR_ANEXO")) {
                CrmAssinaturaDigitalDocumento anexo = new CrmAssinaturaDigitalDocumento(
                    "anexo.pdf",
                    gerarPDFAnexo(parametros),
                    "#ANEXO"
                );
                documentos.add(anexo);
            }
            
        } catch (Exception e) {
            throw new DataException("Erro ao gerar documentos: " + e.getMessage(), e);
        }
        
        return documentos;
    }

    private byte[] gerarPDFDocumento(JSONObject parametros) throws DataException {
        // Implementar geração do PDF principal
        throw new UnsupportedOperationException("Método não implementado");
    }

    private byte[] gerarPDFAnexo(JSONObject parametros) throws DataException {
        // Implementar geração do PDF anexo
        throw new UnsupportedOperationException("Método não implementado");
    }
}
```

### **Configuração no Banco (Template 4):**

```sql
-- 1. Inserir na CRM_ASSINATURA_DIGITAL
INSERT INTO CRM_ASSINATURA_DIGITAL (
    ID_ASSINATURA_DIGITAL, TIPO_ASSINATURA, DESCRICAO, TIPO_CODIGO, ID_SISTEMA
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.NEXTVAL, 'NOME_ASSINATURA_VALIDADA', 'Assinatura com Validações', 'PARAM1;PARAM2;PARAM3', 1
);

-- 2. Inserir parâmetros na CRM_ASSINATURA_DIGITAL_PARM
INSERT INTO CRM_ASSINATURA_DIGITAL_PARM (
    ID_ASSINATURA_DIGITAL, NOME_PARAMETRO, EH_TIPO_CODIGO, ORDEM_TIPO_CODIGO, TIPO_VALOR
) VALUES 
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM1', 'S', 1, 'I'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM2', 'S', 2, 'I'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM3', 'S', 3, 'I');

-- 3. Inserir signatários na CRM_ASSINATURA_DIGITAL_SIGN
INSERT INTO CRM_ASSINATURA_DIGITAL_SIGN (
    ID_ASSINATURA_DIGITAL, TAG_DOCUMENTO, LOOKUP_TABLE, LOOKUP_FIELD, SIGNATARIO_TIPO, DESCRICAO
) VALUES 
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#DOC_PRINCIPAL', 'TABELA_SIGNATARIOS', 'CAMPO_ID', 'TIPO_SIGNATARIO', 'Signatário Principal'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#ANEXO', 'TABELA_SIGNATARIOS', 'CAMPO_ID', 'TIPO_SIGNATARIO', 'Signatário Anexo');
```

---

## Template 5: Estratégia com Múltiplos Tipos de Documento

```java
package freedom.util.assinaturaDigital.strategy;

import freedom.util.EmpresaUtil;
import freedom.util.assinaturaDigital.CrmAssinaturaDigitalDocumento;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class NomeAssinaturaMultiDocumentoStrategy implements TipoAssinaturaStrategy {
    
    @Override
    public String getTipo() {
        return "NOME_ASSINATURA_MULTI";
    }

    @Override
    public String getTipoCodigo() {
        return "PARAM1;PARAM2";
    }

    @Override
    public String getCodigoAcesso() {
        return EmpresaUtil.isCrmService() ? "CODIGO_ACESSO" : "";
    }

    @Override
    public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException, NaoEncontrouDocumentoException {
        JSONObject jsonParametros = getJsonParametrosFromCodigoValores(stringCodigoValores);
        List<CrmAssinaturaDigitalDocumento> documentos = new ArrayList<>();
        
        // Documento principal
        documentos.add(gerarDocumentoPrincipal(jsonParametros));
        
        // Documentos condicionais baseados em parâmetros
        if (deveGerarAnexo(jsonParametros)) {
            documentos.add(gerarAnexo(jsonParametros));
        }
        
        if (deveGerarTermo(jsonParametros)) {
            documentos.add(gerarTermo(jsonParametros));
        }
        
        return documentos;
    }

    private CrmAssinaturaDigitalDocumento gerarDocumentoPrincipal(JSONObject parametros) throws DataException {
        byte[] pdf = gerarPDFPrincipal(parametros);
        return new CrmAssinaturaDigitalDocumento("principal.pdf", pdf, "#PRINCIPAL");
    }

    private CrmAssinaturaDigitalDocumento gerarAnexo(JSONObject parametros) throws DataException {
        byte[] pdf = gerarPDFAnexo(parametros);
        return new CrmAssinaturaDigitalDocumento("anexo.pdf", pdf, "#ANEXO");
    }

    private CrmAssinaturaDigitalDocumento gerarTermo(JSONObject parametros) throws DataException {
        byte[] pdf = gerarPDFTermo(parametros);
        return new CrmAssinaturaDigitalDocumento("termo.pdf", pdf, "#TERMO");
    }

    private boolean deveGerarAnexo(JSONObject parametros) {
        // Lógica para determinar se deve gerar anexo
        return parametros.has("GERAR_ANEXO") && parametros.getBoolean("GERAR_ANEXO");
    }

    private boolean deveGerarTermo(JSONObject parametros) {
        // Lógica para determinar se deve gerar termo
        return parametros.has("GERAR_TERMO") && parametros.getBoolean("GERAR_TERMO");
    }

    // Métodos de geração de PDF (implementar conforme necessário)
    private byte[] gerarPDFPrincipal(JSONObject parametros) throws DataException {
        throw new UnsupportedOperationException("Implementar geração do PDF principal");
    }

    private byte[] gerarPDFAnexo(JSONObject parametros) throws DataException {
        throw new UnsupportedOperationException("Implementar geração do PDF anexo");
    }

    private byte[] gerarPDFTermo(JSONObject parametros) throws DataException {
        throw new UnsupportedOperationException("Implementar geração do PDF termo");
    }
}
```

### **Configuração no Banco (Template 5):**

```sql
-- 1. Inserir na CRM_ASSINATURA_DIGITAL
INSERT INTO CRM_ASSINATURA_DIGITAL (
    ID_ASSINATURA_DIGITAL, TIPO_ASSINATURA, DESCRICAO, TIPO_CODIGO, ID_SISTEMA
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.NEXTVAL, 'NOME_ASSINATURA_MULTI', 'Assinatura Múltiplos Documentos', 'PARAM1;PARAM2', 1
);

-- 2. Inserir parâmetros na CRM_ASSINATURA_DIGITAL_PARM
INSERT INTO CRM_ASSINATURA_DIGITAL_PARM (
    ID_ASSINATURA_DIGITAL, NOME_PARAMETRO, EH_TIPO_CODIGO, ORDEM_TIPO_CODIGO, TIPO_VALOR
) VALUES 
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM1', 'S', 1, 'I'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, 'PARAM2', 'S', 2, 'I');

-- 3. Inserir signatários na CRM_ASSINATURA_DIGITAL_SIGN
INSERT INTO CRM_ASSINATURA_DIGITAL_SIGN (
    ID_ASSINATURA_DIGITAL, TAG_DOCUMENTO, LOOKUP_TABLE, LOOKUP_FIELD, SIGNATARIO_TIPO, DESCRICAO
) VALUES 
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#PRINCIPAL', 'TABELA_SIGNATARIOS', 'CAMPO_ID', 'TIPO_SIGNATARIO', 'Signatário Principal'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#ANEXO', 'TABELA_SIGNATARIOS', 'CAMPO_ID', 'TIPO_SIGNATARIO', 'Signatário Anexo'),
(SEQ_CRM_ASSINATURA_DIGITAL.CURRVAL, '#TERMO', 'TABELA_SIGNATARIOS', 'CAMPO_ID', 'TIPO_SIGNATARIO', 'Signatário Termo');
```

---

## ?? Como Usar os Templates

### **Passo a Passo:**

1. **Escolha o template** mais adequado ao seu caso de uso
2. **Configure o banco de dados** usando os scripts SQL fornecidos
3. **Copie o código** para um novo arquivo Java
4. **Substitua os placeholders**:
   - `NomeAssinaturaStrategy` ? Nome da sua classe
   - `NOME_ASSINATURA` ? Identificador único do tipo
   - `PARAM1;PARAM2` ? Parâmetros necessários
   - `CODIGO_ACESSO` ? Código de acesso (se houver)
5. **Implemente os métodos** específicos para sua lógica
6. **Registre no factory** `EnTipoAssinaturaFactory`
7. **Teste** com dados reais

### **Exemplo de Uso Completo:**

```java
// 1. Criar instância da estratégia
TipoAssinaturaStrategy strategy = EnTipoAssinaturaFactory.getTipoAssinatura("NOVA_ASSINATURA");

// 2. Validar acesso
if (strategy.validarAcessoTipoAssinatura()) {
    
    // 3. Gerar parâmetros
    JSONObject parametros = strategy.getJsonParametros(12345, 1);
    
    // 4. Obter documentos para assinatura
    List<CrmAssinaturaDigitalDocumento> documentos = strategy.getDocumentosParaAssinar("12345;1");
    
    // 5. Criar envelope na fila
    NBSAPI_ENVELOPES_FILA envelope = new NBSAPI_ENVELOPES_FILA("envelope");
    envelope.setTIPO_ASSINATURA("NOVA_ASSINATURA");
    envelope.setVALOR_CODIGO("12345;1");
    envelope.setSTATUS("PENDENTE");
    envelope.insert();
}
```

## ?? Checklist de Implementação

### **Antes do Código Java:**
- ? Configurar `CRM_ASSINATURA_DIGITAL`
- ? Configurar `CRM_ASSINATURA_DIGITAL_PARM`
- ? Configurar `CRM_ASSINATURA_DIGITAL_SIGN`
- ? Configurar `CRM_ASSINATURA_DIGITAL_DOC` (se necessário)
- ? Configurar `CRM_ASSINATURA_DIGITAL_SISTEMA` (se necessário)

### **Após o Código Java:**
- ? Registrar no `EnTipoAssinaturaFactory`
- ? Testar com dados reais
- ? Validar funcionamento dos parâmetros
- ? Verificar geração de documentos
- ? Testar criação de envelopes

## ?? Dicas Importantes

- **Sempre configure o banco primeiro** antes de implementar o código
- **Teste com dados reais** após a configuração
- **Valide os parâmetros** antes de processar
- **Use try-catch adequadamente** para tratamento de erros
- **Comente lógicas complexas** para facilitar manutenção
- **Reutilize métodos** da interface quando possível
- **Monitore os logs** em `NBSAPI_ENVELOPES_LOG` para debugging


