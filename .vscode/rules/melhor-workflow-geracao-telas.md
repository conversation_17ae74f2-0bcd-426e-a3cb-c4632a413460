---
description: 
globs: 
alwaysApply: false
---
### (melhor) Criando workflow para geração de telas — Freedom (ZK)

Esta regra torna operacional o guia principal em [crmservice/.cursor/README.md](mdc:crmservice/crmservice/.cursor/README.md), especificando passos obrigatórios, limites do que pode ser editado, mensagens-gatilho para aguardar a geração manual e checklists de qualidade. Use esta regra sempre que criar/alterar uma tela (FrmXxx).

---

### Quando usar
- Criar uma nova tela: `crmservice/design/FrmXxx.frm` ainda não existe.
- Alterar uma tela existente: atualizar layout, eventos ou bindings no `.frm` e/ou comportamentos em `FrmXxxA.java` e `XxxRNA.java`.

### O que pode/DEVE editar
- `crmservice/design/FrmXxx.frm` (layout, colunas, bindings, eventos)
- `crmservice/src/main/java/freedom/bytecode/form/FrmXxxA.java` (comportamento concreto)
- `crmservice/src/main/java/freedom/bytecode/rn/XxxRNA.java` (regras de negócio concretas)

### O que NÃO pode editar (gerado pelo Freedom)
- `crmservice/src/main/java/freedom/bytecode/form/wizard/*`
- `crmservice/src/main/java/freedom/bytecode/rn/wizard/*`
- Cursores (módulo `cursores-zk`): somente USAR os existentes; caso precise de novo, solicitar via “Solicitação de Cursor”.

---

### Fluxo operacional obrigatório
1) Pré-checagem (rápida)
- Identifique a tela: `FrmXxx`.
- Levante os cursores/tabelas que a tela usa (existentes). Se faltar algo, planeje uma “Solicitação de Cursor”.
- Mapeie eventos atuais do Wizard e implementações em `FrmXxxA.java`.

2) Editar o `.frm`
- Arquivo: `crmservice/design/FrmXxx.frm`.
- O que fazer: adicionar/alterar componentes, colunas, bindings e eventos.
- Atenções críticas:
  - Se renomear eventos/componentes, você DEVERÁ ajustar `FrmXxxA.java` após a geração (ver Pitfalls).
  - Evite “quebrar” bindings: mantenha consistência de nomes e tipos esperados pelo RN/Wizard.

3) PONTO DE GERAÇÃO (humano)
- Pare aqui e peça geração manual. Emita a mensagem abaixo:
```
PONTO DE GERAÇÃO — FrmXxx
- Arquivo alterado: crmservice/design/FrmXxx.frm
- Mudanças principais: [liste componentes/colunas/eventos]
- Impacto esperado: [novos métodos abstratos no Wizard, novas colunas ligadas]
? Aguardar: “Wizard/RNW atualizados” para prosseguir.
```
- Somente após a confirmação “Wizard/RNW atualizados”, continue.

4) Implementar comportamento concreto em `FrmXxxA.java`
- Implemente TODOS os novos métodos abstratos expostos pelo Wizard.
- Trate exceções com `CrmServiceUtil.showError(...)`.
- Após mutações, atualize a UI: `tbX.refreshRecord()` ou reabra (`close ? setFilter ? open`).

5) Implementar regras de negócio em `XxxRNA.java`
- Padrão de abertura/filtragem:
```
tbX.close();
tbX.setFilter...(...);
tbX.setOrderBy("...");
tbX.open();
```
- Crie getters utilitários para dados usados na UI.
- Encapsule chamadas a pacotes/serviços nesta camada.

6) Validação e teste manual
- Abrir a tela e validar rotas/eventos principais.
- Confirmar que todos os métodos abstratos foram implementados.

---

### Solicitações (templates prontos)
- Solicitação de Geração (após editar `.frm`):
```
Form: FrmXxx
Arquivo alterado: crmservice/design/FrmXxx.frm
Mudanças no layout: [componentes/colunas/eventos adicionados/alterados]
Impacto esperado: [novos métodos abstratos no Wizard, novas colunas ligadas]
```

- Solicitação de Cursor (se faltar dataset/campo):
```
Tela: FrmXxx
Motivo: [campo/dataset ausente]
Cursor alvo: [existente a estender] ou [novo cursor]
Campos: [nomes + tipos]
Filtros/ordenadores: [critérios]
Relacionamentos: [joins necessários]
(Aguardar geração no Freedom antes de referenciar na RNA)
```

---

### Checklist de qualidade (DoD)
- `.frm` alterado conforme escopo.
- Geração confirmada (Wizard/RNW atualizados).
- `FrmXxxA.java` ajustado com todos os handlers de eventos.
- `XxxRNA.java` com filtros/aberturas/getters necessários.
- Nenhum arquivo gerado (wizard/*, rn/wizard/*) foi editado.
- Nenhum cursor foi criado/editado manualmente; somente uso dos existentes.
- Testes manuais básicos executados: abrir tela, acionar eventos principais, fluxo feliz.

---

### Dúvidas comuns e melhorias
- Renomeei um evento no `.frm`. O que fazer?
  - Após a geração, o Wizard mudará a assinatura dos métodos. Implemente/ajuste os nomes em `FrmXxxA.java`. Se métodos antigos não existirem mais, remova-os ou adapte para os novos nomes.

- Como evitar quebrar a UI após uma operação de escrita?
  - Sempre refletir as alterações: `tbX.refreshRecord()` quando afetar apenas o registro atual; reabrir tabela para alterações que afetem o conjunto.

- Faltou um método abstrato que eu esperava no Wizard. Por quê?
  - Verifique se o evento está corretamente nomeado e ligado no `.frm`. Sem ligar o evento, o Wizard não expõe o método. Ajuste o `.frm` e volte ao PONTO DE GERAÇÃO.

- Preciso de um novo dataset/campo que não existe.
  - Não crie cursores. Use a “Solicitação de Cursor” e aguarde a geração. Só então consuma na `RNA`.

- Posso marcar que foi “gerado por IA” nas classes?
  - Não. Siga as convenções existentes (sem sufixos/nomes especiais). A distinção operacional já está no fluxo (PONTO DE GERAÇÃO e edits no A/RNA).

- Em qual ordem implementar filtros no `RNA`?
  - Use o padrão: fechar ? aplicar filtros ? ordenar ? abrir. Mantenha a ordem determinística (`setOrderBy`) conforme necessário pela UI.

---

### Exemplos de referência no repositório
- Checklist de Importação:
  - `.frm`: `FrmChecklistImportacao.frm`
  - Wizard: `freedom.bytecode.form.wizard.FrmChecklistImportacao`
  - App: `freedom.bytecode.form.FrmChecklistImportacaoA`
  - RN: `freedom.bytecode.rn.ChecklistImportacaoRNA`

- Frota do Cliente:
  - `.frm`: `FrmFrotaCliente.frm`
  - Wizard: `freedom.bytecode.form.wizard.FrmFrotaCliente`
  - App: `freedom.bytecode.form.FrmFrotaClienteA`
  - RN: `freedom.bytecode.rn.FrotaClienteRNA`

---
