# Componentes Freedom - <PERSON><PERSON><PERSON> de <PERSON>o em Arquivos .frm

## Estrutura Básica de um Arquivo .frm

Os arquivos `.frm` seguem uma sintaxe similar ao Pascal/Delphi:

```pascal
object NomeFormulario: TFForm
  Left = 320
  Top = 162
  Caption = 'Título da Tela'
  ClientHeight = 400
  ClientWidth = 600
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '12345'
  ShortcutKeys = <>
  InterfaceRN = 'NomeTelaRN'
  Access = False
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  
  object componenteFilho: TipoComponente
    // propriedades do componente
  end
end
```

## Componentes de Layout

### TFVBox - Container Vertical
**Propriedades principais:**
- `Left`, `Top`: Posição
- `Width`, `Height`: Dimensões
- `Align`: Alinhamento (alClient, alTop, alBottom, alLeft, alRight)
- `AutoWrap`: Quebra automática
- `BevelKind`: Tipo de borda (bkTile, bkNone)
- `BevelOuter`: Borda externa (bvNone, bvRaised, bvLowered)
- `BorderStyle`: Estilo da borda (stNone)
- `Caption`: Texto do container
- `FlowStyle`: Estilo do fluxo (fsTopBottomLeftRight)
- `Padding.Top`, `Padding.Left`, `Padding.Right`, `Padding.Bottom`: Espaçamento interno

**Exemplo de uso:**
```pascal
object vBoxPrincipal: TFVBox
  Left = 0
  Top = 0
  Width = 423
  Height = 213
  Align = alClient
  AutoWrap = False
  BevelKind = bkTile
  BevelOuter = bvNone
  BorderStyle = stNone
  Caption = ' '
  FlowStyle = fsTopBottomLeftRight
  Padding.Top = 5
  Padding.Left = 10
  Padding.Right = 10
  Padding.Bottom = 5
end
```

### TFHBox - Container Horizontal
**Propriedades similares ao TFVBox**

### TFFrame - Frame Container
**Propriedades principais:**
- `Align`: Alinhamento
- `AutoWrap`: Quebra automática
- `Caption`: Título
- `FlowStyle`: Estilo do fluxo
- `TabOrder`: Ordem de tabulação
- `Flex.Vflex`, `Flex.Hflex`: Flexibilidade (ftTrue, ftFalse)
- `WOwner`: Proprietário (FrInterno, FrWizard)
- `WOrigem`: Origem (EhNone, EhMain)

**Exemplo de uso:**
```pascal
object FFramePrincipal: TFFrame
  Left = 0
  Top = 0
  Width = 1172
  Height = 619
  Align = alClient
  AutoWrap = False
  Caption = ' '
  FlowStyle = fsTopBottomLeftRight
  TabOrder = 0
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
  WOwner = FrInterno
  WOrigem = EhNone
end
```

## Componentes de Entrada de Dados

### TFString - Campo de Texto
**Propriedades principais:**
- `Left`, `Top`, `Width`, `Height`: Posição e dimensões
- `DataField`: Campo da tabela vinculado
- `MaxLength`: Tamanho máximo
- `ReadOnly`: Somente leitura
- `Required`: Campo obrigatório
- `TabOrder`: Ordem de tabulação
- `Text`: Texto padrão

### TFInteger - Campo Numérico Inteiro
**Propriedades principais:**
- `DataField`: Campo da tabela
- `MinValue`, `MaxValue`: Valores mínimo e máximo
- `ReadOnly`: Somente leitura
- `Value`: Valor padrão

### TFDecimal - Campo Numérico Decimal
**Propriedades principais:**
- `DataField`: Campo da tabela
- `Precision`: Número total de dígitos
- `Scale`: Casas decimais
- `Currency`: Formato monetário
- `DisplayFormat`: Formato de exibição

### TFDate - Campo de Data
**Propriedades principais:**
- `DataField`: Campo da tabela
- `Date`: Data padrão
- `Format`: Formato da data
- `ShowButton`: Mostrar botão do calendário

### TFTime - Campo de Hora
**Propriedades principais:**
- `DataField`: Campo da tabela
- `Time`: Hora padrão
- `Format`: Formato da hora

### TFCheckBox - Caixa de Seleção
**Propriedades principais:**
- `Caption`: Texto ao lado da caixa
- `DataField`: Campo da tabela
- `Checked`: Marcado por padrão
- `State`: Estado (cbChecked, cbUnchecked, cbGrayed)

### TFCombo - Lista Suspensa
**Propriedades principais:**
- `DataField`: Campo da tabela
- `Items.Strings`: Lista de itens
- `Style`: Estilo (csDropDown, csDropDownList)
- `ItemIndex`: Índice selecionado

### TFMemo - Campo de Texto Multilinha
**Propriedades principais:**
- `DataField`: Campo da tabela
- `Lines.Strings`: Linhas de texto
- `MaxLength`: Tamanho máximo
- `ReadOnly`: Somente leitura
- `ScrollBars`: Barras de rolagem (ssNone, ssVertical, ssHorizontal, ssBoth)
- `WordWrap`: Quebra de linha automática

## Componentes de Ação

### TFButton - Botão
**Propriedades principais:**
- `Caption`: Texto do botão
- `Enabled`: Habilitado/desabilitado
- `Visible`: Visível/invisível
- `Default`: Botão padrão
- `Cancel`: Botão de cancelar
- `ModalResult`: Resultado modal (mrOk, mrCancel, etc.)
- `OnClick`: Evento de clique

**Exemplo de uso:**
```pascal
object btnSalvar: TFButton
  Left = 10
  Top = 350
  Width = 75
  Height = 25
  Caption = 'Salvar'
  Default = True
  TabOrder = 1
  OnClick = btnSalvarClick
end
```

### TFLabel - Rótulo
**Propriedades principais:**
- `Caption`: Texto exibido
- `Font.Style`: Estilo da fonte ([fsBold], [fsItalic], [])
- `Font.Color`: Cor do texto
- `Alignment`: Alinhamento (taLeftJustify, taCenter, taRightJustify)
- `AutoSize`: Redimensionar automaticamente
- `WordWrap`: Quebra de linha

**Exemplo de uso:**
```pascal
object lblTitulo: TFLabel
  Left = 10
  Top = 10
  Width = 100
  Height = 13
  Caption = 'Nome do Cliente:'
  Font.Style = [fsBold]
end
```

## Componentes de Dados

### TFTable - Cursor/Tabela
**Propriedades principais:**
- `TableName`: Nome da tabela no banco
- `KeyFields`: Campos chave
- `Filter`: Filtro SQL
- `OrderBy`: Ordenação
- `Active`: Ativo/inativo
- `ReadOnly`: Somente leitura

**Exemplo de uso:**
```pascal
object tbClientes: TFTable
  TableName = 'CLIENTES'
  KeyFields = 'ID_CLIENTE'
  Active = True
  Left = 100
  Top = 50
end
```

### TFGrid - Grade de Dados
**Propriedades principais:**
- `DataSource`: Fonte de dados (nome da tabela)
- `ReadOnly`: Somente leitura
- `Options`: Opções da grade
- `TabOrder`: Ordem de tabulação
- `OnCellClick`: Evento de clique na célula
- `OnDblClick`: Evento de duplo clique

**Exemplo com colunas:**
```pascal
object gridPrincipal: TFGrid
  Left = 10
  Top = 50
  Width = 500
  Height = 200
  DataSource = tbClientes
  TabOrder = 0
  
  object colCodigo: TFGridColumn
    DataField = 'CODIGO'
    Title.Caption = 'Código'
    Width = 80
    ReadOnly = True
  end
  
  object colNome: TFGridColumn
    DataField = 'NOME'
    Title.Caption = 'Nome'
    Width = 200
  end
end
```

### TFGridColumn - Coluna de Grade
**Propriedades principais:**
- `DataField`: Campo da tabela
- `Title.Caption`: Título da coluna
- `Width`: Largura da coluna
- `Alignment`: Alinhamento (taLeftJustify, taCenter, taRightJustify)
- `Visible`: Visibilidade da coluna
- `ReadOnly`: Somente leitura
- `Required`: Campo obrigatório

## Componentes Especiais

### TFImage - Imagem
**Propriedades principais:**
- `Picture`: Imagem
- `Stretch`: Redimensionar imagem
- `Proportional`: Manter proporção
- `Center`: Centralizar imagem

**Exemplo de uso:**
```pascal
object imgLogo: TFImage
  Left = 10
  Top = 10
  Width = 100
  Height = 50
  Stretch = True
  Proportional = True
end
```

### TFPageControl - Controle de Abas
**Propriedades principais:**
- `ActivePage`: Página ativa
- `TabPosition`: Posição das abas (tpTop, tpBottom, tpLeft, tpRight)
- `TabOrder`: Ordem de tabulação

**Exemplo de uso:**
```pascal
object pgcPrincipal: TFPageControl
  Left = 0
  Top = 0
  Width = 500
  Height = 300
  ActivePage = tabDados
  TabOrder = 0
  
  object tabDados: TFTabSheet
    Caption = 'Dados Gerais'
    
    object lblNome: TFLabel
      Left = 10
      Top = 10
      Caption = 'Nome:'
    end
  end
  
  object tabEndereco: TFTabSheet
    Caption = 'Endereço'
  end
end
```

### TFTabSheet - Página de Aba
**Propriedades principais:**
- `Caption`: Título da aba
- `TabVisible`: Visibilidade da aba
- `PageIndex`: Índice da página

## Propriedades Especiais do TFForm

### Propriedades Obrigatórias
- `WOwner = FrWizard`: Proprietário do formulário
- `WOrigem = EhMain`: Origem do formulário  
- `WKey = 'numero'`: Chave única do formulário
- `InterfaceRN = 'NomeTelaRN'`: Interface de regras de negócio
- `ShortcutKeys = <>`: Teclas de atalho
- `Access = False`: Controle de acesso

### Propriedades de Layout
- `ClientHeight`, `ClientWidth`: Dimensões da área cliente
- `Constraints.MaxHeight`, `Constraints.MaxWidth`: Restrições de tamanho
- `BorderIcons`: Ícones da borda ([biSystemMenu, biMinimize])

### Eventos Comuns
- `OnCreate = FFormCreate`: Evento de criação do formulário
- `OnClose = FFormClose`: Evento de fechamento
- `OnShow = FFormShow`: Evento de exibição

## Exemplo Completo

```pascal
object FrmExemplo: TFForm
  Left = 320
  Top = 162
  ActiveControl = vBoxPrincipal
  Caption = 'Exemplo de Cadastro'
  ClientHeight = 400
  ClientWidth = 600
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '12345'
  ShortcutKeys = <>
  InterfaceRN = 'ExemploRN'
  Access = False
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  
  object tbPrincipal: TFTable
    TableName = 'TABELA_EXEMPLO'
    KeyFields = 'ID'
    Active = True
    Left = 50
    Top = 50
  end
  
  object vBoxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 600
    Height = 400
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 10
    Padding.Left = 10
    Padding.Right = 10
    Padding.Bottom = 10
    
    object lblCodigo: TFLabel
      Left = 0
      Top = 0
      Caption = 'Código:'
      Font.Style = [fsBold]
    end
    
    object edtCodigo: TFInteger
      Left = 0
      Top = 20
      Width = 100
      Height = 21
      DataField = 'CODIGO'
      TabOrder = 0
    end
    
    object gridDados: TFGrid
      Left = 0
      Top = 60
      Width = 580
      Height = 250
      DataSource = tbPrincipal
      TabOrder = 1
      
      object colCodigo: TFGridColumn
        DataField = 'CODIGO'
        Title.Caption = 'Código'
        Width = 80
      end
      
      object colNome: TFGridColumn
        DataField = 'NOME'
        Title.Caption = 'Nome'
        Width = 200
      end
    end
    
    object btnSalvar: TFButton
      Left = 0
      Top = 330
      Width = 75
      Height = 25
      Caption = 'Salvar'
      TabOrder = 2
      OnClick = btnSalvarClick
    end
  end
end
```

## Dicas Importantes

1. **Hierarquia**: Componentes filhos devem estar dentro do bloco `object...end` do pai
2. **Posicionamento**: Use `Left`, `Top`, `Width`, `Height` para posicionamento absoluto
3. **Alinhamento**: Use `Align` para alinhamento automático (alClient, alTop, etc.)
4. **TabOrder**: Define a ordem de navegação por Tab
5. **DataField**: Vincula componentes aos campos das tabelas
6. **Eventos**: Nomeie eventos seguindo o padrão `nomeComponenteEvento` (ex: `btnSalvarClick`)
7. **atributos**:cuidado ao adicionar atributos que não existem ao compoenente, em caso de duvida pesquisar casos de uso em outros .frm
8. **TFTable**:não adicionar objetos TFTable, sempre solicitar, ou apenas utilizar os que já estão no .frm
