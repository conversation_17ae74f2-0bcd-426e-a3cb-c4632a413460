---
description: componentes freedom, Padrões de nomenclatura e convenções, Informações sobre dependências e tecnologias
globs: 
alwaysApply: false
---
# Freedom ZK Components - Documentação Técnica

## Estrutura do JAR freedom_zk_components-********.jar

### Localização
```
C:\Users\<USER>\.m2\repository\br\com\nbs\freedom\freedom_zk_components\********\freedom_zk_components-********.jar
```

### Pacote Principal
```
freedom.client.controls.impl
```

## Organização dos Componentes

### Classes Principais (153 componentes total)

#### Componentes de Layout
- **TFVBox** / **TFHBox** - Containers flexbox
- **TFPanel** - Panel básico
- **TFBorderPanel** - Panel com bordas
- **TFGroupbox** - Caixa de grupo
- **TFScrollpanel** - Panel com scroll
- **TFDrawer** - Gaveta lateral
- **TFPortalLayout** / **TFPortalPanel** - Layout portal
- **TFGridPanel** - Panel para grids

#### Componentes de Entrada
- **TFButton** - Botão
- **TFLabel** - Rótulo
- **TFCombo** / **TFComboEx** - Caixa de seleção
- **TFDate** - Campo de data
- **TFTime** - Campo de tempo
- **TFMemo** - Texto multilinha
- **TFRichEdit** - Editor rico
- **TFSpinner** / **TFDecimalSpinner** - Campos numéricos
- **TFSlider** - Controle deslizante
- **TFSignature** - Assinatura digital
- **TFBandbox** - Campo com busca
- **TFInputGroup** - Grupo de inputs
- **TFString** / **TFInteger** / **TFDecimal** - Campos tipados
- **TFCheckBox** - Checkbox
- **TFRadioButton** / **TFRadioGroup** - Botões de rádio

#### Componentes de Dados
- **TFGrid** - Grid básico
- **TFTreeGrid** - Grid hierárquico
- **TFTree** - Árvore
- **TFTable** - Tabela
- **TFDataSet** - Conjunto de dados
- **TFCustomDataSource** / **TFCustomNonQueryDataSource** - Fontes de dados
- **TFGridKanban** - Grid Kanban
- **TFDualList** - Lista dupla

#### Componentes de Gráficos
- **TFChartBar** - Gráfico de barras
- **TFChartLine** - Gráfico de linhas
- **TFChartPie** - Gráfico de pizza
- **TFChartGauge** - Medidor
- **TFChartGantt** - Gráfico de Gantt
- **TFChartDial** / **TFChartDialRange** - Mostradores
- **TFChartFunnel** - Gráfico funil
- **TFChartCandlestick** - Gráfico de velas
- **TFChartSerie** / **TFChartSerieGantt** / **TFChartStop** - Séries de dados

#### Componentes Especializados
- **TFAccordionMenu** - Menu accordion
- **TFTimeline** - Linha do tempo
- **TFCamera** - Câmera
- **TFCalendar** / **TFMonthCalendar** - Calendários
- **TFModalBox** - Caixa modal
- **TFCoachmark** - Tutoriais
- **TFFilterPanel** / **TFFilterWindow** - Filtros
- **TFMenu** / **TFMenuItem** / **TFPopupMenu** - Menus
- **TFTabsheet** / **TFPageControl** - Abas
- **TFBreadcrumb** - Navegação
- **TFChatpanel** - Chat
- **TFDiagram** - Diagramas

#### Componentes de Comunicação
- **TFSMTPClient** - Cliente SMTP
- **TFFTPClient** - Cliente FTP
- **TFPOP3Client** - Cliente POP3
- **TFIMAPClient** - Cliente IMAP

#### Componentes Utilitários
- **TFTimer** - Timer
- **TFSchema** - Esquema
- **TFIconClass** - Ícones
- **TFColorButton** / **TFColor** - Cores
- **TFImage** - Imagens
- **TFReport** - Relatórios
- **TFServiceConfig** - Configuração de serviços
- **TFStoredProcedure** - Procedimentos armazenados

## Subpacotes Especializados

### accordion/
- **TFAccordionMenuItemRenderer** - Renderizador de itens do accordion

### calendar/
- **CalendarsEvent** - Eventos de calendário

### coachmark/
- **TFCoachmarkItemList** - Lista de itens do coachmark

### dataset/
- **TFDataSetFieldList** - Lista de campos do dataset

### duallist/
- **SwapOnClick** - Troca por clique
- **SwapOnDrop** - Troca por arrastar
- **SwapRows** - Troca de linhas
- **TFDualListEvents** - Eventos da lista dupla

### filterpanel/
- **CMPFilterCol** / **CMPFilterColTopLabel** - Colunas de filtro
- **CMPFilterOptRow** - Linha de opções de filtro
- **FilterConfigLoader** - Carregador de configuração
- **ICMPFilterCol** - Interface de coluna de filtro
- **TFFilterPanelDesign** - Design do panel de filtro

### grid/
- **BindableCell** / **EditableCell** / **EditableListitem** - Células editáveis
- **BlobDownloadEvent** / **BlobViewer** - Visualização de blobs
- **FExcelExporter** / **FPdfExporter** / **XLSXExporter** - Exportadores
- **TFAuxColumnHeaderList** / **TFColumnEditor** - Editores de coluna
- **TFFieldComparator** - Comparador de campos
- **TFGridColumnList** / **TFGridEditRenderer** / **TFGridExporter** - Grid
- **TFGridGroupComparator** / **TFGridGroupModel** / **TFGridModel** - Modelos
- **TFGridRenderer** - Renderizador do grid
- **ToggleEnableBind** - Bind de habilitação

### gridmd/
- **TFGridMDCell** - Célula do grid MD
- **TFGridMDColumnList** - Lista de colunas MD
- **TFGridMDRenderer** - Renderizador MD

### kanban/
- **TFGridKanbanExpression** - Expressões Kanban
- **KanbanConfig.js** - Configuração JavaScript

### messagebox/
- **MessageboxDlg** - Diálogo de mensagem

### schema/
- **TFSchemaItemList** - Lista de itens do schema

### table/
- **TFTableEvents** - Eventos da tabela
- **TFTableFieldList** - Lista de campos da tabela

### tree/
- **TFTreeItemRenderer** - Renderizador de itens
- **TFTreeModel** - Modelo da árvore
- **TFTreeNode** - Nó da árvore
- **TFTreeNodeComparator** - Comparador de nós

### treegrid/
- **EditableTreecell** / **EditableTreerow** - Células editáveis
- **TFTreeGridColumnList** - Lista de colunas
- **TFTreeGridEditRenderer** / **TFTreeGridExporter** - Renderização e exportação
- **TFTreeGridItemRenderer** / **TFTreeGridModel** - Renderização e modelo

## Padrões de Nomenclatura

### Prefixos
- **TF** - Todos os componentes principais
- **CMP** - Componentes de filtro
- **F** - Exportadores e utilitários específicos

### Sufixos
- **Renderer** - Renderizadores
- **Model** - Modelos de dados
- **List** - Listas de itens
- **Events** - Manipuladores de eventos
- **Config** - Configurações
- **Exporter** - Exportadores

## Arquivos de Exemplo

### Principais arquivos .frm com exemplos:
- [FrmChecklistImportacao.frm](mdc:crmservice/design/FrmChecklistImportacao.frm) - Layout básico
- [FrmOportunidadeVendas.frm](mdc:crmservice/design/FrmOportunidadeVendas.frm) - Formulário complexo
- [FrmHomeService.frm](mdc:crmservice/design/FrmHomeService.frm) - Accordion menu
- [FrmCentralReclamacoes.frm](mdc:crmservice/design/FrmCentralReclamacoes.frm) - Gráficos
- [FrmAgendaPremium.frm](mdc:crmservice/design/FrmAgendaPremium.frm) - Timeline
- [FrmAssinatura.frm](mdc:crmservice/design/FrmAssinatura.frm) - Assinatura digital

### Localização dos arquivos:
```
crmservice/design/          - Formulários principais (725+ arquivos)
nbs-empresa-zk/design/      - Formulários do módulo empresa (220+ arquivos)
```

## Dependências

### JAR Principal
```xml
<dependency>
    <groupId>br.com.nbs.freedom</groupId>
    <artifactId>freedom_zk_components</artifactId>
    <version>********</version>
</dependency>
```

### Tecnologias Base
- **ZK Framework** - Framework web Java
- **Java** - Linguagem de programação
- **Pascal/Delphi** - Sintaxe dos arquivos .frm

## Uso nos Formulários

### Estrutura típica de um formulário:
```pascal
object FormName: TFForm
  // Propriedades do formulário
  object containerPrincipal: TFVBox
    // Container principal
    object componenteFilho: TFComponente
      // Configurações do componente
    end
  end
end
```

### Propriedades comuns:
- **Left, Top, Width, Height** - Posicionamento e tamanho
- **WOwner, WOrigem** - Propriedades de ownership
- **Flex.Vflex, Flex.Hflex** - Flexibilidade
- **Padding, Margin, Spacing** - Espaçamentos
- **BorderRadius, BoxShadowConfig** - Aparência

### Eventos comuns:
- **OnClick, OnResize, OnCreate** - Eventos básicos
- **OnCellClick, OnEventClick** - Eventos específicos de componentes
- **OnNavigate, OnDrag** - Eventos de navegação e arrastar

## Busca de Exemplos

Para encontrar exemplos de uso de um componente específico:

```bash
# Buscar por nome do componente
grep -r "TFComponenteName" design/

# Buscar em arquivos .frm
find design/ -name "*.frm" -exec grep -l "TFComponenteName" {} \;
```

## Integração com Regras de Negócio

Os formulários (.frm) são associados a classes de regra de negócio (.dtm) através da propriedade `InterfaceRN`:

```pascal
InterfaceRN = 'NomeClasseRN'
```

As classes RN correspondentes estão em:
- `src/main/java/freedom/bytecode/rn/` - Regras compiladas
- `design/*.dtm` - Definições das regras

