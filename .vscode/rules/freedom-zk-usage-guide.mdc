---
description: componentes freedom, Comandos de busca para encontrar exemplos específicos, Padrões de implementação com código completo, Configurações detalhadas para componentes complexos
globs: 
alwaysApply: false
---
# Freedom ZK Components - Guia de Uso Prático

## Como Buscar Exemplos de Componentes

### Busca por Tipo de Componente

#### Componentes de Layout
```bash
# Buscar containers
grep -r "TFVBox\|TFHBox\|TFPanel\|TFGroupbox" design/ --include="*.frm"

# Buscar layouts especializados
grep -r "TFDrawer\|TFPortalLayout\|TFBorderPanel" design/ --include="*.frm"
```

#### Componentes de Entrada
```bash
# Buscar campos de entrada básicos
grep -r "TFButton\|TFLabel\|TFCombo\|TFDate\|TFMemo" design/ --include="*.frm"

# Buscar campos especializados
grep -r "TFSignature\|TFSpinner\|TFSlider\|TFBandbox" design/ --include="*.frm"
```

#### Componentes de Dados
```bash
# Buscar grids e tabelas
grep -r "TFGrid\|TFTreeGrid\|TFTree\|TFTable" design/ --include="*.frm"

# Buscar fontes de dados
grep -r "TFDataSet\|TFCustomDataSource" design/ --include="*.frm"
```

#### Componentes de Gráficos
```bash
# Buscar todos os gráficos
grep -r "TFChart" design/ --include="*.frm"

# Buscar tipos específicos
grep -r "TFChartBar\|TFChartLine\|TFChartPie\|TFChartGauge" design/ --include="*.frm"
```

### Busca por Funcionalidade

#### Assinatura Digital
Arquivos com exemplos de `TFSignature`:
- [FrmAssinatura.frm](mdc:crmservice/design/FrmAssinatura.frm)
- [FrmConsultaOrcAprovCliente.frm](mdc:crmservice/design/FrmConsultaOrcAprovCliente.frm)
- [FrmChecklistOsAssinaturaConsultor.frm](mdc:crmservice/design/FrmChecklistOsAssinaturaConsultor.frm)
- [FrmAssinaturaGenerica.frm](mdc:crmservice/design/FrmAssinaturaGenerica.frm)

#### Timeline/Agenda
Arquivos com exemplos de `TFTimeline`:
- [FrmAgendaPremium.frm](mdc:crmservice/design/FrmAgendaPremium.frm)
- [FrmAgendaProgramacao.frm](mdc:crmservice/design/FrmAgendaProgramacao.frm)
- [FrmAgendaPremiumServico.frm](mdc:crmservice/design/FrmAgendaPremiumServico.frm)
- [FrmAgendaConsultor.frm](mdc:crmservice/design/FrmAgendaConsultor.frm)

#### Gráficos e Dashboards
Arquivos com exemplos de gráficos:
- [FrmCentralReclamacoes.frm](mdc:crmservice/design/FrmCentralReclamacoes.frm) - Bar e Line
- [FrmcursoresServico.frm](mdc:crmservice/design/FrmcursoresServico.frm) - Pie
- [FrmChipDetalheOS.frm](mdc:crmservice/design/FrmChipDetalheOS.frm) - Gauge
- [FrmGerencialPainel.frm](mdc:crmservice/nbs-empresa-zk/design/FrmGerencialPainel.frm) - Múltiplos tipos

#### Câmera e Captura
Arquivos com exemplos de `TFCamera`:
- [FrmVideoSnapShot.frm](mdc:crmservice/nbs-empresa-zk/design/FrmVideoSnapShot.frm)
- [FrmSnapShotCamera.frm](mdc:crmservice/nbs-empresa-zk/design/FrmSnapShotCamera.frm)

## Padrões de Implementação

### Layout Responsivo com Flexbox

#### Container Principal
```pascal
object containerPrincipal: TFVBox
  Align = alClient
  AutoWrap = False
  FlowStyle = fsTopBottomLeftRight
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
  Padding.Top = 5
  Padding.Left = 5
  Padding.Right = 5
  Padding.Bottom = 5
  Spacing = 1
end
```

#### Container Horizontal
```pascal
object containerHorizontal: TFHBox
  AutoWrap = False
  FlowStyle = fsLeftRightTopBottom
  Flex.Vflex = ftFalse
  Flex.Hflex = ftTrue
  Spacing = 10
end
```

### Configuração de Gráficos

#### Gráfico de Barras Completo
```pascal
object chartBar: TFChartBar
  Width = 400
  Height = 300
  BuildIn3d = False
  ShowLegend = True
  ShowTooltip = True
  ShowDataLabel = True
  Table = tbDados
  Title = 'Título do Gráfico'
  TooltipFormat = '{0}: {1}'
  Series = <
    item
      CaptionField = 'DESCRICAO'
      XField = 'CATEGORIA'
      YField = 'VALOR'
    end>
  Orient = coVertical
  Stacking = False
  XLabelRotation = 0
  YLabelRotation = 0
  LegendAlign = center
  LegendVerticalAlign = bottom
  ColorPaletteIndex = 13
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
end
```

#### Gráfico de Pizza
```pascal
object chartPie: TFChartPie
  Width = 300
  Height = 300
  ShowLegend = True
  Table = tbDados
  Title = 'Distribuição'
  Series = <
    item
      CaptionField = 'DESCRICAO'
      YField = 'VALOR'
    end>
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
end
```

### Timeline/Agenda

#### Configuração Básica
```pascal
object timeline: TFTimeline
  Width = 800
  Height = 600
  Caption = 'Agenda'
  Table = tbEventos
  LookupTable = tbRecursos
  OnClick = timelineClick
  OnCellClick = timelineCellClick
  OnEventClick = timelineEventClick
  OnEventChanged = timelineEventChanged
  OnNavigate = timelineNavigate
  OnDrag = timelineDrag
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
end
```

### Accordion Menu

#### Menu Lateral
```pascal
object accordionMenu: TFAccordionMenu
  Width = 250
  Height = 400
  Menu = menuPrincipal
  ExpandAll = False
  ShowSearchBar = True
  OpenOnSelect = True
  Font.Color = clWhite
  Font.Height = -14
  Font.Name = 'Tahoma'
  Flex.Vflex = ftTrue
  Flex.Hflex = ftFalse
end
```

### Assinatura Digital

#### Campo de Assinatura
```pascal
object signature: TFSignature
  Width = 400
  Height = 200
  // Eventos específicos da assinatura
  OnSignatureChanged = signatureChanged
  Flex.Vflex = ftFalse
  Flex.Hflex = ftTrue
end
```

### Grid com Funcionalidades Avançadas

#### TreeGrid Editável
```pascal
object treeGrid: TFTreeGrid
  Width = 600
  Height = 400
  Table = tbDados
  // Configurações de edição
  AllowEdit = True
  AllowInsert = True
  AllowDelete = True
  // Eventos
  OnCellClick = treeGridCellClick
  OnCellEdit = treeGridCellEdit
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
end
```

## Propriedades Importantes

### Flexbox (Todos os Containers)
- `Flex.Vflex` - Flexibilidade vertical:
  - `ftTrue` - Expande verticalmente
  - `ftFalse` - Tamanho fixo
  - `ftMin` - Tamanho mínimo
- `Flex.Hflex` - Flexibilidade horizontal (mesmas opções)

### Espaçamento
- `Padding.Top/Left/Right/Bottom` - Espaçamento interno
- `Margin.Top/Left/Right/Bottom` - Espaçamento externo
- `Spacing` - Espaçamento entre elementos filhos

### Aparência
- `BorderRadius.TopLeft/TopRight/BottomLeft/BottomRight` - Bordas arredondadas
- `BoxShadowConfig` - Configuração de sombra:
  - `HorizontalLength` - Deslocamento horizontal
  - `VerticalLength` - Deslocamento vertical
  - `BlurRadius` - Raio de desfoque
  - `SpreadRadius` - Raio de espalhamento
  - `ShadowColor` - Cor da sombra
  - `Opacity` - Opacidade (0-100)

### Dados (Componentes de Dados)
- `Table` - Tabela principal de dados
- `LookupTable` - Tabela de lookup/referência
- `Field` - Campo específico da tabela
- `CaptionField` - Campo para caption/descrição
- `XField/YField` - Campos para eixos X e Y (gráficos)

## Eventos Comuns

### Eventos Básicos
- `OnClick` - Clique simples
- `OnResize` - Redimensionamento
- `OnCreate` - Criação do componente

### Eventos de Dados
- `OnCellClick` - Clique em célula
- `OnCellEdit` - Edição de célula
- `OnEventClick` - Clique em evento (Timeline)
- `OnEventChanged` - Mudança de evento

### Eventos de Navegação
- `OnNavigate` - Navegação
- `OnDrag` - Arrastar
- `OnDrop` - Soltar

## Dicas de Desenvolvimento

### 1. Sempre use containers flexbox
```pascal
// Container principal sempre TFVBox ou TFHBox
object containerPrincipal: TFVBox
  Align = alClient
  Flex.Vflex = ftTrue
  Flex.Hflex = ftTrue
end
```

### 2. Configure espaçamentos adequados
```pascal
// Espaçamento padrão recomendado
Padding.Top = 5
Padding.Left = 5
Padding.Right = 5
Padding.Bottom = 5
Spacing = 1
```

### 3. Use propriedades WOwner e WOrigem
```pascal
// Sempre definir ownership
WOwner = FrInterno
WOrigem = EhNone
```

### 4. Para gráficos, sempre configure Series
```pascal
Series = <
  item
    CaptionField = 'CAMPO_DESCRICAO'
    XField = 'CAMPO_X'
    YField = 'CAMPO_Y'
    GUID = '{GUID-UNICO}'
    WOwner = FrInterno
    WOrigem = EhNone
  end>
```

### 5. Timeline precisa de tabelas configuradas
```pascal
// Timeline sempre precisa de Table e LookupTable
Table = tbEventos
LookupTable = tbRecursos
```

## Resolução de Problemas

### Componente não aparece
1. Verificar `Align` ou `Flex` configurações
2. Verificar `Width` e `Height`
3. Verificar `Visible = True`

### Gráfico não exibe dados
1. Verificar se `Table` está configurada
2. Verificar se `Series` tem campos corretos
3. Verificar se dados existem na tabela

### Timeline não funciona
1. Verificar `Table` e `LookupTable`
2. Verificar eventos configurados
3. Verificar estrutura dos dados

### Layout quebrado
1. Verificar configurações de `Flex`
2. Verificar `FlowStyle`
3. Verificar `Padding` e `Margin`

## Referências Rápidas

### Buscar componente específico:
```bash
grep -r "TFNomeComponente" design/ --include="*.frm" -n
```

### Buscar por propriedade:
```bash
grep -r "propriedade =" design/ --include="*.frm" -A 5 -B 5
```

### Buscar por evento:
```bash
grep -r "OnEvento =" design/ --include="*.frm" -A 2 -B 2
```

### Listar todos os componentes TF em um arquivo:
```bash
grep -o "TF[A-Za-z]*" design/arquivo.frm | sort | uniq
```

