---
description: 
globs: 
alwaysApply: false
---
# **Regras e Documentação para Agente de IA — Framework Freedom/ZK**

## **1\. Objetivo**

Este documento define as diretrizes, limitações e procedimentos operacionais para o agente de IA que auxilia no desenvolvimento sobre o ecossistema de projetos Java com o framework **Freedom** e **ZK**. O objetivo é garantir que o agente atue de forma consistente, segura e eficiente, maximizando a reutilização de código e aderindo estritamente à arquitetura estabelecida.

## **2\. Arquitetura do Ecossistema**

O agente deve ter um conhecimento profundo da seguinte estrutura para tomar decisões corretas.

### **2.1. Divisão de Projetos**

| Projeto | Responsabilidade Principal | Análise do Agente |
| :---- | :---- | :---- |
| **crmservice** | Projeto principal da aplicação. | Contém a lógica de negócio e as telas específicas do CRM. É o foco principal das implementações. |
| **nbs-crm-empresa-zk** | Módulo com funcionalidades de negócio comuns. | Fonte de consulta para lógica de negócio que pode ser compartilhada entre diferentes projetos. |
| **cursores-zk** | **Biblioteca central de cursores (datasets).** | **Fonte única da verdade para estruturas de dados.** O agente deve apenas consultar e utilizar cursores deste projeto, **nunca criar**. |
| **nbs-utils-zk** | Biblioteca de utilitários e funções genéricas. | Principal fonte de consulta para funções reutilizáveis, especialmente chamadas a pacotes de banco de dados (@freedom.util.pkg). |

### **2.2. Estrutura de Arquivos de Tela (Ex: FrotaCliente)**

A arquitetura de uma tela é segmentada em três arquivos. O agente deve respeitar a responsabilidade de cada um:

| Arquivo | Conteúdo e Regras de Manipulação |
| :---- | :---- |
| **@FrmFrotaCliente.java** | **ARQUIVO INTOCÁVEL (SOMENTE LEITURA).** Contém a declaração da interface do usuário (UI), listando todos os componentes Freedom (TFLabel, TFButton, TFGrid, etc.) e seus eventos. **Este arquivo é gerado e sincronizado por uma ferramenta externa. O agente NUNCA deve modificá-lo.** |
| **@FrmFrotaClienteA.java** | **ÁREA DE ATUAÇÃO PRINCIPAL.** Contém a lógica de controle da tela (controller). Implementa os métodos de eventos (onClick, onSelect), manipula os componentes visuais e coordena as chamadas para as regras de negócio. |
| **@FrotaClienteRNA.java** | **ÁREA DE REGRAS DE NEGÓCIO.** Contém a lógica de negócio associada aos cursores (datasets) da tela. Funções de validação, cálculo, e manipulação de dados residem aqui. |

## **3\. As Regras de Ouro (Restrições Mandatórias)**

Estas são as diretrizes fundamentais que o agente deve seguir sem exceção.

1. **IMUTABILIDADE DO ARQUIVO @FrmXXX.java**: O agente é **proibido** de escrever, alterar ou sugerir alterações para os arquivos @Frm\[NomeTela\].java. Ele deve tratá-los como um "mapa" de somente leitura dos componentes disponíveis em tela.  
2. **USO EXCLUSIVO DE COMPONENTES EXISTENTES**: O agente só pode interagir com os componentes (botões, campos, grids) que já estão **declarados no arquivo @Frm\[NomeTela\].java**.  
3. **PROTOCOLO DE SOLICITAÇÃO DE COMPONENTES**: Se uma tarefa exige um componente que não existe na tela, o agente **NÃO DEVE** criá-lo. Em vez disso, ele deve interromper o processo e solicitar formalmente ao desenvolvedor que adicione o componente necessário através da ferramenta externa.  
   * **Exemplo de Solicitação Formal**: *"AÇÃO REQUERIDA: Para implementar a funcionalidade de 'Exportar para Excel', é necessário um TFButton na tela. Por favor, adicione um botão com o ID btnExportarExcel ao arquivo @FrmFrotaCliente.java para que eu possa prosseguir com a implementação da lógica."*  
4. **PROIBIÇÃO DA CRIAÇÃO DE CURSORES**: O agente é **proibido** de definir ou criar novos cursores (datasets). Ele deve, obrigatoriamente, buscar e utilizar os cursores já definidos no projeto cursores-zk.  
5. **REUTILIZAÇÃO ACIMA DA CRIAÇÃO**: Antes de escrever qualquer nova função, o agente deve primeiro realizar uma busca por funcionalidades similares em:  
   * nbs-utils-zk (com atenção especial ao pacote @freedom.util.pkg).  
   * nbs-crm-empresa-zk.  
   * Outros arquivos ...A.java e ...RNA.java de telas já existentes no projeto.  
6. **NÃO GERAÇÃO DE ARQUIVOS**: O agente não cria a estrutura de arquivos de tela (@Frm..., @Frm...A, @...RNA). Ele assume que esses arquivos já existem e seu trabalho é preencher a lógica nos locais apropriados (...A.java e ...RNA.java).

## **4\. Procedimentos Operacionais Padrão (POPs)**

### **POP-01: Implementando uma Nova Funcionalidade em Tela**

1. **Análise da Solicitação**: Identificar a tela de destino (ex: FrotaCliente) e a funcionalidade desejada (ex: "validar CNPJ ao digitar").  
2. **Inspeção de Componentes**: Consultar o arquivo @FrmFrotaCliente.java para listar todos os componentes de UI e eventos disponíveis. Identificar o campo de CNPJ (ex: txtCNPJ) e seu evento (ex: onChanging).  
3. **Validação de Pré-requisitos**: Verificar se todos os componentes e eventos necessários existem.  
   * **Se SIM**: Prosseguir para o passo 4\.  
   * **Se NÃO**: Executar o **PROTOCOLO DE SOLICITAÇÃO DE COMPONENTES** (Regra de Ouro \#3) e aguardar a intervenção do desenvolvedor.  
4. **Busca por Lógica Reutilizável**: Pesquisar por uma função de validação de CNPJ em nbs-utils-zk.  
   * **Se encontrar Util.validaCNPJ(String cnpj)**: Utilizar a função pronta.  
   * **Se não encontrar**: Implementar a lógica de validação, mas informar ao desenvolvedor sobre a criação para que possa ser avaliada a sua extração para o nbs-utils-zk no futuro.  
5. **Implementação da Lógica**:  
   * Escrever o código que captura o evento no arquivo @FrmFrotaClienteA.java.  
   * Chamar a função de validação (seja do utils ou do RNA).  
   * Se a validação for uma regra de negócio do cursor, a lógica principal deve residir no @FrotaClienteRNA.java.  

6. **Apresentação da Solução**: Apresentar os blocos de código a serem inseridos nos arquivos corretos, com comentários claros sobre o que cada trecho faz.