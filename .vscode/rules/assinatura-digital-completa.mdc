---
description: 
globs: 
alwaysApply: false
---
# Guia Completo para Criação de Assinaturas Digitais

## ?? Visão Geral
Este projeto utiliza o padrão Strategy para implementar diferentes tipos de assinatura digital. Cada tipo de assinatura deve implementar a interface `TipoAssinaturaStrategy` e ser registrado no factory `EnTipoAssinaturaFactory`.

## ??? Estrutura Principal

### Interface Base
- **Arquivo**: [TipoAssinaturaStrategy.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/TipoAssinaturaStrategy.java)
- **Propósito**: Define o contrato que todas as estratégias de assinatura digital devem implementar

### Factory de Estratégias
- **Arquivo**: [EnTipoAssinaturaFactory.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/EnTipoAssinaturaFactory.java)
- **Propósito**: Centraliza o registro e recuperação das estratégias de assinatura digital

## ??? Configuração no Banco de Dados

### ?? **IMPORTANTE: Antes de criar uma nova assinatura, as seguintes tabelas DEVEM ser alimentadas:**

#### 1. **CRM_ASSINATURA_DIGITAL** (Tabela Principal)
```sql
-- Estrutura da tabela principal
INSERT INTO CRM_ASSINATURA_DIGITAL (
    ID_ASSINATURA_DIGITAL,    -- Chave primária (auto-incremento)
    TIPO_ASSINATURA,          -- Deve corresponder ao valor de getTipo()
    DESCRICAO,                -- Descrição da assinatura
    OBSERVACAO,               -- Observações adicionais
    TIPO_CODIGO,              -- Deve corresponder ao valor de getTipoCodigo()
    ID_SISTEMA                -- Referência ao sistema
) VALUES (
    SEQ_CRM_ASSINATURA_DIGITAL.NEXTVAL,
    'NOVA_ASSINATURA',        -- Exemplo: valor retornado por getTipo()
    'Descrição da Nova Assinatura',
    'Observações sobre a assinatura',
    'PARAM1;PARAM2',          -- Exemplo: valor retornado por getTipoCodigo()
    1                         -- ID do sistema
);
```

#### 2. **CRM_ASSINATURA_DIGITAL_PARM** (Parâmetros da Assinatura)
```sql
-- Para cada parâmetro definido em getTipoCodigo()
INSERT INTO CRM_ASSINATURA_DIGITAL_PARM (
    ID_ASSINATURA_DIGITAL,    -- FK para CRM_ASSINATURA_DIGITAL
    NOME_PARAMETRO,           -- Nome do parâmetro (deve estar em getTipoCodigo())
    EH_TIPO_CODIGO,           -- 'S' para obrigatórios, 'N' para opcionais
    ORDEM_TIPO_CODIGO,        -- Ordem dos parâmetros (1, 2, 3...)
    TIPO_VALOR,               -- 'T' (Texto), 'I' (Inteiro), 'BI' (BigInteger)
    VALOR_DEFAULT             -- Valor padrão para parâmetros opcionais
) VALUES 
-- Parâmetro 1 (obrigatório)
(1, 'PARAM1', 'S', 1, 'I', NULL),
-- Parâmetro 2 (obrigatório)
(1, 'PARAM2', 'S', 2, 'I', NULL),
-- Parâmetro opcional
(1, 'PARAM_OPCIONAL', 'N', 3, 'T', 'VALOR_PADRAO');
```

#### 3. **CRM_ASSINATURA_DIGITAL_SIGN** (Signatários)
```sql
-- Para cada tipo de documento que pode ser assinado
INSERT INTO CRM_ASSINATURA_DIGITAL_SIGN (
    ID_ASSINATURA_DIGITAL,    -- FK para CRM_ASSINATURA_DIGITAL
    TAG_DOCUMENTO,            -- Tag do documento (ex: #CLIENTE, #FUNCIONARIO)
    LOOKUP_TABLE,             -- Tabela para buscar signatários
    LOOKUP_FIELD,             -- Campo para buscar signatários
    LOOKUP_FILTER,            -- Filtro adicional
    SIGNATARIO_TIPO,          -- Tipo do signatário
    DESCRICAO                 -- Descrição do signatário
) VALUES (
    1,                        -- ID da assinatura digital
    '#CLIENTE',               -- Tag do documento
    'CLIENTES',               -- Tabela de clientes
    'CPF_CNPJ',              -- Campo CPF/CNPJ
    'ATIVO = "S"',           -- Filtro adicional
    'CLIENTE',                -- Tipo do signatário
    'Cliente da OS'           -- Descrição
);
```

#### 4. **CRM_ASSINATURA_DIGITAL_DOC** (Documentos da Assinatura)
```sql
-- Para documentos que são sempre anexados à assinatura
INSERT INTO CRM_ASSINATURA_DIGITAL_DOC (
    ID_ASSINATURA_DIGITAL,    -- FK para CRM_ASSINATURA_DIGITAL
    NOME_ARQUIVO,             -- Nome do arquivo
    DESCRICAO,                -- Descrição do documento
    ORDEM                     -- Ordem de exibição
) VALUES (
    1,                        -- ID da assinatura digital
    'termo_lgpd.pdf',         -- Nome do arquivo
    'Termo de LGPD',          -- Descrição
    1                         -- Ordem
);
```

#### 5. **CRM_ASSINATURA_DIGITAL_SISTEMA** (Sistemas)
```sql
-- Sistema associado à assinatura (se necessário)
INSERT INTO CRM_ASSINATURA_DIGITAL_SISTEMA (
    ID_SISTEMA,               -- Chave primária
    DESCRICAO                 -- Descrição do sistema
) VALUES (
    1,                        -- ID do sistema
    'Sistema Principal'       -- Descrição
);
```

## ?? Métodos Obrigatórios da Interface

### 1. **getTipo()**
```java
@Override
public String getTipo() {
    return "NOVA_ASSINATURA"; // Deve ser único e consistente
}
```
- **Propósito**: Define o identificador único do tipo de assinatura
- **Convenção**: Use UPPER_CASE com underscores (ex: "OS_ABERTURA", "NFE_CANHOTO")
- **?? IMPORTANTE**: Este valor DEVE corresponder ao cadastro em `CRM_ASSINATURA_DIGITAL.TIPO_ASSINATURA`

### 2. **getTipoCodigo()**
```java
@Override
public String getTipoCodigo() {
    return "PARAM1;PARAM2;PARAM3"; // Parâmetros separados por ponto e vírgula
}
```
- **Propósito**: Define os parâmetros necessários para identificar o documento
- **Formato**: Parâmetros separados por ponto e vírgula (;)
- **?? IMPORTANTE**: Este valor DEVE corresponder ao cadastro em `CRM_ASSINATURA_DIGITAL.TIPO_CODIGO`
- **Exemplos**: 
  - `"NUMERO_OS;COD_EMPRESA"` para OS
  - `"NUMERO_NFE;COD_EMPRESA"` para NFE

### 3. **getCodigoAcesso()**
```java
@Override
public String getCodigoAcesso() {
    return EmpresaUtil.isCrmService() ? "CODIGO_ACESSO" : "";
}
```
- **Propósito**: Define o código de acesso necessário para usar esta assinatura
- **Lógica**: Verifica se é CRM Service e retorna o código apropriado
- **Retorno vazio**: Indica que não há restrição de acesso

### 4. **getDocumentosParaAssinar()**
```java
@Override
public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException, NaoEncontrouDocumentoException {
    // Implementação específica para gerar documentos
}
```
- **Propósito**: Gera a lista de documentos que serão assinados digitalmente
- **Parâmetro**: String com os códigos separados por ponto e vírgula
- **Retorno**: Lista de documentos para assinatura

## ?? Estratégias de Implementação

### 1. **Implementação Direta (Recomendado para casos simples)**
Baseada em [OsPadraoStrategy.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/OsPadraoStrategy.java)

```java
public class NovaAssinaturaStrategy implements TipoAssinaturaStrategy {
    
    @Override
    public String getTipo() {
        return "NOVA_ASSINATURA";
    }

    @Override
    public String getTipoCodigo() {
        return "PARAM1;PARAM2";
    }

    @Override
    public String getCodigoAcesso() {
        return EmpresaUtil.isCrmService() ? "CODIGO" : "";
    }

    @Override
    public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) {
        return new ArrayList<>();
    }
}
```

### 2. **Implementação com Classe Base (Recomendado para casos complexos)**
Baseada em [CrmAssinaturaDigitalComum.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/CrmAssinaturaDigitalComum.java)

```java
public class NovaAssinaturaComumStrategy extends CrmAssinaturaDigitalComum {
    public NovaAssinaturaComumStrategy() {
        super("NOVA_ASSINATURA");
    }

    @Override
    public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException, NaoEncontrouDocumentoException {
        // Implementação específica
    }
}
```

### 3. **Implementação com Geração de Relatórios**
Baseada em [OsMontadoraStrategy.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/OsMontadoraStrategy.java)

```java
@Override
public List<CrmAssinaturaDigitalDocumento> getDocumentosParaAssinar(String stringCodigoValores) throws DataException {
    JSONObject jsonParametros = getJsonParametrosFromCodigoValores(stringCodigoValores);
    
    // Extrair parâmetros
    double param1 = jsonParametros.getInt("PARAM1");
    double param2 = jsonParametros.getInt("PARAM2");
    
    // Gerar documento
    byte[] documentoPdf = gerarRelatorio(param1, param2, jsonParametros);
    
    // Criar lista de documentos
    List<CrmAssinaturaDigitalDocumento> listaDocumentos = new ArrayList<>();
    CrmAssinaturaDigitalDocumento documento = new CrmAssinaturaDigitalDocumento(
        "nome_arquivo.pdf", 
        documentoPdf, 
        "#IDENTIFICADOR"
    );
    listaDocumentos.add(documento);
    
    return listaDocumentos;
}
```

## ?? Registro no Factory

Após criar a nova estratégia, registre-a no [EnTipoAssinaturaFactory.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/EnTipoAssinaturaFactory.java):

```java
static {
    // ... outras estratégias ...
    strategies.put("NOVA_ASSINATURA", new NovaAssinaturaStrategy());
}
```

## ?? Tabelas de Documentos e Envelopes

### **NBSAPI_ENVELOPES_FILA**
- **Propósito**: Fila de envelopes aguardando assinatura
- **Campos principais**:
  - `TIPO_ASSINATURA`: Tipo da assinatura digital
  - `VALOR_CODIGO`: Códigos dos parâmetros separados por ponto e vírgula
  - `STATUS`: Status do envelope (PENDENTE, ASSINADO, ERRO)

### **NBSAPI_ENVELOPES_LOG**
- **Propósito**: Log de todas as operações realizadas nos envelopes
- **Campos principais**:
  - `ID_ENVELOPE`: Referência ao envelope
  - `TIPO_ASSINATURA`: Tipo da assinatura
  - `ACAO`: Ação realizada (CRIADO, ASSINADO, ERRO)

## ?? Exemplos de Uso

### **Exemplo 1: Assinatura Simples**
```java
// Criar instância
TipoAssinaturaStrategy strategy = EnTipoAssinaturaFactory.getTipoAssinatura("OS_ABERTURA");

// Validar acesso
if (strategy.validarAcessoTipoAssinatura()) {
    // Gerar parâmetros
    JSONObject parametros = strategy.getJsonParametros(12345, 1);
    
    // Obter documentos para assinatura
    List<CrmAssinaturaDigitalDocumento> documentos = strategy.getDocumentosParaAssinar("12345;1");
}
```

### **Exemplo 2: Assinatura com Parâmetros do Banco**
```java
// Gerar parâmetros a partir de string de códigos
JSONObject parametros = strategy.getJsonParametrosFromCodigoValores("12345;1");
```

### **Exemplo 3: Criação de Envelope para Assinatura**
```java
// Criar envelope na fila
NBSAPI_ENVELOPES_FILA envelope = new NBSAPI_ENVELOPES_FILA("envelope");
envelope.setTIPO_ASSINATURA("NOVA_ASSINATURA");
envelope.setVALOR_CODIGO("12345;1");
envelope.setSTATUS("PENDENTE");
envelope.insert();
```

## ?? Checklist de Configuração

### **Antes de implementar o código Java:**

1. ? **CRM_ASSINATURA_DIGITAL**: Inserir registro principal
2. ? **CRM_ASSINATURA_DIGITAL_PARM**: Configurar todos os parâmetros
3. ? **CRM_ASSINATURA_DIGITAL_SIGN**: Definir signatários
4. ? **CRM_ASSINATURA_DIGITAL_DOC**: Adicionar documentos fixos (se houver)
5. ? **CRM_ASSINATURA_DIGITAL_SISTEMA**: Configurar sistema (se necessário)

### **Após implementar o código Java:**

1. ? **EnTipoAssinaturaFactory**: Registrar a nova estratégia
2. ? **Testar**: Validar funcionamento com dados reais
3. ? **Documentar**: Comentar métodos complexos
4. ? **Validar**: Testar diferentes cenários de parâmetros

## ?? Troubleshooting

### **Erro: "Tipo de assinatura não encontrado"**
- ? Verifique se a estratégia foi registrada no `EnTipoAssinaturaFactory`
- ? Confirme se o valor de `getTipo()` está correto
- ? **Solução**: Verificar se o registro existe em `CRM_ASSINATURA_DIGITAL`

### **Erro: "Parâmetros não encontrados"**
- ? Verifique se os parâmetros estão cadastrados no banco
- ? Confirme se `EH_TIPO_CODIGO = 'S'` para parâmetros obrigatórios
- ? **Solução**: Verificar configuração em `CRM_ASSINATURA_DIGITAL_PARM`

### **Erro: "Número de parâmetros incorreto"**
- ? Verifique se `getTipoCodigo()` retorna a quantidade correta de parâmetros
- ? Confirme se os parâmetros estão sendo passados na ordem correta
- ? **Solução**: Verificar `TIPO_CODIGO` em `CRM_ASSINATURA_DIGITAL` e `ORDEM_TIPO_CODIGO` em `CRM_ASSINATURA_DIGITAL_PARM`

### **Erro: "Nenhum Signatário cadastrado"**
- ? Verifique se existem registros em `CRM_ASSINATURA_DIGITAL_SIGN`
- ? Confirme se as tags dos documentos correspondem aos signatários
- ? **Solução**: Configurar signatários em `CRM_ASSINATURA_DIGITAL_SIGN`

## ?? Boas Práticas

1. **Nomenclatura**: Use nomes descritivos e consistentes
2. **Tratamento de Erros**: Implemente validações adequadas nos parâmetros
3. **Documentação**: Comente métodos complexos e lógicas de negócio
4. **Testes**: Teste diferentes cenários de parâmetros
5. **Logs**: Adicione logs para facilitar debugging
6. **Validações**: Valide parâmetros antes de processar
7. **Reutilização**: Use métodos da interface quando possível
8. **Configuração**: Sempre configure o banco antes de implementar o código
9. **Testes**: Teste com dados reais após a configuração
10. **Monitoramento**: Monitore os logs em `NBSAPI_ENVELOPES_LOG`

## ?? Arquivos de Referência

- [TipoAssinaturaStrategy.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/TipoAssinaturaStrategy.java) - Interface principal
- [EnTipoAssinaturaFactory.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/EnTipoAssinaturaFactory.java) - Factory de estratégias
- [OsPadraoStrategy.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/OsPadraoStrategy.java) - Estratégia simples
- [OsMontadoraStrategy.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/OsMontadoraStrategy.java) - Com relatórios
- [CrmAssinaturaDigitalComum.java](mdc:crmservice/nbs-empresa-zk/src/main/java/freedom/util/assinaturaDigital/strategy/CrmAssinaturaDigitalComum.java) - Classe base

